target/
!**/src/main/**/target/
!**/src/test/**/target/

### Maven Wrapper ###
!.mvn/wrapper/maven-wrapper.jar
!.mvn/wrapper/maven-wrapper.properties

### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Mac OS ###
.DS_Store

### Node.js ###
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

### Frontend specific ###
frontend/node_modules/
user-frontend/node_modules/
frontend/build/
user-frontend/build/
frontend/dist/
user-frontend/dist/

### Environment files ###
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

### Logs ###
*.log
logs/

### Security & Certificates ###
*.crt
*.key
*.pem
*.p12
*.jks
server_san.crt

### Backup files ###
*.backup
*.bak
*~
*.swp
*.swo

### Temporary files ###
*.tmp
*.temp
*.pid

### Augment AI ###
.augment/

### Configuration files (sensitive) ###
ngrok*.yml
deploy.conf

### OS specific ###
Thumbs.db
ehthumbs.db
Desktop.ini

### Application specific ###
frontend.log
backend.log
zoombus.log