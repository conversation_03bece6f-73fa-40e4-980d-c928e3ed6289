# Zoom用户账户用途功能

## 功能概述

在Zoom用户管理页面中新增了"账户用途"字段，支持以下四种类型：
- **参会账号-公共** - 用于公共参会的账号
- **参会账号-专属** - 用于专属参会的账号
- **开会账号-公共** - 用于公共开会的账号
- **开会账号-专属** - 用于专属开会的账号

## 功能特点

1. **独立字段**: 账户用途与现有的用户类型（基础版、专业版、本地部署版）完全独立，不会产生混淆
2. **同步保护**: 从Zoom API同步用户信息时，不会覆盖账户用途字段，确保手动设置的用途不会丢失
3. **完整支持**: 支持创建、编辑、查看账户用途
4. **可视化标识**: 在用户列表中使用不同颜色的标签显示账户用途

## 数据库变更

### 新增字段
- 表名: `t_zoom_users`
- 字段名: `account_usage`
- 类型: `VARCHAR(50)`
- 可选值: `PUBLIC_MEETING`, `PRIVATE_MEETING`, `PUBLIC_HOST`, `PRIVATE_HOST`
- 允许为空: 是

### 执行迁移
```sql
-- 执行数据库迁移脚本
source add_account_usage_column.sql
```

## 后端变更

### 实体类更新
- `ZoomUser.java`: 添加了 `AccountUsage` 枚举和 `accountUsage` 字段
- `CreateZoomUserRequest.java`: 添加了 `accountUsage` 字段
- `UpdateZoomUserRequest.java`: 添加了 `accountUsage` 字段

### 服务层更新
- `ZoomUserService.java`: 
  - 创建用户时支持设置账户用途
  - 更新用户时支持修改账户用途
  - 同步时不会覆盖账户用途字段

## 前端变更

### 用户界面更新
- **用户列表**: 新增"账户用途"列，使用彩色标签显示
- **创建表单**: 添加账户用途选择器
- **编辑表单**: 添加账户用途选择器

### 标签颜色映射
- 参会账号-公共: 蓝色标签
- 参会账号-专属: 青色标签
- 开会账号-公共: 绿色标签
- 开会账号-专属: 紫色标签

## 使用方法

### 创建用户时设置账户用途
1. 点击"创建Zoom账号"按钮
2. 填写基本信息
3. 在"账户用途"下拉框中选择合适的用途
4. 提交创建

### 编辑用户账户用途
1. 在用户列表中点击"编辑"按钮
2. 在弹出的编辑表单中修改"账户用途"
3. 保存更改

### 查看账户用途
在用户列表的"账户用途"列中可以直接查看每个用户的账户用途，通过不同颜色的标签进行区分。

## 注意事项

1. **数据迁移**: 现有用户的账户用途字段为空，需要手动设置
2. **同步安全**: Zoom API同步不会影响账户用途设置
3. **可选字段**: 账户用途是可选字段，可以不设置
4. **后续扩展**: 该字段为后续业务逻辑预留，可根据不同用途实现不同的功能限制

## 测试建议

1. 创建新用户并设置不同的账户用途
2. 编辑现有用户的账户用途
3. 执行Zoom用户同步，验证账户用途不会被覆盖
4. 检查用户列表中的标签显示是否正确
