# 复制功能使用指南

## 📋 概述
本指南介绍了优化后的复制功能的使用方法，包括管理台和用户前端的复制功能。

## 🔧 管理台前端 (frontend/)

### 导入和使用
```javascript
import { copyMeetingInvitation } from '../utils/copyMeetingInfo';

// 复制会议邀请（主会议）
await copyMeetingInvitation(meeting);

// 复制会议邀请（包含occurrence信息）
await copyMeetingInvitation(meeting, occurrence);
```

### 功能特性
- **智能缓存**：5分钟缓存，减少重复API调用
- **API优先**：优先调用后台invitation接口获取最新信息
- **优雅降级**：API失败时使用本地信息生成邀请
- **详细错误处理**：根据错误类型提供具体提示

### 错误处理
```javascript
// 系统会自动处理以下错误：
// - 404: 会议不存在或已被删除
// - 401: Zoom认证失效，请联系管理员
// - 500+: 服务器暂时不可用，请稍后重试
// - 其他: 获取邀请信息失败 (状态码)
```

## 👥 用户前端 (user-frontend/)

### 导入和使用
```javascript
import { copyWithMessage, formatPmiInfo, formatAccountInfo } from '../utils/copyUtils';

// 基础复制功能
await copyWithMessage(text, message, '成功消息', '失败消息');

// 复制PMI信息
const pmiText = formatPmiInfo(pmiInfo);
await copyWithMessage(pmiText, message);

// 复制账号信息
const accountText = formatAccountInfo(accountInfo, timeRange);
await copyWithMessage(accountText, message);
```

### 可用方法

#### 1. 基础复制方法
```javascript
// 复制文本到剪贴板
const success = await copyToClipboard(text);

// 复制文本并显示消息
await copyWithMessage(text, messageApi, successMsg, errorMsg);
```

#### 2. 格式化方法
```javascript
// 格式化PMI信息
const pmiText = formatPmiInfo({
  pmiNumber: '*********',
  pmiPassword: 'abc123',
  joinUrl: 'https://zoom.us/j/*********',
  hostUrl: 'https://zoom.us/s/*********',
  topic: '会议室',
  hasActiveMeeting: true
});

// 格式化账号信息
const accountText = formatAccountInfo({
  email: '<EMAIL>',
  password: 'password123',
  accountName: '测试账号'
}, '2025-08-11 14:00 至 2025-08-11 16:00');

// 格式化简短会议信息
const shortText = formatShortMeetingInfo({
  topic: '产品会议',
  meetingId: '*********',
  password: 'abc123',
  startTime: '2025-08-11T14:00:00'
});
```

#### 3. 高级功能
```javascript
// 检查浏览器支持
const isSupported = isClipboardSupported();

// 复制对象为JSON
await copyObjectAsJson(dataObject, message);

// 批量复制多个项目
await copyMultipleItems(['文本1', '文本2', '文本3'], '\n\n', message);
```

## 🎯 最佳实践

### 1. 错误处理
```javascript
// 推荐：使用copyWithMessage自动处理错误
await copyWithMessage(text, message);

// 不推荐：手动处理复制错误
try {
  await navigator.clipboard.writeText(text);
  message.success('复制成功');
} catch (error) {
  message.error('复制失败');
}
```

### 2. 格式化数据
```javascript
// 推荐：使用专用格式化函数
const formattedText = formatPmiInfo(pmiData);

// 不推荐：手动拼接字符串
const manualText = `会议号：${pmiData.pmiNumber}\n密码：${pmiData.pmiPassword}`;
```

### 3. 缓存利用
```javascript
// 管理台会自动缓存API响应，无需手动管理
// 连续复制同一会议时会使用缓存，提升性能
await copyMeetingInvitation(meeting); // API调用
await copyMeetingInvitation(meeting); // 使用缓存
```

## 🔍 调试和测试

### 1. 测试页面
打开 `test_optimized_copy_function.html` 进行功能测试：
- 正常复制测试
- 缓存功能测试
- 错误处理测试
- 周期性会议测试

### 2. 控制台日志
```javascript
// 查看复制操作日志
console.log('使用缓存的邀请信息');
console.log('成功获取并缓存Zoom API邀请信息');
console.warn('获取Zoom API邀请信息失败，使用默认格式');
```

### 3. 缓存状态
```javascript
// 在浏览器控制台查看缓存状态（仅管理台）
// 缓存会自动管理，通常无需手动干预
```

## ⚠️ 注意事项

### 1. 浏览器兼容性
- 现代浏览器优先使用 `navigator.clipboard` API
- 旧浏览器自动降级到 `document.execCommand`
- HTTPS环境下功能完整，HTTP环境可能受限

### 2. 权限要求
- 某些浏览器需要用户授权剪贴板权限
- 建议在用户交互后调用复制功能
- 避免在页面加载时自动复制

### 3. 缓存策略
- 管理台缓存时间：5分钟
- 缓存基于会议ID，不同会议独立缓存
- 页面刷新会清空缓存

### 4. 错误恢复
- API失败时自动使用本地信息
- 复制失败时提供手动复制提示
- 网络问题时显示相应错误信息

## 🚀 性能优化

### 1. 缓存命中率
- 短时间内重复复制同一会议时使用缓存
- 减少不必要的API调用
- 提升用户体验

### 2. 内存管理
- 自动清理过期缓存
- 避免内存泄漏
- 合理的缓存大小控制

### 3. 网络优化
- 智能的降级策略
- 减少重复请求
- 优化错误处理流程

## 📞 技术支持

如果遇到问题：
1. 查看浏览器控制台错误信息
2. 确认网络连接状态
3. 检查浏览器剪贴板权限
4. 尝试刷新页面重新操作

更多技术细节请参考 `COPY_FUNCTION_OPTIMIZATION_REPORT.md`。
