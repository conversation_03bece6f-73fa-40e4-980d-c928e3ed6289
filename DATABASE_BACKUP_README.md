# 数据库备份和导入脚本使用说明

## 概述

这个脚本用于从远程生产服务器备份 `zoombusV` 数据库并导入到本地环境中。提供了两个版本：

1. `database_backup_import.sh` - 基础版本
2. `database_backup_import_secure.sh` - 安全版本（推荐）

## 配置信息

### 远程服务器配置
- **服务器地址**: nslcp.com
- **用户名**: root
- **数据库主机**: localhost
- **数据库用户**: root
- **数据库密码**: nvshen2018
- **数据库名**: zoombusV

### 本地数据库配置
- **数据库主机**: localhost
- **数据库用户**: root
- **数据库密码**: nvshen2018
- **数据库名**: zoombusV

## 使用方法

### 方法一：使用安全版本（推荐）

```bash
./database_backup_import_secure.sh
```

### 方法二：使用基础版本

```bash
./database_backup_import.sh
```

## 脚本功能

### 主要功能
1. **连接测试**: 测试远程服务器和数据库连接
2. **数据备份**: 从远程服务器备份完整的数据库
3. **数据导入**: 将备份数据导入到本地数据库
4. **安全清理**: 自动清理临时文件和敏感信息

### 备份特性
- 使用 `mysqldump` 进行完整备份
- 包含存储过程、触发器、事件
- 支持大型数据库（使用 `--single-transaction`）
- 自动处理字符编码（UTF-8MB4）
- 包含数据库结构和数据

## 安全特性

### 安全版本优势
- 使用 MySQL 配置文件存储密码，避免命令行暴露
- 临时文件权限设置为 600（仅当前用户可读）
- 执行完成后自动清理所有临时文件
- 支持 SSH 密钥认证

### 文件权限
```bash
# 脚本文件权限
-rwxr-xr-x database_backup_import_secure.sh

# 临时配置文件权限（脚本自动设置）
-rw------- /tmp/.my_remote.cnf
-rw------- /tmp/.my_local.cnf
```

## 执行流程

1. **环境检查**
   - 检查必要工具（ssh, mysql, mysqldump）
   - 创建 MySQL 配置文件

2. **连接测试**
   - 测试远程服务器 SSH 连接
   - 测试远程数据库连接
   - 测试本地数据库连接

3. **数据备份**
   - 在远程服务器执行 mysqldump
   - 将备份文件传输到本地

4. **数据导入**
   - 用户确认操作
   - 导入数据到本地数据库
   - 验证导入结果

5. **清理工作**
   - 删除临时备份文件
   - 删除配置文件
   - 清理远程临时文件

## 前置条件

### 系统要求
- Linux 或 macOS 系统
- 已安装 MySQL 客户端工具
- 已安装 SSH 客户端

### 网络要求
- 能够 SSH 连接到 nslcp.com
- 远程服务器能够访问其本地 MySQL 数据库
- 本地能够访问本地 MySQL 数据库

### SSH 配置
建议配置 SSH 密钥认证：

```bash
# 生成 SSH 密钥（如果没有）
ssh-keygen -t rsa -b 4096

# 复制公钥到远程服务器
ssh-copy-id <EMAIL>
```

## 故障排除

### 常见问题

1. **SSH 连接失败**
   ```
   解决方案：
   - 检查网络连接
   - 确认服务器地址正确
   - 配置 SSH 密钥或确保密码正确
   ```

2. **远程数据库连接失败**
   ```
   解决方案：
   - 检查数据库服务是否运行
   - 确认数据库用户名和密码
   - 检查数据库权限设置
   ```

3. **本地数据库连接失败**
   ```
   解决方案：
   - 启动本地 MySQL 服务
   - 检查本地数据库配置
   - 确认用户权限
   ```

4. **备份文件过大**
   ```
   解决方案：
   - 确保本地磁盘空间充足
   - 考虑使用压缩备份
   ```

### 日志输出

脚本提供彩色日志输出：
- 🔵 **[INFO]** - 信息提示
- 🟢 **[SUCCESS]** - 操作成功
- 🟡 **[WARNING]** - 警告信息
- 🔴 **[ERROR]** - 错误信息

## 注意事项

### ⚠️ 重要警告
- **数据覆盖**: 导入操作会完全覆盖本地数据库
- **备份建议**: 建议在导入前备份本地数据库
- **权限要求**: 需要数据库管理员权限

### 最佳实践
1. 在非生产环境先测试脚本
2. 定期验证备份文件的完整性
3. 保持脚本和配置文件的安全
4. 记录每次备份和导入的时间

## 示例输出

```bash
$ ./database_backup_import_secure.sh

[INFO] 开始数据库备份和导入流程...
[INFO] 远程服务器: <EMAIL>
[INFO] 远程数据库: zoombusV
[INFO] 本地数据库: zoombusV

[INFO] 检查必要的工具...
[SUCCESS] 所有必要工具检查完成
[INFO] 创建 MySQL 配置文件...
[SUCCESS] MySQL 配置文件创建完成
[INFO] 测试远程服务器连接...
[SUCCESS] 远程服务器连接正常
[INFO] 测试远程数据库连接...
[SUCCESS] 远程数据库连接正常
[INFO] 测试本地数据库连接...
[SUCCESS] 本地数据库连接正常
[INFO] 开始备份远程数据库 zoombusV...
[SUCCESS] 远程数据库备份完成，文件大小：15M
[WARNING] 即将导入数据到本地数据库 zoombusV
[WARNING] 这将覆盖本地数据库的所有数据！
确认继续吗？(yes/no): yes
[INFO] 开始导入数据到本地数据库...
[SUCCESS] 数据导入完成
[INFO] 检查导入结果...
[SUCCESS] 导入完成！本地数据库 zoombusV 现在包含 25 个表
[INFO] 清理临时文件...
[SUCCESS] 临时文件清理完成
[SUCCESS] 所有操作完成！
```

## 技术支持

如果遇到问题，请检查：
1. 网络连接状态
2. 数据库服务状态
3. 用户权限配置
4. 磁盘空间情况
