# 数据库迁移部署指南

## 📋 概述

本指南说明如何将meeting.started事件修复所需的数据库结构变更安全地部署到远程服务器。

## 🎯 变更内容

### 主要修改
- 修改 `t_zoom_meetings` 表的 `pmi_record_id` 字段，允许NULL值
- 添加性能优化索引

### 业务影响
- **正面影响**：支持所有类型会议的监控（PMI会议、安排会议、其他会议）
- **风险评估**：低风险，向后兼容，不影响现有功能
- **回滚方案**：可以回滚，但会影响非PMI会议的记录创建

## 📁 迁移文件

### 文件位置
```
src/main/resources/db/migration/V20250804_001__Allow_Null_PMI_Record_ID_For_Non_PMI_Meetings.sql
```

### 文件内容
```sql
-- 修改字段约束，允许NULL值
ALTER TABLE t_zoom_meetings 
MODIFY COLUMN pmi_record_id BIGINT NULL 
COMMENT 'PMI记录ID（PMI会议时有值，非PMI会议时为null）';

-- 添加性能优化索引
CREATE INDEX IF NOT EXISTS idx_zoom_meetings_pmi_record_id ON t_zoom_meetings(pmi_record_id);
CREATE INDEX IF NOT EXISTS idx_zoom_meetings_status_start_time ON t_zoom_meetings(status, start_time);
CREATE INDEX IF NOT EXISTS idx_zoom_meetings_host_id ON t_zoom_meetings(host_id);
CREATE INDEX IF NOT EXISTS idx_zoom_meetings_uuid ON t_zoom_meetings(zoom_meeting_uuid);
CREATE INDEX IF NOT EXISTS idx_zoom_meetings_meeting_id ON t_zoom_meetings(zoom_meeting_id);
```

## 🚀 部署步骤

### 1. 开发环境验证

#### 1.1 检查迁移文件
```bash
# 确认迁移文件存在
ls -la src/main/resources/db/migration/V20250804_001__*

# 检查文件内容
cat src/main/resources/db/migration/V20250804_001__Allow_Null_PMI_Record_ID_For_Non_PMI_Meetings.sql
```

#### 1.2 本地测试迁移
```bash
# 执行Flyway迁移
./mvnw flyway:migrate

# 验证迁移结果
./mvnw flyway:info
```

#### 1.3 验证功能
```bash
# 测试meeting.started事件处理
curl -X POST http://localhost:8080/api/webhooks/test/meeting-started \
  -H "Content-Type: application/json" \
  -d '{
    "meetingUuid": "test-deploy-'$(date +%s)'",
    "meetingId": "test-deploy-123",
    "hostId": "test-host",
    "topic": "部署测试会议"
  }'
```

### 2. 测试环境部署

#### 2.1 代码部署
```bash
# 提交代码到版本控制
git add src/main/resources/db/migration/V20250804_001__*
git add src/main/java/com/zoombus/service/ZoomMeetingService.java
git add src/main/java/com/zoombus/entity/ZoomMeeting.java
git add src/main/java/com/zoombus/controller/WebhookController.java

git commit -m "fix: 支持非PMI会议的t_zoom_meetings记录创建

- 修改pmi_record_id字段允许NULL值
- 增强meeting.started事件处理逻辑
- 支持PMI会议、安排会议、其他会议的统一监控
- 添加性能优化索引"

# 推送到测试分支
git push origin feature/meeting-started-fix
```

#### 2.2 测试环境迁移
```bash
# 在测试服务器上执行
cd /path/to/zoombus
git pull origin feature/meeting-started-fix

# 执行数据库迁移
./mvnw flyway:migrate -Dspring.profiles.active=test

# 重启应用
./restart-test-app.sh
```

#### 2.3 测试环境验证
```bash
# 检查数据库结构
mysql -u test_user -p test_db -e "
SELECT COLUMN_NAME, IS_NULLABLE, DATA_TYPE 
FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 't_zoom_meetings' 
AND COLUMN_NAME = 'pmi_record_id';"

# 测试功能
curl -X POST http://test-server:8080/api/webhooks/test/meeting-started \
  -H "Content-Type: application/json" \
  -d '{"meetingUuid":"test-'$(date +%s)'","meetingId":"test-123","hostId":"test","topic":"测试"}'
```

### 3. 生产环境部署

#### 3.1 部署前检查
- [ ] 测试环境验证通过
- [ ] 代码审查完成
- [ ] 数据库备份完成
- [ ] 回滚方案准备就绪
- [ ] 监控告警配置完成

#### 3.2 数据库备份
```bash
# 备份生产数据库
mysqldump -u prod_user -p prod_db t_zoom_meetings > backup_zoom_meetings_$(date +%Y%m%d_%H%M%S).sql

# 备份整个数据库（可选）
mysqldump -u prod_user -p prod_db > backup_full_$(date +%Y%m%d_%H%M%S).sql
```

#### 3.3 生产部署
```bash
# 1. 部署代码
cd /path/to/production/zoombus
git pull origin main

# 2. 执行数据库迁移
./mvnw flyway:migrate -Dspring.profiles.active=production

# 3. 验证迁移结果
./mvnw flyway:info -Dspring.profiles.active=production

# 4. 重启应用
./restart-prod-app.sh
```

#### 3.4 生产验证
```bash
# 检查应用状态
curl http://prod-server:8080/actuator/health

# 检查数据库结构
mysql -u prod_user -p prod_db -e "
SELECT COLUMN_NAME, IS_NULLABLE 
FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 't_zoom_meetings' 
AND COLUMN_NAME = 'pmi_record_id';"

# 监控日志
tail -f /var/log/zoombus/application.log | grep -E "(meeting.started|从Webhook创建)"
```

## 🔄 回滚方案

### 如果需要回滚

#### 1. 代码回滚
```bash
# 回滚到上一个版本
git revert <commit-hash>
git push origin main
```

#### 2. 数据库回滚
```sql
-- 注意：这会导致非PMI会议记录创建失败
ALTER TABLE t_zoom_meetings 
MODIFY COLUMN pmi_record_id BIGINT NOT NULL;

-- 删除新增的索引（可选）
DROP INDEX IF EXISTS idx_zoom_meetings_pmi_record_id ON t_zoom_meetings;
DROP INDEX IF EXISTS idx_zoom_meetings_status_start_time ON t_zoom_meetings;
DROP INDEX IF EXISTS idx_zoom_meetings_host_id ON t_zoom_meetings;
DROP INDEX IF EXISTS idx_zoom_meetings_uuid ON t_zoom_meetings;
DROP INDEX IF EXISTS idx_zoom_meetings_meeting_id ON t_zoom_meetings;
```

#### 3. 应用重启
```bash
./restart-prod-app.sh
```

## 📊 监控指标

### 部署后监控
- 应用启动状态
- meeting.started事件处理成功率
- t_zoom_meetings表记录创建数量
- 数据库性能指标
- 错误日志监控

### 关键SQL查询
```sql
-- 检查新创建的记录
SELECT COUNT(*) as total_records,
       COUNT(pmi_record_id) as pmi_meetings,
       COUNT(*) - COUNT(pmi_record_id) as non_pmi_meetings
FROM t_zoom_meetings 
WHERE created_at >= '2025-08-04 00:00:00';

-- 检查最近的记录
SELECT id, zoom_meeting_uuid, pmi_record_id, topic, created_at
FROM t_zoom_meetings 
ORDER BY created_at DESC 
LIMIT 10;
```

## ✅ 部署检查清单

### 部署前
- [ ] 迁移文件已创建并测试
- [ ] 代码变更已完成并测试
- [ ] 测试环境验证通过
- [ ] 数据库备份已完成
- [ ] 回滚方案已准备

### 部署中
- [ ] 代码部署成功
- [ ] 数据库迁移执行成功
- [ ] 应用重启成功
- [ ] 基础功能验证通过

### 部署后
- [ ] 监控指标正常
- [ ] meeting.started事件处理正常
- [ ] 新记录创建正常
- [ ] 无错误日志
- [ ] 性能指标正常

## 📞 应急联系

如果部署过程中遇到问题：
1. 立即停止部署流程
2. 检查错误日志
3. 如有必要，执行回滚方案
4. 联系开发团队进行问题排查

---

**重要提醒**：生产环境部署前务必在测试环境完整验证所有功能！
