# 数据库持久化配置说明

## 修改内容

为了保留测试数据，已将数据库配置从 `create-drop` 修改为 `update`。

### 修改前
```yaml
jpa:
  hibernate:
    ddl-auto: create-drop  # 每次启动都会删除并重新创建表
```

### 修改后
```yaml
jpa:
  hibernate:
    ddl-auto: update       # 只更新表结构，保留现有数据
```

## DDL-AUTO 选项说明

| 选项 | 说明 | 适用场景 |
|------|------|----------|
| `create` | 每次启动时创建表，不删除现有表 | 开发环境初次建表 |
| `create-drop` | 启动时创建表，关闭时删除表 | 测试环境 |
| `update` | 根据实体更新表结构，保留数据 | 开发环境（推荐） |
| `validate` | 验证表结构，不做任何修改 | 生产环境 |
| `none` | 不执行任何DDL操作 | 手动管理数据库 |

## 当前配置效果

### 开发环境 (application.yml)
- **DDL模式**: `update`
- **效果**: 保留所有测试数据，只在实体变更时更新表结构
- **数据库**: MySQL (zoombusV)

### 生产环境 (application-prod.yml)
- **DDL模式**: `validate`
- **效果**: 只验证表结构，不做任何修改
- **数据库**: MySQL (zoombus)

### 测试环境 (application-test.yml)
- **DDL模式**: `create-drop`
- **效果**: 每次测试都使用全新的数据库
- **数据库**: H2 内存数据库

## 数据初始化机制

### 默认管理员用户
- **用户名**: admin
- **密码**: admin123
- **创建逻辑**: 只在用户不存在时创建，避免重复

### 检查机制
```java
if (adminUserRepository.existsByUsername(defaultUsername)) {
    log.info("默认管理员用户已存在，跳过初始化");
    return;
}
```

## 注意事项

1. **数据备份**: 虽然使用 `update` 模式，但建议定期备份重要数据
2. **表结构变更**: 当实体类发生变更时，Hibernate 会自动更新表结构
3. **生产环境**: 生产环境使用 `validate` 模式，需要手动管理数据库变更
4. **测试数据**: 现在重启应用不会丢失测试数据，可以放心添加测试数据

## 如何添加测试数据

### 方法1: 通过管理界面
1. 启动应用后访问 http://localhost:3000
2. 使用 admin/admin123 登录
3. 在界面中添加用户、会议等数据

### 方法2: 通过SQL脚本
1. 连接到 MySQL 数据库
2. 执行 INSERT 语句添加测试数据
3. 数据会在下次启动时保留

### 方法3: 通过API
1. 使用 Postman 或其他工具
2. 调用 REST API 添加数据
3. 数据会持久化保存

## 恢复到清空模式

如果需要恢复到每次启动都清空数据的模式：

```yaml
jpa:
  hibernate:
    ddl-auto: create-drop
```

然后重启应用即可。
