# ZoomBus 数据库版本管理指南

## 📋 概述

本指南建立了完整的数据库版本管理体系，确保应用程序版本与数据库版本的一致性，并提供自动化的数据库迁移机制。

## 🏗️ 版本管理架构

### 核心组件

1. **Flyway迁移系统**：管理数据库结构变更
2. **版本记录服务**：记录应用和数据库版本对应关系
3. **自动迁移服务**：应用启动时自动执行数据库迁移
4. **版本管理API**：提供版本信息查询和管理接口

### 版本号规范

- **应用版本**：语义化版本，如 `1.0.0`, `1.1.0`, `2.0.0`
- **数据库版本**：基于日期的版本，如 `20250804_001`, `20250804_002`
- **Flyway版本**：与数据库版本一致

## 📁 文件结构

```
src/main/resources/db/migration/
├── V1.0__Baseline.sql                                    # 基线数据库结构
├── V1.1__add_recurring_meeting_fields.sql               # 循环会议字段
├── V1.2__add_occurrence_fields.sql                      # 会议实例字段
├── V1.3__allow_null_pmi_record_id.sql                   # 旧版本（已废弃）
├── V20250721_2__Add_repeat_details_to_pmi_schedules.sql # PMI计划重复详情
├── V20250723_2__add_window_actual_times.sql             # 窗口实际时间
├── V20250729_001__Add_Account_Name_To_Webhook_Events.sql # Webhook账号名称
└── V20250804_001__Allow_Null_PMI_Record_ID_For_Non_PMI_Meetings.sql # 允许PMI记录ID为null
```

## 🔧 使用方法

### 1. 创建新的数据库迁移

#### 命名规范
```
V{YYYYMMDD}_{序号}__{描述}.sql
```

示例：
```
V20250804_002__Add_User_Preferences_Table.sql
```

#### 创建步骤

1. **创建迁移文件**：
```bash
# 在 src/main/resources/db/migration/ 目录下创建新文件
touch src/main/resources/db/migration/V20250804_002__Add_User_Preferences_Table.sql
```

2. **编写迁移脚本**：
```sql
-- 添加用户偏好设置表
-- 版本: V20250804_002
-- 描述: 为用户添加个性化偏好设置功能

CREATE TABLE IF NOT EXISTS t_user_preferences (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    preference_key VARCHAR(100) NOT NULL COMMENT '偏好键',
    preference_value TEXT COMMENT '偏好值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_preference (user_id, preference_key),
    INDEX idx_user_id (user_id),
    INDEX idx_preference_key (preference_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户偏好设置表';

-- 添加默认偏好设置
INSERT IGNORE INTO t_user_preferences (user_id, preference_key, preference_value)
SELECT id, 'theme', 'light' FROM admin_users WHERE username = 'admin';
```

3. **更新init.sql**：
```bash
# 将新的迁移内容添加到init.sql文件中
# 确保新环境部署时包含所有变更
```

### 2. 执行数据库迁移

#### 自动迁移（推荐）
应用启动时会自动执行待处理的迁移：

```yaml
# application.yml
spring:
  flyway:
    enabled: true
    auto-migrate: true
    baseline-on-migrate: true
```

#### 手动迁移
```bash
# 使用Maven插件
./mvnw flyway:migrate

# 或使用API接口
curl -X POST http://localhost:8080/api/migration/execute
```

### 3. 版本管理API

#### 获取当前版本信息
```bash
curl http://localhost:8080/api/version/current
```

响应示例：
```json
{
  "applicationName": "zoombus",
  "applicationVersion": "1.0.0",
  "databaseVersion": "20250804_001",
  "serverInfo": "localhost",
  "timestamp": "2025-08-04T12:00:00"
}
```

#### 获取版本历史
```bash
curl http://localhost:8080/api/version/history?limit=10
```

#### 检查迁移状态
```bash
curl http://localhost:8080/api/migration/status
```

#### 手动记录版本信息
```bash
curl -X POST http://localhost:8080/api/version/record \
  -H "Content-Type: application/json" \
  -d '{
    "description": "手动部署到生产环境"
  }'
```

## 🚀 部署流程

### 新环境部署

1. **使用init.sql快速部署**：
```bash
mysql -u root -p zoombusV < init.sql
```

2. **启动应用**：
```bash
./mvnw spring-boot:run
```

3. **验证部署**：
```bash
curl http://localhost:8080/api/version/current
```

### 现有环境更新

1. **拉取最新代码**：
```bash
git pull origin main
```

2. **启动应用**（自动执行迁移）：
```bash
./mvnw spring-boot:run
```

3. **验证迁移**：
```bash
curl http://localhost:8080/api/migration/status
```

## 📊 版本兼容性管理

### 版本兼容性检查
```bash
curl "http://localhost:8080/api/version/compatibility?requiredDbVersion=20250804_001"
```

### 版本升级路径

| 应用版本 | 数据库版本 | 兼容性 | 升级路径 |
|---------|-----------|--------|----------|
| 1.0.0 | 20250804_001 | ✅ | 当前版本 |
| 1.1.0 | 20250810_001 | ⬆️ | 需要迁移 |
| 2.0.0 | 20250901_001 | ⬆️ | 需要迁移 |

## 🔍 监控和故障排除

### 版本历史查询
```sql
-- 查看版本历史
SELECT 
    application_version,
    database_version,
    event_type,
    event_description,
    server_info,
    created_at
FROM t_version_history 
ORDER BY created_at DESC 
LIMIT 10;
```

### Flyway状态查询
```sql
-- 查看Flyway迁移状态
SELECT 
    version,
    description,
    script,
    installed_on,
    success
FROM flyway_schema_history 
ORDER BY installed_rank DESC;
```

### 常见问题

#### 1. 迁移失败
```bash
# 查看详细错误信息
./mvnw flyway:info
./mvnw flyway:validate

# 修复后重新迁移
./mvnw flyway:repair
./mvnw flyway:migrate
```

#### 2. 版本不一致
```bash
# 检查当前状态
curl http://localhost:8080/api/version/current
curl http://localhost:8080/api/migration/status

# 手动记录正确版本
curl -X POST http://localhost:8080/api/version/record \
  -H "Content-Type: application/json" \
  -d '{
    "description": "修复版本不一致问题"
  }'
```

## 📋 最佳实践

### 1. 迁移脚本编写
- ✅ 使用 `IF NOT EXISTS` 确保幂等性
- ✅ 添加详细的注释说明
- ✅ 包含回滚方案（如果可能）
- ✅ 测试脚本在空数据库和现有数据库上的执行

### 2. 版本管理
- ✅ 每次发布前更新应用版本号
- ✅ 数据库变更必须通过Flyway迁移
- ✅ 记录重要的版本变更说明
- ✅ 定期清理过期的版本历史记录

### 3. 部署流程
- ✅ 生产部署前在测试环境验证
- ✅ 备份数据库后再执行迁移
- ✅ 监控迁移执行状态
- ✅ 准备回滚方案

## 🔄 回滚策略

### 应用回滚
```bash
# 回滚到上一个版本
git checkout <previous-version-tag>
./mvnw spring-boot:run
```

### 数据库回滚
```sql
-- 注意：数据库回滚需要谨慎操作
-- 建议通过新的迁移脚本来"撤销"变更
-- 而不是直接回滚Flyway历史
```

## 📞 支持和维护

### 日常维护
- 定期检查版本一致性
- 监控迁移执行状态
- 清理过期的版本历史
- 更新文档和最佳实践

### 紧急情况
- 迁移失败时的快速恢复流程
- 版本不一致的修复方案
- 数据库损坏的恢复策略

---

**重要提醒**：所有数据库变更都必须通过Flyway迁移系统进行，禁止直接修改生产数据库结构！
