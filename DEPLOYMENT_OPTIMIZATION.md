# ZoomBus 部署脚本优化完成

## 🎯 优化概述

成功优化了deploy.sh脚本，现在支持双前端部署架构，并新增了专门的用户端前端快速部署脚本。

## ✅ 主要改进

### 1. 双前端支持
- **管理端前端**: 部署到 `/home/<USER>/m.zoombus.com/dist`
- **用户端前端**: 部署到 `/home/<USER>/zoombus.com/dist`

### 2. 灵活的部署选项
优化后的deploy.sh提供6种部署模式：

1. **完整部署** - 后端 + 管理端前端 + 用户端前端
2. **仅部署后端** - 只部署Spring Boot应用
3. **仅部署管理端前端** - 只部署React管理端
4. **仅部署用户端前端** - 只部署Vue用户端
5. **仅部署前端** - 管理端 + 用户端前端
6. **自定义选择** - 用户自由选择组合

### 3. 专用快速部署脚本
新增 `deploy-user-frontend.sh` 专门用于用户端前端的快速部署。

## 🔧 技术实现

### 配置变量更新
```bash
# 原来
FRONTEND_TARGET_DIR="/home/<USER>/m.zoombus.com/dist"

# 现在
ADMIN_FRONTEND_TARGET_DIR="/home/<USER>/m.zoombus.com/dist"
USER_FRONTEND_TARGET_DIR="/home/<USER>/zoombus.com/dist"
```

### 构建函数分离
```bash
# 管理端前端构建
build_admin_frontend() {
    cd frontend
    npm install
    npm run build
    # 检查 frontend/build
}

# 用户端前端构建
build_user_frontend() {
    cd user-frontend
    npm install
    npm run build
    # 检查 user-frontend/dist
}
```

### 部署函数分离
```bash
# 管理端前端部署
deploy_admin_frontend() {
    # 部署到 /home/<USER>/m.zoombus.com/dist
    scp -r frontend/build/* $TARGET_SERVER:$ADMIN_FRONTEND_TARGET_DIR/
}

# 用户端前端部署
deploy_user_frontend() {
    # 部署到 /home/<USER>/zoombus.com/dist
    scp -r user-frontend/dist/* $TARGET_SERVER:$USER_FRONTEND_TARGET_DIR/
}
```

## 🚀 使用方式

### 1. 完整部署脚本 (deploy.sh)

#### 交互式部署
```bash
./deploy.sh
```
然后选择部署模式 (1-6)

#### 部署选项说明
- **选项1**: 完整部署 - 适合首次部署或重大更新
- **选项2**: 仅后端 - 适合后端代码更新
- **选项3**: 仅管理端前端 - 适合管理界面更新
- **选项4**: 仅用户端前端 - 适合用户界面更新
- **选项5**: 仅前端 - 适合前端代码更新
- **选项6**: 自定义 - 灵活选择需要部署的组件

### 2. 用户端前端快速部署脚本

#### 交互式部署
```bash
./deploy-user-frontend.sh
```

#### 快速部署（跳过确认）
```bash
./deploy-user-frontend.sh --quick
# 或
./deploy-user-frontend.sh -q
```

## 📊 部署目录结构

### 服务器目录布局
```
/home/<USER>/
├── m.zoombus.com/
│   └── dist/           # 管理端前端 (React)
│       ├── index.html
│       ├── static/
│       └── ...
└── zoombus.com/
    └── dist/           # 用户端前端 (Vue)
        ├── index.html
        ├── assets/
        └── ...

/root/zoombus/
├── zoombus-1.0.0.jar   # 后端应用
├── zoombus.log         # 应用日志
└── zoombus.pid         # 进程ID
```

### 本地项目结构
```
zoombus/
├── frontend/           # 管理端前端 (React)
│   ├── build/         # 构建输出
│   └── package.json
├── user-frontend/      # 用户端前端 (Vue)
│   ├── dist/          # 构建输出
│   └── package.json
├── target/            # 后端构建输出
│   └── zoombus-1.0.0.jar
├── deploy.sh          # 完整部署脚本
└── deploy-user-frontend.sh  # 用户端快速部署
```

## 🔍 部署流程详解

### 完整部署流程 (选项1)
1. **环境检查** - Java 11, Node.js, SSH连接
2. **清理构建** - 删除旧的构建文件
3. **构建后端** - Maven编译打包
4. **构建管理端前端** - React构建
5. **构建用户端前端** - Vue构建
6. **部署后端** - 上传jar文件
7. **部署管理端前端** - 上传到m.zoombus.com/dist
8. **部署用户端前端** - 上传到zoombus.com/dist
9. **重启服务** - 重启Spring Boot应用
10. **显示信息** - 显示部署结果

### 用户端前端快速部署流程
1. **环境检查** - Node.js, SSH连接
2. **清理构建** - 删除user-frontend/dist
3. **构建前端** - Vue构建
4. **部署前端** - 上传到zoombus.com/dist
5. **验证部署** - 检查文件完整性

## 🛡️ 安全特性

### 备份机制
- 自动备份现有文件
- 时间戳命名: `.backup.20250729_160000`
- 支持快速回滚

### 验证机制
- 构建结果验证
- 上传文件验证
- 服务启动验证

### 错误处理
- 立即退出机制 (`set -e`)
- 详细错误日志
- 回滚建议

## 📋 Nginx配置建议

### 管理端配置 (m.zoombus.com)
```nginx
server {
    listen 80;
    server_name m.zoombus.com;
    root /home/<USER>/m.zoombus.com/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 用户端配置 (zoombus.com)
```nginx
server {
    listen 80;
    server_name zoombus.com www.zoombus.com;
    root /home/<USER>/zoombus.com/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 💡 最佳实践

### 日常开发部署
```bash
# 开发管理端功能
./deploy.sh  # 选择3 (仅管理端前端)

# 开发用户端功能
./deploy-user-frontend.sh --quick

# 开发后端功能
./deploy.sh  # 选择2 (仅后端)
```

### 生产环境部署
```bash
# 首次部署
./deploy.sh  # 选择1 (完整部署)

# 版本更新
./deploy.sh  # 选择1 (完整部署)

# 紧急修复
./deploy.sh  # 选择对应组件
```

### 部署验证
```bash
# 检查服务状态
ssh <EMAIL> 'pgrep -f zoombus-1.0.0.jar'

# 检查日志
ssh <EMAIL> 'tail -f /root/zoombus/zoombus.log'

# 检查前端文件
ssh <EMAIL> 'ls -la /home/<USER>/zoombus.com/dist/'
ssh <EMAIL> 'ls -la /home/<USER>/m.zoombus.com/dist/'
```

## 🎯 优化效果

### 部署效率提升
- ✅ **选择性部署**: 只部署需要的组件
- ✅ **快速部署**: 用户端前端1分钟内完成
- ✅ **并行构建**: 支持独立构建各组件

### 运维便利性
- ✅ **清晰的目录结构**: 管理端和用户端分离
- ✅ **自动备份**: 支持快速回滚
- ✅ **详细日志**: 便于问题排查

### 开发体验
- ✅ **灵活部署**: 6种部署模式满足不同需求
- ✅ **快速迭代**: 支持单独部署前端或后端
- ✅ **错误提示**: 清晰的错误信息和解决建议

## 🎉 总结

部署脚本优化已完成！现在您拥有：

1. **完整的部署解决方案**: deploy.sh支持6种部署模式
2. **专用快速部署**: deploy-user-frontend.sh专门用于用户端
3. **正确的目录映射**: user-frontend → /home/<USER>/zoombus.com/dist
4. **完善的错误处理**: 自动备份、验证、回滚机制
5. **灵活的使用方式**: 交互式和快速模式

现在可以高效、安全地部署ZoomBus的双前端架构！🚀
