# ZoomBus 部署指南

本文档介绍如何使用提供的脚本将ZoomBus应用部署到生产服务器。

## 部署脚本说明

### 1. deploy.sh - 完整部署脚本
功能最全面的部署脚本，包含详细的环境检查、构建、部署和服务管理。

**特性：**
- 完整的环境检查（Java、Maven、Node.js、SSH连接）
- 详细的日志输出和错误处理
- 自动备份现有文件
- 服务重启和状态检查
- 部署确认提示

**使用方法：**
```bash
./deploy.sh
```

### 2. quick-deploy.sh - 快速部署脚本
简化版本的部署脚本，适合日常快速部署。

**特性：**
- 快速构建和部署
- 简洁的输出信息
- 自动服务重启
- 基本的状态检查

**使用方法：**
```bash
./quick-deploy.sh
```

### 3. server-control.sh - 服务器端控制脚本
用于在目标服务器上管理ZoomBus服务的脚本。

**特性：**
- 启动/停止/重启服务
- 查看服务状态
- 实时日志查看
- PID文件管理

**使用方法：**
```bash
# 将脚本上传到服务器
scp server-control.sh <EMAIL>:/root/zoombus/

# 在服务器上使用
ssh <EMAIL>
cd /root/zoombus
chmod +x server-control.sh

# 控制服务
./server-control.sh start    # 启动服务
./server-control.sh stop     # 停止服务
./server-control.sh restart  # 重启服务
./server-control.sh status   # 查看状态
./server-control.sh logs     # 查看日志
```

## 部署配置

### 目标服务器配置
- **服务器地址：** <EMAIL>
- **后端部署路径：** /root/zoombus
- **前端部署路径：** /home/<USER>/m.zoombus.com/dist
- **JAR文件名：** zoombus-1.0.0.jar

### 前置条件

#### 本地环境要求
- Java 11 或更高版本
- Maven 3.6+ 或使用项目自带的Maven Wrapper
- Node.js 16 或更高版本
- SSH客户端和SFTP支持

#### 服务器环境要求
- Java 11 运行时环境
- 已配置SSH证书信任（无需密码登录）
- 目标目录的写入权限

#### SSH配置验证
确保可以无密码连接到目标服务器：
```bash
ssh <EMAIL> "echo 'SSH连接测试成功'"
```

## 部署流程

### 标准部署流程
1. **环境检查** - 验证本地构建环境和SSH连接
2. **清理构建** - 清理之前的构建文件
3. **构建后端** - 使用Maven编译Java项目为JAR包
4. **构建前端** - 使用npm构建React应用
5. **部署后端** - 通过SFTP上传JAR文件到服务器
6. **部署前端** - 通过SFTP上传前端静态文件
7. **重启服务** - 停止旧服务并启动新服务

### 快速部署流程
1. **构建项目** - 同时构建前后端
2. **部署文件** - 上传到目标服务器
3. **重启服务** - 自动重启应用服务

## 故障排除

### 常见问题

#### 1. SSH连接失败
```bash
# 检查SSH配置
ssh -v <EMAIL>

# 确保SSH密钥已添加
ssh-add -l
```

#### 2. Java版本不匹配
```bash
# 检查本地Java版本
java -version

# 检查服务器Java版本
ssh <EMAIL> "java -version"
```

#### 3. 构建失败
```bash
# 清理并重新构建
./mvnw clean
./mvnw package -DskipTests

# 前端构建问题
cd frontend
rm -rf node_modules package-lock.json
npm install
npm run build
```

#### 4. 服务启动失败
```bash
# 查看服务器日志
ssh <EMAIL> "tail -f /root/zoombus/zoombus.log"

# 检查端口占用
ssh <EMAIL> "netstat -tlnp | grep :8080"

# 手动启动服务进行调试
ssh <EMAIL> "cd /root/zoombus && java -jar zoombus-1.0.0.jar"
```

### 日志查看

#### 本地构建日志
- Maven构建日志会直接输出到控制台
- npm构建日志在 `frontend/` 目录下

#### 服务器运行日志
```bash
# 实时查看日志
ssh <EMAIL> "tail -f /root/zoombus/zoombus.log"

# 查看最近的日志
ssh <EMAIL> "tail -100 /root/zoombus/zoombus.log"

# 搜索错误日志
ssh <EMAIL> "grep -i error /root/zoombus/zoombus.log"
```

## 安全注意事项

1. **SSH密钥管理** - 定期更新SSH密钥，确保密钥安全
2. **服务器访问** - 限制对生产服务器的访问权限
3. **备份策略** - 部署脚本会自动备份，但建议定期进行完整备份
4. **日志管理** - 定期清理和归档日志文件

## 自动化建议

### 设置别名
在 `~/.bashrc` 或 `~/.zshrc` 中添加：
```bash
alias zb-deploy='cd /path/to/zoombus && ./quick-deploy.sh'
alias zb-logs='ssh <EMAIL> "tail -f /root/zoombus/zoombus.log"'
alias zb-status='ssh <EMAIL> "/root/zoombus/server-control.sh status"'
```

### CI/CD集成
这些脚本可以集成到CI/CD流水线中：
```yaml
# GitHub Actions 示例
- name: Deploy to Production
  run: |
    chmod +x ./quick-deploy.sh
    ./quick-deploy.sh
```

## 联系支持

如果在部署过程中遇到问题，请检查：
1. 本文档的故障排除部分
2. 服务器日志文件
3. 网络连接和SSH配置

部署成功后，可以通过配置的域名访问应用。
