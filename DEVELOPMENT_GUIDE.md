# ZoomBus 开发指南

## 开发环境启动

### 推荐方式：使用开发脚本

```bash
# 给脚本执行权限
chmod +x dev-start.sh

# 启动开发环境
./dev-start.sh
```

这个脚本会：
1. 自动检查环境要求（Java、Maven、Node.js、npm）
2. 启动后端服务（端口8080）
3. 启动前端开发服务器（端口3000）
4. 显示所有服务的访问地址

### 手动启动方式

如果需要分别启动前后端：

#### 启动后端
```bash
# 在项目根目录
./mvnw spring-boot:run
# 或者
mvn spring-boot:run
```

#### 启动前端
```bash
# 在frontend目录
cd frontend
npm install  # 首次运行或依赖更新时
npm start
```

## 开发环境访问地址

- **前端应用**: http://localhost:3000 （推荐用于开发）
- **后端API**: http://localhost:8080
- **H2数据库控制台**: http://localhost:8080/h2-console
- **健康检查**: http://localhost:8080/actuator/health

## 开发特性

### 前端开发特性
- ✅ 热重载：代码修改后自动刷新页面
- ✅ 错误覆盖：编译错误会在浏览器中显示
- ✅ 源码映射：便于调试
- ✅ API代理：自动将/api请求代理到后端
- ✅ ESLint集成：代码质量检查

### 后端开发特性
- ✅ 热重载：使用Spring Boot DevTools
- ✅ 自动重启：代码修改后自动重启应用
- ✅ 数据库自动更新：JPA DDL自动更新
- ✅ 详细日志：开发环境下显示SQL和调试信息

## API代理配置

前端开发服务器会自动将以下请求代理到后端：
- `/api/*` → `http://localhost:8080/api/*`
- `/actuator/*` → `http://localhost:8080/actuator/*`
- `/h2-console/*` → `http://localhost:8080/h2-console/*`

这意味着在前端代码中，你可以直接使用相对路径：
```javascript
// 这会自动代理到 http://localhost:8080/api/users
axios.get('/api/users')
```

## 环境变量

### 前端环境变量
开发环境变量在 `frontend/.env.development` 中配置：
- `PORT=3000` - 前端服务端口
- `BROWSER=none` - 禁用自动打开浏览器
- `REACT_APP_API_BASE_URL` - API基础URL

### 后端环境变量
可以通过以下方式设置：
1. 系统环境变量
2. `application-dev.yml` 配置文件
3. IDE运行配置

## 数据库配置

### 开发环境（默认）
- 数据库：MySQL
- 地址：localhost:3306
- 数据库名：zoombusV
- 用户名：root
- 密码：nvshen2018

### H2控制台访问
- URL: http://localhost:8080/h2-console
- JDBC URL: 根据控制台显示的URL
- 用户名：sa
- 密码：（空）

## 调试技巧

### 前端调试
1. 使用浏览器开发者工具
2. React DevTools扩展
3. 网络面板查看API请求
4. Console面板查看日志

### 后端调试
1. IDE断点调试
2. 查看控制台日志
3. 使用Actuator端点监控
4. H2控制台查看数据库状态

## 常见问题

### 端口冲突
如果3000或8080端口被占用：
```bash
# 查看端口占用
lsof -i :3000
lsof -i :8080

# 杀死占用进程
kill -9 <PID>
```

### 前端代理失败
检查后端是否正常启动：
```bash
curl http://localhost:8080/actuator/health
```

### 依赖问题
清理并重新安装依赖：
```bash
# 前端
cd frontend
rm -rf node_modules package-lock.json
npm install

# 后端
./mvnw clean install
```

## 生产环境构建

### 构建前端
```bash
cd frontend
npm run build
```

### 构建完整应用
```bash
# 使用Maven构建（会自动构建前端）
./mvnw clean package

# 或使用Docker
docker build -t zoombus .
```

## 代码规范

### 前端
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 组件使用函数式组件和Hooks
- 使用Ant Design组件库

### 后端
- 遵循Spring Boot最佳实践
- 使用Lombok减少样板代码
- 使用JPA进行数据访问
- 使用全局异常处理

## 提交代码前检查

1. 前端代码通过ESLint检查
2. 后端代码编译无错误
3. 单元测试通过
4. 功能测试正常
5. 代码已格式化

## 开发工具和脚本

### 可用脚本
- `./quick-dev.sh` - 快速启动开发环境
- `./dev-start.sh` - 详细的开发环境启动（带检查）
- `./test-dev-env.sh` - 测试开发环境是否正常
- `frontend/start-dev.sh` - 仅启动前端开发服务器

### 环境测试
```bash
# 测试开发环境是否正常
./test-dev-env.sh
```

### 日志查看
```bash
# 查看实时日志（如果使用quick-dev.sh启动）
tail -f backend.log frontend.log

# 查看后端日志
./mvnw spring-boot:run | tee backend.log

# 查看前端日志
cd frontend && npm start | tee ../frontend.log
```

## 获取帮助

如果遇到问题：
1. 运行 `./test-dev-env.sh` 检查环境状态
2. 查看控制台错误信息
3. 检查网络请求状态（浏览器开发者工具）
4. 查看后端日志文件
5. 参考项目文档和开发指南

### 常见问题解决
- **端口冲突**: 使用 `lsof -i :3000 -i :8080` 查看占用
- **依赖问题**: 删除 `node_modules` 重新安装
- **代理失败**: 确认后端服务正常启动
- **热重载失败**: 重启前端开发服务器
