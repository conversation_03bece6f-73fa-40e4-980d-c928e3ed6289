# ZoomBus 双前端架构设置完成

## 🎉 功能完成总结

我已经成功为ZoomBus项目实现了双前端架构，现在项目包含两个独立的前端子项目：

### 1. 管理端前端 (`frontend/`)
- **端口**: 3000
- **用途**: 管理员使用，需要登录
- **技术栈**: React + Ant Design + Create React App
- **功能**: 用户管理、PMI管理、会议管理、系统监控等

### 2. 用户端前端 (`user-frontend/`)
- **端口**: 3001 (开发环境)
- **用途**: 终端用户使用，无需登录
- **技术栈**: React + Ant Design + Vite
- **功能**: PMI使用页面，一键开启PMI功能

## 🚀 启动方式

### 开发环境启动
```bash
./start.sh
# 选择 "1. 开发模式 (前后端分离启动)"
```

这将同时启动：
- 后端服务 (端口8080)
- 管理端前端 (端口3000)
- 用户端前端 (端口3001)

### 其他启动选项
```bash
./start.sh
```
然后选择：
- `1` - 开发模式 (同时启动后端和两个前端)
- `2` - 生产模式 (Docker Compose)
- `3` - 仅启动后端
- `4` - 仅启动管理端前端
- `5` - 仅启动用户端前端
- `6` - 仅启动两个前端

## 📍 访问地址

### 开发环境
- **管理端前端**: http://localhost:3000
- **用户端前端**: http://localhost:3001
- **后端API**: http://localhost:8080

### 生产环境
- **管理端前端**: http://localhost:8080 (默认路由)
- **用户端前端**: http://localhost:8080/m
- **特定PMI页面**: http://localhost:8080/m/{pmiNumber}

## 🛠️ 构建和部署

### 构建用户前端
```bash
./build-user-frontend.sh
```
或者手动构建：
```bash
cd user-frontend
npm run build
```

构建文件会自动输出到 `src/main/resources/static-user/`

### 构建管理端前端
```bash
cd frontend
npm run build
```

## 📁 项目结构

```
zoombus/
├── frontend/                    # 管理端前端
│   ├── src/
│   ├── package.json
│   └── ...
├── user-frontend/              # 用户端前端
│   ├── src/
│   │   ├── pages/
│   │   │   └── PmiUsage.jsx   # PMI使用页面
│   │   ├── services/
│   │   └── App.jsx
│   ├── vite.config.js
│   └── package.json
├── src/main/resources/
│   ├── static/                 # 管理端构建文件
│   └── static-user/           # 用户端构建文件
├── start.sh                   # 启动脚本
└── build-user-frontend.sh     # 用户前端构建脚本
```

## 🎯 用户端功能

### PMI使用页面特性
- ✅ 支持 `/m/{pmiNumber}` 直接访问特定PMI
- ✅ 支持 `/m` 访问PMI输入页面
- ✅ 无需登录即可使用
- ✅ 响应式设计，支持移动设备
- ✅ 一键开启PMI功能
- ✅ 会议信息复制功能
- ✅ 完善的错误处理

### 后端API支持
- ✅ 自动查找可用的LICENSED类型Zoom账号
- ✅ 自动配置PMI设置
- ✅ 生成主持人链接
- ✅ 复用现有的PublicPmiController API

## 🔧 开发提示

### 端口分配
- **8080**: 后端服务
- **3000**: 管理端前端开发服务器
- **3001**: 用户端前端开发服务器

### 热重载
- 两个前端项目都支持热重载
- 代码修改会自动刷新页面
- API请求会自动代理到后端

### 环境变量
- 管理端前端使用 `PORT=3000`
- 用户端前端在 `vite.config.js` 中配置端口3001
- 设置 `BROWSER=none` 防止自动打开浏览器

## 🚨 注意事项

1. **Java版本**: 确保使用Java 11或更高版本
2. **Node.js版本**: 确保使用Node.js 16或更高版本
3. **端口冲突**: 确保3000、3001、8080端口未被占用
4. **依赖安装**: 首次运行会自动安装依赖，可能需要一些时间

## 🎉 测试验证

启动完成后，可以通过以下方式验证：

1. 访问 http://localhost:3000 - 管理端前端
2. 访问 http://localhost:3001/m - 用户端前端
3. 访问 http://localhost:8080/actuator/health - 后端健康检查

用户端功能测试：
1. 在管理端创建一个PMI记录
2. 访问 http://localhost:3001/m/{pmiNumber} 测试特定PMI
3. 测试一键开启PMI功能

## 🔮 未来扩展

这个双前端架构为将来的功能扩展提供了良好的基础：
- 用户端可以添加登录和鉴权功能
- 可以为用户端添加更多功能页面
- 管理端和用户端可以独立部署和扩展
