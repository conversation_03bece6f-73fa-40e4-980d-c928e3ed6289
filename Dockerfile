# 多阶段构建
FROM node:16-alpine AS frontend-build

# 设置工作目录
WORKDIR /app/frontend

# 复制前端依赖文件
COPY frontend/package*.json ./

# 安装前端依赖
RUN npm ci --only=production

# 复制前端源码
COPY frontend/ ./

# 构建前端
RUN npm run build

# Java构建阶段
FROM maven:3.8.6-openjdk-11-slim AS backend-build

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件
COPY pom.xml ./

# 下载依赖
RUN mvn dependency:go-offline -B

# 复制源码
COPY src ./src

# 复制前端构建结果到静态资源目录
COPY --from=frontend-build /app/frontend/build ./src/main/resources/static

# 构建应用
RUN mvn clean package -DskipTests

# 运行阶段
FROM openjdk:11-jre-slim

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN groupadd -r zoombus && useradd -r -g zoombus zoombus

# 复制jar包
COPY --from=backend-build /app/target/zoombus-1.0.0.jar ./app.jar

# 更改文件所有者
RUN chown -R zoombus:zoombus /app

# 切换到非root用户
USER zoombus

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "app.jar"]
