# ZoomBus 最终部署指南

## 🎉 问题已完全解决！

经过修复，现在所有部署脚本都能正确使用Java 11，并且服务能稳定运行。

## ✅ 修复内容

### 1. Java 11环境自动检测
所有部署脚本现在都会自动检测并设置Java 11环境：
- **macOS**: 自动检测多个可能的Java 11路径
- **Linux**: 自动检测系统Java 11安装
- **验证**: 确保使用的是Java 11版本

### 2. 稳定的启动机制
创建了稳定的启动脚本 `stable_zoombus_start.sh`：
- ✅ 正确的进程管理（PID文件）
- ✅ 优化的JVM参数
- ✅ 详细的启动日志
- ✅ 自动错误检测和恢复

### 3. 修复的部署脚本
- ✅ `deploy.sh` - 完整部署脚本
- ✅ `quick-deploy.sh` - 快速部署脚本
- ✅ `deploy-with-config.sh` - 配置化部署脚本

## 🚀 使用方法

### 方法1: 快速部署（推荐）
```bash
./quick-deploy.sh
```

### 方法2: 完整部署
```bash
./deploy.sh
```

### 方法3: 配置化部署
```bash
./deploy-with-config.sh
```

### 方法4: 手动部署
```bash
# 1. 构建项目（自动使用Java 11）
./mvnw clean package -DskipTests

# 2. 部署JAR文件
scp target/zoombus-1.0.0.jar <EMAIL>:/root/zoombus/

# 3. 启动服务
ssh <EMAIL> "/root/zoombus/stable_zoombus_start.sh"
```

## 📊 验证结果

部署成功后运行验证：
```bash
./verify-deployment.sh
```

应该显示：
```
✅ SSH连接
✅ 后端文件
✅ 前端文件
✅ 服务运行
✅ 端口监听
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
验证结果: 5/5 项检查通过
🎉 部署验证完全通过!
```

## 🛠️ 服务管理

### 启动服务
```bash
ssh <EMAIL> "/root/zoombus/stable_zoombus_start.sh"
```

### 停止服务
```bash
ssh <EMAIL> "kill \$(cat /root/zoombus/zoombus.pid)"
```

### 查看状态
```bash
ssh <EMAIL> "ps aux | grep zoombus-1.0.0.jar"
ssh <EMAIL> "netstat -tlnp | grep :8080"
```

### 查看日志
```bash
ssh <EMAIL> "tail -f /root/zoombus/zoombus.log"
```

### 重启服务
```bash
ssh <EMAIL> "/root/zoombus/stable_zoombus_start.sh"
```

## 🔧 技术细节

### Java 11自动检测逻辑
脚本会按以下顺序查找Java 11：

**macOS:**
1. `/Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home`
2. `/Library/Java/JavaVirtualMachines/jdk-11*/Contents/Home`
3. `/Library/Java/JavaVirtualMachines/openjdk-11*/Contents/Home`
4. 使用 `/usr/libexec/java_home -v 11`

**Linux:**
1. `/usr/lib/jvm/java-11-openjdk`
2. `/usr/lib/jvm/java-11-openjdk-amd64`
3. `/usr/lib/jvm/jdk-11`

### JVM优化参数
稳定启动脚本使用以下JVM参数：
```bash
-Xms512m -Xmx1024m                    # 内存设置
-Dspring.profiles.active=production   # 生产环境配置
-Dserver.port=8080                    # 端口设置
-Djava.awt.headless=true              # 无头模式
```

### 进程管理
- **PID文件**: `/root/zoombus/zoombus.pid`
- **日志文件**: `/root/zoombus/zoombus.log`
- **自动清理**: 启动前自动停止旧进程

## 🎯 当前状态

- ✅ ZoomBus应用正常运行在8080端口
- ✅ 使用Java 11启动
- ✅ 前端文件正确部署到 `/home/<USER>/m.zoombus.com/dist`
- ✅ 后端JAR文件部署到 `/root/zoombus`
- ✅ 所有验证检查通过
- ✅ 部署脚本已修复并可重复使用

## 💡 最佳实践

1. **部署前检查**: 确保本地有Java 11环境
2. **使用快速部署**: 日常开发使用 `./quick-deploy.sh`
3. **监控日志**: 部署后查看应用日志确认正常启动
4. **验证部署**: 每次部署后运行 `./verify-deployment.sh`
5. **服务管理**: 使用提供的管理命令操作服务

## 🔗 相关文件

- `deploy.sh` - 完整部署脚本
- `quick-deploy.sh` - 快速部署脚本
- `deploy-with-config.sh` - 配置化部署脚本
- `verify-deployment.sh` - 部署验证脚本
- `stable_zoombus_start.sh` - 稳定启动脚本（服务器上）
- `create-stable-startup.sh` - 创建稳定启动脚本的工具

现在您可以正常访问 `https://m.zoombus.com/api/auth/login` 等API端点了！🎯
