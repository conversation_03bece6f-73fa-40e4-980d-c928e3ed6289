# Java 11多版本支持安装指南

本指南将帮助您在CentOS 7服务器上安装Java 11，同时保持Java 1.8的兼容性，确保现有应用不受影响。

## 🎯 目标

- 在服务器上安装Java 11
- 配置多版本Java支持
- 保持Java 1.8作为系统默认版本
- 让ZoomBus应用使用Java 11运行
- 确保现有Java 1.8应用不受影响

## 📋 前置条件

- CentOS 7服务器
- root权限访问
- SSH证书信任已配置
- 当前Java 1.8.0_151正常运行

## 🚀 安装步骤

### 1. 安装Java 11

运行安装脚本：
```bash
./install-java11-server.sh
```

这个脚本会：
- 检查SSH连接
- 安装OpenJDK 11
- 配置alternatives系统
- 创建版本切换脚本
- 设置环境变量

### 2. 验证安装

运行测试脚本：
```bash
./test-java11-setup.sh
```

这个脚本会验证：
- Java 8和Java 11都已正确安装
- alternatives配置正确
- 版本切换脚本可用
- 系统资源充足

### 3. 部署ZoomBus应用

使用更新后的部署脚本：
```bash
./quick-deploy.sh
```

或者使用完整部署脚本：
```bash
./deploy.sh
```

### 4. 验证部署

运行验证脚本：
```bash
./verify-deployment.sh
```

## 🔧 Java版本管理

### 在服务器上切换Java版本

```bash
# 切换到Java 8（默认）
switch-java 8

# 切换到Java 11
switch-java 11

# 查看当前版本
java -version
```

### 直接使用特定Java版本

```bash
# 使用Java 8
/usr/lib/jvm/java-1.8.0-openjdk*/bin/java -version

# 使用Java 11
/usr/lib/jvm/java-11-openjdk*/bin/java -version
```

### 环境变量

系统会自动设置以下环境变量：
- `JAVA11_HOME`: Java 11安装路径
- `PATH11`: 包含Java 11的PATH

## 📁 文件结构

安装完成后的文件结构：
```
/usr/lib/jvm/
├── java-1.8.0-openjdk-*     # Java 8
└── java-11-openjdk-*        # Java 11

/usr/local/bin/
└── switch-java              # 版本切换脚本

/etc/profile.d/
└── java11.sh               # Java 11环境变量

/root/zoombus/
├── zoombus-1.0.0.jar       # 应用JAR文件
├── zoombus.log             # 应用日志
└── server-control.sh       # 服务控制脚本（已更新）
```

## 🛠️ 服务管理

### 使用更新后的服务控制脚本

```bash
# 上传控制脚本到服务器
scp server-control.sh <EMAIL>:/root/zoombus/

# 在服务器上使用
ssh <EMAIL>
cd /root/zoombus

# 启动服务（自动使用Java 11）
./server-control.sh start

# 停止服务
./server-control.sh stop

# 重启服务
./server-control.sh restart

# 查看状态
./server-control.sh status

# 查看日志
./server-control.sh logs
```

### 手动启动（用于调试）

```bash
# 在服务器上手动启动ZoomBus
ssh <EMAIL>
cd /root/zoombus

# 使用Java 11启动
JAVA11_PATH=$(find /usr/lib/jvm -name "java-11-openjdk*" -type d | head -1)
$JAVA11_PATH/bin/java -jar zoombus-1.0.0.jar
```

## 🔍 故障排除

### 1. Java 11安装失败

```bash
# 检查yum源
yum list available | grep java-11-openjdk

# 手动安装
yum install -y java-11-openjdk java-11-openjdk-devel
```

### 2. alternatives配置问题

```bash
# 查看当前配置
alternatives --display java

# 重新配置
alternatives --config java
```

### 3. ZoomBus启动失败

```bash
# 检查Java版本
ssh <EMAIL> "java -version"

# 检查Java 11
ssh <EMAIL> "find /usr/lib/jvm -name 'java-11-openjdk*' -type d"

# 查看详细错误日志
ssh <EMAIL> "tail -50 /root/zoombus/zoombus.log"

# 手动测试启动
ssh <EMAIL> "/tmp/test_zoombus_java11.sh"
```

### 4. 现有应用受影响

```bash
# 确认默认Java版本
java -version

# 应该显示Java 1.8.0_151

# 如果不是，切换回Java 8
switch-java 8
```

## ⚠️ 重要注意事项

1. **系统默认Java保持1.8**: 确保现有应用兼容性
2. **ZoomBus专用Java 11**: 只有ZoomBus使用Java 11
3. **备份重要数据**: 安装前备份重要配置
4. **测试现有应用**: 安装后验证现有Java应用正常运行

## 📊 验证清单

安装完成后，请确认以下项目：

- [ ] SSH连接正常
- [ ] Java 8仍然是系统默认版本
- [ ] Java 11已正确安装
- [ ] alternatives配置正确
- [ ] 版本切换脚本可用
- [ ] ZoomBus可以使用Java 11启动
- [ ] 现有Java应用正常运行
- [ ] 端口8080可用
- [ ] 日志文件正常生成

## 🎉 完成

安装完成后，您可以：
1. 使用 `./quick-deploy.sh` 快速部署ZoomBus
2. 使用 `./verify-deployment.sh` 验证部署状态
3. 使用 `ssh <EMAIL> "/root/zoombus/server-control.sh status"` 检查服务状态

如果遇到问题，请参考故障排除部分或查看相关日志文件。
