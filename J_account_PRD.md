# Join Account Rental - Zoom参会者账号短租功能需求文档

## 1. 项目概述

### 1.1 项目背景
为了满足客户短期使用Zoom参会者账号的需求，开发一套Join Account Rental（参会账号租赁）系统，通过权益链接的方式为客户提供临时的Zoom参会者账号使用权限。

### 1.2 项目目标
- 实现Zoom参会者账号的自动化短期租赁
- 提供权益链接管理功能
- 确保账号使用的时间窗口管理和安全性
- 实现账号密码的自动化管理
- 支持通过系统配置管理客户访问域名

### 1.3 核心流程
1. 管理员通过系统配置设置域名并批量生成权益链接
2. 外部电商系统自动发货，将权益链接发送给客户
3. 客户通过链接进入账号领取页面
4. 客户选择使用日期并获取账号
5. 系统自动分配账号并管理使用窗口

## 2. 功能需求

### 2.1 Join Account Rental管理功能

#### 2.1.1 系统域名配置
**功能描述：** 管理员可以配置客户访问的域名

**配置项：**
- 基础域名（如：zoombus.cn）
- 是否启用HTTPS
- 链接路径前缀（默认：rf）

**业务规则：**
- 域名配置后立即生效
- 影响新生成的权益链接格式
- 已生成的链接仍然有效

#### 2.1.2 批量生成权益链接
**功能描述：** 管理员可以批量生成权益链接

**输入参数：**
- 使用天数（必填，正整数）
- 生成数量（必填，正整数，建议限制最大值）
- 备注信息（可选）

**业务规则：**
- 自动生成批次号（格式：JAR_BATCH_YYYYMMDD_HHMMSS）
- tokenNumber生成规则：6位数字字母组合，字母大写，数字不含4
- 权益链接格式：`{配置域名}/rf/{tokenNumber}`（如：https://zoombus.cn/rf/{tokenNumber}）
- 每个tokenNumber全局唯一
- 生成后不可修改使用天数

**输出结果：**
- 生成成功的权益链接列表
- 批次号
- 生成时间
- 可导出Excel文件

#### 2.1.3 权益链接查询管理
**功能描述：** 查询和管理已生成的权益链接

**查询条件：**
- 批次号
- tokenNumber
- 生成时间范围
- 权益状态（待使用/已预约/使用中/已完成/已作废）
- 导出状态（未导出/已导出）
- 使用天数

**显示信息：**
- tokenNumber
- 权益链接
- 使用天数
- 生成时间
- 权益状态（待使用/已预约/使用中/已完成/已作废）
- 导出状态（未导出/已导出）
- 导出时间（如已导出）
- 预约时间（如已预约）
- 分配的账号邮箱（如已分配，可点击链接查看详情）
- 分配的密码（如已分配）
- 使用窗口（开始时间-结束时间）

**批量操作：**
- 批量导出权益链接
- 批量作废选中记录

#### 2.1.4 权益链接导出功能
**功能描述：** 导出权益链接供外部电商平台使用

**导出格式：**
- 单列Excel文件，列名：权益链接
- 每行一个完整的权益链接URL
- 示例：https://zoombus.cn/rf/ABC123

**导出流程：**
1. 管理员选择需要导出的权益记录（支持批量选择）
2. 点击"导出权益链接"按钮
3. 系统生成Excel文件并下载
4. 导出成功后，自动更新记录状态为"已导出"
5. 记录导出时间和操作人

**业务规则：**
- 只能导出状态为"待使用"的权益记录
- 已导出的记录不能重复导出
- 导出后状态不可逆转
- 支持按批次号批量导出

#### 2.1.5 权益记录作废功能
**功能描述：** 作废无效或错误的权益记录

**作废条件：**
- 只能作废状态为"待使用"或"已导出"的记录
- 已预约、使用中、已完成的记录不能作废

**作废流程：**
1. 管理员选择需要作废的权益记录
2. 点击"作废"按钮
3. 弹出确认对话框："确认作废选中的权益记录？作废后不可恢复"
4. 确认后更新记录状态为"已作废"
5. 记录作废时间和操作人

**作废影响：**
- 权益链接立即失效，客户无法访问
- 如已分配账号，自动释放账号资源
- 如有使用窗口，自动取消窗口安排

### 2.4 使用窗口查询功能

#### 2.4.1 窗口记录查询
**功能描述：** 查询和管理账号使用窗口记录

**查询条件：**
- Zoom账号ID/邮箱
- 关联的Token编号
- 窗口时间范围（开始时间、结束时间）
- 窗口状态（待开启/使用中/已关闭）
- 创建时间范围

**显示信息：**
- 窗口ID
- Zoom账号邮箱（可点击链接查看账号详情）
- 关联Token编号（可点击链接查看权益详情）
- 计划窗口时间（开始时间-结束时间）
- 实际执行时间（实际开启时间、实际关闭时间）
- 窗口状态
- 创建时间

**操作功能：**
- 查看窗口详情
- 导出窗口记录
- 按条件筛选和排序

### 2.5 密码变更日志查询功能

#### 2.5.1 密码变更记录查询
**功能描述：** 查询和管理密码变更历史记录

**查询条件：**
- Zoom账号ID/邮箱
- 变更时间范围
- 变更类型（窗口开启/窗口关闭/手动变更）
- 操作人
- 关联窗口ID

**显示信息：**
- 变更记录ID
- Zoom账号邮箱（可点击链接查看账号详情）
- 旧密码（脱敏显示，如：Aa****67）
- 新密码（脱敏显示，如：Aa****23）
- 变更类型
- 关联窗口ID（如有，可点击查看窗口详情）
- 变更时间
- 操作人

**安全特性：**
- 密码脱敏显示，只显示前2位和后2位
- 完整密码仅超级管理员可查看
- 操作日志记录查看行为

**操作功能：**
- 查看变更详情
- 导出变更记录
- 按条件筛选和排序

### 2.2 权益状态管理

#### 2.2.1 权益状态定义
**状态流转图：**
```
待使用 ──┬─→ 已导出 ──→ 已预约 ──→ 使用中 ──→ 已完成
         │                ↑
         └────────────────┘
         │
         ↓
       已作废 ←─── 已导出
```

**状态说明：**
- **待使用**：权益记录已生成，可以直接使用或先导出
- **已导出**：权益链接已导出到Excel文件，准备上架电商平台（可选状态）
- **已预约**：客户已选择使用日期，系统已分配账号和密码
- **使用中**：使用窗口已开启，账号密码已激活
- **已完成**：使用窗口已关闭，权益使用完毕
- **已作废**：权益记录被管理员作废，链接失效

**关键修正：**
- "已导出"不是必须经过的状态，客户可以直接从"待使用"状态预约
- 支持两种使用路径：导出后使用 和 直接使用

#### 2.2.2 状态变更规则
**自动状态变更：**
- 导出操作：待使用 → 已导出
- 客户预约：待使用 → 已预约 或 已导出 → 已预约
- 窗口开启：已预约 → 使用中
- 窗口关闭：使用中 → 已完成

**手动状态变更：**
- 管理员作废：待使用/已导出 → 已作废

**状态验证规则：**
- 只有"待使用"和"已导出"状态的权益可以被预约
- 只有"待使用"状态的权益可以被导出
- 只有"待使用"和"已导出"状态的权益可以被作废

### 2.3 客户端账号领取功能

#### 2.3.1 权益链接访问
**访问地址：** `{配置域名}/rf/{tokenNumber}`（如：https://zoombus.cn/rf/{tokenNumber}）

**页面验证：**
- 验证tokenNumber有效性
- 检查权益状态（只允许"待使用"或"已导出"状态访问）
- 显示使用天数信息

#### 2.3.2 使用日期选择
**功能描述：** 用户选择账号开始使用日期

**交互规则：**
- 提供日历组件选择开始日期
- 开始日期不能早于当前日期
- 开始日期不能超过系统配置的预约限制天数
- 根据tokenNumber预定义的使用天数自动计算结束日期
- 不允许手动修改结束日期
- 显示使用期限：开始日期 - 结束日期

**预约时间验证：**
- 检查：开始日期 - 当前日期 ≤ 系统配置的max_advance_days
- 超出限制时显示错误提示："系统当前仅支持预约{max_advance_days}天内的使用时段"
- 日历组件禁用超出限制的日期

**日期计算规则：**
```
结束日期 = 开始日期 + 使用天数 - 1天
例：开始日期2024-01-01，使用天数3天，结束日期2024-01-03
```

#### 2.3.3 账号获取确认
**功能描述：** 用户确认获取账号

**确认流程：**
1. 显示确认信息：使用期限、注意事项
2. 用户点击"获取账号"按钮
3. 弹出二次确认："确认后不可修改，是否继续？"
4. 用户确认后发送请求到后端
5. 后端处理成功后，权益状态更新为"已预约"

**注意事项提示：**
- 账号仅在指定时间段内有效
- 确认后无法修改使用时间
- 请妥善保管账号密码
- 账号仅供参会使用

#### 2.3.4 账号信息展示
**功能描述：** 展示分配的账号信息

**显示内容：**
- Zoom账号邮箱
- 账号密码（Aa+6位不含4的数字）
- 使用期限
- 使用说明

**密码生成规则：**
- 格式：Aa + 6位数字
- 数字范围：0-9，不包含4
- 示例：Aa123567、Aa890123

**密码记录：**
- 生成的密码同时记录到权益记录中
- 管理员可在后台查看分配的密码
- 密码变更日志完整记录

## 3. 后端业务逻辑

### 3.1 账号分配算法

#### 3.1.1 可用账号筛选条件
```sql
SELECT * FROM t_zoom_accounts 
WHERE user_type = 'BASIC' 
  AND account_usage = 'PUBLIC_MEETING'
  AND status = 'ACTIVE'
```

#### 3.1.2 智能账号选择算法（负载均衡优化版）
**目标：** 综合考虑时间距离、负载均衡和历史使用情况，选择最优账号

**算法策略：** 多因子综合评分算法，平衡时间冲突避免和负载均衡

**算法步骤：**
1. 确定本次需要安排的时间窗口：[start_time, end_time]
2. 筛选所有符合条件的参会者账号
3. 对每个账号计算综合评分
4. 选择评分最高的账号

**综合评分公式：**
```
总评分 = 时间距离分数 × 0.4 + 负载均衡分数 × 0.4 + 历史使用分数 × 0.2
```

**各项分数计算：**

**1. 时间距离分数（0-100分）：**
```sql
-- 查找每个账号大于等于当前窗口开始时间的最近窗口
WITH next_windows AS (
    SELECT zoom_user_id, MIN(start_time) as next_window_start
    FROM t_join_account_usage_windows
    WHERE start_time >= #{currentWindowStartTime}
      AND status IN ('PENDING', 'ACTIVE')
    GROUP BY zoom_user_id
)
-- 时间距离分数计算
CASE
    WHEN next_window_start IS NULL THEN 100  -- 无未来窗口，满分
    WHEN TIMESTAMPDIFF(HOUR, #{currentWindowEndTime}, next_window_start) >= 24 THEN 90
    WHEN TIMESTAMPDIFF(HOUR, #{currentWindowEndTime}, next_window_start) >= 12 THEN 70
    WHEN TIMESTAMPDIFF(HOUR, #{currentWindowEndTime}, next_window_start) >= 6 THEN 50
    WHEN TIMESTAMPDIFF(HOUR, #{currentWindowEndTime}, next_window_start) >= 2 THEN 30
    ELSE 0  -- 时间间隔太短
END as time_distance_score
```

**2. 负载均衡分数（0-100分）：**
```sql
-- 计算账号在未来30天内的窗口数量
WITH account_load AS (
    SELECT
        zoom_user_id,
        COUNT(*) as future_window_count
    FROM t_join_account_usage_windows
    WHERE start_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY)
      AND status IN ('PENDING', 'ACTIVE')
    GROUP BY zoom_user_id
),
load_stats AS (
    SELECT
        AVG(future_window_count) as avg_load,
        MAX(future_window_count) as max_load
    FROM account_load
)
-- 负载均衡分数计算
CASE
    WHEN al.future_window_count IS NULL THEN 100  -- 无负载，满分
    WHEN al.future_window_count <= ls.avg_load * 0.5 THEN 90
    WHEN al.future_window_count <= ls.avg_load THEN 70
    WHEN al.future_window_count <= ls.avg_load * 1.5 THEN 50
    ELSE 20  -- 负载过高
END as load_balance_score
```

**3. 历史使用分数（0-100分）：**
```sql
-- 计算账号历史使用情况（最近90天）
WITH usage_history AS (
    SELECT
        zoom_user_id,
        COUNT(*) as total_usage,
        AVG(TIMESTAMPDIFF(MINUTE, start_time, end_time)) as avg_duration,
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) / COUNT(*) as success_rate
    FROM t_join_account_usage_windows
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY zoom_user_id
)
-- 历史使用分数计算（使用频率适中、成功率高的账号得分高）
CASE
    WHEN uh.total_usage IS NULL THEN 80  -- 新账号，中等分数
    WHEN uh.success_rate >= 0.95 AND uh.total_usage BETWEEN 5 AND 20 THEN 100
    WHEN uh.success_rate >= 0.90 AND uh.total_usage BETWEEN 3 AND 30 THEN 85
    WHEN uh.success_rate >= 0.85 THEN 70
    ELSE 50  -- 成功率较低或使用过于频繁
END as history_score
```

**最终选择逻辑：**
1. 排除与本次窗口时间重叠的账号
2. 排除状态异常的账号
3. 计算所有可用账号的综合评分
4. 选择评分最高的账号
5. 如果评分相同，优先选择ID较小的账号（保证结果稳定性）

**完整SQL示例：**
```sql
-- 智能账号选择的完整SQL
WITH available_accounts AS (
    SELECT za.id, za.email
    FROM t_zoom_accounts za
    WHERE za.user_type = 'BASIC'
      AND za.account_usage = 'PUBLIC_MEETING'
      AND za.status = 'ACTIVE'
),
-- 时间距离计算
time_scores AS (
    SELECT
        aa.id,
        aa.email,
        CASE
            WHEN fw.next_window_start IS NULL THEN 100
            WHEN TIMESTAMPDIFF(HOUR, #{currentWindowEndTime}, fw.next_window_start) >= 24 THEN 90
            WHEN TIMESTAMPDIFF(HOUR, #{currentWindowEndTime}, fw.next_window_start) >= 12 THEN 70
            WHEN TIMESTAMPDIFF(HOUR, #{currentWindowEndTime}, fw.next_window_start) >= 6 THEN 50
            WHEN TIMESTAMPDIFF(HOUR, #{currentWindowEndTime}, fw.next_window_start) >= 2 THEN 30
            ELSE 0
        END as time_distance_score
    FROM available_accounts aa
    LEFT JOIN (
        SELECT zoom_user_id, MIN(start_time) as next_window_start
        FROM t_join_account_usage_windows
        WHERE start_time >= #{currentWindowStartTime}
          AND status IN ('PENDING', 'ACTIVE')
        GROUP BY zoom_user_id
    ) fw ON aa.id = fw.zoom_user_id
    WHERE NOT EXISTS (
        SELECT 1 FROM t_join_account_usage_windows w2
        WHERE w2.zoom_user_id = aa.id
          AND w2.status IN ('PENDING', 'ACTIVE')
          AND w2.start_time < #{currentWindowEndTime}
          AND w2.end_time > #{currentWindowStartTime}
    )
),
-- 负载均衡计算
load_scores AS (
    SELECT
        ts.id,
        ts.email,
        ts.time_distance_score,
        CASE
            WHEN al.future_window_count IS NULL THEN 100
            WHEN al.future_window_count <= COALESCE(ls.avg_load * 0.5, 1) THEN 90
            WHEN al.future_window_count <= COALESCE(ls.avg_load, 2) THEN 70
            WHEN al.future_window_count <= COALESCE(ls.avg_load * 1.5, 3) THEN 50
            ELSE 20
        END as load_balance_score
    FROM time_scores ts
    LEFT JOIN (
        SELECT zoom_user_id, COUNT(*) as future_window_count
        FROM t_join_account_usage_windows
        WHERE start_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY)
          AND status IN ('PENDING', 'ACTIVE')
        GROUP BY zoom_user_id
    ) al ON ts.id = al.zoom_user_id
    CROSS JOIN (
        SELECT AVG(future_window_count) as avg_load
        FROM (
            SELECT zoom_user_id, COUNT(*) as future_window_count
            FROM t_join_account_usage_windows
            WHERE start_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY)
              AND status IN ('PENDING', 'ACTIVE')
            GROUP BY zoom_user_id
        ) load_data
    ) ls
),
-- 历史使用评分
final_scores AS (
    SELECT
        ls.id,
        ls.email,
        ls.time_distance_score,
        ls.load_balance_score,
        CASE
            WHEN uh.total_usage IS NULL THEN 80
            WHEN uh.success_rate >= 0.95 AND uh.total_usage BETWEEN 5 AND 20 THEN 100
            WHEN uh.success_rate >= 0.90 AND uh.total_usage BETWEEN 3 AND 30 THEN 85
            WHEN uh.success_rate >= 0.85 THEN 70
            ELSE 50
        END as history_score,
        -- 综合评分计算
        (ls.time_distance_score * 0.4 +
         ls.load_balance_score * 0.4 +
         CASE
            WHEN uh.total_usage IS NULL THEN 80
            WHEN uh.success_rate >= 0.95 AND uh.total_usage BETWEEN 5 AND 20 THEN 100
            WHEN uh.success_rate >= 0.90 AND uh.total_usage BETWEEN 3 AND 30 THEN 85
            WHEN uh.success_rate >= 0.85 THEN 70
            ELSE 50
         END * 0.2) as total_score
    FROM load_scores ls
    LEFT JOIN (
        SELECT
            zoom_user_id,
            COUNT(*) as total_usage,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) / COUNT(*) as success_rate
        FROM t_join_account_usage_windows
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
        GROUP BY zoom_user_id
    ) uh ON ls.id = uh.zoom_user_id
)
SELECT id, email, total_score, time_distance_score, load_balance_score, history_score
FROM final_scores
ORDER BY total_score DESC, id ASC
LIMIT 1;
```

### 3.2 窗口管理机制

#### 3.2.1 窗口开启定时任务
**执行时机：** 每分钟检查是否有窗口需要开启

**执行逻辑：**
1. 查询当前时间需要开启的窗口
2. 调用Zoom API设置账号密码
3. 更新窗口状态为"已开启"
4. 记录密码变更日志

#### 3.2.2 窗口关闭定时任务
**执行时机：** 每分钟检查是否有窗口需要关闭

**执行逻辑：**
1. 查询当前时间需要关闭的窗口
2. 生成新的随机密码
3. 调用Zoom API更新账号密码
4. 更新窗口状态为"已关闭"
5. 记录密码变更日志

## 4. 数据库设计

### 4.1 系统配置扩展 (现有t_system_config表)
**说明：** 域名配置作为系统通用配置项，添加到现有的系统配置表中

**新增配置项：**
```sql
-- 在现有系统配置表中新增以下配置项
INSERT INTO t_system_config (config_key, config_value, description, created_at, updated_at) VALUES
('join_account.domain.base_url', 'https://zoombus.cn', 'Join Account Rental基础域名配置', NOW(), NOW()),
('join_account.domain.path_prefix', 'rf', 'Join Account Rental链接路径前缀', NOW(), NOW()),
('join_account.reservation.max_advance_days', '30', '用户预约的开始日期距离当前日期的最大天数', NOW(), NOW()),
('join_account.algorithm.time_weight', '0.4', '账号选择算法中时间距离权重', NOW(), NOW()),
('join_account.algorithm.load_weight', '0.4', '账号选择算法中负载均衡权重', NOW(), NOW()),
('join_account.algorithm.history_weight', '0.2', '账号选择算法中历史使用权重', NOW(), NOW());
('join_account.reservation.max_advance_days', '30', '用户预约的开始日期距离当前日期的最大天数', NOW(), NOW()),
('join_account.algorithm.time_weight', '0.4', '账号选择算法中时间距离权重', NOW(), NOW()),
('join_account.algorithm.load_weight', '0.4', '账号选择算法中负载均衡权重', NOW(), NOW()),
('join_account.algorithm.history_weight', '0.2', '账号选择算法中历史使用权重', NOW(), NOW());
```

**配置键命名规范：**
- 使用 `join_account.` 前缀区分功能模块
- 采用层级结构：`模块.分类.具体项`
- 示例：
  - `join_account.domain.base_url` - 域名配置
  - `join_account.reservation.max_advance_days` - 预约限制配置
  - `join_account.algorithm.time_weight` - 算法权重配置

### 4.2 Join Account Rental令牌表 (t_join_account_rental_tokens)
```sql
CREATE TABLE t_join_account_rental_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    token_number VARCHAR(6) UNIQUE NOT NULL COMMENT 'Token编号',
    batch_number VARCHAR(50) NOT NULL COMMENT '批次号',
    usage_days INT NOT NULL COMMENT '使用天数',
    status ENUM('PENDING', 'EXPORTED', 'RESERVED', 'ACTIVE', 'COMPLETED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '权益状态：待使用/已导出/已预约/使用中/已完成/已作废',
    export_status ENUM('NOT_EXPORTED', 'EXPORTED') DEFAULT 'NOT_EXPORTED' COMMENT '导出状态',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    exported_at DATETIME NULL COMMENT '导出时间',
    reserved_at DATETIME NULL COMMENT '预约时间',
    assigned_zoom_user_id BIGINT NULL COMMENT '分配的Zoom账号ID',
    assigned_zoom_user_email VARCHAR(100) NULL COMMENT '分配的Zoom账号邮箱',
    assigned_password VARCHAR(20) NULL COMMENT '分配的密码',
    window_start_time DATETIME NULL COMMENT '窗口开始时间',
    window_end_time DATETIME NULL COMMENT '窗口结束时间',
    cancelled_at DATETIME NULL COMMENT '作废时间',
    cancelled_by VARCHAR(50) NULL COMMENT '作废操作人',
    exported_by VARCHAR(50) NULL COMMENT '导出操作人',
    remark TEXT NULL COMMENT '备注',
    INDEX idx_token_number (token_number),
    INDEX idx_batch_number (batch_number),
    INDEX idx_status (status),
    INDEX idx_export_status (export_status),
    INDEX idx_assigned_zoom_user_id (assigned_zoom_user_id)
);
```

### 4.3 Join Account使用窗口表 (t_join_account_usage_windows)
```sql
CREATE TABLE t_join_account_usage_windows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    zoom_user_id BIGINT NOT NULL COMMENT 'Zoom账号ID',
    token_number VARCHAR(6) NOT NULL COMMENT '关联的Token',
    start_time DATETIME NOT NULL COMMENT '窗口开始时间',
    end_time DATETIME NOT NULL COMMENT '窗口结束时间',
    status ENUM('PENDING', 'ACTIVE', 'CLOSED') DEFAULT 'PENDING' COMMENT '窗口状态',
    opened_at DATETIME NULL COMMENT '实际开启时间',
    closed_at DATETIME NULL COMMENT '实际关闭时间',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_token_number (token_number),
    INDEX idx_time_range (start_time, end_time),
    INDEX idx_status (status)
);
```

### 4.4 Join Account密码变更日志表 (t_join_account_password_logs)
```sql
CREATE TABLE t_join_account_password_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    zoom_user_id BIGINT NOT NULL COMMENT 'Zoom账号ID',
    old_password VARCHAR(100) NULL COMMENT '旧密码',
    new_password VARCHAR(100) NOT NULL COMMENT '新密码',
    change_type ENUM('WINDOW_OPEN', 'WINDOW_CLOSE', 'MANUAL') NOT NULL COMMENT '变更类型',
    window_id BIGINT NULL COMMENT '关联窗口ID',
    created_at DATETIME NOT NULL COMMENT '变更时间',
    created_by VARCHAR(50) DEFAULT 'SYSTEM' COMMENT '操作人',
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_change_type (change_type),
    INDEX idx_created_at (created_at)
);
```

## 5. API接口设计

### 5.1 管理端接口

#### 5.1.1 系统配置管理（扩展现有接口）
**说明：** 域名配置通过现有的系统配置接口进行管理

```
GET /api/admin/system/config?keys=join_account.domain.base_url,join_account.domain.path_prefix
Response:
{
    "success": true,
    "data": {
        "join_account.domain.base_url": "https://zoombus.cn",
        "join_account.domain.path_prefix": "rf"
    }
}

PUT /api/admin/system/config
Content-Type: application/json
Request:
{
    "configs": [
        {
            "key": "join_account.domain.base_url",
            "value": "https://zoombus.cn"
        },
        {
            "key": "join_account.domain.path_prefix",
            "value": "rf"
        }
    ]
}
```

#### 5.1.2 批量生成权益链接
```
POST /api/admin/join-account-rental/tokens/batch-generate
Content-Type: application/json

Request:
{
    "usageDays": 3,
    "quantity": 100,
    "remark": "测试批次"
}

Response:
{
    "success": true,
    "data": {
        "batchNumber": "JAR_BATCH_20240101_143022",
        "tokens": [
            {
                "tokenNumber": "ABC123",
                "link": "https://zoombus.cn/rf/ABC123",
                "usageDays": 3
            }
        ]
    }
}
```

#### 5.1.3 权益链接查询
```
GET /api/admin/join-account-rental/tokens?page=0&size=20&batchNumber=xxx&status=xxx&exportStatus=xxx

Response:
{
    "success": true,
    "data": {
        "content": [
            {
                "id": 1,
                "tokenNumber": "ABC123",
                "batchNumber": "JAR_BATCH_20240101_143022",
                "usageDays": 3,
                "status": "RESERVED",
                "exportStatus": "EXPORTED",
                "link": "https://zoombus.cn/rf/ABC123",
                "createdAt": "2024-01-01 14:30:22",
                "exportedAt": "2024-01-01 15:00:00",
                "reservedAt": "2024-01-02 10:30:00",
                "assignedZoomUserEmail": "<EMAIL>",
                "assignedPassword": "Aa123567",
                "windowStartTime": "2024-01-03 00:00:00",
                "windowEndTime": "2024-01-05 23:59:59"
            }
        ],
        "totalElements": 100,
        "totalPages": 5
    }
}
```

#### 5.1.4 批量导出权益链接
```
POST /api/admin/join-account-rental/tokens/export
Content-Type: application/json

Request:
{
    "tokenIds": [1, 2, 3, 4, 5]
}

Response:
{
    "success": true,
    "data": {
        "fileName": "权益链接_JAR_BATCH_20240101_143022.xlsx",
        "downloadUrl": "/api/admin/join-account-rental/tokens/download/xxx",
        "exportedCount": 5
    }
}
```

#### 5.1.5 批量作废权益记录
```
POST /api/admin/join-account-rental/tokens/cancel
Content-Type: application/json

Request:
{
    "tokenIds": [1, 2, 3],
    "reason": "批量作废原因"
}

Response:
{
    "success": true,
    "data": {
        "cancelledCount": 3,
        "message": "成功作废3条权益记录"
    }
}
```

#### 5.1.6 使用窗口查询
```
GET /api/admin/join-account-rental/usage-windows?page=0&size=20&zoomUserEmail=xxx&status=xxx&startTime=xxx&endTime=xxx

Response:
{
    "success": true,
    "data": {
        "content": [
            {
                "id": 1,
                "zoomUserId": 123,
                "zoomUserEmail": "<EMAIL>",
                "tokenNumber": "ABC123",
                "startTime": "2024-01-01 00:00:00",
                "endTime": "2024-01-03 23:59:59",
                "status": "ACTIVE",
                "openedAt": "2024-01-01 00:00:05",
                "closedAt": null,
                "createdAt": "2023-12-31 15:30:00"
            }
        ],
        "totalElements": 50,
        "totalPages": 3
    }
}
```

#### 5.1.7 密码变更日志查询
```
GET /api/admin/join-account-rental/password-logs?page=0&size=20&zoomUserEmail=xxx&changeType=xxx&startTime=xxx&endTime=xxx

Response:
{
    "success": true,
    "data": {
        "content": [
            {
                "id": 1,
                "zoomUserId": 123,
                "zoomUserEmail": "<EMAIL>",
                "oldPasswordMasked": "Aa****67",
                "newPasswordMasked": "Aa****23",
                "changeType": "WINDOW_OPEN",
                "windowId": 1,
                "createdAt": "2024-01-01 00:00:00",
                "createdBy": "SYSTEM"
            }
        ],
        "totalElements": 100,
        "totalPages": 5
    }
}
```

#### 5.1.8 密码完整信息查询（仅超级管理员）
```
GET /api/admin/join-account-rental/password-logs/{id}/full

Response:
{
    "success": true,
    "data": {
        "id": 1,
        "zoomUserId": 123,
        "zoomUserEmail": "<EMAIL>",
        "oldPassword": "Aa123567",
        "newPassword": "Aa890123",
        "changeType": "WINDOW_OPEN",
        "windowId": 1,
        "createdAt": "2024-01-01 00:00:00",
        "createdBy": "SYSTEM"
    }
}
```

### 5.2 客户端接口

#### 5.2.1 获取Token信息
```
GET /api/join-account-rental/token/{tokenNumber}

Response:
{
    "success": true,
    "data": {
        "tokenNumber": "ABC123",
        "usageDays": 3,
        "status": "UNUSED",
        "maxAdvanceDays": 30,
        "currentDate": "2024-01-01",
        "maxStartDate": "2024-01-31"
    }
}
```

#### 5.2.2 申请账号
```
POST /api/join-account-rental/token/{tokenNumber}/claim
Content-Type: application/json

Request:
{
    "startDate": "2024-01-01"
}

Response (成功):
{
    "success": true,
    "data": {
        "email": "<EMAIL>",
        "password": "Aa123567",
        "startTime": "2024-01-01 00:00:00",
        "endTime": "2024-01-03 23:59:59",
        "status": "RESERVED"
    }
}

Response (预约时间超出限制):
{
    "success": false,
    "errorCode": "RESERVATION_TIME_EXCEEDED",
    "message": "系统当前仅支持预约30天内的使用时段",
    "data": {
        "maxAdvanceDays": 30,
        "requestedDate": "2024-02-15",
        "maxAllowedDate": "2024-01-31"
    }
}
```

## 6. 菜单结构设计

### 6.1 管理端菜单结构
```
管理系统菜单
├── 管理
│   ├── 用户管理
│   ├── 系统配置  ← 扩展：新增域名配置等通用配置项
│   └── 其他管理功能...
├── 参会  ← 新增菜单分组
│   ├── 权益链接管理  ← 新增：批量生成、查询、导出、作废
│   ├── 使用窗口查询  ← 新增：账号使用窗口查询
│   ├── 密码变更日志  ← 新增：密码变更记录查询
│   └── 使用记录统计  ← 新增：权益使用情况统计
├── Zoom
│   ├── Zoom用户管理
│   ├── PMI管理
│   └── 会议管理
└── 其他现有菜单...
```

### 6.2 菜单功能说明

#### 6.2.1 管理 > 系统配置
**功能：** 系统通用配置管理
- 域名配置（基础域名、路径前缀）- 作为系统通用配置项
- 其他系统参数配置
- 权限：仅超级管理员可访问
- **设计原因：** 域名配置是系统级通用功能，应整合到现有的系统配置中

#### 6.2.2 参会 > 权益链接管理
**功能：** 权益链接的完整生命周期管理
- 批量生成权益链接
- 权益记录查询和筛选
- 批量导出权益链接
- 批量作废权益记录
- 权限：管理员及以上可访问

#### 6.2.3 参会 > 使用窗口查询
**功能：** 账号使用窗口详细查询
- 窗口时间范围查询
- 按账号筛选窗口记录
- 窗口状态查询（待开启/使用中/已关闭）
- 权限：管理员及以上可访问

#### 6.2.4 参会 > 密码变更日志
**功能：** 密码变更记录查询
- 密码变更历史查询
- 按账号筛选变更记录
- 变更类型筛选（窗口开启/窗口关闭/手动变更）
- 权限：管理员及以上可访问

#### 6.2.5 参会 > 使用记录统计
**功能：** 权益使用情况统计和分析
- 使用情况统计报表
- 账号使用率分析
- 窗口使用情况汇总
- 权限：管理员及以上可访问

### 6.3 菜单排序规则
**菜单组排序：**
1. 管理（系统级功能）
2. 参会（新增业务功能）
3. Zoom（原有核心功能）
4. 其他业务功能...

**设计原因：**
- "参会"功能是新的业务模块，放在"Zoom"上面便于用户快速找到
- "管理"功能包含系统配置，保持在顶部位置
- 保持现有菜单结构的稳定性

## 7. 域名配置详细说明

### 7.1 配置项说明（系统通用配置）
| 配置项 | 配置键 | 默认值 | 说明 |
|--------|--------|--------|------|
| 基础域名 | join_account.domain.base_url | https://zoombus.cn | 客户访问的基础域名，支持HTTP/HTTPS |
| 路径前缀 | join_account.domain.path_prefix | rf | 权益链接的路径前缀 |

**配置特点：**
- 作为系统通用配置项，与其他系统配置统一管理
- 使用 `join_account.` 前缀进行模块化管理
- 通过现有的系统配置界面进行设置

### 7.1.1 预约时间限制配置
| 配置项 | 配置键 | 默认值 | 说明 |
|--------|--------|--------|------|
| 预约时间限制 | join_account.reservation.max_advance_days | 30 | 用户预约的开始日期距离当前日期的最大天数 |

**业务规则：**
- 用户选择开始日期时，系统检查：开始日期 - 当前日期 ≤ max_advance_days
- 超出限制时提示："系统当前仅支持预约{max_advance_days}天内的使用时段"
- 管理员可通过系统配置调整此参数

### 7.2 链接生成规则
```
完整链接格式：{join_account.domain.base_url}/{join_account.domain.path_prefix}/{tokenNumber}
示例：https://zoombus.cn/rf/ABC123
```

**配置读取方式：**
```java
// 在服务中读取系统配置
String baseUrl = systemConfigService.getConfigValue("join_account.domain.base_url");
String pathPrefix = systemConfigService.getConfigValue("join_account.domain.path_prefix");
String fullLink = baseUrl + "/" + pathPrefix + "/" + tokenNumber;
```

### 7.3 配置变更影响
- **新生成链接**：立即使用新配置
- **已生成链接**：保持原有格式，仍然有效
- **系统兼容性**：支持多种域名格式同时访问

### 7.4 账号详情链接功能
**功能描述：** 在权益记录中显示分配账号的详情链接

**链接格式：** `http://localhost:3000/zoom-users/zoom-user/{zoomUserId}`

**显示规则：**
- 只有已分配账号的权益记录才显示链接
- 链接文本显示为账号邮箱
- 点击链接在新窗口打开账号详情页面

**实现方式：**
```html
<a href="http://localhost:3000/zoom-users/zoom-user/123" target="_blank">
    <EMAIL>
</a>
```

## 8. 非功能性需求

### 8.1 性能要求
- 权益链接生成：单次最多1000个
- 页面响应时间：< 3秒
- 定时任务执行间隔：1分钟
- 域名配置变更：实时生效
- 使用窗口查询：支持分页，单页最多100条
- 密码日志查询：支持分页，单页最多50条
- 查询结果导出：最多支持10000条记录

### 8.2 安全要求
- Token唯一性校验
- 防止重复使用
- 密码安全存储
- API访问控制
- 域名配置权限控制
- 密码查询权限分级控制
- 敏感信息脱敏显示
- 查询操作审计日志

### 8.3 可用性要求
- 系统可用性：99.9%
- 错误处理和用户友好提示
- 操作日志记录
- 配置变更审计

## 9. 验收标准

### 9.1 功能验收
- [ ] 菜单结构正确：参会组在Zoom组上面，管理组在顶部
- [ ] 域名配置功能整合到现有"管理 > 系统配置"中
- [ ] 权益链接管理功能在"参会"菜单下
- [ ] 使用窗口查询功能在"参会"菜单下
- [ ] 密码变更日志功能在"参会"菜单下
- [ ] 管理员可以配置系统域名
- [ ] 域名配置变更实时生效
- [ ] 管理员可以批量生成权益链接
- [ ] 权益链接导出功能正常，格式正确
- [ ] 权益记录作废功能正常
- [ ] 权益状态流转正确（支持直接预约和导出后预约两种路径）
- [ ] 使用窗口查询功能正常，筛选条件有效
- [ ] 密码变更日志查询功能正常，密码正确脱敏
- [ ] 客户可以通过配置的域名链接领取账号
- [ ] 预约时间限制功能正常，超出限制时正确提示
- [ ] 账号分配算法正确执行（负载均衡优化版：考虑时间距离、负载均衡、历史使用）
- [ ] 分配的密码正确记录和显示
- [ ] 账号详情链接正确跳转
- [ ] 窗口管理定时任务正常运行
- [ ] 密码自动变更功能正常

### 9.2 数据验收
- [ ] 所有数据正确存储
- [ ] 权益状态流转正确（支持多路径流转）
- [ ] 预约时间限制配置生效
- [ ] 导出状态记录准确
- [ ] 密码信息正确保存
- [ ] 账号关联信息完整
- [ ] 日志记录完整
- [ ] 配置数据持久化

### 9.3 界面验收
- [ ] 管理界面功能完整
- [ ] 权益状态显示清晰
- [ ] 批量操作功能正常
- [ ] 导出功能界面友好
- [ ] 账号链接正确显示
- [ ] 使用窗口查询界面友好，筛选功能完善
- [ ] 密码变更日志界面友好，脱敏显示正确
- [ ] 域名配置界面友好
- [ ] 客户端界面友好易用
- [ ] 响应式设计兼容移动端

### 9.4 导出文件验收
- [ ] Excel文件格式正确
- [ ] 权益链接完整有效
- [ ] 文件命名规范
- [ ] 下载功能正常

## 10. 实体类和服务类命名规范

### 10.1 实体类 (Entity)
- `JoinAccountRentalToken` - 权益令牌实体
- `JoinAccountUsageWindow` - 使用窗口实体
- `JoinAccountPasswordLog` - 密码变更日志实体
- **注：** 配置相关实体使用现有的 `SystemConfig` 实体

### 10.2 服务类 (Service)
- `JoinAccountRentalTokenService` - 令牌管理服务
- `JoinAccountRentalExportService` - 导出管理服务
- `JoinAccountUsageWindowService` - 窗口管理服务
- `JoinAccountUsageWindowQueryService` - 窗口查询服务
- `JoinAccountPasswordService` - 密码管理服务
- `JoinAccountPasswordLogQueryService` - 密码日志查询服务
- `JoinAccountAllocationService` - 账号分配服务（智能算法）
- `JoinAccountLoadBalanceService` - 负载均衡服务
- `JoinAccountStatusService` - 状态管理服务
- **注：** 配置管理使用现有的 `SystemConfigService`

### 10.3 控制器类 (Controller)
- `JoinAccountRentalAdminController` - 管理端控制器
- `JoinAccountRentalClientController` - 客户端控制器
- `JoinAccountUsageWindowController` - 使用窗口查询控制器
- `JoinAccountPasswordLogController` - 密码日志查询控制器

### 10.4 仓库类 (Repository)
- `JoinAccountRentalTokenRepository`
- `JoinAccountUsageWindowRepository`
- `JoinAccountPasswordLogRepository`
- **注：** 配置相关数据访问使用现有的 `SystemConfigRepository`
