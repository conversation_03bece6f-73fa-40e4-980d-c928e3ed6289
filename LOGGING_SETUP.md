# ZoomBus 日志配置说明

## 📋 概述

已配置ZoomBus后端应用将日志输出到固定文件，便于调试和问题排查。

## 🔧 配置修改

### 1. 应用配置 (application.yml)
```yaml
logging:
  level:
    com.zoombus: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.transaction: DEBUG
  file:
    name: zoombus-application.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 1GB
```

### 2. 启动脚本修改 (start.sh)
- 模式1、2、4都已配置日志文件输出
- 添加了JVM参数确保日志正确写入文件
- 增加了日志监控提示信息

## 📁 日志文件

### 主要日志文件
- **`zoombus-application.log`** - 应用主日志文件
- **`zoombus-console.log`** - 控制台输出日志
- **`zoombus.pid`** - 应用进程ID文件

### 日志特点
- **自动滚动**: 单文件最大100MB，保留30个历史文件
- **详细级别**: DEBUG级别，包含详细的调试信息
- **事务日志**: 包含Spring事务处理日志
- **SQL日志**: 包含Hibernate SQL执行日志

## 🚀 启动应用

### 使用start.sh启动
```bash
# 选择模式4 - 仅启动后端
echo "4" | ./start.sh

# 或者交互式选择
./start.sh
```

### 启动后的输出
启动成功后会显示：
- 应用状态和健康检查URL
- 日志文件位置
- 日志监控命令
- 停止服务命令

## 📋 日志查看工具

### 1. check-logs.sh 脚本
```bash
# 交互式菜单
./check-logs.sh

# 直接命令
./check-logs.sh tail 100        # 查看最新100行
./check-logs.sh follow          # 实时监控
./check-logs.sh error           # 查看错误日志
./check-logs.sh meeting         # 查看meeting相关日志
./check-logs.sh webhook         # 查看webhook相关日志
./check-logs.sh search "关键词"  # 搜索特定内容
```

### 2. 直接命令
```bash
# 实时查看日志
tail -f zoombus-application.log

# 查看最新日志
tail -50 zoombus-application.log

# 搜索错误
grep -i error zoombus-application.log

# 搜索meeting相关
grep -i meeting zoombus-application.log

# 搜索webhook相关
grep -i webhook zoombus-application.log

# 搜索特定时间
grep "2025-08-05 00:" zoombus-application.log
```

## 🛑 停止应用

### 使用stop-backend.sh脚本
```bash
./stop-backend.sh
```

### 手动停止
```bash
# 如果有PID文件
kill $(cat zoombus.pid)

# 或者查找进程
pkill -f "spring-boot:run"
```

## 🔍 调试Meeting.Started事件

### 关键日志搜索
```bash
# 搜索特定UUID
grep "k94oXl3pTnaD7QJUsmhvKA==" zoombus-application.log

# 搜索特定Meeting ID
grep "86094417902" zoombus-application.log

# 搜索特定时间戳
grep "1754324428546" zoombus-application.log

# 搜索meeting.started处理
grep -i "meeting.*started\|handleMeetingStarted" zoombus-application.log

# 搜索参数验证
grep -i "validateMeetingStartedParams" zoombus-application.log

# 搜索分布式锁
grep -i "distributedLock\|executeWithMeetingUuidLock" zoombus-application.log

# 搜索数据库操作
grep -i "zoomMeetingRepository\|t_zoom_meetings" zoombus-application.log
```

### 错误排查
```bash
# 查看所有错误
grep -i "error\|exception\|failed" zoombus-application.log

# 查看事务相关错误
grep -i "transaction.*error\|rollback" zoombus-application.log

# 查看数据库相关错误
grep -i "sql.*error\|database.*error" zoombus-application.log

# 查看Redis相关错误
grep -i "redis.*error\|lock.*error" zoombus-application.log
```

## 📊 日志级别说明

### DEBUG级别包含
- 方法调用跟踪
- 参数值详情
- SQL执行语句
- 事务边界信息
- 分布式锁操作
- 业务逻辑处理步骤

### 关键日志标识
- `com.zoombus.controller.WebhookController` - Webhook接收
- `com.zoombus.service.ZoomMeetingService` - 会议服务处理
- `org.springframework.transaction` - 事务处理
- `org.hibernate.SQL` - SQL执行

## 💡 使用建议

### 1. 实时监控
启动应用后，在另一个终端窗口运行：
```bash
./check-logs.sh follow
```

### 2. 问题排查
当遇到问题时：
```bash
# 1. 查看最新错误
./check-logs.sh error

# 2. 搜索特定事件
./check-logs.sh search "事件UUID或关键词"

# 3. 查看完整上下文
grep -A 10 -B 10 "错误关键词" zoombus-application.log
```

### 3. 性能监控
```bash
# 查看SQL执行
grep "Hibernate:" zoombus-application.log

# 查看事务处理
grep "Transaction" zoombus-application.log

# 查看响应时间
grep -i "completed\|finished" zoombus-application.log
```

## 🎯 Meeting.Started事件调试流程

1. **启动应用并监控日志**
   ```bash
   echo "4" | ./start.sh
   ./check-logs.sh follow
   ```

2. **发送测试事件**
   ```bash
   ./manual_test_meeting_started.sh
   ```

3. **检查处理日志**
   ```bash
   ./check-logs.sh meeting
   ```

4. **如果没有日志，检查webhook接收**
   ```bash
   ./check-logs.sh webhook
   ```

5. **查看错误信息**
   ```bash
   ./check-logs.sh error
   ```

---

**配置完成时间**: 2025-08-05  
**日志文件**: zoombus-application.log  
**监控工具**: check-logs.sh
