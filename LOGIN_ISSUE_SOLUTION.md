# 管理台登录问题解决方案

## 🔍 问题诊断

经过检查，发现以下情况：

### ✅ 后端状态正常
- 后端服务正在8080端口运行
- 数据库连接正常
- 管理员用户存在：`admin` / `admin123`
- 登录API测试成功

### ❌ 前端连接问题
- 前端显示：`登录失败: Request failed with status code 500`
- 实际问题：前端无法连接到后端（ECONNREFUSED）
- 原因：系统代理设置影响了前端到后端的连接

## 🛠️ 解决方案

### 方案1：临时禁用系统代理（推荐）

1. **关闭系统代理**：
   - 打开 系统偏好设置 > 网络
   - 选择当前网络连接
   - 点击"高级" > "代理"
   - 取消勾选所有代理选项
   - 点击"好"和"应用"

2. **重启前端服务**：
   ```bash
   # 停止当前启动脚本 (Ctrl+C)
   # 然后重新启动
   ./start.sh
   # 选择 "1. 开发模式"
   ```

### 方案2：配置代理绕过

1. **设置环境变量**：
   ```bash
   export NO_PROXY=localhost,127.0.0.1
   export no_proxy=localhost,127.0.0.1
   ```

2. **重启开发环境**：
   ```bash
   ./start.sh
   ```

### 方案3：修改前端代理配置

1. **编辑 frontend/package.json**：
   ```json
   {
     "proxy": "http://127.0.0.1:8080"
   }
   ```

2. **或者创建 frontend/src/setupProxy.js**：
   ```javascript
   const { createProxyMiddleware } = require('http-proxy-middleware');

   module.exports = function(app) {
     app.use(
       '/api',
       createProxyMiddleware({
         target: 'http://127.0.0.1:8080',
         changeOrigin: true,
         timeout: 30000,
       })
     );
   };
   ```

### 方案4：直接访问后端（测试用）

如果前端仍有问题，可以直接测试后端：

```bash
# 测试登录API
curl --noproxy localhost -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## 🎯 验证步骤

1. **检查后端健康状态**：
   ```bash
   curl --noproxy localhost http://localhost:8080/actuator/health
   ```

2. **访问管理端前端**：
   - 打开浏览器访问：http://localhost:3000
   - 使用账号：`admin` / `admin123`

3. **访问用户端前端**：
   - 打开浏览器访问：http://localhost:3001/m

## 📋 默认登录信息

- **用户名**：`admin`
- **密码**：`admin123`
- **邮箱**：`<EMAIL>`
- **角色**：超级管理员

## 🔧 故障排除

### 如果仍然无法登录：

1. **检查端口占用**：
   ```bash
   lsof -i :8080  # 后端
   lsof -i :3000  # 管理端前端
   lsof -i :3001  # 用户端前端
   ```

2. **检查数据库连接**：
   ```bash
   mysql -u root -pnvshen2018 -e "USE zoombusV; SELECT COUNT(*) FROM admin_users;"
   ```

3. **查看后端日志**：
   - 在启动脚本的终端中查看日志输出
   - 寻找错误信息或异常

4. **清除浏览器缓存**：
   - 清除浏览器缓存和Cookie
   - 尝试使用无痕模式

### 如果数据库问题：

```bash
# 重新初始化数据库
mysql -u root -pnvshen2018 < init.sql
```

## 💡 预防措施

1. **开发环境配置**：
   - 在开发环境中禁用系统代理
   - 或者配置代理绕过本地地址

2. **网络配置**：
   - 确保localhost和127.0.0.1不被代理
   - 检查防火墙设置

3. **环境变量**：
   ```bash
   # 添加到 ~/.bashrc 或 ~/.zshrc
   export NO_PROXY=localhost,127.0.0.1,*.local
   export no_proxy=localhost,127.0.0.1,*.local
   ```

## 🎉 成功标志

当问题解决后，您应该能够：
- ✅ 访问 http://localhost:3000 看到登录页面
- ✅ 使用 admin/admin123 成功登录
- ✅ 看到管理台仪表板
- ✅ 访问 http://localhost:3001/m 看到用户端PMI页面
