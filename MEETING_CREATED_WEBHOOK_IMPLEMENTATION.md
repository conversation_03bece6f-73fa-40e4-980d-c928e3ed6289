# Meeting.Created Webhook事件处理实现

## 🎯 功能概述

成功实现了Zoom `meeting.created` webhook事件的处理功能，当Zoom中创建新会议时，系统会自动将会议信息登记到数据库中。支持多账号处理，能够正确关联主持人用户和创建者信息。

## ✅ 实现的功能

### 1. 事件处理流程

1. **接收Webhook事件**: 通过 `/api/webhooks/zoom/{accountId}` 接收事件
2. **账号验证**: 验证accountId是否在ZoomAuth表中存在
3. **签名验证**: 使用HMAC-SHA256验证webhook签名
4. **数据解析**: 解析meeting.created事件的payload数据
5. **用户查找**: 根据host_id查找对应的ZoomUser
6. **会议创建**: 创建Meeting实体并保存到数据库
7. **日志记录**: 记录处理过程和结果

### 2. 支持的数据字段

#### 基本会议信息
- `id`: Zoom会议ID
- `topic`: 会议主题
- `type`: 会议类型（即时会议、预定会议等）
- `status`: 会议状态（默认为SCHEDULED）
- `agenda`: 会议议程
- `duration`: 会议时长（分钟）
- `start_time`: 开始时间
- `timezone`: 时区

#### 会议链接和密码
- `join_url`: 参会链接
- `start_url`: 主持人开始链接
- `password`: 会议密码

#### 用户关联
- `host_id`: 主持人的Zoom用户ID
- `zoom_user_id`: 关联到Meeting的zoomUserId字段
- `creator_user_id`: 如果找到对应的ZoomUser，关联到其User

#### 会议设置（扩展支持）
- `settings`: 会议设置信息（等待室、入会静音等）
- `recurrence`: 周期性会议设置

## 🔧 技术实现

### 1. 核心方法

#### `handleMeetingCreatedWithAuth`
```java
private void handleMeetingCreatedWithAuth(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth)
```
- 主要的事件处理方法
- 支持多账号处理
- 包含完整的错误处理和日志记录

#### `createMeetingFromWebhookData`
```java
private Meeting createMeetingFromWebhookData(JsonNode object, ZoomAuth zoomAuth, ZoomUser hostUser)
```
- 从webhook数据创建Meeting对象
- 支持用户关联和详细字段解析

#### `setDetailedMeetingFields`
```java
private void setDetailedMeetingFields(Meeting meeting, JsonNode object, ZoomAuth zoomAuth)
```
- 设置会议的详细字段
- 支持会议设置和周期性会议解析

### 2. 数据流程

```
Zoom Webhook Event
       ↓
Account Validation
       ↓
Signature Verification
       ↓
Event Data Parsing
       ↓
Host User Lookup
       ↓
Meeting Object Creation
       ↓
Database Save
       ↓
Log & Response
```

### 3. 错误处理

- **账号不存在**: 返回404错误
- **签名验证失败**: 返回401错误
- **数据解析错误**: 记录错误日志，标记事件为FAILED
- **会议已存在**: 跳过创建，记录日志
- **用户未找到**: 记录警告，但继续创建会议

## 📋 配置要求

### 1. ZoomAuth表配置
确保数据库中有对应的ZoomAuth记录：
```sql
INSERT INTO t_zoom_auth (
    account_name, 
    zoom_account_id, 
    webhook_secret_token,
    ...
) VALUES (
    'Test Account',
    'test-account-001',
    'your-webhook-secret-token',
    ...
);
```

### 2. Zoom开发者控制台配置
```
Event notification endpoint URL:
https://your-domain.com/api/webhooks/zoom/{zoom_account_id}

Event types:
☑ Meeting Created (meeting.created)

Webhook Secret Token:
your-webhook-secret-token
```

### 3. ZoomUser数据（可选）
如果需要关联主持人用户，确保ZoomUser表中有对应记录：
```sql
INSERT INTO t_zoom_users (
    zoom_auth_id,
    zoom_user_id,
    email,
    ...
) VALUES (
    1,  -- ZoomAuth的ID
    'host123456',  -- 对应webhook中的host_id
    '<EMAIL>',
    ...
);
```

## 🧪 测试方法

### 1. 使用测试脚本
```bash
chmod +x test-meeting-created-webhook.sh
./test-meeting-created-webhook.sh
```

### 2. 手动测试
```bash
curl -X POST \
  http://localhost:8080/api/webhooks/zoom/test-account-001 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-signature" \
  -d '{
    "event": "meeting.created",
    "payload": {
      "object": {
        "id": "*********",
        "host_id": "host123456",
        "topic": "测试会议",
        "type": 2,
        "start_time": "2025-07-29T14:00:00Z",
        "duration": 60,
        "join_url": "https://zoom.us/j/*********",
        "password": "123456"
      }
    }
  }'
```

### 3. 验证结果
```sql
-- 检查会议是否创建成功
SELECT * FROM t_meetings WHERE zoom_meeting_id = '*********';

-- 检查webhook事件记录
SELECT * FROM t_webhook_events WHERE event_type = 'meeting.created' ORDER BY created_at DESC LIMIT 5;
```

## 📊 监控和日志

### 1. 关键日志信息
- 事件接收: `收到Zoom Webhook [账号ID: xxx]`
- 用户查找: `找到主持人用户: xxx [xxx]` 或 `未找到主持人用户`
- 会议创建: `通过Webhook创建会议成功 [账号: xxx, 会议: xxx, ID: xxx]`
- 错误处理: `处理会议创建事件失败 [账号: xxx]`

### 2. 数据库记录
- `t_webhook_events`: 记录所有webhook事件的处理状态
- `t_meetings`: 存储创建的会议信息

### 3. 统计查询
```bash
# 查看webhook统计
curl http://localhost:8080/api/webhooks/stats

# 查看特定账号的事件
curl http://localhost:8080/api/webhooks/events/account/test-account-001
```

## 🔄 向后兼容性

- 保留了原有的`handleMeetingCreated`方法用于单账号模式
- 保留了`createMeetingFromWebhookData(String, String, JsonNode)`方法
- 新增的多账号功能不影响现有的webhook处理

## 🚀 扩展功能

### 1. 支持更多事件类型
- `meeting.updated`: 会议更新
- `meeting.deleted`: 会议删除
- `meeting.started`: 会议开始
- `meeting.ended`: 会议结束

### 2. 增强的数据解析
- 会议设置详细解析
- 周期性会议支持
- 参会者信息处理

### 3. 业务逻辑扩展
- 会议通知发送
- 日历同步
- 权限验证

## 🎉 使用效果

现在当在Zoom中创建会议时，系统会：
1. 自动接收webhook事件
2. 验证账号和签名
3. 解析会议信息
4. 查找并关联主持人用户
5. 创建会议记录到数据库
6. 记录处理日志

这样就实现了Zoom会议与ZoomBus系统的自动同步！
