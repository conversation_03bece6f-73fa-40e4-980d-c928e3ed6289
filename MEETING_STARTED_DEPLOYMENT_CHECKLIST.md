# Meeting.Started 优化部署检查清单

## 📋 部署前检查

### 1. **代码审查**
- [x] 分布式锁机制实现正确
- [x] 参数验证逻辑完整
- [x] 事务边界优化合理
- [x] 错误处理机制完善
- [x] 幂等性逻辑正确
- [x] 日志记录详细

### 2. **依赖检查**
- [x] DistributedLockManager 服务可用
- [x] BillingMonitorService 正常工作
- [x] ZoomMeetingRepository 方法完整
- [x] 相关实体类字段正确

### 3. **配置检查**
- [ ] 分布式锁超时时间配置（建议30秒）
- [ ] 数据库连接池配置充足
- [ ] 事务超时配置合理
- [ ] 日志级别配置适当

## 🚀 部署步骤

### 阶段1：测试环境部署

#### 1.1 代码部署
```bash
# 1. 备份当前版本
cp -r /path/to/zoombus /path/to/zoombus_backup_$(date +%Y%m%d_%H%M%S)

# 2. 部署新代码
git pull origin main
./mvnw clean package -DskipTests

# 3. 重启服务
./stop.sh
./start.sh
```

#### 1.2 功能验证
```bash
# 验证基本功能
curl -X POST http://test-server:8080/api/webhooks/zoom/test \
  -H "Content-Type: application/json" \
  -d '{
    "event": "meeting.started",
    "payload": {
      "object": {
        "uuid": "test-uuid-001",
        "id": "123456789",
        "host_id": "host-123",
        "topic": "测试会议"
      }
    }
  }'

# 检查日志
tail -f backend.log | grep "处理会议开始事件"
```

#### 1.3 并发测试
```bash
# 并发测试脚本
for i in {1..10}; do
  curl -X POST http://test-server:8080/api/webhooks/zoom/test \
    -H "Content-Type: application/json" \
    -d '{
      "event": "meeting.started",
      "payload": {
        "object": {
          "uuid": "concurrent-test-'$i'",
          "id": "concurrent-'$i'",
          "host_id": "host-concurrent",
          "topic": "并发测试会议'$i'"
        }
      }
    }' &
done
wait
```

### 阶段2：生产环境部署

#### 2.1 预部署检查
- [ ] 测试环境验证通过
- [ ] 数据库备份完成
- [ ] 回滚方案准备就绪
- [ ] 监控告警配置完成

#### 2.2 灰度部署
```bash
# 1. 部署到部分服务器
# 2. 观察关键指标
# 3. 逐步扩展到全部服务器
```

#### 2.3 全量部署
```bash
# 1. 停止所有服务
# 2. 部署新版本
# 3. 启动服务
# 4. 验证功能
```

## 📊 监控指标

### 关键指标监控

#### 1. **性能指标**
- 会议开始事件处理延迟（目标：< 500ms）
- 分布式锁等待时间（目标：< 100ms）
- 数据库操作响应时间（目标：< 200ms）

#### 2. **业务指标**
- 会议开始事件处理成功率（目标：> 99.9%）
- 重复记录创建率（目标：< 0.1%）
- 计费监控启动成功率（目标：> 99%）

#### 3. **系统指标**
- CPU使用率
- 内存使用率
- 数据库连接数
- 线程池使用情况

### 监控配置

#### 日志监控
```bash
# 关键日志模式
grep -E "(处理会议开始事件|会议状态更新|创建新会议记录|启动计费监控)" backend.log

# 错误日志监控
grep -E "(ERROR|Exception|Failed)" backend.log | grep "meeting"
```

#### 告警配置
- 会议开始事件处理失败率 > 1%
- 分布式锁等待时间 > 5秒
- 重复记录创建检测
- 计费监控启动失败

## 🔧 故障排查

### 常见问题及解决方案

#### 1. **分布式锁超时**
**症状**：日志显示锁等待超时
**排查**：
```bash
# 检查锁状态
# 查看是否有死锁情况
# 检查锁超时配置
```
**解决**：调整锁超时时间或优化锁粒度

#### 2. **参数验证失败**
**症状**：大量IllegalArgumentException
**排查**：
```bash
grep "参数验证失败" backend.log
```
**解决**：检查Webhook数据格式，修复数据源问题

#### 3. **计费监控启动失败**
**症状**：会议记录创建成功但计费监控未启动
**排查**：
```bash
grep "启动计费监控失败" backend.log
```
**解决**：检查BillingMonitorService状态，重启相关服务

#### 4. **数据库连接问题**
**症状**：数据库操作超时或失败
**排查**：
```bash
# 检查数据库连接池状态
# 查看数据库性能指标
```
**解决**：优化数据库配置或扩容

## 🔄 回滚方案

### 快速回滚步骤

#### 1. **代码回滚**
```bash
# 1. 停止服务
./stop.sh

# 2. 恢复备份
rm -rf /path/to/zoombus
mv /path/to/zoombus_backup_YYYYMMDD_HHMMSS /path/to/zoombus

# 3. 重启服务
./start.sh
```

#### 2. **数据回滚**
```bash
# 如果需要数据回滚
# 1. 恢复数据库备份
# 2. 清理可能的脏数据
# 3. 重启相关服务
```

### 回滚触发条件
- 会议开始事件处理成功率 < 95%
- 系统响应时间增加 > 50%
- 出现大量重复记录
- 计费监控大面积失效

## ✅ 部署验证

### 验证清单

#### 功能验证
- [ ] 正常会议开始事件处理
- [ ] 重复事件幂等性处理
- [ ] 参数验证正常工作
- [ ] 计费监控正常启动
- [ ] 错误处理机制有效

#### 性能验证
- [ ] 响应时间在预期范围内
- [ ] 并发处理能力满足要求
- [ ] 资源使用率正常
- [ ] 无内存泄漏

#### 监控验证
- [ ] 关键指标正常采集
- [ ] 告警规则正常触发
- [ ] 日志记录完整清晰
- [ ] 仪表板显示正常

## 📞 应急联系

### 关键人员
- 开发负责人：[联系方式]
- 运维负责人：[联系方式]
- 产品负责人：[联系方式]

### 应急流程
1. 发现问题 → 立即评估影响范围
2. 影响严重 → 启动回滚流程
3. 影响轻微 → 尝试热修复
4. 通知相关人员 → 记录问题详情
5. 问题解决 → 总结经验教训

## 🎯 成功标准

### 部署成功标准
- [x] 代码部署无错误
- [ ] 功能验证100%通过
- [ ] 性能指标达到预期
- [ ] 监控告警正常
- [ ] 无业务影响

### 运行稳定标准
- 连续运行24小时无重大问题
- 会议开始事件处理成功率 > 99.9%
- 系统响应时间稳定
- 无重复记录产生
- 计费监控正常工作

部署完成后，持续观察系统运行状况，确保优化效果达到预期目标。
