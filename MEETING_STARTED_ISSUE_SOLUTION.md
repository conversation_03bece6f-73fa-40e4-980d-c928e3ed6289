# Meeting.Started 事件未生成记录问题解决方案

## 📋 问题描述

**问题**: 收到meeting.started事件（时间戳：1754323512605），但未按照预期生成t_zoom_meetings记录

**事件时间**: 2025-08-05 00:05:12  
**问题发现时间**: 2025-08-05 00:15:26  
**时间差**: 约10分钟前的事件

## 🔍 问题分析

### 初步诊断结果

✅ **代码状态正常**
- WebhookController存在且包含meeting.started处理
- ZoomMeetingService优化代码已正确部署
- handleMeetingStarted方法存在

❌ **日志异常**
- 未找到该时间戳的相关日志
- 未找到meeting.started处理日志
- 说明事件可能未到达应用程序

⚠️ **服务状态**
- 当前服务未在8080端口响应
- 需要确认服务运行状态

## 🎯 可能的根本原因

### 1. **事件未到达应用程序** (最可能)
- Webhook URL配置错误
- 网络连接问题
- 服务未运行或端口错误
- ngrok隧道问题

### 2. **事件到达但处理失败**
- 参数验证失败
- 分布式锁获取失败
- 数据库连接问题
- 异常被静默捕获

### 3. **事件格式问题**
- 事件JSON格式不符合预期
- 必要字段缺失或为空
- 事件类型识别错误

## 🔧 解决方案

### 立即执行步骤

#### 1. **确认服务状态**
```bash
# 检查服务是否运行
curl http://localhost:8080/actuator/health

# 检查进程
ps aux | grep java | grep -v grep

# 检查端口占用
lsof -i :8080
```

#### 2. **启动服务**（如果未运行）
```bash
# 启动后端服务
./mvnw spring-boot:run -DskipTests

# 或使用启动脚本
echo "4" | ./start.sh
```

#### 3. **立即测试**（服务启动后）
```bash
# 运行手动测试脚本
./manual_test_meeting_started.sh
```

### 详细排查步骤

#### 步骤1: 服务验证
```bash
# 1. 检查健康状态
curl http://localhost:8080/actuator/health

# 2. 测试调试接口
curl -X POST http://localhost:8080/api/webhooks/zoom/debug/meeting-started \
     -H 'Content-Type: application/json' \
     -d '{"meetingUuid":"debug-test","meetingId":"123","hostId":"test","topic":"调试测试"}'
```

#### 步骤2: 日志分析
```bash
# 1. 启用调试日志（在application.yml中）
logging:
  level:
    com.zoombus: DEBUG
    org.springframework.transaction: DEBUG

# 2. 检查最新日志
tail -50 backend.log | grep -i 'meeting\|error'

# 3. 搜索特定时间戳
grep "1754323512605" backend.log
```

#### 步骤3: 数据库检查
```sql
-- 检查最近的会议记录
SELECT * FROM t_zoom_meetings 
ORDER BY created_at DESC 
LIMIT 10;

-- 检查特定时间范围的记录
SELECT * FROM t_zoom_meetings 
WHERE created_at >= '2025-08-05 00:00:00' 
  AND created_at <= '2025-08-05 00:30:00';

-- 检查表结构
SHOW CREATE TABLE t_zoom_meetings;
```

#### 步骤4: Redis检查（分布式锁）
```bash
# 检查Redis连接
redis-cli ping

# 检查锁相关的键
redis-cli keys '*meeting*'
redis-cli keys '*lock*'
```

#### 步骤5: Webhook配置检查
1. 登录Zoom开发者控制台
2. 检查Webhook URL配置
3. 验证事件订阅设置
4. 测试Webhook URL可访问性

## 🧪 测试脚本

### 已创建的测试工具

1. **`diagnose_meeting_started_issue.sh`** - 综合诊断脚本
2. **`analyze_meeting_started_issue.sh`** - 问题分析脚本
3. **`manual_test_meeting_started.sh`** - 手动测试脚本
4. **`test_meeting_started_event.sh`** - 事件测试脚本

### 使用方法
```bash
# 1. 运行综合诊断
./diagnose_meeting_started_issue.sh

# 2. 分析具体问题
./analyze_meeting_started_issue.sh

# 3. 手动测试（需要服务运行）
./manual_test_meeting_started.sh
```

## 📊 预期测试结果

### 正常情况下的响应
```json
{
  "success": true,
  "message": "会议开始事件处理成功",
  "meetingId": 123,
  "meetingUuid": "test-uuid",
  "status": "STARTED",
  "createdAt": "2025-08-05T00:15:30"
}
```

### 异常情况的响应
```json
{
  "success": false,
  "error": "具体错误信息",
  "details": "详细错误描述"
}
```

## 🔍 调试技巧

### 1. **启用详细日志**
```yaml
# application.yml
logging:
  level:
    com.zoombus.controller.WebhookController: DEBUG
    com.zoombus.service.ZoomMeetingService: DEBUG
    org.springframework.transaction: DEBUG
    org.springframework.web: DEBUG
```

### 2. **使用调试接口**
```bash
# 调试接口提供详细的步骤信息
curl -X POST http://localhost:8080/api/webhooks/zoom/debug/meeting-started \
     -H 'Content-Type: application/json' \
     -d '{"meetingUuid":"debug-$(date +%s)","meetingId":"123","hostId":"test","topic":"调试"}'
```

### 3. **数据库直接测试**
```bash
# 使用数据库检查接口
curl -X POST http://localhost:8080/api/webhooks/zoom/debug/database-check \
     -H 'Content-Type: application/json' \
     -d '{"searchPattern":"test"}'
```

## 🚨 常见问题及解决方案

### 问题1: 服务未运行
**症状**: curl请求超时或连接拒绝  
**解决**: 启动服务 `./mvnw spring-boot:run -DskipTests`

### 问题2: 参数验证失败
**症状**: 返回参数验证错误  
**解决**: 检查meetingUuid、meetingId、hostId是否为空

### 问题3: 分布式锁获取失败
**症状**: 锁获取超时错误  
**解决**: 检查Redis连接，重启Redis服务

### 问题4: 数据库连接失败
**症状**: 数据库连接错误  
**解决**: 检查数据库配置和服务状态

### 问题5: 事务回滚
**症状**: 操作成功但数据未保存  
**解决**: 检查事务配置和异常处理

## 📋 检查清单

### 服务启动前
- [ ] Java 11环境已配置
- [ ] 数据库服务已启动
- [ ] Redis服务已启动（如果使用）
- [ ] 配置文件正确

### 服务启动后
- [ ] 健康检查通过
- [ ] 调试接口可访问
- [ ] 数据库连接正常
- [ ] Redis连接正常

### 问题排查
- [ ] 检查应用日志
- [ ] 验证事件格式
- [ ] 测试手动触发
- [ ] 检查数据库记录

## 🎯 下一步行动

### 立即执行
1. **启动服务**（如果未运行）
2. **运行手动测试脚本**
3. **检查测试结果**

### 如果测试通过
1. 检查Zoom Webhook配置
2. 验证原始事件格式
3. 对比测试事件与原始事件差异

### 如果测试失败
1. 检查详细错误日志
2. 验证数据库和Redis连接
3. 检查代码逻辑

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **测试报告**: 运行测试脚本生成的报告文件
2. **应用日志**: backend.log或控制台输出
3. **原始事件**: 完整的JSON格式
4. **配置信息**: Zoom Webhook配置截图
5. **环境信息**: 数据库和Redis状态

---

**文档创建时间**: 2025-08-05  
**问题状态**: 待解决  
**优先级**: 高
