# Meeting.Started Webhook问题排查指南

## 🔍 问题现象

用户报告：`"event_ts": 1754238553297` 依然没能创建t_zoom_meetings记录

## 🛠️ 排查步骤

### 第一步：检查数据库表结构

执行SQL脚本检查表结构：
```bash
mysql -u root -p zoombusV < check_table_structure.sql
```

**重点检查**：
1. `pmi_record_id` 字段是否允许NULL
2. Flyway迁移V1.3是否执行成功
3. 表的所有约束和索引

### 第二步：使用诊断接口

调用诊断接口详细记录处理过程：
```bash
curl -X POST http://localhost:8080/api/webhooks/debug/meeting-started \
  -H "Content-Type: application/json" \
  -d '{
    "meetingUuid": "debug-test-' $(date +%s) '",
    "meetingId": "debug-meeting-123",
    "hostId": "debug-host",
    "topic": "诊断测试会议"
  }'
```

**关注响应中的**：
- `steps` 数组：详细的处理步骤
- `validationError`：字段验证错误
- `saveError`：保存失败错误
- `constraintViolation`：约束违反

### 第三步：检查表结构状态

调用表结构检查接口：
```bash
curl http://localhost:8080/api/webhooks/debug/table-structure
```

**检查项目**：
- `totalRecords`：表中记录总数
- `canCreateObject`：是否能创建对象
- `createObjectError`：创建对象时的错误

### 第四步：检查应用日志

查看应用日志中的详细信息：
```bash
# 查看最近的日志
tail -f logs/application.log | grep -E "(createMeetingRecordFromWebhook|从Webhook创建|验证失败|保存失败)"
```

**关注日志关键词**：
- `=== 开始从Webhook创建新会议记录 ===`
- `字段验证通过`
- `UUID重复检查通过`
- `准备保存会议记录到数据库`
- `从Webhook创建会议记录成功`
- `从Webhook创建会议记录失败`

## 🔧 常见问题和解决方案

### 问题1：数据库迁移未执行

**症状**：`pmi_record_id` 字段不允许NULL

**检查**：
```sql
SELECT IS_NULLABLE FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 't_zoom_meetings' 
AND COLUMN_NAME = 'pmi_record_id';
```

**解决**：
```bash
# 手动执行迁移
./mvnw flyway:migrate

# 或者手动执行SQL
ALTER TABLE t_zoom_meetings 
MODIFY COLUMN pmi_record_id BIGINT NULL;
```

### 问题2：字段验证失败

**症状**：`meetingUuid` 或 `meetingId` 为空

**检查**：诊断接口返回 `validationError`

**解决**：检查Webhook事件的数据格式，确保包含必要字段

### 问题3：UUID重复

**症状**：相同UUID的记录已存在

**检查**：
```sql
SELECT COUNT(*) FROM t_zoom_meetings 
WHERE zoom_meeting_uuid = 'your-uuid-here';
```

**解决**：
- 检查是否有重复处理
- 清理测试数据
- 确保UUID唯一性

### 问题4：其他数据库约束

**症状**：保存时出现约束违反错误

**检查**：
```sql
-- 检查所有约束
SELECT 
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE,
    TABLE_NAME
FROM information_schema.TABLE_CONSTRAINTS 
WHERE TABLE_NAME = 't_zoom_meetings';
```

**解决**：根据具体约束调整数据或修改表结构

### 问题5：事务回滚

**症状**：记录创建后又被回滚

**检查**：查看完整的事务日志

**解决**：
- 检查是否有其他异常导致事务回滚
- 确保所有相关操作都成功

## 📊 测试验证

### 基础功能测试

```bash
# 测试1：PMI会议
curl -X POST http://localhost:8080/api/webhooks/test/meeting-started \
  -H "Content-Type: application/json" \
  -d '{
    "meetingUuid": "test-pmi-' $(date +%s) '",
    "meetingId": "test-pmi-123456",
    "hostId": "test-host-123",
    "topic": "PMI测试会议"
  }'

# 测试2：普通会议
curl -X POST http://localhost:8080/api/webhooks/test/meeting-started \
  -H "Content-Type: application/json" \
  -d '{
    "meetingUuid": "test-normal-' $(date +%s) '",
    "meetingId": "test-normal-789012",
    "hostId": "test-host-456",
    "topic": "普通测试会议"
  }'
```

### 验证结果

```sql
-- 检查是否创建了记录
SELECT 
    id,
    zoom_meeting_uuid,
    zoom_meeting_id,
    pmi_record_id,
    topic,
    status,
    created_at
FROM t_zoom_meetings 
WHERE zoom_meeting_uuid LIKE 'test-%'
ORDER BY created_at DESC;
```

## 🚨 紧急修复

如果问题紧急，可以尝试以下快速修复：

### 方案1：手动执行数据库迁移

```sql
-- 确保pmi_record_id允许NULL
ALTER TABLE t_zoom_meetings 
MODIFY COLUMN pmi_record_id BIGINT NULL 
COMMENT 'PMI记录ID（PMI会议时有值，非PMI会议时为null）';

-- 添加缺失的索引
CREATE INDEX IF NOT EXISTS idx_zoom_meetings_pmi_record_id ON t_zoom_meetings(pmi_record_id);
CREATE INDEX IF NOT EXISTS idx_zoom_meetings_status_start_time ON t_zoom_meetings(status, start_time);
CREATE INDEX IF NOT EXISTS idx_zoom_meetings_host_id ON t_zoom_meetings(host_id);
```

### 方案2：重启应用

```bash
# 重启应用确保所有更改生效
./mvnw spring-boot:stop
./mvnw spring-boot:run
```

### 方案3：清理冲突数据

```sql
-- 如果有UUID冲突，清理测试数据
DELETE FROM t_zoom_meetings 
WHERE zoom_meeting_uuid LIKE 'test-%' 
OR zoom_meeting_uuid LIKE 'debug-%';
```

## 📞 获取帮助

如果以上步骤都无法解决问题，请提供以下信息：

1. **诊断接口响应**：`/debug/meeting-started` 的完整响应
2. **表结构检查结果**：`/debug/table-structure` 的响应
3. **数据库查询结果**：`check_table_structure.sql` 的执行结果
4. **应用日志**：包含错误信息的日志片段
5. **Webhook原始数据**：触发问题的原始Webhook事件数据

这些信息将帮助快速定位和解决问题。
