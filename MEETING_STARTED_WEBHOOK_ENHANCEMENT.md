# Meeting.Started Webhook处理增强

## 功能概述

处理`meeting.started`事件时，自动生成`t_zoom_meetings`记录，以便通过"Zoom会议看板"监控全量进行中的Zoom会议。

## 问题背景

并不是所有的`meeting.started`事件都是PMI会议，会议可能来自以下几种情况：

1. **PMI会议**：使用个人会议室号码的会议
2. **安排会议**：通过 `http://localhost:3000/meetings` 会议安排功能创建的会议
3. **Zoom App会议**：用户在Zoom客户端直接创建的会议

对于安排会议和Zoom App会议，系统在`meeting.created`事件时会生成：
- `t_meetings` 记录
- `t_zoom_meeting_details` 记录

但是当`meeting.started`事件到来时，需要补全`t_zoom_meetings`记录以便在"Zoom会议看板"中监控。

## 解决方案

### 实现位置
**文件**: `src/main/java/com/zoombus/service/ZoomMeetingService.java`
**方法**: `createMeetingRecordFromWebhook(String meetingUuid, String meetingId, String hostId, String topic)`

### 核心逻辑

```java
/**
 * 从Webhook创建会议记录（带详细信息和ZoomUser补全）
 * 支持PMI会议和通过会议安排功能创建的会议
 */
private void createMeetingRecordFromWebhook(String meetingUuid, String meetingId, String hostId, String topic) {
    // 第一步：尝试通过meetingId查找PMI记录
    // 第二步：如果不是PMI会议，尝试通过meetingId查找t_meetings记录
    // 第三步：如果既不是PMI会议也不是安排会议，则为其他类型会议
}
```

### 处理流程

#### 1. PMI会议处理
```java
Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findByPmiNumber(meetingId);
if (pmiRecordOpt.isPresent()) {
    PmiRecord pmiRecord = pmiRecordOpt.get();
    meeting.setPmiRecordId(pmiRecord.getId());
    meeting.setBillingMode(pmiRecord.getBillingMode());
    isPmiMeeting = true;
}
```

#### 2. 安排会议处理
```java
Optional<Meeting> scheduledMeetingOpt = meetingRepository.findByZoomMeetingId(meetingId);
if (scheduledMeetingOpt.isPresent()) {
    Meeting scheduledMeeting = scheduledMeetingOpt.get();
    isScheduledMeeting = true;
    
    // 从t_meetings记录中获取创建者信息
    if (scheduledMeeting.getCreatorUserId() != null) {
        // 使用安排会议的主题（如果Webhook没有提供主题）
        if (topic == null || topic.trim().isEmpty()) {
            meeting.setTopic(scheduledMeeting.getTopic());
        }
    }
    meeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);
}
```

#### 3. 其他会议处理
```java
if (!isPmiMeeting && !isScheduledMeeting) {
    log.info("这是其他类型的会议（可能是Zoom App直接创建）: meetingId={}", meetingId);
    meeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);
}
```

## 业务场景

### 场景1: PMI会议启动
**事件**: `meeting.started`
**meetingId**: `**********` (PMI号码)

**处理流程**:
1. 通过`meetingId`在`t_pmi_records`中查找PMI记录 ✅
2. 关联PMI记录ID和计费模式
3. 生成`t_zoom_meetings`记录
4. 启动计费监控（如果是按时长计费）

**结果**:
```sql
INSERT INTO t_zoom_meetings (
    pmi_record_id, zoom_meeting_uuid, zoom_meeting_id, 
    host_id, topic, status, start_time, billing_mode
) VALUES (
    123, 'uuid-xxx', '**********', 
    'host123', 'PMI会议', 'USING', NOW(), 'BY_TIME'
);
```

### 场景2: 安排会议启动
**事件**: `meeting.started`
**meetingId**: `**********` (安排会议ID)

**处理流程**:
1. 通过`meetingId`在`t_pmi_records`中查找 ❌ (未找到)
2. 通过`meetingId`在`t_meetings`中查找 ✅
3. 获取创建者信息和会议主题
4. 生成`t_zoom_meetings`记录

**结果**:
```sql
INSERT INTO t_zoom_meetings (
    pmi_record_id, zoom_meeting_uuid, zoom_meeting_id, 
    host_id, topic, status, start_time, billing_mode
) VALUES (
    NULL, 'uuid-yyy', '**********', 
    'host456', '项目讨论会议', 'USING', NOW(), 'BY_TIME'
);
```

### 场景3: Zoom App直接创建的会议
**事件**: `meeting.started`
**meetingId**: `5555555555` (临时会议ID)

**处理流程**:
1. 通过`meetingId`在`t_pmi_records`中查找 ❌ (未找到)
2. 通过`meetingId`在`t_meetings`中查找 ❌ (未找到)
3. 标记为其他类型会议
4. 生成`t_zoom_meetings`记录

**结果**:
```sql
INSERT INTO t_zoom_meetings (
    pmi_record_id, zoom_meeting_uuid, zoom_meeting_id, 
    host_id, topic, status, start_time, billing_mode
) VALUES (
    NULL, 'uuid-zzz', '5555555555', 
    'host789', 'Webhook会议 - 5555555555', 'USING', NOW(), 'BY_TIME'
);
```

## 数据关联

### t_zoom_meetings表结构
```sql
CREATE TABLE t_zoom_meetings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    pmi_record_id BIGINT NULL,                    -- PMI记录ID（PMI会议时有值）
    zoom_meeting_uuid VARCHAR(255) NOT NULL,     -- Zoom会议UUID
    zoom_meeting_id VARCHAR(255) NOT NULL,       -- Zoom会议ID
    host_id VARCHAR(255),                        -- 主持人ID
    topic VARCHAR(500),                          -- 会议主题
    status ENUM('PENDING', 'USING', 'ENDED', 'SETTLED'),
    start_time DATETIME,                         -- 开始时间
    billing_mode ENUM('BY_TIME', 'BY_MEETING'),  -- 计费模式
    assigned_zoom_user_id BIGINT,               -- 分配的ZoomUser ID
    assigned_zoom_user_email VARCHAR(255),      -- 分配的ZoomUser邮箱
    zoom_auth_account_name VARCHAR(255),        -- Zoom账号名称
    -- 其他字段...
);
```

### 关联关系
1. **PMI会议**: `pmi_record_id` → `t_pmi_records.id`
2. **安排会议**: `zoom_meeting_id` → `t_meetings.zoom_meeting_id`
3. **ZoomUser**: `assigned_zoom_user_id` → `t_zoom_users.id`

## 日志输出

### PMI会议
```
INFO  c.z.service.ZoomMeetingService - 找到对应的PMI记录: meetingId=**********, pmiRecordId=123, billingMode=BY_TIME
INFO  c.z.service.ZoomMeetingService - 从Webhook创建会议记录成功: id=456, meetingId=**********, uuid=uuid-xxx, type=PMI会议
INFO  c.z.service.ZoomMeetingService - 已启动Webhook会议的计费监控: meetingId=456
```

### 安排会议
```
INFO  c.z.service.ZoomMeetingService - 找到对应的安排会议记录: meetingId=**********, creatorUserId=789, topic=项目讨论会议
INFO  c.z.service.ZoomMeetingService - 这是通过会议安排功能创建的会议: meetingId=**********
INFO  c.z.service.ZoomMeetingService - 从Webhook创建会议记录成功: id=457, meetingId=**********, uuid=uuid-yyy, type=安排会议
```

### 其他会议
```
INFO  c.z.service.ZoomMeetingService - 这是其他类型的会议（可能是Zoom App直接创建）: meetingId=5555555555
INFO  c.z.service.ZoomMeetingService - 从Webhook创建会议记录成功: id=458, meetingId=5555555555, uuid=uuid-zzz, type=其他会议
```

## 业务价值

1. **全量监控**: 所有类型的会议都会在"Zoom会议看板"中显示
2. **统一管理**: 不同来源的会议使用统一的数据结构
3. **智能识别**: 自动识别会议类型并关联相应的业务数据
4. **完整追踪**: 从会议创建到结束的完整生命周期管理

## 注意事项

1. **数据一致性**: 确保`t_meetings`和`t_zoom_meetings`的数据同步
2. **性能考虑**: 查询操作按优先级进行，先查PMI再查安排会议
3. **错误处理**: 每个查询步骤都有异常处理，确保流程不中断
4. **日志记录**: 详细记录会议类型识别过程，便于问题排查

## 扩展性

该实现支持未来扩展：
- 添加新的会议类型识别逻辑
- 增加更多的业务数据关联
- 支持更复杂的会议分类规则
