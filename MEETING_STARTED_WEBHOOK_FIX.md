# Meeting.Started Webhook问题修复

## 🐛 问题描述

用户报告：`"event_ts": 1754237146898` 未能按照预期生成t_zoom_meetings记录。

## 🔍 问题分析

经过代码审查，发现了以下几个问题：

### 1. 重复处理逻辑冲突
WebhookController中同时调用了三个不同的处理方法：
```java
// 问题代码
zoomMeetingEventService.handleMeetingStarted(meetingId, hostId, meetingUuid, topic);
zoomMeetingService.handleMeetingStarted(meetingUuid, meetingId, hostId, topic);
zoomMeetingService.handlePmiMeetingStarted(meetingUuid, meetingId, hostId, topic);
```

这可能导致：
- 重复处理同一个事件
- 逻辑冲突和数据不一致
- 性能问题

### 2. 数据库约束问题
ZoomMeeting实体的`pmi_record_id`字段被标记为`@NotNull`和`nullable = false`：
```java
// 问题代码
@NotNull(message = "PMI记录ID不能为空")
@Column(name = "pmi_record_id", nullable = false)
private Long pmiRecordId;
```

但是对于非PMI会议（安排会议、Zoom App会议），需要将此字段设置为null，导致数据库约束违反。

### 3. 缺少必填字段验证
createMeetingRecordFromWebhook方法没有验证meetingUuid和meetingId是否为空，可能导致@NotBlank约束违反。

### 4. 缺少重复UUID检查
没有检查UUID是否已存在，可能导致unique约束违反。

## ✅ 修复方案

### 1. 简化Webhook处理逻辑
**文件**: `src/main/java/com/zoombus/controller/WebhookController.java`

```java
// 修复后的代码
private void handleMeetingStarted(String meetingUuid, String meetingId, String hostId, String topic) {
    try {
        log.info("处理会议开始事件: uuid={}, id={}, hostId={}, topic={}", meetingUuid, meetingId, hostId, topic);

        // 使用统一的会议开始处理逻辑（包含查找现有记录和创建新记录的完整逻辑）
        zoomMeetingService.handleMeetingStarted(meetingUuid, meetingId, hostId, topic);

        log.info("会议开始事件处理完成: {}", meetingUuid);

    } catch (Exception e) {
        log.error("处理会议开始事件失败: uuid={}", meetingUuid, e);
    }
}
```

**改进**：
- 移除重复的处理调用
- 使用统一的处理逻辑
- 简化错误处理

### 2. 修复数据库约束问题
**文件**: `src/main/java/com/zoombus/entity/ZoomMeeting.java`

```java
// 修复后的代码
@Column(name = "pmi_record_id", nullable = true)
private Long pmiRecordId;
```

**数据库迁移**: `src/main/resources/db/migration/V1.3__allow_null_pmi_record_id.sql`

```sql
-- 修改t_zoom_meetings表，允许pmi_record_id为null
ALTER TABLE t_zoom_meetings 
MODIFY COLUMN pmi_record_id BIGINT NULL COMMENT 'PMI记录ID（PMI会议时有值，非PMI会议时为null）';

-- 添加索引以提高查询性能
CREATE INDEX idx_zoom_meetings_pmi_record_id ON t_zoom_meetings(pmi_record_id);
CREATE INDEX idx_zoom_meetings_status_start_time ON t_zoom_meetings(status, start_time);
CREATE INDEX idx_zoom_meetings_host_id ON t_zoom_meetings(host_id);
```

### 3. 增强字段验证和重复检查
**文件**: `src/main/java/com/zoombus/service/ZoomMeetingService.java`

```java
// 修复后的代码
private void createMeetingRecordFromWebhook(String meetingUuid, String meetingId, String hostId, String topic) {
    log.info("从Webhook创建新会议记录: uuid={}, meetingId={}, hostId={}", meetingUuid, meetingId, hostId);

    // 验证必填字段
    if (meetingUuid == null || meetingUuid.trim().isEmpty()) {
        log.error("meetingUuid不能为空，无法创建会议记录");
        return;
    }
    if (meetingId == null || meetingId.trim().isEmpty()) {
        log.error("meetingId不能为空，无法创建会议记录");
        return;
    }

    // 检查是否已存在相同UUID的记录
    Optional<ZoomMeeting> existingMeeting = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid.trim());
    if (existingMeeting.isPresent()) {
        log.warn("会议UUID {} 已存在，跳过创建新记录，记录ID: {}", meetingUuid, existingMeeting.get().getId());
        return;
    }

    // 继续创建逻辑...
}
```

## 🎯 修复效果

### 1. 解决重复处理问题
- 统一使用`zoomMeetingService.handleMeetingStarted()`
- 避免逻辑冲突和重复操作
- 提高处理效率

### 2. 支持所有类型会议
- **PMI会议**：pmi_record_id有值，关联PMI记录
- **安排会议**：pmi_record_id为null，关联t_meetings记录
- **其他会议**：pmi_record_id为null，使用默认设置

### 3. 增强错误处理
- 必填字段验证
- 重复UUID检查
- 详细的错误日志

### 4. 数据库兼容性
- 支持pmi_record_id为null
- 添加性能优化索引
- 向后兼容现有数据

## 🧪 测试验证

### 测试数据准备
使用`test_meeting_started_webhook.sql`脚本创建测试数据：
- PMI记录：`test-pmi-123456`
- 安排会议：`test-meeting-789012`

### 测试场景

#### 场景1: PMI会议启动
```json
{
  "event": "meeting.started",
  "payload": {
    "object": {
      "uuid": "test-uuid-pmi-123",
      "id": "test-pmi-123456",
      "host_id": "test-host-123",
      "topic": "PMI测试会议"
    }
  }
}
```
**预期结果**：创建t_zoom_meetings记录，pmi_record_id有值

#### 场景2: 安排会议启动
```json
{
  "event": "meeting.started",
  "payload": {
    "object": {
      "uuid": "test-uuid-scheduled-456",
      "id": "test-meeting-789012",
      "host_id": "test-host-456",
      "topic": "安排会议测试"
    }
  }
}
```
**预期结果**：创建t_zoom_meetings记录，pmi_record_id为null

#### 场景3: 其他会议启动
```json
{
  "event": "meeting.started",
  "payload": {
    "object": {
      "uuid": "test-uuid-other-789",
      "id": "test-other-999999",
      "host_id": "test-host-789",
      "topic": "Zoom App会议"
    }
  }
}
```
**预期结果**：创建t_zoom_meetings记录，pmi_record_id为null

### 验证查询
```sql
SELECT 
    zm.id,
    zm.zoom_meeting_uuid,
    zm.zoom_meeting_id,
    zm.pmi_record_id,
    zm.topic,
    zm.status,
    zm.billing_mode,
    CASE 
        WHEN zm.pmi_record_id IS NOT NULL THEN 'PMI会议'
        WHEN m.id IS NOT NULL THEN '安排会议'
        ELSE '其他会议'
    END as meeting_type
FROM t_zoom_meetings zm
LEFT JOIN t_meetings m ON zm.zoom_meeting_id = m.zoom_meeting_id
WHERE zm.zoom_meeting_uuid LIKE 'test-uuid-%'
ORDER BY zm.created_at;
```

## 📋 部署步骤

1. **应用数据库迁移**：
   ```bash
   # 执行迁移脚本
   ./mvnw flyway:migrate
   ```

2. **重启应用**：
   ```bash
   # 重启后端应用
   ./mvnw spring-boot:run
   ```

3. **验证修复**：
   - 运行测试SQL脚本
   - 发送测试webhook请求
   - 检查t_zoom_meetings表记录

## ✅ 总结

通过以上修复，解决了meeting.started事件未能生成t_zoom_meetings记录的问题：

1. **🔧 简化处理逻辑**：避免重复处理和逻辑冲突
2. **🗄️ 修复数据库约束**：支持非PMI会议的pmi_record_id为null
3. **🛡️ 增强错误处理**：添加字段验证和重复检查
4. **📊 完整监控覆盖**：所有类型会议都能在"Zoom会议看板"中监控

现在meeting.started事件应该能够正确生成t_zoom_meetings记录，实现全量会议的统一监控。
