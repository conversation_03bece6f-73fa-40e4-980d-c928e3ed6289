# 数据迁移脚本修复说明

## 问题分析

### 发现的问题
在 PMI 184 的迁移中发现，该 PMI 有 26 个计划，但所有的窗口都被错误地分配到了最后一个计划（ID=927）。

### 根本原因
原始迁移脚本中的计划映射逻辑有缺陷：

```sql
-- 原始错误逻辑
WHERE ps.created_at = (
    SELECT MAX(ps2.created_at) 
    FROM t_pmi_schedules ps2 
    WHERE ps2.pmi_record_id = pm.new_pmi_id
);
```

这个逻辑导致所有老计划都映射到了同一个新计划（最新创建的那个），而不是一对一映射。

## 修复内容

### 1. 修复计划映射逻辑 (第277-301行)

**修复前**：
- 所有老计划映射到最新的新计划
- 导致窗口分配错误

**修复后**：
```sql
WHERE ps.id = (
    -- 为每个老计划找到对应的新计划（按创建时间顺序匹配）
    SELECT ps2.id
    FROM t_pmi_schedules ps2 
    WHERE ps2.pmi_record_id = pm.new_pmi_id
    ORDER BY ps2.id
    LIMIT 1 OFFSET (
        -- 计算当前老计划在同一PMI下的排序位置
        SELECT COUNT(*) - 1
        FROM old_t_zoom_plan old2
        WHERE old2.zoompmiid = old.zoompmiid
        AND old2.id <= old.id
        AND old2.start_date IS NOT NULL 
        AND old2.end_date IS NOT NULL
    )
);
```

### 2. 修复窗口迁移逻辑 (第319-361行)

**修复前**：
- 使用 `LIMIT 1` 随机选择计划
- 没有保证正确的映射关系

**修复后**：
- 使用 `ORDER BY ps.id LIMIT 1` 确保一致性
- 添加 `ORDER BY old.plan_id, old.create_time` 保证迁移顺序

### 3. 修复默认计划创建逻辑 (第363-395行)

**修复前**：
- 可能为已有计划的PMI创建重复的默认计划

**修复后**：
```sql
AND pm.new_pmi_id NOT IN (SELECT DISTINCT pmi_record_id FROM t_pmi_schedules)
```
- 确保只为真正没有计划的PMI创建默认计划

### 4. 修复MySQL路径问题

**修复前**：
- 硬编码使用 `mysql` 命令
- 在某些系统上可能找不到命令

**修复后**：
```bash
MYSQL_CMD="/usr/local/mysql-8.0.11-macos10.13-x86_64/bin/mysql"
```
- 使用完整路径确保命令可用

## 验证方法

### 迁移前验证
```sql
-- 检查原始数据中的计划-窗口映射关系
SELECT 
    old_plan.id as old_plan_id,
    old_plan.comments as plan_name,
    COUNT(old_windows.id) as window_count
FROM old_t_zoom_plan old_plan
LEFT JOIN old_t_zoom_windows old_windows ON old_plan.id = old_windows.plan_id
WHERE old_plan.zoompmiid = 'PMI_ID'
GROUP BY old_plan.id, old_plan.comments
ORDER BY old_plan.id;
```

### 迁移后验证
```sql
-- 检查新系统中的计划-窗口分布
SELECT 
    ps.id as schedule_id,
    ps.name,
    ps.pmi_record_id,
    COUNT(psw.id) as window_count
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.pmi_record_id = 184
GROUP BY ps.id, ps.name, ps.pmi_record_id
ORDER BY ps.id;
```

## 预期结果

修复后，PMI 184 应该：
- 有 26 个计划，每个计划对应 1 个窗口
- 窗口正确分配到对应的计划
- 保持原始数据的映射关系

## 使用说明

1. **重新运行迁移脚本**：
   ```bash
   ./run_complete_migration_optimized.sh --test-only
   ```

2. **验证结果**：
   - 检查 PMI 184 的计划分布
   - 确认窗口正确映射
   - 验证其他PMI的数据完整性

3. **如果满意结果**：
   ```bash
   ./run_complete_migration_optimized.sh
   ```

## 注意事项

1. **数据备份**：运行前确保已备份现有数据
2. **测试环境**：建议先在测试环境验证
3. **监控日志**：关注迁移过程中的错误信息
4. **验证完整性**：迁移后进行全面的数据验证

## 技术细节

### 映射算法
新的映射算法使用 `OFFSET` 来确保老计划按顺序映射到新计划：
- 老计划按 ID 排序
- 新计划按 ID 排序  
- 第 N 个老计划映射到第 N 个新计划

### 性能优化
- 使用临时表减少重复查询
- 添加适当的索引提高查询效率
- 批量操作减少事务开销

### 错误处理
- 事务保证数据一致性
- 详细的验证步骤
- 清理临时表避免资源泄露
