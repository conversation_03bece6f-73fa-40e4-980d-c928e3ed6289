# 生产环境迁移快速指南

## 🚀 立即执行迁移

### 推荐方式（完整版）
```bash
./migrate_to_production.sh
```

### 快速方式
```bash
./quick_migrate_to_production.sh
```

## ⭐ 新增功能：字符集自动转换

### 问题解决
- ✅ **自动转换** `utf8mb4_0900_ai_ci` → `utf8mb4_general_ci`
- ✅ **兼容MySQL 5.7.18** 生产环境版本
- ✅ **无需手动处理** 脚本自动检测和转换

### 转换示例
```sql
-- 转换前（本地MySQL 8.0+）
/*!50003 SET collation_connection = utf8mb4_0900_ai_ci */ ;

-- 转换后（兼容MySQL 5.7.18）
/*!50003 SET collation_connection = utf8mb4_general_ci */ ;
```

## 📊 当前数据状态

### 本地环境 → 生产环境
```
表名                    本地    生产    状态
t_users                 625  →  625    ✓ 同步
t_pmi_records           626  →  626    ✓ 同步
t_pmi_schedules         934  →  21     ⚠ 需更新
t_pmi_schedule_windows  296  →  21     ⚠ 需更新
```

## 🛡️ 安全保障

### 自动备份
- ✅ 迁移前自动备份生产数据
- ✅ 备份位置：`/tmp/prod_backup_YYYYMMDD_HHMMSS/`
- ✅ 支持完整回滚

### 确认机制
- ⚠️ 执行前需要手动确认
- ⚠️ 明确提示将清空生产数据

## 🔧 执行步骤

1. **连接检查** - SSH和数据库连接验证
2. **数据统计** - 显示迁移前状态
3. **生产备份** - 自动备份现有数据
4. **清空目标表** - 按依赖关系安全清空
5. **数据导出** - 从本地导出最新数据
6. **字符集转换** ⭐ - 自动转换兼容格式
7. **数据传输** - 安全传输到生产服务器
8. **数据导入** - 按顺序导入到生产环境
9. **结果验证** - 验证迁移完整性
10. **清理工作** - 清理临时文件

## 🎯 预期结果

迁移完成后：
```
✅ t_users:                625 条记录
✅ t_pmi_records:          626 条记录  
✅ t_pmi_schedules:        934 条记录 (从21更新)
✅ t_pmi_schedule_windows: 296 条记录 (从21更新)
```

## 🧪 测试工具

### 字符集转换测试
```bash
./test_charset_conversion.sh
```

### 连接测试
```bash
# SSH连接测试
ssh <EMAIL> "echo 'SSH OK'"

# 数据库连接测试
ssh <EMAIL> "mysql -uroot -pnvshen2018 -e 'SELECT VERSION();'"
```

## ⚡ 执行时间预估

- **完整版迁移**：约5-10分钟
- **快速版迁移**：约3-5分钟
- **数据量**：约1.2MB数据传输

## 🆘 故障排除

### 常见问题

1. **SSH连接失败**
   ```bash
   # 检查SSH密钥
   ssh-add -l
   ```

2. **字符集错误**
   - ✅ 已自动解决：脚本自动转换字符集

3. **外键约束错误**
   - ✅ 已自动解决：脚本按依赖关系处理

4. **权限问题**
   ```bash
   # 检查文件权限
   ls -la migrate_to_production.sh
   chmod +x migrate_to_production.sh
   ```

### 回滚方案
如果迁移失败，使用备份恢复：
```bash
# 连接生产服务器
ssh <EMAIL>

# 找到备份目录
ls -la /tmp/prod_backup_*

# 恢复数据（按实际备份目录替换）
BACKUP_DIR="/tmp/prod_backup_YYYYMMDD_HHMMSS"
mysql -uroot -pnvshen2018 zoombusv < $BACKUP_DIR/t_users_backup.sql
mysql -uroot -pnvshen2018 zoombusv < $BACKUP_DIR/t_pmi_records_backup.sql
mysql -uroot -pnvshen2018 zoombusv < $BACKUP_DIR/t_pmi_schedules_backup.sql
mysql -uroot -pnvshen2018 zoombusv < $BACKUP_DIR/t_pmi_schedule_windows_backup.sql
```

## 📞 支持

- **详细文档**：`PRODUCTION_MIGRATION_README.md`
- **测试脚本**：`test_charset_conversion.sh`
- **备用脚本**：`quick_migrate_to_production.sh`

---

**准备就绪！现在可以安全地执行生产环境迁移了。**

```bash
# 开始迁移
./migrate_to_production.sh
```
