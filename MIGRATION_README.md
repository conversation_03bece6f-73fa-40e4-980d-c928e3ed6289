# 新老系统数据迁移指南

## 概述

本迁移方案用于将老系统的用户数据和PMI数据迁移到新系统中，包括：
- 从 `old_t_wx_user` 迁移到 `t_users`
- 从 `old_t_zoom_pmi` 迁移到 `t_pmi_records`
- 为LONG类型PMI自动创建计划和窗口记录
- 正确迁移剩余可用时长

## 文件说明

### 1. pre_migration_check.sql
**迁移前预检查脚本**
- 验证老系统数据完整性
- 检查新系统表结构
- 识别潜在的数据冲突
- 评估数据质量
- 预估迁移结果

### 2. data_migration_script.sql
**主迁移脚本**
- 执行用户数据迁移
- 执行PMI数据迁移
- 为LONG类型PMI创建计划和窗口
- 处理剩余时长迁移
- 包含完整的事务控制

### 3. post_migration_validation.sql
**迁移后验证脚本**
- 验证数据量的正确性
- 检查数据完整性
- 验证关联关系
- 检查业务逻辑
- 计算迁移成功率

## 执行步骤

### 第一步：执行预检查
```bash
mysql -u root -pnvshen2018 -D zoombusV < pre_migration_check.sql
```

**检查要点：**
- 确认老系统表存在且有数据
- 确认新系统表结构正确
- 检查是否有数据冲突
- 评估数据质量问题

### 第二步：备份现有数据
```bash
# 备份新系统当前数据
mysqldump -u root -pnvshen2018 zoombusV t_users t_pmi_records t_pmi_schedules t_pmi_schedule_windows > backup_before_migration.sql
```

### 第三步：执行迁移
```bash
mysql -u root -pnvshen2018 -D zoombusV < data_migration_script.sql
```

**注意事项：**
- 脚本使用事务控制，如果出错会自动回滚
- 迁移过程中会显示进度信息
- 建议在低峰期执行

### 第四步：验证迁移结果
```bash
mysql -u root -pnvshen2018 -D zoombusV < post_migration_validation.sql
```

**验证要点：**
- 数据量是否正确
- 关联关系是否完整
- LONG类型PMI是否正确创建了计划和窗口
- 时长数据是否正确迁移

## 迁移逻辑说明

### 用户数据迁移 (old_t_wx_user -> t_users)

**字段映射：**
- `username`: 优先使用 `real_name`，其次 `nick_name`，最后生成 `user_${id}`
- `email`: 优先使用 `mobile`，其次生成 `user_${id}@temp.com`
- `full_name`: 优先使用 `real_name`，其次 `nick_name`，最后生成 `用户_${id}`
- `phone`: 使用 `mobile`
- `status`: 根据 `del_flag` 判断，1为INACTIVE，其他为ACTIVE

### PMI数据迁移 (old_t_zoom_pmi -> t_pmi_records)

**字段映射：**
- `user_id`: 通过用户名或邮箱匹配新用户ID
- `pmi_number`: 直接使用 `pmi`
- `pmi_password`: 使用 `pmi_password`，默认为 '123456'
- `billing_mode`: `now_plan_type` 为 'LONG' 则为 'LONG'，否则为 'BY_TIME'
- `total_minutes`: `durationh * 60 + durationm`
- `available_minutes`: 总时长 - 冻结时长 - 长期冻结时长
- `billing_status`: 根据 `del_flag` 和 `plan_end_date_time` 判断

### LONG类型PMI特殊处理

**自动创建计划 (t_pmi_schedules):**
- 计划名称: `长期计划_${pmi_number}`
- 开始日期: 当前日期
- 结束日期: 从 `plan_end_date_time` 解析，默认为一年后
- 重复类型: DAILY（每日重复）
- 全天: TRUE

**自动创建窗口 (t_pmi_schedule_windows):**
- 窗口日期: 当前日期
- 结束日期: 使用计划的结束日期
- 时间范围: 00:00:00 - 23:59:59（全天）
- 状态: 根据结束日期判断（PENDING/ACTIVE/COMPLETED）

**更新PMI记录:**
- `current_window_id`: 设置为创建的窗口ID
- `window_expire_time`: 设置为窗口结束时间
- `active_window_ids`: JSON数组包含窗口ID

## 数据验证要点

### 关键验证项
1. **数据量验证**: 确保迁移的记录数与源数据匹配
2. **LONG类型完整性**: 每个LONG类型PMI都有对应的计划和窗口
3. **时长数据准确性**: 总时长和可用时长计算正确
4. **关联关系完整**: 用户-PMI、PMI-计划、计划-窗口关联正确
5. **数据格式正确**: PMI号码格式、邮箱格式等符合要求

### 常见问题处理

**用户映射失败:**
- 症状: PMI记录的 `user_id` 为 1（默认用户）
- 原因: 老系统用户数据与新系统用户无法匹配
- 处理: 手动检查和修正用户映射关系

**PMI格式问题:**
- 症状: PMI号码长度不是10位或包含非数字字符
- 原因: 老系统数据质量问题
- 处理: 清理或修正PMI号码格式

**时长数据异常:**
- 症状: 可用时长为负数或异常大
- 原因: 老系统冻结时长数据异常
- 处理: 检查和修正时长计算逻辑

## 回滚方案

如果迁移出现问题，可以使用以下方式回滚：

```bash
# 方式1: 使用备份恢复
mysql -u root -pnvshen2018 -D zoombusV < backup_before_migration.sql

# 方式2: 清空迁移的数据
mysql -u root -pnvshen2018 -D zoombusV -e "
DELETE FROM t_pmi_schedule_windows WHERE created_at >= '迁移开始时间';
DELETE FROM t_pmi_schedules WHERE created_at >= '迁移开始时间';
DELETE FROM t_pmi_records WHERE created_at >= '迁移开始时间';
DELETE FROM t_users WHERE created_at >= '迁移开始时间';
"
```

## 注意事项

1. **执行环境**: 确保在正确的数据库环境中执行
2. **权限要求**: 需要数据库的读写权限
3. **时间安排**: 建议在业务低峰期执行
4. **监控**: 密切监控迁移过程和结果
5. **测试**: 在生产环境执行前，先在测试环境验证

## 联系支持

如果在迁移过程中遇到问题，请：
1. 保存错误信息和相关日志
2. 记录执行到哪个步骤出现问题
3. 联系技术支持团队协助处理
