# Ngrok 跨域配置解决方案

## 🎯 问题描述

通过ngrok域名 `https://3c61cbcb7ae6.ngrok-free.app` 访问ZoomBus应用时遇到403错误，这是由于后端CORS配置不允许ngrok域名访问导致的。

## ✅ 已完成的配置修改

### 1. 更新CORS配置类

**文件**: `src/main/java/com/zoombus/config/CorsConfig.java`

**修改内容**:
- 添加了ngrok域名模式支持
- 更新了 `getAllowedOriginPatterns()` 方法

```java
private String[] getAllowedOriginPatterns() {
    return new String[] {
        // 生产环境
        "https://m.zoombus.com",
        "http://m.zoombus.com",
        "https://*.zoombus.com",
        "http://*.zoombus.com",
        
        // 开发环境
        "http://localhost:*",
        "http://127.0.0.1:*",
        "https://localhost:*",
        "https://127.0.0.1:*",
        
        // ngrok隧道支持
        "https://*.ngrok.io",
        "https://*.ngrok-free.app",
        "http://*.ngrok.io",
        "http://*.ngrok-free.app"
    };
}
```

### 2. 更新应用配置文件

**文件**: `src/main/resources/application.yml`

**添加内容**:
```yaml
# CORS配置
cors:
  allowed-origins: 
    - https://m.zoombus.com
    - http://m.zoombus.com
    - http://localhost:3000
    - http://127.0.0.1:3000
    - https://3c61cbcb7ae6.ngrok-free.app
    - https://*.ngrok.io
    - https://*.ngrok-free.app
```

### 3. 更新默认允许源列表

**修改**: `@Value` 注解中的默认值，添加了当前ngrok域名

## 🔧 配置特点

### 支持的域名模式
1. **生产环境**: `*.zoombus.com`
2. **开发环境**: `localhost:*`, `127.0.0.1:*`
3. **ngrok隧道**: `*.ngrok.io`, `*.ngrok-free.app`
4. **特定域名**: `https://3c61cbcb7ae6.ngrok-free.app`

### CORS策略
- **允许的方法**: GET, POST, PUT, DELETE, OPTIONS, PATCH
- **允许的头部**: 所有头部 (`*`)
- **允许凭证**: `true`
- **预检缓存**: 3600秒
- **暴露的头部**: Authorization, Content-Type等

## 🚀 使用方法

### 1. 重启后端应用
```bash
# 停止现有进程
lsof -ti:8080 | xargs kill -9

# 启动应用（使用Java 11）
export JAVA_HOME=/Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
./mvnw spring-boot:run
```

### 2. 验证配置
使用提供的测试工具验证CORS配置：

**方法1: 使用测试脚本**
```bash
./test-ngrok-cors.sh
```

**方法2: 使用测试页面**
1. 在浏览器中打开 `test-cors.html`
2. 点击各个测试按钮
3. 查看测试结果

**方法3: 直接访问ngrok域名**
访问: `https://3c61cbcb7ae6.ngrok-free.app`

## 📋 测试检查点

### 成功标志
1. ✅ 预检请求返回正确的CORS头部
2. ✅ 实际请求不再返回403错误
3. ✅ 浏览器控制台无CORS错误
4. ✅ 可以正常登录和使用功能

### 预期响应头
```
Access-Control-Allow-Origin: https://3c61cbcb7ae6.ngrok-free.app
Access-Control-Allow-Methods: GET,POST,PUT,DELETE,OPTIONS,PATCH
Access-Control-Allow-Headers: *
Access-Control-Allow-Credentials: true
Access-Control-Max-Age: 3600
```

## 🔍 故障排除

### 如果仍然遇到403错误

1. **检查应用是否重启**:
```bash
lsof -i:8080  # 确认应用在运行
```

2. **检查日志**:
查看应用启动日志中的CORS配置信息

3. **验证配置加载**:
确认新的配置文件已被加载

4. **清除浏览器缓存**:
清除浏览器缓存和Cookie

### 如果ngrok域名变化

当ngrok重新启动时域名会变化，需要：

1. **更新配置文件**中的具体域名
2. **重启应用**使配置生效
3. 或者依赖通配符模式 `*.ngrok-free.app`

## 📝 注意事项

1. **安全性**: 生产环境应限制允许的域名
2. **性能**: CORS预检请求有缓存，不会影响性能
3. **维护**: ngrok域名变化时需要更新配置
4. **测试**: 每次配置更改后都应进行测试验证

## 🎉 预期结果

配置完成后，您应该能够：
- ✅ 通过ngrok域名正常访问应用
- ✅ 成功进行登录操作
- ✅ 使用所有前端功能
- ✅ 无CORS相关错误

现在您可以通过 `https://3c61cbcb7ae6.ngrok-free.app` 正常访问ZoomBus应用了！
