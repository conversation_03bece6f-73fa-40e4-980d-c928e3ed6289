# Ngrok 自定义服务器最终解决方案

## 🎯 问题总结
连接自定义ngrok服务器 `ngrok.nslcp.com:4443` 时遇到TLS证书验证失败，主要原因：
1. 服务器使用自签名证书
2. ngrok v3客户端严格验证TLS证书
3. 客户端与服务器版本不兼容

## ✅ 已完成的升级工作

### 服务器端升级
- ✅ 备份原有ngrok配置
- ✅ 生成新的SAN证书（符合现代TLS标准）
- ✅ 重启ngrokd服务器，使用新证书
- ✅ 服务器运行状态：PID 14285，监听4443端口

### 客户端配置
- ✅ 升级ngrok配置为v3格式
- ✅ 创建多个启动脚本处理不同场景
- ✅ 下载服务器证书到本地

## 🔧 解决方案选项

### 方案1: 信任服务器证书（推荐）
```bash
# 将服务器证书添加到系统信任存储
./setup-ngrok-certificate.sh

# 然后使用标准启动脚本
./start-ngrok-simple.sh
```

### 方案2: 使用TCP隧道
```bash
# TCP协议可以避免部分TLS问题
./start-ngrok-tcp.sh
```

### 方案3: 手动添加证书信任
```bash
# 手动添加证书到macOS钥匙串
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain server_san.crt

# 然后测试连接
./start-ngrok-simple.sh
```

### 方案4: 使用官方ngrok服务器
```bash
# 需要先验证邮箱
./test-ngrok-basic.sh
```

## 📋 可用脚本列表

1. **setup-ngrok-certificate.sh** - 设置证书信任
2. **start-ngrok-simple.sh** - 标准启动（需要信任证书）
3. **start-ngrok-tcp.sh** - TCP隧道启动
4. **start-ngrok-ultimate.sh** - 多方案尝试
5. **test-ngrok-basic.sh** - 测试官方服务器
6. **stop-ngrok.sh** - 停止ngrok进程

## 🚀 推荐使用步骤

1. **首先设置证书信任**：
```bash
./setup-ngrok-certificate.sh
```

2. **启动ngrok隧道**：
```bash
./start-ngrok-simple.sh
```

3. **如果仍有问题，尝试TCP隧道**：
```bash
./start-ngrok-tcp.sh
```

## 📝 服务器状态

- **服务器地址**: ngrok.nslcp.com:4443
- **HTTP端口**: 8964
- **HTTPS端口**: 8943
- **证书**: server_san.crt（包含SAN）
- **状态**: 运行中

## ⚠️ 注意事项

1. 自签名证书仅适用于测试环境
2. 生产环境建议使用CA签名的证书
3. 添加证书到系统信任存储需要管理员权限
4. 确保本地应用在8080端口运行

## 🔍 故障排除

如果仍然遇到问题：

1. **检查服务器状态**：
```bash
ssh <EMAIL> "ps aux | grep ngrok"
```

2. **查看详细日志**：
```bash
./start-ngrok-simple.sh  # 查看连接日志
```

3. **验证证书**：
```bash
openssl s_client -connect ngrok.nslcp.com:4443 -servername ngrok.nslcp.com
```

4. **重新生成证书**（如需要）：
```bash
# 在服务器上重新生成证书
ssh <EMAIL>
cd /usr/local/ngrok
# 重新生成证书的命令...
```

## 📞 技术支持

如果以上方案都无法解决问题，建议：
1. 检查网络连接
2. 确认服务器防火墙设置
3. 考虑使用官方ngrok服务器作为备选方案
