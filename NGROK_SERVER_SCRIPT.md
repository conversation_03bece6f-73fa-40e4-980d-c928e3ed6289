# ngrok服务器启动脚本说明

## 📁 文件位置

**服务器路径**: `/root/zoombus/start-ngrok-server.sh`

## 🎯 功能说明

这个脚本是从`start.sh`的选项8中抽取的独立ngrok启动功能，专门用于在服务器上启动ngrok隧道。

### 主要功能
- 启动ngrok隧道连接到本地8080端口
- 使用固定域名：`patient-correctly-pipefish.ngrok-free.app`
- 提供HTTPS访问，适合webhook测试
- 自动检测和处理ngrok进程
- 详细的错误诊断和日志记录

## 🔧 配置信息

### ngrok固定域名配置
```bash
NGROK_DOMAIN="patient-correctly-pipefish.ngrok-free.app"
NGROK_DOMAIN_ID="rd_30XRXvkrG7BXth1jBbMadiQiQwV"
```

### 映射端口
- **本地端口**: 8080 (Spring Boot应用)
- **公网访问**: https://patient-correctly-pipefish.ngrok-free.app

## 🚀 使用方法

### 1. 基本启动
```bash
# 进入zoombus目录
cd /root/zoombus

# 启动ngrok隧道
./start-ngrok-server.sh
```

### 2. 后台运行
```bash
# 后台启动ngrok隧道
nohup ./start-ngrok-server.sh > ngrok-tunnel.log 2>&1 &

# 查看日志
tail -f ngrok-tunnel.log
```

### 3. 停止隧道
```bash
# 查找ngrok进程
pgrep -f ngrok

# 停止ngrok进程
pkill -f ngrok
```

## 📋 前置条件

### 1. ngrok安装
脚本会自动检测ngrok是否安装，如果未安装会提示：
```
⚠ 未检测到ngrok，webhook功能将不可用
  安装ngrok: https://ngrok.com/download
```

### 2. ngrok认证配置
需要配置ngrok认证token：
```bash
# 1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken
# 2. 复制认证token
# 3. 配置token
ngrok config add-authtoken YOUR_TOKEN
```

### 3. 后端服务运行
确保ZoomBus后端服务在8080端口运行：
```bash
# 检查服务状态
pgrep -f zoombus-1.0.0.jar

# 检查端口占用
netstat -tlnp | grep :8080
```

## 📊 脚本输出示例

### 成功启动
```
=== ZoomBus ngrok隧道启动脚本 ===
✓ 检测到ngrok
=== 仅启动ngrok隧道 ===
启动ngrok隧道连接到localhost:8080...
启动ngrok隧道...
检查ngrok认证配置...
启动ngrok隧道到localhost:8080...
使用固定域名: patient-correctly-pipefish.ngrok-free.app
ngrok进程ID: 12345
等待ngrok启动...
等待ngrok隧道建立...
固定域名URL: https://patient-correctly-pipefish.ngrok-free.app
✓ 隧道建立成功 (3/15)
测试隧道连通性...
✅ 隧道连通性测试成功

🎉 ngrok隧道启动成功!
🌐 固定域名: patient-correctly-pipefish.ngrok-free.app
🔗 公网访问地址: https://patient-correctly-pipefish.ngrok-free.app
🎯 Webhook URL: https://patient-correctly-pipefish.ngrok-free.app/api/webhooks/zoom/{account_id}
📊 ngrok控制台: http://localhost:4040
📋 隧道信息: curl http://localhost:4040/api/tunnels

=== ngrok隧道启动完成 ===
💡 使用提示:
   - 使用固定域名，URL永远不变
   - 确保后端服务在8080端口运行
   - 将Webhook URL配置到Zoom开发者控制台
   - 隧道提供HTTPS访问，适合webhook测试

按 Ctrl+C 停止ngrok隧道
```

### 启动失败
```
❌ ngrok隧道启动失败
查看详细错误信息:
[错误日志内容]

故障排除建议:
1. 检查认证token是否正确
2. 检查网络连接
3. 查看完整日志: cat ngrok.log
```

## 🔍 故障排除

### 1. ngrok未安装
```bash
# 下载并安装ngrok
wget https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz
tar -xzf ngrok-v3-stable-linux-amd64.tgz
sudo mv ngrok /usr/local/bin/
```

### 2. 认证token问题
```bash
# 检查当前配置
ngrok config check

# 重新配置token
ngrok config add-authtoken YOUR_TOKEN
```

### 3. 端口占用问题
```bash
# 检查8080端口
netstat -tlnp | grep :8080

# 如果端口被占用，停止相关进程或更改端口
```

### 4. 网络连接问题
```bash
# 测试网络连接
ping ngrok.com

# 检查防火墙设置
iptables -L
```

## 📝 日志文件

### ngrok.log
脚本运行时会生成`ngrok.log`文件，包含详细的ngrok运行日志：
```bash
# 查看实时日志
tail -f ngrok.log

# 查看错误信息
grep -i error ngrok.log
```

## 🎯 Webhook配置

### Zoom开发者控制台配置
使用以下URL配置Zoom Webhook：
```
Event notification endpoint URL: https://patient-correctly-pipefish.ngrok-free.app/api/webhooks/zoom/KNDMAXZ_SVGeAgOTaK_TEw
```

### 测试Webhook
```bash
# 测试端点验证
curl -X POST https://patient-correctly-pipefish.ngrok-free.app/api/webhooks/zoom/KNDMAXZ_SVGeAgOTaK_TEw \
  -H "Content-Type: application/json" \
  -d '{"event":"endpoint.url_validation","payload":{"plainToken":"test123"}}'

# 期望响应
{"plainToken":"test123"}
```

## 💡 使用建议

### 开发环境
1. **启动后端服务**
2. **运行ngrok脚本**
3. **配置Zoom Webhook**
4. **测试webhook事件**

### 生产环境
- 建议使用固定域名而不是ngrok
- 生产环境使用：`https://m.zoombus.com`

### 监控和维护
```bash
# 检查ngrok进程状态
ps aux | grep ngrok

# 检查隧道状态
curl http://localhost:4040/api/tunnels

# 重启隧道（如果需要）
pkill -f ngrok && ./start-ngrok-server.sh
```

## 🎉 总结

这个脚本提供了完整的ngrok隧道启动功能，包括：
- ✅ 自动环境检测
- ✅ 固定域名配置
- ✅ 详细错误处理
- ✅ 连通性测试
- ✅ 完整的使用说明

现在您可以在服务器上独立启动ngrok隧道，为开发环境提供稳定的webhook测试能力！
