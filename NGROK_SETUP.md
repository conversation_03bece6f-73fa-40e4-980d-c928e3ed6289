# ngrok配置指南

## 📋 概述

本项目集成了ngrok隧道功能，用于将本地开发环境暴露到公网，特别适用于Zoom Webhook的开发和测试。

## 🚀 快速开始

### 1. 安装ngrok

#### macOS
```bash
# 使用Homebrew安装
brew install ngrok/ngrok/ngrok

# 或者下载二进制文件
curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
```

#### Windows
```bash
# 使用Chocolatey
choco install ngrok

# 或者使用Scoop
scoop bucket add extras
scoop install ngrok
```

#### Linux
```bash
# 下载并安装
curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | sudo tee /etc/apt/sources.list.d/ngrok.list
sudo apt update && sudo apt install ngrok
```

### 2. 获取认证Token

1. 访问 [ngrok Dashboard](https://dashboard.ngrok.com/get-started/your-authtoken)
2. 注册或登录ngrok账号
3. 复制您的认证token

### 3. 配置认证Token

```bash
# 方法1: 使用命令行配置
ngrok config add-authtoken YOUR_AUTH_TOKEN

# 方法2: 编辑配置文件
# 取消注释 ngrok.yml 中的 authtoken 行并填入您的token
```

### 4. 启动服务

使用启动脚本选择包含ngrok的模式：

```bash
./start.sh
# 选择 "2. 开发模式 + ngrok (包含webhook隧道)"
# 或选择 "8. 仅启动ngrok隧道"
```

## 🔧 配置说明

### ngrok.yml配置文件

项目包含预配置的`ngrok.yml`文件，主要配置：

```yaml
version: "2"
authtoken: your_auth_token_here  # 需要配置您的token

tunnels:
  zoombus:
    proto: http
    addr: 8080
    request_header:
      add:
        - "ngrok-skip-browser-warning: true"

region: us
web_addr: localhost:4040
```

### 主要配置项说明

- **authtoken**: 您的ngrok认证token
- **proto**: 协议类型（http/https/tcp/tls）
- **addr**: 本地服务端口（8080）
- **region**: 服务器区域（us/eu/ap/au/sa/jp/in）
- **web_addr**: ngrok Web界面地址

## 🌐 使用方式

### 启动模式

1. **开发模式 + ngrok**: 启动完整开发环境 + 公网隧道
2. **仅启动ngrok隧道**: 只启动ngrok隧道（需要后端服务已运行）

### 获取Webhook URL

启动成功后，您将看到：

```
✓ ngrok隧道启动成功
🌐 公网访问地址: https://abc123.ngrok-free.app
🔗 Webhook URL: https://abc123.ngrok-free.app/api/webhooks/zoom/{account_id}
📊 ngrok控制台: http://localhost:4040
```

### 配置Zoom Webhook

1. 登录 [Zoom Marketplace](https://marketplace.zoom.us/)
2. 进入您的应用设置
3. 在"Event Subscriptions"中配置：
   - **Event notification endpoint URL**: `https://your-ngrok-url.ngrok-free.app/api/webhooks/zoom/{account_id}`
   - **Event types**: 选择需要的事件（如 Meeting Created）

## 📊 监控和调试

### ngrok Web界面

访问 http://localhost:4040 查看：
- 实时请求日志
- 请求/响应详情
- 隧道状态信息
- 流量统计

### 日志文件

ngrok日志保存在 `ngrok.log` 文件中：

```bash
# 查看实时日志
tail -f ngrok.log

# 查看错误日志
grep ERROR ngrok.log
```

## 🔒 安全注意事项

### 免费版限制

- 随机生成的URL（每次重启都会变化）
- 有访问频率限制
- 显示ngrok品牌页面

### 付费版功能

- 自定义子域名
- 固定域名
- IP白名单
- 密码保护
- 更高的并发限制

### 生产环境建议

⚠️ **不建议在生产环境使用ngrok**

生产环境建议：
- 使用专用服务器和域名
- 配置SSL证书
- 设置防火墙和安全组
- 使用负载均衡器

## 🛠️ 故障排除

### 常见问题

1. **认证失败**
   ```
   ERROR: authentication failed
   ```
   解决：检查authtoken是否正确配置

2. **端口占用**
   ```
   ERROR: bind: address already in use
   ```
   解决：检查8080端口是否被占用

3. **隧道连接失败**
   ```
   ERROR: failed to connect to ngrok service
   ```
   解决：检查网络连接，尝试更换region

4. **Webhook验证失败**
   ```
   Webhook签名验证失败
   ```
   解决：检查webhook_secret_token配置

### 调试命令

```bash
# 检查ngrok状态
curl http://localhost:4040/api/tunnels

# 测试本地服务
curl http://localhost:8080/actuator/health

# 测试公网访问
curl https://your-ngrok-url.ngrok-free.app/actuator/health
```

## 📚 相关文档

- [ngrok官方文档](https://ngrok.com/docs)
- [Zoom Webhook文档](https://marketplace.zoom.us/docs/api-reference/webhook-reference)
- [ZoomBus Webhook实现文档](./MEETING_CREATED_WEBHOOK_IMPLEMENTATION.md)

## 💡 开发提示

1. **开发阶段**: 使用免费版ngrok即可满足需求
2. **测试阶段**: 考虑使用付费版获得固定URL
3. **生产阶段**: 部署到云服务器，不依赖ngrok
4. **调试技巧**: 利用ngrok Web界面查看请求详情
5. **安全考虑**: 不要在公共网络暴露敏感信息
