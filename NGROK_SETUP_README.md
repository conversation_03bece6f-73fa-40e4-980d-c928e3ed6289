# Ngrok 隧道配置说明

本项目已配置ngrok客户端，用于连接到自定义ngrok服务器 `ngrok.nslcp.com:4443`，并映射本机8080端口。

## 文件说明

### 1. ngrok.yml
ngrok配置文件，包含以下配置：
- 服务器地址：`ngrok.nslcp.com:4443`
- HTTP隧道：映射本机8080端口
- TLS隧道：映射本机8080端口（用于HTTPS访问）
- Web界面：`http://localhost:4040`

### 2. start-ngrok.sh
ngrok启动脚本，支持多种启动方式：

```bash
# 启动所有隧道（HTTP + TLS）
./start-ngrok.sh

# 仅启动HTTP隧道
./start-ngrok.sh http

# 仅启动TLS隧道（HTTPS）
./start-ngrok.sh tls

# 显示帮助信息
./start-ngrok.sh help
```

### 3. stop-ngrok.sh
ngrok停止脚本，用于停止所有ngrok进程：

```bash
./stop-ngrok.sh
```

### 4. start-ngrok-insecure.sh
专门用于处理TLS证书验证问题的启动脚本（跳过证书验证）：

```bash
# 启动HTTP隧道（推荐，避免TLS问题）
./start-ngrok-insecure.sh

# 启动TLS隧道
./start-ngrok-insecure.sh tls
```

## 使用步骤

### 1. 确保应用运行
在启动ngrok之前，确保您的应用已在8080端口运行：

```bash
# 启动后端应用
./start.sh
```

### 2. 启动ngrok隧道

**如果遇到TLS证书验证问题，推荐使用：**
```bash
# 启动ngrok隧道（跳过证书验证）
./start-ngrok-insecure.sh
```

**正常启动方式：**
```bash
# 启动ngrok隧道
./start-ngrok.sh
```

### 3. 查看隧道信息
启动后，可以通过以下方式查看隧道信息：
- 访问Web界面：http://localhost:4040
- 查看终端输出的隧道URL

### 4. 停止ngrok
```bash
# 停止ngrok隧道
./stop-ngrok.sh
```

## 配置说明

### 认证Token（如需要）
如果ngrok服务器需要认证token，请编辑 `ngrok.yml` 文件，取消注释并设置实际的token：

```yaml
# 认证token（如果服务器需要，请替换为实际token）
authtoken: your_actual_auth_token_here
```

### 自定义子域名（如需要）
如果需要指定子域名，请编辑 `ngrok.yml` 文件，取消注释并设置子域名：

```yaml
tunnels:
  zoombus-http:
    proto: http
    addr: 8080
    subdomain: your-subdomain  # 取消注释并设置
```

## 故障排除

### 1. ngrok客户端不存在
如果提示ngrok客户端不存在，请检查路径：`/Users/<USER>/bin/ngrok-v3/ngrok`

### 2. 端口8080未被占用
如果提示端口8080未被占用，请先启动您的应用。

### 3. TLS证书验证失败
如果遇到类似错误：`failed to verify certificate: x509:`

**解决方案：**
```bash
# 使用跳过证书验证的脚本
./start-ngrok-insecure.sh
```

### 4. 连接服务器失败
请检查：
- 网络连接是否正常
- 服务器地址是否正确：`ngrok.nslcp.com:4443`
- 是否需要认证token

### 5. 查看详细日志
启动ngrok时会显示详细的连接信息和错误信息，请根据提示进行排查。

## 注意事项

1. 确保本机8080端口的应用已启动
2. 如果服务器需要认证，请配置正确的authtoken
3. 可以通过Web界面 http://localhost:4040 查看隧道状态和请求日志
4. 停止ngrok时会提示确认，避免误操作
