# Ngrok TLS证书问题解决方案

## 问题描述
连接自定义ngrok服务器 `ngrok.nslcp.com:4443` 时遇到TLS证书验证失败：
```
Session Status reconnecting (failed to send authentication request: tls: failed to verify certificate: x509:
```

## 根本原因
1. 自定义ngrok服务器使用的可能是自签名证书或证书链不完整
2. ngrok v3客户端默认进行严格的TLS证书验证
3. 需要特殊配置来绕过或处理证书验证问题

## 解决方案

### 方案1: 使用TCP隧道（推荐）
TCP协议可以避免HTTP/TLS相关的证书验证问题：

```bash
./start-ngrok-tcp.sh
```

这将创建一个TCP隧道，您的应用可以通过分配的TCP端口访问。

### 方案2: 使用默认ngrok服务器（需要邮箱验证）
如果可以使用官方ngrok服务器：

```bash
# 首先验证邮箱: https://dashboard.ngrok.com/user/settings
./test-ngrok-basic.sh
```

### 方案3: 联系服务器管理员
联系 `ngrok.nslcp.com` 服务器管理员：
1. 确认服务器状态
2. 获取正确的连接参数
3. 获取有效的认证token（如果需要）
4. 确认证书配置

## 当前配置状态

### ✅ 已完成
1. ngrok配置已升级到v3格式
2. 创建了多个启动脚本处理不同场景
3. 配置文件语法验证通过

### 📋 可用脚本
- `start-ngrok-tcp.sh` - TCP隧道（推荐）
- `test-ngrok-basic.sh` - 测试默认服务器
- `start-ngrok-simple.sh` - 简化配置连接自定义服务器
- `start-ngrok-bypass-tls.sh` - 尝试绕过TLS验证

## 下一步建议

1. **立即可用**: 使用 `./start-ngrok-tcp.sh` 创建TCP隧道
2. **长期解决**: 联系 `ngrok.nslcp.com` 管理员解决证书问题
3. **备选方案**: 考虑使用官方ngrok服务器（需要邮箱验证）

## 使用TCP隧道的注意事项

TCP隧道会给您一个类似 `tcp://0.tcp.ngrok.io:12345` 的地址，您需要：
1. 记录分配的端口号
2. 将客户端配置为连接到该TCP地址和端口
3. 确保您的应用能够处理TCP连接

## 测试连接

启动TCP隧道后，可以使用以下命令测试连接：
```bash
# 测试本地连接
curl http://localhost:8080

# 测试ngrok隧道连接（替换为实际分配的地址）
curl http://your-assigned-url.ngrok.io
```
