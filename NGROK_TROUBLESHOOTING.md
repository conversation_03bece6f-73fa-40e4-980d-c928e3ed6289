# ngrok故障排除指南

## 🚨 当前问题

在您的系统上遇到了ngrok安装和运行问题：

1. **旧版本ngrok**: 系统中有一个2018年的旧版本ngrok，无法正常工作
2. **网络连接问题**: 下载新版本ngrok时遇到SOCKS代理错误
3. **认证token问题**: 新的认证token与旧版本不兼容

## ✅ 已完成的工作

### 1. start.sh脚本增强
- ✅ 添加了ngrok环境检测
- ✅ 新增了"开发模式 + ngrok"选项
- ✅ 新增了"仅启动ngrok隧道"选项
- ✅ 改进了错误处理和提示
- ✅ 修改了ngrok.yml配置连接到官方服务器

### 2. 配套工具
- ✅ 创建了ngrok安装脚本 (`install-ngrok.sh`)
- ✅ 创建了ngrok测试脚本 (`test-ngrok.sh`)
- ✅ 创建了详细的配置指南 (`NGROK_SETUP.md`)

### 3. Webhook功能
- ✅ Meeting.Created webhook事件处理已完全实现
- ✅ 支持多账号处理
- ✅ 数据库记录和状态跟踪
- ✅ 完整的测试工具

## 🔧 解决方案

### 方案1: 手动安装ngrok (推荐)

1. **访问官网下载**:
   ```
   https://ngrok.com/download
   ```

2. **选择macOS版本**:
   - 下载 `ngrok-v3-stable-darwin-amd64.zip`

3. **手动安装**:
   ```bash
   # 解压下载的文件
   unzip ngrok-v3-stable-darwin-amd64.zip
   
   # 移动到系统路径
   sudo mv ngrok /usr/local/bin/
   sudo chmod +x /usr/local/bin/ngrok
   
   # 验证安装
   ngrok version
   ```

4. **配置认证token**:
   ```bash
   ngrok config add-authtoken 30Vj7O49nR3W5qlyaMNNyS9mpAo_3j1a42CcuQLEdWLLiARrP
   ```

### 方案2: 使用Homebrew (如果网络允许)

```bash
# 卸载旧版本
brew uninstall ngrok 2>/dev/null || true

# 安装新版本
brew install ngrok/ngrok/ngrok

# 配置认证token
ngrok config add-authtoken 30Vj7O49nR3W5qlyaMNNyS9mpAo_3j1a42CcuQLEdWLLiARrP
```

### 方案3: 临时跳过ngrok

如果暂时无法安装ngrok，您仍然可以：

1. **使用本地开发**:
   ```bash
   ./start.sh
   # 选择 "1. 开发模式" (不包含ngrok)
   ```

2. **本地测试webhook**:
   ```bash
   # 启动应用
   ./start.sh  # 选择1
   
   # 在另一个终端测试webhook
   python3 test-webhook-direct.py
   ```

3. **部署到云服务器**:
   - 将应用部署到有公网IP的服务器
   - 直接使用服务器的公网地址作为webhook URL

## 🧪 测试当前功能

即使没有ngrok，您也可以测试webhook功能：

### 1. 启动应用
```bash
./start.sh
# 选择 "1. 开发模式"
```

### 2. 测试webhook处理
```bash
# 使用Python测试脚本
python3 test-webhook-direct.py

# 或使用curl
curl -X POST http://localhost:8080/api/webhooks/zoom/KNDMAXZ_SVGeAgOTaK_TEw \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-signature" \
  -d @test-meeting-created-simple.json
```

### 3. 验证结果
```bash
# 检查数据库记录
mysql -u root -pnvshen2018 -e "
USE zoombusV; 
SELECT id, topic, zoom_meeting_id, creation_source, status 
FROM t_meetings 
ORDER BY created_at DESC LIMIT 5;
"

# 检查webhook事件
mysql -u root -pnvshen2018 -e "
USE zoombusV; 
SELECT id, event_type, processing_status, zoom_account_id 
FROM t_webhook_events 
ORDER BY created_at DESC LIMIT 5;
"
```

## 📋 网络问题排查

如果遇到网络连接问题：

### 1. 检查代理设置
```bash
echo $http_proxy
echo $https_proxy
echo $HTTP_PROXY
echo $HTTPS_PROXY

# 如果有代理设置，临时清除
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY
```

### 2. 检查DNS解析
```bash
nslookup bin.equinox.io
ping bin.equinox.io
```

### 3. 尝试不同的下载方式
```bash
# 使用wget
wget https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-darwin-amd64.zip

# 使用浏览器下载
open https://ngrok.com/download
```

## 🎯 下一步计划

1. **解决ngrok安装**: 按照上述方案手动安装ngrok
2. **测试完整流程**: 使用ngrok测试公网webhook
3. **配置Zoom应用**: 在Zoom开发者控制台配置webhook URL
4. **生产部署**: 考虑部署到云服务器

## 💡 重要提醒

- ✅ **Webhook核心功能已完全实现并测试通过**
- ✅ **本地开发和测试不依赖ngrok**
- ⚠️ **ngrok仅用于公网访问，非必需功能**
- 🚀 **可以先使用本地测试，后续再配置ngrok**

## 📞 技术支持

如果需要进一步帮助：

1. **查看日志**: `cat ngrok.log`
2. **检查网络**: 确认网络连接和代理设置
3. **手动下载**: 直接从官网下载ngrok
4. **联系支持**: 如果问题持续，可以寻求技术支持

现在您可以继续使用ZoomBus的webhook功能，ngrok只是一个可选的增强功能！
