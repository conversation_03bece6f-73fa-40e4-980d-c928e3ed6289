# 密码加密配置说明

## 概述
本项目已实现数据库密码的加密存储功能，原始密码 "nvshen2018" 已被加密为 "Ds+S3TwEqUkWVcthbG93xg=="。

## 配置详情

### 当前配置
- **原始密码**: nvshen2018
- **加密后密码**: Ds+S3TwEqUkWVcthbG93xg==
- **配置格式**: ENC(Ds+S3TwEqUkWVcthbG93xg==)

### 文件位置
- **配置文件**: `src/main/resources/application.yml`
- **解密配置**: `src/main/java/com/zoombus/config/PasswordDecryptorConfig.java`
- **加密工具**: `src/main/java/com/zoombus/util/SimplePasswordEncryptor.java`

## 工作原理

1. **加密算法**: AES 对称加密
2. **密钥**: "zoombus2024key16" (16字节固定密钥)
3. **编码**: Base64 编码存储

## 如何使用

### 加密新密码
```bash
# 编译并运行加密工具
javac src/main/java/com/zoombus/util/SimplePasswordEncryptor.java
java -cp src/main/java com.zoombus.util.SimplePasswordEncryptor
```

### 配置新的加密密码
1. 运行上述加密工具获得加密后的密码
2. 在 `application.yml` 中使用格式: `ENC(加密后的密码)`
3. 应用启动时会自动解密

## 安全注意事项

1. **密钥安全**: 当前密钥硬编码在代码中，生产环境建议从环境变量读取
2. **密钥管理**: 建议定期更换加密密钥
3. **访问控制**: 确保只有授权人员能访问加密工具和配置

## 生产环境建议

1. 将加密密钥存储在环境变量中
2. 使用更强的加密算法（如 AES-256）
3. 实现密钥轮换机制
4. 添加密钥访问日志

## 故障排除

如果遇到密码解密失败：
1. 检查密码格式是否为 `ENC(加密字符串)`
2. 确认加密密钥是否正确
3. 验证加密字符串是否完整

## 测试验证

可以通过以下方式验证加密解密功能：
```java
String original = "nvshen2018";
String encrypted = SimplePasswordEncryptor.encrypt(original);
String decrypted = SimplePasswordEncryptor.decrypt(encrypted);
System.out.println("验证结果: " + original.equals(decrypted));
```
