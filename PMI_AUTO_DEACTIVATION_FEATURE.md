# PMI自动停用功能实现

## 功能概述

当会议结算后，如果PMI记录的剩余可用余额不大于0，系统将自动修改PMI状态为未激活（INACTIVE）。

## 实现位置

### 主要修改文件
- `src/main/java/com/zoombus/service/MeetingSettlementService.java`
- `src/test/java/com/zoombus/service/MeetingSettlementServiceTest.java`

### 核心逻辑

在 `MeetingSettlementService.settleTimeBillingMeeting()` 方法中添加了余额检查逻辑：

```java
// 检查剩余可用余额，如果不大于0则修改PMI状态为未激活
if (availableMinutes <= 0) {
    log.info("PMI {} 剩余可用余额不大于0（{}分钟），将状态修改为未激活", 
        pmiRecord.getId(), availableMinutes);
    pmiRecord.setStatus(PmiRecord.PmiStatus.INACTIVE);
}
```

## 功能特点

### 1. 仅适用于按时长计费模式
- 只有 `BillingMode.BY_TIME` 的PMI会被检查余额
- `BillingMode.LONG`（按时段计费）不受影响，因为不扣费

### 2. 智能状态管理
- **余额 > 0**：PMI保持 `ACTIVE` 状态
- **余额 ≤ 0**：PMI自动设置为 `INACTIVE` 状态

### 3. 详细日志记录
- 记录PMI状态变更的详细信息
- 在结算消息中说明PMI状态变化

### 4. 与充值功能互补
- **结算时**：余额 ≤ 0 → 自动停用PMI
- **充值时**：余额 > 0 → 自动激活PMI（已有功能）

## 测试覆盖

### 测试用例
1. **余额充足**：PMI保持活跃状态
2. **余额刚好用完**：PMI被设置为未激活状态
3. **余额不足产生超额**：PMI被设置为未激活状态
4. **按时段计费模式**：不检查余额，PMI保持活跃状态
5. **已结算会议**：跳过结算，不修改PMI状态
6. **无PMI记录**：跳过结算，不执行任何操作

### 测试结果
```
Tests run: 6, Failures: 0, Errors: 0, Skipped: 0
```

## 业务流程

### 会议结算流程
1. 检查会议是否已结算 → 如果是，跳过
2. 检查是否有PMI记录 → 如果没有，跳过
3. 根据计费模式执行结算：
   - **按时长计费**：扣除待扣时长，检查余额，可能停用PMI
   - **按时段计费**：记录使用情况，不扣费，不检查余额

### PMI状态变更逻辑
```
会议结算 → 扣除时长 → 计算剩余余额
                    ↓
            余额 ≤ 0？ → 是 → 设置PMI为INACTIVE
                    ↓
                   否 → 保持PMI为ACTIVE
```

## 相关功能

### 1. PMI充值服务 (`PmiRechargeService`)
- 充值后如果余额 > 0，自动激活PMI为 `ACTIVE` 状态
- 与本功能形成完整的状态管理循环

### 2. 窗口管理服务 (`PmiWindowCheckService`)
- 窗口关闭后如果没有可用时长，也会设置PMI为 `INACTIVE` 状态
- 提供了另一个PMI状态检查点

### 3. 计费监控服务 (`BillingMonitorService`)
- 实时监控会议计费，累积待扣时长
- 为结算服务提供准确的计费数据

## 日志示例

### 成功停用PMI
```
INFO  - PMI 123 剩余可用余额不大于0（0分钟），将状态修改为未激活
INFO  - 会议 456 结算完成: 会议结算完成，扣除 50 分钟，剩余 0 分钟，PMI已自动设置为未激活状态
```

### 余额充足保持活跃
```
INFO  - 会议 456 结算完成: 会议结算完成，扣除 30 分钟，剩余 70 分钟
```

### 产生超额时长
```
INFO  - PMI 123 剩余可用余额不大于0（0分钟），将状态修改为未激活
INFO  - 会议 456 结算完成: 会议结算完成，扣除 80 分钟，产生超额 30 分钟，PMI已自动设置为未激活状态
```

## 注意事项

1. **事务安全**：PMI状态更新在同一事务中完成，确保数据一致性
2. **幂等性**：重复调用结算不会产生副作用
3. **错误处理**：结算失败不会影响PMI状态
4. **性能影响**：状态检查逻辑简单，对性能影响微乎其微

## 未来扩展

1. **通知机制**：可以添加PMI状态变更通知
2. **批量处理**：支持批量检查和更新PMI状态
3. **策略配置**：允许配置不同的余额阈值策略
4. **审计日志**：记录详细的状态变更历史
