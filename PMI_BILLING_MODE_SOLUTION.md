# PMI计费模式切换业务流程缺陷分析与解决方案

## 🚨 发现的重大缺陷

### 1. 原始计费模式丢失 ❌
**问题**: 没有保存PMI的原始计费模式，导致窗口关闭后无法正确恢复
**影响**: FREE模式的PMI在窗口关闭后可能被错误地设置为BY_TIME模式

### 2. 多窗口重叠处理错误 ❌
**问题**: 当一个PMI有多个重叠窗口时，只处理第一个找到的窗口
**影响**: 后续窗口的到期时间被忽略，可能导致提前切换回原始模式

### 3. 已是LONG模式时不更新窗口信息 ❌
**问题**: 如果PMI已经是LONG模式，直接返回，不更新窗口ID和到期时间
**影响**: 新窗口的信息丢失，无法正确管理多个窗口

### 4. 窗口关闭逻辑不完整 ❌
**问题**: 只根据可用时长决定下一步，没有考虑原始计费模式
**影响**: FREE模式的PMI可能被错误地切换到BY_TIME模式

### 5. 缺乏并发控制 ❌
**问题**: 没有锁机制防止同一PMI的并发状态变更
**影响**: 可能导致数据不一致

### 6. 异常恢复机制缺失 ❌
**问题**: 没有事务回滚和状态恢复机制
**影响**: 异常情况下可能导致数据不一致

### 7. 活跃会议计费连续性问题 ❌
**问题**: 计费模式切换时可能导致会议计费数据不连续
**影响**: 计费数据可能不准确

## ✅ 完整解决方案

### 第一步：数据模型改进

#### 新增字段
```sql
-- 原始计费模式（用于窗口关闭后恢复）
ALTER TABLE t_pmi_records 
ADD COLUMN original_billing_mode VARCHAR(20) COMMENT '原始计费模式（用于窗口关闭后恢复）';

-- 活跃窗口ID列表（JSON格式存储）
ALTER TABLE t_pmi_records 
ADD COLUMN active_window_ids TEXT COMMENT '活跃窗口ID列表（JSON格式）';
```

### 第二步：业务逻辑改进

#### 1. 改进的窗口开启逻辑
```java
@Transactional(isolation = Isolation.SERIALIZABLE)
public void switchToLongBilling(Long pmiRecordId, Long windowId, LocalDateTime expireTime) {
    // 使用悲观锁防止并发问题
    PmiRecord pmiRecord = pmiRecordRepository.findByIdForUpdate(pmiRecordId);
    
    // 保存原始计费模式（仅首次）
    if (pmiRecord.getBillingMode() != PmiRecord.BillingMode.LONG) {
        pmiRecord.setOriginalBillingMode(pmiRecord.getBillingMode());
    }
    
    // 管理多窗口
    Set<Long> activeWindowIds = getActiveWindowIds(pmiRecord);
    activeWindowIds.add(windowId);
    
    // 计算最晚到期时间
    LocalDateTime latestExpireTime = calculateLatestExpireTime(activeWindowIds);
    
    // 更新PMI状态
    pmiRecord.setBillingMode(PmiRecord.BillingMode.LONG);
    pmiRecord.setWindowExpireTime(latestExpireTime);
    pmiRecord.setActiveWindowIds(serializeWindowIds(activeWindowIds));
}
```

#### 2. 改进的窗口关闭逻辑
```java
@Transactional(isolation = Isolation.SERIALIZABLE)
public void switchToOriginalBilling(Long pmiRecordId) {
    PmiRecord pmiRecord = pmiRecordRepository.findByIdForUpdate(pmiRecordId);
    
    // 恢复到原始计费模式
    PmiRecord.BillingMode originalMode = pmiRecord.getOriginalBillingMode();
    if (originalMode == null) {
        originalMode = PmiRecord.BillingMode.BY_TIME; // 默认值
    }
    
    pmiRecord.setBillingMode(originalMode);
    pmiRecord.setOriginalBillingMode(null);
    pmiRecord.setCurrentWindowId(null);
    pmiRecord.setWindowExpireTime(null);
    pmiRecord.setActiveWindowIds(null);
}
```

### 第三步：并发控制和事务管理

#### 1. 悲观锁
```java
@Lock(LockModeType.PESSIMISTIC_WRITE)
@Query("SELECT p FROM PmiRecord p WHERE p.id = :id")
Optional<PmiRecord> findByIdForUpdate(@Param("id") Long id);
```

#### 2. 事务隔离
```java
@Transactional(isolation = Isolation.SERIALIZABLE)
```

### 第四步：数据一致性检查

#### 定期检查服务
```java
@Scheduled(cron = "0 0 * * * ?") // 每小时执行
public void checkBillingConsistency() {
    // 检查所有LONG模式的PMI
    // 验证窗口状态和计费模式的一致性
    // 自动修复不一致的数据
}
```

## 🎯 改进后的完整流程

### 窗口开启流程
1. **获取悲观锁**: 防止并发修改
2. **保存原始模式**: 仅在首次切换时保存
3. **管理窗口列表**: 添加新窗口到活跃列表
4. **计算到期时间**: 使用所有活跃窗口中最晚的时间
5. **切换计费模式**: 更新为LONG模式
6. **处理活跃会议**: 仅在首次切换时处理

### 窗口关闭流程
1. **获取悲观锁**: 防止并发修改
2. **更新窗口列表**: 移除关闭的窗口
3. **检查剩余窗口**: 如果还有活跃窗口，更新到期时间
4. **恢复原始模式**: 如果没有活跃窗口，恢复到原始模式
5. **处理活跃会议**: 根据新模式调整会议计费

### 状态转换规则
- **FREE → LONG**: 保存FREE为原始模式，窗口关闭后恢复FREE
- **BY_TIME → LONG**: 保存BY_TIME为原始模式，窗口关闭后恢复BY_TIME
- **LONG → 原始模式**: 恢复到保存的原始模式

## 🔧 关键改进点

### 1. 原始模式保存 ✅
- 新增`original_billing_mode`字段
- 仅在首次切换到LONG时保存
- 窗口关闭后正确恢复

### 2. 多窗口管理 ✅
- 新增`active_window_ids`字段（JSON格式）
- 维护活跃窗口列表
- 使用最晚到期时间

### 3. 并发安全 ✅
- 使用悲观锁防止并发修改
- SERIALIZABLE事务隔离级别
- 原子性操作

### 4. 异常恢复 ✅
- 事务回滚机制
- 定期一致性检查
- 自动修复功能

### 5. 计费连续性 ✅
- 改进活跃会议处理逻辑
- 确保计费数据连续性
- 避免重复处理

## 📊 测试验证

### 测试场景
1. **单窗口开启关闭**: 验证基本功能
2. **多窗口重叠**: 验证多窗口管理
3. **FREE模式处理**: 验证原始模式恢复
4. **并发操作**: 验证锁机制
5. **异常恢复**: 验证一致性检查

### 预期结果
- ✅ 原始计费模式正确保存和恢复
- ✅ 多窗口重叠时正确管理到期时间
- ✅ FREE模式PMI正确处理
- ✅ 并发操作数据一致
- ✅ 异常情况自动恢复

## 🚀 部署步骤

1. **执行数据库迁移**: `V20250106_002__add_pmi_billing_mode_fields.sql`
2. **重启应用服务**: 加载新的业务逻辑
3. **验证功能**: 测试窗口开启关闭流程
4. **监控日志**: 观察一致性检查结果

## 📝 总结

通过这个完整的解决方案，我们解决了PMI计费模式切换中的所有重大缺陷：

- ✅ **数据完整性**: 保存原始模式，正确恢复
- ✅ **业务正确性**: 支持多窗口，处理各种模式
- ✅ **系统稳定性**: 并发控制，异常恢复
- ✅ **数据一致性**: 定期检查，自动修复

这个解决方案确保了PMI计费模式切换的可靠性和准确性。
