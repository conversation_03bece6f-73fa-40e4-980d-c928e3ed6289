# PMI计费系统部署和使用指南

## 🚀 快速开始

### 前置条件
- Java 11+
- Node.js 14+
- MySQL 5.7+
- Maven 3.6+

### 1. 数据库初始化

```bash
# 连接到MySQL数据库
mysql -u root -pnvshen2018

# 执行数据库迁移
mysql -u root -pnvshen2018 zoombusV < pmi_billing_system_migration.sql
```

### 2. 后端启动

```bash
# 设置Java 11环境
export JAVA_HOME=/path/to/java11

# 启动后端服务
./mvnw spring-boot:run
```

后端将在 `http://localhost:8080` 启动

### 3. 前端启动

```bash
# 进入前端目录
cd frontend

# 安装依赖（首次运行）
npm install

# 启动开发服务器
npm start
```

前端将在 `http://localhost:3000` 启动

## 📱 功能访问

### 主要页面
- **主页**: http://localhost:3000
- **PMI管理**: http://localhost:3000/pmi-management
- **Zoom会议看板**: http://localhost:3000/zoom-meeting-dashboard
- **PMI计费管理**: http://localhost:3000/pmi-billing-management

### 新增功能

#### 1. PMI充值功能
- **位置**: PMI管理页面 → 操作列 → 充值按钮
- **功能**: 
  - 充值预览（智能分配策略）
  - 执行充值
  - 实时余额更新

#### 2. Zoom会议看板
- **路径**: `/zoom-meeting-dashboard`
- **功能**:
  - 活跃会议实时监控
  - 历史会议查询
  - 会议统计展示
  - 会议操作（结束会议）

#### 3. PMI计费管理
- **路径**: `/pmi-billing-management`
- **功能**:
  - 计费记录查询和过滤
  - 监控状态展示
  - ZoomUser统计
  - 系统管理操作

## 🔧 配置说明

### 后端配置 (application.yml)

```yaml
# 计费系统配置
zoombus:
  billing:
    window-expiry-scheduler:
      enabled: true  # 启用窗口到期检查
    monitor-scheduler:
      enabled: true  # 启用计费监控
    auto-init:
      enabled: true  # 启用系统自动初始化
```

### 前端配置
- 开发环境：API自动指向 `http://localhost:8080/api`
- 生产环境：API指向 `/api`（需要nginx代理）

## 🧪 测试验证

### 运行单元测试
```bash
# 设置Java 11环境
export JAVA_HOME=/path/to/java11

# 运行所有测试
./mvnw test

# 运行特定测试
./mvnw test -Dtest=PmiBillingModeServiceTest
./mvnw test -Dtest=PmiRechargeServiceTest
./mvnw test -Dtest=ZoomMeetingServiceTest
```

### 运行前端测试
```bash
# 运行前端功能测试
./test_pmi_billing_frontend.sh

# 运行端到端测试
./test_pmi_billing_system.sh
```

## 📊 核心功能说明

### 计费模式

#### 1. 按时长计费 (BY_TIME)
- **特点**: 每分钟扣除1分钟时长
- **监控**: 实时计费监控
- **充值**: 支持智能充值分配
- **超额**: 支持超额使用和结清

#### 2. 按时段计费 (LONG)
- **特点**: 固定时间窗口，不扣除时长
- **监控**: 窗口到期自动检查
- **切换**: 可动态切换计费模式

### 智能充值策略
1. **优先级1**: 结清超额时长
2. **优先级2**: 结清待扣时长
3. **优先级3**: 增加可用时长

### 实时监控
- **频率**: 每分钟检查一次
- **范围**: 所有按时长计费的活跃会议
- **故障恢复**: 自动重启监控

## 🔗 API接口

### PMI充值相关
```bash
# 获取充值预览
GET /api/pmi/{id}/recharge/preview?minutes=60

# 执行充值
POST /api/pmi/{id}/recharge?minutes=60&description=测试充值
```

### 计费模式管理
```bash
# 切换到按时段计费
POST /api/pmi/{id}/billing/switch-to-long?windowId=123&expireTime=2024-01-01T12:00:00

# 切换到按时长计费
POST /api/pmi/{id}/billing/switch-to-time

# 检查是否可开启会议
GET /api/pmi/{id}/billing/can-start-meeting
```

### 会议看板
```bash
# 获取看板数据
GET /api/zoom-meetings/dashboard

# 获取活跃会议
GET /api/zoom-meetings/active?page=0&size=10

# 获取历史会议
GET /api/zoom-meetings/history?page=0&size=10

# 结束会议
POST /api/zoom-meetings/{id}/end
```

### 计费管理
```bash
# 获取计费记录
GET /api/pmi-billing/records?page=0&size=10

# 获取监控状态
GET /api/pmi-billing/monitor/status

# 同步监控状态
POST /api/pmi-billing/monitor/sync

# 批量结算会议
POST /api/pmi-billing/settlement/batch
```

## 🚨 故障排除

### 常见问题

#### 1. API调用返回HTML页面
- **原因**: 前端API调用指向了错误的端口
- **解决**: 确保使用 `import api from '../services/api'` 而不是直接使用 `axios`

#### 2. 充值功能不工作
- **检查**: 后端是否在8080端口运行
- **检查**: 数据库迁移是否执行成功
- **检查**: PMI记录是否存在且为按时长计费模式

#### 3. 会议监控不工作
- **检查**: 调度器是否启用 (`zoombus.billing.monitor-scheduler.enabled=true`)
- **检查**: 数据库连接是否正常
- **检查**: 会议记录是否正确创建

#### 4. 前端页面空白
- **检查**: 是否已登录
- **检查**: 路由配置是否正确
- **检查**: 浏览器控制台是否有错误

### 日志查看
```bash
# 查看后端日志
tail -f backend.log

# 查看前端日志
# 浏览器开发者工具 → Console
```

## 📈 性能优化

### 数据库优化
- 为计费记录表添加索引
- 定期清理历史数据
- 监控查询性能

### 前端优化
- 启用代码分割
- 优化包大小
- 使用CDN加速

### 后端优化
- 配置连接池
- 启用缓存
- 监控内存使用

## 🔒 安全考虑

### 认证授权
- 所有API都需要JWT认证
- 管理员权限控制
- 操作日志记录

### 数据保护
- 敏感数据加密
- SQL注入防护
- XSS攻击防护

## 📞 技术支持

如遇到问题，请检查：
1. 服务是否正常运行
2. 数据库连接是否正常
3. 配置文件是否正确
4. 日志中的错误信息

更多技术细节请参考：
- `PMI_BILLING_SYSTEM_IMPLEMENTATION_SUMMARY.md`
- `accounting.md`
- 源代码注释
