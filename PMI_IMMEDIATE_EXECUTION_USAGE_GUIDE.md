# PMI窗口即时执行机制使用指南

## 概述

PMI窗口即时执行机制解决了创建即刻开始或过往时刻窗口时的延迟执行问题，确保用户期望立即生效的窗口能够秒级响应。

## 功能特性

### 1. 智能策略判断
- **立即执行**: 过往时间或当前时间±容差范围内的任务
- **调度执行**: 未来时间的任务
- **标记过期**: 超出有效期的任务

### 2. 时间容差机制
- 默认2分钟容差，在此范围内的任务视为"当前时间"
- 可配置最大延迟时间（默认60分钟）
- 支持开启/关闭过往时间任务执行

### 3. 降级保护
- 异常时自动降级到原有调度机制
- 确保系统稳定性

## 配置说明

### 基础配置
```yaml
pmi:
  task:
    scheduling:
      immediate-execution:
        enabled: true                    # 是否启用立即执行机制
        tolerance-minutes: 2             # 时间容差（分钟）
        max-delay-minutes: 60           # 最大延迟执行时间（分钟）
        enable-past-execution: true      # 是否允许执行过往时间的任务
```

### 高级配置
```yaml
        # 线程池配置
        thread-pool:
          core-size: 5                   # 核心线程数
          max-size: 10                   # 最大线程数
          queue-capacity: 100            # 队列容量
          thread-name-prefix: "immediate-task-"
          keep-alive-seconds: 60
          allow-core-thread-time-out: false
        
        # 监控配置
        monitoring:
          enabled: true
          metrics-interval-seconds: 60
          record-execution-details: true
          
          # 告警配置
          alert:
            enabled: true
            failure-rate-threshold: 5.0      # 失败率阈值（百分比）
            expired-task-threshold: 10       # 过期任务数量阈值（每小时）
            avg-execution-time-threshold: 5  # 平均执行时间阈值（秒）
            queue-full-threshold: 80.0       # 队列满阈值（百分比）
```

## 使用场景

### 场景1: 创建即刻开始的窗口
```java
// 用户创建一个立即开始的PMI窗口
PmiScheduleWindow window = new PmiScheduleWindow();
window.setStartDateTime(LocalDateTime.now()); // 当前时间
window.setEndDateTime(LocalDateTime.now().plusHours(2));

// 系统会自动判断为立即执行，无需等待调度器
```

**执行流程**:
1. 窗口创建事件触发
2. 策略判断器识别为"当前时间"
3. 立即异步执行开启任务
4. 用户秒级看到窗口激活

### 场景2: 创建过往时刻的窗口
```java
// 用户创建一个30分钟前开始的窗口（补录场景）
PmiScheduleWindow window = new PmiScheduleWindow();
window.setStartDateTime(LocalDateTime.now().minusMinutes(30)); // 30分钟前
window.setEndDateTime(LocalDateTime.now().plusMinutes(90));

// 系统判断窗口仍在有效期内，立即执行
```

**执行流程**:
1. 策略判断器检查窗口是否仍然有效
2. 30分钟延迟 < 60分钟最大延迟，且窗口未结束
3. 立即执行开启任务
4. 窗口状态快速更新为ACTIVE

### 场景3: 创建未来时间的窗口
```java
// 用户创建一个1小时后开始的窗口
PmiScheduleWindow window = new PmiScheduleWindow();
window.setStartDateTime(LocalDateTime.now().plusHours(1)); // 1小时后
window.setEndDateTime(LocalDateTime.now().plusHours(3));

// 系统使用原有调度机制
```

**执行流程**:
1. 策略判断器识别为"未来时间"
2. 使用原有调度机制
3. 任务在指定时间执行

## 监控和诊断

### 1. 日志监控
```bash
# 查看立即执行相关日志
tail -f logs/zoombus.log | grep -i "immediate\|strategy"

# 关键日志示例
[INFO] 任务执行策略: windowId=123, strategy=IMMEDIATE
[INFO] 立即执行任务: windowId=123, originalTime=2024-12-15T10:00:00
[INFO] 任务已提交立即执行: taskKey=PMI_OPEN_123, taskId=456
```

### 2. API监控
```bash
# 检查任务状态
curl http://localhost:8080/api/pmi-scheduled-tasks/456

# 诊断任务问题
curl http://localhost:8080/api/pmi-scheduled-tasks/456/diagnose
```

### 3. 性能指标
- 立即执行任务数量
- 立即执行成功率
- 平均执行时间
- 过期任务数量

## 故障排查

### 问题1: 立即执行不生效
**症状**: 即刻开始的窗口仍然延迟执行

**排查步骤**:
1. 检查配置是否启用
```yaml
immediate-execution:
  enabled: true  # 确保为true
```

2. 查看日志中的策略判断
```bash
grep "任务执行策略" logs/zoombus.log
```

3. 检查时间容差设置
```yaml
tolerance-minutes: 2  # 可能需要调大
```

### 问题2: 过往任务被标记为过期
**症状**: 历史时间的窗口无法执行

**排查步骤**:
1. 检查过往执行开关
```yaml
enable-past-execution: true
```

2. 检查最大延迟时间
```yaml
max-delay-minutes: 60  # 可能需要调大
```

3. 检查窗口是否仍然有效
- 开启任务：窗口结束时间 > 当前时间
- 关闭任务：延迟时间 <= 最大延迟时间

### 问题3: 系统性能问题
**症状**: 大量立即执行导致系统负载高

**解决方案**:
1. 调整线程池配置
```yaml
thread-pool:
  core-size: 10      # 增加核心线程数
  max-size: 20       # 增加最大线程数
  queue-capacity: 200 # 增加队列容量
```

2. 临时禁用立即执行
```yaml
immediate-execution:
  enabled: false
```

## 最佳实践

### 1. 配置建议
- **生产环境**: 保守配置，容差2分钟，最大延迟30分钟
- **测试环境**: 宽松配置，便于测试各种场景
- **开发环境**: 启用详细日志和监控

### 2. 监控建议
- 设置失败率告警（建议5%）
- 监控过期任务数量
- 关注平均执行时间
- 定期检查线程池使用情况

### 3. 运维建议
- 定期清理过期任务记录
- 监控系统资源使用情况
- 根据业务量调整线程池配置
- 建立应急降级预案

## 版本兼容性

- **向后兼容**: 不影响现有功能
- **配置兼容**: 默认配置保持原有行为
- **API兼容**: 不改变现有API接口
- **数据兼容**: 不影响现有数据结构

## 总结

PMI窗口即时执行机制通过智能的策略判断，显著改善了用户体验，特别是对于需要立即生效的窗口。该机制具有以下优势：

1. **响应迅速**: 即刻和过往窗口秒级响应
2. **配置灵活**: 支持多种参数调整
3. **稳定可靠**: 完善的降级和异常处理
4. **监控完善**: 提供全面的监控和诊断能力
5. **兼容性好**: 不影响现有功能

通过合理的配置和监控，该机制可以显著提升PMI窗口管理的用户体验。
