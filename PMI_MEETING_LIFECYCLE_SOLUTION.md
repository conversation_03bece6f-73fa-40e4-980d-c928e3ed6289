# PMI会议生命周期解决方案

## 🎯 问题描述

当用户通过PMI开启会议时，系统需要：
1. 创建PENDING状态的ZoomMeeting记录
2. 当收到Zoom的`meeting.start`事件时，找到对应的PENDING记录
3. 将状态更新为USING并开始计费

## 🔍 核心挑战

**关联问题**：
- PMI开启时生成的`zoom_meeting_uuid`是自定义的（如：`pending-9135368323-1234567890`）
- Zoom Webhook中的`meeting_uuid`是Zoom实际生成的（如：`abc123def456`）
- 两者无法直接匹配！

## 💡 解决方案

### 1. 关联策略
使用**PMI号码 + 主持人ID**作为关联桥梁：

```sql
-- 查找PENDING状态的会议记录
SELECT * FROM t_zoom_meetings 
WHERE zoom_meeting_id = '{pmi_number}' 
  AND host_id = '{host_id}' 
  AND status = 'PENDING'
```

### 2. 数据流程

#### 步骤1：PMI开启时
```java
// PublicPmiController.createMeetingRecord()
ZoomMeeting meeting = new ZoomMeeting();
meeting.setZoomMeetingUuid("pending-" + pmiNumber + "-" + timestamp); // 临时UUID
meeting.setZoomMeetingId(pmiNumber);        // 关键：PMI号码
meeting.setHostId(zoomUser.getZoomUserId()); // 关键：主持人ID
meeting.setStatus(PENDING);
```

#### 步骤2：收到meeting.start事件时
```java
// ZoomMeetingService.handlePmiMeetingStarted()
Optional<ZoomMeeting> pendingMeeting = zoomMeetingRepository
    .findPendingMeetingByMeetingIdAndHostId(meetingId, hostId);

if (pendingMeeting.isPresent()) {
    ZoomMeeting meeting = pendingMeeting.get();
    meeting.setZoomMeetingUuid(realMeetingUuid); // 更新为真实UUID
    meeting.setStatus(USING);
    meeting.setStartTime(LocalDateTime.now());
    // 开始计费...
}
```

## 🔧 实现细节

### 1. Repository查询方法
```java
// ZoomMeetingRepository.java
@Query("SELECT zm FROM ZoomMeeting zm WHERE zm.zoomMeetingId = :meetingId AND zm.hostId = :hostId AND zm.status = 'PENDING'")
Optional<ZoomMeeting> findPendingMeetingByMeetingIdAndHostId(@Param("meetingId") String meetingId, @Param("hostId") String hostId);
```

### 2. 新增服务方法
```java
// ZoomMeetingService.java
@Transactional
public void handlePmiMeetingStarted(String meetingUuid, String meetingId, String hostId, String topic) {
    // 通过PMI号码和主持人ID查找PENDING记录
    Optional<ZoomMeeting> pendingMeeting = zoomMeetingRepository
        .findPendingMeetingByMeetingIdAndHostId(meetingId, hostId);
    
    if (pendingMeeting.isPresent()) {
        // 更新PENDING记录为USING状态
        ZoomMeeting meeting = pendingMeeting.get();
        meeting.setZoomMeetingUuid(meetingUuid); // 更新真实UUID
        meeting.setStatus(USING);
        meeting.setStartTime(LocalDateTime.now());
        // 开始计费监控...
    }
}
```

### 3. Webhook处理示例
```java
// ZoomWebhookController.java (需要创建)
@PostMapping("/webhook/meeting")
public ResponseEntity<String> handleMeetingWebhook(@RequestBody Map<String, Object> payload) {
    String event = (String) payload.get("event");
    Map<String, Object> meetingData = (Map<String, Object>) payload.get("payload");
    
    if ("meeting.started".equals(event)) {
        String meetingUuid = (String) meetingData.get("uuid");
        String meetingId = (String) meetingData.get("id");
        String hostId = (String) meetingData.get("host_id");
        String topic = (String) meetingData.get("topic");
        
        // 调用新的处理方法
        zoomMeetingService.handlePmiMeetingStarted(meetingUuid, meetingId, hostId, topic);
    }
    
    return ResponseEntity.ok("OK");
}
```

## 📊 数据库设计

### 关键字段说明
```sql
CREATE TABLE t_zoom_meetings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    zoom_meeting_uuid VARCHAR(255) NOT NULL,  -- Zoom真实UUID（初始为临时值）
    zoom_meeting_id VARCHAR(255) NOT NULL,    -- PMI号码（关联关键字段）
    host_id VARCHAR(255),                     -- 主持人ID（关联关键字段）
    status ENUM('PENDING', 'USING', 'ENDED', 'SETTLED'),
    start_time DATETIME,
    -- 其他字段...
);

-- 关键索引
CREATE INDEX idx_meeting_host_status ON t_zoom_meetings(zoom_meeting_id, host_id, status);
```

## 🔄 完整生命周期

### 1. PMI开启阶段
```
用户访问 /m/{pmi} → 点击开启 → 创建PENDING记录
状态：PENDING
UUID：pending-{pmi}-{timestamp}
```

### 2. 会议开始阶段
```
用户点击startURL → Zoom会议开始 → Webhook触发 → 更新为USING
状态：PENDING → USING
UUID：pending-{pmi}-{timestamp} → {real-zoom-uuid}
```

### 3. 会议进行阶段
```
计费监控运行 → 实时计算费用
状态：USING
```

### 4. 会议结束阶段
```
会议结束 → Webhook触发 → 更新为ENDED → 费用结算
状态：USING → ENDED → SETTLED
```

## 🧪 测试验证

### 1. 创建测试数据
```sql
-- 创建PENDING状态的会议记录
INSERT INTO t_zoom_meetings (
    pmi_record_id, zoom_meeting_uuid, zoom_meeting_id, 
    host_id, status, topic, billing_mode,
    assigned_zoom_user_id, assigned_zoom_user_email
) VALUES (
    7, 'pending-9135368323-1691234567890', '9135368323',
    'test-host-id-123', 'PENDING', 'PMI测试会议', 'BY_TIME',
    73, '<EMAIL>'
);
```

### 2. 模拟Webhook调用
```bash
curl -X POST http://localhost:8080/api/webhook/meeting \
  -H "Content-Type: application/json" \
  -d '{
    "event": "meeting.started",
    "payload": {
      "uuid": "real-zoom-uuid-abc123",
      "id": "9135368323",
      "host_id": "test-host-id-123",
      "topic": "PMI会议 - 9135368323"
    }
  }'
```

### 3. 验证结果
```sql
-- 检查记录是否正确更新
SELECT id, zoom_meeting_uuid, zoom_meeting_id, host_id, status, start_time
FROM t_zoom_meetings 
WHERE zoom_meeting_id = '9135368323';

-- 预期结果：
-- status: USING
-- zoom_meeting_uuid: real-zoom-uuid-abc123
-- start_time: 当前时间
```

## 🎯 优势

1. **可靠关联**：通过PMI号码+主持人ID确保唯一性
2. **状态追踪**：完整的会议生命周期管理
3. **计费准确**：精确的开始时间记录
4. **容错处理**：找不到PENDING记录时创建新记录
5. **扩展性好**：支持多种会议类型

## 📝 注意事项

1. **时间窗口**：PMI开启和实际会议开始之间可能有时间差
2. **并发处理**：需要考虑同一PMI多次开启的情况
3. **异常处理**：Webhook可能重复发送或丢失
4. **数据一致性**：确保状态转换的原子性

这个解决方案通过PMI号码和主持人ID作为桥梁，成功解决了PMI开启记录与Zoom Webhook事件的关联问题，实现了完整的会议生命周期管理和精确计费。
