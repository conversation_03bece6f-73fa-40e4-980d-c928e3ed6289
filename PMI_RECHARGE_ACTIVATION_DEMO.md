# PMI充值自动激活功能演示

## 功能描述

给PMI充值后，如果可用时长大于0则需要激活PMI为活跃状态。

## 实现位置

**文件**: `src/main/java/com/zoombus/service/PmiRechargeService.java`

**方法**: `executeRecharge(PmiRecord pmiRecord, RechargeAllocation allocation)`

## 核心逻辑

```java
// 检查并激活PMI状态：如果可用时长大于0则激活PMI为活跃状态
checkAndActivatePmi(pmiRecord, allocation.getFinalAvailable(), "充值后");
```

### 工具方法实现

```java
/**
 * 检查并激活PMI状态
 * 如果可用时长大于0且当前状态不是ACTIVE，则激活PMI为活跃状态
 */
private void checkAndActivatePmi(PmiRecord pmiRecord, int availableMinutes, String context) {
    if (availableMinutes > 0 && pmiRecord.getStatus() != PmiRecord.PmiStatus.ACTIVE) {
        PmiRecord.PmiStatus oldStatus = pmiRecord.getStatus();
        pmiRecord.setStatus(PmiRecord.PmiStatus.ACTIVE);
        log.info("PMI {} {}可用时长大于0({} 分钟)，激活PMI状态：{} -> ACTIVE",
                pmiRecord.getId(), context, availableMinutes, oldStatus);
    }
}
```

## 功能场景

### 场景1: INACTIVE PMI充值后激活

**初始状态**:
- PMI状态: `INACTIVE`
- 可用时长: `0` 分钟
- 总时长: `100` 分钟

**操作**: 充值 `50` 分钟

**结果**:
- PMI状态: `INACTIVE` → `ACTIVE` ✅
- 可用时长: `0` → `50` 分钟
- 总时长: `100` → `150` 分钟

### 场景2: 已经是ACTIVE状态的PMI

**初始状态**:
- PMI状态: `ACTIVE`
- 可用时长: `10` 分钟
- 总时长: `100` 分钟

**操作**: 充值 `50` 分钟

**结果**:
- PMI状态: `ACTIVE` → `ACTIVE` (保持不变)
- 可用时长: `10` → `60` 分钟
- 总时长: `100` → `150` 分钟

### 场景3: 充值仅结清超额，最终可用时长为0

**初始状态**:
- PMI状态: `INACTIVE`
- 可用时长: `0` 分钟
- 超额时长: `50` 分钟
- 总时长: `100` 分钟

**操作**: 充值 `50` 分钟

**结果**:
- PMI状态: `INACTIVE` → `INACTIVE` (保持不变，因为最终可用时长为0)
- 可用时长: `0` → `0` 分钟
- 超额时长: `50` → `0` 分钟
- 总时长: `100` → `100` 分钟

### 场景4: 充值结清超额后还有余额

**初始状态**:
- PMI状态: `INACTIVE`
- 可用时长: `0` 分钟
- 超额时长: `30` 分钟
- 总时长: `100` 分钟

**操作**: 充值 `50` 分钟

**结果**:
- PMI状态: `INACTIVE` → `ACTIVE` ✅ (因为最终可用时长为20分钟)
- 可用时长: `0` → `20` 分钟 (50-30=20)
- 超额时长: `30` → `0` 分钟
- 总时长: `100` → `120` 分钟

## 测试用例

已添加以下测试用例到 `PmiRechargeServiceTest.java`:

1. **testRechargeActivatesPmiWhenAvailableMinutesGreaterThanZero**: 测试INACTIVE PMI充值后激活
2. **testRechargeDoesNotChangeStatusWhenAlreadyActive**: 测试已经是ACTIVE的PMI保持状态不变
3. **testRechargeDoesNotActivateWhenFinalAvailableIsZero**: 测试充值后可用时长为0时不激活

## API调用示例

### 充值API

```http
POST /api/pmi/{pmiRecordId}/recharge
Content-Type: application/x-www-form-urlencoded

minutes=50&description=测试充值激活
```

### 响应示例

```json
{
  "success": true,
  "message": "充值成功",
  "data": {
    "rechargeAmount": 50,
    "overdraftSettled": 0,
    "pendingDeductSettled": 0,
    "actualAdded": 50,
    "balanceBefore": 0,
    "balanceAfter": 50,
    "totalBefore": 100,
    "totalAfter": 150,
    "overdraftBefore": 0,
    "overdraftAfter": 0,
    "pendingDeductBefore": 0,
    "pendingDeductAfter": 0
  },
  "billingRecord": {
    "id": 123,
    "transactionType": "RECHARGE",
    "amountMinutes": 50,
    "balanceBefore": 0,
    "balanceAfter": 50,
    "status": "COMPLETED"
  }
}
```

## 日志输出

当PMI状态被激活时，会输出以下日志：

```
INFO  c.z.service.PmiRechargeService - PMI 1 充值后可用时长大于0(50 分钟)，激活PMI状态：INACTIVE -> ACTIVE
```

## 业务价值

1. **自动化管理**: 无需手动激活PMI，充值后自动激活
2. **用户体验**: 充值后立即可用，无需额外操作
3. **状态一致性**: 确保有可用时长的PMI处于活跃状态
4. **智能判断**: 只有在可用时长大于0时才激活，避免无意义的状态变更

## 注意事项

1. 只有按时长计费(`BY_TIME`)的PMI才支持充值
2. 充值分配策略：优先结清超额时长 → 结清待扣时长 → 增加可用时长
3. 只有当最终可用时长大于0且当前状态不是ACTIVE时才会激活
4. 状态变更会记录在日志中，便于追踪和调试
5. 使用了通用的`checkAndActivatePmi`方法，便于将来扩展到其他操作

## 扩展性

### 通用激活方法

`checkAndActivatePmi(PmiRecord pmiRecord, int availableMinutes, String context)` 方法被设计为通用工具方法，可以在其他需要检查PMI激活状态的地方复用，例如：

- 退款操作后
- 管理员手动调整时长后
- 其他增加可用时长的业务操作

### 使用示例

```java
// 在其他服务中使用
checkAndActivatePmi(pmiRecord, newAvailableMinutes, "退款后");
checkAndActivatePmi(pmiRecord, adjustedMinutes, "管理员调整后");
```
