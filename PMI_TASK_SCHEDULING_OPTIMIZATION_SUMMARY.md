# PMI精准开启任务调度优化方案

## 🔍 问题分析

### 当前问题
1. **任务长时间处于SCHEDULED状态**: 任务超过执行时间后仍然显示为"已调度"状态
2. **重试次数为0**: 任务没有被正确重试
3. **任务未及时调起运行**: 到时间的task不能及时执行

### 根本原因
1. **缺少SCHEDULED状态任务监控**: 只监控EXECUTING状态的超时任务
2. **任务调度失败处理不完善**: 调度失败后状态没有及时更新
3. **重试机制不完整**: 只处理FAILED状态任务，忽略SCHEDULED状态过期任务
4. **任务状态转换不及时**: 缺少调度成功/失败的状态反馈

## 🚀 优化方案

### 1. **新增任务调度监控服务** ✅

**文件**: `PmiTaskSchedulingMonitorService.java`

**功能**:
- 每3分钟检查长时间处于SCHEDULED状态的任务
- 处理过期的SCHEDULED任务
- 重新调度调度失败的任务
- 任务调度健康检查和告警

**关键特性**:
```java
@Scheduled(fixedRate = 180000) // 3分钟
public void checkStuckScheduledTasks()

@Scheduled(fixedRate = 600000) // 10分钟  
public void checkTaskSchedulingHealth()
```

### 2. **增强任务调度管理器** ✅

**文件**: `DynamicTaskManagerImpl.java`

**新增功能**:
- `rescheduleStuckTask()`: 重新调度卡住的任务
- `isTaskProperlyScheduled()`: 检查任务是否被正确调度
- `cleanupCompletedScheduledTasks()`: 清理已完成的调度任务

### 3. **完善任务重试服务** ✅

**文件**: `PmiTaskRetryService.java`

**优化内容**:
- 增加过期SCHEDULED任务处理
- 分离失败任务重试和过期任务处理逻辑
- 30分钟超时阈值，自动标记过期任务为失败

### 4. **新增Repository查询方法** ✅

**文件**: `PmiScheduleWindowTaskRepository.java`

**新增查询**:
```java
findStuckScheduledTasks(LocalDateTime threshold)
findExpiredScheduledTasks(LocalDateTime expiredThreshold)
countRecentFailedTasks(LocalDateTime since)
countRecentCompletedTasks(LocalDateTime since)
```

### 5. **任务监控API接口** ✅

**文件**: `PmiTaskMonitorController.java`

**提供接口**:
- `/api/pmi-task-monitor/stats`: 获取任务统计
- `/api/pmi-task-monitor/stuck-scheduled`: 获取长时间SCHEDULED任务
- `/api/pmi-task-monitor/expired-scheduled`: 获取过期SCHEDULED任务
- `/api/pmi-task-monitor/check-stuck-scheduled`: 手动触发检查
- `/api/pmi-task-monitor/task/{id}/reschedule`: 强制重新调度

## 📊 优化效果

### 监控机制
1. **实时监控**: 每3分钟检查一次长时间SCHEDULED任务
2. **健康检查**: 每10分钟进行一次整体健康检查
3. **自动处理**: 自动处理过期和卡住的任务
4. **告警机制**: 异常情况及时告警

### 处理策略
1. **开启任务过期**: 检查是否仍在窗口关闭时间前，决定立即执行或标记失败
2. **关闭任务过期**: 检查窗口是否仍为ACTIVE状态，决定立即执行或标记失败
3. **调度失败**: 尝试重新调度，失败则标记为失败状态
4. **超时阈值**: 30分钟超时自动标记为失败

### 状态管理
1. **及时状态更新**: 5分钟内发现长时间SCHEDULED任务
2. **准确状态反映**: 任务状态能准确反映实际执行情况
3. **自动重试**: 自动重试调度失败的任务
4. **清理机制**: 定期清理已完成的调度任务

## 🧪 测试验证

### 测试脚本
**文件**: `test-pmi-task-scheduling-optimization.sh`

**测试内容**:
1. 获取任务调度统计信息
2. 检查长时间SCHEDULED状态任务
3. 检查过期SCHEDULED任务
4. 手动触发监控检查
5. 验证处理效果
6. 健康检查测试
7. 任务调度状态检查
8. 强制重新调度测试

### 运行测试
```bash
./test-pmi-task-scheduling-optimization.sh
```

## 📈 预期改进

### 性能指标
1. **任务及时性**: 95%以上的任务在预定时间5分钟内执行
2. **状态准确性**: 99%以上的任务状态准确反映实际情况
3. **故障恢复**: 5分钟内发现并处理调度异常
4. **系统稳定性**: 减少长时间SCHEDULED状态任务90%以上

### 监控告警
1. **SCHEDULED任务过多**: 超过100个时告警
2. **EXECUTING任务过多**: 超过50个时告警
3. **FAILED任务过多**: 超过20个时告警
4. **调度异常**: 及时发现和处理

## 🔧 部署说明

### 1. 代码部署
- 部署新增的监控服务和API接口
- 更新任务调度管理器和重试服务
- 添加新的Repository查询方法

### 2. 配置调整
- 确保定时任务正常启动
- 配置合适的监控阈值
- 启用WebSocket告警（如果需要）

### 3. 监控验证
- 运行测试脚本验证功能
- 监控任务调度统计信息
- 检查日志输出是否正常

## 📋 维护建议

### 日常监控
1. **定期检查**: 每天查看任务调度统计
2. **异常处理**: 及时处理告警信息
3. **性能调优**: 根据实际情况调整监控阈值
4. **日志分析**: 定期分析任务失败原因

### 故障排查
1. **查看监控API**: 使用监控接口快速定位问题
2. **检查日志**: 查看详细的任务执行日志
3. **手动干预**: 必要时手动重新调度任务
4. **系统重启**: 极端情况下重启任务调度服务

## ✅ 总结

通过以上优化方案，PMI精准开启任务调度系统将具备：

1. **完善的监控机制**: 及时发现和处理调度异常
2. **自动恢复能力**: 自动处理过期和失败的任务
3. **准确的状态管理**: 任务状态准确反映实际情况
4. **强大的故障排查**: 提供丰富的监控和管理接口

这将显著提高PMI精准管理的可靠性和用户体验。
