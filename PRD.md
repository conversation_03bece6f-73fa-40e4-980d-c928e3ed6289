# ZoomBus 产品需求文档 (PRD)

## 📋 文档信息

| 项目 | 信息 |
|------|------|
| **产品名称** | ZoomBus - 企业级Zoom用户管理系统 |
| **版本** | v1.0 |
| **文档版本** | 1.0 |
| **创建日期** | 2025-07-31 |
| **最后更新** | 2025-07-31 |
| **产品经理** | - |
| **开发团队** | ZoomBus开发团队 |

---

## 🎯 产品概述

### 产品定位
ZoomBus是一个基于Spring Boot和React的企业级Zoom用户管理系统，为企业提供统一的Zoom账号管理、会议调度和PMI个人会议室管理解决方案。

### 核心价值
- **统一管理**: 集中管理企业内部用户和Zoom账号
- **高效协作**: 简化会议创建和PMI使用流程
- **安全可控**: 提供权限管理和访问控制
- **响应式设计**: 支持桌面端和移动端访问

### 目标用户
- **企业管理员**: 负责用户和Zoom账号管理
- **会议组织者**: 需要创建和管理会议
- **终端用户**: 使用PMI个人会议室的员工

---

## 🏗️ 系统架构

### 技术架构
```
┌─────────────────┐    ┌─────────────────┐
│   管理端前端     │    │   用户端前端     │
│   (React)       │    │   (React)       │
│   端口: 3000    │    │   端口: 3001    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │   后端API服务    │
         │  (Spring Boot)  │
         │   端口: 8080    │
         └─────────────────┘
                     │
         ┌─────────────────┐
         │   MySQL数据库   │
         │   (生产环境)    │
         └─────────────────┘
```

### 部署架构
- **开发环境**: H2内存数据库
- **生产环境**: MySQL数据库
- **前端构建**: 静态文件部署到后端resources目录
- **API集成**: Zoom API完整集成

---

## 🎨 产品功能

### 1. 管理端系统 (端口3000)

#### 1.1 用户认证与权限
- **登录系统**: JWT token认证
- **权限管理**: 基于角色的访问控制
- **会话管理**: 自动过期和续期

#### 1.2 仪表板 (Dashboard)
- **系统统计**: 用户数量、会议数量、PMI数量
- **实时监控**: 系统性能和状态监控
- **最近活动**: 最近会议和操作记录
- **数据可视化**: 图表展示关键指标

#### 1.3 用户管理 (User Management)
- **用户CRUD**: 创建、查看、编辑、删除用户
- **用户信息**: 姓名、邮箱、部门、电话等
- **状态管理**: 激活、禁用用户状态
- **批量操作**: 批量导入、导出用户
- **搜索筛选**: 按姓名、邮箱、部门搜索

#### 1.4 Zoom账号管理 (Zoom Auth Management)
- **认证配置**: OAuth 2.0和Server-to-Server认证
- **账号管理**: 多Zoom账号支持
- **Token管理**: 自动刷新和状态监控
- **权限配置**: API权限和范围设置

#### 1.5 Zoom用户管理 (Zoom User Management)
- **用户同步**: 从Zoom API同步用户信息
- **用户类型**: LICENSED、BASIC用户管理
- **状态监控**: 用户状态和使用情况
- **批量操作**: 批量更新和管理

#### 1.6 会议管理 (Meeting Management)
- **会议创建**: 即时会议和预约会议
- **会议配置**: 密码、等候室、录制等设置
- **重复会议**: 支持每日、每周、每月重复
- **会议控制**: 开始、结束、删除会议
- **参会链接**: 主持人和参会者链接生成

#### 1.7 PMI管理 (PMI Management)
- **PMI生成**: 为用户生成个人会议室
- **PMI配置**: 自定义密码和设置
- **状态管理**: 激活、禁用PMI状态
- **使用统计**: PMI使用情况和统计
- **批量管理**: 批量生成和管理PMI

#### 1.8 PMI计划管理 (PMI Schedule Management)
- **计划创建**: 为PMI创建使用计划
- **时间窗口**: 设置可用时间段
- **重复规则**: 支持复杂的重复模式
- **状态调度**: 自动激活和禁用PMI

#### 1.9 Webhook事件监控 (Webhook Events)
- **事件接收**: 接收Zoom webhook通知
- **事件处理**: 自动处理会议状态变化
- **事件日志**: 详细的事件记录和查询
- **实时监控**: 实时显示事件状态

#### 1.10 管理员用户管理 (Admin User Management)
- **管理员账号**: 创建和管理管理员
- **角色权限**: 不同级别的管理权限
- **操作日志**: 管理员操作记录
- **安全设置**: 密码策略和安全配置

### 2. 用户端系统 (端口3001)

#### 2.1 PMI使用页面 (Public PMI Usage)
- **直接访问**: 仅支持`/m/{pmiNumber}`直接访问
- **无需登录**: 终端用户无需注册登录
- **一键开启**: 快速配置Zoom账号并生成主持人链接
- **信息复制**: 一键复制会议信息
- **状态显示**: 显示PMI状态和可用性
- **响应式设计**: 完美适配移动设备

#### 2.2 安全特性
- **访问控制**: 用户只能访问自己的PMI链接
- **无搜索功能**: 防止用户枚举其他PMI
- **状态验证**: 验证PMI有效性和状态
- **错误处理**: 友好的错误提示和处理

---

## 📊 数据模型

### 核心实体

#### 1. 用户实体 (User)
```sql
t_users
├── id (主键)
├── username (用户名，唯一)
├── email (邮箱，唯一)
├── full_name (姓名)
├── department (部门)
├── phone (电话)
├── status (状态: ACTIVE/INACTIVE)
├── created_at (创建时间)
└── updated_at (更新时间)
```

#### 2. Zoom认证实体 (ZoomAuth)
```sql
t_zoom_auth
├── id (主键)
├── account_name (账号名称)
├── client_id (客户端ID)
├── client_secret (客户端密钥)
├── access_token (访问令牌)
├── token_expires_at (令牌过期时间)
├── status (状态)
└── 其他认证字段
```

#### 3. PMI记录实体 (PmiRecord)
```sql
t_pmi_records
├── id (主键)
├── user_id (用户ID，外键)
├── pmi_number (PMI号码，唯一)
├── pmi_password (PMI密码)
├── host_url (主持人链接)
├── status (状态: ACTIVE/INACTIVE)
├── created_at (创建时间)
└── updated_at (更新时间)
```

#### 4. 会议实体 (Meeting)
```sql
t_meetings
├── id (主键)
├── creator_user_id (创建者ID)
├── zoom_meeting_id (Zoom会议ID)
├── topic (会议主题)
├── agenda (会议议程)
├── start_time (开始时间)
├── duration (持续时间)
├── status (状态)
└── 其他会议配置字段
```

### 关系设计
- User 1:N PmiRecord (一个用户可以有多个PMI)
- User 1:N Meeting (一个用户可以创建多个会议)
- ZoomAuth 1:N ZoomUser (一个认证账号下有多个Zoom用户)
- PmiRecord 1:N PmiSchedule (一个PMI可以有多个计划)

---

## 🔌 API设计

### RESTful API规范

#### 认证相关
```
POST /api/auth/login          # 管理员登录
POST /api/auth/logout         # 登出
```

#### 用户管理
```
GET    /api/users             # 获取用户列表(分页)
POST   /api/users             # 创建用户
GET    /api/users/{id}        # 获取用户详情
PUT    /api/users/{id}        # 更新用户
DELETE /api/users/{id}        # 删除用户
GET    /api/users/search      # 搜索用户
```

#### PMI管理
```
GET    /api/pmi               # 获取PMI列表
POST   /api/pmi/generate      # 生成PMI
GET    /api/pmi/{id}          # 获取PMI详情
PUT    /api/pmi/{id}          # 更新PMI
DELETE /api/pmi/{id}          # 删除PMI
GET    /api/pmi/user/{userId} # 获取用户的PMI列表
```

#### 公共PMI API
```
GET    /api/public/pmi/{pmiNumber}     # 获取PMI信息
POST   /api/public/pmi/{pmiNumber}/use # 使用PMI开启会议
```

#### 会议管理
```
GET    /api/meetings          # 获取会议列表
POST   /api/meetings          # 创建会议
GET    /api/meetings/{id}     # 获取会议详情
PUT    /api/meetings/{id}     # 更新会议
DELETE /api/meetings/{id}     # 删除会议
```

#### Zoom集成
```
GET    /api/zoom-auth         # 获取Zoom认证列表
POST   /api/zoom-auth         # 创建Zoom认证
GET    /api/zoom-users        # 获取Zoom用户列表
POST   /api/zoom-users/sync   # 同步Zoom用户
```

#### Webhook
```
POST   /api/webhooks/zoom     # 接收Zoom webhook
GET    /api/webhooks/events   # 获取webhook事件
```

---

## 🎨 用户界面设计

### 设计原则
- **响应式设计**: 适配桌面端和移动端
- **一致性**: 统一的设计语言和交互模式
- **易用性**: 简洁直观的用户界面
- **可访问性**: 支持键盘导航和屏幕阅读器

### 管理端界面

#### 布局结构
```
┌─────────────────────────────────────┐
│ Header (Logo + 用户信息 + 操作)      │
├─────────┬───────────────────────────┤
│ Sidebar │ Main Content Area         │
│ (菜单)  │ (页面内容)                │
│         │                           │
│         │                           │
│         │                           │
└─────────┴───────────────────────────┘
```

#### 主要页面
1. **仪表板**: 卡片式布局，展示关键指标
2. **列表页面**: 表格 + 搜索 + 分页 + 操作按钮
3. **表单页面**: 分组表单 + 验证 + 提交按钮
4. **详情页面**: 信息展示 + 操作按钮

### 用户端界面

#### 设计特点
- **极简设计**: 专注于PMI使用功能
- **大按钮**: 适合移动端触摸操作
- **清晰提示**: 明确的状态和操作提示
- **快速操作**: 一键复制和开启功能

---

## 🔒 安全需求

### 认证与授权
- **JWT认证**: 基于token的无状态认证
- **角色权限**: 基于角色的访问控制(RBAC)
- **会话管理**: 自动过期和安全登出
- **密码安全**: 密码加密存储和强度要求

### 数据安全
- **数据加密**: 敏感数据加密存储
- **API安全**: 请求验证和防护
- **输入验证**: 防止SQL注入和XSS攻击
- **访问控制**: 用户只能访问授权资源

### 隐私保护
- **数据最小化**: 只收集必要的用户信息
- **访问限制**: 用户端无法搜索其他用户PMI
- **日志记录**: 操作日志和审计跟踪
- **数据备份**: 定期备份和恢复机制

---

## 📱 响应式设计

### 断点设计
- **桌面端**: ≥ 1200px
- **平板端**: 768px - 1199px  
- **移动端**: < 768px

### 适配策略
- **菜单适配**: 移动端自动折叠侧边栏
- **表格适配**: 移动端卡片式展示
- **按钮适配**: 移动端增大触摸区域
- **字体适配**: 根据屏幕尺寸调整字体大小

---

## 🚀 性能需求

### 响应时间
- **页面加载**: < 3秒
- **API响应**: < 1秒
- **数据库查询**: < 500ms
- **文件上传**: 支持大文件上传

### 并发处理
- **用户并发**: 支持100+并发用户
- **API并发**: 支持1000+并发请求
- **数据库连接**: 连接池管理
- **缓存策略**: Redis缓存热点数据

### 可扩展性
- **水平扩展**: 支持多实例部署
- **数据库扩展**: 支持读写分离
- **CDN支持**: 静态资源CDN加速
- **负载均衡**: 支持负载均衡器

---

## 🔄 业务流程

### 1. 用户管理流程
```mermaid
graph TD
    A[创建用户] --> B[设置用户信息]
    B --> C[激活用户]
    C --> D[分配Zoom账号]
    D --> E[生成PMI]
    E --> F[用户可使用PMI]
```

### 2. PMI使用流程
```mermaid
graph TD
    A[用户访问PMI链接] --> B[验证PMI状态]
    B --> C{PMI是否可用?}
    C -->|是| D[显示PMI信息]
    C -->|否| E[显示错误信息]
    D --> F[用户点击开启]
    F --> G[配置Zoom账号]
    G --> H[生成主持人链接]
    H --> I[用户开始会议]
```

### 3. 会议创建流程
```mermaid
graph TD
    A[管理员创建会议] --> B[选择Zoom用户]
    B --> C[设置会议参数]
    C --> D[调用Zoom API]
    D --> E{创建成功?}
    E -->|是| F[保存会议信息]
    E -->|否| G[显示错误信息]
    F --> H[生成会议链接]
    H --> I[发送邀请]
```

---

## 📋 功能清单

### 已完成功能 ✅

#### 后端功能
- ✅ 用户管理CRUD操作
- ✅ Zoom认证管理和Token自动刷新
- ✅ PMI生成和管理
- ✅ 会议创建和管理
- ✅ Webhook事件处理
- ✅ 管理员认证和权限控制
- ✅ 公共PMI API
- ✅ PMI计划和调度管理
- ✅ 系统监控和统计
- ✅ 数据库迁移和初始化

#### 前端功能
- ✅ 管理端完整界面
- ✅ 用户端PMI使用页面
- ✅ 响应式设计和移动端适配
- ✅ 仪表板和数据可视化
- ✅ 表单验证和错误处理
- ✅ 搜索、分页和筛选
- ✅ 批量操作支持
- ✅ 实时状态更新

#### 部署和运维
- ✅ 开发环境配置
- ✅ 生产环境部署
- ✅ 自动化部署脚本
- ✅ 数据库配置和迁移
- ✅ 日志记录和监控
- ✅ 错误处理和恢复

### 待优化功能 🔄

#### 功能增强
- 🔄 用户权限细化管理
- 🔄 会议录制管理
- 🔄 报表统计功能
- 🔄 邮件通知系统
- 🔄 多语言支持
- 🔄 主题定制功能

#### 技术优化
- 🔄 Redis缓存集成
- 🔄 消息队列处理
- 🔄 分布式配置中心
- 🔄 容器化部署
- 🔄 API文档自动生成
- 🔄 单元测试覆盖

---

## 🧪 测试策略

### 单元测试
- **Service层测试**: 业务逻辑验证
- **Repository层测试**: 数据访问验证
- **Controller层测试**: API接口验证
- **工具类测试**: 工具方法验证

### 集成测试
- **API端到端测试**: 完整业务流程测试
- **数据库集成测试**: 数据持久化测试
- **Zoom API集成测试**: 第三方API集成测试
- **Webhook测试**: 事件处理测试

### 前端测试
- **组件单元测试**: React组件测试
- **用户交互测试**: 用户操作流程测试
- **API调用测试**: 前后端接口测试
- **响应式测试**: 多设备适配测试

### 性能测试
- **负载测试**: 并发用户测试
- **压力测试**: 系统极限测试
- **稳定性测试**: 长时间运行测试
- **资源监控**: CPU、内存、网络监控

---

## 📈 监控和运维

### 系统监控
- **应用监控**: Spring Boot Actuator
- **数据库监控**: 连接池和查询性能
- **API监控**: 响应时间和错误率
- **资源监控**: CPU、内存、磁盘使用

### 日志管理
- **应用日志**: 分级日志记录
- **访问日志**: 用户访问记录
- **错误日志**: 异常和错误追踪
- **审计日志**: 操作审计记录

### 备份策略
- **数据库备份**: 定期自动备份
- **配置备份**: 系统配置备份
- **代码备份**: Git版本控制
- **文件备份**: 上传文件备份

### 故障处理
- **监控告警**: 异常情况自动告警
- **故障恢复**: 自动重启和恢复
- **数据恢复**: 备份数据恢复流程
- **应急预案**: 故障处理预案

---

## 🔮 未来规划

### 短期规划 (1-3个月)
- 🎯 用户权限系统完善
- 🎯 会议录制功能开发
- 🎯 邮件通知系统集成
- 🎯 移动端APP开发
- 🎯 API文档完善

### 中期规划 (3-6个月)
- 🎯 多租户架构支持
- 🎯 高级报表和分析
- 🎯 第三方系统集成
- 🎯 AI智能调度
- 🎯 国际化支持

### 长期规划 (6-12个月)
- 🎯 微服务架构重构
- 🎯 云原生部署
- 🎯 大数据分析平台
- 🎯 机器学习优化
- 🎯 开放平台建设

---

## 📚 技术文档

### 开发文档
- [开发环境搭建指南](DEVELOPMENT_GUIDE.md)
- [API接口文档](docs/api-documentation.md)
- [数据库设计文档](docs/database-design.md)
- [部署指南](docs/deployment-guide.md)

### 用户文档
- [用户使用手册](docs/user-manual.md)
- [管理员指南](docs/admin-guide.md)
- [常见问题FAQ](docs/faq.md)
- [故障排查指南](docs/troubleshooting.md)

### 项目文档
- [项目总结](PROJECT_SUMMARY.md)
- [开发记录](command.md)
- [功能特性说明](README.md)
- [更新日志](CHANGELOG.md)

---

## 🤝 团队协作

### 开发流程
- **需求分析**: 产品需求分析和评估
- **技术设计**: 系统架构和技术方案设计
- **开发实现**: 功能开发和代码实现
- **测试验证**: 功能测试和质量保证
- **部署上线**: 生产环境部署和监控

### 代码管理
- **版本控制**: Git版本控制
- **分支策略**: GitFlow工作流
- **代码审查**: Pull Request审查
- **持续集成**: 自动化构建和测试

### 沟通协作
- **需求沟通**: 定期需求评审会议
- **技术讨论**: 技术方案讨论和决策
- **进度同步**: 每日站会和周报
- **问题解决**: 及时沟通和协作解决

---

## 📞 联系信息

### 项目信息
- **项目名称**: ZoomBus
- **项目地址**: https://github.com/zbspace01/zoombus
- **文档地址**: 项目根目录文档
- **演示地址**: 待部署

### 技术支持
- **开发团队**: ZoomBus开发团队
- **技术栈**: Java 11 + Spring Boot + React + MySQL
- **部署环境**: Linux + Docker + Nginx
- **监控工具**: Spring Boot Actuator + 自定义监控

---

*文档版本: v1.0*
*最后更新: 2025-07-31*
*状态: 已完成开发，持续优化中*
*下次更新: 根据功能迭代需要*
