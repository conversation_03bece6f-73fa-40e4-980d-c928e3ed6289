# PMI窗口即时执行机制设计文档

## 1. 问题分析

### 1.1 当前问题
目前通过task精准管理窗口机制存在以下问题：
1. **即刻开始的窗口延迟执行**：创建时间为当前时间或过往时刻的窗口没有立即开始
2. **过往时刻窗口处理不及时**：历史时间点的窗口需要等待调度器轮询才能执行
3. **用户体验差**：用户期望立即生效的窗口需要等待，影响使用体验

### 1.2 当前机制分析
```java
// 当前流程：窗口创建 -> 事件监听 -> 创建任务 -> 调度执行
@EventListener
public void handlePmiWindowCreated(PmiWindowCreatedEvent event) {
    // 创建开启任务（总是调度到未来时间）
    Long openTaskId = dynamicTaskManager.schedulePmiWindowOpenTask(
        window.getId(), window.getStartDateTime());
}
```

**问题根源**：
- 所有任务都通过调度器在指定时间执行
- 没有区分"立即执行"和"延迟执行"的场景
- 缺少对过往时间和当前时间的特殊处理

## 2. 解决方案设计

### 2.1 核心思路
在任务创建时判断执行时间，对于即刻或过往时刻的任务立即执行，而不是等待调度。

### 2.2 时间判断策略
```java
public enum ExecutionStrategy {
    IMMEDIATE,    // 立即执行（过往时间或当前时间±容差范围内）
    SCHEDULED,    // 调度执行（未来时间）
    EXPIRED       // 已过期（超出有效期）
}
```

### 2.3 时间容差配置
```yaml
pmi:
  task:
    scheduling:
      immediate-execution:
        enabled: true                    # 是否启用立即执行
        tolerance-minutes: 2             # 时间容差（分钟）
        max-delay-minutes: 60           # 最大延迟执行时间（分钟）
        enable-past-execution: true      # 是否允许执行过往时间的任务
```

## 3. 详细设计

### 3.1 执行策略判断器
```java
@Component
public class TaskExecutionStrategyDecider {
    
    @Value("${pmi.task.scheduling.immediate-execution.tolerance-minutes:2}")
    private int toleranceMinutes;
    
    @Value("${pmi.task.scheduling.immediate-execution.max-delay-minutes:60}")
    private int maxDelayMinutes;
    
    public ExecutionStrategy decideStrategy(LocalDateTime scheduledTime, 
                                          PmiScheduleWindow window) {
        LocalDateTime now = LocalDateTime.now();
        long minutesDiff = Duration.between(scheduledTime, now).toMinutes();
        
        // 过往时间：检查是否在有效期内
        if (scheduledTime.isBefore(now)) {
            if (isOpenTaskStillValid(scheduledTime, window, now)) {
                return ExecutionStrategy.IMMEDIATE;
            } else {
                return ExecutionStrategy.EXPIRED;
            }
        }
        
        // 当前时间（容差范围内）
        if (Math.abs(minutesDiff) <= toleranceMinutes) {
            return ExecutionStrategy.IMMEDIATE;
        }
        
        // 未来时间
        return ExecutionStrategy.SCHEDULED;
    }
    
    private boolean isOpenTaskStillValid(LocalDateTime scheduledTime, 
                                       PmiScheduleWindow window, 
                                       LocalDateTime now) {
        // 开启任务：只要窗口还没结束就可以执行
        if (window.getEndDateTime().isAfter(now)) {
            return true;
        }
        
        // 关闭任务：允许一定延迟
        long delayMinutes = Duration.between(scheduledTime, now).toMinutes();
        return delayMinutes <= maxDelayMinutes;
    }
}
```

### 3.2 增强的任务调度器
```java
@Service
public class EnhancedDynamicTaskManager implements DynamicTaskManager {
    
    private final TaskExecutionStrategyDecider strategyDecider;
    private final PmiWindowTaskExecutor taskExecutor;
    
    @Override
    public Long schedulePmiWindowOpenTask(Long windowId, LocalDateTime executeTime) {
        PmiScheduleWindow window = getWindow(windowId);
        ExecutionStrategy strategy = strategyDecider.decideStrategy(executeTime, window);
        
        switch (strategy) {
            case IMMEDIATE:
                return executeImmediately(windowId, executeTime, TaskType.PMI_WINDOW_OPEN);
                
            case SCHEDULED:
                return scheduleForFuture(windowId, executeTime, TaskType.PMI_WINDOW_OPEN);
                
            case EXPIRED:
                return markAsExpired(windowId, executeTime, TaskType.PMI_WINDOW_OPEN);
                
            default:
                throw new IllegalStateException("未知的执行策略: " + strategy);
        }
    }
    
    /**
     * 立即执行任务
     */
    private Long executeImmediately(Long windowId, LocalDateTime originalTime, TaskType taskType) {
        log.info("立即执行任务: windowId={}, originalTime={}, taskType={}", 
                windowId, originalTime, taskType);
        
        // 创建任务记录
        PmiScheduleWindowTask task = createTaskRecord(windowId, taskType, originalTime);
        task = taskRepository.save(task);
        
        // 异步立即执行
        CompletableFuture.runAsync(() -> {
            try {
                if (taskType == TaskType.PMI_WINDOW_OPEN) {
                    taskExecutor.executeOpenTask(windowId, task.getId());
                } else {
                    taskExecutor.executeCloseTask(windowId, task.getId());
                }
            } catch (Exception e) {
                log.error("立即执行任务失败: taskId={}", task.getId(), e);
            }
        });
        
        return task.getId();
    }
    
    /**
     * 调度到未来执行
     */
    private Long scheduleForFuture(Long windowId, LocalDateTime executeTime, TaskType taskType) {
        // 使用原有的调度逻辑
        return originalScheduleLogic(windowId, executeTime, taskType);
    }
    
    /**
     * 标记为过期
     */
    private Long markAsExpired(Long windowId, LocalDateTime executeTime, TaskType taskType) {
        log.warn("任务已过期，标记为失败: windowId={}, executeTime={}", windowId, executeTime);
        
        PmiScheduleWindowTask task = createTaskRecord(windowId, taskType, executeTime);
        task.markAsFailed("任务创建时已过期");
        task = taskRepository.save(task);
        
        return task.getId();
    }
}
```

### 3.3 配置类
```java
@Configuration
@ConfigurationProperties(prefix = "pmi.task.scheduling.immediate-execution")
@Data
public class ImmediateExecutionConfig {
    
    /**
     * 是否启用立即执行机制
     */
    private boolean enabled = true;
    
    /**
     * 时间容差（分钟）
     * 在此范围内的任务将被视为"当前时间"并立即执行
     */
    private int toleranceMinutes = 2;
    
    /**
     * 最大延迟执行时间（分钟）
     * 超过此时间的过往任务将被标记为过期
     */
    private int maxDelayMinutes = 60;
    
    /**
     * 是否允许执行过往时间的任务
     */
    private boolean enablePastExecution = true;
    
    /**
     * 立即执行的线程池配置
     */
    private ThreadPool threadPool = new ThreadPool();
    
    @Data
    public static class ThreadPool {
        private int coreSize = 5;
        private int maxSize = 10;
        private int queueCapacity = 100;
        private String threadNamePrefix = "immediate-task-";
    }
}
```

## 4. 实现步骤

### 4.1 第一阶段：基础实现
1. 创建执行策略判断器
2. 增强DynamicTaskManager
3. 添加配置支持
4. 单元测试

### 4.2 第二阶段：优化完善
1. 添加监控指标
2. 性能优化
3. 错误处理增强
4. 集成测试

### 4.3 第三阶段：生产部署
1. 灰度发布
2. 监控告警
3. 性能调优
4. 文档完善

## 5. 监控和告警

### 5.1 关键指标
```java
@Component
public class ImmediateExecutionMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // 立即执行任务数量
    private final Counter immediateExecutionCounter;
    
    // 立即执行耗时
    private final Timer immediateExecutionTimer;
    
    // 过期任务数量
    private final Counter expiredTaskCounter;
    
    public void recordImmediateExecution(Duration duration) {
        immediateExecutionCounter.increment();
        immediateExecutionTimer.record(duration);
    }
    
    public void recordExpiredTask() {
        expiredTaskCounter.increment();
    }
}
```

### 5.2 告警规则
- 立即执行失败率 > 5%
- 过期任务数量 > 10/小时
- 立即执行平均耗时 > 5秒

## 6. 兼容性考虑

### 6.1 向后兼容
- 保持原有API不变
- 通过配置开关控制新功能
- 渐进式迁移

### 6.2 降级策略
```java
@Component
public class TaskExecutionFallback {
    
    public Long executeWithFallback(Long windowId, LocalDateTime executeTime, TaskType taskType) {
        try {
            // 尝试新的立即执行机制
            return enhancedTaskManager.scheduleTask(windowId, executeTime, taskType);
        } catch (Exception e) {
            log.warn("立即执行机制失败，降级到原有调度: {}", e.getMessage());
            // 降级到原有调度机制
            return originalTaskManager.scheduleTask(windowId, executeTime, taskType);
        }
    }
}
```

## 7. 测试策略

### 7.1 单元测试
- 执行策略判断逻辑
- 时间容差计算
- 过期判断逻辑

### 7.2 集成测试
- 端到端任务执行流程
- 并发执行场景
- 异常处理场景

### 7.3 性能测试
- 大量立即执行任务的性能
- 内存使用情况
- 线程池使用效率

## 8. 风险评估

### 8.1 技术风险
- **并发问题**：大量立即执行可能导致资源竞争
- **内存泄漏**：异步执行可能导致内存问题
- **数据一致性**：立即执行与调度执行的状态同步

### 8.2 缓解措施
- 限制并发执行数量
- 完善监控和告警
- 充分的测试覆盖
- 渐进式发布

## 9. 实现状态

### 9.1 已完成的组件

#### 核心组件
- ✅ **TaskExecutionStrategyDecider**: 执行策略判断器
- ✅ **ImmediateExecutionConfig**: 配置类
- ✅ **DynamicTaskManagerImpl**: 增强的任务管理器
- ✅ **配置文件**: application-pmi-tasks.yml

#### 关键特性
- ✅ **时间判断逻辑**: 支持过往、当前、未来时间的不同处理策略
- ✅ **立即执行**: 异步立即执行机制
- ✅ **降级策略**: 异常时自动降级到原有调度机制
- ✅ **配置灵活性**: 支持开关控制和参数调整

### 9.2 配置示例

```yaml
pmi:
  task:
    scheduling:
      immediate-execution:
        enabled: true                    # 启用立即执行
        tolerance-minutes: 2             # 2分钟容差
        max-delay-minutes: 60           # 最大延迟60分钟
        enable-past-execution: true      # 允许执行过往任务
```

### 9.3 使用效果

#### 改进前
```
窗口创建 -> 事件监听 -> 创建任务 -> 等待调度器 -> 执行
延迟: 可能数分钟
```

#### 改进后
```
窗口创建 -> 事件监听 -> 策略判断 -> 立即执行
延迟: 秒级
```

### 9.4 测试覆盖

- ✅ **单元测试**: TaskExecutionStrategyDeciderTest
- ✅ **策略判断**: 各种时间场景的测试
- ✅ **异常处理**: 降级机制测试
- ✅ **配置验证**: 参数有效性测试

### 9.5 部署建议

1. **灰度发布**: 先在测试环境验证
2. **监控指标**: 关注立即执行成功率和耗时
3. **配置调优**: 根据实际情况调整容差和延迟参数
4. **告警设置**: 配置失败率和过期任务告警

## 10. 总结

通过引入即时执行机制，显著改善了用户体验，特别是对于需要立即生效的PMI窗口。该设计：

- **保持兼容性**: 不影响现有功能
- **提升响应速度**: 即刻和过往时刻的窗口秒级响应
- **增强稳定性**: 完善的降级和异常处理机制
- **配置灵活**: 支持开关控制和参数调整
- **监控完善**: 提供充分的监控和告警能力

该实现已经完成核心功能，可以投入生产使用。
