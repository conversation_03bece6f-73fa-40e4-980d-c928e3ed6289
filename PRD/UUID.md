# 全局流水号机制设计方案

## 1. 背景与问题

### 1.1 现状问题
- 应用日志分布在多个文件和服务中
- 单个请求的完整调用链难以追踪
- 排查问题时需要在多个日志文件中搜索
- 缺乏统一的请求标识符

### 1.2 目标
- 为每个HTTP请求分配唯一的全局流水号
- 在整个请求生命周期中传递和记录流水号
- 支持跨服务、跨线程的日志追踪
- 提供便捷的日志查询和分析能力

## 2. 技术方案

### 2.1 流水号生成策略

#### 2.1.1 UUID格式设计
```
格式：{timestamp}-{nodeId}-{sequence}-{random}
示例：20250823143025-001-000001-a1b2c3
```

**组成部分：**
- `timestamp`: 14位时间戳 (yyyyMMddHHmmss)
- `nodeId`: 3位节点标识 (支持集群部署)
- `sequence`: 6位序列号 (同毫秒内递增)
- `random`: 6位随机字符 (增强唯一性)

#### 2.1.2 生成器实现
```java
@Component
public class TraceIdGenerator {
    private static final String NODE_ID = getNodeId(); // 从配置获取
    private final AtomicLong sequence = new AtomicLong(0);
    private volatile long lastTimestamp = 0;
    
    public String generateTraceId() {
        long timestamp = System.currentTimeMillis();
        long seq = getSequence(timestamp);
        String random = generateRandom();
        
        return String.format("%s-%s-%06d-%s", 
            formatTimestamp(timestamp), NODE_ID, seq, random);
    }
}
```

### 2.2 流水号传递机制

#### 2.2.1 HTTP请求拦截器
```java
@Component
public class TraceIdInterceptor implements HandlerInterceptor {
    private static final String TRACE_ID_HEADER = "X-Trace-Id";
    private static final String TRACE_ID_PARAM = "traceId";
    
    @Override
    public boolean preHandle(HttpServletRequest request, 
                           HttpServletResponse response, 
                           Object handler) {
        String traceId = extractTraceId(request);
        if (traceId == null) {
            traceId = traceIdGenerator.generateTraceId();
        }
        
        // 设置到ThreadLocal
        TraceContext.setTraceId(traceId);
        
        // 添加到响应头
        response.setHeader(TRACE_ID_HEADER, traceId);
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request,
                              HttpServletResponse response,
                              Object handler, Exception ex) {
        TraceContext.clear();
    }

    private String extractTraceId(HttpServletRequest request) {
        // 1. 从请求头获取
        String traceId = request.getHeader(TRACE_ID_HEADER);
        if (traceId != null) {
            return traceId;
        }

        // 2. 从URL参数获取
        traceId = request.getParameter(TRACE_ID_PARAM);
        if (traceId != null) {
            return traceId;
        }

        // 3. 特殊处理：Webhook请求
        if (isWebhookRequest(request)) {
            return generateWebhookTraceId(request);
        }

        return null;
    }

    private boolean isWebhookRequest(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return uri.contains("/webhook") || uri.contains("/callback");
    }

    private String generateWebhookTraceId(HttpServletRequest request) {
        // 为webhook生成特殊格式的TraceId
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String source = extractWebhookSource(request);
        String eventId = extractWebhookEventId(request);
        String random = String.format("%04d", new Random().nextInt(10000));

        return String.format("WH-%s-%s-%s-%s", timestamp, source, eventId, random);
    }

    private String extractWebhookSource(HttpServletRequest request) {
        // 从User-Agent或特定头部识别来源
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null && userAgent.toLowerCase().contains("zoom")) {
            return "ZOOM";
        }
        return "UNKNOWN";
    }

    private String extractWebhookEventId(HttpServletRequest request) {
        // 尝试从请求体或头部提取事件ID
        String eventId = request.getHeader("X-Event-Id");
        if (eventId != null) {
            return eventId.substring(0, Math.min(eventId.length(), 8));
        }
        return "EVENT";
    }
}
```

#### 2.2.2 ThreadLocal上下文
```java
public class TraceContext {
    private static final ThreadLocal<String> TRACE_ID = new ThreadLocal<>();
    private static final ThreadLocal<Map<String, Object>> CONTEXT = new ThreadLocal<>();
    
    public static void setTraceId(String traceId) {
        TRACE_ID.set(traceId);
        initContext();
    }
    
    public static String getTraceId() {
        return TRACE_ID.get();
    }
    
    public static void addContext(String key, Object value) {
        Map<String, Object> context = CONTEXT.get();
        if (context != null) {
            context.put(key, value);
        }
    }
    
    public static void clear() {
        TRACE_ID.remove();
        CONTEXT.remove();
    }
}
```

### 2.3 日志增强

#### 2.3.1 自定义日志格式
```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <pattern>
                    <pattern>
                        {
                            "traceId": "%X{traceId:-}",
                            "userId": "%X{userId:-}",
                            "requestUri": "%X{requestUri:-}",
                            "method": "%X{method:-}",
                            "message": "%message"
                        }
                    </pattern>
                </encoder>
        </encoder>
    </appender>
    
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/application.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
</configuration>
```

#### 2.3.2 MDC自动设置
```java
@Component
public class MdcFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        try {
            // 设置MDC
            MDC.put("traceId", TraceContext.getTraceId());
            MDC.put("requestUri", httpRequest.getRequestURI());
            MDC.put("method", httpRequest.getMethod());
            MDC.put("userAgent", httpRequest.getHeader("User-Agent"));
            
            chain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }
}
```

### 2.4 异步任务支持

#### 2.4.1 线程池装饰器
```java
@Configuration
public class AsyncConfig {
    
    @Bean
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setTaskDecorator(new TraceIdTaskDecorator());
        return executor;
    }
}

public class TraceIdTaskDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        String traceId = TraceContext.getTraceId();
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        
        return () -> {
            try {
                TraceContext.setTraceId(traceId);
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                runnable.run();
            } finally {
                TraceContext.clear();
                MDC.clear();
            }
        };
    }
}
```

#### 2.4.2 事件监听器增强
```java
@EventListener
@Async
public void handlePmiWindowCreated(PmiWindowCreatedEvent event) {
    // TraceId会自动传递到异步线程
    log.info("处理PMI窗口创建事件: windowId={}", event.getWindow().getId());
}
```

## 3. 实现细节

### 3.1 核心组件

#### 3.1.1 TraceId生成器
```java
@Component
@Slf4j
public class TraceIdGenerator {
    private static final String NODE_ID;
    private static final AtomicLong SEQUENCE = new AtomicLong(0);
    private static final SecureRandom RANDOM = new SecureRandom();
    
    static {
        // 从配置文件或环境变量获取节点ID
        NODE_ID = System.getProperty("app.node.id", "001");
    }
    
    public String generateTraceId() {
        long timestamp = System.currentTimeMillis();
        long sequence = SEQUENCE.incrementAndGet() % 1000000;
        String random = String.format("%06d", RANDOM.nextInt(1000000));
        
        return String.format("%s-%s-%06d-%s",
            new SimpleDateFormat("yyyyMMddHHmmss").format(new Date(timestamp)),
            NODE_ID,
            sequence,
            random);
    }
}
```

#### 3.1.2 日志工具类
```java
@Slf4j
public class TraceLogger {
    
    public static void info(String message, Object... args) {
        log.info("[{}] " + message, TraceContext.getTraceId(), args);
    }
    
    public static void error(String message, Throwable throwable, Object... args) {
        log.error("[{}] " + message, TraceContext.getTraceId(), throwable, args);
    }
    
    public static void debug(String message, Object... args) {
        log.debug("[{}] " + message, TraceContext.getTraceId(), args);
    }
    
    public static void warn(String message, Object... args) {
        log.warn("[{}] " + message, TraceContext.getTraceId(), args);
    }
}
```

### 3.2 配置管理

#### 3.2.1 应用配置
```yaml
# application.yml
app:
  trace:
    enabled: true
    node-id: ${NODE_ID:001}
    header-name: X-Trace-Id
    mdc-key: traceId
  logging:
    pattern:
      console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] %logger{36} - %msg%n"
      file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] %logger{36} - %msg%n"
```

#### 3.2.2 自动配置类
```java
@Configuration
@ConditionalOnProperty(name = "app.trace.enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(TraceProperties.class)
public class TraceAutoConfiguration {
    
    @Bean
    public TraceIdGenerator traceIdGenerator() {
        return new TraceIdGenerator();
    }
    
    @Bean
    public TraceIdInterceptor traceIdInterceptor() {
        return new TraceIdInterceptor();
    }
    
    @Bean
    public MdcFilter mdcFilter() {
        return new MdcFilter();
    }
}
```

## 4. 使用示例

### 4.1 控制器中使用
```java
@RestController
@Slf4j
public class PmiScheduleController {
    
    @PostMapping
    public ResponseEntity<Map<String, Object>> createSchedule(@RequestBody PmiScheduleRequest request) {
        String traceId = TraceContext.getTraceId();
        log.info("收到创建PMI计划请求: traceId={}, request={}", traceId, request);
        
        try {
            PmiScheduleResponse schedule = pmiScheduleService.createSchedule(request);
            log.info("PMI计划创建成功: traceId={}, scheduleId={}", traceId, schedule.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", schedule);
            response.put("traceId", traceId);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("PMI计划创建失败: traceId={}", traceId, e);
            throw e;
        }
    }
}
```

### 4.2 服务层中使用
```java
@Service
@Slf4j
public class PmiScheduleService {
    
    @Transactional
    public PmiScheduleResponse createSchedule(PmiScheduleRequest request) {
        String traceId = TraceContext.getTraceId();
        log.info("开始创建PMI计划: traceId={}, pmiRecordId={}", traceId, request.getPmiRecordId());
        
        // 业务逻辑...
        
        log.info("PMI计划创建完成: traceId={}, scheduleId={}", traceId, schedule.getId());
        return response;
    }
}
```

### 4.3 前端集成

#### 4.3.1 基础集成
```javascript
// TraceId生成器
const generateTraceId = () => {
  const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14);
  const nodeId = '001'; // 前端节点ID
  const sequence = String(Math.floor(Math.random() * 1000000)).padStart(6, '0');
  const random = Math.random().toString(36).substring(2, 8);
  return `${timestamp}-${nodeId}-${sequence}-${random}`;
};

// 前端请求拦截器
api.interceptors.request.use(
  (config) => {
    // 生成或获取traceId
    let traceId = sessionStorage.getItem('currentTraceId');
    if (!traceId) {
      traceId = generateTraceId();
      sessionStorage.setItem('currentTraceId', traceId);
    }

    config.headers['X-Trace-Id'] = traceId;

    // 在控制台显示请求信息
    console.log(`🚀 [${traceId}] ${config.method?.toUpperCase()} ${config.url}`);

    return config;
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    const traceId = response.headers['x-trace-id'] ||
                   response.config.headers['X-Trace-Id'];

    if (traceId) {
      // 更新当前traceId
      sessionStorage.setItem('currentTraceId', traceId);

      // 在控制台显示响应信息
      console.log(`✅ [${traceId}] ${response.status} ${response.config.url}`);

      // 触发traceId更新事件
      window.dispatchEvent(new CustomEvent('traceIdUpdated', {
        detail: { traceId, response }
      }));
    }

    return response;
  },
  (error) => {
    const traceId = error.response?.headers['x-trace-id'] ||
                   error.config?.headers['X-Trace-Id'];

    // 在控制台显示错误信息
    console.error(`❌ [${traceId}] ${error.response?.status || 'Network Error'} ${error.config?.url}`);

    // 在错误消息中显示traceId
    if (traceId) {
      const originalMessage = error.response?.data?.message || error.message;
      error.userMessage = `${originalMessage} (错误码: ${traceId})`;
    }

    return Promise.reject(error);
  }
);
```

#### 4.3.2 UI组件显示
```javascript
// TraceId显示组件
const TraceIdDisplay = () => {
  const [currentTraceId, setCurrentTraceId] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateTraceId = (event) => {
      setCurrentTraceId(event.detail.traceId);
      setIsVisible(true);

      // 3秒后自动隐藏
      setTimeout(() => setIsVisible(false), 3000);
    };

    window.addEventListener('traceIdUpdated', updateTraceId);
    return () => window.removeEventListener('traceIdUpdated', updateTraceId);
  }, []);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(currentTraceId);
    message.success('TraceId已复制到剪贴板');
  };

  if (!currentTraceId) return null;

  return (
    <div
      className={`trace-id-display ${isVisible ? 'visible' : ''}`}
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '6px',
        fontSize: '12px',
        zIndex: 9999,
        transition: 'opacity 0.3s',
        opacity: isVisible ? 1 : 0.3,
        cursor: 'pointer'
      }}
      onClick={copyToClipboard}
      title="点击复制TraceId"
    >
      🔍 {currentTraceId}
    </div>
  );
};

// 在App组件中使用
const App = () => {
  return (
    <div>
      {/* 其他组件 */}
      <TraceIdDisplay />
    </div>
  );
};
```

#### 4.3.3 开发者工具增强
```javascript
// 开发环境下的增强功能
if (process.env.NODE_ENV === 'development') {
  // 在window对象上暴露traceId相关方法
  window.traceUtils = {
    getCurrentTraceId: () => sessionStorage.getItem('currentTraceId'),

    generateNewTraceId: () => {
      const traceId = generateTraceId();
      sessionStorage.setItem('currentTraceId', traceId);
      console.log(`🆕 New TraceId generated: ${traceId}`);
      return traceId;
    },

    searchLogs: (traceId) => {
      console.log(`🔍 Searching logs for TraceId: ${traceId}`);
      // 可以集成到日志查询系统
    }
  };

  // 添加快捷键支持
  document.addEventListener('keydown', (e) => {
    // Ctrl+Shift+T 显示当前TraceId
    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
      const traceId = window.traceUtils.getCurrentTraceId();
      if (traceId) {
        console.log(`📋 Current TraceId: ${traceId}`);
        navigator.clipboard.writeText(traceId);
      }
    }
  });
}
```

## 5. 浏览器中查看TraceId的方式

### 5.1 开发者工具查看（推荐）

#### 5.1.1 Network面板
1. 打开浏览器开发者工具 (F12)
2. 切换到 Network 面板
3. 发起请求后，点击对应的请求
4. 在 **Request Headers** 中查看 `X-Trace-Id`
5. 在 **Response Headers** 中查看服务器返回的 `X-Trace-Id`

#### 5.1.2 Console面板
```javascript
// 方式1：查看拦截器输出
// 每个请求都会自动打印：
// 🚀 [20250823143025-001-000001-a1b2c3] POST /api/pmi/schedules
// ✅ [20250823143025-001-000001-a1b2c3] 200 /api/pmi/schedules

// 方式2：手动查询当前TraceId
console.log('当前TraceId:', sessionStorage.getItem('currentTraceId'));

// 方式3：使用工具方法（开发环境）
window.traceUtils.getCurrentTraceId();
```

### 5.2 页面UI查看（用户友好）

#### 5.2.1 右下角浮动显示
- 每次请求后，右下角会短暂显示TraceId
- 点击可复制到剪贴板
- 适合普通用户和客服人员

#### 5.2.2 错误消息中显示
```javascript
// 当请求失败时，错误消息会包含TraceId
message.error('操作失败，请联系技术支持并提供错误码：20250823143025-001-000001-a1b2c3');
```

### 5.3 快捷键查看（开发环境）
- 按 `Ctrl+Shift+T` 在控制台显示并复制当前TraceId
- 方便开发人员快速获取

### 5.4 URL参数查看（特殊场景）
```
https://example.com/page?traceId=20250823143025-001-000001-a1b2c3
```

### 5.5 浏览器扩展查看（高级）
- 开发专门的Chrome扩展
- 在地址栏旁边显示当前页面的TraceId
- 提供一键复制和日志查询功能

## 6. 管理台日志检索功能

### 6.1 功能概述

在管理台的"管理"菜单下提供TraceId日志检索功能，支持：
- 根据TraceId查询完整调用链
- 支持时间范围过滤
- 多种日志来源聚合查询
- 实时日志流查看
- 日志导出功能

### 6.2 前端界面设计

#### 6.2.1 菜单配置
```javascript
// frontend/src/components/Layout.js
{
  key: '/log-search',
  icon: <SearchOutlined />,
  label: '日志检索',
}
```

#### 6.2.2 页面组件结构
```javascript
// frontend/src/pages/LogSearch.js
const LogSearch = () => {
  const [searchForm] = Form.useForm();
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);

  return (
    <div className="log-search-container">
      {/* 搜索表单 */}
      <Card title="日志检索" className="search-form-card">
        <Form form={searchForm} layout="inline" onFinish={handleSearch}>
          <Form.Item name="traceId" label="TraceId">
            <Input placeholder="输入完整TraceId或部分关键字" style={{ width: 300 }} />
          </Form.Item>
          <Form.Item name="timeRange" label="时间范围">
            <RangePicker showTime />
          </Form.Item>
          <Form.Item name="logLevel" label="日志级别">
            <Select placeholder="选择日志级别" allowClear style={{ width: 120 }}>
              <Option value="ERROR">ERROR</Option>
              <Option value="WARN">WARN</Option>
              <Option value="INFO">INFO</Option>
              <Option value="DEBUG">DEBUG</Option>
            </Select>
          </Form.Item>
          <Form.Item name="source" label="日志来源">
            <Select placeholder="选择来源" allowClear style={{ width: 150 }}>
              <Option value="APPLICATION">应用日志</Option>
              <Option value="WEBHOOK">Webhook</Option>
              <Option value="SCHEDULER">定时任务</Option>
              <Option value="API">API调用</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              搜索
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 搜索结果 */}
      <Card title="搜索结果" className="search-results-card">
        <LogResultsTable
          data={searchResults}
          loading={loading}
          onRowClick={setSelectedLog}
        />
      </Card>

      {/* 日志详情 */}
      {selectedLog && (
        <LogDetailModal
          log={selectedLog}
          visible={!!selectedLog}
          onClose={() => setSelectedLog(null)}
        />
      )}
    </div>
  );
};
```

#### 6.2.3 完整的LogSearch页面实现
```javascript
// frontend/src/pages/LogSearch.js
import React, { useState } from 'react';
import {
  Card, Form, Input, Button, Select, DatePicker, Table, Modal,
  message, Tag, Space, Tooltip, Typography, Row, Col
} from 'antd';
import { SearchOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import api from '../services/api';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Text, Paragraph } = Typography;

const LogSearch = () => {
  const [searchForm] = Form.useForm();
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0
  });

  // 搜索日志
  const handleSearch = async (values) => {
    setLoading(true);
    try {
      const searchParams = {
        ...values,
        timeRange: values.timeRange ? [
          values.timeRange[0].format('YYYY-MM-DD HH:mm:ss'),
          values.timeRange[1].format('YYYY-MM-DD HH:mm:ss')
        ] : null,
        page: 1,
        size: pagination.pageSize
      };

      const response = await api.post('/logs/search', searchParams);
      setSearchResults(response.data.data);
      setPagination({
        ...pagination,
        current: 1,
        total: response.data.total
      });

      message.success(`找到 ${response.data.total} 条日志记录`);
    } catch (error) {
      message.error('搜索失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 查看完整调用链
  const handleViewTraceChain = async (traceId) => {
    setLoading(true);
    try {
      const response = await api.get(`/logs/trace/${traceId}`);
      setSearchResults(response.data);
      setPagination({
        ...pagination,
        current: 1,
        total: response.data.length
      });
      message.success(`找到 ${response.data.length} 条调用链记录`);
    } catch (error) {
      message.error('获取调用链失败');
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      render: (text) => <Text code style={{ fontSize: '12px' }}>{text}</Text>
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level) => {
        const colors = { ERROR: 'red', WARN: 'orange', INFO: 'blue', DEBUG: 'gray' };
        return <Tag color={colors[level] || 'default'}>{level}</Tag>;
      }
    },
    {
      title: 'TraceId',
      dataIndex: 'traceId',
      key: 'traceId',
      width: 200,
      render: (traceId) => (
        <Tooltip title="点击查看完整调用链">
          <Button
            type="link"
            size="small"
            onClick={() => handleViewTraceChain(traceId)}
            style={{ padding: 0, fontSize: '12px' }}
          >
            {traceId}
          </Button>
        </Tooltip>
      )
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 100,
      render: (source) => <Tag>{source}</Tag>
    },
    {
      title: '日志内容',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <Text style={{ fontSize: '12px' }}>{text}</Text>
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedLog(record);
              setDetailModalVisible(true);
            }}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className="log-search-container">
      {/* 搜索表单 */}
      <Card title="日志检索" style={{ marginBottom: 16 }}>
        <Form form={searchForm} layout="vertical" onFinish={handleSearch}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="traceId" label="TraceId">
                <Input placeholder="输入完整TraceId或部分关键字" allowClear />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="timeRange" label="时间范围">
                <RangePicker showTime style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="logLevel" label="日志级别">
                <Select placeholder="选择级别" allowClear>
                  <Option value="ERROR">ERROR</Option>
                  <Option value="WARN">WARN</Option>
                  <Option value="INFO">INFO</Option>
                  <Option value="DEBUG">DEBUG</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="source" label="日志来源">
                <Select placeholder="选择来源" allowClear>
                  <Option value="APPLICATION">应用日志</Option>
                  <Option value="WEBHOOK">Webhook</Option>
                  <Option value="SCHEDULER">定时任务</Option>
                  <Option value="API">API调用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />}>
              搜索
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 搜索结果 */}
      <Card title={`搜索结果 (${pagination.total} 条)`}>
        <Table
          columns={columns}
          dataSource={searchResults}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1000 }}
          size="small"
        />
      </Card>

      {/* 日志详情模态框 */}
      <Modal
        title="日志详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedLog && (
          <div>
            <Paragraph><Text strong>时间：</Text><Text code>{selectedLog.timestamp}</Text></Paragraph>
            <Paragraph><Text strong>级别：</Text><Tag>{selectedLog.level}</Tag></Paragraph>
            <Paragraph><Text strong>TraceId：</Text><Text code>{selectedLog.traceId}</Text></Paragraph>
            <Paragraph><Text strong>消息：</Text></Paragraph>
            <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
              {selectedLog.message}
            </pre>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default LogSearch;
```

### 6.3 Webhook日志处理特殊设计

#### 6.3.1 Webhook TraceId生成策略
由于Zoom等外部系统调用webhook时不会传递TraceId，需要特殊处理：

```java
private String generateWebhookTraceId(HttpServletRequest request) {
    String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    String source = extractWebhookSource(request);
    String eventId = extractWebhookEventId(request);
    String random = String.format("%04d", new Random().nextInt(10000));

    // Webhook专用格式: WH-{timestamp}-{source}-{eventId}-{random}
    return String.format("WH-%s-%s-%s-%s", timestamp, source, eventId, random);
}
```

#### 6.3.2 Zoom Webhook事件关联
```java
@PostMapping("/webhook/zoom/events")
public ResponseEntity<Map<String, Object>> handleZoomWebhook(
        @RequestBody Map<String, Object> payload,
        HttpServletRequest request) {

    String webhookTraceId = TraceContext.getTraceId();

    // 提取Zoom事件信息
    String eventType = (String) payload.get("event");
    String meetingId = extractMeetingId(payload);
    String userId = extractUserId(payload);

    // 增强TraceId包含业务信息
    String enhancedTraceId = String.format("%s-%s-%s",
            webhookTraceId, eventType, meetingId);
    TraceContext.setTraceId(enhancedTraceId);

    // 记录关联信息到MDC
    MDC.put("webhookEventType", eventType);
    MDC.put("meetingId", meetingId);
    MDC.put("userId", userId);

    log.info("处理Zoom Webhook: eventType={}, meetingId={}, userId={}",
            eventType, meetingId, userId);

    // 处理业务逻辑...

    return ResponseEntity.ok(Map.of(
        "success", true,
        "traceId", enhancedTraceId
    ));
}
```

### 6.4 后端API设计

#### 6.4.1 日志检索控制器
```java
@RestController
@RequestMapping("/api/logs")
@Slf4j
public class LogSearchController {

    @Autowired
    private LogSearchService logSearchService;

    @PostMapping("/search")
    public ResponseEntity<PageResult<LogEntry>> searchLogs(
            @RequestBody LogSearchRequest request) {

        String traceId = TraceContext.getTraceId();
        log.info("收到日志检索请求: traceId={}, request={}", traceId, request);

        try {
            PageResult<LogEntry> result = logSearchService.searchLogs(request);
            log.info("日志检索完成: traceId={}, resultCount={}", traceId, result.getTotal());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("日志检索失败: traceId={}", traceId, e);
            throw e;
        }
    }

    @GetMapping("/trace/{traceId}")
    public ResponseEntity<List<LogEntry>> getTraceChain(@PathVariable String traceId) {
        log.info("获取调用链: targetTraceId={}", traceId);

        List<LogEntry> chain = logSearchService.getCompleteTraceChain(traceId);
        return ResponseEntity.ok(chain);
    }

    @GetMapping("/export")
    public ResponseEntity<Resource> exportLogs(
            @RequestParam String traceId,
            @RequestParam(required = false) String format) {

        log.info("导出日志: traceId={}, format={}", traceId, format);

        Resource resource = logSearchService.exportLogs(traceId, format);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                       "attachment; filename=logs_" + traceId + ".txt")
                .body(resource);
    }
}
```

#### 6.4.2 日志检索服务
```java
@Service
@Slf4j
public class LogSearchService {

    @Value("${app.logs.directory:/var/logs/app}")
    private String logDirectory;

    @Value("${app.logs.max-search-days:30}")
    private int maxSearchDays;

    public PageResult<LogEntry> searchLogs(LogSearchRequest request) {
        validateSearchRequest(request);

        List<LogEntry> allEntries = new ArrayList<>();

        // 1. 搜索应用日志文件
        if (shouldSearchSource(request.getSource(), "APPLICATION")) {
            allEntries.addAll(searchApplicationLogs(request));
        }

        // 2. 搜索Webhook日志
        if (shouldSearchSource(request.getSource(), "WEBHOOK")) {
            allEntries.addAll(searchWebhookLogs(request));
        }

        // 3. 搜索定时任务日志
        if (shouldSearchSource(request.getSource(), "SCHEDULER")) {
            allEntries.addAll(searchSchedulerLogs(request));
        }

        // 4. 排序和分页
        allEntries.sort((a, b) -> b.getTimestamp().compareTo(a.getTimestamp()));

        return paginateResults(allEntries, request.getPage(), request.getSize());
    }

    private List<LogEntry> searchApplicationLogs(LogSearchRequest request) {
        List<LogEntry> entries = new ArrayList<>();

        try {
            // 获取日志文件列表
            List<File> logFiles = getLogFiles(request.getTimeRange());

            for (File logFile : logFiles) {
                entries.addAll(parseLogFile(logFile, request));
            }
        } catch (Exception e) {
            log.error("搜索应用日志失败", e);
        }

        return entries;
    }

    private LogEntry parseLogLine(String line) {
        // 解析日志行，支持多种格式
        // 格式1: 2025-08-23 14:30:25.123 [thread] LEVEL [traceId] logger - message
        // 格式2: {"timestamp":"2025-08-23T14:30:25.123Z","level":"INFO","traceId":"xxx","message":"xxx"}

        if (line.startsWith("{")) {
            return parseJsonLogLine(line);
        } else {
            return parseTextLogLine(line);
        }
    }

    public List<LogEntry> getCompleteTraceChain(String traceId) {
        LogSearchRequest request = new LogSearchRequest();
        request.setTraceId(traceId);
        request.setTimeRange(getRecentTimeRange()); // 最近24小时
        request.setPage(1);
        request.setSize(10000); // 获取所有相关日志

        PageResult<LogEntry> result = searchLogs(request);
        return result.getData();
    }
}
```

#### 6.4.3 数据传输对象
```java
@Data
public class LogSearchRequest {
    private String traceId;
    private String[] timeRange; // [startTime, endTime]
    private String logLevel;
    private String source;
    private String keyword;
    private int page = 1;
    private int size = 50;
}

@Data
public class LogEntry {
    private String timestamp;
    private String level;
    private String traceId;
    private String logger;
    private String message;
    private String thread;
    private String source;
    private Map<String, Object> context;
    private String rawLine;
}

@Data
public class PageResult<T> {
    private List<T> data;
    private long total;
    private int page;
    private int size;
    private int totalPages;
}
```

### 6.5 Webhook处理增强

#### 6.5.1 Webhook拦截器
```java
@Component
public class WebhookTraceInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler) {

        if (isWebhookRequest(request)) {
            String webhookTraceId = generateWebhookTraceId(request);
            TraceContext.setTraceId(webhookTraceId);

            // 记录Webhook接收日志
            logWebhookReceived(request, webhookTraceId);

            // 设置响应头
            response.setHeader("X-Trace-Id", webhookTraceId);
        }

        return true;
    }

    private void logWebhookReceived(HttpServletRequest request, String traceId) {
        try {
            String body = getRequestBody(request);
            String source = extractWebhookSource(request);
            String eventType = extractEventType(body);

            log.info("Webhook接收: source={}, eventType={}, contentLength={}, userAgent={}",
                    source, eventType, body.length(), request.getHeader("User-Agent"));

            // 记录详细的Webhook数据
            MDC.put("webhookSource", source);
            MDC.put("webhookEventType", eventType);
            MDC.put("webhookBody", body);

        } catch (Exception e) {
            log.error("记录Webhook日志失败", e);
        }
    }
}
```

## 7. 日志查询与分析

### 7.1 命令行日志查询脚本
```bash
#!/bin/bash
# 根据traceId查询完整调用链
TRACE_ID=$1
LOG_DIR="/var/logs/app"

echo "查询TraceId: $TRACE_ID 的完整调用链..."
echo "=================================="

# 查询所有相关日志
grep -r "$TRACE_ID" $LOG_DIR/ --include="*.log" | sort -k1,1
```

### 5.2 ELK集成
```yaml
# logstash配置
input {
  file {
    path => "/var/logs/app/*.log"
    start_position => "beginning"
  }
}

filter {
  grok {
    match => { 
      "message" => "%{TIMESTAMP_ISO8601:timestamp} \[%{DATA:thread}\] %{LOGLEVEL:level} \[%{DATA:traceId}\] %{DATA:logger} - %{GREEDYDATA:msg}" 
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "app-logs-%{+YYYY.MM.dd}"
  }
}
```

## 6. 性能考虑

### 6.1 性能优化
- TraceId生成使用高效算法，避免UUID的性能开销
- ThreadLocal使用后及时清理，防止内存泄漏
- 异步日志写入，减少对主线程的影响
- 合理设置日志级别，避免过多DEBUG日志

### 6.2 监控指标
- TraceId生成耗时
- ThreadLocal内存使用
- 日志写入性能
- 跨线程传递成功率

## 7. 部署与运维

### 7.1 部署配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    image: zoombus-app:latest
    environment:
      - NODE_ID=001
      - TRACE_ENABLED=true
    volumes:
      - ./logs:/var/logs/app
```

### 7.2 运维工具
- 提供TraceId查询接口
- 集成到现有监控系统
- 支持实时日志流查看
- 提供性能分析报告

## 8. 部署和使用指南

### 8.1 实施步骤

#### 8.1.1 第一阶段：基础TraceId机制
1. **后端实现**
   ```bash
   # 1. 添加TraceId生成器
   # 2. 实现HTTP拦截器
   # 3. 配置ThreadLocal上下文
   # 4. 更新日志配置
   ```

2. **前端集成**
   ```bash
   # 1. 添加请求/响应拦截器
   # 2. 实现TraceId显示组件
   # 3. 配置开发者工具增强
   ```

#### 8.1.2 第二阶段：管理台日志检索
1. **菜单和路由配置**
   ```javascript
   // frontend/src/components/Layout.js
   {
     key: '/log-search',
     icon: <SearchOutlined />,
     label: '日志检索',
   }

   // frontend/src/App.js
   <Route path="log-search" element={<LogSearch />} />
   ```

2. **后端API实现**
   ```bash
   # 1. 创建LogSearchController
   # 2. 实现LogSearchService
   # 3. 配置日志文件解析
   # 4. 添加导出功能
   ```

#### 8.1.3 第三阶段：Webhook处理增强
1. **Webhook拦截器**
   ```java
   @Component
   public class WebhookTraceInterceptor implements HandlerInterceptor {
       // 实现webhook专用TraceId生成
   }
   ```

2. **Zoom事件处理**
   ```java
   @PostMapping("/webhook/zoom/events")
   public ResponseEntity<Map<String, Object>> handleZoomWebhook(...) {
       // 增强TraceId包含事件信息
   }
   ```

### 8.2 配置说明

#### 8.2.1 应用配置
```yaml
# application.yml
app:
  trace:
    enabled: true
    node-id: ${NODE_ID:001}
    header-name: X-Trace-Id
  logs:
    directory: /var/logs/app
    max-search-days: 30
    retention-days: 90
```

#### 8.2.2 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>logs/application.log</file>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] %logger{36} - %msg%n</pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxFileSize>100MB</maxFileSize>
      <maxHistory>30</maxHistory>
    </rollingPolicy>
  </appender>
</configuration>
```

### 8.3 使用指南

#### 8.3.1 开发人员使用
1. **查看TraceId**
   - 浏览器开发者工具 → Network → 请求头/响应头
   - 控制台自动打印：`🚀 [traceId] POST /api/xxx`
   - 快捷键：`Ctrl+Shift+T`

2. **日志检索**
   - 管理台 → 管理 → 日志检索
   - 输入TraceId查询完整调用链
   - 支持时间范围和日志级别过滤

#### 8.3.2 运维人员使用
1. **问题排查**
   ```bash
   # 命令行查询
   grep "traceId" /var/logs/app/*.log | grep "ERROR"

   # 或使用管理台界面查询
   ```

2. **性能分析**
   - 通过TraceId追踪请求完整耗时
   - 分析调用链中的性能瓶颈
   - 监控异常请求的处理流程

#### 8.3.3 客服人员使用
1. **用户问题处理**
   - 要求用户提供页面显示的TraceId
   - 在管理台中搜索相关日志
   - 快速定位问题原因

### 8.4 监控和维护

#### 8.4.1 性能监控
- TraceId生成耗时监控
- 日志文件大小和增长速度
- 检索接口响应时间
- ThreadLocal内存使用情况

#### 8.4.2 日志维护
- 定期清理过期日志文件
- 监控磁盘空间使用
- 备份重要日志数据
- 优化日志检索性能

## 9. 总结

通过实施全局流水号机制，可以显著提升问题排查效率，实现完整的请求链路追踪。该方案具有以下优势：

### 9.1 核心优势
1. **完整性**: 覆盖整个请求生命周期，包括Webhook调用
2. **一致性**: 统一的TraceId格式和传递机制
3. **高性能**: 优化的生成算法和异步处理
4. **易用性**: 简单的API和自动化配置
5. **可扩展**: 支持集群部署和多服务架构

### 9.2 特殊处理
1. **Webhook支持**: 为外部系统调用生成专用TraceId
2. **事件关联**: 将Zoom等事件与业务处理关联
3. **管理台集成**: 提供可视化的日志检索界面
4. **多来源聚合**: 支持应用日志、Webhook日志等多种来源

### 9.3 实施建议
建议分阶段实施：
1. **第一阶段**：实现基础的TraceId生成和传递
2. **第二阶段**：完善日志格式和管理台检索功能
3. **第三阶段**：集成监控和分析系统，优化Webhook处理

### 9.4 预期效果
- **问题排查效率提升80%**：通过TraceId快速定位问题
- **用户体验改善**：客服能快速响应用户问题
- **开发效率提升**：开发人员能快速调试和优化代码
- **运维质量提升**：运维人员能更好地监控和维护系统
