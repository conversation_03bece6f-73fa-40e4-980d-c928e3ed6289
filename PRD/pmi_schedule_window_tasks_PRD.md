# PMI窗口精准定时任务管理系统设计说明书

## 1. 项目背景与目标

### 1.1 现状分析
当前系统采用轮询机制管理PMI窗口：
- **执行频率**：每分钟执行一次 `activatePmiWindows()` 和 `completePmiWindows()`
- **主要问题**：
  1. 资源浪费：大部分时间都是空跑，没有符合条件的记录处理
  2. 时间精度低：最大误差可达1分钟，无法精确控制
  3. 系统负载：持续的数据库查询和CPU占用

### 1.2 设计目标
1. **消除轮询开销**：变被动轮询为主动调度
2. **精确时间控制**：精确到秒级的窗口开启/关闭
3. **可视化管理**：提供管理台监控和人工干预能力
4. **系统稳定性**：保证高可用性和容错能力

## 2. 系统架构设计

### 2.1 核心组件架构

```mermaid
graph TB
    A[窗口管理事件] --> B[PmiWindowTaskSchedulingService]
    B --> C[DynamicTaskManager]
    C --> D[Spring TaskScheduler]
    D --> E[PmiWindowTaskExecutor]
    E --> F[PMI开启/关闭逻辑]
    
    G[管理台] --> H[PmiTaskManagementController]
    H --> C
    
    I[PmiTaskStatusManager] --> J[数据库]
    C --> I
    E --> I
    
    K[监控告警] --> I
```

### 2.2 核心服务类设计

#### 2.2.1 DynamicTaskManager（动态任务管理器）
```java
@Service
public class DynamicTaskManager {
    // 调度PMI窗口开启任务
    public String schedulePmiWindowOpenTask(Long windowId, LocalDateTime executeTime);
    
    // 调度PMI窗口关闭任务  
    public String schedulePmiWindowCloseTask(Long windowId, LocalDateTime executeTime);
    
    // 取消任务
    public boolean cancelTask(String taskKey);
    
    // 重新调度任务
    public String rescheduleTask(String oldTaskKey, LocalDateTime newExecuteTime);
    
    // 获取即将执行的任务列表
    public List<ScheduledTaskInfo> getUpcomingTasks(int hours);
}
```

#### 2.2.2 PmiWindowTaskSchedulingService（PMI窗口任务调度服务）
- 监听PMI窗口创建/修改/删除事件
- 自动创建对应的开启/关闭任务
- 处理批量PMI窗口的任务调度
- 提供任务重新调度能力

#### 2.2.3 PmiWindowTaskExecutor（PMI任务执行器）
- 实际执行PMI窗口开启/关闭逻辑
- 集成现有的PMI激活/关闭服务
- 处理执行异常和重试机制
- 记录执行结果和状态更新

## 3. 数据库设计

### 3.1 新增表：t_pmi_schedule_window_tasks

```sql
CREATE TABLE t_pmi_schedule_window_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    pmi_window_id BIGINT NOT NULL COMMENT '关联的PMI窗口ID',
    task_type ENUM('PMI_WINDOW_OPEN', 'PMI_WINDOW_CLOSE') NOT NULL COMMENT '任务类型',
    scheduled_time DATETIME NOT NULL COMMENT '计划执行时间',
    actual_execution_time DATETIME COMMENT '实际执行时间',
    status ENUM('SCHEDULED', 'EXECUTING', 'COMPLETED', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'SCHEDULED',
    task_key VARCHAR(255) UNIQUE NOT NULL COMMENT '任务唯一标识',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    error_message TEXT COMMENT '错误信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_pmi_window_id (pmi_window_id),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status (status),
    INDEX idx_task_key (task_key),
    
    FOREIGN KEY (pmi_window_id) REFERENCES t_pmi_schedule_windows(id) ON DELETE CASCADE
) COMMENT='PMI窗口定时任务表';
```

### 3.2 扩展现有表：t_pmi_schedule_windows

```sql
ALTER TABLE t_pmi_schedule_windows 
ADD COLUMN open_task_id BIGINT COMMENT '开启任务ID',
ADD COLUMN close_task_id BIGINT COMMENT '关闭任务ID',
ADD INDEX idx_open_task_id (open_task_id),
ADD INDEX idx_close_task_id (close_task_id);
```

## 4. 核心功能实现

### 4.1 事件驱动机制

```java
// PMI窗口创建事件
@EventListener
public void handlePmiWindowCreated(PmiWindowCreatedEvent event) {
    PmiScheduleWindow window = event.getWindow();
    
    // 创建开启任务
    String openTaskKey = dynamicTaskManager.schedulePmiWindowOpenTask(
        window.getId(), window.getStartDateTime());
    
    // 创建关闭任务
    String closeTaskKey = dynamicTaskManager.schedulePmiWindowCloseTask(
        window.getId(), window.getEndDateTime());
    
    // 更新窗口记录
    window.setOpenTaskId(openTaskKey);
    window.setCloseTaskId(closeTaskKey);
    pmiWindowRepository.save(window);
}
```

### 4.2 任务调度核心逻辑

```java
@Service
public class DynamicTaskManagerImpl implements DynamicTaskManager {
    
    private final TaskScheduler taskScheduler;
    private final ConcurrentHashMap<String, ScheduledFuture<?>> scheduledTasks;
    
    @Override
    public String schedulePmiWindowOpenTask(Long windowId, LocalDateTime executeTime) {
        String taskKey = generateTaskKey("PMI_OPEN", windowId);
        
        // 创建任务记录
        PmiScheduleWindowTask task = createTaskRecord(windowId, TaskType.PMI_WINDOW_OPEN, executeTime, taskKey);
        
        // 调度任务
        ScheduledFuture<?> future = taskScheduler.schedule(
            () -> pmiWindowTaskExecutor.executeOpenTask(windowId),
            Date.from(executeTime.atZone(ZoneId.systemDefault()).toInstant())
        );
        
        scheduledTasks.put(taskKey, future);
        return taskKey;
    }
}
```

### 4.3 容错和恢复机制

```java
@PostConstruct
public void initializePmiScheduledTasks() {
    // 系统启动时恢复未完成的PMI任务
    List<PmiScheduleWindowTask> pendingTasks = pmiTaskRepository.findByStatus(TaskStatus.SCHEDULED);
    
    for (PmiScheduleWindowTask task : pendingTasks) {
        LocalDateTime now = LocalDateTime.now();
        
        if (task.getScheduledTime().isBefore(now)) {
            // 时间已过期，立即执行或标记为过期
            handleExpiredPmiTask(task);
        } else {
            // 重新调度任务
            reschedulePmiTask(task);
        }
    }
}
```

## 5. 实体类设计

### 5.1 PmiScheduleWindowTask 实体类

```java
@Entity
@Table(name = "t_pmi_schedule_window_tasks")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmiScheduleWindowTask {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "pmi_window_id", nullable = false)
    private Long pmiWindowId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "task_type", nullable = false)
    private TaskType taskType;
    
    @Column(name = "scheduled_time", nullable = false)
    private LocalDateTime scheduledTime;
    
    @Column(name = "actual_execution_time")
    private LocalDateTime actualExecutionTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TaskStatus status = TaskStatus.SCHEDULED;
    
    @Column(name = "task_key", unique = true, nullable = false)
    private String taskKey;
    
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 任务类型枚举
     */
    public enum TaskType {
        PMI_WINDOW_OPEN("pmi_window_open", "PMI窗口开启"),
        PMI_WINDOW_CLOSE("pmi_window_close", "PMI窗口关闭");
        
        private final String value;
        private final String description;
        
        TaskType(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() { return value; }
        public String getDescription() { return description; }
    }
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        SCHEDULED("scheduled", "已调度"),
        EXECUTING("executing", "执行中"),
        COMPLETED("completed", "已完成"),
        FAILED("failed", "执行失败"),
        CANCELLED("cancelled", "已取消");
        
        private final String value;
        private final String description;
        
        TaskStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() { return value; }
        public String getDescription() { return description; }
    }
}
```

## 6. Repository 接口设计

### 6.1 PmiScheduleWindowTaskRepository

```java
@Repository
public interface PmiScheduleWindowTaskRepository extends JpaRepository<PmiScheduleWindowTask, Long> {

    /**
     * 根据PMI窗口ID查找任务
     */
    List<PmiScheduleWindowTask> findByPmiWindowId(Long pmiWindowId);

    /**
     * 根据任务状态查找任务
     */
    List<PmiScheduleWindowTask> findByStatus(PmiScheduleWindowTask.TaskStatus status);

    /**
     * 根据任务类型查找任务
     */
    List<PmiScheduleWindowTask> findByTaskType(PmiScheduleWindowTask.TaskType taskType);

    /**
     * 根据任务键查找任务
     */
    Optional<PmiScheduleWindowTask> findByTaskKey(String taskKey);

    /**
     * 查找指定时间范围内的任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.scheduledTime BETWEEN :startTime AND :endTime ORDER BY t.scheduledTime")
    List<PmiScheduleWindowTask> findTasksInTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 查找即将执行的任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.status = :status AND t.scheduledTime <= :maxTime ORDER BY t.scheduledTime")
    List<PmiScheduleWindowTask> findUpcomingTasks(
            @Param("status") PmiScheduleWindowTask.TaskStatus status,
            @Param("maxTime") LocalDateTime maxTime);

    /**
     * 查找过期的任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.status = :status AND t.scheduledTime < :currentTime")
    List<PmiScheduleWindowTask> findExpiredTasks(
            @Param("status") PmiScheduleWindowTask.TaskStatus status,
            @Param("currentTime") LocalDateTime currentTime);

    /**
     * 统计任务数量按状态
     */
    @Query("SELECT t.status, COUNT(t) FROM PmiScheduleWindowTask t GROUP BY t.status")
    List<Object[]> countTasksByStatus();

    /**
     * 统计任务数量按类型
     */
    @Query("SELECT t.taskType, COUNT(t) FROM PmiScheduleWindowTask t GROUP BY t.taskType")
    List<Object[]> countTasksByType();

    /**
     * 删除PMI窗口的所有任务
     */
    void deleteByPmiWindowId(Long pmiWindowId);

    /**
     * 查找需要重试的失败任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.status = :status AND t.retryCount < :maxRetryCount")
    List<PmiScheduleWindowTask> findRetryableTasks(
            @Param("status") PmiScheduleWindowTask.TaskStatus status,
            @Param("maxRetryCount") Integer maxRetryCount);
}
```

## 7. 管理台集成改造设计

### 7.1 现有管理台分析

当前管理台已有以下相关功能：
1. **PMI管理页面** (`frontend/src/pages/PmiManagement.js`)
2. **PMI计划管理页面** (`frontend/src/pages/PmiScheduleManagement.js`)
3. **定时任务监控页面** (`frontend/src/pages/ScheduledTaskManagement.js`)
4. **系统监控页面** (`frontend/src/pages/Dashboard/RealTimeMonitoring.js`)

### 7.2 管理台改造方案

#### 7.2.1 PMI计划管理页面增强

**现有功能保持不变：**
- PMI计划的创建、编辑、删除
- 窗口列表展示和管理
- 计划状态管理

**新增功能模块：**

1. **任务调度状态展示**
```javascript
// 在窗口列表中新增任务状态列
{
  title: '调度状态',
  key: 'taskStatus',
  render: (_, record) => (
    <Space direction="vertical" size="small">
      <Tag color={getTaskStatusColor(record.openTaskStatus)}>
        开启: {record.openTaskStatus || '未调度'}
      </Tag>
      <Tag color={getTaskStatusColor(record.closeTaskStatus)}>
        关闭: {record.closeTaskStatus || '未调度'}
      </Tag>
    </Space>
  )
}
```

2. **任务操作按钮**
```javascript
// 在操作列中新增任务管理按钮
<Dropdown overlay={
  <Menu>
    <Menu.Item key="viewTasks" icon={<ClockCircleOutlined />}>
      查看任务
    </Menu.Item>
    <Menu.Item key="reschedule" icon={<EditOutlined />}>
      重新调度
    </Menu.Item>
    <Menu.Item key="cancelTasks" icon={<StopOutlined />} danger>
      取消任务
    </Menu.Item>
  </Menu>
}>
  <Button size="small">
    任务管理 <DownOutlined />
  </Button>
</Dropdown>
```

#### 7.2.2 新增PMI任务监控页面

**页面路径：** `/pmi-task-monitor`

**页面结构：**
```javascript
// frontend/src/pages/PmiTaskMonitor.js
const PmiTaskMonitor = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Tabs defaultActiveKey="upcoming">
        <TabPane tab="即将执行" key="upcoming">
          <UpcomingTasksTable />
        </TabPane>
        <TabPane tab="执行历史" key="history">
          <TaskHistoryTable />
        </TabPane>
        <TabPane tab="统计分析" key="statistics">
          <TaskStatistics />
        </TabPane>
        <TabPane tab="系统状态" key="system">
          <SystemStatus />
        </TabPane>
      </Tabs>
    </div>
  );
};
```

#### 7.2.3 集成到现有定时任务监控

**扩展现有 ScheduledTaskManagement.js：**

1. **新增PMI任务类别**
```javascript
// 在任务类型过滤中新增PMI相关任务
const taskTypeOptions = [
  { label: '全部', value: 'all' },
  { label: 'PMI窗口管理', value: 'PMI_WINDOW' },  // 新增
  { label: 'PMI状态管理', value: 'PMI_STATUS' },
  { label: 'Token刷新', value: 'TOKEN_REFRESH' },
  // ... 其他现有类型
];
```

2. **PMI任务详情展示**
```javascript
// 在任务详情模态框中新增PMI特定信息
const PmiTaskDetail = ({ task }) => (
  <Descriptions column={2}>
    <Descriptions.Item label="PMI窗口ID">{task.pmiWindowId}</Descriptions.Item>
    <Descriptions.Item label="任务类型">{task.taskType}</Descriptions.Item>
    <Descriptions.Item label="计划执行时间">{task.scheduledTime}</Descriptions.Item>
    <Descriptions.Item label="实际执行时间">{task.actualExecutionTime}</Descriptions.Item>
    <Descriptions.Item label="重试次数">{task.retryCount}</Descriptions.Item>
    <Descriptions.Item label="错误信息" span={2}>{task.errorMessage}</Descriptions.Item>
  </Descriptions>
);
```

### 7.3 API接口设计

#### 7.3.1 PMI任务管理接口

```java
@RestController
@RequestMapping("/api/pmi-scheduled-tasks")
public class PmiTaskManagementController {

    @GetMapping
    public PageResult<PmiScheduledTaskInfo> getPmiTasks(
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false) String status,
        @RequestParam(required = false) String taskType);

    @PostMapping("/{id}/execute")
    public ApiResponse executePmiTask(@PathVariable Long id);

    @DeleteMapping("/{id}")
    public ApiResponse cancelPmiTask(@PathVariable Long id);

    @PutMapping("/{id}/reschedule")
    public ApiResponse reschedulePmiTask(@PathVariable Long id, @RequestBody RescheduleRequest request);

    @GetMapping("/statistics")
    public PmiTaskStatistics getPmiTaskStatistics();

    @GetMapping("/upcoming")
    public List<PmiScheduledTaskInfo> getUpcomingPmiTasks(@RequestParam(defaultValue = "24") int hours);

    @GetMapping("/history")
    public PageResult<PmiScheduledTaskInfo> getPmiTaskHistory(
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "10") int size);

    @GetMapping("/window/{windowId}")
    public List<PmiScheduledTaskInfo> getWindowTasks(@PathVariable Long windowId);

    @PostMapping("/batch/cancel")
    public ApiResponse batchCancelTasks(@RequestBody List<Long> taskIds);

    @PostMapping("/batch/reschedule")
    public ApiResponse batchRescheduleTasks(@RequestBody BatchRescheduleRequest request);
}
```

#### 7.3.2 扩展现有PMI计划接口

```java
// 在 PmiScheduleController 中新增任务相关接口
@GetMapping("/{id}/tasks")
public ApiResponse getScheduleTasks(@PathVariable Long id);

@PostMapping("/{id}/reschedule-tasks")
public ApiResponse rescheduleScheduleTasks(@PathVariable Long id);

@PostMapping("/{id}/cancel-tasks")
public ApiResponse cancelScheduleTasks(@PathVariable Long id);
```

### 7.4 前端服务层扩展

#### 7.4.1 新增API服务

```javascript
// frontend/src/services/api.js 中新增
export const pmiTaskApi = {
  // 获取PMI任务列表
  getPmiTasks: (params) => api.get('/pmi-scheduled-tasks', { params }),

  // 手动执行PMI任务
  executePmiTask: (id) => api.post(`/pmi-scheduled-tasks/${id}/execute`),

  // 取消PMI任务
  cancelPmiTask: (id) => api.delete(`/pmi-scheduled-tasks/${id}`),

  // 重新调度PMI任务
  reschedulePmiTask: (id, data) => api.put(`/pmi-scheduled-tasks/${id}/reschedule`, data),

  // 获取PMI任务统计
  getPmiTaskStatistics: () => api.get('/pmi-scheduled-tasks/statistics'),

  // 获取即将执行的PMI任务
  getUpcomingPmiTasks: (hours = 24) => api.get('/pmi-scheduled-tasks/upcoming', { params: { hours } }),

  // 获取PMI任务历史
  getPmiTaskHistory: (params) => api.get('/pmi-scheduled-tasks/history', { params }),

  // 获取窗口的任务
  getWindowTasks: (windowId) => api.get(`/pmi-scheduled-tasks/window/${windowId}`),

  // 批量取消任务
  batchCancelTasks: (taskIds) => api.post('/pmi-scheduled-tasks/batch/cancel', taskIds),

  // 批量重新调度任务
  batchRescheduleTasks: (data) => api.post('/pmi-scheduled-tasks/batch/reschedule', data)
};

// 扩展现有pmiScheduleApi
export const pmiScheduleApi = {
  // ... 现有方法保持不变

  // 新增任务相关方法
  getScheduleTasks: (id) => api.get(`/pmi-schedules/${id}/tasks`),
  rescheduleScheduleTasks: (id) => api.post(`/pmi-schedules/${id}/reschedule-tasks`),
  cancelScheduleTasks: (id) => api.post(`/pmi-schedules/${id}/cancel-tasks`)
};
```

### 7.5 数据传输对象设计

#### 7.5.1 PMI任务信息DTO

```java
@Data
public class PmiScheduledTaskInfo {
    private Long id;
    private Long pmiWindowId;
    private String taskType;
    private LocalDateTime scheduledTime;
    private LocalDateTime actualExecutionTime;
    private String status;
    private String taskKey;
    private Integer retryCount;
    private String errorMessage;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // 关联信息
    private String pmiNumber;
    private String pmiPassword;
    private String userName;
    private String scheduleName;

    // 计算字段
    private Long delayMinutes; // 延迟分钟数
    private String statusDescription;
    private Boolean canCancel;
    private Boolean canReschedule;
}
```

#### 7.5.2 任务统计DTO

```java
@Data
public class PmiTaskStatistics {
    private Long totalTasks;
    private Long scheduledTasks;
    private Long executingTasks;
    private Long completedTasks;
    private Long failedTasks;
    private Long cancelledTasks;

    private Double successRate;
    private Double averageExecutionTime;

    // 24小时内统计
    private Long tasksLast24Hours;
    private Long successfulTasksLast24Hours;
    private Long failedTasksLast24Hours;

    // 按类型统计
    private Map<String, Long> tasksByType;

    // 按小时统计（用于图表）
    private List<HourlyTaskStats> hourlyStats;
}

@Data
public class HourlyTaskStats {
    private Integer hour;
    private Long totalTasks;
    private Long successfulTasks;
    private Long failedTasks;
}
```

### 7.6 实时监控集成

#### 7.6.1 WebSocket推送PMI任务状态

```java
// 在现有 MonitoringScheduler 中新增PMI任务监控
@Scheduled(fixedRate = 30000) // 每30秒推送一次
public void pushPmiTaskStatus() {
    try {
        PmiTaskMonitoringData data = pmiTaskMonitoringService.getCurrentStatus();
        webSocketService.pushPmiTaskStatus(data);
    } catch (Exception e) {
        log.error("推送PMI任务状态失败", e);
    }
}
```

#### 7.6.2 前端实时数据订阅

```javascript
// 在 RealTimeMonitoring.js 中新增PMI任务状态订阅
stompClient.current.subscribe('/topic/monitoring/pmi-tasks', (message) => {
  const data = JSON.parse(message.body);
  setPmiTaskStatus(data.data);
});
```

### 7.7 导航菜单集成

#### 7.7.1 新增菜单项

```javascript
// 在管理台主菜单中新增PMI任务监控入口
const menuItems = [
  // ... 现有菜单项
  {
    key: 'pmi-management',
    icon: <VideoCameraOutlined />,
    label: 'PMI管理',
    children: [
      {
        key: 'pmi-records',
        label: 'PMI记录',
        path: '/pmi-management'
      },
      {
        key: 'pmi-schedules',
        label: 'PMI计划',
        path: '/pmi-schedule-management'
      },
      {
        key: 'pmi-tasks',  // 新增
        label: 'PMI任务监控',
        path: '/pmi-task-monitor'
      }
    ]
  },
  // ... 其他菜单项
];
```

### 7.8 权限控制

#### 7.8.1 后端权限配置

```java
// 在 SecurityConfig 中配置PMI任务管理权限
@Override
protected void configure(HttpSecurity http) throws Exception {
    http.authorizeRequests()
        // ... 现有配置
        .antMatchers("/api/pmi-scheduled-tasks/**").hasAnyRole("ADMIN", "SUPER_ADMIN")
        .antMatchers(HttpMethod.POST, "/api/pmi-scheduled-tasks/*/execute").hasRole("SUPER_ADMIN")
        .antMatchers(HttpMethod.DELETE, "/api/pmi-scheduled-tasks/*").hasRole("SUPER_ADMIN")
        // ... 其他配置
}
```

#### 7.8.2 前端权限控制

```javascript
// 在组件中根据用户角色显示不同操作
const canExecuteTask = hasRole(['SUPER_ADMIN']);
const canCancelTask = hasRole(['SUPER_ADMIN']);
const canRescheduleTask = hasRole(['ADMIN', 'SUPER_ADMIN']);

return (
  <Space>
    {canExecuteTask && (
      <Button type="primary" onClick={() => executeTask(record.id)}>
        立即执行
      </Button>
    )}
    {canRescheduleTask && (
      <Button onClick={() => showRescheduleModal(record)}>
        重新调度
      </Button>
    )}
    {canCancelTask && (
      <Button danger onClick={() => cancelTask(record.id)}>
        取消任务
      </Button>
    )}
  </Space>
);
```

### 7.9 移动端适配

#### 7.9.1 响应式设计

```javascript
// PMI任务监控页面的移动端适配
const PmiTaskMonitor = () => {
  const [isMobileView, setIsMobileView] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const mobileColumns = [
    {
      title: '任务信息',
      key: 'taskInfo',
      render: (_, record) => (
        <div>
          <div><strong>{record.taskType}</strong></div>
          <div><small>{record.scheduledTime}</small></div>
          <div><Tag color={getStatusColor(record.status)}>{record.status}</Tag></div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Dropdown overlay={getActionMenu(record)}>
          <Button size="small">
            操作 <DownOutlined />
          </Button>
        </Dropdown>
      )
    }
  ];

  return (
    <Table
      columns={isMobileView ? mobileColumns : desktopColumns}
      dataSource={tasks}
      scroll={{ x: isMobileView ? undefined : 'max-content' }}
      size={isMobileView ? 'small' : 'middle'}
    />
  );
};
```

### 7.10 管理台改造实施计划

#### 7.10.1 第一阶段：基础集成（1-2天）

**后端改造：**
1. 创建 `PmiTaskManagementController` 控制器
2. 实现基础的CRUD API接口
3. 创建相关DTO类
4. 配置权限控制

**前端改造：**
1. 在 `api.js` 中新增 `pmiTaskApi` 服务
2. 在PMI计划管理页面中新增任务状态展示
3. 创建基础的PMI任务监控页面

#### 7.10.2 第二阶段：功能完善（2-3天）

**高级功能：**
1. 实现批量操作功能
2. 添加任务重新调度功能
3. 完善错误处理和用户反馈
4. 实现实时状态更新

**用户体验优化：**
1. 添加加载状态和进度指示
2. 实现移动端响应式设计
3. 添加操作确认和提示
4. 优化表格和列表展示

#### 7.10.3 第三阶段：监控集成（1-2天）

**实时监控：**
1. 集成WebSocket推送PMI任务状态
2. 在系统监控页面中添加PMI任务指标
3. 实现任务执行告警
4. 添加性能监控图表

**统计分析：**
1. 实现任务执行统计
2. 添加成功率和性能分析
3. 创建任务执行趋势图表
4. 实现数据导出功能

### 7.11 用户界面设计规范

#### 7.11.1 颜色和状态标识

```javascript
// 任务状态颜色映射
const getTaskStatusColor = (status) => {
  const colorMap = {
    'SCHEDULED': 'blue',      // 已调度 - 蓝色
    'EXECUTING': 'orange',    // 执行中 - 橙色
    'COMPLETED': 'green',     // 已完成 - 绿色
    'FAILED': 'red',          // 失败 - 红色
    'CANCELLED': 'gray'       // 已取消 - 灰色
  };
  return colorMap[status] || 'default';
};

// 任务类型图标映射
const getTaskTypeIcon = (taskType) => {
  const iconMap = {
    'PMI_WINDOW_OPEN': <PlayCircleOutlined style={{ color: '#52c41a' }} />,
    'PMI_WINDOW_CLOSE': <PauseCircleOutlined style={{ color: '#fa8c16' }} />
  };
  return iconMap[taskType] || <ClockCircleOutlined />;
};
```

#### 7.11.2 交互设计原则

1. **操作确认**：所有危险操作（取消、删除）需要二次确认
2. **状态反馈**：操作后立即显示结果，成功/失败都有明确提示
3. **加载状态**：长时间操作显示加载指示器
4. **错误处理**：友好的错误信息展示，提供解决建议

#### 7.11.3 表格设计规范

```javascript
// PMI任务列表表格配置
const taskTableColumns = [
  {
    title: '任务ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    render: (id) => <Text code>{id}</Text>
  },
  {
    title: '任务类型',
    dataIndex: 'taskType',
    key: 'taskType',
    width: 120,
    render: (type) => (
      <Space>
        {getTaskTypeIcon(type)}
        <span>{type === 'PMI_WINDOW_OPEN' ? '开启' : '关闭'}</span>
      </Space>
    )
  },
  {
    title: 'PMI信息',
    key: 'pmiInfo',
    width: 200,
    render: (_, record) => (
      <div>
        <div><strong>{record.pmiNumber}</strong></div>
        <div><small>{record.userName}</small></div>
      </div>
    )
  },
  {
    title: '计划时间',
    dataIndex: 'scheduledTime',
    key: 'scheduledTime',
    width: 160,
    render: (time) => (
      <div>
        <div>{moment(time).format('MM-DD HH:mm')}</div>
        <div><small>{moment(time).fromNow()}</small></div>
      </div>
    )
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    render: (status) => (
      <Tag color={getTaskStatusColor(status)}>
        {status}
      </Tag>
    )
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (_, record) => <TaskActionButtons record={record} />
  }
];
```

## 8. 技术实现细节

### 8.1 并发控制和线程安全
- 使用 `ConcurrentHashMap` 存储PMI任务映射关系
- PMI任务调度和取消操作加锁，防止竞态条件
- 数据库层面使用乐观锁防止并发修改

### 8.2 性能优化策略
- **批量PMI任务调度**：一次性处理多个PMI窗口的任务创建
- **PMI任务缓存**：缓存即将执行的PMI任务信息，减少数据库查询
- **异步处理**：PMI任务调度操作异步执行，不阻塞主流程

### 8.3 监控和可观测性
- 集成现有监控系统，添加PMI任务调度相关指标
- 关键指标：PMI任务执行时间、成功率、失败率
- 调度器健康检查：定期检查TaskScheduler状态

## 8. 技术实现细节

### 8.1 并发控制和线程安全
- 使用 `ConcurrentHashMap` 存储PMI任务映射关系
- PMI任务调度和取消操作加锁，防止竞态条件
- 数据库层面使用乐观锁防止并发修改

### 8.2 性能优化策略
- **批量PMI任务调度**：一次性处理多个PMI窗口的任务创建
- **PMI任务缓存**：缓存即将执行的PMI任务信息，减少数据库查询
- **异步处理**：PMI任务调度操作异步执行，不阻塞主流程

### 8.3 监控和可观测性
- 集成现有监控系统，添加PMI任务调度相关指标
- 关键指标：PMI任务执行时间、成功率、失败率
- 调度器健康检查：定期检查TaskScheduler状态

## 9. 实施计划

### 9.1 分阶段实施

**第一阶段（基础设施）**：
- 创建 `t_pmi_schedule_window_tasks` 表和 `PmiScheduleWindowTask` 实体类
- 实现DynamicTaskManager核心功能
- 基本的PMI任务调度和取消功能

**第二阶段（业务集成）**：
- 实现PmiWindowTaskSchedulingService
- 集成现有的PMI开启/关闭逻辑
- 添加PMI窗口事件监听机制

**第三阶段（管理功能）**：
- 开发PMI任务管理台界面
- 实现人工干预功能
- 添加PMI任务监控和告警

**第四阶段（优化和切换）**：
- 性能优化和压力测试
- 灰度发布和A/B测试
- 完全切换到新的PMI任务机制

### 9.2 向后兼容策略
- 保留现有PMI轮询机制作为备用方案
- 通过配置开关控制使用哪种PMI调度方式
- 渐进式迁移：先在测试环境验证，再逐步切换生产环境

## 10. 风险评估与缓解

### 10.1 主要风险

**高风险：**
- **PMI任务丢失**：系统重启或异常时PMI任务可能丢失
- **时间精度**：系统时钟不准确影响PMI任务执行时间
- **内存泄漏**：长期运行可能导致ScheduledFuture对象积累

**中风险：**
- **性能影响**：大量PMI任务调度可能影响系统性能
- **数据一致性**：PMI任务状态与窗口状态不一致
- **并发冲突**：多实例部署时的PMI任务重复执行

### 10.2 缓解措施
- 完善的测试覆盖，包括单元测试、集成测试、压力测试
- 详细的PMI任务监控和告警机制
- 保留原有PMI轮询机制作为备用方案
- 分阶段实施，逐步验证和优化

## 11. 预期效果

### 11.1 性能提升
- **CPU使用率降低**：消除每分钟的PMI轮询开销
- **时间精度提升**：精确到秒的PMI窗口时间控制
- **数据库负载减少**：减少无效的PMI查询频率

### 11.2 运维改善
- **可视化监控**：便于运维人员掌握PMI任务系统状态
- **人工干预能力**：提高PMI故障处理效率
- **详细执行日志**：便于PMI问题排查

### 11.3 业务价值
- **用户体验提升**：更精确的PMI窗口控制
- **系统扩展性**：支持更复杂的PMI调度需求
- **运营效率**：减少PMI人工运维工作量

## 12. 扩展性考虑

### 12.1 为权益链接管理预留设计
本设计采用了通用的任务调度框架，可以轻松扩展到其他窗口管理场景：

- **表命名规范**：`t_pmi_schedule_window_tasks` 明确标识PMI相关
- **类命名规范**：`PmiScheduleWindowTask` 等类名具有明确的业务标识
- **接口设计**：DynamicTaskManager 可以支持不同类型的任务调度

### 12.2 未来扩展方向
1. **权益链接窗口管理**：创建 `t_join_account_usage_window_tasks` 表
2. **其他业务窗口**：可以按照相同模式扩展
3. **分布式任务调度**：考虑引入XXL-JOB等分布式调度框架
4. **智能调度**：基于历史数据优化任务执行时间

## 13. 总结

这个PMI窗口精准定时任务管理系统设计方案通过引入事件驱动的精准调度机制，彻底解决了当前轮询方式的性能和精度问题。主要优势包括：

1. **技术先进性**：采用Spring TaskScheduler + 事件驱动架构
2. **业务适配性**：完美契合PMI窗口管理的业务需求
3. **运维友好性**：提供完善的监控和人工干预能力
4. **扩展性良好**：为后续权益链接等业务预留了扩展空间
5. **风险可控性**：分阶段实施，保留备用方案

建议按照分阶段实施计划逐步推进，确保系统稳定性的同时实现功能升级。通过这个设计，PMI窗口管理将从被动轮询转变为主动精准调度，大幅提升系统性能和用户体验。
