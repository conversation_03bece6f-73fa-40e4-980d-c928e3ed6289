# 生产环境数据迁移指南

## 概述

本文档说明如何将四张核心表的数据从本地开发环境迁移到生产环境 `<EMAIL>`。

## 迁移的表

1. **t_users** - 用户表
2. **t_pmi_records** - PMI记录表  
3. **t_pmi_schedules** - PMI计划表
4. **t_pmi_schedule_windows** - PMI窗口表

## 环境信息

### 本地环境
- 数据库: `zoombusV` (大写V)
- 用户名: `root`
- 密码: `nvshen2018`

### 生产环境
- 服务器: `<EMAIL>`
- 数据库: `zoombusv` (小写v)
- 用户名: `root`
- 密码: `nvshen2018`

## 当前数据状态

### 本地环境数据量
```
t_users:                625 条记录
t_pmi_records:          626 条记录
t_pmi_schedules:        934 条记录
t_pmi_schedule_windows: 296 条记录
```

### 生产环境数据量（迁移前）
```
t_users:                625 条记录
t_pmi_records:          626 条记录
t_pmi_schedules:        21 条记录
t_pmi_schedule_windows: 21 条记录
```

## 迁移脚本

### 1. 完整版迁移脚本 (推荐)

**文件**: `migrate_to_production.sh`

**特点**:
- ✅ 完整的错误处理和日志记录
- ✅ 自动备份生产环境数据
- ✅ 详细的验证和确认步骤
- ✅ 自动清理临时文件
- ✅ 彩色输出和进度显示

**使用方法**:
```bash
./migrate_to_production.sh
```

### 2. 快速迁移脚本

**文件**: `quick_migrate_to_production.sh`

**特点**:
- ⚡ 快速执行，适合紧急情况
- ✅ 基本的备份和验证
- ✅ 简洁的输出信息
- ⚠️ 较少的错误处理

**使用方法**:
```bash
./quick_migrate_to_production.sh
```

## 迁移流程

### 完整版迁移流程

1. **连接检查**
   - 检查SSH连接到生产服务器
   - 检查本地数据库连接
   - 检查生产环境数据库连接

2. **数据统计**
   - 统计本地各表数据量
   - 显示迁移前的数据状态

3. **生产环境备份**
   - 在生产服务器创建备份目录
   - 备份所有目标表的数据
   - 备份位置: `/tmp/prod_backup_YYYYMMDD_HHMMSS/`

4. **清空生产表**
   - 按依赖关系倒序清空表数据
   - 临时禁用外键检查确保清空成功

5. **数据导出**
   - 从本地数据库导出表数据
   - 使用完整插入语句确保数据完整性

6. **字符集转换** ⭐ **新增功能**
   - 自动检测并转换字符集声明
   - 将 `utf8mb4_0900_ai_ci` 转换为 `utf8mb4_general_ci`
   - 兼容生产环境MySQL 5.7.18版本
   - 支持多种字符集格式转换

7. **数据传输**
   - 将转换后的数据文件传输到生产服务器
   - 使用SCP确保传输安全

8. **数据导入**
   - 按依赖关系顺序导入数据
   - 实时显示导入进度

9. **结果验证**
   - 对比本地和生产环境的记录数
   - 确保数据迁移完整性

10. **清理工作**
    - 清理本地临时文件
    - 清理生产服务器临时文件

## 安全措施

### 数据备份
- ✅ 迁移前自动备份生产环境数据
- ✅ 备份文件保存在生产服务器的 `/tmp/` 目录
- ✅ 备份文件名包含时间戳，便于识别

### 确认机制
- ⚠️ 执行前需要手动确认操作
- ⚠️ 明确提示将清空生产环境数据
- ⚠️ 只有输入 'y' 或 'Y' 才会继续执行

### 错误处理
- ✅ 任何步骤失败都会立即停止执行
- ✅ 自动清理已创建的临时文件
- ✅ 详细的错误信息输出

## 执行前检查清单

- [ ] 确认SSH密钥已配置，可以无密码连接到 `<EMAIL>`
- [ ] 确认本地数据库连接正常
- [ ] 确认生产环境数据库连接正常
- [ ] 确认本地数据是最新的完整数据
- [ ] 确认有足够的磁盘空间进行备份和传输
- [ ] 确认生产环境服务已停止（避免数据冲突）

## 执行后验证

### 数据量验证
```bash
# 本地环境
mysql -uroot -pnvshen2018 zoombusV -e "
SELECT 't_users' as table_name, COUNT(*) FROM t_users
UNION ALL SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
UNION ALL SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules  
UNION ALL SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows;
"

# 生产环境
ssh <EMAIL> "mysql -uroot -pnvshen2018 zoombusv -e \"
SELECT 't_users' as table_name, COUNT(*) FROM t_users
UNION ALL SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
UNION ALL SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
UNION ALL SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows;
\""
```

### 数据完整性验证
- 检查用户数据是否完整
- 检查PMI记录是否正确关联
- 检查计划和窗口的依赖关系
- 验证关键字段如magic_id等是否正确

## 回滚方案

如果迁移后发现问题，可以使用备份数据回滚：

```bash
# 连接到生产服务器
ssh <EMAIL>

# 找到备份目录
ls -la /tmp/prod_backup_*

# 恢复数据（替换为实际的备份目录）
BACKUP_DIR="/tmp/prod_backup_YYYYMMDD_HHMMSS"

# 按依赖关系倒序清空表
mysql -uroot -pnvshen2018 zoombusv -e "
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE t_pmi_schedule_windows;
TRUNCATE TABLE t_pmi_schedules;
TRUNCATE TABLE t_pmi_records;
TRUNCATE TABLE t_users;
SET FOREIGN_KEY_CHECKS = 1;
"

# 按依赖关系顺序恢复数据
mysql -uroot -pnvshen2018 zoombusv < $BACKUP_DIR/t_users_backup.sql
mysql -uroot -pnvshen2018 zoombusv < $BACKUP_DIR/t_pmi_records_backup.sql
mysql -uroot -pnvshen2018 zoombusv < $BACKUP_DIR/t_pmi_schedules_backup.sql
mysql -uroot -pnvshen2018 zoombusv < $BACKUP_DIR/t_pmi_schedule_windows_backup.sql
```

## 字符集兼容性 ⭐ **重要更新**

### 问题背景
- **本地环境**: MySQL 8.0+ 使用 `utf8mb4_0900_ai_ci` 字符集
- **生产环境**: MySQL 5.7.18 不支持 `utf8mb4_0900_ai_ci` 字符集
- **解决方案**: 自动转换为兼容的 `utf8mb4_general_ci` 字符集

### 转换规则
脚本会自动转换以下字符集声明：
- `utf8mb4_0900_ai_ci` → `utf8mb4_general_ci`
- `utf8mb4_unicode_ci` → `utf8mb4_general_ci`
- `utf8mb4_unicode_520_ci` → `utf8mb4_general_ci`
- `utf8mb4_bin` → `utf8mb4_general_ci`

### 测试工具
使用 `test_charset_conversion.sh` 脚本测试字符集转换功能：
```bash
./test_charset_conversion.sh
```

## 注意事项

1. **数据库名称差异**: 本地是 `zoombusV`，生产是 `zoombusv`
2. **字符集兼容性**: 脚本自动处理字符集转换，确保兼容性
3. **依赖关系**: 必须按正确顺序清空和导入数据
4. **外键约束**: 清空表时需要临时禁用外键检查
5. **备份重要性**: 每次迁移前都会自动备份，请妥善保管备份文件
6. **服务停止**: 建议在迁移期间停止生产环境的应用服务
7. **MySQL版本**: 生产环境MySQL 5.7.18，本地MySQL 8.0+

## 联系信息

如有问题，请联系系统管理员。

---

**最后更新**: 2025-08-20
**版本**: 1.0
