# ZoomBus - Zoom用户管理系统

ZoomBus是一个基于Spring Boot和React的Zoom用户管理系统，提供企业级的Zoom账号和会议管理功能。

## 功能特性

### 后端功能
- **用户管理**: 创建、编辑、删除企业内部用户
- **Zoom账号管理**: 为用户创建和管理Zoom账号
- **会议管理**: 创建、管理Zoom会议
- **Webhook集成**: 接收Zoom的webhook通知，同步会议状态
- **API集成**: 完整的Zoom API集成

### 前端功能

#### 管理端前端 (端口3000)
- **仪表板**: 显示系统统计信息和最近会议
- **用户管理界面**: 用户的增删改查操作
- **Zoom账号管理**: 为用户创建Zoom账号
- **会议管理**: 创建和管理会议，支持直接加入会议
- **PMI管理**: 生成和管理PMI会议室
- **Webhook事件监控**: 查看和监控Zoom webhook事件

#### 用户端前端 (端口3001)
- **PMI使用页面**: 终端用户无需登录即可使用PMI功能
- **一键开启PMI**: 快速配置Zoom账号并生成主持人链接
- **会议信息复制**: 一键复制会议详情
- **响应式设计**: 支持桌面和移动设备访问

## 技术栈

### 后端
- Java 11
- Spring Boot 2.7.14
- Spring Data JPA
- Spring Security
- Spring WebFlux (WebClient)
- H2/MySQL数据库
- Lombok
- Jackson

### 前端

#### 管理端前端 (frontend/)
- React 18
- Ant Design 5
- Axios
- React Router
- Day.js

#### 用户端前端 (user-frontend/)
- React 18
- Ant Design 5
- Axios
- React Router
- Vite (构建工具)

## 项目结构

```
zoombus/
├── src/main/java/com/zoombus/
│   ├── ZoomBusApplication.java          # 主应用类
│   ├── config/                          # 配置类
│   │   ├── ZoomConfig.java             # Zoom配置
│   │   └── WebClientConfig.java        # WebClient配置
│   ├── controller/                      # 控制器层
│   │   ├── UserController.java         # 用户控制器
│   │   ├── PublicPmiController.java    # 公共PMI API控制器
│   │   └── UserFrontendController.java # 用户前端路由控制器
│   │   ├── ZoomAccountController.java  # Zoom账号控制器
│   │   ├── MeetingController.java      # 会议控制器
│   │   └── WebhookController.java      # Webhook控制器
│   ├── dto/                            # 数据传输对象
│   ├── entity/                         # 实体类
│   │   ├── User.java                   # 用户实体
│   │   ├── ZoomAccount.java           # Zoom账号实体
│   │   ├── Meeting.java               # 会议实体
│   │   └── WebhookEvent.java          # Webhook事件实体
│   ├── repository/                     # 数据访问层
│   ├── service/                        # 业务逻辑层
│   │   ├── UserService.java           # 用户服务
│   │   ├── ZoomAccountService.java    # Zoom账号服务
│   │   ├── MeetingService.java        # 会议服务
│   │   ├── ZoomApiService.java        # Zoom API服务
│   │   └── WebhookService.java        # Webhook服务
│   └── exception/                      # 异常处理
├── src/main/resources/
│   ├── application.yml                 # 应用配置
│   ├── application-prod.yml           # 生产环境配置
│   ├── static/                        # 管理端前端构建文件
│   └── static-user/                   # 用户端前端构建文件
├── frontend/                          # 管理端前端项目
│   ├── src/
│   │   ├── pages/                     # 页面组件
│   │   ├── services/                  # API服务
│   │   └── App.js                     # 主应用组件
│   └── package.json
├── user-frontend/                     # 用户端前端项目
│   ├── src/
│   │   ├── pages/                     # 页面组件
│   │   │   └── PmiUsage.jsx          # PMI使用页面
│   │   ├── services/                  # API服务
│   │   └── App.jsx                    # 主应用组件
│   ├── vite.config.js                # Vite配置
│   └── package.json
├── start.sh                          # 开发环境启动脚本
└── build-user-frontend.sh            # 用户前端构建脚本
```

## 快速开始

### 前置要求
- Java 11+
- Node.js 16+
- Maven 3.6+
- Zoom开发者账号

### 开发环境启动

#### 🚀 推荐方式：一键启动

```bash
# 1. 克隆项目
git clone <repository-url>
cd zoombus

# 2. 启动开发环境
./start.sh
# 选择 "1. 开发模式 (前后端分离启动)"
```

这个脚本会自动：
- 启动后端服务（端口8080）
- 启动管理端前端开发服务器（端口3000）
- 启动用户端前端开发服务器（端口3001）
- 配置API代理
- 显示访问地址

#### 🔧 手动启动方式

```bash
# 1. 启动后端
export JAVA_HOME=/path/to/java11
./mvnw spring-boot:run

# 2. 启动管理端前端（新终端）
cd frontend
npm install && npm start

# 3. 启动用户端前端（新终端）
cd user-frontend
npm install && npm run dev
```

#### 📍 访问地址
- **管理端前端**: http://localhost:3000 （管理员使用，需要登录）
- **用户端前端**: http://localhost:3001 （终端用户使用，无需登录）
- **后端API**: http://localhost:8080
- **用户端生产访问**: http://localhost:8080/m
- **H2控制台**: http://localhost:8080/h2-console
- **健康检查**: http://localhost:8080/actuator/health

#### ✨ 开发特性
- ✅ **双前端架构**：管理端和用户端分离开发
- ✅ **前端热重载**：代码修改自动刷新页面
- ✅ **API自动代理**：前端请求自动转发到后端
- ✅ **错误覆盖**：编译错误在浏览器中显示
- ✅ **源码映射**：便于调试和错误定位
- ✅ **环境隔离**：开发和生产环境配置分离

#### 🎯 启动脚本选项
使用 `./start.sh` 可以选择不同的启动模式：
1. **开发模式** - 同时启动后端和两个前端
2. **生产模式** - 使用Docker Compose启动
3. **仅启动后端** - 只启动Spring Boot应用
4. **仅启动管理端前端** - 只启动管理端前端
5. **仅启动用户端前端** - 只启动用户端前端
6. **仅启动两个前端** - 同时启动两个前端（需要单独启动后端）

### 配置Zoom API
在`src/main/resources/application.yml`中配置Zoom API信息：

```yaml
zoom:
  account-id: your_account_id
  client-id: your_client_id
  client-secret: your_client_secret
  webhook-secret-token: your_webhook_secret
```

### 访问应用
打开浏览器访问 http://localhost:3000

## API文档

### 用户管理API
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

### Zoom账号管理API
- `GET /api/zoom-accounts` - 获取Zoom账号列表
- `POST /api/zoom-accounts` - 创建Zoom账号
- `POST /api/zoom-accounts/{id}/sync` - 同步Zoom用户信息

### 会议管理API
- `GET /api/meetings` - 获取会议列表
- `POST /api/meetings` - 创建会议
- `PUT /api/meetings/{id}/status` - 更新会议状态

### Webhook API
- `POST /api/webhooks/zoom` - 接收Zoom webhook事件
- `GET /api/webhooks/events` - 获取webhook事件列表

## Zoom Webhook配置

1. 在Zoom Marketplace创建Webhook应用
2. 配置Webhook URL: `https://your-domain.com/api/webhooks/zoom`
3. 订阅以下事件：
   - Meeting Created
   - Meeting Updated
   - Meeting Deleted
   - Meeting Started
   - Meeting Ended
   - User Created
   - User Updated
   - User Deleted

## 数据库配置

### 开发环境 (H2)
默认使用H2内存数据库，无需额外配置。
访问H2控制台: http://localhost:8080/h2-console

### 生产环境 (MySQL)
1. 创建MySQL数据库
2. 修改`application-prod.yml`中的数据库配置
3. 使用`--spring.profiles.active=prod`启动应用

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t zoombus .

# 运行容器
docker run -p 8080:8080 -e ZOOM_CLIENT_ID=your_client_id -e ZOOM_CLIENT_SECRET=your_client_secret zoombus
```

### 传统部署
```bash
# 打包应用
mvn clean package

# 运行jar包
java -jar target/zoombus-1.0.0.jar --spring.profiles.active=prod
```

## 开发指南

### 添加新功能
1. 在`entity`包中定义实体类
2. 在`repository`包中创建Repository接口
3. 在`service`包中实现业务逻辑
4. 在`controller`包中创建REST API
5. 在前端`pages`目录中创建页面组件

### 测试
```bash
# 运行后端测试
mvn test

# 运行前端测试
cd frontend
npm test
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题，请联系开发团队。
