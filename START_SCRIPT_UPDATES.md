# start.sh启动脚本更新说明

## 🎯 更新概述

已成功修改`start.sh`启动脚本，集成了ngrok隧道功能，连接到ngrok.com官方服务器，为Zoom Webhook开发提供公网访问能力。

## ✅ 新增功能

### 1. ngrok环境检测
- 自动检测ngrok是否已安装
- 显示ngrok可用状态
- 提供安装指导

### 2. 新增启动选项
原有选项保持不变，新增以下选项：

- **选项2**: 开发模式 + ngrok (包含webhook隧道)
- **选项8**: 仅启动ngrok隧道

选项编号调整：
- 原选项2-6 → 现选项3-7
- 新增选项2和8

### 3. ngrok启动函数
新增`start_ngrok()`函数，包含：
- 进程检测和清理
- 认证token检查
- 隧道启动和状态监控
- URL获取和显示
- 错误处理和提示

### 4. 配置文件支持
- 修改`ngrok.yml`配置文件
- 连接到ngrok.com官方服务器
- 移除自定义服务器配置
- 添加请求头优化

## 🔧 技术实现

### 启动选项详情

#### 选项1: 开发模式 (原有功能)
```bash
# 启动内容
- 后端服务 (8080端口)
- 管理端前端 (3000端口)  
- 用户端前端 (3001端口)
```

#### 选项2: 开发模式 + ngrok (新增)
```bash
# 启动内容
- 后端服务 (8080端口)
- 管理端前端 (3000端口)
- 用户端前端 (3001端口)
- ngrok隧道 (公网访问)

# 显示信息
🚀 后端服务: http://localhost:8080
🎨 管理端前端: http://localhost:3000
👥 用户端前端: http://localhost:3001
🌐 公网访问地址: https://xxx.ngrok-free.app
🔗 Webhook URL: https://xxx.ngrok-free.app/api/webhooks/zoom/{account_id}
📊 ngrok控制台: http://localhost:4040
```

#### 选项8: 仅启动ngrok隧道 (新增)
```bash
# 启动内容
- ngrok隧道连接到localhost:8080

# 使用场景
- 后端服务已在运行
- 只需要公网访问能力
- 调试webhook功能
```

### ngrok配置优化

#### 修改前 (连接自定义服务器)
```yaml
version: "3"
agent:
  connect_url: ngrok.nslcp.com:4443
```

#### 修改后 (连接官方服务器)
```yaml
version: "2"
authtoken: your_auth_token_here

tunnels:
  zoombus:
    proto: http
    addr: 8080
    request_header:
      add:
        - "ngrok-skip-browser-warning: true"

region: us
web_addr: localhost:4040
```

### 错误处理和提示

1. **ngrok未安装**
   ```
   ⚠ 未检测到ngrok，webhook功能将不可用
   安装ngrok: https://ngrok.com/download
   ```

2. **认证token未配置**
   ```
   ⚠ ngrok认证token未配置或无效
   请按照以下步骤配置:
   1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken
   2. 复制您的认证token
   3. 运行: ngrok config add-authtoken YOUR_TOKEN
   ```

3. **隧道启动失败**
   ```
   ❌ ngrok隧道启动失败
   查看日志: cat ngrok.log
   ```

## 📋 使用指南

### 快速开始

1. **安装ngrok**
   ```bash
   # macOS
   brew install ngrok/ngrok/ngrok
   
   # 或访问 https://ngrok.com/download
   ```

2. **配置认证token** (可选，但推荐)
   ```bash
   # 获取token: https://dashboard.ngrok.com/get-started/your-authtoken
   ngrok config add-authtoken YOUR_TOKEN
   ```

3. **启动服务**
   ```bash
   ./start.sh
   # 选择 "2. 开发模式 + ngrok (包含webhook隧道)"
   ```

### Zoom Webhook配置

启动成功后，使用显示的Webhook URL配置Zoom应用：

```
Event notification endpoint URL:
https://your-ngrok-url.ngrok-free.app/api/webhooks/zoom/{account_id}

Event types:
☑ Meeting Created (meeting.created)
```

### 监控和调试

- **ngrok控制台**: http://localhost:4040
- **实时日志**: `tail -f ngrok.log`
- **隧道状态**: `curl http://localhost:4040/api/tunnels`

## 🔒 安全考虑

### 免费版限制
- 随机URL（每次重启变化）
- 访问频率限制
- 显示ngrok品牌页面

### 生产环境
⚠️ **不建议生产环境使用ngrok**

生产环境建议：
- 专用服务器和域名
- SSL证书配置
- 防火墙和安全组
- 负载均衡器

## 🧪 测试工具

### 测试脚本
```bash
# 测试ngrok功能
./test-ngrok.sh

# 测试webhook功能
python3 test-webhook-direct.py
```

### 验证步骤
1. 启动服务：`./start.sh` → 选择2
2. 检查隧道：访问 http://localhost:4040
3. 测试访问：访问显示的公网URL
4. 配置webhook：使用显示的Webhook URL
5. 测试webhook：发送测试事件

## 📚 相关文档

- [ngrok配置指南](./NGROK_SETUP.md)
- [Webhook实现文档](./MEETING_CREATED_WEBHOOK_IMPLEMENTATION.md)
- [ngrok官方文档](https://ngrok.com/docs)

## 🎉 使用效果

现在开发者可以：

1. **一键启动**: 完整开发环境 + 公网隧道
2. **即时测试**: 无需部署即可测试webhook
3. **实时调试**: 通过ngrok控制台查看请求
4. **灵活配置**: 支持多种启动模式
5. **安全连接**: HTTPS隧道，满足webhook要求

这大大简化了Zoom Webhook的开发和测试流程！🚀
