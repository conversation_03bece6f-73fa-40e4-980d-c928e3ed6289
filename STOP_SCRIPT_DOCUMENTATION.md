# stop.sh 停止脚本文档

## 🎯 功能概述

`stop.sh` 是与 `start.sh` 配套的停止脚本，用于安全、完整地停止ZoomBus项目的所有服务，包括前后端应用、ngrok隧道、Docker容器等。

## ✅ 支持的服务

### 1. 后端服务
- **Spring Boot应用** (端口8080)
- 通过进程名称和端口双重检测
- 支持优雅停止和强制停止

### 2. 前端服务
- **管理端前端** (React, 端口3000)
- **用户端前端** (Vue, 端口3001)
- 自动检测npm进程

### 3. ngrok隧道
- **ngrok进程**
- 清理ngrok日志文件
- 释放4040控制台端口

### 4. Docker服务
- **Docker Compose服务**
- 完整的容器停止和清理

### 5. 临时文件清理
- ngrok.log
- /tmp/ngrok_api.json
- 其他临时文件

## 🚀 使用方式

### 交互模式

```bash
./stop.sh
```

显示菜单选择停止模式：
```
请选择停止模式:
1. 停止所有服务 (后端 + 前端 + ngrok + Docker)
2. 停止开发模式服务 (后端 + 前端 + ngrok)
3. 停止后端服务
4. 停止前端服务 (管理端 + 用户端)
5. 停止管理端前端
6. 停止用户端前端
7. 停止ngrok隧道
8. 停止Docker Compose服务
9. 仅清理临时文件
0. 退出
```

### 快速模式

```bash
# 停止所有服务
./stop.sh all

# 停止开发模式服务 (不包括Docker)
./stop.sh dev

# 停止后端服务
./stop.sh backend

# 停止前端服务
./stop.sh frontend

# 停止ngrok隧道
./stop.sh ngrok

# 停止Docker服务
./stop.sh docker

# 显示帮助信息
./stop.sh help
```

## 🔧 技术实现

### 停止机制

#### 1. 进程检测
```bash
# 通过进程名称查找
pgrep -f "spring-boot:run"
pgrep -f "npm.*start"
pgrep -f "ngrok"
```

#### 2. 端口检测
```bash
# 通过端口查找占用进程
lsof -ti:8080  # 后端
lsof -ti:3000  # 管理端前端
lsof -ti:3001  # 用户端前端
```

#### 3. 优雅停止
```bash
# 先尝试正常停止
kill $pid

# 等待2秒后检查
sleep 2

# 如果仍在运行，强制停止
kill -9 $pid
```

### 状态检查

停止操作完成后，自动检查各服务状态：
- ✅ 服务已停止
- ⚠️ 服务仍在运行

### 错误处理

- 服务未运行时显示警告而非错误
- 支持部分服务停止失败的情况
- 提供详细的状态反馈

## 📋 停止模式详解

### 模式1: 停止所有服务
- 后端服务 (Spring Boot)
- 管理端前端 (React)
- 用户端前端 (Vue)
- ngrok隧道
- Docker Compose服务
- 清理临时文件

### 模式2: 停止开发模式服务
- 后端服务 (Spring Boot)
- 管理端前端 (React)
- 用户端前端 (Vue)
- ngrok隧道
- 清理临时文件
- **不包括**: Docker服务

### 模式3-6: 单独服务停止
- 精确停止指定服务
- 适合部分重启场景

### 模式7: 停止ngrok隧道
- 停止ngrok进程
- 清理ngrok相关文件
- 释放4040端口

### 模式8: 停止Docker服务
- 执行 `docker-compose down`
- 停止所有容器
- 清理网络和卷

### 模式9: 仅清理临时文件
- 清理日志文件
- 清理临时文件
- 不停止任何服务

## 🎨 输出特性

### 颜色编码
- 🔵 **蓝色**: 操作提示
- 🟢 **绿色**: 成功信息
- 🟡 **黄色**: 警告信息
- 🔴 **红色**: 错误信息

### 状态图标
- ✅ 操作成功
- ⚠️ 警告状态
- ❌ 操作失败

### 详细反馈
- 显示找到的进程ID
- 显示停止操作结果
- 显示最终服务状态

## 🔍 故障排除

### 常见问题

#### 1. 服务停止失败
```bash
# 手动检查进程
ps aux | grep "spring-boot\|npm\|ngrok"

# 手动停止
kill -9 $(pgrep -f "spring-boot:run")
```

#### 2. 端口仍被占用
```bash
# 检查端口占用
lsof -i:8080
lsof -i:3000
lsof -i:3001

# 强制释放端口
sudo lsof -ti:8080 | xargs kill -9
```

#### 3. Docker容器停止失败
```bash
# 手动停止所有容器
docker stop $(docker ps -q)

# 强制删除容器
docker rm -f $(docker ps -aq)
```

### 调试模式

如果需要调试停止过程，可以修改脚本添加详细输出：
```bash
# 在脚本开头添加
set -x  # 显示执行的命令
```

## 📊 与start.sh的对应关系

| start.sh选项 | 启动的服务 | stop.sh对应模式 |
|-------------|-----------|----------------|
| 选项1: 开发模式 | 后端+前端 | 模式2: 开发模式 |
| 选项2: 开发模式+ngrok | 后端+前端+ngrok | 模式2: 开发模式 |
| 选项3: 生产模式 | Docker Compose | 模式8: Docker |
| 选项4: 仅后端 | 后端 | 模式3: 后端 |
| 选项5: 管理端前端 | 管理端前端 | 模式5: 管理端 |
| 选项6: 用户端前端 | 用户端前端 | 模式6: 用户端 |
| 选项7: 两个前端 | 前端服务 | 模式4: 前端 |
| 选项8: ngrok隧道 | ngrok | 模式7: ngrok |

## 💡 最佳实践

### 开发阶段
```bash
# 启动开发环境
./start.sh  # 选择2

# 停止开发环境
./stop.sh dev
```

### 生产部署
```bash
# 启动生产环境
./start.sh  # 选择3

# 停止生产环境
./stop.sh docker
```

### 部分重启
```bash
# 只重启后端
./stop.sh backend
./start.sh  # 选择4

# 只重启前端
./stop.sh frontend
./start.sh  # 选择7
```

### 完全清理
```bash
# 停止所有服务并清理
./stop.sh all
```

## 🎉 总结

`stop.sh` 脚本提供了：

- ✅ **完整的服务停止**: 支持所有start.sh启动的服务
- ✅ **灵活的停止模式**: 交互式和快速命令行模式
- ✅ **安全的停止机制**: 优雅停止 + 强制停止
- ✅ **详细的状态反馈**: 彩色输出和状态检查
- ✅ **智能的错误处理**: 容错性强，用户友好
- ✅ **完整的清理功能**: 临时文件和日志清理

现在您可以安全、便捷地管理ZoomBus项目的所有服务！🚀
