# Task 9 诊断指南

## 问题描述
生产环境中 `<EMAIL>` 的 task 9 没有执行。

## 诊断工具

### 1. API接口诊断 (推荐)
```bash
# 使用诊断脚本
./scripts/diagnose_task_9.sh http://your-server:8080 your-auth-token

# 或者直接调用API
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://your-server:8080/api/pmi-scheduled-tasks/9/diagnose
```

### 2. 数据库直接查询
```bash
# 连接到生产数据库
mysql -u root -p zoombusV < scripts/check_task_9_database.sql
```

### 3. Web界面诊断
访问管理后台：
- 任务详情：`/admin/pmi-tasks/9`
- 诊断页面：通过任务详情页面的"诊断"按钮

## 可能的原因分析

### 1. 任务状态问题
- **SCHEDULED但过期**：任务计划时间已过，但仍为SCHEDULED状态
- **EXECUTING卡死**：任务标记为执行中，但实际已卡死
- **FAILED需重试**：任务执行失败，需要重试

### 2. 调度器问题
- **调度器停止**：定时任务调度器没有运行
- **线程池满**：任务执行线程池已满，无法执行新任务
- **系统资源不足**：内存或CPU不足导致调度器异常

### 3. 分布式锁问题
- **锁未释放**：Redis中的分布式锁没有正确释放
- **Redis连接异常**：Redis连接问题导致锁机制失效

### 4. PMI窗口状态问题
- **窗口已过期**：关联的PMI窗口已过期，任务无法执行
- **窗口状态异常**：PMI窗口状态不正确

### 5. 数据一致性问题
- **数据库与Redis不一致**：数据库和Redis中的任务状态不一致
- **任务记录损坏**：任务记录数据损坏

## 诊断步骤

### 第一步：快速检查
```bash
# 1. 检查任务基本信息
curl http://your-server:8080/api/pmi-scheduled-tasks/9

# 2. 检查系统健康状态
curl http://your-server:8080/api/scheduler-monitor/health

# 3. 检查调度器概览
curl http://your-server:8080/api/scheduler-monitor/overview
```

### 第二步：深度诊断
```bash
# 使用诊断工具
curl -X GET http://your-server:8080/api/pmi-scheduled-tasks/9/diagnose
```

### 第三步：查看日志
```bash
# 查看应用日志
tail -f logs/zoombus.log | grep -i "task.*9"

# 查看任务执行记录
curl http://your-server:8080/api/scheduled-tasks/records?taskName=task_9
```

## 解决方案

### 1. 自动修复 (推荐)
```bash
# 使用自动修复工具
curl -X POST http://your-server:8080/api/pmi-scheduled-tasks/9/repair
```

### 2. 手动执行任务
```bash
# 手动触发任务执行
curl -X POST http://your-server:8080/api/pmi-scheduled-tasks/9/execute
```

### 3. 重新调度任务
```bash
# 重新调度到新时间
curl -X PUT http://your-server:8080/api/pmi-scheduled-tasks/9/reschedule \
     -H "Content-Type: application/json" \
     -d '{"newExecuteTime": "2024-12-15T10:00:00"}'
```

### 4. 清理Redis状态
```bash
# 连接Redis清理锁
redis-cli
> DEL task:9
> DEL running_tasks:task_9
```

### 5. 重启相关服务
```bash
# 重启应用 (最后手段)
systemctl restart zoombus
```

## 预防措施

### 1. 监控告警
- 设置任务执行超时告警
- 监控调度器健康状态
- 监控Redis连接状态

### 2. 定期清理
- 定期清理过期的任务记录
- 清理Redis中的过期锁
- 清理执行记录

### 3. 配置优化
```yaml
# application.yml
pmi:
  task:
    scheduling:
      performance:
        task-timeout-minutes: 30  # 任务超时时间
        thread-pool-size: 10      # 线程池大小
      retry:
        max-retry-count: 3        # 最大重试次数
```

## 常见问题解答

### Q: 任务显示SCHEDULED但已过期很久？
A: 这通常是调度器停止或系统资源不足导致。使用自动修复工具或手动执行任务。

### Q: 任务显示EXECUTING但实际没有执行？
A: 可能是分布式锁没有释放或任务卡死。清理Redis锁并重新执行任务。

### Q: 修复后任务仍然不执行？
A: 检查PMI窗口状态，确认窗口没有过期。如果窗口过期，需要重新创建窗口。

### Q: 如何避免类似问题？
A: 
1. 启用任务监控和告警
2. 定期检查系统健康状态
3. 确保系统资源充足
4. 定期清理过期数据

## 联系支持
如果以上方法都无法解决问题，请联系技术支持并提供：
1. 诊断工具的完整输出
2. 相关的应用日志
3. 系统资源使用情况
4. Redis和数据库连接状态
