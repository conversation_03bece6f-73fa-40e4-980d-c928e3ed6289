# Zoom Webhook 多账号支持实现总结

## 🎯 实现概述

成功为ZoomBus系统实现了多Zoom主账号的webhook支持。系统现在可以通过URL中的`accountId`参数（对应ZoomAuth表中的`zoom_account_id`字段）来区分和处理不同Zoom主账号的webhook事件。

## ✅ 已完成的功能

### 1. 新增的Webhook Endpoints

#### 多账号事件接收端点
- **URL**: `POST /api/webhooks/zoom/{accountId}`
- **功能**: 接收指定账号的webhook事件
- **验证**: 账号ID验证 + 签名验证

#### 多账号验证端点
- **URL**: `POST /api/webhooks/zoom/{accountId}/validate`
- **功能**: 验证webhook endpoint配置
- **返回**: 加密后的token用于Zoom验证

#### 兼容性端点
- **URL**: `POST /api/webhooks/zoom` (保留原有功能)
- **URL**: `POST /api/webhooks/zoom/validate` (保留原有功能)

### 2. 核心服务层改进

#### WebhookService 新增方法
- `isValidAccountId(String accountId)` - 验证账号ID
- `processWebhookEvent(String accountId, String eventType, JsonNode eventData)` - 多账号事件处理
- `verifyWebhookSignature(String accountId, String payload, String authorization)` - 签名验证
- `encryptToken(String accountId, String plainToken)` - Token加密
- `getWebhookEventsByAccount(String accountId)` - 按账号查询事件

#### ZoomAuthService 新增方法
- `getZoomAuthByZoomAccountId(String zoomAccountId)` - 根据账号ID查找认证信息

### 3. 数据库优化

#### 索引优化
- 为`t_webhook_events.zoom_account_id`字段添加索引
- 提高按账号查询的性能

#### 数据结构
- 直接使用现有的`zoom_account_id`字段区分账号
- 无需新增额外字段，保持数据结构简洁

### 4. 安全特性

#### 签名验证
- 每个账号使用独立的Webhook Secret Token
- HMAC-SHA256签名验证算法
- 防止伪造webhook请求

#### 账号验证
- URL中的accountId必须在ZoomAuth表中存在
- 404错误处理无效账号ID
- 401错误处理签名验证失败

### 5. 监控和管理

#### 统计接口
- `GET /api/webhooks/stats` - 获取所有账号的统计信息
- `GET /api/webhooks/events/account/{accountId}` - 获取特定账号的事件

#### 统计数据包含
- 总事件数量
- 按状态分组的事件数量
- 按账号分组的事件数量
- 最近事件列表

## 🔧 技术实现细节

### 1. URL路由设计
```
/api/webhooks/zoom/{accountId}           # 事件接收
/api/webhooks/zoom/{accountId}/validate  # 端点验证
/api/webhooks/events/account/{accountId} # 事件查询
```

### 2. 事件处理流程
1. 接收webhook请求
2. 验证accountId是否存在
3. 验证webhook签名
4. 解析事件数据
5. 根据事件类型处理
6. 保存处理结果

### 3. 签名验证算法
```java
// HMAC-SHA256签名验证
Mac mac = Mac.getInstance("HmacSHA256");
SecretKeySpec secretKeySpec = new SecretKeySpec(webhookSecret.getBytes(), "HmacSHA256");
mac.init(secretKeySpec);
byte[] hash = mac.doFinal(payload.getBytes());
String expectedSignature = Base64.getEncoder().encodeToString(hash);
```

## 📋 配置指南

### 1. Zoom开发者控制台配置
```
Event notification endpoint URL:
https://your-domain.com/api/webhooks/zoom/{zoom_account_id}

例如：
https://3c61cbcb7ae6.ngrok-free.app/api/webhooks/zoom/abc123def456
```

### 2. ZoomAuth表配置
确保每个账号在`t_zoom_auths`表中有记录：
- `zoom_account_id`: 与URL中的accountId一致
- `webhook_secret_token`: 用于签名验证的密钥
- `account_name`: 便于管理的账号名称

### 3. 测试验证
使用提供的测试脚本验证配置：
```bash
chmod +x test-webhook-multi-account.sh
./test-webhook-multi-account.sh
```

## 🚀 使用示例

### 1. 配置多个账号
```sql
-- 账号A
INSERT INTO t_zoom_auths (zoom_account_id, account_name, webhook_secret_token, ...)
VALUES ('abc123def456', 'Company A', 'secret-token-a', ...);

-- 账号B  
INSERT INTO t_zoom_auths (zoom_account_id, account_name, webhook_secret_token, ...)
VALUES ('xyz789ghi012', 'Company B', 'secret-token-b', ...);
```

### 2. Zoom控制台配置
- 账号A: `https://domain.com/api/webhooks/zoom/abc123def456`
- 账号B: `https://domain.com/api/webhooks/zoom/xyz789ghi012`

### 3. 查询特定账号事件
```bash
curl http://localhost:8080/api/webhooks/events/account/abc123def456
```

## 📊 监控数据示例

```json
{
  "totalEvents": 150,
  "eventsByStatus": {
    "PROCESSED": 140,
    "FAILED": 8,
    "PENDING": 2
  },
  "eventsByAccount": {
    "abc123def456": 80,
    "xyz789ghi012": 45,
    "school001main": 25
  },
  "recentEvents": [...]
}
```

## 🔄 向后兼容性

- 保留原有的单账号endpoint
- 现有webhook配置继续有效
- 可以逐步迁移到多账号模式
- 不影响现有数据和功能

## 🎉 优势总结

1. **灵活性**: 支持任意数量的Zoom主账号
2. **安全性**: 每个账号独立的签名验证
3. **性能**: 优化的数据库索引和查询
4. **监控**: 完整的事件统计和查询功能
5. **兼容性**: 保持向后兼容，平滑迁移
6. **简洁性**: 使用现有字段，无需复杂的数据结构

现在ZoomBus系统已经完全支持多Zoom主账号的webhook处理！
