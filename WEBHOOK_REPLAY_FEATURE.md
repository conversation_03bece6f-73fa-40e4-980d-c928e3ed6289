# Webhook事件重放功能

## 📋 功能概述

在前端管理页面的webhook-events页面新增了"重放事件"功能，允许管理员重新向后端发送已接收的webhook事件，用于调试和问题排查。

## 🎯 功能特点

### 1. **一键重放**
- 在事件列表的操作列中添加"重放"按钮
- 支持重放任何已接收的webhook事件
- 确认对话框防止误操作

### 2. **完整重新处理**
- 重新执行完整的事件处理流程
- 包括webhook事件处理和会议事件处理
- 保持原始事件数据不变

### 3. **响应式设计**
- PC端显示完整的"重放"文字
- 移动端只显示图标，节省空间
- 适配不同屏幕尺寸

## 🔧 技术实现

### 后端实现

#### 1. 新增API接口
```java
@PostMapping("/events/{eventId}/replay")
public ResponseEntity<Map<String, Object>> replayWebhookEvent(@PathVariable Long eventId)
```

**功能**:
- 根据事件ID获取原始事件数据
- 重新调用事件处理逻辑
- 返回处理结果

#### 2. 新增服务方法
```java
public WebhookEvent getWebhookEventById(Long eventId)
```

**功能**:
- 根据ID查询单个webhook事件
- 支持重放功能的数据获取

#### 3. 处理流程
1. **验证事件存在性** - 检查事件ID是否有效
2. **解析事件数据** - 提取原始JSON数据
3. **重新处理事件** - 调用原始处理逻辑
4. **处理会议事件** - 如果是会议相关事件，同时处理
5. **返回结果** - 提供详细的处理结果

### 前端实现

#### 1. UI组件更新
```jsx
// 新增操作列
{
  title: '操作',
  key: 'action',
  render: (_, record) => (
    <Popconfirm
      title="确定要重放此事件吗？"
      onConfirm={() => handleReplayEvent(record.id, record.eventType)}
    >
      <Button type="primary" size="small" icon={<ReloadOutlined />}>
        {isMobileView ? '' : '重放'}
      </Button>
    </Popconfirm>
  )
}
```

#### 2. API调用
```javascript
// 新增重放API
replayWebhookEvent: (eventId) => api.post(`/webhooks/events/${eventId}/replay`)
```

#### 3. 事件处理
```javascript
const handleReplayEvent = async (eventId, eventType) => {
  // 调用重放API
  // 显示处理结果
  // 刷新事件列表
}
```

## 🚀 使用方法

### 1. **访问页面**
```
http://localhost:3000/webhook-events
```

### 2. **查找目标事件**
- 在事件列表中找到需要重放的事件
- 可以使用状态筛选功能缩小范围
- 查看事件详情确认是否为目标事件

### 3. **执行重放**
- 点击操作列中的"重放"按钮
- 在确认对话框中点击"确定"
- 等待处理完成并查看结果提示

### 4. **验证结果**
- 查看成功/失败提示消息
- 检查事件列表是否有更新
- 查看应用日志确认处理过程

## 🧪 测试方法

### 自动化测试
```bash
# 运行测试脚本
./test_webhook_replay.sh
```

**测试内容**:
- 创建测试事件
- 执行重放操作
- 验证处理结果
- 测试错误处理

### 手动测试
1. **准备测试数据**
   - 确保有webhook事件记录
   - 或使用测试脚本创建

2. **前端功能测试**
   - 访问webhook-events页面
   - 点击重放按钮
   - 验证确认对话框
   - 检查处理结果

3. **日志验证**
   - 查看应用日志
   - 确认重放处理过程
   - 验证业务逻辑执行

## 📊 API文档

### 重放事件接口

**URL**: `POST /api/webhooks/events/{eventId}/replay`

**参数**:
- `eventId` (路径参数): 事件ID

**响应格式**:
```json
{
  "success": true,
  "message": "事件重放成功",
  "eventId": 123,
  "eventType": "meeting.started"
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "事件不存在",
  "eventId": 999
}
```

## 🔍 使用场景

### 1. **问题调试**
- 重现特定事件的处理过程
- 调试事件处理逻辑
- 验证修复效果

### 2. **数据修复**
- 重新处理失败的事件
- 补充缺失的数据
- 修正处理状态

### 3. **功能测试**
- 测试新的事件处理逻辑
- 验证系统稳定性
- 压力测试

### 4. **开发调试**
- 本地开发环境测试
- 新功能验证
- 集成测试

## ⚠️ 注意事项

### 1. **重复处理**
- 重放会重新执行完整的处理逻辑
- 可能会创建重复的数据记录
- 建议在测试环境使用

### 2. **权限控制**
- 当前未实现权限控制
- 所有管理员都可以重放事件
- 生产环境需要谨慎使用

### 3. **性能影响**
- 重放会消耗系统资源
- 大量重放可能影响性能
- 建议分批处理

### 4. **数据一致性**
- 重放不会修改原始事件记录
- 可能会创建新的处理记录
- 需要注意数据状态

## 🛠️ 故障排除

### 1. **重放失败**
- 检查事件ID是否存在
- 查看应用日志错误信息
- 验证事件数据格式

### 2. **前端错误**
- 检查网络连接
- 查看浏览器控制台
- 确认API响应格式

### 3. **权限问题**
- 确认用户登录状态
- 检查API访问权限
- 验证token有效性

## 📈 后续优化

### 1. **批量重放**
- 支持选择多个事件批量重放
- 提供批量操作进度显示
- 优化批量处理性能

### 2. **权限控制**
- 添加重放权限验证
- 实现操作日志记录
- 支持权限级别控制

### 3. **高级功能**
- 支持修改事件数据后重放
- 提供重放历史记录
- 添加重放结果统计

### 4. **监控告警**
- 重放操作监控
- 异常情况告警
- 性能指标统计

---

**功能开发时间**: 2025-08-05  
**版本**: v1.0  
**状态**: ✅ 开发完成，待测试
