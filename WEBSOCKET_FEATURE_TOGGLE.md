# WebSocket功能开关使用说明

## 🎯 功能概述

为了便于调试和减少日志输出，新增了WebSocket功能开关，可以控制：
- WebSocket功能的启用/禁用
- 监控数据推送的启用/禁用
- 各种监控任务的执行间隔

## 📋 配置说明

### 配置文件位置
- 主配置：`src/main/resources/application.yml`
- 生产配置：`src/main/resources/application-prod.yml`

### 配置项详解

```yaml
# WebSocket配置
websocket:
  enabled: false  # WebSocket功能总开关，默认关闭
  monitoring:
    enabled: false  # 监控数据推送开关，默认关闭
    intervals:
      zoom-account: 30000     # Zoom账号状态收集间隔（毫秒）
      meeting: 60000          # 会议状态收集间隔（毫秒）
      pmi: 120000            # PMI状态收集间隔（毫秒）
      system: 15000          # 系统性能收集间隔（毫秒）
      comprehensive: 300000   # 综合数据推送间隔（毫秒）
      alert-check: 60000     # 告警检查间隔（毫秒）
```

## 🔧 功能控制

### 1. WebSocket总开关 (`websocket.enabled`)

**作用**：控制整个WebSocket功能模块的启用状态

**影响组件**：
- `WebSocketConfig` - WebSocket配置类
- `WebSocketService` - WebSocket推送服务
- `/ws-monitoring` 端点

**设置方式**：
```yaml
websocket:
  enabled: true   # 启用WebSocket功能
  # 或
  enabled: false  # 禁用WebSocket功能（默认）
```

### 2. 监控推送开关 (`websocket.monitoring.enabled`)

**作用**：控制监控数据的定时收集和推送

**影响组件**：
- `MonitoringScheduler` - 监控定时任务
- 所有监控数据推送方法

**设置方式**：
```yaml
websocket:
  enabled: true
  monitoring:
    enabled: true   # 启用监控推送
    # 或
    enabled: false  # 禁用监控推送（默认）
```

### 3. 监控间隔配置 (`websocket.monitoring.intervals`)

**作用**：控制各种监控任务的执行频率

**配置项说明**：
- `zoom-account`: Zoom账号状态收集间隔
- `meeting`: 会议状态收集间隔
- `pmi`: PMI状态收集间隔
- `system`: 系统性能收集间隔
- `comprehensive`: 综合数据推送间隔
- `alert-check`: 告警检查间隔

## 🚀 使用场景

### 场景1: 完全禁用WebSocket（调试模式）
```yaml
websocket:
  enabled: false
```
**效果**：
- 不加载WebSocket相关组件
- 不启动监控定时任务
- 大幅减少日志输出
- 适合调试其他功能

### 场景2: 启用WebSocket但禁用监控推送
```yaml
websocket:
  enabled: true
  monitoring:
    enabled: false
```
**效果**：
- WebSocket端点可用
- 前端可以连接WebSocket
- 不执行监控数据收集
- 减少监控相关日志

### 场景3: 启用所有功能但调整频率
```yaml
websocket:
  enabled: true
  monitoring:
    enabled: true
    intervals:
      zoom-account: 60000    # 1分钟（原30秒）
      meeting: 120000        # 2分钟（原1分钟）
      pmi: 300000           # 5分钟（原2分钟）
      system: 30000         # 30秒（原15秒）
      comprehensive: 600000  # 10分钟（原5分钟）
      alert-check: 120000   # 2分钟（原1分钟）
```
**效果**：
- 所有功能正常工作
- 降低监控频率
- 减少系统负载和日志量

### 场景4: 生产环境配置
```yaml
websocket:
  enabled: true
  monitoring:
    enabled: true
    intervals:
      zoom-account: 30000
      meeting: 60000
      pmi: 120000
      system: 15000
      comprehensive: 300000
      alert-check: 60000
```

## 🔄 运行时控制

### API端点

#### 1. 查看WebSocket状态
```bash
GET /api/admin/websocket/status
```

**响应示例**：
```json
{
  "enabled": true,
  "monitoring": {
    "enabled": true,
    "intervals": {
      "zoomAccount": 30000,
      "meeting": 60000,
      "pmi": 120000,
      "system": 15000,
      "comprehensive": 300000,
      "alertCheck": 60000
    }
  }
}
```

#### 2. 切换监控推送状态
```bash
POST /api/admin/websocket/monitoring/toggle?enabled=false
```

**响应示例**：
```json
{
  "success": true,
  "monitoring_enabled": false,
  "message": "WebSocket监控推送已禁用"
}
```

#### 3. 更新监控间隔
```bash
POST /api/admin/websocket/monitoring/intervals
Content-Type: application/json

{
  "system": 30000,
  "zoomAccount": 60000
}
```

**响应示例**：
```json
{
  "success": true,
  "updated_intervals": {
    "system": 30000,
    "zoomAccount": 60000
  },
  "current_intervals": {
    "zoomAccount": 60000,
    "meeting": 60000,
    "pmi": 120000,
    "system": 30000,
    "comprehensive": 300000,
    "alertCheck": 60000
  },
  "message": "监控间隔已更新"
}
```

#### 4. 查看连接统计
```bash
GET /api/admin/websocket/connections
```

## 📝 日志级别说明

### WebSocket禁用时
- 使用 `log.trace()` 记录跳过的操作
- 正常情况下不会输出到控制台
- 可通过调整日志级别查看

### WebSocket启用时
- 使用 `log.debug()` 记录监控数据收集
- 使用 `log.info()` 记录重要状态变化
- 使用 `log.error()` 记录错误信息

## 🔧 故障排除

### 问题1: WebSocket连接失败
**检查**：
1. 确认 `websocket.enabled=true`
2. 检查nginx WebSocket代理配置
3. 查看浏览器开发者工具网络标签

### 问题2: 监控数据不更新
**检查**：
1. 确认 `websocket.monitoring.enabled=true`
2. 查看定时任务是否执行
3. 检查监控间隔配置

### 问题3: 日志过多
**解决**：
1. 设置 `websocket.monitoring.enabled=false`
2. 调整监控间隔，增大时间值
3. 调整日志级别

## 💡 最佳实践

### 开发环境
```yaml
websocket:
  enabled: false  # 调试时关闭，减少干扰
```

### 测试环境
```yaml
websocket:
  enabled: true
  monitoring:
    enabled: true
    intervals:
      system: 60000  # 降低频率，减少负载
```

### 生产环境
```yaml
websocket:
  enabled: true
  monitoring:
    enabled: true
    # 使用默认间隔或根据需要调整
```

## 🎉 总结

通过WebSocket功能开关，您可以：
- ✅ 在调试时完全禁用WebSocket，减少日志干扰
- ✅ 灵活控制监控数据收集频率
- ✅ 运行时动态调整配置
- ✅ 根据环境需要优化性能

这个功能开关设计遵循了"默认关闭"的原则，确保在调试时不会产生大量无关的WebSocket日志，同时保持了功能的完整性和可配置性。
