# Zoom主账号认证信息管理功能

## 功能概述

新增了完整的Zoom主账号认证信息管理功能，支持多个Zoom账号的认证信息管理、自动token刷新、手动刷新等功能。

## 主要功能

### 1. 认证信息管理
- ✅ **创建认证信息**: 支持OAuth2.0、JWT、Server-to-Server OAuth三种认证类型
- ✅ **编辑认证信息**: 可修改账号配置、密钥等信息
- ✅ **删除认证信息**: 支持删除不需要的认证配置
- ✅ **状态管理**: 支持启用/禁用认证信息

### 2. Token自动管理
- ✅ **自动刷新**: 定时检查token有效期，自动刷新即将过期的token
- ✅ **有效期监控**: 实时显示token状态（正常/即将过期/已过期）
- ✅ **刷新记录**: 记录每次刷新的时间和次数
- ✅ **错误处理**: 刷新失败时记录错误信息并标记状态

### 3. 手动操作
- ✅ **手动刷新**: 支持单个账号的手动token刷新
- ✅ **批量刷新**: 支持批量刷新所有即将过期的token
- ✅ **实时状态**: 界面实时显示token状态和过期时间

## 技术实现

### 后端架构

#### 1. 实体类 (ZoomAuth)
```java
@Entity
@Table(name = "t_zoom_auth")
public class ZoomAuth {
    // 基本信息
    private String accountName;
    private String zoomAccountId;
    private String clientId;
    private String clientSecret;
    
    // Token信息
    private String accessToken;
    private String refreshToken;
    private LocalDateTime tokenExpiresAt;
    
    // 状态管理
    private AuthType authType;
    private AuthStatus status;
    
    // 自动检查方法
    public boolean isTokenExpired();
    public boolean isTokenExpiringSoon();
}
```

#### 2. 服务层 (ZoomAuthService)
- **认证信息CRUD**: 完整的增删改查功能
- **Token管理**: 自动检查和刷新token
- **多认证类型支持**: OAuth2.0、JWT、Server-to-Server
- **错误处理**: 完善的异常处理和错误记录

#### 3. 定时任务 (ZoomTokenRefreshScheduler)
```java
@Scheduled(fixedRate = 300000) // 每5分钟检查一次
public void refreshExpiredTokens() {
    zoomAuthService.refreshExpiredTokens();
}
```

#### 4. API接口 (ZoomAuthController)
- `GET /api/zoom-auth` - 获取认证列表
- `POST /api/zoom-auth` - 创建认证信息
- `PUT /api/zoom-auth/{id}` - 更新认证信息
- `DELETE /api/zoom-auth/{id}` - 删除认证信息
- `POST /api/zoom-auth/{id}/refresh-token` - 手动刷新token
- `POST /api/zoom-auth/refresh-all` - 批量刷新token

### 前端界面

#### 1. 认证管理页面 (ZoomAuthManagement.js)
- **列表展示**: 表格形式展示所有认证信息
- **状态标识**: 彩色标签显示认证状态和token状态
- **操作按钮**: 编辑、删除、刷新token等操作
- **搜索功能**: 支持按账号名称搜索

#### 2. 创建/编辑表单
- **表单验证**: 完整的前端表单验证
- **认证类型选择**: 支持三种认证类型
- **密钥保护**: 编辑时不显示敏感信息

#### 3. 实时状态显示
- **Token状态**: 正常/即将过期/已过期
- **过期时间**: 显示具体的过期时间
- **刷新次数**: 显示历史刷新次数

## 数据库设计

### t_zoom_auth 表结构
```sql
CREATE TABLE t_zoom_auth (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    account_name VARCHAR(100) NOT NULL UNIQUE,
    zoom_account_id VARCHAR(100) NOT NULL,
    client_id VARCHAR(255) NOT NULL,
    client_secret VARCHAR(255) NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    token_type VARCHAR(50) DEFAULT 'Bearer',
    expires_in INT,
    token_issued_at TIMESTAMP NULL,
    token_expires_at TIMESTAMP NULL,
    scope VARCHAR(500),
    auth_type ENUM('OAUTH2', 'JWT', 'SERVER_TO_SERVER') NOT NULL,
    status ENUM('ACTIVE', 'EXPIRED', 'ERROR', 'DISABLED') NOT NULL,
    last_refresh_at TIMESTAMP NULL,
    refresh_count INT DEFAULT 0,
    error_message TEXT,
    webhook_secret_token VARCHAR(255),
    api_base_url VARCHAR(255) DEFAULT 'https://api.zoom.us/v2',
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 配置说明

### application.yml 配置
```yaml
zoom:
  token:
    refresh:
      enabled: true  # 是否启用定时刷新
      interval: 300000  # 刷新间隔（毫秒）
```

## 使用指南

### 1. 添加Zoom认证信息
1. 进入"Zoom认证管理"页面
2. 点击"新建认证"按钮
3. 填写认证信息：
   - 账号名称（自定义）
   - Zoom账号ID
   - 客户端ID和密钥
   - 选择认证类型
4. 保存后系统会自动获取token

### 2. 监控Token状态
- **正常**: 绿色标签，token有效
- **即将过期**: 橙色标签，5分钟内过期
- **已过期**: 红色标签，需要刷新

### 3. 刷新Token
- **自动刷新**: 系统每5分钟自动检查并刷新
- **手动刷新**: 点击单个账号的刷新按钮
- **批量刷新**: 点击"批量刷新Token"按钮

### 4. 错误处理
- 刷新失败时，状态会标记为"错误"
- 错误信息会显示在详情中
- 可以手动重试刷新

## 安全考虑

### 1. 敏感信息保护
- 客户端密钥加密存储
- 编辑时不显示完整密钥
- Token信息安全传输

### 2. 访问控制
- 需要管理员权限才能访问
- 操作日志记录
- 错误信息不暴露敏感数据

## 监控和维护

### 1. 定时任务监控
- 每5分钟执行一次检查
- 失败时记录错误日志
- 支持配置开关控制

### 2. 数据清理
- 定期清理过期的错误记录
- 保留刷新历史记录
- 支持手动清理功能

## 扩展功能

### 1. 统计信息
- 各状态认证数量统计
- Token刷新成功率统计
- 使用频率分析

### 2. 告警功能
- Token即将过期告警
- 刷新失败告警
- 邮件/短信通知

### 3. 备份恢复
- 认证信息导出
- 批量导入功能
- 配置备份

## 注意事项

1. **首次配置**: 需要在Zoom开发者平台创建应用并获取认证信息
2. **网络要求**: 需要能够访问Zoom API服务器
3. **时间同步**: 服务器时间需要准确，影响token过期判断
4. **权限配置**: 确保Zoom应用有足够的API权限
5. **监控告警**: 建议配置监控告警，及时发现问题

## 故障排除

### 常见问题
1. **Token刷新失败**: 检查客户端ID和密钥是否正确
2. **网络连接问题**: 检查服务器网络连接
3. **权限不足**: 检查Zoom应用权限配置
4. **时间不同步**: 检查服务器时间设置

### 日志查看
```bash
# 查看刷新日志
grep "刷新token" logs/application.log

# 查看错误日志
grep "ERROR" logs/application.log | grep "ZoomAuth"
```
