# Zoom Webhook 多账号配置指南

## 🎯 概述

ZoomBus现在支持多个Zoom主账号的webhook事件处理。每个Zoom主账号可以有独立的webhook endpoint，通过URL中的`accountId`参数（对应ZoomAuth表中的zoom_account_id）来区分不同的主账号。

## 📋 新增的Webhook Endpoints

### 1. 多账号Webhook事件接收端点

**URL格式**: `POST /api/webhooks/zoom/{accountId}`

**示例**:
- `POST /api/webhooks/zoom/abc123def456` - 账号ID为abc123def456的webhook
- `POST /api/webhooks/zoom/xyz789ghi012` - 账号ID为xyz789ghi012的webhook
- `POST /api/webhooks/zoom/school001main` - 账号ID为school001main的webhook

### 2. 多账号Webhook验证端点

**URL格式**: `POST /api/webhooks/zoom/{accountId}/validate`

**示例**:
- `POST /api/webhooks/zoom/abc123def456/validate`
- `POST /api/webhooks/zoom/xyz789ghi012/validate`

### 3. 兼容性端点（向后兼容）

- `POST /api/webhooks/zoom` - 使用默认账号处理
- `POST /api/webhooks/zoom/validate` - 使用默认账号验证

## 🔧 配置步骤

### 步骤1: 在Zoom开发者控制台配置Webhook

1. 登录到每个Zoom主账号的开发者控制台
2. 创建或编辑Webhook应用
3. 设置Event notification endpoint URL:
   ```
   https://your-domain.com/api/webhooks/zoom/{zoom_account_id}
   ```
   例如：`https://3c61cbcb7ae6.ngrok-free.app/api/webhooks/zoom/abc123def456`

4. 设置Webhook Secret Token（用于签名验证）

### 步骤2: 在ZoomBus中配置账号信息

确保在`t_zoom_auths`表中有对应的记录，包含：
- `zoom_account_id`: Zoom账号ID（与URL中的参数一致）
- `account_name`: 账号名称（用于显示和管理）
- `webhook_secret_token`: Webhook密钥（用于签名验证）

### 步骤3: 验证配置

使用Zoom开发者控制台的"Validate endpoint"功能验证配置。

## 📊 支持的事件类型

所有账号都支持以下事件类型：

### 会议事件
- `meeting.created` - 会议创建
- `meeting.updated` - 会议更新
- `meeting.deleted` - 会议删除
- `meeting.started` - 会议开始
- `meeting.ended` - 会议结束

### 用户事件
- `user.created` - 用户创建
- `user.updated` - 用户更新
- `user.deleted` - 用户删除

## 🔍 监控和管理

### 1. 查看特定账号的Webhook事件

```bash
GET /api/webhooks/events/account/{accountId}
```

### 2. 查看所有账号的统计信息

```bash
GET /api/webhooks/stats
```

返回示例：
```json
{
  "totalEvents": 150,
  "eventsByStatus": {
    "PROCESSED": 140,
    "FAILED": 8,
    "PENDING": 2
  },
  "eventsByAccount": {
    "abc123def456": 80,
    "xyz789ghi012": 45,
    "school001main": 25
  },
  "recentEvents": [...]
}
```

### 3. 查看按状态分组的事件

```bash
GET /api/webhooks/events/status/{status}
```

## 🛡️ 安全特性

### 1. 签名验证
每个账号使用独立的Webhook Secret Token进行HMAC-SHA256签名验证。

### 2. 账号验证
系统会验证URL中的`accountId`是否在ZoomAuth表中存在。

### 3. 错误处理
- 无效账号ID：返回404
- 签名验证失败：返回401
- 处理异常：返回500

## 📝 数据库变更

### 优化索引
在`t_webhook_events`表中优化：
- `idx_webhook_events_zoom_account_id` - 提高按账号ID查询的性能

### 使用现有字段
直接使用现有的`zoom_account_id`字段来区分不同账号，无需新增字段。

## 🔄 迁移指南

### 从单账号迁移到多账号

1. **现有数据处理**：
   - 现有的webhook事件记录已经有`zoom_account_id`字段
   - 原有的单账号endpoint仍然可用

2. **逐步迁移**：
   - 可以逐个账号迁移到新的多账号endpoint
   - 不需要一次性迁移所有账号

3. **配置更新**：
   - 在Zoom开发者控制台更新webhook URL，使用账号的zoom_account_id
   - 确保数据库中有对应的ZoomAuth记录

## 🧪 测试

### 1. 测试webhook接收

```bash
curl -X POST \
  https://your-domain.com/api/webhooks/zoom/abc123def456 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-signature" \
  -d '{
    "event": "meeting.created",
    "payload": {
      "object": {
        "id": "*********",
        "topic": "Test Meeting"
      }
    }
  }'
```

### 2. 测试webhook验证

```bash
curl -X POST \
  https://your-domain.com/api/webhooks/zoom/abc123def456/validate \
  -H "Content-Type: application/json" \
  -d '{
    "event": "endpoint.url_validation",
    "payload": {
      "plainToken": "test-token-123"
    }
  }'
```

## 📚 最佳实践

1. **账号ID管理**：使用清晰的Zoom账号ID，确保在ZoomAuth表中正确配置
2. **监控**：定期检查webhook事件的处理状态
3. **日志**：关注应用日志中的webhook处理信息
4. **测试**：在生产环境部署前充分测试所有账号的webhook配置
5. **安全**：确保每个账号都配置了正确的Webhook Secret Token

## 🚨 故障排除

### 常见问题

1. **404错误**：检查账号ID是否在ZoomAuth表中存在
2. **401错误**：检查Webhook Secret Token配置
3. **500错误**：查看应用日志了解具体错误信息

### 调试步骤

1. 检查数据库中的ZoomAuth配置，确认zoom_account_id字段
2. 验证Zoom开发者控制台的webhook配置
3. 查看应用日志
4. 使用测试工具验证endpoint

现在您可以为每个Zoom主账号配置独立的webhook endpoint了！
