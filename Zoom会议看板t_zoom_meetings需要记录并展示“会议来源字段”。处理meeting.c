Zoom会议看板t_zoom_meetings需要记录并展示“会议来源字段”。处理meeting.created事件，判断创建能找到PMI records的，能找到则会议来源是：PMI会议，如果不是PMI会议但是能通过meeting_id匹配上t_meetings则根据 t_meetings的会议来源，将t_zoom_meetings的会议类型标识为：“管理台”、“Zoom度端”。否则是“未知”

如果不是PMI类型会议，则不需要计费billing_mode为FREE不计费。 



private static final Map<MeetingStatus, Set<MeetingStatus>> VALID_TRANSITIONS = Map.of(
    MeetingStatus.WAITING, Set.of(
        MeetingStatus.STARTED,      // 会议开始
        MeetingStatus.ENDED,        // 直接结束
        MeetingStatus.DELETED,      // 删除会议
        MeetingStatus.ERROR         // 异常状态
    ),
    // ... 其他转换规则
);

本系统需要申请《软件著作证书》需要填充如下要素，请基于系统情况，简要生成如下信息，每项不超过200字
硬件环境
软件环境
软件开发环境或者开发工具
软件的运行平台或操作系统
软件运行支撑环境或支持软件
开发目的
面向领域/行业
编程语言
软件的主要功能
软件的技术特点


http://localhost:3000/meetings
