# PMI计费系统设计文档

## 📋 文档信息

| 项目 | 信息 |
|------|------|
| **功能模块** | PMI计费系统 |
| **版本** | v1.0 |
| **创建日期** | 2025-07-31 |
| **状态** | 设计阶段 |

---

## 🎯 系统概述

### 业务背景
ZoomBus系统中的PMI（个人会议室）归属于终端用户，用户可以通过PMI链接开启会议。为了实现商业化运营，系统需要支持两种计费模式：
1. **按时段计费(LONG)** - 用户购买时间窗口，在窗口期内可无限使用
2. **按时长计费(BY_TIME)** - 用户预充值时长，按实际使用时间扣费

### 核心价值
- **灵活计费**: 支持多种计费模式满足不同用户需求
- **精确计量**: 分钟级精确计费，确保公平合理
- **自动结算**: 自动化计费流程，减少人工干预
- **透明账单**: 详细的使用记录和流水明细

---

## 🏗️ 系统架构

### 计费模式架构
```
PMI计费系统
├── 按时段计费(LONG)
│   ├── PMI计划管理
│   ├── 时间窗口控制
│   └── 窗口状态监控
└── 按时长计费(BY_TIME)
    ├── 时长预充值
    ├── 实时计费监控
    ├── 自动扣费结算
    └── 超额使用处理
```

### 数据流架构
```
用户开启PMI → Zoom API → 会议实体记录 → Webhook监听 → 计费引擎 → 账单生成
```

---

## 📊 数据模型设计

### 1. PMI记录实体扩展 (t_pmi_records)
```sql
ALTER TABLE t_pmi_records ADD COLUMN (
    -- 计费模式
    billing_mode ENUM('LONG', 'BY_TIME') DEFAULT 'BY_TIME' COMMENT '计费模式：LONG-按时段，BY_TIME-按时长',
    
    -- 按时段计费相关字段
    current_window_id BIGINT NULL COMMENT '当前关联的时间窗口ID',
    window_expire_time DATETIME NULL COMMENT '窗口到期时间',
    
    -- 按时长计费相关字段
    total_minutes INT DEFAULT 0 COMMENT '总时长(分钟)',
    available_minutes INT DEFAULT 0 COMMENT '可用时长(分钟)',
    pending_deduct_minutes INT DEFAULT 0 COMMENT '待扣除时长(分钟)',
    overdraft_minutes INT DEFAULT 0 COMMENT '超额时长(分钟)',
    total_used_minutes INT DEFAULT 0 COMMENT '总使用时长(分钟)',
    
    -- 状态字段
    billing_status ENUM('ACTIVE', 'SUSPENDED', 'EXPIRED') DEFAULT 'ACTIVE' COMMENT '计费状态',
    last_billing_time DATETIME NULL COMMENT '最后计费时间',
    
    -- 审计字段
    billing_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. Zoom会议实体 (t_zoom_meetings)
```sql
CREATE TABLE t_zoom_meetings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- 会议基本信息
    pmi_record_id BIGINT NOT NULL COMMENT 'PMI记录ID',
    zoom_meeting_uuid VARCHAR(255) UNIQUE NOT NULL COMMENT 'Zoom会议UUID',
    zoom_meeting_id VARCHAR(50) NOT NULL COMMENT 'Zoom会议ID',
    
    -- 会议详情
    topic VARCHAR(255) COMMENT '会议主题',
    host_id VARCHAR(100) COMMENT '主持人ID',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration_minutes INT DEFAULT 0 COMMENT '会议时长(分钟)',
    
    -- 会议状态
    status ENUM('PENDING', 'USING', 'ENDED', 'SETTLED') DEFAULT 'PENDING' COMMENT '会议状态',
    
    -- 计费相关
    billing_mode ENUM('LONG', 'BY_TIME') COMMENT '计费模式',
    billed_minutes INT DEFAULT 0 COMMENT '已计费分钟数',
    is_settled BOOLEAN DEFAULT FALSE COMMENT '是否已结算',
    
    -- 审计字段
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_pmi_record_id (pmi_record_id),
    INDEX idx_zoom_uuid (zoom_meeting_uuid),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    
    -- 外键约束
    FOREIGN KEY (pmi_record_id) REFERENCES t_pmi_records(id) ON DELETE CASCADE
);
```

### 3. PMI时长流水记录 (t_pmi_billing_records)
```sql
CREATE TABLE t_pmi_billing_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- 关联信息
    pmi_record_id BIGINT NOT NULL COMMENT 'PMI记录ID',
    zoom_meeting_id BIGINT NULL COMMENT '关联的Zoom会议ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 交易信息
    transaction_type ENUM('RECHARGE', 'DEDUCT', 'REFUND', 'ADJUSTMENT') NOT NULL COMMENT '交易类型',
    amount_minutes INT NOT NULL COMMENT '时长变动(分钟)',
    balance_before INT NOT NULL COMMENT '变动前余额',
    balance_after INT NOT NULL COMMENT '变动后余额',
    
    -- 业务信息
    description VARCHAR(500) COMMENT '交易描述',
    billing_period_start DATETIME COMMENT '计费周期开始时间',
    billing_period_end DATETIME COMMENT '计费周期结束时间',
    
    -- 状态信息
    status ENUM('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED') DEFAULT 'PENDING',
    
    -- 审计字段
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) COMMENT '创建人',
    
    -- 索引
    INDEX idx_pmi_record_id (pmi_record_id),
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (pmi_record_id) REFERENCES t_pmi_records(id),
    FOREIGN KEY (zoom_meeting_id) REFERENCES t_zoom_meetings(id),
    FOREIGN KEY (user_id) REFERENCES t_users(id)
);
```

### 4. ZoomUser扩展 (t_zoom_users)
```sql
ALTER TABLE t_zoom_users ADD COLUMN (
    -- PMI相关字段
    original_pmi VARCHAR(50) COMMENT '账号原始默认PMI',
    current_pmi VARCHAR(50) COMMENT '当前使用的PMI',

    -- 使用状态
    usage_status ENUM('AVAILABLE', 'IN_USE', 'MAINTENANCE') DEFAULT 'AVAILABLE' COMMENT '使用状态',
    current_meeting_id BIGINT NULL COMMENT '当前会议ID',

    -- 使用记录
    last_used_time DATETIME NULL COMMENT '最后使用时间',
    total_usage_count INT DEFAULT 0 COMMENT '总使用次数',
    total_usage_minutes INT DEFAULT 0 COMMENT '总使用时长(分钟)',

    -- 审计字段
    pmi_updated_at DATETIME NULL COMMENT 'PMI最后更新时间',

    -- 索引
    INDEX idx_usage_status (usage_status),
    INDEX idx_original_pmi (original_pmi),
    INDEX idx_current_pmi (current_pmi),

    -- 外键约束
    FOREIGN KEY (current_meeting_id) REFERENCES t_zoom_meetings(id) ON DELETE SET NULL
);
```

### 5. PMI计费配置 (t_pmi_billing_config)
```sql
CREATE TABLE t_pmi_billing_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,

    -- 配置信息
    config_name VARCHAR(100) UNIQUE NOT NULL COMMENT '配置名称',
    billing_unit_minutes INT DEFAULT 1 COMMENT '计费单位(分钟)',
    min_billing_minutes INT DEFAULT 1 COMMENT '最小计费时长',
    grace_period_minutes INT DEFAULT 0 COMMENT '免费时长(分钟)',

    -- 费率配置
    rate_per_minute DECIMAL(10,4) DEFAULT 0.0000 COMMENT '每分钟费率',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '货币单位',

    -- 状态
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    effective_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',

    -- 审计字段
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

---

## 🔄 业务流程设计

### 1. PMI计费模式切换流程
```mermaid
graph TD
    A[PMI创建] --> B[默认BY_TIME模式]
    B --> C{开启时间窗口?}
    C -->|是| D[切换到LONG模式]
    C -->|否| E[保持BY_TIME模式]
    D --> F[记录窗口ID和到期时间]
    F --> G[窗口期内无限使用]
    G --> H{窗口到期?}
    H -->|是| I[切换回BY_TIME模式]
    H -->|否| G
    I --> J[清空窗口信息]
    E --> K[按时长计费]
```

### 2. PMI开启和会议记录流程
```mermaid
graph TD
    A[用户点击开启PMI] --> B[调用Zoom API创建会议]
    B --> C[获取会议详情]
    C --> D[创建t_zoom_meetings记录]
    D --> E[状态设为PENDING]
    E --> F[返回主持人链接给用户]
    F --> G[用户开始会议]
    G --> H[Zoom发送meeting.started webhook]
    H --> I[根据UUID查找会议记录]
    I --> J{找到记录?}
    J -->|是| K{当前状态?}
    J -->|否| L[创建新记录]
    K -->|PENDING| M[更新为USING]
    K -->|USING| N[保持USING状态]
    L --> O[状态设为USING]
    M --> P[开始计费监控]
    N --> P
    O --> P
```

### 3. 按时长计费流程（更新）
```mermaid
graph TD
    A[会议状态USING] --> B[每分钟检查一次]
    B --> C[增加1分钟待扣时长]
    C --> D[更新PMI待扣时长]
    D --> E[更新会议已计费分钟数]
    E --> F{会议结束?}
    F -->|否| B
    F -->|是| G[接收meeting.ended webhook]
    G --> H[更新会议状态为ENDED]
    H --> I[执行结算流程]
    I --> J{可用时长足够?}
    J -->|是| K[扣减可用时长]
    J -->|否| L[记录超额到超额时长]
    K --> M[清零待扣时长]
    L --> N[待扣时长清零]
    M --> O[生成流水记录]
    N --> O
    O --> P[会议状态更新为SETTLED]
```

### 4. PMI充值流程
```mermaid
graph TD
    A[用户点击时长充值] --> B[打开充值弹窗]
    B --> C[显示当前状态]
    C --> D[用户选择充值金额]
    D --> E[计算充值预览]
    E --> F[显示预览结果]
    F --> G[用户确认充值]
    G --> H[执行充值逻辑]
    H --> I{存在超额时长?}
    I -->|是| J[优先结清超额时长]
    I -->|否| K[检查待扣时长]
    J --> L{还有剩余充值?}
    L -->|是| K
    L -->|否| M[充值完成]
    K --> N{存在待扣时长?}
    N -->|是| O[结清待扣时长]
    N -->|否| P[增加可用时长]
    O --> Q{还有剩余充值?}
    Q -->|是| P
    Q -->|否| M
    P --> R[更新总时长和可用时长]
    R --> M
    M --> S[生成流水记录]
    S --> T[刷新页面数据]
```

### 5. PMI开启验证流程（更新）
```mermaid
graph TD
    A[用户尝试开启PMI] --> B[验证PMI状态]
    B --> C{计费模式?}
    C -->|LONG| D[检查时间窗口]
    C -->|BY_TIME| E[检查超额时长]
    D --> F{窗口有效?}
    F -->|是| G[允许开启]
    F -->|否| H[拒绝开启-窗口过期]
    E --> I{超额时长>0?}
    I -->|是| J[拒绝开启-需结清超额]
    I -->|否| K[检查可用时长]
    K --> L{可用时长>0?}
    L -->|是| G
    L -->|否| M[拒绝开启-余额不足]
    G --> N[创建会议记录]
    N --> O[分配ZoomUser账号]
    O --> P[更新ZoomUser PMI]
    P --> Q[开始计费监控]
    H --> R[显示错误提示]
    J --> R
    M --> R
```

### 6. ZoomUser PMI管理流程
```mermaid
graph TD
    A[系统启动] --> B[检查ZoomUser原始PMI]
    B --> C{PMI是否为空?}
    C -->|是| D[调用Zoom API获取PMI]
    C -->|否| E[PMI已存在，跳过]
    D --> F[保存原始PMI]
    F --> G[设置当前PMI为原始PMI]
    G --> H[PMI初始化完成]
    E --> H

    I[会议开始] --> J[查找可用ZoomUser]
    J --> K[分配ZoomUser给会议]
    K --> L[更新ZoomUser PMI为目标PMI]
    L --> M[设置状态为使用中]
    M --> N[记录当前会议ID]

    O[会议结束] --> P[恢复ZoomUser原始PMI]
    P --> Q[设置状态为可用]
    Q --> R[清空当前会议ID]
    R --> S[ZoomUser释放完成]
```

### 7. ZoomUser状态管理流程
```mermaid
graph TD
    A[ZoomUser创建] --> B[状态: AVAILABLE]
    B --> C{需要分配?}
    C -->|会议使用| D[状态: IN_USE]
    C -->|PMI操作| E[状态: IN_USE]
    C -->|否| F[保持AVAILABLE]
    D --> G[执行会议任务]
    E --> H[执行PMI操作]
    G --> I{会议结束?}
    H --> J{操作完成?}
    I -->|是| K[状态: AVAILABLE]
    J -->|是| K
    I -->|否| G
    J -->|否| H
    K --> L[等待下次分配]

    M[管理员操作] --> N{设置维护?}
    N -->|是| O[状态: MAINTENANCE]
    N -->|否| P[状态: AVAILABLE]
    O --> Q[维护完成]
    Q --> P

    F --> C
    L --> C
    P --> C
```

### 8. PMI创建和修改流程
```mermaid
graph TD
    A[PMI创建/修改请求] --> B[分配可用ZoomUser]
    B --> C{分配成功?}
    C -->|否| D[返回错误: 无可用账号]
    C -->|是| E[设置ZoomUser状态为IN_USE]
    E --> F[调用Zoom API设置PMI]
    F --> G{API调用成功?}
    G -->|是| H[保存PMI记录到数据库]
    G -->|否| I[记录错误信息]
    H --> J[记录操作日志]
    I --> J
    J --> K[恢复ZoomUser原始PMI]
    K --> L[释放ZoomUser状态为AVAILABLE]
    L --> M{操作成功?}
    M -->|是| N[返回成功结果]
    M -->|否| O[返回失败结果]

    style K fill:#f9f,stroke:#333,stroke-width:2px
    style L fill:#f9f,stroke:#333,stroke-width:2px
```

### 9. PMI操作的ZoomUser生命周期
```mermaid
graph TD
    A[PMI操作开始] --> B[查找AVAILABLE状态的ZoomUser]
    B --> C{找到可用账号?}
    C -->|否| D[抛出异常: 无可用账号]
    C -->|是| E[更新状态为IN_USE]
    E --> F[设置currentMeetingId为-1]
    F --> G[记录操作类型和时间]
    G --> H[执行PMI相关操作]
    H --> I[PMI操作完成]
    I --> J[检查当前PMI是否为原始PMI]
    J --> K{需要恢复原始PMI?}
    K -->|是| L[调用Zoom API恢复原始PMI]
    K -->|否| M[跳过PMI恢复]
    L --> N{恢复成功?}
    N -->|否| O[记录警告日志]
    N -->|是| P[更新currentPmi为originalPmi]
    O --> Q[强制更新本地状态]
    P --> Q
    M --> Q
    Q --> R[设置状态为AVAILABLE]
    R --> S[清空currentMeetingId]
    S --> T[记录释放日志]
    T --> U[ZoomUser释放完成]

    style L fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style Q fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style R fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
```

---

## 🔧 核心功能实现

### 1. PMI计费模式管理

#### 1.1 计费模式切换服务
```java
@Service
public class PmiBillingModeService {
    
    /**
     * 开启时间窗口，切换到按时段计费
     */
    public void switchToLongBilling(Long pmiRecordId, Long windowId, LocalDateTime expireTime) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));
        
        pmiRecord.setBillingMode(BillingMode.LONG);
        pmiRecord.setCurrentWindowId(windowId);
        pmiRecord.setWindowExpireTime(expireTime);
        pmiRecord.setBillingStatus(BillingStatus.ACTIVE);
        
        pmiRecordRepository.save(pmiRecord);
        
        log.info("PMI {} 切换到按时段计费模式，窗口ID: {}, 到期时间: {}", 
                pmiRecordId, windowId, expireTime);
    }
    
    /**
     * 关闭时间窗口，切换到按时长计费
     */
    public void switchToTimeBilling(Long pmiRecordId) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));
        
        pmiRecord.setBillingMode(BillingMode.BY_TIME);
        pmiRecord.setCurrentWindowId(null);
        pmiRecord.setWindowExpireTime(null);
        
        pmiRecordRepository.save(pmiRecord);
        
        log.info("PMI {} 切换到按时长计费模式", pmiRecordId);
    }
}
```

#### 1.2 窗口到期检查调度器
```java
@Component
public class WindowExpiryScheduler {
    
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkExpiredWindows() {
        LocalDateTime now = LocalDateTime.now();
        
        List<PmiRecord> expiredPmis = pmiRecordRepository
            .findByBillingModeAndWindowExpireTimeBefore(BillingMode.LONG, now);
        
        for (PmiRecord pmi : expiredPmis) {
            pmiBillingModeService.switchToTimeBilling(pmi.getId());
            log.info("PMI {} 窗口已到期，自动切换到按时长计费", pmi.getId());
        }
    }
}
```

### 2. 会议记录管理

#### 2.1 会议记录服务
```java
@Service
public class ZoomMeetingService {
    
    /**
     * 创建会议记录（PMI开启时）- 增强版
     */
    public ZoomMeeting createMeetingRecord(Long pmiRecordId, ZoomMeetingDetail zoomDetail) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

        // 创建会议记录
        ZoomMeeting meeting = new ZoomMeeting();
        meeting.setPmiRecordId(pmiRecordId);
        meeting.setZoomMeetingUuid(zoomDetail.getUuid());
        meeting.setZoomMeetingId(zoomDetail.getId());
        meeting.setTopic(zoomDetail.getTopic());
        meeting.setHostId(zoomDetail.getHostId());
        meeting.setStartTime(zoomDetail.getStartTime());
        meeting.setStatus(MeetingStatus.PENDING);
        meeting.setBillingMode(pmiRecord.getBillingMode());

        meeting = zoomMeetingRepository.save(meeting);

        // 分配ZoomUser账号
        try {
            ZoomUser assignedUser = zoomUserPmiService.assignZoomUserForMeeting(
                meeting.getId(), pmiRecord.getPmiNumber());

            // 更新会议记录中的ZoomUser信息
            meeting.setAssignedZoomUserId(assignedUser.getId());
            meeting.setAssignedZoomUserEmail(assignedUser.getEmail());
            zoomMeetingRepository.save(meeting);

            log.info("会议 {} 已分配ZoomUser: {}", meeting.getId(), assignedUser.getEmail());

        } catch (Exception e) {
            log.error("为会议 {} 分配ZoomUser失败", meeting.getId(), e);
            // 分配失败不影响会议记录创建，但需要记录错误
            meeting.setAssignmentError("ZoomUser分配失败: " + e.getMessage());
            zoomMeetingRepository.save(meeting);
        }

        return meeting;
    }
    
    /**
     * 处理会议开始事件
     */
    public void handleMeetingStarted(String meetingUuid) {
        Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository
            .findByZoomMeetingUuid(meetingUuid);

        if (meetingOpt.isPresent()) {
            ZoomMeeting meeting = meetingOpt.get();
            if (meeting.getStatus() == MeetingStatus.PENDING) {
                meeting.setStatus(MeetingStatus.USING);
                meeting.setStartTime(LocalDateTime.now());
                zoomMeetingRepository.save(meeting);

                // 开始计费监控
                if (meeting.getBillingMode() == BillingMode.BY_TIME) {
                    billingMonitorService.startBillingMonitor(meeting.getId());
                }
            }
        } else {
            // 创建新记录
            createMeetingRecordFromWebhook(meetingUuid);
        }
    }

    /**
     * 处理会议结束事件
     */
    public void handleMeetingEnded(String meetingUuid) {
        Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository
            .findByZoomMeetingUuid(meetingUuid);

        if (meetingOpt.isPresent()) {
            ZoomMeeting meeting = meetingOpt.get();

            // 更新会议状态
            meeting.setStatus(MeetingStatus.ENDED);
            meeting.setEndTime(LocalDateTime.now());

            // 计算会议时长
            if (meeting.getStartTime() != null) {
                Duration duration = Duration.between(meeting.getStartTime(), meeting.getEndTime());
                meeting.setDurationMinutes((int) duration.toMinutes());
            }

            zoomMeetingRepository.save(meeting);

            // 停止计费监控
            billingMonitorService.stopBillingMonitor(meeting.getId());

            // 执行结算
            try {
                meetingSettlementService.settleMeeting(meeting.getId());
            } catch (Exception e) {
                log.error("会议 {} 结算失败", meeting.getId(), e);
            }

            // 释放ZoomUser账号
            try {
                zoomUserPmiService.releaseZoomUser(meeting.getId());
            } catch (Exception e) {
                log.error("释放会议 {} 的ZoomUser失败", meeting.getId(), e);
            }

            log.info("会议 {} 已结束并完成处理", meeting.getId());
        }
    }
}
```

### 3. 实时计费监控

#### 3.1 计费监控服务
```java
@Service
public class BillingMonitorService {
    
    private final Map<Long, ScheduledFuture<?>> billingTasks = new ConcurrentHashMap<>();
    
    /**
     * 开始计费监控
     */
    public void startBillingMonitor(Long meetingId) {
        ScheduledFuture<?> task = taskScheduler.scheduleAtFixedRate(
            () -> processBillingTick(meetingId),
            Duration.ofMinutes(1)
        );
        
        billingTasks.put(meetingId, task);
        log.info("开始监控会议 {} 的计费", meetingId);
    }
    
    /**
     * 停止计费监控
     */
    public void stopBillingMonitor(Long meetingId) {
        ScheduledFuture<?> task = billingTasks.remove(meetingId);
        if (task != null) {
            task.cancel(false);
            log.info("停止监控会议 {} 的计费", meetingId);
        }
    }
    
    /**
     * 处理每分钟计费
     */
    private void processBillingTick(Long meetingId) {
        try {
            ZoomMeeting meeting = zoomMeetingRepository.findById(meetingId)
                .orElse(null);
            
            if (meeting == null || meeting.getStatus() != MeetingStatus.USING) {
                stopBillingMonitor(meetingId);
                return;
            }
            
            // 增加会议计费分钟数
            meeting.setBilledMinutes(meeting.getBilledMinutes() + 1);
            zoomMeetingRepository.save(meeting);
            
            // 增加PMI待扣时长
            PmiRecord pmiRecord = pmiRecordRepository.findById(meeting.getPmiRecordId())
                .orElse(null);
            if (pmiRecord != null) {
                pmiRecord.setPendingDeductMinutes(pmiRecord.getPendingDeductMinutes() + 1);
                pmiRecordRepository.save(pmiRecord);
            }
            
            log.debug("会议 {} 计费 +1 分钟，总计费: {} 分钟", 
                    meetingId, meeting.getBilledMinutes());
            
        } catch (Exception e) {
            log.error("处理会议 {} 计费时发生错误", meetingId, e);
        }
    }
}
```

---

## 📱 前端界面设计

### 1. Zoom会议看板页面

#### 1.1 页面布局（增强版）
```jsx
const ZoomMeetingDashboard = () => {
    const [activeTab, setActiveTab] = useState('active');
    const [meetings, setMeetings] = useState([]);
    const [loading, setLoading] = useState(false);
    const [filters, setFilters] = useState({
        billingMode: null,
        keyword: '',
        startDate: null,
        endDate: null
    });
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    // 实时刷新定时器
    useEffect(() => {
        if (activeTab === 'active') {
            const interval = setInterval(() => {
                loadActiveMeetings();
            }, 30000); // 30秒刷新一次
            return () => clearInterval(interval);
        }
    }, [activeTab]);

    const loadActiveMeetings = async () => {
        try {
            setLoading(true);
            const response = await meetingApi.getActiveMeetings({
                page: pagination.current - 1,
                size: pagination.pageSize,
                billingMode: filters.billingMode,
                keyword: filters.keyword
            });

            if (response.success) {
                setMeetings(response.data.content);
                setPagination(prev => ({
                    ...prev,
                    total: response.data.totalElements
                }));
            }
        } catch (error) {
            message.error('加载活跃会议失败');
        } finally {
            setLoading(false);
        }
    };

    const loadHistoryMeetings = async () => {
        try {
            setLoading(true);
            const response = await meetingApi.getHistoryMeetings({
                page: pagination.current - 1,
                size: pagination.pageSize,
                ...filters
            });

            if (response.success) {
                setMeetings(response.data.content);
                setPagination(prev => ({
                    ...prev,
                    total: response.data.totalElements
                }));
            }
        } catch (error) {
            message.error('加载历史会议失败');
        } finally {
            setLoading(false);
        }
    };

    const handleTabChange = (key) => {
        setActiveTab(key);
        setPagination(prev => ({ ...prev, current: 1 }));

        if (key === 'active') {
            loadActiveMeetings();
        } else {
            loadHistoryMeetings();
        }
    };

    const handleEndMeeting = async (meeting) => {
        Modal.confirm({
            title: '确认结束会议',
            content: `确定要结束PMI ${meeting.pmiNumber} 的会议吗？这将同时结束Zoom会议并完成计费结算。`,
            okText: '确认结束',
            cancelText: '取消',
            okType: 'danger',
            onOk: async () => {
                try {
                    const response = await meetingApi.endMeeting(meeting.id);
                    if (response.success) {
                        message.success('会议已成功结束并完成结算');
                        loadActiveMeetings(); // 刷新列表

                        // 显示结算结果
                        Modal.info({
                            title: '会议结算完成',
                            content: (
                                <div>
                                    <p>PMI号码: {response.data.pmiNumber}</p>
                                    <p>会议时长: {response.data.totalDuration} 分钟</p>
                                    <p>计费时长: {response.data.billedMinutes} 分钟</p>
                                    {response.data.finalCost && (
                                        <p>费用: ¥{response.data.finalCost}</p>
                                    )}
                                </div>
                            )
                        });
                    } else {
                        message.error(response.message || '结束会议失败');
                    }
                } catch (error) {
                    message.error('结束会议失败');
                }
            }
        });
    };

    const tabItems = [
        {
            key: 'active',
            label: (
                <span>
                    <PlayCircleOutlined />
                    进行中会议
                </span>
            ),
            children: (
                <ActiveMeetingsTab
                    meetings={meetings}
                    loading={loading}
                    filters={filters}
                    onFiltersChange={setFilters}
                    onEndMeeting={handleEndMeeting}
                    onRefresh={loadActiveMeetings}
                />
            )
        },
        {
            key: 'history',
            label: (
                <span>
                    <HistoryOutlined />
                    历史会议
                </span>
            ),
            children: (
                <HistoryMeetingsTab
                    meetings={meetings}
                    loading={loading}
                    filters={filters}
                    onFiltersChange={setFilters}
                    onRefresh={loadHistoryMeetings}
                />
            )
        }
    ];

    return (
        <Card title="Zoom会议看板">
            <Tabs
                activeKey={activeTab}
                onChange={handleTabChange}
                items={tabItems}
                tabBarExtraContent={
                    <Space>
                        <Button
                            icon={<ReloadOutlined />}
                            onClick={activeTab === 'active' ? loadActiveMeetings : loadHistoryMeetings}
                        >
                            刷新
                        </Button>
                        <Button
                            icon={<DownloadOutlined />}
                            onClick={() => exportMeetings(activeTab)}
                        >
                            导出
                        </Button>
                    </Space>
                }
            />
        </Card>
    );
};
```

#### 1.2 活跃会议标签页组件
```jsx
const ActiveMeetingsTab = ({ meetings, loading, filters, onFiltersChange, onEndMeeting, onRefresh }) => {
    return (
        <div>
            {/* 筛选器 */}
            <Card size="small" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                    <Col span={6}>
                        <Select
                            placeholder="计费模式"
                            value={filters.billingMode}
                            onChange={(value) => onFiltersChange({ ...filters, billingMode: value })}
                            style={{ width: '100%' }}
                            allowClear
                        >
                            <Option value="LONG">按时段</Option>
                            <Option value="BY_TIME">按时长</Option>
                        </Select>
                    </Col>
                    <Col span={8}>
                        <Input.Search
                            placeholder="搜索PMI号码或会议主题"
                            value={filters.keyword}
                            onChange={(e) => onFiltersChange({ ...filters, keyword: e.target.value })}
                            onSearch={onRefresh}
                            allowClear
                        />
                    </Col>
                    <Col span={4}>
                        <Button type="primary" onClick={onRefresh} block>
                            查询
                        </Button>
                    </Col>
                </Row>
            </Card>

            {/* 会议列表 */}
            <Table
                dataSource={meetings}
                loading={loading}
                columns={getActiveMeetingColumns(onEndMeeting)}
                rowKey="id"
                pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                        `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }}
                scroll={{ x: 1200 }}
            />
        </div>
    );
};

const HistoryMeetingsTab = ({ meetings, loading, filters, onFiltersChange, onRefresh }) => {
    return (
        <div>
            {/* 高级筛选器 */}
            <Card size="small" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                    <Col span={4}>
                        <Select
                            placeholder="计费模式"
                            value={filters.billingMode}
                            onChange={(value) => onFiltersChange({ ...filters, billingMode: value })}
                            style={{ width: '100%' }}
                            allowClear
                        >
                            <Option value="LONG">按时段</Option>
                            <Option value="BY_TIME">按时长</Option>
                        </Select>
                    </Col>
                    <Col span={6}>
                        <Input.Search
                            placeholder="搜索PMI号码或会议主题"
                            value={filters.keyword}
                            onChange={(e) => onFiltersChange({ ...filters, keyword: e.target.value })}
                            allowClear
                        />
                    </Col>
                    <Col span={4}>
                        <DatePicker
                            placeholder="开始日期"
                            value={filters.startDate}
                            onChange={(date) => onFiltersChange({ ...filters, startDate: date })}
                            style={{ width: '100%' }}
                        />
                    </Col>
                    <Col span={4}>
                        <DatePicker
                            placeholder="结束日期"
                            value={filters.endDate}
                            onChange={(date) => onFiltersChange({ ...filters, endDate: date })}
                            style={{ width: '100%' }}
                        />
                    </Col>
                    <Col span={3}>
                        <Button type="primary" onClick={onRefresh} block>
                            查询
                        </Button>
                    </Col>
                    <Col span={3}>
                        <Button onClick={() => onFiltersChange({
                            billingMode: null,
                            keyword: '',
                            startDate: null,
                            endDate: null
                        })} block>
                            重置
                        </Button>
                    </Col>
                </Row>
            </Card>

            {/* 历史会议列表 */}
            <Table
                dataSource={meetings}
                loading={loading}
                columns={getHistoryMeetingColumns()}
                rowKey="id"
                pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                        `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }}
                scroll={{ x: 1400 }}
            />
        </div>
    );
};
```

#### 1.3 表格列定义（增强版）
```jsx
// 活跃会议表格列
const getActiveMeetingColumns = (onEndMeeting) => [
    {
        title: 'PMI号码',
        dataIndex: 'pmiNumber',
        key: 'pmiNumber',
        width: 120,
        render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
        title: '用户',
        dataIndex: 'pmiUserName',
        key: 'pmiUserName',
        width: 100,
        ellipsis: true
    },
    {
        title: '会议主题',
        dataIndex: 'topic',
        key: 'topic',
        width: 200,
        ellipsis: true,
        render: (text) => text || '无主题'
    },
    {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
        width: 150,
        render: (time) => time ? dayjs(time).format('MM-DD HH:mm:ss') : '-'
    },
    {
        title: '实时时长',
        dataIndex: 'currentDuration',
        key: 'currentDuration',
        width: 120,
        render: (_, record) => (
            <RealTimeCounter
                startTime={record.startTime}
                status={record.status}
            />
        )
    },
    {
        title: '计费模式',
        dataIndex: 'billingMode',
        key: 'billingMode',
        width: 100,
        render: (mode) => (
            <Tag color={mode === 'LONG' ? 'green' : 'orange'}>
                {mode === 'LONG' ? '按时段' : '按时长'}
            </Tag>
        )
    },
    {
        title: '计费状态',
        dataIndex: 'billingStatusText',
        key: 'billingStatus',
        width: 120,
        render: (text, record) => (
            <div>
                <div>{text}</div>
                {record.realTimeBilling && (
                    <Text type="secondary" style={{ fontSize: 12 }}>
                        已计费: {record.billedMinutes}分钟
                    </Text>
                )}
            </div>
        )
    },
    {
        title: '会议状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        render: (status) => {
            const statusConfig = {
                'PENDING': { color: 'processing', text: '待开启' },
                'USING': { color: 'success', text: '进行中' },
                'ENDED': { color: 'default', text: '已结束' },
                'SETTLED': { color: 'success', text: '已结算' }
            };
            const config = statusConfig[status] || { color: 'default', text: status };
            return <Badge status={config.color} text={config.text} />;
        }
    },
    {
        title: '预估费用',
        dataIndex: 'estimatedCost',
        key: 'estimatedCost',
        width: 100,
        render: (cost, record) => {
            if (record.billingMode === 'LONG') {
                return <Text type="secondary">包时段</Text>;
            }
            return cost ? `¥${cost}` : '¥0.00';
        }
    },
    {
        title: '操作',
        key: 'action',
        width: 150,
        fixed: 'right',
        render: (_, record) => (
            <Space>
                <Button
                    size="small"
                    onClick={() => viewMeetingBillingDetail(record)}
                >
                    计费详情
                </Button>
                {record.status === 'USING' && (
                    <Button
                        size="small"
                        danger
                        onClick={() => onEndMeeting(record)}
                    >
                        结束会议
                    </Button>
                )}
            </Space>
        )
    }
];

// 历史会议表格列
const getHistoryMeetingColumns = () => [
    {
        title: 'PMI号码',
        dataIndex: 'pmiNumber',
        key: 'pmiNumber',
        width: 120,
        render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
        title: '用户',
        dataIndex: 'pmiUserName',
        key: 'pmiUserName',
        width: 100,
        ellipsis: true
    },
    {
        title: '会议主题',
        dataIndex: 'topic',
        key: 'topic',
        width: 200,
        ellipsis: true,
        render: (text) => text || '无主题'
    },
    {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
        width: 150,
        render: (time) => time ? dayjs(time).format('MM-DD HH:mm') : '-'
    },
    {
        title: '结束时间',
        dataIndex: 'endTime',
        key: 'endTime',
        width: 150,
        render: (time) => time ? dayjs(time).format('MM-DD HH:mm') : '-'
    },
    {
        title: '会议时长',
        dataIndex: 'durationMinutes',
        key: 'duration',
        width: 100,
        render: (minutes) => minutes ? `${minutes}分钟` : '-'
    },
    {
        title: '计费模式',
        dataIndex: 'billingMode',
        key: 'billingMode',
        width: 100,
        render: (mode) => (
            <Tag color={mode === 'LONG' ? 'green' : 'orange'}>
                {mode === 'LONG' ? '按时段' : '按时长'}
            </Tag>
        )
    },
    {
        title: '计费时长',
        dataIndex: 'billedMinutes',
        key: 'billedMinutes',
        width: 100,
        render: (minutes, record) => {
            if (record.billingMode === 'LONG') {
                return <Text type="secondary">包时段</Text>;
            }
            return `${minutes || 0}分钟`;
        }
    },
    {
        title: '费用',
        dataIndex: 'estimatedCost',
        key: 'estimatedCost',
        width: 100,
        render: (cost, record) => {
            if (record.billingMode === 'LONG') {
                return <Text type="secondary">包时段</Text>;
            }
            return cost ? `¥${cost}` : '¥0.00';
        }
    },
    {
        title: '结算状态',
        dataIndex: 'isSettled',
        key: 'isSettled',
        width: 100,
        render: (settled) => (
            <Badge
                status={settled ? 'success' : 'processing'}
                text={settled ? '已结算' : '待结算'}
            />
        )
    },
    {
        title: '操作',
        key: 'action',
        width: 120,
        fixed: 'right',
        render: (_, record) => (
            <Space>
                <Button
                    size="small"
                    onClick={() => viewMeetingBillingDetail(record)}
                >
                    计费详情
                </Button>
                {!record.isSettled && (
                    <Button
                        size="small"
                        type="primary"
                        onClick={() => settleMeeting(record)}
                    >
                        结算
                    </Button>
                )}
            </Space>
        )
    }
];
```

#### 1.4 实时计时器组件
```jsx
const RealTimeCounter = ({ startTime, status }) => {
    const [duration, setDuration] = useState(0);

    useEffect(() => {
        if (!startTime || status !== 'USING') {
            return;
        }

        const updateDuration = () => {
            const now = dayjs();
            const start = dayjs(startTime);
            const minutes = now.diff(start, 'minute');
            setDuration(minutes);
        };

        // 立即更新一次
        updateDuration();

        // 每分钟更新一次
        const interval = setInterval(updateDuration, 60000);

        return () => clearInterval(interval);
    }, [startTime, status]);

    const formatDuration = (minutes) => {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;

        if (hours > 0) {
            return `${hours}小时${mins}分钟`;
        }
        return `${mins}分钟`;
    };

    if (status === 'USING') {
        return (
            <div>
                <Text strong style={{ color: '#52c41a' }}>
                    {formatDuration(duration)}
                </Text>
                <div style={{ fontSize: 12, color: '#999' }}>
                    <ClockCircleOutlined /> 实时计时
                </div>
            </div>
        );
    }

    return <Text type="secondary">-</Text>;
};
```

#### 1.5 会议计费详情弹窗
```jsx
const MeetingBillingDetailModal = ({ visible, onCancel, meetingId }) => {
    const [detail, setDetail] = useState(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (visible && meetingId) {
            loadBillingDetail();
        }
    }, [visible, meetingId]);

    const loadBillingDetail = async () => {
        try {
            setLoading(true);
            const response = await meetingApi.getBillingDetail(meetingId);
            if (response.success) {
                setDetail(response.data);
            }
        } catch (error) {
            message.error('加载计费详情失败');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal
            title={`会议计费详情 - PMI ${detail?.pmiNumber || ''}`}
            open={visible}
            onCancel={onCancel}
            footer={[
                <Button key="close" onClick={onCancel}>
                    关闭
                </Button>
            ]}
            width={900}
            loading={loading}
        >
            {detail && (
                <Tabs defaultActiveKey="overview">
                    <TabPane tab="概览信息" key="overview">
                        <Row gutter={16}>
                            <Col span={12}>
                                <Card title="会议信息" size="small">
                                    <Descriptions column={1} size="small">
                                        <Descriptions.Item label="PMI号码">
                                            {detail.pmiNumber}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="计费模式">
                                            <Tag color={detail.billingMode === 'LONG' ? 'green' : 'orange'}>
                                                {detail.billingMode === 'LONG' ? '按时段计费' : '按时长计费'}
                                            </Tag>
                                        </Descriptions.Item>
                                        <Descriptions.Item label="开始时间">
                                            {dayjs(detail.startTime).format('YYYY-MM-DD HH:mm:ss')}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="结束时间">
                                            {detail.endTime ?
                                                dayjs(detail.endTime).format('YYYY-MM-DD HH:mm:ss') :
                                                '进行中'
                                            }
                                        </Descriptions.Item>
                                        <Descriptions.Item label="会议时长">
                                            {detail.currentDuration} 分钟
                                        </Descriptions.Item>
                                    </Descriptions>
                                </Card>
                            </Col>
                            <Col span={12}>
                                <Card title="计费信息" size="small">
                                    <Descriptions column={1} size="small">
                                        <Descriptions.Item label="已计费时长">
                                            <Text strong style={{ color: '#1890ff' }}>
                                                {detail.billedMinutes} 分钟
                                            </Text>
                                        </Descriptions.Item>
                                        <Descriptions.Item label="预估费用">
                                            {detail.billingMode === 'LONG' ? (
                                                <Text type="secondary">包时段</Text>
                                            ) : (
                                                <Text strong style={{ color: '#52c41a' }}>
                                                    ¥{detail.estimatedCost}
                                                </Text>
                                            )}
                                        </Descriptions.Item>
                                        <Descriptions.Item label="结算状态">
                                            <Badge
                                                status={detail.isSettled ? 'success' : 'processing'}
                                                text={detail.isSettled ? '已结算' : '待结算'}
                                            />
                                        </Descriptions.Item>
                                        {detail.realTimeBilling && (
                                            <Descriptions.Item label="计费状态">
                                                <Badge status="success" text="实时计费中" />
                                            </Descriptions.Item>
                                        )}
                                    </Descriptions>
                                </Card>
                            </Col>
                        </Row>
                    </TabPane>

                    <TabPane tab="计费记录" key="billing">
                        <Table
                            dataSource={detail.billingRecords || []}
                            columns={[
                                {
                                    title: '时间',
                                    dataIndex: 'createdAt',
                                    render: (time) => dayjs(time).format('MM-DD HH:mm:ss')
                                },
                                {
                                    title: '类型',
                                    dataIndex: 'transactionType',
                                    render: (type) => {
                                        const typeConfig = {
                                            'DEDUCT': { color: 'red', text: '扣费' },
                                            'RECHARGE': { color: 'green', text: '充值' }
                                        };
                                        const config = typeConfig[type] || { color: 'default', text: type };
                                        return <Tag color={config.color}>{config.text}</Tag>;
                                    }
                                },
                                {
                                    title: '变动时长',
                                    dataIndex: 'amountMinutes',
                                    render: (amount) => (
                                        <Text style={{
                                            color: amount > 0 ? '#52c41a' : '#ff4d4f',
                                            fontWeight: 'bold'
                                        }}>
                                            {amount > 0 ? '+' : ''}{amount} 分钟
                                        </Text>
                                    )
                                },
                                {
                                    title: '说明',
                                    dataIndex: 'description',
                                    ellipsis: true
                                }
                            ]}
                            pagination={false}
                            size="small"
                            rowKey="id"
                        />
                    </TabPane>
                </Tabs>
            )}
        </Modal>
    );
};
```

### 2. PMI计费管理页面

#### 2.1 计费信息展示
```jsx
const PmiBillingInfo = ({ pmiRecord }) => {
    return (
        <Card title="计费信息" size="small">
            <Descriptions column={2} size="small">
                <Descriptions.Item label="计费模式">
                    <Tag color={pmiRecord.billingMode === 'LONG' ? 'green' : 'orange'}>
                        {pmiRecord.billingMode === 'LONG' ? '按时段计费' : '按时长计费'}
                    </Tag>
                </Descriptions.Item>
                
                {pmiRecord.billingMode === 'LONG' && (
                    <>
                        <Descriptions.Item label="窗口到期时间">
                            {pmiRecord.windowExpireTime ? 
                                dayjs(pmiRecord.windowExpireTime).format('YYYY-MM-DD HH:mm:ss') : 
                                '-'
                            }
                        </Descriptions.Item>
                    </>
                )}
                
                {pmiRecord.billingMode === 'BY_TIME' && (
                    <>
                        <Descriptions.Item label="可用时长">
                            <Text type={pmiRecord.availableMinutes > 0 ? 'success' : 'danger'}>
                                {pmiRecord.availableMinutes} 分钟
                            </Text>
                        </Descriptions.Item>
                        <Descriptions.Item label="待扣时长">
                            <Text type={pmiRecord.pendingDeductMinutes > 0 ? 'warning' : 'secondary'}>
                                {pmiRecord.pendingDeductMinutes} 分钟
                            </Text>
                        </Descriptions.Item>
                        <Descriptions.Item label="总时长">
                            {pmiRecord.totalMinutes} 分钟
                        </Descriptions.Item>
                        <Descriptions.Item label="总使用时长">
                            {pmiRecord.totalUsedMinutes} 分钟
                        </Descriptions.Item>
                        <Descriptions.Item label="超额时长">
                            <Text type={pmiRecord.overdraftMinutes > 0 ? 'danger' : 'secondary'}>
                                {pmiRecord.overdraftMinutes} 分钟
                            </Text>
                        </Descriptions.Item>
                    </>
                )}
                
                <Descriptions.Item label="计费状态">
                    <Badge 
                        status={pmiRecord.billingStatus === 'ACTIVE' ? 'success' : 'error'} 
                        text={pmiRecord.billingStatus === 'ACTIVE' ? '正常' : '暂停'} 
                    />
                </Descriptions.Item>
            </Descriptions>
        </Card>
    );
};
```

### 3. PMI充值弹窗组件

#### 3.1 充值弹窗主组件
```jsx
const PmiRechargeModal = ({ visible, onCancel, pmiRecord, onSuccess }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [rechargeMinutes, setRechargeMinutes] = useState(0);
    const [previewData, setPreviewData] = useState(null);

    // 快捷充值选项（小时转分钟）
    const quickOptions = [
        { label: '1小时', value: 60 },
        { label: '5小时', value: 300 },
        { label: '10小时', value: 600 },
        { label: '12小时', value: 720 },
        { label: '100小时', value: 6000 }
    ];

    // 计算充值预览
    const calculatePreview = (minutes) => {
        if (!minutes || minutes <= 0) {
            setPreviewData(null);
            return;
        }

        const overdraft = pmiRecord.overdraftMinutes || 0;
        const pendingDeduct = pmiRecord.pendingDeductMinutes || 0;
        const currentTotal = pmiRecord.totalMinutes || 0;
        const currentAvailable = pmiRecord.availableMinutes || 0;

        let remainingRecharge = minutes;
        let overdraftSettled = 0;
        let pendingDeductSettled = 0;

        // 先结清超额时长
        if (overdraft > 0) {
            overdraftSettled = Math.min(remainingRecharge, overdraft);
            remainingRecharge -= overdraftSettled;
        }

        // 再结清待扣时长
        if (remainingRecharge > 0 && pendingDeduct > 0) {
            pendingDeductSettled = Math.min(remainingRecharge, pendingDeduct);
            remainingRecharge -= pendingDeductSettled;
        }

        const preview = {
            rechargeAmount: minutes,
            overdraftSettled,
            pendingDeductSettled,
            actualAdded: remainingRecharge,
            totalAfter: currentTotal + remainingRecharge,
            availableAfter: currentAvailable + remainingRecharge,
            overdraftAfter: overdraft - overdraftSettled,
            pendingDeductAfter: pendingDeduct - pendingDeductSettled,
            canStartMeeting: (overdraft - overdraftSettled) === 0
        };

        setPreviewData(preview);
    };

    // 处理充值金额变化
    const handleRechargeChange = (value) => {
        setRechargeMinutes(value);
        calculatePreview(value);
        form.setFieldsValue({ rechargeMinutes: value });
    };

    // 快捷选择
    const handleQuickSelect = (minutes) => {
        handleRechargeChange(minutes);
    };

    // 提交充值
    const handleSubmit = async () => {
        try {
            await form.validateFields();
            setLoading(true);

            const response = await pmiApi.rechargePmi(pmiRecord.id, {
                minutes: rechargeMinutes,
                description: `用户充值${rechargeMinutes}分钟`
            });

            if (response.success) {
                message.success('充值成功！');
                onSuccess(response.data);
                onCancel();
                form.resetFields();
                setRechargeMinutes(0);
                setPreviewData(null);
            } else {
                message.error(response.message || '充值失败');
            }
        } catch (error) {
            message.error('充值失败，请重试');
            console.error('充值错误:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal
            title="PMI时长充值"
            open={visible}
            onCancel={onCancel}
            onOk={handleSubmit}
            confirmLoading={loading}
            width={600}
            okText="确认充值"
            cancelText="取消"
        >
            <Form form={form} layout="vertical">
                {/* 当前状态展示 */}
                <Card title="当前状态" size="small" style={{ marginBottom: 16 }}>
                    <Row gutter={16}>
                        <Col span={6}>
                            <Statistic
                                title="总时长"
                                value={pmiRecord.totalMinutes || 0}
                                suffix="分钟"
                            />
                        </Col>
                        <Col span={6}>
                            <Statistic
                                title="可用时长"
                                value={pmiRecord.availableMinutes || 0}
                                suffix="分钟"
                                valueStyle={{
                                    color: (pmiRecord.availableMinutes || 0) > 0 ? '#3f8600' : '#cf1322'
                                }}
                            />
                        </Col>
                        <Col span={6}>
                            <Statistic
                                title="待扣时长"
                                value={pmiRecord.pendingDeductMinutes || 0}
                                suffix="分钟"
                                valueStyle={{
                                    color: (pmiRecord.pendingDeductMinutes || 0) > 0 ? '#fa8c16' : '#666'
                                }}
                            />
                        </Col>
                        <Col span={6}>
                            <Statistic
                                title="超额时长"
                                value={pmiRecord.overdraftMinutes || 0}
                                suffix="分钟"
                                valueStyle={{
                                    color: (pmiRecord.overdraftMinutes || 0) > 0 ? '#cf1322' : '#666'
                                }}
                            />
                        </Col>
                    </Row>

                    {(pmiRecord.overdraftMinutes || 0) > 0 && (
                        <Alert
                            message="存在超额时长，无法开启会议"
                            description="请充值结清超额时长后才能正常使用PMI"
                            type="error"
                            showIcon
                            style={{ marginTop: 16 }}
                        />
                    )}
                </Card>

                {/* 快捷充值选项 */}
                <div style={{ marginBottom: 16 }}>
                    <Text strong>快捷选择：</Text>
                    <div style={{ marginTop: 8 }}>
                        <Space wrap>
                            {quickOptions.map(option => (
                                <Button
                                    key={option.value}
                                    size="small"
                                    onClick={() => handleQuickSelect(option.value)}
                                    type={rechargeMinutes === option.value ? 'primary' : 'default'}
                                >
                                    {option.label}
                                </Button>
                            ))}
                        </Space>
                    </div>
                </div>

                {/* 充值金额输入 */}
                <Form.Item
                    name="rechargeMinutes"
                    label="充值时长（分钟）"
                    rules={[
                        { required: true, message: '请输入充值时长' },
                        { type: 'number', min: 1, message: '充值时长必须大于0' },
                        { type: 'number', max: 100000, message: '单次充值不能超过100000分钟' }
                    ]}
                >
                    <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入充值时长"
                        min={1}
                        max={100000}
                        value={rechargeMinutes}
                        onChange={handleRechargeChange}
                        addonAfter="分钟"
                    />
                </Form.Item>

                {/* 充值预览 */}
                {previewData && (
                    <Card title="充值预览" size="small" style={{ backgroundColor: '#f6ffed' }}>
                        <Descriptions column={2} size="small">
                            <Descriptions.Item label="充值金额">
                                <Text strong style={{ color: '#52c41a' }}>
                                    +{previewData.rechargeAmount} 分钟
                                </Text>
                            </Descriptions.Item>

                            {previewData.overdraftSettled > 0 && (
                                <Descriptions.Item label="结清超额">
                                    <Text style={{ color: '#fa541c' }}>
                                        -{previewData.overdraftSettled} 分钟
                                    </Text>
                                </Descriptions.Item>
                            )}

                            {previewData.pendingDeductSettled > 0 && (
                                <Descriptions.Item label="结清待扣">
                                    <Text style={{ color: '#fa8c16' }}>
                                        -{previewData.pendingDeductSettled} 分钟
                                    </Text>
                                </Descriptions.Item>
                            )}

                            <Descriptions.Item label="实际增加">
                                <Text strong style={{ color: '#1890ff' }}>
                                    +{previewData.actualAdded} 分钟
                                </Text>
                            </Descriptions.Item>

                            <Descriptions.Item label="充值后总时长">
                                <Text strong>{previewData.totalAfter} 分钟</Text>
                            </Descriptions.Item>

                            <Descriptions.Item label="充值后可用时长">
                                <Text strong style={{ color: '#52c41a' }}>
                                    {previewData.availableAfter} 分钟
                                </Text>
                            </Descriptions.Item>
                        </Descriptions>

                        <div style={{ marginTop: 12 }}>
                            <Badge
                                status={previewData.canStartMeeting ? 'success' : 'error'}
                                text={previewData.canStartMeeting ? '充值后可正常开启会议' : '充值后仍无法开启会议'}
                            />
                        </div>
                    </Card>
                )}
            </Form>
        </Modal>
    );
};
```

#### 3.2 PMI管理页面集成
```jsx
const PmiManagement = () => {
    const [rechargeModalVisible, setRechargeModalVisible] = useState(false);
    const [selectedPmi, setSelectedPmi] = useState(null);

    // 打开充值弹窗
    const handleOpenRecharge = (record) => {
        setSelectedPmi(record);
        setRechargeModalVisible(true);
    };

    // 充值成功回调
    const handleRechargeSuccess = (result) => {
        // 刷新PMI列表
        loadPmiRecords(pagination.current, pagination.pageSize, searchKeyword, filteredUserId);
        message.success(`充值成功！实际增加 ${result.actualAdded} 分钟`);
    };

    // 在操作列中添加充值按钮
    const actionColumn = {
        title: '操作',
        key: 'action',
        render: (_, record) => (
            <Space direction={isMobileView ? 'vertical' : 'horizontal'} size="small">
                {/* 其他操作按钮... */}

                {/* 时长充值按钮 */}
                {record.billingMode === 'BY_TIME' && (
                    <Button
                        size="small"
                        icon={<DollarOutlined />}
                        onClick={() => handleOpenRecharge(record)}
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: isMobileView ? 'flex-start' : 'center'
                        }}
                    >
                        {isMobileView && <span style={{ marginLeft: 8 }}>时长充值</span>}
                    </Button>
                )}
            </Space>
        )
    };

    return (
        <div>
            {/* PMI列表表格... */}

            {/* 充值弹窗 */}
            <PmiRechargeModal
                visible={rechargeModalVisible}
                onCancel={() => {
                    setRechargeModalVisible(false);
                    setSelectedPmi(null);
                }}
                pmiRecord={selectedPmi}
                onSuccess={handleRechargeSuccess}
            />
        </div>
    );
};
```

---

## 🔧 核心算法实现

### 1. 会议结算算法

#### 1.1 结算服务实现
```java
@Service
@Transactional
public class MeetingSettlementService {

    /**
     * 会议结束结算
     */
    public void settleMeeting(Long meetingId) {
        ZoomMeeting meeting = zoomMeetingRepository.findById(meetingId)
            .orElseThrow(() -> new ResourceNotFoundException("会议记录不存在"));

        if (meeting.getStatus() != MeetingStatus.ENDED) {
            throw new IllegalStateException("只能结算已结束的会议");
        }

        PmiRecord pmiRecord = pmiRecordRepository.findById(meeting.getPmiRecordId())
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

        // 按时段计费模式不需要扣费
        if (pmiRecord.getBillingMode() == BillingMode.LONG) {
            meeting.setIsSettled(true);
            meeting.setStatus(MeetingStatus.SETTLED);
            zoomMeetingRepository.save(meeting);

            createBillingRecord(pmiRecord, meeting, 0, "按时段计费，无需扣费");
            return;
        }

        // 按时长计费模式处理
        int billedMinutes = meeting.getBilledMinutes();
        int availableMinutes = pmiRecord.getAvailableMinutes();
        int pendingDeductMinutes = pmiRecord.getPendingDeductMinutes();

        if (availableMinutes >= billedMinutes) {
            // 余额充足，正常扣费
            pmiRecord.setAvailableMinutes(availableMinutes - billedMinutes);
            pmiRecord.setPendingDeductMinutes(Math.max(0, pendingDeductMinutes - billedMinutes));
            pmiRecord.setTotalUsedMinutes(pmiRecord.getTotalUsedMinutes() + billedMinutes);

            createBillingRecord(pmiRecord, meeting, billedMinutes, "正常扣费");

        } else {
            // 余额不足，记录超额使用
            int deductedMinutes = availableMinutes;
            int overdraftMinutes = billedMinutes - availableMinutes;

            pmiRecord.setAvailableMinutes(0);
            pmiRecord.setPendingDeductMinutes(0); // 待扣时长清零
            pmiRecord.setOverdraftMinutes(pmiRecord.getOverdraftMinutes() + overdraftMinutes); // 记录超额时长
            pmiRecord.setTotalUsedMinutes(pmiRecord.getTotalUsedMinutes() + billedMinutes);

            createBillingRecord(pmiRecord, meeting, deductedMinutes,
                String.format("余额不足，实扣%d分钟，超额%d分钟记录到超额时长", deductedMinutes, overdraftMinutes));
        }

        // 更新会议状态
        meeting.setIsSettled(true);
        meeting.setStatus(MeetingStatus.SETTLED);

        pmiRecordRepository.save(pmiRecord);
        zoomMeetingRepository.save(meeting);

        log.info("会议 {} 结算完成，扣费 {} 分钟", meetingId, billedMinutes);
    }

    /**
     * 创建计费流水记录
     */
    private void createBillingRecord(PmiRecord pmiRecord, ZoomMeeting meeting,
                                   int deductedMinutes, String description) {
        PmiBillingRecord record = new PmiBillingRecord();
        record.setPmiRecordId(pmiRecord.getId());
        record.setZoomMeetingId(meeting.getId());
        record.setUserId(pmiRecord.getUserId());
        record.setTransactionType(TransactionType.DEDUCT);
        record.setAmountMinutes(-deductedMinutes);
        record.setBalanceBefore(pmiRecord.getAvailableMinutes() + deductedMinutes);
        record.setBalanceAfter(pmiRecord.getAvailableMinutes());
        record.setDescription(description);
        record.setBillingPeriodStart(meeting.getStartTime());
        record.setBillingPeriodEnd(meeting.getEndTime());
        record.setStatus(RecordStatus.COMPLETED);

        pmiBillingRecordRepository.save(record);
    }
}
```

### 2. 充值补扣算法

#### 2.1 PMI管理服务实现
```java
/**
 * PMI管理服务（增强版）
 */
@Service
@Transactional
public class PmiManagementService {

    @Autowired
    private PmiRecordRepository pmiRecordRepository;

    @Autowired
    private ZoomUserPmiService zoomUserPmiService;

    @Autowired
    private ZoomApiService zoomApiService;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private ZoomMeetingRepository zoomMeetingRepository;

    /**
     * 创建PMI记录
     */
    public PmiRecord createPmiRecord(CreatePmiRequest request) {
        ZoomUser assignedUser = null;
        try {
            // 1. 分配ZoomUser用于PMI设置
            assignedUser = zoomUserPmiService.assignZoomUserForPmiOperation("CREATE_PMI");

            // 2. 通过ZoomUser设置PMI
            ZoomPmiSetupResult setupResult = zoomApiService.setupPmiWithUser(
                assignedUser.getZoomUserId(), request.getPmiNumber());

            if (!setupResult.isSuccess()) {
                throw new RuntimeException("PMI设置失败: " + setupResult.getMessage());
            }

            // 3. 创建PMI记录
            PmiRecord pmiRecord = new PmiRecord();
            pmiRecord.setPmiNumber(request.getPmiNumber());
            pmiRecord.setUserId(request.getUserId());
            pmiRecord.setBillingMode(request.getBillingMode());
            pmiRecord.setTotalMinutes(request.getInitialMinutes());
            pmiRecord.setAvailableMinutes(request.getInitialMinutes());
            pmiRecord.setPendingDeductMinutes(0);
            pmiRecord.setOverdraftMinutes(0);
            pmiRecord.setIsActive(true);
            pmiRecord.setCreatedAt(LocalDateTime.now());

            // 设置时间窗口（如果是按时段计费）
            if (request.getBillingMode() == BillingMode.LONG) {
                pmiRecord.setWindowStartTime(request.getWindowStartTime());
                pmiRecord.setWindowEndTime(request.getWindowEndTime());
            }

            pmiRecord = pmiRecordRepository.save(pmiRecord);

            // 4. 记录操作日志
            operationLogService.recordPmiOperation(pmiRecord.getId(),
                OperationType.PMI_CREATE,
                String.format("创建PMI %s，初始时长 %d 分钟",
                    request.getPmiNumber(), request.getInitialMinutes()));

            log.info("PMI记录创建成功: {}", pmiRecord.getPmiNumber());
            return pmiRecord;

        } catch (Exception e) {
            log.error("创建PMI记录失败: {}", request.getPmiNumber(), e);
            throw new RuntimeException("创建PMI失败", e);
        } finally {
            // 5. 无论成功失败都释放ZoomUser
            if (assignedUser != null) {
                try {
                    zoomUserPmiService.releaseZoomUserFromOperation(assignedUser.getId());
                } catch (Exception e) {
                    log.error("释放ZoomUser {} 失败", assignedUser.getId(), e);
                }
            }
        }
    }

    /**
     * 修改PMI设置
     */
    public PmiRecord updatePmiSettings(Long pmiId, UpdatePmiRequest request) {
        ZoomUser assignedUser = null;
        try {
            PmiRecord pmiRecord = pmiRecordRepository.findById(pmiId)
                .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

            // 1. 分配ZoomUser用于PMI修改
            assignedUser = zoomUserPmiService.assignZoomUserForPmiOperation("UPDATE_PMI");

            // 2. 记录修改前的状态
            String oldPmiNumber = pmiRecord.getPmiNumber();
            BillingMode oldBillingMode = pmiRecord.getBillingMode();

            // 3. 如果PMI号码发生变化，需要通过ZoomUser设置新PMI
            if (!oldPmiNumber.equals(request.getNewPmiNumber())) {
                ZoomPmiSetupResult setupResult = zoomApiService.setupPmiWithUser(
                    assignedUser.getZoomUserId(), request.getNewPmiNumber());

                if (!setupResult.isSuccess()) {
                    throw new RuntimeException("新PMI设置失败: " + setupResult.getMessage());
                }

                pmiRecord.setPmiNumber(request.getNewPmiNumber());
            }

            // 4. 更新其他设置
            if (request.getBillingMode() != null) {
                pmiRecord.setBillingMode(request.getBillingMode());
            }

            if (request.getWindowStartTime() != null && request.getWindowEndTime() != null) {
                pmiRecord.setWindowStartTime(request.getWindowStartTime());
                pmiRecord.setWindowEndTime(request.getWindowEndTime());
            }

            pmiRecord.setUpdatedAt(LocalDateTime.now());
            pmiRecord = pmiRecordRepository.save(pmiRecord);

            // 5. 记录操作日志
            String changeLog = buildChangeLog(oldPmiNumber, oldBillingMode, request);
            operationLogService.recordPmiOperation(pmiRecord.getId(),
                OperationType.PMI_UPDATE, changeLog);

            log.info("PMI设置修改成功: {}", pmiRecord.getPmiNumber());
            return pmiRecord;

        } catch (Exception e) {
            log.error("修改PMI设置失败: PMI ID {}", pmiId, e);
            throw new RuntimeException("修改PMI设置失败", e);
        } finally {
            // 6. 无论成功失败都释放ZoomUser
            if (assignedUser != null) {
                try {
                    zoomUserPmiService.releaseZoomUserFromOperation(assignedUser.getId());
                } catch (Exception e) {
                    log.error("释放ZoomUser {} 失败", assignedUser.getId(), e);
                }
            }
        }
    }

    /**
     * 删除PMI记录
     */
    public void deletePmiRecord(Long pmiId) {
        ZoomUser assignedUser = null;
        try {
            PmiRecord pmiRecord = pmiRecordRepository.findById(pmiId)
                .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

            // 检查是否有活跃会议
            if (hasActiveMeetings(pmiId)) {
                throw new RuntimeException("存在活跃会议，无法删除PMI");
            }

            // 1. 分配ZoomUser用于PMI清理
            assignedUser = zoomUserPmiService.assignZoomUserForPmiOperation("DELETE_PMI");

            // 2. 通过ZoomUser清理PMI设置
            try {
                zoomApiService.cleanupPmiWithUser(assignedUser.getZoomUserId(), pmiRecord.getPmiNumber());
            } catch (Exception e) {
                log.warn("清理PMI设置失败，继续删除本地记录: {}", e.getMessage());
            }

            // 3. 软删除PMI记录
            pmiRecord.setIsActive(false);
            pmiRecord.setDeletedAt(LocalDateTime.now());
            pmiRecordRepository.save(pmiRecord);

            // 4. 记录操作日志
            operationLogService.recordPmiOperation(pmiRecord.getId(),
                OperationType.PMI_DELETE,
                String.format("删除PMI %s", pmiRecord.getPmiNumber()));

            log.info("PMI记录删除成功: {}", pmiRecord.getPmiNumber());

        } catch (Exception e) {
            log.error("删除PMI记录失败: PMI ID {}", pmiId, e);
            throw new RuntimeException("删除PMI失败", e);
        } finally {
            // 5. 无论成功失败都释放ZoomUser
            if (assignedUser != null) {
                try {
                    zoomUserPmiService.releaseZoomUserFromOperation(assignedUser.getId());
                } catch (Exception e) {
                    log.error("释放ZoomUser {} 失败", assignedUser.getId(), e);
                }
            }
        }
    }

    private String buildChangeLog(String oldPmiNumber, BillingMode oldBillingMode, UpdatePmiRequest request) {
        StringBuilder log = new StringBuilder();

        if (!oldPmiNumber.equals(request.getNewPmiNumber())) {
            log.append(String.format("PMI号码: %s → %s; ", oldPmiNumber, request.getNewPmiNumber()));
        }

        if (request.getBillingMode() != null && !oldBillingMode.equals(request.getBillingMode())) {
            log.append(String.format("计费模式: %s → %s; ", oldBillingMode, request.getBillingMode()));
        }

        if (request.getWindowStartTime() != null && request.getWindowEndTime() != null) {
            log.append(String.format("时间窗口: %s - %s; ",
                request.getWindowStartTime(), request.getWindowEndTime()));
        }

        return log.toString();
    }

    private boolean hasActiveMeetings(Long pmiId) {
        // 检查是否有进行中或待开始的会议
        return zoomMeetingRepository.existsByPmiRecordIdAndStatusIn(pmiId,
            Arrays.asList(MeetingStatus.PENDING, MeetingStatus.USING));
    }
}
```

#### 2.2 充值服务实现
```java
@Service
@Transactional
public class PmiRechargeService {

    /**
     * 用户充值时长
     */
    public RechargeResult rechargeMinutes(Long pmiRecordId, int rechargeMinutes, String description) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

        int totalBefore = pmiRecord.getTotalMinutes();
        int availableBefore = pmiRecord.getAvailableMinutes();
        int pendingDeduct = pmiRecord.getPendingDeductMinutes();
        int overdraft = pmiRecord.getOverdraftMinutes();

        int remainingRecharge = rechargeMinutes;

        // 1. 先结清超额时长
        if (overdraft > 0) {
            int overdraftDeduct = Math.min(remainingRecharge, overdraft);
            remainingRecharge -= overdraftDeduct;
            pmiRecord.setOverdraftMinutes(overdraft - overdraftDeduct);

            createRechargeRecord(pmiRecord, -overdraftDeduct, totalBefore, totalBefore,
                String.format("充值结清超额时长 %d 分钟", overdraftDeduct));
        }

        // 2. 处理待扣款
        if (remainingRecharge > 0 && pendingDeduct > 0) {
            int pendingDeductAmount = Math.min(remainingRecharge, pendingDeduct);
            remainingRecharge -= pendingDeductAmount;
            pmiRecord.setPendingDeductMinutes(pendingDeduct - pendingDeductAmount);

            createRechargeRecord(pmiRecord, -pendingDeductAmount, totalBefore, totalBefore,
                String.format("充值补扣待扣款 %d 分钟", pendingDeductAmount));
        }

        // 3. 剩余充值金额增加总时长和可用时长
        if (remainingRecharge > 0) {
            pmiRecord.setTotalMinutes(totalBefore + remainingRecharge);
            pmiRecord.setAvailableMinutes(availableBefore + remainingRecharge);

            createRechargeRecord(pmiRecord, remainingRecharge, totalBefore,
                totalBefore + remainingRecharge, description);
        }

        pmiRecordRepository.save(pmiRecord);

        // 构建充值结果
        RechargeResult result = new RechargeResult();
        result.setSuccess(true);
        result.setRechargeAmount(rechargeMinutes);
        result.setOverdraftSettled(Math.min(rechargeMinutes, overdraft));
        result.setPendingDeductSettled(Math.min(rechargeMinutes - Math.min(rechargeMinutes, overdraft), pendingDeduct));
        result.setActualAdded(remainingRecharge);
        result.setTotalMinutesAfter(pmiRecord.getTotalMinutes());
        result.setAvailableMinutesAfter(pmiRecord.getAvailableMinutes());
        result.setCanStartMeeting(pmiRecord.getOverdraftMinutes() == 0);

        log.info("PMI {} 充值完成，充值 {} 分钟，结清超额 {} 分钟，补扣 {} 分钟，实际增加 {} 分钟",
                pmiRecordId, rechargeMinutes, result.getOverdraftSettled(),
                result.getPendingDeductSettled(), remainingRecharge);

        return result;
    }

    /**
     * 充值结果类
     */
    @Data
    public static class RechargeResult {
        private boolean success;
        private int rechargeAmount;           // 充值金额
        private int overdraftSettled;         // 结清的超额时长
        private int pendingDeductSettled;     // 结清的待扣时长
        private int actualAdded;              // 实际增加的时长
        private int totalMinutesAfter;        // 充值后总时长
        private int availableMinutesAfter;    // 充值后可用时长
        private boolean canStartMeeting;      // 是否可以开启会议
        private String message;               // 充值结果说明
    }

    private void createRechargeRecord(PmiRecord pmiRecord, int amount,
                                    int balanceBefore, int balanceAfter, String description) {
        PmiBillingRecord record = new PmiBillingRecord();
        record.setPmiRecordId(pmiRecord.getId());
        record.setUserId(pmiRecord.getUserId());
        record.setTransactionType(amount > 0 ? TransactionType.RECHARGE : TransactionType.DEDUCT);
        record.setAmountMinutes(amount);
        record.setBalanceBefore(balanceBefore);
        record.setBalanceAfter(balanceAfter);
        record.setDescription(description);
        record.setStatus(RecordStatus.COMPLETED);

        pmiBillingRecordRepository.save(record);
    }
}

/**
 * ZoomUser PMI管理服务
 */
@Service
@Transactional
public class ZoomUserPmiService {

    @Autowired
    private ZoomUserRepository zoomUserRepository;

    @Autowired
    private ZoomApiService zoomApiService;

    @Autowired
    private OperationLogService operationLogService;

    /**
     * 初始化ZoomUser的原始PMI
     */
    public void initializeOriginalPmi(Long zoomUserId) {
        ZoomUser zoomUser = zoomUserRepository.findById(zoomUserId)
            .orElseThrow(() -> new ResourceNotFoundException("ZoomUser不存在"));

        // 如果已经有原始PMI，则跳过
        if (StringUtils.hasText(zoomUser.getOriginalPmi())) {
            return;
        }

        try {
            // 从Zoom API获取用户信息
            ZoomUserDetail userDetail = zoomApiService.getUserDetail(zoomUser.getZoomUserId());

            if (StringUtils.hasText(userDetail.getPmi())) {
                zoomUser.setOriginalPmi(userDetail.getPmi());
                zoomUser.setCurrentPmi(userDetail.getPmi());
                zoomUser.setPmiUpdatedAt(LocalDateTime.now());

                zoomUserRepository.save(zoomUser);

                log.info("ZoomUser {} 原始PMI初始化完成: {}", zoomUserId, userDetail.getPmi());

                // 记录操作日志
                operationLogService.recordZoomUserOperation(zoomUserId,
                    OperationType.PMI_INITIALIZE, "初始化原始PMI: " + userDetail.getPmi());
            }
        } catch (Exception e) {
            log.error("初始化ZoomUser {} 原始PMI失败", zoomUserId, e);
            throw new RuntimeException("初始化原始PMI失败", e);
        }
    }

    /**
     * 批量初始化所有ZoomUser的原始PMI
     */
    public void batchInitializeOriginalPmi() {
        List<ZoomUser> usersWithoutPmi = zoomUserRepository.findByOriginalPmiIsNull();

        log.info("开始批量初始化 {} 个ZoomUser的原始PMI", usersWithoutPmi.size());

        for (ZoomUser zoomUser : usersWithoutPmi) {
            try {
                initializeOriginalPmi(zoomUser.getId());
                Thread.sleep(100); // 避免API调用过于频繁
            } catch (Exception e) {
                log.error("初始化ZoomUser {} 原始PMI失败", zoomUser.getId(), e);
            }
        }

        log.info("批量初始化原始PMI完成");
    }

    /**
     * 分配ZoomUser给PMI操作使用
     */
    public ZoomUser assignZoomUserForPmiOperation(String operationType) {
        // 查找可用的ZoomUser
        ZoomUser availableUser = findAvailableZoomUser();
        if (availableUser == null) {
            throw new RuntimeException("没有可用的ZoomUser账号进行PMI操作");
        }

        // 确保有原始PMI
        if (!StringUtils.hasText(availableUser.getOriginalPmi())) {
            initializeOriginalPmi(availableUser.getId());
            availableUser = zoomUserRepository.findById(availableUser.getId()).get();
        }

        try {
            // 更新本地记录为PMI操作状态
            availableUser.setUsageStatus(UsageStatus.IN_USE);
            availableUser.setCurrentMeetingId(-1L); // 特殊标记，表示用于PMI操作
            availableUser.setLastUsedTime(LocalDateTime.now());
            availableUser.setTotalUsageCount(availableUser.getTotalUsageCount() + 1);
            availableUser.setPmiUpdatedAt(LocalDateTime.now());

            zoomUserRepository.save(availableUser);

            log.info("ZoomUser {} 已分配用于PMI操作: {}", availableUser.getId(), operationType);

            // 记录操作日志
            operationLogService.recordZoomUserOperation(availableUser.getId(),
                OperationType.USER_ASSIGN,
                String.format("分配用于PMI操作: %s", operationType));

            return availableUser;

        } catch (Exception e) {
            log.error("分配ZoomUser {} 用于PMI操作失败", availableUser.getId(), e);
            throw new RuntimeException("分配ZoomUser失败", e);
        }
    }

    /**
     * 从PMI操作中释放ZoomUser
     */
    public void releaseZoomUserFromOperation(Long zoomUserId) {
        try {
            ZoomUser zoomUser = zoomUserRepository.findById(zoomUserId)
                .orElseThrow(() -> new ResourceNotFoundException("ZoomUser不存在"));

            // 恢复原始PMI（如果当前PMI不是原始PMI）
            if (StringUtils.hasText(zoomUser.getOriginalPmi()) &&
                !zoomUser.getOriginalPmi().equals(zoomUser.getCurrentPmi())) {

                try {
                    zoomApiService.updateUserPmi(zoomUser.getZoomUserId(), zoomUser.getOriginalPmi());
                    zoomUser.setCurrentPmi(zoomUser.getOriginalPmi());
                } catch (Exception e) {
                    log.warn("恢复ZoomUser {} 原始PMI失败: {}", zoomUserId, e.getMessage());
                }
            }

            // 更新使用状态
            zoomUser.setUsageStatus(UsageStatus.AVAILABLE);
            zoomUser.setCurrentMeetingId(null);
            zoomUser.setPmiUpdatedAt(LocalDateTime.now());

            zoomUserRepository.save(zoomUser);

            log.info("ZoomUser {} 已从PMI操作中释放", zoomUserId);

            // 记录操作日志
            operationLogService.recordZoomUserOperation(zoomUserId,
                OperationType.USER_RELEASE, "从PMI操作中释放");

        } catch (Exception e) {
            log.error("释放ZoomUser {} 失败", zoomUserId, e);
            // 即使失败也要尝试更新本地状态
            try {
                ZoomUser zoomUser = zoomUserRepository.findById(zoomUserId).orElse(null);
                if (zoomUser != null) {
                    zoomUser.setUsageStatus(UsageStatus.AVAILABLE);
                    zoomUser.setCurrentMeetingId(null);
                    zoomUserRepository.save(zoomUser);
                }
            } catch (Exception ex) {
                log.error("强制释放ZoomUser {} 状态失败", zoomUserId, ex);
            }
        }
    }

    /**
     * 分配ZoomUser给会议使用
     */
    public ZoomUser assignZoomUserForMeeting(Long meetingId, String targetPmi) {
        // 查找可用的ZoomUser
        ZoomUser availableUser = findAvailableZoomUser();
        if (availableUser == null) {
            throw new RuntimeException("没有可用的ZoomUser账号");
        }

        // 确保有原始PMI
        if (!StringUtils.hasText(availableUser.getOriginalPmi())) {
            initializeOriginalPmi(availableUser.getId());
            availableUser = zoomUserRepository.findById(availableUser.getId()).get();
        }

        try {
            // 更新Zoom账号的PMI
            zoomApiService.updateUserPmi(availableUser.getZoomUserId(), targetPmi);

            // 更新本地记录
            availableUser.setCurrentPmi(targetPmi);
            availableUser.setUsageStatus(UsageStatus.IN_USE);
            availableUser.setCurrentMeetingId(meetingId);
            availableUser.setLastUsedTime(LocalDateTime.now());
            availableUser.setTotalUsageCount(availableUser.getTotalUsageCount() + 1);
            availableUser.setPmiUpdatedAt(LocalDateTime.now());

            zoomUserRepository.save(availableUser);

            log.info("ZoomUser {} 已分配给会议 {}，PMI更新为: {}",
                    availableUser.getId(), meetingId, targetPmi);

            // 记录操作日志
            operationLogService.recordZoomUserOperation(availableUser.getId(),
                OperationType.USER_ASSIGN,
                String.format("分配给会议 %d，PMI从 %s 更新为 %s",
                    meetingId, availableUser.getOriginalPmi(), targetPmi));

            return availableUser;

        } catch (Exception e) {
            log.error("分配ZoomUser {} 给会议 {} 失败", availableUser.getId(), meetingId, e);
            throw new RuntimeException("分配ZoomUser失败", e);
        }
    }

    /**
     * 释放ZoomUser账号
     */
    public void releaseZoomUser(Long meetingId) {
        ZoomUser zoomUser = zoomUserRepository.findByCurrentMeetingId(meetingId);
        if (zoomUser == null) {
            log.warn("会议 {} 没有关联的ZoomUser", meetingId);
            return;
        }

        try {
            // 恢复原始PMI
            if (StringUtils.hasText(zoomUser.getOriginalPmi())) {
                zoomApiService.updateUserPmi(zoomUser.getZoomUserId(), zoomUser.getOriginalPmi());
                zoomUser.setCurrentPmi(zoomUser.getOriginalPmi());
            }

            // 更新使用状态
            zoomUser.setUsageStatus(UsageStatus.AVAILABLE);
            zoomUser.setCurrentMeetingId(null);
            zoomUser.setPmiUpdatedAt(LocalDateTime.now());

            zoomUserRepository.save(zoomUser);

            log.info("ZoomUser {} 已释放，PMI恢复为原始PMI: {}",
                    zoomUser.getId(), zoomUser.getOriginalPmi());

            // 记录操作日志
            operationLogService.recordZoomUserOperation(zoomUser.getId(),
                OperationType.USER_RELEASE,
                String.format("从会议 %d 释放，PMI恢复为 %s", meetingId, zoomUser.getOriginalPmi()));

        } catch (Exception e) {
            log.error("释放ZoomUser {} 失败", zoomUser.getId(), e);
            // 即使Zoom API调用失败，也要更新本地状态
            zoomUser.setUsageStatus(UsageStatus.AVAILABLE);
            zoomUser.setCurrentMeetingId(null);
            zoomUserRepository.save(zoomUser);
        }
    }

    /**
     * 查找可用的ZoomUser
     */
    private ZoomUser findAvailableZoomUser() {
        return zoomUserRepository.findFirstByUsageStatusOrderByLastUsedTimeAsc(UsageStatus.AVAILABLE);
    }

    /**
     * 获取ZoomUser使用统计
     */
    public ZoomUserUsageStatistics getUsageStatistics() {
        long totalUsers = zoomUserRepository.count();
        long availableUsers = zoomUserRepository.countByUsageStatus(UsageStatus.AVAILABLE);
        long inUseUsers = zoomUserRepository.countByUsageStatus(UsageStatus.IN_USE);
        long maintenanceUsers = zoomUserRepository.countByUsageStatus(UsageStatus.MAINTENANCE);

        return ZoomUserUsageStatistics.builder()
            .totalUsers(totalUsers)
            .availableUsers(availableUsers)
            .inUseUsers(inUseUsers)
            .maintenanceUsers(maintenanceUsers)
            .usageRate((double) inUseUsers / totalUsers * 100)
            .build();
    }
}

/**
 * PMI使用验证服务
 */
@Service
public class PmiUsageValidationService {

    /**
     * 验证PMI是否可以开启会议
     */
    public ValidationResult validatePmiUsage(Long pmiRecordId) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

        ValidationResult result = new ValidationResult();
        result.setPmiId(pmiRecordId);
        result.setBillingMode(pmiRecord.getBillingMode());

        // 按时段计费模式验证
        if (pmiRecord.getBillingMode() == BillingMode.LONG) {
            if (pmiRecord.getCurrentWindowId() != null &&
                pmiRecord.getWindowExpireTime() != null &&
                LocalDateTime.now().isBefore(pmiRecord.getWindowExpireTime())) {
                result.setCanStart(true);
                result.setMessage("按时段计费，窗口期内可无限使用");
            } else {
                result.setCanStart(false);
                result.setMessage("时间窗口已过期，请联系管理员");
            }
            return result;
        }

        // 按时长计费模式验证
        if (pmiRecord.getOverdraftMinutes() > 0) {
            result.setCanStart(false);
            result.setMessage(String.format("存在超额时长 %d 分钟，请先充值结清",
                pmiRecord.getOverdraftMinutes()));
            result.setOverdraftMinutes(pmiRecord.getOverdraftMinutes());
            return result;
        }

        if (pmiRecord.getAvailableMinutes() <= 0) {
            result.setCanStart(false);
            result.setMessage("可用时长不足，请先充值");
            return result;
        }

        result.setCanStart(true);
        result.setMessage("可以开启会议");
        result.setAvailableMinutes(pmiRecord.getAvailableMinutes());

        return result;
    }

    /**
     * 验证结果类
     */
    @Data
    public static class ValidationResult {
        private Long pmiId;
        private BillingMode billingMode;
        private boolean canStart;
        private String message;
        private int availableMinutes;
        private int overdraftMinutes;
        private int pendingDeductMinutes;
    }
}
```

---

## 📊 API接口设计

### 1. PMI计费管理API

#### 1.1 获取PMI计费信息
```http
GET /api/pmi/{pmiId}/billing
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "pmiId": 123,
        "billingMode": "BY_TIME",
        "availableMinutes": 1500,
        "pendingDeductMinutes": 30,
        "totalUsedMinutes": 2500,
        "billingStatus": "ACTIVE",
        "currentWindowId": null,
        "windowExpireTime": null,
        "lastBillingTime": "2025-07-31T10:30:00"
    }
}
```

#### 1.2 PMI充值接口
```http
POST /api/pmi/{pmiId}/recharge
```

**请求体：**
```json
{
    "minutes": 1000,
    "description": "用户充值1000分钟",
    "paymentMethod": "ALIPAY",
    "paymentAmount": 100.00
}
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "rechargeAmount": 1000,
        "overdraftSettled": 50,
        "pendingDeductSettled": 30,
        "actualAdded": 920,
        "totalMinutesAfter": 2920,
        "availableMinutesAfter": 1920,
        "canStartMeeting": true,
        "message": "充值成功，结清超额50分钟，补扣待扣款30分钟，实际增加920分钟"
    }
}
```

#### 1.3 PMI使用验证接口
```http
GET /api/pmi/{pmiId}/validate-usage
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "pmiId": 123,
        "billingMode": "BY_TIME",
        "canStart": false,
        "message": "存在超额时长 50 分钟，请先充值结清",
        "availableMinutes": 100,
        "overdraftMinutes": 50,
        "pendingDeductMinutes": 20
    }
}
```

#### 1.3 切换计费模式
```http
PUT /api/pmi/{pmiId}/billing-mode
```

**请求体：**
```json
{
    "billingMode": "LONG",
    "windowId": 456,
    "expireTime": "2025-08-31T23:59:59"
}
```

### 2. 会议看板API

#### 2.1 获取活跃会议列表
```http
GET /api/zoom-meetings/active
```

**查询参数：**
- `status`: 会议状态筛选 (PENDING,USING,ENDED,SETTLED)
- `billingMode`: 计费模式筛选 (LONG,BY_TIME)
- `page`: 页码
- `size`: 页大小

#### 2.2 会议详情接口
```http
GET /api/zoom-meetings/{meetingId}
```

#### 2.3 手动结束会议
```http
POST /api/zoom-meetings/{meetingId}/end
```

### 3. 计费流水API

#### 3.1 获取计费流水
```http
GET /api/pmi/{pmiId}/billing-records
```

#### 3.2 导出计费报表
```http
GET /api/pmi/{pmiId}/billing-report
```

---

## 🔍 监控和告警

### 1. 计费监控指标

#### 1.1 关键指标
- **实时计费会议数量**: 当前正在计费的会议数量
- **待扣款总时长**: 所有PMI的待扣款时长总和
- **余额不足PMI数量**: 可用时长为0的PMI数量
- **超额使用金额**: 超额使用的总时长
- **计费异常次数**: 计费过程中的异常次数

#### 1.2 监控服务
```java
@Component
public class BillingMonitoringService {

    @EventListener
    public void handleBillingEvent(BillingEvent event) {
        // 记录计费事件
        meterRegistry.counter("billing.events",
            "type", event.getType(),
            "pmi", event.getPmiId().toString())
            .increment();

        // 检查异常情况
        if (event.getType() == BillingEventType.OVERDRAFT) {
            alertService.sendOverdraftAlert(event);
        }
    }

    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void checkBillingHealth() {
        // 检查长时间未结算的会议
        List<ZoomMeeting> longRunningMeetings = zoomMeetingRepository
            .findLongRunningMeetings(Duration.ofHours(8));

        if (!longRunningMeetings.isEmpty()) {
            alertService.sendLongRunningMeetingAlert(longRunningMeetings);
        }

        // 检查计费异常
        checkBillingAnomalies();
    }
}
```

### 2. 告警规则

#### 2.1 告警配置
```yaml
billing:
  alerts:
    overdraft:
      enabled: true
      threshold: 100  # 超额使用超过100分钟时告警
    long-running:
      enabled: true
      duration: 8h    # 会议超过8小时时告警
    balance-low:
      enabled: true
      threshold: 60   # 余额低于60分钟时告警
    billing-error:
      enabled: true
      count: 5        # 5分钟内计费错误超过5次时告警
```

---

## 🧪 测试用例

### 1. 计费模式切换测试

#### 1.1 测试用例
```java
@Test
public void testSwitchBillingMode() {
    // 创建PMI，默认按时长计费
    PmiRecord pmi = createTestPmi();
    assertEquals(BillingMode.BY_TIME, pmi.getBillingMode());

    // 开启时间窗口，切换到按时段计费
    LocalDateTime expireTime = LocalDateTime.now().plusDays(30);
    pmiBillingModeService.switchToLongBilling(pmi.getId(), 123L, expireTime);

    pmi = pmiRecordRepository.findById(pmi.getId()).get();
    assertEquals(BillingMode.LONG, pmi.getBillingMode());
    assertEquals(123L, pmi.getCurrentWindowId());
    assertEquals(expireTime, pmi.getWindowExpireTime());

    // 窗口到期，切换回按时长计费
    pmiBillingModeService.switchToTimeBilling(pmi.getId());

    pmi = pmiRecordRepository.findById(pmi.getId()).get();
    assertEquals(BillingMode.BY_TIME, pmi.getBillingMode());
    assertNull(pmi.getCurrentWindowId());
    assertNull(pmi.getWindowExpireTime());
}
```

### 2. 会议计费测试

#### 2.1 正常计费测试
```java
@Test
public void testNormalBilling() {
    // 创建PMI，充值1000分钟
    PmiRecord pmi = createTestPmi();
    pmiRechargeService.rechargeMinutes(pmi.getId(), 1000, "测试充值");

    // 创建会议并开始
    ZoomMeeting meeting = createTestMeeting(pmi.getId());
    zoomMeetingService.handleMeetingStarted(meeting.getZoomMeetingUuid());

    // 模拟计费60分钟
    for (int i = 0; i < 60; i++) {
        billingMonitorService.processBillingTick(meeting.getId());
    }

    // 结束会议并结算
    meeting.setStatus(MeetingStatus.ENDED);
    meeting.setEndTime(LocalDateTime.now());
    zoomMeetingRepository.save(meeting);

    meetingSettlementService.settleMeeting(meeting.getId());

    // 验证结果
    pmi = pmiRecordRepository.findById(pmi.getId()).get();
    assertEquals(940, pmi.getAvailableMinutes()); // 1000 - 60
    assertEquals(60, pmi.getTotalUsedMinutes());
    assertEquals(0, pmi.getPendingDeductMinutes());

    meeting = zoomMeetingRepository.findById(meeting.getId()).get();
    assertEquals(MeetingStatus.SETTLED, meeting.getStatus());
    assertTrue(meeting.getIsSettled());
}
```

### 3. 超额使用测试

#### 3.1 余额不足测试（新逻辑）
```java
@Test
public void testOverdraftBilling() {
    // 创建PMI，只充值30分钟
    PmiRecord pmi = createTestPmi();
    pmiRechargeService.rechargeMinutes(pmi.getId(), 30, "测试充值");

    // 创建会议并使用60分钟
    ZoomMeeting meeting = createTestMeeting(pmi.getId());
    zoomMeetingService.handleMeetingStarted(meeting.getZoomMeetingUuid());

    // 模拟计费60分钟
    for (int i = 0; i < 60; i++) {
        billingMonitorService.processBillingTick(meeting.getId());
    }

    // 结束会议并结算
    meeting.setStatus(MeetingStatus.ENDED);
    meetingSettlementService.settleMeeting(meeting.getId());

    // 验证结果（新逻辑）
    pmi = pmiRecordRepository.findById(pmi.getId()).get();
    assertEquals(0, pmi.getAvailableMinutes());
    assertEquals(0, pmi.getPendingDeductMinutes()); // 待扣时长清零
    assertEquals(30, pmi.getOverdraftMinutes()); // 超额30分钟记录到超额时长
    assertEquals(60, pmi.getTotalUsedMinutes());

    // 验证无法开启新会议
    ValidationResult validation = pmiUsageValidationService.validatePmiUsage(pmi.getId());
    assertFalse(validation.isCanStart());
    assertEquals("存在超额时长 30 分钟，请先充值结清", validation.getMessage());
}
```

### 4. 充值功能测试

#### 4.1 正常充值测试
```java
@Test
public void testNormalRecharge() {
    // 创建PMI
    PmiRecord pmi = createTestPmi();

    // 充值1000分钟
    RechargeResult result = pmiRechargeService.rechargeMinutes(pmi.getId(), 1000, "测试充值");

    // 验证结果
    assertTrue(result.isSuccess());
    assertEquals(1000, result.getRechargeAmount());
    assertEquals(0, result.getOverdraftSettled());
    assertEquals(0, result.getPendingDeductSettled());
    assertEquals(1000, result.getActualAdded());
    assertTrue(result.isCanStartMeeting());

    // 验证数据库
    pmi = pmiRecordRepository.findById(pmi.getId()).get();
    assertEquals(1000, pmi.getTotalMinutes());
    assertEquals(1000, pmi.getAvailableMinutes());
    assertEquals(0, pmi.getOverdraftMinutes());
    assertEquals(0, pmi.getPendingDeductMinutes());
}
```

#### 4.2 超额结清充值测试
```java
@Test
public void testOverdraftSettlementRecharge() {
    // 创建PMI并设置超额时长
    PmiRecord pmi = createTestPmi();
    pmi.setOverdraftMinutes(50);
    pmi.setPendingDeductMinutes(30);
    pmi.setAvailableMinutes(100);
    pmi.setTotalMinutes(100);
    pmiRecordRepository.save(pmi);

    // 充值200分钟
    RechargeResult result = pmiRechargeService.rechargeMinutes(pmi.getId(), 200, "结清超额充值");

    // 验证结果
    assertTrue(result.isSuccess());
    assertEquals(200, result.getRechargeAmount());
    assertEquals(50, result.getOverdraftSettled()); // 结清50分钟超额
    assertEquals(30, result.getPendingDeductSettled()); // 结清30分钟待扣
    assertEquals(120, result.getActualAdded()); // 实际增加120分钟
    assertTrue(result.isCanStartMeeting());

    // 验证数据库
    pmi = pmiRecordRepository.findById(pmi.getId()).get();
    assertEquals(220, pmi.getTotalMinutes()); // 100 + 120
    assertEquals(220, pmi.getAvailableMinutes()); // 100 + 120
    assertEquals(0, pmi.getOverdraftMinutes()); // 超额已结清
    assertEquals(0, pmi.getPendingDeductMinutes()); // 待扣已结清
}
```

#### 4.3 部分结清充值测试
```java
@Test
public void testPartialSettlementRecharge() {
    // 创建PMI并设置超额时长
    PmiRecord pmi = createTestPmi();
    pmi.setOverdraftMinutes(100);
    pmi.setPendingDeductMinutes(50);
    pmi.setAvailableMinutes(0);
    pmi.setTotalMinutes(0);
    pmiRecordRepository.save(pmi);

    // 只充值80分钟（不足以结清所有超额和待扣）
    RechargeResult result = pmiRechargeService.rechargeMinutes(pmi.getId(), 80, "部分结清充值");

    // 验证结果
    assertTrue(result.isSuccess());
    assertEquals(80, result.getRechargeAmount());
    assertEquals(80, result.getOverdraftSettled()); // 只能结清80分钟超额
    assertEquals(0, result.getPendingDeductSettled()); // 无法结清待扣
    assertEquals(0, result.getActualAdded()); // 没有实际增加
    assertFalse(result.isCanStartMeeting()); // 仍有超额，无法开启会议

    // 验证数据库
    pmi = pmiRecordRepository.findById(pmi.getId()).get();
    assertEquals(0, pmi.getTotalMinutes());
    assertEquals(0, pmi.getAvailableMinutes());
    assertEquals(20, pmi.getOverdraftMinutes()); // 剩余20分钟超额
    assertEquals(50, pmi.getPendingDeductMinutes()); // 待扣未变
}
```

### 5. PMI开启验证测试

#### 5.1 超额时长阻止开启测试
```java
@Test
public void testOverdraftPreventStart() {
    // 创建PMI并设置超额时长
    PmiRecord pmi = createTestPmi();
    pmi.setOverdraftMinutes(30);
    pmi.setAvailableMinutes(100);
    pmiRecordRepository.save(pmi);

    // 验证无法开启
    ValidationResult result = pmiUsageValidationService.validatePmiUsage(pmi.getId());

    assertFalse(result.isCanStart());
    assertEquals("存在超额时长 30 分钟，请先充值结清", result.getMessage());
    assertEquals(30, result.getOverdraftMinutes());
}
```

#### 5.2 正常开启验证测试
```java
@Test
public void testNormalStartValidation() {
    // 创建PMI并充值
    PmiRecord pmi = createTestPmi();
    pmiRechargeService.rechargeMinutes(pmi.getId(), 500, "测试充值");

    // 验证可以开启
    ValidationResult result = pmiUsageValidationService.validatePmiUsage(pmi.getId());

    assertTrue(result.isCanStart());
    assertEquals("可以开启会议", result.getMessage());
    assertEquals(500, result.getAvailableMinutes());
    assertEquals(0, result.getOverdraftMinutes());
}
```

---

## 📈 性能优化

### 1. 数据库优化

#### 1.1 索引优化
```sql
-- 会议状态查询优化
CREATE INDEX idx_zoom_meetings_status_start_time ON t_zoom_meetings(status, start_time);

-- PMI计费查询优化
CREATE INDEX idx_pmi_billing_mode_status ON t_pmi_records(billing_mode, billing_status);

-- 流水记录查询优化
CREATE INDEX idx_billing_records_pmi_created ON t_pmi_billing_records(pmi_record_id, created_at);

-- 窗口到期查询优化
CREATE INDEX idx_pmi_window_expire ON t_pmi_records(billing_mode, window_expire_time);
```

#### 1.2 分区策略
```sql
-- 按月分区流水表
ALTER TABLE t_pmi_billing_records
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 2. 缓存策略

#### 2.1 Redis缓存配置
```java
@Configuration
@EnableCaching
public class BillingCacheConfig {

    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());

        return builder.build();
    }

    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

#### 2.2 缓存使用
```java
@Service
public class PmiBillingCacheService {

    @Cacheable(value = "pmi-billing", key = "#pmiId")
    public PmiBillingInfo getPmiBillingInfo(Long pmiId) {
        // 从数据库查询计费信息
        return buildBillingInfo(pmiId);
    }

    @CacheEvict(value = "pmi-billing", key = "#pmiId")
    public void evictPmiBillingCache(Long pmiId) {
        // 清除缓存
    }
}
```

---

## 🔒 安全考虑

### 1. 数据安全

#### 1.1 敏感数据加密
```java
@Entity
public class PmiBillingRecord {

    @Convert(converter = EncryptedStringConverter.class)
    private String description; // 加密存储描述信息

    @Convert(converter = EncryptedIntegerConverter.class)
    private Integer amountMinutes; // 加密存储金额信息
}
```

#### 1.2 访问控制
```java
@PreAuthorize("hasRole('ADMIN') or @pmiSecurityService.canAccessPmi(#pmiId, authentication.name)")
public PmiBillingInfo getPmiBillingInfo(Long pmiId) {
    // 只有管理员或PMI所有者可以查看计费信息
}
```

### 2. 防刷机制

#### 2.1 频率限制
```java
@Component
public class BillingRateLimiter {

    private final RedisTemplate<String, String> redisTemplate;

    public boolean checkRateLimit(String userId, String operation) {
        String key = String.format("billing:rate_limit:%s:%s", userId, operation);
        String count = redisTemplate.opsForValue().get(key);

        if (count == null) {
            redisTemplate.opsForValue().set(key, "1", Duration.ofMinutes(1));
            return true;
        }

        int currentCount = Integer.parseInt(count);
        if (currentCount >= 10) { // 每分钟最多10次操作
            return false;
        }

        redisTemplate.opsForValue().increment(key);
        return true;
    }
}
```

---

## 🔧 后端控制器实现

### 1. PMI计费控制器

#### 1.1 PMI计费信息控制器
```java
@RestController
@RequestMapping("/api/pmi")
@Slf4j
public class PmiBillingController {

    @Autowired
    private PmiRechargeService pmiRechargeService;

    @Autowired
    private PmiUsageValidationService pmiUsageValidationService;

    @Autowired
    private PmiBillingRecordService pmiBillingRecordService;

    /**
     * 获取PMI计费信息
     */
    @GetMapping("/{pmiId}/billing")
    @PreAuthorize("hasRole('ADMIN') or @pmiSecurityService.canAccessPmi(#pmiId, authentication.name)")
    public ResponseEntity<ApiResponse<PmiBillingInfo>> getPmiBillingInfo(@PathVariable Long pmiId) {
        try {
            PmiRecord pmiRecord = pmiRecordService.findById(pmiId);

            PmiBillingInfo billingInfo = PmiBillingInfo.builder()
                .pmiId(pmiId)
                .billingMode(pmiRecord.getBillingMode())
                .totalMinutes(pmiRecord.getTotalMinutes())
                .availableMinutes(pmiRecord.getAvailableMinutes())
                .pendingDeductMinutes(pmiRecord.getPendingDeductMinutes())
                .overdraftMinutes(pmiRecord.getOverdraftMinutes())
                .totalUsedMinutes(pmiRecord.getTotalUsedMinutes())
                .billingStatus(pmiRecord.getBillingStatus())
                .currentWindowId(pmiRecord.getCurrentWindowId())
                .windowExpireTime(pmiRecord.getWindowExpireTime())
                .lastBillingTime(pmiRecord.getLastBillingTime())
                .build();

            return ResponseEntity.ok(ApiResponse.success(billingInfo));
        } catch (Exception e) {
            log.error("获取PMI计费信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取计费信息失败"));
        }
    }

    /**
     * PMI充值接口
     */
    @PostMapping("/{pmiId}/recharge")
    @PreAuthorize("hasRole('ADMIN') or @pmiSecurityService.canAccessPmi(#pmiId, authentication.name)")
    public ResponseEntity<ApiResponse<RechargeResult>> rechargePmi(
            @PathVariable Long pmiId,
            @RequestBody @Valid RechargeRequest request) {
        try {
            // 参数验证
            if (request.getMinutes() <= 0) {
                return ResponseEntity.ok(ApiResponse.error("充值时长必须大于0"));
            }

            if (request.getMinutes() > 100000) {
                return ResponseEntity.ok(ApiResponse.error("单次充值不能超过100000分钟"));
            }

            // 执行充值
            RechargeResult result = pmiRechargeService.rechargeMinutes(
                pmiId,
                request.getMinutes(),
                request.getDescription()
            );

            // 记录操作日志
            operationLogService.recordRecharge(pmiId, request.getMinutes(),
                getCurrentUserId(), request.getDescription());

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (ResourceNotFoundException e) {
            return ResponseEntity.ok(ApiResponse.error("PMI记录不存在"));
        } catch (Exception e) {
            log.error("PMI充值失败", e);
            return ResponseEntity.ok(ApiResponse.error("充值失败，请重试"));
        }
    }

    /**
     * PMI使用验证接口
     */
    @GetMapping("/{pmiId}/validate-usage")
    public ResponseEntity<ApiResponse<ValidationResult>> validatePmiUsage(@PathVariable Long pmiId) {
        try {
            ValidationResult result = pmiUsageValidationService.validatePmiUsage(pmiId);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("PMI使用验证失败", e);
            return ResponseEntity.ok(ApiResponse.error("验证失败"));
        }
    }

    /**
     * 获取PMI计费流水
     */
    @GetMapping("/{pmiId}/billing-records")
    @PreAuthorize("hasRole('ADMIN') or @pmiSecurityService.canAccessPmi(#pmiId, authentication.name)")
    public ResponseEntity<ApiResponse<PageResult<PmiBillingRecord>>> getBillingRecords(
            @PathVariable Long pmiId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String transactionType) {
        try {
            PageRequest pageRequest = PageRequest.of(page, size,
                Sort.by(Sort.Direction.DESC, "createdAt"));

            Page<PmiBillingRecord> records = pmiBillingRecordService
                .findByPmiRecordId(pmiId, transactionType, pageRequest);

            PageResult<PmiBillingRecord> result = PageResult.<PmiBillingRecord>builder()
                .content(records.getContent())
                .totalElements(records.getTotalElements())
                .totalPages(records.getTotalPages())
                .currentPage(page)
                .pageSize(size)
                .build();

            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取计费流水失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取流水失败"));
        }
    }

    /**
     * 导出计费报表
     */
    @GetMapping("/{pmiId}/billing-report")
    @PreAuthorize("hasRole('ADMIN') or @pmiSecurityService.canAccessPmi(#pmiId, authentication.name)")
    public ResponseEntity<Resource> exportBillingReport(
            @PathVariable Long pmiId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            ByteArrayResource resource = pmiBillingReportService
                .generateReport(pmiId, startDate, endDate);

            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename=pmi_billing_report_" + pmiId + ".xlsx")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
        } catch (Exception e) {
            log.error("导出计费报表失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
}
```

#### 1.2 请求响应对象
```java
/**
 * 充值请求对象
 */
@Data
@Valid
public class RechargeRequest {

    @NotNull(message = "充值时长不能为空")
    @Min(value = 1, message = "充值时长必须大于0")
    @Max(value = 100000, message = "单次充值不能超过100000分钟")
    private Integer minutes;

    @Size(max = 500, message = "描述不能超过500字符")
    private String description = "用户充值";

    private String paymentMethod; // 支付方式
    private BigDecimal paymentAmount; // 支付金额
    private String orderNo; // 订单号
}

/**
 * PMI计费信息对象
 */
@Data
@Builder
public class PmiBillingInfo {
    private Long pmiId;
    private BillingMode billingMode;
    private Integer totalMinutes;
    private Integer availableMinutes;
    private Integer pendingDeductMinutes;
    private Integer overdraftMinutes;
    private Integer totalUsedMinutes;
    private BillingStatus billingStatus;
    private Long currentWindowId;
    private LocalDateTime windowExpireTime;
    private LocalDateTime lastBillingTime;

    // 计算属性
    public boolean canStartMeeting() {
        if (billingMode == BillingMode.LONG) {
            return currentWindowId != null && windowExpireTime != null &&
                   LocalDateTime.now().isBefore(windowExpireTime);
        }
        return overdraftMinutes == 0 && availableMinutes > 0;
    }

    public String getBillingStatusText() {
        if (overdraftMinutes > 0) {
            return "存在超额，需要充值";
        }
        if (availableMinutes <= 0 && billingMode == BillingMode.BY_TIME) {
            return "余额不足，需要充值";
        }
        return "正常";
    }
}

/**
 * 分页结果对象
 */
@Data
@Builder
public class PageResult<T> {
    private List<T> content;
    private long totalElements;
    private int totalPages;
    private int currentPage;
    private int pageSize;
    private boolean hasNext;
    private boolean hasPrevious;

    public static <T> PageResult<T> of(Page<T> page) {
        return PageResult.<T>builder()
            .content(page.getContent())
            .totalElements(page.getTotalElements())
            .totalPages(page.getTotalPages())
            .currentPage(page.getNumber())
            .pageSize(page.getSize())
            .hasNext(page.hasNext())
            .hasPrevious(page.hasPrevious())
            .build();
    }
}

/**
 * 会议结束结果对象
 */
@Data
@Builder
public class MeetingEndResult {
    private Long meetingId;
    private String pmiNumber;
    private LocalDateTime endTime;
    private long totalDuration;        // 总时长(分钟)
    private int billedMinutes;         // 计费分钟数
    private boolean zoomApiSuccess;    // Zoom API调用是否成功
    private boolean settlementCompleted; // 结算是否完成
    private String message;            // 结果消息
    private BigDecimal finalCost;      // 最终费用
}

/**
 * 会议计费详情对象
 */
@Data
@Builder
public class MeetingBillingDetail {
    private Long meetingId;
    private String pmiNumber;
    private BillingMode billingMode;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private long currentDuration;      // 当前时长(分钟)
    private int billedMinutes;         // 已计费分钟数
    private boolean isSettled;         // 是否已结算
    private boolean realTimeBilling;   // 是否实时计费中
    private BigDecimal estimatedCost;  // 预估费用
    private List<PmiBillingRecord> billingRecords; // 计费记录

    // 实时计费信息
    private String billingStatus;      // 计费状态文本
    private LocalDateTime lastBillingTime; // 最后计费时间
    private int nextBillingIn;         // 下次计费倒计时(秒)
}

/**
 * 增强的会议VO对象
 */
@Data
@Builder
public class ZoomMeetingVO {
    private Long id;
    private String pmiNumber;
    private String pmiUserName;
    private String topic;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer durationMinutes;   // 总时长
    private Long currentDuration;      // 当前时长(实时)
    private MeetingStatus status;
    private BillingMode billingMode;
    private Integer billedMinutes;     // 已计费分钟数
    private Boolean isSettled;
    private Boolean realTimeBilling;   // 是否实时计费中
    private BigDecimal estimatedCost;  // 预估费用
    private String billingStatusText;  // 计费状态文本

    // ZoomUser相关信息
    private Long assignedZoomUserId;   // 分配的ZoomUser ID
    private String assignedZoomUserEmail; // 分配的ZoomUser邮箱
    private String assignmentError;    // 分配错误信息

    // 扩展信息
    private String statusColor;        // 状态颜色
    private String durationText;       // 时长文本
    private String costText;           // 费用文本
}

/**
 * ZoomUser使用状态枚举
 */
public enum UsageStatus {
    AVAILABLE("可用"),
    IN_USE("使用中"),
    MAINTENANCE("维护中");

    private final String description;

    UsageStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

/**
 * ZoomUser使用统计对象
 */
@Data
@Builder
public class ZoomUserUsageStatistics {
    private long totalUsers;           // 总用户数
    private long availableUsers;       // 可用用户数
    private long inUseUsers;           // 使用中用户数
    private long maintenanceUsers;     // 维护中用户数
    private double usageRate;          // 使用率(%)

    // 扩展统计
    private long todayUsageCount;      // 今日使用次数
    private long totalUsageCount;      // 总使用次数
    private double avgUsageDuration;   // 平均使用时长
}

/**
 * ZoomUser详细信息对象
 */
@Data
@Builder
public class ZoomUserDetail {
    private String zoomUserId;
    private String email;
    private String firstName;
    private String lastName;
    private String pmi;                // 从Zoom API获取的PMI
    private String userType;           // 用户类型
    private String status;             // 账号状态
    private LocalDateTime createdAt;
    private LocalDateTime lastLoginTime;
}

/**
 * ZoomUser VO对象
 */
@Data
@Builder
public class ZoomUserVO {
    private Long id;
    private String email;
    private String firstName;
    private String lastName;
    private String originalPmi;        // 原始PMI
    private String currentPmi;         // 当前PMI
    private UsageStatus usageStatus;   // 使用状态
    private Long currentMeetingId;     // 当前会议ID
    private LocalDateTime lastUsedTime; // 最后使用时间
    private Integer totalUsageCount;   // 总使用次数
    private Integer totalUsageMinutes; // 总使用时长
    private LocalDateTime pmiUpdatedAt; // PMI最后更新时间

    // 扩展信息
    private String usageStatusText;    // 状态文本
    private String usageStatusColor;   // 状态颜色
    private String lastUsedText;       // 最后使用时间文本
    private boolean canRelease;        // 是否可以释放
    private boolean canMaintenance;    // 是否可以设置维护
}

/**
 * 创建PMI请求对象
 */
@Data
public class CreatePmiRequest {
    @NotBlank(message = "PMI号码不能为空")
    private String pmiNumber;

    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @NotNull(message = "计费模式不能为空")
    private BillingMode billingMode;

    @Min(value = 0, message = "初始时长不能为负数")
    private Integer initialMinutes = 0;

    // 按时段计费的时间窗口
    private LocalDateTime windowStartTime;
    private LocalDateTime windowEndTime;

    private String description;        // 创建说明
}

/**
 * 更新PMI请求对象
 */
@Data
public class UpdatePmiRequest {
    @NotBlank(message = "新PMI号码不能为空")
    private String newPmiNumber;

    private BillingMode billingMode;

    // 按时段计费的时间窗口
    private LocalDateTime windowStartTime;
    private LocalDateTime windowEndTime;

    private String description;        // 修改说明
}

/**
 * Zoom PMI设置结果对象
 */
@Data
@Builder
public class ZoomPmiSetupResult {
    private boolean success;           // 是否成功
    private String message;            // 结果消息
    private String pmiNumber;          // PMI号码
    private String zoomUserId;         // 使用的ZoomUser ID
    private LocalDateTime setupTime;   // 设置时间
    private String errorCode;          // 错误代码（如果失败）
}

/**
 * 批量创建PMI请求对象
 */
@Data
public class BatchCreatePmiRequest {
    @NotEmpty(message = "PMI请求列表不能为空")
    private List<CreatePmiRequest> pmiRequests;
}

/**
 * 批量创建PMI结果对象
 */
@Data
public class BatchPmiCreateResult {
    private List<PmiRecord> successList = new ArrayList<>();
    private List<PmiCreateFailure> failureList = new ArrayList<>();

    public void addSuccess(PmiRecord pmiRecord) {
        successList.add(pmiRecord);
    }

    public void addFailure(String pmiNumber, String reason) {
        failureList.add(new PmiCreateFailure(pmiNumber, reason));
    }

    public int getSuccessCount() {
        return successList.size();
    }

    public int getFailureCount() {
        return failureList.size();
    }

    @Data
    @AllArgsConstructor
    public static class PmiCreateFailure {
        private String pmiNumber;
        private String reason;
    }
}

/**
 * PMI验证结果对象
 */
@Data
@Builder
public class PmiValidationResult {
    private String pmiNumber;
    private boolean available;
    private String message;
}
```

### 2. PMI管理控制器

#### 2.1 PMI管理控制器
```java
@RestController
@RequestMapping("/api/pmi-management")
@Slf4j
public class PmiManagementController {

    @Autowired
    private PmiManagementService pmiManagementService;

    @Autowired
    private PmiRecordRepository pmiRecordRepository;

    /**
     * 创建PMI记录
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PmiRecord>> createPmi(@RequestBody @Valid CreatePmiRequest request) {
        try {
            // 检查PMI号码是否已存在
            if (pmiRecordRepository.existsByPmiNumberAndIsActiveTrue(request.getPmiNumber())) {
                return ResponseEntity.ok(ApiResponse.error("PMI号码已存在"));
            }

            PmiRecord pmiRecord = pmiManagementService.createPmiRecord(request);
            return ResponseEntity.ok(ApiResponse.success(pmiRecord));

        } catch (Exception e) {
            log.error("创建PMI失败", e);
            return ResponseEntity.ok(ApiResponse.error("创建PMI失败: " + e.getMessage()));
        }
    }

    /**
     * 修改PMI设置
     */
    @PutMapping("/{pmiId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PmiRecord>> updatePmi(
            @PathVariable Long pmiId,
            @RequestBody @Valid UpdatePmiRequest request) {
        try {
            // 如果PMI号码发生变化，检查新号码是否已存在
            if (!request.getNewPmiNumber().equals(getCurrentPmiNumber(pmiId))) {
                if (pmiRecordRepository.existsByPmiNumberAndIsActiveTrue(request.getNewPmiNumber())) {
                    return ResponseEntity.ok(ApiResponse.error("新PMI号码已存在"));
                }
            }

            PmiRecord pmiRecord = pmiManagementService.updatePmiSettings(pmiId, request);
            return ResponseEntity.ok(ApiResponse.success(pmiRecord));

        } catch (Exception e) {
            log.error("修改PMI设置失败", e);
            return ResponseEntity.ok(ApiResponse.error("修改PMI设置失败: " + e.getMessage()));
        }
    }

    /**
     * 删除PMI记录
     */
    @DeleteMapping("/{pmiId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deletePmi(@PathVariable Long pmiId) {
        try {
            pmiManagementService.deletePmiRecord(pmiId);
            return ResponseEntity.ok(ApiResponse.success("PMI删除成功"));

        } catch (Exception e) {
            log.error("删除PMI失败", e);
            return ResponseEntity.ok(ApiResponse.error("删除PMI失败: " + e.getMessage()));
        }
    }

    /**
     * 批量创建PMI
     */
    @PostMapping("/batch")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BatchPmiCreateResult>> batchCreatePmi(
            @RequestBody @Valid BatchCreatePmiRequest request) {
        try {
            List<CreatePmiRequest> pmiRequests = request.getPmiRequests();
            BatchPmiCreateResult result = new BatchPmiCreateResult();

            for (CreatePmiRequest pmiRequest : pmiRequests) {
                try {
                    // 检查PMI号码是否已存在
                    if (pmiRecordRepository.existsByPmiNumberAndIsActiveTrue(pmiRequest.getPmiNumber())) {
                        result.addFailure(pmiRequest.getPmiNumber(), "PMI号码已存在");
                        continue;
                    }

                    PmiRecord pmiRecord = pmiManagementService.createPmiRecord(pmiRequest);
                    result.addSuccess(pmiRecord);

                } catch (Exception e) {
                    result.addFailure(pmiRequest.getPmiNumber(), e.getMessage());
                }
            }

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("批量创建PMI失败", e);
            return ResponseEntity.ok(ApiResponse.error("批量创建PMI失败: " + e.getMessage()));
        }
    }

    /**
     * 验证PMI号码可用性
     */
    @GetMapping("/validate-pmi/{pmiNumber}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PmiValidationResult>> validatePmiNumber(@PathVariable String pmiNumber) {
        try {
            boolean exists = pmiRecordRepository.existsByPmiNumberAndIsActiveTrue(pmiNumber);

            PmiValidationResult result = PmiValidationResult.builder()
                .pmiNumber(pmiNumber)
                .available(!exists)
                .message(exists ? "PMI号码已存在" : "PMI号码可用")
                .build();

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("验证PMI号码失败", e);
            return ResponseEntity.ok(ApiResponse.error("验证失败"));
        }
    }

    private String getCurrentPmiNumber(Long pmiId) {
        return pmiRecordRepository.findById(pmiId)
            .map(PmiRecord::getPmiNumber)
            .orElse("");
    }
}
```

### 3. ZoomUser管理控制器

#### 2.1 ZoomUser PMI管理控制器
```java
@RestController
@RequestMapping("/api/zoom-users")
@Slf4j
public class ZoomUserPmiController {

    @Autowired
    private ZoomUserPmiService zoomUserPmiService;

    @Autowired
    private ZoomUserRepository zoomUserRepository;

    /**
     * 初始化所有ZoomUser的原始PMI
     */
    @PostMapping("/initialize-pmi")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> initializeAllPmi() {
        try {
            zoomUserPmiService.batchInitializeOriginalPmi();
            return ResponseEntity.ok(ApiResponse.success("PMI初始化完成"));
        } catch (Exception e) {
            log.error("批量初始化PMI失败", e);
            return ResponseEntity.ok(ApiResponse.error("初始化失败"));
        }
    }

    /**
     * 初始化单个ZoomUser的原始PMI
     */
    @PostMapping("/{zoomUserId}/initialize-pmi")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> initializePmi(@PathVariable Long zoomUserId) {
        try {
            zoomUserPmiService.initializeOriginalPmi(zoomUserId);
            return ResponseEntity.ok(ApiResponse.success("PMI初始化完成"));
        } catch (Exception e) {
            log.error("初始化ZoomUser {} PMI失败", zoomUserId, e);
            return ResponseEntity.ok(ApiResponse.error("初始化失败"));
        }
    }

    /**
     * 获取ZoomUser使用统计
     */
    @GetMapping("/usage-statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<ZoomUserUsageStatistics>> getUsageStatistics() {
        try {
            ZoomUserUsageStatistics statistics = zoomUserPmiService.getUsageStatistics();
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            log.error("获取ZoomUser使用统计失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取统计失败"));
        }
    }

    /**
     * 获取ZoomUser列表
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PageResult<ZoomUserVO>>> getZoomUsers(
            @RequestParam(required = false) String usageStatus,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            PageRequest pageRequest = PageRequest.of(page, size,
                Sort.by(Sort.Direction.DESC, "lastUsedTime"));

            Page<ZoomUser> users = zoomUserRepository.findZoomUsers(usageStatus, keyword, pageRequest);

            List<ZoomUserVO> userVOs = users.getContent().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

            PageResult<ZoomUserVO> result = PageResult.<ZoomUserVO>builder()
                .content(userVOs)
                .totalElements(users.getTotalElements())
                .totalPages(users.getTotalPages())
                .currentPage(page)
                .pageSize(size)
                .build();

            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取ZoomUser列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取列表失败"));
        }
    }

    /**
     * 手动释放ZoomUser
     */
    @PostMapping("/{zoomUserId}/release")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> releaseZoomUser(@PathVariable Long zoomUserId) {
        try {
            ZoomUser zoomUser = zoomUserRepository.findById(zoomUserId)
                .orElseThrow(() -> new ResourceNotFoundException("ZoomUser不存在"));

            if (zoomUser.getCurrentMeetingId() != null) {
                zoomUserPmiService.releaseZoomUser(zoomUser.getCurrentMeetingId());
            } else {
                // 直接释放
                zoomUser.setUsageStatus(UsageStatus.AVAILABLE);
                zoomUser.setCurrentMeetingId(null);
                zoomUserRepository.save(zoomUser);
            }

            return ResponseEntity.ok(ApiResponse.success("ZoomUser已释放"));
        } catch (Exception e) {
            log.error("释放ZoomUser {} 失败", zoomUserId, e);
            return ResponseEntity.ok(ApiResponse.error("释放失败"));
        }
    }

    /**
     * 设置ZoomUser维护状态
     */
    @PostMapping("/{zoomUserId}/maintenance")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> setMaintenanceStatus(
            @PathVariable Long zoomUserId,
            @RequestParam boolean maintenance) {
        try {
            ZoomUser zoomUser = zoomUserRepository.findById(zoomUserId)
                .orElseThrow(() -> new ResourceNotFoundException("ZoomUser不存在"));

            if (maintenance) {
                if (zoomUser.getUsageStatus() == UsageStatus.IN_USE) {
                    return ResponseEntity.ok(ApiResponse.error("使用中的账号无法设置为维护状态"));
                }
                zoomUser.setUsageStatus(UsageStatus.MAINTENANCE);
            } else {
                zoomUser.setUsageStatus(UsageStatus.AVAILABLE);
            }

            zoomUserRepository.save(zoomUser);

            String message = maintenance ? "已设置为维护状态" : "已恢复为可用状态";
            return ResponseEntity.ok(ApiResponse.success(message));
        } catch (Exception e) {
            log.error("设置ZoomUser {} 维护状态失败", zoomUserId, e);
            return ResponseEntity.ok(ApiResponse.error("设置失败"));
        }
    }

    private ZoomUserVO convertToVO(ZoomUser zoomUser) {
        return ZoomUserVO.builder()
            .id(zoomUser.getId())
            .email(zoomUser.getEmail())
            .firstName(zoomUser.getFirstName())
            .lastName(zoomUser.getLastName())
            .originalPmi(zoomUser.getOriginalPmi())
            .currentPmi(zoomUser.getCurrentPmi())
            .usageStatus(zoomUser.getUsageStatus())
            .currentMeetingId(zoomUser.getCurrentMeetingId())
            .lastUsedTime(zoomUser.getLastUsedTime())
            .totalUsageCount(zoomUser.getTotalUsageCount())
            .totalUsageMinutes(zoomUser.getTotalUsageMinutes())
            .pmiUpdatedAt(zoomUser.getPmiUpdatedAt())
            .build();
    }
}
```

### 3. Zoom会议看板控制器

#### 2.1 会议看板控制器
```java
@RestController
@RequestMapping("/api/zoom-meetings")
@Slf4j
public class ZoomMeetingDashboardController {

    @Autowired
    private ZoomMeetingService zoomMeetingService;

    @Autowired
    private MeetingSettlementService meetingSettlementService;

    /**
     * 获取活跃会议列表
     */
    @GetMapping("/active")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PageResult<ZoomMeetingVO>>> getActiveMeetings(
            @RequestParam(required = false) List<String> status,
            @RequestParam(required = false) String billingMode,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            // 默认显示活跃状态的会议
            if (status == null || status.isEmpty()) {
                status = Arrays.asList("PENDING", "USING");
            }

            PageRequest pageRequest = PageRequest.of(page, size,
                Sort.by(Sort.Direction.DESC, "startTime"));

            Page<ZoomMeeting> meetings = zoomMeetingService
                .findMeetings(status, billingMode, keyword, pageRequest);

            // 转换为VO对象
            List<ZoomMeetingVO> meetingVOs = meetings.getContent().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

            PageResult<ZoomMeetingVO> result = PageResult.<ZoomMeetingVO>builder()
                .content(meetingVOs)
                .totalElements(meetings.getTotalElements())
                .totalPages(meetings.getTotalPages())
                .currentPage(page)
                .pageSize(size)
                .hasNext(meetings.hasNext())
                .hasPrevious(meetings.hasPrevious())
                .build();

            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取活跃会议列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取会议列表失败"));
        }
    }

    /**
     * 获取会议详情
     */
    @GetMapping("/{meetingId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<ZoomMeetingDetailVO>> getMeetingDetail(@PathVariable Long meetingId) {
        try {
            ZoomMeeting meeting = zoomMeetingService.findById(meetingId);
            ZoomMeetingDetailVO detailVO = convertToDetailVO(meeting);
            return ResponseEntity.ok(ApiResponse.success(detailVO));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.ok(ApiResponse.error("会议记录不存在"));
        } catch (Exception e) {
            log.error("获取会议详情失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取会议详情失败"));
        }
    }

    /**
     * 手动结束会议（增强版）
     */
    @PostMapping("/{meetingId}/end")
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional
    public ResponseEntity<ApiResponse<MeetingEndResult>> endMeeting(@PathVariable Long meetingId) {
        try {
            ZoomMeeting meeting = zoomMeetingService.findById(meetingId);

            if (meeting.getStatus() != MeetingStatus.USING) {
                return ResponseEntity.ok(ApiResponse.error("只能结束进行中的会议"));
            }

            // 记录结束前的状态
            LocalDateTime endTime = LocalDateTime.now();
            Duration meetingDuration = Duration.between(meeting.getStartTime(), endTime);
            int finalBilledMinutes = meeting.getBilledMinutes();

            // 1. 先停止计费监控
            billingMonitorService.stopBillingMonitor(meetingId);

            // 2. 调用Zoom API结束会议
            ZoomApiResponse zoomResponse = zoomApiService.endMeeting(meeting.getZoomMeetingId());
            if (!zoomResponse.isSuccess()) {
                log.warn("Zoom API结束会议失败，但继续本地处理: {}", zoomResponse.getMessage());
            }

            // 3. 更新会议状态
            meeting.setStatus(MeetingStatus.ENDED);
            meeting.setEndTime(endTime);
            meeting.setDurationMinutes((int) meetingDuration.toMinutes());
            zoomMeetingService.save(meeting);

            // 4. 执行结算
            meetingSettlementService.settleMeeting(meetingId);

            // 5. 释放ZoomUser账号
            try {
                zoomUserPmiService.releaseZoomUser(meetingId);
            } catch (Exception e) {
                log.error("释放会议 {} 的ZoomUser失败", meetingId, e);
                // 不影响主流程，继续执行
            }

            // 6. 记录操作日志
            operationLogService.recordMeetingOperation(meetingId, OperationType.MEETING_END,
                getCurrentUserId(), "管理员手动结束会议");

            // 6. 构建结果
            MeetingEndResult result = MeetingEndResult.builder()
                .meetingId(meetingId)
                .pmiNumber(meeting.getPmiRecord().getPmiNumber())
                .endTime(endTime)
                .totalDuration(meetingDuration.toMinutes())
                .billedMinutes(finalBilledMinutes)
                .zoomApiSuccess(zoomResponse.isSuccess())
                .settlementCompleted(true)
                .message("会议已成功结束并完成结算")
                .build();

            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (ZoomApiException e) {
            log.error("Zoom API调用失败", e);
            return ResponseEntity.ok(ApiResponse.error("结束会议失败：Zoom API错误"));
        } catch (Exception e) {
            log.error("结束会议失败", e);
            return ResponseEntity.ok(ApiResponse.error("结束会议失败：系统错误"));
        }
    }

    /**
     * 获取历史会议列表
     */
    @GetMapping("/history")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PageResult<ZoomMeetingVO>>> getHistoryMeetings(
            @RequestParam(required = false) String billingMode,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            // 历史会议状态
            List<String> historyStatus = Arrays.asList("ENDED", "SETTLED");

            PageRequest pageRequest = PageRequest.of(page, size,
                Sort.by(Sort.Direction.DESC, "endTime"));

            Page<ZoomMeeting> meetings = zoomMeetingService
                .findHistoryMeetings(historyStatus, billingMode, keyword, startDate, endDate, pageRequest);

            // 转换为VO对象，包含详细的计费信息
            List<ZoomMeetingVO> meetingVOs = meetings.getContent().stream()
                .map(this::convertToDetailVO)
                .collect(Collectors.toList());

            PageResult<ZoomMeetingVO> result = PageResult.<ZoomMeetingVO>builder()
                .content(meetingVOs)
                .totalElements(meetings.getTotalElements())
                .totalPages(meetings.getTotalPages())
                .currentPage(page)
                .pageSize(size)
                .hasNext(meetings.hasNext())
                .hasPrevious(meetings.hasPrevious())
                .build();

            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取历史会议列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取历史会议失败"));
        }
    }

    /**
     * 获取会议详细计费信息
     */
    @GetMapping("/{meetingId}/billing-detail")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MeetingBillingDetail>> getMeetingBillingDetail(@PathVariable Long meetingId) {
        try {
            ZoomMeeting meeting = zoomMeetingService.findById(meetingId);

            // 获取计费记录
            List<PmiBillingRecord> billingRecords = pmiBillingRecordService
                .findByZoomMeetingId(meetingId);

            // 计算实时计费信息
            MeetingBillingDetail detail = MeetingBillingDetail.builder()
                .meetingId(meetingId)
                .pmiNumber(meeting.getPmiRecord().getPmiNumber())
                .billingMode(meeting.getBillingMode())
                .startTime(meeting.getStartTime())
                .endTime(meeting.getEndTime())
                .currentDuration(calculateCurrentDuration(meeting))
                .billedMinutes(meeting.getBilledMinutes())
                .isSettled(meeting.getIsSettled())
                .billingRecords(billingRecords)
                .realTimeBilling(meeting.getStatus() == MeetingStatus.USING)
                .estimatedCost(calculateEstimatedCost(meeting))
                .build();

            return ResponseEntity.ok(ApiResponse.success(detail));
        } catch (Exception e) {
            log.error("获取会议计费详情失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取计费详情失败"));
        }
    }

    /**
     * 获取会议统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MeetingStatistics>> getMeetingStatistics() {
        try {
            MeetingStatistics statistics = zoomMeetingService.getStatistics();
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            log.error("获取会议统计失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取统计信息失败"));
        }
    }

    private ZoomMeetingVO convertToVO(ZoomMeeting meeting) {
        PmiRecord pmiRecord = meeting.getPmiRecord();

        return ZoomMeetingVO.builder()
            .id(meeting.getId())
            .pmiNumber(pmiRecord.getPmiNumber())
            .pmiUserName(pmiRecord.getUser().getFullName())
            .topic(meeting.getTopic())
            .startTime(meeting.getStartTime())
            .endTime(meeting.getEndTime())
            .durationMinutes(meeting.getDurationMinutes())
            .status(meeting.getStatus())
            .billingMode(meeting.getBillingMode())
            .billedMinutes(meeting.getBilledMinutes())
            .isSettled(meeting.getIsSettled())
            .build();
    }

    private ZoomMeetingVO convertToDetailVO(ZoomMeeting meeting) {
        PmiRecord pmiRecord = meeting.getPmiRecord();

        return ZoomMeetingVO.builder()
            .id(meeting.getId())
            .pmiNumber(pmiRecord.getPmiNumber())
            .pmiUserName(pmiRecord.getUser().getFullName())
            .topic(meeting.getTopic())
            .startTime(meeting.getStartTime())
            .endTime(meeting.getEndTime())
            .durationMinutes(meeting.getDurationMinutes())
            .currentDuration(calculateCurrentDuration(meeting))
            .status(meeting.getStatus())
            .billingMode(meeting.getBillingMode())
            .billedMinutes(meeting.getBilledMinutes())
            .isSettled(meeting.getIsSettled())
            .realTimeBilling(meeting.getStatus() == MeetingStatus.USING)
            .estimatedCost(calculateEstimatedCost(meeting))
            .billingStatusText(getBillingStatusText(meeting))
            .build();
    }

    private long calculateCurrentDuration(ZoomMeeting meeting) {
        if (meeting.getStartTime() == null) return 0;

        LocalDateTime endTime = meeting.getEndTime() != null ?
            meeting.getEndTime() : LocalDateTime.now();

        return Duration.between(meeting.getStartTime(), endTime).toMinutes();
    }

    private BigDecimal calculateEstimatedCost(ZoomMeeting meeting) {
        if (meeting.getBillingMode() == BillingMode.LONG) {
            return BigDecimal.ZERO; // 按时段计费无额外成本
        }

        // 按时长计费，假设每分钟0.1元
        BigDecimal ratePerMinute = new BigDecimal("0.1");
        return ratePerMinute.multiply(new BigDecimal(meeting.getBilledMinutes()));
    }

    private String getBillingStatusText(ZoomMeeting meeting) {
        if (meeting.getBillingMode() == BillingMode.LONG) {
            return "按时段计费";
        }

        switch (meeting.getStatus()) {
            case PENDING:
                return "待开始计费";
            case USING:
                return "正在计费中";
            case ENDED:
                return meeting.getIsSettled() ? "已结算" : "待结算";
            case SETTLED:
                return "结算完成";
            default:
                return meeting.getStatus().toString();
        }
    }
}
```

---

## 📊 数据统计和报表

### 1. 计费统计服务

#### 1.1 统计服务实现
```java
@Service
public class BillingStatisticsService {

    /**
     * 获取PMI计费统计
     */
    public PmiBillingStatistics getPmiBillingStatistics(Long pmiId, LocalDate startDate, LocalDate endDate) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

        // 查询时间范围内的计费记录
        List<PmiBillingRecord> records = pmiBillingRecordRepository
            .findByPmiRecordIdAndCreatedAtBetween(pmiId,
                startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay());

        // 统计充值和扣费
        int totalRecharge = records.stream()
            .filter(r -> r.getTransactionType() == TransactionType.RECHARGE)
            .mapToInt(PmiBillingRecord::getAmountMinutes)
            .sum();

        int totalDeduct = Math.abs(records.stream()
            .filter(r -> r.getTransactionType() == TransactionType.DEDUCT)
            .mapToInt(PmiBillingRecord::getAmountMinutes)
            .sum());

        // 查询会议使用情况
        List<ZoomMeeting> meetings = zoomMeetingRepository
            .findByPmiRecordIdAndStartTimeBetween(pmiId,
                startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay());

        int totalMeetings = meetings.size();
        int totalMeetingMinutes = meetings.stream()
            .mapToInt(ZoomMeeting::getBilledMinutes)
            .sum();

        return PmiBillingStatistics.builder()
            .pmiId(pmiId)
            .pmiNumber(pmiRecord.getPmiNumber())
            .userName(pmiRecord.getUser().getFullName())
            .billingMode(pmiRecord.getBillingMode())
            .periodStart(startDate)
            .periodEnd(endDate)
            .currentTotalMinutes(pmiRecord.getTotalMinutes())
            .currentAvailableMinutes(pmiRecord.getAvailableMinutes())
            .currentOverdraftMinutes(pmiRecord.getOverdraftMinutes())
            .periodRechargeMinutes(totalRecharge)
            .periodDeductMinutes(totalDeduct)
            .periodMeetingCount(totalMeetings)
            .periodMeetingMinutes(totalMeetingMinutes)
            .build();
    }

    /**
     * 获取系统整体统计
     */
    public SystemBillingStatistics getSystemStatistics() {
        // 统计所有PMI
        long totalPmis = pmiRecordRepository.count();
        long activePmis = pmiRecordRepository.countByBillingStatus(BillingStatus.ACTIVE);
        long longBillingPmis = pmiRecordRepository.countByBillingMode(BillingMode.LONG);
        long timeBillingPmis = pmiRecordRepository.countByBillingMode(BillingMode.BY_TIME);

        // 统计时长
        Integer totalMinutes = pmiRecordRepository.sumTotalMinutes();
        Integer availableMinutes = pmiRecordRepository.sumAvailableMinutes();
        Integer overdraftMinutes = pmiRecordRepository.sumOverdraftMinutes();

        // 统计会议
        long totalMeetings = zoomMeetingRepository.count();
        long activeMeetings = zoomMeetingRepository.countByStatusIn(
            Arrays.asList(MeetingStatus.PENDING, MeetingStatus.USING));

        // 今日统计
        LocalDateTime todayStart = LocalDate.now().atStartOfDay();
        LocalDateTime todayEnd = todayStart.plusDays(1);

        long todayMeetings = zoomMeetingRepository.countByStartTimeBetween(todayStart, todayEnd);
        Integer todayMinutes = zoomMeetingRepository.sumBilledMinutesByStartTimeBetween(todayStart, todayEnd);

        return SystemBillingStatistics.builder()
            .totalPmis(totalPmis)
            .activePmis(activePmis)
            .longBillingPmis(longBillingPmis)
            .timeBillingPmis(timeBillingPmis)
            .totalMinutes(totalMinutes != null ? totalMinutes : 0)
            .availableMinutes(availableMinutes != null ? availableMinutes : 0)
            .overdraftMinutes(overdraftMinutes != null ? overdraftMinutes : 0)
            .totalMeetings(totalMeetings)
            .activeMeetings(activeMeetings)
            .todayMeetings(todayMeetings)
            .todayMinutes(todayMinutes != null ? todayMinutes : 0)
            .build();
    }
}
```

### 2. 报表生成服务

#### 2.1 Excel报表生成
```java
@Service
public class PmiBillingReportService {

    /**
     * 生成PMI计费报表
     */
    public ByteArrayResource generateReport(Long pmiId, LocalDate startDate, LocalDate endDate) {
        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建工作表
            Sheet summarySheet = workbook.createSheet("计费汇总");
            Sheet recordsSheet = workbook.createSheet("流水明细");
            Sheet meetingsSheet = workbook.createSheet("会议记录");

            // 生成汇总表
            generateSummarySheet(summarySheet, pmiId, startDate, endDate);

            // 生成流水明细
            generateRecordsSheet(recordsSheet, pmiId, startDate, endDate);

            // 生成会议记录
            generateMeetingsSheet(meetingsSheet, pmiId, startDate, endDate);

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            return new ByteArrayResource(outputStream.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException("生成报表失败", e);
        }
    }

    private void generateSummarySheet(Sheet sheet, Long pmiId, LocalDate startDate, LocalDate endDate) {
        PmiBillingStatistics statistics = billingStatisticsService
            .getPmiBillingStatistics(pmiId, startDate, endDate);

        // 创建标题行
        Row titleRow = sheet.createRow(0);
        titleRow.createCell(0).setCellValue("PMI计费汇总报表");

        // 基本信息
        int rowNum = 2;
        createInfoRow(sheet, rowNum++, "PMI号码", statistics.getPmiNumber());
        createInfoRow(sheet, rowNum++, "用户姓名", statistics.getUserName());
        createInfoRow(sheet, rowNum++, "计费模式", statistics.getBillingMode().toString());
        createInfoRow(sheet, rowNum++, "统计周期", startDate + " 至 " + endDate);

        rowNum++; // 空行

        // 当前状态
        createInfoRow(sheet, rowNum++, "当前总时长", statistics.getCurrentTotalMinutes() + " 分钟");
        createInfoRow(sheet, rowNum++, "当前可用时长", statistics.getCurrentAvailableMinutes() + " 分钟");
        createInfoRow(sheet, rowNum++, "当前超额时长", statistics.getCurrentOverdraftMinutes() + " 分钟");

        rowNum++; // 空行

        // 周期统计
        createInfoRow(sheet, rowNum++, "周期充值时长", statistics.getPeriodRechargeMinutes() + " 分钟");
        createInfoRow(sheet, rowNum++, "周期扣费时长", statistics.getPeriodDeductMinutes() + " 分钟");
        createInfoRow(sheet, rowNum++, "周期会议次数", statistics.getPeriodMeetingCount() + " 次");
        createInfoRow(sheet, rowNum++, "周期会议时长", statistics.getPeriodMeetingMinutes() + " 分钟");

        // 自动调整列宽
        sheet.autoSizeColumn(0);
        sheet.autoSizeColumn(1);
    }

    private void createInfoRow(Sheet sheet, int rowNum, String label, String value) {
        Row row = sheet.createRow(rowNum);
        row.createCell(0).setCellValue(label);
        row.createCell(1).setCellValue(value);
    }
}
```

---

## 🔧 后端服务层实现

### 1. PMI计费记录服务

#### 1.1 计费记录查询服务
```java
@Service
public class PmiBillingRecordService {

    @Autowired
    private PmiBillingRecordRepository pmiBillingRecordRepository;

    /**
     * 分页查询PMI计费记录
     */
    public Page<PmiBillingRecord> findByPmiRecordId(Long pmiRecordId, String transactionType, Pageable pageable) {
        if (StringUtils.hasText(transactionType)) {
            TransactionType type = TransactionType.valueOf(transactionType.toUpperCase());
            return pmiBillingRecordRepository.findByPmiRecordIdAndTransactionType(pmiRecordId, type, pageable);
        }
        return pmiBillingRecordRepository.findByPmiRecordId(pmiRecordId, pageable);
    }

    /**
     * 查询会议相关的计费记录
     */
    public List<PmiBillingRecord> findByZoomMeetingId(Long zoomMeetingId) {
        return pmiBillingRecordRepository.findByZoomMeetingIdOrderByCreatedAtDesc(zoomMeetingId);
    }

    /**
     * 统计用户总充值金额
     */
    public Integer getTotalRechargeMinutes(Long userId) {
        return pmiBillingRecordRepository.sumAmountMinutesByUserIdAndTransactionType(
            userId, TransactionType.RECHARGE);
    }

    /**
     * 统计用户总消费金额
     */
    public Integer getTotalDeductMinutes(Long userId) {
        Integer result = pmiBillingRecordRepository.sumAmountMinutesByUserIdAndTransactionType(
            userId, TransactionType.DEDUCT);
        return result != null ? Math.abs(result) : 0;
    }

    /**
     * 获取用户最近的计费记录
     */
    public List<PmiBillingRecord> getRecentRecords(Long userId, int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        return pmiBillingRecordRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }
}
```

### 2. 会议管理服务扩展

#### 2.1 会议查询和统计服务
```java
@Service
public class ZoomMeetingService {

    /**
     * 多条件查询会议
     */
    public Page<ZoomMeeting> findMeetings(List<String> statusList, String billingMode,
                                         String keyword, Pageable pageable) {
        Specification<ZoomMeeting> spec = Specification.where(null);

        // 状态筛选
        if (statusList != null && !statusList.isEmpty()) {
            List<MeetingStatus> statuses = statusList.stream()
                .map(MeetingStatus::valueOf)
                .collect(Collectors.toList());
            spec = spec.and((root, query, cb) -> root.get("status").in(statuses));
        }

        // 计费模式筛选
        if (StringUtils.hasText(billingMode)) {
            BillingMode mode = BillingMode.valueOf(billingMode.toUpperCase());
            spec = spec.and((root, query, cb) -> cb.equal(root.get("billingMode"), mode));
        }

        // 关键词搜索（会议主题或PMI号码）
        if (StringUtils.hasText(keyword)) {
            spec = spec.and((root, query, cb) -> cb.or(
                cb.like(root.get("topic"), "%" + keyword + "%"),
                cb.like(root.join("pmiRecord").get("pmiNumber"), "%" + keyword + "%")
            ));
        }

        return zoomMeetingRepository.findAll(spec, pageable);
    }

    /**
     * 查询历史会议（增强版）
     */
    public Page<ZoomMeeting> findHistoryMeetings(List<String> statusList, String billingMode,
                                               String keyword, LocalDate startDate, LocalDate endDate,
                                               Pageable pageable) {
        Specification<ZoomMeeting> spec = Specification.where(null);

        // 状态筛选（历史会议）
        if (statusList != null && !statusList.isEmpty()) {
            List<MeetingStatus> statuses = statusList.stream()
                .map(MeetingStatus::valueOf)
                .collect(Collectors.toList());
            spec = spec.and((root, query, cb) -> root.get("status").in(statuses));
        }

        // 计费模式筛选
        if (StringUtils.hasText(billingMode)) {
            BillingMode mode = BillingMode.valueOf(billingMode.toUpperCase());
            spec = spec.and((root, query, cb) -> cb.equal(root.get("billingMode"), mode));
        }

        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            spec = spec.and((root, query, cb) -> cb.or(
                cb.like(root.get("topic"), "%" + keyword + "%"),
                cb.like(root.join("pmiRecord").get("pmiNumber"), "%" + keyword + "%")
            ));
        }

        // 日期范围筛选
        if (startDate != null) {
            spec = spec.and((root, query, cb) ->
                cb.greaterThanOrEqualTo(root.get("startTime"), startDate.atStartOfDay()));
        }

        if (endDate != null) {
            spec = spec.and((root, query, cb) ->
                cb.lessThan(root.get("startTime"), endDate.plusDays(1).atStartOfDay()));
        }

        return zoomMeetingRepository.findAll(spec, pageable);
    }

    /**
     * 获取会议统计信息
     */
    public MeetingStatistics getStatistics() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
        LocalDateTime weekStart = now.minusDays(7);
        LocalDateTime monthStart = now.minusDays(30);

        return MeetingStatistics.builder()
            .totalMeetings(zoomMeetingRepository.count())
            .pendingMeetings(zoomMeetingRepository.countByStatus(MeetingStatus.PENDING))
            .usingMeetings(zoomMeetingRepository.countByStatus(MeetingStatus.USING))
            .endedMeetings(zoomMeetingRepository.countByStatus(MeetingStatus.ENDED))
            .settledMeetings(zoomMeetingRepository.countByStatus(MeetingStatus.SETTLED))
            .todayMeetings(zoomMeetingRepository.countByStartTimeAfter(todayStart))
            .weekMeetings(zoomMeetingRepository.countByStartTimeAfter(weekStart))
            .monthMeetings(zoomMeetingRepository.countByStartTimeAfter(monthStart))
            .totalBilledMinutes(zoomMeetingRepository.sumBilledMinutes())
            .todayBilledMinutes(zoomMeetingRepository.sumBilledMinutesByStartTimeAfter(todayStart))
            .build();
    }

    /**
     * 获取长时间运行的会议
     */
    public List<ZoomMeeting> findLongRunningMeetings(Duration threshold) {
        LocalDateTime cutoffTime = LocalDateTime.now().minus(threshold);
        return zoomMeetingRepository.findByStatusAndStartTimeBefore(
            MeetingStatus.USING, cutoffTime);
    }

    /**
     * 批量结算会议
     */
    @Transactional
    public void batchSettleMeetings(List<Long> meetingIds) {
        for (Long meetingId : meetingIds) {
            try {
                meetingSettlementService.settleMeeting(meetingId);
            } catch (Exception e) {
                log.error("批量结算会议 {} 失败", meetingId, e);
            }
        }
    }
}
```

### 3. 操作日志服务

#### 3.1 操作日志记录
```java
@Service
public class OperationLogService {

    @Autowired
    private OperationLogRepository operationLogRepository;

    /**
     * 记录充值操作
     */
    public void recordRecharge(Long pmiId, int minutes, Long operatorId, String description) {
        OperationLog log = OperationLog.builder()
            .operationType(OperationType.PMI_RECHARGE)
            .targetType(TargetType.PMI)
            .targetId(pmiId)
            .operatorId(operatorId)
            .operationContent(String.format("PMI充值 %d 分钟", minutes))
            .description(description)
            .operationTime(LocalDateTime.now())
            .build();

        operationLogRepository.save(log);
    }

    /**
     * 记录会议操作
     */
    public void recordMeetingOperation(Long meetingId, OperationType operationType,
                                     Long operatorId, String content) {
        OperationLog log = OperationLog.builder()
            .operationType(operationType)
            .targetType(TargetType.MEETING)
            .targetId(meetingId)
            .operatorId(operatorId)
            .operationContent(content)
            .operationTime(LocalDateTime.now())
            .build();

        operationLogRepository.save(log);
    }

    /**
     * 记录计费模式切换
     */
    public void recordBillingModeSwitch(Long pmiId, BillingMode fromMode, BillingMode toMode,
                                       Long operatorId) {
        String content = String.format("计费模式从 %s 切换到 %s", fromMode, toMode);

        OperationLog log = OperationLog.builder()
            .operationType(OperationType.BILLING_MODE_SWITCH)
            .targetType(TargetType.PMI)
            .targetId(pmiId)
            .operatorId(operatorId)
            .operationContent(content)
            .operationTime(LocalDateTime.now())
            .build();

        operationLogRepository.save(log);
    }
}
```

---

## 📱 前端组件库扩展

### 1. 计费状态组件

#### 1.1 PMI状态卡片组件
```jsx
const PmiStatusCard = ({ pmiRecord, showActions = true, onRecharge, onViewDetails }) => {
    const getStatusColor = () => {
        if (pmiRecord.overdraftMinutes > 0) return 'error';
        if (pmiRecord.availableMinutes <= 0) return 'warning';
        return 'success';
    };

    const getStatusText = () => {
        if (pmiRecord.overdraftMinutes > 0) {
            return `超额 ${pmiRecord.overdraftMinutes} 分钟`;
        }
        if (pmiRecord.availableMinutes <= 0) {
            return '余额不足';
        }
        return '状态正常';
    };

    return (
        <Card
            size="small"
            title={
                <Space>
                    <Text strong>PMI: {pmiRecord.pmiNumber}</Text>
                    <Tag color={pmiRecord.billingMode === 'LONG' ? 'green' : 'blue'}>
                        {pmiRecord.billingMode === 'LONG' ? '按时段' : '按时长'}
                    </Tag>
                </Space>
            }
            extra={
                showActions && (
                    <Space>
                        {pmiRecord.billingMode === 'BY_TIME' && (
                            <Button
                                size="small"
                                type="primary"
                                icon={<DollarOutlined />}
                                onClick={() => onRecharge?.(pmiRecord)}
                            >
                                充值
                            </Button>
                        )}
                        <Button
                            size="small"
                            onClick={() => onViewDetails?.(pmiRecord)}
                        >
                            详情
                        </Button>
                    </Space>
                )
            }
        >
            <Row gutter={16}>
                <Col span={6}>
                    <Statistic
                        title="总时长"
                        value={pmiRecord.totalMinutes || 0}
                        suffix="分钟"
                        valueStyle={{ fontSize: '14px' }}
                    />
                </Col>
                <Col span={6}>
                    <Statistic
                        title="可用时长"
                        value={pmiRecord.availableMinutes || 0}
                        suffix="分钟"
                        valueStyle={{
                            fontSize: '14px',
                            color: pmiRecord.availableMinutes > 0 ? '#52c41a' : '#ff4d4f'
                        }}
                    />
                </Col>
                <Col span={6}>
                    <Statistic
                        title="待扣时长"
                        value={pmiRecord.pendingDeductMinutes || 0}
                        suffix="分钟"
                        valueStyle={{
                            fontSize: '14px',
                            color: pmiRecord.pendingDeductMinutes > 0 ? '#fa8c16' : '#666'
                        }}
                    />
                </Col>
                <Col span={6}>
                    <Statistic
                        title="超额时长"
                        value={pmiRecord.overdraftMinutes || 0}
                        suffix="分钟"
                        valueStyle={{
                            fontSize: '14px',
                            color: pmiRecord.overdraftMinutes > 0 ? '#ff4d4f' : '#666'
                        }}
                    />
                </Col>
            </Row>

            <div style={{ marginTop: 12 }}>
                <Badge status={getStatusColor()} text={getStatusText()} />
            </div>
        </Card>
    );
};
```

#### 1.2 计费流水表格组件
```jsx
const BillingRecordsTable = ({ pmiId, height = 400 }) => {
    const [records, setRecords] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });
    const [filters, setFilters] = useState({
        transactionType: null
    });

    const columns = [
        {
            title: '时间',
            dataIndex: 'createdAt',
            key: 'createdAt',
            width: 150,
            render: (time) => dayjs(time).format('MM-DD HH:mm:ss')
        },
        {
            title: '类型',
            dataIndex: 'transactionType',
            key: 'transactionType',
            width: 80,
            render: (type) => {
                const typeConfig = {
                    'RECHARGE': { color: 'green', text: '充值' },
                    'DEDUCT': { color: 'red', text: '扣费' },
                    'REFUND': { color: 'blue', text: '退款' },
                    'ADJUSTMENT': { color: 'orange', text: '调整' }
                };
                const config = typeConfig[type] || { color: 'default', text: type };
                return <Tag color={config.color}>{config.text}</Tag>;
            },
            filters: [
                { text: '充值', value: 'RECHARGE' },
                { text: '扣费', value: 'DEDUCT' },
                { text: '退款', value: 'REFUND' },
                { text: '调整', value: 'ADJUSTMENT' }
            ],
            onFilter: (value, record) => record.transactionType === value
        },
        {
            title: '变动时长',
            dataIndex: 'amountMinutes',
            key: 'amountMinutes',
            width: 100,
            render: (amount) => (
                <Text style={{
                    color: amount > 0 ? '#52c41a' : '#ff4d4f',
                    fontWeight: 'bold'
                }}>
                    {amount > 0 ? '+' : ''}{amount} 分钟
                </Text>
            )
        },
        {
            title: '变动前',
            dataIndex: 'balanceBefore',
            key: 'balanceBefore',
            width: 80,
            render: (balance) => `${balance} 分钟`
        },
        {
            title: '变动后',
            dataIndex: 'balanceAfter',
            key: 'balanceAfter',
            width: 80,
            render: (balance) => `${balance} 分钟`
        },
        {
            title: '说明',
            dataIndex: 'description',
            key: 'description',
            ellipsis: true,
            render: (text) => (
                <Tooltip title={text}>
                    <Text ellipsis style={{ maxWidth: 200 }}>{text}</Text>
                </Tooltip>
            )
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 80,
            render: (status) => {
                const statusConfig = {
                    'PENDING': { color: 'processing', text: '处理中' },
                    'COMPLETED': { color: 'success', text: '已完成' },
                    'FAILED': { color: 'error', text: '失败' },
                    'CANCELLED': { color: 'default', text: '已取消' }
                };
                const config = statusConfig[status] || { color: 'default', text: status };
                return <Badge status={config.color} text={config.text} />;
            }
        }
    ];

    const loadRecords = async (page = 1, pageSize = 10, transactionType = null) => {
        try {
            setLoading(true);
            const response = await pmiApi.getBillingRecords(pmiId, {
                page: page - 1,
                size: pageSize,
                transactionType
            });

            if (response.success) {
                setRecords(response.data.content);
                setPagination({
                    current: page,
                    pageSize,
                    total: response.data.totalElements
                });
            }
        } catch (error) {
            message.error('加载计费记录失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadRecords();
    }, [pmiId]);

    const handleTableChange = (paginationConfig, filtersConfig) => {
        const { current, pageSize } = paginationConfig;
        const { transactionType } = filtersConfig;

        setFilters({ transactionType: transactionType?.[0] || null });
        loadRecords(current, pageSize, transactionType?.[0]);
    };

    return (
        <Table
            columns={columns}
            dataSource={records}
            loading={loading}
            pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }}
            onChange={handleTableChange}
            scroll={{ y: height }}
            size="small"
            rowKey="id"
        />
    );
};
```

### 2. PMI管理组件

#### 2.1 PMI创建弹窗
```jsx
const CreatePmiModal = ({ visible, onCancel, onSuccess }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [pmiValidation, setPmiValidation] = useState(null);

    const handleSubmit = async (values) => {
        try {
            setLoading(true);
            const response = await pmiApi.createPmi(values);

            if (response.success) {
                message.success('PMI创建成功');
                form.resetFields();
                onSuccess?.(response.data);
                onCancel();
            } else {
                message.error(response.message || 'PMI创建失败');
            }
        } catch (error) {
            message.error('PMI创建失败');
        } finally {
            setLoading(false);
        }
    };

    const validatePmiNumber = async (pmiNumber) => {
        if (!pmiNumber || pmiNumber.length < 9) return;

        try {
            const response = await pmiApi.validatePmiNumber(pmiNumber);
            if (response.success) {
                setPmiValidation(response.data);
            }
        } catch (error) {
            console.error('PMI验证失败', error);
        }
    };

    const handlePmiNumberChange = (e) => {
        const value = e.target.value;
        setPmiValidation(null);

        // 防抖验证
        clearTimeout(window.pmiValidationTimer);
        window.pmiValidationTimer = setTimeout(() => {
            validatePmiNumber(value);
        }, 500);
    };

    return (
        <Modal
            title="创建PMI"
            open={visible}
            onCancel={onCancel}
            footer={null}
            width={600}
        >
            <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
            >
                <Form.Item
                    label="PMI号码"
                    name="pmiNumber"
                    rules={[
                        { required: true, message: '请输入PMI号码' },
                        { pattern: /^\d{9,11}$/, message: 'PMI号码应为9-11位数字' }
                    ]}
                    validateStatus={pmiValidation ? (pmiValidation.available ? 'success' : 'error') : ''}
                    help={pmiValidation?.message}
                >
                    <Input
                        placeholder="请输入9-11位PMI号码"
                        onChange={handlePmiNumberChange}
                        suffix={
                            pmiValidation && (
                                pmiValidation.available ?
                                    <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                                    <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                            )
                        }
                    />
                </Form.Item>

                <Form.Item
                    label="用户"
                    name="userId"
                    rules={[{ required: true, message: '请选择用户' }]}
                >
                    <UserSelect placeholder="请选择用户" />
                </Form.Item>

                <Form.Item
                    label="计费模式"
                    name="billingMode"
                    rules={[{ required: true, message: '请选择计费模式' }]}
                >
                    <Radio.Group>
                        <Radio value="BY_TIME">按时长计费</Radio>
                        <Radio value="LONG">按时段计费</Radio>
                    </Radio.Group>
                </Form.Item>

                <Form.Item
                    label="初始时长"
                    name="initialMinutes"
                    rules={[{ required: true, message: '请输入初始时长' }]}
                >
                    <InputNumber
                        min={0}
                        max={100000}
                        placeholder="分钟"
                        style={{ width: '100%' }}
                        formatter={value => `${value} 分钟`}
                        parser={value => value.replace(' 分钟', '')}
                    />
                </Form.Item>

                <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) =>
                        prevValues.billingMode !== currentValues.billingMode
                    }
                >
                    {({ getFieldValue }) => {
                        const billingMode = getFieldValue('billingMode');

                        if (billingMode === 'LONG') {
                            return (
                                <>
                                    <Form.Item
                                        label="窗口开始时间"
                                        name="windowStartTime"
                                        rules={[{ required: true, message: '请选择窗口开始时间' }]}
                                    >
                                        <DatePicker
                                            showTime
                                            format="YYYY-MM-DD HH:mm:ss"
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        label="窗口结束时间"
                                        name="windowEndTime"
                                        rules={[{ required: true, message: '请选择窗口结束时间' }]}
                                    >
                                        <DatePicker
                                            showTime
                                            format="YYYY-MM-DD HH:mm:ss"
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>
                                </>
                            );
                        }
                        return null;
                    }}
                </Form.Item>

                <Form.Item
                    label="创建说明"
                    name="description"
                >
                    <Input.TextArea
                        rows={3}
                        placeholder="请输入创建说明（可选）"
                    />
                </Form.Item>

                <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                    <Space>
                        <Button onClick={onCancel}>
                            取消
                        </Button>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={loading}
                            disabled={pmiValidation && !pmiValidation.available}
                        >
                            创建PMI
                        </Button>
                    </Space>
                </Form.Item>
            </Form>
        </Modal>
    );
};
```

#### 2.2 PMI编辑弹窗
```jsx
const EditPmiModal = ({ visible, onCancel, onSuccess, pmiRecord }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [pmiValidation, setPmiValidation] = useState(null);

    useEffect(() => {
        if (visible && pmiRecord) {
            form.setFieldsValue({
                newPmiNumber: pmiRecord.pmiNumber,
                billingMode: pmiRecord.billingMode,
                windowStartTime: pmiRecord.windowStartTime ? dayjs(pmiRecord.windowStartTime) : null,
                windowEndTime: pmiRecord.windowEndTime ? dayjs(pmiRecord.windowEndTime) : null
            });
        }
    }, [visible, pmiRecord, form]);

    const handleSubmit = async (values) => {
        try {
            setLoading(true);
            const response = await pmiApi.updatePmi(pmiRecord.id, values);

            if (response.success) {
                message.success('PMI修改成功');
                onSuccess?.(response.data);
                onCancel();
            } else {
                message.error(response.message || 'PMI修改失败');
            }
        } catch (error) {
            message.error('PMI修改失败');
        } finally {
            setLoading(false);
        }
    };

    const validatePmiNumber = async (pmiNumber) => {
        if (!pmiNumber || pmiNumber === pmiRecord?.pmiNumber) {
            setPmiValidation(null);
            return;
        }

        try {
            const response = await pmiApi.validatePmiNumber(pmiNumber);
            if (response.success) {
                setPmiValidation(response.data);
            }
        } catch (error) {
            console.error('PMI验证失败', error);
        }
    };

    return (
        <Modal
            title={`编辑PMI - ${pmiRecord?.pmiNumber}`}
            open={visible}
            onCancel={onCancel}
            footer={null}
            width={600}
        >
            <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
            >
                <Form.Item
                    label="PMI号码"
                    name="newPmiNumber"
                    rules={[
                        { required: true, message: '请输入PMI号码' },
                        { pattern: /^\d{9,11}$/, message: 'PMI号码应为9-11位数字' }
                    ]}
                    validateStatus={pmiValidation ? (pmiValidation.available ? 'success' : 'error') : ''}
                    help={pmiValidation?.message}
                >
                    <Input
                        placeholder="请输入9-11位PMI号码"
                        onChange={(e) => {
                            clearTimeout(window.pmiValidationTimer);
                            window.pmiValidationTimer = setTimeout(() => {
                                validatePmiNumber(e.target.value);
                            }, 500);
                        }}
                        suffix={
                            pmiValidation && (
                                pmiValidation.available ?
                                    <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                                    <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                            )
                        }
                    />
                </Form.Item>

                <Form.Item
                    label="计费模式"
                    name="billingMode"
                    rules={[{ required: true, message: '请选择计费模式' }]}
                >
                    <Radio.Group>
                        <Radio value="BY_TIME">按时长计费</Radio>
                        <Radio value="LONG">按时段计费</Radio>
                    </Radio.Group>
                </Form.Item>

                <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) =>
                        prevValues.billingMode !== currentValues.billingMode
                    }
                >
                    {({ getFieldValue }) => {
                        const billingMode = getFieldValue('billingMode');

                        if (billingMode === 'LONG') {
                            return (
                                <>
                                    <Form.Item
                                        label="窗口开始时间"
                                        name="windowStartTime"
                                        rules={[{ required: true, message: '请选择窗口开始时间' }]}
                                    >
                                        <DatePicker
                                            showTime
                                            format="YYYY-MM-DD HH:mm:ss"
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        label="窗口结束时间"
                                        name="windowEndTime"
                                        rules={[{ required: true, message: '请选择窗口结束时间' }]}
                                    >
                                        <DatePicker
                                            showTime
                                            format="YYYY-MM-DD HH:mm:ss"
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>
                                </>
                            );
                        }
                        return null;
                    }}
                </Form.Item>

                <Form.Item
                    label="修改说明"
                    name="description"
                >
                    <Input.TextArea
                        rows={3}
                        placeholder="请输入修改说明（可选）"
                    />
                </Form.Item>

                <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                    <Space>
                        <Button onClick={onCancel}>
                            取消
                        </Button>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={loading}
                            disabled={pmiValidation && !pmiValidation.available}
                        >
                            保存修改
                        </Button>
                    </Space>
                </Form.Item>
            </Form>
        </Modal>
    );
};
```

### 3. ZoomUser管理组件

#### 2.1 ZoomUser管理页面
```jsx
const ZoomUserManagement = () => {
    const [users, setUsers] = useState([]);
    const [loading, setLoading] = useState(false);
    const [statistics, setStatistics] = useState(null);
    const [filters, setFilters] = useState({
        usageStatus: null,
        keyword: ''
    });
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    useEffect(() => {
        loadUsers();
        loadStatistics();
    }, []);

    const loadUsers = async () => {
        try {
            setLoading(true);
            const response = await zoomUserApi.getUsers({
                page: pagination.current - 1,
                size: pagination.pageSize,
                ...filters
            });

            if (response.success) {
                setUsers(response.data.content);
                setPagination(prev => ({
                    ...prev,
                    total: response.data.totalElements
                }));
            }
        } catch (error) {
            message.error('加载ZoomUser列表失败');
        } finally {
            setLoading(false);
        }
    };

    const loadStatistics = async () => {
        try {
            const response = await zoomUserApi.getStatistics();
            if (response.success) {
                setStatistics(response.data);
            }
        } catch (error) {
            console.error('加载统计信息失败', error);
        }
    };

    const handleInitializePmi = async () => {
        Modal.confirm({
            title: '初始化PMI',
            content: '确定要初始化所有ZoomUser的原始PMI吗？这将从Zoom API获取每个账号的PMI信息。',
            okText: '确认初始化',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const response = await zoomUserApi.initializeAllPmi();
                    if (response.success) {
                        message.success('PMI初始化完成');
                        loadUsers();
                        loadStatistics();
                    } else {
                        message.error(response.message || '初始化失败');
                    }
                } catch (error) {
                    message.error('初始化失败');
                }
            }
        });
    };

    const handleReleaseUser = async (user) => {
        Modal.confirm({
            title: '释放ZoomUser',
            content: `确定要释放 ${user.email} 吗？这将恢复账号的原始PMI并设置为可用状态。`,
            okText: '确认释放',
            cancelText: '取消',
            okType: 'danger',
            onOk: async () => {
                try {
                    const response = await zoomUserApi.releaseUser(user.id);
                    if (response.success) {
                        message.success('ZoomUser已释放');
                        loadUsers();
                        loadStatistics();
                    } else {
                        message.error(response.message || '释放失败');
                    }
                } catch (error) {
                    message.error('释放失败');
                }
            }
        });
    };

    const handleMaintenanceToggle = async (user, maintenance) => {
        try {
            const response = await zoomUserApi.setMaintenance(user.id, maintenance);
            if (response.success) {
                message.success(maintenance ? '已设置为维护状态' : '已恢复为可用状态');
                loadUsers();
                loadStatistics();
            } else {
                message.error(response.message || '设置失败');
            }
        } catch (error) {
            message.error('设置失败');
        }
    };

    return (
        <div>
            {/* 统计信息 */}
            {statistics && (
                <ZoomUserStatisticsPanel
                    statistics={statistics}
                    onInitializePmi={handleInitializePmi}
                />
            )}

            {/* 筛选器 */}
            <Card size="small" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                    <Col span={6}>
                        <Select
                            placeholder="使用状态"
                            value={filters.usageStatus}
                            onChange={(value) => setFilters({ ...filters, usageStatus: value })}
                            style={{ width: '100%' }}
                            allowClear
                        >
                            <Option value="AVAILABLE">可用</Option>
                            <Option value="IN_USE">使用中</Option>
                            <Option value="MAINTENANCE">维护中</Option>
                        </Select>
                    </Col>
                    <Col span={8}>
                        <Input.Search
                            placeholder="搜索邮箱或姓名"
                            value={filters.keyword}
                            onChange={(e) => setFilters({ ...filters, keyword: e.target.value })}
                            onSearch={loadUsers}
                            allowClear
                        />
                    </Col>
                    <Col span={4}>
                        <Button type="primary" onClick={loadUsers} block>
                            查询
                        </Button>
                    </Col>
                </Row>
            </Card>

            {/* ZoomUser列表 */}
            <Table
                dataSource={users}
                loading={loading}
                columns={getZoomUserColumns(handleReleaseUser, handleMaintenanceToggle)}
                rowKey="id"
                pagination={{
                    ...pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                        `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    onChange: (page, pageSize) => {
                        setPagination({ ...pagination, current: page, pageSize });
                        loadUsers();
                    }
                }}
                scroll={{ x: 1400 }}
            />
        </div>
    );
};
```

#### 2.2 ZoomUser统计面板
```jsx
const ZoomUserStatisticsPanel = ({ statistics, onInitializePmi }) => {
    return (
        <Card title="ZoomUser使用统计" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
                <Col span={4}>
                    <Statistic
                        title="总账号数"
                        value={statistics.totalUsers}
                        prefix={<UserOutlined />}
                    />
                </Col>
                <Col span={4}>
                    <Statistic
                        title="可用账号"
                        value={statistics.availableUsers}
                        valueStyle={{ color: '#52c41a' }}
                        prefix={<CheckCircleOutlined />}
                    />
                </Col>
                <Col span={4}>
                    <Statistic
                        title="使用中"
                        value={statistics.inUseUsers}
                        valueStyle={{ color: '#1890ff' }}
                        prefix={<PlayCircleOutlined />}
                    />
                </Col>
                <Col span={4}>
                    <Statistic
                        title="维护中"
                        value={statistics.maintenanceUsers}
                        valueStyle={{ color: '#fa8c16' }}
                        prefix={<ToolOutlined />}
                    />
                </Col>
                <Col span={4}>
                    <Statistic
                        title="使用率"
                        value={statistics.usageRate}
                        precision={1}
                        suffix="%"
                        valueStyle={{
                            color: statistics.usageRate > 80 ? '#ff4d4f' : '#52c41a'
                        }}
                    />
                </Col>
                <Col span={4}>
                    <Button
                        type="primary"
                        icon={<SyncOutlined />}
                        onClick={onInitializePmi}
                        block
                    >
                        初始化PMI
                    </Button>
                </Col>
            </Row>
        </Card>
    );
};
```

#### 2.3 ZoomUser表格列定义
```jsx
const getZoomUserColumns = (onRelease, onMaintenanceToggle) => [
    {
        title: '邮箱',
        dataIndex: 'email',
        key: 'email',
        width: 200,
        ellipsis: true
    },
    {
        title: '姓名',
        key: 'name',
        width: 120,
        render: (_, record) => `${record.firstName || ''} ${record.lastName || ''}`.trim()
    },
    {
        title: '原始PMI',
        dataIndex: 'originalPmi',
        key: 'originalPmi',
        width: 120,
        render: (pmi) => pmi ? <Tag color="blue">{pmi}</Tag> : <Text type="secondary">未设置</Text>
    },
    {
        title: '当前PMI',
        dataIndex: 'currentPmi',
        key: 'currentPmi',
        width: 120,
        render: (pmi, record) => {
            if (!pmi) return <Text type="secondary">-</Text>;

            const isOriginal = pmi === record.originalPmi;
            return (
                <Tag color={isOriginal ? 'blue' : 'orange'}>
                    {pmi}
                    {!isOriginal && <span style={{ marginLeft: 4 }}>(临时)</span>}
                </Tag>
            );
        }
    },
    {
        title: '使用状态',
        dataIndex: 'usageStatus',
        key: 'usageStatus',
        width: 100,
        render: (status) => {
            const statusConfig = {
                'AVAILABLE': { color: 'success', text: '可用' },
                'IN_USE': { color: 'processing', text: '使用中' },
                'MAINTENANCE': { color: 'warning', text: '维护中' }
            };
            const config = statusConfig[status] || { color: 'default', text: status };
            return <Badge status={config.color} text={config.text} />;
        }
    },
    {
        title: '当前会议',
        dataIndex: 'currentMeetingId',
        key: 'currentMeetingId',
        width: 100,
        render: (meetingId) => meetingId ? (
            <Button size="small" type="link" onClick={() => viewMeeting(meetingId)}>
                会议 {meetingId}
            </Button>
        ) : <Text type="secondary">-</Text>
    },
    {
        title: '使用统计',
        key: 'usage',
        width: 120,
        render: (_, record) => (
            <div>
                <div>次数: {record.totalUsageCount || 0}</div>
                <div style={{ fontSize: 12, color: '#999' }}>
                    时长: {record.totalUsageMinutes || 0}分钟
                </div>
            </div>
        )
    },
    {
        title: '最后使用',
        dataIndex: 'lastUsedTime',
        key: 'lastUsedTime',
        width: 150,
        render: (time) => time ? dayjs(time).format('MM-DD HH:mm') : <Text type="secondary">从未使用</Text>
    },
    {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right',
        render: (_, record) => (
            <Space>
                {record.usageStatus === 'IN_USE' && (
                    <Button
                        size="small"
                        danger
                        onClick={() => onRelease(record)}
                    >
                        释放
                    </Button>
                )}

                {record.usageStatus === 'AVAILABLE' && (
                    <Button
                        size="small"
                        onClick={() => onMaintenanceToggle(record, true)}
                    >
                        维护
                    </Button>
                )}

                {record.usageStatus === 'MAINTENANCE' && (
                    <Button
                        size="small"
                        type="primary"
                        onClick={() => onMaintenanceToggle(record, false)}
                    >
                        恢复
                    </Button>
                )}

                <Button
                    size="small"
                    onClick={() => viewUserDetail(record)}
                >
                    详情
                </Button>
            </Space>
        )
    }
];
```

### 3. 会议看板组件

#### 2.1 会议状态卡片
```jsx
const MeetingStatusCard = ({ meeting, onEndMeeting, onViewDetail }) => {
    const getStatusConfig = (status) => {
        const configs = {
            'PENDING': { color: 'processing', text: '待开启', bgColor: '#e6f7ff' },
            'USING': { color: 'success', text: '进行中', bgColor: '#f6ffed' },
            'ENDED': { color: 'default', text: '已结束', bgColor: '#f5f5f5' },
            'SETTLED': { color: 'success', text: '已结算', bgColor: '#f6ffed' }
        };
        return configs[status] || configs['ENDED'];
    };

    const statusConfig = getStatusConfig(meeting.status);
    const duration = meeting.durationMinutes || 0;
    const billedMinutes = meeting.billedMinutes || 0;

    return (
        <Card
            size="small"
            style={{
                backgroundColor: statusConfig.bgColor,
                marginBottom: 8
            }}
            bodyStyle={{ padding: 12 }}
        >
            <Row align="middle" justify="space-between">
                <Col flex="auto">
                    <Space direction="vertical" size={4} style={{ width: '100%' }}>
                        <Space>
                            <Text strong>{meeting.pmiNumber}</Text>
                            <Badge status={statusConfig.color} text={statusConfig.text} />
                            <Tag color={meeting.billingMode === 'LONG' ? 'green' : 'blue'} size="small">
                                {meeting.billingMode === 'LONG' ? '按时段' : '按时长'}
                            </Tag>
                        </Space>

                        <Text ellipsis style={{ maxWidth: 300 }}>
                            {meeting.topic || '无主题'}
                        </Text>

                        <Space size={16}>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                                开始: {meeting.startTime ?
                                    dayjs(meeting.startTime).format('HH:mm:ss') : '-'}
                            </Text>
                            {duration > 0 && (
                                <Text type="secondary" style={{ fontSize: 12 }}>
                                    时长: {duration} 分钟
                                </Text>
                            )}
                            {meeting.billingMode === 'BY_TIME' && (
                                <Text type="secondary" style={{ fontSize: 12 }}>
                                    计费: {billedMinutes} 分钟
                                </Text>
                            )}
                        </Space>
                    </Space>
                </Col>

                <Col>
                    <Space>
                        <Button
                            size="small"
                            onClick={() => onViewDetail?.(meeting)}
                        >
                            详情
                        </Button>
                        {meeting.status === 'USING' && (
                            <Button
                                size="small"
                                danger
                                onClick={() => onEndMeeting?.(meeting)}
                            >
                                结束
                            </Button>
                        )}
                    </Space>
                </Col>
            </Row>
        </Card>
    );
};
```

#### 2.2 会议统计面板
```jsx
const MeetingStatisticsPanel = () => {
    const [statistics, setStatistics] = useState(null);
    const [loading, setLoading] = useState(false);

    const loadStatistics = async () => {
        try {
            setLoading(true);
            const response = await meetingApi.getStatistics();
            if (response.success) {
                setStatistics(response.data);
            }
        } catch (error) {
            message.error('加载统计信息失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadStatistics();
        const interval = setInterval(loadStatistics, 30000); // 30秒刷新一次
        return () => clearInterval(interval);
    }, []);

    if (loading || !statistics) {
        return <Skeleton active />;
    }

    return (
        <Row gutter={16}>
            <Col span={6}>
                <Card size="small">
                    <Statistic
                        title="总会议数"
                        value={statistics.totalMeetings}
                        prefix={<VideoCameraOutlined />}
                    />
                </Card>
            </Col>
            <Col span={6}>
                <Card size="small">
                    <Statistic
                        title="进行中"
                        value={statistics.usingMeetings}
                        valueStyle={{ color: '#52c41a' }}
                        prefix={<PlayCircleOutlined />}
                    />
                </Card>
            </Col>
            <Col span={6}>
                <Card size="small">
                    <Statistic
                        title="今日会议"
                        value={statistics.todayMeetings}
                        prefix={<CalendarOutlined />}
                    />
                </Card>
            </Col>
            <Col span={6}>
                <Card size="small">
                    <Statistic
                        title="今日时长"
                        value={statistics.todayBilledMinutes}
                        suffix="分钟"
                        prefix={<ClockCircleOutlined />}
                    />
                </Card>
            </Col>
        </Row>
    );
};
```

---

## 🔍 高级功能扩展

### 1. 自动化任务

#### 1.1 定时任务调度器
```java
@Component
public class BillingTaskScheduler {

    /**
     * 每小时检查长时间运行的会议
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void checkLongRunningMeetings() {
        List<ZoomMeeting> longRunningMeetings = zoomMeetingService
            .findLongRunningMeetings(Duration.ofHours(12));

        for (ZoomMeeting meeting : longRunningMeetings) {
            // 发送告警
            alertService.sendLongRunningMeetingAlert(meeting);

            // 记录日志
            log.warn("发现长时间运行会议: PMI={}, 会议ID={}, 开始时间={}",
                meeting.getPmiRecord().getPmiNumber(),
                meeting.getZoomMeetingId(),
                meeting.getStartTime());
        }
    }

    /**
     * 每日生成计费报表
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    public void generateDailyBillingReport() {
        LocalDate yesterday = LocalDate.now().minusDays(1);

        try {
            // 生成系统级报表
            SystemBillingStatistics statistics = billingStatisticsService.getSystemStatistics();
            dailyReportService.generateSystemReport(yesterday, statistics);

            // 为有活动的PMI生成个人报表
            List<Long> activePmiIds = pmiRecordRepository.findActivePmiIdsWithActivity(yesterday);
            for (Long pmiId : activePmiIds) {
                dailyReportService.generatePmiReport(pmiId, yesterday);
            }

            log.info("每日计费报表生成完成，日期: {}", yesterday);
        } catch (Exception e) {
            log.error("生成每日计费报表失败", e);
        }
    }

    /**
     * 每月清理过期数据
     */
    @Scheduled(cron = "0 0 3 1 * ?") // 每月1号凌晨3点
    public void cleanupExpiredData() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMonths(6);

        try {
            // 清理6个月前的计费记录
            int deletedRecords = pmiBillingRecordRepository.deleteByCreatedAtBefore(cutoffTime);

            // 清理已结算的会议记录
            int deletedMeetings = zoomMeetingRepository.deleteByStatusAndEndTimeBefore(
                MeetingStatus.SETTLED, cutoffTime);

            log.info("数据清理完成，删除计费记录: {}, 删除会议记录: {}", deletedRecords, deletedMeetings);
        } catch (Exception e) {
            log.error("数据清理失败", e);
        }
    }
}
```

### 2. 告警通知系统

#### 2.1 告警服务
```java
@Service
public class AlertService {

    @Autowired
    private NotificationService notificationService;

    /**
     * 超额使用告警
     */
    public void sendOverdraftAlert(BillingEvent event) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(event.getPmiId()).orElse(null);
        if (pmiRecord == null) return;

        String message = String.format(
            "PMI %s 存在超额使用 %d 分钟，用户: %s，请及时处理",
            pmiRecord.getPmiNumber(),
            pmiRecord.getOverdraftMinutes(),
            pmiRecord.getUser().getFullName()
        );

        // 发送邮件通知
        notificationService.sendEmail(
            getAdminEmails(),
            "PMI超额使用告警",
            message
        );

        // 发送系统通知
        notificationService.sendSystemNotification(
            NotificationType.OVERDRAFT_ALERT,
            message,
            pmiRecord.getUserId()
        );
    }

    /**
     * 长时间运行会议告警
     */
    public void sendLongRunningMeetingAlert(ZoomMeeting meeting) {
        Duration runningTime = Duration.between(meeting.getStartTime(), LocalDateTime.now());

        String message = String.format(
            "会议运行时间过长: PMI %s, 会议ID %s, 已运行 %d 小时",
            meeting.getPmiRecord().getPmiNumber(),
            meeting.getZoomMeetingId(),
            runningTime.toHours()
        );

        notificationService.sendEmail(
            getAdminEmails(),
            "长时间运行会议告警",
            message
        );
    }

    /**
     * 余额不足告警
     */
    public void sendLowBalanceAlert(Long pmiId, int remainingMinutes) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiId).orElse(null);
        if (pmiRecord == null) return;

        String message = String.format(
            "PMI %s 余额不足，剩余 %d 分钟，用户: %s",
            pmiRecord.getPmiNumber(),
            remainingMinutes,
            pmiRecord.getUser().getFullName()
        );

        // 通知用户
        notificationService.sendEmail(
            Collections.singletonList(pmiRecord.getUser().getEmail()),
            "PMI余额不足提醒",
            message
        );

        // 通知管理员
        notificationService.sendEmail(
            getAdminEmails(),
            "PMI余额不足告警",
            message
        );
    }

    private List<String> getAdminEmails() {
        return adminUserRepository.findAll().stream()
            .map(AdminUser::getEmail)
            .collect(Collectors.toList());
    }
}
```

### 3. 数据导出和备份

#### 3.1 数据导出服务
```java
@Service
public class DataExportService {

    /**
     * 导出PMI计费数据
     */
    public ByteArrayResource exportPmiBillingData(Long pmiId, LocalDate startDate, LocalDate endDate,
                                                 ExportFormat format) {
        PmiBillingStatistics statistics = billingStatisticsService
            .getPmiBillingStatistics(pmiId, startDate, endDate);

        List<PmiBillingRecord> records = pmiBillingRecordRepository
            .findByPmiRecordIdAndCreatedAtBetween(pmiId,
                startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay());

        switch (format) {
            case EXCEL:
                return exportToExcel(statistics, records);
            case CSV:
                return exportToCsv(statistics, records);
            case PDF:
                return exportToPdf(statistics, records);
            default:
                throw new IllegalArgumentException("不支持的导出格式: " + format);
        }
    }

    /**
     * 导出系统计费汇总
     */
    public ByteArrayResource exportSystemBillingSummary(LocalDate startDate, LocalDate endDate) {
        SystemBillingStatistics systemStats = billingStatisticsService.getSystemStatistics();

        // 获取所有PMI的计费统计
        List<PmiBillingStatistics> pmiStatsList = pmiRecordRepository.findAll().stream()
            .map(pmi -> billingStatisticsService.getPmiBillingStatistics(pmi.getId(), startDate, endDate))
            .collect(Collectors.toList());

        return generateSystemSummaryExcel(systemStats, pmiStatsList, startDate, endDate);
    }

    private ByteArrayResource exportToExcel(PmiBillingStatistics statistics,
                                          List<PmiBillingRecord> records) {
        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建汇总表
            Sheet summarySheet = workbook.createSheet("计费汇总");
            createSummarySheet(summarySheet, statistics);

            // 创建明细表
            Sheet detailSheet = workbook.createSheet("流水明细");
            createDetailSheet(detailSheet, records);

            // 创建图表表
            Sheet chartSheet = workbook.createSheet("统计图表");
            createChartSheet(chartSheet, statistics, records);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return new ByteArrayResource(outputStream.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    private void createChartSheet(Sheet sheet, PmiBillingStatistics statistics,
                                List<PmiBillingRecord> records) {
        // 创建图表数据
        Map<String, Integer> dailyUsage = records.stream()
            .filter(r -> r.getTransactionType() == TransactionType.DEDUCT)
            .collect(Collectors.groupingBy(
                r -> r.getCreatedAt().toLocalDate().toString(),
                Collectors.summingInt(r -> Math.abs(r.getAmountMinutes()))
            ));

        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("日期");
        headerRow.createCell(1).setCellValue("使用时长(分钟)");

        // 填充数据
        int rowNum = 1;
        for (Map.Entry<String, Integer> entry : dailyUsage.entrySet()) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(entry.getKey());
            row.createCell(1).setCellValue(entry.getValue());
        }

        // 自动调整列宽
        sheet.autoSizeColumn(0);
        sheet.autoSizeColumn(1);
    }
}
```

### 4. 移动端API适配

#### 4.1 移动端专用控制器
```java
@RestController
@RequestMapping("/api/mobile/pmi")
@Slf4j
public class MobilePmiBillingController {

    /**
     * 移动端获取PMI简要信息
     */
    @GetMapping("/{pmiId}/summary")
    public ResponseEntity<ApiResponse<MobilePmiSummary>> getPmiSummary(@PathVariable Long pmiId) {
        try {
            PmiRecord pmiRecord = pmiRecordService.findById(pmiId);

            MobilePmiSummary summary = MobilePmiSummary.builder()
                .pmiNumber(pmiRecord.getPmiNumber())
                .billingMode(pmiRecord.getBillingMode())
                .availableMinutes(pmiRecord.getAvailableMinutes())
                .overdraftMinutes(pmiRecord.getOverdraftMinutes())
                .canStartMeeting(pmiRecord.getOverdraftMinutes() == 0)
                .statusText(getBillingStatusText(pmiRecord))
                .statusColor(getBillingStatusColor(pmiRecord))
                .build();

            return ResponseEntity.ok(ApiResponse.success(summary));
        } catch (Exception e) {
            log.error("获取PMI摘要失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取信息失败"));
        }
    }

    /**
     * 移动端快速充值
     */
    @PostMapping("/{pmiId}/quick-recharge")
    public ResponseEntity<ApiResponse<QuickRechargeResult>> quickRecharge(
            @PathVariable Long pmiId,
            @RequestBody QuickRechargeRequest request) {
        try {
            // 验证快速充值金额
            if (!isValidQuickRechargeAmount(request.getMinutes())) {
                return ResponseEntity.ok(ApiResponse.error("无效的充值金额"));
            }

            RechargeResult result = pmiRechargeService.rechargeMinutes(
                pmiId, request.getMinutes(), "移动端快速充值");

            QuickRechargeResult quickResult = QuickRechargeResult.builder()
                .success(result.isSuccess())
                .rechargeAmount(result.getRechargeAmount())
                .newAvailableMinutes(result.getAvailableMinutesAfter())
                .canStartMeeting(result.isCanStartMeeting())
                .message(generateQuickRechargeMessage(result))
                .build();

            return ResponseEntity.ok(ApiResponse.success(quickResult));
        } catch (Exception e) {
            log.error("移动端快速充值失败", e);
            return ResponseEntity.ok(ApiResponse.error("充值失败"));
        }
    }

    /**
     * 移动端获取最近流水
     */
    @GetMapping("/{pmiId}/recent-records")
    public ResponseEntity<ApiResponse<List<MobileBillingRecord>>> getRecentRecords(
            @PathVariable Long pmiId,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<PmiBillingRecord> records = pmiBillingRecordService
                .getRecentRecords(pmiId, limit);

            List<MobileBillingRecord> mobileRecords = records.stream()
                .map(this::convertToMobileRecord)
                .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success(mobileRecords));
        } catch (Exception e) {
            log.error("获取最近流水失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取流水失败"));
        }
    }

    private boolean isValidQuickRechargeAmount(int minutes) {
        List<Integer> validAmounts = Arrays.asList(60, 300, 600, 720, 6000); // 1h, 5h, 10h, 12h, 100h
        return validAmounts.contains(minutes);
    }

    private String generateQuickRechargeMessage(RechargeResult result) {
        if (result.getOverdraftSettled() > 0) {
            return String.format("充值成功！结清超额%d分钟，可用时长%d分钟",
                result.getOverdraftSettled(), result.getAvailableMinutesAfter());
        }
        return String.format("充值成功！可用时长%d分钟", result.getAvailableMinutesAfter());
    }

    private MobileBillingRecord convertToMobileRecord(PmiBillingRecord record) {
        return MobileBillingRecord.builder()
            .id(record.getId())
            .type(record.getTransactionType().toString())
            .typeText(getTransactionTypeText(record.getTransactionType()))
            .amount(record.getAmountMinutes())
            .amountText(formatAmountText(record.getAmountMinutes()))
            .time(record.getCreatedAt())
            .timeText(formatTimeText(record.getCreatedAt()))
            .description(record.getDescription())
            .build();
    }

    private String getTransactionTypeText(TransactionType type) {
        switch (type) {
            case RECHARGE: return "充值";
            case DEDUCT: return "扣费";
            case REFUND: return "退款";
            case ADJUSTMENT: return "调整";
            default: return type.toString();
        }
    }

    private String formatAmountText(int amount) {
        if (amount > 0) {
            return "+" + amount + "分钟";
        } else {
            return amount + "分钟";
        }
    }

    private String formatTimeText(LocalDateTime time) {
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(time, now);

        if (duration.toDays() > 0) {
            return duration.toDays() + "天前";
        } else if (duration.toHours() > 0) {
            return duration.toHours() + "小时前";
        } else if (duration.toMinutes() > 0) {
            return duration.toMinutes() + "分钟前";
        } else {
            return "刚刚";
        }
    }
}
```

### 5. 数据分析和洞察

#### 5.1 数据分析服务
```java
@Service
public class BillingAnalyticsService {

    /**
     * 分析用户使用模式
     */
    public UserUsagePattern analyzeUserUsagePattern(Long userId, int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);

        // 获取用户的所有PMI
        List<PmiRecord> userPmis = pmiRecordRepository.findByUserId(userId);

        // 分析会议使用情况
        List<ZoomMeeting> meetings = zoomMeetingRepository
            .findByPmiRecordInAndStartTimeAfter(userPmis, startTime);

        // 计算使用模式
        Map<Integer, Long> hourlyUsage = meetings.stream()
            .collect(Collectors.groupingBy(
                m -> m.getStartTime().getHour(),
                Collectors.counting()
            ));

        Map<DayOfWeek, Long> weeklyUsage = meetings.stream()
            .collect(Collectors.groupingBy(
                m -> m.getStartTime().getDayOfWeek(),
                Collectors.counting()
            ));

        int totalMeetings = meetings.size();
        int totalMinutes = meetings.stream().mapToInt(ZoomMeeting::getBilledMinutes).sum();
        double avgMeetingDuration = totalMeetings > 0 ? (double) totalMinutes / totalMeetings : 0;

        // 找出最活跃的时间段
        int peakHour = hourlyUsage.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(9); // 默认9点

        DayOfWeek peakDay = weeklyUsage.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(DayOfWeek.MONDAY);

        return UserUsagePattern.builder()
            .userId(userId)
            .analysisStartDate(startTime.toLocalDate())
            .analysisEndDate(LocalDate.now())
            .totalMeetings(totalMeetings)
            .totalMinutes(totalMinutes)
            .avgMeetingDuration(avgMeetingDuration)
            .peakHour(peakHour)
            .peakDay(peakDay)
            .hourlyUsage(hourlyUsage)
            .weeklyUsage(weeklyUsage)
            .usageLevel(calculateUsageLevel(totalMeetings, days))
            .recommendations(generateRecommendations(hourlyUsage, weeklyUsage, avgMeetingDuration))
            .build();
    }

    /**
     * 预测用户时长需求
     */
    public UsagePrediction predictUsageNeeds(Long userId, int predictionDays) {
        // 获取历史30天的使用数据
        LocalDateTime startTime = LocalDateTime.now().minusDays(30);
        List<PmiRecord> userPmis = pmiRecordRepository.findByUserId(userId);
        List<ZoomMeeting> historicalMeetings = zoomMeetingRepository
            .findByPmiRecordInAndStartTimeAfter(userPmis, startTime);

        // 计算日均使用量
        Map<LocalDate, Integer> dailyUsage = historicalMeetings.stream()
            .collect(Collectors.groupingBy(
                m -> m.getStartTime().toLocalDate(),
                Collectors.summingInt(ZoomMeeting::getBilledMinutes)
            ));

        double avgDailyUsage = dailyUsage.values().stream()
            .mapToInt(Integer::intValue)
            .average()
            .orElse(0.0);

        // 考虑趋势因子
        double trendFactor = calculateTrendFactor(dailyUsage);

        // 预测未来需求
        int predictedUsage = (int) (avgDailyUsage * predictionDays * trendFactor);

        // 当前可用时长
        int currentAvailable = userPmis.stream()
            .mapToInt(PmiRecord::getAvailableMinutes)
            .sum();

        // 计算建议充值金额
        int recommendedRecharge = Math.max(0, predictedUsage - currentAvailable + 120); // 额外120分钟缓冲

        return UsagePrediction.builder()
            .userId(userId)
            .predictionDays(predictionDays)
            .avgDailyUsage(avgDailyUsage)
            .trendFactor(trendFactor)
            .predictedUsage(predictedUsage)
            .currentAvailable(currentAvailable)
            .recommendedRecharge(recommendedRecharge)
            .confidence(calculatePredictionConfidence(dailyUsage))
            .build();
    }

    private UsageLevel calculateUsageLevel(int totalMeetings, int days) {
        double dailyAvg = (double) totalMeetings / days;
        if (dailyAvg >= 3) return UsageLevel.HIGH;
        if (dailyAvg >= 1) return UsageLevel.MEDIUM;
        return UsageLevel.LOW;
    }

    private List<String> generateRecommendations(Map<Integer, Long> hourlyUsage,
                                               Map<DayOfWeek, Long> weeklyUsage,
                                               double avgDuration) {
        List<String> recommendations = new ArrayList<>();

        // 基于使用时间的建议
        int peakHour = hourlyUsage.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(9);

        if (peakHour >= 9 && peakHour <= 17) {
            recommendations.add("您主要在工作时间使用会议，建议选择按时段计费模式");
        } else {
            recommendations.add("您经常在非工作时间使用会议，按时长计费可能更经济");
        }

        // 基于会议时长的建议
        if (avgDuration > 120) {
            recommendations.add("您的会议时长较长，建议提前充值足够的时长");
        } else if (avgDuration < 30) {
            recommendations.add("您的会议时长较短，可以考虑批量处理多个议题");
        }

        return recommendations;
    }

    private double calculateTrendFactor(Map<LocalDate, Integer> dailyUsage) {
        // 简单的线性趋势计算
        List<LocalDate> dates = dailyUsage.keySet().stream()
            .sorted()
            .collect(Collectors.toList());

        if (dates.size() < 7) return 1.0; // 数据不足，使用默认因子

        // 比较最近7天和之前7天的平均使用量
        int recentDays = Math.min(7, dates.size());
        double recentAvg = dates.subList(dates.size() - recentDays, dates.size()).stream()
            .mapToInt(date -> dailyUsage.getOrDefault(date, 0))
            .average()
            .orElse(0.0);

        double previousAvg = dates.subList(Math.max(0, dates.size() - 14), dates.size() - recentDays).stream()
            .mapToInt(date -> dailyUsage.getOrDefault(date, 0))
            .average()
            .orElse(0.0);

        if (previousAvg == 0) return 1.0;

        double trend = recentAvg / previousAvg;
        return Math.max(0.5, Math.min(2.0, trend)); // 限制在0.5-2.0之间
    }

    private double calculatePredictionConfidence(Map<LocalDate, Integer> dailyUsage) {
        if (dailyUsage.size() < 7) return 0.3; // 数据不足，低置信度

        // 计算使用量的标准差
        double avg = dailyUsage.values().stream().mapToInt(Integer::intValue).average().orElse(0.0);
        double variance = dailyUsage.values().stream()
            .mapToDouble(usage -> Math.pow(usage - avg, 2))
            .average()
            .orElse(0.0);
        double stdDev = Math.sqrt(variance);

        // 标准差越小，置信度越高
        double coefficient = avg > 0 ? stdDev / avg : 1.0;
        return Math.max(0.1, Math.min(0.9, 1.0 - coefficient));
    }
}
```

---

## 📋 实施计划

### 第一阶段：核心功能开发 (第1-2周)

#### 1.1 数据库设计和实现
- ✅ **数据表创建**: 创建和修改相关数据表
  - 扩展 `t_pmi_records` 表，添加计费相关字段
  - 创建 `t_zoom_meetings` 表
  - 创建 `t_pmi_billing_records` 表
  - 创建 `t_pmi_billing_config` 表
- ✅ **索引优化**: 创建必要的数据库索引
- ✅ **数据迁移**: 现有数据的迁移脚本

#### 1.2 后端核心服务
- ✅ **计费模式管理**: `PmiBillingModeService`
- ✅ **充值服务**: `PmiRechargeService`
- ✅ **会议记录服务**: `ZoomMeetingService`
- ✅ **结算服务**: `MeetingSettlementService`
- ✅ **验证服务**: `PmiUsageValidationService`
- ✅ **实时计费监控**: `BillingMonitorService`

#### 1.3 API接口开发
- ✅ **PMI计费API**: 获取计费信息、充值、验证使用
- ✅ **会议看板API**: 活跃会议列表、会议详情、手动结束
- ✅ **流水记录API**: 计费流水查询、导出

### 第二阶段：前端界面开发 (第2-3周)

#### 2.1 管理端界面
- ✅ **PMI充值弹窗**: 完整的充值界面和预览功能
- ✅ **会议看板页面**: 实时会议监控和管理
- ✅ **计费信息展示**: PMI计费状态和统计
- ✅ **流水记录表格**: 计费流水查询和筛选

#### 2.2 用户端界面
- ✅ **PMI状态展示**: 简化的计费状态显示
- ✅ **开启验证**: 超额时长阻止开启的提示

#### 2.3 移动端适配
- ✅ **响应式设计**: 所有界面的移动端适配
- ✅ **移动端API**: 专门的移动端接口
- ✅ **快速操作**: 移动端快速充值功能

### 第三阶段：高级功能和优化 (第3-4周)

#### 3.1 自动化任务
- ✅ **定时任务**: 窗口到期检查、长时间会议监控
- ✅ **数据清理**: 过期数据自动清理
- ✅ **报表生成**: 自动生成日报、月报

#### 3.2 告警通知
- ✅ **告警系统**: 超额使用、余额不足、长时间会议告警
- ✅ **通知服务**: 邮件通知、系统通知
- ✅ **告警配置**: 可配置的告警阈值

#### 3.3 数据分析
- ✅ **使用模式分析**: 用户使用习惯分析
- ✅ **需求预测**: 基于历史数据的使用预测
- ✅ **智能推荐**: 计费模式和充值建议

### 第四阶段：测试和部署 (第4周)

#### 4.1 测试验证
- ✅ **单元测试**: 核心业务逻辑测试
- ✅ **集成测试**: API接口和数据库集成测试
- ✅ **性能测试**: 并发计费和大数据量测试
- ✅ **用户测试**: 界面交互和用户体验测试

#### 4.2 部署上线
- ✅ **数据库升级**: 生产环境数据库结构升级
- ✅ **应用部署**: 后端服务和前端应用部署
- ✅ **监控配置**: 生产环境监控和告警配置
- ✅ **文档完善**: 用户手册和运维文档

---

## 🎯 功能特性总结

### 核心功能特性

#### 1. **双计费模式支持** 🔄
- **按时段计费(LONG)**: 窗口期内无限使用，适合固定时间段的重度用户
- **按时长计费(BY_TIME)**: 按分钟精确计费，适合灵活使用的用户
- **自动切换**: 窗口开启/关闭时自动切换计费模式

#### 2. **智能充值系统** 💰
- **优先级结算**: 超额时长 → 待扣时长 → 可用时长
- **实时预览**: 充值前显示详细的时长分配预览
- **快捷选项**: 1小时、5小时、10小时、12小时、100小时快捷充值
- **移动端支持**: 专门的移动端快速充值功能

#### 3. **精确计费监控** ⏱️
- **分钟级计费**: 每分钟自动增加计费时长
- **实时监控**: 会议状态和计费状态实时同步
- **自动结算**: 会议结束时自动执行结算流程
- **超额处理**: 余额不足时记录超额时长，阻止新会议开启

#### 4. **完整的会议看板** 📊
- **实时状态**: 显示所有活跃会议的实时状态
- **多维筛选**: 按状态、计费模式、关键词筛选
- **批量操作**: 支持批量结算和管理
- **统计分析**: 实时统计和趋势分析

#### 5. **安全控制机制** 🔒
- **超额阻止**: 存在超额时长时禁止开启新会议
- **权限控制**: 基于角色的访问控制
- **操作日志**: 详细的操作审计记录
- **数据加密**: 敏感数据加密存储

### 技术特性

#### 1. **高性能架构** ⚡
- **异步处理**: 计费监控和结算异步执行
- **缓存优化**: Redis缓存热点数据
- **数据库优化**: 索引优化和分区策略
- **批量处理**: 支持批量操作和处理

#### 2. **可扩展设计** 📈
- **微服务架构**: 模块化设计，易于扩展
- **配置化**: 计费规则和告警阈值可配置
- **插件化**: 支持自定义计费规则和通知方式
- **API标准化**: RESTful API设计，易于集成

#### 3. **智能分析** 🧠
- **使用模式分析**: 分析用户使用习惯和偏好
- **需求预测**: 基于历史数据预测未来需求
- **智能推荐**: 推荐最适合的计费模式和充值策略
- **异常检测**: 自动检测异常使用模式

#### 4. **运维友好** 🛠️
- **自动化任务**: 定时清理、报表生成、状态检查
- **监控告警**: 多层次的监控和告警机制
- **数据备份**: 自动数据备份和恢复
- **性能监控**: 实时性能指标监控

### 用户体验特性

#### 1. **直观的界面设计** 🎨
- **状态可视化**: 清晰的状态指示和进度显示
- **响应式设计**: 完美适配桌面端和移动端
- **交互友好**: 简洁直观的操作流程
- **实时反馈**: 操作结果的实时反馈

#### 2. **便捷的操作流程** 🚀
- **一键充值**: 快捷充值选项和预览功能
- **智能提示**: 根据使用情况提供智能建议
- **批量操作**: 支持批量管理和操作
- **快速导航**: 便捷的页面导航和搜索

#### 3. **完善的帮助系统** 📚
- **操作指南**: 详细的操作说明和帮助文档
- **错误提示**: 友好的错误提示和解决建议
- **FAQ支持**: 常见问题和解答
- **在线帮助**: 上下文相关的帮助信息

---

## 🔮 未来扩展规划

### 短期扩展 (1-3个月)

#### 1. **支付集成** 💳
- 集成支付宝、微信支付等第三方支付
- 支持在线充值和自动扣费
- 发票管理和财务对账

#### 2. **高级报表** 📈
- 更丰富的数据可视化图表
- 自定义报表和仪表板
- 数据导出和分享功能

#### 3. **API开放** 🔌
- 开放API供第三方系统集成
- Webhook事件推送
- SDK和文档支持

### 中期扩展 (3-6个月)

#### 1. **AI智能优化** 🤖
- 基于机器学习的使用预测
- 智能计费策略推荐
- 异常行为自动检测

#### 2. **多租户支持** 🏢
- 企业级多租户架构
- 独立的计费策略和配置
- 数据隔离和安全控制

#### 3. **国际化支持** 🌍
- 多语言界面支持
- 多货币计费支持
- 时区和本地化适配

### 长期扩展 (6-12个月)

#### 1. **云原生架构** ☁️
- 容器化部署和编排
- 微服务架构重构
- 弹性伸缩和负载均衡

#### 2. **大数据分析** 📊
- 实时数据流处理
- 大数据分析平台
- 商业智能和决策支持

#### 3. **生态系统建设** 🌱
- 开发者社区和生态
- 第三方插件市场
- 合作伙伴集成平台

---

## 📋 文档优化改进总结

### 🎯 本次优化重点

根据用户反馈，本次对accounting.md文档进行了全面优化，主要改进了以下几个方面：

#### 1. **会议看板功能增强** 🚀

##### 1.1 手工结束会议功能完善
- ✅ **Zoom API联动**: 结束会议时同步调用Zoom API结束远程会议
- ✅ **事务一致性**: 确保Zoom API调用和本地数据库操作的事务一致性
- ✅ **完整结算流程**: 自动停止计费监控 → 调用Zoom API → 更新会议状态 → 执行结算
- ✅ **详细结果反馈**: 返回完整的结束结果，包括时长、费用、结算状态等
- ✅ **错误处理**: 完善的异常处理和错误提示

##### 1.2 会议看板界面优化
- ✅ **标签页设计**: 明确分为"进行中会议"和"历史会议"两个标签页
- ✅ **默认展示**: 默认显示进行中的会议（PENDING、USING状态）
- ✅ **过往会议查询**: 提供专门的历史会议查询入口，支持日期范围筛选
- ✅ **实时刷新**: 进行中会议每30秒自动刷新
- ✅ **高级筛选**: 支持计费模式、关键词、日期范围等多维度筛选

##### 1.3 会议明细信息完善
- ✅ **计费类型展示**: 清晰显示按时段/按时长计费模式
- ✅ **实时计时**: 进行中会议显示实时计时器，每分钟更新
- ✅ **计费状态**: 详细的计费状态文本和进度显示
- ✅ **费用预估**: 实时计算和显示预估费用
- ✅ **计费详情弹窗**: 专门的计费详情查看功能

#### 2. **数据对象扩展** 📊

##### 2.1 新增数据对象
```java
// 会议结束结果对象
MeetingEndResult {
    meetingId, pmiNumber, endTime, totalDuration,
    billedMinutes, zoomApiSuccess, settlementCompleted,
    message, finalCost
}

// 会议计费详情对象
MeetingBillingDetail {
    meetingId, pmiNumber, billingMode, startTime, endTime,
    currentDuration, billedMinutes, isSettled, realTimeBilling,
    estimatedCost, billingRecords, billingStatus
}

// 增强的会议VO对象
ZoomMeetingVO {
    // 基础信息 + 实时计时 + 计费状态 + 费用信息
    currentDuration, realTimeBilling, estimatedCost,
    billingStatusText, statusColor, durationText, costText
}
```

##### 2.2 API接口扩展
- ✅ **历史会议查询**: `GET /api/zoom-meetings/history`
- ✅ **会议计费详情**: `GET /api/zoom-meetings/{id}/billing-detail`
- ✅ **增强的结束会议**: `POST /api/zoom-meetings/{id}/end`

#### 3. **前端组件增强** 🎨

##### 3.1 新增组件
- ✅ **实时计时器组件**: `RealTimeCounter` - 显示进行中会议的实时时长
- ✅ **会议计费详情弹窗**: `MeetingBillingDetailModal` - 详细的计费信息展示
- ✅ **活跃会议标签页**: `ActiveMeetingsTab` - 专门的进行中会议管理
- ✅ **历史会议标签页**: `HistoryMeetingsTab` - 过往会议查询和管理

##### 3.2 表格列优化
- ✅ **实时时长列**: 显示进行中会议的实时计时
- ✅ **计费状态列**: 详细的计费状态和进度信息
- ✅ **预估费用列**: 实时计算的费用预估
- ✅ **操作列增强**: 计费详情、结束会议等操作

#### 4. **用户体验提升** ✨

##### 4.1 交互优化
- ✅ **确认对话框**: 结束会议前的确认提示
- ✅ **结果展示**: 结束会议后的详细结果展示
- ✅ **实时更新**: 自动刷新和实时数据更新
- ✅ **状态指示**: 清晰的状态颜色和图标

##### 4.2 信息展示
- ✅ **分类展示**: 进行中和历史会议分开展示
- ✅ **详细信息**: 更丰富的会议和计费信息
- ✅ **实时反馈**: 操作结果的即时反馈
- ✅ **错误提示**: 友好的错误信息和处理建议

### 🔧 技术改进

#### 1. **后端服务增强**
- ✅ **事务管理**: `@Transactional` 确保数据一致性
- ✅ **异常处理**: 完善的异常捕获和处理
- ✅ **日志记录**: 详细的操作日志和审计
- ✅ **API响应**: 标准化的API响应格式

#### 2. **前端架构优化**
- ✅ **组件化**: 高度组件化的界面设计
- ✅ **状态管理**: 清晰的状态管理和数据流
- ✅ **性能优化**: 合理的刷新频率和数据加载
- ✅ **用户体验**: 流畅的交互和反馈

### 📈 功能完整性

经过本次优化，PMI计费系统已经达到了企业级应用的标准：

1. ✅ **功能完整**: 涵盖会议监控、管理、结算、ZoomUser管理的完整流程
2. ✅ **数据准确**: 实时计费、精确结算、完整记录、PMI状态同步
3. ✅ **操作便捷**: 直观的界面、简单的操作、清晰的反馈
4. ✅ **信息丰富**: 详细的计费信息、实时的状态更新、ZoomUser使用统计
5. ✅ **扩展性强**: 模块化设计、易于扩展和维护
6. ✅ **资源管理**: 智能的ZoomUser分配和释放机制

### 🔧 本次新增的PMI管理和ZoomUser功能

#### 1. **PMI创建和修改管理** 🎯
- ✅ **ZoomUser分配**: 创建/修改PMI时自动分配可用ZoomUser账号
- ✅ **Zoom API集成**: 通过ZoomUser账号调用Zoom API设置PMI
- ✅ **自动释放**: 无论操作成功失败都自动释放ZoomUser账号
- ✅ **PMI验证**: 实时验证PMI号码可用性
- ✅ **批量操作**: 支持批量创建PMI记录

#### 2. **ZoomUser PMI管理** 🎯
- ✅ **原始PMI登记**: 从Zoom API自动获取并保存账号原始PMI
- ✅ **PMI状态同步**: 会议开始时更新PMI，结束时恢复原始PMI
- ✅ **PMI操作支持**: 支持PMI创建/修改操作的账号分配和释放
- ✅ **批量初始化**: 支持批量初始化所有ZoomUser的原始PMI
- ✅ **状态管理**: AVAILABLE、IN_USE、MAINTENANCE三种状态

#### 3. **智能账号分配** 🤖
- ✅ **会议分配**: 会议开始时自动分配可用的ZoomUser账号
- ✅ **PMI操作分配**: PMI创建/修改时自动分配ZoomUser账号
- ✅ **PMI更新**: 自动将ZoomUser的PMI更新为目标PMI
- ✅ **状态跟踪**: 记录当前会议ID和使用状态
- ✅ **使用统计**: 记录使用次数和时长统计
- ✅ **操作标记**: 使用特殊标记(-1)区分PMI操作和会议使用

#### 4. **自动释放机制** 🔄
- ✅ **会议结束释放**: meeting.ended webhook触发自动释放
- ✅ **手动结束释放**: 管理员手动结束会议时自动释放
- ✅ **PMI操作释放**: PMI创建/修改完成后自动释放（无论成功失败）
- ✅ **PMI恢复**: 自动恢复ZoomUser的原始PMI
- ✅ **状态重置**: 重置为AVAILABLE状态，清空会议关联
- ✅ **异常处理**: 即使Zoom API调用失败也确保本地状态正确释放

#### 5. **管理界面完善** 📊
- ✅ **PMI管理界面**: 完整的PMI创建、编辑、删除功能
- ✅ **PMI验证**: 实时PMI号码可用性验证
- ✅ **ZoomUser管理页面**: 完整的ZoomUser列表和管理功能
- ✅ **使用统计面板**: 实时显示账号使用情况和统计
- ✅ **手动操作**: 支持手动释放、维护状态设置
- ✅ **筛选查询**: 按状态、关键词筛选ZoomUser
- ✅ **批量操作**: 支持批量创建PMI和批量初始化

### 🎯 下一步计划

1. **性能优化**: 大数据量下的查询优化和ZoomUser分配算法优化
2. **移动端适配**: 会议看板和ZoomUser管理的移动端专用界面
3. **批量操作**: 批量结束会议、批量结算、批量ZoomUser操作
4. **数据导出**: 会议报表、计费报表、ZoomUser使用报表导出
5. **告警通知**: 长时间会议、异常计费、ZoomUser不足等告警
6. **负载均衡**: ZoomUser分配的负载均衡和优化策略

---

## 📋 ZoomUser PMI管理功能总结

### 🎯 核心改进点

本次完善主要解决了ZoomUser账号的PMI管理和自动释放问题：

#### 1. **数据库扩展**
```sql
-- ZoomUser表新增字段
ALTER TABLE t_zoom_users ADD COLUMN (
    original_pmi VARCHAR(50),           -- 原始默认PMI
    current_pmi VARCHAR(50),            -- 当前使用PMI
    usage_status ENUM(...),             -- 使用状态
    current_meeting_id BIGINT,          -- 当前会议ID
    last_used_time DATETIME,            -- 最后使用时间
    total_usage_count INT,              -- 总使用次数
    total_usage_minutes INT             -- 总使用时长
);
```

#### 2. **业务流程完善**
- ✅ **PMI初始化**: 系统启动时从Zoom API获取原始PMI
- ✅ **智能分配**: 会议开始时自动分配可用ZoomUser
- ✅ **PMI同步**: 动态更新ZoomUser的PMI为目标PMI
- ✅ **自动释放**: 会议结束时恢复原始PMI并释放账号

#### 3. **服务层实现**
```java
ZoomUserPmiService {
    initializeOriginalPmi()      // 初始化原始PMI
    assignZoomUserForMeeting()   // 分配账号给会议
    releaseZoomUser()            // 释放账号
    getUsageStatistics()         // 获取使用统计
}
```

#### 4. **前端管理界面**
- ✅ **ZoomUser管理页面**: 完整的账号列表和状态管理
- ✅ **使用统计面板**: 实时统计和可视化展示
- ✅ **操作功能**: 手动释放、维护状态设置、PMI初始化

### 🔄 完整的业务闭环

1. **初始化阶段**: 系统获取所有ZoomUser的原始PMI并保存
2. **会议开始**: 自动分配ZoomUser → 更新PMI → 设置使用中状态
3. **会议进行**: 实时监控会议状态和计费情况
4. **会议结束**: 自动恢复原始PMI → 释放账号 → 重置状态
5. **管理监控**: 实时查看ZoomUser使用情况和统计信息

### 🛡️ 异常处理机制

- ✅ **API调用失败**: Zoom API调用失败时的降级处理
- ✅ **状态不一致**: 本地状态与Zoom状态不一致时的修复
- ✅ **资源不足**: 没有可用ZoomUser时的处理策略
- ✅ **手动干预**: 管理员可手动释放和维护ZoomUser

---

## 📋 PMI创建修改的ZoomUser管理总结

### 🎯 核心改进点

本次完善主要解决了PMI创建和修改时的ZoomUser使用和释放问题：

#### 1. **PMI管理服务扩展**
```java
PmiManagementService {
    createPmiRecord()     // 创建PMI，使用ZoomUser设置
    updatePmiSettings()   // 修改PMI，使用ZoomUser更新
    deletePmiRecord()     // 删除PMI，使用ZoomUser清理
}
```

#### 2. **ZoomUser分配机制**
- ✅ **PMI操作分配**: `assignZoomUserForPmiOperation(operationType)`
- ✅ **特殊标记**: 使用`currentMeetingId = -1`标记PMI操作
- ✅ **状态管理**: 设置为`IN_USE`状态，记录操作类型
- ✅ **自动释放**: `releaseZoomUserFromOperation(zoomUserId)`

#### 3. **完整的业务流程**
1. **PMI创建/修改开始** → 分配可用ZoomUser
2. **设置使用状态** → 标记为PMI操作使用中
3. **执行Zoom API** → 通过ZoomUser设置PMI
4. **保存本地记录** → 创建/更新PMI记录
5. **恢复原始PMI** → 恢复ZoomUser原始PMI
6. **释放账号** → 重置为可用状态

#### 4. **异常处理保障**
- ✅ **try-finally模式**: 确保无论成功失败都释放ZoomUser
- ✅ **API失败处理**: Zoom API调用失败时的降级处理
- ✅ **状态一致性**: 本地状态与Zoom状态的同步机制
- ✅ **日志记录**: 详细的操作日志和错误记录

#### 5. **前端界面支持**
- ✅ **PMI创建弹窗**: 完整的PMI创建界面和验证
- ✅ **PMI编辑弹窗**: PMI信息修改和设置更新
- ✅ **实时验证**: PMI号码可用性实时验证
- ✅ **批量操作**: 批量创建PMI功能

### 🔄 新增业务流程图

1. **PMI创建和修改流程**: 完整的ZoomUser分配和释放流程
2. **PMI操作的ZoomUser生命周期**: 详细的状态转换和处理机制
3. **ZoomUser状态管理流程**: 支持会议使用和PMI操作两种场景

### 🛡️ 可靠性保障

- ✅ **资源保护**: 确保ZoomUser账号不会被长期占用
- ✅ **状态一致**: 本地状态与Zoom平台状态保持同步
- ✅ **异常恢复**: 操作失败时的自动恢复机制
- ✅ **并发安全**: 多个PMI操作的并发处理

现在PMI计费系统已经形成了完整的ZoomUser资源管理体系，支持：
- 🎯 **会议使用**: 会议开始时分配，结束时释放
- 🎯 **PMI操作**: 创建/修改时分配，完成后释放
- 🎯 **状态管理**: 统一的状态管理和转换机制
- 🎯 **异常处理**: 完善的异常处理和恢复机制

---

*文档版本: v1.3*
*最后更新: 2025-07-31*
*状态: 设计完成，已完善PMI创建修改的ZoomUser管理*
*预计开发周期: 3-4周*
*主要更新: 新增PMI管理服务、ZoomUser PMI操作分配释放、管理界面*
*下次更新: 根据开发进度和需求变更*
