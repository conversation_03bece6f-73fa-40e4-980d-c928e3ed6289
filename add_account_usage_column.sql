-- 添加账户用途字段到 t_zoom_users 表
-- 执行时间: 2025-07-18

USE zoombusV;

-- 检查字段是否已存在
SELECT COUNT(*) as field_exists
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'zoombusV'
  AND TABLE_NAME = 't_zoom_users'
  AND COLUMN_NAME = 'account_usage';

-- 添加 account_usage 列（如果不存在）
-- 注意：MySQL 5.7 及以下版本不支持 ADD COLUMN IF NOT EXISTS 语法
-- 如果字段已存在，此语句会报错，但不会影响数据
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV'
       AND TABLE_NAME = 't_zoom_users'
       AND COLUMN_NAME = 'account_usage') = 0,
    'ALTER TABLE t_zoom_users ADD COLUMN account_usage VARCHAR(50) NULL COMMENT ''账户用途: PUBLIC_MEETING(参会账号-公共), PRIVATE_MEETING(参会账号-专属), PUBLIC_HOST(开会账号-公共), PRIVATE_HOST(开会账号-专属)''',
    'SELECT ''account_usage column already exists'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建索引以提高查询性能（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'zoombusV'
       AND TABLE_NAME = 't_zoom_users'
       AND INDEX_NAME = 'idx_zoom_users_account_usage') = 0,
    'CREATE INDEX idx_zoom_users_account_usage ON t_zoom_users(account_usage)',
    'SELECT ''idx_zoom_users_account_usage index already exists'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'zoombusV'
  AND TABLE_NAME = 't_zoom_users'
  AND COLUMN_NAME = 'account_usage';

-- 查看表结构
DESCRIBE t_zoom_users;
