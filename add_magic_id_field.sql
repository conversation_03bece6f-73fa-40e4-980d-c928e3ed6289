-- 为t_pmi_records表添加magic_id字段
-- 执行时间：2025-08-14

USE zoombusV;

-- 添加magic_id字段
ALTER TABLE t_pmi_records 
ADD COLUMN magic_id VARCHAR(50) COMMENT '魔链ID，用于生成不变的用户访问链接' 
AFTER pmi_number;

-- 为magic_id字段添加唯一索引
ALTER TABLE t_pmi_records 
ADD UNIQUE KEY uk_magic_id (magic_id);

-- 为现有记录初始化magic_id（使用pmi_number作为默认值）
UPDATE t_pmi_records 
SET magic_id = pmi_number 
WHERE magic_id IS NULL;

-- 设置magic_id字段为非空
ALTER TABLE t_pmi_records 
MODIFY COLUMN magic_id VARCHAR(50) NOT NULL COMMENT '魔链ID，用于生成不变的用户访问链接';

-- 验证数据
SELECT COUNT(*) as total_records, 
       COUNT(DISTINCT magic_id) as unique_magic_ids,
       COUNT(DISTINCT pmi_number) as unique_pmi_numbers
FROM t_pmi_records;

-- 显示前10条记录验证
SELECT id, pmi_number, magic_id, created_at 
FROM t_pmi_records 
ORDER BY id 
LIMIT 10;
