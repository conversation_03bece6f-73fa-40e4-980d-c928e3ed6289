-- 为t_zoom_users表添加PMI相关字段
-- 执行时间：2025-08-01

USE zoombusV;

-- 添加账号使用状态字段
ALTER TABLE t_zoom_users 
ADD COLUMN account_status VARCHAR(20) DEFAULT 'AVAILABLE' COMMENT '账号使用状态: AVAILABLE-可使用, IN_USE-使用中, MAINTENANCE-维护中' 
AFTER in_use;

-- 添加原始PMI字段
ALTER TABLE t_zoom_users 
ADD COLUMN original_pmi VARCHAR(20) COMMENT '原始PMI号码' 
AFTER account_status;

-- 添加当前PMI字段
ALTER TABLE t_zoom_users 
ADD COLUMN current_pmi VARCHAR(20) COMMENT '当前PMI号码' 
AFTER original_pmi;

-- 添加PMI更新时间字段
ALTER TABLE t_zoom_users 
ADD COLUMN pmi_updated_at DATETIME COMMENT 'PMI最后更新时间' 
AFTER current_pmi;

-- 为新字段添加索引
ALTER TABLE t_zoom_users ADD INDEX idx_account_status (account_status);
ALTER TABLE t_zoom_users ADD INDEX idx_original_pmi (original_pmi);
ALTER TABLE t_zoom_users ADD INDEX idx_current_pmi (current_pmi);

-- 初始化现有数据的账号状态
UPDATE t_zoom_users 
SET account_status = CASE 
    WHEN in_use = 1 THEN 'IN_USE'
    ELSE 'AVAILABLE'
END;

-- 验证字段添加成功
DESCRIBE t_zoom_users;

-- 显示表结构确认
SHOW CREATE TABLE t_zoom_users;
