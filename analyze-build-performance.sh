#!/bin/bash

# 构建性能分析脚本
# 分析前端构建的性能瓶颈

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "=== 前端构建性能分析 ==="

# 分析系统环境
log_info "分析系统环境..."
echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"
echo "系统内存: $(free -h 2>/dev/null || echo "macOS - 使用 activity monitor 查看")"
echo "CPU核心数: $(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo "未知")"

# 分析管理端前端
if [ -d "frontend" ]; then
    log_info "分析管理端前端..."
    cd frontend
    
    echo "依赖包数量: $(ls node_modules 2>/dev/null | wc -l || echo "0")"
    echo "node_modules大小: $(du -sh node_modules 2>/dev/null || echo "未安装")"
    
    # 分析package.json依赖
    if [ -f "package.json" ]; then
        DEPS_COUNT=$(node -e "const pkg=require('./package.json'); console.log(Object.keys(pkg.dependencies || {}).length)")
        DEV_DEPS_COUNT=$(node -e "const pkg=require('./package.json'); console.log(Object.keys(pkg.devDependencies || {}).length)")
        echo "生产依赖数量: $DEPS_COUNT"
        echo "开发依赖数量: $DEV_DEPS_COUNT"
    fi
    
    # 检查是否有package-lock.json
    if [ -f "package-lock.json" ]; then
        log_success "有package-lock.json，可以使用npm ci加速安装"
    else
        log_warning "没有package-lock.json，建议运行npm install生成"
    fi
    
    cd ..
fi

# 分析用户端前端
if [ -d "user-frontend" ]; then
    log_info "分析用户端前端..."
    cd user-frontend
    
    echo "依赖包数量: $(ls node_modules 2>/dev/null | wc -l || echo "0")"
    echo "node_modules大小: $(du -sh node_modules 2>/dev/null || echo "未安装")"
    
    if [ -f "package.json" ]; then
        DEPS_COUNT=$(node -e "const pkg=require('./package.json'); console.log(Object.keys(pkg.dependencies || {}).length)")
        DEV_DEPS_COUNT=$(node -e "const pkg=require('./package.json'); console.log(Object.keys(pkg.devDependencies || {}).length)")
        echo "生产依赖数量: $DEPS_COUNT"
        echo "开发依赖数量: $DEV_DEPS_COUNT"
    fi
    
    if [ -f "package-lock.json" ]; then
        log_success "有package-lock.json，可以使用npm ci加速安装"
    else
        log_warning "没有package-lock.json，建议运行npm install生成"
    fi
    
    cd ..
fi

# 性能优化建议
echo ""
log_info "性能优化建议:"
echo "1. 使用npm ci代替npm install（如果有package-lock.json）"
echo "2. 设置NODE_OPTIONS='--max-old-space-size=4096'增加内存限制"
echo "3. 禁用source map生成: GENERATE_SOURCEMAP=false"
echo "4. 禁用ESLint检查: DISABLE_ESLINT_PLUGIN=true"
echo "5. 使用--silent参数减少输出"
echo "6. 启用代码分割减少单个bundle大小"

# 检查是否可以使用yarn
if command -v yarn &> /dev/null; then
    log_info "检测到yarn，可以考虑使用yarn代替npm获得更好性能"
else
    log_info "可以考虑安装yarn: npm install -g yarn"
fi

# 检查磁盘空间
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    log_warning "磁盘使用率较高($DISK_USAGE%)，可能影响构建性能"
fi

log_success "分析完成！"
