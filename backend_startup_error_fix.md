# 后端启动错误修复

## 🐛 错误原因

**Hibernate查询错误**：`could not resolve property: inUse of: com.zoombus.entity.ZoomUser`

### 根本原因
在简化ZoomUser状态字段时，我们移除了`inUse`字段，但是有些Repository查询方法和Service代码仍然在使用这个已删除的字段。

## 🔍 受影响的代码

### 1. Repository查询方法
```java
// ZoomUserRepository.java - 第95行和第104行
@Query("SELECT zu FROM ZoomUser zu WHERE zu.inUse = :inUse ...")
List<ZoomUser> findAvailablePublicHostUsers(@Param("inUse") Integer inUse, ...);

@Query("SELECT zu FROM ZoomUser zu WHERE zu.inUse = :inUse ... ORDER BY zu.id")
List<ZoomUser> findAvailablePublicHostUsersOrdered(@Param("inUse") Integer inUse, ...);
```

### 2. Service层调用
```java
// PublicPmiController.java - 第143行
List<ZoomUser> allUsers = zoomUserRepository.findAvailablePublicHostUsers(
    0, ZoomUser.UserType.LICENSED, ...); // 使用了已删除的inUse参数

// PmiService.java - 多处
zoomUser.setInUse(1); // 调用了已删除的setInUse方法
zoomUser.setInUse(0); // 调用了已删除的setInUse方法
```

### 3. 默认方法调用
```java
// ZoomUserRepository.java - 第114行
List<ZoomUser> users = findAvailablePublicHostUsersOrdered(
    0, // 传递了Integer类型，但方法期望UsageStatus枚举
    ZoomUser.UserType.LICENSED, ...);
```

## 🔧 修复方案

### 1. 更新Repository查询方法

#### 修复前
```java
@Query("SELECT zu FROM ZoomUser zu WHERE zu.inUse = :inUse ...")
List<ZoomUser> findAvailablePublicHostUsers(@Param("inUse") Integer inUse, ...);
```

#### 修复后
```java
@Query("SELECT zu FROM ZoomUser zu WHERE zu.usageStatus = :usageStatus ...")
List<ZoomUser> findAvailablePublicHostUsers(@Param("usageStatus") ZoomUser.UsageStatus usageStatus, ...);
```

### 2. 更新默认方法调用

#### 修复前
```java
List<ZoomUser> users = findAvailablePublicHostUsersOrdered(
    0, // Integer类型
    ZoomUser.UserType.LICENSED, ...);
```

#### 修复后
```java
List<ZoomUser> users = findAvailablePublicHostUsersOrdered(
    ZoomUser.UsageStatus.AVAILABLE, // 枚举类型
    ZoomUser.UserType.LICENSED, ...);
```

### 3. 更新Service层调用

#### 修复前
```java
// 查询调用
List<ZoomUser> allUsers = zoomUserRepository.findAvailablePublicHostUsers(
    0, ZoomUser.UserType.LICENSED, ...);

// 状态设置
zoomUser.setInUse(1); // 设置为使用中
zoomUser.setInUse(0); // 设置为可用
```

#### 修复后
```java
// 查询调用
List<ZoomUser> allUsers = zoomUserRepository.findAvailablePublicHostUsers(
    ZoomUser.UsageStatus.AVAILABLE, ZoomUser.UserType.LICENSED, ...);

// 状态设置
zoomUser.setUsageStatus(ZoomUser.UsageStatus.IN_USE);
zoomUser.setCurrentMeetingId(meetingId);

zoomUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
zoomUser.setCurrentMeetingId(null);
```

## ✅ 修复的文件

### 1. ZoomUserRepository.java
- ✅ 修复`findAvailablePublicHostUsers`查询方法
- ✅ 修复`findAvailablePublicHostUsersOrdered`查询方法  
- ✅ 修复`findFirstAvailablePublicHostUser`默认方法

### 2. PublicPmiController.java
- ✅ 修复调试信息中的查询调用
- ✅ 修复异常处理中的状态恢复逻辑

### 3. PmiService.java
- ✅ 修复`generatePmi`方法中的状态设置
- ✅ 修复`usePmi`方法中的状态设置
- ✅ 修复所有异常处理中的状态恢复逻辑

## 📊 修复效果

### 编译结果
```bash
$ ./mvnw compile -q
# 编译成功，无错误
```

### 启动结果
```
2025-08-03 00:25:47.938  INFO 55093 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
...
2025-08-03 00:27:04.190 DEBUG 55093 --- [ng-scheduler-10] c.zoombus.service.BillingMonitorService  : 会议 10 计费 +1 分钟，总计费: 20 分钟
```

### 功能验证
- ✅ **后端启动成功**：无Hibernate查询错误
- ✅ **数据库连接正常**：可以看到SQL绑定参数日志
- ✅ **计费监控工作**：会议ID为10的计费正在正常运行
- ✅ **状态管理正确**：使用新的UsageStatus枚举

## 🎯 状态字段对比

### 修复前的问题
| 字段 | 类型 | 问题 |
|------|------|------|
| `inUse` | Integer | 已删除但仍被查询使用 |
| `setInUse()` | Method | 已删除但仍被调用 |

### 修复后的解决方案
| 字段 | 类型 | 用途 |
|------|------|------|
| `usageStatus` | UsageStatus枚举 | 统一的状态管理 |
| `currentMeetingId` | Long | 关联当前会议 |

### 状态值映射
| 旧值 | 新值 | 含义 |
|------|------|------|
| `inUse = 0` | `usageStatus = AVAILABLE` | 可用状态 |
| `inUse = 1` | `usageStatus = IN_USE` | 使用中状态 |
| N/A | `usageStatus = MAINTENANCE` | 维护中状态 |

## 🚀 优化效果

### 1. 类型安全
- ✅ **枚举类型**：比Integer更安全，编译时检查
- ✅ **语义明确**：AVAILABLE/IN_USE/MAINTENANCE比0/1更清晰

### 2. 功能完整
- ✅ **三种状态**：支持可用、使用中、维护中
- ✅ **关联信息**：currentMeetingId提供会议关联

### 3. 代码一致性
- ✅ **统一接口**：所有地方都使用相同的状态枚举
- ✅ **减少错误**：消除了状态不一致的可能性

## 📝 后续建议

### 1. 完全移除旧字段（可选）
如果确认系统稳定，可以考虑从数据库中完全移除旧字段：
```sql
-- 在确认系统稳定后执行
ALTER TABLE t_zoom_accounts DROP COLUMN in_use;
ALTER TABLE t_zoom_accounts DROP COLUMN account_status;
```

### 2. 添加数据一致性检查
```sql
-- 定期检查状态一致性
SELECT COUNT(*) FROM t_zoom_meetings zm
JOIN t_zoom_accounts zu ON zm.assigned_zoom_user_id = zu.id
WHERE zm.status = 'USING' AND zu.usage_status != 'IN_USE';
```

### 3. 监控状态转换
添加日志监控，确保状态转换的正确性：
```java
log.info("ZoomUser状态变更: userId={}, {} -> {}", 
        userId, oldStatus, newStatus);
```

## ✅ 修复完成

现在后端应用已经成功启动，所有的状态字段简化相关的错误都已修复：

1. **Hibernate查询错误**：已修复所有使用已删除字段的查询
2. **方法调用错误**：已更新所有使用已删除方法的代码
3. **类型不匹配错误**：已统一使用UsageStatus枚举
4. **功能完整性**：保持了所有原有功能

系统现在使用统一的`usageStatus`枚举进行状态管理，更加安全、清晰和可维护！🎉
