-- MySQL dump 10.13  Distrib 8.0.11, for macos10.13 (x86_64)
--
-- Host: localhost    Database: zoombusV
-- ------------------------------------------------------
-- Server version	8.0.11

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
 SET NAMES utf8mb4 ;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `t_pmi_records`
--

DROP TABLE IF EXISTS `t_pmi_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
 SET character_set_client = utf8mb4 ;
CREATE TABLE `t_pmi_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NOT NULL,
  `current_zoom_user_id` bigint(20) DEFAULT NULL,
  `host_url` text,
  `join_url` text,
  `last_used_at` datetime(6) DEFAULT NULL,
  `pmi_number` varchar(10) NOT NULL,
  `magic_id` varchar(50) NOT NULL COMMENT '魔链ID，用于生成不变的用户访问链接',
  `pmi_password` varchar(50) NOT NULL,
  `status` varchar(255) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `user_id` bigint(20) NOT NULL,
  `billing_mode` enum('LONG','BY_TIME') DEFAULT 'BY_TIME' COMMENT '计费模式：LONG-按时段，BY_TIME-按时长',
  `current_window_id` bigint(20) DEFAULT NULL COMMENT '当前关联的时间窗口ID',
  `window_expire_time` datetime DEFAULT NULL COMMENT '窗口到期时间',
  `total_minutes` int(11) DEFAULT '0' COMMENT '总时长(分钟)',
  `available_minutes` int(11) DEFAULT '0' COMMENT '可用时长(分钟)',
  `pending_deduct_minutes` int(11) DEFAULT '0' COMMENT '待扣除时长(分钟)',
  `overdraft_minutes` int(11) DEFAULT '0' COMMENT '超额时长(分钟)',
  `total_used_minutes` int(11) DEFAULT '0' COMMENT '总使用时长(分钟)',
  `billing_status` enum('ACTIVE','SUSPENDED','EXPIRED') DEFAULT 'ACTIVE' COMMENT '计费状态',
  `last_billing_time` datetime DEFAULT NULL COMMENT '最后计费时间',
  `billing_updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `active_window_ids` text,
  `original_billing_mode` varchar(255) DEFAULT NULL,
  `fallback_enabled` tinyint(1) DEFAULT '0' COMMENT '是否启用回退到老系统',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_5xscujm9xiebrxoo1hogpm80n` (`pmi_number`),
  UNIQUE KEY `uk_magic_id` (`magic_id`),
  KEY `idx_pmi_fallback_enabled` (`fallback_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_pmi_records`
--

LOCK TABLES `t_pmi_records` WRITE;
/*!40000 ALTER TABLE `t_pmi_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_pmi_records` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-20 17:17:53
