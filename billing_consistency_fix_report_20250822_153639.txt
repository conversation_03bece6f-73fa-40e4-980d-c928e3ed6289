PMI计费一致性服务修复报告
========================

修复时间: Fri Aug 22 15:36:39 CST 2025
修复内容: 
1. 修复了PmiBillingConsistencyService中错误的过期时间计算逻辑
2. 恢复了20个被误关闭的长期窗口
3. 恢复了20个PMI的LONG计费模式
4. 增强了日志记录，便于问题追踪

修复前问题:
- 使用windowDate而不是endDate计算过期时间
- 导致长期窗口被提前关闭
- PMI计费模式被错误切换回BY_TIME

修复后改进:
- 正确使用endDate计算过期时间
- 增加详细的调试日志
- 与其他服务逻辑保持一致

测试结果:
- 应用状态: UP
- 恢复窗口数量: 20个
- 恢复PMI数量: 20个
- 所有窗口状态: ACTIVE
- 所有PMI计费模式: LONG

备份文件:
- t_pmi_schedule_windows_backup_20250822
- t_pmi_records_backup_20250822

修复脚本:
- fix_missclosed_windows_20250822.sql
- restore_pmi_billing_modes_20250822.sql
