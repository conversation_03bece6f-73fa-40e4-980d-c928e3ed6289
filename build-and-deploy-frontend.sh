#!/bin/bash

# ZoomBus 前端构建和部署脚本
# 此脚本用于构建前端并将构建结果复制到后端静态资源目录

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
BACKEND_STATIC_DIR="$PROJECT_ROOT/src/main/resources/static"

echo -e "${BLUE}=== ZoomBus 前端构建和部署脚本 ===${NC}"
echo "项目根目录: $PROJECT_ROOT"
echo "前端目录: $FRONTEND_DIR"
echo "后端静态资源目录: $BACKEND_STATIC_DIR"
echo

# 检查目录是否存在
if [ ! -d "$FRONTEND_DIR" ]; then
    echo -e "${RED}错误: 前端目录不存在: $FRONTEND_DIR${NC}"
    exit 1
fi

if [ ! -d "$BACKEND_STATIC_DIR" ]; then
    echo -e "${RED}错误: 后端静态资源目录不存在: $BACKEND_STATIC_DIR${NC}"
    exit 1
fi

# 进入前端目录
cd "$FRONTEND_DIR"

echo -e "${YELLOW}步骤 1: 清理旧的构建文件...${NC}"
if [ -d "build" ]; then
    rm -rf build
    echo "已删除旧的构建目录"
fi

echo -e "${YELLOW}步骤 2: 安装依赖...${NC}"
if [ ! -d "node_modules" ]; then
    echo "正在安装 npm 依赖..."
    npm install
else
    echo "依赖已存在，跳过安装"
fi

echo -e "${YELLOW}步骤 3: 构建前端应用...${NC}"
npm run build

# 检查构建是否成功
if [ ! -d "build" ]; then
    echo -e "${RED}错误: 前端构建失败，build 目录不存在${NC}"
    exit 1
fi

echo -e "${GREEN}前端构建成功！${NC}"

echo -e "${YELLOW}步骤 4: 清理后端静态资源目录...${NC}"
# 备份重要文件（如果存在）
if [ -f "$BACKEND_STATIC_DIR/favicon.ico" ]; then
    cp "$BACKEND_STATIC_DIR/favicon.ico" "/tmp/favicon.ico.backup"
fi

# 清理旧文件
rm -rf "$BACKEND_STATIC_DIR"/*
echo "已清理后端静态资源目录"

echo -e "${YELLOW}步骤 5: 复制构建文件到后端...${NC}"
cp -r build/* "$BACKEND_STATIC_DIR/"

# 恢复备份文件
if [ -f "/tmp/favicon.ico.backup" ]; then
    cp "/tmp/favicon.ico.backup" "$BACKEND_STATIC_DIR/favicon.ico"
    rm "/tmp/favicon.ico.backup"
fi

echo -e "${GREEN}构建文件已成功复制到后端静态资源目录${NC}"

# 显示构建信息
echo -e "${BLUE}=== 构建信息 ===${NC}"
if [ -f "$BACKEND_STATIC_DIR/asset-manifest.json" ]; then
    echo "Asset Manifest:"
    cat "$BACKEND_STATIC_DIR/asset-manifest.json"
else
    echo -e "${YELLOW}警告: 未找到 asset-manifest.json${NC}"
fi

echo
echo -e "${GREEN}=== 构建和部署完成！ ===${NC}"
echo -e "${YELLOW}注意: 请重启后端服务器以加载新的静态文件${NC}"
echo
echo "重启命令示例:"
echo "  ./start.sh"
echo
