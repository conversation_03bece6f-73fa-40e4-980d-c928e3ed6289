#!/bin/bash

# 优化的前端构建脚本
# 解决deploy.sh构建卡顿问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置Node.js环境变量以减少警告
export NODE_OPTIONS="--no-deprecation --max-old-space-size=4096"

# 构建管理端前端（优化版）
build_admin_frontend_optimized() {
    log_info "开始优化构建管理端前端..."

    cd frontend

    # 检查node_modules是否存在且是最新的
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        log_info "安装/更新管理端前端依赖..."
        # 使用npm ci进行更快的安装（如果有package-lock.json）
        if [ -f "package-lock.json" ]; then
            npm ci --silent
        else
            npm install --silent
        fi
    else
        log_info "依赖已是最新，跳过安装"
    fi

    # 清理之前的构建
    if [ -d "build" ]; then
        rm -rf build
        log_info "清理旧的构建文件"
    fi

    # 设置构建环境变量
    export GENERATE_SOURCEMAP=false
    export DISABLE_ESLINT_PLUGIN=true
    export TSC_COMPILE_ON_ERROR=true
    export FAST_REFRESH=false

    # 构建前端（静默模式减少输出）
    log_info "执行管理端前端构建（优化模式）..."
    log_warning "构建过程可能需要1-2分钟，请耐心等待..."
    
    # 显示进度
    npm run build --silent &
    BUILD_PID=$!
    
    # 显示构建进度
    while kill -0 $BUILD_PID 2>/dev/null; do
        echo -n "."
        sleep 2
    done
    echo ""
    
    wait $BUILD_PID
    BUILD_EXIT_CODE=$?
    
    if [ $BUILD_EXIT_CODE -ne 0 ]; then
        log_error "构建失败，退出码: $BUILD_EXIT_CODE"
        exit 1
    fi

    # 检查构建结果
    if [ ! -d "build" ]; then
        log_error "管理端前端构建失败，未找到build目录"
        exit 1
    fi

    # 显示构建统计
    if [ -f "build/static/js/main.*.js" ]; then
        MAIN_JS_SIZE=$(du -h build/static/js/main.*.js | cut -f1)
        log_info "主JS文件大小: $MAIN_JS_SIZE"
    fi

    cd ..
    log_success "管理端前端构建完成: frontend/build"
}

# 构建用户端前端（优化版）
build_user_frontend_optimized() {
    log_info "开始优化构建用户端前端..."

    cd user-frontend

    # 检查node_modules是否存在且是最新的
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        log_info "安装/更新用户端前端依赖..."
        if [ -f "package-lock.json" ]; then
            npm ci --silent
        else
            npm install --silent
        fi
    else
        log_info "依赖已是最新，跳过安装"
    fi

    # 清理之前的构建
    if [ -d "../src/main/resources/static-user" ]; then
        rm -rf ../src/main/resources/static-user
        log_info "清理旧的构建文件"
    fi

    # 设置构建环境变量
    export GENERATE_SOURCEMAP=false
    export DISABLE_ESLINT_PLUGIN=true

    # 构建前端
    log_info "执行用户端前端构建（优化模式）..."
    npm run build --silent &
    BUILD_PID=$!
    
    # 显示构建进度
    while kill -0 $BUILD_PID 2>/dev/null; do
        echo -n "."
        sleep 2
    done
    echo ""
    
    wait $BUILD_PID
    BUILD_EXIT_CODE=$?
    
    if [ $BUILD_EXIT_CODE -ne 0 ]; then
        log_error "用户端构建失败，退出码: $BUILD_EXIT_CODE"
        exit 1
    fi

    # 检查构建结果
    if [ ! -d "../src/main/resources/static-user" ]; then
        log_error "用户端前端构建失败，未找到构建输出目录"
        exit 1
    fi

    cd ..
    log_success "用户端前端构建完成: src/main/resources/static-user"
}

# 主函数
main() {
    echo "=== 优化前端构建脚本 ==="
    log_info "此脚本将使用优化配置构建前端，减少构建时间"
    
    # 检查Node.js版本
    NODE_VERSION=$(node -v)
    log_info "Node.js版本: $NODE_VERSION"
    
    if [ "$1" = "admin" ]; then
        build_admin_frontend_optimized
    elif [ "$1" = "user" ]; then
        build_user_frontend_optimized
    else
        build_admin_frontend_optimized
        build_user_frontend_optimized
    fi
    
    log_success "🎉 前端构建完成！"
    log_info "💡 提示: 如果仍然觉得慢，可以考虑："
    log_info "   - 升级到更快的SSD硬盘"
    log_info "   - 增加系统内存"
    log_info "   - 使用yarn代替npm"
}

# 执行主函数
main "$@"
