#!/bin/bash

# 构建用户前端脚本

echo "=== 构建用户前端 ==="

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请安装Node.js 16或更高版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "错误: Node.js版本过低，需要Node.js 16或更高版本"
    exit 1
fi

# 进入用户前端目录
cd user-frontend

# 安装依赖
if [ ! -d "node_modules" ]; then
    echo "安装用户前端依赖..."
    npm install
fi

# 构建项目
echo "构建用户前端项目..."
npm run build

if [ $? -eq 0 ]; then
    echo ""
    echo "✓ 用户前端构建成功！"
    echo "📁 构建文件已输出到: ../src/main/resources/static-user/"
    echo ""
    echo "💡 提示:"
    echo "   - 重启Spring Boot应用以使用新的构建文件"
    echo "   - 访问 http://localhost:8080/m 测试用户前端"
    echo "   - 访问 http://localhost:8080/m/{pmiNumber} 测试特定PMI"
else
    echo "❌ 用户前端构建失败"
    exit 1
fi
