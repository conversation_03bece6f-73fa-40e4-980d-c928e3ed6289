-- 修改 t_pmi_records 表字符集为 utf8mb4_general_ci
-- 目的：与 old_t_zoom_pmi 表保持一致，避免字符集冲突
-- 执行日期: 2025-08-20

USE zoombusV;

-- ========================================
-- 第一部分：备份和分析
-- ========================================

SELECT '=== 字符集修改前分析 ===' as step;

-- 检查当前表的字符集
SELECT 
    'Current Table Charset' as check_type,
    TABLE_SCHEMA as database_name,
    TABLE_NAME as table_name,
    TABLE_COLLATION as current_collation
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME IN ('t_pmi_records', 'old_t_zoom_pmi');

-- 检查表中的数据量
SELECT 
    'Data Count Before Change' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN magic_id IS NOT NULL THEN 1 END) as has_magic_id,
    COUNT(CASE WHEN pmi_number IS NOT NULL THEN 1 END) as has_pmi_number
FROM t_pmi_records;

-- 显示一些示例数据（确保修改后数据完整）
SELECT 
    'Sample Data Before Change' as check_type,
    id, pmi_number, magic_id, pmi_password, status
FROM t_pmi_records 
ORDER BY id 
LIMIT 5;

-- ========================================
-- 第二部分：修改表字符集
-- ========================================

SELECT '=== 开始修改表字符集 ===' as step;

-- 修改表的默认字符集和排序规则
ALTER TABLE t_pmi_records 
CONVERT TO CHARACTER SET utf8mb4 
COLLATE utf8mb4_general_ci;

SELECT 'Table charset conversion completed' as result;

-- ========================================
-- 第三部分：验证修改结果
-- ========================================

SELECT '=== 验证修改结果 ===' as step;

-- 检查修改后的表字符集
SELECT 
    'Table Charset After Change' as check_type,
    TABLE_SCHEMA as database_name,
    TABLE_NAME as table_name,
    TABLE_COLLATION as new_collation
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME IN ('t_pmi_records', 'old_t_zoom_pmi');

-- 检查列的字符集
SELECT 
    'Column Charset After Change' as check_type,
    COLUMN_NAME as column_name,
    DATA_TYPE as data_type,
    CHARACTER_SET_NAME as charset,
    COLLATION_NAME as collation
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME = 't_pmi_records'
AND CHARACTER_SET_NAME IS NOT NULL
ORDER BY ORDINAL_POSITION;

-- 验证数据完整性
SELECT 
    'Data Count After Change' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN magic_id IS NOT NULL THEN 1 END) as has_magic_id,
    COUNT(CASE WHEN pmi_number IS NOT NULL THEN 1 END) as has_pmi_number
FROM t_pmi_records;

-- 显示修改后的示例数据
SELECT 
    'Sample Data After Change' as check_type,
    id, pmi_number, magic_id, pmi_password, status
FROM t_pmi_records 
ORDER BY id 
LIMIT 5;

-- ========================================
-- 第四部分：测试字符集兼容性
-- ========================================

SELECT '=== 测试字符集兼容性 ===' as step;

-- 测试两表之间的JOIN操作（之前会报字符集冲突错误）
SELECT 
    'Charset Compatibility Test' as test_type,
    COUNT(*) as join_success_count
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi;

-- 测试字符串比较操作
SELECT 
    'String Comparison Test' as test_type,
    pr.pmi_number,
    pr.magic_id,
    ozp.mg_id,
    CASE WHEN pr.magic_id = ozp.mg_id THEN 'MATCH' ELSE 'NO_MATCH' END as comparison_result
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi
LIMIT 5;

-- ========================================
-- 第五部分：性能和索引检查
-- ========================================

SELECT '=== 检查索引状态 ===' as step;

-- 检查索引是否正常
SHOW INDEX FROM t_pmi_records;

-- 检查表状态
SELECT 
    'Table Status After Change' as check_type,
    TABLE_NAME as table_name,
    ENGINE as engine,
    TABLE_ROWS as estimated_rows,
    DATA_LENGTH as data_size,
    INDEX_LENGTH as index_size,
    TABLE_COLLATION as collation
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME = 't_pmi_records';

-- ========================================
-- 第六部分：最终报告
-- ========================================

SELECT '=== 字符集修改完成 ===' as final_report;

-- 最终验证
SELECT 
    'Final Verification' as report_type,
    'SUCCESS: t_pmi_records charset changed to utf8mb4_general_ci' as status,
    'Compatible with old_t_zoom_pmi table' as compatibility,
    'No more charset conflicts in JOIN operations' as benefit;

-- 显示修改摘要
SELECT 
    'Modification Summary' as summary_type,
    'FROM: utf8mb4_0900_ai_ci' as old_charset,
    'TO: utf8mb4_general_ci' as new_charset,
    'REASON: Compatibility with old_t_zoom_pmi' as reason,
    'IMPACT: Eliminates charset conflicts' as impact;

-- 后续建议
SELECT 
    'Recommendations' as recommendation_type,
    '1. Test all application functions' as step1,
    '2. Monitor query performance' as step2,
    '3. Update migration scripts if needed' as step3,
    '4. Consider updating other tables for consistency' as step4;

SELECT 't_pmi_records表字符集修改完成！现在与old_t_zoom_pmi表使用相同的字符集，不再有字符集冲突问题。' as completion_message;
