-- 修改 t_users 表字符集为 utf8mb4_general_ci
-- 目的：与其他表保持一致，避免字符集冲突
-- 执行日期: 2025-08-20

USE zoombusV;

-- ========================================
-- 第一部分：备份和分析
-- ========================================

SELECT '=== t_users表字符集修改前分析 ===' as step;

-- 检查当前表的字符集
SELECT 
    'Current Table Charset' as check_type,
    TABLE_SCHEMA as database_name,
    TABLE_NAME as table_name,
    TABLE_COLLATION as current_collation
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME = 't_users';

-- 检查列的字符集
SELECT 
    'Current Column Charset' as check_type,
    COLUMN_NAME as column_name,
    DATA_TYPE as data_type,
    CHARACTER_SET_NAME as charset,
    COLLATION_NAME as collation
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME = 't_users'
AND CHARACTER_SET_NAME IS NOT NULL
ORDER BY ORDINAL_POSITION;

-- 检查表中的数据量
SELECT 
    'Data Count Before Change' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN username IS NOT NULL THEN 1 END) as has_username,
    COUNT(CASE WHEN full_name IS NOT NULL THEN 1 END) as has_full_name,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as has_email
FROM t_users;

-- 显示一些示例数据（确保修改后数据完整）
SELECT 
    'Sample Data Before Change' as check_type,
    id, username, full_name, email, status
FROM t_users 
ORDER BY id 
LIMIT 5;

-- ========================================
-- 第二部分：修改表字符集
-- ========================================

SELECT '=== 开始修改t_users表字符集 ===' as step;

-- 方法1：使用ALTER TABLE CONVERT TO（推荐）
-- 这会同时修改表和所有列的字符集
ALTER TABLE t_users 
CONVERT TO CHARACTER SET utf8mb4 
COLLATE utf8mb4_general_ci;

SELECT 'Table charset conversion completed using CONVERT TO method' as result;

-- ========================================
-- 第三部分：验证修改结果
-- ========================================

SELECT '=== 验证修改结果 ===' as step;

-- 检查修改后的表字符集
SELECT 
    'Table Charset After Change' as check_type,
    TABLE_SCHEMA as database_name,
    TABLE_NAME as table_name,
    TABLE_COLLATION as new_collation
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME = 't_users';

-- 检查修改后的列字符集
SELECT 
    'Column Charset After Change' as check_type,
    COLUMN_NAME as column_name,
    DATA_TYPE as data_type,
    CHARACTER_SET_NAME as charset,
    COLLATION_NAME as collation
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME = 't_users'
AND CHARACTER_SET_NAME IS NOT NULL
ORDER BY ORDINAL_POSITION;

-- 验证数据完整性
SELECT 
    'Data Count After Change' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN username IS NOT NULL THEN 1 END) as has_username,
    COUNT(CASE WHEN full_name IS NOT NULL THEN 1 END) as has_full_name,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as has_email
FROM t_users;

-- 显示修改后的示例数据
SELECT 
    'Sample Data After Change' as check_type,
    id, username, full_name, email, status
FROM t_users 
ORDER BY id 
LIMIT 5;

-- ========================================
-- 第四部分：测试字符集兼容性
-- ========================================

SELECT '=== 测试字符集兼容性 ===' as step;

-- 测试与其他表的JOIN操作（检查字符集兼容性）
SELECT 
    'Charset Compatibility Test with PMI Records' as test_type,
    COUNT(*) as join_success_count
FROM t_users u
JOIN t_pmi_records pr ON u.id = pr.user_id
LIMIT 1;

-- 测试字符串比较操作
SELECT 
    'String Comparison Test' as test_type,
    u.username,
    u.full_name,
    'utf8mb4_general_ci compatible' as test_result
FROM t_users u
WHERE u.username IS NOT NULL
LIMIT 3;

-- ========================================
-- 第五部分：性能和索引检查
-- ========================================

SELECT '=== 检查索引状态 ===' as step;

-- 检查索引是否正常
SHOW INDEX FROM t_users;

-- 检查表状态
SELECT 
    'Table Status After Change' as check_type,
    TABLE_NAME as table_name,
    ENGINE as engine,
    TABLE_ROWS as estimated_rows,
    DATA_LENGTH as data_size,
    INDEX_LENGTH as index_size,
    TABLE_COLLATION as collation
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME = 't_users';

-- ========================================
-- 第六部分：最终报告
-- ========================================

SELECT '=== t_users表字符集修改完成 ===' as final_report;

-- 最终验证
SELECT 
    'Final Verification' as report_type,
    'SUCCESS: t_users charset changed to utf8mb4_general_ci' as status,
    'Compatible with other tables using utf8mb4_general_ci' as compatibility,
    'No charset conflicts in JOIN operations' as benefit;

-- 显示修改摘要
SELECT 
    'Modification Summary' as summary_type,
    'FROM: utf8mb4_0900_ai_ci (or other)' as old_charset,
    'TO: utf8mb4_general_ci' as new_charset,
    'REASON: Consistency with other tables' as reason,
    'IMPACT: Eliminates charset conflicts' as impact;

-- 后续建议
SELECT 
    'Recommendations' as recommendation_type,
    '1. Test all user-related functions' as step1,
    '2. Monitor query performance' as step2,
    '3. Verify JOIN operations with other tables' as step3,
    '4. Check application login and user management' as step4;

-- 显示最终的字符集状态
SELECT 
    'Final Charset Status' as final_status,
    TABLE_NAME as table_name,
    TABLE_COLLATION as collation,
    'READY FOR PRODUCTION MIGRATION' as migration_status
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME = 't_users';

SELECT 't_users表字符集修改完成！现在使用utf8mb4_general_ci字符集，与其他表保持一致。' as completion_message;
