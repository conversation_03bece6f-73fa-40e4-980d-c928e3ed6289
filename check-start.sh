#!/bin/bash

# ZoomBus 项目启动检查脚本
# 检查前后端服务是否运行，如果没有则自动启动

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -ti:$port > /dev/null 2>&1; then
        log_success "$service_name 正在运行 (端口 $port)"
        return 0
    else
        log_warning "$service_name 未运行 (端口 $port)"
        return 1
    fi
}

# 检查服务健康状态
check_service_health() {
    local url=$1
    local service_name=$2
    local timeout=${3:-10}

    if curl -s --max-time $timeout "$url" > /dev/null 2>&1; then
        log_success "$service_name 健康检查通过"
        return 0
    else
        log_warning "$service_name 健康检查失败"
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_wait=${3:-60}
    local wait_time=0

    log_info "等待 $service_name 启动..."

    while [ $wait_time -lt $max_wait ]; do
        if check_port $port "$service_name" > /dev/null 2>&1; then
            log_success "$service_name 启动成功"
            return 0
        fi

        sleep 2
        wait_time=$((wait_time + 2))
        echo -n "."
    done

    echo ""
    log_error "$service_name 启动超时"
    return 1
}

# 自动启动所有服务
start_services_automatically() {
    log_info "开始自动启动服务..."

    # 检查Java环境
    if ! command -v java &> /dev/null; then
        log_error "未找到Java，请安装Java 11或更高版本"
        return 1
    fi

    # 检查Node.js环境
    if ! command -v node &> /dev/null; then
        log_error "未找到Node.js，请安装Node.js 16或更高版本"
        return 1
    fi

    # 检查Maven
    if [ -f "./mvnw" ]; then
        MVN_CMD="./mvnw"
    elif command -v mvn &> /dev/null; then
        MVN_CMD="mvn"
    else
        log_error "未找到Maven，请安装Maven或使用Maven Wrapper"
        return 1
    fi

    # 设置Java环境
    if command -v /usr/libexec/java_home &> /dev/null; then
        JAVA_11_HOME=$(/usr/libexec/java_home -v 11 2>/dev/null)
        if [ -n "$JAVA_11_HOME" ]; then
            export JAVA_HOME="$JAVA_11_HOME"
            export PATH="$JAVA_HOME/bin:$PATH"
            log_info "使用Java 11: $JAVA_HOME"
        fi
    fi

    # 启动后端服务
    if ! check_port 8080 "后端服务" > /dev/null 2>&1; then
        log_info "启动后端服务..."

        # 停止可能存在的进程
        pkill -f "spring-boot:run" 2>/dev/null || true
        sleep 2

        # 启动后端
        nohup $MVN_CMD spring-boot:run -DskipTests \
            -Dspring-boot.run.profiles=dev \
            > backend.log 2>&1 &

        log_info "后端服务启动中..."
    fi

    # 启动管理前端
    if ! check_port 3000 "管理前端" > /dev/null 2>&1; then
        log_info "启动管理前端..."

        cd frontend
        if [ ! -d "node_modules" ]; then
            log_info "安装管理前端依赖..."
            npm install > /dev/null 2>&1
        fi

        export BROWSER=none
        export PORT=3000
        nohup npm start > ../frontend.log 2>&1 &
        cd ..

        log_info "管理前端启动中..."
    fi

    # 启动用户前端
    if ! check_port 3001 "用户前端" > /dev/null 2>&1; then
        log_info "启动用户前端..."

        cd user-frontend
        if [ ! -d "node_modules" ]; then
            log_info "安装用户前端依赖..."
            npm install > /dev/null 2>&1
        fi

        nohup npm run dev > ../user-frontend.log 2>&1 &
        cd ..

        log_info "用户前端启动中..."
    fi

    log_success "所有服务启动命令已执行"
    return 0
}

# 主检查函数
main() {
    log_info "🔍 检查 ZoomBus 项目服务状态..."
    
    # 检查项目根目录
    if [ ! -f "pom.xml" ] || [ ! -d "frontend" ] || [ ! -d "user-frontend" ]; then
        log_error "请在 ZoomBus 项目根目录下运行此脚本"
        exit 1
    fi
    
    # 服务状态标记
    backend_running=false
    frontend_running=false
    user_frontend_running=false
    need_start=false
    
    # 检查后端服务 (8080端口)
    if check_port 8080 "后端服务"; then
        if check_service_health "http://localhost:8080/actuator/health" "后端服务" 10; then
            backend_running=true
        else
            log_warning "后端服务端口占用但健康检查失败，可能需要重启"
            need_start=true
        fi
    else
        need_start=true
    fi

    # 检查前端服务 (3000端口)
    if check_port 3000 "管理前端"; then
        if check_service_health "http://localhost:3000" "管理前端" 10; then
            frontend_running=true
        else
            log_warning "管理前端端口占用但健康检查失败"
        fi
    else
        need_start=true
    fi

    # 检查用户前端服务 (3001端口)
    if check_port 3001 "用户前端"; then
        if check_service_health "http://localhost:3001" "用户前端" 10; then
            user_frontend_running=true
        else
            log_warning "用户前端端口占用但健康检查失败"
        fi
    else
        need_start=true
    fi
    
    # 显示当前状态
    echo ""
    log_info "📊 服务状态总览:"
    echo "  后端服务 (8080):    $([ "$backend_running" = true ] && echo -e "${GREEN}✓ 运行中${NC}" || echo -e "${RED}✗ 未运行${NC}")"
    echo "  管理前端 (3000):    $([ "$frontend_running" = true ] && echo -e "${GREEN}✓ 运行中${NC}" || echo -e "${RED}✗ 未运行${NC}")"
    echo "  用户前端 (3001):    $([ "$user_frontend_running" = true ] && echo -e "${GREEN}✓ 运行中${NC}" || echo -e "${RED}✗ 未运行${NC}")"
    echo ""
    
    # 如果所有服务都在运行
    if [ "$backend_running" = true ] && [ "$frontend_running" = true ] && [ "$user_frontend_running" = true ]; then
        log_success "🎉 所有服务都在正常运行！"
        echo ""
        log_info "📱 访问地址:"
        echo "  管理后台: http://localhost:3000"
        echo "  用户页面: http://localhost:3001"
        echo "  后端API:  http://localhost:8080"
        return 0
    fi
    
    # 如果需要启动服务
    if [ "$need_start" = true ]; then
        log_warning "⚠️  检测到服务未完全启动"
        
        log_info "🚀 正在自动启动服务..."
        echo ""

        # 自动启动各个服务
        if start_services_automatically; then
            log_success "✅ 服务启动命令执行完成"

            # 等待服务启动并验证
            echo ""
            log_info "🔄 验证服务启动状态..."

            # 等待后端启动
            if [ "$backend_running" = false ]; then
                wait_for_service 8080 "后端服务" 90
            fi

            # 等待前端启动
            if [ "$frontend_running" = false ]; then
                wait_for_service 3000 "管理前端" 60
            fi

            # 等待用户前端启动
            if [ "$user_frontend_running" = false ]; then
                wait_for_service 3001 "用户前端" 60
            fi

            echo ""
            log_success "🎉 所有服务启动完成！"
            echo ""
            log_info "📱 访问地址:"
            echo "  管理后台: http://localhost:3000"
            echo "  用户页面: http://localhost:3001"
            echo "  后端API:  http://localhost:8080"

        else
            log_error "❌ 服务启动失败"
            exit 1
        fi
    fi
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
