-- 检查老系统数据中的日期问题
-- 执行日期: 2025-08-13

USE zoombusV;

SELECT '=== 检查老系统日期数据问题 ===' as title;

-- 检查plan_end_date_time字段的数据分布
SELECT '--- plan_end_date_time字段分析 ---' as section;

-- 1. 检查NULL值
SELECT 
    'NULL值数量' as check_type,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE plan_end_date_time IS NULL;

-- 2. 检查空字符串
SELECT 
    '空字符串数量' as check_type,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE plan_end_date_time = '';

-- 3. 检查空白字符串
SELECT 
    '空白字符串数量' as check_type,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE TRIM(plan_end_date_time) = '';

-- 4. 检查有效日期值
SELECT 
    '有效日期值数量' as check_type,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE plan_end_date_time IS NOT NULL 
AND TRIM(plan_end_date_time) != '';

-- 5. 显示不同的日期值样本
SELECT 
    '日期值样本' as sample_type,
    plan_end_date_time,
    LENGTH(plan_end_date_time) as length,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE plan_end_date_time IS NOT NULL
GROUP BY plan_end_date_time
ORDER BY count DESC
LIMIT 10;

-- 6. 检查LONG类型PMI的日期分布
SELECT '--- LONG类型PMI日期分析 ---' as section;

SELECT 
    'LONG类型PMI总数' as item,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE now_plan_type = 'LONG';

SELECT 
    'LONG类型PMI中NULL日期' as item,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE now_plan_type = 'LONG'
AND plan_end_date_time IS NULL;

SELECT 
    'LONG类型PMI中空字符串日期' as item,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE now_plan_type = 'LONG'
AND plan_end_date_time = '';

SELECT 
    'LONG类型PMI中有效日期' as item,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE now_plan_type = 'LONG'
AND plan_end_date_time IS NOT NULL
AND TRIM(plan_end_date_time) != '';

-- 7. 尝试解析日期，找出格式问题
SELECT '--- 日期格式验证 ---' as section;

-- 检查能否正常解析的日期
SELECT 
    '可正常解析的日期数量' as check_type,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE plan_end_date_time IS NOT NULL 
AND TRIM(plan_end_date_time) != ''
AND STR_TO_DATE(plan_end_date_time, '%Y-%m-%d %H:%i') IS NOT NULL;

-- 显示一些具体的日期值用于分析
SELECT 
    '具体日期值示例' as example_type,
    plan_end_date_time,
    CASE 
        WHEN plan_end_date_time IS NULL THEN 'NULL'
        WHEN plan_end_date_time = '' THEN 'EMPTY_STRING'
        WHEN TRIM(plan_end_date_time) = '' THEN 'WHITESPACE_ONLY'
        WHEN STR_TO_DATE(plan_end_date_time, '%Y-%m-%d %H:%i') IS NULL THEN 'INVALID_FORMAT'
        ELSE 'VALID'
    END as status
FROM old_t_zoom_pmi 
WHERE now_plan_type = 'LONG'
LIMIT 20;

-- 8. 检查其他可能有问题的字段
SELECT '--- 其他字段检查 ---' as section;

-- 检查create_time和update_time
SELECT 
    'create_time为NULL的记录' as check_type,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE create_time IS NULL;

SELECT 
    'update_time为NULL的记录' as check_type,
    COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE update_time IS NULL;

-- 检查用户表的时间字段
SELECT 
    'old_t_wx_user中create_time为NULL' as check_type,
    COUNT(*) as count
FROM old_t_wx_user 
WHERE create_time IS NULL;

SELECT 
    'old_t_wx_user中update_time为NULL' as check_type,
    COUNT(*) as count
FROM old_t_wx_user 
WHERE update_time IS NULL;

SELECT 'Date issues check completed!' as final_message;
