#!/bin/bash

echo "=== 检查PMI Task 82 状态 ==="

# 检查task 82的基本信息
echo "1. 检查PMI Schedule Window Task 82:"
mysql -u root -p'Nslcp@2024' zoombus -e "SELECT * FROM t_pmi_schedule_window_tasks WHERE id = 82;"

echo ""
echo "2. 检查相关的PMI Schedule Window:"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT w.*, t.id as task_id, t.task_type, t.status as task_status, t.scheduled_time, t.actual_execution_time, t.error_message
FROM t_pmi_schedule_windows w 
LEFT JOIN t_pmi_schedule_window_tasks t ON w.id = t.window_id 
WHERE t.id = 82 OR w.id = (SELECT window_id FROM t_pmi_schedule_window_tasks WHERE id = 82);"

echo ""
echo "3. 检查PMI记录信息:"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT p.*, s.name as schedule_name
FROM t_pmi_records p
JOIN t_pmi_schedules s ON p.id = s.pmi_record_id
WHERE s.id = (
    SELECT w.schedule_id 
    FROM t_pmi_schedule_windows w 
    WHERE w.id = (SELECT window_id FROM t_pmi_schedule_window_tasks WHERE id = 82)
);"

echo ""
echo "4. 检查应用日志中相关的错误信息:"
if [ -f "/root/zoombus/zoombus.log" ]; then
    echo "最近的PMI task 82相关日志:"
    grep -i "task.*82\|window.*82" /root/zoombus/zoombus.log | tail -10
fi

echo ""
echo "5. 检查动态任务管理器状态:"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT COUNT(*) as pending_tasks_count 
FROM t_pmi_schedule_window_tasks 
WHERE status = 'PENDING' AND scheduled_time <= NOW();"

echo ""
echo "6. 检查最近的任务执行情况:"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT * FROM t_pmi_schedule_window_tasks 
WHERE id BETWEEN 80 AND 85 
ORDER BY id;"

echo ""
echo "=== 检查完成 ==="
