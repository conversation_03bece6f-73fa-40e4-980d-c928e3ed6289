-- 检查t_zoom_meetings表结构和约束的SQL脚本

-- 1. 检查表是否存在
SELECT 
    TABLE_NAME,
    TABLE_SCHEMA,
    ENGINE,
    TABLE_COLLATION
FROM information_schema.TABLES 
WHERE TABLE_NAME = 't_zoom_meetings' 
AND TABLE_SCHEMA = DATABASE();

-- 2. 检查表结构和字段约束
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_KEY,
    EXTRA,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 't_zoom_meetings' 
AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;

-- 3. 检查索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_NAME = 't_zoom_meetings' 
AND TABLE_SCHEMA = DATABASE()
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 4. 检查外键约束
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 't_zoom_meetings' 
AND TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 5. 检查pmi_record_id字段是否允许NULL
SELECT 
    COLUMN_NAME,
    IS_NULLABLE,
    DATA_TYPE,
    COLUMN_DEFAULT
FROM information_schema.COLUMNS 
WHERE TABLE_NAME = 't_zoom_meetings' 
AND COLUMN_NAME = 'pmi_record_id'
AND TABLE_SCHEMA = DATABASE();

-- 6. 检查最近的记录
SELECT 
    id,
    pmi_record_id,
    zoom_meeting_uuid,
    zoom_meeting_id,
    host_id,
    topic,
    status,
    billing_mode,
    created_at
FROM t_zoom_meetings 
ORDER BY created_at DESC 
LIMIT 5;

-- 7. 检查是否有记录的pmi_record_id为NULL
SELECT 
    COUNT(*) as total_records,
    COUNT(pmi_record_id) as records_with_pmi,
    COUNT(*) - COUNT(pmi_record_id) as records_without_pmi
FROM t_zoom_meetings;

-- 8. 检查Flyway迁移历史
SELECT 
    version,
    description,
    script,
    installed_on,
    success
FROM flyway_schema_history 
ORDER BY installed_rank DESC 
LIMIT 10;

-- 9. 尝试插入测试记录（注意：这会实际插入数据，测试后请删除）
-- INSERT INTO t_zoom_meetings (
--     pmi_record_id,
--     zoom_meeting_uuid,
--     zoom_meeting_id,
--     host_id,
--     topic,
--     status,
--     billing_mode,
--     start_time,
--     created_at,
--     updated_at
-- ) VALUES (
--     NULL,
--     CONCAT('test-debug-', UNIX_TIMESTAMP()),
--     CONCAT('test-meeting-', UNIX_TIMESTAMP()),
--     'test-host-debug',
--     '数据库测试会议',
--     'USING',
--     'BY_TIME',
--     NOW(),
--     NOW(),
--     NOW()
-- );

-- 10. 删除测试记录（如果执行了上面的插入）
-- DELETE FROM t_zoom_meetings WHERE zoom_meeting_uuid LIKE 'test-debug-%';
