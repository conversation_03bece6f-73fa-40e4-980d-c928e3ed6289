-- 清理重复的PMI窗口
-- 这个脚本用于删除数据库中重复的PMI计划窗口

-- 1. 首先查看重复窗口的情况
SELECT 
    pmi_record_id,
    start_date_time,
    end_date_time,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id) as window_ids
FROM t_pmi_schedule_windows 
WHERE status IN ('PENDING', 'ACTIVE')
GROUP BY pmi_record_id, start_date_time, end_date_time
HAVING COUNT(*) > 1
ORDER BY pmi_record_id, start_date_time;

-- 2. 删除重复窗口，保留ID最小的那个
DELETE w1 FROM t_pmi_schedule_windows w1
INNER JOIN t_pmi_schedule_windows w2 
WHERE w1.id > w2.id 
  AND w1.pmi_record_id = w2.pmi_record_id
  AND w1.start_date_time = w2.start_date_time
  AND w1.end_date_time = w2.end_date_time
  AND w1.status IN ('PENDING', 'ACTIVE')
  AND w2.status IN ('PENDING', 'ACTIVE');

-- 3. 验证清理结果
SELECT 
    pmi_record_id,
    start_date_time,
    end_date_time,
    COUNT(*) as remaining_count
FROM t_pmi_schedule_windows 
WHERE status IN ('PENDING', 'ACTIVE')
GROUP BY pmi_record_id, start_date_time, end_date_time
HAVING COUNT(*) > 1;

-- 4. 查看PMI 504的窗口情况
SELECT 
    id,
    schedule_id,
    start_date_time,
    end_date_time,
    status,
    created_at
FROM t_pmi_schedule_windows 
WHERE pmi_record_id = 504
  AND status IN ('PENDING', 'ACTIVE')
ORDER BY start_date_time;
