#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Code Stats: 统计当前目录（脚本所在目录）下的程序代码行数
- 递归扫描，排除日志与常见构建产物目录/文件
- 按编程语言分类统计行数，并输出总行数
- Markdown 文档单独统计，但不纳入总行数

用法：
    python3 code_stats.py

可选环境变量：
    CODE_STATS_ROOT: 指定扫描根目录（默认：脚本所在目录）
"""
from __future__ import annotations
import os
import sys
from collections import defaultdict
from typing import Dict, Set, Tuple

# 语言与扩展名映射（小写，不含点）
LANG_EXTS: Dict[str, Set[str]] = {
    'Python': {'py'},
    'JavaScript': {'js', 'mjs', 'cjs'},
    'TypeScript': {'ts', 'tsx'},
    'JSX': {'jsx'},
    'Java': {'java'},
    'Kotlin': {'kt', 'kts'},
    'Go': {'go'},
    'Ruby': {'rb'},
    'PHP': {'php'},
    'C': {'c', 'h'},
    'C++': {'cc', 'cpp', 'cxx', 'hpp', 'hxx'},
    'C#': {'cs'},
    'Swift': {'swift'},
    'Objective-C': {'m', 'mm', 'hpp'},
    'Scala': {'scala'},
    'Rust': {'rs'},
    'Shell': {'sh', 'bash', 'zsh'},
    'HTML': {'html', 'htm'},
    'CSS': {'css', 'scss', 'sass', 'less'},
    'SQL': {'sql'},
    'YAML': {'yml', 'yaml'},
    'JSON': {'json'},
    'XML': {'xml'},
    'Markdown': {'md', 'mdx', 'markdown'},  # 将被排除
}

# 需要排除的目录（小写匹配）
IGNORE_DIRS = {
    '.git', '.idea', '.vscode', '__pycache__', '.pytest_cache', '.mvn', '.gradle',
    'node_modules', 'dist', 'build', 'out', 'bin', 'target', 'coverage', '.next', '.nuxt', '.cache',
    'logs', 'log', 'tmp', 'temp', '.husky', '.github', '.pnpm-store'
}

# 需要排除的文件扩展（小写，不含点）
EXCLUDE_EXTS = {
    # 文档/日志/归档/二进制
    'log', 'gz', 'zip', '7z', 'rar', 'jar', 'war', 'class', 'pdf',
    # 代码映射/锁文件/产物
    'map', 'lock', 'min',
    # 图片/字体/媒体
    'png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'ico', 'bmp',
    'ttf', 'otf', 'woff', 'woff2',
    'mp4', 'avi', 'mov', 'mp3', 'wav',
}

# 需要排除的文件名（完全匹配，大小写敏感）
EXCLUDE_FILENAMES = {
    'package-lock.json', 'pnpm-lock.yaml', 'yarn.lock',
}

# 需要排除的后缀（包含 .min.js / .min.css 等）
EXCLUDE_SUFFIXES = {
    '.min.js', '.min.css'
}


def lang_of_extension(ext: str) -> str:
    e = ext.lower().lstrip('.')
    # 过滤明确排除的扩展
    if e in EXCLUDE_EXTS:
        return ''
    # Markdown 作为单独类别统计
    if e in LANG_EXTS.get('Markdown', set()):
        return 'Markdown'
    for lang, exts in LANG_EXTS.items():
        if lang == 'Markdown':
            continue
        if e in exts:
            return lang
    return 'Other'


def should_skip_file(path: str, filename: str) -> bool:
    lname = filename.lower()
    # 文件名直接排除
    if filename in EXCLUDE_FILENAMES:
        return True
    # 后缀排除（如 .min.js）
    for suf in EXCLUDE_SUFFIXES:
        if lname.endswith(suf):
            return True
    # 扩展排除
    ext = os.path.splitext(lname)[1].lstrip('.')
    if ext in EXCLUDE_EXTS:
        return True
    return False


def count_file_lines(file_path: str) -> int:
    # 以 utf-8 读取（忽略非法字符），统计物理行数
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return sum(1 for _ in f)
    except Exception:
        return 0


def walk_and_count(root: str) -> Tuple[int, Dict[str, int], int]:
    total = 0
    per_lang: Dict[str, int] = defaultdict(int)
    md_lines_total = 0

    for dirpath, dirnames, filenames in os.walk(root):
        # 过滤目录
        dirnames[:] = [d for d in dirnames if d.lower() not in IGNORE_DIRS]

        for fname in filenames:
            if should_skip_file(os.path.join(dirpath, fname), fname):
                continue

            ext = os.path.splitext(fname)[1]
            lang = lang_of_extension(ext)
            if not lang:  # 被排除的类型
                continue

            file_path = os.path.join(dirpath, fname)
            lines = count_file_lines(file_path)
            if lines <= 0:
                continue
            if lang == 'Markdown':
                md_lines_total += lines  # 文档单独累计
                continue
            total += lines
            per_lang[lang] += lines

    return total, dict(sorted(per_lang.items(), key=lambda kv: (-kv[1], kv[0]))), md_lines_total


def main():
    root = os.environ.get('CODE_STATS_ROOT') or os.path.dirname(os.path.abspath(__file__))
    total, per_lang, md_lines = walk_and_count(root)

    # 输出结果
    print('Code Stats (root = %s)' % root)
    print('---------------------------------------')
    print('Total lines (program code): %d' % total)
    print('By language:')
    for lang, lines in per_lang.items():
        print('  - %-12s %10d' % (lang, lines))
    print('Markdown (excluded from total): %d' % md_lines)

    # 退出码：若找到代码则 0，否则 2
    sys.exit(0 if total > 0 else 2)


if __name__ == '__main__':
    main()
