-- 完成剩余的迁移步骤
-- 执行日期: 2025-08-13

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第四部分：为LONG类型PMI创建窗口记录
-- ========================================

-- 为每个LONG类型的PMI计划创建当前有效的窗口记录
INSERT INTO t_pmi_schedule_windows (
    schedule_id,
    pmi_record_id,
    window_date,
    end_date,
    start_time,
    end_time,
    status,
    zoom_user_id,
    created_at,
    updated_at
)
SELECT 
    schedule.id as schedule_id,
    schedule.pmi_record_id,
    CURDATE() as window_date,
    -- 窗口结束日期使用计划的结束日期
    schedule.end_date as end_date,
    '00:00:00' as start_time,
    '23:59:59' as end_time,
    -- 根据计划结束时间判断窗口状态
    CASE 
        WHEN schedule.end_date < CURDATE() THEN 'COMPLETED'
        WHEN schedule.end_date = CURDATE() THEN 'ACTIVE'
        ELSE 'PENDING'
    END as status,
    NULL as zoom_user_id, -- 暂时不分配具体的zoom用户
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules schedule
JOIN t_pmi_records pmi ON schedule.pmi_record_id = pmi.id
WHERE pmi.billing_mode = 'LONG'
AND NOT EXISTS (
    SELECT 1 FROM t_pmi_schedule_windows w 
    WHERE w.schedule_id = schedule.id AND w.window_date = CURDATE()
);

-- 更新PMI记录的窗口相关字段
UPDATE t_pmi_records pmi
JOIN t_pmi_schedule_windows win ON pmi.id = win.pmi_record_id
JOIN t_pmi_schedules schedule ON win.schedule_id = schedule.id
SET 
    pmi.current_window_id = win.id,
    pmi.window_expire_time = TIMESTAMP(win.end_date, win.end_time),
    pmi.active_window_ids = JSON_ARRAY(win.id)
WHERE pmi.billing_mode = 'LONG'
AND win.status IN ('PENDING', 'ACTIVE');

-- 记录窗口创建结果
SELECT 'Step 4: LONG类型PMI窗口创建完成' as step,
       (SELECT COUNT(*) FROM t_pmi_schedule_windows) as total_windows,
       (SELECT COUNT(*) FROM t_pmi_schedule_windows WHERE status = 'ACTIVE') as active_windows,
       (SELECT COUNT(*) FROM t_pmi_schedule_windows WHERE status = 'PENDING') as pending_windows;

-- 提交事务
COMMIT;

-- ========================================
-- 迁移完成报告
-- ========================================
SELECT '=== 数据迁移完成报告 ===' as report;
SELECT 
    '用户迁移' as item,
    (SELECT COUNT(*) FROM old_t_wx_user) as source_count,
    (SELECT COUNT(*) FROM t_users) as target_count;
    
SELECT 
    'PMI迁移' as item,
    (SELECT COUNT(*) FROM old_t_zoom_pmi) as source_count,
    (SELECT COUNT(*) FROM t_pmi_records) as target_count;
    
SELECT 
    'LONG类型PMI' as item,
    (SELECT COUNT(*) FROM old_t_zoom_pmi WHERE now_plan_type = 'LONG') as source_count,
    (SELECT COUNT(*) FROM t_pmi_records WHERE billing_mode = 'LONG') as target_count;
    
SELECT 
    'PMI计划' as item,
    0 as source_count,
    (SELECT COUNT(*) FROM t_pmi_schedules) as target_count;

SELECT 
    'PMI窗口' as item,
    0 as source_count,
    (SELECT COUNT(*) FROM t_pmi_schedule_windows) as target_count;

-- 显示需要手动处理的问题
SELECT '=== 需要注意的问题 ===' as attention;
SELECT 
    'PMI记录中找不到对应用户的数量' as issue,
    COUNT(*) as count
FROM t_pmi_records 
WHERE user_id = 1; -- 默认用户ID，表示映射失败

SELECT 
    'LONG类型PMI的到期时间分布' as info,
    DATE(STR_TO_DATE(old_pmi.plan_end_date_time, '%Y-%m-%d %H:%i')) as expire_date,
    COUNT(*) as count
FROM old_t_zoom_pmi old_pmi
WHERE old_pmi.now_plan_type = 'LONG'
AND old_pmi.plan_end_date_time IS NOT NULL
AND TRIM(old_pmi.plan_end_date_time) != ''
GROUP BY DATE(STR_TO_DATE(old_pmi.plan_end_date_time, '%Y-%m-%d %H:%i'))
ORDER BY expire_date;

-- ========================================
-- 数据验证和修复
-- ========================================

-- 验证PMI号码格式（应该是10位数字）
SELECT '=== PMI格式验证 ===' as validation;
SELECT 
    'PMI格式不正确的记录' as issue,
    COUNT(*) as count
FROM t_pmi_records 
WHERE LENGTH(pmi_number) != 10 OR pmi_number NOT REGEXP '^[0-9]+$';

-- 显示格式不正确的PMI记录
SELECT 
    id, pmi_number, LENGTH(pmi_number) as length
FROM t_pmi_records 
WHERE LENGTH(pmi_number) != 10 OR pmi_number NOT REGEXP '^[0-9]+$'
LIMIT 10;

-- 验证LONG类型PMI的完整性
SELECT '=== LONG类型PMI完整性验证 ===' as validation;
SELECT 
    'LONG类型PMI记录数' as item,
    COUNT(*) as count
FROM t_pmi_records WHERE billing_mode = 'LONG';

SELECT 
    'LONG类型PMI对应的计划数' as item,
    COUNT(*) as count
FROM t_pmi_schedules s
JOIN t_pmi_records p ON s.pmi_record_id = p.id
WHERE p.billing_mode = 'LONG';

SELECT 
    'LONG类型PMI对应的窗口数' as item,
    COUNT(*) as count
FROM t_pmi_schedule_windows w
JOIN t_pmi_records p ON w.pmi_record_id = p.id
WHERE p.billing_mode = 'LONG';

-- 显示迁移成功的统计信息
SELECT '=== 迁移成功统计 ===' as summary;
SELECT 
    'BY_TIME类型PMI' as type,
    (SELECT COUNT(*) FROM old_t_zoom_pmi WHERE now_plan_type = 'BY_TIME') as old_count,
    (SELECT COUNT(*) FROM t_pmi_records WHERE billing_mode = 'BY_TIME') as new_count;

SELECT 
    'LONG类型PMI' as type,
    (SELECT COUNT(*) FROM old_t_zoom_pmi WHERE now_plan_type = 'LONG') as old_count,
    (SELECT COUNT(*) FROM t_pmi_records WHERE billing_mode = 'LONG') as new_count;

-- 显示剩余时长迁移情况
SELECT '=== 剩余时长迁移验证 ===' as time_validation;
SELECT 
    billing_mode,
    COUNT(*) as record_count,
    SUM(total_minutes) as total_minutes_sum,
    SUM(available_minutes) as available_minutes_sum,
    AVG(available_minutes) as avg_available_minutes
FROM t_pmi_records 
GROUP BY billing_mode;

SELECT 'Migration script completed successfully!' as final_message;
