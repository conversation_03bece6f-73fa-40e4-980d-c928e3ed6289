-- 创建Zoom相关表 (正确的数据库名称)
USE zoombusV;

-- 创建Zoom认证信息表
CREATE TABLE IF NOT EXISTS t_zoom_auth (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    account_name VARCHAR(100) NOT NULL UNIQUE,
    zoom_account_id VARCHAR(100) NOT NULL,
    primary_email VARCHAR(255) NOT NULL COMMENT '主账号邮箱',
    client_id VARCHAR(255) NOT NULL,
    client_secret VARCHAR(255) NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    token_type VARCHAR(50) DEFAULT 'Bearer',
    expires_in INT,
    token_issued_at TIMESTAMP NULL,
    token_expires_at TIMESTAMP NULL,
    scope TEXT,
    auth_type ENUM('OAUTH2', 'JWT', 'SERVER_TO_SERVER') NOT NULL DEFAULT 'OAUTH2',
    status ENUM('ACTIVE', 'EXPIRED', 'ERROR', 'DISABLED') NOT NULL DEFAULT 'ACTIVE',
    last_refresh_at TIMESTAMP NULL,
    refresh_count INT DEFAULT 0,
    error_message TEXT,
    webhook_secret_token VARCHAR(255),
    api_base_url VARCHAR(255) DEFAULT 'https://api.zoom.us/v2',
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_account_name (account_name),
    INDEX idx_zoom_account_id (zoom_account_id),
    INDEX idx_primary_email (primary_email),
    INDEX idx_client_id (client_id),
    INDEX idx_status (status),
    INDEX idx_auth_type (auth_type),
    INDEX idx_token_expires_at (token_expires_at)
);

-- 创建Zoom用户表
CREATE TABLE IF NOT EXISTS t_zoom_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_auth_id BIGINT NOT NULL,
    user_id BIGINT NULL,
    zoom_user_id VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    display_name VARCHAR(100),
    user_type ENUM('BASIC', 'LICENSED', 'ON_PREM') NOT NULL DEFAULT 'BASIC',
    status ENUM('ACTIVE', 'INACTIVE', 'PENDING') NOT NULL DEFAULT 'ACTIVE',
    department VARCHAR(100),
    job_title VARCHAR(100),
    phone_number VARCHAR(20),
    timezone VARCHAR(50),
    language VARCHAR(10),
    account_usage ENUM('PUBLIC_MEETING', 'PRIVATE_MEETING', 'PUBLIC_HOST', 'PRIVATE_HOST') NULL COMMENT '账户用途',
    in_use TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否正在使用中',
    zoom_created_at TIMESTAMP NULL,
    zoom_last_login_time TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (zoom_auth_id) REFERENCES t_zoom_auth(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES t_user(id) ON DELETE SET NULL,
    UNIQUE KEY uk_zoom_auth_user (zoom_auth_id, zoom_user_id),
    UNIQUE KEY uk_zoom_auth_email (zoom_auth_id, email),
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_user_type (user_type),
    INDEX idx_zoom_auth_id (zoom_auth_id),
    INDEX idx_user_id (user_id),
    INDEX idx_account_usage (account_usage),
    INDEX idx_in_use (in_use)
);

-- 创建会议表
CREATE TABLE IF NOT EXISTS t_meetings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    creator_user_id BIGINT NULL COMMENT '创建者用户ID',
    zoom_user_id VARCHAR(100) NULL COMMENT 'Zoom用户ID',
    zoom_meeting_id VARCHAR(100) NULL COMMENT 'Zoom会议ID',
    topic VARCHAR(200) NOT NULL,
    agenda TEXT,
    start_time TIMESTAMP NOT NULL,
    duration_minutes INT,
    timezone VARCHAR(50),
    join_url TEXT,
    start_url TEXT,
    meeting_password VARCHAR(50),
    type ENUM('INSTANT', 'SCHEDULED', 'RECURRING_NO_FIXED_TIME', 'RECURRING_FIXED_TIME') NOT NULL DEFAULT 'SCHEDULED',
    status ENUM('SCHEDULED', 'STARTED', 'ENDED', 'CANCELLED', 'CREATING', 'CREATE_FAILED') NOT NULL DEFAULT 'CREATING',
    creation_source ENUM('ADMIN_PANEL', 'ZOOM_APP_WEBHOOK') NOT NULL DEFAULT 'ADMIN_PANEL',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_user_id) REFERENCES t_user(id) ON DELETE SET NULL,
    INDEX idx_zoom_meeting_id (zoom_meeting_id),
    INDEX idx_creator_user_id (creator_user_id),
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status)
);

-- 创建Webhook事件表
CREATE TABLE IF NOT EXISTS t_webhook_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    zoom_account_id VARCHAR(100),
    zoom_meeting_id VARCHAR(100),
    event_data TEXT,
    processing_status ENUM('PENDING', 'PROCESSED', 'FAILED', 'IGNORED') NOT NULL DEFAULT 'PENDING',
    error_message TEXT,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_event_type (event_type),
    INDEX idx_processing_status (processing_status),
    INDEX idx_zoom_account_id (zoom_account_id),
    INDEX idx_zoom_meeting_id (zoom_meeting_id),
    INDEX idx_created_at (created_at)
);

-- 插入测试ZoomAuth数据
INSERT INTO t_zoom_auth (account_name, zoom_account_id, primary_email, client_id, client_secret, access_token, refresh_token, token_type, scope, expires_in, status, auth_type, api_base_url, webhook_secret_token, created_at, updated_at)
VALUES 
('测试账号A', 'KNDMAXZ_SVGeAgOTaK_TEw', '<EMAIL>', 'test_client_id', 'test_client_secret', 'test_access_token', 'test_refresh_token', 'Bearer', 'meeting:write', 3600, 'ACTIVE', 'OAUTH2', 'https://api.zoom.us/v2', 'test_webhook_secret', NOW(), NOW()),
('测试账号B', 'test-account-001', '<EMAIL>', 'test_client_id_2', 'test_client_secret_2', 'test_access_token_2', 'test_refresh_token_2', 'Bearer', 'meeting:write', 3600, 'ACTIVE', 'OAUTH2', 'https://api.zoom.us/v2', 'test_webhook_secret_2', NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 插入测试ZoomUser数据
INSERT INTO t_zoom_users (zoom_auth_id, zoom_user_id, email, first_name, last_name, display_name, user_type, status, account_usage, in_use, created_at, updated_at)
VALUES
(1, 'test-host-456', '<EMAIL>', 'Test', 'Host', 'Test Host User', 'LICENSED', 'ACTIVE', 'PUBLIC_HOST', 0, NOW(), NOW()),
(1, 'host123456', '<EMAIL>', 'Host', 'User', 'Host User', 'LICENSED', 'ACTIVE', 'PUBLIC_HOST', 0, NOW(), NOW()),
(2, 'test_user_1', '<EMAIL>', 'Test', 'User1', 'Test User 1', 'LICENSED', 'ACTIVE', 'PUBLIC_HOST', 0, NOW(), NOW()),
(2, 'test_user_2', '<EMAIL>', 'Test', 'User2', 'Test User 2', 'LICENSED', 'ACTIVE', 'PUBLIC_HOST', 0, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

COMMIT;
