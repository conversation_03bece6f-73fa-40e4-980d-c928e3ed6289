-- 创建LONG类型PMI测试数据
-- 用于测试计费类型筛选和排序功能
-- 执行日期: 2025-08-18

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 创建LONG类型PMI测试数据
-- ========================================

SELECT '=== 创建LONG类型PMI测试数据 ===' as step;

-- 1. 修改现有的一些PMI为LONG类型，用于测试
UPDATE t_pmi_records 
SET 
    billing_mode = 'LONG',
    window_expire_time = '2025-08-25 23:59:59',
    current_window_id = 1
WHERE id IN (
    SELECT id FROM (
        SELECT id FROM t_pmi_records 
        WHERE billing_mode = 'BY_TIME' 
        ORDER BY id 
        LIMIT 5
    ) as temp
);

-- 显示第一批修改结果
SELECT 
    '第一批：即将到期的LONG类型PMI' as batch,
    ROW_COUNT() as updated_count;

-- 2. 创建第二批LONG类型PMI（30天后到期）
UPDATE t_pmi_records 
SET 
    billing_mode = 'LONG',
    window_expire_time = '2025-09-18 23:59:59',
    current_window_id = 2
WHERE id IN (
    SELECT id FROM (
        SELECT id FROM t_pmi_records 
        WHERE billing_mode = 'BY_TIME' 
        ORDER BY id 
        LIMIT 5 OFFSET 5
    ) as temp
);

-- 显示第二批修改结果
SELECT 
    '第二批：30天后到期的LONG类型PMI' as batch,
    ROW_COUNT() as updated_count;

-- 3. 创建第三批LONG类型PMI（90天后到期）
UPDATE t_pmi_records 
SET 
    billing_mode = 'LONG',
    window_expire_time = '2025-11-18 23:59:59',
    current_window_id = 3
WHERE id IN (
    SELECT id FROM (
        SELECT id FROM t_pmi_records 
        WHERE billing_mode = 'BY_TIME' 
        ORDER BY id 
        LIMIT 5 OFFSET 10
    ) as temp
);

-- 显示第三批修改结果
SELECT 
    '第三批：90天后到期的LONG类型PMI' as batch,
    ROW_COUNT() as updated_count;

-- 4. 创建第四批LONG类型PMI（6个月后到期）
UPDATE t_pmi_records 
SET 
    billing_mode = 'LONG',
    window_expire_time = '2026-02-18 23:59:59',
    current_window_id = 4
WHERE id IN (
    SELECT id FROM (
        SELECT id FROM t_pmi_records 
        WHERE billing_mode = 'BY_TIME' 
        ORDER BY id 
        LIMIT 5 OFFSET 15
    ) as temp
);

-- 显示第四批修改结果
SELECT 
    '第四批：6个月后到期的LONG类型PMI' as batch,
    ROW_COUNT() as updated_count;

-- ========================================
-- 验证创建的测试数据
-- ========================================

SELECT '=== 验证创建的测试数据 ===' as verification;

-- 统计各计费类型的数量
SELECT 
    billing_mode,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_pmi_records), 2) as percentage
FROM t_pmi_records
GROUP BY billing_mode
ORDER BY count DESC;

-- 显示LONG类型PMI的详细信息
SELECT 
    'LONG类型PMI详细信息:' as info,
    id,
    pmi_number,
    billing_mode,
    DATE_FORMAT(window_expire_time, '%Y-%m-%d %H:%i:%s') as window_expire_time,
    DATEDIFF(window_expire_time, NOW()) as days_until_expire,
    current_window_id
FROM t_pmi_records
WHERE billing_mode = 'LONG'
ORDER BY window_expire_time ASC;

-- 测试混合排序（LONG优先，按到期日；BY_TIME按剩余时长）
SELECT '=== 测试混合排序结果 ===' as mixed_sort_test;

SELECT 
    id,
    pmi_number,
    billing_mode,
    CASE 
        WHEN billing_mode = 'LONG' THEN DATE_FORMAT(window_expire_time, '%Y-%m-%d')
        ELSE '-'
    END as expire_date,
    CASE 
        WHEN billing_mode = 'BY_TIME' THEN CONCAT(ROUND(available_minutes / 60.0, 1), '小时')
        ELSE '-'
    END as available_time,
    CASE 
        WHEN billing_mode = 'LONG' THEN DATEDIFF(window_expire_time, NOW())
        ELSE NULL
    END as days_until_expire
FROM t_pmi_records
ORDER BY 
    CASE WHEN billing_mode = 'LONG' THEN 0 ELSE 1 END,
    CASE WHEN billing_mode = 'LONG' THEN window_expire_time END ASC,
    CASE WHEN billing_mode = 'BY_TIME' THEN available_minutes END DESC,
    created_at DESC
LIMIT 25;

-- 提交事务
COMMIT;

SELECT '=== LONG类型PMI测试数据创建完成 ===' as completion;
