-- 创建Zoom API调用流水表
-- 执行时间：2025-08-01

USE zoombusV;

-- 创建Zoom API调用日志表
CREATE TABLE IF NOT EXISTS t_zoom_api_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- 请求基本信息
    request_id VARCHAR(64) NOT NULL COMMENT '请求唯一标识',
    api_method VARCHAR(10) NOT NULL COMMENT 'HTTP方法(GET/POST/PUT/PATCH/DELETE)',
    api_path VARCHAR(500) NOT NULL COMMENT 'API路径',
    api_url VARCHAR(1000) NOT NULL COMMENT '完整API URL',
    
    -- 请求内容
    request_headers TEXT COMMENT '请求头信息(JSON格式)',
    request_body LONGTEXT COMMENT '请求体内容',
    
    -- 响应内容
    response_status INT COMMENT 'HTTP响应状态码',
    response_headers TEXT COMMENT '响应头信息(JSON格式)',
    response_body LONGTEXT COMMENT '响应体内容',
    
    -- 时间信息
    request_time DATETIME(3) NOT NULL COMMENT '请求发起时间(毫秒精度)',
    response_time DATETIME(3) COMMENT '响应接收时间(毫秒精度)',
    duration_ms BIGINT COMMENT '请求耗时(毫秒)',
    
    -- 结果信息
    is_success BOOLEAN DEFAULT FALSE COMMENT '是否成功',
    error_code VARCHAR(50) COMMENT '错误代码',
    error_message TEXT COMMENT '错误信息',
    
    -- 业务信息
    business_type VARCHAR(50) COMMENT '业务类型(USER_INFO/CREATE_MEETING/UPDATE_PMI等)',
    business_id VARCHAR(100) COMMENT '业务ID(用户ID/会议ID/PMI号码等)',
    zoom_user_id VARCHAR(100) COMMENT '关联的ZoomUser ID',
    
    -- 系统信息
    client_ip VARCHAR(45) COMMENT '客户端IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_request_id (request_id),
    INDEX idx_api_path (api_path),
    INDEX idx_request_time (request_time),
    INDEX idx_business_type (business_type),
    INDEX idx_business_id (business_id),
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_is_success (is_success),
    INDEX idx_response_status (response_status),
    INDEX idx_duration (duration_ms),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Zoom API调用日志表';

-- 验证表创建
DESCRIBE t_zoom_api_logs;

-- 显示表结构
SHOW CREATE TABLE t_zoom_api_logs;
