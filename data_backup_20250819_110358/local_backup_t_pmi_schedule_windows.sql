-- MySQL dump 10.13  Distrib 8.0.11, for macos10.13 (x86_64)
--
-- Host: localhost    Database: zoombusV
-- ------------------------------------------------------
-- Server version	8.0.11

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
 SET NAMES utf8mb4 ;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `t_pmi_schedule_windows`
--

DROP TABLE IF EXISTS `t_pmi_schedule_windows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
 SET character_set_client = utf8mb4 ;
CREATE TABLE `t_pmi_schedule_windows` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NOT NULL,
  `end_time` time NOT NULL,
  `error_message` text,
  `schedule_id` bigint(20) NOT NULL,
  `pmi_record_id` bigint(20) NOT NULL,
  `start_time` time NOT NULL,
  `status` varchar(255) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `window_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `zoom_user_id` bigint(20) DEFAULT NULL,
  `actual_end_time` datetime(6) DEFAULT NULL,
  `actual_start_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_window_pmi_record` (`pmi_record_id`),
  CONSTRAINT `fk_window_pmi_record` FOREIGN KEY (`pmi_record_id`) REFERENCES `t_pmi_records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=396 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_pmi_schedule_windows`
--

LOCK TABLES `t_pmi_schedule_windows` WRITE;
/*!40000 ALTER TABLE `t_pmi_schedule_windows` DISABLE KEYS */;
INSERT INTO `t_pmi_schedule_windows` VALUES (365,'2025-08-19 09:32:10.000000','23:59:59',NULL,153,2869,'00:00:00','ACTIVE','2025-08-19 09:33:07.316993','2025-08-19','2026-05-19',NULL,NULL,'2025-08-19 09:33:07.193493'),(366,'2025-08-19 09:32:10.000000','23:59:59',NULL,154,2868,'00:00:00','ACTIVE','2025-08-19 09:33:07.356932','2025-08-19','2026-04-14',NULL,NULL,'2025-08-19 09:33:07.354740'),(367,'2025-08-19 09:32:10.000000','23:59:59',NULL,155,2835,'00:00:00','ACTIVE','2025-08-19 09:33:07.385454','2025-08-19','2025-11-22',NULL,NULL,'2025-08-19 09:33:07.381479'),(368,'2025-08-19 09:32:10.000000','23:59:59',NULL,156,2857,'00:00:00','ACTIVE','2025-08-19 09:33:07.412760','2025-08-19','2026-02-25',NULL,NULL,'2025-08-19 09:33:07.409092'),(369,'2025-08-19 09:32:10.000000','23:59:59',NULL,157,2882,'00:00:00','ACTIVE','2025-08-19 09:33:07.435108','2025-08-19','2026-07-01',NULL,NULL,'2025-08-19 09:33:07.432141'),(370,'2025-08-19 09:32:10.000000','23:59:59',NULL,158,2998,'00:00:00','ACTIVE','2025-08-19 09:33:07.460428','2025-08-19','2026-07-21',NULL,NULL,'2025-08-19 09:33:07.457989'),(371,'2025-08-19 09:32:10.000000','23:59:59',NULL,159,2911,'00:00:00','ACTIVE','2025-08-19 09:33:07.479237','2025-08-19','2025-11-08',NULL,NULL,'2025-08-19 09:33:07.476873'),(372,'2025-08-19 09:32:10.000000','23:59:59',NULL,160,2901,'00:00:00','ACTIVE','2025-08-19 09:33:07.501407','2025-08-19','2025-11-28',NULL,NULL,'2025-08-19 09:33:07.498556'),(373,'2025-08-19 09:32:10.000000','23:59:59',NULL,161,2904,'00:00:00','ACTIVE','2025-08-19 09:33:07.525334','2025-08-19','2025-10-27',NULL,NULL,'2025-08-19 09:33:07.522095'),(374,'2025-08-19 09:32:10.000000','23:59:59',NULL,162,2823,'00:00:00','ACTIVE','2025-08-19 09:33:07.543631','2025-08-19','2025-08-25',NULL,NULL,'2025-08-19 09:33:07.541287'),(375,'2025-08-19 09:32:10.000000','23:59:59',NULL,163,2820,'00:00:00','COMPLETED','2025-08-19 09:32:10.000000','2025-08-19','2025-08-17',NULL,NULL,NULL),(376,'2025-08-19 09:32:10.000000','23:59:59',NULL,164,2716,'00:00:00','ACTIVE','2025-08-19 09:33:07.570734','2025-08-19','2026-04-25',NULL,NULL,'2025-08-19 09:33:07.566478'),(377,'2025-08-19 09:32:10.000000','23:59:59',NULL,165,2850,'00:00:00','ACTIVE','2025-08-19 09:33:07.593538','2025-08-19','2026-03-06',NULL,NULL,'2025-08-19 09:33:07.590452'),(378,'2025-08-19 09:32:10.000000','23:59:59',NULL,166,2861,'00:00:00','ACTIVE','2025-08-19 09:33:07.618582','2025-08-19','2026-04-11',NULL,NULL,'2025-08-19 09:33:07.612400'),(379,'2025-08-19 09:32:10.000000','23:59:59',NULL,167,2945,'00:00:00','ACTIVE','2025-08-19 09:33:07.637288','2025-08-19','2026-01-18',NULL,NULL,'2025-08-19 09:33:07.634924'),(380,'2025-08-19 09:32:10.000000','23:59:59',NULL,168,2953,'00:00:00','ACTIVE','2025-08-19 09:33:07.656714','2025-08-19','2026-03-01',NULL,NULL,'2025-08-19 09:33:07.654632'),(381,'2025-08-19 09:32:10.000000','23:59:59',NULL,169,2849,'00:00:00','ACTIVE','2025-08-19 09:33:07.682800','2025-08-19','2026-06-06',NULL,NULL,'2025-08-19 09:33:07.676090'),(382,'2025-08-19 09:32:10.000000','23:59:59',NULL,170,2865,'00:00:00','ACTIVE','2025-08-19 09:33:07.699540','2025-08-19','2026-03-01',NULL,NULL,'2025-08-19 09:33:07.697027'),(383,'2025-08-19 09:32:10.000000','23:59:59',NULL,171,2899,'00:00:00','ACTIVE','2025-08-19 09:33:07.718765','2025-08-19','2025-11-10',NULL,NULL,'2025-08-19 09:33:07.716511'),(384,'2025-08-19 09:32:10.000000','23:59:59',NULL,172,3200,'00:00:00','ACTIVE','2025-08-19 09:33:07.737423','2025-08-19','2025-12-07',NULL,NULL,'2025-08-19 09:33:07.734523'),(385,'2025-08-19 09:32:10.000000','23:59:59',NULL,173,3307,'00:00:00','ACTIVE','2025-08-19 09:33:07.757934','2025-08-19','2026-07-07',NULL,NULL,'2025-08-19 09:33:07.754795');
/*!40000 ALTER TABLE `t_pmi_schedule_windows` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-19 11:04:25
