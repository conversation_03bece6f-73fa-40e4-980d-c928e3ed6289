#!/bin/bash

# 数据迁移脚本：从 nslcp.com 服务器导出数据并迁移到本地
# 作者：ZoomBus 开发团队
# 日期：2025-08-19

set -e  # 遇到错误立即退出

# 配置变量
REMOTE_HOST="nslcp.com"
REMOTE_USER="root"
REMOTE_PASSWORD="nvshen2018"
REMOTE_DB_HOST="127.0.0.1"
REMOTE_DB_USER="root"
REMOTE_DB_PASSWORD="nvshen2018"
REMOTE_DB_NAME="xp"  # 假设远程数据库名，请确认

LOCAL_DB_HOST="localhost"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="nvshen2018"
LOCAL_DB_NAME="zoombusV"

# 工作目录
WORK_DIR="/Users/<USER>/vibeCoding/zoombus"
BACKUP_DIR="$WORK_DIR/data_backup_$(date +%Y%m%d_%H%M%S)"
MIGRATION_SCRIPT="$WORK_DIR/data_migration_script.sql"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL 客户端未安装"
        exit 1
    fi
    
    if ! command -v mysqldump &> /dev/null; then
        log_error "mysqldump 工具未安装"
        exit 1
    fi
    
    if ! command -v ssh &> /dev/null; then
        log_error "SSH 客户端未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    log_success "备份目录创建完成"
}

# 测试远程连接
test_remote_connection() {
    log_info "测试远程服务器连接..."
    
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH连接成功'" 2>/dev/null; then
        log_error "无法连接到远程服务器 $REMOTE_HOST"
        log_error "请确保："
        log_error "1. 服务器地址正确"
        log_error "2. SSH密钥已配置或可以密码登录"
        log_error "3. 网络连接正常"
        exit 1
    fi
    
    log_success "远程服务器连接测试成功"
}

# 测试本地数据库连接
test_local_db_connection() {
    log_info "测试本地数据库连接..."
    
    if ! mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" -e "SELECT 1;" &>/dev/null; then
        log_error "无法连接到本地数据库"
        exit 1
    fi
    
    log_success "本地数据库连接测试成功"
}

# 从远程服务器导出数据
export_remote_data() {
    log_info "从远程服务器导出数据..."
    
    # 导出 t_wx_user 表
    log_info "导出 t_wx_user 表..."
    ssh "$REMOTE_USER@$REMOTE_HOST" "mysqldump -h$REMOTE_DB_HOST -u$REMOTE_DB_USER -p$REMOTE_DB_PASSWORD $REMOTE_DB_NAME t_wx_user --single-transaction --routines --triggers" > "$BACKUP_DIR/t_wx_user.sql"
    
    if [ $? -eq 0 ]; then
        log_success "t_wx_user 表导出完成"
    else
        log_error "t_wx_user 表导出失败"
        exit 1
    fi
    
    # 导出 t_zoom_pmi 表
    log_info "导出 t_zoom_pmi 表..."
    ssh "$REMOTE_USER@$REMOTE_HOST" "mysqldump -h$REMOTE_DB_HOST -u$REMOTE_DB_USER -p$REMOTE_DB_PASSWORD $REMOTE_DB_NAME t_zoom_pmi --single-transaction --routines --triggers" > "$BACKUP_DIR/t_zoom_pmi.sql"
    
    if [ $? -eq 0 ]; then
        log_success "t_zoom_pmi 表导出完成"
    else
        log_error "t_zoom_pmi 表导出失败"
        exit 1
    fi
    
    # 检查导出文件大小
    wx_user_size=$(wc -l < "$BACKUP_DIR/t_wx_user.sql")
    zoom_pmi_size=$(wc -l < "$BACKUP_DIR/t_zoom_pmi.sql")
    
    log_info "导出文件统计："
    log_info "  t_wx_user.sql: $wx_user_size 行"
    log_info "  t_zoom_pmi.sql: $zoom_pmi_size 行"
}

# 备份本地数据
backup_local_data() {
    log_info "备份本地数据..."
    
    # 备份相关表
    local tables=("t_users" "t_pmi_records" "t_pmi_schedule_windows" "t_pmi_schedules" "old_t_wx_user" "old_t_zoom_pmi")
    
    for table in "${tables[@]}"; do
        log_info "备份表: $table"
        mysqldump -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" "$LOCAL_DB_NAME" "$table" --single-transaction > "$BACKUP_DIR/local_backup_$table.sql" 2>/dev/null || log_warning "表 $table 不存在或备份失败"
    done
    
    log_success "本地数据备份完成"
}

# 清空本地old表
clear_local_old_tables() {
    log_info "清空本地old表..."
    
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" "$LOCAL_DB_NAME" <<EOF
-- 清空 old_t_wx_user 表
DELETE FROM old_t_wx_user;
ALTER TABLE old_t_wx_user AUTO_INCREMENT = 1;

-- 清空 old_t_zoom_pmi 表  
DELETE FROM old_t_zoom_pmi;
ALTER TABLE old_t_zoom_pmi AUTO_INCREMENT = 1;
EOF
    
    if [ $? -eq 0 ]; then
        log_success "old表清空完成"
    else
        log_error "old表清空失败"
        exit 1
    fi
}

# 导入远程数据到本地old表
import_remote_data() {
    log_info "导入远程数据到本地old表..."
    
    # 修改SQL文件中的表名
    log_info "修改表名: t_wx_user -> old_t_wx_user"
    sed 's/`t_wx_user`/`old_t_wx_user`/g' "$BACKUP_DIR/t_wx_user.sql" > "$BACKUP_DIR/old_t_wx_user.sql"
    
    log_info "修改表名: t_zoom_pmi -> old_t_zoom_pmi"
    sed 's/`t_zoom_pmi`/`old_t_zoom_pmi`/g' "$BACKUP_DIR/t_zoom_pmi.sql" > "$BACKUP_DIR/old_t_zoom_pmi.sql"
    
    # 导入数据
    log_info "导入 old_t_wx_user 数据..."
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" "$LOCAL_DB_NAME" < "$BACKUP_DIR/old_t_wx_user.sql"
    
    if [ $? -eq 0 ]; then
        log_success "old_t_wx_user 数据导入完成"
    else
        log_error "old_t_wx_user 数据导入失败"
        exit 1
    fi
    
    log_info "导入 old_t_zoom_pmi 数据..."
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" "$LOCAL_DB_NAME" < "$BACKUP_DIR/old_t_zoom_pmi.sql"
    
    if [ $? -eq 0 ]; then
        log_success "old_t_zoom_pmi 数据导入完成"
    else
        log_error "old_t_zoom_pmi 数据导入失败"
        exit 1
    fi
}

# 清空目标表
clear_target_tables() {
    log_info "清空目标表..."
    
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" "$LOCAL_DB_NAME" <<EOF
-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 清空目标表
DELETE FROM t_pmi_schedule_windows;
ALTER TABLE t_pmi_schedule_windows AUTO_INCREMENT = 1;

DELETE FROM t_pmi_schedules;
ALTER TABLE t_pmi_schedules AUTO_INCREMENT = 1;

DELETE FROM t_pmi_records;
ALTER TABLE t_pmi_records AUTO_INCREMENT = 1;

DELETE FROM t_users;
ALTER TABLE t_users AUTO_INCREMENT = 1;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
EOF
    
    if [ $? -eq 0 ]; then
        log_success "目标表清空完成"
    else
        log_error "目标表清空失败"
        exit 1
    fi
}

# 执行数据迁移
execute_migration() {
    log_info "执行数据迁移脚本..."
    
    if [ ! -f "$MIGRATION_SCRIPT" ]; then
        log_error "迁移脚本不存在: $MIGRATION_SCRIPT"
        exit 1
    fi
    
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" "$LOCAL_DB_NAME" < "$MIGRATION_SCRIPT"
    
    if [ $? -eq 0 ]; then
        log_success "数据迁移完成"
    else
        log_error "数据迁移失败"
        exit 1
    fi
}

# 验证迁移结果
verify_migration() {
    log_info "验证迁移结果..."
    
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" "$LOCAL_DB_NAME" <<EOF
-- 统计迁移后的数据
SELECT 'old_t_wx_user' as table_name, COUNT(*) as count FROM old_t_wx_user
UNION ALL
SELECT 'old_t_zoom_pmi' as table_name, COUNT(*) as count FROM old_t_zoom_pmi
UNION ALL
SELECT 't_users' as table_name, COUNT(*) as count FROM t_users
UNION ALL
SELECT 't_pmi_records' as table_name, COUNT(*) as count FROM t_pmi_records
UNION ALL
SELECT 't_pmi_schedules' as table_name, COUNT(*) as count FROM t_pmi_schedules
UNION ALL
SELECT 't_pmi_schedule_windows' as table_name, COUNT(*) as count FROM t_pmi_schedule_windows;
EOF
    
    log_success "迁移结果验证完成"
}

# 主函数
main() {
    log_info "开始数据迁移流程..."
    log_info "时间: $(date)"
    log_info "工作目录: $WORK_DIR"
    log_info "备份目录: $BACKUP_DIR"
    
    # 执行步骤
    check_dependencies
    create_backup_dir
    test_remote_connection
    test_local_db_connection
    export_remote_data
    backup_local_data
    clear_local_old_tables
    import_remote_data
    clear_target_tables
    execute_migration
    verify_migration
    
    log_success "数据迁移流程完成！"
    log_info "备份文件保存在: $BACKUP_DIR"
    log_info "如有问题，可以使用备份文件恢复数据"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
