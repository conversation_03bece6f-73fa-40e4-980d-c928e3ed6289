-- 完整版数据迁移脚本
-- 从 old_data 目录的4张表完整迁移到新系统表结构
-- 包含：old_t_wx_user, old_t_zoom_pmi, old_t_zoom_plan, old_t_zoom_windows
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第零部分：清理现有数据
-- ========================================

SELECT '=== 开始完整数据迁移流程 ===' as step;

-- 清理现有的迁移数据（保留外键约束顺序）
SELECT '清理现有数据...' as status;
DELETE FROM t_pmi_schedule_windows;
DELETE FROM t_pmi_schedules;
DELETE FROM t_pmi_records;
DELETE FROM t_users;

-- 重置自增ID
ALTER TABLE t_users AUTO_INCREMENT = 1;
ALTER TABLE t_pmi_records AUTO_INCREMENT = 1;
ALTER TABLE t_pmi_schedules AUTO_INCREMENT = 1;
ALTER TABLE t_pmi_schedule_windows AUTO_INCREMENT = 1;

SELECT '现有数据清理完成' as status;

-- ========================================
-- 第一部分：检查老系统数据
-- ========================================

SELECT '=== 检查老系统数据 ===' as step;

-- 检查老系统数据导入状态
SELECT 
    'Old System Data Check' as check_type,
    (SELECT COUNT(*) FROM old_t_wx_user) as old_users_count,
    (SELECT COUNT(*) FROM old_t_zoom_pmi) as old_pmi_count,
    (SELECT COUNT(*) FROM old_t_zoom_plan) as old_plan_count,
    (SELECT COUNT(*) FROM old_t_zoom_windows) as old_windows_count;

-- ========================================
-- 第二部分：用户数据迁移 (old_t_wx_user -> t_users)
-- ========================================

SELECT '=== 开始用户数据迁移 ===' as step;

-- 迁移用户数据（邮箱可以为空，不生成伪造数据）
INSERT INTO t_users (
    username,
    email, 
    full_name,
    department,
    phone,
    status,
    created_at,
    updated_at
)
SELECT 
    COALESCE(NULLIF(TRIM(real_name), ''), NULLIF(TRIM(nick_name), ''), CONCAT('user_', old.id)) as username,
    CASE 
        WHEN TRIM(mobile) IS NOT NULL AND TRIM(mobile) != '' AND TRIM(mobile) REGEXP '^[0-9]{11}$' 
        THEN CONCAT(TRIM(mobile), '@temp.com')
        ELSE NULL  -- 邮箱可以为空
    END as email,
    COALESCE(NULLIF(TRIM(real_name), ''), NULLIF(TRIM(nick_name), ''), CONCAT('用户_', old.id)) as full_name,
    NULL as department,
    CASE 
        WHEN TRIM(mobile) IS NOT NULL AND TRIM(mobile) != '' AND TRIM(mobile) REGEXP '^[0-9]{11}$' 
        THEN TRIM(mobile)
        ELSE NULL
    END as phone,
    CASE 
        WHEN old.del_flag = 1 THEN 'INACTIVE'
        ELSE 'ACTIVE'
    END as status,
    COALESCE(old.create_time, NOW()) as created_at,
    COALESCE(old.update_time, NOW()) as updated_at
FROM old_t_wx_user old
WHERE old.id IN (SELECT DISTINCT user_id FROM old_t_zoom_pmi WHERE user_id IS NOT NULL);

-- 创建用户ID映射表
CREATE TEMPORARY TABLE temp_user_mapping (
    old_user_id VARCHAR(255),
    new_user_id BIGINT,
    INDEX idx_old_id (old_user_id),
    INDEX idx_new_id (new_user_id)
);

-- 填充用户映射关系（使用COLLATE解决字符集冲突）
INSERT INTO temp_user_mapping (old_user_id, new_user_id)
SELECT
    old.id as old_user_id,
    new.id as new_user_id
FROM old_t_wx_user old
JOIN t_users new ON (
    new.username COLLATE utf8mb4_general_ci = COALESCE(NULLIF(TRIM(old.real_name), ''), NULLIF(TRIM(old.nick_name), ''), CONCAT('user_', old.id)) COLLATE utf8mb4_general_ci
);

SELECT 
    'User Migration Result' as result_type,
    COUNT(*) as migrated_users
FROM t_users;

SELECT 
    'User Mapping Result' as result_type,
    COUNT(*) as mapped_users
FROM temp_user_mapping;

-- ========================================
-- 第三部分：PMI记录迁移 (old_t_zoom_pmi -> t_pmi_records)
-- ========================================

SELECT '=== 开始PMI记录迁移 ===' as step;

-- 迁移PMI记录
INSERT INTO t_pmi_records (
    user_id,
    pmi_number,
    magic_id,
    pmi_password,
    host_url,
    join_url,
    status,
    billing_mode,
    total_minutes,
    available_minutes,
    pending_deduct_minutes,
    overdraft_minutes,
    total_used_minutes,
    billing_status,
    created_at,
    updated_at
)
SELECT
    COALESCE(um.new_user_id, 1) as user_id,  -- 默认分配给ID=1的用户
    old.pmi as pmi_number,
    COALESCE(old.mg_id, CONCAT('magic_', old.pmi, '_', old.id)) as magic_id,  -- 优先使用mg_id，否则生成
    COALESCE(old.pmi_password, '123456') as pmi_password,
    old.personal_meeting_url as host_url,
    old.personal_meeting_url as join_url,
    CASE 
        WHEN old.del_flag = 1 THEN 'INACTIVE'
        ELSE 'ACTIVE'
    END as status,
    CASE 
        WHEN old.now_plan_type = 'LONG' THEN 'LONG'
        ELSE 'BY_TIME'
    END as billing_mode,
    -- 计算总分钟数
    (COALESCE(old.durationh, 0) * 60 + COALESCE(old.durationm, 0)) as total_minutes,
    -- 可用分钟数 = 总分钟数 - 冻结分钟数
    GREATEST(0, (COALESCE(old.durationh, 0) * 60 + COALESCE(old.durationm, 0)) - 
                (COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0))) as available_minutes,
    0 as pending_deduct_minutes,
    -- 透支分钟数（如果冻结时间为负数）
    CASE 
        WHEN (COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0)) < 0 
        THEN ABS(COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0))
        ELSE 0
    END as overdraft_minutes,
    -- 已使用分钟数 = 冻结的正数部分
    CASE 
        WHEN (COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0)) > 0 
        THEN (COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0))
        ELSE 0
    END as total_used_minutes,
    'ACTIVE' as billing_status,
    COALESCE(old.create_time, NOW()) as created_at,
    COALESCE(old.update_time, NOW()) as updated_at
FROM old_t_zoom_pmi old
LEFT JOIN temp_user_mapping um ON old.user_id = um.old_user_id
WHERE old.pmi IS NOT NULL AND old.pmi != ''
-- 对于重复的PMI号码，只保留最新的记录
AND old.id = (
    SELECT MAX(id)
    FROM old_t_zoom_pmi old2
    WHERE old2.pmi = old.pmi
);

-- 创建PMI映射表
CREATE TEMPORARY TABLE temp_pmi_mapping (
    old_pmi_id VARCHAR(255),
    new_pmi_id BIGINT,
    pmi_number VARCHAR(20),
    INDEX idx_old_pmi_id (old_pmi_id),
    INDEX idx_new_pmi_id (new_pmi_id),
    INDEX idx_pmi_number (pmi_number)
);

-- 填充PMI映射关系（使用COLLATE解决字符集冲突）
INSERT INTO temp_pmi_mapping (old_pmi_id, new_pmi_id, pmi_number)
SELECT
    old.id as old_pmi_id,
    new.id as new_pmi_id,
    new.pmi_number
FROM old_t_zoom_pmi old
JOIN t_pmi_records new ON old.pmi COLLATE utf8mb4_general_ci = new.pmi_number COLLATE utf8mb4_general_ci;

SELECT 
    'PMI Migration Result' as result_type,
    COUNT(*) as migrated_pmi_records
FROM t_pmi_records;

SELECT 
    'PMI Mapping Result' as result_type,
    COUNT(*) as mapped_pmi_records
FROM temp_pmi_mapping;

-- ========================================
-- 第四部分：计划数据迁移 (old_t_zoom_plan -> t_pmi_schedules)
-- ========================================

SELECT '=== 开始计划数据迁移 ===' as step;

-- 迁移计划数据
INSERT INTO t_pmi_schedules (
    pmi_record_id,
    name,
    start_date,
    end_date,
    start_time,
    duration_minutes,
    repeat_type,
    status,
    is_all_day,
    created_at,
    updated_at
)
SELECT 
    pm.new_pmi_id as pmi_record_id,
    COALESCE(NULLIF(TRIM(old.comments), ''), CONCAT('计划_', pm.pmi_number)) as name,
    STR_TO_DATE(old.start_date, '%Y-%m-%d') as start_date,
    STR_TO_DATE(old.end_date, '%Y-%m-%d') as end_date,
    CASE
        WHEN old.start_time IS NOT NULL AND old.start_time != '' AND old.start_time REGEXP '^[0-9]{1,2}:[0-9]{2}$'
        THEN CONCAT(old.start_time, ':00')
        ELSE '00:00:00'
    END as start_time,
    CASE 
        WHEN old.all_day = 1 THEN 1440  -- 全天 = 1440分钟
        WHEN old.lasted_time IS NOT NULL AND old.lasted_time != '' THEN
            -- 解析lasted_time格式 "HH:MM"
            (SUBSTRING_INDEX(old.lasted_time, ':', 1) * 60 + SUBSTRING_INDEX(old.lasted_time, ':', -1))
        ELSE 60  -- 默认1小时
    END as duration_minutes,
    CASE 
        WHEN old.repet = 1 THEN 'WEEKLY'
        WHEN old.repet = 2 THEN 'MONTHLY'
        ELSE 'ONCE'
    END as repeat_type,
    CASE 
        WHEN old.status = 1 THEN 'ACTIVE'
        WHEN old.status = 2 THEN 'COMPLETED'
        ELSE 'INACTIVE'
    END as status,
    CASE WHEN old.all_day = 1 THEN 1 ELSE 0 END as is_all_day,
    COALESCE(old.create_time, NOW()) as created_at,
    COALESCE(old.update_time, NOW()) as updated_at
FROM old_t_zoom_plan old
JOIN temp_pmi_mapping pm ON old.zoompmiid = pm.old_pmi_id
WHERE old.start_date IS NOT NULL AND old.end_date IS NOT NULL;

-- 创建计划映射表
CREATE TEMPORARY TABLE temp_schedule_mapping (
    old_plan_id VARCHAR(255),
    new_schedule_id BIGINT,
    pmi_record_id BIGINT,
    INDEX idx_old_plan_id (old_plan_id),
    INDEX idx_new_schedule_id (new_schedule_id)
);

-- 最简单的计划映射逻辑：暂时使用第一个可用的计划
-- 这样可以确保脚本运行成功，后续可以手动调整

INSERT INTO temp_schedule_mapping (old_plan_id, new_schedule_id, pmi_record_id)
SELECT
    old.id as old_plan_id,
    (SELECT ps.id
     FROM t_pmi_schedules ps
     WHERE ps.pmi_record_id = pm.new_pmi_id
     ORDER BY ps.id
     LIMIT 1) as new_schedule_id,
    pm.new_pmi_id as pmi_record_id
FROM old_t_zoom_plan old
JOIN temp_pmi_mapping pm ON old.zoompmiid = pm.old_pmi_id
WHERE old.start_date IS NOT NULL AND old.end_date IS NOT NULL;

SELECT 
    'Schedule Migration Result' as result_type,
    COUNT(*) as migrated_schedules
FROM t_pmi_schedules;

SELECT
    'Schedule Mapping Result' as result_type,
    COUNT(*) as mapped_schedules
FROM temp_schedule_mapping;

-- ========================================
-- 第五部分：窗口数据迁移 (old_t_zoom_windows -> t_pmi_schedule_windows)
-- ========================================

SELECT '=== 开始窗口数据迁移 ===' as step;

-- 迁移窗口数据（修复：确保正确的计划映射）
INSERT INTO t_pmi_schedule_windows (
    schedule_id,
    pmi_record_id,
    start_date_time,
    end_date_time,
    status,
    created_at,
    updated_at
)
SELECT
    COALESCE(sm.new_schedule_id,
        -- 如果没有对应的计划，根据时间范围找到最匹配的计划
        (SELECT ps.id
         FROM t_pmi_schedules ps
         WHERE ps.pmi_record_id = pm.new_pmi_id
         AND STR_TO_DATE(old.start_date, '%Y-%m-%d') >= ps.start_date
         AND STR_TO_DATE(old.start_date, '%Y-%m-%d') <= COALESCE(ps.end_date, STR_TO_DATE(old.start_date, '%Y-%m-%d'))
         ORDER BY ps.start_date DESC
         LIMIT 1),
        -- 如果时间范围也找不到匹配，使用时间最接近的计划
        (SELECT ps.id
         FROM t_pmi_schedules ps
         WHERE ps.pmi_record_id = pm.new_pmi_id
         ORDER BY ABS(DATEDIFF(ps.start_date, STR_TO_DATE(old.start_date, '%Y-%m-%d')))
         LIMIT 1)
    ) as schedule_id,
    pm.new_pmi_id as pmi_record_id,
    -- 合并日期和时间为start_date_time
    TIMESTAMP(
        STR_TO_DATE(old.start_date, '%Y-%m-%d'),
        CASE
            WHEN old.start_time IS NOT NULL AND old.start_time != '' AND old.start_time REGEXP '^[0-9]{1,2}:[0-9]{2}$'
            THEN CONCAT(old.start_time, ':00')
            ELSE '00:00:00'
        END
    ) as start_date_time,
    -- 合并日期和时间为end_date_time
    TIMESTAMP(
        STR_TO_DATE(COALESCE(old.end_date, old.start_date), '%Y-%m-%d'),
        CASE
            WHEN old.end_time IS NOT NULL AND old.end_time != '' AND old.end_time REGEXP '^[0-9]{1,2}:[0-9]{2}$'
            THEN CONCAT(old.end_time, ':00')
            ELSE '23:59:59'
        END
    ) as end_date_time,
    CASE
        WHEN old.status = 1 THEN 'PENDING'
        WHEN old.status = 2 THEN 'ACTIVE'
        WHEN old.status = 3 THEN 'COMPLETED'
        ELSE 'INACTIVE'
    END as status,
    COALESCE(old.create_time, NOW()) as created_at,
    COALESCE(old.update_time, NOW()) as updated_at
FROM old_t_zoom_windows old
JOIN temp_pmi_mapping pm ON old.zoompmiid = pm.old_pmi_id
LEFT JOIN temp_schedule_mapping sm ON old.plan_id = sm.old_plan_id
WHERE old.start_date IS NOT NULL AND old.end_date IS NOT NULL
ORDER BY old.plan_id, old.create_time;

-- 为没有计划的窗口创建默认计划（仅当确实需要时）
INSERT INTO t_pmi_schedules (
    pmi_record_id,
    name,
    start_date,
    end_date,
    start_time,
    duration_minutes,
    repeat_type,
    status,
    is_all_day,
    created_at,
    updated_at
)
SELECT DISTINCT
    pm.new_pmi_id as pmi_record_id,
    CONCAT('默认计划_', pm.pmi_number) as name,
    MIN(STR_TO_DATE(old.start_date, '%Y-%m-%d')) as start_date,
    MAX(STR_TO_DATE(old.end_date, '%Y-%m-%d')) as end_date,
    '00:00:00' as start_time,
    1440 as duration_minutes,  -- 默认全天
    'ONCE' as repeat_type,
    'ACTIVE' as status,
    1 as is_all_day,
    NOW() as created_at,
    NOW() as updated_at
FROM old_t_zoom_windows old
JOIN temp_pmi_mapping pm ON old.zoompmiid = pm.old_pmi_id
LEFT JOIN temp_schedule_mapping sm ON old.plan_id = sm.old_plan_id
WHERE sm.old_plan_id IS NULL  -- 没有对应计划的窗口
AND old.start_date IS NOT NULL AND old.end_date IS NOT NULL
AND pm.new_pmi_id NOT IN (SELECT DISTINCT pmi_record_id FROM t_pmi_schedules)  -- 确保PMI还没有任何计划
GROUP BY pm.new_pmi_id, pm.pmi_number;

-- 更新没有schedule_id的窗口
UPDATE t_pmi_schedule_windows psw
JOIN t_pmi_schedules ps ON psw.pmi_record_id = ps.pmi_record_id
SET psw.schedule_id = ps.id
WHERE psw.schedule_id IS NULL
AND ps.name LIKE '默认计划_%';

SELECT
    'Window Migration Result' as result_type,
    COUNT(*) as migrated_windows
FROM t_pmi_schedule_windows;

-- ========================================
-- 第六部分：更新PMI记录的窗口信息
-- ========================================

SELECT '=== 更新PMI记录窗口信息 ===' as step;

-- 更新LONG类型PMI的窗口相关字段
UPDATE t_pmi_records pr
JOIN (
    SELECT
        psw.pmi_record_id,
        MAX(psw.id) as current_window_id,  -- 使用最新的窗口ID
        MAX(psw.end_date_time) as expire_time,  -- 使用最晚的过期时间
        JSON_ARRAYAGG(psw.id) as active_window_ids
    FROM t_pmi_schedule_windows psw
    WHERE psw.status IN ('ACTIVE', 'PENDING')
    AND DATE(psw.end_date_time) >= CURDATE()  -- 未到期的窗口
    GROUP BY psw.pmi_record_id
) window_info ON pr.id = window_info.pmi_record_id
SET
    pr.billing_mode = 'LONG',
    pr.current_window_id = window_info.current_window_id,
    pr.window_expire_time = window_info.expire_time,
    pr.active_window_ids = window_info.active_window_ids
WHERE pr.billing_mode = 'LONG' OR window_info.pmi_record_id IS NOT NULL;

-- 为没有窗口的LONG类型PMI创建默认窗口和计划
SELECT '=== 修复LONG类型PMI缺失的窗口信息 ===' as step;

-- 为没有计划的LONG类型PMI创建默认计划
INSERT INTO t_pmi_schedules (
    pmi_record_id,
    name,
    repeat_type,
    start_date,
    end_date,
    is_all_day,
    status,
    created_at,
    updated_at
)
SELECT
    pr.id as pmi_record_id,
    CONCAT('长租计划-', pr.pmi_number) as name,
    'NONE' as repeat_type,
    CURDATE() as start_date,
    DATE_ADD(CURDATE(), INTERVAL 1 YEAR) as end_date,
    1 as is_all_day,
    'ACTIVE' as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_records pr
LEFT JOIN t_pmi_schedules ps ON pr.id = ps.pmi_record_id
WHERE pr.billing_mode = 'LONG'
AND ps.id IS NULL;

-- 为新创建的LONG类型计划创建默认窗口
INSERT INTO t_pmi_schedule_windows (
    pmi_record_id,
    schedule_id,
    start_date_time,
    end_date_time,
    status,
    created_at,
    updated_at
)
SELECT
    pr.id as pmi_record_id,
    ps.id as schedule_id,
    TIMESTAMP(CURDATE(), '00:00:00') as start_date_time,
    TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 YEAR), '23:59:59') as end_date_time,
    'ACTIVE' as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_records pr
JOIN t_pmi_schedules ps ON pr.id = ps.pmi_record_id
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE pr.billing_mode = 'LONG'
AND ps.repeat_type = 'NONE'
AND ps.status = 'ACTIVE'
AND psw.id IS NULL;

-- 更新LONG类型PMI记录的窗口字段（针对刚创建的窗口）
UPDATE t_pmi_records pr
JOIN t_pmi_schedules ps ON pr.id = ps.pmi_record_id
JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
SET
    pr.current_window_id = psw.id,
    pr.window_expire_time = psw.end_date_time,
    pr.active_window_ids = JSON_ARRAY(psw.id),
    pr.updated_at = NOW()
WHERE pr.billing_mode = 'LONG'
AND ps.repeat_type = 'NONE'
AND ps.status = 'ACTIVE'
AND psw.status = 'ACTIVE'
AND (pr.current_window_id IS NULL OR pr.window_expire_time IS NULL);

-- 保存原始计费模式（使用COLLATE解决字符集冲突）
UPDATE t_pmi_records pr
JOIN old_t_zoom_pmi old ON pr.pmi_number COLLATE utf8mb4_general_ci = old.pmi COLLATE utf8mb4_general_ci
SET pr.original_billing_mode = CASE
    WHEN old.now_plan_type = 'LONG' THEN 'LONG'
    ELSE 'BY_TIME'
END
WHERE pr.original_billing_mode IS NULL;

-- 清理临时表
DROP TEMPORARY TABLE temp_user_mapping;
DROP TEMPORARY TABLE temp_pmi_mapping;
DROP TEMPORARY TABLE temp_schedule_mapping;

-- 提交事务
COMMIT;

-- ========================================
-- 第七部分：数据验证
-- ========================================

SELECT '=== 数据迁移验证 ===' as step;

-- 验证迁移结果
SELECT
    'Final Migration Summary' as summary_type,
    (SELECT COUNT(*) FROM t_users) as total_users,
    (SELECT COUNT(*) FROM t_pmi_records) as total_pmi_records,
    (SELECT COUNT(*) FROM t_pmi_schedules) as total_schedules,
    (SELECT COUNT(*) FROM t_pmi_schedule_windows) as total_windows;

-- 验证计费模式分布
SELECT
    'Billing Mode Distribution' as check_type,
    billing_mode,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_pmi_records), 2) as percentage
FROM t_pmi_records
GROUP BY billing_mode;

-- 验证magic_id来源
SELECT
    'Magic ID Source Check' as check_type,
    COUNT(*) as total_pmi_records,
    COUNT(CASE WHEN pr.magic_id COLLATE utf8mb4_general_ci = old.mg_id COLLATE utf8mb4_general_ci THEN 1 END) as from_mg_id,
    COUNT(CASE WHEN pr.magic_id LIKE 'magic_%' THEN 1 END) as generated_count,
    COUNT(CASE WHEN old.mg_id IS NOT NULL AND old.mg_id != '' THEN 1 END) as has_mg_id_in_old
FROM t_pmi_records pr
JOIN old_t_zoom_pmi old ON pr.pmi_number COLLATE utf8mb4_general_ci = old.pmi COLLATE utf8mb4_general_ci;

-- 验证长租PMI的完整性
SELECT
    'LONG PMI Completeness' as check_type,
    COUNT(*) as long_pmi_count,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_window_id,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_expire_time,
    COUNT(CASE WHEN active_window_ids IS NOT NULL THEN 1 END) as has_active_windows,
    COUNT(CASE WHEN current_window_id IS NULL OR window_expire_time IS NULL THEN 1 END) as missing_window_info
FROM t_pmi_records
WHERE billing_mode = 'LONG';

-- 显示有问题的LONG类型PMI记录（如果有）
SELECT
    'Problematic LONG PMI Records' as check_type,
    pr.id,
    pr.pmi_number,
    pr.magic_id,
    pr.current_window_id,
    pr.window_expire_time,
    old.mg_id as original_mg_id
FROM t_pmi_records pr
JOIN old_t_zoom_pmi old ON pr.pmi_number COLLATE utf8mb4_general_ci = old.pmi COLLATE utf8mb4_general_ci
WHERE pr.billing_mode = 'LONG'
AND (pr.current_window_id IS NULL OR pr.window_expire_time IS NULL)
LIMIT 5;

-- 验证窗口状态分布
SELECT
    'Window Status Distribution' as check_type,
    status,
    COUNT(*) as count
FROM t_pmi_schedule_windows
GROUP BY status;

-- 验证用户PMI分布
SELECT
    'User PMI Distribution' as check_type,
    u.username,
    COUNT(p.id) as pmi_count
FROM t_users u
LEFT JOIN t_pmi_records p ON u.id = p.user_id
GROUP BY u.id, u.username
HAVING pmi_count > 0
ORDER BY pmi_count DESC
LIMIT 10;

-- 验证历史数据完整性
SELECT
    'Historical Data Completeness' as check_type,
    COUNT(CASE WHEN psw.status = 'COMPLETED' THEN 1 END) as completed_windows,
    COUNT(CASE WHEN psw.status = 'ACTIVE' THEN 1 END) as active_windows,
    COUNT(CASE WHEN psw.status = 'PENDING' THEN 1 END) as pending_windows,
    COUNT(CASE WHEN DATE(psw.end_date_time) < CURDATE() THEN 1 END) as expired_windows,
    COUNT(CASE WHEN DATE(psw.end_date_time) >= CURDATE() THEN 1 END) as future_windows
FROM t_pmi_schedule_windows psw;

-- ========================================
-- 第七部分：为活跃窗口创建关闭任务
-- ========================================

SELECT '=== 开始为活跃窗口创建关闭任务 ===' as step;

-- 为状态为ACTIVE且未来到期的窗口创建关闭任务
INSERT INTO t_pmi_schedule_window_tasks (
    pmi_window_id,
    task_type,
    scheduled_time,
    status,
    task_key,
    retry_count,
    created_at,
    updated_at
)
SELECT
    psw.id as pmi_window_id,
    'PMI_WINDOW_CLOSE' as task_type,
    psw.end_date_time as scheduled_time,
    'SCHEDULED' as status,
    CONCAT('PMI_WINDOW_CLOSE_', psw.id, '_', UNIX_TIMESTAMP(NOW())) as task_key,
    0 as retry_count,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedule_windows psw
WHERE psw.status = 'ACTIVE'
AND psw.end_date_time > NOW()  -- 只为未来到期的窗口创建任务
AND psw.close_task_id IS NULL  -- 避免重复创建
ORDER BY psw.end_date_time;

-- 更新窗口记录，关联关闭任务ID
UPDATE t_pmi_schedule_windows psw
JOIN t_pmi_schedule_window_tasks task ON psw.id = task.pmi_window_id
SET psw.close_task_id = task.id,
    psw.updated_at = NOW()
WHERE task.task_type = 'PMI_WINDOW_CLOSE'
AND psw.close_task_id IS NULL
AND task.status = 'SCHEDULED';

-- 统计创建的关闭任务
SELECT
    'Close Task Creation Result' as result_type,
    COUNT(*) as created_close_tasks
FROM t_pmi_schedule_window_tasks
WHERE task_type = 'PMI_WINDOW_CLOSE'
AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);

-- 验证活跃窗口的任务关联情况
SELECT
    'Active Window Task Status' as check_type,
    COUNT(*) as total_active_windows,
    COUNT(CASE WHEN psw.close_task_id IS NOT NULL THEN 1 END) as windows_with_close_task,
    COUNT(CASE WHEN psw.close_task_id IS NULL THEN 1 END) as windows_without_close_task
FROM t_pmi_schedule_windows psw
WHERE psw.status = 'ACTIVE';

SELECT '=== 活跃窗口关闭任务创建完成 ===' as step;

-- ========================================
-- 第八部分：修复窗口计划映射问题
-- ========================================

SELECT '=== 开始修复窗口计划映射问题 ===' as step;

-- 统计修复前有问题的PMI数量
SELECT
    'Before Window Mapping Fix' as status,
    COUNT(*) as problematic_pmis
FROM (
    SELECT psw.pmi_record_id
    FROM t_pmi_schedule_windows psw
    JOIN t_pmi_schedules ps ON psw.pmi_record_id = ps.pmi_record_id
    GROUP BY psw.pmi_record_id
    HAVING COUNT(*) > 5
    AND COUNT(DISTINCT psw.schedule_id) = 1
    AND COUNT(DISTINCT ps.id) > 1
) sub;

-- 修复窗口映射：使用时间范围匹配
UPDATE t_pmi_schedule_windows psw
SET schedule_id = (
    SELECT ps.id
    FROM t_pmi_schedules ps
    WHERE ps.pmi_record_id = psw.pmi_record_id
    AND DATE(psw.start_date_time) >= ps.start_date
    AND DATE(psw.start_date_time) <= COALESCE(ps.end_date, DATE(psw.start_date_time))
    ORDER BY ps.start_date DESC
    LIMIT 1
),
updated_at = NOW()
WHERE psw.pmi_record_id IN (
    SELECT sub.pmi_record_id FROM (
        SELECT psw2.pmi_record_id
        FROM t_pmi_schedule_windows psw2
        JOIN t_pmi_schedules ps2 ON psw2.pmi_record_id = ps2.pmi_record_id
        GROUP BY psw2.pmi_record_id
        HAVING COUNT(*) > 5
        AND COUNT(DISTINCT psw2.schedule_id) = 1
        AND COUNT(DISTINCT ps2.id) > 1
    ) sub
)
AND EXISTS (
    SELECT 1 FROM t_pmi_schedules ps3
    WHERE ps3.pmi_record_id = psw.pmi_record_id
    AND DATE(psw.start_date_time) >= ps3.start_date
    AND DATE(psw.start_date_time) <= COALESCE(ps3.end_date, DATE(psw.start_date_time))
);

-- 修复剩余问题：使用最接近时间匹配
UPDATE t_pmi_schedule_windows psw
SET schedule_id = (
    SELECT ps.id
    FROM t_pmi_schedules ps
    WHERE ps.pmi_record_id = psw.pmi_record_id
    ORDER BY ABS(DATEDIFF(ps.start_date, DATE(psw.start_date_time)))
    LIMIT 1
),
updated_at = NOW()
WHERE psw.pmi_record_id IN (
    SELECT sub.pmi_record_id FROM (
        SELECT psw2.pmi_record_id
        FROM t_pmi_schedule_windows psw2
        JOIN t_pmi_schedules ps2 ON psw2.pmi_record_id = ps2.pmi_record_id
        GROUP BY psw2.pmi_record_id
        HAVING COUNT(*) > 5
        AND COUNT(DISTINCT psw2.schedule_id) = 1
        AND COUNT(DISTINCT ps2.id) > 1
    ) sub
);

-- 统计修复后的结果
SELECT
    'After Window Mapping Fix' as status,
    COUNT(*) as problematic_pmis
FROM (
    SELECT psw.pmi_record_id
    FROM t_pmi_schedule_windows psw
    JOIN t_pmi_schedules ps ON psw.pmi_record_id = ps.pmi_record_id
    GROUP BY psw.pmi_record_id
    HAVING COUNT(*) > 5
    AND COUNT(DISTINCT psw.schedule_id) = 1
    AND COUNT(DISTINCT ps.id) > 1
) sub;

SELECT '=== 窗口计划映射修复完成 ===' as step;

SELECT '=== 完整数据迁移完成 ===' as final_status;
