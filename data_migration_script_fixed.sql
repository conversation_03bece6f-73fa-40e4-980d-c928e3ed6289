-- 新老系统数据迁移脚本 (修复版本)
-- 从 old_t_wx_user 和 old_t_zoom_pmi 迁移到 t_users 和 t_pmi_records
-- 执行日期: 2025-08-13
-- 修复: 处理空字符串日期值问题

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一部分：用户数据迁移 (old_t_wx_user -> t_users)
-- ========================================

INSERT INTO t_users (
    username,
    email, 
    full_name,
    department,
    phone,
    status,
    created_at,
    updated_at
)
SELECT 
    COALESCE(NULLIF(TRIM(real_name), ''), NULLIF(TRIM(nick_name), ''), CONCAT('user_', old.id)) as username,
    COALESCE(NULLIF(TRIM(mobile), ''), CONCAT('user_', old.id, '@temp.com')) as email,
    COALESCE(NULLIF(TRIM(real_name), ''), NULLIF(TRIM(nick_name), ''), CONCAT('用户_', old.id)) as full_name,
    NULL as department,
    NULLIF(TRIM(mobile), '') as phone,
    CASE 
        WHEN old.del_flag = 1 THEN 'INACTIVE'
        ELSE 'ACTIVE'
    END as status,
    COALESCE(old.create_time, NOW()) as created_at,
    COALESCE(old.update_time, NOW()) as updated_at
FROM old_t_wx_user old
WHERE old.id IS NOT NULL
ON DUPLICATE KEY UPDATE
    full_name = VALUES(full_name),
    phone = VALUES(phone),
    status = VALUES(status),
    updated_at = VALUES(updated_at);

-- 记录用户迁移结果
SELECT 'Step 1: 用户数据迁移完成' as step, 
       (SELECT COUNT(*) FROM t_users) as total_users_after_migration;

-- ========================================
-- 第二部分：PMI数据迁移 (old_t_zoom_pmi -> t_pmi_records)
-- ========================================

-- 首先需要创建用户ID映射表（临时表）
CREATE TEMPORARY TABLE temp_user_mapping AS
SELECT 
    old_user.id as old_user_id,
    new_user.id as new_user_id
FROM old_t_wx_user old_user
JOIN t_users new_user ON (
    new_user.username = COALESCE(NULLIF(TRIM(old_user.real_name), ''), NULLIF(TRIM(old_user.nick_name), ''), CONCAT('user_', old_user.id))
    OR new_user.email = COALESCE(NULLIF(TRIM(old_user.mobile), ''), CONCAT('user_', old_user.id, '@temp.com'))
);

-- 迁移PMI记录
INSERT INTO t_pmi_records (
    user_id,
    pmi_number,
    magic_id,
    pmi_password,
    host_url,
    join_url,
    status,
    billing_mode,
    total_minutes,
    available_minutes,
    billing_status,
    created_at,
    updated_at
)
SELECT
    COALESCE(mapping.new_user_id, 1) as user_id, -- 如果找不到用户映射，使用默认用户ID 1
    old_pmi.pmi as pmi_number,
    COALESCE(old_pmi.mg_id, old_pmi.pmi) as magic_id, -- 优先使用mg_id，如果为空则使用pmi
    COALESCE(old_pmi.pmi_password, '123456') as pmi_password,
    old_pmi.personal_meeting_url as host_url,
    old_pmi.personal_meeting_url as join_url,
    CASE
        WHEN old_pmi.del_flag = 1 THEN 'INACTIVE'
        ELSE 'ACTIVE'
    END as status,
    CASE
        WHEN old_pmi.now_plan_type = 'LONG' THEN 'LONG'
        ELSE 'BY_TIME'
    END as billing_mode,
    -- 计算总时长（小时*60 + 分钟）
    (COALESCE(old_pmi.durationh, 0) * 60 + COALESCE(old_pmi.durationm, 0)) as total_minutes,
    -- 可用时长 = 总时长 - 冻结时长
    (COALESCE(old_pmi.durationh, 0) * 60 + COALESCE(old_pmi.durationm, 0)
     - COALESCE(old_pmi.frozen_durationh, 0) * 60 - COALESCE(old_pmi.frozen_durationm, 0)
     - COALESCE(old_pmi.long_frozen_durationh, 0) * 60 - COALESCE(old_pmi.long_frozen_durationm, 0)) as available_minutes,
    CASE
        WHEN old_pmi.del_flag = 1 THEN 'SUSPENDED'
        WHEN old_pmi.plan_end_date_time IS NOT NULL
             AND TRIM(old_pmi.plan_end_date_time) != ''
             AND STR_TO_DATE(old_pmi.plan_end_date_time, '%Y-%m-%d %H:%i') < NOW() THEN 'EXPIRED'
        ELSE 'ACTIVE'
    END as billing_status,
    COALESCE(old_pmi.create_time, NOW()) as created_at,
    COALESCE(old_pmi.update_time, NOW()) as updated_at
FROM old_t_zoom_pmi old_pmi
LEFT JOIN temp_user_mapping mapping ON old_pmi.user_id = mapping.old_user_id
WHERE old_pmi.pmi IS NOT NULL AND old_pmi.pmi != ''
ON DUPLICATE KEY UPDATE
    magic_id = VALUES(magic_id),
    pmi_password = VALUES(pmi_password),
    host_url = VALUES(host_url),
    join_url = VALUES(join_url),
    status = VALUES(status),
    billing_mode = VALUES(billing_mode),
    total_minutes = VALUES(total_minutes),
    available_minutes = VALUES(available_minutes),
    billing_status = VALUES(billing_status),
    updated_at = VALUES(updated_at);

-- 记录PMI迁移结果
SELECT 'Step 2: PMI数据迁移完成' as step,
       (SELECT COUNT(*) FROM t_pmi_records) as total_pmi_after_migration,
       (SELECT COUNT(*) FROM t_pmi_records WHERE billing_mode = 'LONG') as long_billing_count;

-- ========================================
-- 第三部分：为LONG类型PMI自动补全计划和窗口记录
-- ========================================

-- 为每个LONG类型的PMI记录创建一个默认的长期计划
INSERT INTO t_pmi_schedules (
    pmi_record_id,
    name,
    start_date,
    end_date,
    start_time,
    duration_minutes,
    repeat_type,
    is_all_day,
    status,
    created_at,
    updated_at
)
SELECT 
    pmi.id as pmi_record_id,
    CONCAT('长期计划_', pmi.pmi_number) as name,
    CURDATE() as start_date,
    -- 从老系统的plan_end_date_time解析结束日期，修复空字符串问题
    CASE 
        WHEN old_pmi.plan_end_date_time IS NOT NULL 
             AND TRIM(old_pmi.plan_end_date_time) != ''
        THEN DATE(STR_TO_DATE(old_pmi.plan_end_date_time, '%Y-%m-%d %H:%i'))
        ELSE DATE_ADD(CURDATE(), INTERVAL 1 YEAR)
    END as end_date,
    '00:00:00' as start_time,
    1440 as duration_minutes, -- 24小时 = 1440分钟
    'DAILY' as repeat_type,
    TRUE as is_all_day,
    'ACTIVE' as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_records pmi
JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
WHERE pmi.billing_mode = 'LONG'
AND NOT EXISTS (
    SELECT 1 FROM t_pmi_schedules s WHERE s.pmi_record_id = pmi.id
);

-- 记录计划创建结果
SELECT 'Step 3: LONG类型PMI计划创建完成' as step,
       (SELECT COUNT(*) FROM t_pmi_schedules) as total_schedules;

-- ========================================
-- 第四部分：为LONG类型PMI创建窗口记录
-- ========================================

-- 为每个LONG类型的PMI计划创建当前有效的窗口记录
INSERT INTO t_pmi_schedule_windows (
    schedule_id,
    pmi_record_id,
    window_date,
    end_date,
    start_time,
    end_time,
    status,
    zoom_user_id,
    created_at,
    updated_at
)
SELECT 
    schedule.id as schedule_id,
    schedule.pmi_record_id,
    CURDATE() as window_date,
    -- 窗口结束日期使用计划的结束日期
    schedule.end_date as end_date,
    '00:00:00' as start_time,
    '23:59:59' as end_time,
    -- 根据计划结束时间判断窗口状态
    CASE 
        WHEN schedule.end_date < CURDATE() THEN 'COMPLETED'
        WHEN schedule.end_date = CURDATE() THEN 'ACTIVE'
        ELSE 'PENDING'
    END as status,
    NULL as zoom_user_id, -- 暂时不分配具体的zoom用户
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules schedule
JOIN t_pmi_records pmi ON schedule.pmi_record_id = pmi.id
WHERE pmi.billing_mode = 'LONG'
AND NOT EXISTS (
    SELECT 1 FROM t_pmi_schedule_windows w 
    WHERE w.schedule_id = schedule.id AND w.window_date = CURDATE()
);

-- 更新PMI记录的窗口相关字段
UPDATE t_pmi_records pmi
JOIN t_pmi_schedule_windows win ON pmi.id = win.pmi_record_id
JOIN t_pmi_schedules schedule ON win.schedule_id = schedule.id
SET
    pmi.current_window_id = win.id,
    pmi.window_expire_time = TIMESTAMP(win.end_date, win.end_time),
    pmi.active_window_ids = JSON_ARRAY(win.id)
WHERE pmi.billing_mode = 'LONG'
AND win.status IN ('PENDING', 'ACTIVE');

-- 记录窗口创建结果
SELECT 'Step 4: LONG类型PMI窗口创建完成' as step,
       (SELECT COUNT(*) FROM t_pmi_schedule_windows) as total_windows,
       (SELECT COUNT(*) FROM t_pmi_schedule_windows WHERE status = 'ACTIVE') as active_windows,
       (SELECT COUNT(*) FROM t_pmi_schedule_windows WHERE status = 'PENDING') as pending_windows;

-- 清理临时表
DROP TEMPORARY TABLE temp_user_mapping;

-- 提交事务
COMMIT;

-- ========================================
-- 迁移完成报告
-- ========================================
SELECT '=== 数据迁移完成报告 ===' as report;
SELECT 
    '用户迁移' as item,
    (SELECT COUNT(*) FROM old_t_wx_user) as source_count,
    (SELECT COUNT(*) FROM t_users) as target_count;
    
SELECT 
    'PMI迁移' as item,
    (SELECT COUNT(*) FROM old_t_zoom_pmi) as source_count,
    (SELECT COUNT(*) FROM t_pmi_records) as target_count;
    
SELECT 
    'LONG类型PMI' as item,
    (SELECT COUNT(*) FROM old_t_zoom_pmi WHERE now_plan_type = 'LONG') as source_count,
    (SELECT COUNT(*) FROM t_pmi_records WHERE billing_mode = 'LONG') as target_count;
    
SELECT 
    'PMI计划' as item,
    0 as source_count,
    (SELECT COUNT(*) FROM t_pmi_schedules) as target_count;

-- 显示需要手动处理的问题
SELECT '=== 需要注意的问题 ===' as attention;
SELECT 
    'PMI记录中找不到对应用户的数量' as issue,
    COUNT(*) as count
FROM t_pmi_records 
WHERE user_id = 1; -- 默认用户ID，表示映射失败

SELECT 
    'LONG类型PMI的到期时间分布' as info,
    DATE(STR_TO_DATE(old_pmi.plan_end_date_time, '%Y-%m-%d %H:%i')) as expire_date,
    COUNT(*) as count
FROM old_t_zoom_pmi old_pmi
WHERE old_pmi.now_plan_type = 'LONG'
AND old_pmi.plan_end_date_time IS NOT NULL
AND TRIM(old_pmi.plan_end_date_time) != ''
GROUP BY DATE(STR_TO_DATE(old_pmi.plan_end_date_time, '%Y-%m-%d %H:%i'))
ORDER BY expire_date;

SELECT 'Migration script completed successfully!' as final_message;
