-- 优化版数据迁移脚本
-- 从 old_data 目录导入老系统数据，然后迁移到新系统表结构
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第零部分：清理现有数据
-- ========================================

SELECT '=== 开始数据迁移流程 ===' as step;

-- 清理现有的迁移数据（保留外键约束顺序）
SELECT '清理现有数据...' as status;
DELETE FROM t_pmi_schedule_windows;
DELETE FROM t_pmi_schedules;
DELETE FROM t_pmi_records;
DELETE FROM t_users;

-- 重置自增ID
ALTER TABLE t_users AUTO_INCREMENT = 1;
ALTER TABLE t_pmi_records AUTO_INCREMENT = 1;
ALTER TABLE t_pmi_schedules AUTO_INCREMENT = 1;
ALTER TABLE t_pmi_schedule_windows AUTO_INCREMENT = 1;

SELECT '现有数据清理完成' as status;

-- ========================================
-- 第一部分：用户数据迁移 (old_t_wx_user -> t_users)
-- ========================================

SELECT '=== 开始用户数据迁移 ===' as step;

-- 检查老系统数据
SELECT 
    'Old System Data Check' as check_type,
    (SELECT COUNT(*) FROM old_t_wx_user) as old_users_count,
    (SELECT COUNT(*) FROM old_t_zoom_pmi) as old_pmi_count;

-- 迁移用户数据
INSERT INTO t_users (
    username,
    email, 
    full_name,
    department,
    phone,
    status,
    created_at,
    updated_at
)
SELECT 
    COALESCE(NULLIF(TRIM(real_name), ''), NULLIF(TRIM(nick_name), ''), CONCAT('user_', old.id)) as username,
    COALESCE(NULLIF(TRIM(mobile), ''), CONCAT('user_', old.id, '@temp.com')) as email,
    COALESCE(NULLIF(TRIM(real_name), ''), NULLIF(TRIM(nick_name), ''), CONCAT('用户_', old.id)) as full_name,
    NULL as department,
    NULLIF(TRIM(mobile), '') as phone,
    CASE 
        WHEN old.del_flag = 1 THEN 'INACTIVE'
        ELSE 'ACTIVE'
    END as status,
    COALESCE(old.create_time, NOW()) as created_at,
    COALESCE(old.update_time, NOW()) as updated_at
FROM old_t_wx_user old
WHERE old.id IN (SELECT DISTINCT user_id FROM old_t_zoom_pmi WHERE user_id IS NOT NULL);

-- 创建用户ID映射表
CREATE TEMPORARY TABLE temp_user_mapping (
    old_user_id VARCHAR(255),
    new_user_id BIGINT,
    INDEX idx_old_id (old_user_id),
    INDEX idx_new_id (new_user_id)
);

-- 填充用户映射关系
INSERT INTO temp_user_mapping (old_user_id, new_user_id)
SELECT 
    old.id as old_user_id,
    new.id as new_user_id
FROM old_t_wx_user old
JOIN t_users new ON (
    new.email = COALESCE(NULLIF(TRIM(old.mobile), ''), CONCAT('user_', old.id, '@temp.com'))
    OR new.username = COALESCE(NULLIF(TRIM(old.real_name), ''), NULLIF(TRIM(old.nick_name), ''), CONCAT('user_', old.id))
);

SELECT 
    'User Migration Result' as result_type,
    COUNT(*) as migrated_users
FROM t_users;

SELECT 
    'User Mapping Result' as result_type,
    COUNT(*) as mapped_users
FROM temp_user_mapping;

-- ========================================
-- 第二部分：PMI记录迁移 (old_t_zoom_pmi -> t_pmi_records)
-- ========================================

SELECT '=== 开始PMI记录迁移 ===' as step;

-- 迁移PMI记录
INSERT INTO t_pmi_records (
    user_id,
    pmi_number,
    pmi_password,
    host_url,
    join_url,
    status,
    billing_mode,
    total_minutes,
    available_minutes,
    pending_deduct_minutes,
    overdraft_minutes,
    total_used_minutes,
    billing_status,
    created_at,
    updated_at
)
SELECT 
    COALESCE(um.new_user_id, 1) as user_id,  -- 默认分配给ID=1的用户
    old.pmi as pmi_number,
    COALESCE(old.pmi_password, '123456') as pmi_password,
    old.personal_meeting_url as host_url,
    old.personal_meeting_url as join_url,
    CASE 
        WHEN old.del_flag = 1 THEN 'INACTIVE'
        ELSE 'ACTIVE'
    END as status,
    CASE 
        WHEN old.now_plan_type = 'LONG' THEN 'LONG'
        ELSE 'BY_TIME'
    END as billing_mode,
    -- 计算总分钟数
    (COALESCE(old.durationh, 0) * 60 + COALESCE(old.durationm, 0)) as total_minutes,
    -- 可用分钟数 = 总分钟数 - 冻结分钟数
    GREATEST(0, (COALESCE(old.durationh, 0) * 60 + COALESCE(old.durationm, 0)) - 
                (COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0))) as available_minutes,
    0 as pending_deduct_minutes,
    -- 透支分钟数（如果冻结时间为负数）
    CASE 
        WHEN (COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0)) < 0 
        THEN ABS(COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0))
        ELSE 0
    END as overdraft_minutes,
    -- 已使用分钟数 = 冻结的正数部分
    CASE 
        WHEN (COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0)) > 0 
        THEN (COALESCE(old.frozen_durationh, 0) * 60 + COALESCE(old.frozen_durationm, 0))
        ELSE 0
    END as total_used_minutes,
    'NORMAL' as billing_status,
    COALESCE(old.create_time, NOW()) as created_at,
    COALESCE(old.update_time, NOW()) as updated_at
FROM old_t_zoom_pmi old
LEFT JOIN temp_user_mapping um ON old.user_id = um.old_user_id
WHERE old.pmi IS NOT NULL AND old.pmi != '';

SELECT 
    'PMI Migration Result' as result_type,
    COUNT(*) as migrated_pmi_records
FROM t_pmi_records;

-- ========================================
-- 第三部分：创建长租PMI的计划和窗口
-- ========================================

SELECT '=== 开始创建长租PMI计划和窗口 ===' as step;

-- 为LONG类型的PMI创建计划
INSERT INTO t_pmi_schedules (
    pmi_record_id,
    name,
    start_date,
    end_date,
    start_time,
    duration_minutes,
    repeat_type,
    status,
    is_all_day,
    created_at,
    updated_at
)
SELECT 
    pr.id as pmi_record_id,
    CONCAT('长租计划_', pr.pmi_number) as name,
    CURDATE() as start_date,
    CASE 
        WHEN old.plan_end_date_time IS NOT NULL AND old.plan_end_date_time != '' 
        THEN STR_TO_DATE(old.plan_end_date_time, '%Y-%m-%d %H:%i:%s')
        ELSE DATE_ADD(CURDATE(), INTERVAL 1 YEAR)
    END as end_date,
    '00:00:00' as start_time,
    1440 as duration_minutes,  -- 24小时 = 1440分钟
    'ONCE' as repeat_type,
    'ACTIVE' as status,
    '1' as is_all_day,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_records pr
JOIN old_t_zoom_pmi old ON pr.pmi_number = old.pmi
LEFT JOIN temp_user_mapping um ON old.user_id = um.old_user_id
WHERE pr.billing_mode = 'LONG';

-- 为每个长租计划创建对应的窗口
INSERT INTO t_pmi_schedule_windows (
    schedule_id,
    pmi_record_id,
    window_date,
    end_date,
    start_time,
    end_time,
    status,
    created_at,
    updated_at
)
SELECT 
    ps.id as schedule_id,
    ps.pmi_record_id,
    ps.start_date as window_date,
    ps.end_date as end_date,
    ps.start_time,
    '23:59:59' as end_time,
    'ACTIVE' as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules ps
WHERE ps.repeat_type = 'ONCE';

SELECT 
    'Schedule Creation Result' as result_type,
    COUNT(*) as created_schedules
FROM t_pmi_schedules;

SELECT 
    'Window Creation Result' as result_type,
    COUNT(*) as created_windows
FROM t_pmi_schedule_windows;

-- ========================================
-- 第四部分：更新PMI记录的窗口信息
-- ========================================

SELECT '=== 更新PMI记录窗口信息 ===' as step;

-- 更新LONG类型PMI的窗口相关字段
UPDATE t_pmi_records pr
JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
SET 
    pr.billing_mode = 'LONG',
    pr.current_window_id = psw.id,
    pr.window_expire_time = TIMESTAMP(psw.end_date, psw.end_time),
    pr.active_window_ids = JSON_ARRAY(psw.id)
WHERE psw.status = 'ACTIVE';

-- 清理临时表
DROP TEMPORARY TABLE temp_user_mapping;

-- 提交事务
COMMIT;

-- ========================================
-- 第五部分：数据验证
-- ========================================

SELECT '=== 数据迁移验证 ===' as step;

-- 验证迁移结果
SELECT 
    'Final Migration Summary' as summary_type,
    (SELECT COUNT(*) FROM t_users) as total_users,
    (SELECT COUNT(*) FROM t_pmi_records) as total_pmi_records,
    (SELECT COUNT(*) FROM t_pmi_schedules) as total_schedules,
    (SELECT COUNT(*) FROM t_pmi_schedule_windows) as total_windows;

-- 验证计费模式分布
SELECT 
    'Billing Mode Distribution' as check_type,
    billing_mode,
    COUNT(*) as count
FROM t_pmi_records 
GROUP BY billing_mode;

-- 验证长租PMI的完整性
SELECT 
    'LONG PMI Completeness' as check_type,
    COUNT(*) as long_pmi_count,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_window_id,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_expire_time
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

SELECT '=== 数据迁移完成 ===' as final_status;
