# ZoomMeetingDetail 字段迁移说明

## 📋 概述

本次迁移将 `t_zoom_meeting_details` 表中的会议级别字段移除，这些字段已经迁移到 `t_meetings` 表中。这样可以避免数据重复，确保数据一致性。

## 🎯 迁移目标

### 问题背景
- `t_zoom_meeting_details` 表代表会议的一个 Occurrence（单场会议）
- 该表中包含了一些会议级别的字段，这些字段应该只在 `t_meetings` 表中保存
- 数据重复导致维护困难和一致性问题

### 解决方案
- 将会议级别字段从 `t_zoom_meeting_details` 表中移除
- 修改相关代码，从 `t_meetings` 表获取这些信息
- 保持 `t_zoom_meeting_details` 表只存储单场级别的信息

## 📊 字段分类

### 已移除的会议级别字段
| 字段名 | 说明 | 迁移到 |
|--------|------|--------|
| `topic` | 会议主题 | `t_meetings.topic` |
| `agenda` | 会议议程 | `t_meetings.agenda` |
| `type` | 会议类型 | `t_meetings.type` |
| `timezone` | 时区 | `t_meetings.timezone` |
| `duration` | 持续时间 | `t_meetings.duration_minutes` |
| `password` | 会议密码 | `t_meetings.meeting_password` |
| `recurrence` | 周期性设置 | `t_meetings` 的周期性字段 |

### 保留的单场级别字段
| 字段名 | 说明 | 原因 |
|--------|------|------|
| `uuid` | 每场会议的唯一标识 | 每场不同 |
| `start_time` | 实际开始时间 | 可能与计划不同 |
| `status` | 单场会议状态 | 每场状态可能不同 |
| `join_url` | 加入链接 | 可能包含特定参数 |
| `start_url` | 主持人开始链接 | 每场不同 |
| `host_id` | 实际主持人ID | 可能有替代主持人 |
| `host_email` | 实际主持人邮箱 | 可能有替代主持人 |
| `occurrence_id` | 周期性会议的场次ID | 每场不同 |
| `occurrence_start_time` | 场次开始时间 | 每场不同 |
| `occurrence_status` | 场次状态 | 每场不同 |
| `settings` | 会议设置 | 可能每场不同 |
| `created_at` | Zoom创建时间 | 每场不同 |
| `pmi` | 个人会议室ID | 单场相关 |
| `tracking_fields` | 跟踪字段 | 单场相关 |
| `encrypted` | 是否加密 | 单场设置 |
| `waiting_room` | 等候室设置 | 单场设置 |
| `mute_upon_entry` | 入会时静音 | 单场设置 |
| `join_before_host` | 允许主持人之前加入 | 单场设置 |
| `auto_recording` | 自动录制 | 单场设置 |
| `alternative_hosts` | 替代主持人 | 单场设置 |

## 🔧 代码修改

### 1. 实体类修改
- `ZoomMeetingDetail.java`: 将重复字段标记为 `@Deprecated`
- 添加注释说明字段已迁移

### 2. Service 层修改
- `ZoomMeetingDetailService.java`: 移除对已废弃字段的设置
- `MeetingService.java`: 移除 webhook 处理中对已废弃字段的更新

### 3. Repository 层修改
- `ZoomMeetingDetailRepository.java`: 
  - 修改查询以包含 Meeting 表信息
  - 将基于已废弃字段的查询方法标记为 `@Deprecated`

### 4. DTO 修改
- `MeetingDetailWithTime.java`: 修改构造方法从 Meeting 表获取已迁移字段

## 🧪 测试验证

### API 测试
- ✅ `/api/meetings/recent-week-details` - 最近一周会议详情
- ✅ 编译通过
- ✅ 应用启动成功

### 数据一致性检查
```sql
-- 检查数据一致性
SELECT COUNT(*) as inconsistent_records
FROM t_zoom_meeting_details zmd
LEFT JOIN t_meetings m ON zmd.meeting_id = m.id
WHERE (
    (zmd.topic IS NOT NULL AND zmd.topic != m.topic) OR
    (zmd.password IS NOT NULL AND zmd.password != m.meeting_password)
);
```

## 📝 执行步骤

### 1. 代码部署
- ✅ 完成所有代码修改
- ✅ 测试API功能正常
- ✅ 部署到生产环境

### 2. 数据库迁移
```bash
# 执行迁移脚本
mysql -u root -p zoombusV < database/migrations/remove_duplicate_fields_from_zoom_meeting_details.sql
```

### 3. 验证
- 检查表结构
- 测试API功能
- 验证数据完整性

## ⚠️ 注意事项

### 执行前检查
1. ✅ 确保代码已经修改并测试通过
2. ✅ 确保API功能正常
3. ⚠️ 备份数据库（强烈建议）
4. ⚠️ 在非生产环境先测试

### 风险控制
- 脚本会自动创建备份表
- 使用事务确保原子性
- 可以通过备份表回滚

### 回滚方案
如果需要回滚，可以从备份表恢复：
```sql
-- 恢复字段（示例）
ALTER TABLE t_zoom_meeting_details ADD COLUMN topic VARCHAR(255);
UPDATE t_zoom_meeting_details zmd 
SET topic = (SELECT topic FROM t_zoom_meeting_details_backup_YYYYMMDD_HHMMSS b WHERE b.id = zmd.id);
```

## 📈 预期效果

### 数据一致性
- ✅ 消除重复数据
- ✅ 单一数据源
- ✅ 减少维护复杂度

### 性能优化
- ✅ 减少表大小
- ✅ 简化查询逻辑
- ✅ 提高数据完整性

### 代码质量
- ✅ 清晰的职责分离
- ✅ 减少代码重复
- ✅ 提高可维护性
