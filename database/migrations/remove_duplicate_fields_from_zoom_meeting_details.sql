-- 移除 t_zoom_meeting_details 表中与 t_meetings 表重复的会议级别字段
-- 这些字段应该只在 t_meetings 表中保存，t_zoom_meeting_details 表只保存单场级别的字段
--
-- 执行前请确保：
-- 1. 已经完成代码修改，不再使用这些字段
-- 2. 已经测试过API功能正常
-- 3. 已经备份数据库

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- 1. 创建备份表（强烈建议）
CREATE TABLE t_zoom_meeting_details_backup_$(date +%Y%m%d_%H%M%S) AS
SELECT * FROM t_zoom_meeting_details;

-- 2. 检查当前表中的重复字段数据情况
SELECT
    '=== 字段使用情况统计 ===' as info,
    COUNT(*) as total_records,
    COUNT(CASE WHEN topic IS NOT NULL AND topic != '' THEN 1 END) as records_with_topic,
    COUNT(CASE WHEN agenda IS NOT NULL AND agenda != '' THEN 1 END) as records_with_agenda,
    COUNT(CASE WHEN type IS NOT NULL THEN 1 END) as records_with_type,
    COUNT(CASE WHEN timezone IS NOT NULL AND timezone != '' THEN 1 END) as records_with_timezone,
    COUNT(CASE WHEN duration IS NOT NULL THEN 1 END) as records_with_duration,
    COUNT(CASE WHEN password IS NOT NULL AND password != '' THEN 1 END) as records_with_password,
    COUNT(CASE WHEN recurrence IS NOT NULL AND recurrence != '' THEN 1 END) as records_with_recurrence
FROM t_zoom_meeting_details;

-- 3. 显示即将删除的字段中有数据的记录数量
SELECT 
    'topic' as field_name,
    COUNT(*) as records_with_data
FROM t_zoom_meeting_details 
WHERE topic IS NOT NULL AND topic != ''

UNION ALL

SELECT 
    'agenda' as field_name,
    COUNT(*) as records_with_data
FROM t_zoom_meeting_details 
WHERE agenda IS NOT NULL AND agenda != ''

UNION ALL

SELECT 
    'type' as field_name,
    COUNT(*) as records_with_data
FROM t_zoom_meeting_details 
WHERE type IS NOT NULL

UNION ALL

SELECT 
    'timezone' as field_name,
    COUNT(*) as records_with_data
FROM t_zoom_meeting_details 
WHERE timezone IS NOT NULL AND timezone != ''

UNION ALL

SELECT 
    'duration' as field_name,
    COUNT(*) as records_with_data
FROM t_zoom_meeting_details 
WHERE duration IS NOT NULL

UNION ALL

SELECT 
    'password' as field_name,
    COUNT(*) as records_with_data
FROM t_zoom_meeting_details 
WHERE password IS NOT NULL AND password != ''

UNION ALL

SELECT 
    'recurrence' as field_name,
    COUNT(*) as records_with_data
FROM t_zoom_meeting_details 
WHERE recurrence IS NOT NULL AND recurrence != '';

-- 4. 检查数据一致性（可选）
SELECT
    '=== 数据一致性检查 ===' as info,
    COUNT(*) as total_inconsistent_records
FROM t_zoom_meeting_details zmd
LEFT JOIN t_meetings m ON zmd.meeting_id = m.id
WHERE (
    (zmd.topic IS NOT NULL AND zmd.topic != '' AND zmd.topic != m.topic) OR
    (zmd.password IS NOT NULL AND zmd.password != '' AND zmd.password != m.meeting_password) OR
    (zmd.duration IS NOT NULL AND zmd.duration != m.duration_minutes)
);

-- 5. 删除重复的会议级别字段
-- 注意：这些操作是不可逆的，请确保已经备份数据并完成代码修改

-- 删除 topic 字段（会议主题 - 已在 t_meetings.topic 中）
ALTER TABLE t_zoom_meeting_details DROP COLUMN topic;

-- 删除 agenda 字段（会议议程 - 已在 t_meetings.agenda 中）
ALTER TABLE t_zoom_meeting_details DROP COLUMN agenda;

-- 删除 type 字段（会议类型 - 已在 t_meetings.type 中）
ALTER TABLE t_zoom_meeting_details DROP COLUMN type;

-- 删除 timezone 字段（时区 - 已在 t_meetings.timezone 中）
ALTER TABLE t_zoom_meeting_details DROP COLUMN timezone;

-- 删除 duration 字段（持续时间 - 已在 t_meetings.duration_minutes 中）
ALTER TABLE t_zoom_meeting_details DROP COLUMN duration;

-- 删除 password 字段（会议密码 - 已在 t_meetings.meeting_password 中）
ALTER TABLE t_zoom_meeting_details DROP COLUMN password;

-- 删除 recurrence 字段（周期性设置 - 已在 t_meetings 的多个字段中）
ALTER TABLE t_zoom_meeting_details DROP COLUMN recurrence;

-- 6. 验证删除结果
SELECT '=== 删除后表结构 ===' as info;
DESCRIBE t_zoom_meeting_details;

-- 7. 显示剩余的字段（应该都是单场级别的字段）
SELECT
    '=== 剩余字段列表 ===' as info,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM information_schema.COLUMNS
WHERE TABLE_NAME = 't_zoom_meeting_details'
AND TABLE_SCHEMA = 'zoombusV'
ORDER BY ORDINAL_POSITION;

-- 8. 验证API功能（建议在执行此脚本前先测试）
SELECT
    '=== 重要提醒 ===' as info,
    '请确保在执行此脚本前已经测试过API功能正常' as reminder,
    '特别是 /api/meetings/recent-week-details 接口' as api_endpoint;

-- 提交事务
COMMIT;

-- 显示完成信息
SELECT
    '✅ 字段清理完成' as status,
    '已移除会议级别的重复字段' as message,
    'topic, agenda, type, timezone, duration, password, recurrence' as removed_fields,
    '数据现在只在 t_meetings 表中维护' as note;
