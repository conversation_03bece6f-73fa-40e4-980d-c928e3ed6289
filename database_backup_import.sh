#!/bin/bash

# 数据库备份和导入脚本
# 功能：从远程生产服务器备份 zoombusV 数据库并导入到本地

set -e  # 遇到错误立即退出

# 配置变量
REMOTE_HOST="nslcp.com"
REMOTE_USER="root"
REMOTE_DB_HOST="localhost"
REMOTE_DB_USER="root"
REMOTE_DB_PASSWORD="nvshen2018"
REMOTE_DB_NAME="zoombusV"

LOCAL_DB_HOST="localhost"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="nvshen2018"
LOCAL_DB_NAME="zoombusV"

# 备份文件名（包含时间戳）
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="zoombusV_backup_${TIMESTAMP}.sql"
LOCAL_BACKUP_PATH="/tmp/${BACKUP_FILE}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查必要的工具..."
    
    if ! command -v ssh &> /dev/null; then
        log_error "ssh 命令未找到，请安装 OpenSSH 客户端"
        exit 1
    fi
    
    if ! command -v mysql &> /dev/null; then
        log_error "mysql 命令未找到，请安装 MySQL 客户端"
        exit 1
    fi
    
    if ! command -v mysqldump &> /dev/null; then
        log_error "mysqldump 命令未找到，请安装 MySQL 客户端工具"
        exit 1
    fi
    
    log_success "所有必要工具检查完成"
}

# 测试远程连接
test_remote_connection() {
    log_info "测试远程服务器连接..."
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes ${REMOTE_USER}@${REMOTE_HOST} "echo 'Connection test successful'" 2>/dev/null; then
        log_success "远程服务器连接正常"
    else
        log_error "无法连接到远程服务器 ${REMOTE_HOST}"
        log_info "请确保："
        log_info "1. SSH 密钥已配置或可以使用密码登录"
        log_info "2. 远程服务器地址正确"
        log_info "3. 网络连接正常"
        exit 1
    fi
}

# 测试远程数据库连接
test_remote_database() {
    log_info "测试远程数据库连接..."
    
    REMOTE_DB_TEST=$(ssh ${REMOTE_USER}@${REMOTE_HOST} "mysql -h${REMOTE_DB_HOST} -u${REMOTE_DB_USER} -p${REMOTE_DB_PASSWORD} -e 'SELECT 1' 2>/dev/null && echo 'OK' || echo 'FAILED'")
    
    if [[ "$REMOTE_DB_TEST" == *"OK"* ]]; then
        log_success "远程数据库连接正常"
    else
        log_error "无法连接到远程数据库"
        log_info "请检查远程数据库配置：主机=${REMOTE_DB_HOST}, 用户=${REMOTE_DB_USER}"
        exit 1
    fi
}

# 测试本地数据库连接
test_local_database() {
    log_info "测试本地数据库连接..."
    
    if mysql -h${LOCAL_DB_HOST} -u${LOCAL_DB_USER} -p${LOCAL_DB_PASSWORD} -e "SELECT 1" &>/dev/null; then
        log_success "本地数据库连接正常"
    else
        log_error "无法连接到本地数据库"
        log_info "请检查本地数据库配置：主机=${LOCAL_DB_HOST}, 用户=${LOCAL_DB_USER}"
        exit 1
    fi
}

# 备份远程数据库
backup_remote_database() {
    log_info "开始备份远程数据库 ${REMOTE_DB_NAME}..."
    
    # 在远程服务器上执行备份
    ssh ${REMOTE_USER}@${REMOTE_HOST} "mysqldump -h${REMOTE_DB_HOST} -u${REMOTE_DB_USER} -p${REMOTE_DB_PASSWORD} \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        ${REMOTE_DB_NAME}" > ${LOCAL_BACKUP_PATH}
    
    if [ $? -eq 0 ] && [ -s ${LOCAL_BACKUP_PATH} ]; then
        BACKUP_SIZE=$(du -h ${LOCAL_BACKUP_PATH} | cut -f1)
        log_success "远程数据库备份完成，文件大小：${BACKUP_SIZE}"
        log_info "备份文件保存在：${LOCAL_BACKUP_PATH}"
    else
        log_error "远程数据库备份失败"
        exit 1
    fi
}

# 确认导入操作
confirm_import() {
    log_warning "即将导入数据到本地数据库 ${LOCAL_DB_NAME}"
    log_warning "这将覆盖本地数据库的所有数据！"
    
    read -p "确认继续吗？(yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 导入到本地数据库
import_to_local_database() {
    log_info "开始导入数据到本地数据库..."
    
    # 创建数据库（如果不存在）
    mysql -h${LOCAL_DB_HOST} -u${LOCAL_DB_USER} -p${LOCAL_DB_PASSWORD} \
        -e "CREATE DATABASE IF NOT EXISTS ${LOCAL_DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    # 导入数据
    mysql -h${LOCAL_DB_HOST} -u${LOCAL_DB_USER} -p${LOCAL_DB_PASSWORD} ${LOCAL_DB_NAME} < ${LOCAL_BACKUP_PATH}
    
    if [ $? -eq 0 ]; then
        log_success "数据导入完成"
    else
        log_error "数据导入失败"
        exit 1
    fi
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    if [ -f ${LOCAL_BACKUP_PATH} ]; then
        rm -f ${LOCAL_BACKUP_PATH}
        log_success "临时备份文件已删除"
    fi
}

# 显示导入结果
show_import_result() {
    log_info "检查导入结果..."
    
    TABLE_COUNT=$(mysql -h${LOCAL_DB_HOST} -u${LOCAL_DB_USER} -p${LOCAL_DB_PASSWORD} ${LOCAL_DB_NAME} \
        -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='${LOCAL_DB_NAME}'" -s -N)
    
    log_success "导入完成！本地数据库 ${LOCAL_DB_NAME} 现在包含 ${TABLE_COUNT} 个表"
}

# 主函数
main() {
    log_info "开始数据库备份和导入流程..."
    log_info "远程服务器: ${REMOTE_USER}@${REMOTE_HOST}"
    log_info "远程数据库: ${REMOTE_DB_NAME}"
    log_info "本地数据库: ${LOCAL_DB_NAME}"
    echo
    
    # 执行各个步骤
    check_dependencies
    test_remote_connection
    test_remote_database
    test_local_database
    backup_remote_database
    confirm_import
    import_to_local_database
    show_import_result
    cleanup
    
    log_success "所有操作完成！"
}

# 错误处理
trap 'log_error "脚本执行过程中发生错误，正在清理..."; cleanup; exit 1' ERR

# 执行主函数
main "$@"
