#!/bin/bash

# 安全的数据库备份和导入脚本
# 功能：从远程生产服务器备份 zoombusV 数据库并导入到本地
# 使用 MySQL 配置文件来避免密码暴露

set -e  # 遇到错误立即退出

# 配置变量
REMOTE_HOST="nslcp.com"
REMOTE_USER="root"
REMOTE_DB_HOST="localhost"
REMOTE_DB_NAME="zoombusV"

LOCAL_DB_HOST="localhost"
LOCAL_DB_NAME="zoombusV"

# 备份文件名（包含时间戳）
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="zoombusV_backup_${TIMESTAMP}.sql"
LOCAL_BACKUP_PATH="/tmp/${BACKUP_FILE}"

# MySQL 配置文件路径
REMOTE_MYSQL_CONFIG="/tmp/.my_remote.cnf"
LOCAL_MYSQL_CONFIG="/tmp/.my_local.cnf"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建 MySQL 配置文件
create_mysql_configs() {
    log_info "创建 MySQL 配置文件..."
    
    # 远程数据库配置文件
    cat > ${REMOTE_MYSQL_CONFIG} << EOF
[client]
host=localhost
user=root
password=nvshen2018
default-character-set=utf8mb4
EOF
    
    # 本地数据库配置文件
    cat > ${LOCAL_MYSQL_CONFIG} << EOF
[client]
host=localhost
user=root
password=nvshen2018
default-character-set=utf8mb4
EOF
    
    # 设置文件权限（只有当前用户可读）
    chmod 600 ${REMOTE_MYSQL_CONFIG}
    chmod 600 ${LOCAL_MYSQL_CONFIG}
    
    log_success "MySQL 配置文件创建完成"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查必要的工具..."
    
    if ! command -v ssh &> /dev/null; then
        log_error "ssh 命令未找到，请安装 OpenSSH 客户端"
        exit 1
    fi
    
    if ! command -v mysql &> /dev/null; then
        log_error "mysql 命令未找到，请安装 MySQL 客户端"
        exit 1
    fi
    
    if ! command -v mysqldump &> /dev/null; then
        log_error "mysqldump 命令未找到，请安装 MySQL 客户端工具"
        exit 1
    fi
    
    log_success "所有必要工具检查完成"
}

# 测试远程连接
test_remote_connection() {
    log_info "测试远程服务器连接..."
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes ${REMOTE_USER}@${REMOTE_HOST} "echo 'Connection test successful'" 2>/dev/null; then
        log_success "远程服务器连接正常"
    else
        log_warning "无法使用密钥连接到远程服务器，将尝试密码连接"
        log_info "请确保可以通过 SSH 连接到 ${REMOTE_USER}@${REMOTE_HOST}"
    fi
}

# 测试远程数据库连接
test_remote_database() {
    log_info "测试远程数据库连接..."
    
    # 将配置文件复制到远程服务器
    scp ${REMOTE_MYSQL_CONFIG} ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_MYSQL_CONFIG}
    
    REMOTE_DB_TEST=$(ssh ${REMOTE_USER}@${REMOTE_HOST} "mysql --defaults-file=${REMOTE_MYSQL_CONFIG} -e 'SELECT 1' 2>/dev/null && echo 'OK' || echo 'FAILED'")
    
    if [[ "$REMOTE_DB_TEST" == *"OK"* ]]; then
        log_success "远程数据库连接正常"
    else
        log_error "无法连接到远程数据库"
        log_info "请检查远程数据库配置和密码"
        exit 1
    fi
}

# 测试本地数据库连接
test_local_database() {
    log_info "测试本地数据库连接..."
    
    if mysql --defaults-file=${LOCAL_MYSQL_CONFIG} -e "SELECT 1" &>/dev/null; then
        log_success "本地数据库连接正常"
    else
        log_error "无法连接到本地数据库"
        log_info "请检查本地数据库配置和密码"
        exit 1
    fi
}

# 备份远程数据库
backup_remote_database() {
    log_info "开始备份远程数据库 ${REMOTE_DB_NAME}..."
    
    # 在远程服务器上执行备份
    ssh ${REMOTE_USER}@${REMOTE_HOST} "mysqldump --defaults-file=${REMOTE_MYSQL_CONFIG} \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --add-drop-database \
        --databases ${REMOTE_DB_NAME}" > ${LOCAL_BACKUP_PATH}
    
    if [ $? -eq 0 ] && [ -s ${LOCAL_BACKUP_PATH} ]; then
        BACKUP_SIZE=$(du -h ${LOCAL_BACKUP_PATH} | cut -f1)
        log_success "远程数据库备份完成，文件大小：${BACKUP_SIZE}"
        log_info "备份文件保存在：${LOCAL_BACKUP_PATH}"
    else
        log_error "远程数据库备份失败"
        exit 1
    fi
}

# 确认导入操作
confirm_import() {
    log_warning "即将导入数据到本地数据库 ${LOCAL_DB_NAME}"
    log_warning "这将覆盖本地数据库的所有数据！"
    
    read -p "确认继续吗？(yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 导入到本地数据库
import_to_local_database() {
    log_info "开始导入数据到本地数据库..."
    
    # 导入数据（包含数据库创建）
    mysql --defaults-file=${LOCAL_MYSQL_CONFIG} < ${LOCAL_BACKUP_PATH}
    
    if [ $? -eq 0 ]; then
        log_success "数据导入完成"
    else
        log_error "数据导入失败"
        exit 1
    fi
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    # 清理本地临时文件
    [ -f ${LOCAL_BACKUP_PATH} ] && rm -f ${LOCAL_BACKUP_PATH}
    [ -f ${REMOTE_MYSQL_CONFIG} ] && rm -f ${REMOTE_MYSQL_CONFIG}
    [ -f ${LOCAL_MYSQL_CONFIG} ] && rm -f ${LOCAL_MYSQL_CONFIG}
    
    # 清理远程临时文件
    ssh ${REMOTE_USER}@${REMOTE_HOST} "rm -f ${REMOTE_MYSQL_CONFIG}" 2>/dev/null || true
    
    log_success "临时文件清理完成"
}

# 显示导入结果
show_import_result() {
    log_info "检查导入结果..."
    
    TABLE_COUNT=$(mysql --defaults-file=${LOCAL_MYSQL_CONFIG} ${LOCAL_DB_NAME} \
        -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='${LOCAL_DB_NAME}'" -s -N)
    
    log_success "导入完成！本地数据库 ${LOCAL_DB_NAME} 现在包含 ${TABLE_COUNT} 个表"
}

# 主函数
main() {
    log_info "开始数据库备份和导入流程..."
    log_info "远程服务器: ${REMOTE_USER}@${REMOTE_HOST}"
    log_info "远程数据库: ${REMOTE_DB_NAME}"
    log_info "本地数据库: ${LOCAL_DB_NAME}"
    echo
    
    # 执行各个步骤
    check_dependencies
    create_mysql_configs
    test_remote_connection
    test_remote_database
    test_local_database
    backup_remote_database
    confirm_import
    import_to_local_database
    show_import_result
    cleanup
    
    log_success "所有操作完成！"
}

# 错误处理
trap 'log_error "脚本执行过程中发生错误，正在清理..."; cleanup; exit 1' ERR

# 执行主函数
main "$@"
