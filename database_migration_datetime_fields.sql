-- PMI窗口表时间字段重构迁移脚本
-- 将 window_date/start_time/end_date/end_time 重构为 start_date_time/end_date_time
-- 这将大大简化窗口关闭逻辑，避免复杂的跨日判断

USE zoombusV;

-- ========================================
-- 第一步：添加新的DATETIME字段
-- ========================================
SELECT '=== 第一步：添加新的DATETIME字段 ===' as step;

ALTER TABLE t_pmi_schedule_windows 
ADD COLUMN start_date_time DATETIME NULL COMMENT '窗口开始时间（精确到秒）',
ADD COLUMN end_date_time DATETIME NULL COMMENT '窗口结束时间（精确到秒）';

-- 验证字段添加
DESCRIBE t_pmi_schedule_windows;

-- ========================================
-- 第二步：从现有字段计算并填充新字段
-- ========================================
SELECT '=== 第二步：计算并填充新字段 ===' as step;

-- 更新所有记录的新时间字段
UPDATE t_pmi_schedule_windows 
SET 
    start_date_time = TIMESTAMP(window_date, start_time),
    end_date_time = TIMESTAMP(end_date, end_time),
    updated_at = NOW()
WHERE start_date_time IS NULL OR end_date_time IS NULL;

-- 显示更新结果
SELECT 
    'Updated Records' as metric,
    ROW_COUNT() as count;

-- ========================================
-- 第三步：验证数据迁移正确性
-- ========================================
SELECT '=== 第三步：验证数据迁移正确性 ===' as step;

-- 检查所有记录是否都有新字段值
SELECT 
    'Total Records' as check_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows

UNION ALL

SELECT 
    'Records with start_date_time' as check_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE start_date_time IS NOT NULL

UNION ALL

SELECT 
    'Records with end_date_time' as check_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE end_date_time IS NOT NULL

UNION ALL

SELECT 
    'Records with both new fields' as check_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE start_date_time IS NOT NULL AND end_date_time IS NOT NULL;

-- ========================================
-- 第四步：验证时间计算的正确性
-- ========================================
SELECT '=== 第四步：验证时间计算正确性 ===' as step;

-- 显示一些示例记录，对比新旧字段
SELECT 
    id,
    window_date,
    start_time,
    end_date,
    end_time,
    start_date_time,
    end_date_time,
    -- 验证计算是否正确
    CASE 
        WHEN start_date_time = TIMESTAMP(window_date, start_time) THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as start_time_check,
    CASE 
        WHEN end_date_time = TIMESTAMP(end_date, end_time) THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as end_time_check,
    -- 计算窗口持续时间
    TIMESTAMPDIFF(MINUTE, start_date_time, end_date_time) as duration_minutes,
    -- 检查是否为跨日窗口
    CASE 
        WHEN DATE(start_date_time) != DATE(end_date_time) THEN 'CROSS_DAY'
        ELSE 'SAME_DAY'
    END as window_type
FROM t_pmi_schedule_windows 
ORDER BY id DESC
LIMIT 10;

-- ========================================
-- 第五步：验证特殊情况（跨日窗口）
-- ========================================
SELECT '=== 第五步：验证跨日窗口 ===' as step;

SELECT 
    id,
    window_date,
    start_time,
    end_date,
    end_time,
    start_date_time,
    end_date_time,
    TIMESTAMPDIFF(MINUTE, start_date_time, end_date_time) as duration_minutes,
    status
FROM t_pmi_schedule_windows 
WHERE DATE(start_date_time) != DATE(end_date_time)
ORDER BY id DESC
LIMIT 5;

-- ========================================
-- 第六步：验证窗口1036的修复情况
-- ========================================
SELECT '=== 第六步：验证窗口1036 ===' as step;

SELECT 
    psw.id,
    pr.pmi_number,
    psw.window_date,
    psw.start_time,
    psw.end_date,
    psw.end_time,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    TIMESTAMPDIFF(MINUTE, psw.start_date_time, psw.end_date_time) as duration_minutes,
    CASE 
        WHEN psw.end_date_time > NOW() THEN 'SHOULD_BE_ACTIVE'
        ELSE 'SHOULD_BE_COMPLETED'
    END as expected_status,
    CASE 
        WHEN psw.status = 'ACTIVE' AND psw.end_date_time > NOW() THEN 'CORRECT'
        WHEN psw.status = 'COMPLETED' AND psw.end_date_time <= NOW() THEN 'CORRECT'
        ELSE 'NEEDS_FIX'
    END as status_correctness
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 1036;

-- ========================================
-- 第七步：设置新字段为NOT NULL（在验证无误后）
-- ========================================
SELECT '=== 第七步：设置字段约束 ===' as step;

-- 检查是否所有记录都有新字段值
SELECT 
    COUNT(*) as total_records,
    COUNT(start_date_time) as start_time_filled,
    COUNT(end_date_time) as end_time_filled,
    CASE 
        WHEN COUNT(*) = COUNT(start_date_time) AND COUNT(*) = COUNT(end_date_time) THEN 'READY_FOR_NOT_NULL'
        ELSE 'NOT_READY'
    END as readiness_status
FROM t_pmi_schedule_windows;

-- 如果所有记录都有值，则设置NOT NULL约束
-- 注意：这个操作需要在确认数据无误后手动执行
-- ALTER TABLE t_pmi_schedule_windows 
-- MODIFY COLUMN start_date_time DATETIME NOT NULL COMMENT '窗口开始时间（精确到秒）',
-- MODIFY COLUMN end_date_time DATETIME NOT NULL COMMENT '窗口结束时间（精确到秒）';

-- ========================================
-- 第八步：创建索引优化查询性能
-- ========================================
SELECT '=== 第八步：创建索引 ===' as step;

-- 为新字段创建索引，优化查询性能
CREATE INDEX idx_start_date_time ON t_pmi_schedule_windows(start_date_time);
CREATE INDEX idx_end_date_time ON t_pmi_schedule_windows(end_date_time);
CREATE INDEX idx_status_end_time ON t_pmi_schedule_windows(status, end_date_time);

-- ========================================
-- 第九步：测试新的查询逻辑
-- ========================================
SELECT '=== 第九步：测试新查询逻辑 ===' as step;

-- 测试查找需要关闭的窗口（新逻辑）
SELECT 
    'Windows to Close (New Logic)' as query_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE' 
AND end_date_time <= NOW();

-- 测试查找需要开启的窗口（新逻辑）
SELECT 
    'Windows to Open (New Logic)' as query_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status = 'PENDING' 
AND start_date_time <= NOW();

-- 对比旧逻辑的结果
SELECT 
    'Windows to Close (Old Logic)' as query_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE' 
AND (CURDATE() > end_date OR (CURDATE() = end_date AND end_time != '00:00:00' AND CURTIME() >= end_time));

-- ========================================
-- 第十步：最终验证报告
-- ========================================
SELECT '=== 最终验证报告 ===' as step;

SELECT 
    'Migration Summary' as report_type,
    COUNT(*) as total_windows,
    COUNT(CASE WHEN start_date_time IS NOT NULL THEN 1 END) as start_time_migrated,
    COUNT(CASE WHEN end_date_time IS NOT NULL THEN 1 END) as end_time_migrated,
    COUNT(CASE WHEN DATE(start_date_time) != DATE(end_date_time) THEN 1 END) as cross_day_windows,
    COUNT(CASE WHEN status = 'ACTIVE' AND end_date_time > NOW() THEN 1 END) as active_future_windows,
    COUNT(CASE WHEN status = 'COMPLETED' AND end_date_time > NOW() THEN 1 END) as completed_future_windows
FROM t_pmi_schedule_windows;

-- 显示迁移成功的关键案例
SELECT 
    'Key Migration Cases' as report_type,
    psw.id,
    pr.pmi_number,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    CASE 
        WHEN psw.end_date_time > NOW() THEN CONCAT('剩余 ', TIMESTAMPDIFF(HOUR, NOW(), psw.end_date_time), ' 小时')
        ELSE '已过期'
    END as time_remaining
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id IN (1036, 1026, 927, 963, 1013)
ORDER BY psw.id;
