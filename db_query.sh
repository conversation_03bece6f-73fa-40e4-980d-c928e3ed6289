#!/bin/bash

# 数据库连接配置
DB_HOST="localhost"
DB_USER="root"
DB_PASS="nvshen2018"
DB_NAME="zoombusV"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}数据库查询工具${NC}"
    echo "用法: $0 [选项] [SQL语句]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -f, --file     从文件执行SQL"
    echo "  -q, --query    执行单条SQL查询"
    echo "  -i, --interactive  进入交互模式"
    echo ""
    echo "示例:"
    echo "  $0 -q \"SELECT * FROM t_pmi_schedules LIMIT 5\""
    echo "  $0 -f script.sql"
    echo "  $0 -i"
}

# 执行SQL查询
execute_sql() {
    local sql="$1"
    echo -e "${YELLOW}执行SQL:${NC} $sql"
    echo ""
    
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$sql" 2>&1
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo -e "\n${GREEN}查询执行成功${NC}"
    else
        echo -e "\n${RED}查询执行失败，退出码: $exit_code${NC}"
    fi
    
    return $exit_code
}

# 从文件执行SQL
execute_sql_file() {
    local file="$1"
    if [ ! -f "$file" ]; then
        echo -e "${RED}错误: 文件 '$file' 不存在${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}从文件执行SQL:${NC} $file"
    echo ""
    
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$file" 2>&1
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo -e "\n${GREEN}SQL文件执行成功${NC}"
    else
        echo -e "\n${RED}SQL文件执行失败，退出码: $exit_code${NC}"
    fi
    
    return $exit_code
}

# 交互模式
interactive_mode() {
    echo -e "${BLUE}进入交互模式 (输入 'exit' 或 'quit' 退出)${NC}"
    echo ""
    
    while true; do
        echo -n "SQL> "
        read -r sql
        
        if [ "$sql" = "exit" ] || [ "$sql" = "quit" ]; then
            echo -e "${GREEN}退出交互模式${NC}"
            break
        fi
        
        if [ -n "$sql" ]; then
            execute_sql "$sql"
            echo ""
        fi
    done
}

# 主程序
main() {
    case "$1" in
        -h|--help)
            show_help
            ;;
        -f|--file)
            if [ -z "$2" ]; then
                echo -e "${RED}错误: 请指定SQL文件${NC}"
                exit 1
            fi
            execute_sql_file "$2"
            ;;
        -q|--query)
            if [ -z "$2" ]; then
                echo -e "${RED}错误: 请指定SQL查询语句${NC}"
                exit 1
            fi
            execute_sql "$2"
            ;;
        -i|--interactive)
            interactive_mode
            ;;
        "")
            show_help
            ;;
        *)
            # 如果没有选项，直接当作SQL执行
            execute_sql "$*"
            ;;
    esac
}

# 检查mysql命令是否存在
if ! command -v mysql &> /dev/null; then
    echo -e "${RED}错误: mysql命令未找到，请确保MySQL客户端已安装${NC}"
    exit 1
fi

# 执行主程序
main "$@"
