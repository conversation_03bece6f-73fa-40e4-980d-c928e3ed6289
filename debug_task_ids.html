<!DOCTYPE html>
<html>
<head>
    <title>调试任务ID问题</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
        }
        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
        }
    </style>
</head>
<body>
    <h1>任务ID调试工具</h1>
    
    <div class="test-section">
        <h2>1. 检查任务列表</h2>
        <p>获取所有任务，查看实际存在的任务ID</p>
        <button class="test-button" onclick="checkTaskList()">获取任务列表</button>
        <div id="task-list-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 检查窗口数据</h2>
        <p>获取PMI记录320的计划窗口数据</p>
        <button class="test-button" onclick="checkWindowData()">获取窗口数据</button>
        <div id="window-data-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试特定任务ID</h2>
        <p>测试任务ID 29、30是否存在</p>
        <button class="test-button" onclick="testTaskId(29)">测试任务29</button>
        <button class="test-button" onclick="testTaskId(30)">测试任务30</button>
        <button class="test-button" onclick="testTaskId(31)">测试任务31</button>
        <div id="task-id-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 模拟前端调用</h2>
        <p>模拟前端弹窗的完整调用流程</p>
        <button class="test-button" onclick="simulateFrontendCall()">模拟前端调用</button>
        <div id="frontend-call-result" class="result"></div>
    </div>

    <script>
        async function checkTaskList() {
            const resultDiv = document.getElementById('task-list-result');
            resultDiv.textContent = '正在获取任务列表...';
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/pmi-scheduled-tasks?page=1&size=50', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const tasks = data.data.content || [];
                    
                    let result = `任务列表检查结果:\n\n`;
                    result += `总任务数: ${tasks.length}\n`;
                    result += `页面信息: 第${data.data.number + 1}页，共${data.data.totalPages}页，总计${data.data.totalElements}个任务\n\n`;
                    
                    if (tasks.length > 0) {
                        result += `任务ID列表: ${tasks.map(t => t.id).join(', ')}\n\n`;
                        
                        // 查找任务29和30
                        const task29 = tasks.find(t => t.id === 29);
                        const task30 = tasks.find(t => t.id === 30);
                        
                        result += `任务29存在: ${!!task29}\n`;
                        result += `任务30存在: ${!!task30}\n\n`;
                        
                        if (task29) {
                            result += `任务29详情:\n`;
                            result += `- 类型: ${task29.taskType}\n`;
                            result += `- 状态: ${task29.status}\n`;
                            result += `- PMI窗口ID: ${task29.pmiWindowId}\n\n`;
                        }
                        
                        if (task30) {
                            result += `任务30详情:\n`;
                            result += `- 类型: ${task30.taskType}\n`;
                            result += `- 状态: ${task30.status}\n`;
                            result += `- PMI窗口ID: ${task30.pmiWindowId}\n\n`;
                        }
                        
                        // 显示最近的几个任务
                        result += `最近的5个任务:\n`;
                        tasks.slice(0, 5).forEach(task => {
                            result += `- ID: ${task.id}, 类型: ${task.taskType}, 状态: ${task.status}, 窗口ID: ${task.pmiWindowId}\n`;
                        });
                    } else {
                        result += `没有找到任务数据\n`;
                    }
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = result;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取任务列表失败: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `检查任务列表失败: ${error.message}`;
            }
        }
        
        async function checkWindowData() {
            const resultDiv = document.getElementById('window-data-result');
            resultDiv.textContent = '正在获取窗口数据...';
            
            try {
                const token = localStorage.getItem('token');
                
                // 首先获取PMI记录320的计划列表
                const scheduleResponse = await fetch('/api/pmi-schedules?pmiRecordId=320&page=0&size=10', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!scheduleResponse.ok) {
                    throw new Error(`获取计划失败: ${scheduleResponse.status}`);
                }
                
                const scheduleData = await scheduleResponse.json();
                const schedules = scheduleData.data.content || [];
                
                let result = `窗口数据检查结果:\n\n`;
                result += `PMI记录320的计划数: ${schedules.length}\n\n`;
                
                if (schedules.length > 0) {
                    for (const schedule of schedules) {
                        result += `计划ID: ${schedule.id}, 名称: ${schedule.name}\n`;
                        
                        // 获取该计划的窗口
                        const windowResponse = await fetch(`/api/pmi-schedules/${schedule.id}/windows?page=0&size=10`, {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (windowResponse.ok) {
                            const windowData = await windowResponse.json();
                            const windows = windowData.data.content || [];
                            
                            result += `  窗口数: ${windows.length}\n`;
                            
                            windows.forEach(window => {
                                result += `  窗口ID: ${window.id}\n`;
                                if (window.openTask) {
                                    result += `    开启任务ID: ${window.openTask.id}, 状态: ${window.openTask.status}\n`;
                                }
                                if (window.closeTask) {
                                    result += `    关闭任务ID: ${window.closeTask.id}, 状态: ${window.closeTask.status}\n`;
                                }
                            });
                        }
                        result += '\n';
                    }
                } else {
                    result += `没有找到PMI记录320的计划\n`;
                }
                
                resultDiv.className = 'result success';
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `检查窗口数据失败: ${error.message}`;
            }
        }
        
        async function testTaskId(taskId) {
            const resultDiv = document.getElementById('task-id-result');
            const currentContent = resultDiv.textContent || '';
            resultDiv.textContent = currentContent + `\n正在测试任务${taskId}...`;
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`/api/pmi-scheduled-tasks/${taskId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                let result = currentContent + `\n\n任务${taskId}测试结果:\n`;
                result += `状态码: ${response.status} ${response.statusText}\n`;
                
                if (response.ok) {
                    const data = await response.json();
                    result += `成功: ✅\n`;
                    result += `任务类型: ${data.data.taskType}\n`;
                    result += `任务状态: ${data.data.status}\n`;
                    result += `PMI窗口ID: ${data.data.pmiWindowId}\n`;
                    result += `计划执行时间: ${data.data.scheduledTime}\n`;
                    resultDiv.className = 'result success';
                } else {
                    const errorText = await response.text();
                    result += `失败: ❌\n`;
                    result += `错误信息: ${errorText}\n`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                const currentContent = resultDiv.textContent || '';
                resultDiv.className = 'result error';
                resultDiv.textContent = currentContent + `\n任务${taskId}测试失败: ${error.message}`;
            }
        }
        
        async function simulateFrontendCall() {
            const resultDiv = document.getElementById('frontend-call-result');
            resultDiv.textContent = '正在模拟前端调用...';
            
            try {
                // 模拟前端的完整调用流程
                let result = '前端调用模拟结果:\n\n';
                
                // 1. 获取窗口数据
                result += '1. 获取窗口数据...\n';
                const token = localStorage.getItem('token');
                
                // 获取计划列表
                const scheduleResponse = await fetch('/api/pmi-schedules?pmiRecordId=320&page=0&size=10', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!scheduleResponse.ok) {
                    throw new Error('获取计划失败');
                }
                
                const scheduleData = await scheduleResponse.json();
                const schedules = scheduleData.data.content || [];
                
                if (schedules.length === 0) {
                    throw new Error('没有找到计划');
                }
                
                const schedule = schedules[0];
                result += `   找到计划: ${schedule.name} (ID: ${schedule.id})\n`;
                
                // 获取窗口列表
                const windowResponse = await fetch(`/api/pmi-schedules/${schedule.id}/windows?page=0&size=10`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!windowResponse.ok) {
                    throw new Error('获取窗口失败');
                }
                
                const windowData = await windowResponse.json();
                const windows = windowData.data.content || [];
                
                result += `   找到窗口数: ${windows.length}\n\n`;
                
                // 2. 测试任务详情调用
                result += '2. 测试任务详情调用...\n';
                
                for (const window of windows.slice(0, 3)) { // 只测试前3个窗口
                    result += `   窗口ID: ${window.id}\n`;
                    
                    if (window.openTask) {
                        const taskId = window.openTask.id;
                        result += `     测试开启任务${taskId}...`;
                        
                        try {
                            const taskResponse = await fetch(`/api/pmi-scheduled-tasks/${taskId}`, {
                                headers: {
                                    'Authorization': `Bearer ${token}`,
                                    'Content-Type': 'application/json'
                                }
                            });
                            
                            if (taskResponse.ok) {
                                const taskData = await taskResponse.json();
                                result += ` ✅ 成功\n`;
                                result += `       状态: ${taskData.data.status}\n`;
                                result += `       类型: ${taskData.data.taskType}\n`;
                            } else {
                                result += ` ❌ 失败 (${taskResponse.status})\n`;
                            }
                        } catch (e) {
                            result += ` ❌ 错误: ${e.message}\n`;
                        }
                    }
                    
                    if (window.closeTask) {
                        const taskId = window.closeTask.id;
                        result += `     测试关闭任务${taskId}...`;
                        
                        try {
                            const taskResponse = await fetch(`/api/pmi-scheduled-tasks/${taskId}`, {
                                headers: {
                                    'Authorization': `Bearer ${token}`,
                                    'Content-Type': 'application/json'
                                }
                            });
                            
                            if (taskResponse.ok) {
                                const taskData = await taskResponse.json();
                                result += ` ✅ 成功\n`;
                                result += `       状态: ${taskData.data.status}\n`;
                                result += `       类型: ${taskData.data.taskType}\n`;
                            } else {
                                result += ` ❌ 失败 (${taskResponse.status})\n`;
                            }
                        } catch (e) {
                            result += ` ❌ 错误: ${e.message}\n`;
                        }
                    }
                    
                    result += '\n';
                }
                
                resultDiv.className = 'result success';
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `模拟前端调用失败: ${error.message}`;
            }
        }
        
        // 页面加载时显示基本信息
        window.onload = function() {
            console.log('任务ID调试工具加载完成');
            
            const userInfo = localStorage.getItem('userInfo');
            const token = localStorage.getItem('token');
            
            console.log('用户信息:', userInfo ? JSON.parse(userInfo) : '无');
            console.log('Token存在:', !!token);
        };
    </script>
</body>
</html>
