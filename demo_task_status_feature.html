<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PMI窗口任务状态展示演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #1890ff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .table th, .table td {
            border: 1px solid #e8e8e8;
            padding: 12px;
            text-align: left;
        }
        .table th {
            background: #fafafa;
            font-weight: 600;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin: 2px;
        }
        .tag-blue { background: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff; }
        .tag-green { background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
        .tag-orange { background: #fff7e6; color: #fa8c16; border: 1px solid #ffd591; }
        .tag-red { background: #fff2f0; color: #ff4d4f; border: 1px solid #ffb3b3; }
        .tag-default { background: #f5f5f5; color: #666; border: 1px solid #d9d9d9; }
        
        .task-info {
            font-size: 12px;
            margin-bottom: 4px;
        }
        .task-label {
            color: #666;
            margin-right: 4px;
        }
        .detail-link {
            color: #1890ff;
            text-decoration: none;
            font-size: 12px;
            margin-left: 4px;
        }
        .detail-link:hover {
            text-decoration: underline;
        }
        .status-active { color: #52c41a; font-weight: 600; }
        .status-pending { color: #1890ff; }
        .status-failed { color: #ff4d4f; font-weight: 600; }
        
        .demo-note {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .demo-note h3 {
            margin: 0 0 8px 0;
            color: #1890ff;
        }
        
        .feature-highlight {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 16px;
            margin-top: 20px;
        }
        .feature-highlight h3 {
            margin: 0 0 12px 0;
            color: #52c41a;
        }
        .feature-list {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PMI计划窗口列表 - 任务状态展示功能演示</h1>
            <p>展示窗口2082修复后的任务状态可视化效果</p>
        </div>
        
        <div class="content">
            <div class="demo-note">
                <h3>🎯 修复说明</h3>
                <p><strong>问题</strong>：窗口2082开启失败，原因是事件监听器时序问题导致任务创建失败</p>
                <p><strong>解决</strong>：修复了事件监听器代码，手动创建了缺失的任务，并在前端添加了任务状态展示功能</p>
            </div>

            <h2>计划窗口列表（含任务状态）</h2>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>窗口ID</th>
                        <th>计划ID</th>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>窗口状态</th>
                        <th>任务状态</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2080</td>
                        <td>1052</td>
                        <td>2025-08-23 19:39:00</td>
                        <td>2025-08-23 20:39:00</td>
                        <td><span class="status-active">ACTIVE</span></td>
                        <td>
                            <div class="task-info">
                                <span class="task-label">开启任务:</span>
                                <span class="tag tag-green">已完成</span>
                                <a href="#" class="detail-link" onclick="showTaskDetail(23)">详情</a>
                            </div>
                            <div class="task-info">
                                <span class="task-label">关闭任务:</span>
                                <span class="tag tag-blue">已调度</span>
                                <a href="#" class="detail-link" onclick="showTaskDetail(24)">详情</a>
                            </div>
                        </td>
                        <td>2025-08-23 19:39:52</td>
                    </tr>
                    <tr style="background-color: #fff7e6;">
                        <td><strong>2082</strong></td>
                        <td>1053</td>
                        <td>2025-08-23 20:09:00</td>
                        <td>2025-08-23 21:09:00</td>
                        <td><span class="status-active">ACTIVE</span></td>
                        <td>
                            <div class="task-info">
                                <span class="task-label">开启任务:</span>
                                <span class="tag tag-green">已完成</span>
                                <a href="#" class="detail-link" onclick="showTaskDetail(25)">详情</a>
                            </div>
                            <div class="task-info">
                                <span class="task-label">关闭任务:</span>
                                <span class="tag tag-blue">已调度</span>
                                <a href="#" class="detail-link" onclick="showTaskDetail(26)">详情</a>
                            </div>
                        </td>
                        <td>2025-08-23 20:09:38</td>
                    </tr>
                    <tr>
                        <td>2078</td>
                        <td>1051</td>
                        <td>2025-08-23 19:39:00</td>
                        <td>2025-08-23 20:39:00</td>
                        <td><span class="status-active">ACTIVE</span></td>
                        <td>
                            <div class="task-info">
                                <span class="task-label">开启任务:</span>
                                <span class="tag tag-green">已完成</span>
                                <a href="#" class="detail-link" onclick="showTaskDetail(21)">详情</a>
                            </div>
                            <div class="task-info">
                                <span class="task-label">关闭任务:</span>
                                <span class="tag tag-blue">已调度</span>
                                <a href="#" class="detail-link" onclick="showTaskDetail(22)">详情</a>
                            </div>
                        </td>
                        <td>2025-08-23 19:39:51</td>
                    </tr>
                    <tr>
                        <td>2084</td>
                        <td>1054</td>
                        <td>2025-08-23 21:00:00</td>
                        <td>2025-08-23 22:00:00</td>
                        <td><span class="status-pending">PENDING</span></td>
                        <td>
                            <div class="task-info">
                                <span class="task-label">开启任务:</span>
                                <span class="tag tag-blue">已调度</span>
                                <a href="#" class="detail-link" onclick="showTaskDetail(27)">详情</a>
                            </div>
                            <div class="task-info">
                                <span class="task-label">关闭任务:</span>
                                <span class="tag tag-blue">已调度</span>
                                <a href="#" class="detail-link" onclick="showTaskDetail(28)">详情</a>
                            </div>
                        </td>
                        <td>2025-08-23 20:15:22</td>
                    </tr>
                    <tr>
                        <td>2086</td>
                        <td>1055</td>
                        <td>2025-08-23 22:00:00</td>
                        <td>2025-08-23 23:00:00</td>
                        <td><span class="status-pending">PENDING</span></td>
                        <td>
                            <div class="task-info">
                                <span class="task-label">开启任务:</span>
                                <span class="tag tag-default">无任务</span>
                            </div>
                            <div class="task-info">
                                <span class="task-label">关闭任务:</span>
                                <span class="tag tag-default">无任务</span>
                            </div>
                        </td>
                        <td>2025-08-23 20:20:15</td>
                    </tr>
                </tbody>
            </table>

            <div class="feature-highlight">
                <h3>✨ 新增功能特性</h3>
                <ul class="feature-list">
                    <li><strong>任务状态可视化</strong>：每个窗口显示开启任务和关闭任务的状态</li>
                    <li><strong>状态标签</strong>：用不同颜色区分任务状态（蓝色=已调度，绿色=已完成，橙色=执行中，红色=失败）</li>
                    <li><strong>任务详情链接</strong>：点击"详情"可以查看任务的详细信息</li>
                    <li><strong>问题识别</strong>：可以快速识别缺失任务的窗口（显示"无任务"）</li>
                    <li><strong>运维友好</strong>：便于运维人员快速定位和处理任务相关问题</li>
                </ul>
            </div>

            <div style="margin-top: 30px; padding: 20px; background: #f0f0f0; border-radius: 4px;">
                <h3>🔧 修复前后对比</h3>
                <div style="display: flex; gap: 20px;">
                    <div style="flex: 1;">
                        <h4 style="color: #ff4d4f;">修复前</h4>
                        <ul>
                            <li>窗口2082开启失败</li>
                            <li>无法看到任务状态</li>
                            <li>问题排查困难</li>
                            <li>用户体验差</li>
                        </ul>
                    </div>
                    <div style="flex: 1;">
                        <h4 style="color: #52c41a;">修复后</h4>
                        <ul>
                            <li>窗口2082正常运行</li>
                            <li>任务状态清晰可见</li>
                            <li>支持任务详情查看</li>
                            <li>运维效率提升</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTaskDetail(taskId) {
            alert(`跳转到任务详情页面\n任务ID: ${taskId}\n\n在实际系统中，这里会打开新窗口显示任务的详细信息，包括：\n- 任务类型（开启/关闭）\n- 计划执行时间\n- 实际执行时间\n- 执行状态\n- 错误信息（如有）\n- 重试次数等`);
        }
    </script>
</body>
</html>
