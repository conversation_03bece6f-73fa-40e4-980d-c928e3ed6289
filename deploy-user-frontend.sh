#!/bin/bash

# ZoomBus 用户端前端快速部署脚本
# 专门用于部署用户端前端到 /home/<USER>/zoombus.com/dist

set -e  # 遇到错误立即退出

echo "=== ZoomBus 用户端前端快速部署 ==="

# 配置变量
TARGET_SERVER="<EMAIL>"
USER_FRONTEND_TARGET_DIR="/home/<USER>/zoombus.com/dist"
USER_FRONTEND_SOURCE_DIR="user-frontend"
USER_FRONTEND_BUILD_DIR="src/main/resources/static-user"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查用户端前端目录
    if [ ! -d "$USER_FRONTEND_SOURCE_DIR" ]; then
        log_error "未找到用户端前端目录: $USER_FRONTEND_SOURCE_DIR"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "未找到Node.js，请安装Node.js 16或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        log_error "Node.js版本过低，需要Node.js 16或更高版本，当前版本: $NODE_VERSION"
        exit 1
    fi
    log_success "Node.js版本检查通过: v$(node -v | cut -d'v' -f2)"
    
    # 检查SFTP连接
    if ! command -v sftp &> /dev/null; then
        log_error "未找到SFTP客户端"
        exit 1
    fi
    
    # 测试SSH连接
    log_info "测试SSH连接到 $TARGET_SERVER..."
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes $TARGET_SERVER "echo 'SSH连接测试成功'" 2>/dev/null; then
        log_error "无法连接到 $TARGET_SERVER，请检查SSH配置和证书信任"
        exit 1
    fi
    log_success "SSH连接测试成功"
}

# 清理构建目录
clean_build() {
    log_info "清理用户端前端构建目录..."

    if [ -d "$USER_FRONTEND_BUILD_DIR" ]; then
        rm -rf "$USER_FRONTEND_BUILD_DIR"
        log_info "清理用户端前端构建目录"
    fi

    log_success "构建目录清理完成"
}

# 构建用户端前端
build_user_frontend() {
    log_info "开始构建用户端前端..."
    
    cd "$USER_FRONTEND_SOURCE_DIR"
    
    # 检查package.json
    if [ ! -f "package.json" ]; then
        log_error "未找到package.json文件"
        exit 1
    fi
    
    # 安装依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        log_info "安装用户端前端依赖..."
        npm install
    fi
    
    # 构建前端
    log_info "执行用户端前端构建..."
    npm run build
    
    # 检查构建结果
    if [ ! -d "../$USER_FRONTEND_BUILD_DIR" ]; then
        log_error "用户端前端构建失败，未找到构建输出目录"
        exit 1
    fi

    # 检查关键文件
    if [ ! -f "../$USER_FRONTEND_BUILD_DIR/index.html" ]; then
        log_error "用户端前端构建失败，未找到index.html"
        exit 1
    fi

    cd ..
    log_success "用户端前端构建完成: $USER_FRONTEND_BUILD_DIR"
}

# 部署用户端前端
deploy_user_frontend() {
    log_info "开始部署用户端前端到 $TARGET_SERVER:$USER_FRONTEND_TARGET_DIR..."
    
    # 创建目标目录的父目录
    ssh $TARGET_SERVER "mkdir -p $(dirname $USER_FRONTEND_TARGET_DIR)"
    
    # 备份现有前端文件
    BACKUP_DIR="${USER_FRONTEND_TARGET_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    ssh $TARGET_SERVER "if [ -d $USER_FRONTEND_TARGET_DIR ]; then mv $USER_FRONTEND_TARGET_DIR $BACKUP_DIR && echo '已备份到: $BACKUP_DIR'; fi"
    
    # 创建新的目标目录
    ssh $TARGET_SERVER "mkdir -p $USER_FRONTEND_TARGET_DIR"
    
    # 上传前端文件
    log_info "上传用户端前端文件..."
    scp -r "$USER_FRONTEND_BUILD_DIR/"* $TARGET_SERVER:$USER_FRONTEND_TARGET_DIR/
    
    # 验证上传
    if ssh $TARGET_SERVER "[ -f $USER_FRONTEND_TARGET_DIR/index.html ]"; then
        log_success "用户端前端部署完成"
        
        # 显示文件列表
        log_info "部署的文件列表:"
        ssh $TARGET_SERVER "ls -la $USER_FRONTEND_TARGET_DIR/"
        
        # 显示文件大小
        TOTAL_SIZE=$(ssh $TARGET_SERVER "du -sh $USER_FRONTEND_TARGET_DIR" | cut -f1)
        log_info "部署文件总大小: $TOTAL_SIZE"
        
    else
        log_error "用户端前端部署失败，index.html未找到"
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=== 部署完成 ==="
    log_success "用户端前端部署路径: $TARGET_SERVER:$USER_FRONTEND_TARGET_DIR"
    echo ""
    echo "💡 部署后操作建议:"
    echo "   - 访问用户端: 根据您的域名配置访问 zoombus.com"
    echo "   - 检查文件权限: ssh $TARGET_SERVER 'ls -la $USER_FRONTEND_TARGET_DIR/'"
    echo "   - 检查Nginx配置: 确保域名指向正确的目录"
    echo ""
    echo "🌐 如果使用Nginx，建议的配置:"
    echo "   server {"
    echo "       listen 80;"
    echo "       server_name zoombus.com www.zoombus.com;"
    echo "       root $USER_FRONTEND_TARGET_DIR;"
    echo "       index index.html;"
    echo "       try_files \$uri \$uri/ /index.html;"
    echo "   }"
    echo ""
}

# 主函数
main() {
    echo "开始用户端前端部署流程..."
    echo ""
    
    # 显示配置信息
    log_info "部署配置:"
    echo "   源目录: $USER_FRONTEND_SOURCE_DIR"
    echo "   目标服务器: $TARGET_SERVER"
    echo "   目标目录: $USER_FRONTEND_TARGET_DIR"
    echo ""
    
    # 确认部署
    read -p "确认要部署用户端前端到 $TARGET_SERVER 吗? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    # 执行部署步骤
    check_environment
    clean_build
    build_user_frontend
    deploy_user_frontend
    show_deployment_info
    
    log_success "🎉 用户端前端部署完成!"
}

# 快速模式（跳过确认）
if [ "$1" = "--quick" ] || [ "$1" = "-q" ]; then
    log_info "快速部署模式（跳过确认）"
    check_environment
    clean_build
    build_user_frontend
    deploy_user_frontend
    show_deployment_info
    log_success "🎉 用户端前端快速部署完成!"
else
    # 执行主函数
    main "$@"
fi
