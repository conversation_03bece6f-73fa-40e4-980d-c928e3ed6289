#!/bin/bash

# ZoomBus 配置化部署脚本
# 使用 deploy.conf 配置文件进行部署

set -e

# 默认配置文件路径
CONFIG_FILE="deploy.conf"

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    echo "请创建配置文件或使用默认配置"
    exit 1
fi

# 加载配置文件
source "$CONFIG_FILE"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示配置信息
show_config() {
    echo "🔧 部署配置:"
    echo "   目标服务器: $TARGET_SERVER"
    echo "   后端路径: $BACKEND_TARGET_DIR"
    echo "   前端路径: $FRONTEND_TARGET_DIR"
    echo "   JAR文件: $JAR_NAME"
    echo "   应用端口: $APPLICATION_PORT"
    echo ""
}

# 设置Java 11环境
setup_java11_environment() {
    log_info "检查并设置Java 11环境..."

    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        JAVA11_PATHS=(
            "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home"
            "/Library/Java/JavaVirtualMachines/jdk-11*/Contents/Home"
            "/Library/Java/JavaVirtualMachines/openjdk-11*/Contents/Home"
        )

        for path in "${JAVA11_PATHS[@]}"; do
            if [ -d "$path" ]; then
                export JAVA_HOME="$path"
                export PATH="$JAVA_HOME/bin:$PATH"
                log_success "设置Java 11环境: $JAVA_HOME"
                break
            fi
        done

        # 尝试使用java_home工具
        if [ -z "$JAVA_HOME" ] && command -v /usr/libexec/java_home &> /dev/null; then
            JAVA11_HOME=$(/usr/libexec/java_home -v 11 2>/dev/null)
            if [ -n "$JAVA11_HOME" ]; then
                export JAVA_HOME="$JAVA11_HOME"
                export PATH="$JAVA_HOME/bin:$PATH"
                log_success "通过java_home设置Java 11: $JAVA_HOME"
            fi
        fi
    else
        # Linux
        JAVA11_PATHS=(
            "/usr/lib/jvm/java-11-openjdk"
            "/usr/lib/jvm/java-11-openjdk-amd64"
            "/usr/lib/jvm/jdk-11"
        )

        for path in "${JAVA11_PATHS[@]}"; do
            if [ -d "$path" ]; then
                export JAVA_HOME="$path"
                export PATH="$JAVA_HOME/bin:$PATH"
                log_success "设置Java 11环境: $JAVA_HOME"
                break
            fi
        done
    fi

    # 验证Java版本
    if ! command -v java &> /dev/null; then
        log_error "未找到Java，请安装Java 11"
        exit 1
    fi

    JAVA_VERSION=$(java -version 2>&1 | head -1)
    if [[ "$JAVA_VERSION" == *"11."* ]] || [[ "$JAVA_VERSION" == *"version \"11"* ]]; then
        log_success "Java 11验证成功: $JAVA_VERSION"
    else
        log_error "Java版本不正确，需要Java 11。当前版本: $JAVA_VERSION"
        log_error "请确保已安装Java 11并设置正确的JAVA_HOME"
        exit 1
    fi
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."

    # 检查并设置Java 11
    setup_java11_environment
    
    # 检查Maven
    if [ -f "./mvnw" ]; then
        MVN_CMD="./mvnw"
    elif command -v mvn &> /dev/null; then
        MVN_CMD="mvn"
    else
        log_error "未找到Maven"
        exit 1
    fi
    
    # 检查Node.js（如果需要部署前端）
    if [ "$DEPLOY_FRONTEND" = "true" ]; then
        if ! command -v node &> /dev/null; then
            log_error "未找到Node.js"
            exit 1
        fi
    fi
    
    # 检查SSH连接
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes $TARGET_SERVER "echo 'SSH连接测试成功'" 2>/dev/null; then
        log_error "无法连接到 $TARGET_SERVER"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 构建后端
build_backend() {
    if [ "$DEPLOY_BACKEND" != "true" ]; then
        log_info "跳过后端构建"
        return
    fi
    
    log_info "构建后端..."
    
    BUILD_ARGS="clean package"
    if [ "$MAVEN_SKIP_TESTS" = "true" ]; then
        BUILD_ARGS="$BUILD_ARGS -DskipTests"
    fi
    if [ -n "$MAVEN_PROFILES" ]; then
        BUILD_ARGS="$BUILD_ARGS $MAVEN_PROFILES"
    fi
    
    $MVN_CMD $BUILD_ARGS
    
    if [ ! -f "target/$JAR_NAME" ]; then
        log_error "后端构建失败"
        exit 1
    fi
    
    log_success "后端构建完成"
}

# 构建前端
build_frontend() {
    if [ "$DEPLOY_FRONTEND" != "true" ]; then
        log_info "跳过前端构建"
        return
    fi
    
    log_info "构建前端..."
    
    cd frontend
    
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        npm install
    fi
    
    npm run $NPM_BUILD_COMMAND
    
    if [ ! -d "build" ]; then
        log_error "前端构建失败"
        exit 1
    fi
    
    cd ..
    log_success "前端构建完成"
}

# 创建备份
create_backup() {
    if [ "$BACKUP_ENABLED" != "true" ]; then
        return
    fi
    
    log_info "创建备份..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    
    # 备份后端
    if [ "$DEPLOY_BACKEND" = "true" ]; then
        ssh $TARGET_SERVER "if [ -f $BACKEND_TARGET_DIR/$JAR_NAME ]; then mkdir -p $BACKEND_TARGET_DIR/$BACKUP_DIR && cp $BACKEND_TARGET_DIR/$JAR_NAME $BACKEND_TARGET_DIR/$BACKUP_DIR/${JAR_NAME}.backup.$TIMESTAMP; fi"
    fi
    
    # 备份前端
    if [ "$DEPLOY_FRONTEND" = "true" ]; then
        ssh $TARGET_SERVER "if [ -d $FRONTEND_TARGET_DIR ]; then mkdir -p $(dirname $FRONTEND_TARGET_DIR)/$BACKUP_DIR && cp -r $FRONTEND_TARGET_DIR $(dirname $FRONTEND_TARGET_DIR)/$BACKUP_DIR/dist.backup.$TIMESTAMP; fi"
    fi
    
    log_success "备份创建完成"
}

# 部署后端
deploy_backend() {
    if [ "$DEPLOY_BACKEND" != "true" ]; then
        log_info "跳过后端部署"
        return
    fi
    
    log_info "部署后端..."
    
    ssh $TARGET_SERVER "mkdir -p $BACKEND_TARGET_DIR"
    scp target/$JAR_NAME $TARGET_SERVER:$BACKEND_TARGET_DIR/
    
    log_success "后端部署完成"
}

# 部署前端
deploy_frontend() {
    if [ "$DEPLOY_FRONTEND" != "true" ]; then
        log_info "跳过前端部署"
        return
    fi
    
    log_info "部署前端..."
    
    ssh $TARGET_SERVER "rm -rf $FRONTEND_TARGET_DIR && mkdir -p $FRONTEND_TARGET_DIR"
    scp -r frontend/build/* $TARGET_SERVER:$FRONTEND_TARGET_DIR/
    
    log_success "前端部署完成"
}

# 重启服务
restart_service() {
    if [ "$RESTART_SERVICE" != "true" ]; then
        log_info "跳过服务重启"
        return
    fi
    
    log_info "重启服务..."
    
    # 停止现有服务
    ssh $TARGET_SERVER "pkill -f '$JAR_NAME' || true"
    sleep 3
    
    # 启动新服务
    ssh $TARGET_SERVER "cd $BACKEND_TARGET_DIR && JAVA11_PATH=\$(find /usr/lib/jvm -name 'java-11-openjdk*' -type d 2>/dev/null | head -1) && if [ -n \"\$JAVA11_PATH\" ]; then JAVA_CMD=\"\$JAVA11_PATH/bin/java\"; else JAVA_CMD=\"java\"; fi && nohup \"\$JAVA_CMD\" -jar $JAR_NAME > $LOG_FILE 2>&1 &"
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep $SERVICE_START_TIMEOUT
    
    # 检查服务状态
    if ssh $TARGET_SERVER "pgrep -f '$JAR_NAME' > /dev/null"; then
        log_success "服务启动成功"
    else
        log_warning "服务可能未完全启动"
    fi
}

# 运行验证
run_verification() {
    if [ "$RUN_VERIFICATION" != "true" ]; then
        log_info "跳过部署验证"
        return
    fi
    
    log_info "运行部署验证..."
    
    if [ -f "verify-deployment.sh" ]; then
        ./verify-deployment.sh
    else
        log_warning "验证脚本不存在，跳过验证"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    if [ "$BACKUP_ENABLED" != "true" ]; then
        return
    fi
    
    log_info "清理旧备份..."
    
    ssh $TARGET_SERVER "find $BACKEND_TARGET_DIR/$BACKUP_DIR -name '*.backup.*' -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null || true"
    ssh $TARGET_SERVER "find $(dirname $FRONTEND_TARGET_DIR)/$BACKUP_DIR -name 'dist.backup.*' -mtime +$BACKUP_RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true"
    
    log_success "旧备份清理完成"
}

# 发送通知
send_notification() {
    if [ "$NOTIFICATION_ENABLED" != "true" ]; then
        return
    fi
    
    local status=$1
    local message="ZoomBus 部署$status - $(date)"
    
    if [ -n "$NOTIFICATION_WEBHOOK" ]; then
        curl -X POST -H 'Content-type: application/json' \
             --data "{\"text\":\"$message\"}" \
             "$NOTIFICATION_WEBHOOK" 2>/dev/null || true
    fi
    
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        echo "$message" | mail -s "ZoomBus 部署通知" "$NOTIFICATION_EMAIL" 2>/dev/null || true
    fi
}

# 主函数
main() {
    echo "🚀 ZoomBus 配置化部署"
    echo ""
    
    show_config
    
    # 确认部署
    read -p "确认要部署到 $TARGET_SERVER 吗? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    # 执行部署步骤
    check_environment
    create_backup
    build_backend
    build_frontend
    deploy_backend
    deploy_frontend
    restart_service
    run_verification
    cleanup_old_backups
    
    log_success "🎉 部署完成!"
    send_notification "成功"
}

# 错误处理
trap 'log_error "部署过程中发生错误"; send_notification "失败"; exit 1' ERR

# 执行主函数
main "$@"
