#!/bin/bash

# ZoomBus 部署脚本
# 编译并部署到 <EMAIL>

set -e  # 遇到错误立即退出

echo "=== ZoomBus 部署脚本 ==="

# 配置变量
TARGET_SERVER="<EMAIL>"
BACKEND_TARGET_DIR="/root/zoombus"
ADMIN_FRONTEND_TARGET_DIR="/home/<USER>/m.zoombus.com/dist"
USER_FRONTEND_TARGET_DIR="/home/<USER>/zoombus.com/dist"
PROJECT_NAME="zoombus"
JAR_NAME="zoombus-1.0.0.jar"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置Java 11环境
setup_java11_environment() {
    log_info "检查并设置Java 11环境..."

    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        JAVA11_PATHS=(
            "/Library/Java/JavaVirtualMachines/microsoft-11.jdk/Contents/Home"
            "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home"
            "/Library/Java/JavaVirtualMachines/jdk-11*/Contents/Home"
            "/Library/Java/JavaVirtualMachines/openjdk-11*/Contents/Home"
        )

        for path in "${JAVA11_PATHS[@]}"; do
            if [ -d "$path" ]; then
                export JAVA_HOME="$path"
                export PATH="$JAVA_HOME/bin:$PATH"
                log_success "设置Java 11环境: $JAVA_HOME"
                break
            fi
        done

        # 尝试使用java_home工具
        if [ -z "$JAVA_HOME" ] && command -v /usr/libexec/java_home &> /dev/null; then
            JAVA11_HOME=$(/usr/libexec/java_home -v 11 2>/dev/null)
            if [ -n "$JAVA11_HOME" ]; then
                export JAVA_HOME="$JAVA11_HOME"
                export PATH="$JAVA_HOME/bin:$PATH"
                log_success "通过java_home设置Java 11: $JAVA_HOME"
            fi
        fi
    else
        # Linux
        JAVA11_PATHS=(
            "/usr/lib/jvm/java-11-openjdk"
            "/usr/lib/jvm/java-11-openjdk-amd64"
            "/usr/lib/jvm/jdk-11"
        )

        for path in "${JAVA11_PATHS[@]}"; do
            if [ -d "$path" ]; then
                export JAVA_HOME="$path"
                export PATH="$JAVA_HOME/bin:$PATH"
                log_success "设置Java 11环境: $JAVA_HOME"
                break
            fi
        done
    fi

    # 验证Java版本
    if ! command -v java &> /dev/null; then
        log_error "未找到Java，请安装Java 11"
        exit 1
    fi

    JAVA_VERSION=$(java -version 2>&1 | head -1)
    if [[ "$JAVA_VERSION" == *"11."* ]] || [[ "$JAVA_VERSION" == *"version \"11"* ]]; then
        log_success "Java 11验证成功: $JAVA_VERSION"
    else
        log_error "Java版本不正确，需要Java 11。当前版本: $JAVA_VERSION"
        log_error "请确保已安装Java 11并设置正确的JAVA_HOME"
        exit 1
    fi
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."

    # 检查并设置Java 11
    setup_java11_environment

    log_success "Java版本检查通过: Java 11"
    
    # 检查Maven
    if [ -f "./mvnw" ]; then
        MVN_CMD="./mvnw"
        log_info "使用Maven Wrapper"
    elif command -v mvn &> /dev/null; then
        MVN_CMD="mvn"
        log_info "使用系统Maven"
    else
        log_error "未找到Maven，请安装Maven或使用Maven Wrapper"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "未找到Node.js，请安装Node.js 16或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        log_error "Node.js版本过低，需要Node.js 16或更高版本，当前版本: $NODE_VERSION"
        exit 1
    fi
    log_success "Node.js版本检查通过: $NODE_VERSION"
    
    # 检查SFTP连接
    if ! command -v sftp &> /dev/null; then
        log_error "未找到SFTP客户端"
        exit 1
    fi
    
    # 测试SSH连接
    log_info "测试SSH连接到 $TARGET_SERVER..."
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes $TARGET_SERVER "echo 'SSH连接测试成功'" 2>/dev/null; then
        log_error "无法连接到 $TARGET_SERVER，请检查SSH配置和证书信任"
        exit 1
    fi
    log_success "SSH连接测试成功"
}

# 清理构建目录
clean_build() {
    log_info "清理构建目录..."

    # 清理后端构建目录
    if [ -d "target" ]; then
        rm -rf target
        log_info "清理后端target目录"
    fi

    # 清理管理端前端构建目录
    if [ -d "frontend/build" ]; then
        rm -rf frontend/build
        log_info "清理管理端前端build目录"
    fi

    # 清理用户端前端构建目录
    if [ -d "src/main/resources/static-user" ]; then
        rm -rf src/main/resources/static-user
        log_info "清理用户端前端构建目录"
    fi

    log_success "构建目录清理完成"
}

# 构建后端
build_backend() {
    log_info "开始构建后端..."
    
    # 跳过测试进行构建
    log_info "执行Maven构建 (跳过测试)..."
    $MVN_CMD clean package -DskipTests
    
    # 检查jar文件是否生成
    if [ ! -f "target/$JAR_NAME" ]; then
        log_error "后端构建失败，未找到jar文件: target/$JAR_NAME"
        exit 1
    fi
    
    log_success "后端构建完成: target/$JAR_NAME"
}

# 构建管理端前端
build_admin_frontend() {
    log_info "开始构建管理端前端..."

    cd frontend

    # 设置Node.js环境变量以减少警告和优化性能
    export NODE_OPTIONS="--no-deprecation --max-old-space-size=4096"
    export GENERATE_SOURCEMAP=false
    export DISABLE_ESLINT_PLUGIN=true

    # 安装依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        log_info "安装管理端前端依赖..."
        # 使用npm ci进行更快的安装（如果有package-lock.json）
        if [ -f "package-lock.json" ]; then
            npm ci --silent
        else
            npm install --silent
        fi
    fi

    # 构建前端
    log_info "执行管理端前端构建..."
    log_warning "构建过程可能需要1-2分钟，请耐心等待..."
    npm run build --silent

    # 检查构建结果
    if [ ! -d "build" ]; then
        log_error "管理端前端构建失败，未找到build目录"
        exit 1
    fi

    cd ..
    log_success "管理端前端构建完成: frontend/build"
}

# 构建用户端前端
build_user_frontend() {
    log_info "开始构建用户端前端..."

    cd user-frontend

    # 设置Node.js环境变量以减少警告和优化性能
    export NODE_OPTIONS="--no-deprecation --max-old-space-size=4096"
    export GENERATE_SOURCEMAP=false
    export DISABLE_ESLINT_PLUGIN=true

    # 安装依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        log_info "安装用户端前端依赖..."
        # 使用npm ci进行更快的安装（如果有package-lock.json）
        if [ -f "package-lock.json" ]; then
            npm ci --silent
        else
            npm install --silent
        fi
    fi

    # 构建前端
    log_info "执行用户端前端构建..."
    log_warning "构建过程可能需要1-2分钟，请耐心等待..."
    npm run build --silent

    # 检查构建结果
    if [ ! -d "../src/main/resources/static-user" ]; then
        log_error "用户端前端构建失败，未找到构建输出目录"
        exit 1
    fi

    cd ..
    log_success "用户端前端构建完成: src/main/resources/static-user"
}

# 构建所有前端
build_frontend() {
    build_admin_frontend
    build_user_frontend
}

# 部署后端
deploy_backend() {
    log_info "开始部署后端到 $TARGET_SERVER:$BACKEND_TARGET_DIR..."
    
    # 创建目标目录
    ssh $TARGET_SERVER "mkdir -p $BACKEND_TARGET_DIR"
    
    # 备份现有jar文件
    ssh $TARGET_SERVER "if [ -f $BACKEND_TARGET_DIR/$JAR_NAME ]; then mv $BACKEND_TARGET_DIR/$JAR_NAME $BACKEND_TARGET_DIR/${JAR_NAME}.backup.$(date +%Y%m%d_%H%M%S); fi"
    
    # 上传新的jar文件
    log_info "上传jar文件..."
    scp target/$JAR_NAME $TARGET_SERVER:$BACKEND_TARGET_DIR/
    
    # 验证上传
    if ssh $TARGET_SERVER "[ -f $BACKEND_TARGET_DIR/$JAR_NAME ]"; then
        log_success "后端部署完成"
    else
        log_error "后端部署失败，jar文件未找到"
        exit 1
    fi
}

# 部署管理端前端
deploy_admin_frontend() {
    log_info "开始部署管理端前端到 $TARGET_SERVER:$ADMIN_FRONTEND_TARGET_DIR..."

    # 创建目标目录的父目录
    ssh $TARGET_SERVER "mkdir -p $(dirname $ADMIN_FRONTEND_TARGET_DIR)"

    # 备份现有前端文件
    ssh $TARGET_SERVER "if [ -d $ADMIN_FRONTEND_TARGET_DIR ]; then mv $ADMIN_FRONTEND_TARGET_DIR ${ADMIN_FRONTEND_TARGET_DIR}.backup.$(date +%Y%m%d_%H%M%S); fi"

    # 创建新的目标目录
    ssh $TARGET_SERVER "mkdir -p $ADMIN_FRONTEND_TARGET_DIR"

    # 上传前端文件
    log_info "上传管理端前端文件..."
    scp -r frontend/build/* $TARGET_SERVER:$ADMIN_FRONTEND_TARGET_DIR/

    # 验证上传
    if ssh $TARGET_SERVER "[ -f $ADMIN_FRONTEND_TARGET_DIR/index.html ]"; then
        log_success "管理端前端部署完成"
    else
        log_error "管理端前端部署失败，index.html未找到"
        exit 1
    fi
}

# 部署用户端前端
deploy_user_frontend() {
    log_info "开始部署用户端前端到 $TARGET_SERVER:$USER_FRONTEND_TARGET_DIR..."

    # 创建目标目录的父目录
    ssh $TARGET_SERVER "mkdir -p $(dirname $USER_FRONTEND_TARGET_DIR)"

    # 备份现有前端文件
    ssh $TARGET_SERVER "if [ -d $USER_FRONTEND_TARGET_DIR ]; then mv $USER_FRONTEND_TARGET_DIR ${USER_FRONTEND_TARGET_DIR}.backup.$(date +%Y%m%d_%H%M%S); fi"

    # 创建新的目标目录
    ssh $TARGET_SERVER "mkdir -p $USER_FRONTEND_TARGET_DIR"

    # 上传前端文件
    log_info "上传用户端前端文件..."
    scp -r src/main/resources/static-user/* $TARGET_SERVER:$USER_FRONTEND_TARGET_DIR/

    # 验证上传
    if ssh $TARGET_SERVER "[ -f $USER_FRONTEND_TARGET_DIR/index.html ]"; then
        log_success "用户端前端部署完成"
    else
        log_error "用户端前端部署失败，index.html未找到"
        exit 1
    fi
}

# 部署所有前端
deploy_frontend() {
    deploy_admin_frontend
    deploy_user_frontend
}

# 重启服务
restart_services() {
    log_info "重启服务..."

    # 上传quick_start_zoombus.sh脚本到服务器
    log_info "上传启动脚本到服务器..."
    scp quick_start_zoombus.sh $TARGET_SERVER:$BACKEND_TARGET_DIR/
    ssh $TARGET_SERVER "chmod +x $BACKEND_TARGET_DIR/quick_start_zoombus.sh"

    # 使用quick_start_zoombus.sh重启服务
    log_info "使用quick_start_zoombus.sh重启ZoomBus服务..."

    if ssh $TARGET_SERVER "cd $BACKEND_TARGET_DIR && ./quick_start_zoombus.sh"; then
        log_success "ZoomBus服务重启成功"

        # 等待服务完全启动
        sleep 5

        # 检查服务状态
        if ssh $TARGET_SERVER "pgrep -f '$JAR_NAME' > /dev/null"; then
            PID=$(ssh $TARGET_SERVER "pgrep -f '$JAR_NAME'")
            log_success "ZoomBus服务运行正常，PID: $PID"

            # 检查端口监听
            if ssh $TARGET_SERVER "netstat -tlnp | grep :8080 > /dev/null"; then
                log_success "端口8080监听正常"
            else
                log_warning "端口8080暂未监听，应用可能还在启动中"
            fi
        else
            log_error "服务启动失败，请检查日志"
            ssh $TARGET_SERVER "tail -20 $BACKEND_TARGET_DIR/zoombus.log"
            exit 1
        fi
    else
        log_error "quick_start_zoombus.sh执行失败，使用备用启动方法"
        use_fallback_start
    fi
}

# 备用启动方法
use_fallback_start() {
    log_info "使用备用启动方法..."

    # 创建临时启动脚本
    cat > /tmp/start_zoombus.sh << 'EOF'
#!/bin/bash
cd /root/zoombus

# 查找Java 11
JAVA11_PATHS=(
    "/usr/lib/jvm/java-11-openjdk"
    "/usr/lib/jvm/java-11-openjdk-amd64"
    "/usr/lib/jvm/jdk-11"
    "/usr/lib/jvm/java-1.11.0-openjdk"
    "/usr/lib/jvm/java-1.11.0-openjdk-amd64"
)

JAVA_CMD=""
for path in "${JAVA11_PATHS[@]}"; do
    if [ -d "$path" ] && [ -x "$path/bin/java" ]; then
        JAVA_CMD="$path/bin/java"
        echo "找到Java 11: $JAVA_CMD"
        break
    fi
done

# 如果没找到Java 11，尝试通过find查找
if [ -z "$JAVA_CMD" ]; then
    JAVA11_PATH=$(find /usr/lib/jvm -name "java-11*" -type d 2>/dev/null | head -1)
    if [ -n "$JAVA11_PATH" ] && [ -x "$JAVA11_PATH/bin/java" ]; then
        JAVA_CMD="$JAVA11_PATH/bin/java"
        echo "通过find找到Java 11: $JAVA_CMD"
    fi
fi

# 最后使用系统默认Java
if [ -z "$JAVA_CMD" ]; then
    JAVA_CMD="java"
    echo "警告: 未找到Java 11，使用系统默认Java: $JAVA_CMD"
fi

# 验证Java版本
JAVA_VERSION=$($JAVA_CMD -version 2>&1 | head -1)
echo "Java版本: $JAVA_VERSION"

# 检查JAR文件
if [ ! -f "zoombus-1.0.0.jar" ]; then
    echo "错误: JAR文件不存在"
    exit 1
fi

# 设置JVM参数
JVM_OPTS="-Xms512m -Xmx1024m"
JVM_OPTS="$JVM_OPTS -Dspring.profiles.active=production"
JVM_OPTS="$JVM_OPTS -Dserver.port=8080"
JVM_OPTS="$JVM_OPTS -Djava.awt.headless=true"

echo "启动ZoomBus应用..."
echo "JVM参数: $JVM_OPTS"

nohup $JAVA_CMD $JVM_OPTS -jar zoombus-1.0.0.jar > zoombus.log 2>&1 &
PID=$!
echo "ZoomBus已启动，PID: $PID"
echo $PID > zoombus.pid

# 等待启动
sleep 10

# 检查进程状态
if ps -p $PID > /dev/null 2>&1; then
    echo "进程运行正常"
else
    echo "进程启动失败"
    tail -20 zoombus.log
    exit 1
fi
EOF

    # 上传并执行临时启动脚本
    scp /tmp/start_zoombus.sh $TARGET_SERVER:/tmp/
    ssh $TARGET_SERVER "chmod +x /tmp/start_zoombus.sh && /tmp/start_zoombus.sh && rm -f /tmp/start_zoombus.sh"
    rm -f /tmp/start_zoombus.sh
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=== 部署完成 ==="
    log_success "后端部署路径: $TARGET_SERVER:$BACKEND_TARGET_DIR/$JAR_NAME"
    log_success "管理端前端部署路径: $TARGET_SERVER:$ADMIN_FRONTEND_TARGET_DIR"
    log_success "用户端前端部署路径: $TARGET_SERVER:$USER_FRONTEND_TARGET_DIR"
    echo ""
    echo "💡 部署后操作建议:"
    echo "   - 检查服务日志: ssh $TARGET_SERVER 'tail -f $BACKEND_TARGET_DIR/zoombus.log'"
    echo "   - 检查服务状态: ssh $TARGET_SERVER 'pgrep -f $JAR_NAME'"
    echo "   - 访问管理端: 根据您的域名配置访问 m.zoombus.com"
    echo "   - 访问用户端: 根据您的域名配置访问 zoombus.com"
    echo ""
}

# 显示部署选项
show_deployment_options() {
    echo "请选择部署模式:"
    echo "1. 完整部署 (后端 + 管理端前端 + 用户端前端)"
    echo "2. 仅部署后端"
    echo "3. 仅部署管理端前端"
    echo "4. 仅部署用户端前端"
    echo "5. 仅部署前端 (管理端 + 用户端)"
    echo "6. 自定义选择"
    echo ""
}

# 主函数
main() {
    echo "开始部署流程..."
    echo ""

    # 显示部署选项
    show_deployment_options
    read -p "请选择部署模式 (1-6): " deploy_mode

    # 确认部署
    read -p "确认要部署到 $TARGET_SERVER 吗? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi

    # 根据选择执行部署
    case $deploy_mode in
        1)
            log_info "执行完整部署..."
            check_environment
            clean_build
            build_backend
            build_frontend
            deploy_backend
            deploy_frontend
            restart_services
            ;;
        2)
            log_info "仅部署后端..."
            check_environment
            if [ -d "target" ]; then rm -rf target; fi
            build_backend
            deploy_backend
            restart_services
            ;;
        3)
            log_info "仅部署管理端前端..."
            check_environment
            if [ -d "frontend/build" ]; then rm -rf frontend/build; fi
            build_admin_frontend
            deploy_admin_frontend
            ;;
        4)
            log_info "仅部署用户端前端..."
            check_environment
            if [ -d "src/main/resources/static-user" ]; then rm -rf src/main/resources/static-user; fi
            build_user_frontend
            deploy_user_frontend
            ;;
        5)
            log_info "仅部署前端..."
            check_environment
            if [ -d "frontend/build" ]; then rm -rf frontend/build; fi
            if [ -d "src/main/resources/static-user" ]; then rm -rf src/main/resources/static-user; fi
            build_frontend
            deploy_frontend
            ;;
        6)
            log_info "自定义部署..."
            check_environment

            read -p "是否部署后端? (y/N): " deploy_backend_confirm
            read -p "是否部署管理端前端? (y/N): " deploy_admin_confirm
            read -p "是否部署用户端前端? (y/N): " deploy_user_confirm

            # 清理和构建
            if [[ $deploy_backend_confirm =~ ^[Yy]$ ]]; then
                if [ -d "target" ]; then rm -rf target; fi
                build_backend
            fi

            if [[ $deploy_admin_confirm =~ ^[Yy]$ ]]; then
                if [ -d "frontend/build" ]; then rm -rf frontend/build; fi
                build_admin_frontend
            fi

            if [[ $deploy_user_confirm =~ ^[Yy]$ ]]; then
                if [ -d "src/main/resources/static-user" ]; then rm -rf src/main/resources/static-user; fi
                build_user_frontend
            fi

            # 部署
            if [[ $deploy_backend_confirm =~ ^[Yy]$ ]]; then
                deploy_backend
            fi

            if [[ $deploy_admin_confirm =~ ^[Yy]$ ]]; then
                deploy_admin_frontend
            fi

            if [[ $deploy_user_confirm =~ ^[Yy]$ ]]; then
                deploy_user_frontend
            fi

            # 重启服务（如果部署了后端）
            if [[ $deploy_backend_confirm =~ ^[Yy]$ ]]; then
                restart_services
            fi
            ;;
        *)
            log_error "无效的选择，请输入1-6"
            exit 1
            ;;
    esac

    show_deployment_info
    log_success "🎉 部署完成!"
}

# 执行主函数
main "$@"
