#!/bin/bash

# ZoomBus 开发环境启动脚本
# 此脚本专门用于开发环境，前端运行在3000端口，后端运行在8080端口

set -e

echo "🚀 ZoomBus 开发环境启动脚本"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查必要的工具
check_requirements() {
    echo -e "${BLUE}检查环境要求...${NC}"
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        echo -e "${RED}❌ Java未安装或未在PATH中${NC}"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1-2)
    echo -e "${GREEN}✓ Java版本: $JAVA_VERSION${NC}"
    
    # 检查Maven
    if [ -f "./mvnw" ]; then
        MVN_CMD="./mvnw"
        echo -e "${GREEN}✓ 使用项目Maven Wrapper${NC}"
    elif command -v mvn &> /dev/null; then
        MVN_CMD="mvn"
        echo -e "${GREEN}✓ 使用系统Maven${NC}"
    else
        echo -e "${RED}❌ Maven未安装且无Maven Wrapper${NC}"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js未安装${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✓ Node.js版本: $NODE_VERSION${NC}"
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm未安装${NC}"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✓ npm版本: $NPM_VERSION${NC}"
    
    echo -e "${GREEN}✓ 环境检查通过${NC}"
}

# 启动后端服务
start_backend() {
    echo -e "${BLUE}启动后端服务...${NC}"
    
    # 设置开发环境变量
    export SPRING_PROFILES_ACTIVE=dev
    
    # 启动后端
    $MVN_CMD spring-boot:run &
    BACKEND_PID=$!
    
    echo -e "${YELLOW}等待后端服务启动...${NC}"
    
    # 等待后端启动（最多等待60秒）
    for i in {1..60}; do
        if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 后端服务启动成功 (耗时: ${i}秒)${NC}"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo -e "${RED}❌ 后端服务启动超时${NC}"
    return 1
}

# 启动前端服务
start_frontend() {
    echo -e "${BLUE}启动前端服务...${NC}"
    
    cd frontend
    
    # 检查并安装依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        echo -e "${YELLOW}安装/更新前端依赖...${NC}"
        npm install
    fi
    
    # 设置前端环境变量
    export BROWSER=none  # 防止自动打开浏览器
    export PORT=3000     # 确保使用3000端口
    export REACT_APP_API_BASE_URL=http://localhost:8080
    
    # 启动前端开发服务器
    npm start &
    FRONTEND_PID=$!
    
    echo -e "${YELLOW}等待前端服务启动...${NC}"
    
    # 等待前端启动（最多等待30秒）
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 前端服务启动成功 (耗时: ${i}秒)${NC}"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo -e "${YELLOW}⚠ 前端服务可能仍在启动中...${NC}"
    return 0
}

# 显示启动信息
show_info() {
    echo ""
    echo "🎉 开发环境启动完成!"
    echo "================================"
    echo -e "${GREEN}🚀 后端服务: http://localhost:8080${NC}"
    echo -e "${GREEN}🎨 前端服务: http://localhost:3000${NC}"
    echo -e "${GREEN}🗄️  H2控制台: http://localhost:8080/h2-console${NC}"
    echo -e "${GREEN}📊 健康检查: http://localhost:8080/actuator/health${NC}"
    echo ""
    echo -e "${BLUE}💡 开发提示:${NC}"
    echo "   - 推荐使用前端地址进行开发: http://localhost:3000"
    echo "   - 前端代码修改会自动热重载"
    echo "   - 后端API请求会通过proxy自动转发到8080端口"
    echo "   - 前端控制台日志可在浏览器开发者工具中查看"
    echo "   - 后端日志会在当前终端显示"
    echo ""
    echo -e "${YELLOW}按 Ctrl+C 停止所有服务${NC}"
}

# 清理函数
cleanup() {
    echo ""
    echo -e "${YELLOW}正在停止服务...${NC}"
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        echo -e "${GREEN}✓ 后端服务已停止${NC}"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo -e "${GREEN}✓ 前端服务已停止${NC}"
    fi
    
    echo -e "${GREEN}✓ 所有服务已停止${NC}"
    exit 0
}

# 主函数
main() {
    # 设置信号处理
    trap cleanup INT TERM
    
    # 检查环境
    check_requirements
    
    echo ""
    
    # 启动后端
    if ! start_backend; then
        echo -e "${RED}❌ 后端启动失败${NC}"
        exit 1
    fi
    
    echo ""
    
    # 启动前端
    if ! start_frontend; then
        echo -e "${RED}❌ 前端启动失败${NC}"
        cleanup
        exit 1
    fi
    
    # 显示信息
    show_info
    
    # 等待用户中断
    wait
}

# 运行主函数
main "$@"
