#!/bin/bash

echo "=== PMI Task 82 日志诊断报告 ==="
echo "执行时间: $(date)"
echo ""

# 1. 检查应用进程
echo "1. 检查ZoomBus应用进程:"
ps aux | grep java | grep -v grep || echo "未找到Java进程"
echo ""

# 2. 检查日志文件位置
echo "2. 查找日志文件:"
find /root -name "*.log" -type f 2>/dev/null | head -10
echo ""

# 3. 检查应用目录
echo "3. 检查应用目录结构:"
ls -la /root/zoombus/ 2>/dev/null || echo "应用目录不存在"
echo ""

# 4. 检查可能的日志位置
echo "4. 检查可能的日志位置:"
for logdir in "/root/zoombus" "/root/zoombus/logs" "/var/log" "/tmp"; do
    echo "检查目录: $logdir"
    ls -la $logdir/*.log 2>/dev/null || echo "  无日志文件"
done
echo ""

# 5. 检查系统日志中的相关信息
echo "5. 检查系统日志中的Java应用信息:"
journalctl -u zoombus --no-pager -n 20 2>/dev/null || echo "未找到zoombus服务"
echo ""

# 6. 检查最近的系统日志
echo "6. 检查最近的系统日志(包含zoombus):"
journalctl --no-pager -n 50 | grep -i zoombus || echo "系统日志中未找到zoombus相关信息"
echo ""

# 7. 检查Docker容器(如果使用Docker)
echo "7. 检查Docker容器:"
docker ps | grep zoombus 2>/dev/null || echo "未使用Docker或无zoombus容器"
echo ""

# 8. 检查端口占用
echo "8. 检查8080端口占用:"
netstat -tlnp | grep :8080 || ss -tlnp | grep :8080 || echo "8080端口未被占用"
echo ""

# 9. 检查最近的应用启动日志
echo "9. 检查最近的应用启动相关日志:"
if [ -f "/root/zoombus/nohup.out" ]; then
    echo "nohup.out文件内容(最后50行):"
    tail -50 /root/zoombus/nohup.out
elif [ -f "/root/zoombus/application.log" ]; then
    echo "application.log文件内容(最后50行):"
    tail -50 /root/zoombus/application.log
else
    echo "未找到标准的应用日志文件"
fi
echo ""

# 10. 检查数据库连接
echo "10. 测试数据库连接:"
mysql -u root -p'Nslcp@2024' -e "SELECT NOW() as current_time;" 2>/dev/null && echo "数据库连接正常" || echo "数据库连接失败"
echo ""

echo "=== 诊断完成 ==="
