#!/bin/bash

echo "=== PMI Task 82 诊断报告 ==="
echo "执行时间: $(date)"
echo ""

# 1. 检查Task 82的基本信息
echo "1. 检查Task 82基本信息:"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT 
    id, 
    pmi_window_id, 
    task_type, 
    scheduled_time, 
    actual_execution_time, 
    status, 
    retry_count, 
    error_message,
    created_at,
    updated_at
FROM t_pmi_schedule_window_tasks 
WHERE id = 82;" 2>/dev/null

echo ""

# 2. 检查对应的PMI窗口信息
echo "2. 检查PMI窗口信息 (Window ID: 2053):"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT 
    id,
    schedule_id,
    start_datetime,
    end_datetime,
    status,
    actual_start_time,
    actual_end_time,
    created_at
FROM t_pmi_schedule_windows 
WHERE id = 2053;" 2>/dev/null

echo ""

# 3. 检查PMI记录信息
echo "3. 检查PMI记录信息 (PMI: 9271906174):"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT 
    p.id,
    p.pmi_number,
    p.status,
    p.billing_mode,
    p.remaining_duration_minutes,
    u.username
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
WHERE p.pmi_number = '9271906174';" 2>/dev/null

echo ""

# 4. 检查相关的所有任务
echo "4. 检查窗口2053的所有任务:"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT 
    id,
    task_type,
    scheduled_time,
    actual_execution_time,
    status,
    retry_count,
    error_message
FROM t_pmi_schedule_window_tasks 
WHERE pmi_window_id = 2053
ORDER BY id;" 2>/dev/null

echo ""

# 5. 检查应用是否正在运行
echo "5. 检查ZoomBus应用状态:"
if pgrep -f "zoombus" > /dev/null; then
    echo "✓ ZoomBus应用正在运行"
    echo "进程信息:"
    ps aux | grep zoombus | grep -v grep
else
    echo "✗ ZoomBus应用未运行"
fi

echo ""

# 6. 检查应用配置
echo "6. 检查精准调度配置:"
if [ -f "/root/zoombus/src/main/resources/application-pmi-tasks.yml" ]; then
    echo "精准调度配置:"
    grep -A 5 "enable-precise-scheduling" /root/zoombus/src/main/resources/application-pmi-tasks.yml 2>/dev/null || echo "配置文件不存在或无法读取"
else
    echo "配置文件不存在"
fi

echo ""

# 7. 检查最近的应用日志
echo "7. 检查最近的应用日志 (Task 82相关):"
if [ -f "/root/zoombus/zoombus.log" ]; then
    echo "最近的Task 82相关日志:"
    grep -i "task.*82\|82.*task" /root/zoombus/zoombus.log | tail -10 2>/dev/null || echo "未找到相关日志"
elif [ -f "/root/zoombus/logs/zoombus-application.log" ]; then
    echo "最近的Task 82相关日志:"
    grep -i "task.*82\|82.*task" /root/zoombus/logs/zoombus-application.log | tail -10 2>/dev/null || echo "未找到相关日志"
else
    echo "未找到日志文件"
fi

echo ""

# 8. 检查系统时间
echo "8. 系统时间信息:"
echo "当前时间: $(date)"
echo "时区: $(timedatectl show --property=Timezone --value 2>/dev/null || echo 'Unknown')"

echo ""

# 9. 检查数据库连接
echo "9. 数据库连接测试:"
mysql -u root -p'Nslcp@2024' -e "SELECT NOW() as current_time, VERSION() as mysql_version;" 2>/dev/null && echo "✓ 数据库连接正常" || echo "✗ 数据库连接失败"

echo ""

# 10. 检查是否有其他相关任务
echo "10. 检查最近的PMI任务执行情况:"
mysql -u root -p'Nslcp@2024' zoombus -e "
SELECT 
    id,
    task_type,
    scheduled_time,
    actual_execution_time,
    status,
    TIMESTAMPDIFF(MINUTE, scheduled_time, NOW()) as delay_minutes
FROM t_pmi_schedule_window_tasks 
WHERE scheduled_time >= '2025-08-27 00:00:00'
ORDER BY id DESC
LIMIT 10;" 2>/dev/null

echo ""
echo "=== 诊断完成 ==="
