version: '3.8'

services:
  zoombus:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - DB_USERNAME=zoombus
      - DB_PASSWORD=zoombus123
      - ZOOM_ACCOUNT_ID=${ZOOM_ACCOUNT_ID}
      - ZOOM_CLIENT_ID=${ZOOM_CLIENT_ID}
      - ZOOM_CLIENT_SECRET=${ZOOM_CLIENT_SECRET}
      - ZOOM_WEBHOOK_SECRET=${ZOOM_WEBHOOK_SECRET}
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - zoombus-network

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=zoombus
      - MYSQL_USER=zoombus
      - MYSQL_PASSWORD=zoombus123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - zoombus-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - zoombus
    restart: unless-stopped
    networks:
      - zoombus-network

volumes:
  mysql_data:

networks:
  zoombus-network:
    driver: bridge
