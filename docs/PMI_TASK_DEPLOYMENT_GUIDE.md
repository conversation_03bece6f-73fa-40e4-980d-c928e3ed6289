# PMI窗口精准定时任务系统部署指南

## 概述

本文档描述了如何部署和配置PMI窗口精准定时任务管理系统，该系统提供了从轮询机制到精准调度机制的平滑迁移方案。

## 系统要求

- Java 8+
- Spring Boot 2.x+
- MySQL 5.7+
- Redis（可选，用于缓存）

## 部署步骤

### 1. 数据库迁移

执行以下SQL脚本创建必要的数据库表：

```sql
-- 执行 V20250122_001__create_pmi_schedule_window_tasks_table.sql
-- 该脚本会创建 t_pmi_schedule_window_tasks 表并扩展现有的 t_pmi_schedule_windows 表
```

### 2. 配置文件设置

在 `application.yml` 或 `application.properties` 中添加以下配置：

```yaml
# PMI任务调度配置
pmi:
  task:
    scheduling:
      # 是否启用精准定时任务机制（默认关闭，使用轮询机制）
      enable-precise-scheduling: false
      
      # 是否启用任务监控
      enable-task-monitoring: true
      
      # 任务清理配置
      cleanup:
        enabled: true
        retention-days: 30
        cron-expression: "0 0 2 * * ?"
      
      # 任务重试配置
      retry:
        max-retry-count: 3
        retry-interval-minutes: 5
        exponential-backoff: true
      
      # 性能配置
      performance:
        batch-size: 100
        thread-pool-size: 10
        task-timeout-minutes: 30
        enable-task-cache: true
        cache-expiration-minutes: 10
```

### 3. 分阶段部署策略

#### 阶段1：基础部署（保持轮询机制）

1. 部署新代码，但保持 `enable-precise-scheduling: false`
2. 验证系统正常运行，轮询机制继续工作
3. 观察日志，确认新组件正常初始化

#### 阶段2：测试环境验证

1. 在测试环境中设置 `enable-precise-scheduling: true`
2. 创建测试PMI计划，验证精准调度功能
3. 测试管理台功能，确认任务监控正常

#### 阶段3：生产环境切换

1. 选择业务低峰期进行切换
2. 设置 `enable-precise-scheduling: true`
3. 重启应用服务
4. 监控系统运行状态

## 配置说明

### 核心配置项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `enable-precise-scheduling` | `false` | 是否启用精准调度机制 |
| `enable-task-monitoring` | `true` | 是否启用任务监控 |
| `cleanup.enabled` | `true` | 是否启用自动清理 |
| `cleanup.retention-days` | `30` | 任务记录保留天数 |
| `retry.max-retry-count` | `3` | 最大重试次数 |
| `performance.thread-pool-size` | `10` | 线程池大小 |

### 环境特定配置

#### 开发环境
```yaml
pmi.task.scheduling:
  enable-precise-scheduling: true
  enable-task-monitoring: true
  cleanup.retention-days: 7
```

#### 测试环境
```yaml
pmi.task.scheduling:
  enable-precise-scheduling: true
  enable-task-monitoring: true
  cleanup.retention-days: 14
```

#### 生产环境
```yaml
pmi.task.scheduling:
  enable-precise-scheduling: false  # 初始部署时保持false
  enable-task-monitoring: true
  cleanup.retention-days: 30
  performance.thread-pool-size: 20  # 根据服务器性能调整
```

## 监控和告警

### 关键指标

1. **任务执行成功率**
   - 指标：`pmi.task.success.rate`
   - 阈值：< 95% 告警

2. **任务执行延迟**
   - 指标：`pmi.task.execution.delay`
   - 阈值：> 5分钟告警

3. **调度器健康状态**
   - 指标：`pmi.scheduler.health`
   - 阈值：状态异常时告警

### 日志监控

重要日志关键词：
- `PMI窗口任务创建成功`
- `PMI窗口任务执行成功`
- `任务执行失败`
- `调度器状态异常`

## 故障排查

### 常见问题

#### 1. 任务不执行
**症状**：PMI窗口到时间不开启/关闭
**排查步骤**：
1. 检查 `enable-precise-scheduling` 配置
2. 查看任务表 `t_pmi_schedule_window_tasks` 中的任务状态
3. 检查调度器健康状态
4. 查看应用日志中的错误信息

#### 2. 任务重复执行
**症状**：同一个窗口被多次开启/关闭
**排查步骤**：
1. 检查是否有多个应用实例
2. 确认数据库事务隔离级别
3. 检查任务键的唯一性

#### 3. 内存泄漏
**症状**：应用内存持续增长
**排查步骤**：
1. 检查 `ScheduledFuture` 对象是否正确清理
2. 启用任务清理功能
3. 调整任务缓存配置

### 应急处理

#### 回滚到轮询机制
如果精准调度出现问题，可以快速回滚：

1. 设置 `enable-precise-scheduling: false`
2. 重启应用
3. 轮询机制将自动接管

#### 手动清理任务
如果需要手动清理异常任务：

```sql
-- 取消所有待执行的任务
UPDATE t_pmi_schedule_window_tasks 
SET status = 'CANCELLED' 
WHERE status = 'SCHEDULED' AND scheduled_time < NOW();

-- 清理过期的任务记录
DELETE FROM t_pmi_schedule_window_tasks 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY) 
AND status IN ('COMPLETED', 'CANCELLED', 'FAILED');
```

## 性能调优

### 线程池配置
根据服务器性能和并发需求调整：
```yaml
pmi.task.scheduling.performance:
  thread-pool-size: 20  # CPU核心数 * 2
  task-timeout-minutes: 30
```

### 批处理优化
对于大量任务的场景：
```yaml
pmi.task.scheduling.performance:
  batch-size: 200
  enable-task-cache: true
  cache-expiration-minutes: 5
```

## 安全考虑

1. **权限控制**：确保只有授权用户可以访问任务管理API
2. **数据加密**：敏感配置信息使用加密存储
3. **审计日志**：记录所有任务操作的审计信息

## 备份和恢复

### 数据备份
定期备份以下数据：
- `t_pmi_schedule_window_tasks` 表
- `t_pmi_schedule_windows` 表
- 应用配置文件

### 恢复流程
1. 恢复数据库表
2. 恢复配置文件
3. 重启应用服务
4. 验证任务调度功能

## 联系支持

如遇到部署问题，请联系技术支持团队，并提供：
- 错误日志
- 配置文件
- 系统环境信息
- 问题复现步骤
