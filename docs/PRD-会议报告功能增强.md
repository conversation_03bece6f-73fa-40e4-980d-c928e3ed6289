# PRD：会议报告功能增强

## 1. 产品概述

### 1.1 背景
当前系统已实现了会议级别的会议报告查询和展示功能，主要通过"用户-会议管理-会议报告"页面和user-frontend的会议主持页面展示。现需要进一步完善会议报告体系，增加PMI级别的会议报告展示和全量会议报告管理功能。

### 1.2 目标
1. 实现PMI级别的会议报告聚合展示
2. 建立全量会议报告管理功能
3. 完善会议报告与业务实体的关联关系
4. 提升会议数据的可观测性和管理效率

### 1.3 核心价值
- **数据聚合**：PMI维度的会议报告聚合，便于PMI使用情况分析
- **全局视图**：提供系统级别的会议报告全景视图
- **业务关联**：会议报告与会议管理、PMI管理的深度集成
- **多维展示**：支持会议、PMI、账号等多个维度的报告查看

## 2. 功能需求

### 2.1 PMI级别会议报告

#### 2.1.1 功能描述
PMI（Personal Meeting ID）作为固定会议室，可能由不同的Zoom用户账号在不同时间召开会议，需要提供PMI维度的会议报告聚合视图。

#### 2.1.2 核心特性
- **多账号聚合**：一个PMI可能对应多个Zoom用户账号的会议记录
- **时间序列展示**：按时间顺序展示PMI下的所有会议报告
- **使用统计**：PMI的总使用时长、会议次数、参会人数等统计
- **账号分布**：展示PMI在不同Zoom账号下的使用情况

#### 2.1.3 数据来源
- **主表**：`t_pmi_records` - PMI基础信息
- **关联表**：`t_meeting_reports` - 会议报告详情
- **关联字段**：通过PMI号码关联会议报告

#### 2.1.4 展示页面
1. **PMI管理页面增强**：在现有PMI管理页面添加"会议报告"操作
2. **user-frontend PMI控制台**：在PMI控制台页面展示历史会议报告
3. **独立PMI报告页面**：专门的PMI会议报告详情页面

### 2.2 全量会议报告管理

#### 2.2.1 功能描述
提供系统级别的会议报告管理功能，支持按Zoom主账号、用户账号、时间等维度查看和管理所有会议报告。

#### 2.2.2 核心特性
- **多维度筛选**：支持按账号、时间、会议类型、PMI等维度筛选
- **批量操作**：支持批量导出、删除等操作
- **统计分析**：提供会议报告的统计分析功能
- **关联跳转**：支持跳转到对应的会议管理或PMI管理页面

#### 2.2.3 数据来源
- **主表**：`t_meeting_reports` - 会议报告
- **关联表**：
  - `t_meetings` - 会议信息
  - `t_pmi_records` - PMI信息
  - `t_zoom_accounts` - Zoom用户账号
  - `t_zoom_auth` - Zoom主账号

#### 2.2.4 展示页面
- **新增菜单**：管理台新增"会议报告"一级菜单
- **报告列表页**：全量会议报告列表和筛选功能
- **报告详情页**：单个会议报告的详细信息

### 2.3 业务关联增强

#### 2.3.1 会议管理页面增强
- **报告状态显示**：在会议列表中显示会议报告状态
- **快速查看**：支持直接在会议管理页面查看会议报告
- **报告触发**：支持手动触发会议报告生成

#### 2.3.2 PMI管理页面增强
- **使用统计**：显示PMI的会议报告统计信息
- **报告列表**：展示PMI下的所有会议报告
- **趋势分析**：PMI使用趋势和分析图表

#### 2.3.3 user-frontend增强
- **会议主持页面**：显示当前会议的历史报告（如果是重复会议）
- **PMI控制台页面**：显示PMI的会议报告历史和统计

## 3. 技术需求

### 3.1 数据模型增强

#### 3.1.1 会议报告关联字段
```sql
-- t_meeting_reports 表增强
ALTER TABLE t_meeting_reports ADD COLUMN pmi_number VARCHAR(20) COMMENT 'PMI号码';
ALTER TABLE t_meeting_reports ADD COLUMN meeting_type ENUM('SCHEDULED', 'PMI', 'INSTANT') COMMENT '会议类型';
ALTER TABLE t_meeting_reports ADD COLUMN zoom_auth_id BIGINT COMMENT 'Zoom主账号ID';

-- 添加索引
CREATE INDEX idx_meeting_reports_pmi ON t_meeting_reports(pmi_number);
CREATE INDEX idx_meeting_reports_type ON t_meeting_reports(meeting_type);
CREATE INDEX idx_meeting_reports_auth ON t_meeting_reports(zoom_auth_id);
```

#### 3.1.2 PMI统计表
```sql
-- PMI会议统计表
CREATE TABLE t_pmi_meeting_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    pmi_number VARCHAR(20) NOT NULL COMMENT 'PMI号码',
    total_meetings INT DEFAULT 0 COMMENT '总会议次数',
    total_duration_minutes INT DEFAULT 0 COMMENT '总时长(分钟)',
    total_participants INT DEFAULT 0 COMMENT '总参会人数',
    last_meeting_time DATETIME COMMENT '最后会议时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_pmi_number (pmi_number)
);
```

### 3.2 API接口设计

#### 3.2.1 PMI会议报告接口
```
GET /api/pmi-reports/{pmiNumber}
- 获取指定PMI的所有会议报告

GET /api/pmi-reports/{pmiNumber}/stats
- 获取PMI的会议统计信息

GET /api/pmi-reports/{pmiNumber}/timeline
- 获取PMI的会议时间线
```

#### 3.2.2 全量会议报告接口
```
GET /api/meeting-reports/all
- 获取全量会议报告列表（支持分页和筛选）

GET /api/meeting-reports/stats/overview
- 获取会议报告总体统计

POST /api/meeting-reports/batch/export
- 批量导出会议报告

DELETE /api/meeting-reports/batch
- 批量删除会议报告
```

#### 3.2.3 关联查询接口
```
GET /api/meetings/{meetingId}/reports
- 获取指定会议的所有报告（支持重复会议）

GET /api/zoom-accounts/{accountId}/reports
- 获取指定Zoom账号的所有会议报告
```

### 3.3 前端组件设计

#### 3.3.1 PMI报告组件
- **PmiReportList**：PMI会议报告列表组件
- **PmiReportStats**：PMI统计信息组件
- **PmiReportTimeline**：PMI会议时间线组件

#### 3.3.2 全量报告组件
- **MeetingReportTable**：会议报告表格组件
- **ReportFilterPanel**：报告筛选面板组件
- **ReportStatsCard**：报告统计卡片组件

#### 3.3.3 关联展示组件
- **MeetingReportBadge**：会议报告状态徽章
- **QuickReportViewer**：快速报告查看器
- **ReportActionButton**：报告操作按钮

## 4. 用户界面设计

### 4.1 管理台界面

#### 4.1.1 新增"会议报告"菜单
```
管理台
├── 用户管理
├── 会议管理
├── PMI管理
├── 会议报告 (新增)
│   ├── 报告列表
│   ├── 统计分析
│   └── 导出管理
└── 系统设置
```

#### 4.1.2 会议报告列表页面
- **筛选区域**：时间范围、Zoom账号、会议类型、PMI等筛选条件
- **列表区域**：会议报告列表，包含基本信息和操作按钮
- **统计区域**：总体统计信息展示
- **操作区域**：批量操作按钮

#### 4.1.3 PMI管理页面增强
- **PMI列表**：增加"会议报告"操作列
- **PMI详情**：增加会议报告统计和列表展示
- **统计图表**：PMI使用趋势图表

### 4.2 用户前端界面

#### 4.2.1 会议主持页面增强
- **历史报告**：显示该会议的历史报告（如果是重复会议）
- **快速查看**：支持快速查看最近的会议报告
- **报告状态**：显示当前会议的报告生成状态

#### 4.2.2 PMI控制台页面增强
- **使用统计**：PMI的使用统计信息
- **报告历史**：PMI的会议报告历史列表
- **趋势图表**：PMI使用趋势图表

## 5. 实施计划

### 5.1 第一阶段：数据模型和基础接口
- [ ] 数据库表结构调整
- [ ] 基础API接口开发
- [ ] 数据关联逻辑实现

### 5.2 第二阶段：PMI报告功能
- [ ] PMI会议报告聚合逻辑
- [ ] PMI管理页面增强
- [ ] user-frontend PMI控制台增强

### 5.3 第三阶段：全量报告管理
- [ ] 会议报告管理页面开发
- [ ] 筛选和搜索功能实现
- [ ] 批量操作功能开发

### 5.4 第四阶段：业务关联和优化
- [ ] 会议管理页面集成
- [ ] user-frontend会议主持页面集成
- [ ] 性能优化和测试

## 6. 验收标准

### 6.1 功能验收
- [ ] PMI维度的会议报告正确聚合和展示
- [ ] 全量会议报告管理功能完整可用
- [ ] 业务页面的报告关联功能正常
- [ ] 数据统计和分析功能准确

### 6.2 性能验收
- [ ] 报告列表页面加载时间 < 3秒
- [ ] 大数据量筛选响应时间 < 5秒
- [ ] 统计计算准确性 100%

### 6.3 用户体验验收
- [ ] 界面操作流畅，无明显卡顿
- [ ] 筛选和搜索功能易用
- [ ] 数据展示清晰直观
- [ ] 移动端适配良好

## 7. 风险评估

### 7.1 技术风险
- **数据量风险**：大量会议报告可能影响查询性能
- **关联复杂性**：多表关联查询的复杂性和性能问题

### 7.2 业务风险
- **数据一致性**：PMI和会议报告的数据一致性保证
- **历史数据**：现有数据的迁移和兼容性问题

### 7.3 缓解措施
- 实施数据库索引优化和查询优化
- 建立数据一致性检查机制
- 制定详细的数据迁移方案
- 实施分阶段发布和灰度测试

## 8. 用例场景

### 8.1 PMI报告查看场景

#### 场景1：PMI管理员查看PMI使用情况
**用户角色**：PMI管理员
**操作流程**：
1. 进入PMI管理页面
2. 点击某个PMI的"会议报告"按钮
3. 查看该PMI的所有会议记录和统计信息
4. 分析PMI的使用频率和时长分布

**预期结果**：
- 显示PMI下所有会议的时间线
- 展示不同Zoom账号的使用分布
- 提供总时长、会议次数等统计数据

#### 场景2：用户在PMI控制台查看历史
**用户角色**：PMI使用者
**操作流程**：
1. 进入user-frontend的PMI控制台
2. 查看"历史会议"或"会议报告"区域
3. 浏览PMI的历史会议记录
4. 点击查看具体会议的详细报告

**预期结果**：
- 显示用户有权限查看的PMI会议历史
- 提供会议报告的快速预览
- 支持下载或分享会议报告

### 8.2 全量报告管理场景

#### 场景3：系统管理员进行报告统计分析
**用户角色**：系统管理员
**操作流程**：
1. 进入"会议报告"菜单
2. 设置时间范围筛选条件
3. 按Zoom主账号分组查看统计
4. 导出报告数据进行进一步分析

**预期结果**：
- 提供多维度的统计图表
- 支持灵活的筛选和分组
- 提供数据导出功能

#### 场景4：会议管理员查看账号使用情况
**用户角色**：会议管理员
**操作流程**：
1. 在会议管理页面查看会议列表
2. 注意到某些会议有报告状态标识
3. 点击查看具体会议的报告详情
4. 分析会议质量和参与情况

**预期结果**：
- 会议列表显示报告状态徽章
- 支持快速跳转到报告详情
- 提供会议质量分析数据

### 8.3 业务关联场景

#### 场景5：重复会议的报告关联
**用户角色**：会议主持人
**操作流程**：
1. 进入会议主持页面（重复会议）
2. 查看"历史会议报告"区域
3. 对比不同期次的会议数据
4. 分析会议效果的变化趋势

**预期结果**：
- 显示同一重复会议的所有历史报告
- 提供数据对比和趋势分析
- 支持快速查看和下载历史报告

## 9. 数据流设计

### 9.1 PMI报告数据流
```
PMI创建 → 会议召开 → 会议结束 → 报告生成 → PMI统计更新
    ↓           ↓           ↓           ↓           ↓
t_pmi_records → Zoom API → Webhook → t_meeting_reports → t_pmi_meeting_stats
```

### 9.2 全量报告数据流
```
多个数据源 → 数据聚合 → 筛选处理 → 前端展示
    ↓           ↓           ↓           ↓
t_meeting_reports + t_meetings + t_pmi_records → 关联查询 → API接口 → 管理界面
```

### 9.3 实时统计数据流
```
会议报告生成 → 触发统计更新 → 缓存刷新 → 前端实时更新
    ↓               ↓               ↓           ↓
Webhook事件 → 异步统计任务 → Redis缓存 → WebSocket推送
```

## 10. 性能优化策略

### 10.1 数据库优化
- **分区表**：按时间对会议报告表进行分区
- **索引优化**：为常用查询字段建立复合索引
- **读写分离**：统计查询使用只读副本
- **数据归档**：定期归档历史数据

### 10.2 缓存策略
- **统计缓存**：PMI和账号级别的统计数据缓存
- **查询缓存**：常用筛选条件的查询结果缓存
- **页面缓存**：静态统计页面的缓存

### 10.3 前端优化
- **虚拟滚动**：大数据量列表的虚拟滚动
- **懒加载**：图表和详情数据的懒加载
- **分页优化**：智能分页和预加载

## 11. 监控和告警

### 11.1 业务监控
- **报告生成成功率**：监控会议报告的生成成功率
- **数据一致性**：监控PMI统计数据的一致性
- **查询性能**：监控报告查询的响应时间

### 11.2 技术监控
- **API响应时间**：监控各个报告API的响应时间
- **数据库性能**：监控数据库查询的执行时间
- **缓存命中率**：监控缓存的命中率和效果

### 11.3 告警机制
- **报告生成失败**：报告生成失败时发送告警
- **性能异常**：查询响应时间超阈值时告警
- **数据异常**：统计数据异常时告警

## 12. 安全考虑

### 12.1 数据权限
- **账号隔离**：不同Zoom主账号的数据严格隔离
- **用户权限**：用户只能查看有权限的PMI和会议报告
- **敏感信息**：会议内容等敏感信息的脱敏处理

### 12.2 操作权限
- **查看权限**：基于角色的报告查看权限控制
- **导出权限**：数据导出功能的权限控制
- **删除权限**：报告删除操作的严格权限控制

### 12.3 数据安全
- **传输加密**：API数据传输的HTTPS加密
- **存储加密**：敏感数据的数据库加密存储
- **审计日志**：重要操作的审计日志记录
