# 会议管理API文档

本文档基于Zoom API官方文档完善了会议管理功能，包括创建、更新、删除会议的完整实现。

## 📋 API概览

### 1. 创建会议
- **接口**: `POST /api/meetings`
- **Zoom API**: `POST /users/{userId}/meetings`
- **功能**: 创建即时会议或周期性会议

### 2. 更新会议
- **接口**: `PATCH /api/meetings/{id}`
- **Zoom API**: `PATCH /meetings/{meetingId}`
- **功能**: 更新会议信息

### 3. 删除会议
- **接口**: `DELETE /api/meetings/{id}`
- **Zoom API**: `DELETE /meetings/{meetingId}`
- **功能**: 删除会议或特定occurrence

## 🚀 新增功能

### 1. 完善的会议设置支持

#### 音视频设置
```json
{
  "settings": {
    "host_video": true,
    "participant_video": true,
    "audio": "voip",
    "auto_recording": "none"
  }
}
```

#### 会议控制设置
```json
{
  "settings": {
    "join_before_host": false,
    "jbh_time": 0,
    "mute_upon_entry": true,
    "watermark": false,
    "use_pmi": false
  }
}
```

#### 安全设置
```json
{
  "settings": {
    "approval_type": 2,
    "enforce_login": false,
    "enforce_login_domains": "",
    "alternative_hosts": "",
    "waiting_room": false,
    "meeting_authentication": false
  }
}
```

### 2. 跟踪字段支持
```json
{
  "tracking_fields": [
    {
      "field": "department",
      "value": "IT"
    },
    {
      "field": "project",
      "value": "ZoomBus"
    }
  ]
}
```

### 3. 高级删除选项
```json
{
  "schedule_for_reminder": "meeting",
  "cancel_meeting_reminder": true,
  "occurrence_id": "1753547340000"
}
```

## 🔧 时区处理改进

### 问题修复
1. **创建会议时区转换**: 正确将东八区时间转换为UTC时间发送给Zoom API
2. **occurrence时间转换**: 正确处理周期性会议实例的时间显示
3. **星期几格式转换**: 正确转换我们系统的星期几格式到Zoom API格式

### 转换示例
```javascript
// 用户选择: 东八区 2025-07-21T15:00
// 发送给Zoom: 2025-07-21T07:00:00.000Z (UTC)
// 星期几转换: "1,3,5" -> "2,4,6" (周一三五)
```

## 📝 API接口详情

### 创建会议
```http
POST /api/meetings
Content-Type: application/json

{
  "topic": "测试会议",
  "agenda": "讨论项目进展",
  "startTime": "2025-07-21T15:00:00",
  "duration": 60,
  "password": "123456",
  "type": "SCHEDULED",
  "recurrenceType": "WEEKLY",
  "weeklyDays": "1,3,5",
  "endType": "BY_DATE",
  "endDateTime": "2025-08-21T15:00:00",
  "settings": {
    "host_video": true,
    "participant_video": true,
    "mute_upon_entry": true,
    "waiting_room": false
  }
}
```

### 更新会议
```http
PATCH /api/meetings/123
Content-Type: application/json

{
  "topic": "更新后的会议主题",
  "startTime": "2025-07-21T16:00:00",
  "duration": 90,
  "settings": {
    "waiting_room": true,
    "mute_upon_entry": false
  }
}
```

### 删除会议
```http
DELETE /api/meetings/123
```

### 高级删除（带参数）
```http
DELETE /api/meetings/123/advanced
Content-Type: application/json

{
  "schedule_for_reminder": "occurrence",
  "cancel_meeting_reminder": true,
  "occurrence_id": "1753547340000"
}
```

## 🎯 支持的会议类型

### 1. 即时会议 (INSTANT)
- 立即开始的会议
- 无需预约

### 2. 预约会议 (SCHEDULED)
- 指定时间开始的会议
- 支持周期性重复

### 3. 周期性会议 (RECURRING)
- 每日重复
- 每周重复（支持指定星期几）
- 每月重复（支持按日期或按星期）

## 🔒 权限要求

确保Zoom应用具有以下权限：
- `meeting:write:meeting:admin` - 创建和更新会议
- `meeting:delete:meeting:admin` - 删除会议
- `meeting:read:meeting:admin` - 读取会议信息

## ⚠️ 注意事项

1. **时区处理**: 所有时间都按东八区处理，自动转换为UTC发送给Zoom API
2. **星期几格式**: 系统内部使用0-6（周日-周六），自动转换为Zoom API的1-7格式
3. **occurrence删除**: 删除特定occurrence时需要提供occurrence_id
4. **权限检查**: 删除会议时会检查应用权限，如权限不足会返回相应错误信息

## 🧪 测试建议

1. **创建不同类型的会议**测试基本功能
2. **测试周期性会议**的时间和星期几设置
3. **验证时区转换**是否正确
4. **测试更新会议**的各种设置
5. **测试删除会议**的不同选项
