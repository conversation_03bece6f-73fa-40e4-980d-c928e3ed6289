# 网络环境自动检测和代理配置

## 概述

ZoomBus系统集成了智能的网络环境检测功能，能够在应用启动时自动检测当前环境的外网IP，并根据配置自动设置代理，确保Zoom API在特定网络环境下能够正常访问。

## 功能特性

### 🌐 自动IP检测
- 支持多个IP检测服务，确保检测的可靠性
- 自动重试机制，提高检测成功率
- 详细的检测日志，便于问题排查

### 🔧 智能代理配置
- 基于外网IP自动判断是否需要配置代理
- 支持HTTP和HTTPS代理
- 自动配置Zoom域名绕过，确保API访问正常

### 📊 实时监控
- 提供Web管理界面
- 支持手动触发检测和配置
- 网络连接测试功能

## 配置说明

### application.yml配置

```yaml
zoombus:
  network:
    auto-detect-enabled: true  # 是否启用自动检测外网IP
    proxy-enabled: true        # 是否启用代理功能
    target-external-ip: "**************"  # 需要使用代理的目标外网IP
    proxy-host: "127.0.0.1"    # 代理服务器地址
    proxy-port: "6690"         # 代理服务器端口
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `auto-detect-enabled` | boolean | true | 是否在启动时自动检测外网IP |
| `proxy-enabled` | boolean | true | 是否启用代理功能 |
| `target-external-ip` | string | "**************" | 需要使用代理的目标外网IP |
| `proxy-host` | string | "127.0.0.1" | 代理服务器地址 |
| `proxy-port` | string | "6690" | 代理服务器端口 |

## 工作原理

### 启动流程

1. **应用启动时**：`NetworkEnvironmentConfig` 自动执行
2. **IP检测**：通过多个外部服务检测当前外网IP
3. **代理判断**：比较当前IP与目标IP，决定是否需要配置代理
4. **代理配置**：如果需要，自动设置系统代理属性
5. **连接测试**：测试Zoom API连接是否正常
6. **日志输出**：输出详细的环境信息

### 代理配置逻辑

```java
// 当检测到的外网IP等于目标IP时，自动配置代理
if (currentExternalIp.equals("**************")) {
    // 设置HTTP代理
    System.setProperty("http.proxyHost", "127.0.0.1");
    System.setProperty("http.proxyPort", "6690");
    
    // 设置HTTPS代理
    System.setProperty("https.proxyHost", "127.0.0.1");
    System.setProperty("https.proxyPort", "6690");
    
    // 配置不使用代理的域名
    System.setProperty("http.nonProxyHosts", "localhost|127.0.0.1|*.local");
}
```

## 使用方法

### 1. 自动模式（推荐）

应用启动时会自动检测和配置，无需手动干预。启动日志示例：

```
🚀 启动网络环境检测和配置...
🌐 开始检测当前环境外网IP...
✅ 检测到外网IP: ************** (通过服务: https://api.ipify.org)
🔧 检测到目标环境IP (**************), 开始配置Zoom API代理...
📋 代理配置详情:
   - 目标环境IP: **************
   - 当前外网IP: **************
   - 代理地址: 127.0.0.1:6690
✅ Zoom API代理配置完成
🔗 测试Zoom API连接...
✅ Zoom API连接测试成功
```

### 2. 手动管理

通过Web管理界面进行手动操作：

1. 访问：`http://localhost:8080/network-environment`
2. 可执行的操作：
   - 检测外网IP
   - 配置代理
   - 清除代理
   - 测试网络连接
   - 查看系统代理配置

### 3. API接口

系统提供RESTful API进行程序化管理：

```bash
# 获取网络环境信息
GET /api/network/environment

# 检测外网IP
POST /api/network/detect-ip

# 配置代理
POST /api/network/configure-proxy

# 清除代理
POST /api/network/clear-proxy

# 测试连接
POST /api/network/test-connection?url=https://api.zoom.us

# 测试Zoom API
POST /api/network/test-zoom-api
```

## 故障排除

### 常见问题

#### 1. IP检测失败
**现象**：启动日志显示"所有IP检测服务都失败"

**原因**：
- 网络连接问题
- 防火墙阻止外部访问
- IP检测服务不可用

**解决方案**：
- 检查网络连接
- 确认防火墙设置
- 手动指定外网IP

#### 2. 代理配置无效
**现象**：配置了代理但Zoom API仍然无法访问

**原因**：
- 代理服务器未启动
- 代理地址或端口错误
- 域名绕过配置问题

**解决方案**：
- 确认代理服务器状态：`curl -x http://127.0.0.1:6690 https://api.zoom.us`
- 检查配置参数
- 查看系统代理属性

#### 3. Zoom API连接失败
**现象**：代理配置成功但连接测试失败

**解决方案**：
1. 检查代理服务器状态
2. 验证代理配置：访问 `/api/network/diagnosis`
3. 手动测试连接：`curl -x http://127.0.0.1:6690 https://api.zoom.us`

### 调试方法

#### 1. 查看详细日志
```bash
# 启动时添加网络调试参数
java -Djava.net.debug=all -jar zoombus.jar
```

#### 2. 检查系统属性
访问 `/api/network/diagnosis` 查看当前系统代理配置

#### 3. 手动测试
```bash
# 测试代理连接
curl -x http://127.0.0.1:6690 https://api.zoom.us

# 测试直连
curl https://api.zoom.us
```

## 最佳实践

### 1. 生产环境配置
- 确保代理服务器的高可用性
- 配置监控告警
- 定期检查网络连接状态

### 2. 开发环境配置
- 启用自动检测功能
- 使用Web管理界面进行调试
- 保留详细的调试日志

### 3. 安全考虑
- 代理服务器访问控制
- 网络流量监控
- 定期更新代理配置

## 扩展功能

### 1. 支持多个目标IP
可以扩展配置支持多个需要代理的IP地址：

```yaml
zoombus:
  network:
    target-external-ips:
      - "**************"
      - "*************"
```

### 2. 代理认证
支持需要认证的代理服务器：

```yaml
zoombus:
  network:
    proxy-username: "username"
    proxy-password: "password"
```

### 3. 自定义IP检测服务
支持配置自定义的IP检测服务：

```yaml
zoombus:
  network:
    ip-check-services:
      - "https://custom-ip-service.com/ip"
      - "https://api.ipify.org"
```

## 总结

网络环境自动检测和代理配置功能为ZoomBus系统提供了智能的网络适配能力，确保在不同网络环境下都能正常访问Zoom API。通过自动化的检测和配置，大大简化了部署和运维工作。
