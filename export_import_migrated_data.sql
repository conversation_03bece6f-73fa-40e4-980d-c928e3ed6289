-- 移植数据导出导入脚本
-- 用于将测试环境的移植数据导入到生产环境
-- 执行日期: 2025-08-20

-- ========================================
-- 第一部分：数据导出（在测试环境执行）
-- ========================================

-- 使用说明：
-- 1. 在测试环境执行以下命令导出数据
-- 2. 将生成的文件传输到生产环境
-- 3. 在生产环境执行第二部分的导入命令

/*
=== 导出命令（在测试环境的命令行执行）===

# 方法1：分别导出各表
mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  --where="1=1" zoombusV t_users > migrated_t_users.sql

mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  --where="1=1" zoombusV t_pmi_records > migrated_t_pmi_records.sql

mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  --where="1=1" zoombusV t_pmi_schedules > migrated_t_pmi_schedules.sql

mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  --where="1=1" zoombusV t_pmi_schedule_windows > migrated_t_pmi_schedule_windows.sql

# 方法2：一次性导出所有表
mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  zoombusV t_users t_pmi_records t_pmi_schedules t_pmi_schedule_windows > migrated_all_tables.sql

# 打包导出文件
tar -czf migrated_data_$(date +%Y%m%d_%H%M%S).tar.gz migrated_*.sql

=== 导入命令（在生产环境的命令行执行）===

# 方法1：分别导入各表（按顺序）
mysql -u root -p zoombusV < migrated_t_users.sql
mysql -u root -p zoombusV < migrated_t_pmi_records.sql
mysql -u root -p zoombusV < migrated_t_pmi_schedules.sql
mysql -u root -p zoombusV < migrated_t_pmi_schedule_windows.sql

# 方法2：一次性导入所有表
mysql -u root -p zoombusV < migrated_all_tables.sql
*/

-- ========================================
-- 第二部分：数据导入（在生产环境执行）
-- ========================================

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- 导入前数据状态检查
SELECT '=== 导入前数据状态检查 ===' as step;

SELECT 
    'Before Import - Table Counts' as check_type,
    (SELECT COUNT(*) FROM t_users) as users_count,
    (SELECT COUNT(*) FROM t_pmi_records) as pmi_records_count,
    (SELECT COUNT(*) FROM t_pmi_schedules) as schedules_count,
    (SELECT COUNT(*) FROM t_pmi_schedule_windows) as windows_count;

-- 备份现有数据（可选）
SELECT '=== 创建备份表 ===' as step;

-- 创建备份表
CREATE TABLE IF NOT EXISTS t_users_backup_$(date +%Y%m%d) AS SELECT * FROM t_users;
CREATE TABLE IF NOT EXISTS t_pmi_records_backup_$(date +%Y%m%d) AS SELECT * FROM t_pmi_records;
CREATE TABLE IF NOT EXISTS t_pmi_schedules_backup_$(date +%Y%m%d) AS SELECT * FROM t_pmi_schedules;
CREATE TABLE IF NOT EXISTS t_pmi_schedule_windows_backup_$(date +%Y%m%d) AS SELECT * FROM t_pmi_schedule_windows;

-- 清空目标表（根据需要选择是否执行）
SELECT '=== 清空目标表（可选） ===' as step;

-- 注意：如果生产环境已有数据，请谨慎执行以下清空操作
-- 建议先备份，然后根据实际情况决定是否清空

-- TRUNCATE TABLE t_pmi_schedule_windows;  -- 先删除子表
-- TRUNCATE TABLE t_pmi_schedules;
-- TRUNCATE TABLE t_pmi_records;
-- TRUNCATE TABLE t_users;

-- 或者删除特定条件的数据
-- DELETE FROM t_pmi_schedule_windows WHERE created_at >= '2025-08-13';
-- DELETE FROM t_pmi_schedules WHERE created_at >= '2025-08-13';
-- DELETE FROM t_pmi_records WHERE created_at >= '2025-08-13';
-- DELETE FROM t_users WHERE created_at >= '2025-08-13';

-- 导入数据
SELECT '=== 开始导入数据 ===' as step;

-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 导入用户数据
SELECT '导入用户数据...' as status;
-- 在命令行执行：mysql -u root -p zoombusV < migrated_t_users.sql

-- 导入PMI记录数据
SELECT '导入PMI记录数据...' as status;
-- 在命令行执行：mysql -u root -p zoombusV < migrated_t_pmi_records.sql

-- 导入PMI计划数据
SELECT '导入PMI计划数据...' as status;
-- 在命令行执行：mysql -u root -p zoombusV < migrated_t_pmi_schedules.sql

-- 导入PMI窗口数据
SELECT '导入PMI窗口数据...' as status;
-- 在命令行执行：mysql -u root -p zoombusV < migrated_t_pmi_schedule_windows.sql

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 导入后数据验证
SELECT '=== 导入后数据验证 ===' as step;

SELECT 
    'After Import - Table Counts' as check_type,
    (SELECT COUNT(*) FROM t_users) as users_count,
    (SELECT COUNT(*) FROM t_pmi_records) as pmi_records_count,
    (SELECT COUNT(*) FROM t_pmi_schedules) as schedules_count,
    (SELECT COUNT(*) FROM t_pmi_schedule_windows) as windows_count;

-- 验证数据完整性
SELECT '=== 数据完整性验证 ===' as step;

-- 验证用户数据
SELECT 
    'Users Validation' as check_type,
    COUNT(*) as total_users,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_users,
    COUNT(CASE WHEN email LIKE '%@temp.com' THEN 1 END) as temp_email_users
FROM t_users;

-- 验证PMI记录数据
SELECT 
    'PMI Records Validation' as check_type,
    COUNT(*) as total_pmi_records,
    COUNT(CASE WHEN billing_mode = 'LONG' THEN 1 END) as long_billing_count,
    COUNT(CASE WHEN billing_mode = 'BY_TIME' THEN 1 END) as by_time_billing_count,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_current_window
FROM t_pmi_records;

-- 验证PMI计划数据
SELECT 
    'PMI Schedules Validation' as check_type,
    COUNT(*) as total_schedules,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_schedules
FROM t_pmi_schedules;

-- 验证PMI窗口数据
SELECT 
    'PMI Windows Validation' as check_type,
    COUNT(*) as total_windows,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_windows,
    COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_windows,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_windows
FROM t_pmi_schedule_windows;

-- 验证关联关系
SELECT '=== 关联关系验证 ===' as step;

-- 验证PMI记录与用户的关联
SELECT 
    'PMI-User Relationship' as check_type,
    COUNT(*) as total_pmi_records,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as has_valid_user,
    COUNT(CASE WHEN u.id IS NULL THEN 1 END) as missing_user
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id;

-- 验证计划与PMI记录的关联
SELECT 
    'Schedule-PMI Relationship' as check_type,
    COUNT(*) as total_schedules,
    COUNT(CASE WHEN p.id IS NOT NULL THEN 1 END) as has_valid_pmi,
    COUNT(CASE WHEN p.id IS NULL THEN 1 END) as missing_pmi
FROM t_pmi_schedules s
LEFT JOIN t_pmi_records p ON s.pmi_record_id = p.id;

-- 验证窗口与计划的关联
SELECT 
    'Window-Schedule Relationship' as check_type,
    COUNT(*) as total_windows,
    COUNT(CASE WHEN s.id IS NOT NULL THEN 1 END) as has_valid_schedule,
    COUNT(CASE WHEN s.id IS NULL THEN 1 END) as missing_schedule
FROM t_pmi_schedule_windows w
LEFT JOIN t_pmi_schedules s ON w.schedule_id = s.id;

-- 验证LONG类型PMI的完整性
SELECT 
    'LONG PMI Completeness' as check_type,
    COUNT(*) as long_pmi_count,
    COUNT(CASE WHEN s.id IS NOT NULL THEN 1 END) as has_schedule,
    COUNT(CASE WHEN w.id IS NOT NULL THEN 1 END) as has_window,
    COUNT(CASE WHEN p.current_window_id IS NOT NULL THEN 1 END) as has_current_window_id
FROM t_pmi_records p
LEFT JOIN t_pmi_schedules s ON p.id = s.pmi_record_id
LEFT JOIN t_pmi_schedule_windows w ON p.id = w.pmi_record_id AND w.status = 'ACTIVE'
WHERE p.billing_mode = 'LONG';

-- 检查数据异常
SELECT '=== 数据异常检查 ===' as step;

-- 检查重复的PMI号码
SELECT 
    'Duplicate PMI Numbers' as check_type,
    COUNT(*) - COUNT(DISTINCT pmi_number) as duplicate_count
FROM t_pmi_records;

-- 检查无效的PMI格式
SELECT 
    'Invalid PMI Format' as check_type,
    COUNT(*) as invalid_count
FROM t_pmi_records
WHERE LENGTH(pmi_number) != 10 OR pmi_number NOT REGEXP '^[0-9]+$';

-- 检查孤立的记录
SELECT 
    'Orphaned Schedules' as check_type,
    COUNT(*) as orphaned_count
FROM t_pmi_schedules s
LEFT JOIN t_pmi_records p ON s.pmi_record_id = p.id
WHERE p.id IS NULL;

SELECT 
    'Orphaned Windows' as check_type,
    COUNT(*) as orphaned_count
FROM t_pmi_schedule_windows w
LEFT JOIN t_pmi_schedules s ON w.schedule_id = s.id
WHERE s.id IS NULL;

-- 提交事务
COMMIT;

-- 导入完成报告
SELECT '=== 导入完成报告 ===' as report;

SELECT 
    '导入项目' as item,
    '导入结果' as result
UNION ALL
SELECT 
    '用户数据' as item,
    CONCAT((SELECT COUNT(*) FROM t_users), ' 条记录') as result
UNION ALL
SELECT 
    'PMI记录' as item,
    CONCAT((SELECT COUNT(*) FROM t_pmi_records), ' 条记录') as result
UNION ALL
SELECT 
    'PMI计划' as item,
    CONCAT((SELECT COUNT(*) FROM t_pmi_schedules), ' 条记录') as result
UNION ALL
SELECT 
    'PMI窗口' as item,
    CONCAT((SELECT COUNT(*) FROM t_pmi_schedule_windows), ' 条记录') as result;

SELECT 'Data import completed successfully!' as final_message;
