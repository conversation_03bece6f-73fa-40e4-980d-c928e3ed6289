#!/usr/bin/env python3
"""
从原始导出文件中提取正确的用户映射关系
"""

import re
import sys

def extract_user_mapping(sql_file):
    """从SQL文件中提取PMI记录ID和用户ID的映射关系"""
    mappings = []
    
    with open(sql_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找INSERT语句
    insert_pattern = r'INSERT INTO `t_pmi_records`.*?VALUES\s+(.*?);'
    match = re.search(insert_pattern, content, re.DOTALL)
    
    if not match:
        print("未找到INSERT语句")
        return mappings
    
    values_part = match.group(1)
    
    # 解析每个记录
    # 使用正则表达式匹配每个记录的括号内容
    record_pattern = r'\(([^)]+)\)'
    records = re.findall(record_pattern, values_part)
    
    for record in records:
        # 分割字段，注意处理引号内的逗号
        fields = []
        current_field = ""
        in_quotes = False
        quote_char = None
        
        i = 0
        while i < len(record):
            char = record[i]
            
            if not in_quotes:
                if char in ["'", '"']:
                    in_quotes = True
                    quote_char = char
                    current_field += char
                elif char == ',':
                    fields.append(current_field.strip())
                    current_field = ""
                else:
                    current_field += char
            else:
                current_field += char
                if char == quote_char:
                    # 检查是否是转义的引号
                    if i + 1 < len(record) and record[i + 1] == quote_char:
                        current_field += record[i + 1]
                        i += 1
                    else:
                        in_quotes = False
                        quote_char = None
            
            i += 1
        
        # 添加最后一个字段
        if current_field.strip():
            fields.append(current_field.strip())
        
        if len(fields) >= 12:  # 确保有足够的字段
            try:
                pmi_id = int(fields[0])  # 第1个字段是ID
                user_id = int(fields[11])  # 第12个字段是user_id
                pmi_number = fields[6].strip("'\"")  # 第7个字段是pmi_number
                
                mappings.append({
                    'pmi_id': pmi_id,
                    'pmi_number': pmi_number,
                    'user_id': user_id
                })
            except (ValueError, IndexError) as e:
                print(f"解析记录时出错: {e}")
                continue
    
    return mappings

def generate_sql_script(mappings):
    """生成修复SQL脚本"""
    
    print("-- 修复PMI记录的正确用户关联关系")
    print("-- 基于原始导出数据的正确映射")
    print("")
    print("USE zoombusV;")
    print("")
    print("-- 开始事务")
    print("START TRANSACTION;")
    print("")
    print("-- 创建临时表存储正确的用户映射")
    print("CREATE TEMPORARY TABLE temp_correct_user_mapping (")
    print("    pmi_record_id BIGINT,")
    print("    pmi_number VARCHAR(20),")
    print("    correct_user_id BIGINT,")
    print("    INDEX idx_pmi_id (pmi_record_id)")
    print(");")
    print("")
    print("-- 插入正确的用户映射关系")
    
    # 分批插入，每批100条记录
    batch_size = 100
    for i in range(0, len(mappings), batch_size):
        batch = mappings[i:i + batch_size]
        
        print("INSERT INTO temp_correct_user_mapping (pmi_record_id, pmi_number, correct_user_id) VALUES")
        
        values = []
        for mapping in batch:
            values.append(f"({mapping['pmi_id']}, '{mapping['pmi_number']}', {mapping['user_id']})")
        
        print(",\n".join(values) + ";")
        print("")
    
    print("-- 更新PMI记录的用户关联（仅更新存在的用户）")
    print("UPDATE t_pmi_records p")
    print("JOIN temp_correct_user_mapping m ON p.id = m.pmi_record_id")
    print("JOIN t_users u ON m.correct_user_id = u.id")
    print("SET p.user_id = m.correct_user_id;")
    print("")
    print("-- 检查修复结果")
    print("SELECT")
    print("    'User Association Fix Result' as check_type,")
    print("    COUNT(*) as total_pmi,")
    print("    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_links,")
    print("    ROUND(COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate")
    print("FROM t_pmi_records p")
    print("LEFT JOIN t_users u ON p.user_id = u.id;")
    print("")
    print("-- 清理临时表")
    print("DROP TEMPORARY TABLE temp_correct_user_mapping;")
    print("")
    print("-- 提交事务")
    print("COMMIT;")
    print("")
    print("SELECT 'User association correction completed successfully!' as final_message;")

def main():
    sql_file = "migration_export_20250820_105304/migrated_t_pmi_records.sql"
    
    print(f"正在解析文件: {sql_file}", file=sys.stderr)
    
    mappings = extract_user_mapping(sql_file)
    
    print(f"提取到 {len(mappings)} 条映射关系", file=sys.stderr)
    print(f"用户ID范围: {min(m['user_id'] for m in mappings)} - {max(m['user_id'] for m in mappings)}", file=sys.stderr)
    print(f"PMI ID范围: {min(m['pmi_id'] for m in mappings)} - {max(m['pmi_id'] for m in mappings)}", file=sys.stderr)
    
    # 生成SQL脚本
    generate_sql_script(mappings)

if __name__ == "__main__":
    main()
