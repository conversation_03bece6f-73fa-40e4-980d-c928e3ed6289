-- 修复跨日窗口的 end_date 计算错误
-- 执行日期: 2025-08-21

USE zoombusV;

-- 1. 检查当前跨日窗口的问题
SELECT '=== 检查跨日窗口问题 ===' as step;

SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    pr.pmi_number,
    CASE 
        WHEN psw.end_time < psw.start_time OR psw.end_time = '00:00:00' THEN 'CROSS_DAY'
        ELSE 'SAME_DAY'
    END as window_type,
    CASE 
        WHEN (psw.end_time < psw.start_time OR psw.end_time = '00:00:00') 
             AND psw.end_date = psw.window_date THEN 'NEEDS_FIX'
        ELSE 'OK'
    END as fix_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.end_time < psw.start_time OR psw.end_time = '00:00:00'
ORDER BY psw.id;

-- 2. 显示窗口2051的详细信息
SELECT '=== 窗口2051修复前状态 ===' as step;

SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    pr.pmi_number,
    pr.billing_mode,
    CASE 
        WHEN psw.end_time < psw.start_time THEN 'CROSS_DAY_WINDOW'
        ELSE 'SAME_DAY_WINDOW'
    END as window_type,
    CASE 
        WHEN psw.end_time < psw.start_time THEN DATE_ADD(psw.window_date, INTERVAL 1 DAY)
        ELSE psw.window_date
    END as correct_end_date
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 2051;

-- 3. 修复所有跨日窗口的 end_date
UPDATE t_pmi_schedule_windows 
SET 
    end_date = DATE_ADD(window_date, INTERVAL 1 DAY),
    updated_at = NOW()
WHERE (end_time < start_time OR end_time = '00:00:00')
AND end_date = window_date;

-- 4. 显示修复结果
SELECT '=== 跨日窗口修复结果 ===' as step;

SELECT 
    'Fixed Cross-Day Windows' as metric,
    ROW_COUNT() as count;

-- 5. 验证窗口2051的修复结果
SELECT '=== 窗口2051修复后状态 ===' as step;

SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    pr.pmi_number,
    pr.billing_mode,
    CASE 
        WHEN psw.end_time < psw.start_time THEN 'CROSS_DAY_WINDOW'
        ELSE 'SAME_DAY_WINDOW'
    END as window_type,
    CASE 
        WHEN psw.end_time < psw.start_time AND psw.end_date = DATE_ADD(psw.window_date, INTERVAL 1 DAY) THEN 'CORRECT'
        WHEN psw.end_time >= psw.start_time AND psw.end_date = psw.window_date THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as date_correctness
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 2051;

-- 6. 检查所有跨日窗口的修复状态
SELECT '=== 所有跨日窗口修复验证 ===' as step;

SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    pr.pmi_number,
    CASE 
        WHEN psw.end_time < psw.start_time THEN 'CROSS_DAY'
        ELSE 'SAME_DAY'
    END as window_type,
    CASE 
        WHEN psw.end_time < psw.start_time AND psw.end_date = DATE_ADD(psw.window_date, INTERVAL 1 DAY) THEN 'CORRECT'
        WHEN psw.end_time >= psw.start_time AND psw.end_date = psw.window_date THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as date_correctness
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.end_time < psw.start_time OR psw.end_time = '00:00:00'
ORDER BY psw.id;

-- 7. 统计修复结果
SELECT '=== 修复统计报告 ===' as step;

SELECT 
    'Total Cross-Day Windows' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE end_time < start_time OR end_time = '00:00:00'

UNION ALL

SELECT 
    'Correctly Fixed Cross-Day Windows' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE (end_time < start_time OR end_time = '00:00:00')
AND end_date = DATE_ADD(window_date, INTERVAL 1 DAY)

UNION ALL

SELECT 
    'Same-Day Windows' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE end_time >= start_time 
AND end_time != '00:00:00'
AND end_date = window_date;

-- 8. 检查窗口关闭逻辑的正确性
SELECT '=== 窗口关闭逻辑验证 ===' as step;

SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    -- 当前时间是否应该关闭窗口
    CASE 
        WHEN CURDATE() > psw.end_date THEN 'SHOULD_CLOSE'
        WHEN CURDATE() = psw.end_date AND CURTIME() >= psw.end_time THEN 'SHOULD_CLOSE'
        ELSE 'SHOULD_REMAIN_ACTIVE'
    END as close_logic_check,
    -- 计算窗口实际持续时间
    CASE 
        WHEN psw.end_date = psw.window_date THEN 
            TIMEDIFF(psw.end_time, psw.start_time)
        ELSE 
            CONCAT(
                TIMESTAMPDIFF(HOUR, 
                    TIMESTAMP(psw.window_date, psw.start_time),
                    TIMESTAMP(psw.end_date, psw.end_time)
                ), ' hours'
            )
    END as window_duration
FROM t_pmi_schedule_windows psw
WHERE psw.id IN (2051) OR (psw.end_time < psw.start_time)
ORDER BY psw.id;

-- 9. 最终验证：确保所有窗口的 end_date 都正确
SELECT '=== 最终验证：所有窗口 end_date 正确性 ===' as step;

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'ALL_WINDOWS_HAVE_CORRECT_END_DATE'
        ELSE CONCAT('FOUND_', COUNT(*), '_INCORRECT_WINDOWS')
    END as validation_result
FROM t_pmi_schedule_windows psw
WHERE (
    -- 跨日窗口但 end_date 不正确
    (psw.end_time < psw.start_time AND psw.end_date != DATE_ADD(psw.window_date, INTERVAL 1 DAY))
    OR
    -- 同日窗口但 end_date 不正确  
    (psw.end_time >= psw.start_time AND psw.end_date != psw.window_date)
);

-- 10. 显示修复后的窗口2051完整信息
SELECT '=== 窗口2051最终状态 ===' as step;

SELECT 
    psw.*,
    pr.pmi_number,
    pr.billing_mode,
    CASE 
        WHEN psw.end_time < psw.start_time THEN 'CROSS_DAY_WINDOW'
        ELSE 'SAME_DAY_WINDOW'
    END as window_type,
    TIMESTAMPDIFF(MINUTE, 
        TIMESTAMP(psw.window_date, psw.start_time),
        TIMESTAMP(psw.end_date, psw.end_time)
    ) as duration_minutes
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 2051;
