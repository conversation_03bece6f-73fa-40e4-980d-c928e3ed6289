SELECT 'Before Fix - Problem Data:' as status;
SELECT 
    id, 
    is_all_day, 
    LENG<PERSON>(is_all_day) as len, 
    HEX(is_all_day) as hex_value
FROM t_pmi_schedules 
WHERE pmi_record_id = 600;

UPDATE t_pmi_schedules 
SET is_all_day = '0' 
WHERE is_all_day = '' 
   OR is_all_day IS NULL 
   OR HEX(is_all_day) = '00'
   OR HEX(is_all_day) = '01'
   OR is_all_day NOT IN ('0', '1', 'true', 'false', 'TRUE', 'FALSE');

SELECT 'After Fix:' as status;
SELECT 
    id, 
    is_all_day, 
    LENGTH(is_all_day) as len, 
    HEX(is_all_day) as hex_value
FROM t_pmi_schedules 
WHERE pmi_record_id = 600;
