-- 修复数据库结构的 SQL 脚本

-- 1. 创建缺失的 t_join_account_usage_windows 表
CREATE TABLE IF NOT EXISTS t_join_account_usage_windows (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_user_id BIGINT NOT NULL COMMENT 'Zoom账号ID',
    token_number VARCHAR(6) NOT NULL COMMENT '关联的Token',
    start_time DATETIME NOT NULL COMMENT '窗口开始时间',
    end_time DATETIME NOT NULL COMMENT '窗口结束时间',
    status ENUM('PENDING', 'ACTIVE', 'CLOSED') DEFAULT 'PENDING' COMMENT '窗口状态',
    opened_at DATETIME NULL COMMENT '实际开启时间',
    closed_at DATETIME NULL COMMENT '实际关闭时间',
    error_message TEXT COMMENT '错误信息',
    last_operation_error TEXT COMMENT '最后操作错误',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_token_number (token_number),
    INDEX idx_time_range (start_time, end_time),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 添加缺失的字段到 t_meetings 表（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_meetings' AND COLUMN_NAME = 'host_key') = 0,
    'ALTER TABLE t_meetings ADD COLUMN host_key VARCHAR(255) COMMENT ''主持人密钥''',
    'SELECT ''Column host_key already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 添加缺失的字段到 t_pmi_records 表（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'fallback_enabled') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN fallback_enabled BOOLEAN DEFAULT FALSE COMMENT ''是否启用回退机制''',
    'SELECT ''Column fallback_enabled already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 添加缺失的字段到 task_execution_record 表（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'task_execution_record' AND COLUMN_NAME = 'execution_node') = 0,
    'ALTER TABLE task_execution_record ADD COLUMN execution_node VARCHAR(255) COMMENT ''执行节点''',
    'SELECT ''Column execution_node already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 添加缺失的字段到 t_zoom_meeting_details 表（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_zoom_meeting_details' AND COLUMN_NAME = 'creation_source') = 0,
    'ALTER TABLE t_zoom_meeting_details ADD COLUMN creation_source VARCHAR(50) COMMENT ''创建来源''',
    'SELECT ''Column creation_source already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 添加缺失的字段到 t_pmi_records 表（original_billing_mode）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'original_billing_mode') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN original_billing_mode VARCHAR(20) COMMENT ''原始计费模式（用于窗口关闭后恢复）''',
    'SELECT ''Column original_billing_mode already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 添加缺失的字段到 t_pmi_records 表（active_window_ids）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'active_window_ids') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN active_window_ids TEXT COMMENT ''活跃窗口ID列表（JSON格式）''',
    'SELECT ''Column active_window_ids already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
