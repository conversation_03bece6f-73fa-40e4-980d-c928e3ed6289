-- 修复t_zoom_meetings表的status字段长度问题
-- 
-- 问题：Data truncated for column 'status' at row 1
-- 原因：status字段长度可能不足以存储新的枚举值
-- 解决：扩大status字段长度并确保支持所有枚举值

-- 1. 查看当前表结构
SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'T_ZOOM_MEETINGS' AND COLUMN_NAME = 'STATUS';

-- 2. 查看现有的status值
SELECT DISTINCT status, COUNT(*) as count
FROM t_zoom_meetings
GROUP BY status
ORDER BY count DESC;

-- 3. 修复status字段长度（扩大到足够容纳所有枚举值）
ALTER TABLE t_zoom_meetings 
MODIFY COLUMN status VARCHAR(50) DEFAULT 'WAITING' 
COMMENT '状态：CREATING, CREATE_FAILED, WAITING, STARTED, ENDED, SETTLING, SETTLED, ERROR, DELETED';

-- 4. 验证修复结果
SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'T_ZOOM_MEETINGS' AND COLUMN_NAME = 'STATUS';

-- 5. 测试插入新记录
INSERT INTO t_zoom_meetings (
    zoom_meeting_uuid, 
    zoom_meeting_id, 
    host_id, 
    topic, 
    status,
    start_time,
    created_at,
    updated_at
) VALUES (
    'test-fix-' || CAST(UNIX_TIMESTAMP() AS CHAR),
    '999888777',
    'test-host-fix',
    '数据库修复测试会议',
    'STARTED',
    NOW(),
    NOW(),
    NOW()
);

-- 6. 验证插入结果
SELECT * FROM t_zoom_meetings 
WHERE zoom_meeting_uuid LIKE 'test-fix-%'
ORDER BY created_at DESC
LIMIT 5;

-- 7. 清理测试数据（可选）
-- DELETE FROM t_zoom_meetings WHERE zoom_meeting_uuid LIKE 'test-fix-%';
