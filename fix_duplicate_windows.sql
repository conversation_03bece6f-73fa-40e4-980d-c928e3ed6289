-- 修复重复窗口问题的SQL脚本
-- 针对TraceId: 20250823193951674-001-833004-0s659y 的问题

-- 1. 检查当前重复窗口的情况
SELECT '=== 检查重复窗口情况 ===' as step;
SELECT 
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    COUNT(*) as window_count,
    GROUP_CONCAT(psw.id ORDER BY psw.id) as window_ids,
    GROUP_CONCAT(psw.created_at ORDER BY psw.id) as created_times
FROM t_pmi_schedule_windows psw
WHERE psw.status IN ('PENDING', 'ACTIVE')
GROUP BY psw.schedule_id, psw.pmi_record_id, psw.start_date_time, psw.end_date_time
HAVING COUNT(*) > 1
ORDER BY psw.schedule_id;

-- 2. 检查具体的问题窗口（scheduleId=1052）
SELECT '=== 检查scheduleId=1052的重复窗口 ===' as step;
SELECT 
    psw.id,
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    psw.open_task_id,
    psw.close_task_id,
    psw.created_at,
    TIMESTAMPDIFF(MICROSECOND, LAG(psw.created_at) OVER (ORDER BY psw.created_at), psw.created_at) / 1000 as time_diff_ms
FROM t_pmi_schedule_windows psw
WHERE psw.schedule_id = 1052
ORDER BY psw.created_at;

-- 3. 检查任务表中的相关任务
SELECT '=== 检查相关任务 ===' as step;
SELECT
    pswt.id,
    pswt.task_key,
    pswt.pmi_window_id,
    pswt.task_type,
    pswt.scheduled_time,
    pswt.status,
    pswt.created_at
FROM t_pmi_schedule_window_tasks pswt
WHERE pswt.id IN (23, 24)
ORDER BY pswt.id;

-- 4. 删除重复的窗口（保留最早创建的）
-- 对于scheduleId=1052，删除窗口2081，保留窗口2080
SELECT '=== 删除重复窗口 ===' as step;

-- 首先检查要删除的窗口
SELECT 
    psw.id,
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    psw.created_at,
    'WILL_BE_DELETED' as action
FROM t_pmi_schedule_windows psw
WHERE psw.id = 2081;

-- 删除重复的窗口
DELETE FROM t_pmi_schedule_windows WHERE id = 2081;

-- 5. 验证删除结果
SELECT '=== 验证删除结果 ===' as step;
SELECT 
    psw.id,
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    psw.open_task_id,
    psw.close_task_id,
    psw.created_at
FROM t_pmi_schedule_windows psw
WHERE psw.schedule_id = 1052
ORDER BY psw.created_at;

-- 6. 检查是否还有其他重复窗口需要处理
SELECT '=== 检查其他重复窗口 ===' as step;
SELECT 
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    COUNT(*) as window_count,
    GROUP_CONCAT(psw.id ORDER BY psw.id) as window_ids,
    MIN(psw.created_at) as first_created,
    MAX(psw.created_at) as last_created,
    TIMESTAMPDIFF(MICROSECOND, MIN(psw.created_at), MAX(psw.created_at)) / 1000 as time_diff_ms
FROM t_pmi_schedule_windows psw
WHERE psw.status IN ('PENDING', 'ACTIVE')
GROUP BY psw.schedule_id, psw.pmi_record_id, psw.start_date_time, psw.end_date_time
HAVING COUNT(*) > 1
ORDER BY psw.schedule_id;

-- 7. 通用的重复窗口清理（如果有其他重复窗口）
-- 这个查询会找出所有重复窗口，并标记哪些应该被删除
SELECT '=== 通用重复窗口清理分析 ===' as step;
WITH duplicate_windows AS (
    SELECT 
        psw.id,
        psw.schedule_id,
        psw.pmi_record_id,
        psw.start_date_time,
        psw.end_date_time,
        psw.status,
        psw.created_at,
        ROW_NUMBER() OVER (
            PARTITION BY psw.schedule_id, psw.pmi_record_id, psw.start_date_time, psw.end_date_time 
            ORDER BY psw.created_at ASC
        ) as row_num,
        COUNT(*) OVER (
            PARTITION BY psw.schedule_id, psw.pmi_record_id, psw.start_date_time, psw.end_date_time
        ) as total_count
    FROM t_pmi_schedule_windows psw
    WHERE psw.status IN ('PENDING', 'ACTIVE')
)
SELECT 
    id,
    schedule_id,
    pmi_record_id,
    start_date_time,
    end_date_time,
    status,
    created_at,
    row_num,
    total_count,
    CASE 
        WHEN total_count > 1 AND row_num = 1 THEN 'KEEP'
        WHEN total_count > 1 AND row_num > 1 THEN 'DELETE'
        ELSE 'SINGLE'
    END as action
FROM duplicate_windows
WHERE total_count > 1
ORDER BY schedule_id, created_at;

-- 8. 最终统计报告
SELECT '=== 最终统计报告 ===' as step;
SELECT 
    'Total Active Windows' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status IN ('PENDING', 'ACTIVE')

UNION ALL

SELECT 
    'Schedules with Windows' as metric,
    COUNT(DISTINCT schedule_id) as count
FROM t_pmi_schedule_windows 
WHERE status IN ('PENDING', 'ACTIVE')

UNION ALL

SELECT 
    'PMI Records with Windows' as metric,
    COUNT(DISTINCT pmi_record_id) as count
FROM t_pmi_schedule_windows 
WHERE status IN ('PENDING', 'ACTIVE');

-- 9. 检查修复后的计划状态
SELECT '=== 修复后的计划状态 ===' as step;
SELECT 
    ps.id as schedule_id,
    ps.pmi_record_id,
    ps.name,
    ps.start_date,
    ps.end_date,
    ps.repeat_type,
    ps.created_at,
    COUNT(psw.id) as window_count
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id AND psw.status IN ('PENDING', 'ACTIVE')
WHERE ps.id = 1052
GROUP BY ps.id, ps.pmi_record_id, ps.name, ps.start_date, ps.end_date, ps.repeat_type, ps.created_at;
