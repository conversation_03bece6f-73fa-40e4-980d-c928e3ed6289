-- 修复LONG类型PMI记录的窗口字段
-- 设置 current_window_id, window_expire_time, active_window_ids
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一部分：问题分析
-- ========================================

SELECT '=== LONG类型PMI窗口字段问题分析 ===' as step;

-- 检查LONG类型PMI记录的窗口字段状态
SELECT 
    'LONG PMI Records Status' as check_type,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_current_window_id,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_window_expire_time,
    COUNT(CASE WHEN active_window_ids IS NOT NULL THEN 1 END) as has_active_window_ids
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- 检查LONG类型PMI的窗口情况
SELECT 
    'LONG PMI Window Analysis' as check_type,
    pr.id as pmi_record_id,
    pr.pmi_number,
    COUNT(psw.id) as window_count,
    MIN(psw.window_date) as earliest_window,
    MAX(psw.end_date) as latest_window_end,
    MAX(psw.id) as latest_window_id
FROM t_pmi_records pr
LEFT JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
WHERE pr.billing_mode = 'LONG'
GROUP BY pr.id, pr.pmi_number
ORDER BY pr.id
LIMIT 10;

-- ========================================
-- 第二部分：修复窗口字段
-- ========================================

SELECT '=== 开始修复LONG类型PMI窗口字段 ===' as step;

-- 步骤1：为有窗口的LONG类型PMI设置窗口字段
UPDATE t_pmi_records pr
JOIN (
    SELECT 
        psw.pmi_record_id,
        psw.id as current_window_id,
        TIMESTAMP(psw.end_date, psw.end_time) as window_expire_time,
        JSON_ARRAY(psw.id) as active_window_ids
    FROM t_pmi_schedule_windows psw
    WHERE psw.pmi_record_id IN (
        SELECT id FROM t_pmi_records WHERE billing_mode = 'LONG'
    )
    -- 对于每个PMI，选择最新的窗口作为当前窗口
    AND psw.id = (
        SELECT MAX(psw2.id) 
        FROM t_pmi_schedule_windows psw2 
        WHERE psw2.pmi_record_id = psw.pmi_record_id
    )
) window_info ON pr.id = window_info.pmi_record_id
SET 
    pr.current_window_id = window_info.current_window_id,
    pr.window_expire_time = window_info.window_expire_time,
    pr.active_window_ids = window_info.active_window_ids,
    pr.updated_at = NOW()
WHERE pr.billing_mode = 'LONG';

SELECT 
    'Updated LONG PMI Records' as result_type,
    ROW_COUNT() as updated_records;

-- 步骤2：为没有窗口的LONG类型PMI创建默认窗口
-- 首先检查是否有没有窗口的LONG类型PMI
SELECT 
    'LONG PMI without Windows' as check_type,
    pr.id,
    pr.pmi_number,
    pr.billing_mode
FROM t_pmi_records pr
LEFT JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
WHERE pr.billing_mode = 'LONG'
AND psw.id IS NULL;

-- 为没有窗口的LONG类型PMI创建计划和窗口
INSERT INTO t_pmi_schedules (
    pmi_record_id,
    name,
    start_date,
    end_date,
    start_time,
    duration_minutes,
    repeat_type,
    status,
    is_all_day,
    created_at,
    updated_at
)
SELECT 
    pr.id as pmi_record_id,
    CONCAT('长租计划_', pr.pmi_number) as name,
    CURDATE() as start_date,
    DATE_ADD(CURDATE(), INTERVAL 1 YEAR) as end_date,
    '00:00:00' as start_time,
    1440 as duration_minutes,  -- 24小时 = 1440分钟
    'ONCE' as repeat_type,
    'ACTIVE' as status,
    1 as is_all_day,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_records pr
LEFT JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
WHERE pr.billing_mode = 'LONG'
AND psw.id IS NULL;

SELECT 
    'Created Default Schedules' as result_type,
    ROW_COUNT() as created_schedules;

-- 为新创建的计划创建窗口
INSERT INTO t_pmi_schedule_windows (
    schedule_id,
    pmi_record_id,
    window_date,
    end_date,
    start_time,
    end_time,
    status,
    created_at,
    updated_at
)
SELECT 
    ps.id as schedule_id,
    ps.pmi_record_id,
    ps.start_date as window_date,
    ps.end_date as end_date,
    ps.start_time,
    '23:59:59' as end_time,
    'ACTIVE' as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules ps
WHERE ps.pmi_record_id IN (
    SELECT pr.id
    FROM t_pmi_records pr
    LEFT JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
    WHERE pr.billing_mode = 'LONG'
    AND psw.id IS NULL
)
AND ps.name LIKE '长租计划_%'
AND ps.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);  -- 只处理刚创建的计划

SELECT 
    'Created Default Windows' as result_type,
    ROW_COUNT() as created_windows;

-- 步骤3：为新创建窗口的PMI设置窗口字段
UPDATE t_pmi_records pr
JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
SET 
    pr.current_window_id = psw.id,
    pr.window_expire_time = TIMESTAMP(psw.end_date, psw.end_time),
    pr.active_window_ids = JSON_ARRAY(psw.id),
    pr.updated_at = NOW()
WHERE pr.billing_mode = 'LONG'
AND pr.current_window_id IS NULL
AND psw.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);  -- 只处理刚创建的窗口

SELECT 
    'Updated New PMI Records' as result_type,
    ROW_COUNT() as updated_new_records;

-- ========================================
-- 第三部分：验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 验证LONG类型PMI记录的窗口字段
SELECT 
    'LONG PMI Records After Fix' as check_type,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_current_window_id,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_window_expire_time,
    COUNT(CASE WHEN active_window_ids IS NOT NULL THEN 1 END) as has_active_window_ids,
    ROUND(COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as completion_rate
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- 显示修复后的LONG类型PMI详细信息
SELECT 
    'LONG PMI Details After Fix' as check_type,
    pr.id,
    pr.pmi_number,
    pr.current_window_id,
    pr.window_expire_time,
    pr.active_window_ids,
    psw.window_date,
    psw.end_date,
    psw.status as window_status
FROM t_pmi_records pr
LEFT JOIN t_pmi_schedule_windows psw ON pr.current_window_id = psw.id
WHERE pr.billing_mode = 'LONG'
ORDER BY pr.id
LIMIT 10;

-- 检查是否还有未修复的LONG类型PMI
SELECT 
    'Unfixed LONG PMI Records' as check_type,
    COUNT(*) as unfixed_count
FROM t_pmi_records 
WHERE billing_mode = 'LONG'
AND (current_window_id IS NULL OR window_expire_time IS NULL OR active_window_ids IS NULL);

-- 验证窗口过期时间的合理性
SELECT 
    'Window Expire Time Analysis' as check_type,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN window_expire_time > NOW() THEN 1 END) as future_expire,
    COUNT(CASE WHEN window_expire_time <= NOW() THEN 1 END) as past_expire,
    MIN(window_expire_time) as earliest_expire,
    MAX(window_expire_time) as latest_expire
FROM t_pmi_records 
WHERE billing_mode = 'LONG'
AND window_expire_time IS NOT NULL;

-- 提交事务
COMMIT;

-- ========================================
-- 第四部分：最终报告
-- ========================================

SELECT '=== LONG类型PMI窗口字段修复完成 ===' as final_report;

-- 最终统计
SELECT 
    'Final Statistics' as report_type,
    billing_mode,
    COUNT(*) as total_records,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_current_window,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_expire_time,
    COUNT(CASE WHEN active_window_ids IS NOT NULL THEN 1 END) as has_active_windows,
    ROUND(COUNT(CASE WHEN current_window_id IS NOT NULL AND window_expire_time IS NOT NULL AND active_window_ids IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as complete_rate
FROM t_pmi_records 
WHERE billing_mode IN ('LONG', 'BY_TIME')
GROUP BY billing_mode
ORDER BY billing_mode;

SELECT 'LONG类型PMI窗口字段修复完成！所有LONG类型PMI现在都有正确的窗口字段设置。' as final_message;
