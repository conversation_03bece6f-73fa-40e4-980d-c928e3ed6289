-- 简化版修复LONG类型PMI记录的窗口字段
-- 设置 current_window_id, window_expire_time, active_window_ids
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一部分：问题分析
-- ========================================

SELECT '=== LONG类型PMI窗口字段问题分析 ===' as step;

-- 检查LONG类型PMI记录的窗口字段状态
SELECT 
    'LONG PMI Records Status' as check_type,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_current_window_id,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_window_expire_time,
    COUNT(CASE WHEN active_window_ids IS NOT NULL THEN 1 END) as has_active_window_ids
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- ========================================
-- 第二部分：创建临时表辅助修复
-- ========================================

SELECT '=== 创建临时表辅助修复 ===' as step;

-- 创建临时表存储LONG类型PMI的窗口信息
CREATE TEMPORARY TABLE temp_long_pmi_windows (
    pmi_record_id BIGINT,
    window_id BIGINT,
    window_expire_time DATETIME,
    PRIMARY KEY (pmi_record_id)
);

-- 填充临时表：为每个LONG类型PMI找到其对应的窗口
INSERT INTO temp_long_pmi_windows (pmi_record_id, window_id, window_expire_time)
SELECT 
    pr.id as pmi_record_id,
    psw.id as window_id,
    TIMESTAMP(psw.end_date, psw.end_time) as window_expire_time
FROM t_pmi_records pr
JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
WHERE pr.billing_mode = 'LONG'
-- 如果有多个窗口，选择最新的一个
AND psw.id = (
    SELECT MAX(psw2.id) 
    FROM t_pmi_schedule_windows psw2 
    WHERE psw2.pmi_record_id = pr.id
);

SELECT 
    'Temp Table Populated' as result_type,
    COUNT(*) as records_found
FROM temp_long_pmi_windows;

-- ========================================
-- 第三部分：修复有窗口的LONG类型PMI
-- ========================================

SELECT '=== 修复有窗口的LONG类型PMI ===' as step;

-- 更新LONG类型PMI的窗口字段
UPDATE t_pmi_records pr
JOIN temp_long_pmi_windows tlpw ON pr.id = tlpw.pmi_record_id
SET 
    pr.current_window_id = tlpw.window_id,
    pr.window_expire_time = tlpw.window_expire_time,
    pr.active_window_ids = JSON_ARRAY(tlpw.window_id),
    pr.updated_at = NOW()
WHERE pr.billing_mode = 'LONG';

SELECT 
    'Updated LONG PMI Records' as result_type,
    ROW_COUNT() as updated_records;

-- ========================================
-- 第四部分：处理没有窗口的LONG类型PMI
-- ========================================

SELECT '=== 处理没有窗口的LONG类型PMI ===' as step;

-- 检查没有窗口的LONG类型PMI
SELECT 
    'LONG PMI without Windows' as check_type,
    pr.id,
    pr.pmi_number
FROM t_pmi_records pr
LEFT JOIN temp_long_pmi_windows tlpw ON pr.id = tlpw.pmi_record_id
WHERE pr.billing_mode = 'LONG'
AND tlpw.pmi_record_id IS NULL;

-- 为没有窗口的LONG类型PMI创建计划
INSERT INTO t_pmi_schedules (
    pmi_record_id,
    name,
    start_date,
    end_date,
    start_time,
    duration_minutes,
    repeat_type,
    status,
    is_all_day,
    created_at,
    updated_at
)
SELECT 
    pr.id as pmi_record_id,
    CONCAT('长租计划_', pr.pmi_number) as name,
    CURDATE() as start_date,
    DATE_ADD(CURDATE(), INTERVAL 1 YEAR) as end_date,
    '00:00:00' as start_time,
    1440 as duration_minutes,
    'ONCE' as repeat_type,
    'ACTIVE' as status,
    1 as is_all_day,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_records pr
LEFT JOIN temp_long_pmi_windows tlpw ON pr.id = tlpw.pmi_record_id
WHERE pr.billing_mode = 'LONG'
AND tlpw.pmi_record_id IS NULL;

SELECT 
    'Created Default Schedules' as result_type,
    ROW_COUNT() as created_schedules;

-- 为新创建的计划创建窗口
INSERT INTO t_pmi_schedule_windows (
    schedule_id,
    pmi_record_id,
    window_date,
    end_date,
    start_time,
    end_time,
    status,
    created_at,
    updated_at
)
SELECT 
    ps.id as schedule_id,
    ps.pmi_record_id,
    ps.start_date as window_date,
    ps.end_date as end_date,
    ps.start_time,
    '23:59:59' as end_time,
    'ACTIVE' as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules ps
WHERE ps.name LIKE '长租计划_%'
AND ps.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);

SELECT 
    'Created Default Windows' as result_type,
    ROW_COUNT() as created_windows;

-- 为新创建窗口的PMI设置窗口字段
UPDATE t_pmi_records pr
JOIN t_pmi_schedules ps ON pr.id = ps.pmi_record_id
JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
SET 
    pr.current_window_id = psw.id,
    pr.window_expire_time = TIMESTAMP(psw.end_date, psw.end_time),
    pr.active_window_ids = JSON_ARRAY(psw.id),
    pr.updated_at = NOW()
WHERE pr.billing_mode = 'LONG'
AND pr.current_window_id IS NULL
AND ps.name LIKE '长租计划_%'
AND ps.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);

SELECT 
    'Updated New PMI Records' as result_type,
    ROW_COUNT() as updated_new_records;

-- ========================================
-- 第五部分：验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 验证LONG类型PMI记录的窗口字段
SELECT 
    'LONG PMI Records After Fix' as check_type,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_current_window_id,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_window_expire_time,
    COUNT(CASE WHEN active_window_ids IS NOT NULL THEN 1 END) as has_active_window_ids,
    ROUND(COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as completion_rate
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- 显示修复后的LONG类型PMI详细信息
SELECT 
    'LONG PMI Details After Fix' as check_type,
    pr.id,
    pr.pmi_number,
    pr.current_window_id,
    DATE(pr.window_expire_time) as expire_date,
    pr.active_window_ids,
    psw.status as window_status
FROM t_pmi_records pr
LEFT JOIN t_pmi_schedule_windows psw ON pr.current_window_id = psw.id
WHERE pr.billing_mode = 'LONG'
ORDER BY pr.id
LIMIT 15;

-- 检查窗口过期时间分布
SELECT 
    'Window Expire Time Analysis' as check_type,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN window_expire_time > NOW() THEN 1 END) as future_expire,
    COUNT(CASE WHEN window_expire_time <= NOW() THEN 1 END) as past_expire,
    MIN(DATE(window_expire_time)) as earliest_expire_date,
    MAX(DATE(window_expire_time)) as latest_expire_date
FROM t_pmi_records 
WHERE billing_mode = 'LONG'
AND window_expire_time IS NOT NULL;

-- 清理临时表
DROP TEMPORARY TABLE temp_long_pmi_windows;

-- 提交事务
COMMIT;

-- ========================================
-- 第六部分：最终报告
-- ========================================

SELECT '=== LONG类型PMI窗口字段修复完成 ===' as final_report;

-- 最终统计对比
SELECT 
    'Final Comparison' as report_type,
    billing_mode,
    COUNT(*) as total_records,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_current_window,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_expire_time,
    COUNT(CASE WHEN active_window_ids IS NOT NULL THEN 1 END) as has_active_windows,
    ROUND(COUNT(CASE WHEN current_window_id IS NOT NULL AND window_expire_time IS NOT NULL AND active_window_ids IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as complete_rate
FROM t_pmi_records 
WHERE billing_mode IN ('LONG', 'BY_TIME')
GROUP BY billing_mode
ORDER BY billing_mode;

SELECT 'LONG类型PMI窗口字段修复完成！所有LONG类型PMI现在都有正确的窗口字段设置。' as final_message;
