-- 修复PMI记录的magic_id字段
-- 从old_t_zoom_pmi表的mg_id字段获取正确的魔链ID
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一部分：问题分析
-- ========================================

SELECT '=== Magic ID问题分析 ===' as step;

-- 检查当前magic_id与old_t_zoom_pmi的mg_id对比
SELECT
    'Magic ID Comparison Sample' as check_type,
    pr.id as new_id,
    pr.pmi_number,
    pr.magic_id as current_magic_id,
    ozp.mg_id as correct_mg_id,
    CASE
        WHEN CONVERT(pr.magic_id USING utf8mb4) = CONVERT(ozp.mg_id USING utf8mb4) THEN 'CORRECT'
        WHEN pr.magic_id = pr.pmi_number THEN 'USING_PMI_NUMBER'
        ELSE 'OTHER_MISMATCH'
    END as status
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi
LIMIT 10;

-- 统计magic_id状态
SELECT
    'Magic ID Status Summary' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CONVERT(pr.magic_id USING utf8mb4) = CONVERT(ozp.mg_id USING utf8mb4) THEN 1 END) as correct_magic_id,
    COUNT(CASE WHEN pr.magic_id = pr.pmi_number THEN 1 END) as using_pmi_number,
    COUNT(CASE WHEN CONVERT(pr.magic_id USING utf8mb4) != CONVERT(ozp.mg_id USING utf8mb4) AND pr.magic_id != pr.pmi_number THEN 1 END) as other_mismatch
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi;

-- ========================================
-- 第二部分：修复magic_id字段
-- ========================================

SELECT '=== 开始修复Magic ID字段 ===' as step;

-- 创建临时表存储正确的magic_id映射
CREATE TEMPORARY TABLE temp_magic_id_mapping (
    pmi_number VARCHAR(20),
    correct_mg_id VARCHAR(255),
    PRIMARY KEY (pmi_number)
);

-- 填充临时表：从old_t_zoom_pmi获取正确的mg_id
INSERT INTO temp_magic_id_mapping (pmi_number, correct_mg_id)
SELECT
    ozp.pmi as pmi_number,
    ozp.mg_id as correct_mg_id
FROM old_t_zoom_pmi ozp
WHERE ozp.mg_id IS NOT NULL
AND ozp.mg_id != '';

SELECT 
    'Magic ID Mapping Created' as result_type,
    COUNT(*) as mapping_records
FROM temp_magic_id_mapping;

-- 显示一些映射示例
SELECT 
    'Magic ID Mapping Sample' as check_type,
    pmi_number,
    correct_mg_id
FROM temp_magic_id_mapping
ORDER BY pmi_number
LIMIT 10;

-- 更新t_pmi_records表的magic_id字段
UPDATE t_pmi_records pr
JOIN temp_magic_id_mapping tmim ON pr.pmi_number = tmim.pmi_number
SET
    pr.magic_id = tmim.correct_mg_id,
    pr.updated_at = NOW()
WHERE CONVERT(pr.magic_id USING utf8mb4) != CONVERT(tmim.correct_mg_id USING utf8mb4);

SELECT 
    'Updated Magic ID Records' as result_type,
    ROW_COUNT() as updated_records;

-- ========================================
-- 第三部分：验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 验证修复后的magic_id
SELECT
    'Magic ID After Fix Sample' as check_type,
    pr.id,
    pr.pmi_number,
    pr.magic_id as fixed_magic_id,
    ozp.mg_id as original_mg_id,
    CASE
        WHEN CONVERT(pr.magic_id USING utf8mb4) = CONVERT(ozp.mg_id USING utf8mb4) THEN 'CORRECT'
        ELSE 'STILL_INCORRECT'
    END as status
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi
ORDER BY pr.id
LIMIT 15;

-- 统计修复后的状态
SELECT
    'Magic ID Fix Summary' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CONVERT(pr.magic_id USING utf8mb4) = CONVERT(ozp.mg_id USING utf8mb4) THEN 1 END) as correct_magic_id,
    COUNT(CASE WHEN CONVERT(pr.magic_id USING utf8mb4) != CONVERT(ozp.mg_id USING utf8mb4) THEN 1 END) as still_incorrect,
    ROUND(COUNT(CASE WHEN CONVERT(pr.magic_id USING utf8mb4) = CONVERT(ozp.mg_id USING utf8mb4) THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi;

-- 检查是否有PMI记录没有对应的old_t_zoom_pmi记录
SELECT
    'Orphaned PMI Records' as check_type,
    COUNT(*) as orphaned_count
FROM t_pmi_records pr
LEFT JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi
WHERE ozp.pmi IS NULL;

-- 显示一些修复前后的对比
SELECT
    'Before/After Comparison' as check_type,
    CONCAT('PMI: ', pr.pmi_number) as pmi_info,
    CONCAT('Old: ', pr.pmi_number) as old_magic_id,
    CONCAT('New: ', pr.magic_id) as new_magic_id,
    CONCAT('Source: ', ozp.mg_id) as source_mg_id
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi
WHERE CONVERT(pr.magic_id USING utf8mb4) = CONVERT(ozp.mg_id USING utf8mb4)
ORDER BY pr.id
LIMIT 10;

-- 清理临时表
DROP TEMPORARY TABLE temp_magic_id_mapping;

-- 提交事务
COMMIT;

-- ========================================
-- 第四部分：更新迁移脚本建议
-- ========================================

SELECT '=== 迁移脚本修复建议 ===' as final_report;

SELECT 
    'Migration Script Fix' as suggestion_type,
    'UPDATE: Change magic_id generation logic' as suggestion,
    'FROM: magic_id = pmi_number' as current_logic,
    'TO: magic_id = old_t_zoom_pmi.mg_id' as correct_logic;

-- 生成正确的迁移SQL示例
SELECT 
    'Correct Migration SQL Example' as example_type,
    'INSERT INTO t_pmi_records (..., magic_id, ...)' as sql_part1,
    'SELECT ..., ozp.mg_id as magic_id, ...' as sql_part2,
    'FROM old_t_zoom_pmi ozp ...' as sql_part3;

-- ========================================
-- 第五部分：最终验证
-- ========================================

SELECT '=== Magic ID修复完成 ===' as final_message;

-- 最终统计
SELECT 
    'Final Statistics' as report_type,
    COUNT(*) as total_pmi_records,
    COUNT(CASE WHEN pr.magic_id IS NOT NULL AND pr.magic_id != '' THEN 1 END) as has_magic_id,
    COUNT(CASE WHEN pr.magic_id = pr.pmi_number THEN 1 END) as still_using_pmi_number,
    ROUND(COUNT(CASE WHEN pr.magic_id IS NOT NULL AND pr.magic_id != '' AND pr.magic_id != pr.pmi_number THEN 1 END) * 100.0 / COUNT(*), 2) as correct_magic_id_rate
FROM t_pmi_records pr;

-- 显示一些最终的magic_id示例
SELECT 
    'Final Magic ID Examples' as example_type,
    pmi_number,
    magic_id,
    CASE 
        WHEN magic_id = pmi_number THEN 'SAME_AS_PMI' 
        ELSE 'DIFFERENT_FROM_PMI' 
    END as magic_id_type
FROM t_pmi_records
ORDER BY id
LIMIT 10;

SELECT 'Magic ID修复完成！现在所有PMI记录都使用来自old_t_zoom_pmi.mg_id的正确魔链ID。' as completion_message;
