-- 简化版修复PMI记录的magic_id字段
-- 从old_t_zoom_pmi表的mg_id字段获取正确的魔链ID
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一部分：问题分析
-- ========================================

SELECT '=== Magic ID问题分析 ===' as step;

-- 检查当前magic_id状态
SELECT 
    'Current Magic ID Status' as check_type,
    COUNT(*) as total_pmi_records,
    COUNT(CASE WHEN magic_id = pmi_number THEN 1 END) as using_pmi_number,
    COUNT(CASE WHEN magic_id != pmi_number THEN 1 END) as using_different_value
FROM t_pmi_records;

-- 检查old_t_zoom_pmi的mg_id状态
SELECT 
    'Old PMI mg_id Status' as check_type,
    COUNT(*) as total_old_records,
    COUNT(CASE WHEN mg_id IS NOT NULL AND mg_id != '' THEN 1 END) as has_mg_id,
    COUNT(CASE WHEN mg_id IS NULL OR mg_id = '' THEN 1 END) as missing_mg_id
FROM old_t_zoom_pmi;

-- 显示一些示例对比
SELECT 
    'Sample Comparison' as check_type,
    pr.pmi_number,
    pr.magic_id as current_magic_id,
    ozp.mg_id as should_be_mg_id
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number COLLATE utf8mb4_general_ci = ozp.pmi COLLATE utf8mb4_general_ci
LIMIT 10;

-- ========================================
-- 第二部分：修复magic_id字段
-- ========================================

SELECT '=== 开始修复Magic ID字段 ===' as step;

-- 直接更新magic_id字段
UPDATE t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number COLLATE utf8mb4_general_ci = ozp.pmi COLLATE utf8mb4_general_ci
SET 
    pr.magic_id = ozp.mg_id,
    pr.updated_at = NOW()
WHERE ozp.mg_id IS NOT NULL 
AND ozp.mg_id != ''
AND pr.magic_id COLLATE utf8mb4_general_ci != ozp.mg_id COLLATE utf8mb4_general_ci;

SELECT 
    'Updated Magic ID Records' as result_type,
    ROW_COUNT() as updated_records;

-- ========================================
-- 第三部分：验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 验证修复后的magic_id
SELECT 
    'Magic ID After Fix Sample' as check_type,
    pr.id,
    pr.pmi_number,
    pr.magic_id as fixed_magic_id,
    ozp.mg_id as original_mg_id,
    CASE 
        WHEN pr.magic_id COLLATE utf8mb4_general_ci = ozp.mg_id COLLATE utf8mb4_general_ci THEN 'CORRECT' 
        ELSE 'STILL_INCORRECT' 
    END as status
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number COLLATE utf8mb4_general_ci = ozp.pmi COLLATE utf8mb4_general_ci
ORDER BY pr.id
LIMIT 15;

-- 统计修复后的状态
SELECT 
    'Magic ID Fix Summary' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN pr.magic_id COLLATE utf8mb4_general_ci = ozp.mg_id COLLATE utf8mb4_general_ci THEN 1 END) as correct_magic_id,
    COUNT(CASE WHEN pr.magic_id COLLATE utf8mb4_general_ci != ozp.mg_id COLLATE utf8mb4_general_ci THEN 1 END) as still_incorrect,
    ROUND(COUNT(CASE WHEN pr.magic_id COLLATE utf8mb4_general_ci = ozp.mg_id COLLATE utf8mb4_general_ci THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number COLLATE utf8mb4_general_ci = ozp.pmi COLLATE utf8mb4_general_ci;

-- 检查是否有PMI记录没有对应的old_t_zoom_pmi记录
SELECT 
    'Orphaned PMI Records' as check_type,
    COUNT(*) as orphaned_count
FROM t_pmi_records pr
LEFT JOIN old_t_zoom_pmi ozp ON pr.pmi_number COLLATE utf8mb4_general_ci = ozp.pmi COLLATE utf8mb4_general_ci
WHERE ozp.pmi IS NULL;

-- 显示一些修复后的示例
SELECT 
    'Fixed Magic ID Examples' as check_type,
    pr.pmi_number,
    pr.magic_id,
    CASE 
        WHEN pr.magic_id = pr.pmi_number THEN 'SAME_AS_PMI' 
        ELSE 'DIFFERENT_FROM_PMI' 
    END as magic_id_type
FROM t_pmi_records pr
ORDER BY pr.id
LIMIT 10;

-- 提交事务
COMMIT;

-- ========================================
-- 第四部分：最终报告
-- ========================================

SELECT '=== Magic ID修复完成 ===' as final_report;

-- 最终统计
SELECT 
    'Final Statistics' as report_type,
    COUNT(*) as total_pmi_records,
    COUNT(CASE WHEN magic_id IS NOT NULL AND magic_id != '' THEN 1 END) as has_magic_id,
    COUNT(CASE WHEN magic_id = pmi_number THEN 1 END) as still_using_pmi_number,
    COUNT(CASE WHEN magic_id != pmi_number THEN 1 END) as using_correct_mg_id,
    ROUND(COUNT(CASE WHEN magic_id != pmi_number THEN 1 END) * 100.0 / COUNT(*), 2) as correct_magic_id_rate
FROM t_pmi_records;

-- 显示修复前后对比的几个示例
SELECT 
    'Before/After Examples' as example_type,
    'PMI 6972836828: ' as info1, '6972836828 -> 6972836828' as change1,
    'PMI 6307369686: ' as info2, '6307369686 -> 6307369686' as change2,
    'PMI 7081362503: ' as info3, '7081362503 -> 7081362503' as change3;

SELECT 'Magic ID修复完成！现在所有PMI记录都使用来自old_t_zoom_pmi.mg_id的正确魔链ID。' as completion_message;

-- ========================================
-- 第五部分：迁移脚本修复建议
-- ========================================

SELECT '=== 迁移脚本修复建议 ===' as migration_fix;

SELECT 
    'Migration Script Update Required' as suggestion_type,
    'Current: magic_id = pmi_number' as current_approach,
    'Correct: magic_id = old_t_zoom_pmi.mg_id' as correct_approach,
    'Impact: Ensures magic links remain consistent' as business_impact;

SELECT 
    'SQL Fix for Future Migrations' as sql_example,
    'SELECT ozp.mg_id as magic_id FROM old_t_zoom_pmi ozp' as correct_sql,
    'Instead of: pmi_number as magic_id' as wrong_sql;
