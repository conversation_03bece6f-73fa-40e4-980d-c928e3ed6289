-- 修复午夜窗口关闭Bug的紧急修复脚本
-- 问题：end_time = 00:00:00 的窗口在午夜被错误关闭
-- 原因：查询逻辑错误地将 00:00:00 >= 00:00:00 判断为应该关闭

USE zoombusV;

-- 1. 分析被误关闭的窗口
SELECT '=== 分析被误关闭的窗口 ===' as step;

SELECT 
    'Midnight Window Close Bug Analysis' as analysis_type,
    COUNT(*) as total_affected_windows,
    COUNT(CASE WHEN psw.end_time = '00:00:00' THEN 1 END) as midnight_end_time_windows,
    COUNT(CASE WHEN psw.updated_at >= '2025-08-22 00:00:00' AND psw.updated_at <= '2025-08-22 00:01:00' THEN 1 END) as closed_at_midnight,
    COUNT(CASE WHEN psw.end_date > CURDATE() THEN 1 END) as should_be_active
FROM t_pmi_schedule_windows psw
WHERE psw.status = 'COMPLETED'
AND psw.end_time = '00:00:00'
AND psw.end_date > CURDATE();

-- 2. 显示所有被误关闭的窗口详情
SELECT '=== 被误关闭的窗口详情 ===' as step;

SELECT 
    psw.id,
    pr.pmi_number,
    pr.billing_mode,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    psw.updated_at,
    ps.is_all_day,
    ps.repeat_type,
    DATEDIFF(psw.end_date, CURDATE()) as days_remaining,
    CASE 
        WHEN psw.end_date > CURDATE() THEN 'SHOULD_BE_ACTIVE'
        WHEN psw.end_date = CURDATE() AND psw.end_time = '00:00:00' THEN 'SHOULD_BE_ACTIVE_UNTIL_TOMORROW'
        ELSE 'CORRECTLY_CLOSED'
    END as correct_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_schedules ps ON psw.schedule_id = ps.id
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.status = 'COMPLETED'
AND psw.end_time = '00:00:00'
AND psw.end_date > CURDATE()
ORDER BY psw.updated_at DESC;

-- 3. 检查这些窗口的PMI状态
SELECT '=== 被误关闭窗口的PMI状态 ===' as step;

SELECT 
    pr.id as pmi_id,
    pr.pmi_number,
    pr.billing_mode,
    pr.status as pmi_status,
    pr.current_window_id,
    pr.window_expire_time,
    psw.id as window_id,
    psw.status as window_status,
    psw.end_date,
    CASE 
        WHEN pr.current_window_id = psw.id THEN 'WINDOW_IS_CURRENT'
        WHEN pr.current_window_id IS NULL THEN 'NO_CURRENT_WINDOW'
        ELSE 'DIFFERENT_CURRENT_WINDOW'
    END as window_relationship
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.status = 'COMPLETED'
AND psw.end_time = '00:00:00'
AND psw.end_date > CURDATE()
ORDER BY pr.id;

-- 4. 修复被误关闭的窗口
UPDATE t_pmi_schedule_windows 
SET 
    status = 'ACTIVE',
    updated_at = NOW(),
    actual_end_time = NULL
WHERE status = 'COMPLETED'
AND end_time = '00:00:00'
AND end_date > CURDATE();

-- 5. 显示修复结果
SELECT '=== 窗口修复结果 ===' as step;

SELECT 
    'Fixed Windows' as metric,
    ROW_COUNT() as count;

-- 6. 修复相关PMI的状态
-- 对于被误关闭窗口的PMI，如果当前没有活跃窗口，需要重新激活
UPDATE t_pmi_records pr
SET 
    status = 'ACTIVE',
    billing_mode = 'LONG',
    current_window_id = (
        SELECT psw.id 
        FROM t_pmi_schedule_windows psw 
        WHERE psw.pmi_record_id = pr.id 
        AND psw.status = 'ACTIVE' 
        ORDER BY psw.window_date DESC, psw.id DESC 
        LIMIT 1
    ),
    window_expire_time = (
        SELECT TIMESTAMP(psw.end_date, psw.end_time)
        FROM t_pmi_schedule_windows psw 
        WHERE psw.pmi_record_id = pr.id 
        AND psw.status = 'ACTIVE' 
        ORDER BY psw.window_date DESC, psw.id DESC 
        LIMIT 1
    ),
    updated_at = NOW()
WHERE pr.id IN (
    SELECT DISTINCT psw.pmi_record_id
    FROM t_pmi_schedule_windows psw
    WHERE psw.status = 'ACTIVE'
    AND psw.end_time = '00:00:00'
    AND psw.end_date > CURDATE()
    AND psw.updated_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
)
AND pr.status != 'ACTIVE';

-- 7. 验证修复结果
SELECT '=== 修复验证 ===' as step;

SELECT 
    'Currently Active Windows with end_time 00:00:00' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE'
AND end_time = '00:00:00'
AND end_date > CURDATE()

UNION ALL

SELECT 
    'PMIs with Active Windows' as metric,
    COUNT(DISTINCT pmi_record_id) as count
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE'
AND end_time = '00:00:00'
AND end_date > CURDATE()

UNION ALL

SELECT 
    'PMIs in ACTIVE Status' as metric,
    COUNT(*) as count
FROM t_pmi_records pr
WHERE pr.id IN (
    SELECT DISTINCT psw.pmi_record_id
    FROM t_pmi_schedule_windows psw
    WHERE psw.status = 'ACTIVE'
    AND psw.end_time = '00:00:00'
    AND psw.end_date > CURDATE()
)
AND pr.status = 'ACTIVE';

-- 8. 检查窗口1036的具体修复情况
SELECT '=== 窗口1036修复验证 ===' as step;

SELECT 
    psw.id,
    pr.pmi_number,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status as window_status,
    psw.updated_at,
    pr.status as pmi_status,
    pr.billing_mode,
    pr.current_window_id,
    pr.window_expire_time,
    DATEDIFF(psw.end_date, CURDATE()) as days_remaining,
    CASE 
        WHEN psw.status = 'ACTIVE' AND psw.end_date > CURDATE() THEN 'CORRECTLY_FIXED'
        WHEN psw.status = 'COMPLETED' AND psw.end_date > CURDATE() THEN 'STILL_NEEDS_FIX'
        ELSE 'CHECK_MANUALLY'
    END as fix_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 1036;

-- 9. 最终统计报告
SELECT '=== 最终修复统计报告 ===' as step;

SELECT 
    'Total Windows' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows

UNION ALL

SELECT 
    'Active Windows with end_time 00:00:00' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE' 
AND end_time = '00:00:00'

UNION ALL

SELECT 
    'Completed Windows with future end_date' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status = 'COMPLETED' 
AND end_date > CURDATE()

UNION ALL

SELECT 
    'Windows Fixed in This Operation' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE'
AND end_time = '00:00:00'
AND end_date > CURDATE()
AND updated_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE);

-- 10. 显示修复成功的关键案例
SELECT '=== 修复成功的关键案例 ===' as step;

SELECT 
    psw.id as window_id,
    pr.pmi_number,
    CONCAT('修复前: COMPLETED -> 修复后: ', psw.status) as status_change,
    CONCAT('剩余天数: ', DATEDIFF(psw.end_date, CURDATE()), '天') as remaining_days,
    CONCAT('PMI状态: ', pr.status) as pmi_status,
    CONCAT('计费模式: ', pr.billing_mode) as billing_mode
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id IN (1036, 1026, 927, 963, 1013)
AND psw.status = 'ACTIVE'
ORDER BY psw.id;
