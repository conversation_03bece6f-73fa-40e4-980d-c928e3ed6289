-- 修复移植后窗口错误关闭的问题
-- 执行日期: 2025-08-20
-- 问题描述: 移植的t_pmi_schedule_windows在移植日23:59:59全部关闭了

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 问题分析和修复
-- ========================================

-- 1. 分析当前窗口状态
SELECT '=== 当前窗口状态分析 ===' as analysis;

SELECT 
    'window_status_summary' as type,
    status,
    COUNT(*) as count,
    MIN(end_date) as min_end_date,
    MAX(end_date) as max_end_date
FROM t_pmi_schedule_windows 
GROUP BY status;

-- 2. 分析窗口关闭逻辑的问题
SELECT '=== 窗口关闭逻辑验证 ===' as analysis;

-- 模拟2025-08-19 23:59:59的查询逻辑
SELECT 
    'logic_test_2025-08-19_23:59:59' as test_scenario,
    COUNT(*) as total_windows,
    -- 条件1：endDate IS NULL AND currentDate = windowDate AND currentTime >= endTime
    COUNT(CASE WHEN (end_date IS NULL AND '2025-08-19' = window_date AND '23:59:59' >= end_time) THEN 1 END) as condition1_matches,
    -- 条件2：endDate IS NOT NULL AND currentDate > endDate  
    COUNT(CASE WHEN (end_date IS NOT NULL AND '2025-08-19' > end_date) THEN 1 END) as condition2_matches,
    -- 条件3：endDate IS NOT NULL AND currentDate = endDate AND currentTime >= endTime
    COUNT(CASE WHEN (end_date IS NOT NULL AND '2025-08-19' = end_date AND '23:59:59' >= end_time) THEN 1 END) as condition3_matches,
    -- 总体应该关闭的窗口数
    COUNT(CASE WHEN (
        (end_date IS NULL AND '2025-08-19' = window_date AND '23:59:59' >= end_time) OR 
        (end_date IS NOT NULL AND '2025-08-19' > end_date) OR 
        (end_date IS NOT NULL AND '2025-08-19' = end_date AND '23:59:59' >= end_time)
    ) THEN 1 END) as should_close_count
FROM t_pmi_schedule_windows;

-- 3. 检查是否有end_date为移植日的窗口
SELECT 
    'windows_ending_on_migration_date' as type,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE end_date = '2025-08-19';

-- 4. 恢复被错误关闭的窗口（已经在之前执行过，这里只是记录）
SELECT '=== 窗口恢复操作 ===' as operation;

-- 检查当前状态
SELECT 
    'current_window_status' as type,
    status,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
GROUP BY status;

-- 检查当前计划状态
SELECT 
    'current_schedule_status' as type,
    status,
    COUNT(*) as count
FROM t_pmi_schedules 
GROUP BY status;

-- ========================================
-- 预防措施：优化移植脚本逻辑
-- ========================================

-- 5. 创建一个视图来正确判断窗口状态
CREATE OR REPLACE VIEW v_correct_window_status AS
SELECT 
    w.*,
    CASE 
        -- 对于有end_date的窗口，基于end_date判断状态
        WHEN w.end_date IS NOT NULL THEN
            CASE 
                WHEN CURDATE() > w.end_date THEN 'COMPLETED'
                WHEN CURDATE() = w.end_date AND CURTIME() >= w.end_time THEN 'COMPLETED'
                WHEN CURDATE() = w.window_date AND CURTIME() >= w.start_time THEN 'ACTIVE'
                WHEN CURDATE() > w.window_date THEN 'ACTIVE'
                ELSE 'PENDING'
            END
        -- 对于没有end_date的窗口，基于window_date判断状态
        ELSE
            CASE 
                WHEN CURDATE() > w.window_date THEN 'COMPLETED'
                WHEN CURDATE() = w.window_date AND CURTIME() >= w.end_time THEN 'COMPLETED'
                WHEN CURDATE() = w.window_date AND CURTIME() >= w.start_time THEN 'ACTIVE'
                ELSE 'PENDING'
            END
    END as correct_status,
    -- 标记状态是否正确
    CASE 
        WHEN w.end_date IS NOT NULL THEN
            CASE 
                WHEN CURDATE() > w.end_date THEN 'COMPLETED'
                WHEN CURDATE() = w.end_date AND CURTIME() >= w.end_time THEN 'COMPLETED'
                WHEN CURDATE() = w.window_date AND CURTIME() >= w.start_time THEN 'ACTIVE'
                WHEN CURDATE() > w.window_date THEN 'ACTIVE'
                ELSE 'PENDING'
            END
        ELSE
            CASE 
                WHEN CURDATE() > w.window_date THEN 'COMPLETED'
                WHEN CURDATE() = w.window_date AND CURTIME() >= w.end_time THEN 'COMPLETED'
                WHEN CURDATE() = w.window_date AND CURTIME() >= w.start_time THEN 'ACTIVE'
                ELSE 'PENDING'
            END
    END = w.status as status_is_correct
FROM t_pmi_schedule_windows w;

-- 6. 检查状态不正确的窗口
SELECT '=== 状态不正确的窗口 ===' as check_result;

SELECT 
    id,
    window_date,
    end_date,
    start_time,
    end_time,
    status as current_status,
    correct_status,
    status_is_correct
FROM v_correct_window_status 
WHERE NOT status_is_correct
LIMIT 10;

-- 7. 创建一个函数来安全地关闭窗口
DELIMITER //
DROP FUNCTION IF EXISTS safe_close_window_check //
CREATE FUNCTION safe_close_window_check(
    p_window_id BIGINT,
    p_current_date DATE,
    p_current_time TIME
) RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_window_date DATE;
    DECLARE v_end_date DATE;
    DECLARE v_start_time TIME;
    DECLARE v_end_time TIME;
    DECLARE v_status VARCHAR(50);
    DECLARE v_should_close BOOLEAN DEFAULT FALSE;
    DECLARE v_reason VARCHAR(500);
    DECLARE v_result JSON;
    
    -- 获取窗口信息
    SELECT window_date, end_date, start_time, end_time, status
    INTO v_window_date, v_end_date, v_start_time, v_end_time, v_status
    FROM t_pmi_schedule_windows
    WHERE id = p_window_id;
    
    -- 检查窗口是否存在
    IF v_window_date IS NULL THEN
        SET v_result = JSON_OBJECT(
            'window_id', p_window_id,
            'should_close', FALSE,
            'reason', 'WINDOW_NOT_FOUND',
            'current_date', p_current_date,
            'current_time', p_current_time
        );
        RETURN v_result;
    END IF;
    
    -- 检查窗口状态
    IF v_status != 'ACTIVE' THEN
        SET v_result = JSON_OBJECT(
            'window_id', p_window_id,
            'should_close', FALSE,
            'reason', CONCAT('WINDOW_NOT_ACTIVE: ', v_status),
            'current_date', p_current_date,
            'current_time', p_current_time,
            'window_date', v_window_date,
            'end_date', v_end_date,
            'start_time', v_start_time,
            'end_time', v_end_time
        );
        RETURN v_result;
    END IF;
    
    -- 应用关闭逻辑
    IF v_end_date IS NULL THEN
        -- 没有end_date的窗口：在window_date当天的end_time时关闭
        IF p_current_date = v_window_date AND p_current_time >= v_end_time THEN
            SET v_should_close = TRUE;
            SET v_reason = 'SINGLE_DAY_WINDOW_EXPIRED';
        ELSEIF p_current_date > v_window_date THEN
            SET v_should_close = TRUE;
            SET v_reason = 'SINGLE_DAY_WINDOW_PAST_DATE';
        ELSE
            SET v_should_close = FALSE;
            SET v_reason = 'SINGLE_DAY_WINDOW_NOT_EXPIRED';
        END IF;
    ELSE
        -- 有end_date的窗口：在end_date当天的end_time时关闭
        IF p_current_date > v_end_date THEN
            SET v_should_close = TRUE;
            SET v_reason = 'MULTI_DAY_WINDOW_PAST_END_DATE';
        ELSEIF p_current_date = v_end_date AND p_current_time >= v_end_time THEN
            SET v_should_close = TRUE;
            SET v_reason = 'MULTI_DAY_WINDOW_END_TIME_REACHED';
        ELSE
            SET v_should_close = FALSE;
            SET v_reason = 'MULTI_DAY_WINDOW_NOT_EXPIRED';
        END IF;
    END IF;
    
    SET v_result = JSON_OBJECT(
        'window_id', p_window_id,
        'should_close', v_should_close,
        'reason', v_reason,
        'current_date', p_current_date,
        'current_time', p_current_time,
        'window_date', v_window_date,
        'end_date', v_end_date,
        'start_time', v_start_time,
        'end_time', v_end_time,
        'status', v_status
    );
    
    RETURN v_result;
END //
DELIMITER ;

-- 8. 测试安全关闭函数
SELECT '=== 安全关闭函数测试 ===' as test_result;

-- 测试移植日23:59:59的情况
SELECT 
    id,
    safe_close_window_check(id, '2025-08-19', '23:59:59') as close_check_result
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE'
LIMIT 5;

-- 提交事务
COMMIT;

-- ========================================
-- 总结和建议
-- ========================================

SELECT '=== 修复总结 ===' as summary;

SELECT 
    '问题根源' as item,
    '移植脚本将所有窗口的window_date设置为移植日，但关闭逻辑错误地在移植日关闭了这些窗口' as description
UNION ALL
SELECT 
    '修复措施' as item,
    '1. 恢复被错误关闭的窗口状态为ACTIVE; 2. 恢复对应计划状态为ACTIVE; 3. 创建安全关闭检查函数' as description
UNION ALL
SELECT 
    '预防措施' as item,
    '1. 优化移植脚本逻辑; 2. 增强窗口关闭验证; 3. 添加详细日志记录' as description;

SELECT 'Migration window close issue fix completed!' as final_message;
