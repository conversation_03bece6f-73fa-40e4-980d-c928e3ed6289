-- PMI窗口误关闭修复脚本
-- 修复日期: 2025-08-22
-- 问题: PmiBillingConsistencyService错误使用windowDate而不是endDate计算过期时间
-- 导致20个长期窗口被误关闭

-- 备份被误关闭的窗口数据
CREATE TABLE IF NOT EXISTS t_pmi_schedule_windows_backup_20250822 AS
SELECT * FROM t_pmi_schedule_windows 
WHERE id IN (
    1026, 927, 2049, 963, 1013, 962, 946, 983, 966, 989, 
    1031, 892, 923, 907, 1030, 1023, 954, 2048, 985, 991
);

-- 显示备份信息
SELECT 
    '备份完成' as status,
    COUNT(*) as backup_count,
    MIN(actual_end_time) as earliest_close_time,
    MAX(actual_end_time) as latest_close_time
FROM t_pmi_schedule_windows_backup_20250822;

-- 恢复被误关闭的窗口
UPDATE t_pmi_schedule_windows 
SET 
    status = 'ACTIVE',
    actual_end_time = NULL,
    updated_at = NOW()
WHERE id IN (
    1026, 927, 2049, 963, 1013, 962, 946, 983, 966, 989, 
    1031, 892, 923, 907, 1030, 1023, 954, 2048, 985, 991
)
AND status = 'COMPLETED'
AND actual_end_time >= '2025-08-22 14:00:00';

-- 验证恢复结果
SELECT 
    '恢复验证' as check_type,
    COUNT(*) as recovered_count,
    GROUP_CONCAT(id ORDER BY id) as recovered_window_ids
FROM t_pmi_schedule_windows 
WHERE id IN (
    1026, 927, 2049, 963, 1013, 962, 946, 983, 966, 989, 
    1031, 892, 923, 907, 1030, 1023, 954, 2048, 985, 991
)
AND status = 'ACTIVE';

-- 检查需要恢复计费模式的PMI记录
SELECT DISTINCT
    p.id as pmi_record_id,
    p.pmi_number,
    p.billing_mode,
    p.status as pmi_status,
    p.current_window_id,
    p.window_expire_time,
    COUNT(w.id) as active_windows_count
FROM t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.id = w.pmi_record_id
WHERE w.id IN (
    1026, 927, 2049, 963, 1013, 962, 946, 983, 966, 989, 
    1031, 892, 923, 907, 1030, 1023, 954, 2048, 985, 991
)
AND w.status = 'ACTIVE'
GROUP BY p.id, p.pmi_number, p.billing_mode, p.status, p.current_window_id, p.window_expire_time
ORDER BY p.id;
