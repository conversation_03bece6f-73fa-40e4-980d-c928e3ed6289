-- 修复ONCE类型计划的窗口问题
-- 对于ONCE类型的计划，应该只有一个窗口覆盖整个计划期间
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一部分：问题分析
-- ========================================

SELECT '=== ONCE类型计划窗口问题分析 ===' as step;

-- 查找所有ONCE类型的计划及其窗口数量
SELECT 
    'ONCE Schedule Analysis' as check_type,
    ps.id as schedule_id,
    ps.pmi_record_id,
    ps.name,
    ps.start_date,
    ps.end_date,
    ps.repeat_type,
    COUNT(psw.id) as window_count
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.repeat_type = 'ONCE'
GROUP BY ps.id, ps.pmi_record_id, ps.name, ps.start_date, ps.end_date, ps.repeat_type
HAVING window_count > 1
ORDER BY window_count DESC
LIMIT 10;

-- 特别检查计划927
SELECT 
    'Schedule 927 Detail' as check_type,
    ps.id,
    ps.pmi_record_id,
    ps.start_date,
    ps.end_date,
    ps.repeat_type,
    ps.is_all_day,
    COUNT(psw.id) as window_count,
    MIN(psw.window_date) as first_window_date,
    MAX(psw.end_date) as last_window_end_date
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id = 927
GROUP BY ps.id;

-- ========================================
-- 第二部分：修复ONCE类型计划的窗口
-- ========================================

SELECT '=== 开始修复ONCE类型计划窗口 ===' as step;

-- 创建临时表存储需要修复的计划
CREATE TEMPORARY TABLE temp_once_schedules_to_fix (
    schedule_id BIGINT,
    pmi_record_id BIGINT,
    start_date DATE,
    end_date DATE,
    is_all_day BOOLEAN,
    start_time TIME,
    duration_minutes INT,
    window_count INT,
    INDEX idx_schedule_id (schedule_id)
);

-- 填充需要修复的ONCE类型计划
INSERT INTO temp_once_schedules_to_fix (
    schedule_id, pmi_record_id, start_date, end_date, 
    is_all_day, start_time, duration_minutes, window_count
)
SELECT 
    ps.id,
    ps.pmi_record_id,
    ps.start_date,
    ps.end_date,
    ps.is_all_day,
    ps.start_time,
    ps.duration_minutes,
    COUNT(psw.id) as window_count
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.repeat_type = 'ONCE'
GROUP BY ps.id, ps.pmi_record_id, ps.start_date, ps.end_date, ps.is_all_day, ps.start_time, ps.duration_minutes
HAVING window_count > 1;

SELECT 
    'Schedules to Fix' as check_type,
    COUNT(*) as schedules_count,
    SUM(window_count) as total_windows_to_fix
FROM temp_once_schedules_to_fix;

-- 删除多余的窗口，保留每个计划的第一个窗口
DELETE psw FROM t_pmi_schedule_windows psw
JOIN temp_once_schedules_to_fix tosf ON psw.schedule_id = tosf.schedule_id
WHERE psw.id NOT IN (
    SELECT min_id FROM (
        SELECT MIN(psw2.id) as min_id
        FROM t_pmi_schedule_windows psw2
        JOIN temp_once_schedules_to_fix tosf2 ON psw2.schedule_id = tosf2.schedule_id
        GROUP BY psw2.schedule_id
    ) as subquery
);

SELECT 
    'Deleted Extra Windows' as result_type,
    ROW_COUNT() as deleted_windows;

-- 更新保留的窗口，使其覆盖整个计划期间
UPDATE t_pmi_schedule_windows psw
JOIN temp_once_schedules_to_fix tosf ON psw.schedule_id = tosf.schedule_id
SET
    psw.window_date = tosf.start_date,
    psw.end_date = tosf.end_date,
    psw.start_time = CASE
        WHEN tosf.is_all_day = 1 THEN '00:00:00'
        ELSE tosf.start_time
    END,
    psw.end_time = CASE
        WHEN tosf.is_all_day = 1 THEN '23:59:59'
        ELSE TIME(ADDTIME(tosf.start_time, SEC_TO_TIME(tosf.duration_minutes * 60)))
    END,
    psw.updated_at = NOW()
WHERE psw.id IN (
    SELECT min_id FROM (
        SELECT MIN(psw2.id) as min_id
        FROM t_pmi_schedule_windows psw2
        JOIN temp_once_schedules_to_fix tosf2 ON psw2.schedule_id = tosf2.schedule_id
        GROUP BY psw2.schedule_id
    ) as subquery
);

SELECT 
    'Updated Windows' as result_type,
    ROW_COUNT() as updated_windows;

-- ========================================
-- 第三部分：验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 验证ONCE类型计划现在都只有一个窗口
SELECT 
    'ONCE Schedules After Fix' as check_type,
    ps.id as schedule_id,
    ps.pmi_record_id,
    ps.start_date as schedule_start,
    ps.end_date as schedule_end,
    COUNT(psw.id) as window_count,
    MIN(psw.window_date) as window_start,
    MAX(psw.end_date) as window_end
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.repeat_type = 'ONCE'
GROUP BY ps.id, ps.pmi_record_id, ps.start_date, ps.end_date
ORDER BY ps.id
LIMIT 10;

-- 特别验证计划927
SELECT 
    'Schedule 927 After Fix' as check_type,
    ps.id,
    ps.start_date as schedule_start,
    ps.end_date as schedule_end,
    ps.repeat_type,
    COUNT(psw.id) as window_count,
    psw.window_date as window_start,
    psw.end_date as window_end,
    psw.start_time,
    psw.end_time,
    psw.status
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id = 927
GROUP BY ps.id, ps.start_date, ps.end_date, ps.repeat_type, psw.window_date, psw.end_date, psw.start_time, psw.end_time, psw.status;

-- 统计修复结果
SELECT 
    'Fix Summary' as summary_type,
    COUNT(CASE WHEN ps.repeat_type = 'ONCE' THEN 1 END) as total_once_schedules,
    COUNT(CASE WHEN ps.repeat_type = 'ONCE' AND window_count = 1 THEN 1 END) as fixed_schedules,
    COUNT(CASE WHEN ps.repeat_type = 'ONCE' AND window_count > 1 THEN 1 END) as still_problematic
FROM (
    SELECT 
        ps.id,
        ps.repeat_type,
        COUNT(psw.id) as window_count
    FROM t_pmi_schedules ps
    LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
    WHERE ps.repeat_type = 'ONCE'
    GROUP BY ps.id, ps.repeat_type
) as schedule_stats
JOIN t_pmi_schedules ps ON schedule_stats.id = ps.id;

-- 清理临时表
DROP TEMPORARY TABLE temp_once_schedules_to_fix;

-- 提交事务
COMMIT;

-- ========================================
-- 第四部分：最终报告
-- ========================================

SELECT '=== ONCE类型计划窗口修复完成 ===' as final_report;

-- 显示修复后的统计
SELECT 
    'Final Statistics' as report_type,
    ps.repeat_type,
    COUNT(ps.id) as schedule_count,
    AVG(window_count) as avg_windows_per_schedule,
    MIN(window_count) as min_windows,
    MAX(window_count) as max_windows
FROM t_pmi_schedules ps
JOIN (
    SELECT 
        schedule_id,
        COUNT(*) as window_count
    FROM t_pmi_schedule_windows
    GROUP BY schedule_id
) window_stats ON ps.id = window_stats.schedule_id
GROUP BY ps.repeat_type
ORDER BY ps.repeat_type;

SELECT 'ONCE类型计划窗口修复完成！每个ONCE计划现在都只有一个窗口。' as final_message;
