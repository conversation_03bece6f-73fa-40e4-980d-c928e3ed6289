-- 简化版修复ONCE类型计划的窗口问题
-- 对于ONCE类型的计划，应该只有一个窗口覆盖整个计划期间
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一部分：问题分析
-- ========================================

SELECT '=== ONCE类型计划窗口问题分析 ===' as step;

-- 查找所有ONCE类型的计划及其窗口数量
SELECT 
    'ONCE Schedule Analysis' as check_type,
    ps.id as schedule_id,
    ps.pmi_record_id,
    ps.name,
    ps.start_date,
    ps.end_date,
    ps.repeat_type,
    COUNT(psw.id) as window_count
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.repeat_type = 'ONCE'
GROUP BY ps.id, ps.pmi_record_id, ps.name, ps.start_date, ps.end_date, ps.repeat_type
HAVING window_count > 1
ORDER BY window_count DESC
LIMIT 10;

-- 特别检查计划927
SELECT 
    'Schedule 927 Before Fix' as check_type,
    ps.id,
    ps.pmi_record_id,
    ps.start_date,
    ps.end_date,
    ps.repeat_type,
    ps.is_all_day,
    COUNT(psw.id) as window_count,
    MIN(psw.window_date) as first_window_date,
    MAX(psw.end_date) as last_window_end_date
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id = 927
GROUP BY ps.id;

-- ========================================
-- 第二部分：修复策略 - 分步执行
-- ========================================

SELECT '=== 开始修复ONCE类型计划窗口 ===' as step;

-- 步骤1：为每个ONCE类型的计划，删除除了第一个窗口之外的所有窗口
-- 首先创建一个表来存储要保留的窗口ID
CREATE TEMPORARY TABLE temp_windows_to_keep (
    schedule_id BIGINT,
    window_id BIGINT,
    PRIMARY KEY (schedule_id)
);

-- 为每个ONCE类型的计划找到最小的窗口ID（要保留的）
INSERT INTO temp_windows_to_keep (schedule_id, window_id)
SELECT 
    ps.id as schedule_id,
    MIN(psw.id) as window_id
FROM t_pmi_schedules ps
JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.repeat_type = 'ONCE'
GROUP BY ps.id;

SELECT 
    'Windows to Keep' as check_type,
    COUNT(*) as schedules_count
FROM temp_windows_to_keep;

-- 删除不需要保留的窗口
DELETE psw FROM t_pmi_schedule_windows psw
JOIN t_pmi_schedules ps ON psw.schedule_id = ps.id
LEFT JOIN temp_windows_to_keep twtk ON psw.schedule_id = twtk.schedule_id AND psw.id = twtk.window_id
WHERE ps.repeat_type = 'ONCE' 
AND twtk.window_id IS NULL;

SELECT 
    'Deleted Extra Windows' as result_type,
    ROW_COUNT() as deleted_windows;

-- 步骤2：更新保留的窗口，使其覆盖整个计划期间
UPDATE t_pmi_schedule_windows psw
JOIN t_pmi_schedules ps ON psw.schedule_id = ps.id
JOIN temp_windows_to_keep twtk ON psw.id = twtk.window_id
SET 
    psw.window_date = ps.start_date,
    psw.end_date = ps.end_date,
    psw.start_time = CASE 
        WHEN ps.is_all_day = 1 THEN '00:00:00'
        ELSE COALESCE(ps.start_time, '00:00:00')
    END,
    psw.end_time = CASE 
        WHEN ps.is_all_day = 1 THEN '23:59:59'
        ELSE CASE 
            WHEN ps.start_time IS NOT NULL AND ps.duration_minutes IS NOT NULL 
            THEN TIME(ADDTIME(ps.start_time, SEC_TO_TIME(ps.duration_minutes * 60)))
            ELSE '23:59:59'
        END
    END,
    psw.updated_at = NOW()
WHERE ps.repeat_type = 'ONCE';

SELECT 
    'Updated Windows' as result_type,
    ROW_COUNT() as updated_windows;

-- ========================================
-- 第三部分：验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 验证ONCE类型计划现在都只有一个窗口
SELECT 
    'ONCE Schedules After Fix' as check_type,
    ps.id as schedule_id,
    ps.pmi_record_id,
    ps.start_date as schedule_start,
    ps.end_date as schedule_end,
    COUNT(psw.id) as window_count,
    MIN(psw.window_date) as window_start,
    MAX(psw.end_date) as window_end
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.repeat_type = 'ONCE'
GROUP BY ps.id, ps.pmi_record_id, ps.start_date, ps.end_date
HAVING window_count > 1
ORDER BY ps.id
LIMIT 5;

-- 特别验证计划927
SELECT 
    'Schedule 927 After Fix' as check_type,
    ps.id,
    ps.start_date as schedule_start,
    ps.end_date as schedule_end,
    ps.repeat_type,
    COUNT(psw.id) as window_count,
    psw.window_date as window_start,
    psw.end_date as window_end,
    psw.start_time,
    psw.end_time,
    psw.status
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id = 927
GROUP BY ps.id, ps.start_date, ps.end_date, ps.repeat_type, psw.window_date, psw.end_date, psw.start_time, psw.end_time, psw.status;

-- 统计修复结果
SELECT 
    'Fix Summary' as summary_type,
    ps.repeat_type,
    COUNT(ps.id) as total_schedules,
    COUNT(CASE WHEN window_count = 1 THEN 1 END) as single_window_schedules,
    COUNT(CASE WHEN window_count > 1 THEN 1 END) as multi_window_schedules,
    AVG(window_count) as avg_windows_per_schedule
FROM t_pmi_schedules ps
JOIN (
    SELECT 
        schedule_id,
        COUNT(*) as window_count
    FROM t_pmi_schedule_windows
    GROUP BY schedule_id
) window_stats ON ps.id = window_stats.schedule_id
GROUP BY ps.repeat_type
ORDER BY ps.repeat_type;

-- 清理临时表
DROP TEMPORARY TABLE temp_windows_to_keep;

-- 提交事务
COMMIT;

-- ========================================
-- 第四部分：最终报告
-- ========================================

SELECT '=== ONCE类型计划窗口修复完成 ===' as final_report;

-- 验证计划927的最终状态
SELECT 
    'Final Schedule 927 Status' as final_check,
    ps.id,
    ps.pmi_record_id,
    ps.start_date,
    ps.end_date,
    ps.repeat_type,
    COUNT(psw.id) as window_count,
    psw.window_date,
    psw.end_date as window_end_date,
    psw.start_time,
    psw.end_time
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id = 927
GROUP BY ps.id, ps.pmi_record_id, ps.start_date, ps.end_date, ps.repeat_type, psw.window_date, psw.end_date, psw.start_time, psw.end_time;

SELECT 'ONCE类型计划窗口修复完成！每个ONCE计划现在都只有一个窗口覆盖整个计划期间。' as final_message;
