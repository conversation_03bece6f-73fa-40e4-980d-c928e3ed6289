-- 修复PMI冲突问题
-- 问题：PMI ********** 无法使用，提示"由另一用户主持"

USE zoombusV;

-- 1. 查看当前问题PMI的状态
SELECT 'Current PMI Status:' as info;
SELECT id, pmi_number, pmi_password, status, current_zoom_user_id, last_used_at 
FROM t_pmi_records WHERE pmi_number = '**********';

-- 2. 查看分配的ZoomUser状态
SELECT 'Assigned ZoomUser Status:' as info;
SELECT id, email, zoom_user_id, original_pmi, current_pmi, usage_status, current_meeting_id 
FROM t_zoom_accounts WHERE id = 73;

-- 3. 查看相关的会议记录
SELECT 'Related Meeting:' as info;
SELECT id, zoom_meeting_id, zoom_meeting_uuid, status, assigned_zoom_user_id, start_time 
FROM t_zoom_meetings WHERE id = 11;

-- 4. 解决方案1：回收ZoomUser并重置状态
UPDATE t_zoom_accounts 
SET usage_status = 'AVAILABLE',
    current_meeting_id = NULL,
    current_pmi = original_pmi,
    last_used_time = NOW()
WHERE id = 73;

-- 5. 解决方案2：将会议状态设置为CANCELLED
UPDATE t_zoom_meetings 
SET status = 'CANCELLED',
    updated_at = NOW()
WHERE id = 11;

-- 6. 解决方案3：重置PMI记录的ZoomUser分配
UPDATE t_pmi_records 
SET current_zoom_user_id = NULL,
    last_used_at = NULL
WHERE pmi_number = '**********';

-- 7. 验证修复结果
SELECT 'After Fix - PMI Status:' as info;
SELECT id, pmi_number, status, current_zoom_user_id FROM t_pmi_records WHERE pmi_number = '**********';

SELECT 'After Fix - ZoomUser Status:' as info;
SELECT id, email, usage_status, current_meeting_id, current_pmi FROM t_zoom_accounts WHERE id = 73;

SELECT 'After Fix - Meeting Status:' as info;
SELECT id, zoom_meeting_id, status FROM t_zoom_meetings WHERE id = 11;

-- 8. 查找其他可用的ZoomUser
SELECT 'Available ZoomUsers:' as info;
SELECT id, email, zoom_user_id, usage_status, original_pmi, current_pmi 
FROM t_zoom_accounts 
WHERE usage_status = 'AVAILABLE' 
  AND user_type = 'LICENSED' 
  AND account_usage = 'PUBLIC_HOST' 
  AND status = 'ACTIVE'
LIMIT 5;
