-- 修复PMI数据问题脚本
-- 1. 修复LONG类型PMI记录的窗口字段问题
-- 2. 验证magic_id是否正确取自mg_id
-- 执行日期: 2025-08-20

USE zoombusV;

-- ========================================
-- 第一部分：问题分析
-- ========================================

SELECT '=== PMI数据问题分析 ===' as step;

-- 检查LONG类型PMI记录的窗口字段问题
SELECT 
    'LONG PMI Window Fields Issue' as check_type,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN current_window_id IS NULL THEN 1 END) as missing_current_window_id,
    COUNT(CASE WHEN window_expire_time IS NULL THEN 1 END) as missing_window_expire_time,
    COUNT(CASE WHEN current_window_id IS NULL OR window_expire_time IS NULL THEN 1 END) as problematic_records
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- 检查magic_id字段
SELECT 
    'Magic ID Check' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN magic_id IS NOT NULL AND magic_id != '' THEN 1 END) as has_magic_id,
    COUNT(CASE WHEN magic_id IS NULL OR magic_id = '' THEN 1 END) as missing_magic_id
FROM t_pmi_records;

-- 检查老数据中的mg_id字段
SELECT 
    'Old Data mg_id Check' as check_type,
    COUNT(*) as total_old_records,
    COUNT(CASE WHEN mg_id IS NOT NULL AND mg_id != '' THEN 1 END) as has_mg_id,
    COUNT(CASE WHEN mg_id IS NULL OR mg_id = '' THEN 1 END) as missing_mg_id
FROM old_t_zoom_pmi;

-- 显示有问题的LONG类型PMI记录
SELECT 
    'Problematic LONG PMI Records' as check_type,
    id, pmi_number, magic_id, billing_mode, current_window_id, window_expire_time, created_at
FROM t_pmi_records 
WHERE billing_mode = 'LONG' 
AND (current_window_id IS NULL OR window_expire_time IS NULL)
ORDER BY created_at DESC
LIMIT 10;

-- ========================================
-- 第二部分：验证magic_id来源
-- ========================================

SELECT '=== 验证magic_id来源 ===' as step;

-- 对比magic_id和mg_id的对应关系
SELECT
    'Magic ID vs MG ID Comparison' as comparison_type,
    otp.pmi as old_pmi_number,
    otp.mg_id as old_mg_id,
    pr.magic_id as new_magic_id,
    pr.billing_mode,
    CASE
        WHEN otp.mg_id = pr.magic_id THEN 'MATCH'
        WHEN otp.mg_id IS NULL OR otp.mg_id = '' THEN 'OLD_MISSING'
        WHEN pr.magic_id IS NULL OR pr.magic_id = '' THEN 'NEW_MISSING'
        ELSE 'MISMATCH'
    END as match_status
FROM old_t_zoom_pmi otp
JOIN t_pmi_records pr ON otp.pmi = pr.pmi_number
WHERE otp.mg_id IS NOT NULL AND otp.mg_id != ''
ORDER BY match_status, otp.pmi
LIMIT 10;

-- 统计magic_id匹配情况
SELECT
    'Magic ID Match Statistics' as stat_type,
    COUNT(*) as total_matched_records,
    COUNT(CASE WHEN otp.mg_id = pr.magic_id THEN 1 END) as exact_matches,
    COUNT(CASE WHEN otp.mg_id != pr.magic_id THEN 1 END) as mismatches,
    COUNT(CASE WHEN otp.mg_id IS NULL OR otp.mg_id = '' THEN 1 END) as old_missing,
    COUNT(CASE WHEN pr.magic_id IS NULL OR pr.magic_id = '' THEN 1 END) as new_missing
FROM old_t_zoom_pmi otp
JOIN t_pmi_records pr ON otp.pmi = pr.pmi_number;

-- ========================================
-- 第三部分：修复magic_id问题
-- ========================================

SELECT '=== 修复magic_id问题 ===' as step;

-- 更新缺失或错误的magic_id
UPDATE t_pmi_records pr
JOIN old_t_zoom_pmi otp ON pr.pmi_number = otp.pmi
SET pr.magic_id = otp.mg_id
WHERE otp.mg_id IS NOT NULL
AND otp.mg_id != ''
AND (pr.magic_id IS NULL OR pr.magic_id = '' OR pr.magic_id != otp.mg_id);

SELECT ROW_COUNT() as magic_id_updated_count;

-- ========================================
-- 第四部分：修复LONG类型PMI的窗口问题
-- ========================================

SELECT '=== 修复LONG类型PMI窗口问题 ===' as step;

-- 为LONG类型PMI创建对应的计划记录（如果不存在）
INSERT INTO t_pmi_schedules (
    pmi_record_id,
    schedule_type,
    start_date,
    end_date,
    is_active,
    created_at,
    updated_at
)
SELECT 
    pr.id as pmi_record_id,
    'LONG_TERM' as schedule_type,
    CURDATE() as start_date,
    DATE_ADD(CURDATE(), INTERVAL 1 YEAR) as end_date,
    1 as is_active,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_records pr
LEFT JOIN t_pmi_schedules ps ON pr.id = ps.pmi_record_id
WHERE pr.billing_mode = 'LONG'
AND ps.id IS NULL;

SELECT ROW_COUNT() as long_schedules_created;

-- 为新创建的LONG类型计划创建窗口记录
INSERT INTO t_pmi_schedule_windows (
    schedule_id,
    window_start,
    window_end,
    status,
    created_at,
    updated_at
)
SELECT 
    ps.id as schedule_id,
    CURDATE() as window_start,
    DATE_ADD(CURDATE(), INTERVAL 1 YEAR) as window_end,
    'ACTIVE' as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules ps
JOIN t_pmi_records pr ON ps.pmi_record_id = pr.id
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE pr.billing_mode = 'LONG'
AND ps.schedule_type = 'LONG_TERM'
AND psw.id IS NULL;

SELECT ROW_COUNT() as long_windows_created;

-- 更新LONG类型PMI记录的窗口字段
UPDATE t_pmi_records pr
JOIN t_pmi_schedules ps ON pr.id = ps.pmi_record_id
JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
SET 
    pr.current_window_id = psw.id,
    pr.window_expire_time = psw.window_end,
    pr.updated_at = NOW()
WHERE pr.billing_mode = 'LONG'
AND ps.schedule_type = 'LONG_TERM'
AND psw.status = 'ACTIVE'
AND (pr.current_window_id IS NULL OR pr.window_expire_time IS NULL);

SELECT ROW_COUNT() as long_pmi_updated_count;

-- ========================================
-- 第五部分：验证修复结果
-- ========================================

SELECT '=== 验证修复结果 ===' as step;

-- 重新检查LONG类型PMI记录的窗口字段
SELECT 
    'LONG PMI After Fix' as check_type,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN current_window_id IS NULL THEN 1 END) as missing_current_window_id,
    COUNT(CASE WHEN window_expire_time IS NULL THEN 1 END) as missing_window_expire_time,
    COUNT(CASE WHEN current_window_id IS NULL OR window_expire_time IS NULL THEN 1 END) as still_problematic
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- 重新检查magic_id字段
SELECT 
    'Magic ID After Fix' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN magic_id IS NOT NULL AND magic_id != '' THEN 1 END) as has_magic_id,
    COUNT(CASE WHEN magic_id IS NULL OR magic_id = '' THEN 1 END) as missing_magic_id
FROM t_pmi_records;

-- 验证数据完整性
SELECT 
    'Data Integrity Check' as check_type,
    COUNT(DISTINCT pr.id) as total_pmi_records,
    COUNT(DISTINCT ps.id) as total_schedules,
    COUNT(DISTINCT psw.id) as total_windows,
    COUNT(CASE WHEN pr.billing_mode = 'LONG' THEN 1 END) as long_pmi_count,
    COUNT(CASE WHEN pr.billing_mode = 'HOURLY' THEN 1 END) as hourly_pmi_count
FROM t_pmi_records pr
LEFT JOIN t_pmi_schedules ps ON pr.id = ps.pmi_record_id
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id;

-- 显示修复后的LONG类型PMI记录示例
SELECT 
    'Fixed LONG PMI Examples' as example_type,
    pr.id, pr.pmi_number, pr.magic_id, pr.billing_mode, 
    pr.current_window_id, pr.window_expire_time,
    ps.schedule_type, psw.status
FROM t_pmi_records pr
JOIN t_pmi_schedules ps ON pr.id = ps.pmi_record_id
JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE pr.billing_mode = 'LONG'
AND pr.current_window_id IS NOT NULL
AND pr.window_expire_time IS NOT NULL
ORDER BY pr.id
LIMIT 5;

-- ========================================
-- 第六部分：最终报告
-- ========================================

SELECT '=== PMI数据修复完成报告 ===' as final_report;

SELECT 
    'Final Statistics' as report_type,
    'PMI Records' as item_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN billing_mode = 'LONG' THEN 1 END) as long_count,
    COUNT(CASE WHEN billing_mode = 'HOURLY' THEN 1 END) as hourly_count,
    COUNT(CASE WHEN magic_id IS NOT NULL AND magic_id != '' THEN 1 END) as has_magic_id_count,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_window_id_count
FROM t_pmi_records

UNION ALL

SELECT 
    'Final Statistics' as report_type,
    'PMI Schedules' as item_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN schedule_type = 'LONG_TERM' THEN 1 END) as long_term_count,
    COUNT(CASE WHEN schedule_type = 'HOURLY' THEN 1 END) as hourly_count,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count,
    0 as placeholder1
FROM t_pmi_schedules

UNION ALL

SELECT 
    'Final Statistics' as report_type,
    'Schedule Windows' as item_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_count,
    COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) as expired_count,
    COUNT(CASE WHEN window_end > NOW() THEN 1 END) as future_windows,
    0 as placeholder1
FROM t_pmi_schedule_windows;

SELECT 'PMI数据修复完成！所有LONG类型PMI记录现在都有正确的窗口信息，magic_id已从mg_id正确设置。' as completion_message;
