-- 修复 t_pmi_records 计费模式和窗口信息
-- 以 t_pmi_schedule_windows 为准，修复 billing_mode、current_window_id、window_expire_time、active_window_ids
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 1. 数据状态检查
-- ========================================

SELECT '=== 修复前数据状态检查 ===' as step;

-- 检查PMI记录状态
SELECT 
    'PMI Records Before Fix' as check_type,
    COUNT(*) as total_pmi_records,
    COUNT(CASE WHEN billing_mode = 'LONG' THEN 1 END) as long_billing_count,
    COUNT(CASE WHEN billing_mode = 'BY_TIME' THEN 1 END) as by_time_billing_count,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_current_window,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_expire_time,
    COUNT(CASE WHEN active_window_ids IS NOT NULL THEN 1 END) as has_active_windows
FROM t_pmi_records;

-- 检查窗口状态
SELECT 
    'Schedule Windows Status' as check_type,
    COUNT(*) as total_windows,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_windows,
    COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_windows,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_windows
FROM t_pmi_schedule_windows;

-- 检查需要修复的PMI记录
SELECT 
    'PMI Records Need Fix' as check_type,
    COUNT(DISTINCT w.pmi_record_id) as pmi_with_active_windows,
    COUNT(DISTINCT CASE WHEN p.billing_mode != 'LONG' THEN w.pmi_record_id END) as need_billing_mode_fix,
    COUNT(DISTINCT CASE WHEN p.current_window_id IS NULL THEN w.pmi_record_id END) as need_window_id_fix,
    COUNT(DISTINCT CASE WHEN p.window_expire_time IS NULL THEN w.pmi_record_id END) as need_expire_time_fix
FROM t_pmi_schedule_windows w
JOIN t_pmi_records p ON w.pmi_record_id = p.id
WHERE w.status = 'ACTIVE';

-- ========================================
-- 2. 修复计费模式和窗口信息
-- ========================================

SELECT '=== 开始修复PMI记录 ===' as step;

-- 2.1 保存原始计费模式（如果还没有保存）
UPDATE t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.id = w.pmi_record_id
SET p.original_billing_mode = p.billing_mode
WHERE w.status = 'ACTIVE'
AND p.original_billing_mode IS NULL;

SELECT 
    'Step 2.1 - Save Original Billing Mode' as step_result,
    ROW_COUNT() as updated_count;

-- 2.2 更新计费模式为LONG（对于有活跃窗口的PMI）
UPDATE t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.id = w.pmi_record_id
SET p.billing_mode = 'LONG'
WHERE w.status = 'ACTIVE'
AND p.billing_mode != 'LONG';

SELECT 
    'Step 2.2 - Update Billing Mode to LONG' as step_result,
    ROW_COUNT() as updated_count;

-- 2.3 更新当前窗口ID和到期时间（选择最新的活跃窗口）
UPDATE t_pmi_records p
JOIN (
    SELECT 
        w.pmi_record_id,
        w.id as latest_window_id,
        CASE 
            WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
            ELSE TIMESTAMP(w.window_date, w.end_time)
        END as expire_time,
        ROW_NUMBER() OVER (
            PARTITION BY w.pmi_record_id 
            ORDER BY 
                CASE WHEN w.end_date IS NOT NULL THEN w.end_date ELSE w.window_date END DESC,
                w.end_time DESC,
                w.id DESC
        ) as rn
    FROM t_pmi_schedule_windows w
    WHERE w.status = 'ACTIVE'
) latest_window ON p.id = latest_window.pmi_record_id AND latest_window.rn = 1
SET 
    p.current_window_id = latest_window.latest_window_id,
    p.window_expire_time = latest_window.expire_time;

SELECT 
    'Step 2.3 - Update Current Window ID and Expire Time' as step_result,
    ROW_COUNT() as updated_count;

-- 2.4 更新活跃窗口ID列表（JSON格式）
UPDATE t_pmi_records p
JOIN (
    SELECT 
        w.pmi_record_id,
        JSON_ARRAYAGG(w.id) as active_window_ids_json
    FROM t_pmi_schedule_windows w
    WHERE w.status = 'ACTIVE'
    GROUP BY w.pmi_record_id
) active_windows ON p.id = active_windows.pmi_record_id
SET p.active_window_ids = active_windows.active_window_ids_json;

SELECT 
    'Step 2.4 - Update Active Window IDs' as step_result,
    ROW_COUNT() as updated_count;

-- ========================================
-- 3. 数据验证
-- ========================================

SELECT '=== 修复后数据验证 ===' as step;

-- 验证修复结果
SELECT 
    'PMI Records After Fix' as check_type,
    COUNT(*) as total_pmi_records,
    COUNT(CASE WHEN billing_mode = 'LONG' THEN 1 END) as long_billing_count,
    COUNT(CASE WHEN billing_mode = 'BY_TIME' THEN 1 END) as by_time_billing_count,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_current_window,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as has_expire_time,
    COUNT(CASE WHEN active_window_ids IS NOT NULL THEN 1 END) as has_active_windows,
    COUNT(CASE WHEN original_billing_mode IS NOT NULL THEN 1 END) as has_original_mode
FROM t_pmi_records;

-- 验证窗口关联的正确性
SELECT 
    'Window Association Validation' as check_type,
    COUNT(*) as total_active_windows,
    COUNT(CASE WHEN p.billing_mode = 'LONG' THEN 1 END) as windows_with_long_billing,
    COUNT(CASE WHEN p.current_window_id IS NOT NULL THEN 1 END) as windows_with_current_id,
    COUNT(CASE WHEN p.window_expire_time IS NOT NULL THEN 1 END) as windows_with_expire_time
FROM t_pmi_schedule_windows w
JOIN t_pmi_records p ON w.pmi_record_id = p.id
WHERE w.status = 'ACTIVE';

-- 显示修复后的样例数据
SELECT 
    'Sample Fixed Data' as check_type,
    p.id as pmi_record_id,
    p.pmi_number,
    p.billing_mode,
    p.original_billing_mode,
    p.current_window_id,
    p.window_expire_time,
    p.active_window_ids,
    w.window_date,
    w.end_date,
    w.status as window_status
FROM t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
WHERE p.billing_mode = 'LONG'
ORDER BY p.id
LIMIT 5;

-- ========================================
-- 4. 一致性检查
-- ========================================

SELECT '=== 一致性检查 ===' as step;

-- 检查是否有活跃窗口但计费模式不是LONG的PMI
SELECT 
    'Inconsistent Billing Mode' as check_type,
    COUNT(*) as inconsistent_count
FROM t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.id = w.pmi_record_id
WHERE w.status = 'ACTIVE'
AND p.billing_mode != 'LONG';

-- 检查是否有LONG模式但没有当前窗口ID的PMI
SELECT 
    'Missing Current Window ID' as check_type,
    COUNT(*) as missing_count
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND p.current_window_id IS NULL;

-- 检查是否有LONG模式但没有到期时间的PMI
SELECT 
    'Missing Expire Time' as check_type,
    COUNT(*) as missing_count
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND p.window_expire_time IS NULL;

-- 检查当前窗口ID是否指向有效的活跃窗口
SELECT 
    'Invalid Current Window ID' as check_type,
    COUNT(*) as invalid_count
FROM t_pmi_records p
LEFT JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
WHERE p.current_window_id IS NOT NULL
AND (w.id IS NULL OR w.status != 'ACTIVE' OR w.pmi_record_id != p.id);

-- ========================================
-- 5. 前端展示数据格式验证
-- ========================================

SELECT '=== 前端展示数据格式验证 ===' as step;

SELECT 
    p.id,
    p.pmi_number,
    p.billing_mode,
    p.current_window_id,
    DATE_FORMAT(p.window_expire_time, '%Y-%m-%d') as expire_date,
    TIME_FORMAT(TIME(p.window_expire_time), '%H:%i:%s') as expire_time,
    -- 前端显示状态
    CASE 
        WHEN p.billing_mode = 'LONG' AND p.window_expire_time IS NOT NULL THEN
            CASE 
                WHEN p.window_expire_time <= NOW() THEN '已过期'
                WHEN p.window_expire_time <= DATE_ADD(NOW(), INTERVAL 7 DAY) THEN '即将到期'
                ELSE '长租中'
            END
        ELSE '按时长计费'
    END as display_status,
    p.active_window_ids
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
ORDER BY p.window_expire_time
LIMIT 10;

-- 提交事务
COMMIT;

-- ========================================
-- 6. 总结报告
-- ========================================

SELECT '=== 修复完成总结 ===' as summary;

SELECT 
    '修复项目' as item,
    '修复内容' as description
UNION ALL
SELECT 
    '计费模式修复' as item,
    '将有活跃窗口的PMI计费模式更新为LONG' as description
UNION ALL
SELECT 
    '原始模式保存' as item,
    '保存PMI的原始计费模式，用于窗口关闭后恢复' as description
UNION ALL
SELECT 
    '当前窗口ID' as item,
    '设置每个LONG模式PMI的当前活跃窗口ID' as description
UNION ALL
SELECT 
    '窗口到期时间' as item,
    '计算并设置窗口的到期时间（end_date + end_time）' as description
UNION ALL
SELECT 
    '活跃窗口列表' as item,
    '以JSON格式存储PMI的所有活跃窗口ID列表' as description;

SELECT 'PMI Records billing mode and window info fix completed successfully!' as final_message;
