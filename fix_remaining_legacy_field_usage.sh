#!/bin/bash

# 批量修复剩余使用旧字段的代码
# 执行日期: 2025-08-22
# 目的: 快速修复所有使用windowDate、endDate、startTime、endTime的代码

echo "=== 开始批量修复剩余的旧字段使用 ==="

# 1. 修复PmiWindowCheckService
echo "1. 修复PmiWindowCheckService..."
sed -i '' 's/window\.getEndDate() != null/window.getEndDateTime() != null/g' src/main/java/com/zoombus/service/PmiWindowCheckService.java
sed -i '' 's/LocalDateTime\.of(window\.getEndDate(), window\.getEndTime())/window.getEndDateTime()/g' src/main/java/com/zoombus/service/PmiWindowCheckService.java
sed -i '' 's/LocalDateTime\.of(window\.getWindowDate(), window\.getEndTime())/window.getEndDateTime()/g' src/main/java/com/zoombus/service/PmiWindowCheckService.java
sed -i '' 's/LocalDateTime\.of(window\.getWindowDate(), window\.getStartTime())/window.getStartDateTime()/g' src/main/java/com/zoombus/service/PmiWindowCheckService.java
sed -i '' 's/window\.getEndDate() != null ? window\.getEndDate() : window\.getWindowDate()/window.getEndDateTime().toLocalDate()/g' src/main/java/com/zoombus/service/PmiWindowCheckService.java
sed -i '' 's/window\.getEndTime()/window.getEndDateTime().toLocalTime()/g' src/main/java/com/zoombus/service/PmiWindowCheckService.java

# 2. 修复PmiScheduleWindowService
echo "2. 修复PmiScheduleWindowService..."
sed -i '' 's/window\.setWindowDate(/\/\/ window.setWindowDate(/g' src/main/java/com/zoombus/service/PmiScheduleWindowService.java
sed -i '' 's/window\.setStartTime(/\/\/ window.setStartTime(/g' src/main/java/com/zoombus/service/PmiScheduleWindowService.java
sed -i '' 's/window\.setEndTime(/\/\/ window.setEndTime(/g' src/main/java/com/zoombus/service/PmiScheduleWindowService.java
sed -i '' 's/window\.setEndDate(/\/\/ window.setEndDate(/g' src/main/java/com/zoombus/service/PmiScheduleWindowService.java
sed -i '' 's/window\.getWindowDate()/window.getStartDateTime().toLocalDate()/g' src/main/java/com/zoombus/service/PmiScheduleWindowService.java
sed -i '' 's/window\.getStartTime()/window.getStartDateTime().toLocalTime()/g' src/main/java/com/zoombus/service/PmiScheduleWindowService.java
sed -i '' 's/window\.getEndTime()/window.getEndDateTime().toLocalTime()/g' src/main/java/com/zoombus/service/PmiScheduleWindowService.java
sed -i '' 's/window\.getEndDate()/window.getEndDateTime().toLocalDate()/g' src/main/java/com/zoombus/service/PmiScheduleWindowService.java

# 3. 修复PmiScheduleService中剩余的setter调用
echo "3. 修复PmiScheduleService..."
sed -i '' 's/window\.setWindowDate(/\/\/ window.setWindowDate(/g' src/main/java/com/zoombus/service/PmiScheduleService.java
sed -i '' 's/window\.setEndDate(/\/\/ window.setEndDate(/g' src/main/java/com/zoombus/service/PmiScheduleService.java
sed -i '' 's/window\.setStartTime(/\/\/ window.setStartTime(/g' src/main/java/com/zoombus/service/PmiScheduleService.java
sed -i '' 's/window\.setEndTime(/\/\/ window.setEndTime(/g' src/main/java/com/zoombus/service/PmiScheduleService.java

echo "=== 批量修复完成 ==="
echo "注意: 某些复杂的逻辑可能需要手动调整"
echo "请运行编译检查是否还有错误"
