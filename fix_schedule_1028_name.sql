-- 修复计划1028的名称显示问题
-- 将日期范围从 "2025-08-21 ~ 2025-08-21" 修复为 "2025-08-21 ~ 2025-08-22"

USE zoombusV;

-- 1. 检查计划1028的当前状态
SELECT '=== 计划1028修复前状态 ===' as step;

SELECT 
    ps.id,
    ps.name,
    ps.start_date,
    ps.end_date,
    ps.start_time,
    ps.duration_minutes,
    ps.is_all_day,
    pr.pmi_number,
    -- 计算结束时间
    CASE 
        WHEN ps.is_all_day = 1 THEN '全天'
        WHEN ps.start_time IS NOT NULL AND ps.duration_minutes IS NOT NULL THEN
            TIME_FORMAT(ADDTIME(ps.start_time, SEC_TO_TIME(ps.duration_minutes * 60)), '%H:%i:%s')
        ELSE 'N/A'
    END as calculated_end_time,
    -- 检查是否为跨日窗口
    CASE 
        WHEN ps.is_all_day = 1 THEN 'FULL_DAY'
        WHEN ps.start_time IS NOT NULL AND ps.duration_minutes IS NOT NULL THEN
            CASE 
                WHEN TIME_TO_SEC(ADDTIME(ps.start_time, SEC_TO_TIME(ps.duration_minutes * 60))) >= 86400 THEN 'CROSS_DAY'
                WHEN ADDTIME(ps.start_time, SEC_TO_TIME(ps.duration_minutes * 60)) < ps.start_time THEN 'CROSS_DAY'
                ELSE 'SAME_DAY'
            END
        ELSE 'UNKNOWN'
    END as window_type
FROM t_pmi_schedules ps
JOIN t_pmi_records pr ON ps.pmi_record_id = pr.id
WHERE ps.id = 1028;

-- 2. 检查计划1028对应的窗口信息
SELECT '=== 计划1028对应的窗口信息 ===' as step;

SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    CASE 
        WHEN psw.end_time < psw.start_time THEN 'CROSS_DAY_WINDOW'
        ELSE 'SAME_DAY_WINDOW'
    END as window_type,
    TIMESTAMPDIFF(MINUTE, 
        TIMESTAMP(psw.window_date, psw.start_time),
        TIMESTAMP(psw.end_date, psw.end_time)
    ) as duration_minutes
FROM t_pmi_schedule_windows psw
WHERE psw.schedule_id = 1028;

-- 3. 生成正确的计划名称
SELECT '=== 生成正确的计划名称 ===' as step;

SELECT 
    ps.id,
    ps.name as current_name,
    -- 基于窗口的实际日期范围生成正确的名称
    CASE 
        WHEN ps.repeat_type = 'DAILY' THEN
            CONCAT(
                MIN(psw.window_date), 
                ' ~ ', 
                MAX(psw.end_date), 
                ' 每天'
            )
        WHEN ps.repeat_type = 'WEEKLY' THEN
            CONCAT(
                MIN(psw.window_date), 
                ' ~ ', 
                MAX(psw.end_date), 
                ' 每周'
            )
        WHEN ps.repeat_type = 'MONTHLY' THEN
            CONCAT(
                MIN(psw.window_date), 
                ' ~ ', 
                MAX(psw.end_date), 
                ' 每月'
            )
        ELSE
            CONCAT(
                MIN(psw.window_date), 
                ' ~ ', 
                MAX(psw.end_date), 
                ' 重复'
            )
    END as correct_name
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id = 1028
GROUP BY ps.id, ps.name, ps.repeat_type;

-- 4. 修复计划1028的名称
UPDATE t_pmi_schedules ps
SET 
    name = (
        SELECT 
            CASE 
                WHEN ps.repeat_type = 'DAILY' THEN
                    CONCAT(
                        MIN(psw.window_date), 
                        ' ~ ', 
                        MAX(psw.end_date), 
                        ' 每天'
                    )
                WHEN ps.repeat_type = 'WEEKLY' THEN
                    CONCAT(
                        MIN(psw.window_date), 
                        ' ~ ', 
                        MAX(psw.end_date), 
                        ' 每周'
                    )
                WHEN ps.repeat_type = 'MONTHLY' THEN
                    CONCAT(
                        MIN(psw.window_date), 
                        ' ~ ', 
                        MAX(psw.end_date), 
                        ' 每月'
                    )
                ELSE
                    CONCAT(
                        MIN(psw.window_date), 
                        ' ~ ', 
                        MAX(psw.end_date), 
                        ' 重复'
                    )
            END
        FROM t_pmi_schedule_windows psw
        WHERE psw.schedule_id = ps.id
    ),
    updated_at = NOW()
WHERE ps.id = 1028;

-- 5. 验证修复结果
SELECT '=== 计划1028修复后状态 ===' as step;

SELECT 
    ps.id,
    ps.name as updated_name,
    ps.start_date,
    ps.end_date,
    ps.start_time,
    ps.duration_minutes,
    ps.is_all_day,
    ps.repeat_type,
    pr.pmi_number,
    ps.updated_at
FROM t_pmi_schedules ps
JOIN t_pmi_records pr ON ps.pmi_record_id = pr.id
WHERE ps.id = 1028;

-- 6. 检查是否还有其他类似问题的计划
SELECT '=== 检查其他可能有类似问题的计划 ===' as step;

SELECT 
    ps.id,
    ps.name,
    ps.start_date,
    ps.end_date,
    MIN(psw.window_date) as actual_start_date,
    MAX(psw.end_date) as actual_end_date,
    CASE 
        WHEN ps.start_date != MIN(psw.window_date) OR ps.end_date != MAX(psw.end_date) THEN 'NEEDS_FIX'
        ELSE 'OK'
    END as status
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id != 1028
GROUP BY ps.id, ps.name, ps.start_date, ps.end_date
HAVING ps.start_date != MIN(psw.window_date) OR ps.end_date != MAX(psw.end_date)
ORDER BY ps.id DESC
LIMIT 10;

-- 7. 批量修复所有有类似问题的计划名称
UPDATE t_pmi_schedules ps
SET 
    name = (
        SELECT 
            CASE 
                WHEN ps.repeat_type = 'DAILY' THEN
                    CONCAT(
                        MIN(psw.window_date), 
                        ' ~ ', 
                        MAX(psw.end_date), 
                        ' 每天'
                    )
                WHEN ps.repeat_type = 'WEEKLY' THEN
                    CONCAT(
                        MIN(psw.window_date), 
                        ' ~ ', 
                        MAX(psw.end_date), 
                        ' 每周'
                    )
                WHEN ps.repeat_type = 'MONTHLY' THEN
                    CONCAT(
                        MIN(psw.window_date), 
                        ' ~ ', 
                        MAX(psw.end_date), 
                        ' 每月'
                    )
                ELSE
                    CONCAT(
                        MIN(psw.window_date), 
                        ' ~ ', 
                        MAX(psw.end_date), 
                        ' 重复'
                    )
            END
        FROM t_pmi_schedule_windows psw
        WHERE psw.schedule_id = ps.id
        GROUP BY psw.schedule_id
    ),
    updated_at = NOW()
WHERE ps.id IN (
    SELECT schedule_id FROM (
        SELECT 
            ps2.id as schedule_id
        FROM t_pmi_schedules ps2
        LEFT JOIN t_pmi_schedule_windows psw2 ON ps2.id = psw2.schedule_id
        GROUP BY ps2.id, ps2.start_date, ps2.end_date
        HAVING ps2.start_date != MIN(psw2.window_date) OR ps2.end_date != MAX(psw2.end_date)
    ) as subquery
);

-- 8. 最终验证报告
SELECT '=== 最终修复验证报告 ===' as step;

SELECT 
    'Total Schedules' as metric,
    COUNT(*) as count
FROM t_pmi_schedules

UNION ALL

SELECT 
    'Schedules with Correct Names' as metric,
    COUNT(*) as count
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
GROUP BY ps.id
HAVING ps.start_date = MIN(psw.window_date) AND ps.end_date = MAX(psw.end_date)

UNION ALL

SELECT 
    'Schedules Fixed Today' as metric,
    COUNT(*) as count
FROM t_pmi_schedules 
WHERE updated_at >= CURDATE();

-- 9. 显示计划1028的最终状态
SELECT '=== 计划1028最终完整状态 ===' as step;

SELECT 
    ps.*,
    pr.pmi_number,
    COUNT(psw.id) as window_count,
    MIN(psw.window_date) as first_window_date,
    MAX(psw.end_date) as last_window_end_date
FROM t_pmi_schedules ps
JOIN t_pmi_records pr ON ps.pmi_record_id = pr.id
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id = 1028
GROUP BY ps.id;
