-- 修复计划1028的名称显示问题（简化版本）
-- 将日期范围从 "2025-08-21 ~ 2025-08-21" 修复为 "2025-08-21 ~ 2025-08-22"

USE zoombusV;

-- 1. 验证计划1028已经修复
SELECT '=== 计划1028修复验证 ===' as step;

SELECT 
    ps.id,
    ps.name,
    ps.start_date,
    ps.end_date,
    ps.start_time,
    ps.duration_minutes,
    pr.pmi_number,
    psw.window_date,
    psw.end_date as window_end_date,
    psw.start_time as window_start_time,
    psw.end_time as window_end_time,
    CASE 
        WHEN psw.end_time < psw.start_time THEN 'CROSS_DAY_WINDOW'
        ELSE 'SAME_DAY_WINDOW'
    END as window_type
FROM t_pmi_schedules ps
JOIN t_pmi_records pr ON ps.pmi_record_id = pr.id
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id = 1028;

-- 2. 修复计划1027（也有同样的问题）
UPDATE t_pmi_schedules 
SET 
    name = '2025-08-21 ~ 2025-08-22 每天',
    updated_at = NOW()
WHERE id = 1027;

-- 3. 验证计划1027的修复
SELECT '=== 计划1027修复验证 ===' as step;

SELECT 
    ps.id,
    ps.name,
    ps.start_date,
    ps.end_date,
    ps.start_time,
    ps.duration_minutes,
    pr.pmi_number,
    psw.window_date,
    psw.end_date as window_end_date,
    psw.start_time as window_start_time,
    psw.end_time as window_end_time,
    CASE 
        WHEN psw.end_time < psw.start_time THEN 'CROSS_DAY_WINDOW'
        ELSE 'SAME_DAY_WINDOW'
    END as window_type
FROM t_pmi_schedules ps
JOIN t_pmi_records pr ON ps.pmi_record_id = pr.id
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.id = 1027;

-- 4. 检查所有跨日窗口对应的计划名称是否正确
SELECT '=== 跨日窗口计划名称检查 ===' as step;

SELECT 
    ps.id as schedule_id,
    ps.name as schedule_name,
    ps.start_date as schedule_start,
    ps.end_date as schedule_end,
    psw.id as window_id,
    psw.window_date,
    psw.end_date as window_end_date,
    psw.start_time,
    psw.end_time,
    CASE 
        WHEN psw.end_time < psw.start_time THEN 'CROSS_DAY'
        ELSE 'SAME_DAY'
    END as window_type,
    CASE 
        WHEN psw.end_time < psw.start_time AND ps.name LIKE CONCAT('%', ps.start_date, ' ~ ', ps.start_date, '%') THEN 'NAME_NEEDS_FIX'
        WHEN psw.end_time < psw.start_time AND ps.name LIKE CONCAT('%', ps.start_date, ' ~ ', psw.end_date, '%') THEN 'NAME_CORRECT'
        ELSE 'CHECK_MANUALLY'
    END as name_status
FROM t_pmi_schedules ps
JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE psw.end_time < psw.start_time
ORDER BY ps.id DESC
LIMIT 20;

-- 5. 显示修复成功的案例
SELECT '=== 修复成功案例 ===' as step;

SELECT 
    'Schedule 1028' as case_name,
    '修复前: 2025-08-21 ~ 2025-08-21 每天' as before_fix,
    ps.name as after_fix,
    '跨日窗口: 23:41:00 - 00:41:00' as window_info,
    'end_date: 2025-08-22' as window_end_date
FROM t_pmi_schedules ps
WHERE ps.id = 1028

UNION ALL

SELECT 
    'Schedule 1027' as case_name,
    '修复前: 2025-08-21 ~ 2025-08-21 每天' as before_fix,
    ps.name as after_fix,
    '跨日窗口: 23:41:00 - 00:41:00' as window_info,
    'end_date: 2025-08-22' as window_end_date
FROM t_pmi_schedules ps
WHERE ps.id = 1027;

-- 6. 统计报告
SELECT '=== 修复统计报告 ===' as step;

SELECT 
    'Total Schedules' as metric,
    COUNT(*) as count
FROM t_pmi_schedules

UNION ALL

SELECT 
    'Cross-Day Windows' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE end_time < start_time

UNION ALL

SELECT 
    'Schedules Updated Today' as metric,
    COUNT(*) as count
FROM t_pmi_schedules 
WHERE DATE(updated_at) = CURDATE();
