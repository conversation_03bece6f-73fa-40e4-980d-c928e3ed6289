-- 修复PMI记录与用户的关联关系
-- 问题：测试环境的用户ID与生产环境不匹配
-- 解决方案：通过邮箱匹配用户，更新PMI记录的user_id

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 1. 问题分析
-- ========================================

SELECT '=== 用户关联问题分析 ===' as step;

-- 检查PMI记录的用户ID范围
SELECT 
    'PMI Records User ID Range' as check_type,
    MIN(user_id) as min_user_id,
    MAX(user_id) as max_user_id,
    COUNT(*) as total_pmi_records
FROM t_pmi_records;

-- 检查生产环境用户ID范围
SELECT 
    'Production Users ID Range' as check_type,
    MIN(id) as min_user_id,
    MAX(id) as max_user_id,
    COUNT(*) as total_users
FROM t_users;

-- 检查无效的用户关联
SELECT 
    'Invalid User Associations' as check_type,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN u.id IS NULL THEN 1 END) as invalid_links,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_links
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id;

-- ========================================
-- 2. 创建临时映射表
-- ========================================

SELECT '=== 创建用户映射表 ===' as step;

-- 创建临时表存储用户映射关系
CREATE TEMPORARY TABLE temp_user_mapping (
    old_user_id BIGINT,
    new_user_id BIGINT,
    email VARCHAR(255),
    username VARCHAR(255),
    INDEX idx_old_id (old_user_id),
    INDEX idx_new_id (new_user_id)
);

-- 通过邮箱匹配用户（假设邮箱是唯一标识）
-- 注意：这里需要根据实际的用户匹配逻辑调整

-- 方案1：如果用户邮箱格式是 <EMAIL>，提取ID进行匹配
INSERT INTO temp_user_mapping (old_user_id, new_user_id, email, username)
SELECT 
    p.user_id as old_user_id,
    u.id as new_user_id,
    u.email,
    u.username
FROM t_pmi_records p
JOIN t_users u ON (
    -- 尝试通过邮箱模式匹配
    u.email LIKE CONCAT('user_', SUBSTRING(u.email, 6, LOCATE('@', u.email) - 6), '@temp.com')
    OR u.username LIKE CONCAT('user_', SUBSTRING(u.username, 6))
)
WHERE p.user_id != u.id
GROUP BY p.user_id, u.id;

-- 检查映射结果
SELECT 
    'User Mapping Results' as check_type,
    COUNT(*) as mapped_users,
    COUNT(DISTINCT old_user_id) as unique_old_ids,
    COUNT(DISTINCT new_user_id) as unique_new_ids
FROM temp_user_mapping;

-- 显示一些映射示例
SELECT 
    'Mapping Examples' as check_type,
    old_user_id,
    new_user_id,
    email,
    username
FROM temp_user_mapping
LIMIT 5;

-- ========================================
-- 3. 备用方案：创建缺失的用户
-- ========================================

SELECT '=== 处理无法映射的用户 ===' as step;

-- 如果映射不成功，我们需要为这些PMI记录分配默认用户或创建新用户
-- 这里我们选择分配给一个默认的管理员用户

-- 查找管理员用户（假设ID为1或email包含admin）
SELECT 
    'Admin User Info' as check_type,
    id,
    username,
    email,
    status
FROM t_users 
WHERE id = 1 OR email LIKE '%admin%' OR username LIKE '%admin%'
LIMIT 1;

-- 设置默认用户ID（使用ID=1作为默认）
SET @default_user_id = 1;

-- 检查默认用户是否存在
SELECT 
    'Default User Check' as check_type,
    CASE 
        WHEN EXISTS(SELECT 1 FROM t_users WHERE id = @default_user_id) 
        THEN 'EXISTS' 
        ELSE 'NOT_EXISTS' 
    END as user_exists;

-- ========================================
-- 4. 更新PMI记录的用户关联
-- ========================================

SELECT '=== 更新PMI记录用户关联 ===' as step;

-- 方案A：使用映射表更新（如果有成功的映射）
UPDATE t_pmi_records p
JOIN temp_user_mapping m ON p.user_id = m.old_user_id
SET p.user_id = m.new_user_id;

SELECT 
    'Mapped Updates' as update_type,
    ROW_COUNT() as updated_records;

-- 方案B：将剩余无法映射的PMI记录分配给默认用户
UPDATE t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
SET p.user_id = @default_user_id
WHERE u.id IS NULL;

SELECT 
    'Default User Updates' as update_type,
    ROW_COUNT() as updated_records;

-- ========================================
-- 5. 验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 检查修复后的用户关联
SELECT 
    'After Fix - User Associations' as check_type,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN u.id IS NULL THEN 1 END) as invalid_links,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_links,
    ROUND(COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id;

-- 检查用户分布
SELECT 
    'User Distribution' as check_type,
    p.user_id,
    u.username,
    u.email,
    COUNT(*) as pmi_count
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
GROUP BY p.user_id, u.username, u.email
ORDER BY pmi_count DESC
LIMIT 10;

-- 检查LONG类型PMI的用户关联
SELECT 
    'LONG PMI User Associations' as check_type,
    COUNT(*) as long_pmi_count,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_user_links,
    COUNT(DISTINCT p.user_id) as unique_users
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
WHERE p.billing_mode = 'LONG';

-- ========================================
-- 6. 清理临时表
-- ========================================

DROP TEMPORARY TABLE temp_user_mapping;

-- 提交事务
COMMIT;

-- ========================================
-- 7. 最终报告
-- ========================================

SELECT '=== 用户关联修复完成 ===' as final_report;

SELECT 
    '修复项目' as item,
    '修复结果' as result
UNION ALL
SELECT 
    '总PMI记录数' as item,
    CAST((SELECT COUNT(*) FROM t_pmi_records) AS CHAR) as result
UNION ALL
SELECT 
    '有效用户关联' as item,
    CAST((SELECT COUNT(*) FROM t_pmi_records p JOIN t_users u ON p.user_id = u.id) AS CHAR) as result
UNION ALL
SELECT 
    '关联成功率' as item,
    CONCAT(ROUND((SELECT COUNT(*) FROM t_pmi_records p JOIN t_users u ON p.user_id = u.id) * 100.0 / (SELECT COUNT(*) FROM t_pmi_records), 2), '%') as result;

SELECT 'User association fix completed successfully!' as final_message;
