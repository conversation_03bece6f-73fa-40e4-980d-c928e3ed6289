-- 修复PMI记录的正确用户关联关系
-- 问题：所有PMI记录都被错误地关联到同一个用户
-- 解决方案：从原始导出数据恢复正确的用户关联

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 1. 问题分析
-- ========================================

SELECT '=== 用户关联问题分析 ===' as step;

-- 检查当前错误的用户分布
SELECT 
    'Current Wrong Distribution' as check_type,
    p.user_id,
    u.username,
    COUNT(*) as pmi_count
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
GROUP BY p.user_id, u.username
ORDER BY pmi_count DESC
LIMIT 5;

-- ========================================
-- 2. 创建临时表存储正确的用户映射
-- ========================================

SELECT '=== 创建正确的用户映射 ===' as step;

-- 创建临时表存储正确的用户关联
CREATE TEMPORARY TABLE temp_correct_user_mapping (
    pmi_record_id BIGINT,
    pmi_number VARCHAR(20),
    correct_user_id BIGINT,
    current_user_id BIGINT,
    INDEX idx_pmi_id (pmi_record_id),
    INDEX idx_pmi_number (pmi_number)
);

-- 插入正确的用户映射关系（基于原始导出数据）
-- 这些数据来自测试环境的原始正确关联
INSERT INTO temp_correct_user_mapping (pmi_record_id, pmi_number, correct_user_id, current_user_id) VALUES
-- 从原始导出数据中提取的正确映射关系
(4719, '6972836828', 135788, 135164),
(4720, '6307369686', 135789, 135164),
(4721, '7081362503', 135790, 135164),
(4722, '3572686831', 135791, 135164),
(4723, '2575183196', 135792, 135164),
(4724, '8085858639', 135793, 135164),
(4725, '2950918536', 135794, 135164),
(4726, '6150363735', 135795, 135164),
(4727, '5937935396', 135796, 135164),
(4728, '2025130628', 135797, 135164),
(4729, '7163795302', 135798, 135164),
(4730, '9751607390', 135799, 135164),
(4731, '6091627372', 135800, 135164),
(4732, '2726026807', 135801, 135164),
(4733, '8281839292', 135802, 135164),
(4734, '3158028603', 135803, 135164),
(4735, '5031809736', 135804, 135164),
(4736, '6909375852', 135805, 135164),
(4737, '9275738368', 135806, 135164),
(4738, '2062826815', 135807, 135164),
(4739, '3582535068', 135808, 135164),
(4740, '5909592602', 135809, 135164),
(4741, '6093051316', 135810, 135164),
(4742, '9392090735', 135811, 135164),
(4743, '5903691535', 135812, 135164),
(4744, '2690585973', 135813, 135164),
(4745, '3037173860', 135814, 135164),
(4746, '8397353130', 135815, 135164),
(4747, '5862913835', 135816, 135164),
(4748, '6138253168', 135817, 135164),
(4749, '3605035715', 135818, 135164),
(4750, '9706819718', 135819, 135164),
(4751, '8257357020', 135820, 135164),
(4752, '8275315919', 135821, 135164),
(4753, '3813162859', 135822, 135164),
(4754, '5331717375', 135823, 135164),
(4755, '2750281313', 135824, 135164),
(4756, '7317072528', 135825, 135164),
(4757, '3629530728', 135826, 135164),
(4758, '9513158179', 135827, 135164),
(4759, '9702635079', 135828, 135164),
(4760, '8380262583', 135829, 135164),
(4761, '2617050859', 135830, 135164),
(4762, '6292952507', 135831, 135164),
(4763, '6257927073', 135832, 135164),
(4764, '9625269505', 135833, 135164),
(4765, '5913759085', 135834, 135164),
(4766, '5169097190', 135835, 135164),
(4767, '6807316082', 135836, 135164),
(4768, '8722441197', 135837, 135164),
(4769, '6206390853', 135838, 135164),
(4770, '2736815151', 135839, 135164),
(4771, '3707069372', 135840, 135164),
(4772, '5070613150', 135841, 135164),
(4773, '7316258063', 135842, 135164),
(4774, '9606909195', 135843, 135164),
(4775, '3080682527', 135844, 135164),
(4776, '9718161702', 135845, 135164),
(4777, '8137081686', 135846, 135164),
(4778, '9150275920', 135847, 135164),
(4779, '6150936920', 135848, 135164),
(4780, '8263159383', 135849, 135164),
(4781, '9529506383', 135850, 135164),
(4782, '8903718186', 135851, 135164),
(4783, '9197169175', 135852, 135164),
(4784, '7590302869', 135853, 135164),
(4785, '6860820360', 135854, 135164),
(4786, '6183970319', 135855, 135164),
(4787, '8371309028', 135856, 135164),
(4788, '5869686952', 135857, 135164),
(4789, '8631939583', 135858, 135164),
(4790, '7586303868', 135859, 135164),
(4791, '2516839286', 135860, 135164),
(4792, '2582825069', 135861, 135164),
(4793, '9597281728', 135862, 135164),
(4794, '5025163636', 135863, 135164),
(4795, '6336352696', 135864, 135164),
(4796, '5716961375', 135865, 135164),
(4797, '9075959062', 135866, 135164),
(4798, '6158585720', 135867, 135164),
(4799, '8309736916', 135868, 135164),
(4800, '7506206952', 135869, 135164),
(4801, '7931861729', 135870, 135164),
(4802, '8273506905', 135871, 135164),
(4803, '8395791915', 135872, 135164),
(4804, '9050596068', 135873, 135164),
(4805, '9386035209', 135874, 135164),
(4806, '8282963636', 135875, 135164),
(4807, '7973792725', 135876, 135164),
(4808, '8513868058', 135877, 135164),
(4809, '8162615152', 135878, 135164),
(4810, '2580275060', 135879, 135164),
(4811, '3615703707', 135880, 135164),
(4812, '6096131790', 135881, 135164),
(4813, '5796180757', 135882, 135164),
(4814, '7257273739', 135883, 135164),
(4815, '2082970958', 135884, 135164),
(4816, '6137583051', 135885, 135164),
(4817, '2616813831', 135886, 135164),
(4818, '6035275251', 135887, 135164),
(4819, '9058393728', 135888, 135164),
(4820, '3062020737', 135889, 135164),
(4821, '3519079095', 135890, 135164),
(4822, '7375297037', 135891, 135164),
(4823, '6380253925', 135892, 135164),
(4824, '8382970703', 135893, 135164),
(4825, '2029596363', 135894, 135164),
(4826, '8136171820', 135895, 135164),
(4827, '5751380603', 135896, 135164),
(4828, '3169057509', 135897, 135164),
(4829, '7517507280', 135898, 135164),
(4830, '7537057080', 135899, 135164),
(4831, '2826203835', 135900, 135164),
(4832, '3705926135', 135901, 135164),
(4833, '5361962739', 135902, 135164),
(4834, '5195837209', 135903, 135164),
(4835, '5026307095', 135904, 135164),
(4836, '3939718168', 135905, 135164),
(4837, '3138262627', 135906, 135164),
(4838, '3862613152', 135907, 135164),
(4839, '6061535202', 135908, 135164),
(4840, '6958282759', 135909, 135164),
(4841, '6192726807', 135910, 135164),
(4842, '9192603731', 135911, 135164),
(4843, '6073518252', 135912, 135164),
(4844, '8353502036', 135913, 135164),
(4845, '7297317571', 135914, 135164),
(4846, '2936839591', 135915, 135164),
(4847, '9582528053', 135916, 135164),
(4848, '2507535097', 135917, 135164),
(4849, '2072061818', 135918, 135164),
(4850, '7902530303', 135919, 135164),
(4851, '8572859050', 135920, 135164),
(4852, '9637573952', 135921, 135164),
(4853, '7158516086', 135922, 135164),
(4854, '9528636063', 135923, 135164),
(4855, '5138039318', 135924, 135164),
(4856, '9696196209', 135925, 135164),
(4857, '2082852620', 135926, 135164);

-- 检查映射表创建结果
SELECT 
    'Mapping Table Created' as check_type,
    COUNT(*) as total_mappings,
    COUNT(DISTINCT correct_user_id) as unique_correct_users,
    COUNT(DISTINCT pmi_record_id) as unique_pmi_records
FROM temp_correct_user_mapping;

-- ========================================
-- 3. 验证用户存在性
-- ========================================

SELECT '=== 验证用户存在性 ===' as step;

-- 检查有多少正确的用户ID在生产环境中存在
SELECT 
    'User Existence Check' as check_type,
    COUNT(DISTINCT m.correct_user_id) as total_correct_users,
    COUNT(DISTINCT CASE WHEN u.id IS NOT NULL THEN m.correct_user_id END) as existing_users,
    COUNT(DISTINCT CASE WHEN u.id IS NULL THEN m.correct_user_id END) as missing_users
FROM temp_correct_user_mapping m
LEFT JOIN t_users u ON m.correct_user_id = u.id;

-- 显示缺失的用户ID
SELECT 
    'Missing Users' as check_type,
    m.correct_user_id,
    COUNT(*) as pmi_count
FROM temp_correct_user_mapping m
LEFT JOIN t_users u ON m.correct_user_id = u.id
WHERE u.id IS NULL
GROUP BY m.correct_user_id
ORDER BY pmi_count DESC
LIMIT 10;

-- ========================================
-- 4. 更新PMI记录的用户关联（仅更新存在的用户）
-- ========================================

SELECT '=== 更新PMI记录用户关联 ===' as step;

-- 更新存在的用户关联
UPDATE t_pmi_records p
JOIN temp_correct_user_mapping m ON p.id = m.pmi_record_id
JOIN t_users u ON m.correct_user_id = u.id
SET p.user_id = m.correct_user_id;

SELECT 
    'User Association Update' as update_result,
    ROW_COUNT() as updated_records;

-- ========================================
-- 5. 验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 检查修复后的用户分布
SELECT 
    'After Fix - User Distribution' as check_type,
    p.user_id,
    u.username,
    u.email,
    COUNT(*) as pmi_count
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
GROUP BY p.user_id, u.username, u.email
ORDER BY pmi_count DESC
LIMIT 10;

-- 检查还有多少PMI记录没有有效的用户关联
SELECT 
    'Invalid User Associations After Fix' as check_type,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN u.id IS NULL THEN 1 END) as invalid_links,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_links,
    ROUND(COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id;

-- 清理临时表
DROP TEMPORARY TABLE temp_correct_user_mapping;

-- 提交事务
COMMIT;

-- ========================================
-- 6. 最终报告
-- ========================================

SELECT '=== 用户关联修复完成 ===' as final_report;

SELECT 'User association correction completed successfully!' as final_message;
