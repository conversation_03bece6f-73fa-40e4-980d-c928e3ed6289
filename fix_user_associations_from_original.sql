-- 修复PMI记录的正确用户关联关系
-- 基于原始导出数据的正确映射

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- 创建临时表存储正确的用户映射
CREATE TEMPORARY TABLE temp_correct_user_mapping (
    pmi_record_id BIGINT,
    pmi_number VARCHAR(20),
    correct_user_id BIGINT,
    INDEX idx_pmi_id (pmi_record_id)
);

-- 插入正确的用户映射关系
INSERT INTO temp_correct_user_mapping (pmi_record_id, pmi_number, correct_user_id) VALUES
(4719, '6972836828', 135788),
(4720, '6307369686', 135789),
(4721, '7081362503', 135790),
(4722, '3572686831', 135791),
(4723, '2575183196', 135792),
(4724, '8085858639', 135793),
(4725, '2950918536', 135794),
(4726, '6150363735', 135795),
(4727, '5937935396', 135796),
(4728, '2025130628', 135797),
(4729, '7163795302', 135798),
(4730, '9751607390', 135799),
(4731, '6091627372', 135800),
(4732, '2726026807', 135801),
(4733, '8281839292', 135802),
(4734, '3158028603', 135803),
(4735, '5031809736', 135804),
(4736, '6909375852', 135805),
(4737, '9275738368', 135806),
(4738, '2062826815', 135807),
(4739, '3582535068', 135808),
(4740, '5909592602', 135809),
(4741, '6093051316', 135810),
(4742, '9392090735', 135811),
(4743, '5903691535', 135812),
(4744, '2690585973', 135813),
(4745, '3037173860', 135814),
(4746, '8397353130', 135815),
(4747, '5862913835', 135816),
(4748, '6138253168', 135817),
(4749, '3605035715', 135818),
(4750, '9706819718', 135819),
(4751, '8257357020', 135820),
(4752, '8275315919', 135821),
(4753, '3813162859', 135822),
(4754, '5331717375', 135823),
(4755, '2750281313', 135824),
(4756, '7317072528', 135825),
(4757, '3629530728', 135826),
(4758, '9513158179', 135827),
(4759, '9702635079', 135828),
(4760, '8380262583', 135829),
(4761, '2617050859', 135830),
(4762, '6292952507', 135831),
(4763, '6257927073', 135832),
(4764, '9625269505', 135833),
(4765, '5913759085', 135834),
(4766, '5169097190', 135835),
(4767, '6807316082', 135836),
(4768, '8722441197', 135837),
(4769, '6206390853', 135838),
(4770, '2736815151', 135839),
(4771, '3707069372', 135840),
(4772, '5070613150', 135841),
(4773, '7316258063', 135842),
(4774, '9606909195', 135843),
(4775, '3080682527', 135844),
(4776, '9718161702', 135845),
(4777, '8137081686', 135846),
(4778, '9150275920', 135847),
(4779, '6150936920', 135848),
(4780, '8263159383', 135849),
(4781, '9529506383', 135850),
(4782, '8903718186', 135851),
(4783, '9197169175', 135852),
(4784, '7590302869', 135853),
(4785, '6860820360', 135854),
(4786, '6183970319', 135855),
(4787, '8371309028', 135856),
(4788, '5869686952', 135857),
(4789, '8631939583', 135858),
(4790, '7586303868', 135859),
(4791, '2516839286', 135860),
(4792, '2582825069', 135861),
(4793, '9597281728', 135862),
(4794, '5025163636', 135863),
(4795, '6336352696', 135864),
(4796, '5716961375', 135865),
(4797, '9075959062', 135866),
(4798, '6158585720', 135867),
(4799, '8309736916', 135868),
(4800, '7506206952', 135869),
(4801, '7931861729', 135870),
(4802, '8273506905', 135871),
(4803, '8395791915', 135872),
(4804, '9050596068', 135873),
(4805, '9386035209', 135874),
(4806, '8282963636', 135875),
(4807, '7973792725', 135876),
(4808, '8513868058', 135877),
(4809, '8162615152', 135878),
(4810, '2580275060', 135879),
(4811, '3615703707', 135880),
(4812, '6096131790', 135881),
(4813, '5796180757', 135882),
(4814, '7257273739', 135883),
(4815, '2082970958', 135884),
(4816, '6137583051', 135885),
(4817, '2616813831', 135886),
(4818, '6035275251', 135887);

INSERT INTO temp_correct_user_mapping (pmi_record_id, pmi_number, correct_user_id) VALUES
(4819, '9058393728', 135888),
(4820, '3062020737', 135889),
(4821, '3519079095', 135890),
(4822, '7375297037', 135891),
(4823, '6380253925', 135892),
(4824, '8382970703', 135893),
(4825, '2029596363', 135894),
(4826, '8136171820', 135895),
(4827, '5751380603', 135896),
(4828, '3169057509', 135897),
(4829, '7517507280', 135898),
(4830, '7537057080', 135899),
(4831, '2826203835', 135900),
(4832, '3705926135', 135901),
(4833, '5361962739', 135902),
(4834, '5195837209', 135903),
(4835, '5026307095', 135904),
(4836, '3939718168', 135905),
(4837, '3138262627', 135906),
(4838, '3862613152', 135907),
(4839, '6061535202', 135908),
(4840, '6958282759', 135909),
(4841, '6192726807', 135910),
(4842, '9192603731', 135911),
(4843, '6073518252', 135912),
(4844, '8353502036', 135913),
(4845, '7297317571', 135914),
(4846, '2936839591', 135915),
(4847, '9582528053', 135916),
(4848, '2507535097', 135917),
(4849, '2072061818', 135918),
(4850, '7902530303', 135919),
(4851, '8572859050', 135920),
(4852, '9637573952', 135921),
(4853, '7158516086', 135922),
(4854, '9528636063', 135923),
(4855, '5138039318', 135924),
(4856, '9696196209', 135925),
(4857, '2082852620', 135926),
(4858, '6196381758', 135927),
(4859, '5379028357', 135928),
(4860, '9362619382', 135929),
(4861, '5026257131', 135930),
(4862, '5029028253', 135931),
(4863, '9092071019', 135932),
(4864, '2037162903', 135933),
(4865, '8191358602', 135934),
(4866, '5602038871', 135935),
(4867, '3910115064', 135936),
(4868, '9268692606', 135937),
(4869, '5080725281', 135938),
(4870, '6257353059', 135939),
(4871, '8185051728', 135940),
(4872, '6286806920', 135941),
(4873, '9158515737', 135942),
(4874, '6917520582', 135943),
(4875, '7929072515', 135944),
(4876, '8197072516', 135945),
(4877, '8091072517', 135946),
(4878, '6162072518', 135947),
(4879, '9269072519', 135948),
(4880, '2068072520', 135949),
(4881, '5137072521', 135950),
(4882, '8180071018', 135951),
(4883, '6936072508', 135952),
(4884, '5819072509', 135953),
(4885, '5917072510', 135954),
(4886, '3738072511', 135955),
(4887, '9791072512', 135956),
(4888, '2937072513', 135957),
(4889, '5190072514', 135958),
(4890, '5927072503', 135959),
(4891, '5960072504', 135960),
(4892, '5869072505', 135961),
(4893, '7175072506', 135962),
(4894, '7186072507', 135963),
(4895, '6352071015', 135964),
(4896, '5317071016', 135965),
(4897, '6079071017', 135966),
(4898, '3708072501', 135967),
(4899, '9580072502', 135968),
(4900, '8571071008', 135969),
(4901, '2531071009', 135970),
(4902, '6857071010', 135971),
(4903, '7918071011', 135972),
(4904, '2752071012', 135973),
(4905, '7309071013', 135974),
(4906, '7968071014', 135975),
(4907, '3028071003', 135976),
(4908, '3925071004', 135977),
(4909, '5253071005', 135978),
(4910, '6535071006', 135979),
(4911, '7268071007', 135980),
(4912, '3913071002', 135981),
(4913, '3573071001', 135982),
(4914, '9595061009', 135983),
(4915, '7520850579', 135984),
(4916, '8073166221', 135985),
(4917, '9386283030', 135986),
(4918, '2731350957', 135987);

INSERT INTO temp_correct_user_mapping (pmi_record_id, pmi_number, correct_user_id) VALUES
(4919, '5308308519', 135988),
(4920, '5820907509', 135989),
(4921, '6826938209', 135990),
(4922, '9683136390', 135991),
(4923, '5152068696', 135992),
(4924, '3680061007', 135993),
(4925, '8362061008', 135994),
(4926, '2605062515', 135995),
(4927, '5759638158', 135996),
(4928, '5296059395', 135997),
(4929, '2615061005', 135998),
(4930, '3839061006', 135999),
(4931, '8391062502', 136000),
(4932, '7081392720', 136001),
(4933, '3068616152', 136002),
(4934, '9082060593', 136003),
(4935, '5979750635', 136004),
(4936, '6193060696', 136005),
(4937, '5137583625', 136006),
(4938, '8352530902', 136007),
(4939, '7926309607', 136008),
(4940, '6828262631', 136009),
(4941, '2631797920', 136010),
(4942, '9250730507', 136011),
(4943, '9206061001', 136012),
(4944, '5718061002', 136013),
(4945, '2906061003', 136014),
(4946, '5818061004', 136015),
(4947, '2082062501', 136016),
(4948, '3137062635', 136017),
(4949, '8623099815', 136018),
(4950, '5817973570', 136020),
(4951, '6350971680', 136021),
(4952, '8260758163', 136022),
(4953, '7950313590', 136023),
(4954, '9626859759', 136024),
(4955, '9639315263', 136025),
(4956, '7038359357', 136026),
(4957, '5371950603', 136027),
(4958, '9692968628', 136028),
(4959, '5391572529', 136029),
(4960, '9207052504', 136030),
(4961, '8382051013', 136031),
(4962, '6135131790', 136032),
(4963, '9172520252', 136033),
(4964, '7383051011', 136034),
(4965, '5153051012', 136035),
(4966, '7902802953', 136036),
(4967, '2639168526', 136037),
(4968, '2826357279', 136038),
(4969, '8079062593', 136039),
(4970, '5972937280', 136040),
(4971, '9258280937', 136041),
(4972, '2790692637', 136042),
(4973, '6930906157', 136043),
(4974, '9615193806', 136044),
(4975, '3702186952', 136045),
(4976, '3905371266', 136046),
(4977, '5719597969', 136047),
(4978, '3519595272', 136048),
(4979, '7926193571', 136049),
(4980, '9275058026', 136050),
(4981, '9737580261', 136051),
(4982, '3151052503', 136052),
(4983, '7136052502', 136053),
(4984, '6069051010', 136054),
(4985, '9350052501', 136055),
(4986, '6951051009', 136056),
(4987, '8615051008', 136057),
(4988, '9283051007', 136058),
(4989, '3835051004', 136059),
(4990, '6272051006', 136060),
(4991, '6285051005', 136061),
(4992, '7196051003', 136062),
(4993, '7582051002', 136063),
(4994, '9362686815', 136064),
(4995, '6079051001', 136065),
(4996, '8271973753', 136066),
(4997, '3506859705', 136067),
(4998, '5251351862', 136068),
(4999, '5816270802', 136069),
(5000, '7928283158', 136070),
(5001, '5715079180', 136071),
(5002, '7970379382', 136072),
(5003, '3935296318', 136073),
(5004, '3829269262', 136074),
(5005, '7250971375', 136075),
(5006, '3160960369', 136076),
(5007, '6039280368', 136077),
(5008, '2920939036', 136078),
(5009, '9275738371', 136079),
(5010, '3139163075', 136080),
(5011, '6075135926', 136081),
(5012, '5906269681', 136082),
(5013, '9186920282', 136083),
(5014, '9751691370', 136084),
(5015, '5381907315', 136085),
(5016, '9583169518', 136086),
(5017, '9536318190', 136087),
(5018, '9636280528', 136088);

INSERT INTO temp_correct_user_mapping (pmi_record_id, pmi_number, correct_user_id) VALUES
(5019, '2505262960', 136089),
(5020, '2597285261', 136090),
(5021, '8695717161', 136091),
(5022, '8516029715', 136092),
(5023, '7038616052', 136093),
(5024, '7352802708', 136094),
(5025, '2535060580', 136095),
(5026, '2638258295', 136096),
(5027, '2960920862', 136097),
(5028, '7530353157', 136098),
(5029, '8130839070', 136099),
(5030, '5303638150', 136100),
(5031, '3172738505', 136101),
(5032, '9516035828', 136102),
(5033, '6192515807', 136103),
(5034, '8190936926', 136104),
(5035, '9616180791', 136105),
(5036, '8593169180', 136106),
(5037, '7575175162', 136107),
(5038, '8280820835', 136108),
(5039, '9705370750', 136109),
(5040, '8359352818', 136110),
(5041, '6192061358', 136111),
(5042, '8639628130', 136112),
(5043, '7270505203', 136113),
(5044, '5859273979', 136114),
(5045, '9585718370', 136115),
(5046, '5302026163', 136116),
(5047, '9193752531', 136117),
(5048, '8273975703', 136118),
(5049, '5285802818', 136119),
(5050, '3867409386', 136120),
(5051, '3863815718', 136121),
(5052, '5728502706', 136122),
(5053, '3586050791', 136123),
(5054, '7596315796', 136124),
(5055, '3079072826', 136125),
(5056, '3436373329', 136126),
(5057, '9727252836', 136127),
(5058, '3620825758', 136128),
(5059, '8363603546', 136129),
(5060, '2024062168', 136130),
(5061, '6828130618', 136131),
(5062, '5028381682', 136132),
(5063, '9526090929', 136133),
(5064, '2838263536', 136134),
(5065, '8801802347', 136135),
(5066, '2958686816', 136136),
(5067, '5826860706', 136137),
(5068, '8351372686', 136138),
(5069, '3859098873', 136139),
(5070, '2816281368', 136140),
(5071, '2958609202', 136141),
(5072, '5352028280', 136142),
(5073, '7371308615', 136143),
(5074, '3835205293', 136144),
(5075, '8068285350', 136145),
(5076, '8318830001', 136146),
(5077, '8528360628', 136147),
(5078, '2513135916', 136148),
(5079, '8579150618', 136149),
(5080, '9079097925', 136150),
(5081, '5811434475', 136151),
(5082, '3810781333', 136152),
(5083, '5916069135', 136153),
(5084, '2518317157', 136154),
(5085, '9691869390', 136155),
(5086, '9185729037', 136154),
(5087, '6372906818', 136156),
(5088, '9729726060', 136156),
(5089, '2735829575', 136157),
(5090, '3152068590', 136158),
(5091, '3683232322', 136159),
(5092, '3868185191', 136160),
(5093, '8281791791', 136161),
(5094, '6379695292', 136162),
(5095, '8085375825', 136163),
(5096, '7975259096', 136164),
(5097, '6202590281', 136165),
(5098, '6917306160', 136166),
(5099, '6300543401', 136167),
(5100, '5250312376', 136168),
(5101, '6309059362', 136169),
(5102, '8131836206', 136170),
(5103, '9052068296', 136171),
(5104, '8639251580', 136172),
(5105, '8538020582', 136173),
(5106, '2093616069', 136174),
(5107, '5951531520', 136175),
(5108, '3751369373', 136176),
(5109, '3053037968', 136177),
(5110, '5917028536', 136178),
(5111, '2757518386', 136179),
(5112, '5378841027', 136180),
(5113, '7537597973', 136181),
(5114, '9917249779', 136182),
(5115, '6731725205', 136183),
(5116, '8676830304', 136184),
(5117, '3852151937', 136185),
(5118, '3378850888', 136186);

INSERT INTO temp_correct_user_mapping (pmi_record_id, pmi_number, correct_user_id) VALUES
(5119, '2715827173', 136187),
(5120, '6828397526', 136188),
(5121, '3536268579', 136189),
(5122, '2906860630', 136190),
(5123, '7350839727', 136191),
(5124, '6809796930', 136192),
(5125, '5138281368', 136193),
(5126, '9797537091', 136194),
(5127, '8697152688', 136195),
(5128, '9375703096', 136196),
(5129, '3732225846', 136197),
(5130, '3680975805', 136198),
(5131, '8370929093', 136199),
(5132, '2725269637', 136200),
(5133, '3130731626', 136201),
(5134, '8315307960', 136202),
(5135, '6308083151', 136203),
(5136, '3681576033', 136204),
(5137, '5170916802', 136205),
(5138, '9986483257', 136206),
(5139, '8334701698', 136207),
(5140, '2918503819', 136208),
(5141, '3810434358', 136209),
(5142, '9273031795', 136210),
(5143, '9913366887', 136211),
(5144, '2696315720', 136212),
(5145, '6153815975', 136213),
(5146, '7258527173', 136214),
(5147, '6258630727', 136215),
(5148, '7273575908', 136216),
(5149, '8162621975', 136217),
(5150, '5950850737', 136218),
(5151, '5826195196', 136219),
(5152, '8131602962', 136220),
(5153, '6673178823', 136221),
(5154, '8363172708', 136222),
(5155, '7317602017', 136223),
(5156, '5792056051', 136224),
(5157, '6358136030', 136225),
(5158, '2582681688', 136226),
(5159, '2950613951', 136227),
(5160, '6080361909', 136228),
(5161, '6919271368', 136229),
(5162, '9703838206', 136230),
(5163, '6375292908', 136231),
(5164, '2530318537', 136232),
(5165, '6092862059', 136233),
(5166, '6137070738', 136234),
(5167, '7357961978', 136235),
(5168, '2703706295', 136236),
(5169, '7090615917', 136237),
(5170, '9622151925', 136238),
(5171, '8361796908', 136239),
(5172, '9273630816', 136240),
(5173, '3936345043', 136241),
(5174, '8593718159', 136242),
(5175, '8622851182', 136243),
(5176, '7302539520', 136244),
(5177, '8390851628', 136245),
(5178, '2617262052', 136246),
(5179, '3062808697', 136247),
(5180, '8910810326', 136248),
(5181, '9572959290', 136249),
(5182, '8291908473', 136250),
(5183, '8636280926', 136251),
(5184, '8060397360', 136252),
(5185, '7086283169', 136253),
(5186, '2903690370', 136254),
(5187, '9951716452', 136255),
(5188, '8159203829', 136256),
(5189, '2086202039', 136257),
(5190, '9263058362', 136258),
(5191, '6030256738', 136259),
(5192, '9717302727', 136260),
(5193, '5973095136', 136261),
(5194, '8370805968', 136262),
(5195, '6581499596', 136263),
(5196, '5025038026', 136264),
(5197, '5713053575', 136265),
(5198, '8516687932', 136266),
(5199, '8028627961', 136267),
(5200, '6930401591', 136268),
(5201, '6153622642', 136269),
(5202, '7153936070', 136270),
(5203, '2507357205', 136271),
(5204, '8307273796', 136272),
(5205, '8615260953', 136273),
(5206, '9153919620', 136274),
(5207, '8068086803', 136275),
(5208, '2863717280', 136276),
(5209, '8748213940', 136277),
(5210, '7313639197', 136278),
(5211, '9271906174', 136279),
(5212, '2596928159', 136280),
(5213, '3277887638', 136281),
(5214, '5728591605', 136282),
(5215, '5724768898', 136283),
(5216, '3926085196', 136284),
(5217, '5605958937', 136285),
(5218, '2868261316', 136286);

INSERT INTO temp_correct_user_mapping (pmi_record_id, pmi_number, correct_user_id) VALUES
(5219, '3592925186', 136287),
(5220, '5134332505', 136288),
(5221, '5121001895', 136289),
(5222, '5957101322', 136290),
(5223, '2805275169', 136291),
(5224, '2727585375', 136292),
(5225, '6168318161', 136293),
(5226, '5373252520', 136294),
(5227, '3888216068', 136295),
(5228, '5683622519', 136296),
(5229, '3739691685', 136297),
(5230, '6915091794', 136298),
(5231, '3966662768', 136299),
(5232, '6592333103', 136300),
(5233, '9818909720', 136301),
(5234, '8930361237', 136302),
(5235, '8840147348', 136303),
(5236, '5535501624', 136304),
(5237, '5708309693', 136305),
(5238, '6971716869', 136306),
(5239, '9790293905', 136307),
(5240, '5587791026', 136308),
(5241, '4621803943', 136309),
(5242, '9616869266', 136310),
(5243, '3857151015', 136311),
(5244, '3570919161', 136312),
(5245, '5309031025', 136313),
(5246, '7317585371', 136314),
(5247, '5786321023', 136315),
(5248, '2051963024', 136316),
(5249, '5250585028', 136317),
(5250, '5543311022', 136318),
(5251, '8529595052', 136319),
(5252, '5321911021', 136320),
(5253, '6257918282', 136321),
(5254, '6971090115', 136322),
(5255, '8602230774', 136323),
(5256, '9666352579', 136324),
(5257, '8362683928', 136325),
(5258, '3080713515', 136326),
(5259, '6436373331', 136327),
(5260, '6181728062', 136328),
(5261, '5807081708', 136329),
(5262, '8387008194', 136330),
(5263, '6436373330', 136331),
(5264, '8170086629', 136332),
(5265, '8986986516', 136333),
(5266, '5322201020', 136334),
(5267, '8036201363', 136335),
(5268, '5361231019', 136336),
(5269, '9390359196', 136337),
(5270, '9086911808', 136338),
(5271, '6131813136', 136339),
(5272, '8916485409', 136341),
(5273, '6038059080', 136342),
(5274, '9916481559', 136343),
(5275, '6209083093', 136344),
(5276, '5661202501', 136345),
(5277, '8858681010', 136346),
(5278, '5917487879', 136347),
(5279, '6683677009', 136348),
(5280, '9800319136', 136349),
(5281, '8742306886', 136350),
(5282, '6207086273', 136351),
(5283, '6271429319', 136352),
(5284, '9817830083', 136353),
(5285, '6201788596', 136354),
(5286, '3091825737', 136355),
(5287, '9834061193', 136356),
(5288, '7011310071', 136357),
(5289, '8921567678', 136358),
(5290, '6518318181', 136359),
(5291, '8811812648', 136360),
(5292, '5910273789', 136361),
(5293, '8059521510', 136362),
(5294, '5130202506', 136363),
(5295, '5558612507', 136364),
(5296, '5309562508', 136365),
(5297, '5301762509', 136366),
(5298, '6651894553', 136367),
(5299, '6623415931', 136368),
(5300, '7705320828', 136369),
(5301, '6820250001', 136370),
(5302, '9357983573', 136371),
(5303, '9197263626', 136372),
(5304, '5255955925', 136373),
(5305, '5362201018', 136374),
(5306, '5343201016', 136375),
(5307, '5340201017', 136376),
(5308, '5342211015', 136377),
(5309, '5531231012', 136378),
(5310, '8191375858', 136379),
(5311, '5717074695', 136380),
(5312, '8981056456', 136381),
(5313, '6349931776', 136382),
(5314, '5535211011', 136383),
(5315, '6358573929', 136384),
(5316, '6620210501', 136385),
(5317, '5356202504', 136386),
(5318, '8818066426', 136387);

INSERT INTO temp_correct_user_mapping (pmi_record_id, pmi_number, correct_user_id) VALUES
(5319, '6871237136', 136388),
(5320, '8601122333', 136389),
(5321, '5351202503', 136390),
(5322, '7251901010', 136391),
(5323, '5563562502', 136392),
(5324, '5523311007', 136393),
(5325, '5562511008', 136394),
(5326, '5535201009', 136395),
(5327, '5280121005', 136396),
(5328, '5281521006', 136397),
(5329, '5627021002', 136398),
(5330, '5427521003', 136399),
(5331, '6214380037', 136400),
(5332, '5131021001', 136401),
(5333, '9911161806', 136402),
(5334, '3751527570', 136403),
(5335, '8257342930', 136404),
(5336, '8677368326', 136405),
(5337, '6817313310', 136406),
(5338, '8073166209', 136407),
(5339, '9911269089', 136408),
(5340, '8690693518', 136409),
(5341, '8519274868', 136410),
(5342, '6716931608', 136411),
(5343, '3725182727', 136411),
(5344, '7958296909', 136411);

-- 更新PMI记录的用户关联（仅更新存在的用户）
UPDATE t_pmi_records p
JOIN temp_correct_user_mapping m ON p.id = m.pmi_record_id
JOIN t_users u ON m.correct_user_id = u.id
SET p.user_id = m.correct_user_id;

-- 检查修复结果
SELECT
    'User Association Fix Result' as check_type,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_links,
    ROUND(COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id;

-- 清理临时表
DROP TEMPORARY TABLE temp_correct_user_mapping;

-- 提交事务
COMMIT;

SELECT 'User association correction completed successfully!' as final_message;
