-- 简化版PMI用户关联修复脚本
-- 将PMI记录分散分配给现有的活跃用户

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 1. 问题分析
-- ========================================

SELECT '=== 当前问题分析 ===' as step;

SELECT 
    'Current Problem' as check_type,
    p.user_id,
    u.username,
    COUNT(*) as pmi_count
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
GROUP BY p.user_id, u.username;

-- ========================================
-- 2. 创建用户分配策略
-- ========================================

SELECT '=== 创建用户分配策略 ===' as step;

-- 创建临时表存储PMI到用户的分配
CREATE TEMPORARY TABLE temp_pmi_user_assignment (
    pmi_record_id BIGINT,
    assigned_user_id BIGINT,
    INDEX idx_pmi_id (pmi_record_id)
);

-- 获取可用的用户列表（排除当前错误关联的用户）
SELECT 
    'Available Users for Assignment' as check_type,
    COUNT(*) as available_users
FROM t_users 
WHERE status = 'ACTIVE' 
AND id != 135164  -- 排除当前错误关联的用户
AND email LIKE '%@temp.com';

-- 将PMI记录分散分配给这些用户
INSERT INTO temp_pmi_user_assignment (pmi_record_id, assigned_user_id)
SELECT 
    p.id as pmi_record_id,
    -- 使用轮询方式分配用户
    (
        SELECT u.id 
        FROM t_users u 
        WHERE u.status = 'ACTIVE' 
        AND u.id != 135164  -- 排除当前错误关联的用户
        AND u.email LIKE '%@temp.com'
        ORDER BY u.id 
        LIMIT 1 OFFSET (
            (p.id - 4719) % (
                SELECT COUNT(*) 
                FROM t_users 
                WHERE status = 'ACTIVE' 
                AND id != 135164 
                AND email LIKE '%@temp.com'
            )
        )
    ) as assigned_user_id
FROM t_pmi_records p
ORDER BY p.id;

-- 检查分配结果
SELECT 
    'User Assignment Preview' as check_type,
    a.assigned_user_id,
    u.username,
    COUNT(*) as pmi_count
FROM temp_pmi_user_assignment a
LEFT JOIN t_users u ON a.assigned_user_id = u.id
GROUP BY a.assigned_user_id, u.username
ORDER BY pmi_count DESC
LIMIT 10;

-- ========================================
-- 3. 执行用户关联更新
-- ========================================

SELECT '=== 更新PMI用户关联 ===' as step;

-- 更新PMI记录的用户关联
UPDATE t_pmi_records p
JOIN temp_pmi_user_assignment a ON p.id = a.pmi_record_id
SET p.user_id = a.assigned_user_id
WHERE a.assigned_user_id IS NOT NULL;

SELECT 
    'PMI User Assignment Update' as update_result,
    ROW_COUNT() as updated_records;

-- 对于没有分配到用户的PMI，使用第一个可用用户
UPDATE t_pmi_records p
LEFT JOIN temp_pmi_user_assignment a ON p.id = a.pmi_record_id
SET p.user_id = (
    SELECT id FROM t_users 
    WHERE status = 'ACTIVE' 
    AND id != 135164 
    AND email LIKE '%@temp.com'
    ORDER BY id 
    LIMIT 1
)
WHERE a.assigned_user_id IS NULL;

SELECT 
    'Fallback User Assignment Update' as update_result,
    ROW_COUNT() as updated_records;

-- ========================================
-- 4. 验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 检查修复后的用户分布
SELECT 
    'After Fix - User Distribution' as check_type,
    p.user_id,
    u.username,
    SUBSTRING(u.email, 1, 30) as email_preview,
    COUNT(*) as pmi_count
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
GROUP BY p.user_id, u.username, u.email
ORDER BY pmi_count DESC
LIMIT 15;

-- 验证用户关联的完整性
SELECT 
    'Final User Association Status' as check_type,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_links,
    COUNT(DISTINCT p.user_id) as unique_users,
    ROUND(COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id;

-- 检查是否还有关联到Ami用户的PMI
SELECT 
    'Ami User Check After Fix' as check_type,
    COUNT(*) as ami_pmi_count
FROM t_pmi_records p
JOIN t_users u ON p.user_id = u.id
WHERE u.username LIKE '%Ami%' OR u.username LIKE '%暨南大学%';

-- 检查LONG类型PMI的用户分布
SELECT 
    'LONG PMI User Distribution' as check_type,
    u.username,
    COUNT(*) as long_pmi_count
FROM t_pmi_records p
JOIN t_users u ON p.user_id = u.id
WHERE p.billing_mode = 'LONG'
GROUP BY u.username
ORDER BY long_pmi_count DESC;

-- 显示一些样例数据验证
SELECT 
    'Sample PMI User Associations' as check_type,
    p.id,
    p.pmi_number,
    p.billing_mode,
    u.username,
    SUBSTRING(u.email, 1, 25) as email_preview
FROM t_pmi_records p
JOIN t_users u ON p.user_id = u.id
ORDER BY p.id
LIMIT 10;

-- 清理临时表
DROP TEMPORARY TABLE temp_pmi_user_assignment;

-- 提交事务
COMMIT;

-- ========================================
-- 5. 最终报告
-- ========================================

SELECT '=== 用户关联修复完成 ===' as final_report;

SELECT 'User association distribution fix completed successfully!' as final_message;
