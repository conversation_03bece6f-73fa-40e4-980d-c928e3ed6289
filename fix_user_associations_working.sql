-- 工作版PMI用户关联修复脚本
-- 将PMI记录分散分配给现有的活跃用户

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 1. 问题分析
-- ========================================

SELECT '=== 当前问题分析 ===' as step;

SELECT 
    'Current Problem' as check_type,
    p.user_id,
    u.username,
    COUNT(*) as pmi_count
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
GROUP BY p.user_id, u.username;

-- ========================================
-- 2. 获取可用用户列表
-- ========================================

SELECT '=== 可用用户分析 ===' as step;

-- 获取可用的用户列表（排除当前错误关联的用户）
SELECT 
    'Available Users for Assignment' as check_type,
    COUNT(*) as available_users
FROM t_users 
WHERE status = 'ACTIVE' 
AND id != 135164  -- 排除当前错误关联的用户
AND email LIKE '%@temp.com';

-- 显示一些可用用户
SELECT 
    'Sample Available Users' as check_type,
    id, username, email
FROM t_users 
WHERE status = 'ACTIVE' 
AND id != 135164 
AND email LIKE '%@temp.com'
ORDER BY id
LIMIT 10;

-- ========================================
-- 3. 创建用户分配表
-- ========================================

SELECT '=== 创建用户分配 ===' as step;

-- 创建临时表存储用户列表
CREATE TEMPORARY TABLE temp_available_users (
    user_id BIGINT,
    username VARCHAR(255),
    row_num INT AUTO_INCREMENT PRIMARY KEY
);

-- 插入可用用户
INSERT INTO temp_available_users (user_id, username)
SELECT id, username
FROM t_users 
WHERE status = 'ACTIVE' 
AND id != 135164 
AND email LIKE '%@temp.com'
ORDER BY id;

-- 获取用户总数
SET @total_users = (SELECT COUNT(*) FROM temp_available_users);

SELECT 
    'User Assignment Setup' as check_type,
    @total_users as total_available_users;

-- ========================================
-- 4. 分配PMI给用户（使用简单的轮询）
-- ========================================

SELECT '=== 开始分配PMI给用户 ===' as step;

-- 创建PMI分配表
CREATE TEMPORARY TABLE temp_pmi_assignments (
    pmi_id BIGINT,
    assigned_user_id BIGINT,
    pmi_row_num INT
);

-- 为每个PMI分配一个用户（基于行号的轮询）
INSERT INTO temp_pmi_assignments (pmi_id, assigned_user_id, pmi_row_num)
SELECT 
    p.id as pmi_id,
    au.user_id as assigned_user_id,
    (@row_number := @row_number + 1) as pmi_row_num
FROM t_pmi_records p
CROSS JOIN (SELECT @row_number := 0) r
JOIN temp_available_users au ON au.row_num = ((@row_number % @total_users) + 1)
ORDER BY p.id;

-- 检查分配结果
SELECT 
    'Assignment Preview' as check_type,
    ta.assigned_user_id,
    au.username,
    COUNT(*) as pmi_count
FROM temp_pmi_assignments ta
JOIN temp_available_users au ON ta.assigned_user_id = au.user_id
GROUP BY ta.assigned_user_id, au.username
ORDER BY pmi_count DESC
LIMIT 10;

-- ========================================
-- 5. 执行更新
-- ========================================

SELECT '=== 执行PMI用户关联更新 ===' as step;

-- 更新PMI记录的用户关联
UPDATE t_pmi_records p
JOIN temp_pmi_assignments ta ON p.id = ta.pmi_id
SET p.user_id = ta.assigned_user_id;

SELECT 
    'PMI User Assignment Update' as update_result,
    ROW_COUNT() as updated_records;

-- ========================================
-- 6. 验证修复结果
-- ========================================

SELECT '=== 修复结果验证 ===' as step;

-- 检查修复后的用户分布
SELECT 
    'After Fix - User Distribution' as check_type,
    p.user_id,
    u.username,
    SUBSTRING(u.email, 1, 30) as email_preview,
    COUNT(*) as pmi_count
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
GROUP BY p.user_id, u.username, u.email
ORDER BY pmi_count DESC
LIMIT 15;

-- 验证用户关联的完整性
SELECT 
    'Final User Association Status' as check_type,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_links,
    COUNT(DISTINCT p.user_id) as unique_users,
    ROUND(COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id;

-- 检查是否还有关联到Ami用户的PMI
SELECT 
    'Ami User Check After Fix' as check_type,
    COUNT(*) as ami_pmi_count
FROM t_pmi_records p
JOIN t_users u ON p.user_id = u.id
WHERE u.username LIKE '%Ami%' OR u.username LIKE '%暨南大学%';

-- 检查LONG类型PMI的用户分布
SELECT 
    'LONG PMI User Distribution' as check_type,
    u.username,
    COUNT(*) as long_pmi_count
FROM t_pmi_records p
JOIN t_users u ON p.user_id = u.id
WHERE p.billing_mode = 'LONG'
GROUP BY u.username
ORDER BY long_pmi_count DESC
LIMIT 10;

-- 显示一些样例数据验证
SELECT 
    'Sample PMI User Associations' as check_type,
    p.id,
    p.pmi_number,
    p.billing_mode,
    u.username,
    SUBSTRING(u.email, 1, 25) as email_preview
FROM t_pmi_records p
JOIN t_users u ON p.user_id = u.id
ORDER BY p.id
LIMIT 10;

-- 清理临时表
DROP TEMPORARY TABLE temp_available_users;
DROP TEMPORARY TABLE temp_pmi_assignments;

-- 提交事务
COMMIT;

-- ========================================
-- 7. 最终报告
-- ========================================

SELECT '=== 用户关联修复完成 ===' as final_report;

SELECT 'User association distribution fix completed successfully!' as final_message;
