-- 修复窗口1036的状态问题
-- 该窗口到期时间为2025-09-17，但被提前在2025-08-21关闭了

USE zoombusV;

-- 1. 检查当前状态
SELECT '=== 修复前窗口1036状态 ===' as step;
SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.status,
    psw.updated_at,
    pr.pmi_number,
    pr.billing_mode,
    CASE 
        WHEN psw.end_date > CURDATE() THEN 'SHOULD_BE_ACTIVE'
        ELSE 'SHOULD_BE_COMPLETED'
    END as expected_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 1036;

-- 2. 修复窗口状态
UPDATE t_pmi_schedule_windows 
SET 
    status = 'ACTIVE',
    updated_at = NOW()
WHERE id = 1036 
AND end_date > CURDATE();

-- 3. 检查修复后状态
SELECT '=== 修复后窗口1036状态 ===' as step;
SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.status,
    psw.updated_at,
    pr.pmi_number,
    pr.billing_mode
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 1036;

-- 4. 检查是否还有其他类似问题的窗口
SELECT '=== 检查其他可能有问题的窗口 ===' as step;
SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.status,
    psw.updated_at,
    pr.pmi_number,
    CASE 
        WHEN psw.end_date > CURDATE() AND psw.status = 'COMPLETED' THEN 'NEEDS_FIX'
        ELSE 'OK'
    END as issue_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.end_date > CURDATE() 
AND psw.status = 'COMPLETED'
ORDER BY psw.end_date;

-- 5. 批量修复所有提前关闭的窗口
UPDATE t_pmi_schedule_windows 
SET 
    status = 'ACTIVE',
    updated_at = NOW()
WHERE end_date > CURDATE() 
AND status = 'COMPLETED';

-- 6. 显示修复结果统计
SELECT '=== 修复结果统计 ===' as step;
SELECT 
    'Fixed Windows Count' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE end_date > CURDATE() 
AND status = 'ACTIVE'
AND updated_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);

-- 7. 显示当前所有活跃窗口
SELECT '=== 当前所有活跃窗口 ===' as step;
SELECT 
    psw.id,
    pr.pmi_number,
    psw.window_date,
    psw.end_date,
    psw.status,
    DATEDIFF(psw.end_date, CURDATE()) as days_remaining
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.status = 'ACTIVE'
ORDER BY psw.end_date;
