-- 修复窗口2082任务创建失败的问题

-- 1. 检查窗口2082的当前状态
SELECT '=== 检查窗口2082当前状态 ===' as step;
SELECT 
    psw.id,
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    psw.open_task_id,
    psw.close_task_id,
    psw.error_message,
    psw.created_at,
    psw.updated_at
FROM t_pmi_schedule_windows psw
WHERE psw.id = 2082;

-- 2. 检查是否有相关的任务记录
SELECT '=== 检查相关任务记录 ===' as step;
SELECT 
    pswt.id,
    pswt.task_key,
    pswt.pmi_window_id,
    pswt.task_type,
    pswt.scheduled_time,
    pswt.status,
    pswt.error_message,
    pswt.created_at
FROM t_pmi_schedule_window_tasks pswt
WHERE pswt.pmi_window_id = 2082
ORDER BY pswt.created_at;

-- 3. 手动创建开启任务
SELECT '=== 创建开启任务 ===' as step;
INSERT INTO t_pmi_schedule_window_tasks (
    pmi_window_id,
    task_type,
    scheduled_time,
    status,
    task_key,
    retry_count,
    created_at,
    updated_at
) VALUES (
    2082,
    'PMI_WINDOW_OPEN',
    '2025-08-23 20:09:00',
    'SCHEDULED',
    CONCAT('PMI_OPEN_2082_', UNIX_TIMESTAMP(NOW()) * 1000 + FLOOR(RAND() * 1000)),
    0,
    NOW(),
    NOW()
);

-- 4. 手动创建关闭任务
SELECT '=== 创建关闭任务 ===' as step;
INSERT INTO t_pmi_schedule_window_tasks (
    pmi_window_id,
    task_type,
    scheduled_time,
    status,
    task_key,
    retry_count,
    created_at,
    updated_at
) VALUES (
    2082,
    'PMI_WINDOW_CLOSE',
    '2025-08-23 21:09:00',
    'SCHEDULED',
    CONCAT('PMI_CLOSE_2082_', UNIX_TIMESTAMP(NOW()) * 1000 + FLOOR(RAND() * 1000)),
    0,
    NOW(),
    NOW()
);

-- 5. 获取新创建的任务ID并更新窗口记录
SELECT '=== 更新窗口任务关联 ===' as step;

-- 获取开启任务ID
SET @open_task_id = (
    SELECT id FROM t_pmi_schedule_window_tasks 
    WHERE pmi_window_id = 2082 AND task_type = 'PMI_WINDOW_OPEN' 
    ORDER BY created_at DESC LIMIT 1
);

-- 获取关闭任务ID
SET @close_task_id = (
    SELECT id FROM t_pmi_schedule_window_tasks 
    WHERE pmi_window_id = 2082 AND task_type = 'PMI_WINDOW_CLOSE' 
    ORDER BY created_at DESC LIMIT 1
);

-- 更新窗口记录
UPDATE t_pmi_schedule_windows 
SET 
    open_task_id = @open_task_id,
    close_task_id = @close_task_id,
    updated_at = NOW()
WHERE id = 2082;

-- 6. 验证修复结果
SELECT '=== 验证修复结果 ===' as step;
SELECT 
    psw.id,
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    psw.open_task_id,
    psw.close_task_id,
    psw.updated_at,
    open_task.task_key as open_task_key,
    open_task.status as open_task_status,
    close_task.task_key as close_task_key,
    close_task.status as close_task_status
FROM t_pmi_schedule_windows psw
LEFT JOIN t_pmi_schedule_window_tasks open_task ON psw.open_task_id = open_task.id
LEFT JOIN t_pmi_schedule_window_tasks close_task ON psw.close_task_id = close_task.id
WHERE psw.id = 2082;

-- 7. 检查任务详情
SELECT '=== 检查任务详情 ===' as step;
SELECT 
    pswt.id,
    pswt.task_key,
    pswt.pmi_window_id,
    pswt.task_type,
    pswt.scheduled_time,
    pswt.status,
    pswt.retry_count,
    pswt.error_message,
    pswt.created_at,
    pswt.updated_at,
    CASE 
        WHEN pswt.scheduled_time < NOW() THEN 'OVERDUE'
        WHEN pswt.scheduled_time <= DATE_ADD(NOW(), INTERVAL 5 MINUTE) THEN 'IMMINENT'
        ELSE 'FUTURE'
    END as timing_status
FROM t_pmi_schedule_window_tasks pswt
WHERE pswt.pmi_window_id = 2082
ORDER BY pswt.task_type, pswt.created_at;

-- 8. 检查PMI记录状态
SELECT '=== 检查PMI记录状态 ===' as step;
SELECT 
    pr.id,
    pr.pmi_number,
    pr.status,
    pr.billing_mode,
    pr.current_window_id,
    pr.window_expire_time,
    pr.last_used_at
FROM t_pmi_records pr
WHERE pr.id = 554;

-- 9. 统计报告
SELECT '=== 修复统计报告 ===' as step;
SELECT 
    'Window 2082 Tasks Created' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_window_tasks 
WHERE pmi_window_id = 2082

UNION ALL

SELECT 
    'Open Tasks for Window 2082' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_window_tasks 
WHERE pmi_window_id = 2082 AND task_type = 'PMI_WINDOW_OPEN'

UNION ALL

SELECT 
    'Close Tasks for Window 2082' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_window_tasks 
WHERE pmi_window_id = 2082 AND task_type = 'PMI_WINDOW_CLOSE'

UNION ALL

SELECT 
    'Windows Without Tasks' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status IN ('PENDING', 'ACTIVE') 
AND (open_task_id IS NULL OR close_task_id IS NULL);
