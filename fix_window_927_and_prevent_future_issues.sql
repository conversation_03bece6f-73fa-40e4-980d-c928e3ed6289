-- 修复窗口927和防止未来误关闭问题的完整解决方案
-- 执行日期: 2025-08-21

USE zoombusV;

-- 1. 检查窗口927的当前状态
SELECT '=== 窗口927修复前状态 ===' as step;
SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.status,
    psw.updated_at,
    psw.actual_end_time,
    pr.pmi_number,
    pr.billing_mode,
    pr.window_expire_time,
    DATEDIFF(psw.end_date, CURDATE()) as days_remaining
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 927;

-- 2. 修复窗口927的状态
UPDATE t_pmi_schedule_windows 
SET 
    status = 'ACTIVE',
    updated_at = NOW(),
    actual_end_time = NULL  -- 清除错误的结束时间
WHERE id = 927 
AND end_date > CURDATE();

-- 3. 检查修复后的状态
SELECT '=== 窗口927修复后状态 ===' as step;
SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.status,
    psw.updated_at,
    psw.actual_end_time,
    pr.pmi_number,
    pr.billing_mode,
    pr.window_expire_time,
    DATEDIFF(psw.end_date, CURDATE()) as days_remaining
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 927;

-- 4. 检查是否还有其他在22:00被错误关闭的窗口
SELECT '=== 检查22:00被错误关闭的窗口 ===' as step;
SELECT 
    psw.id,
    pr.pmi_number,
    psw.window_date,
    psw.end_date,
    psw.status,
    psw.updated_at,
    psw.actual_end_time,
    DATEDIFF(psw.end_date, CURDATE()) as days_remaining,
    CASE 
        WHEN psw.end_date > CURDATE() AND psw.status = 'COMPLETED' THEN 'NEEDS_FIX'
        ELSE 'OK'
    END as issue_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.updated_at >= '2025-08-21 22:00:00'
AND psw.updated_at <= '2025-08-21 22:59:59'
AND psw.status = 'COMPLETED'
AND psw.end_date > CURDATE()
ORDER BY psw.updated_at;

-- 5. 批量修复所有在22:00被错误关闭的窗口
UPDATE t_pmi_schedule_windows 
SET 
    status = 'ACTIVE',
    updated_at = NOW(),
    actual_end_time = NULL
WHERE updated_at >= '2025-08-21 22:00:00'
AND updated_at <= '2025-08-21 22:59:59'
AND status = 'COMPLETED'
AND end_date > CURDATE();

-- 6. 显示修复结果统计
SELECT '=== 22:00错误关闭窗口修复统计 ===' as step;
SELECT 
    'Fixed Windows in 22:00 hour' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE end_date > CURDATE() 
AND status = 'ACTIVE'
AND updated_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE);

-- 7. 检查所有LONG模式PMI的窗口状态一致性
SELECT '=== LONG模式PMI窗口状态一致性检查 ===' as step;
SELECT 
    pr.id as pmi_id,
    pr.pmi_number,
    pr.billing_mode,
    pr.current_window_id,
    pr.window_expire_time,
    COUNT(psw.id) as total_windows,
    COUNT(CASE WHEN psw.status = 'ACTIVE' THEN 1 END) as active_windows,
    COUNT(CASE WHEN psw.status = 'COMPLETED' AND psw.end_date > CURDATE() THEN 1 END) as incorrectly_closed_windows
FROM t_pmi_records pr
LEFT JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
WHERE pr.billing_mode = 'LONG'
GROUP BY pr.id, pr.pmi_number, pr.billing_mode, pr.current_window_id, pr.window_expire_time
HAVING incorrectly_closed_windows > 0
ORDER BY pr.window_expire_time;

-- 8. 修复所有LONG模式PMI中被错误关闭的窗口
UPDATE t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
SET 
    psw.status = 'ACTIVE',
    psw.updated_at = NOW(),
    psw.actual_end_time = NULL
WHERE pr.billing_mode = 'LONG'
AND psw.status = 'COMPLETED'
AND psw.end_date > CURDATE();

-- 9. 最终验证：显示所有当前活跃的窗口
SELECT '=== 最终验证：当前所有活跃窗口 ===' as step;
SELECT 
    psw.id,
    pr.pmi_number,
    pr.billing_mode,
    psw.window_date,
    psw.end_date,
    psw.status,
    DATEDIFF(psw.end_date, CURDATE()) as days_remaining,
    psw.updated_at
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.status = 'ACTIVE'
AND pr.billing_mode = 'LONG'
ORDER BY psw.end_date;

-- 10. 检查PMI记录的窗口信息是否需要更新
SELECT '=== PMI记录窗口信息检查 ===' as step;
SELECT 
    pr.id,
    pr.pmi_number,
    pr.current_window_id,
    pr.window_expire_time,
    psw.id as actual_window_id,
    psw.end_date as actual_end_date,
    CASE 
        WHEN pr.current_window_id != psw.id THEN 'WINDOW_ID_MISMATCH'
        WHEN DATE(pr.window_expire_time) != psw.end_date THEN 'EXPIRE_TIME_MISMATCH'
        ELSE 'OK'
    END as consistency_check
FROM t_pmi_records pr
JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
WHERE pr.billing_mode = 'LONG'
AND psw.status = 'ACTIVE'
AND (pr.current_window_id != psw.id OR DATE(pr.window_expire_time) != psw.end_date);

-- 11. 更新PMI记录的窗口信息以保持一致性
UPDATE t_pmi_records pr
JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
SET 
    pr.current_window_id = psw.id,
    pr.window_expire_time = TIMESTAMP(psw.end_date, psw.end_time),
    pr.active_window_ids = CONCAT('[', psw.id, ']')
WHERE pr.billing_mode = 'LONG'
AND psw.status = 'ACTIVE'
AND (pr.current_window_id != psw.id OR DATE(pr.window_expire_time) != psw.end_date);

-- 12. 最终统计报告
SELECT '=== 最终修复统计报告 ===' as step;
SELECT 
    'Total LONG Mode PMIs' as metric,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG'

UNION ALL

SELECT 
    'Active Windows for LONG PMIs' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE pr.billing_mode = 'LONG'
AND psw.status = 'ACTIVE'

UNION ALL

SELECT 
    'Windows Fixed Today' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE'
AND end_date > CURDATE()
AND updated_at >= CURDATE();
