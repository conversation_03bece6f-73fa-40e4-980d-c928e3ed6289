-- 修复窗口关闭逻辑问题的脚本
-- 2025-08-18

-- 1. 创建一个视图来监控窗口关闭逻辑
CREATE OR REPLACE VIEW v_window_close_analysis AS
SELECT 
    w.id,
    w.schedule_id,
    w.pmi_record_id,
    w.window_date,
    w.end_date,
    w.start_time,
    w.end_time,
    w.status,
    w.actual_start_time,
    w.actual_end_time,
    w.created_at,
    w.updated_at,
    -- 分析当前时间与窗口时间的关系
    CURDATE() as current_date_val,
    CURTIME() as current_time_val,
    -- 检查各个关闭条件
    CASE 
        WHEN w.end_date IS NULL THEN 'NULL_END_DATE'
        WHEN CURDATE() > w.end_date THEN 'PAST_END_DATE'
        WHEN CURDATE() = w.end_date THEN 'SAME_END_DATE'
        WHEN CURDATE() < w.end_date THEN 'FUTURE_END_DATE'
    END as end_date_analysis,
    -- 条件1：endDate IS NULL AND currentDate = windowDate AND currentTime >= endTime
    (w.end_date IS NULL AND CURDATE() = w.window_date AND CURTIME() >= w.end_time) as condition1_match,
    -- 条件2：endDate IS NOT NULL AND currentDate > endDate
    (w.end_date IS NOT NULL AND CURDATE() > w.end_date) as condition2_match,
    -- 条件3：endDate IS NOT NULL AND currentDate = endDate AND currentTime >= endTime
    (w.end_date IS NOT NULL AND CURDATE() = w.end_date AND CURTIME() >= w.end_time) as condition3_match,
    -- 总体是否应该关闭
    (w.status = 'ACTIVE' AND 
     ((w.end_date IS NULL AND CURDATE() = w.window_date AND CURTIME() >= w.end_time) OR 
      (w.end_date IS NOT NULL AND CURDATE() > w.end_date) OR 
      (w.end_date IS NOT NULL AND CURDATE() = w.end_date AND CURTIME() >= w.end_time))) as should_close_now
FROM t_pmi_schedule_windows w;

-- 2. 创建一个函数来验证窗口关闭逻辑
DELIMITER //
CREATE OR REPLACE FUNCTION validate_window_close_logic(
    p_window_id BIGINT,
    p_current_date DATE,
    p_current_time TIME
) RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_window_date DATE;
    DECLARE v_end_date DATE;
    DECLARE v_start_time TIME;
    DECLARE v_end_time TIME;
    DECLARE v_status VARCHAR(50);
    DECLARE v_condition1 BOOLEAN DEFAULT FALSE;
    DECLARE v_condition2 BOOLEAN DEFAULT FALSE;
    DECLARE v_condition3 BOOLEAN DEFAULT FALSE;
    DECLARE v_should_close BOOLEAN DEFAULT FALSE;
    DECLARE v_result JSON;
    
    -- 获取窗口信息
    SELECT window_date, end_date, start_time, end_time, status
    INTO v_window_date, v_end_date, v_start_time, v_end_time, v_status
    FROM t_pmi_schedule_windows
    WHERE id = p_window_id;
    
    -- 检查条件1：endDate IS NULL AND currentDate = windowDate AND currentTime >= endTime
    IF v_end_date IS NULL AND p_current_date = v_window_date AND p_current_time >= v_end_time THEN
        SET v_condition1 = TRUE;
    END IF;
    
    -- 检查条件2：endDate IS NOT NULL AND currentDate > endDate
    IF v_end_date IS NOT NULL AND p_current_date > v_end_date THEN
        SET v_condition2 = TRUE;
    END IF;
    
    -- 检查条件3：endDate IS NOT NULL AND currentDate = endDate AND currentTime >= endTime
    IF v_end_date IS NOT NULL AND p_current_date = v_end_date AND p_current_time >= v_end_time THEN
        SET v_condition3 = TRUE;
    END IF;
    
    -- 总体判断
    IF v_status = 'ACTIVE' AND (v_condition1 OR v_condition2 OR v_condition3) THEN
        SET v_should_close = TRUE;
    END IF;
    
    -- 构建结果JSON
    SET v_result = JSON_OBJECT(
        'window_id', p_window_id,
        'window_date', v_window_date,
        'end_date', v_end_date,
        'start_time', v_start_time,
        'end_time', v_end_time,
        'status', v_status,
        'current_date', p_current_date,
        'current_time', p_current_time,
        'condition1', v_condition1,
        'condition2', v_condition2,
        'condition3', v_condition3,
        'should_close', v_should_close
    );
    
    RETURN v_result;
END //
DELIMITER ;

-- 3. 创建一个存储过程来安全地关闭窗口
DELIMITER //
CREATE OR REPLACE PROCEDURE safe_close_window(
    IN p_window_id BIGINT,
    IN p_current_date DATE,
    IN p_current_time TIME,
    OUT p_result VARCHAR(500)
)
BEGIN
    DECLARE v_validation_result JSON;
    DECLARE v_should_close BOOLEAN DEFAULT FALSE;
    DECLARE v_status VARCHAR(50);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR: 关闭窗口时发生异常';
    END;
    
    START TRANSACTION;
    
    -- 验证窗口是否应该关闭
    SET v_validation_result = validate_window_close_logic(p_window_id, p_current_date, p_current_time);
    SET v_should_close = JSON_EXTRACT(v_validation_result, '$.should_close');
    SET v_status = JSON_UNQUOTE(JSON_EXTRACT(v_validation_result, '$.status'));
    
    IF v_status != 'ACTIVE' THEN
        SET p_result = CONCAT('SKIP: 窗口状态不是ACTIVE，当前状态: ', v_status);
        ROLLBACK;
    ELSEIF v_should_close = FALSE THEN
        SET p_result = CONCAT('SKIP: 窗口不满足关闭条件，验证结果: ', v_validation_result);
        ROLLBACK;
    ELSE
        -- 安全关闭窗口
        UPDATE t_pmi_schedule_windows 
        SET 
            status = 'COMPLETED',
            actual_end_time = NOW(),
            updated_at = NOW()
        WHERE id = p_window_id AND status = 'ACTIVE';
        
        IF ROW_COUNT() > 0 THEN
            SET p_result = CONCAT('SUCCESS: 窗口已安全关闭，验证结果: ', v_validation_result);
            COMMIT;
        ELSE
            SET p_result = 'SKIP: 窗口状态已变更，未执行关闭操作';
            ROLLBACK;
        END IF;
    END IF;
END //
DELIMITER ;

-- 4. 创建监控表来记录窗口关闭操作
CREATE TABLE IF NOT EXISTS t_window_close_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    window_id BIGINT NOT NULL,
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型：CLOSE_ATTEMPT, CLOSE_SUCCESS, CLOSE_SKIP, CLOSE_ERROR',
    current_date DATE NOT NULL,
    current_time TIME NOT NULL,
    validation_result JSON,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_window_id (window_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_at (created_at)
) COMMENT '窗口关闭操作日志表';

-- 5. 创建触发器来记录窗口状态变更
DELIMITER //
CREATE OR REPLACE TRIGGER tr_window_status_change_log
AFTER UPDATE ON t_pmi_schedule_windows
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status AND NEW.status = 'COMPLETED' THEN
        INSERT INTO t_window_close_log (
            window_id,
            operation_type,
            current_date,
            current_time,
            validation_result,
            error_message
        ) VALUES (
            NEW.id,
            'CLOSE_SUCCESS',
            CURDATE(),
            CURTIME(),
            validate_window_close_logic(NEW.id, CURDATE(), CURTIME()),
            NULL
        );
    END IF;
END //
DELIMITER ;

-- 6. 查询当前可能有问题的窗口
SELECT 
    '当前可能有问题的窗口' as analysis_type,
    COUNT(*) as count
FROM v_window_close_analysis 
WHERE status = 'COMPLETED' 
AND end_date_analysis = 'FUTURE_END_DATE'
AND NOT (condition1_match OR condition2_match OR condition3_match);

-- 7. 显示使用说明
SELECT '=== 使用说明 ===' as info
UNION ALL
SELECT '1. 使用 v_window_close_analysis 视图监控窗口状态' as info
UNION ALL
SELECT '2. 使用 validate_window_close_logic(window_id, date, time) 函数验证关闭逻辑' as info
UNION ALL
SELECT '3. 使用 safe_close_window(window_id, date, time, @result) 存储过程安全关闭窗口' as info
UNION ALL
SELECT '4. 查看 t_window_close_log 表了解窗口关闭历史' as info
UNION ALL
SELECT '5. 触发器会自动记录所有窗口状态变更' as info;
