-- 修复PMI窗口到期时间脚本
-- 问题：window_expire_time被错误地设置为当前日期+end_time，应该使用end_date+end_time
-- 执行日期: 2025-08-19

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- 显示修复前的状态
SELECT '=== 修复前的window_expire_time状态 ===' as status;
SELECT 
    p.id,
    p.pmi_number,
    p.window_expire_time,
    w.window_date,
    w.end_date,
    w.end_time,
    CASE 
        WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
        ELSE TIMESTAMP(w.window_date, w.end_time)
    END as correct_expire_time,
    CASE 
        WHEN p.window_expire_time = TIMESTAMP(CURDATE(), w.end_time) THEN 'NEEDS_FIX'
        WHEN p.window_expire_time != CASE 
            WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
            ELSE TIMESTAMP(w.window_date, w.end_time)
        END THEN 'NEEDS_FIX'
        ELSE 'CORRECT'
    END as fix_status
FROM t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
WHERE p.billing_mode = 'LONG'
AND p.current_window_id IS NOT NULL
ORDER BY p.id;

-- 统计需要修复的记录数量
SELECT 
    '需要修复的PMI记录数量' as description,
    COUNT(*) as count
FROM t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
WHERE p.billing_mode = 'LONG'
AND p.current_window_id IS NOT NULL
AND (
    p.window_expire_time = TIMESTAMP(CURDATE(), w.end_time)
    OR p.window_expire_time != CASE 
        WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
        ELSE TIMESTAMP(w.window_date, w.end_time)
    END
);

-- 修复window_expire_time字段
UPDATE t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
SET 
    p.window_expire_time = CASE 
        WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
        ELSE TIMESTAMP(w.window_date, w.end_time)
    END,
    p.updated_at = NOW()
WHERE p.billing_mode = 'LONG'
AND p.current_window_id IS NOT NULL
AND (
    p.window_expire_time = TIMESTAMP(CURDATE(), w.end_time)
    OR p.window_expire_time != CASE 
        WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
        ELSE TIMESTAMP(w.window_date, w.end_time)
    END
);

-- 显示修复结果
SELECT '=== 修复完成统计 ===' as result;
SELECT 
    '修复的PMI记录数量' as description,
    ROW_COUNT() as count;

-- 显示修复后的状态
SELECT '=== 修复后的window_expire_time状态 ===' as status;
SELECT 
    p.id,
    p.pmi_number,
    p.window_expire_time,
    w.window_date,
    w.end_date,
    w.end_time,
    CASE 
        WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
        ELSE TIMESTAMP(w.window_date, w.end_time)
    END as correct_expire_time,
    CASE 
        WHEN p.window_expire_time = CASE 
            WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
            ELSE TIMESTAMP(w.window_date, w.end_time)
        END THEN 'CORRECT'
        ELSE 'STILL_WRONG'
    END as fix_status
FROM t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
WHERE p.billing_mode = 'LONG'
AND p.current_window_id IS NOT NULL
ORDER BY p.id;

-- 验证修复结果
SELECT '=== 修复验证 ===' as validation;
SELECT 
    '修复后仍有问题的记录数量' as description,
    COUNT(*) as count
FROM t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
WHERE p.billing_mode = 'LONG'
AND p.current_window_id IS NOT NULL
AND p.window_expire_time != CASE 
    WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
    ELSE TIMESTAMP(w.window_date, w.end_time)
END;

-- 显示到期时间分布
SELECT '=== 修复后的到期时间分布 ===' as distribution;
SELECT 
    DATE(p.window_expire_time) as expire_date,
    COUNT(*) as count,
    GROUP_CONCAT(p.pmi_number ORDER BY p.pmi_number) as pmi_numbers
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND p.window_expire_time IS NOT NULL
GROUP BY DATE(p.window_expire_time)
ORDER BY expire_date;

-- 显示即将到期的PMI（7天内）
SELECT '=== 即将到期的PMI（7天内）===' as urgent;
SELECT 
    p.id,
    p.pmi_number,
    p.window_expire_time,
    DATEDIFF(p.window_expire_time, NOW()) as days_until_expire,
    CASE 
        WHEN p.window_expire_time <= NOW() THEN 'EXPIRED'
        WHEN DATEDIFF(p.window_expire_time, NOW()) <= 1 THEN 'CRITICAL'
        WHEN DATEDIFF(p.window_expire_time, NOW()) <= 3 THEN 'WARNING'
        WHEN DATEDIFF(p.window_expire_time, NOW()) <= 7 THEN 'NOTICE'
        ELSE 'NORMAL'
    END as urgency_level
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND p.window_expire_time IS NOT NULL
AND p.window_expire_time <= DATE_ADD(NOW(), INTERVAL 7 DAY)
ORDER BY p.window_expire_time;

-- 提交事务
COMMIT;

SELECT 'PMI窗口到期时间修复完成！' as final_message;
