--- a/src/main/java/com/zoombus/service/PmiBillingModeService.java
+++ b/src/main/java/com/zoombus/service/PmiBillingModeService.java
@@ -158,19 +158,45 @@ public class PmiBillingModeService {
     /**
      * 检查并处理过期的时间窗口
+     * 修复：只有当所有关联的窗口都真正过期时，才切换计费模式
      */
     @Transactional
     public void processExpiredWindows() {
         LocalDateTime now = LocalDateTime.now();
         
-        // 查找所有过期的时间窗口
-        var expiredPmis = pmiRecordRepository.findByBillingModeAndWindowExpireTimeBefore(now);
+        // 查找所有LONG模式的PMI记录
+        var longModePmis = pmiRecordRepository.findByBillingMode(PmiRecord.BillingMode.LONG);
         
-        for (PmiRecord pmi : expiredPmis) {
+        for (PmiRecord pmi : longModePmis) {
             try {
-                switchToTimeBilling(pmi.getId());
-                log.info("PMI {} 窗口已到期，自动切换到按时长计费", pmi.getId());
+                // 检查PMI的所有活跃窗口是否真的都过期了
+                boolean hasActiveWindows = checkIfPmiHasActiveWindows(pmi.getId());
+                
+                if (!hasActiveWindows) {
+                    // 只有当PMI没有任何活跃窗口时，才切换到原始计费模式
+                    switchToTimeBilling(pmi.getId());
+                    log.info("PMI {} 所有窗口已过期，自动切换到按时长计费", pmi.getId());
+                } else {
+                    // 如果还有活跃窗口，更新PMI的窗口信息
+                    updatePmiWindowInfo(pmi.getId());
+                    log.debug("PMI {} 仍有活跃窗口，更新窗口信息", pmi.getId());
+                }
             } catch (Exception e) {
                 log.error("处理PMI {} 窗口到期时发生错误", pmi.getId(), e);
             }
         }
-        
-        if (!expiredPmis.isEmpty()) {
-            log.info("处理了 {} 个过期窗口", expiredPmis.size());
-        }
+    }
+    
+    /**
+     * 检查PMI是否还有活跃的窗口
+     * @param pmiRecordId PMI记录ID
+     * @return true如果有活跃窗口，false如果没有
+     */
+    private boolean checkIfPmiHasActiveWindows(Long pmiRecordId) {
+        List<PmiScheduleWindow> activeWindows = windowRepository.findByPmiRecordIdAndStatus(
+                pmiRecordId, PmiScheduleWindow.WindowStatus.ACTIVE);
+        
+        // 进一步检查这些"活跃"窗口是否真的还在有效期内
+        LocalDateTime now = LocalDateTime.now();
+        return activeWindows.stream().anyMatch(window -> {
+            LocalDateTime windowExpireTime = window.getEndDate() != null 
+                ? LocalDateTime.of(window.getEndDate(), window.getEndTime())
+                : LocalDateTime.of(window.getWindowDate(), window.getEndTime());
+            return windowExpireTime.isAfter(now);
+        });
     }
