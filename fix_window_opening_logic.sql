-- 修复窗口开启逻辑问题
-- 确保窗口开启时正确更新PMI记录的billing_mode、current_window_id和window_expire_time

USE zoombusV;

-- ========================================
-- 第一步：检查当前问题状态
-- ========================================
SELECT '=== 当前问题状态检查 ===' as step;

SELECT 
    'Problem Analysis' as analysis_type,
    psw.id as window_id,
    psw.status as window_status,
    psw.start_date_time,
    psw.end_date_time,
    pr.id as pmi_id,
    pr.pmi_number,
    pr.status as pmi_status,
    pr.billing_mode,
    pr.current_window_id,
    pr.window_expire_time,
    CASE 
        WHEN psw.status = 'ACTIVE' AND pr.billing_mode != 'LONG' THEN 'BILLING_MODE_NOT_UPDATED'
        WHEN psw.status = 'ACTIVE' AND pr.current_window_id != psw.id THEN 'CURRENT_WINDOW_NOT_UPDATED'
        WHEN psw.status = 'ACTIVE' AND pr.window_expire_time != psw.end_date_time THEN 'EXPIRE_TIME_NOT_UPDATED'
        WHEN psw.status = 'ACTIVE' AND pr.billing_mode = 'LONG' AND pr.current_window_id = psw.id THEN 'CORRECTLY_CONFIGURED'
        ELSE 'OTHER_ISSUE'
    END as issue_type
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.status = 'ACTIVE'
AND (
    pr.billing_mode != 'LONG' 
    OR pr.current_window_id != psw.id 
    OR pr.window_expire_time != psw.end_date_time
    OR pr.current_window_id IS NULL
    OR pr.window_expire_time IS NULL
)
ORDER BY psw.id;

-- ========================================
-- 第二步：修复所有有问题的活跃窗口
-- ========================================
SELECT '=== 修复活跃窗口的PMI记录 ===' as step;

-- 修复所有活跃窗口对应的PMI记录
UPDATE t_pmi_records pr
JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
SET 
    pr.billing_mode = 'LONG',
    pr.current_window_id = psw.id,
    pr.window_expire_time = psw.end_date_time,
    pr.status = 'ACTIVE',
    pr.updated_at = NOW()
WHERE psw.status = 'ACTIVE'
AND (
    pr.billing_mode != 'LONG' 
    OR pr.current_window_id != psw.id 
    OR pr.window_expire_time != psw.end_date_time
    OR pr.current_window_id IS NULL
    OR pr.window_expire_time IS NULL
);

-- 显示修复结果
SELECT 
    'Fixed Records' as metric,
    ROW_COUNT() as count;

-- ========================================
-- 第三步：验证修复结果
-- ========================================
SELECT '=== 修复结果验证 ===' as step;

SELECT 
    'Verification Results' as result_type,
    psw.id as window_id,
    psw.status as window_status,
    psw.start_date_time,
    psw.end_date_time,
    pr.id as pmi_id,
    pr.pmi_number,
    pr.status as pmi_status,
    pr.billing_mode,
    pr.current_window_id,
    pr.window_expire_time,
    CASE 
        WHEN psw.status = 'ACTIVE' AND pr.billing_mode = 'LONG' AND pr.current_window_id = psw.id AND pr.window_expire_time = psw.end_date_time THEN 'CORRECTLY_FIXED'
        ELSE 'STILL_HAS_ISSUES'
    END as fix_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.status = 'ACTIVE'
ORDER BY psw.id;

-- ========================================
-- 第四步：特别验证窗口2052
-- ========================================
SELECT '=== 窗口2052特别验证 ===' as step;

SELECT 
    'Window 2052 Verification' as verification_type,
    psw.id as window_id,
    pr.pmi_number,
    psw.status as window_status,
    psw.start_date_time,
    psw.end_date_time,
    pr.status as pmi_status,
    pr.billing_mode,
    pr.current_window_id,
    pr.window_expire_time,
    TIMESTAMPDIFF(MINUTE, NOW(), psw.end_date_time) as minutes_remaining,
    CASE 
        WHEN psw.status = 'ACTIVE' AND pr.billing_mode = 'LONG' AND pr.current_window_id = 2052 AND pr.window_expire_time = psw.end_date_time THEN 'PERFECT'
        ELSE 'NEEDS_ATTENTION'
    END as final_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 2052;

-- ========================================
-- 第五步：检查是否有其他需要开启的窗口
-- ========================================
SELECT '=== 检查需要开启的窗口 ===' as step;

SELECT 
    'Windows to Activate' as check_type,
    psw.id as window_id,
    pr.pmi_number,
    psw.status as window_status,
    psw.start_date_time,
    psw.end_date_time,
    pr.status as pmi_status,
    pr.billing_mode,
    CASE 
        WHEN psw.start_date_time <= NOW() AND psw.end_date_time > NOW() THEN 'SHOULD_BE_ACTIVE'
        WHEN psw.start_date_time > NOW() THEN 'FUTURE_WINDOW'
        WHEN psw.end_date_time <= NOW() THEN 'EXPIRED_WINDOW'
        ELSE 'UNKNOWN'
    END as expected_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.status = 'PENDING'
AND psw.start_date_time <= NOW()
AND psw.end_date_time > NOW()
ORDER BY psw.start_date_time;

-- ========================================
-- 第六步：激活需要开启的窗口
-- ========================================

-- 更新窗口状态为ACTIVE
UPDATE t_pmi_schedule_windows 
SET 
    status = 'ACTIVE',
    actual_start_time = NOW(),
    updated_at = NOW()
WHERE status = 'PENDING'
AND start_date_time <= NOW()
AND end_date_time > NOW();

-- 显示激活的窗口数量
SELECT 
    'Activated Windows' as metric,
    ROW_COUNT() as count;

-- 更新对应的PMI记录
UPDATE t_pmi_records pr
JOIN t_pmi_schedule_windows psw ON pr.id = psw.pmi_record_id
SET 
    pr.billing_mode = 'LONG',
    pr.current_window_id = psw.id,
    pr.window_expire_time = psw.end_date_time,
    pr.status = 'ACTIVE',
    pr.updated_at = NOW()
WHERE psw.status = 'ACTIVE'
AND psw.actual_start_time >= DATE_SUB(NOW(), INTERVAL 1 MINUTE); -- 刚刚激活的窗口

-- 显示更新的PMI记录数量
SELECT 
    'Updated PMI Records' as metric,
    ROW_COUNT() as count;

-- ========================================
-- 第七步：最终状态报告
-- ========================================
SELECT '=== 最终状态报告 ===' as step;

SELECT 
    'Final Status Report' as report_type,
    COUNT(*) as total_active_windows,
    COUNT(CASE WHEN pr.billing_mode = 'LONG' THEN 1 END) as correct_billing_mode,
    COUNT(CASE WHEN pr.current_window_id = psw.id THEN 1 END) as correct_current_window,
    COUNT(CASE WHEN pr.window_expire_time = psw.end_date_time THEN 1 END) as correct_expire_time,
    COUNT(CASE WHEN pr.billing_mode = 'LONG' AND pr.current_window_id = psw.id AND pr.window_expire_time = psw.end_date_time THEN 1 END) as fully_correct
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.status = 'ACTIVE';

-- 显示所有活跃窗口的详细状态
SELECT 
    'Active Windows Details' as details_type,
    psw.id as window_id,
    pr.pmi_number,
    psw.start_date_time,
    psw.end_date_time,
    pr.billing_mode,
    pr.current_window_id,
    pr.window_expire_time,
    TIMESTAMPDIFF(MINUTE, NOW(), psw.end_date_time) as minutes_remaining,
    CASE 
        WHEN pr.billing_mode = 'LONG' AND pr.current_window_id = psw.id AND pr.window_expire_time = psw.end_date_time THEN 'PERFECT'
        ELSE 'HAS_ISSUES'
    END as status_check
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.status = 'ACTIVE'
ORDER BY psw.id;

-- ========================================
-- 第八步：创建监控视图（可选）
-- ========================================
SELECT '=== 创建监控视图 ===' as step;

-- 创建一个视图来监控窗口和PMI状态的一致性
CREATE OR REPLACE VIEW v_window_pmi_status_check AS
SELECT 
    psw.id as window_id,
    pr.id as pmi_id,
    pr.pmi_number,
    psw.status as window_status,
    pr.status as pmi_status,
    psw.start_date_time,
    psw.end_date_time,
    pr.billing_mode,
    pr.current_window_id,
    pr.window_expire_time,
    CASE 
        WHEN psw.status = 'ACTIVE' AND pr.billing_mode = 'LONG' AND pr.current_window_id = psw.id AND pr.window_expire_time = psw.end_date_time THEN 'CONSISTENT'
        WHEN psw.status = 'ACTIVE' AND (pr.billing_mode != 'LONG' OR pr.current_window_id != psw.id OR pr.window_expire_time != psw.end_date_time) THEN 'INCONSISTENT'
        WHEN psw.status = 'PENDING' AND psw.start_date_time <= NOW() AND psw.end_date_time > NOW() THEN 'SHOULD_BE_ACTIVE'
        WHEN psw.status = 'ACTIVE' AND psw.end_date_time <= NOW() THEN 'SHOULD_BE_COMPLETED'
        ELSE 'NORMAL'
    END as consistency_status,
    TIMESTAMPDIFF(MINUTE, NOW(), psw.end_date_time) as minutes_remaining
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
ORDER BY psw.id;

SELECT 'Monitoring view created successfully' as result;
