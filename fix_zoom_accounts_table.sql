-- 修复Zoom账号表结构
-- 删除错误创建的t_zoom_users表，为t_zoom_accounts表添加正确字段
-- 执行时间：2025-08-01

USE zoombusV;

-- 删除错误创建的t_zoom_users表（如果存在）
DROP TABLE IF EXISTS t_zoom_users;

-- 检查t_zoom_accounts表当前结构
DESCRIBE t_zoom_accounts;

-- 为t_zoom_accounts表添加账号使用状态字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'zoombusV' 
     AND TABLE_NAME = 't_zoom_accounts' 
     AND COLUMN_NAME = 'account_status') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN account_status VARCHAR(20) DEFAULT ''AVAILABLE'' COMMENT ''账号使用状态: AVAILABLE-可使用, IN_USE-使用中, MAINTENANCE-维护中'' AFTER usage_status',
    'SELECT ''account_status column already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加原始PMI字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'zoombusV' 
     AND TABLE_NAME = 't_zoom_accounts' 
     AND COLUMN_NAME = 'original_pmi') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN original_pmi VARCHAR(20) COMMENT ''原始PMI号码'' AFTER account_status',
    'SELECT ''original_pmi column already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加当前PMI字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'zoombusV' 
     AND TABLE_NAME = 't_zoom_accounts' 
     AND COLUMN_NAME = 'current_pmi') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN current_pmi VARCHAR(20) COMMENT ''当前PMI号码'' AFTER original_pmi',
    'SELECT ''current_pmi column already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加PMI更新时间字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'zoombusV' 
     AND TABLE_NAME = 't_zoom_accounts' 
     AND COLUMN_NAME = 'pmi_updated_at') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN pmi_updated_at DATETIME COMMENT ''PMI最后更新时间'' AFTER current_pmi',
    'SELECT ''pmi_updated_at column already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为新字段添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = 'zoombusV' 
     AND TABLE_NAME = 't_zoom_accounts' 
     AND INDEX_NAME = 'idx_account_status') = 0,
    'ALTER TABLE t_zoom_accounts ADD INDEX idx_account_status (account_status)',
    'SELECT ''idx_account_status index already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = 'zoombusV' 
     AND TABLE_NAME = 't_zoom_accounts' 
     AND INDEX_NAME = 'idx_original_pmi') = 0,
    'ALTER TABLE t_zoom_accounts ADD INDEX idx_original_pmi (original_pmi)',
    'SELECT ''idx_original_pmi index already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = 'zoombusV' 
     AND TABLE_NAME = 't_zoom_accounts' 
     AND INDEX_NAME = 'idx_current_pmi') = 0,
    'ALTER TABLE t_zoom_accounts ADD INDEX idx_current_pmi (current_pmi)',
    'SELECT ''idx_current_pmi index already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 初始化现有数据的账号状态（基于usage_status）
UPDATE t_zoom_accounts 
SET account_status = CASE 
    WHEN usage_status = 'IN_USE' THEN 'IN_USE'
    WHEN usage_status = 'MAINTENANCE' THEN 'MAINTENANCE'
    ELSE 'AVAILABLE'
END
WHERE account_status IS NULL OR account_status = '';

-- 显示最终表结构
DESCRIBE t_zoom_accounts;

-- 显示表的创建语句确认
SHOW CREATE TABLE t_zoom_accounts;
