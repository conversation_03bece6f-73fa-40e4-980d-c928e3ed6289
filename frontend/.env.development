# 开发环境配置文件
# 此文件仅在开发环境(npm start)时生效

# 端口配置
PORT=3000

# 禁用自动打开浏览器
BROWSER=none

# 启用快速刷新
FAST_REFRESH=true

# 启用源码映射
GENERATE_SOURCEMAP=true

# 禁用ESLint警告作为错误
ESLINT_NO_DEV_ERRORS=true

# 启用热重载
WDS_SOCKET_HOST=localhost
WDS_SOCKET_PORT=3000

# API代理配置（通过package.json中的proxy字段处理）
# 所有/api请求会自动代理到http://localhost:8080

# React应用配置
REACT_APP_ENV=development
REACT_APP_API_BASE_URL=http://localhost:8080
REACT_APP_VERSION=$npm_package_version

# Mock API配置（当后端服务不可用时启用）
REACT_APP_USE_MOCK_API=false

# 开发工具配置
REACT_EDITOR=code
