const path = require('path');

module.exports = {
  webpack: {
    configure: (webpackConfig, { env, paths }) => {
      // 忽略source map警告
      webpackConfig.ignoreWarnings = [
        {
          module: /node_modules\/@antv\/scale/,
          message: /Failed to parse source map/,
        },
        {
          module: /node_modules\/@antv/,
          message: /Failed to parse source map/,
        },
        // 忽略所有source map相关的警告
        /Failed to parse source map/,
      ];

      // 在开发环境中禁用source map loader对特定模块的处理
      if (env === 'development') {
        const sourceMapLoaderRule = webpackConfig.module.rules.find(
          rule => rule.enforce === 'pre' && rule.use && rule.use.some(
            use => use.loader && use.loader.includes('source-map-loader')
          )
        );

        if (sourceMapLoaderRule) {
          sourceMapLoaderRule.exclude = [
            /node_modules\/@antv/,
            /node_modules\/d3-/,
            ...(sourceMapLoaderRule.exclude || [])
          ];
        }
      }

      return webpackConfig;
    },
  },
  devServer: {
    host: '0.0.0.0', // 允许外部访问
    port: 3000, // 明确指定端口
    // 禁用overlay中的警告显示
    client: {
      overlay: {
        errors: true,
        warnings: false, // 不显示警告
      },
    },
  },
};
