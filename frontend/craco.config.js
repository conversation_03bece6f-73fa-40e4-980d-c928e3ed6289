const path = require('path');

module.exports = {
  webpack: {
    configure: (webpackConfig, { env, paths }) => {
      // 忽略source map警告
      webpackConfig.ignoreWarnings = [
        {
          module: /node_modules\/@antv\/scale/,
          message: /Failed to parse source map/,
        },
        {
          module: /node_modules\/@antv/,
          message: /Failed to parse source map/,
        },
        // 忽略所有source map相关的警告
        /Failed to parse source map/,
        // 忽略fs.F_OK deprecation warning
        /fs\.F_OK is deprecated/,
      ];

      // 在开发环境中禁用source map loader对特定模块的处理
      if (env === 'development') {
        const sourceMapLoaderRule = webpackConfig.module.rules.find(
          rule => rule.enforce === 'pre' && rule.use && rule.use.some(
            use => use.loader && use.loader.includes('source-map-loader')
          )
        );

        if (sourceMapLoaderRule) {
          sourceMapLoaderRule.exclude = [
            /node_modules\/@antv/,
            /node_modules\/d3-/,
            ...(sourceMapLoaderRule.exclude || [])
          ];
        }
      }

      // 生产环境优化
      if (env === 'production') {
        // 启用代码分割
        webpackConfig.optimization = {
          ...webpackConfig.optimization,
          splitChunks: {
            chunks: 'all',
            cacheGroups: {
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name: 'vendors',
                chunks: 'all',
                priority: 10,
              },
              antd: {
                test: /[\\/]node_modules[\\/]antd[\\/]/,
                name: 'antd',
                chunks: 'all',
                priority: 20,
              },
              react: {
                test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
                name: 'react',
                chunks: 'all',
                priority: 20,
              },
            },
          },
        };

        // 禁用source map生成以加快构建速度
        webpackConfig.devtool = false;
      }

      return webpackConfig;
    },
  },
  devServer: {
    host: '0.0.0.0', // 允许外部访问
    port: 3000, // 明确指定端口
    // 禁用overlay中的警告显示
    client: {
      overlay: {
        errors: true,
        warnings: false, // 不显示警告
      },
    },
  },
};
