{"name": "zoombus-frontend", "version": "1.0.0", "description": "Zoom User Management System Frontend", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/plots": "^2.6.0", "@stomp/stompjs": "^7.1.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@uiw/react-json-view": "^2.0.0-alpha.34", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.10", "http-proxy-middleware": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^3.1.2", "sockjs-client": "^1.6.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "devDependencies": {"@craco/craco": "^7.1.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080"}