<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试事务回滚对日志记录的影响</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1890ff;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        button.danger {
            background: #ff4d4f;
        }
        button.danger:hover {
            background: #ff7875;
        }
        .result {
            margin-top: 15px;
            padding: 12px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .result.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .result.info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .warning {
            background: #fffbe6;
            border: 1px solid #ffe58f;
            color: #d48806;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 事务回滚对日志记录影响测试</h1>
        <p>测试在业务事务失败回滚时，Zoom API日志和Webhook事件记录是否仍然被保存</p>
        
        <div class="warning">
            ⚠️ <strong>注意</strong>：这些测试可能会触发实际的API调用或模拟业务失败场景，请在测试环境中运行。
        </div>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>📊 测试1：Zoom API日志记录独立性</h3>
            <p>测试当业务逻辑失败时，Zoom API调用日志是否仍然被记录</p>
            <button onclick="testApiLogIndependence()">触发API调用（模拟业务失败）</button>
            <button onclick="checkApiLogs()">查看最新API日志</button>
            <div id="apiLogResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试2：Webhook事件记录独立性</h3>
            <p>测试当Webhook业务处理失败时，事件接收记录是否仍然被保存</p>
            <button onclick="testWebhookEventIndependence()">模拟Webhook事件（业务失败）</button>
            <button onclick="checkWebhookEvents()">查看最新Webhook事件</button>
            <div id="webhookEventResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 测试3：会议报告获取事务测试</h3>
            <p>测试会议报告获取过程中的事务回滚对日志记录的影响</p>
            <button onclick="testMeetingReportTransaction()">触发会议报告获取</button>
            <button onclick="checkMeetingReportLogs()">查看相关日志</button>
            <div id="meetingReportResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📈 测试4：数据库日志验证</h3>
            <p>直接查询数据库验证日志记录的完整性</p>
            <button onclick="verifyDatabaseLogs()">验证数据库日志</button>
            <div id="databaseResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';

        // 测试API日志记录独立性
        async function testApiLogIndependence() {
            const resultDiv = document.getElementById('apiLogResult');
            resultDiv.textContent = '正在测试API日志记录独立性...';
            resultDiv.className = 'result info';
            
            try {
                // 这里可以调用一个会触发Zoom API但业务逻辑可能失败的接口
                const response = await fetch(`${API_BASE}/zoom-users/sync`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ API调用完成！
响应状态: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}

请点击"查看最新API日志"按钮验证日志是否被记录。`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ API调用失败: ${error.message}

即使调用失败，API日志也应该被记录。请点击"查看最新API日志"按钮验证。`;
            }
        }

        // 查看最新API日志
        async function checkApiLogs() {
            const resultDiv = document.getElementById('apiLogResult');
            resultDiv.textContent = '正在查询最新API日志...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch(`${API_BASE}/zoom-api-logs?page=0&size=5&sort=requestTime,desc`);
                const data = await response.json();
                
                if (response.ok && data.content && data.content.length > 0) {
                    resultDiv.className = 'result success';
                    let resultText = `✅ 查询到 ${data.content.length} 条最新API日志：\n\n`;
                    
                    data.content.forEach((log, index) => {
                        resultText += `📋 日志 ${index + 1}:\n`;
                        resultText += `  请求时间: ${log.requestTime}\n`;
                        resultText += `  API路径: ${log.apiPath}\n`;
                        resultText += `  业务类型: ${log.businessType}\n`;
                        resultText += `  是否成功: ${log.isSuccess ? '✅' : '❌'}\n`;
                        resultText += `  响应状态: ${log.responseStatus || 'N/A'}\n`;
                        resultText += `  耗时: ${log.durationMs || 'N/A'}ms\n\n`;
                    });
                    
                    resultDiv.textContent = resultText;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 未查询到API日志记录`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 查询API日志失败: ${error.message}`;
            }
        }

        // 测试Webhook事件记录独立性
        async function testWebhookEventIndependence() {
            const resultDiv = document.getElementById('webhookEventResult');
            resultDiv.textContent = '正在测试Webhook事件记录独立性...';
            resultDiv.className = 'result info';
            
            try {
                // 模拟一个Webhook事件
                const testEvent = {
                    event: 'meeting.started',
                    payload: {
                        object: {
                            uuid: 'test-uuid-' + Date.now(),
                            id: '123456789',
                            host_id: 'test-host',
                            topic: '测试会议 - 事务回滚测试'
                        }
                    }
                };
                
                const response = await fetch(`${API_BASE}/webhook/zoom/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testEvent)
                });
                
                const data = await response.text();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ Webhook事件已发送！
响应状态: ${response.status}
响应数据: ${data}

请点击"查看最新Webhook事件"按钮验证事件是否被记录。`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Webhook事件发送失败: ${error.message}

即使发送失败，Webhook事件记录也应该被保存。请点击"查看最新Webhook事件"按钮验证。`;
            }
        }

        // 查看最新Webhook事件
        async function checkWebhookEvents() {
            const resultDiv = document.getElementById('webhookEventResult');
            resultDiv.textContent = '正在查询最新Webhook事件...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch(`${API_BASE}/webhook/events?page=0&size=5`);
                const data = await response.json();
                
                if (response.ok && data.content && data.content.length > 0) {
                    resultDiv.className = 'result success';
                    let resultText = `✅ 查询到 ${data.content.length} 条最新Webhook事件：\n\n`;
                    
                    data.content.forEach((event, index) => {
                        resultText += `📋 事件 ${index + 1}:\n`;
                        resultText += `  接收时间: ${event.createdAt}\n`;
                        resultText += `  事件类型: ${event.eventType}\n`;
                        resultText += `  处理状态: ${event.processingStatus}\n`;
                        resultText += `  账号ID: ${event.zoomAccountId || 'N/A'}\n`;
                        resultText += `  会议ID: ${event.zoomMeetingId || 'N/A'}\n`;
                        if (event.errorMessage) {
                            resultText += `  错误信息: ${event.errorMessage}\n`;
                        }
                        resultText += `\n`;
                    });
                    
                    resultDiv.textContent = resultText;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 未查询到Webhook事件记录`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 查询Webhook事件失败: ${error.message}`;
            }
        }

        // 测试会议报告获取事务
        async function testMeetingReportTransaction() {
            const resultDiv = document.getElementById('meetingReportResult');
            resultDiv.textContent = '正在测试会议报告获取事务...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch(`${API_BASE}/meeting-reports/fetch/meeting/1a0d3ce3-512a-4526-a4ed-6ee727e8a60f`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 会议报告获取请求已发送！
响应状态: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}

请点击"查看相关日志"按钮验证相关日志是否被记录。`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 会议报告获取失败: ${error.message}

请点击"查看相关日志"按钮验证日志记录情况。`;
            }
        }

        // 查看会议报告相关日志
        async function checkMeetingReportLogs() {
            const resultDiv = document.getElementById('meetingReportResult');
            resultDiv.textContent = '正在查询会议报告相关日志...';
            resultDiv.className = 'result info';
            
            try {
                // 查询API日志
                const apiResponse = await fetch(`${API_BASE}/zoom-api-logs?businessType=MEETING_REPORT&page=0&size=3&sort=requestTime,desc`);
                const apiData = await apiResponse.json();
                
                // 查询Webhook事件
                const webhookResponse = await fetch(`${API_BASE}/webhook/events?eventType=meeting&page=0&size=3`);
                const webhookData = await webhookResponse.json();
                
                let resultText = '';
                
                if (apiResponse.ok && apiData.content && apiData.content.length > 0) {
                    resultText += `✅ 会议报告相关API日志 (${apiData.content.length}条):\n`;
                    apiData.content.forEach((log, index) => {
                        resultText += `  ${index + 1}. ${log.requestTime} - ${log.apiPath} - ${log.isSuccess ? '成功' : '失败'}\n`;
                    });
                    resultText += '\n';
                } else {
                    resultText += '❌ 未找到会议报告相关API日志\n\n';
                }
                
                if (webhookResponse.ok && webhookData.content && webhookData.content.length > 0) {
                    resultText += `✅ 会议相关Webhook事件 (${webhookData.content.length}条):\n`;
                    webhookData.content.forEach((event, index) => {
                        resultText += `  ${index + 1}. ${event.createdAt} - ${event.eventType} - ${event.processingStatus}\n`;
                    });
                } else {
                    resultText += '❌ 未找到会议相关Webhook事件';
                }
                
                resultDiv.className = 'result success';
                resultDiv.textContent = resultText;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 查询日志失败: ${error.message}`;
            }
        }

        // 验证数据库日志
        async function verifyDatabaseLogs() {
            const resultDiv = document.getElementById('databaseResult');
            resultDiv.textContent = '正在验证数据库日志完整性...';
            resultDiv.className = 'result info';
            
            try {
                // 获取统计信息
                const apiStatsResponse = await fetch(`${API_BASE}/zoom-api-logs/stats/overall?hours=1`);
                const webhookStatsResponse = await fetch(`${API_BASE}/webhook/stats`);
                
                let resultText = '';
                
                if (apiStatsResponse.ok) {
                    const apiStats = await apiStatsResponse.json();
                    resultText += `📊 API日志统计 (最近1小时):\n`;
                    resultText += `  总调用次数: ${apiStats.totalCount || 0}\n`;
                    resultText += `  成功次数: ${apiStats.successCount || 0}\n`;
                    resultText += `  失败次数: ${apiStats.failureCount || 0}\n`;
                    resultText += `  平均响应时间: ${apiStats.avgDuration || 0}ms\n\n`;
                }
                
                if (webhookStatsResponse.ok) {
                    const webhookStats = await webhookStatsResponse.json();
                    resultText += `📊 Webhook事件统计:\n`;
                    resultText += `  总事件数: ${webhookStats.totalWebhookCount || 0}\n`;
                    resultText += `  成功处理: ${webhookStats.successfulWebhookCount || 0}\n`;
                    resultText += `  处理失败: ${webhookStats.failedWebhookCount || 0}\n`;
                }
                
                resultDiv.className = 'result success';
                resultDiv.textContent = resultText || '✅ 数据库日志验证完成';
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 验证数据库日志失败: ${error.message}`;
            }
        }

        // 页面加载完成后的提示
        window.onload = function() {
            console.log('事务回滚测试页面加载完成');
            console.log('请按顺序执行测试，验证日志记录的独立性');
        };
    </script>
</body>
</html>
