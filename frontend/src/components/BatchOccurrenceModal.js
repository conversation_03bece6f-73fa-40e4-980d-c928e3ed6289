import React, { useState } from 'react';
import { Modal, Table, Button, Checkbox, message, Space, Popconfirm, Tag } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { meetingApi } from '../services/api';

const BatchOccurrenceModal = ({ 
  visible, 
  onCancel, 
  onSuccess, 
  meetingId, 
  occurrences, 
  mainDetail 
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的会议实例');
      return;
    }

    setLoading(true);
    try {
      const selectedOccurrences = occurrences.filter(occ =>
        selectedRowKeys.includes(occ.id)
      );

      // 提取occurrence IDs
      const occurrenceIds = selectedOccurrences.map(occ => occ.occurrenceId);

      // 调用批量删除API
      const response = await meetingApi.batchDeleteMeetingOccurrences(meetingId, occurrenceIds);

      if (response.data.success) {
        const { successCount, failCount, failedOccurrences } = response.data;

        if (failCount > 0) {
          message.warning(`批量删除完成：成功 ${successCount} 个，失败 ${failCount} 个`);
        } else {
          message.success(`成功删除 ${successCount} 个会议实例`);
        }

        setSelectedRowKeys([]);
        onSuccess();
        onCancel();
      } else {
        message.error('批量删除失败');
      }

    } catch (error) {
      console.error('批量删除失败:', error);
      if (error.response?.data?.error) {
        message.error(`批量删除失败: ${error.response.data.error}`);
      } else {
        message.error('批量删除失败');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSingleDelete = async (occurrence) => {
    setLoading(true);
    try {
      await meetingApi.deleteMeetingOccurrence(meetingId, occurrence.occurrenceId);
      message.success('会议实例删除成功');
      onSuccess(); // 这会触发父组件的handleEditSuccess，重新加载数据
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除会议实例失败');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: '开始时间',
      dataIndex: 'occurrenceStartTime',
      key: 'occurrenceStartTime',
      width: 180,
      render: (time) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '未设置',
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration) => `${duration || mainDetail?.duration || '未设置'} 分钟`,
    },
    {
      title: '状态',
      dataIndex: 'occurrenceStatus',
      key: 'occurrenceStatus',
      width: 100,
      render: (status) => (
        <Tag color={status === 'available' ? 'green' : 'orange'}>
          {status || '未知'}
        </Tag>
      ),
    },
    {
      title: 'Occurrence ID',
      dataIndex: 'occurrenceId',
      key: 'occurrenceId',
      width: 150,
      render: (id) => (
        <span style={{ fontSize: '12px', fontFamily: 'monospace' }}>
          {id ? id.substring(0, 12) + '...' : '无'}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            title="编辑"
            onClick={() => {
              // 这里可以触发单个编辑
              message.info('单个编辑功能开发中...');
            }}
          />
          <Popconfirm
            title="确定删除这个会议实例吗？"
            onConfirm={() => handleSingleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              title="删除"
              danger
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: handleSelectChange,
    getCheckboxProps: (record) => ({
      disabled: record.occurrenceStatus === 'deleted',
    }),
  };

  return (
    <Modal
      title={`批量管理会议实例 (${occurrences.length} 个)`}
      open={visible}
      onCancel={onCancel}
      width={900}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
        <Popconfirm
          key="batchDelete"
          title={`确定要删除选中的 ${selectedRowKeys.length} 个会议实例吗？`}
          onConfirm={handleBatchDelete}
          okText="确定删除"
          cancelText="取消"
          disabled={selectedRowKeys.length === 0}
        >
          <Button 
            type="primary" 
            danger
            loading={loading}
            disabled={selectedRowKeys.length === 0}
          >
            批量删除 ({selectedRowKeys.length})
          </Button>
        </Popconfirm>,
      ]}
    >
      <div style={{ marginBottom: '16px' }}>
        <div style={{ marginBottom: '8px', fontSize: '14px', color: '#666' }}>
          <strong>会议主题：</strong>{mainDetail?.topic}
        </div>
        <div style={{ fontSize: '12px', color: '#999' }}>
          选择要操作的会议实例，支持批量删除操作
        </div>
      </div>

      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={occurrences}
        rowKey="id"
        pagination={false}
        size="small"
        scroll={{ y: 400 }}
        loading={loading}
      />

      <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: '6px' }}>
        <div style={{ fontSize: '12px', color: '#cf1322' }}>
          <strong>批量操作说明：</strong>
          <ul style={{ margin: '4px 0 0 16px', padding: 0 }}>
            <li>批量删除操作不可撤销，请谨慎操作</li>
            <li>已删除的会议实例无法选择</li>
            <li>删除会议实例会通知所有参会者</li>
          </ul>
        </div>
      </div>
    </Modal>
  );
};

export default BatchOccurrenceModal;
