import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, DatePicker, InputNumber, message, Spin } from 'antd';
import dayjs from 'dayjs';
import { meetingApi } from '../services/api';

const EditOccurrenceModal = ({ 
  visible, 
  onCancel, 
  onSuccess, 
  meetingId, 
  occurrence, 
  mainDetail 
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (visible && occurrence) {
      // 初始化表单数据
      form.setFieldsValue({
        topic: occurrence.topic || mainDetail?.topic || '',
        agenda: occurrence.agenda || mainDetail?.agenda || '',
        startTime: occurrence.occurrenceStartTime ? 
          dayjs(occurrence.occurrenceStartTime) : 
          (mainDetail?.startTime ? dayjs(mainDetail.startTime) : null),
        duration: occurrence.duration || mainDetail?.duration || 60
      });
    }
  }, [visible, occurrence, mainDetail, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      // 构建更新数据
      const updateData = {
        topic: values.topic,
        agenda: values.agenda,
        start_time: values.startTime ? values.startTime.toISOString() : undefined,
        duration: values.duration
      };

      // 调用API更新occurrence
      await meetingApi.updateMeetingOccurrence(meetingId, occurrence.occurrenceId, updateData);
      
      message.success('会议实例更新成功');
      onSuccess();
      onCancel();
      
    } catch (error) {
      console.error('更新会议实例失败:', error);
      if (error.response?.data?.error) {
        message.error(`更新失败: ${error.response.data.error}`);
      } else {
        message.error('更新会议实例失败');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="编辑会议实例"
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={submitting}
      width={600}
      destroyOnClose
    >
      <Spin spinning={loading}>
        <div style={{ marginBottom: '16px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
          <div style={{ fontSize: '14px', color: '#666' }}>
            <div><strong>Occurrence ID:</strong> {occurrence?.occurrenceId}</div>
            <div><strong>当前状态:</strong> {occurrence?.occurrenceStatus || '未知'}</div>
          </div>
        </div>

        <Form
          form={form}
          layout="vertical"
          preserve={false}
        >
          <Form.Item
            name="topic"
            label="会议主题"
            rules={[
              { required: true, message: '请输入会议主题' },
              { max: 200, message: '主题长度不能超过200个字符' }
            ]}
          >
            <Input placeholder="请输入会议主题" />
          </Form.Item>

          <Form.Item
            name="agenda"
            label="会议议程"
            rules={[
              { max: 2000, message: '议程长度不能超过2000个字符' }
            ]}
          >
            <Input.TextArea 
              rows={3} 
              placeholder="请输入会议议程（可选）" 
            />
          </Form.Item>

          <Form.Item
            name="startTime"
            label="开始时间"
            rules={[
              { required: true, message: '请选择开始时间' }
            ]}
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择开始时间"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="duration"
            label="持续时间（分钟）"
            rules={[
              { required: true, message: '请输入持续时间' },
              { type: 'number', min: 1, max: 1440, message: '持续时间必须在1-1440分钟之间' }
            ]}
          >
            <InputNumber
              min={1}
              max={1440}
              placeholder="请输入持续时间"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>

        <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#fff7e6', border: '1px solid #ffd591', borderRadius: '6px' }}>
          <div style={{ fontSize: '12px', color: '#d46b08' }}>
            <strong>注意：</strong>
            <ul style={{ margin: '4px 0 0 16px', padding: 0 }}>
              <li>修改会议实例只会影响这一场会议，不会影响其他场次</li>
              <li>时间修改后，参会者会收到会议更新通知</li>
              <li>建议在会议开始前至少15分钟完成修改</li>
            </ul>
          </div>
        </div>
      </Spin>
    </Modal>
  );
};

export default EditOccurrenceModal;
