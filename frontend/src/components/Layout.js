import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, Space, message } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  VideoCameraOutlined,
  UserSwitchOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  LogoutOutlined,
  SettingOutlined,
  KeyOutlined,
  UsergroupAddOutlined,
  LinkOutlined,
  DollarOutlined,
  ApiOutlined,
  CodeOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import TraceIdDisplay from './TraceIdDisplay';
import '../styles/mobile.css';

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const { Header, Sider, Content } = Layout;

const AppLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [openKeys, setOpenKeys] = useState([]); // 控制SubMenu展开状态
  const navigate = useNavigate();
  const location = useLocation();

  // 初始化移动端状态
  useEffect(() => {
    const mobile = isMobile();
    setIsMobileView(mobile);
    if (mobile) {
      setCollapsed(true);
    }
  }, []);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      const mobile = isMobile();
      setIsMobileView(mobile);
      // 从桌面端切换到移动端时自动折叠
      if (mobile && !isMobileView) {
        setCollapsed(true);
      }
      // 从移动端切换回桌面端时自动展开
      if (!mobile && isMobileView) {
        setCollapsed(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobileView]);

  // 根据当前路径自动展开对应的SubMenu
  useEffect(() => {
    const adminPaths = ['/admin-users', '/version-management', '/database-migration', '/scheduled-task-management'];
    if (adminPaths.includes(location.pathname)) {
      setOpenKeys(['admin']);
    }
  }, [location.pathname]);

  const handleLogout = () => {
    // 清除本地存储的认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    message.success('已退出登录');
    // 重定向到登录页面
    navigate('/login');
  };

  // 移动端点击遮罩关闭菜单
  const handleMaskClick = () => {
    if (isMobileView && !collapsed) {
      setCollapsed(true);
    }
  };

  const userMenu = [
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const menuItems = [
    // 看板分组
    {
      type: 'group',
      label: '看板',
      children: [
        {
          key: '/dashboard',
          icon: <DashboardOutlined />,
          label: '仪表板',
        },
        {
          key: '/zoom-meeting-dashboard',
          icon: <VideoCameraOutlined />,
          label: 'Zoom会议看板',
        },
        {
          key: '/pmi-billing-management',
          icon: <DollarOutlined />,
          label: 'PMI计费管理',
        },
        {
          key: '/pmi-reports',
          icon: <FileTextOutlined />,
          label: 'PMI报告',
        },
        {
          key: '/scheduled-task-management',
          icon: <DashboardOutlined />,
          label: '定时任务管理',
        },
        {
          key: '/pmi-task-monitor',
          icon: <ClockCircleOutlined />,
          label: 'PMI任务监控',
        },
      ],
    },
    // 用户分组
    {
      type: 'group',
      label: '用户',
      children: [
        {
          key: '/users',
          icon: <UserOutlined />,
          label: '用户管理',
        },
        {
          key: '/meetings',
          icon: <VideoCameraOutlined />,
          label: '会议管理',
        },
        {
          key: '/pmi-management',
          icon: <LinkOutlined />,
          label: 'PMI管理',
        },
      ],
    },
    // Zoom分组
    {
      type: 'group',
      label: 'Zoom',
      children: [
        {
          key: '/zoom-auth',
          icon: <KeyOutlined />,
          label: 'Zoom主账号管理',
        },
        {
          key: '/zoom-users',
          icon: <UsergroupAddOutlined />,
          label: 'Zoom用户管理',
        },
        {
          key: '/webhook-events',
          icon: <BellOutlined />,
          label: 'Webhook事件',
        },
        {
          key: '/zoom-api-logs',
          icon: <ApiOutlined />,
          label: 'API调用日志',
        },
      ],
    },
    // 参会分组
    {
      type: 'group',
      label: '参会',
      children: [
        {
          key: '/join-account/tokens',
          icon: <LinkOutlined />,
          label: '权益链接管理',
        },
        {
          key: '/join-account/windows',
          icon: <ClockCircleOutlined />,
          label: '使用窗口查询',
        },
        {
          key: '/join-account/password-logs',
          icon: <KeyOutlined />,
          label: '密码变更日志',
        },
      ],
    },
    // 管理SubMenu - 默认折叠
    {
      key: 'admin',
      icon: <SettingOutlined />,
      label: '管理',
      children: [
        {
          key: '/join-account/system-config',
          icon: <SettingOutlined />,
          label: '系统配置管理',
        },
        {
          key: '/admin-users',
          icon: <UserSwitchOutlined />,
          label: '管理员管理',
        },
        {
          key: '/version-management',
          icon: <CodeOutlined />,
          label: '版本管理',
        },
        {
          key: '/database-migration',
          icon: <DatabaseOutlined />,
          label: '数据库迁移',
        },

        {
          key: '/network-environment',
          icon: <GlobalOutlined />,
          label: '网络环境管理',
        },
        {
          key: '/log-search',
          icon: <SearchOutlined />,
          label: '日志检索',
        },
      ],
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 移动端遮罩层 */}
      {isMobileView && !collapsed && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.45)',
            zIndex: 999,
          }}
          onClick={handleMaskClick}
        />
      )}

      {/* 侧边栏始终存在，通过collapsed控制显示状态 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        theme="dark"
        width={200}
        collapsedWidth={isMobileView ? 0 : 80}
        style={{
          overflow: isMobileView ? 'auto' : 'hidden',
          transition: 'all 0.2s ease',
          ...(isMobileView ? {
            position: 'fixed',
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 1000,
            height: '100vh',
            overflowY: 'auto',
            overflowX: 'hidden',
          } : {}),
        }}
      >
        <div className="logo">
          {collapsed ? (isMobileView ? '' : 'ZB') : 'ZoomBus'}
        </div>
        {/* 菜单显示逻辑：非折叠状态时显示，或者桌面端折叠时显示图标菜单 */}
        {(!collapsed || (!isMobileView && collapsed)) && (
          <Menu
            theme="dark"
            mode="inline"
            selectedKeys={[location.pathname]}
            openKeys={openKeys}
            onOpenChange={setOpenKeys}
            items={menuItems}
            onClick={(item) => {
              handleMenuClick(item);
              // 移动端点击菜单项后自动收起菜单
              if (isMobileView) {
                setCollapsed(true);
              }
            }}
            inlineCollapsed={collapsed && !isMobileView}
            style={{
              height: isMobileView ? 'calc(100vh - 64px)' : 'auto',
              overflowY: isMobileView ? 'auto' : 'visible',
              overflowX: 'hidden',
            }}
          />
        )}
      </Sider>

      <Layout>
        <Header style={{
          padding: isMobileView ? '0 16px' : '0 24px',
          background: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}>
          {/* 菜单切换按钮始终显示 */}
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => {
              console.log('Button clicked, current collapsed:', collapsed, 'isMobileView:', isMobileView);
              setCollapsed(!collapsed);
            }}
            style={{
              fontSize: isMobileView ? 14 : 16,
              width: isMobileView ? 48 : 64,
              height: isMobileView ? 48 : 64
            }}
          />

          <div style={{
            color: '#1890ff',
            fontSize: isMobileView ? 16 : 18,
            fontWeight: 'bold'
          }}>
            {isMobileView ? 'ZoomBus' : 'ZoomBus 管理台'}
          </div>

          <Space>
            <Dropdown menu={{ items: userMenu }} placement="bottomRight">
              <Button type="text" icon={<UserOutlined />} style={{ fontSize: 16 }}>
                管理员
              </Button>
            </Dropdown>
          </Space>
        </Header>

        <Content className="site-layout-content">
          <Outlet />
        </Content>
      </Layout>

      {/* TraceId显示组件 */}
      <TraceIdDisplay />
    </Layout>
  );
};

export default AppLayout;
