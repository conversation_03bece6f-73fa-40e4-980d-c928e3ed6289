import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
    Modal,
    Card,
    Table,
    Button,
    Row,
    Col,
    Tag,
    Badge,
    Typography,
    message,
    Spin,
    Select,
    Alert,
    Space
} from 'antd';
import {
    EyeOutlined,
    SyncOutlined,
    UserOutlined,
    ClockCircleOutlined,
    VideoCameraOutlined,
    DesktopOutlined
} from '@ant-design/icons';
import { meetingReportApi } from '../services/api';
import dayjs from 'dayjs';

const { Text } = Typography;

/**
 * 会议报告详情弹窗组件
 */
const MeetingReportModal = ({
    visible,
    onCancel,
    meeting,
    isMobileView = false
}) => {
    const [loading, setLoading] = useState(false);
    const [reportData, setReportData] = useState(null);
    const [allReports, setAllReports] = useState([]);
    const [selectedReportIndex, setSelectedReportIndex] = useState(0);
    const [participants, setParticipants] = useState([]);
    const [backgroundFetching, setBackgroundFetching] = useState(false);
    const [lastReportId, setLastReportId] = useState(null);

    // 使用 ref 来防止重复触发和记录状态
    const lastFetchTimeRef = useRef(0);
    const pollingTimeoutRef = useRef(null);
    const isPollingRef = useRef(false);

    // 处理报告切换
    const handleReportChange = (index) => {
        if (allReports && allReports[index]) {
            setSelectedReportIndex(index);
            setReportData(allReports[index]);
            setParticipants(allReports[index].participants || []);
        }
    };

    // 获取会议报告数据
    const fetchReportData = useCallback(async (isPollingCall = false) => {
        if (!meeting) return null;

        // 如果是轮询调用，添加日志
        if (isPollingCall) {
            console.log('轮询获取会议报告数据:', meeting.zoomMeetingUuid || meeting.zoomMeetingId);
        } else {
            console.log('获取会议报告数据:', meeting.zoomMeetingUuid || meeting.zoomMeetingId);
        }

        // 只有非轮询调用才显示loading状态
        if (!isPollingCall) {
            setLoading(true);
        }
        try {
            let reportResponse = null;
            let allReportsResponse = null;

            // 首先尝试通过UUID获取所有报告记录
            if (meeting.zoomMeetingUuid) {
                try {
                    allReportsResponse = await meetingReportApi.getAllReportsByUuid(meeting.zoomMeetingUuid);
                    if (allReportsResponse && allReportsResponse.data && allReportsResponse.data.length > 0) {
                        setAllReports(allReportsResponse.data);
                        // 默认选择最新的报告（第一条）
                        reportResponse = { data: allReportsResponse.data[0] };
                        setSelectedReportIndex(0);
                    }
                } catch (error) {
                    console.log('通过UUID获取所有报告失败，尝试获取单条报告:', error);
                    // 如果获取所有报告失败，回退到获取单条报告
                    try {
                        reportResponse = await meetingReportApi.getReportByUuid(meeting.zoomMeetingUuid);
                        if (reportResponse && reportResponse.data) {
                            setAllReports([reportResponse.data]);
                            setSelectedReportIndex(0);
                        }
                    } catch (singleError) {
                        console.log('通过UUID获取单条报告也失败:', singleError);
                    }
                }
            }

            // 如果通过UUID获取失败，尝试通过会议ID获取
            if (!reportResponse && meeting.zoomMeetingId) {
                try {
                    allReportsResponse = await meetingReportApi.getAllReportsByMeetingId(meeting.zoomMeetingId);
                    if (allReportsResponse && allReportsResponse.data && allReportsResponse.data.length > 0) {
                        setAllReports(allReportsResponse.data);
                        // 默认选择最新的报告（第一条）
                        reportResponse = { data: allReportsResponse.data[0] };
                        setSelectedReportIndex(0);
                    }
                } catch (error) {
                    console.log('通过会议ID获取所有报告失败，尝试获取单条报告:', error);
                    // 如果获取所有报告失败，回退到获取单条报告
                    try {
                        reportResponse = await meetingReportApi.getReportByMeetingId(meeting.zoomMeetingId);
                        if (reportResponse && reportResponse.data) {
                            setAllReports([reportResponse.data]);
                            setSelectedReportIndex(0);
                        }
                    } catch (singleError) {
                        console.log('通过会议ID获取单条报告也失败:', singleError);
                    }
                }
            }

            if (reportResponse && reportResponse.data) {
                setReportData(reportResponse.data);
                setLastReportId(reportResponse.data.id);

                // 直接从报告数据中获取参会者列表
                if (reportResponse.data.participants) {
                    setParticipants(reportResponse.data.participants);
                } else {
                    // 如果报告中没有参会者数据，尝试单独获取
                    if (reportResponse.data.id) {
                        try {
                            const participantsResponse = await meetingReportApi.getParticipants(reportResponse.data.id);
                            if (participantsResponse.data) {
                                setParticipants(participantsResponse.data.content || []);
                            }
                        } catch (error) {
                            console.error('获取参会者列表失败:', error);
                            setParticipants([]);
                        }
                    } else {
                        setParticipants([]);
                    }
                }
                return reportResponse; // 返回获取到的数据
            } else {
                setReportData(null);
                setAllReports([]);
                setSelectedReportIndex(0);
                setParticipants([]);
                setLastReportId(null);
                return null;
            }
        } catch (error) {
            console.error('获取会议报告失败:', error);
            if (!isPollingCall) {
                message.error('获取会议报告失败');
            }
            return null;
        } finally {
            if (!isPollingCall) {
                setLoading(false);
            }
        }
    }, [meeting]);

    // 异步触发报告拉取（后台执行，不阻塞UI）
    const triggerBackgroundReportFetch = useCallback(async () => {
        if (!meeting?.meetingUuid && !meeting?.zoomMeetingUuid) {
            return;
        }

        // 防止同一个弹窗会话中重复触发（如果已经在轮询中，跳过）
        if (isPollingRef.current) {
            console.log('已有轮询在进行中，跳过本次触发');
            return;
        }

        // 记录触发时间（用于日志和调试）
        const now = Date.now();
        lastFetchTimeRef.current = now;

        try {
            setBackgroundFetching(true);
            console.log('触发后台报告获取:', meeting.meetingUuid || meeting.zoomMeetingUuid);

            // 优先使用系统会议UUID
            let response;
            if (meeting.meetingUuid) {
                response = await meetingReportApi.triggerReportFetchByMeetingUuid(meeting.meetingUuid);
            } else if (meeting.zoomMeetingUuid) {
                response = await meetingReportApi.triggerReportFetch(meeting.zoomMeetingUuid);
            }

            if (response?.data?.success) {
                console.log('后台报告获取触发成功，开始轮询检查更新');
                isPollingRef.current = true;

                // 静默触发成功，开始轮询检查更新
                let retryCount = 0;
                const maxRetries = 4; // 减少到4次，总共约40秒
                const pollInterval = 10000; // 增加到10秒间隔

                const pollForUpdatedReport = async () => {
                    try {
                        console.log(`轮询检查更新 (${retryCount + 1}/${maxRetries})`);
                        const result = await fetchReportData(true); // 标记为轮询调用

                        // 检查是否获取到了新的报告数据
                        if (result?.data) {
                            // 检查是否是新的报告（通过ID或更新时间判断）
                            const isNewReport = result.data.id !== lastReportId;
                            const hasMoreRecentData = lastReportId && result.data.id && result.data.id !== lastReportId;

                            if (isNewReport || hasMoreRecentData) {
                                // 有新的报告数据，显示提示
                                console.log('检测到最新会议报告，停止轮询', {
                                    newId: result.data.id,
                                    oldId: lastReportId
                                });
                                message.success('检测到最新会议报告，已自动更新！', 3);
                                isPollingRef.current = false;
                                return;
                            }
                        }

                        retryCount++;
                        if (retryCount < maxRetries) {
                            pollingTimeoutRef.current = setTimeout(pollForUpdatedReport, pollInterval);
                        } else {
                            console.log('轮询结束，未检测到新报告');
                            isPollingRef.current = false;
                        }
                    } catch (error) {
                        console.error('轮询获取更新报告失败:', error);
                        retryCount++;
                        if (retryCount < maxRetries) {
                            pollingTimeoutRef.current = setTimeout(pollForUpdatedReport, pollInterval);
                        } else {
                            isPollingRef.current = false;
                        }
                    }
                };

                // 延迟开始轮询，给服务器一些处理时间
                setTimeout(pollForUpdatedReport, 3000);
            }
        } catch (error) {
            console.error('后台触发报告获取失败:', error);
            // 静默失败，不显示错误消息
        } finally {
            setBackgroundFetching(false);
        }
    }, [meeting, lastReportId, fetchReportData]);

    // 手动触发会议报告获取
    const handleTriggerReportFetch = async () => {
        if (!meeting?.zoomMeetingUuid) {
            message.error('缺少会议UUID，无法获取报告');
            return;
        }

        try {
            setLoading(true);
            const response = await meetingReportApi.triggerReportFetch(meeting.zoomMeetingUuid);

            if (response.data && response.data.success) {
                message.success('会议报告获取已触发，正在获取中...');

                // 轮询检查报告是否获取成功
                let retryCount = 0;
                const maxRetries = 12; // 最多重试12次，总共约1分钟

                const pollForReport = async () => {
                    try {
                        const result = await fetchReportData();
                        // 检查是否获取到了报告数据
                        if (result && result.data) {
                            message.success('会议报告获取成功！');
                            return;
                        }

                        retryCount++;
                        if (retryCount < maxRetries) {
                            setTimeout(pollForReport, 5000); // 每5秒检查一次
                        } else {
                            message.warning('报告获取时间较长，请稍后手动刷新查看');
                        }
                    } catch (error) {
                        console.error('轮询获取报告失败:', error);
                        retryCount++;
                        if (retryCount < maxRetries) {
                            setTimeout(pollForReport, 5000);
                        }
                    }
                };

                // 开始轮询
                setTimeout(pollForReport, 5000);
            } else {
                message.error(response.data?.message || '触发报告获取失败');
            }
        } catch (error) {
            console.error('触发报告获取失败:', error);
            message.error('触发报告获取失败');
        } finally {
            setLoading(false);
        }
    };

    // 当弹窗打开时获取数据
    useEffect(() => {
        if (visible && meeting) {
            console.log('弹窗打开，开始获取会议报告数据');
            // 立即获取并展示已有的报告数据
            fetchReportData().then(() => {
                // 无论是否有现有数据，都触发后台获取最新报告
                // 因为一个会议可能多次开启，产生多个报告
                console.log('触发后台获取最新报告（会议可能有多次开启）');
                triggerBackgroundReportFetch();
            });
        }

        // 清理函数：当弹窗关闭时清理轮询
        return () => {
            if (!visible) {
                console.log('弹窗关闭，清理轮询状态');
                isPollingRef.current = false;
                if (pollingTimeoutRef.current) {
                    clearTimeout(pollingTimeoutRef.current);
                    pollingTimeoutRef.current = null;
                }
            }
        };
    }, [visible, meeting, fetchReportData, triggerBackgroundReportFetch]); // 添加必要的依赖项

    // 参会者表格列配置
    const participantColumns = [
        {
            title: '姓名',
            dataIndex: 'participantName',
            key: 'name',
            ellipsis: true,
            render: (name) => name || '未知用户'
        },
        {
            title: '邮箱',
            dataIndex: 'participantEmail',
            key: 'email',
            ellipsis: true,
            render: (email) => email || '-'
        },
        {
            title: '参会时长',
            dataIndex: 'durationMinutes',
            key: 'duration',
            width: isMobileView ? 80 : 100,
            render: (minutes) => (
                <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <ClockCircleOutlined style={{ fontSize: '12px', color: '#666' }} />
                    {minutes || 0}分钟
                </span>
            )
        },
        {
            title: '用户类型',
            dataIndex: 'userType',
            key: 'userType',
            width: isMobileView ? 70 : 80,
            render: (type) => (
                <Tag 
                    color={type === 'HOST' ? 'blue' : 'default'}
                    icon={<UserOutlined />}
                    style={{ fontSize: isMobileView ? '10px' : '12px' }}
                >
                    {type === 'HOST' ? '主持人' : '参会者'}
                </Tag>
            )
        }
    ];

    return (
        <Modal
            title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <EyeOutlined />
                        <span>会议报告详情</span>
                        {meeting && (
                            <Tag color="blue" style={{ marginLeft: '8px' }}>
                                {meeting.zoomMeetingId}
                            </Tag>
                        )}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        {backgroundFetching && (
                            <div style={{ display: 'flex', alignItems: 'center', gap: '4px', color: '#1890ff', fontSize: '12px' }}>
                                <SyncOutlined spin />
                                <span>正在获取最新报告...</span>
                            </div>
                        )}
                        <Button
                            type="text"
                            size="small"
                            icon={<SyncOutlined />}
                            onClick={() => {
                                console.log('手动刷新会议报告');
                                fetchReportData().then(() => {
                                    triggerBackgroundReportFetch();
                                });
                            }}
                            loading={loading}
                            title="刷新报告数据"
                        />
                    </div>
                </div>
            }
            open={visible}
            onCancel={onCancel}
            footer={null}
            width={isMobileView ? '95%' : 800}
            style={{ top: isMobileView ? 20 : 50 }}
            destroyOnClose={true}
        >
            {loading ? (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <Spin size="large" />
                    <div style={{ marginTop: '16px', color: '#666' }}>正在获取会议报告...</div>
                </div>
            ) : !reportData ? (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <div style={{ fontSize: '16px', color: '#666', marginBottom: '16px' }}>
                        暂无会议报告数据
                    </div>
                    <div style={{ fontSize: '14px', color: '#999', marginBottom: '24px' }}>
                        会议报告通常在会议结束后5-10分钟内自动生成
                    </div>
                    <div style={{ fontSize: '12px', color: '#999', marginBottom: '24px' }}>
                        如果会议刚结束，请稍等片刻后刷新页面查看
                    </div>
                    <Button
                        type="primary"
                        icon={<SyncOutlined />}
                        onClick={handleTriggerReportFetch}
                        loading={loading}
                    >
                        立即获取报告
                    </Button>
                </div>
            ) : (
                <div>
                    {/* 多报告选择器 */}
                    {allReports && allReports.length > 1 && (
                        <Alert
                            message={
                                <Space>
                                    <span>检测到该会议有 {allReports.length} 次开启记录，请选择要查看的报告：</span>
                                    <Select
                                        value={selectedReportIndex}
                                        onChange={handleReportChange}
                                        style={{ minWidth: 200 }}
                                        size="small"
                                    >
                                        {allReports.map((report, index) => (
                                            <Select.Option key={index} value={index}>
                                                第{index + 1}次 - {dayjs(report.startTime).format('MM-DD HH:mm')}
                                                ({report.durationMinutes}分钟)
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Space>
                            }
                            type="info"
                            showIcon
                            style={{ marginBottom: '16px' }}
                        />
                    )}

                    {/* 会议基本信息 */}
                    <Card
                        size="small"
                        style={{ marginBottom: '16px' }}
                        title={
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <span>会议基本信息</span>
                                {backgroundFetching && (
                                    <Badge
                                        status="processing"
                                        text="正在获取最新报告..."
                                        style={{ fontSize: '12px', color: '#666' }}
                                    />
                                )}
                            </div>
                        }
                    >
                        <Row gutter={[16, 8]}>
                            <Col xs={24} sm={12}>
                                <Text strong>会议主题：</Text>
                                <Text>{reportData.topic || '无主题'}</Text>
                            </Col>
                            <Col xs={24} sm={12}>
                                <Text strong>会议时长：</Text>
                                <Text>
                                    <ClockCircleOutlined style={{ marginRight: '4px', color: '#666' }} />
                                    {reportData.durationMinutes || 0} 分钟
                                </Text>
                            </Col>
                            <Col xs={24} sm={12}>
                                <Text strong>开始时间：</Text>
                                <Text>
                                    {reportData.startTime ? 
                                        dayjs(reportData.startTime).format('YYYY-MM-DD HH:mm:ss') : 
                                        '-'
                                    }
                                </Text>
                            </Col>
                            <Col xs={24} sm={12}>
                                <Text strong>结束时间：</Text>
                                <Text>
                                    {reportData.endTime ? 
                                        dayjs(reportData.endTime).format('YYYY-MM-DD HH:mm:ss') : 
                                        '-'
                                    }
                                </Text>
                            </Col>
                            <Col xs={24} sm={12}>
                                <Text strong>参会人数：</Text>
                                <Text>
                                    <UserOutlined style={{ marginRight: '4px', color: '#666' }} />
                                    {reportData.totalParticipants || 0} 人
                                </Text>
                            </Col>
                            <Col xs={24} sm={12}>
                                <Text strong>唯一参会人数：</Text>
                                <Text>
                                    <UserOutlined style={{ marginRight: '4px', color: '#666' }} />
                                    {reportData.uniqueParticipants || 0} 人
                                </Text>
                            </Col>
                        </Row>
                    </Card>

                    {/* 会议功能使用情况 */}
                    <Card size="small" title="会议功能使用情况" style={{ marginBottom: '16px' }}>
                        <Row gutter={[16, 8]}>
                            <Col xs={8} sm={8}>
                                <div style={{ textAlign: 'center' }}>
                                    <Badge 
                                        status={reportData.hasRecording ? 'success' : 'default'} 
                                        text={
                                            <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                                                {reportData.hasRecording ? '有录制' : '无录制'}
                                            </span>
                                        }
                                    />
                                </div>
                            </Col>
                            <Col xs={8} sm={8}>
                                <div style={{ textAlign: 'center' }}>
                                    <Badge 
                                        status={reportData.hasVideo ? 'success' : 'default'} 
                                        text={
                                            <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                                                <VideoCameraOutlined style={{ marginRight: '4px' }} />
                                                {reportData.hasVideo ? '有视频' : '无视频'}
                                            </span>
                                        }
                                    />
                                </div>
                            </Col>
                            <Col xs={8} sm={8}>
                                <div style={{ textAlign: 'center' }}>
                                    <Badge 
                                        status={reportData.hasScreenShare ? 'success' : 'default'} 
                                        text={
                                            <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                                                <DesktopOutlined style={{ marginRight: '4px' }} />
                                                {reportData.hasScreenShare ? '有屏幕共享' : '无屏幕共享'}
                                            </span>
                                        }
                                    />
                                </div>
                            </Col>
                        </Row>
                    </Card>

                    {/* 参会者列表 */}
                    {participants.length > 0 && (
                        <Card size="small" title={`参会者列表 (${participants.length}人)`}>
                            <Table
                                size="small"
                                dataSource={participants}
                                rowKey="id"
                                pagination={{ 
                                    pageSize: isMobileView ? 3 : 5, 
                                    size: 'small',
                                    showSizeChanger: false
                                }}
                                columns={participantColumns}
                                scroll={{ x: isMobileView ? 300 : 'auto' }}
                            />
                        </Card>
                    )}
                </div>
            )}
        </Modal>
    );
};

export default MeetingReportModal;
