import React, { useState, useEffect } from 'react';
import {
    Card,
    Row,
    Col,
    Statistic,
    DatePicker,
    Select,
    Button,
    Space,
    Spin,
    message,
    Typography
} from 'antd';
import {
    BarChartOutlined,
    UserOutlined,
    ClockCircleOutlined,
    VideoCameraOutlined,
    CalendarOutlined,
    ReloadOutlined,
    DownloadOutlined
} from '@ant-design/icons';
import { meetingReportApi } from '../services/api';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Title } = Typography;

/**
 * 会议报告统计组件
 */
const MeetingReportStatistics = ({ isMobileView = false }) => {
    const [loading, setLoading] = useState(false);
    const [exportLoading, setExportLoading] = useState(false);
    const [statistics, setStatistics] = useState({});
    const [dateRange, setDateRange] = useState([
        dayjs().subtract(7, 'days'),
        dayjs()
    ]);
    const [hostId, setHostId] = useState('');
    const [pmiRecordId, setPmiRecordId] = useState('');

    // 获取统计数据
    const fetchStatistics = async () => {
        setLoading(true);
        try {
            const params = {};
            
            if (dateRange && dateRange.length === 2) {
                params.startTime = dateRange[0].startOf('day').toISOString();
                params.endTime = dateRange[1].endOf('day').toISOString();
            }
            
            if (hostId) {
                params.hostId = hostId;
            }
            
            if (pmiRecordId) {
                params.pmiRecordId = pmiRecordId;
            }

            const response = await meetingReportApi.getReportsOverview(params);
            
            if (response.data) {
                setStatistics(response.data);
            }
        } catch (error) {
            console.error('获取统计数据失败:', error);
            message.error('获取统计数据失败');
        } finally {
            setLoading(false);
        }
    };

    // 初始加载
    useEffect(() => {
        fetchStatistics();
    }, []);

    // 处理筛选条件变化
    const handleFilterChange = () => {
        fetchStatistics();
    };

    // 重置筛选条件
    const handleReset = () => {
        setDateRange([dayjs().subtract(7, 'days'), dayjs()]);
        setHostId('');
        setPmiRecordId('');
        setTimeout(() => {
            fetchStatistics();
        }, 100);
    };

    // 导出报告
    const handleExport = async () => {
        setExportLoading(true);
        try {
            const params = {};

            if (dateRange && dateRange.length === 2) {
                params.startTime = dateRange[0].startOf('day').toISOString();
                params.endTime = dateRange[1].endOf('day').toISOString();
            }

            if (hostId) {
                params.hostId = hostId;
            }

            if (pmiRecordId) {
                params.pmiRecordId = pmiRecordId;
            }

            const response = await meetingReportApi.exportReports(params);

            // 创建下载链接
            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;

            // 生成文件名
            const dateStr = dateRange && dateRange.length === 2 ?
                `${dateRange[0].format('YYYY-MM-DD')}_${dateRange[1].format('YYYY-MM-DD')}` :
                dayjs().format('YYYY-MM-DD');
            link.download = `会议报告_${dateStr}.xlsx`;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            message.success('报告导出成功');
        } catch (error) {
            console.error('导出报告失败:', error);
            message.error('导出报告失败');
        } finally {
            setExportLoading(false);
        }
    };

    return (
        <div style={{ padding: isMobileView ? '12px' : '24px' }}>
            <div style={{ marginBottom: '24px' }}>
                <Title level={isMobileView ? 4 : 3}>
                    <BarChartOutlined style={{ marginRight: '8px' }} />
                    会议报告统计
                </Title>
            </div>

            {/* 筛选条件 */}
            <Card size="small" style={{ marginBottom: '24px' }}>
                <Row gutter={[16, 16]} align="middle">
                    <Col xs={24} sm={12} md={8}>
                        <Space direction="vertical" size="small" style={{ width: '100%' }}>
                            <span style={{ fontSize: '12px', color: '#666' }}>时间范围</span>
                            <RangePicker
                                value={dateRange}
                                onChange={setDateRange}
                                style={{ width: '100%' }}
                                size={isMobileView ? 'small' : 'middle'}
                                format="YYYY-MM-DD"
                            />
                        </Space>
                    </Col>
                    <Col xs={24} sm={12} md={8}>
                        <Space direction="vertical" size="small" style={{ width: '100%' }}>
                            <span style={{ fontSize: '12px', color: '#666' }}>主持人ID</span>
                            <Select
                                value={hostId}
                                onChange={setHostId}
                                placeholder="选择主持人"
                                allowClear
                                style={{ width: '100%' }}
                                size={isMobileView ? 'small' : 'middle'}
                            >
                                {/* 这里可以动态加载主持人列表 */}
                            </Select>
                        </Space>
                    </Col>
                    <Col xs={24} sm={24} md={8}>
                        <Space style={{ width: '100%', justifyContent: isMobileView ? 'center' : 'flex-start' }}>
                            <Button
                                type="primary"
                                icon={<BarChartOutlined />}
                                onClick={handleFilterChange}
                                loading={loading}
                                size={isMobileView ? 'small' : 'middle'}
                            >
                                查询
                            </Button>
                            <Button
                                icon={<ReloadOutlined />}
                                onClick={handleReset}
                                size={isMobileView ? 'small' : 'middle'}
                            >
                                重置
                            </Button>
                            <Button
                                icon={<DownloadOutlined />}
                                onClick={handleExport}
                                loading={exportLoading}
                                size={isMobileView ? 'small' : 'middle'}
                            >
                                {isMobileView ? '' : '导出'}
                            </Button>
                        </Space>
                    </Col>
                </Row>
            </Card>

            {/* 统计卡片 */}
            <Spin spinning={loading}>
                <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
                    <Col xs={12} sm={6} md={6}>
                        <Card>
                            <Statistic
                                title="总会议数"
                                value={statistics.totalReports || 0}
                                prefix={<CalendarOutlined />}
                                valueStyle={{ color: '#3f8600', fontSize: isMobileView ? '20px' : '24px' }}
                            />
                        </Card>
                    </Col>
                    <Col xs={12} sm={6} md={6}>
                        <Card>
                            <Statistic
                                title="总参会人数"
                                value={statistics.totalParticipants || 0}
                                prefix={<UserOutlined />}
                                valueStyle={{ color: '#1890ff', fontSize: isMobileView ? '20px' : '24px' }}
                            />
                        </Card>
                    </Col>
                    <Col xs={12} sm={6} md={6}>
                        <Card>
                            <Statistic
                                title="总会议时长"
                                value={statistics.totalDuration || 0}
                                suffix="分钟"
                                prefix={<ClockCircleOutlined />}
                                valueStyle={{ color: '#722ed1', fontSize: isMobileView ? '20px' : '24px' }}
                            />
                        </Card>
                    </Col>
                    <Col xs={12} sm={6} md={6}>
                        <Card>
                            <Statistic
                                title="平均会议时长"
                                value={statistics.averageDuration || 0}
                                suffix="分钟"
                                prefix={<ClockCircleOutlined />}
                                valueStyle={{ color: '#fa8c16', fontSize: isMobileView ? '20px' : '24px' }}
                                precision={1}
                            />
                        </Card>
                    </Col>
                </Row>

                {/* 功能使用统计 */}
                <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
                    <Col xs={24} sm={12} md={8}>
                        <Card title="录制使用情况" size="small">
                            <Row>
                                <Col span={12}>
                                    <Statistic
                                        title="有录制"
                                        value={statistics.recordingCount || 0}
                                        valueStyle={{ color: '#52c41a', fontSize: '18px' }}
                                    />
                                </Col>
                                <Col span={12}>
                                    <Statistic
                                        title="录制率"
                                        value={statistics.recordingRate || 0}
                                        suffix="%"
                                        valueStyle={{ color: '#52c41a', fontSize: '18px' }}
                                        precision={1}
                                    />
                                </Col>
                            </Row>
                        </Card>
                    </Col>
                    <Col xs={24} sm={12} md={8}>
                        <Card title="视频使用情况" size="small">
                            <Row>
                                <Col span={12}>
                                    <Statistic
                                        title="有视频"
                                        value={statistics.videoCount || 0}
                                        valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                                    />
                                </Col>
                                <Col span={12}>
                                    <Statistic
                                        title="视频率"
                                        value={statistics.videoRate || 0}
                                        suffix="%"
                                        valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                                        precision={1}
                                    />
                                </Col>
                            </Row>
                        </Card>
                    </Col>
                    <Col xs={24} sm={12} md={8}>
                        <Card title="屏幕共享情况" size="small">
                            <Row>
                                <Col span={12}>
                                    <Statistic
                                        title="有共享"
                                        value={statistics.screenShareCount || 0}
                                        valueStyle={{ color: '#722ed1', fontSize: '18px' }}
                                    />
                                </Col>
                                <Col span={12}>
                                    <Statistic
                                        title="共享率"
                                        value={statistics.screenShareRate || 0}
                                        suffix="%"
                                        valueStyle={{ color: '#722ed1', fontSize: '18px' }}
                                        precision={1}
                                    />
                                </Col>
                            </Row>
                        </Card>
                    </Col>
                </Row>

                {/* 最近会议报告 */}
                {statistics.recentReports && statistics.recentReports.length > 0 && (
                    <Card title="最近会议报告" size="small">
                        <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                            {statistics.recentReports.map((report, index) => (
                                <div
                                    key={report.id || index}
                                    style={{
                                        padding: '12px',
                                        borderBottom: index < statistics.recentReports.length - 1 ? '1px solid #f0f0f0' : 'none',
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center'
                                    }}
                                >
                                    <div style={{ flex: 1 }}>
                                        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                                            {report.topic || '无主题'}
                                        </div>
                                        <div style={{ fontSize: '12px', color: '#666' }}>
                                            {report.startTime ? dayjs(report.startTime).format('MM-DD HH:mm') : '-'}
                                            {' · '}
                                            {report.durationMinutes || 0}分钟
                                            {' · '}
                                            {report.totalParticipants || 0}人
                                        </div>
                                    </div>
                                    <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                                        {report.hasRecording && (
                                            <VideoCameraOutlined style={{ color: '#52c41a' }} title="有录制" />
                                        )}
                                        {report.hasVideo && (
                                            <VideoCameraOutlined style={{ color: '#1890ff' }} title="有视频" />
                                        )}
                                        {report.hasScreenShare && (
                                            <BarChartOutlined style={{ color: '#722ed1' }} title="有屏幕共享" />
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </Card>
                )}
            </Spin>
        </div>
    );
};

export default MeetingReportStatistics;
