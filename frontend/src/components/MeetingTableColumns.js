import React from 'react';
import { Tag, Button, Tooltip } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { copyMeetingInfo } from '../utils/copyMeetingInfo';

/**
 * 生成会议表格列配置
 * @param {boolean} isMobileView - 是否为移动端视图
 * @param {function} isTodayMeeting - 判断是否为当天会议的函数
 * @returns {Array} 表格列配置
 */
export const createMeetingColumns = (isMobileView, isTodayMeeting) => [
  {
    title: isMobileView ? '主题' : '会议主题',
    dataIndex: 'topic',
    key: 'topic',
    width: isMobileView ? 120 : 200,
    ellipsis: true,
    render: (text, record) => {
      return (
        <span style={{ 
          fontSize: isMobileView ? '11px' : '14px'
        }}>
          {text || '未命名会议'}
        </span>
      );
    },
  },
  {
    title: '主持人',
    dataIndex: 'hostEmail',
    key: 'host',
    width: isMobileView ? 80 : 120,
    ellipsis: true,
    render: (hostEmail, record) => {
      return (
        <span style={{ 
          fontSize: isMobileView ? '11px' : '14px'
        }}>
          {hostEmail || record.hostId || '-'}
        </span>
      );
    },
  },
  {
    title: isMobileView ? '时间' : '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: isMobileView ? 100 : 150,
    render: (text, record) => {
      const displayTime = text ? 
        dayjs(text).format(isMobileView ? 'MM-DD HH:mm' : 'YYYY-MM-DD HH:mm') : 
        '待定';
      
      return (
        <span style={{ 
          fontSize: isMobileView ? '10px' : '14px'
        }}>
          {displayTime}
        </span>
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: isMobileView ? 60 : 100,
    render: (status, record) => {
      const statusConfig = {
        waiting: { color: 'blue', text: isMobileView ? '等待' : '等待中' },
        started: { color: 'green', text: isMobileView ? '进行' : '进行中' },
        ended: { color: 'default', text: isMobileView ? '结束' : '已结束' },
        cancelled: { color: 'red', text: isMobileView ? '取消' : '已取消' },
      };
      const config = statusConfig[status] || { color: 'default', text: status || '未知' };
      return (
        <Tag
          color={config.color}
          style={{ 
            fontSize: isMobileView ? '9px' : '12px'
          }}
        >
          {config.text}
        </Tag>
      );
    },
  },
  {
    title: isMobileView ? 'ID' : '会议ID',
    dataIndex: 'zoomMeetingId',
    key: 'zoomMeetingId',
    width: isMobileView ? 80 : 120,
    ellipsis: true,
    render: (zoomMeetingId, record) => {
      return (
        <span style={{ 
          fontSize: isMobileView ? '10px' : '12px',
          fontFamily: 'monospace',
          color: '#666'
        }}>
          {zoomMeetingId || '-'}
        </span>
      );
    },
  },
  {
    title: '操作',
    key: 'action',
    width: isMobileView ? 60 : 80,
    render: (text, record) => {
      return (
        <Tooltip title="复制参会信息">
          <Button
            type="text"
            size={isMobileView ? 'small' : 'middle'}
            icon={<CopyOutlined />}
            onClick={() => copyMeetingInfo(record)}
            style={{
              color: '#1890ff',
              padding: isMobileView ? '2px 4px' : '4px 8px'
            }}
          />
        </Tooltip>
      );
    },
  },
];
