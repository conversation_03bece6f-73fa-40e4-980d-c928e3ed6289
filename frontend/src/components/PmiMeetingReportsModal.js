import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Button,
  message,
  Space,
  Tag,
  Tooltip,
  Empty,
  Alert,
  Card,
  Statistic,
  Row,
  Col,
  DatePicker,
  Select
} from 'antd';
import {
  EyeOutlined,
  SyncOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FileTextOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { meetingReportApi } from '../services/api';
import MeetingReportModal from './MeetingReportModal';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

const PmiMeetingReportsModal = ({ 
  visible, 
  onCancel, 
  pmiRecord,
  isMobileView = false 
}) => {
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [selectedMeeting, setSelectedMeeting] = useState(null);
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [statistics, setStatistics] = useState(null);
  const [filters, setFilters] = useState({
    dateRange: null,
    fetchStatus: null
  });
  const [fetchingReports, setFetchingReports] = useState(false);

  // 获取PMI会议报告列表
  const fetchReports = async (page = 1, size = 10) => {
    if (!pmiRecord?.id) return;
    
    setLoading(true);
    try {
      const params = {
        page: page - 1,
        size,
        pmiRecordId: pmiRecord.id
      };

      // 添加过滤条件
      if (filters.dateRange && filters.dateRange.length === 2) {
        params.startTime = filters.dateRange[0].startOf('day').toISOString();
        params.endTime = filters.dateRange[1].endOf('day').toISOString();
      }

      if (filters.fetchStatus) {
        params.fetchStatus = filters.fetchStatus;
      }

      const response = await meetingReportApi.getReportsByPmiRecordId(pmiRecord.id, params);
      
      if (response.data) {
        setReports(response.data.content || []);
        setPagination({
          current: page,
          pageSize: size,
          total: response.data.totalElements || 0
        });
      }
    } catch (error) {
      console.error('获取PMI会议报告失败:', error);
      message.error('获取会议报告失败');
    } finally {
      setLoading(false);
    }
  };

  // 计算统计信息
  const calculateStatistics = (reportsList) => {
    if (!reportsList || reportsList.length === 0) return null;

    const totalMeetings = reportsList.length;
    const totalDuration = reportsList.reduce((sum, report) => sum + (report.durationMinutes || 0), 0);
    const totalParticipants = reportsList.reduce((sum, report) => sum + (report.participantCount || 0), 0);
    const avgDuration = totalMeetings > 0 ? Math.round(totalDuration / totalMeetings) : 0;
    const avgParticipants = totalMeetings > 0 ? Math.round(totalParticipants / totalMeetings) : 0;

    return {
      totalMeetings,
      totalDuration,
      totalParticipants,
      avgDuration,
      avgParticipants
    };
  };

  // 查看会议报告详情
  const handleViewReport = (record) => {
    setSelectedMeeting({
      zoomMeetingUuid: record.zoomMeetingUuid,
      zoomMeetingId: record.zoomMeetingId,
      meetingUuid: record.meetingUuid,
      topic: record.topic || `PMI ${pmiRecord.pmiNumber} 会议`
    });
    setReportModalVisible(true);
  };

  // 重新获取会议报告
  const handleRefetchReport = async (record) => {
    try {
      message.loading('正在重新获取会议报告...', 0);
      
      let response;
      if (record.meetingUuid) {
        response = await meetingReportApi.triggerReportFetchByMeetingUuid(record.meetingUuid);
      } else if (record.zoomMeetingUuid) {
        response = await meetingReportApi.triggerReportFetch(record.zoomMeetingUuid);
      }

      message.destroy();
      
      if (response?.data?.success) {
        message.success('已触发报告重新获取，请稍后刷新查看');
        // 延迟刷新列表
        setTimeout(() => {
          fetchReports(pagination.current, pagination.pageSize);
        }, 3000);
      } else {
        message.error('触发报告获取失败');
      }
    } catch (error) {
      message.destroy();
      console.error('重新获取报告失败:', error);
      message.error('重新获取报告失败');
    }
  };

  // 获取状态标签
  const getStatusTag = (fetchStatus) => {
    const statusConfig = {
      'SUCCESS': { color: 'green', text: '成功' },
      'PENDING': { color: 'blue', text: '待处理' },
      'FAILED': { color: 'red', text: '失败' },
      'PROCESSING': { color: 'orange', text: '处理中' },
    };
    
    const config = statusConfig[fetchStatus] || { color: 'default', text: fetchStatus };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '会议主题',
      dataIndex: 'topic',
      key: 'topic',
      ellipsis: true,
      render: (text) => text || '未命名会议'
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: isMobileView ? 120 : 150,
      render: (text) => text ? dayjs(text).format('MM-DD HH:mm') : '-',
      sorter: (a, b) => dayjs(a.startTime).unix() - dayjs(b.startTime).unix(),
    },
    {
      title: '时长',
      dataIndex: 'durationMinutes',
      key: 'durationMinutes',
      width: 80,
      render: (duration) => {
        if (!duration) return '-';
        const hours = Math.floor(duration / 60);
        const minutes = duration % 60;
        return hours > 0 ? `${hours}h${minutes}m` : `${minutes}m`;
      },
      sorter: (a, b) => (a.durationMinutes || 0) - (b.durationMinutes || 0),
    },
    {
      title: '参与人数',
      dataIndex: 'participantCount',
      key: 'participantCount',
      width: 100,
      render: (count) => count || 0,
      sorter: (a, b) => (a.participantCount || 0) - (b.participantCount || 0),
    },
    {
      title: '状态',
      dataIndex: 'fetchStatus',
      key: 'fetchStatus',
      width: 80,
      render: getStatusTag,
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 100 : 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewReport(record)}
            />
          </Tooltip>
          {record.fetchStatus !== 'SUCCESS' && (
            <Tooltip title="重新获取">
              <Button
                type="link"
                size="small"
                icon={<SyncOutlined />}
                onClick={() => handleRefetchReport(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  // 处理过滤条件变化
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 立即获取报告
  const handleFetchReports = async () => {
    if (!pmiRecord?.id) return;

    setFetchingReports(true);
    try {
      const response = await meetingReportApi.triggerReportFetchByPmiRecordId(pmiRecord.id);

      if (response.data?.success) {
        message.success(`会议报告获取完成，共获取 ${response.data.count || 0} 条报告`);
        // 重新加载报告列表
        fetchReports(pagination.current, pagination.pageSize);
      } else {
        message.error(response.data?.message || '会议报告获取失败');
      }
    } catch (error) {
      console.error('立即获取报告失败:', error);
      message.error('会议报告获取失败，请稍后重试');
    } finally {
      setFetchingReports(false);
    }
  };

  // 应用过滤条件
  const applyFilters = () => {
    fetchReports(1, pagination.pageSize);
  };

  // 重置过滤条件
  const resetFilters = () => {
    setFilters({
      dateRange: null,
      fetchStatus: null
    });
    setTimeout(() => {
      fetchReports(1, pagination.pageSize);
    }, 100);
  };

  // 初始化数据
  useEffect(() => {
    if (visible && pmiRecord?.id) {
      fetchReports();
    }
  }, [visible, pmiRecord?.id]);

  // 计算统计信息
  useEffect(() => {
    if (reports.length > 0) {
      const stats = calculateStatistics(reports);
      setStatistics(stats);
    } else {
      setStatistics(null);
    }
  }, [reports]);

  // 监听过滤条件变化
  useEffect(() => {
    if (visible && pmiRecord?.id) {
      applyFilters();
    }
  }, [filters]);

  return (
    <>
      <Modal
        title={`PMI ${pmiRecord?.pmiNumber || ''} 会议报告`}
        open={visible}
        onCancel={onCancel}
        footer={null}
        width={isMobileView ? '95%' : 1200}
        style={{ top: isMobileView ? 20 : 50 }}
      >
        {/* 过滤条件 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col xs={24} sm={8}>
              <RangePicker
                value={filters.dateRange}
                onChange={(dates) => handleFilterChange('dateRange', dates)}
                placeholder={['开始日期', '结束日期']}
                style={{ width: '100%' }}
                size="small"
              />
            </Col>
            <Col xs={24} sm={6}>
              <Select
                value={filters.fetchStatus}
                onChange={(value) => handleFilterChange('fetchStatus', value)}
                placeholder="状态筛选"
                style={{ width: '100%' }}
                size="small"
                allowClear
              >
                <Option value="SUCCESS">成功</Option>
                <Option value="PENDING">待处理</Option>
                <Option value="FAILED">失败</Option>
                <Option value="PROCESSING">处理中</Option>
              </Select>
            </Col>
            <Col xs={24} sm={10}>
              <Space>
                <Button size="small" onClick={resetFilters}>重置</Button>
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={() => fetchReports(pagination.current, pagination.pageSize)}
                >
                  刷新
                </Button>
                <Button
                  size="small"
                  type="primary"
                  icon={<SyncOutlined />}
                  loading={fetchingReports}
                  onClick={handleFetchReports}
                >
                  获取报告
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 统计信息 */}
        {statistics && (
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col xs={12} sm={6}>
                <Statistic
                  title="总会议数"
                  value={statistics.totalMeetings}
                  prefix={<CalendarOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="总时长(分钟)"
                  value={statistics.totalDuration}
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="平均时长(分钟)"
                  value={statistics.avgDuration}
                  prefix={<FileTextOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="平均参与人数"
                  value={statistics.avgParticipants}
                  prefix={<UserOutlined />}
                />
              </Col>
            </Row>
          </Card>
        )}

        {/* 会议报告列表 */}
        {reports.length > 0 ? (
          <Table
            columns={columns}
            dataSource={reports}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              onChange: fetchReports,
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total) => `共 ${total} 条记录`,
              size: isMobileView ? 'small' : 'default'
            }}
            scroll={{ x: isMobileView ? 600 : 'auto' }}
            size={isMobileView ? 'small' : 'middle'}
          />
        ) : (
          !loading && (
            <Empty
              description="暂无会议报告数据"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Alert
                message="会议报告说明"
                description={
                  <div>
                    <p>• 会议报告通常在会议结束后5-10分钟内自动生成</p>
                    <p>• 如果会议刚结束，请稍等片刻后刷新页面查看</p>
                    <p>• 您也可以点击下方按钮手动获取报告</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginTop: 16, marginBottom: 16 }}
              />
              <Button
                type="primary"
                icon={<SyncOutlined />}
                loading={fetchingReports}
                onClick={handleFetchReports}
                size="large"
              >
                立即获取报告
              </Button>
            </Empty>
          )
        )}
      </Modal>

      {/* 会议报告详情弹窗 */}
      <MeetingReportModal
        visible={reportModalVisible}
        onCancel={() => {
          setReportModalVisible(false);
          setSelectedMeeting(null);
        }}
        meeting={selectedMeeting}
        isMobileView={isMobileView}
      />
    </>
  );
};

export default PmiMeetingReportsModal;
