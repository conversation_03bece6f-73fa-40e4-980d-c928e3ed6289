import React, { useState, useEffect } from 'react';
import { 
    Modal, 
    Form, 
    InputNumber, 
    Input, 
    Button, 
    message, 
    Descriptions, 
    Alert,
    Divider,
    Space,
    Tag
} from 'antd';
import { DollarOutlined, InfoCircleOutlined } from '@ant-design/icons';
import api from '../services/api';

const PmiRechargeModal = ({ visible, onCancel, onSuccess, pmiRecordId }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [previewLoading, setPreviewLoading] = useState(false);
    const [rechargePreview, setRechargePreview] = useState(null);
    const [rechargeMinutes, setRechargeMinutes] = useState(0);
    const [rechargeHours, setRechargeHours] = useState(0);
    const [selectedQuickOption, setSelectedQuickOption] = useState(null);

    // 时长格式化函数：将分钟数转换为 *小时*分钟 格式
    const formatDuration = (minutes) => {
        if (!minutes || minutes === 0) return '0分钟';

        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;

        if (hours === 0) {
            return `${remainingMinutes}分钟`;
        } else if (remainingMinutes === 0) {
            return `${hours}小时`;
        } else {
            return `${hours}小时${remainingMinutes}分钟`;
        }
    };

    // 自动生成充值说明
    const generateRechargeDescription = (hours) => {
        if (!hours || hours <= 0) return '';

        const currentDate = new Date().toLocaleDateString('zh-CN');
        const currentTime = new Date().toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });

        let purpose = '';
        if (hours <= 2) {
            purpose = '短期会议使用';
        } else if (hours <= 8) {
            purpose = '日常会议使用';
        } else if (hours <= 24) {
            purpose = '长时间会议使用';
        } else {
            purpose = '大量会议时长储备';
        }

        return `${currentDate} ${currentTime} 充值${formatDuration(hours * 60)}，用于${purpose}`;
    };

    // 获取充值预览
    const fetchRechargePreview = async (minutes) => {
        if (!minutes || minutes <= 0) {
            setRechargePreview(null);
            return;
        }

        setPreviewLoading(true);
        try {
            const response = await api.get(`/pmi/${pmiRecordId}/recharge/preview`, {
                params: { minutes }
            });
            if (response.data.success) {
                setRechargePreview(response.data.data);
            } else {
                message.error(response.data.message);
                setRechargePreview(null);
            }
        } catch (error) {
            console.error('获取充值预览失败:', error);
            setRechargePreview(null);
        } finally {
            setPreviewLoading(false);
        }
    };

    // 执行充值
    const handleRecharge = async (values) => {
        setLoading(true);
        try {
            // 将小时转换为分钟
            const minutes = Math.round(values.hours * 60);
            const response = await api.post(`/pmi/${pmiRecordId}/recharge`, null, {
                params: {
                    minutes: minutes,
                    description: values.description
                }
            });

            if (response.data.success) {
                message.success('充值成功');
                form.resetFields();
                setRechargePreview(null);
                setRechargeMinutes(0);
                setRechargeHours(0);
                setSelectedQuickOption(null);
                onSuccess && onSuccess(response.data);
                onCancel();
            } else {
                message.error(response.data.message);
            }
        } catch (error) {
            message.error('充值失败');
            console.error('充值失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 监听充值分钟数变化
    useEffect(() => {
        const timer = setTimeout(() => {
            fetchRechargePreview(rechargeMinutes);
        }, 300); // 减少防抖时间，提高响应速度

        return () => clearTimeout(timer);
    }, [rechargeMinutes, pmiRecordId]);

    // 重置状态
    useEffect(() => {
        if (!visible) {
            form.resetFields();
            setRechargePreview(null);
            setRechargeMinutes(0);
            setRechargeHours(0);
            setSelectedQuickOption(null);
        }
    }, [visible, form]);

    return (
        <Modal
            title={
                <Space>
                    <DollarOutlined />
                    PMI充值
                </Space>
            }
            open={visible}
            onCancel={onCancel}
            footer={null}
            width={600}
            destroyOnClose
        >
            <Form
                form={form}
                layout="vertical"
                onFinish={handleRecharge}
                initialValues={{
                    hours: 0,
                    description: ''
                }}
            >
                <Form.Item
                    label="充值时长（小时）"
                    name="hours"
                    rules={[
                        { required: true, message: '请输入充值时长' },
                        { type: 'number', min: 0.1, message: '充值时长必须大于0' }
                    ]}
                >
                    {/* 快捷选项按钮 */}
                    <div style={{ marginBottom: 12 }}>
                        <Space wrap>
                            <Button
                                size="small"
                                type={selectedQuickOption === 1 ? 'primary' : 'default'}
                                onClick={() => {
                                    const hours = 1;
                                    const minutes = hours * 60;
                                    const description = generateRechargeDescription(hours);
                                    form.setFieldsValue({ hours, description });
                                    setRechargeHours(hours);
                                    setRechargeMinutes(minutes);
                                    setSelectedQuickOption(1);
                                    // 移除直接调用，依赖useEffect防抖
                                }}
                            >
                                1小时
                            </Button>
                            <Button
                                size="small"
                                type={selectedQuickOption === 5 ? 'primary' : 'default'}
                                onClick={() => {
                                    const hours = 5;
                                    const minutes = hours * 60;
                                    const description = generateRechargeDescription(hours);
                                    form.setFieldsValue({ hours, description });
                                    setRechargeHours(hours);
                                    setRechargeMinutes(minutes);
                                    setSelectedQuickOption(5);
                                    // 移除直接调用，依赖useEffect防抖
                                }}
                            >
                                5小时
                            </Button>
                            <Button
                                size="small"
                                type={selectedQuickOption === 10 ? 'primary' : 'default'}
                                onClick={() => {
                                    const hours = 10;
                                    const minutes = hours * 60;
                                    const description = generateRechargeDescription(hours);
                                    form.setFieldsValue({ hours, description });
                                    setRechargeHours(hours);
                                    setRechargeMinutes(minutes);
                                    setSelectedQuickOption(10);
                                    // 移除直接调用，依赖useEffect防抖
                                }}
                            >
                                10小时
                            </Button>
                            <Button
                                size="small"
                                type={selectedQuickOption === 12 ? 'primary' : 'default'}
                                onClick={() => {
                                    const hours = 12;
                                    const minutes = hours * 60;
                                    const description = generateRechargeDescription(hours);
                                    form.setFieldsValue({ hours, description });
                                    setRechargeHours(hours);
                                    setRechargeMinutes(minutes);
                                    setSelectedQuickOption(12);
                                    // 移除直接调用，依赖useEffect防抖
                                }}
                            >
                                12小时
                            </Button>
                            <Button
                                size="small"
                                type={selectedQuickOption === 100 ? 'primary' : 'default'}
                                onClick={() => {
                                    const hours = 100;
                                    const minutes = hours * 60;
                                    const description = generateRechargeDescription(hours);
                                    form.setFieldsValue({ hours, description });
                                    setRechargeHours(hours);
                                    setRechargeMinutes(minutes);
                                    setSelectedQuickOption(100);
                                    // 移除直接调用，依赖useEffect防抖
                                }}
                            >
                                100小时
                            </Button>
                        </Space>
                    </div>

                    <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入充值时长"
                        min={0.1}
                        max={9999}
                        step={0.5}
                        precision={1}
                        value={rechargeHours || undefined}
                        onChange={(value) => {
                            const hours = value || 0;
                            const minutes = Math.round(hours * 60);
                            const description = generateRechargeDescription(hours);
                            setRechargeHours(hours);
                            setRechargeMinutes(minutes);
                            form.setFieldsValue({ hours, description });
                            // 清除快捷选项选中状态（手动输入时）
                            if (![1, 5, 10, 12, 100].includes(hours)) {
                                setSelectedQuickOption(null);
                            } else {
                                setSelectedQuickOption(hours);
                            }
                            // 移除直接调用，依赖useEffect防抖
                        }}
                        addonAfter="小时"
                    />
                </Form.Item>

                <Form.Item
                    label="充值说明"
                    name="description"
                    extra="系统已自动生成充值说明，您可以根据需要进行修改"
                >
                    <Input.TextArea
                        placeholder="系统将自动生成充值说明"
                        rows={3}
                        maxLength={200}
                        showCount
                    />
                </Form.Item>

                {/* 充值预览 */}
                {previewLoading && (
                    <Alert
                        message="正在计算充值分配..."
                        type="info"
                        showIcon
                        style={{ marginBottom: 16 }}
                    />
                )}

                {rechargePreview && !previewLoading && (
                    <div style={{ marginBottom: 16 }}>
                        <Divider orientation="left">充值预览</Divider>
                        
                        <Alert
                            message={
                                <div>
                                    <div style={{ marginBottom: 8 }}>
                                        <strong>充值分配策略：</strong>
                                    </div>
                                    <Space direction="vertical" size="small">
                                        {rechargePreview.allocation.overdraftSettled > 0 && (
                                            <div>
                                                <Tag color="red">超额结清</Tag>
                                                {formatDuration(rechargePreview.allocation.overdraftSettled)}
                                            </div>
                                        )}
                                        {rechargePreview.allocation.pendingDeductSettled > 0 && (
                                            <div>
                                                <Tag color="orange">待扣结清</Tag>
                                                {formatDuration(rechargePreview.allocation.pendingDeductSettled)}
                                            </div>
                                        )}
                                        {rechargePreview.allocation.actualAdded > 0 && (
                                            <div>
                                                <Tag color="green">实际增加</Tag>
                                                {formatDuration(rechargePreview.allocation.actualAdded)}
                                            </div>
                                        )}
                                    </Space>
                                </div>
                            }
                            type="info"
                            showIcon
                            icon={<InfoCircleOutlined />}
                        />

                        <Descriptions 
                            size="small" 
                            column={2} 
                            style={{ marginTop: 16 }}
                            bordered
                        >
                            <Descriptions.Item label="充值前可用时长">
                                {formatDuration(rechargePreview.allocation.balanceBefore)}
                            </Descriptions.Item>
                            <Descriptions.Item label="充值后可用时长">
                                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                                    {formatDuration(rechargePreview.allocation.balanceAfter)}
                                </span>
                            </Descriptions.Item>
                            <Descriptions.Item label="充值前总时长">
                                {formatDuration(rechargePreview.allocation.totalBefore)}
                            </Descriptions.Item>
                            <Descriptions.Item label="充值后总时长">
                                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                                    {formatDuration(rechargePreview.allocation.totalAfter)}
                                </span>
                            </Descriptions.Item>
                            <Descriptions.Item label="充值前超额时长">
                                <span style={{ color: rechargePreview.allocation.overdraftBefore > 0 ? '#ff4d4f' : 'inherit' }}>
                                    {formatDuration(rechargePreview.allocation.overdraftBefore)}
                                </span>
                            </Descriptions.Item>
                            <Descriptions.Item label="充值后超额时长">
                                <span style={{ color: rechargePreview.allocation.overdraftAfter > 0 ? '#ff4d4f' : '#52c41a' }}>
                                    {formatDuration(rechargePreview.allocation.overdraftAfter)}
                                </span>
                            </Descriptions.Item>
                            <Descriptions.Item label="充值后可开启会议" span={2}>
                                {rechargePreview.canStartMeeting ? (
                                    <Tag color="green">是</Tag>
                                ) : (
                                    <Tag color="red">否</Tag>
                                )}
                            </Descriptions.Item>
                        </Descriptions>
                    </div>
                )}

                <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                    <Space>
                        <Button onClick={onCancel}>
                            取消
                        </Button>
                        <Button 
                            type="primary" 
                            htmlType="submit" 
                            loading={loading}
                            disabled={!rechargePreview || previewLoading}
                        >
                            确认充值
                        </Button>
                    </Space>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default PmiRechargeModal;
