import React from 'react';
import { Card, Select, Empty } from 'antd';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar } from 'recharts';
import moment from 'moment';

const { Option } = Select;

const PmiTrendsChart = ({ 
  data = [], 
  loading = false, 
  days = 30, 
  onDaysChange,
  chartType = 'line' // 'line' or 'bar'
}) => {
  const formatData = (rawData) => {
    return rawData.map(item => ({
      ...item,
      date: moment(item.date).format('MM-DD'),
      meetingCount: item.meetingCount || 0,
      totalDuration: item.totalDuration || 0,
      avgDuration: item.avgDuration || 0,
      participantCount: item.participantCount || 0,
    }));
  };

  const formattedData = formatData(data);

  const renderChart = () => {
    if (!formattedData || formattedData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    const commonProps = {
      data: formattedData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    };

    if (chartType === 'bar') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip 
              formatter={(value, name) => [
                value,
                name === 'meetingCount' ? '会议数量' :
                name === 'totalDuration' ? '总时长(分钟)' :
                name === 'avgDuration' ? '平均时长(分钟)' :
                name === 'participantCount' ? '参与人数' : name
              ]}
              labelFormatter={(label) => `日期: ${label}`}
            />
            <Legend />
            <Bar yAxisId="left" dataKey="meetingCount" fill="#1890ff" name="会议数量" />
            <Bar yAxisId="right" dataKey="totalDuration" fill="#52c41a" name="总时长(分钟)" />
          </BarChart>
        </ResponsiveContainer>
      );
    }

    return (
      <ResponsiveContainer width="100%" height={300}>
        <LineChart {...commonProps}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis yAxisId="left" />
          <YAxis yAxisId="right" orientation="right" />
          <Tooltip 
            formatter={(value, name) => [
              value,
              name === 'meetingCount' ? '会议数量' :
              name === 'totalDuration' ? '总时长(分钟)' :
              name === 'avgDuration' ? '平均时长(分钟)' :
              name === 'participantCount' ? '参与人数' : name
            ]}
            labelFormatter={(label) => `日期: ${label}`}
          />
          <Legend />
          <Line 
            yAxisId="left" 
            type="monotone" 
            dataKey="meetingCount" 
            stroke="#1890ff" 
            name="会议数量"
            strokeWidth={2}
            dot={{ r: 4 }}
          />
          <Line 
            yAxisId="right" 
            type="monotone" 
            dataKey="totalDuration" 
            stroke="#52c41a" 
            name="总时长(分钟)"
            strokeWidth={2}
            dot={{ r: 4 }}
          />
          <Line 
            yAxisId="left" 
            type="monotone" 
            dataKey="participantCount" 
            stroke="#faad14" 
            name="参与人数"
            strokeWidth={2}
            dot={{ r: 4 }}
          />
        </LineChart>
      </ResponsiveContainer>
    );
  };

  return (
    <Card 
      title="PMI使用趋势" 
      loading={loading}
      extra={
        <div style={{ display: 'flex', gap: '8px' }}>
          <Select value={days} onChange={onDaysChange} style={{ width: 120 }}>
            <Option value={7}>最近7天</Option>
            <Option value={30}>最近30天</Option>
            <Option value={90}>最近90天</Option>
          </Select>
          <Select 
            value={chartType} 
            onChange={(value) => {
              // 这里可以通过props传递chartType变化的回调
            }} 
            style={{ width: 100 }}
          >
            <Option value="line">折线图</Option>
            <Option value="bar">柱状图</Option>
          </Select>
        </div>
      }
    >
      {renderChart()}
    </Card>
  );
};

export default PmiTrendsChart;
