.trace-id-display {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  z-index: 9999;
  transition: opacity 0.3s ease;
  opacity: 0.3;
  cursor: pointer;
  user-select: none;
  max-width: 300px;
  word-break: break-all;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.trace-id-display:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.05);
}

.trace-id-display.visible {
  opacity: 1;
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0.3; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0.3; }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .trace-id-display {
    bottom: 10px;
    right: 10px;
    font-size: 10px;
    padding: 6px 8px;
    max-width: 200px;
  }
}
