import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import './TraceIdDisplay.css';

/**
 * TraceId显示组件
 * 在页面右下角显示当前请求的TraceId，支持点击复制
 */
const TraceIdDisplay = () => {
  const [currentTraceId, setCurrentTraceId] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateTraceId = (event) => {
      setCurrentTraceId(event.detail.traceId);
      setIsVisible(true);
      
      // 3秒后自动隐藏
      setTimeout(() => setIsVisible(false), 3000);
    };

    // 监听traceId更新事件
    window.addEventListener('traceIdUpdated', updateTraceId);
    
    // 初始化时获取当前traceId
    const initialTraceId = sessionStorage.getItem('currentTraceId');
    if (initialTraceId) {
      setCurrentTraceId(initialTraceId);
    }

    return () => window.removeEventListener('traceIdUpdated', updateTraceId);
  }, []);

  const copyToClipboard = () => {
    if (currentTraceId) {
      navigator.clipboard.writeText(currentTraceId).then(() => {
        message.success('TraceId已复制到剪贴板');
      }).catch(() => {
        // 降级方案：使用传统的复制方法
        const textArea = document.createElement('textarea');
        textArea.value = currentTraceId;
        document.body.appendChild(textArea);
        textArea.select();
        try {
          document.execCommand('copy');
          message.success('TraceId已复制到剪贴板');
        } catch (err) {
          message.error('复制失败，请手动复制');
        }
        document.body.removeChild(textArea);
      });
    }
  };

  if (!currentTraceId) return null;

  return (
    <div 
      className={`trace-id-display ${isVisible ? 'visible' : ''}`}
      onClick={copyToClipboard}
      title="点击复制TraceId"
    >
      🔍 {currentTraceId}
    </div>
  );
};

export default TraceIdDisplay;
