import React, { useState } from 'react';
import { 
  Drawer, 
  Form, 
  Select, 
  DatePicker, 
  Button, 
  Space, 
  Divider,
  Typography,
  Radio,
  Switch,
  Slider
} from 'antd';
import {
  FilterOutlined,
  ClearOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { TASK_STATUS, TASK_TYPE } from '../../services/pmiTaskApi';
import { hapticFeedback } from '../../utils/mobile';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

/**
 * 移动端筛选抽屉组件
 */
const MobileFilterDrawer = ({ 
  visible, 
  onClose, 
  onFilter, 
  initialFilters = {} 
}) => {
  const [form] = Form.useForm();
  const [quickFilter, setQuickFilter] = useState('all');

  /**
   * 快速筛选选项
   */
  const quickFilterOptions = [
    { label: '全部', value: 'all' },
    { label: '今日任务', value: 'today' },
    { label: '即将执行', value: 'upcoming' },
    { label: '执行中', value: 'executing' },
    { label: '已完成', value: 'completed' },
    { label: '失败', value: 'failed' }
  ];

  /**
   * 任务状态选项
   */
  const statusOptions = [
    { label: '已调度', value: TASK_STATUS.SCHEDULED },
    { label: '执行中', value: TASK_STATUS.EXECUTING },
    { label: '已完成', value: TASK_STATUS.COMPLETED },
    { label: '失败', value: TASK_STATUS.FAILED },
    { label: '已取消', value: TASK_STATUS.CANCELLED }
  ];

  /**
   * 任务类型选项
   */
  const typeOptions = [
    { label: 'PMI开启', value: TASK_TYPE.PMI_WINDOW_OPEN },
    { label: 'PMI关闭', value: TASK_TYPE.PMI_WINDOW_CLOSE }
  ];

  /**
   * 处理快速筛选
   */
  const handleQuickFilter = (value) => {
    setQuickFilter(value);
    hapticFeedback('light');
    
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    
    let filters = {};
    
    switch (value) {
      case 'today':
        filters = {
          startTime: today,
          endTime: tomorrow
        };
        break;
      case 'upcoming':
        filters = {
          status: TASK_STATUS.SCHEDULED,
          startTime: now,
          endTime: new Date(now.getTime() + 24 * 60 * 60 * 1000)
        };
        break;
      case 'executing':
        filters = { status: TASK_STATUS.EXECUTING };
        break;
      case 'completed':
        filters = { status: TASK_STATUS.COMPLETED };
        break;
      case 'failed':
        filters = { status: TASK_STATUS.FAILED };
        break;
      default:
        filters = {};
    }
    
    form.setFieldsValue(filters);
  };

  /**
   * 应用筛选
   */
  const handleApplyFilter = async () => {
    try {
      const values = await form.validateFields();
      hapticFeedback('success');
      onFilter(values);
      onClose();
    } catch (error) {
      hapticFeedback('error');
    }
  };

  /**
   * 清除筛选
   */
  const handleClearFilter = () => {
    form.resetFields();
    setQuickFilter('all');
    hapticFeedback('light');
    onFilter({});
    onClose();
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FilterOutlined style={{ marginRight: 8 }} />
          <span>筛选条件</span>
        </div>
      }
      placement="bottom"
      height="70%"
      visible={visible}
      onClose={onClose}
      bodyStyle={{ padding: '16px' }}
      footer={
        <div style={{ 
          padding: '12px 16px', 
          borderTop: '1px solid #f0f0f0',
          background: '#fff'
        }}>
          <Space style={{ width: '100%' }}>
            <Button 
              block 
              size="large"
              icon={<ClearOutlined />}
              onClick={handleClearFilter}
            >
              清除
            </Button>
            <Button 
              type="primary" 
              block 
              size="large"
              icon={<SearchOutlined />}
              onClick={handleApplyFilter}
            >
              应用筛选
            </Button>
          </Space>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialFilters}
      >
        {/* 快速筛选 */}
        <div style={{ marginBottom: '20px' }}>
          <Title level={5} style={{ marginBottom: '12px' }}>快速筛选</Title>
          <Radio.Group 
            value={quickFilter} 
            onChange={(e) => handleQuickFilter(e.target.value)}
            style={{ width: '100%' }}
          >
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(2, 1fr)', 
              gap: '8px' 
            }}>
              {quickFilterOptions.map(option => (
                <Radio.Button 
                  key={option.value} 
                  value={option.value}
                  style={{ 
                    textAlign: 'center',
                    height: '40px',
                    lineHeight: '40px',
                    borderRadius: '6px'
                  }}
                >
                  {option.label}
                </Radio.Button>
              ))}
            </div>
          </Radio.Group>
        </div>

        <Divider />

        {/* 详细筛选 */}
        <Title level={5} style={{ marginBottom: '16px' }}>详细筛选</Title>

        {/* 任务状态 */}
        <Form.Item
          name="status"
          label={<Text strong>任务状态</Text>}
        >
          <Select
            placeholder="选择任务状态"
            allowClear
            size="large"
            style={{ borderRadius: '6px' }}
          >
            {statusOptions.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* 任务类型 */}
        <Form.Item
          name="taskType"
          label={<Text strong>任务类型</Text>}
        >
          <Select
            placeholder="选择任务类型"
            allowClear
            size="large"
            style={{ borderRadius: '6px' }}
          >
            {typeOptions.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* 时间范围 */}
        <Form.Item
          name="timeRange"
          label={<Text strong>时间范围</Text>}
        >
          <RangePicker
            style={{ width: '100%', borderRadius: '6px' }}
            size="large"
            showTime
            format="YYYY-MM-DD HH:mm"
            placeholder={['开始时间', '结束时间']}
          />
        </Form.Item>

        {/* PMI号码 */}
        <Form.Item
          name="pmiNumber"
          label={<Text strong>PMI号码</Text>}
        >
          <Select
            mode="tags"
            placeholder="输入PMI号码"
            size="large"
            style={{ borderRadius: '6px' }}
            tokenSeparators={[',', ' ']}
          />
        </Form.Item>

        {/* 重试次数筛选 */}
        <Form.Item
          name="retryCount"
          label={<Text strong>重试次数</Text>}
        >
          <div>
            <div style={{ marginBottom: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                显示重试次数大于等于指定值的任务
              </Text>
            </div>
            <Slider
              min={0}
              max={5}
              marks={{
                0: '0',
                1: '1',
                2: '2',
                3: '3',
                4: '4',
                5: '5+'
              }}
              step={1}
              tooltip={{ formatter: (value) => `${value}次` }}
            />
          </div>
        </Form.Item>

        {/* 高级选项 */}
        <Divider />
        <Title level={5} style={{ marginBottom: '16px' }}>高级选项</Title>

        {/* 只显示有错误的任务 */}
        <Form.Item
          name="hasError"
          valuePropName="checked"
        >
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            padding: '8px 0'
          }}>
            <div>
              <Text strong>只显示有错误的任务</Text>
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                筛选出执行失败或有错误信息的任务
              </Text>
            </div>
            <Switch />
          </div>
        </Form.Item>

        {/* 只显示即将执行的任务 */}
        <Form.Item
          name="upcomingOnly"
          valuePropName="checked"
        >
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            padding: '8px 0'
          }}>
            <div>
              <Text strong>只显示即将执行的任务</Text>
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                筛选出未来24小时内即将执行的任务
              </Text>
            </div>
            <Switch />
          </div>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default MobileFilterDrawer;
