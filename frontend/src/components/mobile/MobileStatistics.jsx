import React from 'react';
import { Card, Row, Col, Statistic, Progress, Typography } from 'antd';
import {
  ClockCircleOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { formatMobileNumber } from '../../utils/mobile';

const { Text, Title } = Typography;

/**
 * 移动端统计卡片组件
 */
const MobileStatistics = ({ statistics, isMobile }) => {
  
  /**
   * 获取统计项配置
   */
  const getStatItems = () => [
    {
      title: '总任务',
      value: statistics.totalTasks || 0,
      icon: <ClockCircleOutlined />,
      color: '#1890ff',
      key: 'total'
    },
    {
      title: '已调度',
      value: statistics.scheduledTasks || 0,
      icon: <PlayCircleOutlined />,
      color: '#722ed1',
      key: 'scheduled'
    },
    {
      title: '执行中',
      value: statistics.executingTasks || 0,
      icon: <LoadingOutlined />,
      color: '#fa8c16',
      key: 'executing'
    },
    {
      title: '成功率',
      value: statistics.successRate || 0,
      suffix: '%',
      precision: 1,
      icon: <CheckCircleOutlined />,
      color: '#52c41a',
      key: 'success'
    }
  ];

  /**
   * 渲染单个统计卡片
   */
  const renderStatCard = (item) => (
    <Col xs={12} sm={6} key={item.key}>
      <Card 
        className="mobile-statistic-card"
        size={isMobile ? 'small' : 'default'}
        style={{
          textAlign: 'center',
          borderRadius: '8px',
          border: `1px solid ${item.color}20`,
          background: `${item.color}05`
        }}
        bodyStyle={{ 
          padding: isMobile ? '12px 8px' : '16px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}
      >
        <div style={{ 
          fontSize: isMobile ? '20px' : '24px', 
          color: item.color,
          marginBottom: '4px'
        }}>
          {item.icon}
        </div>
        <Statistic
          title={
            <Text style={{ 
              fontSize: isMobile ? '11px' : '12px',
              color: '#666',
              fontWeight: 500
            }}>
              {item.title}
            </Text>
          }
          value={isMobile ? formatMobileNumber(item.value) : item.value}
          precision={item.precision}
          suffix={item.suffix}
          valueStyle={{ 
            fontSize: isMobile ? '18px' : '20px',
            color: item.color,
            fontWeight: 'bold',
            lineHeight: 1.2
          }}
        />
      </Card>
    </Col>
  );

  /**
   * 渲染详细统计（移动端折叠显示）
   */
  const renderDetailedStats = () => {
    if (!statistics.tasksByType && !statistics.tasksLast24Hours) {
      return null;
    }

    return (
      <Card 
        size="small"
        title={
          <Title level={5} style={{ margin: 0, fontSize: isMobile ? '14px' : '16px' }}>
            详细统计
          </Title>
        }
        style={{ marginTop: '12px' }}
      >
        {/* 24小时统计 */}
        {statistics.tasksLast24Hours > 0 && (
          <div style={{ marginBottom: '16px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              最近24小时
            </Text>
            <Row gutter={8} style={{ marginTop: '8px' }}>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                    {formatMobileNumber(statistics.tasksLast24Hours)}
                  </div>
                  <div style={{ fontSize: '11px', color: '#666' }}>总执行</div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#52c41a' }}>
                    {formatMobileNumber(statistics.successfulTasksLast24Hours || 0)}
                  </div>
                  <div style={{ fontSize: '11px', color: '#666' }}>成功</div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#ff4d4f' }}>
                    {formatMobileNumber(statistics.failedTasksLast24Hours || 0)}
                  </div>
                  <div style={{ fontSize: '11px', color: '#666' }}>失败</div>
                </div>
              </Col>
            </Row>
          </div>
        )}

        {/* 任务类型分布 */}
        {statistics.tasksByType && Object.keys(statistics.tasksByType).length > 0 && (
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              任务类型分布
            </Text>
            <div style={{ marginTop: '8px' }}>
              {Object.entries(statistics.tasksByType).map(([type, count]) => {
                const total = Object.values(statistics.tasksByType).reduce((sum, val) => sum + val, 0);
                const percentage = total > 0 ? (count / total * 100) : 0;
                const typeName = type === 'PMI_WINDOW_OPEN' ? 'PMI开启' : 'PMI关闭';
                const color = type === 'PMI_WINDOW_OPEN' ? '#52c41a' : '#fa8c16';
                
                return (
                  <div key={type} style={{ marginBottom: '8px' }}>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      marginBottom: '4px'
                    }}>
                      <Text style={{ fontSize: '12px' }}>{typeName}</Text>
                      <Text style={{ fontSize: '12px', fontWeight: 'bold' }}>
                        {formatMobileNumber(count)}
                      </Text>
                    </div>
                    <Progress 
                      percent={percentage} 
                      size="small"
                      strokeColor={color}
                      showInfo={false}
                      style={{ marginBottom: '4px' }}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* 成功率进度条 */}
        {statistics.successRate !== undefined && (
          <div style={{ marginTop: '16px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '8px'
            }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                整体成功率
              </Text>
              <Text style={{ fontSize: '12px', fontWeight: 'bold', color: '#52c41a' }}>
                {statistics.successRate.toFixed(1)}%
              </Text>
            </div>
            <Progress 
              percent={statistics.successRate} 
              strokeColor={{
                '0%': '#ff4d4f',
                '50%': '#fa8c16',
                '80%': '#52c41a',
                '100%': '#52c41a'
              }}
              size="small"
              showInfo={false}
            />
          </div>
        )}
      </Card>
    );
  };

  return (
    <div>
      {/* 主要统计卡片 */}
      <Row gutter={isMobile ? 8 : 16} style={{ marginBottom: isMobile ? '12px' : '16px' }}>
        {getStatItems().map(renderStatCard)}
      </Row>

      {/* 详细统计（移动端可选显示） */}
      {isMobile && renderDetailedStats()}
    </div>
  );
};

export default MobileStatistics;
