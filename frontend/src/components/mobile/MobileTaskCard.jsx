import React from 'react';
import { <PERSON>, <PERSON>, But<PERSON>, Space, Typography, Dropdown, Menu } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ClockCircleOutlined,
  EditOutlined,
  StopOutlined,
  EyeOutlined,
  MoreOutlined
} from '@ant-design/icons';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { formatMobileTime, hapticFeedback } from '../../utils/mobile';
import { 
  getTaskStatusText, 
  getTaskTypeText, 
  getTaskStatusColor,
  TASK_STATUS,
  TASK_TYPE
} from '../../services/pmiTaskApi';

const { Text } = Typography;

/**
 * 移动端任务卡片组件
 */
const MobileTaskCard = ({ 
  task, 
  onView, 
  onExecute, 
  onCancel, 
  onReschedule,
  currentUser,
  canExecuteTask,
  canCancelTask,
  canRescheduleTask
}) => {
  
  /**
   * 获取任务类型图标
   */
  const getTaskTypeIcon = (taskType) => {
    const iconMap = {
      [TASK_TYPE.PMI_WINDOW_OPEN]: <PlayCircleOutlined style={{ color: '#52c41a' }} />,
      [TASK_TYPE.PMI_WINDOW_CLOSE]: <PauseCircleOutlined style={{ color: '#fa8c16' }} />
    };
    return iconMap[taskType] || <ClockCircleOutlined />;
  };

  /**
   * 任务操作菜单
   */
  const getActionMenu = () => (
    <Menu>
      {canExecuteTask(currentUser) && task.status === TASK_STATUS.SCHEDULED && (
        <Menu.Item 
          key="execute" 
          icon={<PlayCircleOutlined />}
          onClick={() => {
            hapticFeedback('light');
            onExecute(task.id);
          }}
        >
          立即执行
        </Menu.Item>
      )}
      {canRescheduleTask(currentUser) && task.status === TASK_STATUS.SCHEDULED && (
        <Menu.Item 
          key="reschedule" 
          icon={<EditOutlined />}
          onClick={() => {
            hapticFeedback('light');
            onReschedule(task);
          }}
        >
          重新调度
        </Menu.Item>
      )}
      {canCancelTask(currentUser) && task.status === TASK_STATUS.SCHEDULED && (
        <Menu.Item 
          key="cancel" 
          icon={<StopOutlined />} 
          danger
          onClick={() => {
            hapticFeedback('medium');
            onCancel(task.id);
          }}
        >
          取消任务
        </Menu.Item>
      )}
    </Menu>
  );

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status) => {
    const colorMap = {
      [TASK_STATUS.SCHEDULED]: '#1890ff',
      [TASK_STATUS.EXECUTING]: '#fa8c16',
      [TASK_STATUS.COMPLETED]: '#52c41a',
      [TASK_STATUS.FAILED]: '#ff4d4f',
      [TASK_STATUS.CANCELLED]: '#8c8c8c'
    };
    return colorMap[status] || '#d9d9d9';
  };

  /**
   * 计算时间差
   */
  const getTimeInfo = () => {
    const now = moment();
    const scheduledTime = moment(task.scheduledTime);
    const diffMinutes = scheduledTime.diff(now, 'minutes');
    
    if (task.status === TASK_STATUS.COMPLETED && task.actualExecutionTime) {
      return {
        text: formatMobileTime(task.actualExecutionTime),
        type: 'completed',
        color: '#52c41a'
      };
    }
    
    if (task.status === TASK_STATUS.FAILED) {
      return {
        text: '执行失败',
        type: 'failed',
        color: '#ff4d4f'
      };
    }
    
    if (diffMinutes < 0) {
      return {
        text: `延迟${Math.abs(diffMinutes)}分钟`,
        type: 'delayed',
        color: '#fa8c16'
      };
    }
    
    if (diffMinutes < 60) {
      return {
        text: `${diffMinutes}分钟后执行`,
        type: 'soon',
        color: '#1890ff'
      };
    }
    
    return {
      text: formatMobileTime(task.scheduledTime),
      type: 'normal',
      color: '#666'
    };
  };

  const timeInfo = getTimeInfo();

  return (
    <Card 
      className="mobile-task-card"
      size="small" 
      style={{ 
        marginBottom: 8,
        borderLeft: `4px solid ${getStatusColor(task.status)}`,
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}
      bodyStyle={{ padding: '12px' }}
      onClick={() => {
        hapticFeedback('light');
        onView(task);
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1, minWidth: 0 }}>
          {/* 任务类型和状态 */}
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
            {getTaskTypeIcon(task.taskType)}
            <Text strong style={{ marginLeft: 8, fontSize: 14 }}>
              {getTaskTypeText(task.taskType)}
            </Text>
            <Tag 
              color={getTaskStatusColor(task.status)} 
              size="small" 
              style={{ marginLeft: 8 }}
            >
              {getTaskStatusText(task.status)}
            </Tag>
          </div>
          
          {/* 任务ID */}
          <div style={{ marginBottom: 4 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>ID: </Text>
            <Text style={{ fontSize: 12, fontFamily: 'monospace' }}>{task.id}</Text>
          </div>
          
          {/* PMI信息 */}
          {task.pmiNumber && (
            <div style={{ marginBottom: 4 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>PMI: </Text>
              {task.pmiRecordId ? (
                <Link
                  to={`/pmi-management/${task.pmiRecordId}`}
                  style={{
                    fontSize: 12,
                    fontWeight: 'bold',
                    color: '#1890ff',
                    textDecoration: 'none'
                  }}
                >
                  {task.pmiNumber}
                </Link>
              ) : (
                <Text strong style={{ fontSize: 12 }}>{task.pmiNumber}</Text>
              )}
              {task.userName && (
                <Text type="secondary" style={{ fontSize: 12, marginLeft: 8 }}>
                  ({task.userName})
                </Text>
              )}
            </div>
          )}
          
          {/* 时间信息 */}
          <div style={{ marginBottom: 4 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>时间: </Text>
            <Text style={{ fontSize: 12, color: timeInfo.color }}>
              {timeInfo.text}
            </Text>
          </div>
          
          {/* 重试信息 */}
          {task.retryCount > 0 && (
            <div style={{ marginBottom: 4 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>重试: </Text>
              <Text style={{ fontSize: 12, color: '#fa8c16' }}>{task.retryCount}次</Text>
            </div>
          )}
          
          {/* 错误信息 */}
          {task.errorMessage && (
            <div style={{ marginTop: 4 }}>
              <Text type="danger" style={{ fontSize: 11 }} ellipsis>
                {task.errorMessage}
              </Text>
            </div>
          )}
        </div>
        
        {/* 操作按钮 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4, marginLeft: 8 }}>
          <Button 
            size="small" 
            icon={<EyeOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              hapticFeedback('light');
              onView(task);
            }}
          />
          <Dropdown 
            overlay={getActionMenu()} 
            trigger={['click']}
            placement="bottomRight"
          >
            <Button 
              size="small" 
              icon={<MoreOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                hapticFeedback('light');
              }}
            />
          </Dropdown>
        </div>
      </div>
      
      {/* 进度指示器（仅执行中任务显示） */}
      {task.status === TASK_STATUS.EXECUTING && (
        <div style={{ 
          marginTop: 8, 
          height: 2, 
          background: '#f0f0f0', 
          borderRadius: 1,
          overflow: 'hidden'
        }}>
          <div style={{
            height: '100%',
            background: 'linear-gradient(90deg, #1890ff, #40a9ff)',
            animation: 'progress 2s ease-in-out infinite',
            width: '30%'
          }} />
        </div>
      )}
    </Card>
  );
};

export default MobileTaskCard;
