import { useState, useEffect, useCallback } from 'react';
import { getFrontendBaseUrl, getUserFrontendBaseUrl, getWebSocketEnabled, getSystemConfig, clearConfigCache } from '../utils/systemConfig';

/**
 * 系统配置Hook
 * 提供统一的配置获取和管理
 */
export const useSystemConfig = () => {
  const [configs, setConfigs] = useState({});
  const [loading, setLoading] = useState(false);

  // 获取单个配置
  const getConfig = useCallback(async (configKey, defaultValue = '') => {
    try {
      setLoading(true);
      const value = await getSystemConfig(configKey, defaultValue);
      setConfigs(prev => ({ ...prev, [configKey]: value }));
      return value;
    } catch (error) {
      console.error(`获取配置 ${configKey} 失败:`, error);
      return defaultValue;
    } finally {
      setLoading(false);
    }
  }, []);

  // 刷新配置
  const refreshConfig = useCallback(async (configKey) => {
    clearConfigCache(configKey);
    return await getConfig(configKey);
  }, [getConfig]);

  return {
    configs,
    loading,
    getConfig,
    refreshConfig
  };
};

/**
 * 域名配置Hook
 * 专门用于管理前端域名配置
 */
export const useDomainConfig = () => {
  const [frontendBaseUrl, setFrontendBaseUrl] = useState('');
  const [userFrontendBaseUrl, setUserFrontendBaseUrl] = useState('');
  const [loading, setLoading] = useState(false);

  // 加载域名配置
  const loadDomainConfigs = useCallback(async () => {
    try {
      setLoading(true);
      const [frontend, userFrontend] = await Promise.all([
        getFrontendBaseUrl(),
        getUserFrontendBaseUrl()
      ]);
      setFrontendBaseUrl(frontend);
      setUserFrontendBaseUrl(userFrontend);
    } catch (error) {
      console.error('加载域名配置失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取管理台基础URL
  const getFrontendUrl = useCallback(() => {
    return frontendBaseUrl || 'http://localhost:3000';
  }, [frontendBaseUrl]);

  // 获取用户前端基础URL
  const getUserFrontendUrl = useCallback(() => {
    return userFrontendBaseUrl || 'http://localhost:3001';
  }, [userFrontendBaseUrl]);

  // 生成管理台内部链接
  const generateFrontendLink = useCallback((path) => {
    const baseUrl = getFrontendUrl();
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${baseUrl}${cleanPath}`;
  }, [getFrontendUrl]);

  // 生成用户前端链接
  const generateUserFrontendLink = useCallback((path) => {
    const baseUrl = getUserFrontendUrl();
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${baseUrl}${cleanPath}`;
  }, [getUserFrontendUrl]);

  // 生成PMI链接
  const generatePmiLink = useCallback((pmiNumber) => {
    return generateUserFrontendLink(`/m/${pmiNumber}`);
  }, [generateUserFrontendLink]);

  // 生成Join Account链接
  const generateJoinAccountLink = useCallback((tokenNumber) => {
    return generateUserFrontendLink(`/join/${tokenNumber}`);
  }, [generateUserFrontendLink]);

  // 生成会议主持人链接
  const generateMeetingHostLink = useCallback((meetingUuid) => {
    return generateUserFrontendLink(`/meeting/${meetingUuid}`);
  }, [generateUserFrontendLink]);

  // 刷新域名配置
  const refreshDomainConfigs = useCallback(async () => {
    clearConfigCache('frontend.domain.base_url');
    clearConfigCache('user_frontend.domain.base_url');
    await loadDomainConfigs();
  }, [loadDomainConfigs]);

  // 初始化加载
  useEffect(() => {
    loadDomainConfigs();
  }, [loadDomainConfigs]);

  return {
    frontendBaseUrl,
    userFrontendBaseUrl,
    loading,
    getFrontendUrl,
    getUserFrontendUrl,
    generateFrontendLink,
    generateUserFrontendLink,
    generatePmiLink,
    generateJoinAccountLink,
    generateMeetingHostLink,
    refreshDomainConfigs
  };
};

/**
 * WebSocket配置Hook
 */
export const useWebSocketConfig = () => {
  const [enabled, setEnabled] = useState(false);
  const [loading, setLoading] = useState(false);

  // 加载WebSocket配置
  const loadWebSocketConfig = useCallback(async () => {
    try {
      setLoading(true);
      const isEnabled = await getWebSocketEnabled();
      setEnabled(isEnabled);
      return isEnabled;
    } catch (error) {
      console.error('加载WebSocket配置失败:', error);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // 刷新WebSocket配置
  const refreshWebSocketConfig = useCallback(async () => {
    clearConfigCache('dashboard.websocket.enabled');
    return await loadWebSocketConfig();
  }, [loadWebSocketConfig]);

  // 初始化加载
  useEffect(() => {
    loadWebSocketConfig();
  }, [loadWebSocketConfig]);

  return {
    enabled,
    loading,
    loadWebSocketConfig,
    refreshWebSocketConfig
  };
};
