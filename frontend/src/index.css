body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.ant-layout {
  min-height: 100vh;
}

.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.site-layout-content {
  min-height: 280px;
  padding: 24px;
  background: #fff;
}

/* 菜单高亮样式 */
.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
  border-radius: 6px;
  margin: 4px 8px;
  width: calc(100% - 16px);
}

.ant-menu-dark .ant-menu-item-selected::after {
  border-right: 3px solid #1890ff;
}

.ant-menu-dark .ant-menu-item-selected .ant-menu-item-icon,
.ant-menu-dark .ant-menu-item-selected .anticon {
  color: #ffffff !important;
}

.ant-menu-dark .ant-menu-item-selected span {
  color: #ffffff !important;
  font-weight: 600;
}

/* 菜单项悬停效果 */
.ant-menu-dark .ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.2) !important;
  border-radius: 6px;
  margin: 4px 8px;
  width: calc(100% - 16px);
  transition: all 0.3s ease;
}

/* 菜单项间距调整 */
.ant-menu-dark .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  width: calc(100% - 16px);
  transition: all 0.3s ease;
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  /* 页面内边距调整 */
  .site-layout-content {
    padding: 12px !important;
  }

  /* 卡片内边距调整 */
  .ant-card-body {
    padding: 16px !important;
  }

  /* 表格响应式 */
  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-table-thead > tr > th {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }

  /* 隐藏不重要的列 */
  .ant-table-thead > tr > th.mobile-hidden,
  .ant-table-tbody > tr > td.mobile-hidden {
    display: none !important;
  }

  /* 操作列样式优化 */
  .ant-table-thead > tr > th.action-column,
  .ant-table-tbody > tr > td.action-column {
    position: static !important;
    right: auto !important;
    width: auto !important;
    min-width: 80px !important;
  }

  /* 操作按钮样式 */
  .mobile-action-buttons .ant-btn {
    padding: 4px 6px !important;
    font-size: 12px !important;
    height: auto !important;
    min-width: auto !important;
  }

  .mobile-action-buttons .ant-space-item {
    margin-right: 4px !important;
  }

  /* 统计信息响应式 */
  .mobile-stats .ant-col {
    margin-bottom: 12px;
  }

  .mobile-stats .ant-statistic-title {
    font-size: 12px !important;
  }

  .mobile-stats .ant-statistic-content-value {
    font-size: 16px !important;
  }

  /* 搜索和筛选区域 */
  .zoom-user-filters .ant-row {
    margin-bottom: 8px !important;
  }

  .zoom-user-filters .ant-col {
    margin-bottom: 8px;
  }

  /* 操作按钮区域 */
  .zoom-user-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
  }

  .zoom-user-actions .ant-btn {
    width: 100% !important;
    margin-bottom: 0 !important;
    font-size: 13px !important;
    height: 36px !important;
  }

  /* 表格优化 */
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 6px 3px !important;
    font-size: 11px !important;
    white-space: nowrap;
    min-width: 60px;
  }

  .ant-table-thead > tr > th {
    background: #fafafa !important;
    font-weight: 600 !important;
    font-size: 10px !important;
  }

  .mobile-action-buttons .ant-btn {
    padding: 2px 4px !important;
    height: 24px !important;
    font-size: 10px !important;
    min-width: 24px !important;
  }

  .action-column {
    position: sticky !important;
    right: 0 !important;
    background: white !important;
    z-index: 1 !important;
    box-shadow: -2px 0 4px rgba(0,0,0,0.1) !important;
  }

  /* 移动端表格标签优化 */
  .ant-tag {
    margin: 0 !important;
    padding: 0 4px !important;
    font-size: 9px !important;
    line-height: 16px !important;
    border-radius: 2px !important;
  }

  /* 移动端状态标签优化 */
  .ant-table-tbody .ant-tag {
    max-width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 表格滚动提示 */
  .mobile-scroll-hint {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    padding: 8px 12px;
    margin-bottom: 12px;
    border-radius: 4px;
    font-size: 11px;
    color: #1890ff;
    text-align: center;
    line-height: 1.4;
  }

  /* 分页器响应式 */
  .ant-pagination {
    text-align: center !important;
  }

  .ant-pagination-options {
    display: none !important;
  }

  .ant-pagination-total-text {
    display: none !important;
  }
}

/* 超小屏幕优化 (iPhone SE等) */
@media (max-width: 375px) {
  .site-layout-content {
    padding: 8px !important;
  }

  .ant-card-body {
    padding: 12px !important;
  }

  .zoom-user-actions .ant-btn {
    font-size: 12px !important;
    height: 32px !important;
    padding: 4px 8px !important;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 6px 2px !important;
    font-size: 11px !important;
  }

  .mobile-stats .ant-statistic-content-value {
    font-size: 14px !important;
  }
}

/* 移动端布局优化 */
@media (max-width: 768px) {
  /* 确保内容区域占满全宽 */
  .ant-layout {
    margin-left: 0 !important;
  }

  /* 头部在移动端的优化 */
  .ant-layout-header {
    padding: 0 16px !important;
  }

  /* 移动端内容区域调整 */
  .ant-layout-content {
    margin-left: 0 !important;
  }

  /* 移动端用户下拉菜单优化 */
  .ant-dropdown-menu {
    min-width: 120px !important;
  }

  .ant-dropdown-menu-item {
    padding: 8px 12px !important;
    font-size: 14px !important;
  }

  /* 移动端标题优化 */
  .mobile-header-title {
    font-size: 16px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 移动端菜单切换按钮优化 */
  .ant-layout-header .ant-btn {
    border-radius: 6px !important;
  }

  /* 移动端侧边栏滚动优化 */
  .ant-layout-sider {
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端菜单滚动优化 */
  .ant-menu-inline {
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端菜单项间距优化 */
  .ant-menu-dark .ant-menu-item {
    margin: 2px 8px !important;
    border-radius: 4px !important;
  }

  .ant-menu-dark .ant-menu-submenu {
    margin: 2px 8px !important;
  }

  .ant-menu-dark .ant-menu-submenu-title {
    border-radius: 4px !important;
  }
}
