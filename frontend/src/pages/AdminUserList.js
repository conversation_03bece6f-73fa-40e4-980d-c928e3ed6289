import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Popconfirm,
  Tag,
  Card,
  Row,
  Col
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Option } = Select;
const { Search } = Input;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const AdminUserList = () => {
  const [adminUsers, setAdminUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [form] = Form.useForm();
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentUser, setCurrentUser] = useState(null);
  const [isMobileView, setIsMobileView] = useState(isMobile());

  // 获取当前登录用户信息
  useEffect(() => {
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      setCurrentUser(JSON.parse(userInfo));
    }
  }, []);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 加载管理员用户列表
  const loadAdminUsers = async () => {
    setLoading(true);
    try {
      const response = await api.get('/admin-users');
      setAdminUsers(response.data.content || response.data);
    } catch (error) {
      message.error('加载管理员用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAdminUsers();
  }, []);

  // 搜索管理员用户
  const handleSearch = async (value) => {
    if (!value.trim()) {
      loadAdminUsers();
      return;
    }
    
    setLoading(true);
    try {
      const response = await api.get(`/admin-users/search?keyword=${value}`);
      setAdminUsers(response.data);
    } catch (error) {
      message.error('搜索失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开创建/编辑模态框
  const showModal = (user = null) => {
    setEditingUser(user);
    setModalVisible(true);
    if (user) {
      form.setFieldsValue({
        email: user.email,
        fullName: user.fullName,
        phone: user.phone,
        role: user.role,
        status: user.status
      });
    } else {
      form.resetFields();
    }
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
    setEditingUser(null);
    form.resetFields();
  };

  // 提交表单
  const handleSubmit = async (values) => {
    try {
      if (editingUser) {
        // 更新用户
        await api.put(`/admin-users/${editingUser.id}`, values);
        message.success('管理员用户更新成功');
      } else {
        // 创建用户
        await api.post('/admin-users', values);
        message.success('管理员用户创建成功');
      }
      handleCancel();
      loadAdminUsers();
    } catch (error) {
      message.error(error.response?.data?.message || '操作失败');
    }
  };

  // 删除用户
  const handleDelete = async (id) => {
    try {
      await api.delete(`/admin-users/${id}`);
      message.success('管理员用户删除成功');
      loadAdminUsers();
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 角色标签颜色
  const getRoleColor = (role) => {
    switch (role) {
      case 'SUPER_ADMIN': return 'red';
      case 'ADMIN': return 'blue';
      case 'OPERATOR': return 'green';
      default: return 'default';
    }
  };

  // 状态标签颜色
  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'INACTIVE': return 'default';
      case 'SUSPENDED': return 'error';
      default: return 'default';
    }
  };

  // 角色中文名
  const getRoleName = (role) => {
    switch (role) {
      case 'SUPER_ADMIN': return '超级管理员';
      case 'ADMIN': return '管理员';
      case 'OPERATOR': return '操作员';
      default: return role;
    }
  };

  // 状态中文名
  const getStatusName = (status) => {
    switch (status) {
      case 'ACTIVE': return '激活';
      case 'INACTIVE': return '未激活';
      case 'SUSPENDED': return '已暂停';
      default: return status;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: isMobileView ? 50 : 80,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: isMobileView ? 80 : 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: isMobileView ? 120 : 180,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      key: 'fullName',
      width: isMobileView ? 80 : 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
      width: isMobileView ? 100 : 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: isMobileView ? 80 : 120,
      render: (role) => (
        <Tag
          color={getRoleColor(role)}
          style={{ fontSize: isMobileView ? '9px' : '12px' }}
        >
          {getRoleName(role)}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: isMobileView ? 60 : 100,
      render: (status) => (
        <Tag
          color={getStatusColor(status)}
          style={{ fontSize: isMobileView ? '9px' : '12px' }}
        >
          {getStatusName(status)}
        </Tag>
      ),
    },
    {
      title: isMobileView ? '登录' : '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: isMobileView ? 80 : 150,
      render: (time) => (
        <span style={{ fontSize: isMobileView ? '10px' : '14px' }}>
          {time ? (isMobileView ?
            new Date(time).toLocaleDateString() :
            new Date(time).toLocaleString()
          ) : '从未登录'}
        </span>
      ),
    },
    {
      title: isMobileView ? '创建' : '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: isMobileView ? 80 : 150,
      render: (time) => (
        <span style={{ fontSize: isMobileView ? '10px' : '14px' }}>
          {isMobileView ?
            new Date(time).toLocaleDateString() :
            new Date(time).toLocaleString()
          }
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 80 : 150,
      fixed: isMobileView ? 'right' : false,
      className: isMobileView ? 'action-column' : '',
      render: (_, record) => (
        <Space size={isMobileView ? 'small' : 'middle'} className={isMobileView ? 'mobile-action-buttons' : ''}>
          {currentUser?.role === 'SUPER_ADMIN' && (
            <>
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => showModal(record)}
                size={isMobileView ? 'small' : 'middle'}
                style={{ fontSize: isMobileView ? '10px' : '14px', padding: isMobileView ? '2px 4px' : '4px 15px' }}
              >
                {!isMobileView && '编辑'}
              </Button>
              <Popconfirm
                title="确定要删除这个管理员用户吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                  size={isMobileView ? 'small' : 'middle'}
                  style={{ fontSize: isMobileView ? '10px' : '14px', padding: isMobileView ? '2px 4px' : '4px 15px' }}
                >
                  {!isMobileView && '删除'}
                </Button>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: isMobileView ? '12px' : '24px' }}>
      {/* 移动端滚动提示 */}
      {isMobileView && (
        <div className="mobile-scroll-hint" style={{ marginBottom: '16px' }}>
          👈 表格可左右滑动查看所有列信息（ID、用户名、邮箱、姓名、电话、角色、状态、登录、创建、操作）
        </div>
      )}

      <Card size={isMobileView ? 'small' : 'default'}>
        <Row gutter={isMobileView ? [8, 8] : [16, 16]} style={{ marginBottom: isMobileView ? 12 : 16 }}>
          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <Search
              placeholder={isMobileView ? "搜索用户" : "搜索用户名或姓名"}
              allowClear
              enterButton={<SearchOutlined />}
              size={isMobileView ? 'middle' : 'large'}
              onSearch={handleSearch}
              onChange={(e) => setSearchKeyword(e.target.value)}
            />
          </Col>
          <Col xs={24} sm={24} md={12} lg={12} xl={12} style={{ textAlign: isMobileView ? 'left' : 'right' }}>
            {currentUser?.role === 'SUPER_ADMIN' && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                size={isMobileView ? 'middle' : 'large'}
                onClick={() => showModal()}
                style={{ width: isMobileView ? '100%' : 'auto' }}
              >
                新增管理员
              </Button>
            )}
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={adminUsers}
          rowKey="id"
          loading={loading}
          size={isMobileView ? 'small' : 'middle'}
          scroll={{ x: isMobileView ? 900 : 'auto' }}
          pagination={{
            showSizeChanger: !isMobileView,
            showQuickJumper: !isMobileView,
            showTotal: (total) => `共 ${total} 条记录`,
            simple: isMobileView,
            size: isMobileView ? 'small' : 'default',
          }}
        />
      </Card>

      <Modal
        title={editingUser ? '编辑管理员用户' : '新增管理员用户'}
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {!editingUser && (
            <>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, max: 50, message: '用户名长度必须在3-50个字符之间' }
                ]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>

              <Form.Item
                name="password"
                label="密码"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, max: 100, message: '密码长度必须在6-100个字符之间' }
                ]}
              >
                <Input.Password placeholder="请输入密码" />
              </Form.Item>
            </>
          )}

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            name="fullName"
            label="姓名"
            rules={[
              { required: true, message: '请输入姓名' },
              { max: 100, message: '姓名长度不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="电话"
            rules={[
              { max: 20, message: '电话号码长度不能超过20个字符' }
            ]}
          >
            <Input placeholder="请输入电话号码" />
          </Form.Item>

          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              <Option value="SUPER_ADMIN">超级管理员</Option>
              <Option value="ADMIN">管理员</Option>
              <Option value="OPERATOR">操作员</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
            initialValue="ACTIVE"
          >
            <Select placeholder="请选择状态">
              <Option value="ACTIVE">激活</Option>
              <Option value="INACTIVE">未激活</Option>
              <Option value="SUSPENDED">已暂停</Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingUser ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminUserList;
