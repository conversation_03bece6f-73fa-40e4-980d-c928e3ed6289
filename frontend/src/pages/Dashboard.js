import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Tabs, Button, Space, message, Tooltip } from 'antd';
import { UserOutlined, TeamOutlined, VideoCameraOutlined, CalendarOutlined, DashboardOutlined, MonitorOutlined, CopyOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { userApi, zoomUserApi, meetingApi } from '../services/api';
import RealTimeMonitoring from './Dashboard/RealTimeMonitoring';
import dayjs from 'dayjs';
import './Dashboard.css';
import { copyMeetingInfo } from '../utils/copyMeetingInfo';

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalZoomUsers: 0,
    totalMeetings: 0,
    todayMeetings: 0
  });
  const [recentMeetings, setRecentMeetings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [activeTab, setActiveTab] = useState('overview');

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 加载仪表板数据
  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // 并行加载所有数据
      const [usersResponse, zoomUsersResponse, meetingsResponse, meetingDetailsResponse] = await Promise.all([
        userApi.getUsers({ page: 0, size: 1 }),
        zoomUserApi.getAllZoomUsers({ page: 0, size: 1 }),
        meetingApi.getMeetings({ page: 0, size: 10 }),
        meetingApi.getRecentWeekMeetingDetails({ page: 0, size: 15 })
      ]);

      // 更新最近会议数据
      console.log('meetingDetailsResponse:', meetingDetailsResponse);
      console.log('meetingDetailsResponse.data:', meetingDetailsResponse.data);

      let meetingDetailsList = [];
      if (meetingDetailsResponse && meetingDetailsResponse.data) {
        if (Array.isArray(meetingDetailsResponse.data)) {
          meetingDetailsList = meetingDetailsResponse.data;
        } else if (meetingDetailsResponse.data.content && Array.isArray(meetingDetailsResponse.data.content)) {
          meetingDetailsList = meetingDetailsResponse.data.content;
        } else {
          console.warn('Unexpected meetingDetailsResponse structure:', meetingDetailsResponse.data);
          meetingDetailsList = [];
        }
      }

      console.log('Final meetingDetailsList:', meetingDetailsList);

      // 计算今天的会议数量
      const todayMeetings = Array.isArray(meetingDetailsList) ? meetingDetailsList.filter(meeting =>
        meeting.startTime && isTodayMeeting(meeting.startTime)
      ) : [];

      // 更新统计数据
      setStats({
        totalUsers: usersResponse.data.totalElements || 0,
        totalZoomUsers: zoomUsersResponse.data.totalElements || 0,
        totalMeetings: Array.isArray(meetingDetailsList) ? meetingDetailsList.length : 0, // 使用实际的会议详情数量
        todayMeetings: Array.isArray(todayMeetings) ? todayMeetings.length : 0 // 使用计算出的今日会议数量
      });

      setRecentMeetings(Array.isArray(meetingDetailsList) ? meetingDetailsList : []);

    } catch (error) {
      console.error('加载仪表板数据失败:', error);
      console.error('Error details:', error.response?.data || error.message);
      console.error('Error status:', error.response?.status);

      // 如果是认证错误，可能需要重新登录
      if (error.response?.status === 401) {
        console.error('认证失败，可能需要重新登录');
        message.error('认证失败，请重新登录');
      } else {
        message.error('加载数据失败: ' + (error.message || '未知错误'));
      }
    } finally {
      setLoading(false);
    }
  };

  // 判断是否为当天会议
  const isTodayMeeting = (startTime) => {
    if (!startTime) return false;
    const today = dayjs().format('YYYY-MM-DD');
    const meetingDate = dayjs(startTime).format('YYYY-MM-DD');
    return today === meetingDate;
  };

  const meetingColumns = [
    {
      title: isMobileView ? '主题' : '会议主题',
      dataIndex: 'topic',
      key: 'topic',
      width: isMobileView ? 120 : 200,
      ellipsis: true,
      render: (text, record) => {
        return (
          <span style={{ 
            fontSize: isMobileView ? '11px' : '14px'
          }}>
            {text || '未命名会议'}
          </span>
        );
      },
    },
    {
      title: '主持人',
      dataIndex: 'hostEmail',
      key: 'host',
      width: isMobileView ? 80 : 120,
      ellipsis: true,
      render: (hostEmail, record) => {
        return (
          <span style={{ 
            fontSize: isMobileView ? '11px' : '14px'
          }}>
            {hostEmail || record.hostId || '-'}
          </span>
        );
      },
    },
    {
      title: isMobileView ? '时间' : '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: isMobileView ? 100 : 150,
      render: (text, record) => {
        const displayTime = text ? 
          dayjs(text).format(isMobileView ? 'MM-DD HH:mm' : 'YYYY-MM-DD HH:mm') : 
          '待定';
        
        return (
          <span style={{ 
            fontSize: isMobileView ? '10px' : '14px'
          }}>
            {displayTime}
          </span>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: isMobileView ? 60 : 100,
      render: (status, record) => {
        const statusConfig = {
          waiting: { color: 'blue', text: isMobileView ? '等待' : '等待中' },
          started: { color: 'green', text: isMobileView ? '进行' : '进行中' },
          ended: { color: 'default', text: isMobileView ? '结束' : '已结束' },
          cancelled: { color: 'red', text: isMobileView ? '取消' : '已取消' },
        };
        const config = statusConfig[status] || { color: 'default', text: status || '未知' };
        return (
          <Tag
            color={config.color}
            style={{ 
              fontSize: isMobileView ? '9px' : '12px'
            }}
          >
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: isMobileView ? 'ID' : '会议ID',
      dataIndex: 'zoomMeetingId',
      key: 'zoomMeetingId',
      width: isMobileView ? 80 : 120,
      ellipsis: true,
      render: (zoomMeetingId, record) => {
        return (
          <span style={{ 
            fontSize: isMobileView ? '10px' : '12px',
            fontFamily: 'monospace',
            color: '#666'
          }}>
            {zoomMeetingId || '-'}
          </span>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 100 : 140,
      render: (text, record) => {
        return (
          <Button
            type="text"
            size={isMobileView ? 'small' : 'middle'}
            icon={<CopyOutlined />}
            onClick={() => copyMeetingInfo(record)}
            style={{
              color: '#1890ff',
              padding: isMobileView ? '2px 4px' : '4px 8px',
              fontSize: isMobileView ? '11px' : '14px'
            }}
          >
            {isMobileView ? '复制' : '复制会议信息'}
          </Button>
        );
      },
    },
  ];



  useEffect(() => {
    loadDashboardData();
  }, []);

  // 概览页面内容
  const OverviewContent = () => (
    <div>

      <Row gutter={isMobileView ? [8, 8] : [16, 16]} style={{ marginBottom: isMobileView ? '16px' : '24px' }}>
        <Col xs={12} sm={12} md={6} lg={6} xl={6}>
          <Card size={isMobileView ? 'small' : 'default'}>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={!isMobileView && <UserOutlined />}
              loading={loading}
              valueStyle={{ fontSize: isMobileView ? '18px' : '24px' }}
              className={isMobileView ? 'mobile-stats' : ''}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6} xl={6}>
          <Card size={isMobileView ? 'small' : 'default'}>
            <Statistic
              title="Zoom用户数"
              value={stats.totalZoomUsers}
              prefix={!isMobileView && <TeamOutlined />}
              loading={loading}
              valueStyle={{ fontSize: isMobileView ? '18px' : '24px' }}
              className={isMobileView ? 'mobile-stats' : ''}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6} xl={6}>
          <Card size={isMobileView ? 'small' : 'default'}>
            <Statistic
              title="总会议数"
              value={stats.totalMeetings}
              prefix={!isMobileView && <VideoCameraOutlined />}
              loading={loading}
              valueStyle={{ fontSize: isMobileView ? '18px' : '24px' }}
              className={isMobileView ? 'mobile-stats' : ''}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6} xl={6}>
          <Card size={isMobileView ? 'small' : 'default'}>
            <Statistic
              title="今日会议"
              value={stats.todayMeetings}
              prefix={!isMobileView && <CalendarOutlined />}
              loading={loading}
              valueStyle={{ fontSize: isMobileView ? '18px' : '24px' }}
              className={isMobileView ? 'mobile-stats' : ''}
            />
          </Card>
        </Col>
      </Row>

      <Card
        title="最近一周会议"
        size={isMobileView ? 'small' : 'default'}
        style={{ marginBottom: isMobileView ? '16px' : '24px' }}
        extra={
          <Button
            type="link"
            size={isMobileView ? 'small' : 'middle'}
            icon={<ArrowRightOutlined />}
            onClick={() => window.location.href = '/meetings'}
            style={{
              padding: 0,
              fontSize: isMobileView ? '12px' : '14px'
            }}
          >
            {isMobileView ? '查看全部' : '管理会议'}
          </Button>
        }
      >
        <Table
          columns={meetingColumns}
          dataSource={recentMeetings}
          rowKey="id"
          loading={loading}
          pagination={false}
          size={isMobileView ? 'small' : 'middle'}
          scroll={{ x: isMobileView ? 500 : 'auto' }}
          rowClassName={(record, index) => {
            const isToday = isTodayMeeting(record.startTime);
            return isToday ? 'today-meeting-row' : '';
          }}
        />
      </Card>
    </div>
  );

  const tabItems = [
    {
      key: 'overview',
      label: isMobileView ? '概览' : '系统概览',
      children: <OverviewContent />,
      icon: <DashboardOutlined />
    },
    {
      key: 'monitoring',
      label: isMobileView ? '监控' : '实时监控',
      children: <RealTimeMonitoring />,
      icon: <MonitorOutlined />
    }
  ];

  return (
    <div style={{ padding: isMobileView ? '8px' : '24px' }}>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size={isMobileView ? 'small' : 'default'}
        tabBarStyle={{
          marginBottom: isMobileView ? '8px' : '16px'
        }}
      />
    </div>
  );
};

export default Dashboard;
