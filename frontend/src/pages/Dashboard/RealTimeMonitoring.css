.real-time-monitoring {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.connection-status {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.monitoring-card {
  height: 100%;
  transition: all 0.3s ease;
}

.monitoring-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.monitoring-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

.monitoring-card .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
}

.monitoring-card .ant-card-body {
  padding: 16px;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background: #f6f6f6;
}

.status-indicator.healthy {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-indicator.warning {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.status-indicator.error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
}

.trend-chart {
  height: 200px;
  margin-top: 16px;
}

.alert-panel {
  margin-bottom: 16px;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
  background: white;
}

.alert-item.critical {
  border-left-color: #ff4d4f;
  background: #fff2f0;
}

.alert-item.warning {
  border-left-color: #faad14;
  background: #fffbe6;
}

.alert-item.info {
  border-left-color: #1890ff;
  background: #e6f7ff;
}

.alert-time {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.performance-item {
  text-align: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.performance-item .value {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.performance-item .label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.chart-container {
  height: 200px;
  margin-top: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .real-time-monitoring {
    padding: 12px;
  }
  
  .monitoring-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .performance-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .performance-grid {
    grid-template-columns: 1fr;
  }
  
  .metric-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.updating {
  animation: pulse 1s infinite;
}

.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 进度条自定义样式 */
.ant-progress-line {
  margin: 8px 0;
}

.ant-progress-text {
  font-size: 12px !important;
}

/* 统计数字自定义样式 */
.ant-statistic-title {
  font-size: 12px !important;
  margin-bottom: 4px !important;
}

.ant-statistic-content {
  font-size: 18px !important;
}

/* 卡片标题图标 */
.ant-card-head-title .anticon {
  margin-right: 8px;
  color: #1890ff;
}

/* Badge状态指示器 */
.ant-badge-status-dot {
  width: 8px !important;
  height: 8px !important;
}

.ant-badge-status-text {
  font-size: 12px;
  margin-left: 8px;
}
