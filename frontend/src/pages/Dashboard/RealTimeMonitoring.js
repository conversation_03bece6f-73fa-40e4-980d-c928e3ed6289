import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Statistic, Progress, Alert, Badge, Button, Spin, message } from 'antd';
import { 
  UserOutlined, 
  VideoCameraOutlined, 
  LinkOutlined, 
  DesktopOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import SockJS from 'sockjs-client';
import { Stomp } from '@stomp/stompjs';
import { Line } from '@ant-design/plots';
import { systemConfigApi } from '../../services/api';
import { getWebSocketEnabled, clearConfigCache } from '../../utils/systemConfig';
import './RealTimeMonitoring.css';

const RealTimeMonitoring = () => {
  const [zoomAccountStatus, setZoomAccountStatus] = useState(null);
  const [meetingStatus, setMeetingStatus] = useState(null);
  const [pmiStatus, setPmiStatus] = useState(null);
  const [systemPerformance, setSystemPerformance] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(true); // 新增连接中状态
  const [loading, setLoading] = useState(true);
  const [websocketEnabled, setWebsocketEnabled] = useState(false);
  const [trendData, setTrendData] = useState({
    zoomUsage: [],
    meetingCount: [],
    systemCpu: [],
    systemMemory: []
  });

  const stompClient = useRef(null);

  // 检查WebSocket配置
  const checkWebSocketConfig = async () => {
    try {
      const enabled = await getWebSocketEnabled();
      setWebsocketEnabled(enabled);
      console.log('WebSocket配置检查结果:', enabled);
      return enabled;
    } catch (error) {
      console.error('获取WebSocket配置失败:', error);
      // 默认启用WebSocket
      setWebsocketEnabled(true);
      return true;
    }
  };

  // WebSocket连接
  useEffect(() => {
    const initializeWebSocket = async () => {
      const enabled = await checkWebSocketConfig();
      if (enabled) {
        connectWebSocket();
      } else {
        setConnecting(false);
        setConnected(false);
        message.info('WebSocket实时监控已禁用，请在系统配置中启用');
      }
    };

    initializeWebSocket();
    loadInitialData();

    return () => {
      if (stompClient.current) {
        stompClient.current.disconnect();
      }
    };
  }, []);

  const connectWebSocket = async () => {
    // 检查WebSocket是否启用
    if (!websocketEnabled) {
      const enabled = await checkWebSocketConfig();
      if (!enabled) {
        setConnecting(false);
        setConnected(false);
        return;
      }
    }

    setConnecting(true); // 开始连接
    setConnected(false);

    const socket = new SockJS('http://localhost:8080/ws-monitoring');
    stompClient.current = Stomp.over(socket);

    stompClient.current.connect({}, (frame) => {
      console.log('WebSocket连接成功:', frame);
      setConnected(true);
      setConnecting(false); // 连接成功，停止连接中状态

      // 订阅各种监控数据
      stompClient.current.subscribe('/topic/monitoring/zoom-account', (message) => {
        const data = JSON.parse(message.body);
        setZoomAccountStatus(data.data);
        updateTrendData('zoomUsage', data.data.usageRate);
      });

      stompClient.current.subscribe('/topic/monitoring/meeting', (message) => {
        const data = JSON.parse(message.body);
        setMeetingStatus(data.data);
        updateTrendData('meetingCount', data.data.totalMeetings);
      });

      stompClient.current.subscribe('/topic/monitoring/pmi', (message) => {
        const data = JSON.parse(message.body);
        setPmiStatus(data.data);
      });

      stompClient.current.subscribe('/topic/monitoring/system', (message) => {
        const data = JSON.parse(message.body);
        setSystemPerformance(data.data);
        updateTrendData('systemCpu', data.data.cpuUsage);
        updateTrendData('systemMemory', data.data.memoryUsage);
      });

      stompClient.current.subscribe('/topic/monitoring/alert', (message) => {
        const data = JSON.parse(message.body);
        setAlerts(prev => [data.data, ...prev.slice(0, 9)]); // 保留最新10条告警
      });

    }, (error) => {
      console.error('WebSocket连接失败:', error);
      setConnected(false);
      setConnecting(false); // 连接失败，停止连接中状态
      // 5秒后重连
      setTimeout(() => {
        console.log('尝试重新连接WebSocket...');
        connectWebSocket();
      }, 5000);
    });
  };

  const updateTrendData = (type, value) => {
    const now = new Date();
    const timeStr = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    setTrendData(prev => ({
      ...prev,
      [type]: [...prev[type].slice(-19), { time: timeStr, value: value || 0 }]
    }));
  };

  const loadInitialData = async () => {
    try {
      const response = await fetch('/api/monitoring/comprehensive');
      const result = await response.json();
      
      if (result.success) {
        const data = result.data;
        setZoomAccountStatus(data.zoomAccount);
        setMeetingStatus(data.meeting);
        setPmiStatus(data.pmi);
        setSystemPerformance(data.system);
      }
    } catch (error) {
      console.error('加载初始数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    try {
      await fetch('/api/monitoring/refresh', { method: 'POST' });
      await loadInitialData();
    } catch (error) {
      console.error('刷新数据失败:', error);
    }
  };

  const getHealthStatusColor = (status) => {
    switch (status) {
      case 'HEALTHY': return 'success';
      case 'WARNING': return 'warning';
      case 'ERROR': return 'error';
      default: return 'default';
    }
  };

  const getHealthStatusIcon = (status) => {
    switch (status) {
      case 'HEALTHY': return <CheckCircleOutlined />;
      case 'WARNING': return <WarningOutlined />;
      case 'ERROR': return <ExclamationCircleOutlined />;
      default: return null;
    }
  };

  const lineConfig = {
    height: 200,
    xField: 'time',
    yField: 'value',
    smooth: true,
    point: {
      size: 3,
    },
    line: {
      size: 2,
    },
    color: '#1890ff',
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载监控数据中...</div>
      </div>
    );
  }

  return (
    <div className="real-time-monitoring">
      {/* 连接状态和操作栏 */}
      <div className="monitoring-header">
        <div className="connection-status">
          <Badge
            status={
              !websocketEnabled ? 'default' :
              connecting ? 'processing' :
              (connected ? 'success' : 'error')
            }
            text={
              !websocketEnabled ? 'WebSocket实时监控已禁用' :
              connecting ? 'WebSocket连接中...' :
              (connected ? 'WebSocket已连接' : 'WebSocket连接断开')
            }
          />
        </div>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshData}
            type="primary"
          >
            刷新数据
          </Button>
          <Button
            onClick={async () => {
              clearConfigCache('dashboard.websocket.enabled');
              const enabled = await checkWebSocketConfig();
              message.success(`WebSocket配置已刷新: ${enabled ? '启用' : '禁用'}`);
              if (enabled && !connected) {
                connectWebSocket();
              } else if (!enabled && connected) {
                if (stompClient.current) {
                  stompClient.current.disconnect();
                }
                setConnected(false);
                setConnecting(false);
              }
            }}
          >
            刷新配置
          </Button>
        </div>
      </div>

      {/* 告警信息 */}
      {alerts.length > 0 && (
        <Alert
          message={`最新告警: ${alerts[0].title}`}
          description={alerts[0].message}
          type={alerts[0].level === 'CRITICAL' ? 'error' : 'warning'}
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 监控卡片 */}
      <Row gutter={[16, 16]}>
        {/* Zoom账号状态 */}
        <Col xs={24} sm={12} lg={6}>
          <Card 
            title={
              <span>
                <UserOutlined /> Zoom账号状态
              </span>
            }
            extra={
              zoomAccountStatus && (
                <Badge 
                  status={getHealthStatusColor(zoomAccountStatus.healthStatus)}
                  text={zoomAccountStatus.healthStatus}
                />
              )
            }
          >
            {zoomAccountStatus ? (
              <>
                <Statistic
                  title="许可证使用率"
                  value={zoomAccountStatus.usageRate}
                  precision={1}
                  suffix="%"
                  valueStyle={{ 
                    color: zoomAccountStatus.usageRate > 80 ? '#cf1322' : '#3f8600' 
                  }}
                />
                <Progress 
                  percent={zoomAccountStatus.usageRate} 
                  status={zoomAccountStatus.usageRate > 90 ? 'exception' : 'active'}
                  size="small"
                />
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  已用: {zoomAccountStatus.usedLicenses} / 总计: {zoomAccountStatus.totalLicenses}
                </div>
              </>
            ) : (
              <Spin />
            )}
          </Card>
        </Col>

        {/* 会议状态 */}
        <Col xs={24} sm={12} lg={6}>
          <Card 
            title={
              <span>
                <VideoCameraOutlined /> 会议状态
              </span>
            }
          >
            {meetingStatus ? (
              <>
                <Row gutter={8}>
                  <Col span={12}>
                    <Statistic
                      title="今日会议"
                      value={meetingStatus.totalMeetings}
                      valueStyle={{ fontSize: '20px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="进行中"
                      value={meetingStatus.activeMeetings}
                      valueStyle={{ color: '#1890ff', fontSize: '20px' }}
                    />
                  </Col>
                </Row>
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  已完成: {meetingStatus.completedMeetings} | 
                  已安排: {meetingStatus.scheduledMeetings}
                </div>
              </>
            ) : (
              <Spin />
            )}
          </Card>
        </Col>

        {/* PMI状态 */}
        <Col xs={24} sm={12} lg={6}>
          <Card 
            title={
              <span>
                <LinkOutlined /> PMI状态
              </span>
            }
          >
            {pmiStatus ? (
              <>
                <Statistic
                  title="PMI使用率"
                  value={pmiStatus.usageRate}
                  precision={1}
                  suffix="%"
                  valueStyle={{ color: '#722ed1' }}
                />
                <Progress 
                  percent={pmiStatus.usageRate} 
                  strokeColor="#722ed1"
                  size="small"
                />
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  使用中: {pmiStatus.inUsePmi} / 总计: {pmiStatus.totalPmi}
                </div>
              </>
            ) : (
              <Spin />
            )}
          </Card>
        </Col>

        {/* 系统性能 */}
        <Col xs={24} sm={12} lg={6}>
          <Card 
            title={
              <span>
                <DesktopOutlined /> 系统性能
              </span>
            }
          >
            {systemPerformance ? (
              <>
                <Row gutter={8}>
                  <Col span={12}>
                    <Statistic
                      title="CPU"
                      value={systemPerformance.cpuUsage}
                      precision={1}
                      suffix="%"
                      valueStyle={{ 
                        color: systemPerformance.cpuUsage > 80 ? '#cf1322' : '#3f8600',
                        fontSize: '16px'
                      }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="内存"
                      value={systemPerformance.memoryUsage}
                      precision={1}
                      suffix="%"
                      valueStyle={{ 
                        color: systemPerformance.memoryUsage > 85 ? '#cf1322' : '#3f8600',
                        fontSize: '16px'
                      }}
                    />
                  </Col>
                </Row>
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  活跃用户: {systemPerformance.activeUsers} | 
                  API调用: {systemPerformance.apiCallsPerMinute}/分钟
                </div>
              </>
            ) : (
              <Spin />
            )}
          </Card>
        </Col>
      </Row>

      {/* 趋势图表 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} lg={12}>
          <Card title="Zoom使用率趋势" size="small">
            <Line {...lineConfig} data={trendData.zoomUsage} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="系统CPU使用率趋势" size="small">
            <Line {...lineConfig} data={trendData.systemCpu} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default RealTimeMonitoring;
