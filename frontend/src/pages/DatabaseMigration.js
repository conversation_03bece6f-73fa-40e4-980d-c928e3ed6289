import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Button,
  Tag,
  Space,
  message,
  Modal,
  Alert,
  Typography,
  Descriptions,
  Progress,
  Tooltip,
  Popconfirm
} from 'antd';
import {
  DatabaseOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';
import api from '../services/api';

const { Title, Text } = Typography;

const DatabaseMigration = () => {
  const [loading, setLoading] = useState(false);
  const [migrationStatus, setMigrationStatus] = useState(null);
  const [executing, setExecuting] = useState(false);

  // 获取迁移状态
  const fetchMigrationStatus = async () => {
    try {
      setLoading(true);
      const response = await api.get('/migration/status');
      if (response.data.success) {
        setMigrationStatus(response.data);
      } else {
        message.error(response.data.message || '获取迁移状态失败');
      }
    } catch (error) {
      message.error('获取迁移状态失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 执行数据库迁移
  const executeMigration = async () => {
    try {
      setExecuting(true);
      const response = await api.post('/migration/execute');
      
      if (response.data.success) {
        message.success(`迁移执行成功！执行了 ${response.data.migrationsExecuted} 个迁移脚本`);
        await fetchMigrationStatus(); // 刷新状态
      } else {
        message.error(response.data.message || '迁移执行失败');
      }
    } catch (error) {
      message.error('迁移执行失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setExecuting(false);
    }
  };

  useEffect(() => {
    fetchMigrationStatus();
  }, []);

  // 迁移状态标签
  const getMigrationStateTag = (state) => {
    const stateMap = {
      'Success': { color: 'success', icon: <CheckCircleOutlined />, text: '成功' },
      'Pending': { color: 'warning', icon: <ClockCircleOutlined />, text: '待执行' },
      'Failed': { color: 'error', icon: <ExclamationCircleOutlined />, text: '失败' },
      'Ignored': { color: 'default', icon: <InfoCircleOutlined />, text: '忽略' },
      'Baseline': { color: 'blue', icon: <DatabaseOutlined />, text: '基线' }
    };
    
    const config = stateMap[state] || { color: 'default', icon: <InfoCircleOutlined />, text: state };
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 迁移记录表格列定义
  const migrationColumns = [
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 150,
      render: (text) => (
        <Tag color="blue">
          {text}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: getMigrationStateTag,
    },
    {
      title: '安装时间',
      dataIndex: 'installedOn',
      key: 'installedOn',
      width: 180,
      render: (text) => text ? (
        <Space>
          <ClockCircleOutlined />
          {new Date(text).toLocaleString()}
        </Space>
      ) : '-',
    },
    {
      title: '执行时间',
      dataIndex: 'executionTime',
      key: 'executionTime',
      width: 100,
      render: (time) => time ? `${time}ms` : '-',
    },
  ];

  // 计算迁移进度
  const getMigrationProgress = () => {
    if (!migrationStatus) return { percent: 0, status: 'normal' };
    
    const { allMigrations, pendingMigrations } = migrationStatus;
    const total = allMigrations?.length || 0;
    const completed = total - (pendingMigrations?.length || 0);
    const percent = total > 0 ? Math.round((completed / total) * 100) : 100;
    
    return {
      percent,
      status: pendingMigrations?.length > 0 ? 'active' : 'success'
    };
  };

  const progress = getMigrationProgress();

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <DatabaseOutlined /> 数据库迁移管理
        </Title>
        <Text type="secondary">
          查看和执行数据库迁移脚本，管理数据库结构变更
        </Text>
      </div>

      {/* 迁移状态概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <SyncOutlined />
                迁移状态概览
              </Space>
            }
            extra={
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={fetchMigrationStatus}
                loading={loading}
              >
                刷新状态
              </Button>
            }
            loading={loading}
          >
            {migrationStatus ? (
              <>
                <Descriptions column={1} bordered size="small" style={{ marginBottom: '16px' }}>
                  <Descriptions.Item label="当前版本">
                    <Tag color="green" icon={<DatabaseOutlined />}>
                      {migrationStatus.currentVersion}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="总迁移数">
                    <Text strong>{migrationStatus.totalMigrations}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="待执行迁移">
                    <Text strong style={{ color: migrationStatus.pendingCount > 0 ? '#ff4d4f' : '#52c41a' }}>
                      {migrationStatus.pendingCount}
                    </Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="迁移进度">
                    <Progress
                      percent={progress.percent}
                      status={progress.status}
                      size="small"
                      format={(percent) => `${percent}%`}
                    />
                  </Descriptions.Item>
                </Descriptions>

                {migrationStatus.hasPendingMigrations && (
                  <Alert
                    message="有待执行的迁移"
                    description={`发现 ${migrationStatus.pendingCount} 个待执行的迁移脚本，建议尽快执行以保持数据库结构最新。`}
                    type="warning"
                    showIcon
                    icon={<WarningOutlined />}
                    style={{ marginTop: '16px' }}
                  />
                )}
              </>
            ) : (
              <Alert
                message="无法获取迁移状态"
                description="请检查服务器连接状态"
                type="error"
                showIcon
              />
            )}
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card
            title={
              <Space>
                <PlayCircleOutlined />
                迁移操作
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Popconfirm
                title="确认执行迁移"
                description="这将执行所有待处理的数据库迁移脚本，请确保已备份数据库。"
                onConfirm={executeMigration}
                okText="确认执行"
                cancelText="取消"
                disabled={!migrationStatus?.hasPendingMigrations || executing}
              >
                <Button
                  type="primary"
                  danger
                  block
                  icon={<PlayCircleOutlined />}
                  loading={executing}
                  disabled={!migrationStatus?.hasPendingMigrations}
                >
                  {executing ? '执行中...' : '执行迁移'}
                </Button>
              </Popconfirm>
              
              <Button
                block
                icon={<ReloadOutlined />}
                onClick={fetchMigrationStatus}
                loading={loading}
              >
                刷新状态
              </Button>
              
              {!migrationStatus?.hasPendingMigrations && (
                <Alert
                  message="所有迁移已完成"
                  type="success"
                  showIcon
                  size="small"
                />
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 待执行迁移 */}
      {migrationStatus?.pendingMigrations?.length > 0 && (
        <Card
          title={
            <Space>
              <WarningOutlined style={{ color: '#faad14' }} />
              待执行迁移
            </Space>
          }
          style={{ marginBottom: '24px' }}
        >
          <Table
            columns={migrationColumns}
            dataSource={migrationStatus.pendingMigrations}
            rowKey="version"
            pagination={false}
            size="small"
          />
        </Card>
      )}

      {/* 所有迁移记录 */}
      <Card
        title={
          <Space>
            <DatabaseOutlined />
            所有迁移记录
          </Space>
        }
        extra={
          <Text type="secondary">
            显示所有迁移脚本的执行状态
          </Text>
        }
      >
        <Table
          columns={migrationColumns}
          dataSource={migrationStatus?.allMigrations || []}
          rowKey="version"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条迁移记录`,
          }}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  );
};

export default DatabaseMigration;
