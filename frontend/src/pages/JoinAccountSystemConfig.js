import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Space,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { systemConfigApi } from '../services/api';

const { Option } = Select;
const { TextArea } = Input;

const JoinAccountSystemConfig = () => {
  const [configs, setConfigs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [form] = Form.useForm();
  const [statistics, setStatistics] = useState({});

  // 获取配置列表
  const fetchConfigs = async (params = {}) => {
    setLoading(true);
    try {
      const response = await systemConfigApi.getConfigs(params);
      if (response.data.success) {
        setConfigs(response.data.data.content || []);
      }
    } catch (error) {
      message.error('获取配置列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await systemConfigApi.getStatistics();
      if (response.data.success) {
        setStatistics(response.data.data);
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  useEffect(() => {
    fetchConfigs();
    fetchStatistics();
  }, []);

  // 处理新增/编辑
  const handleSubmit = async (values) => {
    try {
      if (editingConfig) {
        await systemConfigApi.updateConfig(editingConfig.id, values);
        message.success('配置更新成功');
      } else {
        await systemConfigApi.createConfig(values);
        message.success('配置创建成功');
      }
      setModalVisible(false);
      setEditingConfig(null);
      form.resetFields();
      fetchConfigs();
      fetchStatistics();
    } catch (error) {
      message.error(editingConfig ? '配置更新失败' : '配置创建失败');
    }
  };

  // 处理删除
  const handleDelete = async (id) => {
    try {
      await systemConfigApi.deleteConfig(id);
      message.success('配置删除成功');
      fetchConfigs();
      fetchStatistics();
    } catch (error) {
      message.error('配置删除失败');
    }
  };

  // 处理启用/禁用
  const handleToggleStatus = async (id) => {
    try {
      await systemConfigApi.toggleStatus(id);
      message.success('配置状态更新成功');
      fetchConfigs();
      fetchStatistics();
    } catch (error) {
      message.error('配置状态更新失败');
    }
  };

  // 打开编辑模态框
  const openEditModal = (config) => {
    setEditingConfig(config);
    form.setFieldsValue(config);
    setModalVisible(true);
  };

  // 打开新增模态框
  const openAddModal = () => {
    setEditingConfig(null);
    form.resetFields();
    setModalVisible(true);
  };

  const columns = [
    {
      title: '配置键',
      dataIndex: 'configKey',
      key: 'configKey',
      width: 250,
      render: (text) => (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>{text}</code>
        </Tooltip>
      ),
    },
    {
      title: '配置值',
      dataIndex: 'configValue',
      key: 'configValue',
      width: 200,
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ 
            maxWidth: 180, 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '类型',
      dataIndex: 'configType',
      key: 'configType',
      width: 100,
      render: (type) => {
        const colors = {
          STRING: 'blue',
          NUMBER: 'green',
          BOOLEAN: 'orange',
          JSON: 'purple'
        };
        return <Tag color={colors[type]}>{type}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive, record) => (
        <Switch
          checked={isActive}
          size="small"
          onChange={() => handleToggleStatus(record.id)}
        />
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      render: (time) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => openEditModal(record)}
          />
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总配置数"
              value={statistics.totalCount || 0}
              prefix={<SettingOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="有效配置"
              value={statistics.activeCount || 0}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="无效配置"
              value={statistics.inactiveCount || 0}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Join Account配置"
              value={configs.filter(c => c.configKey.startsWith('join_account')).length}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card
        title={
          <Space>
            <SettingOutlined />
            系统配置管理
            <Tooltip title="管理Join Account Rental系统的配置参数">
              <InfoCircleOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchConfigs();
                fetchStatistics();
              }}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={openAddModal}
            >
              新增配置
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={configs}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingConfig ? '编辑配置' : '新增配置'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingConfig(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="configKey"
            label="配置键"
            rules={[
              { required: true, message: '请输入配置键' },
              { max: 100, message: '配置键长度不能超过100个字符' }
            ]}
          >
            <Input placeholder="例如: join_account.domain.base_url" />
          </Form.Item>

          <Form.Item
            name="configValue"
            label="配置值"
            rules={[{ required: true, message: '请输入配置值' }]}
          >
            <TextArea rows={3} placeholder="请输入配置值" />
          </Form.Item>

          <Form.Item
            name="configType"
            label="配置类型"
            rules={[{ required: true, message: '请选择配置类型' }]}
          >
            <Select placeholder="请选择配置类型">
              <Option value="STRING">字符串</Option>
              <Option value="NUMBER">数字</Option>
              <Option value="BOOLEAN">布尔值</Option>
              <Option value="JSON">JSON对象</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ max: 500, message: '描述长度不能超过500个字符' }]}
          >
            <TextArea rows={2} placeholder="请输入配置描述" />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="是否启用"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingConfig ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default JoinAccountSystemConfig;
