import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  message,
  Space,
  Tag,
  Tooltip,
  Row,
  Col,
  Spin,
  Descriptions,
  Statistic,
  Select,
  DatePicker,
  Popconfirm,
  Divider,
  Typography
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  ExportOutlined,
  StopOutlined,
  LinkOutlined,
  InfoCircleOutlined,
  CopyOutlined,
  EyeOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  RightOutlined,
  DownOutlined
} from '@ant-design/icons';
import { joinAccountTokenApi, joinAccountWindowApi } from '../services/api';
import { useDomainConfig } from '../hooks/useSystemConfig';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Text } = Typography;

const JoinAccountTokenManagement = () => {
  const [tokens, setTokens] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedToken, setSelectedToken] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [form] = Form.useForm();
  const [statistics, setStatistics] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({});
  const [searchForm] = Form.useForm();
  const [searchParams, setSearchParams] = useState({
    tokenNumber: '',
    batchNumber: '',
    status: '',
    exportStatus: ''
  });
  const [isMobileView, setIsMobileView] = useState(false);

  // 使用域名配置Hook
  const { generateJoinAccountLink } = useDomainConfig();

  // 展开行相关状态
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [windowDetails, setWindowDetails] = useState({});
  const [loadingWindowDetails, setLoadingWindowDetails] = useState({});

  // 检测是否为移动端
  const isMobile = () => {
    return window.innerWidth <= 768;
  };

  // 获取令牌列表
  const fetchTokens = async (params = {}) => {
    setLoading(true);
    try {
      // 过滤掉空值的搜索参数
      const filteredSearchParams = Object.entries(searchParams)
        .filter(([key, value]) => value && value.trim() !== '')
        .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

      const queryParams = {
        page: pagination.current - 1,
        size: pagination.pageSize,
        ...filters,
        ...filteredSearchParams,
        ...params,
      };
      
      const response = await joinAccountTokenApi.getTokens(queryParams);
      if (response.data.success) {
        const data = response.data.data;
        setTokens(data.content || []);
        setPagination(prev => ({
          ...prev,
          total: data.totalElements || 0,
        }));
      }
    } catch (error) {
      message.error('获取权益链接列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await joinAccountTokenApi.getStatistics();
      if (response.data.success) {
        setStatistics(response.data.data);
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };



  // 搜索处理
  const handleSearch = (values) => {
    setSearchParams(values);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setSearchParams({
      tokenNumber: '',
      batchNumber: '',
      status: '',
      exportStatus: ''
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  useEffect(() => {
    fetchTokens();
    fetchStatistics();
  }, [pagination.current, pagination.pageSize, filters, searchParams]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    // 初始检测
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 批量生成权益链接
  const handleBatchGenerate = async (values) => {
    try {
      const response = await joinAccountTokenApi.batchGenerate(values);
      if (response.data.success) {
        message.success(`成功生成 ${response.data.generatedCount} 个权益链接`);
        setModalVisible(false);
        form.resetFields();
        fetchTokens();
        fetchStatistics();
      }
    } catch (error) {
      message.error('批量生成权益链接失败');
    }
  };

  // 批量导出Excel文件
  const handleBatchExport = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要导出的权益链接');
      return;
    }

    try {
      const response = await joinAccountTokenApi.exportToExcel(selectedRowKeys, 'ADMIN');

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const fileName = `权益链接_JAR_BATCH_${timestamp}.xlsx`;
      link.setAttribute('download', fileName);

      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      message.success(`成功导出 ${selectedRowKeys.length} 个权益链接`);
      setSelectedRowKeys([]);
      fetchTokens();
      fetchStatistics();
    } catch (error) {
      message.error('批量导出失败');
      console.error('导出失败:', error);
    }
  };

  // 批量作废
  const handleBatchCancel = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要作废的权益链接');
      return;
    }

    Modal.confirm({
      title: '确认作废',
      content: `确定要作废选中的 ${selectedRowKeys.length} 个权益链接吗？`,
      onOk: async () => {
        try {
          const response = await joinAccountTokenApi.cancelTokens(selectedRowKeys, {
            operator: 'ADMIN',
            reason: '批量作废操作'
          });
          if (response.data.success) {
            message.success(`成功作废 ${response.data.cancelledCount} 个权益链接`);
            setSelectedRowKeys([]);
            fetchTokens();
            fetchStatistics();
          }
        } catch (error) {
          message.error('批量作废失败');
        }
      },
    });
  };

  // 复制链接
  const handleCopyLink = async (tokenNumber) => {
    try {
      // 使用用户前端域名生成链接
      const link = generateJoinAccountLink(tokenNumber);
      await navigator.clipboard.writeText(link);
      message.success('链接已复制到剪贴板');
    } catch (error) {
      message.error('复制链接失败');
    }
  };

  // 获取窗口详情
  const fetchWindowDetails = async (tokenId, tokenNumber) => {
    if (windowDetails[tokenId] || loadingWindowDetails[tokenId]) {
      return;
    }

    setLoadingWindowDetails(prev => ({ ...prev, [tokenId]: true }));
    try {
      // 调用获取窗口详情的API
      const response = await joinAccountWindowApi.getWindowByToken(tokenNumber);
      if (response.data.success) {
        setWindowDetails(prev => ({ ...prev, [tokenId]: response.data.data }));
      } else {
        setWindowDetails(prev => ({ ...prev, [tokenId]: null }));
      }
    } catch (error) {
      console.error('获取窗口详情失败:', error);
      setWindowDetails(prev => ({ ...prev, [tokenId]: null }));
    } finally {
      setLoadingWindowDetails(prev => ({ ...prev, [tokenId]: false }));
    }
  };

  // 渲染展开行内容
  const renderExpandedRow = (record) => {
    const tokenId = record.token.id;
    const windowDetail = windowDetails[tokenId];
    const isLoading = loadingWindowDetails[tokenId];

    // 如果没有窗口信息，不显示展开内容
    if (!record.window) {
      return (
        <div style={{ padding: '16px', color: '#999' }}>
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          暂无窗口信息
        </div>
      );
    }

    // 如果正在加载，显示加载状态
    if (isLoading) {
      return (
        <div style={{ padding: '16px', textAlign: 'center' }}>
          <Spin size="small" />
          <span style={{ marginLeft: 8 }}>加载窗口详情中...</span>
        </div>
      );
    }

    // 使用从API获取的窗口详情数据，如果没有则使用record.window作为备用
    const windowData = windowDetail || record.window;

    return (
      <div style={{ padding: '16px', backgroundColor: '#fafafa' }}>
        <Descriptions title="窗口详情" bordered size="small" column={2}>
          <Descriptions.Item label="窗口ID">{windowData.id}</Descriptions.Item>
          <Descriptions.Item label="Token编号">{record.token.tokenNumber}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {windowData.createdAt ? new Date(windowData.createdAt).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="窗口状态">
            <Tag color={windowData.status === 'ACTIVE' ? 'green' : windowData.status === 'PENDING' ? 'orange' : 'default'}>
              {windowData.status === 'ACTIVE' ? '使用中' : windowData.status === 'PENDING' ? '待开启' : '已关闭'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="开始时间">
            {windowData.startTime ? new Date(windowData.startTime).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="结束时间">
            {windowData.endTime ? new Date(windowData.endTime).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="实际开启时间">
            {windowData.openedAt ? new Date(windowData.openedAt).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="实际关闭时间">
            {windowData.closedAt ? new Date(windowData.closedAt).toLocaleString() : '-'}
          </Descriptions.Item>
        </Descriptions>
      </div>
    );
  };

  // 查看详情
  const handleViewDetail = async (token) => {
    try {
      const response = await joinAccountTokenApi.getTokenById(token.id);
      if (response.data.success) {
        setSelectedToken(response.data.data);
        setDetailModalVisible(true);
      }
    } catch (error) {
      message.error('获取详情失败');
    }
  };

  // 状态标签颜色
  const getStatusColor = (status) => {
    const colors = {
      PENDING: 'default',
      EXPORTED: 'blue',
      RESERVED: 'orange',
      ACTIVE: 'green',
      COMPLETED: 'purple',
      CANCELLED: 'red'
    };
    return colors[status] || 'default';
  };

  // 状态描述
  const getStatusText = (status) => {
    const texts = {
      PENDING: '待使用',
      EXPORTED: '已导出',
      RESERVED: '已预约',
      ACTIVE: '使用中',
      COMPLETED: '已完成',
      CANCELLED: '已作废'
    };
    return texts[status] || status;
  };

  const columns = [
    {
      title: 'Token编号',
      dataIndex: ['token', 'tokenNumber'],
      key: 'tokenNumber',
      width: 120,
      render: (text) => (
        <a
          href={generateJoinAccountLink(text)}
          target="_blank"
          rel="noopener noreferrer"
          style={{ fontFamily: 'monospace' }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '批次号',
      dataIndex: ['token', 'batchNumber'],
      key: 'batchNumber',
      width: 250,
      ellipsis: true,
      render: (batchNumber) => {
        if (!batchNumber) return '-';
        return (
          <Button
            type="link"
            onClick={() => {
              setSearchParams(prev => ({ ...prev, batchNumber }));
              searchForm.setFieldsValue({ batchNumber });
              fetchTokens({ batchNumber });
            }}
            style={{ padding: 0, height: 'auto', color: '#1890ff' }}
          >
            {batchNumber}
          </Button>
        );
      },
    },
    {
      title: '使用天数',
      dataIndex: ['token', 'usageDays'],
      key: 'usageDays',
      width: 80,
      render: (days) => `${days}天`,
    },
    {
      title: '状态',
      dataIndex: ['token', 'status'],
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '导出状态',
      dataIndex: ['token', 'exportStatus'],
      key: 'exportStatus',
      width: 100,
      render: (status) => (
        <Tag color={status === 'EXPORTED' ? 'blue' : 'default'}>
          {status === 'EXPORTED' ? '已导出' : '未导出'}
        </Tag>
      ),
    },
    {
      title: '分配账号',
      dataIndex: ['token', 'assignedZoomUserEmail'],
      key: 'assignedZoomUserEmail',
      width: 180,
      ellipsis: true,
      render: (email, record) => {
        if (!email) return '-';
        const zoomUserId = record.token.assignedZoomUserId;
        if (zoomUserId) {
          return (
            <a
              href={`http://localhost:3000/zoom-users/${zoomUserId}`}
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: '#1890ff', textDecoration: 'none' }}
            >
              {email}
            </a>
          );
        }
        return email;
      },
    },
    {
      title: '分配密码',
      dataIndex: ['token', 'assignedPassword'],
      key: 'assignedPassword',
      width: 120,
      render: (password) => {
        if (!password) return '-';
        return (
          <Space>
            <Text code style={{ fontSize: '12px' }}>
              {password}
            </Text>
            <Button
              type="link"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => {
                navigator.clipboard.writeText(password);
                message.success('密码已复制');
              }}
              style={{ padding: '0 4px' }}
            />
          </Space>
        );
      },
    },
    {
      title: '使用窗口',
      key: 'window',
      width: 200,
      render: (_, record) => {
        if (record.token?.windowStartTime && record.token?.windowEndTime) {
          return (
            <div>
              <div>{new Date(record.token.windowStartTime).toLocaleString()}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                至 {new Date(record.token.windowEndTime).toLocaleString()}
              </div>
            </div>
          );
        }
        return '-';
      },
    },
    {
      title: '窗口状态',
      key: 'windowStatus',
      width: 120,
      render: (_, record) => {
        if (!record.window) {
          return <Tag color="default">无窗口</Tag>;
        }

        const { status, errorMessage, lastOperationError } = record.window;
        const hasError = errorMessage || lastOperationError;

        let color, icon, text;
        switch (status) {
          case 'PENDING':
            color = hasError ? 'red' : 'orange';
            icon = hasError ? <ExclamationCircleOutlined /> : <ClockCircleOutlined />;
            text = hasError ? '待开启(错误)' : '待开启';
            break;
          case 'ACTIVE':
            color = hasError ? 'red' : 'green';
            icon = hasError ? <ExclamationCircleOutlined /> : <CheckCircleOutlined />;
            text = hasError ? '使用中(错误)' : '使用中';
            break;
          case 'CLOSED':
            color = hasError ? 'red' : 'default';
            icon = hasError ? <ExclamationCircleOutlined /> : <CheckCircleOutlined />;
            text = hasError ? '已关闭(错误)' : '已关闭';
            break;
          default:
            color = 'default';
            icon = <ClockCircleOutlined />;
            text = status;
        }

        return (
          <Tag color={color} icon={icon}>
            {text}
          </Tag>
        );
      },
    },
    {
      title: '窗口错误',
      key: 'windowError',
      width: 150,
      render: (_, record) => {
        if (!record.window) {
          return '-';
        }

        const { errorMessage, lastOperationError } = record.window;
        if (!errorMessage && !lastOperationError) {
          return <Tag color="green">正常</Tag>;
        }

        return (
          <div>
            {lastOperationError && (
              <Tooltip title={lastOperationError}>
                <Tag color="red" style={{ marginBottom: 4, cursor: 'pointer' }}>
                  最新错误
                </Tag>
              </Tooltip>
            )}
            {errorMessage && errorMessage !== lastOperationError && (
              <Tooltip title={errorMessage}>
                <Tag color="orange" style={{ cursor: 'pointer' }}>
                  历史错误
                </Tag>
              </Tooltip>
            )}
          </div>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: ['token', 'createdAt'],
      key: 'createdAt',
      width: 150,
      render: (time) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record.token)}
            />
          </Tooltip>
          <Tooltip title="复制链接">
            <Button
              type="link"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopyLink(record.token.tokenNumber)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.token?.status === 'CANCELLED',
    }),
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="总数量"
              value={statistics.totalCount || 0}
              prefix={<LinkOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="待使用"
              value={statistics.statusStats?.PENDING || 0}
              valueStyle={{ color: '#666' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="已导出"
              value={statistics.statusStats?.EXPORTED || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="使用中"
              value={statistics.statusStats?.ACTIVE || 0}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="已完成"
              value={statistics.statusStats?.COMPLETED || 0}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="已作废"
              value={statistics.statusStats?.CANCELLED || 0}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form
          form={searchForm}
          onFinish={handleSearch}
        >
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Form.Item name="tokenNumber" label="Token编号" style={{ marginBottom: 0 }}>
                <Input placeholder="请输入Token编号" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="batchNumber" label="批次号" style={{ marginBottom: 0 }}>
                <Input placeholder="请输入批次号" />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="status" label="状态" style={{ marginBottom: 0 }}>
                <Select placeholder="请选择状态" allowClear>
                  <Option value="PENDING">待使用</Option>
                  <Option value="EXPORTED">已导出</Option>
                  <Option value="RESERVED">已预约</Option>
                  <Option value="ACTIVE">使用中</Option>
                  <Option value="COMPLETED">已完成</Option>
                  <Option value="CANCELLED">已作废</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="exportStatus" label="导出状态" style={{ marginBottom: 0 }}>
                <Select placeholder="请选择导出状态" allowClear>
                  <Option value="EXPORTED">已导出</Option>
                  <Option value="NOT_EXPORTED">未导出</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item style={{ marginBottom: 0 }}>
                <Space>
                  <Button type="primary" htmlType="submit">
                    搜索
                  </Button>
                  <Button onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 主要内容 */}
      <Card
        title={
          <Space>
            <LinkOutlined />
            权益链接管理
            <Tooltip title="管理Join Account Rental的权益链接">
              <InfoCircleOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchTokens();
                fetchStatistics();
              }}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setModalVisible(true)}
            >
              批量生成
            </Button>
          </Space>
        }
      >
        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16, padding: 16, background: '#f0f2f5', borderRadius: 6 }}>
            <Space>
              <span>已选择 {selectedRowKeys.length} 项</span>
              <Divider type="vertical" />
              <Button
                size="small"
                icon={<ExportOutlined />}
                onClick={handleBatchExport}
              >
                批量导出
              </Button>
              <Button
                size="small"
                danger
                icon={<StopOutlined />}
                onClick={handleBatchCancel}
              >
                批量作废
              </Button>
              <Button
                size="small"
                onClick={() => setSelectedRowKeys([])}
              >
                取消选择
              </Button>
            </Space>
          </div>
        )}

        <Table
          columns={columns}
          dataSource={tokens}
          rowKey={(record) => record.token?.id}
          loading={loading}
          rowSelection={rowSelection}
          scroll={{ x: 1400 }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({ ...prev, current: page, pageSize }));
            },
          }}
          expandable={{
            expandedRowKeys,
            onExpand: (expanded, record) => {
              if (expanded) {
                setExpandedRowKeys([...expandedRowKeys, record.token.id]);
                if (record.window) {
                  fetchWindowDetails(record.token.id, record.token.tokenNumber);
                }
              } else {
                setExpandedRowKeys(expandedRowKeys.filter(key => key !== record.token.id));
              }
            },
            expandedRowRender: renderExpandedRow,
            expandIcon: ({ expanded, onExpand, record }) => (
              <Tooltip title={expanded ? '收起窗口详情' : '展开窗口详情'}>
                <Button
                  type="text"
                  size="small"
                  icon={expanded ? <DownOutlined /> : <RightOutlined />}
                  onClick={e => onExpand(record, e)}
                  disabled={!record.window}
                />
              </Tooltip>
            ),
            rowExpandable: (record) => !!record.window,
          }}
        />
      </Card>

      {/* 批量生成模态框 */}
      <Modal
        title="批量生成权益链接"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleBatchGenerate}
        >
          <Form.Item
            name="usageDays"
            label="使用天数"
            rules={[
              { required: true, message: '请输入使用天数' },
              { type: 'number', min: 1, max: 365, message: '使用天数必须在1-365之间' }
            ]}
          >
            <InputNumber
              min={1}
              max={365}
              placeholder="请输入使用天数"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="quantity"
            label="生成数量"
            rules={[
              { required: true, message: '请输入生成数量' },
              { type: 'number', min: 1, max: 1000, message: '生成数量必须在1-1000之间' }
            ]}
          >
            <InputNumber
              min={1}
              max={1000}
              placeholder="请输入生成数量"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea rows={3} placeholder="请输入备注信息（可选）" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                生成
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 详情模态框 */}
      <Modal
        title="权益链接详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedToken && (
          <div>
            <p><strong>Token编号:</strong> {selectedToken.token?.tokenNumber || '-'}</p>
            <p><strong>批次号:</strong> {selectedToken.token?.batchNumber || '-'}</p>
            <p><strong>使用天数:</strong> {selectedToken.token?.usageDays ? `${selectedToken.token.usageDays}天` : '-'}</p>
            <p><strong>状态:</strong> {selectedToken.token?.status ? getStatusText(selectedToken.token.status) : '-'}</p>
            <p><strong>导出状态:</strong> {selectedToken.token?.exportStatus === 'EXPORTED' ? '已导出' : '未导出'}</p>
            <p><strong>分配账号:</strong> {selectedToken.token?.assignedZoomUserEmail || '未分配'}</p>
            <p><strong>分配密码:</strong> {selectedToken.token?.assignedPassword || '未分配'}</p>
            <p><strong>创建时间:</strong> {selectedToken.token?.createdAt ? new Date(selectedToken.token.createdAt).toLocaleString() : '-'}</p>
            <p><strong>导出时间:</strong> {selectedToken.token?.exportedAt ? new Date(selectedToken.token.exportedAt).toLocaleString() : '-'}</p>
            <p><strong>权益链接:</strong> {selectedToken.token?.tokenNumber ? <a href={generateJoinAccountLink(selectedToken.token.tokenNumber)} target="_blank" rel="noopener noreferrer">{generateJoinAccountLink(selectedToken.token.tokenNumber)}</a> : '-'}</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default JoinAccountTokenManagement;
