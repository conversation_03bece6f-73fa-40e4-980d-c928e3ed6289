import React, { useState, useEffect } from 'react';
import {
    Table,
    Card,
    Button,
    Input,
    Select,
    DatePicker,
    Space,
    Tag,
    message,
    Row,
    Col,
    Tooltip,
    Typography
} from 'antd';
import {
    SearchOutlined,
    EyeOutlined,
    DownloadOutlined,
    ReloadOutlined,
    SyncOutlined,
    CalendarOutlined,
    UserOutlined,
    ClockCircleOutlined
} from '@ant-design/icons';
import { meetingReportApi } from '../services/api';
import MeetingReportModal from '../components/MeetingReportModal';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Title } = Typography;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const MeetingReportList = () => {
    const [loading, setLoading] = useState(false);
    const [exportLoading, setExportLoading] = useState(false);
    const [reports, setReports] = useState([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0
    });
    const [filters, setFilters] = useState({
        keyword: '',
        hostId: '',
        pmiRecordId: '',
        dateRange: null,
        fetchStatus: ''
    });
    const [isMobileView, setIsMobileView] = useState(isMobile());
    const [reportModalVisible, setReportModalVisible] = useState(false);
    const [currentMeeting, setCurrentMeeting] = useState(null);

    // 获取会议报告列表
    const fetchReports = async (page = 1, size = 20) => {
        setLoading(true);
        try {
            const params = {
                page: page - 1,
                size,
                ...filters
            };

            if (filters.dateRange && filters.dateRange.length === 2) {
                params.startTime = filters.dateRange[0].startOf('day').toISOString();
                params.endTime = filters.dateRange[1].endOf('day').toISOString();
            }

            const response = await meetingReportApi.getReports(params);
            
            if (response.data) {
                setReports(response.data.content || []);
                setPagination({
                    current: page,
                    pageSize: size,
                    total: response.data.totalElements || 0
                });
            }
        } catch (error) {
            console.error('获取会议报告列表失败:', error);
            message.error('获取会议报告列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 查看会议报告详情
    const handleViewReport = (report) => {
        // 构造会议对象
        const meeting = {
            zoomMeetingUuid: report.zoomMeetingUuid,
            zoomMeetingId: report.zoomMeetingId,
            topic: report.topic
        };
        setCurrentMeeting(meeting);
        setReportModalVisible(true);
    };

    // 手动触发报告获取
    const handleTriggerFetch = async (report) => {
        try {
            setLoading(true);
            const response = await meetingReportApi.triggerReportFetch(report.zoomMeetingUuid);
            
            if (response.data && response.data.success) {
                message.success('会议报告获取已触发');
                // 刷新列表
                setTimeout(() => {
                    fetchReports(pagination.current, pagination.pageSize);
                }, 2000);
            } else {
                message.error(response.data?.message || '触发报告获取失败');
            }
        } catch (error) {
            console.error('触发报告获取失败:', error);
            message.error('触发报告获取失败');
        } finally {
            setLoading(false);
        }
    };

    // 导出报告
    const handleExport = async () => {
        setExportLoading(true);
        try {
            const params = { ...filters };
            
            if (filters.dateRange && filters.dateRange.length === 2) {
                params.startTime = filters.dateRange[0].startOf('day').toISOString();
                params.endTime = filters.dateRange[1].endOf('day').toISOString();
            }

            const response = await meetingReportApi.exportReports(params);
            
            // 创建下载链接
            const blob = new Blob([response.data], { 
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
            });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            
            // 生成文件名
            const dateStr = filters.dateRange && filters.dateRange.length === 2 ? 
                `${filters.dateRange[0].format('YYYY-MM-DD')}_${filters.dateRange[1].format('YYYY-MM-DD')}` : 
                dayjs().format('YYYY-MM-DD');
            link.download = `会议报告列表_${dateStr}.xlsx`;
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            message.success('报告导出成功');
        } catch (error) {
            console.error('导出报告失败:', error);
            message.error('导出报告失败');
        } finally {
            setExportLoading(false);
        }
    };

    // 处理筛选条件变化
    const handleFilterChange = () => {
        fetchReports(1, pagination.pageSize);
    };

    // 重置筛选条件
    const handleReset = () => {
        setFilters({
            keyword: '',
            hostId: '',
            pmiRecordId: '',
            dateRange: null,
            fetchStatus: ''
        });
        setTimeout(() => {
            fetchReports(1, pagination.pageSize);
        }, 100);
    };

    // 表格列配置
    const columns = [
        {
            title: '会议主题',
            dataIndex: 'topic',
            key: 'topic',
            ellipsis: true,
            render: (text) => text || '无主题'
        },
        {
            title: '会议ID',
            dataIndex: 'zoomMeetingId',
            key: 'zoomMeetingId',
            width: 120,
            render: (text) => (
                <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
                    {text}
                </span>
            )
        },
        {
            title: '开始时间',
            dataIndex: 'startTime',
            key: 'startTime',
            width: 160,
            render: (time) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-'
        },
        {
            title: '时长',
            dataIndex: 'durationMinutes',
            key: 'duration',
            width: 80,
            render: (minutes) => (
                <span>
                    <ClockCircleOutlined style={{ marginRight: '4px', color: '#666' }} />
                    {minutes || 0}分钟
                </span>
            )
        },
        {
            title: '参会人数',
            dataIndex: 'totalParticipants',
            key: 'participants',
            width: 100,
            render: (count) => (
                <span>
                    <UserOutlined style={{ marginRight: '4px', color: '#666' }} />
                    {count || 0}人
                </span>
            )
        },
        {
            title: '获取状态',
            dataIndex: 'fetchStatus',
            key: 'fetchStatus',
            width: 100,
            render: (status) => {
                const statusConfig = {
                    'SUCCESS': { color: 'green', text: '成功' },
                    'PENDING': { color: 'blue', text: '处理中' },
                    'FAILED': { color: 'red', text: '失败' }
                };
                const config = statusConfig[status] || { color: 'default', text: '未知' };
                return <Tag color={config.color}>{config.text}</Tag>;
            }
        },
        {
            title: '操作',
            key: 'action',
            width: isMobileView ? 100 : 150,
            fixed: isMobileView ? false : 'right',
            render: (_, record) => (
                <Space size="small">
                    <Tooltip title="查看详情">
                        <Button
                            type="link"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => handleViewReport(record)}
                        />
                    </Tooltip>
                    {record.fetchStatus !== 'SUCCESS' && (
                        <Tooltip title="重新获取">
                            <Button
                                type="link"
                                size="small"
                                icon={<SyncOutlined />}
                                onClick={() => handleTriggerFetch(record)}
                            />
                        </Tooltip>
                    )}
                </Space>
            )
        }
    ];

    // 监听窗口大小变化
    useEffect(() => {
        const handleResize = () => {
            setIsMobileView(isMobile());
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // 初始加载
    useEffect(() => {
        fetchReports();
    }, []);

    return (
        <div style={{ padding: isMobileView ? '12px' : '24px' }}>
            <div style={{ marginBottom: '24px' }}>
                <Title level={isMobileView ? 4 : 3}>
                    <CalendarOutlined style={{ marginRight: '8px' }} />
                    会议报告管理
                </Title>
            </div>

            {/* 筛选条件 */}
            <Card size="small" style={{ marginBottom: '24px' }}>
                <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12} md={6}>
                        <Input
                            placeholder="搜索会议主题或ID"
                            prefix={<SearchOutlined />}
                            value={filters.keyword}
                            onChange={(e) => setFilters({ ...filters, keyword: e.target.value })}
                            allowClear
                        />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                        <RangePicker
                            value={filters.dateRange}
                            onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
                            style={{ width: '100%' }}
                            format="YYYY-MM-DD"
                        />
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                        <Select
                            placeholder="获取状态"
                            value={filters.fetchStatus}
                            onChange={(value) => setFilters({ ...filters, fetchStatus: value })}
                            style={{ width: '100%' }}
                            allowClear
                        >
                            <Option value="SUCCESS">成功</Option>
                            <Option value="PENDING">处理中</Option>
                            <Option value="FAILED">失败</Option>
                        </Select>
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                        <Space style={{ width: '100%' }}>
                            <Button
                                type="primary"
                                icon={<SearchOutlined />}
                                onClick={handleFilterChange}
                                loading={loading}
                            >
                                查询
                            </Button>
                            <Button
                                icon={<ReloadOutlined />}
                                onClick={handleReset}
                            >
                                重置
                            </Button>
                            <Button
                                icon={<DownloadOutlined />}
                                onClick={handleExport}
                                loading={exportLoading}
                            >
                                导出
                            </Button>
                        </Space>
                    </Col>
                </Row>
            </Card>

            {/* 报告列表 */}
            <Card>
                <Table
                    columns={columns}
                    dataSource={reports}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                        ...pagination,
                        onChange: fetchReports,
                        showSizeChanger: !isMobileView,
                        showQuickJumper: !isMobileView,
                        showTotal: (total) => `共 ${total} 条记录`,
                        size: isMobileView ? 'small' : 'default'
                    }}
                    scroll={{ x: isMobileView ? 600 : 'auto' }}
                    size={isMobileView ? 'small' : 'middle'}
                />
            </Card>

            {/* 会议报告详情弹窗 */}
            <MeetingReportModal
                visible={reportModalVisible}
                onCancel={() => {
                    setReportModalVisible(false);
                    setCurrentMeeting(null);
                }}
                meeting={currentMeeting}
                isMobileView={isMobileView}
            />
        </div>
    );
};

export default MeetingReportList;
