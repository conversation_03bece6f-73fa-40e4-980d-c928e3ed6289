import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Tag,
  Space,
  message,
  Descriptions,
  Alert,
  Input,
  Modal,
  Typography
} from 'antd';
import {
  ReloadOutlined,
  SettingOutlined,
  ClearOutlined,
  GlobalOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import api from '../services/api';

const { Title, Text } = Typography;

const NetworkEnvironment = () => {
  const [loading, setLoading] = useState(false);
  const [environment, setEnvironment] = useState(null);
  const [testUrl, setTestUrl] = useState('https://api.zoom.us');
  const [testResults, setTestResults] = useState({});

  // 加载网络环境信息
  const loadEnvironment = async () => {
    setLoading(true);
    try {
      const response = await api.get('/network/environment');
      setEnvironment(response.data);
    } catch (error) {
      message.error('加载网络环境信息失败');
      console.error('加载网络环境信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 检测外网IP
  const detectExternalIp = async () => {
    setLoading(true);
    try {
      const response = await api.post('/network/detect-ip');
      if (response.data.success) {
        message.success(`外网IP检测成功: ${response.data.externalIp}`);
        loadEnvironment();
      } else {
        message.error(response.data.message);
      }
    } catch (error) {
      message.error('检测外网IP失败');
      console.error('检测外网IP失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 配置代理
  const configureProxy = async () => {
    setLoading(true);
    try {
      const response = await api.post('/network/configure-proxy');
      if (response.data.success) {
        message.success('代理配置成功');
        loadEnvironment();
      } else {
        message.error(response.data.message);
      }
    } catch (error) {
      message.error('配置代理失败');
      console.error('配置代理失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 清除代理
  const clearProxy = async () => {
    Modal.confirm({
      title: '确认清除代理配置',
      content: '确定要清除当前的代理配置吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const response = await api.post('/network/clear-proxy');
          if (response.data.success) {
            message.success('代理配置已清除');
            loadEnvironment();
          } else {
            message.error(response.data.message);
          }
        } catch (error) {
          message.error('清除代理配置失败');
          console.error('清除代理配置失败:', error);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 测试网络连接
  const testConnection = async (url) => {
    setLoading(true);
    try {
      const response = await api.post(`/network/test-connection?url=${encodeURIComponent(url)}`);
      const result = {
        url,
        connected: response.data.connected,
        message: response.data.message
      };
      setTestResults(prev => ({ ...prev, [url]: result }));
      
      if (response.data.connected) {
        message.success(`连接测试成功: ${url}`);
      } else {
        message.warning(`连接测试失败: ${url}`);
      }
    } catch (error) {
      message.error('网络连接测试失败');
      console.error('网络连接测试失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 测试Zoom API连接
  const testZoomApi = async () => {
    setLoading(true);
    try {
      const response = await api.post('/network/test-zoom-api');
      if (response.data.success) {
        setTestResults(prev => ({ ...prev, zoom: response.data.results }));
        if (response.data.allConnected) {
          message.success('Zoom API连接测试成功');
        } else {
          message.warning('部分Zoom服务连接失败');
        }
      } else {
        message.error(response.data.message);
      }
    } catch (error) {
      message.error('Zoom API连接测试失败');
      console.error('Zoom API连接测试失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadEnvironment();
  }, []);

  const getProxyStatus = () => {
    if (!environment) return null;
    
    if (environment.proxyConfigured) {
      return <Tag color="green" icon={<CheckCircleOutlined />}>已配置</Tag>;
    } else {
      return <Tag color="red" icon={<CloseCircleOutlined />}>未配置</Tag>;
    }
  };

  const renderSystemProxyTable = () => {
    if (!environment?.systemProxy) return null;

    const dataSource = Object.entries(environment.systemProxy)
      .filter(([key, value]) => value !== null)
      .map(([key, value]) => ({
        key,
        property: key,
        value: value || '-'
      }));

    const columns = [
      {
        title: '属性',
        dataIndex: 'property',
        key: 'property',
        width: 200
      },
      {
        title: '值',
        dataIndex: 'value',
        key: 'value'
      }
    ];

    return (
      <Table
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        size="small"
      />
    );
  };

  const renderTestResults = () => {
    if (Object.keys(testResults).length === 0) return null;

    return (
      <Card title="连接测试结果" style={{ marginTop: 16 }}>
        {Object.entries(testResults).map(([key, result]) => {
          if (key === 'zoom') {
            return (
              <div key={key} style={{ marginBottom: 16 }}>
                <Title level={5}>Zoom API 测试结果</Title>
                {Object.entries(result).map(([url, connected]) => (
                  <div key={url} style={{ marginBottom: 8 }}>
                    <Tag color={connected ? 'green' : 'red'} icon={connected ? <CheckCircleOutlined /> : <CloseCircleOutlined />}>
                      {url}
                    </Tag>
                  </div>
                ))}
              </div>
            );
          } else {
            return (
              <div key={key} style={{ marginBottom: 8 }}>
                <Tag color={result.connected ? 'green' : 'red'} icon={result.connected ? <CheckCircleOutlined /> : <CloseCircleOutlined />}>
                  {result.url} - {result.message}
                </Tag>
              </div>
            );
          }
        })}
      </Card>
    );
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>网络环境管理</Title>
      
      {environment?.currentExternalIp === '**************' && (
        <Alert
          message="检测到目标开发环境"
          description="当前外网IP为 **************，建议配置代理以确保Zoom API正常访问"
          type="warning"
          icon={<ExclamationCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      )}

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card 
            title="网络环境信息" 
            extra={
              <Button 
                icon={<ReloadOutlined />} 
                onClick={loadEnvironment}
                loading={loading}
              >
                刷新
              </Button>
            }
          >
            {environment && (
              <Descriptions column={2}>
                <Descriptions.Item label="当前外网IP">
                  <Text strong>{environment.currentExternalIp || '未知'}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="代理状态">
                  {getProxyStatus()}
                </Descriptions.Item>
                <Descriptions.Item label="代理信息" span={2}>
                  {environment.proxyInfo}
                </Descriptions.Item>
              </Descriptions>
            )}
          </Card>
        </Col>

        <Col span={24}>
          <Card title="操作">
            <Space wrap>
              <Button 
                icon={<GlobalOutlined />} 
                onClick={detectExternalIp}
                loading={loading}
              >
                检测外网IP
              </Button>
              <Button 
                type="primary"
                icon={<SettingOutlined />} 
                onClick={configureProxy}
                loading={loading}
              >
                配置代理
              </Button>
              <Button 
                danger
                icon={<ClearOutlined />} 
                onClick={clearProxy}
                loading={loading}
              >
                清除代理
              </Button>
              <Button 
                icon={<CheckCircleOutlined />} 
                onClick={testZoomApi}
                loading={loading}
              >
                测试Zoom API
              </Button>
            </Space>
          </Card>
        </Col>

        <Col span={24}>
          <Card title="自定义连接测试">
            <Space.Compact style={{ width: '100%' }}>
              <Input
                placeholder="输入要测试的URL"
                value={testUrl}
                onChange={(e) => setTestUrl(e.target.value)}
                onPressEnter={() => testConnection(testUrl)}
              />
              <Button 
                type="primary"
                onClick={() => testConnection(testUrl)}
                loading={loading}
                disabled={!testUrl}
              >
                测试连接
              </Button>
            </Space.Compact>
          </Card>
        </Col>

        {environment?.systemProxy && (
          <Col span={24}>
            <Card title="系统代理配置">
              {renderSystemProxyTable()}
            </Card>
          </Col>
        )}

        {renderTestResults()}
      </Row>
    </div>
  );
};

export default NetworkEnvironment;
