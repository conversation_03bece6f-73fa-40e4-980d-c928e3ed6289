import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
    Card,
    Table,
    Button,
    Input,
    Select,
    DatePicker,
    Space,
    Tag,
    Statistic,
    Row,
    Col,
    Modal,
    message,
    Tabs,
    Tooltip,
    Badge,
    Form,
    InputNumber,
    Descriptions
} from 'antd';
import { 
    SearchOutlined, 
    ReloadOutlined, 
    DollarOutlined,
    SettingOutlined,
    MonitorOutlined,
    SyncOutlined,
    UserOutlined,
    ClockCircleOutlined
} from '@ant-design/icons';
import api from '../services/api';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const PmiBillingManagement = () => {
    const location = useLocation();
    const navigate = useNavigate();

    // 时长格式化函数：将分钟数转换为 *小时*分钟 格式
    const formatDuration = (minutes) => {
        if (!minutes || minutes === 0) return '0分钟';

        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;

        if (hours === 0) {
            return `${remainingMinutes}分钟`;
        } else if (remainingMinutes === 0) {
            return `${hours}小时`;
        } else {
            return `${hours}小时${remainingMinutes}分钟`;
        }
    };
    const [loading, setLoading] = useState(false);
    const [billingRecords, setBillingRecords] = useState([]);
    const [monitorStatus, setMonitorStatus] = useState({});
    const [zoomUserStats, setZoomUserStats] = useState({});
    const [billingStats, setBillingStats] = useState([]);
    const [filters, setFilters] = useState({
        pmiRecordId: '',
        pmiNumber: '', // 添加PMI号码筛选
        userId: '',
        transactionType: '',
        status: '',
        dateRange: null
    });
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });
    const [rechargeModalVisible, setRechargeModalVisible] = useState(false);
    const [rechargeForm] = Form.useForm();
    const [selectedPmiRecord, setSelectedPmiRecord] = useState(null);

    // 从URL参数获取PMI筛选
    useEffect(() => {
        const urlParams = new URLSearchParams(location.search);
        const pmiParam = urlParams.get('pmi');
        if (pmiParam) {
            setFilters(prev => ({ ...prev, pmiNumber: pmiParam }));
        }
    }, [location.search]);

    // 获取计费记录
    const fetchBillingRecords = async (page = 1, size = 10) => {
        setLoading(true);
        try {
            const params = {
                page: page - 1,
                size,
                ...filters
            };
            
            if (filters.dateRange) {
                params.startDate = filters.dateRange[0].toISOString();
                params.endDate = filters.dateRange[1].toISOString();
            }
            
            const response = await api.get('/pmi-billing/records', { params });
            console.log('🔍 PMI计费记录API响应:', response.data);
            if (response.data.success) {
                const records = response.data.data.content || [];
                console.log('📊 计费记录数据:', records);
                console.log('📋 第一条记录详情:', records[0]);
                setBillingRecords(records);
                setPagination({
                    current: page,
                    pageSize: size,
                    total: response.data.data.totalElements
                });
            }
        } catch (error) {
            message.error('获取计费记录失败');
            console.error('获取计费记录失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 获取监控状态
    const fetchMonitorStatus = async () => {
        try {
            const response = await api.get('/pmi-billing/monitor/status');
            if (response.data.success) {
                setMonitorStatus(response.data.data);
            }
        } catch (error) {
            console.error('获取监控状态失败:', error);
        }
    };

    // 获取ZoomUser统计
    const fetchZoomUserStats = async () => {
        try {
            const response = await api.get('/pmi-billing/zoom-users/stats');
            if (response.data.success) {
                setZoomUserStats(response.data.data);
            }
        } catch (error) {
            console.error('获取ZoomUser统计失败:', error);
        }
    };

    // 获取计费统计
    const fetchBillingStats = async () => {
        try {
            const response = await api.get('/pmi-billing/statistics');
            if (response.data.success) {
                setBillingStats(response.data.data);
            }
        } catch (error) {
            console.error('获取计费统计失败:', error);
        }
    };

    // 同步监控状态
    const syncMonitorStatus = async () => {
        try {
            const response = await api.post('/pmi-billing/monitor/sync');
            if (response.data.success) {
                message.success('监控状态已同步');
                fetchMonitorStatus();
            } else {
                message.error(response.data.message);
            }
        } catch (error) {
            message.error('同步监控状态失败');
            console.error('同步监控状态失败:', error);
        }
    };

    // 批量结算会议
    const batchSettleMeetings = async () => {
        try {
            const response = await api.post('/pmi-billing/settlement/batch');
            if (response.data.success) {
                message.success(response.data.message);
                fetchBillingRecords();
            } else {
                message.error(response.data.message);
            }
        } catch (error) {
            message.error('批量结算失败');
            console.error('批量结算失败:', error);
        }
    };

    // 强制释放ZoomUser
    const forceReleaseZoomUsers = async () => {
        try {
            const response = await api.post('/pmi-billing/zoom-users/force-release');
            if (response.data.success) {
                message.success('已强制释放所有ZoomUser');
                fetchZoomUserStats();
            } else {
                message.error(response.data.message);
            }
        } catch (error) {
            message.error('强制释放失败');
            console.error('强制释放失败:', error);
        }
    };

    // 获取交易类型标签
    const getTransactionTypeTag = (type) => {
        const typeMap = {
            'RECHARGE': { color: 'green', text: '充值' },
            'DEDUCT': { color: 'red', text: '扣费' },
            'REFUND': { color: 'blue', text: '退款' },
            'ADJUSTMENT': { color: 'orange', text: '调整' }
        };
        const config = typeMap[type] || { color: 'default', text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
    };

    // 获取记录状态标签
    const getRecordStatusTag = (status) => {
        const statusMap = {
            'PENDING': { color: 'orange', text: '待处理' },
            'COMPLETED': { color: 'green', text: '已完成' },
            'FAILED': { color: 'red', text: '失败' },
            'CANCELLED': { color: 'gray', text: '已取消' }
        };
        const config = statusMap[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
    };

    // 计费记录表格列
    const billingRecordColumns = [
        {
            title: 'PMI',
            dataIndex: 'pmiNumber',
            key: 'pmiNumber',
            width: 120,
            render: (pmiNumber, record) => (
                <Button
                    type="link"
                    size="small"
                    onClick={() => {
                        // 跳转到PMI管理页面并筛选对应PMI
                        navigate(`/pmi-management?search=${pmiNumber}`);
                    }}
                    style={{
                        padding: 0,
                        height: 'auto',
                        fontSize: '14px',
                        fontWeight: 'bold'
                    }}
                >
                    {pmiNumber || '-'}
                </Button>
            )
        },
        {
            title: '用户',
            dataIndex: 'userName',
            key: 'userName',
            width: 120,
            render: (userName, record) => (
                <Button
                    type="link"
                    size="small"
                    onClick={() => {
                        // 跳转到用户页面并筛选指定用户
                        navigate(`/users?userId=${record.userId}`);
                    }}
                    style={{
                        padding: 0,
                        height: 'auto',
                        fontSize: '14px'
                    }}
                    title={`用户ID: ${record.userId}, 邮箱: ${record.userEmail}`}
                >
                    {userName || `用户${record.userId}`}
                </Button>
            )
        },
        {
            title: '交易类型',
            dataIndex: 'transactionType',
            key: 'transactionType',
            width: 100,
            render: getTransactionTypeTag
        },
        {
            title: '时长变动',
            dataIndex: 'amountMinutes',
            key: 'amountMinutes',
            width: 120,
            render: (amount, record) => {
                const color = record.transactionType === 'RECHARGE' || record.transactionType === 'REFUND'
                    ? 'green' : 'red';
                const prefix = record.transactionType === 'RECHARGE' || record.transactionType === 'REFUND'
                    ? '+' : '';
                const formattedDuration = formatDuration(Math.abs(amount || 0));
                return <span style={{ color }}>{prefix}{formattedDuration}</span>;
            }
        },
        {
            title: '变动前余额',
            dataIndex: 'balanceBefore',
            key: 'balanceBefore',
            width: 120,
            render: (balance) => formatDuration(balance || 0)
        },
        {
            title: '变动后余额',
            dataIndex: 'balanceAfter',
            key: 'balanceAfter',
            width: 120,
            render: (balance) => formatDuration(balance || 0)
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 80,
            render: getRecordStatusTag
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            key: 'createdAt',
            width: 160,
            render: (time) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-'
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            ellipsis: true
        }
    ];

    useEffect(() => {
        fetchBillingRecords();
        fetchMonitorStatus();
        fetchZoomUserStats();
        fetchBillingStats();
    }, [filters]);

    return (
        <div style={{ padding: '24px' }}>
            <h2>PMI计费管理</h2>
            
            {/* 统计卡片 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="监控会议数"
                            value={monitorStatus.monitoringCount || 0}
                            prefix={<MonitorOutlined />}
                            valueStyle={{ color: monitorStatus.healthy ? '#3f8600' : '#cf1322' }}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="活跃会议数"
                            value={monitorStatus.activeMeetingsCount || 0}
                            prefix={<ClockCircleOutlined />}
                            valueStyle={{ color: '#1890ff' }}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="可用ZoomUser"
                            value={zoomUserStats.availableUsers || 0}
                            prefix={<UserOutlined />}
                            valueStyle={{ color: '#3f8600' }}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="使用中ZoomUser"
                            value={zoomUserStats.inUseUsers || 0}
                            prefix={<UserOutlined />}
                            valueStyle={{ color: '#faad14' }}
                        />
                    </Card>
                </Col>
            </Row>

            {/* 管理操作 */}
            <Card title="系统管理" style={{ marginBottom: 24 }}>
                <Space>
                    <Button 
                        type="primary"
                        icon={<SyncOutlined />}
                        onClick={syncMonitorStatus}
                    >
                        同步监控状态
                    </Button>
                    <Button 
                        icon={<SettingOutlined />}
                        onClick={batchSettleMeetings}
                    >
                        批量结算会议
                    </Button>
                    <Button 
                        danger
                        onClick={() => {
                            Modal.confirm({
                                title: '确认强制释放',
                                content: '确定要强制释放所有ZoomUser吗？这将中断正在进行的会议。',
                                onOk: forceReleaseZoomUsers
                            });
                        }}
                    >
                        强制释放ZoomUser
                    </Button>
                </Space>
                
                <div style={{ marginTop: 16 }}>
                    <Descriptions size="small" column={2}>
                        <Descriptions.Item label="监控状态">
                            <Badge 
                                status={monitorStatus.healthy ? "success" : "error"} 
                                text={monitorStatus.statusDescription || '未知'} 
                            />
                        </Descriptions.Item>
                        <Descriptions.Item label="ZoomUser使用率">
                            {zoomUserStats.totalUsers > 0 ? 
                                `${((zoomUserStats.inUseUsers / zoomUserStats.totalUsers) * 100).toFixed(1)}%` : 
                                '0%'
                            }
                        </Descriptions.Item>
                    </Descriptions>
                </div>
            </Card>

            {/* 计费记录 */}
            <Card title="计费记录">
                {/* 过滤器 */}
                <div style={{ marginBottom: 16 }}>
                    <Space wrap>
                        <Input
                            placeholder="PMI记录ID"
                            value={filters.pmiRecordId}
                            onChange={(e) => setFilters({
                                ...filters,
                                pmiRecordId: e.target.value
                            })}
                            style={{ width: 120 }}
                        />
                        <Input
                            placeholder="PMI号码"
                            value={filters.pmiNumber}
                            onChange={(e) => setFilters({
                                ...filters,
                                pmiNumber: e.target.value
                            })}
                            style={{ width: 120 }}
                        />
                        <Input
                            placeholder="用户ID"
                            value={filters.userId}
                            onChange={(e) => setFilters({
                                ...filters,
                                userId: e.target.value
                            })}
                            style={{ width: 100 }}
                        />
                        <Select
                            placeholder="交易类型"
                            value={filters.transactionType}
                            onChange={(value) => setFilters({
                                ...filters,
                                transactionType: value
                            })}
                            style={{ width: 120 }}
                            allowClear
                        >
                            <Option value="RECHARGE">充值</Option>
                            <Option value="DEDUCT">扣费</Option>
                            <Option value="REFUND">退款</Option>
                            <Option value="ADJUSTMENT">调整</Option>
                        </Select>
                        <Select
                            placeholder="状态"
                            value={filters.status}
                            onChange={(value) => setFilters({
                                ...filters,
                                status: value
                            })}
                            style={{ width: 100 }}
                            allowClear
                        >
                            <Option value="PENDING">待处理</Option>
                            <Option value="COMPLETED">已完成</Option>
                            <Option value="FAILED">失败</Option>
                            <Option value="CANCELLED">已取消</Option>
                        </Select>
                        <RangePicker
                            value={filters.dateRange}
                            onChange={(dates) => setFilters({
                                ...filters,
                                dateRange: dates
                            })}
                            style={{ width: 240 }}
                        />
                        <Button 
                            icon={<ReloadOutlined />}
                            onClick={() => fetchBillingRecords()}
                        >
                            刷新
                        </Button>
                    </Space>
                </div>
                
                <Table
                    columns={billingRecordColumns}
                    dataSource={billingRecords}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                        ...pagination,
                        onChange: fetchBillingRecords,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条记录`
                    }}
                    scroll={{ x: 1200 }}
                />
            </Card>
        </div>
    );
};

export default PmiBillingManagement;
