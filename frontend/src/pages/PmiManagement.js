import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Card,
  Space,
  message,
  Modal,
  Form,
  Input,
  Select,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic,
  Typography,
  Divider,
  notification
} from 'antd';
import {
  PlusOutlined,
  CopyOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SearchOutlined,
  LinkOutlined,
  UserOutlined,
  CalendarOutlined,
  EyeOutlined,
  EditOutlined,
  DollarOutlined,
  RollbackOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { pmiApi, userApi, systemConfigApi } from '../services/api';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import PmiRechargeModal from '../components/PmiRechargeModal';
import PmiMeetingReportsModal from '../components/PmiMeetingReportsModal';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const PmiManagement = () => {
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();
  const [pmiRecords, setPmiRecords] = useState([]);
  const [users, setUsers] = useState([]);

  // 时长格式化函数：将分钟数转换为 *小时*分钟 格式
  const formatDuration = (minutes) => {
    if (!minutes || minutes === 0) return '0分钟';

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours === 0) {
      return `${remainingMinutes}分钟`;
    } else if (remainingMinutes === 0) {
      return `${hours}小时`;
    } else {
      return `${hours}小时${remainingMinutes}分钟`;
    }
  };
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [copyModalVisible, setCopyModalVisible] = useState(false);
  const [selectedPmi, setSelectedPmi] = useState(null);
  const [copyText, setCopyText] = useState('');
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingPmi, setEditingPmi] = useState(null);
  const [editLoading, setEditLoading] = useState(false);
  const [stats, setStats] = useState({});
  const [systemConfigs, setSystemConfigs] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filteredUserId, setFilteredUserId] = useState(null);
  const [filteredUser, setFilteredUser] = useState(null);
  const [billingModeFilter, setBillingModeFilter] = useState('');
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [rechargeModalVisible, setRechargeModalVisible] = useState(false);
  const [rechargePmiRecordId, setRechargePmiRecordId] = useState(null);

  // 会议报告相关状态
  const [meetingReportsVisible, setMeetingReportsVisible] = useState(false);
  const [selectedPmiForReports, setSelectedPmiForReports] = useState(null);

  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 加载PMI记录
  const loadPmiRecords = async (page = 1, pageSize = 10, keyword = '', userIdOverride = null, billingModeOverride = null) => {
    try {
      setLoading(true);
      const params = {
        page: page - 1,
        size: pageSize,
      };

      // 添加计费模式筛选参数
      const targetBillingMode = billingModeOverride !== null ? billingModeOverride : billingModeFilter;
      if (targetBillingMode) {
        params.billingMode = targetBillingMode;
      }

      let response;

      // 优先使用传入的userIdOverride，然后是filteredUserId
      const targetUserId = userIdOverride !== null ? userIdOverride : filteredUserId;

      // 如果有过滤的用户ID，只显示该用户的PMI
      if (targetUserId) {
        response = await pmiApi.getUserPmiRecords(targetUserId, params);
      } else if (keyword && keyword.trim()) {
        response = await pmiApi.searchPmiRecords(keyword.trim(), params);
      } else {
        response = await pmiApi.getAllPmiRecords(params);
      }

      setPmiRecords(response.data.content || []);
      setPagination({
        current: page,
        pageSize: pageSize,
        total: response.data.totalElements || 0,
      });
    } catch (error) {
      message.error('加载PMI记录失败');
      console.error('Error loading PMI records:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载用户列表（懒加载，只在需要时调用）
  const loadUsers = async () => {
    try {
      // 如果已经加载过用户列表，直接返回
      if (users.length > 0) {
        return;
      }
      const response = await userApi.getUsers({ page: 0, size: 1000 });
      setUsers(response.data.content || []);
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  // 确保用户列表已加载（用于创建PMI时的用户选择）
  const ensureUsersLoaded = async () => {
    if (users.length === 0) {
      await loadUsers();
    }
  };

  // 加载统计信息
  const loadStats = async () => {
    try {
      let response;
      if (filteredUserId !== null) {
        // 如果过滤了特定用户，获取该用户的PMI统计
        response = await pmiApi.getUserPmiStats(filteredUserId);
      } else {
        // 否则获取全局PMI统计
        response = await pmiApi.getPmiStats();
      }
      setStats(response.data.data || {});
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  // 获取系统配置
  const fetchSystemConfigs = async () => {
    try {
      const response = await systemConfigApi.getConfigs();
      if (response.data.success) {
        const configMap = {};
        response.data.data.content.forEach(config => {
          configMap[config.configKey] = config.configValue;
        });
        setSystemConfigs(configMap);
      }
    } catch (error) {
      console.error('获取系统配置失败:', error);
    }
  };

  // 获取用户前端域名
  const getUserFrontendBaseUrl = () => {
    return systemConfigs['user_frontend.domain.base_url'] || 'http://localhost:3001';
  };

  useEffect(() => {
    const initializePage = async () => {
      // 处理路径参数和URL参数
      const userId = params.userId;
      const pmiRecordId = params.pmiRecordId;
      const showCreateModal = location.pathname.includes('/create');

      // 处理URL搜索参数
      const urlParams = new URLSearchParams(location.search);
      const searchParam = urlParams.get('search');
      const pmiNumberParam = urlParams.get('pmiNumber');

      // 如果有pmiRecordId路径参数，优先使用它；否则使用pmiNumber参数或search参数
      let finalSearchParam = searchParam;
      const targetPmiRecordId = pmiRecordId; // 只使用路径参数

      if (targetPmiRecordId) {
        // 如果是PMI记录ID，需要先获取对应的PMI记录来获取PMI号码进行搜索
        try {
          const pmiResponse = await pmiApi.getPmiRecordById(parseInt(targetPmiRecordId));
          if (pmiResponse.data && pmiResponse.data.pmiNumber) {
            finalSearchParam = pmiResponse.data.pmiNumber;
          }
        } catch (error) {
          console.error('Error loading PMI record:', error);
          message.error('PMI记录不存在');
        }
      } else if (pmiNumberParam) {
        finalSearchParam = pmiNumberParam;
      }

      // 先加载系统配置
      await fetchSystemConfigs();

      if (userId) {
        const userIdInt = parseInt(userId);
        setFilteredUserId(userIdInt);

        // 只获取特定用户的信息，不需要加载所有用户列表
        try {
          const userResponse = await userApi.getUserById(userIdInt);
          setFilteredUser(userResponse.data);
        } catch (error) {
          console.error('Error loading user info:', error);
          setFilteredUser(null);
        }

        // 加载用户特定的统计信息和PMI记录
        try {
          const statsResponse = await pmiApi.getUserPmiStats(userIdInt);
          setStats(statsResponse.data.data || {});
        } catch (error) {
          console.error('Error loading user stats:', error);
        }

        // 加载用户的PMI记录，传递用户ID确保正确过滤
        loadPmiRecords(1, 10, finalSearchParam || '', userIdInt, billingModeFilter);
      } else {
        // 只有在需要显示全局页面时才加载用户列表
        await loadUsers();

        // 加载全局统计信息和PMI记录
        loadStats();
        loadPmiRecords(1, 10, finalSearchParam || '', null, billingModeFilter);
      }

      // 如果有搜索参数，设置搜索关键词
      if (finalSearchParam) {
        setSearchKeyword(finalSearchParam);
      }

      // 如果需要显示创建弹窗，此时才加载用户列表（如果还没加载）
      if (showCreateModal) {
        if (userId) {
          // 如果是特定用户页面，需要加载用户列表用于下拉选择
          if (users.length === 0) {
            await loadUsers();
          }
          // 预选用户
          createForm.setFieldsValue({ userId: parseInt(userId) });
        }
        setCreateModalVisible(true);
      }
    };

    initializePage();
  }, [params.userId, params.pmiRecordId, location.pathname]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);



  // 生成PMI
  const handleCreateSubmit = async (values) => {
    try {
      setCreateLoading(true);
      await pmiApi.generatePmi(values);
      message.success('PMI生成成功');
      setCreateModalVisible(false);
      createForm.resetFields();
      loadPmiRecords(pagination.current, pagination.pageSize, searchKeyword, filteredUserId, billingModeFilter);
      loadStats();
    } catch (error) {
      message.error('生成PMI失败');
      console.error('Error generating PMI:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  // 复制PMI信息
  const handleCopyPmi = async (record) => {
    try {
      const response = await pmiApi.getPmiCopyText(record.id);
      setSelectedPmi(record);
      setCopyText(response.data.copyText);
      setCopyModalVisible(true);
    } catch (error) {
      message.error('获取复制信息失败');
      console.error('Error getting copy text:', error);
    }
  };

  // 复制到剪贴板
  const copyToClipboard = () => {
    navigator.clipboard.writeText(copyText).then(() => {
      message.success('已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 删除PMI
  const handleDeletePmi = async (id) => {
    try {
      await pmiApi.deletePmiRecord(id);
      message.success('PMI删除成功');
      loadPmiRecords(pagination.current, pagination.pageSize, searchKeyword, filteredUserId, billingModeFilter);
      loadStats();
    } catch (error) {
      message.error('删除PMI失败');
      console.error('Error deleting PMI:', error);
    }
  };

  // 编辑PMI
  const handleEditPmi = (record) => {
    setEditingPmi(record);
    editForm.setFieldsValue({
      pmiNumber: record.pmiNumber,
      pmiPassword: record.pmiPassword,
      userId: record.userId
    });
    setEditModalVisible(true);
  };

  // 提交编辑
  const handleEditSubmit = async (values) => {
    try {
      setEditLoading(true);
      await pmiApi.updatePmiRecord(editingPmi.id, values);
      message.success('PMI更新成功');
      setEditModalVisible(false);
      setEditingPmi(null);
      editForm.resetFields();
      loadPmiRecords(pagination.current, pagination.pageSize, searchKeyword, filteredUserId, billingModeFilter);
      loadStats();
    } catch (error) {
      message.error('更新失败: ' + (error.response?.data?.message || error.message));
      console.error('Error updating PMI:', error);
    } finally {
      setEditLoading(false);
    }
  };

  // 取消编辑
  const handleEditCancel = () => {
    setEditModalVisible(false);
    setEditingPmi(null);
    editForm.resetFields();
  };

  // 处理充值
  const handleRecharge = (record) => {
    setRechargePmiRecordId(record.id);
    setRechargeModalVisible(true);
  };

  // 处理查看会议报告
  const handleViewMeetingReports = (record) => {
    setSelectedPmiForReports(record);
    setMeetingReportsVisible(true);
  };

  // 充值成功回调
  const handleRechargeSuccess = () => {
    loadPmiRecords(pagination.current, pagination.pageSize, searchKeyword, filteredUserId, billingModeFilter);
    loadStats();
  };

  // 设置PMI回退状态
  const handleToggleFallback = async (record) => {
    try {
      const newFallbackStatus = !record.fallbackEnabled;
      const response = await pmiApi.setPmiFallback(record.id, newFallbackStatus);

      if (response.data.success) {
        message.success(`PMI ${newFallbackStatus ? '已设置为回退' : '已取消回退'}`);
        loadPmiRecords(pagination.current, pagination.pageSize, searchKeyword, filteredUserId, billingModeFilter);
      } else {
        message.error(response.data.message || '设置失败');
      }
    } catch (error) {
      console.error('Error toggling fallback:', error);
      message.error('设置回退状态失败');
    }
  };

  // 搜索
  const handleSearch = (value) => {
    setSearchKeyword(value);
    loadPmiRecords(1, pagination.pageSize, value, filteredUserId, billingModeFilter);
  };

  // 计费模式筛选
  const handleBillingModeFilter = (value) => {
    setBillingModeFilter(value);
    loadPmiRecords(1, pagination.pageSize, searchKeyword, filteredUserId, value);
  };

  // 表格列定义
  const columns = [
    {
      title: 'PMI号码',
      dataIndex: 'pmiNumber',
      key: 'pmiNumber',
      width: 'auto',
      minWidth: isMobileView ? 120 : 140,
      ellipsis: false,
      render: (text, record) => (
        <a
          href={`/pmi-management/${record.id}`}
          style={{
            fontSize: isMobileView ? '12px' : '14px',
            fontFamily: 'monospace',
            whiteSpace: 'nowrap',
            fontWeight: 'bold',
            color: '#1890ff',
            textDecoration: 'underline'
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '魔链ID',
      dataIndex: 'magicId',
      key: 'magicId',
      width: 'auto',
      minWidth: isMobileView ? 120 : 140,
      ellipsis: false,
      render: (text, record) => (
        <a
          href={`/pmi/${text || record.pmiNumber}`}
          target="_blank"
          rel="noopener noreferrer"
          style={{
            fontSize: isMobileView ? '11px' : '13px',
            fontFamily: 'monospace',
            whiteSpace: 'nowrap',
            textDecoration: 'underline'
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '密码',
      dataIndex: 'pmiPassword',
      key: 'pmiPassword',
      width: 'auto',
      minWidth: isMobileView ? 80 : 100,
      ellipsis: false,
      render: (text) => <Text code style={{ fontSize: isMobileView ? '11px' : '13px', whiteSpace: 'nowrap' }}>{text}</Text>,
    },
    {
      title: '用户',
      dataIndex: 'userId',
      key: 'userId',
      width: 'auto',
      minWidth: isMobileView ? 100 : 120,
      ellipsis: false,
      render: (userId) => {
        const user = users.find(u => u.id === userId);
        return user ? (
          <Space size="small" style={{ whiteSpace: 'nowrap' }}>
            <UserOutlined style={{ fontSize: isMobileView ? '12px' : '14px' }} />
            <a
              href={`/users/${userId}`}
              style={{
                fontSize: isMobileView ? '12px' : '14px',
                color: '#1890ff',
                textDecoration: 'none',
                whiteSpace: 'nowrap'
              }}
              onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
              onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
            >
              {user.fullName}
            </a>
          </Space>
        ) : userId;
      },
    },
    {
      title: isMobileView ? '计费' : '计费模式',
      dataIndex: 'billingMode',
      key: 'billingMode',
      width: 'auto',
      minWidth: isMobileView ? 60 : 80,
      ellipsis: false,
      align: 'center',
      render: (billingMode, record) => {
        const modeConfig = {
          BY_TIME: { color: 'blue', text: '按时长' },
          LONG: { color: 'purple', text: '按时段' },
        };
        const config = modeConfig[billingMode] || { color: 'default', text: billingMode || '未知' };

        // 如果是LONG模式且有到期时间，显示额外信息
        if (billingMode === 'LONG' && record.windowExpireTime) {
          const expireDate = new Date(record.windowExpireTime);
          const now = new Date();
          const isExpired = expireDate <= now;

          return (
            <div style={{ whiteSpace: 'nowrap' }}>
              <Tag color={isExpired ? 'red' : config.color} style={{ fontSize: isMobileView ? '10px' : '12px' }}>
                {config.text}
              </Tag>
              {!isMobileView && (
                <div style={{ fontSize: '10px', color: isExpired ? '#ff4d4f' : '#666', marginTop: '2px', textAlign: 'center' }}>
                  {isExpired ? '已过期' : '长租中'}
                </div>
              )}
            </div>
          );
        }

        return <Tag color={config.color} style={{ fontSize: isMobileView ? '10px' : '12px', whiteSpace: 'nowrap' }}>{config.text}</Tag>;
      },
    },
    {
      title: isMobileView ? '长租到期' : '长租到期日',
      dataIndex: 'windowExpireTime',
      key: 'windowExpireTime',
      width: 'auto',
      minWidth: isMobileView ? 90 : 130,
      ellipsis: false,
      align: 'center',
      render: (windowExpireTime, record) => {
        // 只有LONG模式才显示到期时间
        if (record.billingMode !== 'LONG' || !windowExpireTime) {
          return <span style={{ color: '#ccc', fontSize: isMobileView ? '11px' : '12px', whiteSpace: 'nowrap' }}>-</span>;
        }

        const expireDate = new Date(windowExpireTime);
        const now = new Date();
        const isExpired = expireDate <= now;
        const diffTime = expireDate - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // 检查是否为当天到期
        const isToday = expireDate.toDateString() === now.toDateString();

        // 计算到期状态和颜色
        let color, statusText;
        if (isExpired) {
          color = '#ff4d4f';
          statusText = '已过期';
        } else if (isToday) {
          // 当天到期，显示具体时间
          color = '#fa8c16';
          statusText = `今日 ${expireDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })} 到期`;
        } else if (diffDays <= 7) {
          color = '#fa8c16';
          statusText = `${diffDays}天后到期`;
        } else {
          color = '#52c41a';
          statusText = '长租中';
        }

        return (
          <div style={{ whiteSpace: 'nowrap', textAlign: 'center' }}>
            <div style={{ fontSize: isMobileView ? '11px' : '12px', color: '#333' }}>
              {expireDate.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              })}
            </div>
            {!isMobileView && (
              <div style={{ fontSize: '10px', color, marginTop: '2px' }}>
                {statusText}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 'auto',
      minWidth: isMobileView ? 80 : 100,
      ellipsis: false,
      render: (status, record) => {
        // 如果有活跃会议，显示"会议进行中"
        if (record.hasActiveMeeting) {
          return <Tag color="blue" style={{ fontSize: isMobileView ? '11px' : '12px', whiteSpace: 'nowrap' }}>会议进行中</Tag>;
        }

        // 否则显示原有状态
        const statusConfig = {
          ACTIVE: { color: 'green', text: '活跃' },
          INACTIVE: { color: 'orange', text: '非活跃' },
          EXPIRED: { color: 'red', text: '已过期' },
        };
        const config = statusConfig[status] || { color: 'default', text: status };
        return <Tag color={config.color} style={{ fontSize: isMobileView ? '11px' : '12px', whiteSpace: 'nowrap' }}>{config.text}</Tag>;
      },
    },
    {
      title: '回退状态',
      dataIndex: 'fallbackEnabled',
      key: 'fallbackEnabled',
      width: 'auto',
      minWidth: isMobileView ? 80 : 100,
      ellipsis: false,
      render: (fallbackEnabled) => {
        return fallbackEnabled ?
          <Tag color="orange" style={{ fontSize: isMobileView ? '11px' : '12px', whiteSpace: 'nowrap' }}>已回退</Tag> :
          <Tag color="default" style={{ fontSize: isMobileView ? '11px' : '12px', whiteSpace: 'nowrap' }}>正常</Tag>;
      },
    },
    {
      title: '工作账号',
      dataIndex: 'activeMeetingZoomUserEmail',
      key: 'activeMeetingZoomUserEmail',
      width: 'auto',
      minWidth: isMobileView ? 120 : 150,
      ellipsis: false,
      render: (email, record) => {
        if (!email || !record.activeMeetingZoomUserId) {
          return <span style={{ color: '#999', fontSize: isMobileView ? '11px' : '12px', whiteSpace: 'nowrap' }}>-</span>;
        }

        return (
          <Button
            type="link"
            size="small"
            style={{
              padding: 0,
              height: 'auto',
              fontSize: isMobileView ? '11px' : '12px',
              color: '#1890ff',
              whiteSpace: 'nowrap'
            }}
            onClick={() => navigate(`/zoom-users/zoom-user/${record.activeMeetingZoomUserId}`)}
          >
            {email}
          </Button>
        );
      },
    },
    {
      title: isMobileView ? '剩余时长' : '剩余可用时长',
      dataIndex: 'availableMinutes',
      key: 'availableMinutes',
      width: 'auto',
      minWidth: isMobileView ? 80 : 110,
      ellipsis: false,
      render: (availableMinutes, record) => {
        // 计算剩余可用时长 = 可用时长 - 待扣时长
        const available = availableMinutes || 0;
        const pending = record.pendingDeductMinutes || 0;
        const remaining = Math.max(0, available - pending);
        const displayText = formatDuration(remaining);

        return (
          <div style={{ textAlign: 'center', whiteSpace: 'nowrap' }}>
            <Button
              type="link"
              size="small"
              onClick={() => navigate(`/pmi-billing-management?pmi=${record.pmiNumber}`)}
              style={{
                fontSize: isMobileView ? '11px' : '12px',
                padding: '0',
                height: 'auto',
                color: remaining > 0 ? '#1890ff' : '#ff4d4f',
                fontWeight: 'bold',
                whiteSpace: 'nowrap'
              }}
            >
              {displayText}
            </Button>
            {pending > 0 && (
              <div style={{
                fontSize: '10px',
                color: '#999',
                marginTop: '2px'
              }}>
                待扣: {formatDuration(pending)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: isMobileView ? '创建' : '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 'auto',
      minWidth: isMobileView ? 80 : 150,
      ellipsis: false,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px', whiteSpace: 'nowrap' }}>
          {new Date(text).toLocaleString(undefined, {
            month: isMobileView ? 'numeric' : 'numeric',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            year: isMobileView ? undefined : 'numeric'
          })}
        </span>
      ),
    },
    {
      title: isMobileView ? '使用' : '最后使用',
      dataIndex: 'lastUsedAt',
      key: 'lastUsedAt',
      width: 'auto',
      minWidth: isMobileView ? 80 : 150,
      ellipsis: false,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px', whiteSpace: 'nowrap' }}>
          {text ? new Date(text).toLocaleString(undefined, {
            month: isMobileView ? 'numeric' : 'numeric',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            year: isMobileView ? undefined : 'numeric'
          }) : '-'}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 200 : 280, // 移动端增加宽度以容纳文字
      fixed: isMobileView ? false : 'right', // PC端固定操作栏，移动端不固定允许左右滑动
      render: (_, record) => {
        // 移动端双列展示，PC端三列展示
        const actionGroups = isMobileView ? [
          // 移动端：双列布局
          [
            <Tooltip key="edit" title="编辑PMI">
              <Button
                type="default"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEditPmi(record)}
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px'
                }}
              >
                编辑
              </Button>
            </Tooltip>
          ],
          [
            <Tooltip key="schedule" title="计划管理">
              <Button
                type="default"
                icon={<CalendarOutlined />}
                size="small"
                onClick={() => navigate(`/pmi-schedule-management/${record.id}`)}
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px'
                }}
              >
                计划
              </Button>
            </Tooltip>,
            <Tooltip key="fallback" title={record.fallbackEnabled ? "取消回退" : "设置回退"}>
              <Button
                type={record.fallbackEnabled ? "primary" : "default"}
                icon={<RollbackOutlined />}
                size="small"
                onClick={() => handleToggleFallback(record)}
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px',
                  backgroundColor: record.fallbackEnabled ? '#ff7a00' : undefined,
                  borderColor: record.fallbackEnabled ? '#ff7a00' : undefined
                }}
              >
                {record.fallbackEnabled ? '取消' : '回退'}
              </Button>
            </Tooltip>
          ],
          [
            <Tooltip key="reports" title="会议报告">
              <Button
                type="default"
                icon={<FileTextOutlined />}
                size="small"
                onClick={() => handleViewMeetingReports(record)}
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px'
                }}
              >
                报告
              </Button>
            </Tooltip>,
            <Tooltip key="recharge" title="PMI充值">
              <Button
                type="default"
                icon={<DollarOutlined />}
                size="small"
                onClick={() => handleRecharge(record)}
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px'
                }}
              >
                充值
              </Button>
            </Tooltip>
          ],
          [
            <Tooltip key="copy" title="复制PMI信息">
              <Button
                type="primary"
                icon={<CopyOutlined />}
                size="small"
                onClick={() => handleCopyPmi(record)}
                style={{
                  fontSize: '11px',
                  width: '70px',
                  height: '28px',
                  padding: '4px 6px'
                }}
              >
                复制
              </Button>
            </Tooltip>
          ],
          [
            <Popconfirm
              key="delete"
              title="确定要删除这个PMI吗？"
              onConfirm={() => handleDeletePmi(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除PMI">
                <Button
                  type="primary"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                  style={{
                    fontSize: '11px',
                    width: '70px',
                    height: '28px',
                    padding: '4px 6px'
                  }}
                >
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>
          ]
        ] : [
          // PC端：每行2个按钮，共3行
          [
            <Tooltip key="edit" title="编辑PMI">
              <Button
                type="default"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEditPmi(record)}
                style={{
                  fontSize: '12px',
                  width: 'auto',
                  height: '28px',
                  padding: '4px 8px'
                }}
              >
                编辑
              </Button>
            </Tooltip>
          ],
          [
            <Tooltip key="schedule" title="计划管理">
              <Button
                type="default"
                icon={<CalendarOutlined />}
                size="small"
                onClick={() => navigate(`/pmi-schedule-management/${record.id}`)}
                style={{
                  fontSize: '12px',
                  width: 'auto',
                  height: '28px',
                  padding: '4px 8px'
                }}
              >
                计划
              </Button>
            </Tooltip>,
            <Tooltip key="fallback" title={record.fallbackEnabled ? "取消回退" : "设置回退"}>
              <Button
                type={record.fallbackEnabled ? "primary" : "default"}
                icon={<RollbackOutlined />}
                size="small"
                onClick={() => handleToggleFallback(record)}
                style={{
                  fontSize: '12px',
                  width: 'auto',
                  height: '28px',
                  padding: '4px 8px',
                  backgroundColor: record.fallbackEnabled ? '#ff7a00' : undefined,
                  borderColor: record.fallbackEnabled ? '#ff7a00' : undefined
                }}
              >
                {record.fallbackEnabled ? '取消回退' : '设置回退'}
              </Button>
            </Tooltip>
          ],
          [
            <Tooltip key="reports" title="会议报告">
              <Button
                type="default"
                icon={<FileTextOutlined />}
                size="small"
                onClick={() => handleViewMeetingReports(record)}
                style={{
                  fontSize: '12px',
                  width: 'auto',
                  height: '28px',
                  padding: '4px 8px'
                }}
              >
                会议报告
              </Button>
            </Tooltip>,
            <Tooltip key="recharge" title="PMI充值">
              <Button
                type="default"
                icon={<DollarOutlined />}
                size="small"
                onClick={() => handleRecharge(record)}
                style={{
                  fontSize: '12px',
                  width: 'auto',
                  height: '28px',
                  padding: '4px 8px'
                }}
              >
                充值
              </Button>
            </Tooltip>
          ],
          [
            <Tooltip key="copy" title="复制PMI信息">
              <Button
                type="primary"
                icon={<CopyOutlined />}
                size="small"
                onClick={() => handleCopyPmi(record)}
                style={{
                  fontSize: '12px',
                  width: 'auto',
                  height: '28px',
                  padding: '4px 8px'
                }}
              >
                复制
              </Button>
            </Tooltip>
          ],
          [
            <Popconfirm
              key="delete"
              title="确定要删除这个PMI吗？"
              onConfirm={() => handleDeletePmi(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除PMI">
                <Button
                  type="primary"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                  style={{
                    fontSize: '12px',
                    width: 'auto',
                    height: '28px',
                    padding: '4px 8px'
                  }}
                >
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>
          ]
        ];

        return (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: isMobileView ? '6px' : '4px',
            width: '100%'
          }}>
            {actionGroups.map((group, index) => (
              <Space
                key={index}
                size={isMobileView ? "small" : "small"}
                style={{
                  display: 'flex',
                  justifyContent: isMobileView ? 'flex-start' : 'flex-start',
                  flexWrap: 'wrap'
                }}
              >
                {group}
              </Space>
            ))}
          </div>
        );
      },
    },
  ];

  return (
    <div style={{ padding: isMobileView ? '12px' : '24px' }}>
      {/* 移动端滚动提示 */}
      {isMobileView && (
        <div style={{
          marginBottom: '16px',
          padding: '8px',
          backgroundColor: '#f0f2f5',
          borderRadius: '4px',
          fontSize: '12px',
          color: '#666'
        }}>
          👈 表格可左右滑动查看所有信息
        </div>
      )}

      <div style={{ marginBottom: isMobileView ? 16 : 24 }}>
        {filteredUser ? (
          <div>
            <Button
              type="link"
              onClick={() => navigate(-1)}
              style={{ padding: 0, marginBottom: 8, fontSize: isMobileView ? '14px' : '16px' }}
            >
              ← 返回用户管理
            </Button>
            <Title level={isMobileView ? 3 : 2}>
              {filteredUser.fullName} 的PMI管理
            </Title>
          </div>
        ) : (
          <Title level={isMobileView ? 3 : 2}>PMI管理</Title>
        )}
      </div>

      {/* 统计信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: isMobileView ? 16 : 24 }}>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card size={isMobileView ? 'small' : 'default'}>
            <Statistic
              title={filteredUserId !== null ? "PMI数量" : "活跃PMI数量"}
              value={filteredUserId !== null ? (stats.totalCount || 0) : (stats.totalActive || 0)}
              prefix={<LinkOutlined />}
              valueStyle={{ fontSize: isMobileView ? '20px' : '24px' }}
            />
          </Card>
        </Col>
        {/* 只在全局统计时显示会议中PMI数量 */}
        {filteredUserId === null && (
          <Col xs={24} sm={12} md={8} lg={6}>
            <Card size={isMobileView ? 'small' : 'default'}>
              <Statistic
                title="会议中PMI"
                value={stats.inMeeting || 0}
                prefix={<CalendarOutlined />}
                valueStyle={{
                  fontSize: isMobileView ? '20px' : '24px',
                  color: (stats.inMeeting || 0) > 0 ? '#52c41a' : '#8c8c8c'
                }}
                suffix={
                  <span style={{
                    fontSize: isMobileView ? '12px' : '14px',
                    color: '#8c8c8c'
                  }}>
                    / {stats.totalActive || 0}
                  </span>
                }
              />
            </Card>
          </Col>
        )}
      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: isMobileView ? 16 : 24 }} size={isMobileView ? 'small' : 'default'}>
        <div style={{
          display: 'flex',
          flexDirection: isMobileView ? 'column' : 'row',
          justifyContent: 'space-between',
          alignItems: isMobileView ? 'stretch' : 'center',
          gap: isMobileView ? '12px' : '0'
        }}>
          <Space direction={isMobileView ? 'horizontal' : 'horizontal'} style={{ width: isMobileView ? '100%' : 'auto' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={async () => {
                // 确保用户列表已加载（用于下拉选择）
                await ensureUsersLoaded();
                setCreateModalVisible(true);
                // 如果有过滤用户，重新设置用户ID
                if (filteredUserId !== null) {
                  createForm.setFieldsValue({ userId: filteredUserId });
                }
              }}
              size={isMobileView ? 'middle' : 'middle'}
              style={{ flex: isMobileView ? 1 : 'none' }}
            >
              生成PMI
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => loadPmiRecords(pagination.current, pagination.pageSize, searchKeyword, filteredUserId, billingModeFilter)}
              size={isMobileView ? 'middle' : 'middle'}
              style={{ flex: isMobileView ? 1 : 'none' }}
            >
              刷新
            </Button>
          </Space>
          <div style={{
            display: 'flex',
            gap: '8px',
            flexDirection: isMobileView ? 'column' : 'row',
            alignItems: isMobileView ? 'stretch' : 'center'
          }}>
            {/* 计费类型筛选 */}
            <Select
              placeholder="计费类型"
              allowClear
              style={{ width: isMobileView ? '100%' : 120 }}
              value={billingModeFilter || undefined}
              onChange={handleBillingModeFilter}
              size={isMobileView ? 'middle' : 'middle'}
            >
              <Option value="">全部</Option>
              <Option value="LONG">按时段</Option>
              <Option value="BY_TIME">按时长</Option>
            </Select>

            {!filteredUserId && (
              <Search
                placeholder={isMobileView ? "搜索PMI" : "搜索PMI号码或用户"}
                allowClear
                style={{ width: isMobileView ? '100%' : 300 }}
                onSearch={handleSearch}
                size={isMobileView ? 'middle' : 'middle'}
              />
            )}
          </div>
        </div>
      </Card>

      {/* PMI列表 */}
      <Card size={isMobileView ? 'small' : 'default'}>
        <div data-page="pmi-management">
          <Table
            columns={columns}
            dataSource={pmiRecords}
            rowKey="id"
            loading={loading}
            size={isMobileView ? 'small' : 'middle'}
            scroll={{ x: 'max-content' }}
            pagination={{
              ...pagination,
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              simple: isMobileView,
              size: isMobileView ? 'small' : 'default',
              onChange: (page, pageSize) => {
                loadPmiRecords(page, pageSize, searchKeyword, filteredUserId, billingModeFilter);
              },
            }}
          />
        </div>
      </Card>

      {/* 生成PMI模态框 */}
      <Modal
        title="生成PMI"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateSubmit}
        >
          <Form.Item
            name="userId"
            label={filteredUserId !== null ? "目标用户（已锁定）" : "选择用户"}
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select
              placeholder="请选择用户"
              showSearch
              disabled={filteredUserId !== null} // 当有过滤用户时禁用选择
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.fullName} ({user.username})
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="customPassword"
            label="自定义密码（可选）"
          >
            <Input placeholder="留空将自动生成6位数字密码" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={createLoading}>
                生成PMI
              </Button>
              <Button onClick={() => {
                setCreateModalVisible(false);
                createForm.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 复制信息模态框 */}
      <Modal
        title="PMI信息"
        open={copyModalVisible}
        onCancel={() => setCopyModalVisible(false)}
        footer={[
          <Button key="copy" type="primary" onClick={copyToClipboard}>
            复制到剪贴板
          </Button>,
          <Button key="close" onClick={() => setCopyModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={600}
      >
        <div style={{ whiteSpace: 'pre-line', fontFamily: 'monospace' }}>
          {copyText}
        </div>
      </Modal>

      {/* 编辑PMI模态框 */}
      <Modal
        title="编辑PMI"
        open={editModalVisible}
        onCancel={handleEditCancel}
        footer={null}
        width={isMobileView ? '95%' : 500}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditSubmit}
        >
          <Form.Item
            name="pmiNumber"
            label="PMI号码"
            rules={[
              { required: true, message: '请输入PMI号码' },
              { len: 10, message: 'PMI号码必须是10位数字' },
              { pattern: /^\d{10}$/, message: 'PMI号码只能包含数字' }
            ]}
          >
            <Input placeholder="请输入10位PMI号码" maxLength={10} />
          </Form.Item>
          <Form.Item
            name="pmiPassword"
            label="PMI密码"
            rules={[
              { required: true, message: '请输入PMI密码' },
              { min: 4, message: '密码至少4位' },
              { max: 10, message: '密码最多10位' }
            ]}
          >
            <Input placeholder="请输入PMI密码" maxLength={10} />
          </Form.Item>
          <Form.Item
            name="userId"
            label="分配用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select
              placeholder="请选择用户"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.fullName} ({user.username})
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={editLoading}>
                更新PMI
              </Button>
              <Button onClick={handleEditCancel}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* PMI充值弹窗 */}
      <PmiRechargeModal
        visible={rechargeModalVisible}
        onCancel={() => setRechargeModalVisible(false)}
        onSuccess={handleRechargeSuccess}
        pmiRecordId={rechargePmiRecordId}
      />

      {/* PMI会议报告弹窗 */}
      <PmiMeetingReportsModal
        visible={meetingReportsVisible}
        onCancel={() => {
          setMeetingReportsVisible(false);
          setSelectedPmiForReports(null);
        }}
        pmiRecord={selectedPmiForReports}
        isMobileView={isMobileView}
      />
    </div>
  );
};

export default PmiManagement;
