import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, DatePicker, Select, Button, message, Spin, Tabs } from 'antd';
import { FileTextOutlined, CalendarOutlined, UserOutlined, ClockCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import axios from 'axios';
import PmiStatsCard from '../components/PmiStatsCard';
import PmiTrendsChart from '../components/PmiTrendsChart';
import PmiMeetingList from '../components/PmiMeetingList';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

const PmiReports = () => {
  const [loading, setLoading] = useState(false);
  const [overviewData, setOverviewData] = useState({});
  const [trendsData, setTrendsData] = useState([]);
  const [mostActiveData, setMostActiveData] = useState([]);
  const [selectedPmi, setSelectedPmi] = useState(null);
  const [pmiDetails, setPmiDetails] = useState(null);
  const [pmiMeetings, setPmiMeetings] = useState([]);
  const [dateRange, setDateRange] = useState([moment().subtract(30, 'days'), moment()]);
  const [trendDays, setTrendDays] = useState(30);

  // 获取概览数据
  const fetchOverviewData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/pmi-reports/overview');
      setOverviewData(response.data);
    } catch (error) {
      message.error('获取概览数据失败');
      console.error('Error fetching overview:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取趋势数据
  const fetchTrendsData = async (days = 30) => {
    try {
      const response = await axios.get(`/api/pmi-reports/trends?days=${days}`);
      setTrendsData(response.data.trends || []);
    } catch (error) {
      message.error('获取趋势数据失败');
      console.error('Error fetching trends:', error);
    }
  };

  // 获取最活跃PMI数据
  const fetchMostActiveData = async (limit = 10, days = 30) => {
    try {
      const response = await axios.get(`/api/pmi-reports/most-active?limit=${limit}&days=${days}`);
      setMostActiveData(response.data);
    } catch (error) {
      message.error('获取最活跃PMI数据失败');
      console.error('Error fetching most active:', error);
    }
  };

  // 获取特定PMI详情
  const fetchPmiDetails = async (pmiNumber) => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/pmi-reports/${pmiNumber}`);
      setPmiDetails(response.data);
      
      // 获取PMI会议列表
      const meetingsResponse = await axios.get(`/api/pmi-reports/${pmiNumber}/meetings?page=0&size=20`);
      setPmiMeetings(meetingsResponse.data.content || []);
    } catch (error) {
      message.error('获取PMI详情失败');
      console.error('Error fetching PMI details:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOverviewData();
    fetchTrendsData(trendDays);
    fetchMostActiveData();
  }, []);

  useEffect(() => {
    fetchTrendsData(trendDays);
  }, [trendDays]);

  // 概览统计卡片
  const renderOverviewCards = () => (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="总PMI数量"
            value={overviewData.totalPmis || 0}
            prefix={<FileTextOutlined />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="活跃PMI数量"
            value={overviewData.activePmis || 0}
            prefix={<UserOutlined />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="30天内会议数"
            value={overviewData.recentMeetings30Days || 0}
            prefix={<CalendarOutlined />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="30天总时长(分钟)"
            value={overviewData.totalDurationMinutes30Days || 0}
            prefix={<ClockCircleOutlined />}
          />
        </Card>
      </Col>
    </Row>
  );

  // 趋势图表
  const renderTrendsChart = () => (
    <div style={{ marginBottom: 24 }}>
      <PmiTrendsChart
        data={trendsData}
        loading={loading}
        days={trendDays}
        onDaysChange={setTrendDays}
      />
    </div>
  );

  // 最活跃PMI表格
  const mostActiveColumns = [
    {
      title: 'PMI号码',
      dataIndex: 'pmiNumber',
      key: 'pmiNumber',
      render: (text) => (
        <Button type="link" onClick={() => {
          setSelectedPmi(text);
          fetchPmiDetails(text);
        }}>
          {text}
        </Button>
      ),
    },
    {
      title: '会议数量',
      dataIndex: 'meetingCount',
      key: 'meetingCount',
      sorter: (a, b) => a.meetingCount - b.meetingCount,
    },
    {
      title: '总时长(分钟)',
      dataIndex: 'totalDuration',
      key: 'totalDuration',
      sorter: (a, b) => a.totalDuration - b.totalDuration,
    },
    {
      title: '平均时长(分钟)',
      dataIndex: 'avgDuration',
      key: 'avgDuration',
      render: (value) => value ? value.toFixed(1) : '0',
    },
    {
      title: '总参与人数',
      dataIndex: 'totalParticipants',
      key: 'totalParticipants',
    },
  ];

  const renderMostActiveTable = () => (
    <Card title="最活跃PMI" style={{ marginBottom: 24 }}>
      <Table
        columns={mostActiveColumns}
        dataSource={Array.isArray(mostActiveData) ? mostActiveData : []}
        rowKey="pmiNumber"
        pagination={{ pageSize: 10 }}
        size="small"
      />
    </Card>
  );

  // PMI详情
  const renderPmiDetails = () => {
    if (!pmiDetails) return null;

    return (
      <Card title={`PMI详情: ${selectedPmi}`} style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 24 }}>
          <PmiStatsCard stats={pmiDetails.stats} loading={loading} />
        </div>

        <Card title="最近会议记录" size="small">
          <PmiMeetingList
            meetings={Array.isArray(pmiMeetings) ? pmiMeetings : []}
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </Card>
      </Card>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <h1>PMI报告管理</h1>
      
      <Spin spinning={loading}>
        <Tabs defaultActiveKey="overview">
          <TabPane tab="概览统计" key="overview">
            {renderOverviewCards()}
            {renderTrendsChart()}
            {renderMostActiveTable()}
          </TabPane>
          
          <TabPane tab="PMI详情" key="details">
            {selectedPmi ? (
              renderPmiDetails()
            ) : (
              <Card>
                <p>请从概览页面点击PMI号码查看详情，或在最活跃PMI表格中选择一个PMI。</p>
              </Card>
            )}
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default PmiReports;
