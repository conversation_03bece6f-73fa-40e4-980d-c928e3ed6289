import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  message,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  TimePicker,
  InputNumber,
  Switch,
  Row,
  Col,
  Statistic,
  Tooltip,
  Popconfirm,
  Tag,
  Divider,
  Collapse,
  Spin,
  Checkbox,
  Dropdown,
  Radio,
  Calendar,
  Alert,
  Descriptions
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EditOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  ArrowLeftOutlined,
  DownOutlined,
  RightOutlined,
  LeftOutlined,
  InfoCircleOutlined,
  FilterOutlined,
  MoreOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
  StopOutlined
} from '@ant-design/icons';
import { pmiScheduleApi, pmiApi } from '../services/api';
import { useParams, useNavigate } from 'react-router-dom';
import { getTaskTypeText, getTaskStatusText as getApiTaskStatusText, pmiTaskApi } from '../services/pmiTaskApi';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

// 任务状态颜色映射
const getTaskStatusColor = (status) => {
  const statusColors = {
    'SCHEDULED': 'blue',
    'EXECUTING': 'orange',
    'COMPLETED': 'green',
    'FAILED': 'red',
    'CANCELLED': 'default',
    'RETRY': 'yellow'
  };
  return statusColors[status] || 'default';
};

// 任务状态文本映射
const getTaskStatusText = (status) => {
  const statusTexts = {
    'SCHEDULED': '已调度',
    'EXECUTING': '执行中',
    'COMPLETED': '已完成',
    'FAILED': '失败',
    'CANCELLED': '已取消',
    'RETRY': '重试中'
  };
  return statusTexts[status] || status;
};

// 任务状态图标映射
const getTaskStatusIcon = (status) => {
  const statusIcons = {
    'SCHEDULED': <ClockCircleOutlined />,
    'EXECUTING': <PlayCircleOutlined />,
    'COMPLETED': <CheckCircleOutlined />,
    'FAILED': <ExclamationCircleOutlined />,
    'CANCELLED': <StopOutlined />,
    'RETRY': <ReloadOutlined />
  };
  return statusIcons[status] || <InfoCircleOutlined />;
};

const PmiScheduleManagement = () => {
  const { pmiRecordId } = useParams();
  const navigate = useNavigate();

  // 添加冲突窗口高亮样式
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .conflict-new-window-row {
        background-color: #fff2e8 !important;
        border: 2px solid #ff7a45 !important;
      }
      .conflict-new-window-row:hover {
        background-color: #ffe7d3 !important;
      }
      .conflict-new-window-row td {
        border-color: #ff7a45 !important;
        font-weight: bold;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const [schedules, setSchedules] = useState([]);
  const [pmiRecord, setPmiRecord] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState(null);
  const [createLoading, setCreateLoading] = useState(false);
  const [scheduleWindows, setScheduleWindows] = useState({});
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [loadingWindows, setLoadingWindows] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 批量操作相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchLoading, setBatchLoading] = useState(false);

  // 快捷选项选中状态
  const [selectedQuickOption, setSelectedQuickOption] = useState(null);

  // 日历相关状态
  const [calendarDate, setCalendarDate] = useState(dayjs());

  // 窗口编辑相关状态
  const [editWindowModalVisible, setEditWindowModalVisible] = useState(false);
  const [editingWindow, setEditingWindow] = useState(null);
  const [editWindowForm] = Form.useForm();

  // 冲突确认对话框状态
  const [conflictModalVisible, setConflictModalVisible] = useState(false);
  const [conflictData, setConflictData] = useState(null);
  const [pendingScheduleData, setPendingScheduleData] = useState(null);

  // 筛选相关状态
  const [filters, setFilters] = useState({
    status: null,
    dateRange: null,
    sortField: 'createdAt',
    sortOrder: 'desc'
  });

  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 任务详情弹窗状态
  const [taskDetailVisible, setTaskDetailVisible] = useState(false);
  const [taskDetail, setTaskDetail] = useState(null);
  const [taskDetailLoading, setTaskDetailLoading] = useState(false);

  // 生成计划名称的函数
  const generateScheduleName = (dateRange, repeatType, weekDays, monthDays, startTime, durationMinutes, isAllDay) => {
    if (!dateRange || !repeatType) return '';

    const startDate = dateRange[0];
    let endDate = dateRange[1];

    // 格式化日期
    const formatDate = (date) => date.format('YYYY-MM-DD');

    // 检查是否为跨日窗口
    if (!isAllDay && startTime && durationMinutes) {
      // 解析开始时间
      const [startHour, startMinute] = startTime.split(':').map(Number);
      const startTimeMinutes = startHour * 60 + startMinute;
      const endTimeMinutes = startTimeMinutes + durationMinutes;

      // 计算结束时间
      const endHour = Math.floor(endTimeMinutes / 60) % 24;
      const endMin = endTimeMinutes % 60;

      // 如果结束时间超过24小时，或者结束时间小于开始时间，说明是跨日窗口
      if (endTimeMinutes >= 1440 || (endHour < startHour || (endHour === startHour && endMin < startMinute))) {
        // 对于跨日窗口，实际结束日期应该是第二天
        endDate = startDate.clone().add(1, 'day');
      }
    }

    let repeatDesc = '';
    switch (repeatType) {
      case 'DAILY':
        repeatDesc = '每天';
        break;
      case 'WEEKLY':
        if (weekDays && weekDays.length > 0) {
          const weekDayNames = {
            1: '周一', 2: '周二', 3: '周三', 4: '周四',
            5: '周五', 6: '周六', 7: '周日'
          };
          const dayNames = weekDays.map(day => weekDayNames[day]).join('、');
          repeatDesc = `每周(${dayNames})`;
        } else {
          repeatDesc = '每周';
        }
        break;
      case 'MONTHLY':
        if (monthDays && monthDays.length > 0) {
          const dayNames = monthDays.map(day => `${day}号`).join('、');
          repeatDesc = `每月(${dayNames})`;
        } else {
          repeatDesc = '每月';
        }
        break;
      default:
        repeatDesc = '重复';
    }

    return `${formatDate(startDate)} ~ ${formatDate(endDate)} ${repeatDesc}`;
  };

  // 检查是否为自动生成的计划名称
  const isAutoGeneratedName = (name, dateRange, repeatType, weekDays, monthDays, startTime, durationMinutes, isAllDay) => {
    if (!name || !dateRange || !repeatType) return false;

    const expectedName = generateScheduleName(dateRange, repeatType, weekDays, monthDays, startTime, durationMinutes, isAllDay);
    return name === expectedName;
  };

  // 自动生成计划名称
  const autoGenerateScheduleName = (form, forceUpdate = false) => {
    const currentName = form.getFieldValue('name');
    const dateRange = form.getFieldValue('dateRange');
    const repeatType = form.getFieldValue('repeatType');
    const weekDays = form.getFieldValue('weekDays');
    const monthDays = form.getFieldValue('monthDays');
    const isAllDay = form.getFieldValue('isAllDay');

    // 获取时间信息
    let startTime = null;
    let durationMinutes = null;

    if (!isAllDay) {
      const startHour = form.getFieldValue('startHour') || 0;
      const startMinute = form.getFieldValue('startMinute') || 0;
      startTime = `${String(startHour).padStart(2, '0')}:${String(startMinute).padStart(2, '0')}`;

      const durationHours = form.getFieldValue('durationHours') || 0;
      const durationMins = form.getFieldValue('durationMinutes') || 0;
      durationMinutes = durationHours * 60 + durationMins;
    }

    // 生成新的计划名称
    const generatedName = generateScheduleName(dateRange, repeatType, weekDays, monthDays, startTime, durationMinutes, isAllDay);

    if (generatedName) {
      // 如果强制更新，或者计划名称为空，或者当前名称是之前自动生成的，则更新
      if (forceUpdate ||
          !currentName ||
          currentName.trim() === '' ||
          isAutoGeneratedName(currentName, dateRange, repeatType, weekDays, monthDays, startTime, durationMinutes, isAllDay)) {
        form.setFieldsValue({ name: generatedName });
      }
    }
  };

  // 快捷设置处理函数
  const handleQuickSet = (type, form = createForm) => {
    const now = dayjs();
    let dateRange, repeatType, isAllDay, startHour, startMinute, durationHours, durationMinutes, description;

    switch (type) {
      case '1hour':
        dateRange = [now, now];
        repeatType = 'DAILY';
        isAllDay = false;
        startHour = now.hour();
        startMinute = now.minute(); // 使用当前精确的分钟
        durationHours = 1;
        durationMinutes = 0;
        description = '即刻开始的1小时计划';
        break;
      case '2hours':
        dateRange = [now, now];
        repeatType = 'DAILY';
        isAllDay = false;
        startHour = now.hour();
        startMinute = now.minute(); // 使用当前精确的分钟
        durationHours = 2;
        durationMinutes = 0;
        description = '即刻开始的2小时计划';
        break;
      case '3hours':
        dateRange = [now, now];
        repeatType = 'DAILY';
        isAllDay = false;
        startHour = now.hour();
        startMinute = now.minute(); // 使用当前精确的分钟
        durationHours = 3;
        durationMinutes = 0;
        description = '即刻开始的3小时计划';
        break;
      case 'today':
        dateRange = [now, now];
        repeatType = 'DAILY';
        isAllDay = true;
        description = '今天全天计划';
        break;
      case 'tomorrow':
        const tomorrow = now.add(1, 'day');
        dateRange = [tomorrow, tomorrow];
        repeatType = 'DAILY';
        isAllDay = true;
        description = '明天全天计划';
        break;
      case 'month':
        const monthEnd = now.add(1, 'month').subtract(1, 'day');
        dateRange = [now, monthEnd];
        repeatType = 'DAILY';
        isAllDay = true;
        description = '一个月每天计划';
        break;
      case 'year':
        const yearEnd = now.add(1, 'year').subtract(1, 'day');
        dateRange = [now, yearEnd];
        repeatType = 'DAILY';
        isAllDay = true;
        description = '一年每天计划';
        break;
      default:
        return;
    }

    // 设置表单值
    const formValues = {
      dateRange,
      repeatType,
      isAllDay,
      name: description,
      weekDays: undefined,
      monthDays: undefined,
    };

    // 如果是指定时段，添加时间相关字段
    if (!isAllDay) {
      formValues.startHour = startHour;
      formValues.startMinute = startMinute;
      formValues.durationHours = durationHours;
      formValues.durationMinutes = durationMinutes;
    }

    form.setFieldsValue(formValues);

    // 设置选中的快捷选项
    setSelectedQuickOption(type);

    // 延迟执行自动生成计划名称
    setTimeout(() => autoGenerateScheduleName(form, true), 100);
  };

  // 加载PMI记录信息
  const loadPmiRecord = async () => {
    try {
      const response = await pmiApi.getPmiRecordById(pmiRecordId);
      setPmiRecord(response.data);
    } catch (error) {
      message.error('加载PMI记录失败');
      console.error('Error loading PMI record:', error);
    }
  };

  // 加载计划列表
  const loadSchedules = async (page = 1, pageSize = 10, customFilters = null) => {
    try {
      setLoading(true);
      const currentFilters = customFilters || filters;
      const params = {
        pmiRecordId: pmiRecordId,
        page: page - 1,
        size: pageSize,
        sort: `${currentFilters.sortField},${currentFilters.sortOrder}`,
      };

      // 添加状态筛选
      if (currentFilters.status) {
        params.status = currentFilters.status;
      }

      // 添加日期范围筛选
      if (currentFilters.dateRange && currentFilters.dateRange.length === 2) {
        params.startDate = currentFilters.dateRange[0].format('YYYY-MM-DD');
        params.endDate = currentFilters.dateRange[1].format('YYYY-MM-DD');
      }

      const response = await pmiScheduleApi.getSchedules(params);
      const pageData = response.data.data; // 后端返回的是 {success: true, data: Page对象}
      setSchedules(pageData.content || []);
      setPagination({
        current: page,
        pageSize: pageSize,
        total: pageData.totalElements || 0,
      });
    } catch (error) {
      message.error('加载计划列表失败');
      console.error('Error loading schedules:', error);
    } finally {
      setLoading(false);
    }
  };

  // 防抖状态
  const [isClickDisabled, setIsClickDisabled] = useState(false);

  // 处理安排计划按钮点击
  const handleCreatePlanClick = useCallback(() => {
    // 防止重复点击
    if (isClickDisabled || createModalVisible) return;

    setIsClickDisabled(true);
    setCreateModalVisible(true);

    // 200ms后重新启用点击
    setTimeout(() => {
      setIsClickDisabled(false);
    }, 200);
  }, [isClickDisabled, createModalVisible]);

  // 创建计划
  const handleCreateSubmit = async (values) => {
    // 处理时间字段
    let startTime = null;
    let durationMinutes = null;

    if (!values.isAllDay) {
      // 合并小时和分钟为时间字符串
      const hour = String(values.startHour || 0).padStart(2, '0');
      const minute = String(values.startMinute || 0).padStart(2, '0');
      startTime = `${hour}:${minute}:00`;

      // 计算总持续时间（分钟）
      durationMinutes = (values.durationHours || 0) * 60 + (values.durationMinutes || 0);
    }

    // 自动生成计划名称
    const generatedName = generateScheduleName(
      values.dateRange,
      values.repeatType,
      values.weekDays,
      values.monthDays,
      startTime,
      durationMinutes,
      values.isAllDay
    );

    const scheduleData = {
      pmiRecordId: parseInt(pmiRecordId),
      name: generatedName || '未命名计划', // 使用自动生成的名称，如果生成失败则使用默认名称
      description: values.name || null, // 将原来的name字段作为description
      startDate: values.dateRange[0].format('YYYY-MM-DD'),
      endDate: values.dateRange[1].format('YYYY-MM-DD'),
      repeatType: values.repeatType,
      weekDays: values.repeatType === 'WEEKLY' ? values.weekDays : null,
      monthDays: values.repeatType === 'MONTHLY' ? values.monthDays : null,
      isAllDay: values.isAllDay,
      startTime: startTime,
      durationMinutes: durationMinutes,
    };

    try {
      setCreateLoading(true);

      await pmiScheduleApi.createSchedule(scheduleData);
      message.success('计划创建成功');
      setCreateModalVisible(false);
      createForm.resetFields();
      setSelectedQuickOption(null);

      // 立即刷新一次
      loadSchedules(pagination.current, pagination.pageSize);

      // 延迟3秒后再次刷新，确保后台任务执行完成，窗口统计准确
      setTimeout(() => {
        loadSchedules(pagination.current, pagination.pageSize);
      }, 3000);
    } catch (error) {
      // 检查是否是窗口冲突错误
      if (error.response?.status === 409 && error.response?.data?.conflict) {
        // 显示冲突确认对话框
        showConflictConfirmDialog(scheduleData, error.response.data.data);
      } else {
        message.error('创建计划失败: ' + (error.response?.data?.message || error.message));
        console.error('Error creating schedule:', error);
      }
    } finally {
      setCreateLoading(false);
    }
  };

  // 显示冲突确认对话框
  const showConflictConfirmDialog = (scheduleData, conflictInfo) => {
    setConflictData(conflictInfo);
    setPendingScheduleData(scheduleData);
    setConflictModalVisible(true);
    setCreateLoading(false); // 停止创建按钮的loading状态
  };

  // 确认合并冲突窗口
  const handleConfirmMerge = async () => {
    try {
      setCreateLoading(true);
      await pmiScheduleApi.createScheduleWithMerge(pendingScheduleData);
      message.success('计划创建成功，冲突窗口已合并');
      setConflictModalVisible(false);
      setCreateModalVisible(false);
      createForm.resetFields();
      setSelectedQuickOption(null);

      // 立即刷新一次
      loadSchedules(pagination.current, pagination.pageSize);

      // 延迟3秒后再次刷新，确保后台任务执行完成，窗口统计准确
      setTimeout(() => {
        loadSchedules(pagination.current, pagination.pageSize);
      }, 3000);
    } catch (error) {
      message.error('创建计划失败: ' + (error.response?.data?.message || error.message));
      console.error('Error creating schedule with merge:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  // 取消合并，关闭冲突对话框
  const handleCancelMerge = () => {
    setConflictModalVisible(false);
    setConflictData(null);
    setPendingScheduleData(null);
  };

  // 编辑计划
  const handleEdit = (record) => {
    setEditingSchedule(record);

    // 处理时间字段的回显
    let startHour = 0;
    let startMinute = 0;
    let durationHours = 0;
    let durationMinutes = 0;

    if (record.startTime) {
      const timeParts = record.startTime.split(':');
      startHour = parseInt(timeParts[0]) || 0;
      startMinute = parseInt(timeParts[1]) || 0;
    }

    if (record.durationMinutes) {
      durationHours = Math.floor(record.durationMinutes / 60);
      durationMinutes = record.durationMinutes % 60;
    }

    editForm.setFieldsValue({
      name: record.description || '', // 将description字段回显到name字段（计划描述）
      dateRange: [dayjs(record.startDate), dayjs(record.endDate)],
      repeatType: record.repeatType,
      weekDays: record.weekDays,
      monthDays: record.monthDays,
      isAllDay: record.isAllDay,
      startHour: startHour,
      startMinute: startMinute,
      durationHours: durationHours,
      durationMinutes: durationMinutes,
    });
    setEditModalVisible(true);
  };

  // 更新计划
  const handleEditSubmit = async (values) => {
    try {
      setCreateLoading(true);

      // 处理时间字段
      let startTime = null;
      let durationMinutes = null;

      if (!values.isAllDay) {
        // 合并小时和分钟为时间字符串
        const hour = String(values.startHour || 0).padStart(2, '0');
        const minute = String(values.startMinute || 0).padStart(2, '0');
        startTime = `${hour}:${minute}:00`;

        // 计算总持续时间（分钟）
        durationMinutes = (values.durationHours || 0) * 60 + (values.durationMinutes || 0);
      }

      // 自动生成计划名称
      const generatedName = generateScheduleName(
        values.dateRange,
        values.repeatType,
        values.weekDays,
        values.monthDays,
        startTime,
        durationMinutes,
        values.isAllDay
      );

      const scheduleData = {
        pmiRecordId: parseInt(pmiRecordId),
        name: generatedName || editingSchedule?.name || '未命名计划', // 使用自动生成的名称，如果生成失败则使用原名称或默认名称
        description: values.name || null, // 将原来的name字段作为description
        startDate: values.dateRange[0].format('YYYY-MM-DD'),
        endDate: values.dateRange[1].format('YYYY-MM-DD'),
        repeatType: values.repeatType,
        weekDays: values.repeatType === 'WEEKLY' ? values.weekDays : null,
        monthDays: values.repeatType === 'MONTHLY' ? values.monthDays : null,
        isAllDay: values.isAllDay,
        startTime: startTime,
        durationMinutes: durationMinutes,
      };

      await pmiScheduleApi.updateSchedule(editingSchedule.id, scheduleData);
      message.success('计划更新成功');
      setEditModalVisible(false);
      editForm.resetFields();
      setEditingSchedule(null);
      setSelectedQuickOption(null);
      loadSchedules(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('更新计划失败');
      console.error('Error updating schedule:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  // 删除计划
  const handleDelete = async (id) => {
    try {
      await pmiScheduleApi.deleteSchedule(id);
      message.success('计划删除成功');
      loadSchedules(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('删除计划失败');
      console.error('Error deleting schedule:', error);
    }
  };

  // 更新计划状态
  const handleUpdateStatus = async (id, status) => {
    try {
      await pmiScheduleApi.updateScheduleStatus(id, status);
      message.success('状态更新成功');
      loadSchedules(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('更新状态失败');
      console.error('Error updating status:', error);
    }
  };

  // 批量删除计划
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的计划');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个计划吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setBatchLoading(true);
          await pmiScheduleApi.batchDeleteSchedules(selectedRowKeys);
          message.success(`成功删除 ${selectedRowKeys.length} 个计划`);
          setSelectedRowKeys([]);
          loadSchedules(pagination.current, pagination.pageSize);
        } catch (error) {
          message.error('批量删除失败');
          console.error('Error batch deleting:', error);
        } finally {
          setBatchLoading(false);
        }
      }
    });
  };

  // 批量更新状态
  const handleBatchUpdateStatus = async (status) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要更新的计划');
      return;
    }

    try {
      setBatchLoading(true);
      await pmiScheduleApi.batchUpdateStatus({
        scheduleIds: selectedRowKeys,
        status: status
      });
      message.success(`成功更新 ${selectedRowKeys.length} 个计划状态`);
      setSelectedRowKeys([]);
      loadSchedules(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('批量更新状态失败');
      console.error('Error batch updating status:', error);
    } finally {
      setBatchLoading(false);
    }
  };

  // 筛选处理
  const handleFilterChange = (newFilters) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    loadSchedules(1, pagination.pageSize, updatedFilters);
  };

  // 重置筛选
  const handleResetFilters = () => {
    const defaultFilters = {
      status: null,
      dateRange: null,
      sortField: 'createdAt',
      sortOrder: 'desc'
    };
    setFilters(defaultFilters);
    loadSchedules(1, pagination.pageSize, defaultFilters);
  };

  // 加载计划窗口
  const loadScheduleWindows = async (scheduleId, forceRefresh = false) => {
    // 如果已经在加载，则不重复加载
    if (loadingWindows[scheduleId]) {
      return;
    }

    // 如果不是强制刷新且已经有数据，则不重复加载
    if (!forceRefresh && scheduleWindows[scheduleId]) {
      return;
    }

    setLoadingWindows(prev => ({ ...prev, [scheduleId]: true }));

    try {
      const response = await pmiScheduleApi.getScheduleWindows(scheduleId, {
        page: 0,
        size: 50,
        sortBy: 'startDateTime',
        sortDir: 'asc'
      });
      const pageData = response.data.data; // 后端返回的是 {success: true, data: Page对象}
      setScheduleWindows(prev => ({
        ...prev,
        [scheduleId]: pageData.content || []
      }));
    } catch (error) {
      console.error('加载计划窗口失败:', error);
      message.error('加载计划窗口失败');
      setScheduleWindows(prev => ({
        ...prev,
        [scheduleId]: [] // 设置为空数组表示加载失败
      }));
    } finally {
      setLoadingWindows(prev => ({ ...prev, [scheduleId]: false }));
    }
  };

  // 编辑窗口
  const handleEditWindow = (windowRecord) => {
    setEditingWindow(windowRecord);
    editWindowForm.setFieldsValue({
      windowDate: dayjs(windowRecord.windowDate),
      startTime: dayjs(windowRecord.startTime, 'HH:mm:ss'),
      endTime: dayjs(windowRecord.endTime, 'HH:mm:ss'),
    });
    setEditWindowModalVisible(true);
  };

  // 删除窗口
  const handleDeleteWindow = async (windowId, scheduleId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个计划窗口吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await pmiScheduleApi.deleteScheduleWindow(windowId);
          message.success('窗口删除成功');
          // 重新加载窗口列表（强制刷新）
          loadScheduleWindows(scheduleId, true);
          // 重新加载计划列表以更新统计
          loadSchedules(pagination.current, pagination.pageSize);
        } catch (error) {
          // 如果窗口不存在，认为删除成功（目标已达到）
          if (error.response && error.response.data &&
              error.response.data.message &&
              error.response.data.message.includes('计划窗口不存在')) {
            message.success('窗口删除成功');
            // 重新加载窗口列表（强制刷新）
            loadScheduleWindows(scheduleId, true);
            // 重新加载计划列表以更新统计
            loadSchedules(pagination.current, pagination.pageSize);
          } else {
            message.error('删除窗口失败');
            console.error('Error deleting window:', error);
          }
        }
      }
    });
  };

  // 一键关闭窗口
  const handleCloseWindow = async (windowId, scheduleId) => {
    Modal.confirm({
      title: '确认关闭',
      content: '确定要关闭这个计划窗口吗？关闭窗口会联动关闭PMI为"非活跃"状态。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await pmiScheduleApi.closeScheduleWindow(windowId);
          message.success('窗口关闭成功');
          // 重新加载窗口列表（强制刷新）
          loadScheduleWindows(scheduleId, true);
          // 重新加载计划列表以更新统计
          loadSchedules(pagination.current, pagination.pageSize);
          // 重新加载PMI记录以更新状态
          loadPmiRecord();
        } catch (error) {
          message.error('关闭窗口失败');
          console.error('Error closing window:', error);
        }
      }
    });
  };

  // 延长窗口
  const handleExtendWindow = (windowId, scheduleId, windowRecord) => {
    // 延长窗口内容组件
    const ExtendWindowContent = () => {
      const [selectedMinutes, setSelectedMinutes] = useState(60);

      // 计算当前结束时间
      const currentEndDateTime = windowRecord.endDateTime ? dayjs(windowRecord.endDateTime) : dayjs();

      // 计算延长后的时间
      const extendedEndDateTime = currentEndDateTime.add(selectedMinutes, 'minute');

      return (
        <div>
          <p style={{ marginBottom: '16px' }}>选择延长时间：</p>
          <Radio.Group
            value={selectedMinutes}
            onChange={(e) => {
              setSelectedMinutes(e.target.value);
              // 存储选择的延长时间
              Modal.extendMinutes = e.target.value;
            }}
          >
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '8px 16px',
              alignItems: 'start'
            }}>
              <Radio value={60}>1小时</Radio>
              <Radio value={120}>2小时</Radio>
              <Radio value={180}>3小时</Radio>
              <Radio value={1440}>1天</Radio>
              <Radio value={43200}>1个月</Radio>
              <Radio value={525600}>1年</Radio>
            </div>
          </Radio.Group>

          {/* 延长前后时间显示 */}
          <div style={{
            marginTop: '24px',
            padding: '16px',
            backgroundColor: '#f5f5f5',
            borderRadius: '6px',
            border: '1px solid #d9d9d9'
          }}>
            <div style={{ marginBottom: '12px' }}>
              <strong style={{ color: '#595959' }}>延长前到期时间：</strong>
              <div style={{
                fontSize: '16px',
                color: '#262626',
                marginTop: '4px',
                fontFamily: 'monospace'
              }}>
                {currentEndDateTime.format('YYYY-MM-DD HH:mm:ss')}
              </div>
            </div>

            <div>
              <strong style={{ color: '#1890ff' }}>延长后到期时间：</strong>
              <div style={{
                fontSize: '16px',
                color: '#1890ff',
                marginTop: '4px',
                fontFamily: 'monospace',
                fontWeight: 'bold'
              }}>
                {extendedEndDateTime.format('YYYY-MM-DD HH:mm:ss')}
              </div>
            </div>

            {/* 显示延长的时长 */}
            <div style={{
              marginTop: '12px',
              padding: '8px 12px',
              backgroundColor: '#e6f7ff',
              borderRadius: '4px',
              fontSize: '14px',
              color: '#1890ff'
            }}>
              <strong>延长时长：</strong>
              {selectedMinutes === 60 && '1小时'}
              {selectedMinutes === 120 && '2小时'}
              {selectedMinutes === 180 && '3小时'}
              {selectedMinutes === 1440 && '1天'}
              {selectedMinutes === 43200 && '1个月'}
              {selectedMinutes === 525600 && '1年'}
              {![60, 120, 180, 1440, 43200, 525600].includes(selectedMinutes) && `${selectedMinutes}分钟`}
            </div>
          </div>
        </div>
      );
    };

    Modal.confirm({
      title: '延长窗口',
      width: 520,
      content: <ExtendWindowContent />,
      okText: '确定延长',
      cancelText: '取消',
      onOk: async () => {
        try {
          const minutes = Modal.extendMinutes || 60;
          let displayText = '';

          // 根据延长时间显示不同的文本
          if (minutes === 60) displayText = '1小时';
          else if (minutes === 120) displayText = '2小时';
          else if (minutes === 180) displayText = '3小时';
          else if (minutes === 1440) displayText = '1天';
          else if (minutes === 43200) displayText = '1个月';
          else if (minutes === 525600) displayText = '1年';
          else displayText = `${minutes}分钟`;

          await pmiScheduleApi.extendScheduleWindow(windowId, minutes);
          message.success(`窗口延长${displayText}成功`);
          // 重新加载窗口列表（强制刷新）
          loadScheduleWindows(scheduleId, true);
          // 重新加载计划列表以更新统计
          loadSchedules(pagination.current, pagination.pageSize);
        } catch (error) {
          message.error('延长窗口失败: ' + (error.response?.data?.message || error.message));
          console.error('Error extending window:', error);
        }
      }
    });
  };

  // 批量操作相关状态
  const [selectedWindowIds, setSelectedWindowIds] = useState([]);
  const [batchOperationVisible, setBatchOperationVisible] = useState(false);

  // 批量选择窗口
  const handleWindowSelection = (windowId, checked) => {
    if (checked) {
      setSelectedWindowIds(prev => [...prev, windowId]);
    } else {
      setSelectedWindowIds(prev => prev.filter(id => id !== windowId));
    }
  };

  // 全选/取消全选窗口
  const handleSelectAllWindows = (scheduleId, checked) => {
    const windows = scheduleWindows[scheduleId] || [];
    const windowIds = windows.map(w => w.id);

    if (checked) {
      setSelectedWindowIds(prev => [...new Set([...prev, ...windowIds])]);
    } else {
      setSelectedWindowIds(prev => prev.filter(id => !windowIds.includes(id)));
    }
  };

  // 批量关闭窗口
  const handleBatchCloseWindows = async () => {
    if (selectedWindowIds.length === 0) {
      message.warning('请先选择要关闭的窗口');
      return;
    }

    Modal.confirm({
      title: '批量关闭窗口',
      content: `确定要关闭选中的 ${selectedWindowIds.length} 个窗口吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 并行执行所有关闭操作
          await Promise.all(
            selectedWindowIds.map(windowId =>
              pmiScheduleApi.closeScheduleWindow(windowId)
            )
          );

          message.success(`成功关闭 ${selectedWindowIds.length} 个窗口`);
          setSelectedWindowIds([]);

          // 重新加载所有相关数据
          loadSchedules(pagination.current, pagination.pageSize);
          loadPmiRecord();

          // 重新加载所有展开的窗口列表
          Object.keys(scheduleWindows).forEach(scheduleId => {
            loadScheduleWindows(scheduleId, true);
          });

        } catch (error) {
          message.error('批量关闭窗口失败: ' + (error.response?.data?.message || error.message));
          console.error('Error batch closing windows:', error);
        }
      }
    });
  };

  // 批量删除窗口
  const handleBatchDeleteWindows = async () => {
    if (selectedWindowIds.length === 0) {
      message.warning('请先选择要删除的窗口');
      return;
    }

    Modal.confirm({
      title: '批量删除窗口',
      content: `确定要删除选中的 ${selectedWindowIds.length} 个窗口吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 并行执行所有删除操作
          await Promise.all(
            selectedWindowIds.map(windowId =>
              pmiScheduleApi.deleteScheduleWindow(windowId)
            )
          );

          message.success(`成功删除 ${selectedWindowIds.length} 个窗口`);
          setSelectedWindowIds([]);

          // 重新加载所有相关数据
          loadSchedules(pagination.current, pagination.pageSize);

          // 重新加载所有展开的窗口列表
          Object.keys(scheduleWindows).forEach(scheduleId => {
            loadScheduleWindows(scheduleId, true);
          });

        } catch (error) {
          message.error('批量删除窗口失败: ' + (error.response?.data?.message || error.message));
          console.error('Error batch deleting windows:', error);
        }
      }
    });
  };

  // 更新窗口
  const handleUpdateWindow = async (values) => {
    try {
      const windowData = {
        windowDate: values.windowDate.format('YYYY-MM-DD'),
        startTime: values.startTime.format('HH:mm:ss'),
        endTime: values.endTime.format('HH:mm:ss'),
      };

      await pmiScheduleApi.updateScheduleWindow(editingWindow.id, windowData);
      message.success('窗口更新成功');
      setEditWindowModalVisible(false);
      editWindowForm.resetFields();
      const scheduleId = editingWindow.scheduleId;
      setEditingWindow(null);
      // 重新加载窗口列表（强制刷新）
      loadScheduleWindows(scheduleId, true);
      // 重新加载计划列表以更新统计
      loadSchedules(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('更新窗口失败');
      console.error('Error updating window:', error);
    }
  };

  // 查看任务详情
  const handleViewTaskDetail = async (taskId) => {
    try {
      console.log('开始获取任务详情, taskId:', taskId);
      setTaskDetailLoading(true);
      setTaskDetailVisible(true);

      // 调用PMI任务API获取任务详情
      const response = await pmiTaskApi.getTaskDetail(taskId);
      console.log('API响应:', response);

      if (response && response.data && response.data.success && response.data.data) {
        // 标准的后端响应格式：{success: true, data: {...}}
        const taskData = response.data.data;
        console.log('✅ 任务详情数据:', taskData);
        setTaskDetail(taskData);
      } else {
        console.error('❌ API响应格式错误:', response);
        throw new Error('API响应格式错误');
      }
    } catch (error) {
      console.error('加载任务详情失败:', error);
      console.error('错误详情:', {
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data
      });

      let errorMessage = '加载任务详情失败';
      let errorType = 'LOAD_FAILED';

      // 检查是否是权限问题
      if (error.response && error.response.status === 403) {
        errorMessage = '权限不足：需要管理员权限才能查看任务详情';
        errorType = 'PERMISSION_DENIED';
        message.error(errorMessage);
      } else if (error.response && error.response.status === 404) {
        errorMessage = '任务不存在或已被删除';
        errorType = 'NOT_FOUND';
        message.error(errorMessage);
      } else if (error.response && error.response.status >= 500) {
        errorMessage = '服务器内部错误，请稍后重试';
        errorType = 'SERVER_ERROR';
        message.error(errorMessage);
      } else {
        errorMessage = `加载失败: ${error.message}`;
        message.error(errorMessage);
      }

      setTaskDetail({
        id: taskId,
        error: errorType,
        message: errorMessage
      });
    } finally {
      setTaskDetailLoading(false);
    }
  };

  // 关闭任务详情弹窗
  const handleCloseTaskDetail = () => {
    setTaskDetailVisible(false);
    setTaskDetail(null);
  };

  // 渲染展开行内容 - 显示计划窗口
  const renderExpandedRow = (record) => {
    const scheduleId = record.id;
    const windows = scheduleWindows[scheduleId];
    const isLoading = loadingWindows[scheduleId];

    // 如果正在加载或者数据还未加载，显示加载状态
    if (isLoading || windows === undefined) {
      return (
        <div style={{ padding: '16px', textAlign: 'center' }}>
          <Spin size="small" />
          <span style={{ marginLeft: 8 }}>加载计划窗口中...</span>
        </div>
      );
    }

    // 如果数据已加载且为空数组，显示"暂无信息"
    if (windows && windows.length === 0) {
      return (
        <div style={{ padding: '16px', color: '#999' }}>
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          暂无计划窗口信息
        </div>
      );
    }

    // 渲染窗口列表
    const windowColumns = [
      {
        title: (
          <Checkbox
            indeterminate={selectedWindowIds.length > 0 && selectedWindowIds.length < windows.length}
            checked={windows.length > 0 && selectedWindowIds.length === windows.length}
            onChange={(e) => handleSelectAllWindows(record.id, e.target.checked)}
          >
            全选
          </Checkbox>
        ),
        dataIndex: 'selection',
        key: 'selection',
        width: 80,
        render: (_, windowRecord) => (
          <Checkbox
            checked={selectedWindowIds.includes(windowRecord.id)}
            onChange={(e) => handleWindowSelection(windowRecord.id, e.target.checked)}
          />
        )
      },
      {
        title: '窗口ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
        render: (id) => (
          <span style={{ fontFamily: 'monospace', fontSize: '12px', color: '#666' }}>
            {id}
          </span>
        ),
      },
      {
        title: '开始时间',
        dataIndex: 'startDateTime',
        key: 'startDateTime',
        render: (startDateTime) => {
          if (startDateTime) {
            return dayjs(startDateTime).format('YYYY-MM-DD HH:mm:ss');
          }
          return '-';
        },
      },
      {
        title: '结束时间',
        dataIndex: 'endDateTime',
        key: 'endDateTime',
        render: (endDateTime) => {
          if (endDateTime) {
            return dayjs(endDateTime).format('YYYY-MM-DD HH:mm:ss');
          }
          return '-';
        },
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status) => {
          const statusConfig = {
            PENDING: { color: 'blue', text: '待执行' },
            ACTIVE: { color: 'green', text: '执行中' },
            COMPLETED: { color: 'gray', text: '已完成' },
            MANUALLY_CLOSED: { color: 'orange', text: '人工关闭' },
            CANCELLED: { color: 'red', text: '已取消' },
            FAILED: { color: 'red', text: '执行失败' }
          };
          const config = statusConfig[status] || { color: 'default', text: status };
          return <Tag color={config.color}>{config.text}</Tag>;
        },
      },
      {
        title: '实际开启时间',
        dataIndex: 'actualStartTime',
        key: 'actualStartTime',
        render: (time) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',
      },
      {
        title: '实际关闭时间',
        dataIndex: 'actualEndTime',
        key: 'actualEndTime',
        render: (time) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '任务状态',
        key: 'taskStatus',
        width: 200,
        render: (_, windowRecord) => {
          const { openTask, closeTask } = windowRecord;
          return (
            <div>
              <div style={{ marginBottom: 4 }}>
                <span style={{ fontSize: '12px', color: '#666' }}>开启任务:</span>
                {openTask ? (
                  <Space size={4}>
                    <Tag
                      color={getTaskStatusColor(openTask.status)}
                      size="small"
                    >
                      {getTaskStatusText(openTask.status)}
                    </Tag>
                    <Button
                      type="link"
                      size="small"
                      style={{ padding: 0, height: 'auto', fontSize: '12px' }}
                      onClick={() => handleViewTaskDetail(openTask.id)}
                    >
                      详情
                    </Button>
                  </Space>
                ) : (
                  <Tag color="default" size="small">无任务</Tag>
                )}
              </div>
              <div>
                <span style={{ fontSize: '12px', color: '#666' }}>关闭任务:</span>
                {closeTask ? (
                  <Space size={4}>
                    <Tag
                      color={getTaskStatusColor(closeTask.status)}
                      size="small"
                    >
                      {getTaskStatusText(closeTask.status)}
                    </Tag>
                    <Button
                      type="link"
                      size="small"
                      style={{ padding: 0, height: 'auto', fontSize: '12px' }}
                      onClick={() => handleViewTaskDetail(closeTask.id)}
                    >
                      详情
                    </Button>
                  </Space>
                ) : (
                  <Tag color="default" size="small">无任务</Tag>
                )}
              </div>
            </div>
          );
        },
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        render: (_, windowRecord) => (
          <Space size="small">
            {windowRecord.status === 'PENDING' && (
              <>
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEditWindow(windowRecord)}
                >
                  编辑
                </Button>
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeleteWindow(windowRecord.id, record.id)}
                >
                  删除
                </Button>
              </>
            )}
            {windowRecord.status === 'ACTIVE' && (
              <>
                <Button
                  type="link"
                  size="small"
                  icon={<ClockCircleOutlined />}
                  onClick={() => handleExtendWindow(windowRecord.id, record.id, windowRecord)}
                >
                  延长窗口
                </Button>
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<CloseOutlined />}
                  onClick={() => handleCloseWindow(windowRecord.id, record.id)}
                >
                  关闭窗口
                </Button>
              </>
            )}
          </Space>
        ),
      },
    ];

    return (
      <div style={{
        margin: '8px 16px 16px 16px',
        padding: '16px',
        backgroundColor: '#f8f9fa',
        border: '1px solid #e9ecef',
        borderRadius: '6px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}>
        <Title level={5} style={{ marginBottom: 16, color: '#495057' }}>
          计划窗口列表 (共 {windows.length} 个)
        </Title>

        {/* 批量操作工具栏 */}
        {selectedWindowIds.length > 0 && (
          <div style={{
            marginBottom: 16,
            padding: '12px 16px',
            backgroundColor: '#e6f7ff',
            border: '1px solid #91d5ff',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <span style={{ color: '#1890ff', fontWeight: 500 }}>
              已选择 {selectedWindowIds.length} 个窗口
            </span>
            <Space>
              <Button
                type="primary"
                danger
                size="small"
                icon={<CloseOutlined />}
                onClick={handleBatchCloseWindows}
              >
                批量关闭
              </Button>
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
                onClick={handleBatchDeleteWindows}
              >
                批量删除
              </Button>
              <Button
                size="small"
                onClick={() => setSelectedWindowIds([])}
              >
                取消选择
              </Button>
            </Space>
          </div>
        )}

        <Table
          columns={windowColumns}
          dataSource={windows}
          rowKey="id"
          pagination={false}
          size="small"
          scroll={{ x: 800 }}
          style={{
            backgroundColor: '#ffffff',
            borderRadius: '4px'
          }}
        />

        {/* 日历视图 */}
        <div style={{ marginTop: '24px' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '16px'
          }}>
            <Title level={5} style={{ margin: 0, color: '#495057' }}>
              日历视图
            </Title>
            <Space>
              <Button
                icon={<LeftOutlined />}
                onClick={() => setCalendarDate(calendarDate.subtract(1, 'month'))}
                size="small"
              >
                上一月
              </Button>
              <span style={{
                fontSize: '16px',
                fontWeight: 'bold',
                minWidth: '120px',
                textAlign: 'center',
                display: 'inline-block'
              }}>
                {calendarDate.format('YYYY年MM月')}
              </span>
              <Button
                icon={<RightOutlined />}
                onClick={() => setCalendarDate(calendarDate.add(1, 'month'))}
                size="small"
              >
                下一月
              </Button>
            </Space>
          </div>

          <Calendar
            value={calendarDate}
            mode="month"
            fullscreen={false}
            headerRender={() => null} // 隐藏默认头部，使用自定义头部
            cellRender={(date, info) => {
              // 只处理日期单元格
              if (info.type !== 'date') return info.originNode;
              // 获取当前日期的窗口
              const dateStr = date.format('YYYY-MM-DD');
              const dayWindows = windows.filter(window => {
                // 使用精确时间字段
                if (window.startDateTime && window.endDateTime) {
                  const startDate = dayjs(window.startDateTime).format('YYYY-MM-DD');
                  const endDate = dayjs(window.endDateTime).format('YYYY-MM-DD');
                  return dateStr >= startDate && dateStr <= endDate;
                }
                return false;
              });

              if (dayWindows.length === 0) return null;

              return (
                <div style={{ padding: '2px' }}>
                  {dayWindows.map(window => (
                    <div
                      key={window.id}
                      style={{
                        fontSize: '10px',
                        padding: '1px 4px',
                        margin: '1px 0',
                        borderRadius: '2px',
                        backgroundColor:
                          window.status === 'ACTIVE' ? '#52c41a' :
                          window.status === 'COMPLETED' ? '#1890ff' :
                          window.status === 'PENDING' ? '#faad14' :
                          window.status === 'FAILED' ? '#ff4d4f' : '#d9d9d9',
                        color: 'white',
                        textAlign: 'center',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                      title={`${
                        window.startDateTime ? dayjs(window.startDateTime).format('HH:mm:ss') : '-'
                      }-${
                        window.endDateTime ? dayjs(window.endDateTime).format('HH:mm:ss') : '-'
                      } (${
                        window.status === 'ACTIVE' ? '执行中' :
                        window.status === 'COMPLETED' ? '已完成' :
                        window.status === 'PENDING' ? '待执行' :
                        window.status === 'FAILED' ? '失败' : '未知'
                      })`}
                    >
                      {window.startDateTime ? dayjs(window.startDateTime).format('HH:mm') : window.startTime}
                    </div>
                  ))}
                </div>
              );
            }}
            style={{
              backgroundColor: '#ffffff',
              borderRadius: '6px',
              border: '1px solid #e9ecef'
            }}
          />
        </div>
      </div>
    );
  };

  useEffect(() => {
    loadPmiRecord();
    loadSchedules();
  }, [pmiRecordId]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '计划ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (id) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px', color: '#666' }}>
          {id}
        </span>
      ),
    },
    {
      title: '日期范围',
      key: 'dateRange',
      width: 200,
      render: (_, record) => (
        <span>
          {record.startDate} ~ {record.endDate}
        </span>
      ),
    },
    {
      title: '重复类型',
      dataIndex: 'repeatType',
      key: 'repeatType',
      render: (type, record) => {
        const typeMap = {
          DAILY: { color: 'blue', text: '每天' },
          WEEKLY: { color: 'green', text: '每周' },
          MONTHLY: { color: 'orange', text: '每月' }
        };
        const config = typeMap[type] || { color: 'default', text: type };

        let detailText = config.text;

        // 显示详细的重复信息
        if (type === 'WEEKLY' && record.weekDays && record.weekDays.length > 0) {
          const weekDayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
          const selectedDays = record.weekDays.map(day => weekDayNames[day]).join('、');
          detailText = `每周(${selectedDays})`;
        } else if (type === 'MONTHLY' && record.monthDays && record.monthDays.length > 0) {
          const selectedDays = record.monthDays.map(day => `${day}号`).join('、');
          detailText = `每月(${selectedDays})`;
        }

        return <Tag color={config.color}>{detailText}</Tag>;
      },
    },
    {
      title: '时间设置',
      key: 'timeSettings',
      width: 150,
      render: (_, record) => (
        <div>
          {record.isAllDay ? (
            <Tag color="purple">全天</Tag>
          ) : (
            <span>
              <ClockCircleOutlined /> {record.startTime} 
              ({record.durationMinutes}分钟)
            </span>
          )}
        </div>
      ),
    },
    {
      title: '窗口统计',
      key: 'windowStats',
      width: 180,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <span>总计: {record.totalWindows || 0}</span>
          <Space size="small">
            <Tag color="blue">待执行: {record.pendingWindows || 0}</Tag>
            <Tag color="green">执行中: {record.activeWindows || 0}</Tag>
          </Space>
          <Space size="small">
            <Tag color="gray">已完成: {record.completedWindows || 0}</Tag>
            <Tag color="red">失败: {record.failedWindows || 0}</Tag>
          </Space>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      sorter: true,
      sortOrder: filters.sortField === 'status' ? (filters.sortOrder === 'asc' ? 'ascend' : 'descend') : null,
      render: (status) => {
        const statusMap = {
          ACTIVE: { color: 'green', text: '活跃' },
          INACTIVE: { color: 'gray', text: '非活跃' },
          COMPLETED: { color: 'gray', text: '已完成' }
        };
        const config = statusMap[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">

          <Tooltip title="编辑计划">
            <Button
              type="link"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          </Tooltip>
          <Select
            size="small"
            value={record.status}
            style={{ width: 90 }}
            onChange={(value) => handleUpdateStatus(record.id, value)}
            disabled={record.status === 'COMPLETED'}
          >
            <Option value="ACTIVE">活跃</Option>
            <Option value="INACTIVE">非活跃</Option>
            <Option value="COMPLETED">已完成</Option>
          </Select>
          <Popconfirm
            title="确定要删除这个计划吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];



  return (
    <div className={isMobileView ? 'mobile-container' : ''} style={{ padding: isMobileView ? '12px' : '24px' }}>
      {/* 页面头部 */}
      <Card size={isMobileView ? 'small' : 'default'} style={{ marginBottom: isMobileView ? 12 : 24 }}>
        {isMobileView ? (
          // 移动端布局
          <div>
            <Row justify="space-between" align="middle" style={{ marginBottom: 12 }}>
              <Col>
                <Button
                  size="small"
                  icon={<ArrowLeftOutlined />}
                  onClick={() => navigate('/pmi-management')}
                >
                  返回
                </Button>
              </Col>
              <Col>
                <Button
                  type="primary"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={handleCreatePlanClick}
                >
                  安排计划
                </Button>
              </Col>
            </Row>
            <div>
              <Title level={4} style={{ margin: '0 0 8px 0' }}>
                PMI计划管理
              </Title>
              {pmiRecord && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  {pmiRecord.userFullName && (
                    <div style={{ marginBottom: '4px' }}>
                      用户: {pmiRecord.userFullName}
                    </div>
                  )}
                  PMI: <Link
                    to={`/pmi-management/${pmiRecord.id}`}
                    style={{ color: '#1890ff', textDecoration: 'underline' }}
                  >
                    {pmiRecord.pmiNumber}
                  </Link> | 密码: {pmiRecord.pmiPassword}
                </div>
              )}
            </div>
          </div>
        ) : (
          // PC端布局
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={() => navigate('/pmi-management')}
                >
                  返回PMI管理
                </Button>
                <Divider type="vertical" />
                <Title level={3} style={{ margin: 0 }}>
                  PMI计划管理
                </Title>
                {pmiRecord && (
                  <div style={{ display: 'inline-block', padding: '4px 8px', backgroundColor: '#f0f0f0', borderRadius: '4px', fontSize: '12px' }}>
                    {pmiRecord.userFullName && (
                      <span style={{ marginRight: '8px' }}>
                        用户: {pmiRecord.userFullName} |
                      </span>
                    )}
                    PMI: <Link
                      to={`/pmi-management/${pmiRecord.id}`}
                      style={{ color: '#1890ff', textDecoration: 'underline' }}
                    >
                      {pmiRecord.pmiNumber}
                    </Link> | 密码: {pmiRecord.pmiPassword}
                  </div>
                )}
              </Space>
            </Col>
            <Col>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreatePlanClick}
              >
                安排计划
              </Button>
            </Col>
          </Row>
        )}
      </Card>

      {/* 计划列表 */}
      <Card size={isMobileView ? 'small' : 'default'}>
        {/* 筛选和批量操作工具栏 */}
        <Row gutter={[8, 8]} style={{ marginBottom: isMobileView ? 8 : 16 }}>
          <Col span={24}>
            <Space wrap size={isMobileView ? 'small' : 'middle'}>
              {/* 状态筛选 */}
              <Select
                placeholder="筛选状态"
                style={{ width: isMobileView ? 100 : 120 }}
                size={isMobileView ? 'small' : 'middle'}
                allowClear
                value={filters.status}
                onChange={(value) => handleFilterChange({ status: value })}
              >
                <Option value="ACTIVE">活跃</Option>
                <Option value="INACTIVE">非活跃</Option>
                <Option value="COMPLETED">已完成</Option>
              </Select>

              {/* 日期范围筛选 */}
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                size={isMobileView ? 'small' : 'middle'}
                value={filters.dateRange}
                onChange={(dates) => handleFilterChange({ dateRange: dates })}
              />

              {/* 排序选择 */}
              <Select
                placeholder="排序方式"
                style={{ width: isMobileView ? 120 : 150 }}
                size={isMobileView ? 'small' : 'middle'}
                value={`${filters.sortField}-${filters.sortOrder}`}
                onChange={(value) => {
                  const [field, order] = value.split('-');
                  handleFilterChange({ sortField: field, sortOrder: order });
                }}
              >
                <Option value="createdAt-desc">创建时间↓</Option>
                <Option value="createdAt-asc">创建时间↑</Option>
                <Option value="name-asc">名称A-Z</Option>
                <Option value="name-desc">名称Z-A</Option>
                <Option value="status-asc">状态↑</Option>
                <Option value="status-desc">状态↓</Option>
              </Select>

              {/* 重置筛选 */}
              <Button
                icon={<ReloadOutlined />}
                size={isMobileView ? 'small' : 'middle'}
                onClick={handleResetFilters}
              >
                重置
              </Button>

              {/* 批量操作 */}
              {selectedRowKeys.length > 0 && (
                <>
                  <Divider type="vertical" />
                  <Text>已选择 {selectedRowKeys.length} 项</Text>
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: 'active',
                          label: '批量激活',
                          onClick: () => handleBatchUpdateStatus('ACTIVE')
                        },
                        {
                          key: 'inactive',
                          label: '批量停用',
                          onClick: () => handleBatchUpdateStatus('INACTIVE')
                        },
                        {
                          type: 'divider'
                        },
                        {
                          key: 'delete',
                          label: '批量删除',
                          danger: true,
                          onClick: handleBatchDelete
                        }
                      ]
                    }}
                    trigger={['click']}
                  >
                    <Button loading={batchLoading}>
                      批量操作 <MoreOutlined />
                    </Button>
                  </Dropdown>
                </>
              )}
            </Space>
          </Col>
        </Row>

{isMobileView ? (
          // 移动端卡片列表
          <div>
            {loading ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
              </div>
            ) : schedules.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                暂无计划数据
              </div>
            ) : (
              schedules.map(record => (
                <Card
                  key={record.id}
                  size="small"
                  style={{ marginBottom: 8 }}
                  className="mobile-task-card"
                >
                  <div className="task-header">
                    <div className="task-info">
                      <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: 4 }}>
                        计划ID: {record.id}
                      </div>
                      <div className="task-meta">
                        日期: {record.startDate} ~ {record.endDate}
                      </div>
                      <div className="task-meta">
                        重复: {record.repeatType === 'ONCE' ? '仅一次' :
                               record.repeatType === 'DAILY' ? '每天' :
                               record.repeatType === 'WEEKLY' ? '每周' : '每月'}
                      </div>
                      <div className="task-meta">
                        时间: {record.isAllDay ? '全天' : `${record.startTime || ''} (${record.durationMinutes || 0}分钟)`}
                      </div>
                    </div>
                    <div className="task-actions">
                      <Tag color={record.status === 'ACTIVE' ? 'green' :
                                 record.status === 'INACTIVE' ? 'orange' : 'blue'}>
                        {record.status === 'ACTIVE' ? '活跃' :
                         record.status === 'INACTIVE' ? '非活跃' : '已完成'}
                      </Tag>
                      <Space direction="vertical" size="small">
                        <Button
                          size="small"
                          icon={<EditOutlined />}
                          onClick={() => handleEdit(record)}
                        >
                          编辑
                        </Button>
                        <Button
                          size="small"
                          icon={<CalendarOutlined />}
                          onClick={() => {
                            if (expandedRowKeys.includes(record.id)) {
                              setExpandedRowKeys(prev => prev.filter(key => key !== record.id));
                            } else {
                              setExpandedRowKeys(prev => [...prev, record.id]);
                              loadScheduleWindows(record.id);
                            }
                          }}
                        >
                          窗口
                        </Button>
                      </Space>
                    </div>
                  </div>

                  {/* 展开的窗口信息 */}
                  {expandedRowKeys.includes(record.id) && (
                    <div style={{ marginTop: 8, paddingTop: 8, borderTop: '1px solid #f0f0f0' }}>
                      {renderExpandedRow(record)}
                    </div>
                  )}
                </Card>
              ))
            )}

            {/* 移动端分页 */}
            {schedules.length > 0 && (
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <Button
                  size="small"
                  disabled={pagination.current === 1}
                  onClick={() => loadSchedules(pagination.current - 1, pagination.pageSize)}
                >
                  上一页
                </Button>
                <span style={{ margin: '0 16px', fontSize: '12px' }}>
                  第 {pagination.current} 页，共 {Math.ceil(pagination.total / pagination.pageSize)} 页
                </span>
                <Button
                  size="small"
                  disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
                  onClick={() => loadSchedules(pagination.current + 1, pagination.pageSize)}
                >
                  下一页
                </Button>
              </div>
            )}
          </div>
        ) : (
          // PC端表格
          <Table
            columns={columns}
            dataSource={schedules}
            rowKey="id"
            loading={loading}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
              getCheckboxProps: (record) => ({
                disabled: record.status === 'COMPLETED', // 已完成的计划不能选择
              }),
            }}
            onChange={(paginationInfo, filters, sorter) => {
              // 处理排序
              if (sorter && sorter.field) {
                const order = sorter.order === 'ascend' ? 'asc' : 'desc';
                handleFilterChange({
                  sortField: sorter.field,
                  sortOrder: order
                });
              }
            }}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                loadSchedules(page, pageSize);
              },
            }}
            expandable={{
              expandedRowRender: renderExpandedRow,
              expandedRowKeys: expandedRowKeys,
              onExpand: (expanded, record) => {
                if (expanded) {
                  setExpandedRowKeys(prev => [...prev, record.id]);
                  loadScheduleWindows(record.id);
                } else {
                  setExpandedRowKeys(prev => prev.filter(key => key !== record.id));
                }
              },
              expandIcon: ({ expanded, onExpand, record }) => (
                <Tooltip title={expanded ? '收起窗口' : '展开窗口'}>
                  <Button
                    type="text"
                    size="small"
                    icon={expanded ? <DownOutlined /> : <RightOutlined />}
                    onClick={e => onExpand(record, e)}
                  />
                </Tooltip>
              ),
              rowExpandable: (record) => true, // 所有计划都可以展开查看窗口
            }}
          />
        )}
      </Card>

      {/* 创建计划模态框 */}
      <Modal
        title="安排计划"
        open={createModalVisible}
        onCancel={useCallback(() => {
          setCreateModalVisible(false);
          createForm.resetFields();
          setSelectedQuickOption(null);
        }, [createForm])}
        footer={null}
        width={isMobileView ? '95%' : 600}
        style={isMobileView ? { top: 20 } : {}}
        styles={{ body: isMobileView ? { padding: '16px 12px' } : {} }}
        destroyOnClose={true}
      >
        {pmiRecord && (
          <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
            <Text strong>PMI信息：</Text>
            <br />
            <Text>PMI号码：{pmiRecord.pmiNumber}</Text>
            <br />
            <Text>PMI密码：{pmiRecord.pmiPassword}</Text>
          </div>
        )}

        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateSubmit}
        >
          <Form.Item label="快捷">
            <Space wrap size="large">
              <Button
                size="small"
                type={selectedQuickOption === '1hour' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('1hour')}
              >
                1小时
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === '2hours' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('2hours')}
              >
                2小时
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === '3hours' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('3hours')}
              >
                3小时
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === 'today' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('today')}
              >
                今天
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === 'tomorrow' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('tomorrow')}
              >
                明天
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === 'month' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('month')}
              >
                一个月
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === 'year' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('year')}
              >
                一年
              </Button>
            </Space>
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="计划日期范围"
            rules={[{ required: true, message: '请选择日期范围' }]}
            initialValue={[dayjs(), dayjs()]}
          >
            <RangePicker
              style={{ width: '100%' }}
              onChange={() => {
                // 延迟执行，确保表单值已更新
                setTimeout(() => autoGenerateScheduleName(createForm), 100);
              }}
            />
          </Form.Item>

          <Form.Item
            name="repeatType"
            label="重复策略"
            rules={[{ required: true, message: '请选择重复策略' }]}
            initialValue="DAILY"
          >
            <Radio.Group
              onChange={(e) => {
                const value = e.target.value;
                // 当重复策略改变时，清空相关字段
                if (value !== 'WEEKLY') {
                  createForm.setFieldsValue({ weekDays: undefined });
                }
                if (value !== 'MONTHLY') {
                  createForm.setFieldsValue({ monthDays: undefined });
                }
                // 延迟执行，确保表单值已更新，强制更新计划名称
                setTimeout(() => autoGenerateScheduleName(createForm, true), 100);
              }}
            >
              <Radio value="DAILY">按天</Radio>
              <Radio value="WEEKLY">按周</Radio>
              <Radio value="MONTHLY">按月</Radio>
            </Radio.Group>
          </Form.Item>

          {/* 按周重复时的星期几选择 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.repeatType !== currentValues.repeatType
            }
          >
            {({ getFieldValue }) => {
              const repeatType = getFieldValue('repeatType');
              return repeatType === 'WEEKLY' ? (
                <Form.Item
                  name="weekDays"
                  label="选择星期"
                  rules={[{ required: true, message: '请选择至少一个星期' }]}
                >
                  <Checkbox.Group
                    onChange={() => {
                      // 延迟执行，确保表单值已更新，强制更新计划名称
                      setTimeout(() => autoGenerateScheduleName(createForm, true), 100);
                    }}
                  >
                    <Row>
                      <Col span={8}><Checkbox value={1}>周一</Checkbox></Col>
                      <Col span={8}><Checkbox value={2}>周二</Checkbox></Col>
                      <Col span={8}><Checkbox value={3}>周三</Checkbox></Col>
                      <Col span={8}><Checkbox value={4}>周四</Checkbox></Col>
                      <Col span={8}><Checkbox value={5}>周五</Checkbox></Col>
                      <Col span={8}><Checkbox value={6}>周六</Checkbox></Col>
                      <Col span={8}><Checkbox value={7}>周日</Checkbox></Col>
                    </Row>
                  </Checkbox.Group>
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          {/* 按月重复时的日期选择 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.repeatType !== currentValues.repeatType
            }
          >
            {({ getFieldValue }) => {
              const repeatType = getFieldValue('repeatType');
              return repeatType === 'MONTHLY' ? (
                <Form.Item
                  name="monthDays"
                  label="选择日期"
                  rules={[{ required: true, message: '请选择至少一个日期' }]}
                >
                  <Checkbox.Group
                    onChange={() => {
                      // 延迟执行，确保表单值已更新，强制更新计划名称
                      setTimeout(() => autoGenerateScheduleName(createForm, true), 100);
                    }}
                  >
                    <Row>
                      {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                        <Col span={4} key={day} style={{ marginBottom: 8 }}>
                          <Checkbox value={day}>{day}号</Checkbox>
                        </Col>
                      ))}
                    </Row>
                  </Checkbox.Group>
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          <Form.Item
            name="isAllDay"
            label="时段设置"
            initialValue={true}
          >
            <Radio.Group>
              <Radio value={true}>全天</Radio>
              <Radio value={false}>指定时段</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.isAllDay !== currentValues.isAllDay
            }
          >
            {({ getFieldValue }) =>
              !getFieldValue('isAllDay') && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="开始时间">
                      <Row gutter={8}>
                        <Col span={12}>
                          <Form.Item
                            name="startHour"
                            rules={[{ required: true, message: '请选择小时' }]}
                            initialValue={8}
                          >
                            <Select placeholder="选择小时">
                              {Array.from({ length: 24 }, (_, i) => (
                                <Select.Option key={i} value={i}>
                                  {String(i).padStart(2, '0')}:00
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name="startMinute"
                            rules={[{ required: true, message: '请选择分钟' }]}
                            initialValue={0}
                          >
                            <Select placeholder="选择分钟">
                              {Array.from({ length: 60 }, (_, i) => (
                                <Select.Option key={i} value={i}>
                                  {String(i).padStart(2, '0')}
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="持续时长">
                      <Row gutter={8}>
                        <Col span={12}>
                          <Form.Item
                            name="durationHours"
                            rules={[{ required: true, message: '请选择小时' }]}
                            initialValue={1}
                          >
                            <Select placeholder="选择小时">
                              {Array.from({ length: 25 }, (_, i) => (
                                <Select.Option key={i} value={i}>
                                  {i} 小时
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name="durationMinutes"
                            rules={[{ required: true, message: '请选择分钟' }]}
                            initialValue={0}
                          >
                            <Select placeholder="选择分钟">
                              <Select.Option value={0}>0 分钟</Select.Option>
                              <Select.Option value={15}>15 分钟</Select.Option>
                              <Select.Option value={30}>30 分钟</Select.Option>
                              <Select.Option value={45}>45 分钟</Select.Option>
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form.Item>
                  </Col>
                </Row>
              )
            }
          </Form.Item>

          <Form.Item
            name="name"
            label="计划描述"
          >
            <Input.TextArea
              placeholder="请输入计划描述（可选）"
              rows={3}
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setCreateModalVisible(false);
                setSelectedQuickOption(null);
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={createLoading}>
                创建计划
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑计划模态框 */}
      <Modal
        title="编辑计划"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
          setEditingSchedule(null);
          setSelectedQuickOption(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditSubmit}
        >
          <Form.Item label="快捷">
            <Space wrap size="large">
              <Button
                size="small"
                type={selectedQuickOption === '1hour' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('1hour', editForm)}
              >
                1小时
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === '2hours' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('2hours', editForm)}
              >
                2小时
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === '3hours' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('3hours', editForm)}
              >
                3小时
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === 'today' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('today', editForm)}
              >
                今天
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === 'tomorrow' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('tomorrow', editForm)}
              >
                明天
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === 'month' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('month', editForm)}
              >
                一个月
              </Button>
              <Button
                size="small"
                type={selectedQuickOption === 'year' ? 'primary' : 'default'}
                onClick={() => handleQuickSet('year', editForm)}
              >
                一年
              </Button>
            </Space>
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="计划日期范围"
            rules={[{ required: true, message: '请选择日期范围' }]}
          >
            <RangePicker
              style={{ width: '100%' }}
              onChange={() => {
                // 延迟执行，确保表单值已更新
                setTimeout(() => autoGenerateScheduleName(editForm), 100);
              }}
            />
          </Form.Item>

          <Form.Item
            name="repeatType"
            label="重复策略"
            rules={[{ required: true, message: '请选择重复策略' }]}
          >
            <Radio.Group
              onChange={(e) => {
                const value = e.target.value;
                // 当重复策略改变时，清空相关字段
                if (value !== 'WEEKLY') {
                  editForm.setFieldsValue({ weekDays: undefined });
                }
                if (value !== 'MONTHLY') {
                  editForm.setFieldsValue({ monthDays: undefined });
                }
                // 延迟执行，确保表单值已更新，强制更新计划名称
                setTimeout(() => autoGenerateScheduleName(editForm, true), 100);
              }}
            >
              <Radio value="DAILY">按天</Radio>
              <Radio value="WEEKLY">按周</Radio>
              <Radio value="MONTHLY">按月</Radio>
            </Radio.Group>
          </Form.Item>

          {/* 按周重复时的星期几选择 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.repeatType !== currentValues.repeatType
            }
          >
            {({ getFieldValue }) => {
              const repeatType = getFieldValue('repeatType');
              return repeatType === 'WEEKLY' ? (
                <Form.Item
                  name="weekDays"
                  label="选择星期"
                  rules={[{ required: true, message: '请选择至少一个星期' }]}
                >
                  <Checkbox.Group
                    onChange={() => {
                      // 延迟执行，确保表单值已更新，强制更新计划名称
                      setTimeout(() => autoGenerateScheduleName(editForm, true), 100);
                    }}
                  >
                    <Row>
                      <Col span={8}><Checkbox value={1}>周一</Checkbox></Col>
                      <Col span={8}><Checkbox value={2}>周二</Checkbox></Col>
                      <Col span={8}><Checkbox value={3}>周三</Checkbox></Col>
                      <Col span={8}><Checkbox value={4}>周四</Checkbox></Col>
                      <Col span={8}><Checkbox value={5}>周五</Checkbox></Col>
                      <Col span={8}><Checkbox value={6}>周六</Checkbox></Col>
                      <Col span={8}><Checkbox value={7}>周日</Checkbox></Col>
                    </Row>
                  </Checkbox.Group>
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          {/* 按月重复时的日期选择 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.repeatType !== currentValues.repeatType
            }
          >
            {({ getFieldValue }) => {
              const repeatType = getFieldValue('repeatType');
              return repeatType === 'MONTHLY' ? (
                <Form.Item
                  name="monthDays"
                  label="选择日期"
                  rules={[{ required: true, message: '请选择至少一个日期' }]}
                >
                  <Checkbox.Group
                    onChange={() => {
                      // 延迟执行，确保表单值已更新，强制更新计划名称
                      setTimeout(() => autoGenerateScheduleName(editForm, true), 100);
                    }}
                  >
                    <Row>
                      {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                        <Col span={4} key={day} style={{ marginBottom: 8 }}>
                          <Checkbox value={day}>{day}号</Checkbox>
                        </Col>
                      ))}
                    </Row>
                  </Checkbox.Group>
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          <Form.Item
            name="isAllDay"
            label="时段设置"
            initialValue={true}
          >
            <Radio.Group>
              <Radio value={true}>全天</Radio>
              <Radio value={false}>指定时段</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.isAllDay !== currentValues.isAllDay
            }
          >
            {({ getFieldValue }) =>
              !getFieldValue('isAllDay') && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="开始时间">
                      <Row gutter={8}>
                        <Col span={12}>
                          <Form.Item
                            name="startHour"
                            rules={[{ required: true, message: '请选择小时' }]}
                          >
                            <Select placeholder="选择小时">
                              {Array.from({ length: 24 }, (_, i) => (
                                <Select.Option key={i} value={i}>
                                  {String(i).padStart(2, '0')}:00
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name="startMinute"
                            rules={[{ required: true, message: '请选择分钟' }]}
                          >
                            <Select placeholder="选择分钟">
                              {Array.from({ length: 60 }, (_, i) => (
                                <Select.Option key={i} value={i}>
                                  {String(i).padStart(2, '0')}
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="持续时长">
                      <Row gutter={8}>
                        <Col span={12}>
                          <Form.Item
                            name="durationHours"
                            rules={[{ required: true, message: '请选择小时' }]}
                          >
                            <Select placeholder="选择小时">
                              {Array.from({ length: 25 }, (_, i) => (
                                <Select.Option key={i} value={i}>
                                  {i} 小时
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name="durationMinutes"
                            rules={[{ required: true, message: '请选择分钟' }]}
                          >
                            <Select placeholder="选择分钟">
                              <Select.Option value={0}>0 分钟</Select.Option>
                              <Select.Option value={15}>15 分钟</Select.Option>
                              <Select.Option value={30}>30 分钟</Select.Option>
                              <Select.Option value={45}>45 分钟</Select.Option>
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form.Item>
                  </Col>
                </Row>
              )
            }
          </Form.Item>

          <Form.Item
            name="name"
            label="计划描述"
          >
            <Input.TextArea
              placeholder="请输入计划描述（可选）"
              rows={3}
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setEditModalVisible(false);
                setSelectedQuickOption(null);
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={createLoading}>
                更新计划
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑窗口模态框 */}
      <Modal
        title="编辑计划窗口"
        open={editWindowModalVisible}
        onCancel={() => {
          setEditWindowModalVisible(false);
          editWindowForm.resetFields();
          setEditingWindow(null);
        }}
        footer={null}
        width={500}
      >
        <Form
          form={editWindowForm}
          layout="vertical"
          onFinish={handleUpdateWindow}
        >
          <Form.Item
            name="windowDate"
            label="窗口日期"
            rules={[{ required: true, message: '请选择窗口日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="startTime"
            label="开始时间"
            rules={[{ required: true, message: '请输入开始时间' }]}
          >
            <TimePicker style={{ width: '100%' }} format="HH:mm:ss" />
          </Form.Item>

          <Form.Item
            name="endTime"
            label="结束时间"
            rules={[{ required: true, message: '请输入结束时间' }]}
          >
            <TimePicker style={{ width: '100%' }} format="HH:mm:ss" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setEditWindowModalVisible(false);
                editWindowForm.resetFields();
                setEditingWindow(null);
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                更新窗口
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 冲突确认对话框 */}
      <Modal
        title={
          <div style={{ color: '#ff4d4f' }}>
            <ExclamationCircleOutlined style={{ marginRight: 8 }} />
            窗口冲突警告
          </div>
        }
        open={conflictModalVisible}
        onCancel={handleCancelMerge}
        width={800}
        footer={[
          <Button key="cancel" onClick={handleCancelMerge}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            danger
            loading={createLoading}
            onClick={handleConfirmMerge}
          >
            确认合并窗口
          </Button>
        ]}
      >
        {conflictData && (
          <div>
            <Alert
              message={conflictData.message}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <div style={{ marginBottom: 16 }}>
              <Title level={5} style={{ color: '#1890ff' }}>
                本次新增窗口 ({conflictData.newWindows?.length || 0} 个)
              </Title>
              <Table
                dataSource={conflictData.newWindows || []}
                pagination={false}
                size="small"
                rowKey={(record, index) => `new-${index}`}
                style={{
                  backgroundColor: '#e6f7ff',
                  border: '1px solid #91d5ff',
                  borderRadius: 4
                }}
                rowClassName={(record, index) => {
                  // 如果当前新窗口存在冲突，则高亮显示
                  if (conflictData.conflictNewWindowIndexes && conflictData.conflictNewWindowIndexes.includes(index)) {
                    return 'conflict-new-window-row';
                  }
                  return '';
                }}
                columns={[
                  {
                    title: '窗口ID',
                    dataIndex: 'id',
                    key: 'id',
                    width: 80,
                    render: (id) => (
                      <span style={{ fontFamily: 'monospace', fontSize: '12px', color: '#666' }}>
                        {id || '新增'}
                      </span>
                    ),
                  },
                  {
                    title: '开始时间',
                    dataIndex: 'startDateTime',
                    key: 'startDateTime',
                    render: (startDateTime) => {
                      if (startDateTime) {
                        return dayjs(startDateTime).format('YYYY-MM-DD HH:mm:ss');
                      }
                      return '-';
                    }
                  },
                  {
                    title: '结束时间',
                    dataIndex: 'endDateTime',
                    key: 'endDateTime',
                    render: (endDateTime) => {
                      if (endDateTime) {
                        return dayjs(endDateTime).format('YYYY-MM-DD HH:mm:ss');
                      }
                      return '-';
                    }
                  },
                  {
                    title: '状态',
                    dataIndex: 'status',
                    key: 'status',
                    render: () => <Tag color="blue">新增</Tag>
                  }
                ]}
              />
            </div>

            <div>
              <Title level={5} style={{ color: '#ff4d4f' }}>
                存量冲突窗口 ({conflictData.conflictWindows?.length || 0} 个)
              </Title>
              <Table
                dataSource={conflictData.conflictWindows || []}
                pagination={false}
                size="small"
                rowKey="id"
                style={{
                  backgroundColor: '#fff2f0',
                  border: '1px solid #ffccc7',
                  borderRadius: 4
                }}
                columns={[
                  {
                    title: '窗口ID',
                    dataIndex: 'id',
                    key: 'id',
                    width: 80,
                    render: (id) => (
                      <span style={{ fontFamily: 'monospace', fontSize: '12px', color: '#666' }}>
                        {id}
                      </span>
                    ),
                  },
                  {
                    title: '开始时间',
                    dataIndex: 'startDateTime',
                    key: 'startDateTime',
                    render: (startDateTime) => {
                      if (startDateTime) {
                        return dayjs(startDateTime).format('YYYY-MM-DD HH:mm:ss');
                      }
                      return '-';
                    }
                  },
                  {
                    title: '结束时间',
                    dataIndex: 'endDateTime',
                    key: 'endDateTime',
                    render: (endDateTime) => {
                      if (endDateTime) {
                        return dayjs(endDateTime).format('YYYY-MM-DD HH:mm:ss');
                      }
                      return '-';
                    }
                  },
                  {
                    title: '状态',
                    dataIndex: 'status',
                    key: 'status',
                    render: (status) => {
                      const statusConfig = {
                        'PENDING': { color: 'orange', text: '待执行' },
                        'ACTIVE': { color: 'green', text: '执行中' },
                        'COMPLETED': { color: 'blue', text: '已完成' },
                        'FAILED': { color: 'red', text: '失败' }
                      };
                      const config = statusConfig[status] || { color: 'default', text: status };
                      return <Tag color={config.color}>{config.text}</Tag>;
                    }
                  }
                ]}
              />
            </div>

            <div style={{ marginTop: 16, padding: 12, backgroundColor: '#fffbe6', border: '1px solid #ffe58f', borderRadius: 4 }}>
              <Text strong style={{ color: '#d48806' }}>
                合并说明：
              </Text>
              <br />
              <Text style={{ color: '#d48806' }}>
                确认合并后，系统将以新老冲突窗口的并集时间范围为结果，修改存量冲突窗口的开始和结束时间。
              </Text>
            </div>
          </div>
        )}
      </Modal>

      {/* 任务详情弹窗 */}
      <Modal
        title="任务详情"
        open={taskDetailVisible}
        onCancel={handleCloseTaskDetail}
        width={800}
        footer={[
          <Button key="close" onClick={handleCloseTaskDetail}>
            关闭
          </Button>
        ]}
      >
        {taskDetailLoading ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Spin size="large" />
          </div>
        ) : taskDetail ? (
          // 调试输出：显示taskDetail的完整内容
          console.log('🔍 弹窗中的taskDetail:', taskDetail) ||
          taskDetail.error ? (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Alert
                message={taskDetail.error === 'PERMISSION_DENIED' ? '权限不足' : '加载失败'}
                description={taskDetail.message}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
              <div style={{ color: '#999' }}>
                任务ID: {taskDetail.id}
              </div>
            </div>
          ) : (
            <div>
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Statistic
                    title="任务状态"
                    value={taskDetail.statusDescription || taskDetail.status}
                    prefix={getTaskStatusIcon(taskDetail.status)}
                    valueStyle={{
                      color: getTaskStatusColor(taskDetail.status) === 'green' ? '#3f8600' :
                             getTaskStatusColor(taskDetail.status) === 'red' ? '#cf1322' :
                             getTaskStatusColor(taskDetail.status) === 'orange' ? '#d46b08' : '#1890ff'
                    }}
                  />
                </Col>
              <Col span={8}>
                <Statistic
                  title="任务类型"
                  value={taskDetail.taskTypeDescription || getTaskTypeText(taskDetail.taskType)}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="重试次数"
                  value={taskDetail.retryCount || 0}
                />
              </Col>
            </Row>

            <Descriptions
              title="任务详情"
              bordered
              column={1}
              style={{ marginTop: 24 }}
            >
              <Descriptions.Item label="任务ID">
                {taskDetail.id}
              </Descriptions.Item>
              <Descriptions.Item label="任务键">
                {taskDetail.taskKey}
              </Descriptions.Item>
              <Descriptions.Item label="PMI窗口ID">
                {taskDetail.pmiWindowId}
              </Descriptions.Item>
              {taskDetail.pmiNumber && (
                <Descriptions.Item label="PMI号码">
                  {taskDetail.pmiNumber}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="计划执行时间">
                {taskDetail.scheduledTime}
              </Descriptions.Item>
              {taskDetail.actualExecutionTime && (
                <Descriptions.Item label="实际执行时间">
                  {taskDetail.actualExecutionTime}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="创建时间">
                {taskDetail.createdAt}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {taskDetail.updatedAt}
              </Descriptions.Item>
              {taskDetail.errorMessage && (
                <Descriptions.Item label="错误信息">
                  <Text type="danger">{taskDetail.errorMessage}</Text>
                </Descriptions.Item>
              )}
            </Descriptions>

            {taskDetail.status === 'FAILED' && (
              <Alert
                message="任务执行失败"
                description={taskDetail.errorMessage || '任务执行过程中发生错误，请检查系统日志获取详细信息。'}
                type="error"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}

            {taskDetail.status === 'SCHEDULED' && (
              <Alert
                message="任务等待执行"
                description="任务已调度，等待系统在指定时间自动执行。"
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
            </div>
          )
        ) : (
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
            暂无任务详情
          </div>
        )}
      </Modal>

      {/* 移动端样式 */}
      <style jsx>{`
        .mobile-container {
          padding: 12px !important;
        }

        .mobile-task-card {
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
        }

        .task-info {
          flex: 1;
          margin-right: 12px;
        }

        .task-meta {
          font-size: 12px;
          color: #666;
          margin-bottom: 2px;
        }

        .task-actions {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 8px;
        }

        @media (max-width: 768px) {
          .ant-table-wrapper {
            overflow-x: auto;
          }

          .ant-modal {
            margin: 0 !important;
            max-width: none !important;
          }

          .ant-modal-content {
            border-radius: 8px 8px 0 0;
          }
        }
      `}</style>
    </div>
  );
};

export default PmiScheduleManagement;
