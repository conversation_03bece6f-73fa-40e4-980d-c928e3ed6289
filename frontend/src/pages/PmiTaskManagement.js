import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  message,
  Tag,
  Descriptions,
  Modal,
  Spin,
  Alert,
  Tooltip,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  ReloadOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { getTaskTypeText, getTaskStatusText as getApiTaskStatusText, pmiTaskApi } from '../services/pmiTaskApi';

const { Title, Text } = Typography;

// 任务状态颜色映射
const getTaskStatusColor = (status) => {
  const statusColors = {
    'SCHEDULED': 'blue',
    'EXECUTING': 'orange',
    'COMPLETED': 'green',
    'FAILED': 'red',
    'CANCELLED': 'default',
    'RETRY': 'yellow'
  };
  return statusColors[status] || 'default';
};

// 任务状态文本映射
const getTaskStatusText = (status) => {
  const statusTexts = {
    'SCHEDULED': '已调度',
    'EXECUTING': '执行中',
    'COMPLETED': '已完成',
    'FAILED': '失败',
    'CANCELLED': '已取消',
    'RETRY': '重试中'
  };
  return statusTexts[status] || status;
};

// 任务状态图标映射
const getTaskStatusIcon = (status) => {
  const statusIcons = {
    'SCHEDULED': <ClockCircleOutlined />,
    'EXECUTING': <PlayCircleOutlined />,
    'COMPLETED': <CheckCircleOutlined />,
    'FAILED': <ExclamationCircleOutlined />,
    'CANCELLED': <StopOutlined />,
    'RETRY': <ReloadOutlined />
  };
  return statusIcons[status] || <InfoCircleOutlined />;
};

const PmiTaskManagement = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [taskDetail, setTaskDetail] = useState(null);
  const [taskDetailVisible, setTaskDetailVisible] = useState(false);

  // 从URL参数获取taskId
  const searchParams = new URLSearchParams(location.search);
  const taskId = searchParams.get('taskId');

  useEffect(() => {
    if (taskId) {
      loadTaskDetail(taskId);
      setTaskDetailVisible(true);
    }
  }, [taskId]);

  // 加载任务详情
  const loadTaskDetail = async (id) => {
    try {
      console.log('开始获取任务详情, taskId:', id);
      setLoading(true);
      // 调用PMI任务API
      const response = await pmiTaskApi.getTaskDetail(id);
      console.log('API响应:', response);

      if (response && response.data && response.data.success && response.data.data) {
        // 标准的后端响应格式：{success: true, data: {...}}
        const taskData = response.data.data;
        console.log('✅ 任务详情数据:', taskData);
        setTaskDetail(taskData);
      } else {
        console.error('❌ API响应格式错误:', response);
        throw new Error('API响应格式错误');
      }
    } catch (error) {
      console.error('加载任务详情失败:', error);
      console.error('错误详情:', {
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data
      });

      let errorMessage = '加载任务详情失败';
      let errorType = 'LOAD_FAILED';

      // 检查是否是权限问题
      if (error.response && error.response.status === 403) {
        errorMessage = '权限不足：需要管理员权限才能查看任务详情';
        errorType = 'PERMISSION_DENIED';
        message.error(errorMessage);
      } else if (error.response && error.response.status === 404) {
        errorMessage = '任务不存在或已被删除';
        errorType = 'NOT_FOUND';
        message.error(errorMessage);
      } else if (error.response && error.response.status >= 500) {
        errorMessage = '服务器内部错误，请稍后重试';
        errorType = 'SERVER_ERROR';
        message.error(errorMessage);
      } else {
        errorMessage = `加载失败: ${error.message}`;
        message.error(errorMessage);
      }

      setTaskDetail({
        id: id,
        error: errorType,
        message: errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  // 关闭任务详情
  const handleCloseTaskDetail = () => {
    setTaskDetailVisible(false);
    setTaskDetail(null);
    // 移除URL参数
    navigate('/pmi-task-management', { replace: true });
  };

  // 执行任务
  const handleExecuteTask = async (taskId) => {
    try {
      // 模拟API调用
      message.success('任务执行成功');
      // 重新加载任务详情
      await loadTaskDetail(taskId);
    } catch (error) {
      message.error('任务执行失败');
    }
  };

  // 取消任务
  const handleCancelTask = async (taskId) => {
    try {
      // 模拟API调用
      message.success('任务取消成功');
      // 重新加载任务详情
      await loadTaskDetail(taskId);
    } catch (error) {
      message.error('任务取消失败');
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>PMI任务管理</Title>
        
        {!taskId && (
          <Alert
            message="任务管理"
            description="请从PMI计划管理页面的窗口列表中点击任务详情链接来查看具体任务信息。"
            type="info"
            showIcon
          />
        )}

        {/* 任务详情模态框 */}
        <Modal
          title="任务详情"
          open={taskDetailVisible}
          onCancel={handleCloseTaskDetail}
          width={800}
          footer={[
            <Button key="close" onClick={handleCloseTaskDetail}>
              关闭
            </Button>,
            taskDetail && taskDetail.status === 'SCHEDULED' && (
              <Button
                key="execute"
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={() => handleExecuteTask(taskDetail.id)}
              >
                立即执行
              </Button>
            ),
            taskDetail && ['SCHEDULED', 'RETRY'].includes(taskDetail.status) && (
              <Button
                key="cancel"
                danger
                icon={<StopOutlined />}
                onClick={() => handleCancelTask(taskDetail.id)}
              >
                取消任务
              </Button>
            )
          ].filter(Boolean)}
        >
          {loading ? (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Spin size="large" />
            </div>
          ) : taskDetail ? (
            taskDetail.error ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Alert
                  message={taskDetail.error === 'PERMISSION_DENIED' ? '权限不足' : '加载失败'}
                  description={taskDetail.message}
                  type="error"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
                <div style={{ color: '#999' }}>
                  任务ID: {taskDetail.id}
                </div>
              </div>
            ) : (
              <div>
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Statistic
                      title="任务状态"
                      value={taskDetail.statusDescription || getApiTaskStatusText(taskDetail.status)}
                      prefix={getTaskStatusIcon(taskDetail.status)}
                      valueStyle={{
                        color: getTaskStatusColor(taskDetail.status) === 'green' ? '#3f8600' :
                               getTaskStatusColor(taskDetail.status) === 'red' ? '#cf1322' :
                               getTaskStatusColor(taskDetail.status) === 'orange' ? '#d46b08' : '#1890ff'
                      }}
                    />
                  </Col>
                <Col span={8}>
                  <Statistic
                    title="任务类型"
                    value={taskDetail.taskTypeDescription || getTaskTypeText(taskDetail.taskType)}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="重试次数"
                    value={taskDetail.retryCount}
                  />
                </Col>
              </Row>

              <Descriptions
                title="任务信息"
                bordered
                column={1}
                style={{ marginTop: 24 }}
              >
                <Descriptions.Item label="任务ID">
                  <Text code>{taskDetail.id}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="任务Key">
                  <Text code>{taskDetail.taskKey}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="关联窗口ID">
                  <Text code>{taskDetail.pmiWindowId}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="计划执行时间">
                  {dayjs(taskDetail.scheduledTime).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                {taskDetail.actualExecutionTime && (
                  <Descriptions.Item label="实际执行时间">
                    {dayjs(taskDetail.actualExecutionTime).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="创建时间">
                  {dayjs(taskDetail.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {dayjs(taskDetail.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                {taskDetail.errorMessage && (
                  <Descriptions.Item label="错误信息">
                    <Text type="danger">{taskDetail.errorMessage}</Text>
                  </Descriptions.Item>
                )}
              </Descriptions>

              {taskDetail.status === 'FAILED' && (
                <Alert
                  message="任务执行失败"
                  description={taskDetail.errorMessage || '任务执行过程中发生错误，请检查系统日志获取详细信息。'}
                  type="error"
                  showIcon
                  style={{ marginTop: 16 }}
                />
              )}

              {taskDetail.status === 'SCHEDULED' && (
                <Alert
                  message="任务等待执行"
                  description="任务已调度，等待系统在指定时间自动执行，或者您可以点击【立即执行】按钮手动触发。"
                  type="info"
                  showIcon
                  style={{ marginTop: 16 }}
                />
              )}
              </div>
            )
          ) : null}
        </Modal>
      </Card>
    </div>
  );
};

export default PmiTaskManagement;
