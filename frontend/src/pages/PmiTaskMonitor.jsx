import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Tabs,
  Statistic,
  Row,
  Col,
  Modal,
  Form,
  DatePicker,
  Input,
  message,
  Dropdown,
  Menu,
  Tooltip,
  Drawer,
  List,
  Avatar,
  Badge,
  Typography,
  Divider,
  FloatButton,
  Affix
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ClockCircleOutlined,
  EditOutlined,
  StopOutlined,
  DownOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  MobileOutlined,
  DesktopOutlined,
  FilterOutlined,
  ExportOutlined,
  EyeOutlined,
  MoreOutlined,
  BellOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import moment from 'moment';
import { Link } from 'react-router-dom';
import {
  pmiTaskApi,
  getTaskStatusText,
  getTaskTypeText,
  getTaskStatusColor,
  canExecuteTask,
  canCancelTask,
  canRescheduleTask,
  TASK_STATUS,
  TASK_TYPE
} from '../services/pmiTaskApi';
import webSocketService from '../services/websocketService';
import { useMobile, usePullToRefresh } from '../hooks/useMobile';
import { formatMobileTime, hapticFeedback } from '../utils/mobile';
import MobileTaskCard from '../components/mobile/MobileTaskCard';
import MobileStatistics from '../components/mobile/MobileStatistics';
import MobileFilterDrawer from '../components/mobile/MobileFilterDrawer';
import { logApiTestResults } from '../utils/apiTest';

const { TabPane } = Tabs;
const { confirm } = Modal;
const { Text, Title } = Typography;

/**
 * PMI任务监控页面
 */
const PmiTaskMonitor = () => {
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [upcomingTasks, setUpcomingTasks] = useState([]);
  const [taskHistory, setTaskHistory] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [rescheduleModalVisible, setRescheduleModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [form] = Form.useForm();

  // 移动端适配状态
  const { isMobile, deviceType, orientation } = useMobile();
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const [taskDetailDrawerVisible, setTaskDetailDrawerVisible] = useState(false);
  const [selectedTaskDetail, setSelectedTaskDetail] = useState(null);
  const [activeTab, setActiveTab] = useState('all');

  // 模拟用户权限（实际应该从用户上下文获取）
  const currentUser = {
    roles: ['ADMIN'] // 或 ['SUPER_ADMIN']
  };

  // 下拉刷新功能
  const refreshData = async () => {
    await Promise.all([
      loadTasks(),
      loadStatistics(),
      loadUpcomingTasks()
    ]);
    hapticFeedback('success');
  };

  const { isPulling, pullDistance, isRefreshing } = usePullToRefresh(refreshData);

  useEffect(() => {
    // 开发模式下测试API路径
    if (process.env.NODE_ENV === 'development') {
      logApiTestResults();
    }

    loadTasks();
    loadStatistics();
    loadUpcomingTasks();

    // 连接WebSocket
    webSocketService.connect();

    // 注册WebSocket回调
    const handleTaskStatusChange = (data) => {
      // 实时更新任务列表
      loadTasks();
      loadStatistics();
      if (data.type === 'TASK_CREATED' || data.type === 'TASK_STATUS_CHANGE') {
        loadUpcomingTasks();
      }
    };

    const handleStatisticsUpdate = (data) => {
      if (data.statistics) {
        setStatistics(data.statistics);
      }
    };

    webSocketService.onTaskStatusChange(handleTaskStatusChange);
    webSocketService.onTaskStatistics(handleStatisticsUpdate);

    // 清理函数
    return () => {
      webSocketService.removeCallback('onTaskStatusChange', handleTaskStatusChange);
      webSocketService.removeCallback('onTaskStatistics', handleStatisticsUpdate);
    };
  }, []);

  /**
   * 加载任务列表
   */
  const loadTasks = async (params = {}, paginationOverride = null) => {
    setLoading(true);
    try {
      // 使用传入的分页参数或当前分页状态
      const currentPagination = paginationOverride || pagination;

      const response = await pmiTaskApi.getPmiTasks({
        page: currentPagination.current,
        size: currentPagination.pageSize,
        ...params
      });

      console.log('📋 任务列表响应:', response);

      // 处理后端返回的数据结构 {success: true, data: {content: [...], totalElements: ...}}
      const responseData = response?.data?.data || response?.data || {};
      const taskData = responseData?.content || [];
      const totalElements = responseData?.totalElements || 0;

      console.log('📋 处理后的任务数据:', { taskData, totalElements });

      setTasks(Array.isArray(taskData) ? taskData : []);
      setPagination({
        ...currentPagination,
        total: totalElements
      });
    } catch (error) {
      console.error('加载任务列表失败:', error);
      message.error('加载任务列表失败');
      setTasks([]); // 确保错误时也设置为空数组
    } finally {
      setLoading(false);
    }
  };

  /**
   * 加载统计数据
   */
  const loadStatistics = async () => {
    try {
      const response = await pmiTaskApi.getTaskStatistics();
      console.log('📊 统计数据响应:', response);

      // 处理后端返回的数据结构 {success: true, data: {...}}
      const statisticsData = response?.data?.data || response?.data || {};
      console.log('📊 处理后的统计数据:', statisticsData);

      setStatistics(statisticsData);
    } catch (error) {
      console.error('加载统计数据失败:', error);
      message.error('加载统计数据失败');
      setStatistics({});
    }
  };

  /**
   * 加载即将执行的任务
   */
  const loadUpcomingTasks = async () => {
    try {
      const response = await pmiTaskApi.getUpcomingTasks(24);
      console.log('⏰ 即将执行任务响应:', response);

      // 处理后端返回的数据结构 {success: true, data: [...]}
      const upcomingData = response?.data?.data || response?.data || [];
      console.log('⏰ 处理后的即将执行任务:', upcomingData);

      setUpcomingTasks(Array.isArray(upcomingData) ? upcomingData : []);
    } catch (error) {
      console.error('加载即将执行的任务失败:', error);
      message.error('加载即将执行的任务失败');
      setUpcomingTasks([]);
    }
  };

  /**
   * 执行任务
   */
  const handleExecuteTask = (taskId) => {
    confirm({
      title: '确认执行任务',
      icon: <ExclamationCircleOutlined />,
      content: '确定要立即执行这个任务吗？',
      onOk: async () => {
        try {
          await pmiTaskApi.executeTask(taskId);
          message.success('任务执行成功');
          loadTasks();
          loadStatistics();
        } catch (error) {
          message.error('任务执行失败');
        }
      }
    });
  };

  /**
   * 取消任务
   */
  const handleCancelTask = (taskId) => {
    confirm({
      title: '确认取消任务',
      icon: <ExclamationCircleOutlined />,
      content: '确定要取消这个任务吗？取消后无法恢复。',
      onOk: async () => {
        try {
          await pmiTaskApi.cancelTask(taskId);
          message.success('任务取消成功');
          loadTasks();
          loadStatistics();
        } catch (error) {
          message.error('任务取消失败');
        }
      }
    });
  };

  /**
   * 显示重新调度模态框
   */
  const showRescheduleModal = (task) => {
    setSelectedTask(task);
    setRescheduleModalVisible(true);
    form.setFieldsValue({
      newExecuteTime: moment(task.scheduledTime)
    });
  };

  /**
   * 处理重新调度
   */
  const handleReschedule = async () => {
    try {
      const values = await form.validateFields();
      await pmiTaskApi.rescheduleTask(selectedTask.id, {
        newExecuteTime: values.newExecuteTime.format('YYYY-MM-DD HH:mm:ss'),
        reason: values.reason
      });

      message.success('任务重新调度成功');
      setRescheduleModalVisible(false);
      loadTasks();
      loadStatistics();
    } catch (error) {
      message.error('任务重新调度失败');
    }
  };

  /**
   * 显示任务详情
   */
  const showTaskDetail = (task) => {
    setSelectedTaskDetail(task);
    setTaskDetailDrawerVisible(true);
  };

  /**
   * 获取任务类型图标
   */
  const getTaskTypeIcon = (taskType) => {
    const iconMap = {
      [TASK_TYPE.PMI_WINDOW_OPEN]: <PlayCircleOutlined style={{ color: '#52c41a' }} />,
      [TASK_TYPE.PMI_WINDOW_CLOSE]: <PauseCircleOutlined style={{ color: '#fa8c16' }} />
    };
    return iconMap[taskType] || <ClockCircleOutlined />;
  };

  /**
   * 移动端任务卡片组件
   */
  const MobileTaskCard = ({ task }) => (
    <Card
      size="small"
      style={{ marginBottom: 8 }}
      bodyStyle={{ padding: '12px' }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
            {getTaskTypeIcon(task.taskType)}
            <Text strong style={{ marginLeft: 8, fontSize: 14 }}>
              {getTaskTypeText(task.taskType)}
            </Text>
            <Tag
              color={getTaskStatusColor(task.status)}
              size="small"
              style={{ marginLeft: 8 }}
            >
              {getTaskStatusText(task.status)}
            </Tag>
          </div>

          <div style={{ marginBottom: 4 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>任务ID: </Text>
            <Text style={{ fontSize: 12, fontFamily: 'monospace' }}>{task.id}</Text>
          </div>

          {task.pmiNumber && (
            <div style={{ marginBottom: 4 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>PMI: </Text>
              <Text strong style={{ fontSize: 12 }}>{task.pmiNumber}</Text>
            </div>
          )}

          <div style={{ marginBottom: 4 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>计划时间: </Text>
            <Text style={{ fontSize: 12 }}>
              {isMobile ? formatMobileTime(task.scheduledTime) : moment(task.scheduledTime).format('MM-DD HH:mm')}
            </Text>
          </div>

          {task.retryCount > 0 && (
            <div>
              <Text type="secondary" style={{ fontSize: 12 }}>重试: </Text>
              <Text style={{ fontSize: 12, color: '#fa8c16' }}>{task.retryCount}次</Text>
            </div>
          )}
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => showTaskDetail(task)}
          />
          <Dropdown
            overlay={getActionMenu(task)}
            trigger={['click']}
            placement="bottomRight"
          >
            <Button size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </div>
      </div>
    </Card>
  );

  /**
   * 任务操作菜单
   */
  const getActionMenu = (record) => (
    <Menu>
      {canExecuteTask(currentUser) && record.status === TASK_STATUS.SCHEDULED && (
        <Menu.Item 
          key="execute" 
          icon={<PlayCircleOutlined />}
          onClick={() => handleExecuteTask(record.id)}
        >
          立即执行
        </Menu.Item>
      )}
      {canRescheduleTask(currentUser) && record.status === TASK_STATUS.SCHEDULED && (
        <Menu.Item 
          key="reschedule" 
          icon={<EditOutlined />}
          onClick={() => showRescheduleModal(record)}
        >
          重新调度
        </Menu.Item>
      )}
      {canCancelTask(currentUser) && record.status === TASK_STATUS.SCHEDULED && (
        <Menu.Item 
          key="cancel" 
          icon={<StopOutlined />} 
          danger
          onClick={() => handleCancelTask(record.id)}
        >
          取消任务
        </Menu.Item>
      )}
    </Menu>
  );

  /**
   * 任务列表表格列定义
   */
  const taskColumns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (id) => <span style={{ fontFamily: 'monospace' }}>{id}</span>
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 120,
      render: (type) => (
        <Space>
          {getTaskTypeIcon(type)}
          <span>{getTaskTypeText(type)}</span>
        </Space>
      )
    },
    {
      title: 'PMI信息',
      key: 'pmiInfo',
      width: 200,
      render: (_, record) => (
        <div>
          <div>
            {record.pmiRecordId ? (
              <Link
                to={`/pmi-management/${record.pmiRecordId}`}
                style={{
                  fontWeight: 'bold',
                  color: '#1890ff',
                  textDecoration: 'none'
                }}
                onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
                onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
              >
                {record.pmiNumber}
              </Link>
            ) : (
              <span style={{ fontWeight: 'bold' }}>{record.pmiNumber}</span>
            )}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.userName}
          </div>
        </div>
      )
    },
    {
      title: '计划时间',
      dataIndex: 'scheduledTime',
      key: 'scheduledTime',
      width: 160,
      render: (time) => (
        <div>
          <div>{moment(time).format('MM-DD HH:mm')}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {moment(time).fromNow()}
          </div>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getTaskStatusColor(status)}>
          {getTaskStatusText(status)}
        </Tag>
      )
    },
    {
      title: '重试次数',
      dataIndex: 'retryCount',
      key: 'retryCount',
      width: 80,
      render: (count) => count || 0
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Dropdown overlay={getActionMenu(record)} trigger={['click']}>
          <Button size="small">
            操作 <DownOutlined />
          </Button>
        </Dropdown>
      )
    }
  ];

  return (
    <div style={{
      padding: isMobile ? '12px' : '24px',
      paddingBottom: isMobile ? '80px' : '24px' // 为移动端浮动按钮留空间
    }}>
      {/* 移动端下拉刷新指示器 */}
      {isMobile && isPulling && (
        <div style={{
          position: 'fixed',
          top: Math.min(pullDistance / 2, 40),
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1000,
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '8px 16px',
          borderRadius: '20px',
          fontSize: '12px',
          transition: 'all 0.3s ease'
        }}>
          {isRefreshing ? '刷新中...' : pullDistance > 80 ? '松开刷新' : '下拉刷新'}
        </div>
      )}

      {/* 统计卡片 */}
      <MobileStatistics statistics={statistics} isMobile={isMobile} />

      {/* 主要内容 */}
      <Card size={isMobile ? 'small' : 'default'}>
        <div style={{
          marginBottom: '16px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '8px'
        }}>
          <Title level={isMobile ? 4 : 3} style={{ margin: 0 }}>
            PMI任务监控
          </Title>
          <Space size="small">
            {isMobile && (
              <Button
                size="small"
                icon={<FilterOutlined />}
                onClick={() => setFilterDrawerVisible(true)}
              >
                筛选
              </Button>
            )}
            <Button
              size={isMobile ? 'small' : 'default'}
              icon={<ReloadOutlined />}
              onClick={() => {
                loadTasks();
                loadStatistics();
                loadUpcomingTasks();
              }}
            >
              {isMobile ? '' : '刷新'}
            </Button>
          </Space>
        </div>

        {isMobile ? (
          // 移动端显示
          <>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              size="small"
              tabBarStyle={{ marginBottom: '12px' }}
            >
              <TabPane tab="全部" key="all">
                <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
                  {tasks.map(task => (
                    <MobileTaskCard
                      key={task.id}
                      task={task}
                      onView={showTaskDetail}
                      onExecute={handleExecuteTask}
                      onCancel={handleCancelTask}
                      onReschedule={showRescheduleModal}
                      currentUser={currentUser}
                      canExecuteTask={canExecuteTask}
                      canCancelTask={canCancelTask}
                      canRescheduleTask={canRescheduleTask}
                    />
                  ))}
                  {tasks.length === 0 && !loading && (
                    <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                      暂无任务数据
                    </div>
                  )}
                </div>
              </TabPane>

              <TabPane tab={`即将执行 (${upcomingTasks.length})`} key="upcoming">
                <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
                  {upcomingTasks.map(task => (
                    <MobileTaskCard
                      key={task.id}
                      task={task}
                      onView={showTaskDetail}
                      onExecute={handleExecuteTask}
                      onCancel={handleCancelTask}
                      onReschedule={showRescheduleModal}
                      currentUser={currentUser}
                      canExecuteTask={canExecuteTask}
                      canCancelTask={canCancelTask}
                      canRescheduleTask={canRescheduleTask}
                    />
                  ))}
                  {upcomingTasks.length === 0 && (
                    <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                      暂无即将执行的任务
                    </div>
                  )}
                </div>
              </TabPane>
            </Tabs>
          </>
        ) : (
          // 桌面端显示
          <Tabs defaultActiveKey="all">
            <TabPane tab="所有任务" key="all">
              <Table
                columns={taskColumns}
                dataSource={tasks}
                loading={loading}
                pagination={{
                  ...pagination,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`
                }}
                onChange={(paginationInfo) => {
                  console.log('📄 分页变更:', paginationInfo);
                  setPagination(paginationInfo);
                  loadTasks({}, paginationInfo);
                }}
                rowKey="id"
                size="small"
                scroll={{ x: 1200 }}
              />
            </TabPane>

            <TabPane tab={`即将执行 (${upcomingTasks.length})`} key="upcoming">
              <Table
                columns={taskColumns}
                dataSource={upcomingTasks}
                pagination={false}
                rowKey="id"
                size="small"
                scroll={{ x: 1200 }}
              />
            </TabPane>
          </Tabs>
        )}
      </Card>

      {/* 重新调度模态框 */}
      <Modal
        title="重新调度任务"
        visible={rescheduleModalVisible}
        onOk={handleReschedule}
        onCancel={() => setRescheduleModalVisible(false)}
        okText="确认"
        cancelText="取消"
        width={isMobile ? '90%' : 520}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="newExecuteTime"
            label="新执行时间"
            rules={[{ required: true, message: '请选择新的执行时间' }]}
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              style={{ width: '100%' }}
              size={isMobile ? 'large' : 'middle'}
            />
          </Form.Item>
          <Form.Item
            name="reason"
            label="重新调度原因"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入重新调度的原因..."
              size={isMobile ? 'large' : 'middle'}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 移动端任务详情抽屉 */}
      <Drawer
        title="任务详情"
        placement="bottom"
        height="70%"
        visible={taskDetailDrawerVisible}
        onClose={() => setTaskDetailDrawerVisible(false)}
        bodyStyle={{ padding: '16px' }}
      >
        {selectedTaskDetail && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                {getTaskTypeIcon(selectedTaskDetail.taskType)}
                <Text strong style={{ marginLeft: '8px', fontSize: '16px' }}>
                  {getTaskTypeText(selectedTaskDetail.taskType)}
                </Text>
                <Tag
                  color={getTaskStatusColor(selectedTaskDetail.status)}
                  style={{ marginLeft: '8px' }}
                >
                  {getTaskStatusText(selectedTaskDetail.status)}
                </Tag>
              </div>
            </div>

            <Divider />

            <div style={{ marginBottom: '12px' }}>
              <Text type="secondary">任务ID</Text>
              <br />
              <Text style={{ fontFamily: 'monospace' }}>{selectedTaskDetail.id}</Text>
            </div>

            <div style={{ marginBottom: '12px' }}>
              <Text type="secondary">PMI窗口ID</Text>
              <br />
              <Text>{selectedTaskDetail.pmiWindowId}</Text>
            </div>

            {selectedTaskDetail.pmiNumber && (
              <div style={{ marginBottom: '12px' }}>
                <Text type="secondary">PMI号码</Text>
                <br />
                <Text strong>{selectedTaskDetail.pmiNumber}</Text>
              </div>
            )}

            <div style={{ marginBottom: '12px' }}>
              <Text type="secondary">计划执行时间</Text>
              <br />
              <Text>{moment(selectedTaskDetail.scheduledTime).format('YYYY-MM-DD HH:mm:ss')}</Text>
            </div>

            {selectedTaskDetail.actualExecutionTime && (
              <div style={{ marginBottom: '12px' }}>
                <Text type="secondary">实际执行时间</Text>
                <br />
                <Text>{moment(selectedTaskDetail.actualExecutionTime).format('YYYY-MM-DD HH:mm:ss')}</Text>
              </div>
            )}

            <div style={{ marginBottom: '12px' }}>
              <Text type="secondary">重试次数</Text>
              <br />
              <Text>{selectedTaskDetail.retryCount || 0}</Text>
            </div>

            {selectedTaskDetail.errorMessage && (
              <div style={{ marginBottom: '12px' }}>
                <Text type="secondary">错误信息</Text>
                <br />
                <Text type="danger">{selectedTaskDetail.errorMessage}</Text>
              </div>
            )}

            <Divider />

            <Space direction="vertical" style={{ width: '100%' }}>
              {canExecuteTask(currentUser) && selectedTaskDetail.status === TASK_STATUS.SCHEDULED && (
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  block
                  size="large"
                  onClick={() => {
                    handleExecuteTask(selectedTaskDetail.id);
                    setTaskDetailDrawerVisible(false);
                  }}
                >
                  立即执行
                </Button>
              )}

              {canRescheduleTask(currentUser) && selectedTaskDetail.status === TASK_STATUS.SCHEDULED && (
                <Button
                  icon={<EditOutlined />}
                  block
                  size="large"
                  onClick={() => {
                    showRescheduleModal(selectedTaskDetail);
                    setTaskDetailDrawerVisible(false);
                  }}
                >
                  重新调度
                </Button>
              )}

              {canCancelTask(currentUser) && selectedTaskDetail.status === TASK_STATUS.SCHEDULED && (
                <Button
                  danger
                  icon={<StopOutlined />}
                  block
                  size="large"
                  onClick={() => {
                    handleCancelTask(selectedTaskDetail.id);
                    setTaskDetailDrawerVisible(false);
                  }}
                >
                  取消任务
                </Button>
              )}
            </Space>
          </div>
        )}
      </Drawer>

      {/* 移动端浮动按钮 */}
      {isMobile && (
        <FloatButton.Group
          trigger="click"
          type="primary"
          style={{ right: 16, bottom: 16 }}
          icon={<MoreOutlined />}
        >
          <FloatButton
            icon={<ReloadOutlined />}
            tooltip="刷新"
            onClick={() => {
              loadTasks();
              loadStatistics();
              loadUpcomingTasks();
            }}
          />
          <FloatButton
            icon={<FilterOutlined />}
            tooltip="筛选"
            onClick={() => setFilterDrawerVisible(true)}
          />
          <FloatButton
            icon={<DashboardOutlined />}
            tooltip="统计"
            onClick={() => {
              // 滚动到顶部显示统计卡片
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }}
          />
        </FloatButton.Group>
      )}

      {/* 移动端筛选抽屉 */}
      <MobileFilterDrawer
        visible={filterDrawerVisible}
        onClose={() => setFilterDrawerVisible(false)}
        onFilter={(filters) => {
          // 应用筛选条件
          loadTasks(filters);
        }}
      />
    </div>
  );
};

export default PmiTaskMonitor;
