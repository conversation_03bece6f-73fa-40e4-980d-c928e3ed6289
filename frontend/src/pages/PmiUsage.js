import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  message,
  Input,
  Row,
  Col,
  Divider,
  <PERSON><PERSON>,
  Spin,
  Result,
  Tag,
  Modal
} from 'antd';
import {
  SearchOutlined,
  PlayCircleOutlined,
  CopyOutlined,
  LinkOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { publicPmiApi, systemConfigApi } from '../services/api';
import { useParams } from 'react-router-dom';

const { Title, Text } = Typography;
const { Search } = Input;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

// 格式化时长为小时分钟格式
const formatDuration = (minutes) => {
  if (!minutes || minutes <= 0) return '0分钟';

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours === 0) {
    return `${remainingMinutes}分钟`;
  } else if (remainingMinutes === 0) {
    return `${hours}小时`;
  } else {
    return `${hours}小时${remainingMinutes}分钟`;
  }
};

// 计算会议持续时长
const calculateMeetingDuration = (startTime) => {
  if (!startTime) return '未知';

  const start = new Date(startTime);
  const now = new Date();
  const diffMs = now - start;
  const diffMinutes = Math.floor(diffMs / (1000 * 60));

  if (diffMinutes < 1) return '刚开始';

  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;

  if (hours === 0) {
    return `${minutes}分钟`;
  } else if (minutes === 0) {
    return `${hours}小时`;
  } else {
    return `${hours}小时${minutes}分钟`;
  }
};

const PmiUsage = () => {
  const { pmiNumber: urlPmiNumber } = useParams();

  const [pmiNumber, setPmiNumber] = useState(urlPmiNumber || '');
  const [pmiInfo, setPmiInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activating, setActivating] = useState(false);
  const [copyModalVisible, setCopyModalVisible] = useState(false);
  const [copyText, setCopyText] = useState('');
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [refreshInterval, setRefreshInterval] = useState(null);
  const [systemConfigs, setSystemConfigs] = useState({});

  // 获取系统配置
  const fetchSystemConfigs = async () => {
    try {
      const response = await systemConfigApi.getConfigs();
      if (response.data.success) {
        const configMap = {};
        response.data.data.content.forEach(config => {
          configMap[config.configKey] = config.configValue;
        });
        setSystemConfigs(configMap);
      }
    } catch (error) {
      console.error('获取系统配置失败:', error);
    }
  };

  // 获取用户前端域名
  const getUserFrontendBaseUrl = () => {
    return systemConfigs['user_frontend.domain.base_url'] || 'http://localhost:3001';
  };

  // 加载PMI信息
  const loadPmiInfo = async (targetPmiNumber) => {
    const pmiToLoad = targetPmiNumber || pmiNumber;
    if (!pmiToLoad || pmiToLoad.trim().length < 3) {
      if (targetPmiNumber) {
        message.error('请输入正确的PMI号码或魔链ID');
      }
      return;
    }

    try {
      setLoading(true);
      const response = await publicPmiApi.getPmiInfo(pmiToLoad.trim());

      if (response.data.success) {
        setPmiInfo(response.data.data);
        if (targetPmiNumber) {
          setPmiNumber(pmiToLoad.trim());
        }
      } else {
        message.error(response.data.message || 'PMI不存在');
        setPmiInfo(null);
      }
    } catch (error) {
      if (error.response?.status === 404) {
        message.error('PMI不存在，请检查号码是否正确');
      } else {
        message.error('查询PMI信息失败: ' + (error.response?.data?.message || error.message));
      }
      setPmiInfo(null);
      console.error('Error loading PMI info:', error);
    } finally {
      setLoading(false);
    }
  };

  // 搜索PMI信息
  const handleSearch = (searchPmiNumber) => {
    loadPmiInfo(searchPmiNumber);
  };

  // 一键开启PMI
  const handleActivatePmi = async () => {
    try {
      setActivating(true);
      const response = await publicPmiApi.activatePmi(pmiNumber);

      message.success('PMI开启成功！正在跳转到主持人链接...');

      // 跳转到主持人链接
      if (response.data.data?.hostUrl) {
        window.open(response.data.data.hostUrl, '_blank');
      } else {
        console.error('响应中没有找到hostUrl:', response.data);
        message.warning('获取主持人链接失败，请手动刷新页面重试');
      }

      // 开启成功后立即刷新页面数据
      setTimeout(() => {
        loadPmiInfo();
      }, 1000); // 延迟1秒刷新，确保后端状态已更新

    } catch (error) {
      message.error('开启PMI失败: ' + (error.response?.data?.message || error.message));
      console.error('Error activating PMI:', error);
    } finally {
      setActivating(false);
    }
  };

  // 获取复制信息
  const handleGetCopyText = async () => {
    try {
      const response = await publicPmiApi.getPmiCopyText(pmiNumber);
      setCopyText(response.data.copyText);
      setCopyModalVisible(true);
    } catch (error) {
      message.error('获取复制信息失败');
      console.error('Error getting copy text:', error);
    }
  };

  // 结束会议
  const handleEndMeeting = async () => {
    if (!pmiInfo?.activeMeetingId) {
      message.error('没有找到活跃的会议');
      return;
    }

    // 使用浏览器原生确认对话框
    const confirmed = window.confirm('确定要结束当前会议吗？会议结束后将无法恢复。');

    if (confirmed) {
      try {
        await publicPmiApi.endMeeting(pmiInfo.activeMeetingId);
        message.success('会议已成功结束');
        // 重新加载PMI信息以更新状态
        loadPmiInfo();
      } catch (error) {
        message.error('结束会议失败: ' + (error.response?.data?.message || error.message));
        console.error('Error ending meeting:', error);
      }
    }
  };

  // 复制到剪贴板
  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(copyText).then(() => {
      message.success('已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败，请手动复制');
    });
  };

  // 页面加载时自动搜索URL中的PMI号码
  useEffect(() => {
    fetchSystemConfigs();
    if (urlPmiNumber) {
      handleSearch(urlPmiNumber);
    }
  }, [urlPmiNumber]);

  // 设置定时刷新
  useEffect(() => {
    // 清除之前的定时器
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }

    // 如果有活跃会议，设置每10秒刷新一次
    if (pmiInfo?.hasActiveMeeting) {
      const interval = setInterval(() => {
        loadPmiInfo();
      }, 10000); // 10秒刷新一次

      setRefreshInterval(interval);
    }

    // 组件卸载时清除定时器
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [pmiInfo?.hasActiveMeeting]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f0f2f5',
      padding: isMobileView ? '16px' : '24px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: isMobileView ? '100%' : 600,
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
        }}
        size={isMobileView ? 'small' : 'default'}
      >
        <div style={{ textAlign: 'center', marginBottom: isMobileView ? 24 : 32 }}>
          <Title level={isMobileView ? 3 : 2}>
            <LinkOutlined /> PMI会议室使用
          </Title>
          <Text type="secondary" style={{ fontSize: isMobileView ? '14px' : '16px' }}>
            输入PMI号码，一键开启Zoom会议室
          </Text>
          <div style={{ marginTop: 8 }}>
            <Tag color="blue">管理台预览版</Tag>
            {urlPmiNumber && (
              <Tag
                color="green"
                style={{ marginLeft: 8, cursor: 'pointer' }}
                onClick={() => window.open(`${getUserFrontendBaseUrl()}/m/${pmiInfo?.magicId || urlPmiNumber}`, '_blank')}
              >
                <LinkOutlined /> 用户版
              </Tag>
            )}
          </div>
        </div>

        {/* 搜索区域 - 只在没有URL参数时显示 */}
        {!urlPmiNumber && (
          <div style={{ marginBottom: isMobileView ? 16 : 24 }}>
            <Search
              placeholder={isMobileView ? "输入PMI号码" : "请输入10位PMI号码"}
              allowClear
              size={isMobileView ? 'middle' : 'large'}
              value={pmiNumber}
              onChange={(e) => setPmiNumber(e.target.value)}
              onSearch={handleSearch}
              enterButton={
                <Button type="primary" icon={<SearchOutlined />}>
                  {isMobileView ? '查询' : '查询PMI'}
                </Button>
              }
              loading={loading}
            />
          </div>
        )}

        {/* PMI信息展示 */}
        {loading && (
          <div style={{ textAlign: 'center', padding: 40 }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text>正在查询PMI信息...</Text>
            </div>
          </div>
        )}

        {pmiInfo && !loading && (
          <Card
            style={{ marginBottom: isMobileView ? 16 : 24 }}
            size={isMobileView ? 'small' : 'default'}
            title={
              <Space>
                <InfoCircleOutlined />
                <span style={{ fontSize: isMobileView ? '14px' : '16px' }}>PMI信息</span>
              </Space>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <div style={{ position: 'relative' }}>
                  <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>会议号：</Text>
                  <br />
                  <Text copyable style={{
                    fontSize: isMobileView ? 16 : 18,
                    color: '#1890ff',
                    wordBreak: 'break-all'
                  }}>
                    {pmiInfo.pmiNumber}
                  </Text>
                </div>
              </Col>
              {pmiInfo.pmiPassword && (
                <Col xs={24} sm={12}>
                  <div style={{ position: 'relative' }}>
                    <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>会议密码：</Text>
                    <br />
                    <Text copyable style={{
                      fontSize: isMobileView ? 16 : 18,
                      color: '#1890ff',
                      wordBreak: 'break-all'
                    }}>
                      {pmiInfo.pmiPassword}
                    </Text>
                    {/* 获取复制信息按钮 - 放在会议密码右下方 */}
                    <Button
                      size="small"
                      icon={<CopyOutlined />}
                    onClick={handleGetCopyText}
                    style={{
                      position: 'absolute',
                      bottom: -8,
                      right: 0,
                      fontSize: '12px',
                      height: '24px',
                      padding: '0 8px'
                    }}
                  >
                    复制信息
                    </Button>
                  </div>
                </Col>
              )}
              <Col xs={24} sm={12}>
                <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>会议室状态：</Text>
                <Tag color={pmiInfo.status === 'ACTIVE' ? 'green' : pmiInfo.status === 'INACTIVE' ? 'orange' : 'red'} style={{ marginLeft: 8, whiteSpace: 'nowrap' }}>
                  {pmiInfo.statusDescription || pmiInfo.status}
                </Tag>
              </Col>
            </Row>

            <Divider />

            {/* 根据会议室状态和计费模式显示不同的提示信息 */}
            {pmiInfo.canActivate || pmiInfo.hasActiveMeeting ? (
              <>
                {/* 会议进行中时的显示 */}
                {pmiInfo.hasActiveMeeting && (
                  <Alert
                    message="会议进行中"
                    description={
                      <div>
                        <p><strong>会议持续时长：</strong>{calculateMeetingDuration(pmiInfo.activeMeetingStartTime)}</p>
                        {pmiInfo.billingMode === 'BY_TIME' && (
                          <p><strong>剩余可用时长：</strong>{
                            pmiInfo.remainingMinutes !== undefined
                              ? formatDuration(pmiInfo.remainingMinutes)
                              : formatDuration(Math.max(0, (pmiInfo.availableMinutes || 0) - (pmiInfo.pendingDeductMinutes || 0)))
                          }</p>
                        )}
                        {pmiInfo.billingMode === 'LONG' && pmiInfo.windowExpireTime && (
                          <p><strong>有效期至：</strong>{new Date(pmiInfo.windowExpireTime).toLocaleString('zh-CN')}</p>
                        )}
                      </div>
                    }
                    type="success"
                    showIcon
                    style={{ marginBottom: 24 }}
                  />
                )}

                {/* 会议未开始时的显示 */}
                {!pmiInfo.hasActiveMeeting && (
                  <>
                    {pmiInfo.billingMode === 'BY_TIME' && (
                      <Alert
                        description={
                          <div>
                            <p><strong>剩余可用时长：</strong>{
                              pmiInfo.remainingMinutes !== undefined
                                ? formatDuration(pmiInfo.remainingMinutes)
                                : formatDuration(Math.max(0, (pmiInfo.availableMinutes || 0) - (pmiInfo.pendingDeductMinutes || 0)))
                            }</p>
                            {pmiInfo.overdraftMinutes > 0 && (
                              <p style={{ color: '#ff4d4f' }}><strong>超额时长：</strong>{pmiInfo.overdraftMinutes} 分钟</p>
                            )}
                          </div>
                        }
                        type="info"
                        showIcon
                        style={{ marginBottom: 24 }}
                      />
                    )}
                    {pmiInfo.billingMode === 'LONG' && (
                      <Alert
                        description={
                          <div>
                            {pmiInfo.windowExpireTime && (
                              <p><strong>有效期至：</strong>{new Date(pmiInfo.windowExpireTime).toLocaleString('zh-CN')}</p>
                            )}
                          </div>
                        }
                        type="info"
                        showIcon
                        style={{ marginBottom: 24 }}
                      />
                    )}
                  </>
                )}
              </>
            ) : (
              <Alert
                message="PMI状态提示"
                description={pmiInfo.statusMessage || "此PMI当前不可用"}
                type="warning"
                showIcon
                style={{ marginBottom: 24 }}
              />
            )}

            {/* 会议室操作按钮 */}
            {pmiInfo.hasActiveMeeting ? (
              // 会议进行中时显示进入会议和结束会议按钮
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={() => window.open(pmiInfo.hostUrl, '_blank')}
                  style={{
                    flex: 1,
                    height: isMobileView ? 48 : 56,
                    fontSize: isMobileView ? '14px' : '16px',
                    fontWeight: 'bold'
                  }}
                >
                  进入会议 Join Meeting
                </Button>
                <Button
                  type="default"
                  size="large"
                  danger
                  onClick={handleEndMeeting}
                  style={{
                    flex: 1,
                    height: isMobileView ? 48 : 56,
                    fontSize: isMobileView ? '14px' : '16px',
                    fontWeight: 'bold'
                  }}
                >
                  结束会议 End Meeting
                </Button>
              </div>
            ) : (
              // 会议未开启时显示开启按钮
              <Button
                type="primary"
                size="large"
                block
                icon={<PlayCircleOutlined />}
                loading={activating}
                onClick={handleActivatePmi}
                disabled={!pmiInfo.canActivate}
                style={{
                  height: isMobileView ? 48 : 56,
                  fontSize: isMobileView ? '16px' : '18px',
                  fontWeight: 'bold'
                }}
              >
                {pmiInfo.canActivate ? '一键开启会议室 Start Meeting' : '会议室不可用'}
              </Button>
            )}
          </Card>
        )}

        {!pmiInfo && !loading && pmiNumber && (
          <Result
            status="404"
            title="会议室不存在"
            subTitle="请检查会议号是否正确，或联系管理员"
            extra={
              <Button type="primary" onClick={() => setPmiNumber('')}>
                重新输入
              </Button>
            }
          />
        )}

        {/* 使用提示 */}
        <Card size="small" style={{ backgroundColor: '#fafafa' }}>
          <Title level={5}>使用提示：</Title>
          <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
            <li>会议号为10位数字</li>
            <li>主持人链接仅供主持人使用，请勿分享给参会者</li>
            <li>会议进行中页面会每10秒自动刷新状态</li>
            <li>如遇问题，请联系技术支持</li>
            <li style={{ color: '#1890ff' }}>管理员可通过此页面预览和测试所有PMI功能</li>
          </ul>
        </Card>
      </Card>

      {/* 复制信息弹窗 */}
      <Modal
        title="复制会议信息"
        open={copyModalVisible}
        onCancel={() => setCopyModalVisible(false)}
        footer={[
          <Button key="copy" type="primary" onClick={handleCopyToClipboard}>
            复制到剪贴板
          </Button>,
          <Button key="close" onClick={() => setCopyModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        <Input.TextArea
          value={copyText}
          rows={8}
          readOnly
          style={{ fontFamily: 'monospace' }}
        />
      </Modal>
    </div>
  );
};

export default PmiUsage;