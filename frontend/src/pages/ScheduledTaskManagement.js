import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Button,
  Tag,
  Space,
  Statistic,
  message,
  Modal,
  Descriptions,
  Badge,
  Typography,
  Tooltip,
  Progress,
  Alert,
  Select,
  DatePicker,
  Input,
  Tabs,
  Drawer,
  Empty,
  Spin
} from 'antd';
import {
  ReloadOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  DashboardOutlined,
  SyncOutlined,
  SearchOutlined,
  HistoryOutlined,
  BarChartOutlined,
  LineChartOutlined,
  RiseOutlined,
  HeartOutlined,
  BellOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import api from '../services/api';
import moment from 'moment';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const ScheduledTaskManagement = () => {
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [taskTrend, setTaskTrend] = useState(null);
  const [taskPerformance, setTaskPerformance] = useState(null);
  const [taskHealth, setTaskHealth] = useState(null);
  const [activeAlerts, setActiveAlerts] = useState([]);
  const [analyticsDrawerVisible, setAnalyticsDrawerVisible] = useState(false);
  const [healthDrawerVisible, setHealthDrawerVisible] = useState(false);
  const [alertsDrawerVisible, setAlertsDrawerVisible] = useState(false);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [trendDays, setTrendDays] = useState(7);
  const [runningTasks, setRunningTasks] = useState([]);
  const [taskHistory, setTaskHistory] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [historyModalVisible, setHistoryModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // 搜索和过滤状态
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState([]);

  // 获取任务概览
  const fetchTaskOverview = async () => {
    try {
      const response = await api.get('/admin/scheduled-tasks/overview');
      if (response.data.success) {
        setTasks(response.data.data.tasks);
        setStatistics(response.data.data.statistics);
      }
    } catch (error) {
      message.error('获取任务概览失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 获取正在运行的任务
  const fetchRunningTasks = async () => {
    try {
      const response = await api.get('/admin/scheduled-tasks/running');
      if (response.data.success) {
        setRunningTasks(response.data.data.tasks);
      }
    } catch (error) {
      console.error('获取运行中任务失败:', error);
    }
  };

  // 获取任务执行历史
  const fetchTaskHistory = async (taskName, page = 0, size = 20) => {
    try {
      setLoading(true);
      const params = {
        page,
        size,
        ...(dateRange.length === 2 && {
          startTime: dateRange[0].format('YYYY-MM-DD HH:mm:ss'),
          endTime: dateRange[1].format('YYYY-MM-DD HH:mm:ss')
        })
      };
      
      const response = await api.get(`/admin/scheduled-tasks/${taskName}/history`, { params });
      if (response.data.success) {
        setTaskHistory(response.data.data.content);
      }
    } catch (error) {
      message.error('获取任务历史失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 手动触发任务
  const triggerTask = async (taskName) => {
    try {
      setLoading(true);
      const response = await api.post(`/admin/scheduled-tasks/${taskName}/trigger`);
      
      if (response.data.success) {
        message.success(`任务 ${taskName} 已手动触发`);
        await fetchTaskOverview();
        await fetchRunningTasks();
      } else {
        message.error(response.data.message || '触发任务失败');
      }
    } catch (error) {
      message.error('触发任务失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };



  // 模拟分析数据（作为fallback）
  const mockAnalytics = {
    globalStats: {
      totalTasks: 12,
      totalExecutions: 1456,
      globalSuccessRate: 0.94,
      globalFailedCount: 87
    },
    taskSummaries: [
      {
        taskName: 'UserDataSync',
        totalExecutions: 288,
        successCount: 276,
        failedCount: 12,
        successRate: 0.958,
        avgDuration: 2340
      },
      {
        taskName: 'ReportGeneration',
        totalExecutions: 144,
        successCount: 138,
        failedCount: 6,
        successRate: 0.958,
        avgDuration: 5670
      },
      {
        taskName: 'DataCleanup',
        totalExecutions: 72,
        successCount: 68,
        failedCount: 4,
        successRate: 0.944,
        avgDuration: 1230
      },
      {
        taskName: 'EmailNotification',
        totalExecutions: 432,
        successCount: 398,
        failedCount: 34,
        successRate: 0.921,
        avgDuration: 890
      },
      {
        taskName: 'BackupTask',
        totalExecutions: 24,
        successCount: 22,
        failedCount: 2,
        successRate: 0.917,
        avgDuration: 12340
      }
    ],
    reportTime: moment().format('YYYY-MM-DD HH:mm:ss')
  };

  const mockTrendData = [
    { date: '2025-08-08', total: 45, success: 42, failed: 3 },
    { date: '2025-08-09', total: 52, success: 48, failed: 4 },
    { date: '2025-08-10', total: 38, success: 36, failed: 2 },
    { date: '2025-08-11', total: 61, success: 57, failed: 4 },
    { date: '2025-08-12', total: 49, success: 46, failed: 3 },
    { date: '2025-08-13', total: 55, success: 52, failed: 3 },
    { date: '2025-08-14', total: 43, success: 41, failed: 2 }
  ];

  const mockActiveAlerts = [
    {
      taskName: 'UserDataSync',
      type: 'EXECUTION_TIME',
      message: '任务执行时间超过阈值',
      severity: 'HIGH',
      alertTime: moment().subtract(2, 'hours').toISOString()
    },
    {
      taskName: 'BackupTask',
      type: 'FAILURE_RATE',
      message: '任务失败率过高',
      severity: 'CRITICAL',
      alertTime: moment().subtract(30, 'minutes').toISOString()
    }
  ];

  // 获取全局分析数据
  const fetchAnalytics = async (days = 7) => {
    try {
      setAnalyticsLoading(true);
      const response = await api.get(`/admin/scheduled-tasks/analytics/overview?days=${days}`);
      if (response.data.success) {
        setAnalytics(response.data.data);
      } else {
        message.error(response.data.message || '获取分析数据失败');
      }
    } catch (error) {
      console.error('获取分析数据失败:', error);
      message.error('获取分析数据失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setAnalyticsLoading(false);
    }
  };

  // 获取任务趋势数据
  const fetchTaskTrend = async (taskName, days = 7) => {
    try {
      const response = await api.get(`/admin/scheduled-tasks/${taskName}/trend?days=${days}`);
      if (response.data.success) {
        setTaskTrend(response.data.data);
      } else {
        message.error(response.data.message || '获取趋势数据失败');
      }
    } catch (error) {
      console.error('获取趋势数据失败:', error);
      message.error('获取趋势数据失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 获取任务性能报告
  const fetchTaskPerformance = async (taskName, days = 7) => {
    try {
      const response = await api.get(`/admin/scheduled-tasks/${taskName}/performance?days=${days}`);
      if (response.data.success) {
        setTaskPerformance(response.data.data);
      } else {
        message.error(response.data.message || '获取性能报告失败');
      }
    } catch (error) {
      console.error('获取性能报告失败:', error);
      message.error('获取性能报告失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 获取任务健康状态
  const fetchTaskHealth = async (taskName) => {
    try {
      const response = await api.get(`/admin/scheduled-tasks/${taskName}/health`);
      if (response.data.success) {
        setTaskHealth(response.data.data);
      } else {
        message.error(response.data.message || '获取健康状态失败');
      }
    } catch (error) {
      console.error('获取健康状态失败:', error);
      message.error('获取健康状态失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 获取活跃告警
  const fetchActiveAlerts = async () => {
    try {
      const response = await api.get('/admin/scheduled-tasks/alerts/active');
      if (response.data.success) {
        setActiveAlerts(response.data.data || []);
      } else {
        message.error(response.data.message || '获取告警信息失败');
      }
    } catch (error) {
      console.error('获取告警信息失败:', error);
      // 如果获取告警失败，设置为空数组，不显示错误消息（告警不是核心功能）
      setActiveAlerts([]);
    }
  };

  // 清除缓存
  const clearCache = async (taskName = null) => {
    try {
      const params = taskName ? { taskName } : {};
      const response = await api.post('/admin/scheduled-tasks/cache/clear', params);
      if (response.data.success) {
        message.success(response.data.message);
        await handleRefresh();
      }
    } catch (error) {
      message.error('清除缓存失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 刷新所有数据
  const handleRefresh = async () => {
    await Promise.all([fetchTaskOverview(), fetchRunningTasks(), fetchActiveAlerts()]);
  };

  useEffect(() => {
    handleRefresh();
    fetchAnalytics(trendDays); // 初始化分析数据

    // 每30秒自动刷新
    const interval = setInterval(handleRefresh, 30000);
    return () => clearInterval(interval);
  }, []);

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      'SUCCESS': { color: 'success', icon: <CheckCircleOutlined />, text: '成功' },
      'FAILED': { color: 'error', icon: <ExclamationCircleOutlined />, text: '失败' },
      'RUNNING': { color: 'processing', icon: <SyncOutlined spin />, text: '运行中' },
      'PENDING': { color: 'default', icon: <ClockCircleOutlined />, text: '等待中' },
      'RETRYING': { color: 'warning', icon: <WarningOutlined />, text: '重试中' },
      'CANCELLED': { color: 'default', icon: <ExclamationCircleOutlined />, text: '已取消' }
    };
    
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color} icon={config.icon}>{config.text}</Tag>;
  };

  // 获取任务类型标签
  const getTaskTypeTag = (taskType) => {
    const typeMap = {
      'MEETING_STATUS_SYNC': { color: 'blue', text: '会议状态同步' },
      'PMI_WINDOW_ACTIVATION': { color: 'purple', text: 'PMI窗口激活' },
      'PMI_STATUS_MANAGEMENT': { color: 'purple', text: 'PMI状态管理' },
      'ZOOM_TOKEN_REFRESH': { color: 'magenta', text: 'Token刷新' },
      'DAILY_USER_SYNC': { color: 'cyan', text: '用户同步' },
      'WINDOW_EXPIRY_CHECK': { color: 'orange', text: '窗口过期检查' },
      'BILLING_MONITOR_CHECK': { color: 'green', text: '计费监控' },
      'BATCH_SETTLEMENT': { color: 'green', text: '批量结算' },
      'TEMP_MEETING_CLEANUP': { color: 'red', text: '临时会议清理' }
    };
    
    const config = typeMap[taskType] || { color: 'default', text: taskType };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 格式化持续时间
  const formatDuration = (durationMs) => {
    if (!durationMs) return '-';
    
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // 计算成功率
  const calculateSuccessRate = (successCount, totalCount) => {
    if (totalCount === 0) return 0;
    return Math.round((successCount / totalCount) * 100);
  };

  // 渲染趋势图表
  const renderTrendChart = () => {
    try {
      // 如果有真实的趋势数据，使用真实数据
      if (taskTrend && taskTrend.trendData && Array.isArray(taskTrend.trendData) && taskTrend.trendData.length > 0) {
        const trendConfig = {
          data: taskTrend.trendData,
          xField: 'date',
          yField: 'value',
          seriesField: 'type',
          smooth: true,
          color: ['#1890ff', '#52c41a', '#ff4d4f'],
          legend: { position: 'top' }
        };
        return <Line {...trendConfig} />;
      }

      // 否则使用模拟数据
      const mockData = mockTrendData.flatMap(item => [
        { date: item.date, type: '总执行', value: item.total },
        { date: item.date, type: '成功', value: item.success },
        { date: item.date, type: '失败', value: item.failed }
      ]);

      if (mockData.length === 0) {
        return <Empty description="暂无趋势数据" />;
      }

      const trendConfig = {
        data: mockData,
        xField: 'date',
        yField: 'value',
        seriesField: 'type',
        smooth: true,
        color: ['#1890ff', '#52c41a', '#ff4d4f'],
        legend: { position: 'top' }
      };

      return <Line {...trendConfig} />;
    } catch (error) {
      console.error('渲染趋势图表失败:', error);
      return <Empty description="图表渲染失败" />;
    }
  };

  // 渲染性能图表
  const renderPerformanceChart = () => {
    try {
      // 如果有真实的性能数据，使用真实数据
      if (taskPerformance && taskPerformance.performance) {
        const performanceData = [
          { type: '平均执行时间', value: taskPerformance.performance.avgDuration || 0 },
          { type: '最小执行时间', value: taskPerformance.performance.minDuration || 0 },
          { type: '最大执行时间', value: taskPerformance.performance.maxDuration || 0 },
          { type: 'P95执行时间', value: taskPerformance.performance.p95Duration || 0 }
        ];

        const config = {
          data: performanceData,
          xField: 'type',
          yField: 'value',
          color: '#1890ff',
          columnWidthRatio: 0.6
        };
        return <Column {...config} />;
      }

      // 否则使用分析数据中的平均执行时间
      const performanceData = analytics?.taskSummaries?.map(task => ({
        taskName: task.taskName,
        avgDuration: task.avgDuration || 0
      })) || [];

      if (performanceData.length === 0) {
        return <Empty description="暂无性能数据" />;
      }

      const config = {
        data: performanceData,
        xField: 'taskName',
        yField: 'avgDuration',
        color: '#1890ff',
        columnWidthRatio: 0.6
      };

      return <Column {...config} />;
    } catch (error) {
      console.error('渲染性能图表失败:', error);
      return <Empty description="图表渲染失败" />;
    }
  };

  // 渲染成功率饼图
  const renderSuccessRatePie = () => {
    try {
      const pieData = analytics?.taskSummaries?.map(task => ({
        type: task.taskName,
        value: Math.round((task.successRate || 0) * 100)
      })) || [];

      if (pieData.length === 0) {
        return <Empty description="暂无成功率数据" />;
      }

      const config = {
        appendPadding: 10,
        data: pieData,
        angleField: 'value',
        colorField: 'type',
        radius: 0.8,
        legend: {
          position: 'bottom'
        },
        interactions: [
          {
            type: 'element-selected'
          },
          {
            type: 'element-active'
          }
        ]
      };

      return <Pie {...config} />;
    } catch (error) {
      console.error('渲染成功率饼图失败:', error);
      return <Empty description="图表渲染失败" />;
    }
  };

  // 过滤任务
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.taskName.toLowerCase().includes(searchText.toLowerCase()) ||
                         task.taskType.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || task.lastStatus === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DashboardOutlined /> 定时任务管理
      </Title>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="任务概览" key="overview">
          {/* 统计卡片 */}
          {statistics && (
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总任务数"
                    value={statistics.totalTasks}
                    prefix={<DashboardOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="运行中任务"
                    value={statistics.runningTasks}
                    prefix={<SyncOutlined spin />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="今日执行次数"
                    value={statistics.todayExecutions}
                    prefix={<PlayCircleOutlined />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="今日成功率"
                    value={statistics.todaySuccessRate}
                    suffix="%"
                    prefix={<CheckCircleOutlined />}
                    valueStyle={{ 
                      color: statistics.todaySuccessRate >= 95 ? '#52c41a' : 
                             statistics.todaySuccessRate >= 80 ? '#faad14' : '#f5222d' 
                    }}
                  />
                </Card>
              </Col>
            </Row>
          )}

          {/* 搜索和过滤 */}
          <Card style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Input
                  placeholder="搜索任务名称或类型"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </Col>
              <Col span={6}>
                <Select
                  style={{ width: '100%' }}
                  placeholder="选择状态"
                  value={statusFilter}
                  onChange={setStatusFilter}
                >
                  <Option value="all">全部状态</Option>
                  <Option value="SUCCESS">成功</Option>
                  <Option value="FAILED">失败</Option>
                  <Option value="RUNNING">运行中</Option>
                  <Option value="PENDING">等待中</Option>
                </Select>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 任务列表 */}
          <Card title="任务列表">
            <Table
              dataSource={filteredTasks}
              loading={loading}
              rowKey="taskName"
              columns={[
                {
                  title: '任务名称',
                  dataIndex: 'taskName',
                  key: 'taskName',
                  render: (text) => <Text strong>{text}</Text>
                },
                {
                  title: '任务类型',
                  dataIndex: 'taskType',
                  key: 'taskType',
                  render: (taskType) => getTaskTypeTag(taskType)
                },
                {
                  title: '最后状态',
                  dataIndex: 'lastStatus',
                  key: 'lastStatus',
                  render: (status) => getStatusTag(status)
                },
                {
                  title: '最后执行时间',
                  dataIndex: 'lastExecutionTime',
                  key: 'lastExecutionTime',
                  render: (time) => time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-'
                },
                {
                  title: '执行次数',
                  dataIndex: 'totalExecutions',
                  key: 'totalExecutions',
                  render: (total, record) => (
                    <Space direction="vertical" size="small">
                      <Text>总计: {total}</Text>
                      <Text type="success">成功: {record.successExecutions}</Text>
                      <Text type="danger">失败: {record.failedExecutions}</Text>
                    </Space>
                  )
                },
                {
                  title: '成功率',
                  dataIndex: 'successRate',
                  key: 'successRate',
                  render: (rate, record) => {
                    const successRate = calculateSuccessRate(record.successExecutions, record.totalExecutions);
                    return (
                      <Progress
                        percent={successRate}
                        size="small"
                        status={successRate >= 95 ? 'success' : successRate >= 80 ? 'normal' : 'exception'}
                      />
                    );
                  }
                },
                {
                  title: '平均执行时间',
                  dataIndex: 'averageExecutionTime',
                  key: 'averageExecutionTime',
                  render: (time) => formatDuration(time)
                },
                {
                  title: '操作',
                  key: 'actions',
                  render: (_, record) => (
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<PlayCircleOutlined />}
                        onClick={() => triggerTask(record.taskName)}
                        loading={loading}
                      >
                        触发
                      </Button>
                      <Button
                        size="small"
                        icon={<HistoryOutlined />}
                        onClick={() => {
                          setSelectedTask(record);
                          fetchTaskHistory(record.taskName);
                          setHistoryModalVisible(true);
                        }}
                      >
                        历史
                      </Button>
                      <Button
                        size="small"
                        icon={<InfoCircleOutlined />}
                        onClick={() => {
                          setSelectedTask(record);
                          setDetailModalVisible(true);
                        }}
                      >
                        详情
                      </Button>
                    </Space>
                  )
                }
              ]}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个任务`
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="运行中任务" key="running">
          <Card title="当前运行中的任务">
            {runningTasks.length === 0 ? (
              <Alert
                message="当前没有运行中的任务"
                type="info"
                showIcon
              />
            ) : (
              <Table
                dataSource={runningTasks}
                loading={loading}
                rowKey="taskName"
                columns={[
                  {
                    title: '任务名称',
                    dataIndex: 'taskName',
                    key: 'taskName',
                    render: (text) => <Text strong>{text}</Text>
                  },
                  {
                    title: '执行ID',
                    dataIndex: 'executionId',
                    key: 'executionId',
                    render: (id) => <Text code>{id}</Text>
                  },
                  {
                    title: '开始时间',
                    dataIndex: 'startTime',
                    key: 'startTime',
                    render: (time) => moment(time).format('YYYY-MM-DD HH:mm:ss')
                  },
                  {
                    title: '运行时长',
                    dataIndex: 'duration',
                    key: 'duration',
                    render: (duration) => (
                      <Badge
                        status="processing"
                        text={duration}
                      />
                    )
                  }
                ]}
                pagination={false}
              />
            )}
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <BarChartOutlined />
            统计分析
          </span>
        } key="statistics">
          <Spin spinning={analyticsLoading}>
            {/* 控制面板 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={24}>
                <Space>
                  <Button
                    type="primary"
                    icon={<LineChartOutlined />}
                    onClick={() => fetchAnalytics(trendDays)}
                  >
                    刷新数据
                  </Button>
                  <Button
                    icon={<BellOutlined />}
                    onClick={() => {
                      fetchActiveAlerts();
                      setAlertsDrawerVisible(true);
                    }}
                  >
                    活跃告警
                    {activeAlerts.length > 0 && <Badge count={activeAlerts.length} style={{ marginLeft: 8 }} />}
                  </Button>
                  <Button
                    icon={<ClearOutlined />}
                    onClick={() => clearCache()}
                  >
                    清除缓存
                  </Button>
                  <Select
                    value={trendDays}
                    onChange={(value) => {
                      setTrendDays(value);
                      fetchAnalytics(value);
                    }}
                    style={{ width: 120 }}
                  >
                    <Option value={1}>1天</Option>
                    <Option value={7}>7天</Option>
                    <Option value={30}>30天</Option>
                  </Select>
                </Space>
              </Col>
            </Row>

          {/* 全局统计卡片 */}
          {analytics && (
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总任务数"
                    value={analytics.globalStats.totalTasks}
                    prefix={<DashboardOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总执行次数"
                    value={analytics.globalStats.totalExecutions}
                    prefix={<PlayCircleOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="全局成功率"
                    value={Math.round(analytics.globalStats.globalSuccessRate * 100)}
                    suffix="%"
                    prefix={<CheckCircleOutlined />}
                    valueStyle={{
                      color: analytics.globalStats.globalSuccessRate >= 0.95 ? '#52c41a' :
                             analytics.globalStats.globalSuccessRate >= 0.8 ? '#faad14' : '#f5222d'
                    }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="失败次数"
                    value={analytics.globalStats.globalFailedCount}
                    prefix={<ExclamationCircleOutlined />}
                    valueStyle={{ color: '#f5222d' }}
                  />
                </Card>
              </Col>
            </Row>
          )}

          {/* 图表区域 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={12}>
              <Card title="执行趋势图" extra={
                <Tooltip title="显示最近几天的任务执行趋势">
                  <InfoCircleOutlined />
                </Tooltip>
              }>
                {renderTrendChart()}
              </Card>
            </Col>
            <Col span={12}>
              <Card title="平均执行时间对比">
                {analytics && analytics.taskSummaries && analytics.taskSummaries.length > 0 ?
                  renderPerformanceChart() :
                  <Empty description="暂无数据" />
                }
              </Card>
            </Col>
          </Row>

          {/* 任务成功率排行 */}
          <Row gutter={16}>
            <Col span={12}>
              <Card title="任务成功率排行" extra={
                <Tooltip title="显示各任务的成功率排名">
                  <InfoCircleOutlined />
                </Tooltip>
              }>
                {analytics && analytics.taskSummaries && analytics.taskSummaries.length > 0 ?
                  renderSuccessRatePie() :
                  <Empty description="暂无数据" />
                }
              </Card>
            </Col>
            <Col span={12}>
              <Card title="任务执行概览">
                {analytics && analytics.taskSummaries ? (
                  <Table
                    dataSource={analytics.taskSummaries.slice(0, 10)}
                    rowKey="taskName"
                    columns={[
                      {
                        title: '任务名称',
                        dataIndex: 'taskName',
                        key: 'taskName',
                        render: (name) => <Text strong>{name}</Text>
                      },
                      {
                        title: '执行次数',
                        dataIndex: 'totalExecutions',
                        key: 'totalExecutions'
                      },
                      {
                        title: '成功率',
                        dataIndex: 'successRate',
                        key: 'successRate',
                        render: (rate) => (
                          <Progress
                            percent={Math.round(rate * 100)}
                            size="small"
                            status={rate >= 0.95 ? 'success' : rate >= 0.8 ? 'normal' : 'exception'}
                          />
                        )
                      },
                      {
                        title: '操作',
                        key: 'action',
                        render: (_, record) => (
                          <Space>
                            <Button
                              size="small"
                              icon={<HeartOutlined />}
                              onClick={() => {
                                setSelectedTask(record);
                                fetchTaskHealth(record.taskName);
                                setHealthDrawerVisible(true);
                              }}
                            >
                              健康度
                            </Button>
                            <Button
                              size="small"
                              icon={<RiseOutlined />}
                              onClick={() => {
                                setSelectedTask(record);
                                fetchTaskTrend(record.taskName, trendDays);
                                fetchTaskPerformance(record.taskName, trendDays);
                                setAnalyticsDrawerVisible(true);
                              }}
                            >
                              趋势
                            </Button>
                          </Space>
                        )
                      }
                    ]}
                    pagination={false}
                    size="small"
                  />
                ) : (
                  <Empty description="暂无数据" />
                )}
              </Card>
            </Col>
          </Row>

          {/* 告警区域 */}
          {activeAlerts.length > 0 && (
            <Row gutter={16} style={{ marginTop: 24 }}>
              <Col span={24}>
                <Card title="活跃告警">
                  {activeAlerts.map((alert, index) => (
                    <Alert
                      key={`alert-${alert.taskName}-${alert.alertTime}-${index}`}
                      message={alert.message}
                      description={`任务: ${alert.taskName} | 类型: ${alert.type}`}
                      type={alert.severity === 'CRITICAL' ? 'error' : alert.severity === 'HIGH' ? 'warning' : 'info'}
                      showIcon
                      style={{ marginBottom: 16 }}
                      action={
                        <Button size="small">
                          处理
                        </Button>
                      }
                    />
                  ))}
                </Card>
              </Col>
            </Row>
          )}
        </Spin>
        </TabPane>
      </Tabs>

      {/* 任务详情模态框 */}
      <Modal
        title={`任务详情 - ${selectedTask?.taskName}`}
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedTask && (
          <Descriptions bordered column={2}>
            <Descriptions.Item label="任务名称" span={2}>
              {selectedTask.taskName}
            </Descriptions.Item>
            <Descriptions.Item label="任务类型">
              {getTaskTypeTag(selectedTask.taskType)}
            </Descriptions.Item>
            <Descriptions.Item label="最后状态">
              {getStatusTag(selectedTask.lastStatus)}
            </Descriptions.Item>
            <Descriptions.Item label="最后执行时间">
              {selectedTask.lastExecutionTime ?
                moment(selectedTask.lastExecutionTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="下次执行时间">
              {selectedTask.nextExecutionTime ?
                moment(selectedTask.nextExecutionTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="总执行次数">
              {selectedTask.totalExecutions}
            </Descriptions.Item>
            <Descriptions.Item label="成功次数">
              <Text type="success">{selectedTask.successExecutions}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="失败次数">
              <Text type="danger">{selectedTask.failedExecutions}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="成功率">
              <Progress
                percent={calculateSuccessRate(selectedTask.successExecutions, selectedTask.totalExecutions)}
                size="small"
              />
            </Descriptions.Item>
            <Descriptions.Item label="平均执行时间">
              {formatDuration(selectedTask.averageExecutionTime)}
            </Descriptions.Item>
            <Descriptions.Item label="最大执行时间">
              {formatDuration(selectedTask.maxExecutionTime)}
            </Descriptions.Item>
            <Descriptions.Item label="最小执行时间">
              {formatDuration(selectedTask.minExecutionTime)}
            </Descriptions.Item>
            <Descriptions.Item label="最后错误信息" span={2}>
              {selectedTask.lastErrorMessage ? (
                <Text type="danger">{selectedTask.lastErrorMessage}</Text>
              ) : (
                <Text type="secondary">无</Text>
              )}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* 任务历史模态框 */}
      <Modal
        title={`执行历史 - ${selectedTask?.taskName}`}
        visible={historyModalVisible}
        onCancel={() => setHistoryModalVisible(false)}
        footer={null}
        width={1200}
      >
        <div style={{ marginBottom: 16 }}>
          <RangePicker
            value={dateRange}
            onChange={(dates) => {
              setDateRange(dates);
              if (selectedTask) {
                fetchTaskHistory(selectedTask.taskName);
              }
            }}
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            placeholder={['开始时间', '结束时间']}
          />
        </div>

        <Table
          dataSource={taskHistory}
          loading={loading}
          rowKey="id"
          columns={[
            {
              title: '执行ID',
              dataIndex: 'id',
              key: 'id',
              render: (id) => <Text code>{id}</Text>
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              render: (status) => getStatusTag(status)
            },
            {
              title: '开始时间',
              dataIndex: 'executionTime',
              key: 'executionTime',
              render: (time) => moment(time).format('YYYY-MM-DD HH:mm:ss')
            },
            {
              title: '完成时间',
              dataIndex: 'completionTime',
              key: 'completionTime',
              render: (time) => time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-'
            },
            {
              title: '执行时长',
              dataIndex: 'durationMs',
              key: 'durationMs',
              render: (duration) => formatDuration(duration)
            },
            {
              title: '重试次数',
              dataIndex: 'retryCount',
              key: 'retryCount',
              render: (count) => count > 0 ? <Badge count={count} /> : '-'
            },
            {
              title: '处理数量',
              dataIndex: 'processedCount',
              key: 'processedCount'
            },
            {
              title: '错误信息',
              dataIndex: 'errorMessage',
              key: 'errorMessage',
              render: (error) => error ? (
                <Tooltip title={error}>
                  <Text type="danger" ellipsis style={{ maxWidth: 200 }}>
                    {error}
                  </Text>
                </Tooltip>
              ) : '-'
            }
          ]}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Modal>

      {/* 分析详情抽屉 */}
      <Drawer
        title={selectedTask ? `${selectedTask.taskName} - 详细分析` : "全局分析"}
        width={800}
        visible={analyticsDrawerVisible}
        onClose={() => setAnalyticsDrawerVisible(false)}
      >
        <Spin spinning={analyticsLoading}>
          {selectedTask ? (
            <Tabs defaultActiveKey="trend">
              <TabPane tab="执行趋势" key="trend">
                <Card title="执行趋势图" style={{ marginBottom: 16 }}>
                  {renderTrendChart()}
                </Card>
              </TabPane>
              <TabPane tab="性能分析" key="performance">
                <Card title="性能指标" style={{ marginBottom: 16 }}>
                  {renderPerformanceChart()}
                </Card>
                {taskPerformance && taskPerformance.performance && (
                  <Card title="详细指标">
                    <Row gutter={16}>
                      <Col span={12}>
                        <Statistic
                          title="平均执行时间"
                          value={taskPerformance.performance.avgDuration}
                          suffix="ms"
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="P95执行时间"
                          value={taskPerformance.performance.p95Duration}
                          suffix="ms"
                        />
                      </Col>
                    </Row>
                  </Card>
                )}
              </TabPane>
            </Tabs>
          ) : (
            analytics && (
              <div>
                <Card title="全局成功率分布" style={{ marginBottom: 16 }}>
                  {renderSuccessRatePie()}
                </Card>
                <Card title="报告时间">
                  <Text>{analytics.reportTime}</Text>
                </Card>
              </div>
            )
          )}
        </Spin>
      </Drawer>

      {/* 健康状态抽屉 */}
      <Drawer
        title={selectedTask ? `${selectedTask.taskName} - 健康状态` : "健康状态"}
        width={600}
        visible={healthDrawerVisible}
        onClose={() => setHealthDrawerVisible(false)}
      >
        {taskHealth && (
          <div>
            <Card title="健康度评分" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="健康度评分"
                    value={taskHealth.score}
                    suffix="/100"
                    valueStyle={{
                      color: taskHealth.score >= 90 ? '#52c41a' :
                             taskHealth.score >= 70 ? '#faad14' : '#f5222d'
                    }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="健康等级"
                    value={taskHealth.level}
                    valueStyle={{
                      color: taskHealth.level === 'EXCELLENT' ? '#52c41a' :
                             taskHealth.level === 'GOOD' ? '#1890ff' :
                             taskHealth.level === 'FAIR' ? '#faad14' : '#f5222d'
                    }}
                  />
                </Col>
              </Row>
            </Card>
            <Card title="详细指标">
              <Descriptions column={2}>
                <Descriptions.Item label="成功率">
                  {Math.round(taskHealth.successRate * 100)}%
                </Descriptions.Item>
                <Descriptions.Item label="总执行次数">
                  {taskHealth.totalExecutions}
                </Descriptions.Item>
                <Descriptions.Item label="平均执行时间">
                  {taskHealth.avgDurationMs}ms
                </Descriptions.Item>
                <Descriptions.Item label="最后执行状态">
                  {getStatusTag(taskHealth.lastExecutionStatus)}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </div>
        )}
      </Drawer>

      {/* 告警抽屉 */}
      <Drawer
        title="活跃告警"
        width={600}
        visible={alertsDrawerVisible}
        onClose={() => setAlertsDrawerVisible(false)}
      >
        {activeAlerts.length > 0 ? (
          <div>
            {activeAlerts.map((alert, index) => (
              <Alert
                key={`drawer-alert-${alert.taskName}-${alert.alertTime}-${index}`}
                message={alert.message}
                description={`任务: ${alert.taskName} | 类型: ${alert.type}`}
                type={alert.severity === 'CRITICAL' ? 'error' : alert.severity === 'HIGH' ? 'warning' : 'info'}
                showIcon
                style={{ marginBottom: 16 }}
                action={
                  <Button size="small" onClick={() => {
                    // 这里可以添加解决告警的逻辑
                    message.info('告警处理功能开发中...');
                  }}>
                    处理
                  </Button>
                }
              />
            ))}
          </div>
        ) : (
          <Empty description="暂无活跃告警" />
        )}
      </Drawer>
    </div>
  );
};

export default ScheduledTaskManagement;
