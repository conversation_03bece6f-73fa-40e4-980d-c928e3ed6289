import React, { useState } from 'react';
import { <PERSON>ton, Card, Space, Typography, Divider } from 'antd';
import api from '../services/api';

const { Title, Paragraph, Text } = Typography;

const SessionExpireTest = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState({});

  // 模拟403错误（session过期）
  const test403Error = async () => {
    setLoading(true);
    try {
      console.log('发送403测试请求...');
      const response = await api.get('/admin/test-403');
      console.log('403测试请求成功:', response);
      setResults(prev => ({
        ...prev,
        test403: '请求成功，但应该返回403错误: ' + JSON.stringify(response.data)
      }));
    } catch (error) {
      console.log('403错误已触发:', error);
      console.log('错误状态码:', error.response?.status);
      console.log('错误数据:', error.response?.data);
      setResults(prev => ({
        ...prev,
        test403: `403错误已触发 (状态码: ${error.response?.status})，应该看到"登录已过期"的确认对话框`
      }));
    } finally {
      setLoading(false);
    }
  };

  // 模拟401错误（未授权）
  const test401Error = async () => {
    setLoading(true);
    try {
      console.log('发送401测试请求...');
      const response = await api.get('/admin/test-401');
      console.log('401测试请求成功:', response);
      setResults(prev => ({
        ...prev,
        test401: '请求成功，但应该返回401错误: ' + JSON.stringify(response.data)
      }));
    } catch (error) {
      console.log('401错误已触发:', error);
      console.log('错误状态码:', error.response?.status);
      console.log('错误数据:', error.response?.data);
      setResults(prev => ({
        ...prev,
        test401: `401错误已触发 (状态码: ${error.response?.status})，应该弹出"认证失败"的确认对话框`
      }));
    } finally {
      setLoading(false);
    }
  };

  // 模拟500错误（服务器错误）
  const test500Error = async () => {
    setLoading(true);
    try {
      // 发送一个会返回500的请求
      await api.get('/admin/test-500');
    } catch (error) {
      console.log('500错误已触发:', error);
      setResults(prev => ({
        ...prev,
        test500: '500错误已触发，应该显示普通的错误消息'
      }));
    } finally {
      setLoading(false);
    }
  };

  // 测试正常请求
  const testNormalRequest = async () => {
    setLoading(true);
    try {
      const response = await api.get('/auth/test');
      setResults(prev => ({
        ...prev,
        normal: '正常请求成功: ' + JSON.stringify(response.data)
      }));
    } catch (error) {
      console.log('正常请求失败:', error);
      setResults(prev => ({
        ...prev,
        normal: '正常请求失败: ' + error.message
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>Session过期处理测试</Title>
        
        <Paragraph>
          这个页面用于测试前端session过期时的处理机制。
          当点击下面的按钮时，会模拟不同的HTTP错误状态码，验证前端的错误处理逻辑。
        </Paragraph>

        <Divider />

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Card size="small" title="模拟403错误（Session过期）">
            <Paragraph>
              点击此按钮会触发403错误，应该弹出"登录已过期"的确认对话框。
            </Paragraph>
            <Button 
              type="primary" 
              onClick={test403Error} 
              loading={loading}
              style={{ marginBottom: '8px' }}
            >
              模拟403错误
            </Button>
            {results.test403 && (
              <div style={{ padding: '8px', backgroundColor: '#f6f6f6', borderRadius: '4px' }}>
                <Text>{results.test403}</Text>
              </div>
            )}
          </Card>

          <Card size="small" title="模拟401错误（认证失败）">
            <Paragraph>
              点击此按钮会触发401错误，应该弹出"认证失败"的确认对话框。
            </Paragraph>
            <Button 
              type="primary" 
              onClick={test401Error} 
              loading={loading}
              style={{ marginBottom: '8px' }}
            >
              模拟401错误
            </Button>
            {results.test401 && (
              <div style={{ padding: '8px', backgroundColor: '#f6f6f6', borderRadius: '4px' }}>
                <Text>{results.test401}</Text>
              </div>
            )}
          </Card>

          <Card size="small" title="模拟500错误（服务器错误）">
            <Paragraph>
              点击此按钮会触发500错误，应该显示普通的错误消息。
            </Paragraph>
            <Button 
              type="primary" 
              onClick={test500Error} 
              loading={loading}
              style={{ marginBottom: '8px' }}
            >
              模拟500错误
            </Button>
            {results.test500 && (
              <div style={{ padding: '8px', backgroundColor: '#f6f6f6', borderRadius: '4px' }}>
                <Text>{results.test500}</Text>
              </div>
            )}
          </Card>

          <Card size="small" title="测试正常请求">
            <Paragraph>
              点击此按钮会发送一个正常的请求，验证基本功能是否正常。
            </Paragraph>
            <Button 
              type="default" 
              onClick={testNormalRequest} 
              loading={loading}
              style={{ marginBottom: '8px' }}
            >
              测试正常请求
            </Button>
            {results.normal && (
              <div style={{ padding: '8px', backgroundColor: '#f6f6f6', borderRadius: '4px' }}>
                <Text>{results.normal}</Text>
              </div>
            )}
          </Card>
        </Space>

        <Divider />

        <Paragraph type="secondary">
          <strong>注意：</strong>
          <br />
          • 403错误应该弹出确认对话框，提示"登录已过期，请重新登录"
          <br />
          • 401错误应该弹出确认对话框，提示"认证失败，您的登录认证已失效，请重新登录"
          <br />
          • 500错误应该显示普通的错误消息
          <br />
          • 正常请求应该能够成功执行
        </Paragraph>
      </Card>
    </div>
  );
};

export default SessionExpireTest;
