import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Table, Button, Modal, Form, Input, Select, Space, Tag, message, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined, UnorderedListOutlined } from '@ant-design/icons';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { userApi, pmiApi } from '../services/api';
import dayjs from 'dayjs';

const { Option } = Select;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const UserList = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [highlightUserId, setHighlightUserId] = useState(null);
  const [filterUserId, setFilterUserId] = useState(null);
  const [allUsers, setAllUsers] = useState([]); // 存储所有用户数据
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [userPmiStats, setUserPmiStats] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [form] = Form.useForm();
  const isInitializedRef = useRef(false);

  // 加载用户PMI统计信息
  const loadUserPmiStats = async (userList) => {
    try {
      const stats = {};
      for (const user of userList) {
        try {
          const response = await pmiApi.getUserPmiStats(user.id);
          // API返回格式：{"success":true,"data":{"totalCount":1}}
          // 正确的数据路径是 response.data.data.totalCount
          const pmiCount = response.data?.data?.totalCount || 0;
          stats[user.id] = pmiCount;
        } catch (error) {
          console.error(`获取用户 ${user.id} PMI统计失败:`, error);
          stats[user.id] = 0;
        }
      }
      setUserPmiStats(stats);
    } catch (error) {
      console.error('加载PMI统计失败:', error);
    }
  };

  // 统一的用户加载函数，避免重复代码
  const loadUsers = useCallback(async (filterUserIdParam = null, page = 1, pageSize = 20) => {
    try {
      console.log('loadUsers 被调用，filterUserIdParam:', filterUserIdParam, 'page:', page, 'pageSize:', pageSize);
      setLoading(true);

      let userData = [];
      let totalElements = 0;

      // 如果有筛选条件，先获取所有数据进行筛选
      if (filterUserIdParam !== null && filterUserIdParam !== undefined) {
        // 获取所有用户数据进行筛选
        const response = await userApi.getUsers({ page: 0, size: 1000 });
        const allUserData = response.data.content || response.data || [];
        setAllUsers(allUserData);

        const filteredUsers = allUserData.filter(user => user.id === filterUserIdParam);
        userData = filteredUsers;
        totalElements = filteredUsers.length;

        if (filteredUsers.length > 0) {
          setHighlightUserId(filterUserIdParam);
          // 5秒后取消高亮
          setTimeout(() => {
            setHighlightUserId(null);
          }, 5000);
        }
      } else {
        // 没有筛选条件时使用分页加载
        const response = await userApi.getUsers({ page: page - 1, size: pageSize });
        userData = response.data.content || response.data || [];
        totalElements = response.data.totalElements || userData.length;
        setAllUsers(userData);
        setHighlightUserId(null);
      }

      setUsers(userData);
      setPagination({
        current: page,
        pageSize: pageSize,
        total: totalElements,
      });

      // 加载用户PMI统计信息
      loadUserPmiStats(userData);
    } catch (error) {
      console.error('加载用户列表失败:', error);
      message.error('加载用户列表失败');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    // 防止重复初始化
    if (isInitializedRef.current) {
      return;
    }

    // 立即设置为已初始化，防止并发调用
    isInitializedRef.current = true;

    // 检查URL路径参数，如果有userId参数，则设置筛选条件
    // 支持两种格式：/users?userId=1 和 /users/1
    const searchParams = new URLSearchParams(location.search);
    const queryUserId = searchParams.get('userId');

    // 优先使用路径参数（React Router params）
    const pathUserId = params.userId;

    let userId = null;
    if (pathUserId) {
      // 优先使用路径参数
      userId = pathUserId;
    } else if (queryUserId) {
      // 兼容查询参数格式
      userId = queryUserId;
    }

    // 使用统一的加载函数
    const userIdNum = userId ? parseInt(userId) : null;
    setFilterUserId(userIdNum);
    loadUsers(userIdNum, 1, 20);
  }, [location.search, params.userId]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleSearch = async () => {
    if (!searchText.trim()) {
      loadUsers(1, pagination.pageSize);
      return;
    }

    try {
      setLoading(true);
      const response = await userApi.searchUsers(searchText);
      console.log('搜索用户响应:', response);
      const searchResults = response.data || [];
      setUsers(searchResults);

      // 加载搜索结果的PMI统计信息
      if (searchResults.length > 0) {
        await loadUserPmiStats(searchResults);
      }
    } catch (error) {
      console.error('搜索用户失败:', error);
      message.error('搜索用户失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingUser(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (user) => {
    setEditingUser(user);
    setModalVisible(true);
    form.setFieldsValue(user);
  };

  // 处理PMI管理按钮点击
  const handlePmiManagement = (record) => {
    const userId = record.id;
    const pmiCount = userPmiStats[userId] || 0;

    if (pmiCount > 0) {
      // 用户有PMI，跳转到PMI列表页面
      navigate(`/pmi-management/user/${userId}`);
    } else {
      // 用户没有PMI，跳转到PMI管理页面并打开创建弹窗
      navigate(`/pmi-management/user/${userId}/create`);
    }
  };

  const handleDelete = async (id) => {
    try {
      await userApi.deleteUser(id);
      message.success('删除成功');
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('删除用户失败:', error);
    }
  };

  const handleStatusChange = async (id, status) => {
    try {
      await userApi.updateUserStatus(id, status);
      message.success('状态更新成功');
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('更新状态失败:', error);
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingUser) {
        await userApi.updateUser(editingUser.id, values);
        message.success('更新成功');
      } else {
        await userApi.createUser(values);
        message.success('创建成功');
      }
      setModalVisible(false);
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('保存用户失败:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: isMobileView ? 50 : 70,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px', fontWeight: 'bold', color: '#1890ff' }}>
          {text}
        </span>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: isMobileView ? 80 : 120,
      ellipsis: true,
      render: (text, record) => (
        <a
          href={`/users/${record.id}`}
          style={{
            fontSize: isMobileView ? '11px' : '14px',
            color: '#1890ff',
            textDecoration: 'none'
          }}
          onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
          onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
        >
          {text}
        </a>
      ),
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      key: 'fullName',
      width: isMobileView ? 80 : 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: isMobileView ? 120 : 180,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: isMobileView ? 80 : 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
      width: isMobileView ? 100 : 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: isMobileView ? 80 : 120,
      render: (status, record) => {
        const statusConfig = {
          ACTIVE: { color: 'green', text: isMobileView ? '活跃' : '活跃' },
          INACTIVE: { color: 'default', text: isMobileView ? '非活跃' : '非活跃' },
          SUSPENDED: { color: 'red', text: isMobileView ? '暂停' : '暂停' },
        };
        const config = statusConfig[status] || { color: 'default', text: status };
        return (
          <Select
            value={status}
            size="small"
            style={{ width: isMobileView ? 60 : 80, fontSize: isMobileView ? '11px' : '14px' }}
            onChange={(value) => handleStatusChange(record.id, value)}
          >
            <Option value="ACTIVE">
              <Tag color="green" style={{ fontSize: isMobileView ? '9px' : '12px' }}>活跃</Tag>
            </Option>
            <Option value="INACTIVE">
              <Tag color="default" style={{ fontSize: isMobileView ? '9px' : '12px' }}>非活跃</Tag>
            </Option>
            <Option value="SUSPENDED">
              <Tag color="red" style={{ fontSize: isMobileView ? '9px' : '12px' }}>暂停</Tag>
            </Option>
          </Select>
        );
      },
    },
    {
      title: isMobileView ? '创建' : '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: isMobileView ? 80 : 150,
      render: (text) => (
        <span style={{ fontSize: isMobileView ? '10px' : '14px' }}>
          {dayjs(text).format(isMobileView ? 'MM-DD HH:mm' : 'YYYY-MM-DD HH:mm')}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 80 : 150,
      fixed: isMobileView ? 'right' : false,
      className: isMobileView ? 'action-column' : '',
      render: (_, record) => (
        <Space size={isMobileView ? 'small' : 'middle'} className={isMobileView ? 'mobile-action-buttons' : ''}>
          <Button
            type="link"
            icon={isMobileView ? <EditOutlined /> : <EditOutlined />}
            onClick={() => handleEdit(record)}
            size={isMobileView ? 'small' : 'middle'}
            style={{ fontSize: isMobileView ? '10px' : '14px', padding: isMobileView ? '2px 4px' : '4px 15px' }}
          >
            {!isMobileView && '编辑'}
          </Button>
          <Button
            type="link"
            icon={(userPmiStats[record.id] || 0) > 0 ? <UnorderedListOutlined /> : <PlusOutlined />}
            onClick={() => handlePmiManagement(record)}
            size={isMobileView ? 'small' : 'middle'}
            style={{
              fontSize: isMobileView ? '10px' : '14px',
              padding: isMobileView ? '2px 4px' : '4px 15px',
              color: (userPmiStats[record.id] || 0) > 0 ? '#1890ff' : '#52c41a'
            }}
            title={(userPmiStats[record.id] || 0) > 0 ? "查看PMI列表" : "创建新PMI"}
          >
            {!isMobileView && ((userPmiStats[record.id] || 0) > 0 ? 'PMI列表' : '创建PMI')}
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={isMobileView ? <DeleteOutlined /> : <DeleteOutlined />}
              size={isMobileView ? 'small' : 'middle'}
              style={{ fontSize: isMobileView ? '10px' : '14px', padding: isMobileView ? '2px 4px' : '4px 15px' }}
            >
              {!isMobileView && '删除'}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 清除筛选
  const clearFilter = () => {
    navigate('/users');
  };

  return (
    <div style={{ padding: isMobileView ? '12px' : '24px' }}>
      {/* 移动端滚动提示 */}
      {isMobileView && (
        <div className="mobile-scroll-hint" style={{ marginBottom: '16px' }}>
          👈 表格可左右滑动查看所有列信息（ID、用户名(可点击)、姓名、邮箱、部门、电话、状态、创建时间、操作）
        </div>
      )}

      <div style={{
        marginBottom: isMobileView ? 12 : 16,
        display: 'flex',
        flexDirection: isMobileView ? 'column' : 'row',
        justifyContent: 'space-between',
        gap: isMobileView ? '12px' : '0'
      }}>
        <div>
          <h1 style={{ fontSize: isMobileView ? '20px' : '24px', marginBottom: isMobileView ? '8px' : '16px' }}>
            用户管理
          </h1>
          {filterUserId && (
            <div style={{ marginTop: 8 }}>
              <span style={{ color: '#1890ff', marginRight: 8, fontSize: isMobileView ? '12px' : '14px' }}>
                正在显示用户ID为 {filterUserId} 的记录
              </span>
              <Button
                type="link"
                size="small"
                onClick={clearFilter}
                style={{ padding: 0, fontSize: isMobileView ? '12px' : '14px' }}
              >
                显示所有用户
              </Button>
            </div>
          )}
        </div>
        <Space direction={isMobileView ? 'vertical' : 'horizontal'} style={{ width: isMobileView ? '100%' : 'auto' }}>
          <Input.Search
            placeholder={isMobileView ? "搜索用户" : "搜索用户名或姓名"}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={handleSearch}
            style={{ width: isMobileView ? '100%' : 200 }}
            size={isMobileView ? 'middle' : 'middle'}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
            style={{ width: isMobileView ? '100%' : 'auto' }}
            size={isMobileView ? 'middle' : 'middle'}
          >
            新建用户
          </Button>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={users}
        rowKey="id"
        loading={loading}
        size={isMobileView ? 'small' : 'middle'}
        scroll={{ x: isMobileView ? 800 : 'auto' }}
        rowClassName={(record) => {
          if (highlightUserId && record.id === highlightUserId) {
            return 'highlighted-row';
          }
          return '';
        }}
        onRow={(record) => ({
          id: `user-row-${record.id}`, // 添加唯一ID便于查找
        })}
        pagination={{
          ...pagination,
          showSizeChanger: !isMobileView,
          showQuickJumper: !isMobileView,
          showTotal: (total) => `共 ${total} 条记录`,
          simple: isMobileView,
          size: isMobileView ? 'small' : 'default',
          onChange: (page, pageSize) => {
            loadUsers(page, pageSize);
          },
          onShowSizeChange: (_, size) => {
            loadUsers(1, size);
          },
        }}
      />

      <style>{`
        .highlighted-row {
          background-color: #fff7e6 !important;
          border: 2px solid #ffa940 !important;
        }
        .highlighted-row:hover {
          background-color: #fff7e6 !important;
        }
      `}</style>

      <Modal
        title={editingUser ? '编辑用户' : '新建用户'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="fullName"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="department"
            label="部门"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="phone"
            label="电话"
          >
            <Input />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingUser ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserList;
