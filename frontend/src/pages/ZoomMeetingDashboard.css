/* Zoom会议看板移动端样式 */

/* 移动端隐藏列 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  /* 移动端表格样式优化 */
  .ant-table-thead > tr > th {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
    font-size: 11px !important;
  }
  
  /* 移动端按钮样式 */
  .ant-btn-sm {
    padding: 0 8px !important;
    font-size: 11px !important;
    height: 24px !important;
  }
  
  /* 移动端标签样式 */
  .ant-tag {
    margin: 0 2px !important;
    padding: 0 4px !important;
    font-size: 10px !important;
    line-height: 16px !important;
  }
  
  /* 移动端统计卡片 */
  .ant-statistic-title {
    font-size: 12px !important;
  }
  
  .ant-statistic-content {
    font-size: 16px !important;
  }
  
  /* 移动端搜索框 */
  .ant-input {
    font-size: 14px !important;
  }
  
  /* 移动端选择框 */
  .ant-select {
    font-size: 14px !important;
  }
  
  /* 移动端标签页 */
  .ant-tabs-tab {
    padding: 8px 12px !important;
    font-size: 14px !important;
  }
  
  /* 移动端卡片 */
  .ant-card-body {
    padding: 12px !important;
  }
  
  /* 移动端分页器 */
  .ant-pagination-simple .ant-pagination-simple-pager {
    font-size: 12px !important;
  }
  
  /* 移动端工具提示 */
  .ant-tooltip-inner {
    font-size: 11px !important;
  }
}

/* PC端操作栏固定 */
@media (min-width: 769px) {
  .ant-table-thead > tr > th.ant-table-cell-fix-right,
  .ant-table-tbody > tr > td.ant-table-cell-fix-right {
    background: #fff !important;
  }
  
  /* PC端操作栏阴影 */
  .ant-table-thead > tr > th.ant-table-cell-fix-right::before,
  .ant-table-tbody > tr > td.ant-table-cell-fix-right::before {
    box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15) !important;
  }
}

/* 通用样式 */
.zoom-meeting-dashboard {
  /* 确保表格在小屏幕上可以滚动 */
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  /* 会议状态标签颜色 */
  .meeting-status-pending {
    background-color: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
  }
  
  .meeting-status-using {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
  
  .meeting-status-ended {
    background-color: #fff2f0;
    border-color: #ffccc7;
    color: #ff4d4f;
  }
  
  .meeting-status-settled {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }
}

/* 响应式间距 */
@media (max-width: 576px) {
  .zoom-meeting-dashboard {
    padding: 8px !important;
  }
  
  .ant-row {
    margin-left: -4px !important;
    margin-right: -4px !important;
  }
  
  .ant-col {
    padding-left: 4px !important;
    padding-right: 4px !important;
  }
}

/* 中等屏幕适配 */
@media (min-width: 577px) and (max-width: 768px) {
  .zoom-meeting-dashboard {
    padding: 16px !important;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .zoom-meeting-dashboard {
    max-width: 1400px;
    margin: 0 auto;
  }
}
