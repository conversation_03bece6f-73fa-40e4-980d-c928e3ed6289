import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Select,
  Card,
  Row,
  Col,
  Statistic,
  Tag,
  message,
  Popconfirm,
  Tooltip,
  Modal,
  Form,
  Divider
} from 'antd';
import {
  SearchOutlined,
  SyncOutlined,
  DeleteOutlined,
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  GlobalOutlined,
  ReloadOutlined,
  KeyOutlined,
  UsergroupAddOutlined,
  PlusOutlined,
  LinkOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  RedoOutlined,
  ThunderboltOutlined,
  CopyOutlined,
  UnorderedListOutlined
} from '@ant-design/icons';
import { zoomUserApi, zoomAuthApi, userApi, pmiApi, zoomUserPmiApi } from '../services/api';
import { useNavigate, useParams } from 'react-router-dom';

const { Option } = Select;
const { Search } = Input;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

// 辅助函数：安全获取统计数据
const getStatValue = (stats, key) => {
  if (!stats || !Array.isArray(stats)) return 0;
  const found = stats.find(s => s[0] === key);
  return found ? found[1] : 0;
};

const ZoomUserManagement = () => {
  const navigate = useNavigate();
  const { userId, zoomUserId } = useParams();
  // 如果路径是 /zoom-users/:id 格式，则将其视为zoomUserId
  const actualZoomUserId = zoomUserId || (userId && !window.location.pathname.includes('/user/') ? userId : null);
  const actualUserId = window.location.pathname.includes('/user/') ? userId : null;
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [searchType, setSearchType] = useState('email');
  const [selectedZoomAuth, setSelectedZoomAuth] = useState('all'); // 默认显示所有
  const [zoomAuthList, setZoomAuthList] = useState([]);
  const [dashboardStats, setDashboardStats] = useState({});
  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [userPmiStats, setUserPmiStats] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 创建用户弹窗相关状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();
  const [searchUsers, setSearchUsers] = useState([]);
  const [originalSearchUsers, setOriginalSearchUsers] = useState([]);
  const [userSearchLoading, setUserSearchLoading] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState(null);
  const [createLoading, setCreateLoading] = useState(false);

  // 编辑用户弹窗相关状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editForm] = Form.useForm();
  const [editingUser, setEditingUser] = useState(null);
  const [editSearchUsers, setEditSearchUsers] = useState([]);
  const [editOriginalSearchUsers, setEditOriginalSearchUsers] = useState([]);
  const [editUserSearchLoading, setEditUserSearchLoading] = useState(false);
  const [editSearchTimeout, setEditSearchTimeout] = useState(null);
  const [editLoading, setEditLoading] = useState(false);

  // PMI编辑相关状态
  const [pmiEditModalVisible, setPmiEditModalVisible] = useState(false);
  const [pmiEditForm] = Form.useForm();
  const [pmiEditingUser, setPmiEditingUser] = useState(null);
  const [pmiEditLoading, setPmiEditLoading] = useState(false);
  const [generatingPmi, setGeneratingPmi] = useState(false);

  // PMI测试相关状态
  const [pmiTestModalVisible, setPmiTestModalVisible] = useState(false);
  const [pmiTestForm] = Form.useForm();
  const [pmiTestingUser, setPmiTestingUser] = useState(null);
  const [pmiTestLoading, setPmiTestLoading] = useState(false);

  // 加载ZoomAuth列表
  const loadZoomAuthList = async () => {
    try {
      const response = await zoomAuthApi.getAllZoomAuth({ page: 0, size: 100 });
      setZoomAuthList(response.data.content || []);
    } catch (error) {
      console.error('Error loading zoom auth list:', error);
    }
  };

  // 统一的用户列表加载函数（避免重复代码）
  const loadUsersForModal = async () => {
    try {
      const response = await userApi.getUsers({ page: 0, size: 100 });
      const userList = response.data.content || [];
      return userList;
    } catch (error) {
      console.error('Error loading users:', error);
      return [];
    }
  };

  // 加载用户列表（用于创建弹窗）
  const loadUsersForCreate = async () => {
    const userList = await loadUsersForModal();
    setSearchUsers(userList);
    setOriginalSearchUsers(userList);
  };

  // 用户搜索处理
  const handleUserSearch = (searchText) => {
    // 清除之前的搜索定时器
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (!searchText || searchText.trim() === '') {
      // 清空搜索时立即恢复原始列表
      setSearchUsers(originalSearchUsers);
      setUserSearchLoading(false);
      return;
    }

    if (searchText.trim().length < 2) {
      setUserSearchLoading(false);
      return;
    }

    setUserSearchLoading(true);

    // 设置新的搜索定时器
    const newTimeout = setTimeout(async () => {
      try {
        const response = await userApi.searchUsers(searchText.trim());
        console.log('搜索用户响应:', response);
        const searchResults = response.data || [];

        // 直接使用搜索结果作为候选项
        setSearchUsers(searchResults);
      } catch (error) {
        console.error('搜索用户失败:', error);
        // 搜索失败时恢复原始列表
        setSearchUsers(originalSearchUsers);
      } finally {
        setUserSearchLoading(false);
      }
    }, 500);

    setSearchTimeout(newTimeout);
  };

  // 打开创建弹窗
  const handleCreate = () => {
    setCreateModalVisible(true);
    createForm.resetFields();
    loadUsersForCreate();
  };

  // 创建Zoom用户
  const handleCreateSubmit = async (values) => {
    try {
      setCreateLoading(true);
      await zoomUserApi.createZoomUser(values);
      message.success('创建Zoom用户成功');
      setCreateModalVisible(false);
      createForm.resetFields();
      loadUsers(pagination.current, pagination.pageSize);
      loadDashboardStats();
    } catch (error) {
      message.error('创建Zoom用户失败');
      console.error('Error creating zoom user:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  // 编辑用户搜索处理
  const handleEditUserSearch = (searchText) => {
    // 清除之前的搜索定时器
    if (editSearchTimeout) {
      clearTimeout(editSearchTimeout);
    }

    if (!searchText || searchText.trim() === '') {
      // 清空搜索时立即恢复原始列表
      setEditSearchUsers(editOriginalSearchUsers);
      setEditUserSearchLoading(false);
      return;
    }

    if (searchText.trim().length < 2) {
      setEditUserSearchLoading(false);
      return;
    }

    setEditUserSearchLoading(true);

    // 设置新的搜索定时器
    const newTimeout = setTimeout(async () => {
      try {
        const response = await userApi.searchUsers(searchText.trim());
        console.log('搜索用户响应:', response);
        const searchResults = response.data || [];

        // 直接使用搜索结果作为候选项
        setEditSearchUsers(searchResults);
      } catch (error) {
        console.error('搜索用户失败:', error);
        // 搜索失败时恢复原始列表
        setEditSearchUsers(editOriginalSearchUsers);
      } finally {
        setEditUserSearchLoading(false);
      }
    }, 500);

    setEditSearchTimeout(newTimeout);
  };

  // 打开编辑弹窗
  const handleEdit = (record) => {
    setEditingUser(record);
    setEditModalVisible(true);

    // 设置表单初始值
    editForm.setFieldsValue({
      userId: record.user?.id || null,
      email: record.email,
      firstName: record.firstName,
      lastName: record.lastName,
      displayName: record.displayName,
      userType: record.userType,
      status: record.status,
      department: record.department,
      jobTitle: record.jobTitle,
      phoneNumber: record.phoneNumber,
      timezone: record.timezone,
      language: record.language,
      accountUsage: record.accountUsage,
    });

    loadUsersForEdit();
  };

  // 加载用户列表（用于编辑弹窗）
  const loadUsersForEdit = async () => {
    const userList = await loadUsersForModal();
    setEditSearchUsers(userList);
    setEditOriginalSearchUsers(userList);
  };

  // 编辑Zoom用户
  const handleEditSubmit = async (values) => {
    try {
      setEditLoading(true);
      await zoomUserApi.updateZoomUser(editingUser.id, values);
      message.success('更新Zoom用户成功');
      setEditModalVisible(false);
      editForm.resetFields();
      setEditingUser(null);
      loadUsers(pagination.current, pagination.pageSize);
      loadDashboardStats();
    } catch (error) {
      message.error('更新Zoom用户失败');
      console.error('Error updating zoom user:', error);
    } finally {
      setEditLoading(false);
    }
  };

  // 加载用户列表
  const loadUsers = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      const params = {
        page: page - 1,
        size: pageSize,
      };

      let response;

      // 如果有zoomUserId参数，直接按ZoomUser ID过滤
      if (actualZoomUserId) {
        response = await zoomUserApi.getZoomUserByZoomUserId(actualZoomUserId, params);
      } else if (actualUserId) {
        // 如果有userId参数，直接按用户ID过滤
        response = await zoomUserApi.getZoomUsersByUserId(actualUserId, params);
      } else if (selectedZoomAuth === 'all') {
        // 显示所有主账号下的用户
        if (searchText) {
          // 全局搜索
          if (searchType === 'email') {
            response = await zoomUserApi.globalSearchZoomUsersByEmail(searchText, params);
          } else {
            response = await zoomUserApi.globalSearchZoomUsersByName(searchText, params);
          }
        } else {
          response = await zoomUserApi.getAllZoomUsers(params);
        }
      } else {
        // 显示特定主账号下的用户
        if (searchText) {
          if (searchType === 'email') {
            response = await zoomUserApi.searchZoomUsersByEmail(selectedZoomAuth, searchText, params);
          } else {
            response = await zoomUserApi.searchZoomUsersByName(selectedZoomAuth, searchText, params);
          }
        } else {
          response = await zoomUserApi.getZoomUsersByAuth(selectedZoomAuth, params);
        }
      }

      const userList = response.data.content;
      setUsers(userList);
      setPagination({
        current: page,
        pageSize: pageSize,
        total: response.data.totalElements,
      });

      // 加载用户PMI统计信息
      loadUserPmiStats(userList);
    } catch (error) {
      message.error('加载用户列表失败');
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载用户PMI统计信息
  const loadUserPmiStats = async (userList) => {
    try {
      const stats = {};
      // 批量获取所有用户的PMI统计
      const promises = userList.map(async (user) => {
        if (user.user?.id) {
          try {
            const response = await pmiApi.getUserPmiStats(user.user.id);
            stats[user.user.id] = response.data.data.totalCount || 0;
          } catch (error) {
            console.error(`获取用户${user.user.id}的PMI统计失败:`, error);
            stats[user.user.id] = 0;
          }
        }
      });

      await Promise.all(promises);
      setUserPmiStats(stats);
    } catch (error) {
      console.error('加载用户PMI统计失败:', error);
    }
  };

  // 处理PMI管理按钮点击
  const handlePmiManagement = (record) => {
    const userId = record.user?.id;
    if (!userId) {
      message.warning('该用户没有关联的系统用户，无法管理PMI');
      return;
    }

    const pmiCount = userPmiStats[userId] || 0;

    if (pmiCount > 0) {
      // 用户有PMI，跳转到PMI列表页面
      navigate(`/pmi-management/user/${userId}`);
    } else {
      // 用户没有PMI，跳转到PMI管理页面并打开创建弹窗
      navigate(`/pmi-management/user/${userId}/create`);
    }
  };

  // 加载统计信息
  const loadDashboardStats = async () => {
    try {
      if (selectedZoomAuth === 'all') {
        // 全局统计 - 调用专门的全局统计API
        const response = await zoomUserApi.getGlobalDashboardStats();
        setDashboardStats(response.data);
      } else {
        const response = await zoomUserApi.getDashboardStats(selectedZoomAuth);
        setDashboardStats(response.data);
      }
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    }
  };

  // 同步用户信息
  const handleSyncUser = async (id) => {
    try {
      setSyncLoading(true);
      await zoomUserApi.syncZoomUserInfo(id);
      message.success('同步用户信息成功');
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('同步用户信息失败');
      console.error('Error syncing user:', error);
    } finally {
      setSyncLoading(false);
    }
  };

  // 从Zoom API同步所有用户
  const handleSyncFromApi = async () => {
    if (selectedZoomAuth === 'all') {
      message.warning('请选择特定的Zoom认证账号进行同步');
      return;
    }

    if (!selectedZoomAuth) {
      message.warning('请先选择Zoom认证账号');
      return;
    }

    try {
      setSyncLoading(true);
      const response = await zoomUserApi.syncUsersFromZoomApi(selectedZoomAuth);
      const result = response.data;

      if (result.success) {
        const { totalUsers, newUsers, updatedUsers, skippedUsers, errors } = result.result;
        let successMsg = `同步完成！总用户: ${totalUsers}, 新增: ${newUsers}, 更新: ${updatedUsers}, 跳过: ${skippedUsers}`;
        if (errors && errors.length > 0) {
          successMsg += `, 错误: ${errors.length}个`;
        }
        message.success(successMsg);
        loadUsers(pagination.current, pagination.pageSize);
        loadDashboardStats();
      } else {
        message.error(`同步失败: ${result.message}`);
      }
    } catch (error) {
      message.error('同步用户失败');
      console.error('Error syncing users from API:', error);
    } finally {
      setSyncLoading(false);
    }
  };

  // 同步用户PMI信息
  const handleSyncPmi = async () => {
    if (selectedZoomAuth === 'all') {
      message.warning('请选择特定的Zoom认证账号进行PMI同步');
      return;
    }

    if (!selectedZoomAuth) {
      message.warning('请先选择Zoom认证账号');
      return;
    }

    try {
      setSyncLoading(true);
      const response = await zoomUserApi.syncUsersPmi(selectedZoomAuth);
      const result = response.data;

      if (result.success) {
        const { totalUsers, newUsers, updatedUsers, skippedUsers } = result.data;
        let successMsg = `PMI同步完成！总用户: ${totalUsers}, 新增: ${newUsers}, 更新: ${updatedUsers}, 跳过: ${skippedUsers}`;
        message.success(successMsg);
        loadUsers(pagination.current, pagination.pageSize);
        loadDashboardStats();
      } else {
        message.error(`PMI同步失败: ${result.message}`);
      }
    } catch (error) {
      message.error('PMI同步失败');
      console.error('Error syncing PMI:', error);
    } finally {
      setSyncLoading(false);
    }
  };

  // 删除用户
  const handleDelete = async (id) => {
    try {
      await zoomUserApi.deleteZoomUser(id);
      message.success('删除成功');
      loadUsers(pagination.current, pagination.pageSize);
      loadDashboardStats();
    } catch (error) {
      message.error('删除失败');
      console.error('Error deleting user:', error);
    }
  };

  // 批量删除用户
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的用户');
      return;
    }

    try {
      await zoomUserApi.deleteZoomUsers(selectedRowKeys);
      message.success(`成功删除 ${selectedRowKeys.length} 个用户`);
      setSelectedRowKeys([]);
      loadUsers(pagination.current, pagination.pageSize);
      loadDashboardStats();
    } catch (error) {
      message.error('批量删除失败');
      console.error('Error batch deleting users:', error);
    }
  };

  // 搜索
  const handleSearch = (value) => {
    setSearchText(value);
    loadUsers(1, pagination.pageSize);
  };

  // 清除搜索
  const handleClearSearch = () => {
    setSearchText('');
    loadUsers(1, pagination.pageSize);
  };

  // 编辑PMI
  const handleEditPmi = (record) => {
    setPmiEditingUser(record);
    setPmiEditModalVisible(true);
    pmiEditForm.setFieldsValue({
      originalPmi: record.originalPmi || '',
    });
  };

  // 测试PMI设置
  const handleTestPmi = (record) => {
    setPmiTestingUser(record);
    setPmiTestModalVisible(true);
    pmiTestForm.setFieldsValue({
      pmi: '',
      password: '',
    });
  };

  // 生成随机PMI
  const handleGeneratePmi = async () => {
    try {
      setGeneratingPmi(true);
      const response = await zoomUserPmiApi.generatePmi();
      if (response.data.success) {
        pmiEditForm.setFieldsValue({
          originalPmi: response.data.data.pmi
        });
        message.success('PMI生成成功');
      } else {
        message.error(response.data.message || '生成PMI失败');
      }
    } catch (error) {
      console.error('生成PMI失败:', error);
      message.error('生成PMI失败');
    } finally {
      setGeneratingPmi(false);
    }
  };

  // 提交PMI编辑
  const handlePmiEditSubmit = async (values) => {
    try {
      setPmiEditLoading(true);
      const response = await zoomUserPmiApi.updateOriginalPmi(pmiEditingUser.id, values.originalPmi);
      if (response.data.success) {
        message.success('PMI更新成功');
        setPmiEditModalVisible(false);
        loadUsers(pagination.current, pagination.pageSize);
      } else {
        message.error(response.data.message || 'PMI更新失败');
      }
    } catch (error) {
      console.error('PMI更新失败:', error);
      message.error('PMI更新失败');
    } finally {
      setPmiEditLoading(false);
    }
  };

  // 提交PMI测试
  const handlePmiTestSubmit = async (values) => {
    if (!pmiTestingUser) return;

    try {
      setPmiTestLoading(true);
      const response = await zoomUserPmiApi.testOptimizedPmiSetup(pmiTestingUser.id, {
        pmi: values.pmi,
        password: values.password || ''
      });

      if (response.data.success) {
        const testResult = response.data.data;

        // 显示测试结果
        Modal.info({
          title: 'PMI设置策略测试结果',
          width: 600,
          content: (
            <div>
              <p><strong>用户ID:</strong> {testResult.userId}</p>
              <p><strong>目标PMI:</strong> {testResult.targetPmi}</p>
              <p><strong>是否有密码:</strong> {testResult.hasPassword ? '是' : '否'}</p>
              <p><strong>安全策略:</strong> {testResult.strategy}</p>
              <div>
                <strong>执行步骤:</strong>
                <ol>
                  {testResult.steps.map((step, index) => (
                    <li key={index}>{step}</li>
                  ))}
                </ol>
              </div>
            </div>
          ),
        });

        setPmiTestModalVisible(false);
      } else {
        message.error(response.data.message || 'PMI测试失败');
      }
    } catch (error) {
      console.error('PMI测试失败:', error);
      message.error('PMI测试失败');
    } finally {
      setPmiTestLoading(false);
    }
  };

  // 检查用户是否在使用中（简化版本，只检查usageStatus）
  const isUserInUse = (record) => {
    const isInUse = record.usageStatus === 'IN_USE';

    // 调试信息
    console.log('用户状态检查:', {
      id: record.id,
      email: record.email,
      usageStatus: record.usageStatus,
      result: isInUse
    });

    return isInUse;
  };

  // 回收账号
  const handleRecycleAccount = async (record) => {
    try {
      const response = await zoomUserPmiApi.recycleAccount(record.id);
      if (response.data.success) {
        message.success('账号回收成功');
        loadUsers(pagination.current, pagination.pageSize);
        loadDashboardStats();
      } else {
        message.error(response.data.message || '账号回收失败');
      }
    } catch (error) {
      console.error('账号回收失败:', error);
      message.error('账号回收失败');
    }
  };

  // 重置Host Key
  const handleResetHostKey = async (record) => {
    try {
      const response = await zoomUserApi.resetHostKey(record.id);
      if (response.data.success) {
        message.success(`Host Key重置成功，新密钥：${response.data.hostKey}`);
        loadUsers(pagination.current, pagination.pageSize);
      } else {
        message.error(response.data.message || '重置Host Key失败');
      }
    } catch (error) {
      console.error('重置Host Key失败:', error);
      message.error('重置Host Key失败');
    }
  };

  // 表格列定义 - 根据设备类型动态调整
  const getColumns = () => [
      {
        title: isMobileView ? 'ID' : 'Zoom用户ID',
        dataIndex: 'zoomUserId',
        key: 'zoomUserId',
        width: isMobileView ? 80 : 120,
        ellipsis: true,
        className: isMobileView ? '' : '',
      },
      // 当显示所有用户时，显示主账号信息
      ...(selectedZoomAuth === 'all' ? [{
        title: isMobileView ? '主账号' : '主账号',
        key: 'zoomAuth',
        width: isMobileView ? 100 : 150,
        ellipsis: true,
        render: (_, record) => (
          <Space size={isMobileView ? 'small' : 'middle'}>
            {!isMobileView && <KeyOutlined />}
            <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
              {record.zoomAuth?.accountName || '-'}
            </span>
          </Space>
        ),
      }] : []),
      {
        title: '邮箱',
        dataIndex: 'email',
        key: 'email',
        width: isMobileView ? 150 : 250,
        ellipsis: true,
        render: (email) => (
          <Space>
            <MailOutlined />
            <span style={{
              wordBreak: 'break-all',
              fontSize: isMobileView ? '11px' : '14px'
            }}>{email}</span>
          </Space>
        ),
      },
      {
        title: '姓名',
        key: 'name',
        width: isMobileView ? 100 : 150,
        ellipsis: true,
        render: (_, record) => (
          <Space>
            <UserOutlined />
            <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
              {record.displayName || `${record.firstName || ''} ${record.lastName || ''}`.trim() || '-'}
            </span>
          </Space>
        ),
      },
      {
        title: isMobileView ? '状态' : '账号使用状态',
        key: 'usageStatus',
        width: isMobileView ? 80 : 120,
        render: (_, record) => {
          const status = record.usageStatus || 'AVAILABLE';
          const statusConfig = {
            'AVAILABLE': { color: 'green', text: '可使用' },
            'IN_USE': { color: 'blue', text: '使用中' },
            'MAINTENANCE': { color: 'orange', text: '维护中' }
          };
          const config = statusConfig[status] || statusConfig['AVAILABLE'];
          return (
            <Tag color={config.color} style={{ fontSize: isMobileView ? '10px' : '12px' }}>
              {isMobileView ? config.text.substring(0, 2) : config.text}
            </Tag>
          );
        },
      },
      {
        title: isMobileView ? '原PMI' : '原始PMI',
        key: 'originalPmi',
        width: isMobileView ? 100 : 120,
        ellipsis: true,
        render: (_, record) => (
          <span style={{
            fontSize: isMobileView ? '11px' : '12px',
            fontFamily: 'monospace'
          }}>
            {record.originalPmi || '-'}
          </span>
        ),
      },
      {
        title: isMobileView ? '当前PMI' : '当前PMI',
        key: 'currentPmi',
        width: isMobileView ? 100 : 120,
        ellipsis: true,
        render: (_, record) => (
          <span style={{
            fontSize: isMobileView ? '11px' : '12px',
            fontFamily: 'monospace',
            color: record.currentPmi !== record.originalPmi ? '#1890ff' : 'inherit'
          }}>
            {record.currentPmi || '-'}
          </span>
        ),
      },
      {
        title: isMobileView ? '主持密钥' : '主持人密钥',
        key: 'hostKey',
        width: isMobileView ? 100 : 120,
        ellipsis: true,
        render: (_, record) => (
          <Space size="small">
            <span style={{
              fontSize: isMobileView ? '11px' : '12px',
              fontFamily: 'monospace',
              color: record.hostKey ? '#52c41a' : '#999'
            }}>
              {record.hostKey || '无'}
            </span>
            {record.hostKey && (
              <Button
                type="link"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => {
                  navigator.clipboard.writeText(record.hostKey);
                  message.success('主持人密钥已复制到剪贴板');
                }}
                style={{
                  padding: 0,
                  height: 'auto',
                  fontSize: isMobileView ? '10px' : '12px'
                }}
                title="复制主持人密钥"
              />
            )}
          </Space>
        ),
      },
      {
        title: isMobileView ? '关联' : '关联状态',
        key: 'userAssociation',
        width: isMobileView ? 80 : 180,
        render: (_, record) => {
          if (record.user && record.user.fullName) {
            return (
              <div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
                  <Tag color="green" icon={<LinkOutlined />} style={{ margin: 0, fontSize: isMobileView ? '10px' : '12px' }}>
                    {isMobileView ? '已关联' : '已关联'}
                  </Tag>
                </div>
                {!isMobileView && (
                  <Button
                    type="link"
                    size="small"
                    onClick={() => navigate(`/users/${record.user.id}`)}
                    style={{ padding: 0, height: 'auto', fontSize: '12px' }}
                    title={`点击查看用户详情: ${record.user.fullName} (${record.user.username})`}
                  >
                    {record.user.fullName}
                  </Button>
                )}
              </div>
            );
          }
          return (
            <div>
              <Tag color="orange" icon={<ExclamationCircleOutlined />} style={{ margin: 0, fontSize: isMobileView ? '10px' : '12px' }}>
                未关联
              </Tag>
              {!isMobileView && (
                <div style={{ fontSize: '11px', color: '#999', marginTop: 2 }}>
                  可选择关联用户
                </div>
              )}
            </div>
          );
        },
      },
      {
        title: isMobileView ? '类型' : '用户类型',
        dataIndex: 'userType',
        key: 'userType',
        width: isMobileView ? 60 : 100,
        className: isMobileView ? 'mobile-hidden' : '',
        render: (userType) => {
          const typeMap = {
            BASIC: { color: 'default', text: isMobileView ? '基础' : '基础版' },
            LICENSED: { color: 'blue', text: isMobileView ? '专业' : '专业版' },
            ON_PREM: { color: 'purple', text: isMobileView ? '本地' : '本地部署版' }
          };
          const type = typeMap[userType] || { color: 'default', text: userType };
          return <Tag color={type.color} style={{ fontSize: isMobileView ? '10px' : '12px' }}>{type.text}</Tag>;
        },
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: isMobileView ? 60 : 100,
        render: (status) => {
          const statusMap = {
            ACTIVE: { color: 'green', text: '活跃' },
            INACTIVE: { color: 'red', text: '非活跃' },
            PENDING: { color: 'orange', text: '待激活' }
          };
          const statusInfo = statusMap[status] || { color: 'default', text: status };
          return <Tag color={statusInfo.color} style={{ fontSize: isMobileView ? '10px' : '12px' }}>{statusInfo.text}</Tag>;
        },
      },
      // 所有设备都显示的列，但移动端使用紧凑样式
      {
        title: isMobileView ? '用途' : '账户用途',
        dataIndex: 'accountUsage',
        key: 'accountUsage',
        width: isMobileView ? 80 : 120,
        render: (accountUsage) => {
          const usageMap = {
            PUBLIC_MEETING: { color: 'blue', text: isMobileView ? '参会公' : '参会账号-公共' },
            PRIVATE_MEETING: { color: 'cyan', text: isMobileView ? '参会专' : '参会账号-专属' },
            PUBLIC_HOST: { color: 'green', text: isMobileView ? '开会公' : '开会账号-公共' },
            PRIVATE_HOST: { color: 'purple', text: isMobileView ? '开会专' : '开会账号-专属' }
          };
          const usage = usageMap[accountUsage] || { color: 'default', text: accountUsage || '-' };
          return <Tag color={usage.color} style={{ fontSize: isMobileView ? '10px' : '12px' }}>{usage.text}</Tag>;
        },
      },
      {
        title: '部门',
        dataIndex: 'department',
        key: 'department',
        width: isMobileView ? 80 : 120,
        ellipsis: true,
        render: (dept) => (
          <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
            {dept || '-'}
          </span>
        ),
      },
      {
        title: '职位',
        dataIndex: 'jobTitle',
        key: 'jobTitle',
        width: isMobileView ? 80 : 120,
        ellipsis: true,
        render: (title) => (
          <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
            {title || '-'}
          </span>
        ),
      },
      {
        title: '电话',
        dataIndex: 'phoneNumber',
        key: 'phoneNumber',
        width: isMobileView ? 100 : 120,
        ellipsis: true,
        render: (phone) => phone ? (
          <Space size="small">
            {!isMobileView && <PhoneOutlined />}
            <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
              {phone}
            </span>
          </Space>
        ) : (
          <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>-</span>
        ),
      },
      {
        title: '时区',
        dataIndex: 'timezone',
        key: 'timezone',
        width: isMobileView ? 80 : 120,
        ellipsis: true,
        render: (timezone) => timezone ? (
          <Space size="small">
            {!isMobileView && <GlobalOutlined />}
            <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>
              {timezone}
            </span>
          </Space>
        ) : (
          <span style={{ fontSize: isMobileView ? '11px' : '14px' }}>-</span>
        ),
      },
      {
        title: isMobileView ? '创建' : '创建时间',
        dataIndex: 'zoomCreatedAt',
        key: 'zoomCreatedAt',
        width: isMobileView ? 80 : 150,
        render: (time) => (
          <span style={{ fontSize: isMobileView ? '10px' : '14px' }}>
            {time ? (isMobileView ?
              new Date(time).toLocaleDateString() :
              new Date(time).toLocaleString()
            ) : '-'}
          </span>
        ),
      },
      {
        title: '操作',
        key: 'action',
        width: isMobileView ? 200 : 400, // PC端增加宽度以容纳所有按钮
        fixed: isMobileView ? false : 'right', // PC端固定操作栏
        render: (_, record) => {
          // 移动端双列展示
          const actionGroups = [
            // 移动端：双列布局
            [
              <Tooltip key="edit" title="编辑用户">
                <Button
                  type="default"
                  icon={<EditOutlined />}
                  size="small"
                  onClick={() => handleEdit(record)}
                  style={{
                    fontSize: '11px',
                    width: '70px',
                    height: '28px',
                    padding: '4px 6px'
                  }}
                >
                  编辑
                </Button>
              </Tooltip>,
              <Tooltip key="sync" title="同步用户信息">
                <Button
                  type="default"
                  icon={<SyncOutlined />}
                  size="small"
                  onClick={() => handleSyncUser(record.id)}
                  loading={syncLoading}
                  style={{
                    fontSize: '11px',
                    width: '70px',
                    height: '28px',
                    padding: '4px 6px'
                  }}
                >
                  同步
                </Button>
              </Tooltip>
            ],
            [
              record.user?.id && (
                <Tooltip key="pmi" title="PMI管理">
                  <Button
                    type="default"
                    icon={<SettingOutlined />}
                    size="small"
                    onClick={() => handlePmiManagement(record)}
                    style={{
                      fontSize: '11px',
                      width: '70px',
                      height: '28px',
                      padding: '4px 6px'
                    }}
                  >
                    PMI
                  </Button>
                </Tooltip>
              ),
              <Tooltip key="editPmi" title="编辑PMI">
                <Button
                  type="default"
                  icon={<ThunderboltOutlined />}
                  size="small"
                  onClick={() => handleEditPmi(record)}
                  style={{
                    fontSize: '11px',
                    width: '70px',
                    height: '28px',
                    padding: '4px 6px'
                  }}
                >
                  编辑PMI
                </Button>
              </Tooltip>,
              <Tooltip key="testPmi" title="测试PMI设置">
                <Button
                  type="default"
                  icon={<CopyOutlined />}
                  size="small"
                  onClick={() => handleTestPmi(record)}
                  style={{
                    fontSize: '11px',
                    width: '70px',
                    height: '28px',
                    padding: '4px 6px'
                  }}
                >
                  测试PMI
                </Button>
              </Tooltip>
            ],
            [
              isUserInUse(record) ? (
                <Popconfirm
                  key="recycle"
                  title="回收账号确认"
                  description="当前操作将结束进行中的会议，确认后恢复账号原始PMI状态改成可使用。确定要回收吗？"
                  onConfirm={() => handleRecycleAccount(record)}
                  okText="确定回收"
                  cancelText="取消"
                  okType="danger"
                  placement="top"
                >
                  <Tooltip title="回收账号">
                    <Button
                      type="primary"
                      danger
                      icon={<RedoOutlined />}
                      size="small"
                      style={{
                        fontSize: '11px',
                        width: '70px',
                        height: '28px',
                        padding: '4px 6px'
                      }}
                    >
                      回收
                    </Button>
                  </Tooltip>
                </Popconfirm>
              ) : (
                <Tooltip key="setInUse" title="设为使用中（测试）">
                  <Button
                    type="primary"
                    icon={<RedoOutlined />}
                    size="small"
                    onClick={() => {
                      const updatedUsers = users.map(user =>
                        user.id === record.id
                          ? { ...user, usageStatus: 'IN_USE' }
                          : user
                      );
                      setUsers(updatedUsers);
                      message.info('已设置为使用中状态（测试用）');
                    }}
                    style={{
                      fontSize: '11px',
                      width: '70px',
                      height: '28px',
                      padding: '4px 6px'
                    }}
                  >
                    设为使用中
                  </Button>
                </Tooltip>
              ),
              <Popconfirm
                key="delete"
                title="确定要删除这个用户吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
                placement="top"
              >
                <Tooltip title="删除用户">
                  <Button
                    type="primary"
                    danger
                    icon={<DeleteOutlined />}
                    size="small"
                    style={{
                      fontSize: '11px',
                      width: '70px',
                      height: '28px',
                      padding: '4px 6px'
                    }}
                  >
                    删除
                  </Button>
                </Tooltip>
              </Popconfirm>
            ]
          ];

          // PC端：单行水平布局
          const pcActions = [
            <Tooltip key="edit" title="编辑用户">
              <Button
                type="link"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEdit(record)}
              >
                编辑
              </Button>
            </Tooltip>,
            <Tooltip key="sync" title="同步用户信息">
              <Button
                type="link"
                icon={<SyncOutlined />}
                size="small"
                onClick={() => handleSyncUser(record.id)}
                loading={syncLoading}
              >
                同步
              </Button>
            </Tooltip>,
            record.user?.id && (
              <Tooltip key="pmi" title={(userPmiStats[record.user.id] || 0) > 0 ? "查看PMI列表" : "创建新PMI"}>
                <Button
                  type="link"
                  icon={(userPmiStats[record.user.id] || 0) > 0 ? <UnorderedListOutlined /> : <PlusOutlined />}
                  size="small"
                  onClick={() => handlePmiManagement(record)}
                  style={{
                    color: (userPmiStats[record.user.id] || 0) > 0 ? '#1890ff' : '#52c41a'
                  }}
                >
                  {(userPmiStats[record.user.id] || 0) > 0 ? 'PMI列表' : 'PMI创建'}
                </Button>
              </Tooltip>
            ),
            <Tooltip key="editPmi" title="编辑PMI">
              <Button
                type="link"
                icon={<ThunderboltOutlined />}
                size="small"
                onClick={() => handleEditPmi(record)}
              >
                编辑PMI
              </Button>
            </Tooltip>,
            isUserInUse(record) ? (
              <Popconfirm
                key="recycle"
                title="回收账号确认"
                description="当前操作将结束进行中的会议，确认后恢复账号原始PMI状态改成可使用。确定要回收吗？"
                onConfirm={() => handleRecycleAccount(record)}
                okText="确定回收"
                cancelText="取消"
                okType="danger"
                placement="topRight"
              >
                <Tooltip title="回收账号">
                  <Button
                    type="link"
                    icon={<RedoOutlined />}
                    size="small"
                    style={{ color: '#ff7875' }}
                  >
                    回收
                  </Button>
                </Tooltip>
              </Popconfirm>
            ) : (
              <Tooltip key="setInUse" title="设为使用中（测试）">
                <Button
                  type="link"
                  icon={<RedoOutlined />}
                  size="small"
                  onClick={() => {
                    const updatedUsers = users.map(user =>
                      user.id === record.id
                        ? { ...user, usageStatus: 'IN_USE' }
                        : user
                    );
                    setUsers(updatedUsers);
                    message.info('已设置为使用中状态（测试用）');
                  }}
                  style={{ color: '#52c41a' }}
                >
                  设为使用中
                </Button>
              </Tooltip>
            ),
            <Popconfirm
              key="delete"
              title="确定要删除这个用户吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
              placement="topRight"
            >
              <Tooltip title="删除用户">
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                >
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>
          ];

          return isMobileView ? (
            // 移动端：分行显示
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '4px',
              alignItems: 'flex-start'
            }}>
              {actionGroups.map((group, groupIndex) => (
                <div key={groupIndex} style={{
                  display: 'flex',
                  gap: '4px',
                  alignItems: 'center'
                }}>
                  {group.filter(Boolean).map((button, buttonIndex) => (
                    <span key={buttonIndex}>{button}</span>
                  ))}
                </div>
              ))}
            </div>
          ) : (
            // PC端：固定展示，一行最多3个按钮
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '4px',
              alignItems: 'flex-start'
            }}>
              {/* 第一行：编辑、同步、PMI管理 */}
              <div style={{
                display: 'flex',
                gap: '4px',
                alignItems: 'center'
              }}>
                <Tooltip title="编辑用户">
                  <Button
                    type="link"
                    icon={<EditOutlined />}
                    size="small"
                    onClick={() => handleEdit(record)}
                  >
                    编辑
                  </Button>
                </Tooltip>
                <Tooltip title="同步用户信息">
                  <Button
                    type="link"
                    icon={<SyncOutlined />}
                    size="small"
                    onClick={() => handleSyncUser(record.id)}
                    loading={syncLoading}
                  >
                    同步
                  </Button>
                </Tooltip>
                {record.user?.id && (
                  <Tooltip title={(userPmiStats[record.user.id] || 0) > 0 ? "查看PMI列表" : "创建新PMI"}>
                    <Button
                      type="link"
                      icon={(userPmiStats[record.user.id] || 0) > 0 ? <UnorderedListOutlined /> : <PlusOutlined />}
                      size="small"
                      onClick={() => handlePmiManagement(record)}
                      style={{
                        color: (userPmiStats[record.user.id] || 0) > 0 ? '#1890ff' : '#52c41a'
                      }}
                    >
                      {(userPmiStats[record.user.id] || 0) > 0 ? 'PMI列表' : 'PMI创建'}
                    </Button>
                  </Tooltip>
                )}
              </div>

              {/* 第二行：编辑PMI、重置密钥、回收/设为使用中 */}
              <div style={{
                display: 'flex',
                gap: '4px',
                alignItems: 'center'
              }}>
                <Tooltip title="编辑PMI">
                  <Button
                    type="link"
                    icon={<ThunderboltOutlined />}
                    size="small"
                    onClick={() => handleEditPmi(record)}
                  >
                    编辑PMI
                  </Button>
                </Tooltip>

                <Tooltip title="测试PMI设置">
                  <Button
                    type="link"
                    icon={<CopyOutlined />}
                    size="small"
                    onClick={() => handleTestPmi(record)}
                  >
                    测试PMI
                  </Button>
                </Tooltip>

                {/* 只有专业版用户才显示重置Host Key按钮 */}
                {record.userType === 'LICENSED' && (
                  <Popconfirm
                    title="重置Host Key确认"
                    description="将为该用户生成新的6位数字Host Key，确定要重置吗？"
                    onConfirm={() => handleResetHostKey(record)}
                    okText="确定重置"
                    cancelText="取消"
                    placement="topRight"
                  >
                    <Tooltip title="重置Host Key">
                      <Button
                        type="link"
                        icon={<KeyOutlined />}
                        size="small"
                        style={{ color: '#1890ff' }}
                      >
                        重置Host Key
                      </Button>
                    </Tooltip>
                  </Popconfirm>
                )}

                {isUserInUse(record) ? (
                  <Popconfirm
                    title="回收账号确认"
                    description="当前操作将结束进行中的会议，确认后恢复账号原始PMI状态改成可使用。确定要回收吗？"
                    onConfirm={() => handleRecycleAccount(record)}
                    okText="确定回收"
                    cancelText="取消"
                    okType="danger"
                    placement="topRight"
                  >
                    <Tooltip title="回收账号">
                      <Button
                        type="link"
                        icon={<RedoOutlined />}
                        size="small"
                        style={{ color: '#ff7875' }}
                      >
                        回收
                      </Button>
                    </Tooltip>
                  </Popconfirm>
                ) : (
                  <Tooltip title="设为使用中（测试）">
                    <Button
                      type="link"
                      icon={<RedoOutlined />}
                      size="small"
                      onClick={() => {
                        const updatedUsers = users.map(user =>
                          user.id === record.id
                            ? { ...user, usageStatus: 'IN_USE' }
                            : user
                        );
                        setUsers(updatedUsers);
                        message.info('已设置为使用中状态（测试用）');
                      }}
                      style={{ color: '#52c41a' }}
                    >
                      设为使用中
                    </Button>
                  </Tooltip>
                )}
              </div>

              {/* 第三行：删除 */}
              <div style={{
                display: 'flex',
                gap: '4px',
                alignItems: 'center'
              }}>
                <Popconfirm
                  title="确定要删除这个用户吗？"
                  onConfirm={() => handleDelete(record.id)}
                  okText="确定"
                  cancelText="取消"
                  placement="topRight"
                >
                  <Tooltip title="删除用户">
                    <Button
                      type="link"
                      danger
                      icon={<DeleteOutlined />}
                      size="small"
                    >
                      删除
                    </Button>
                  </Tooltip>
                </Popconfirm>
              </div>
            </div>
          );
        },
      },
  ];

  const columns = getColumns();

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  useEffect(() => {
    loadZoomAuthList();
    // 页面加载时默认显示所有用户
    loadUsers();
    loadDashboardStats();
  }, []);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (selectedZoomAuth) {
      loadUsers();
      loadDashboardStats();
    }
  }, [selectedZoomAuth]);

  // 监听userId和zoomUserId参数变化
  useEffect(() => {
    if (userId || zoomUserId) {
      // 如果有userId或zoomUserId参数，重新加载用户列表
      loadUsers();
    }
  }, [userId, zoomUserId]);

  return (
    <div style={{ padding: isMobileView ? '12px' : '24px' }}>
      <Card
        title={
          <div>
            <span>Zoom用户管理</span>
            {zoomUserId && (
              <Tag color="green" style={{ marginLeft: '8px' }}>
                按ZoomUser ID过滤: {zoomUserId}
              </Tag>
            )}
            {userId && !zoomUserId && (
              <Tag color="blue" style={{ marginLeft: '8px' }}>
                按用户ID过滤: {userId}
              </Tag>
            )}
          </div>
        }
        style={{ marginBottom: '16px' }}>

        {/* 统计信息 */}
        {selectedZoomAuth && (
          <Row gutter={[16, 12]} style={{ marginBottom: '16px' }} className={isMobileView ? 'mobile-stats' : ''}>
            <Col xs={12} sm={12} md={6} lg={6} xl={6}>
              <Statistic
                title="总用户数"
                value={dashboardStats.totalUsers || 0}
                prefix={<UserOutlined />}
              />
            </Col>
            <Col xs={12} sm={12} md={6} lg={6} xl={6}>
              <Statistic
                title="活跃用户"
                value={getStatValue(dashboardStats.statusStats, 'ACTIVE')}
                valueStyle={{ color: '#3f8600' }}
              />
            </Col>
            <Col xs={12} sm={12} md={6} lg={6} xl={6}>
              <Statistic
                title="专业版用户"
                value={getStatValue(dashboardStats.typeStats, 'LICENSED')}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col xs={12} sm={12} md={6} lg={6} xl={6}>
              <Statistic
                title="基础版用户"
                value={getStatValue(dashboardStats.typeStats, 'BASIC')}
              />
            </Col>
          </Row>
        )}

        <Divider />

        {/* 操作栏 */}
        <div style={{ marginBottom: '16px' }} className={isMobileView ? 'zoom-user-filters' : ''}>
          {/* 第一行：主账号选择和搜索 */}
          <Row gutter={[16, 12]} style={{ marginBottom: 12 }}>
            <Col xs={24} sm={24} md={12} lg={8} xl={8}>
              <Select
                placeholder="选择Zoom认证账号"
                style={{ width: '100%' }}
                value={selectedZoomAuth}
                onChange={setSelectedZoomAuth}
                showSearch
                optionFilterProp="children"
                size={isMobileView ? 'middle' : 'middle'}
              >
                <Option key="all" value="all">
                  <Space>
                    <UsergroupAddOutlined />
                    全部主账号
                  </Space>
                </Option>
                {zoomAuthList.map(auth => (
                  <Option key={auth.id} value={auth.id}>
                    <Space>
                      <KeyOutlined />
                      {auth.accountName} ({auth.primaryEmail})
                    </Space>
                  </Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={24} md={12} lg={16} xl={16}>
              <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                <Select
                  value={searchType}
                  onChange={setSearchType}
                  style={{ width: 100, minWidth: 100 }}
                >
                  <Option value="email">邮箱</Option>
                  <Option value="name">姓名</Option>
                </Select>
                <Search
                  placeholder={`按${searchType === 'email' ? '邮箱' : '姓名'}搜索`}
                  allowClear
                  enterButton={<SearchOutlined />}
                  onSearch={handleSearch}
                  onClear={handleClearSearch}
                  style={{ flex: 1, minWidth: 200, maxWidth: 300 }}
                />
              </div>
            </Col>
          </Row>

          {/* 第二行：操作按钮 */}
          <Row>
            <Col span={24}>
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '8px',
                justifyContent: 'flex-end',
                alignItems: 'center'
              }}
              className="zoom-user-actions"
              >
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreate}
                  style={{ marginBottom: '4px' }}
                >
                  <span style={{ whiteSpace: 'nowrap' }}>创建Zoom账号</span>
                </Button>
                <Button
                  icon={<SyncOutlined />}
                  onClick={handleSyncFromApi}
                  loading={syncLoading}
                  disabled={!selectedZoomAuth || selectedZoomAuth === 'all'}
                  title={selectedZoomAuth === 'all' ? '请选择特定认证账号进行同步' : ''}
                  style={{ marginBottom: '4px' }}
                >
                  <span style={{ whiteSpace: 'nowrap' }}>从Zoom同步用户</span>
                </Button>
                <Button
                  icon={<KeyOutlined />}
                  onClick={handleSyncPmi}
                  loading={syncLoading}
                  disabled={!selectedZoomAuth || selectedZoomAuth === 'all'}
                  title={selectedZoomAuth === 'all' ? '请选择特定认证账号进行PMI同步' : '同步用户PMI信息，补全original_pmi字段'}
                  style={{ marginBottom: '4px' }}
                >
                  <span style={{ whiteSpace: 'nowrap' }}>同步PMI信息</span>
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => loadUsers(pagination.current, pagination.pageSize)}
                  loading={loading}
                  style={{ marginBottom: '4px' }}
                >
                  <span style={{ whiteSpace: 'nowrap' }}>刷新</span>
                </Button>
                {selectedRowKeys.length > 0 && (
                  <Popconfirm
                    title={`确定要删除选中的 ${selectedRowKeys.length} 个用户吗？`}
                    onConfirm={handleBatchDelete}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      style={{ marginBottom: '4px' }}
                    >
                      <span style={{ whiteSpace: 'nowrap' }}>批量删除</span>
                    </Button>
                  </Popconfirm>
                )}
              </div>
            </Col>
          </Row>
        </div>

        {/* 移动端滚动提示 */}
        {isMobileView && (
          <div className="mobile-scroll-hint">
            👈 表格可左右滑动查看所有列信息（ID、邮箱、姓名、状态、类型、用途、部门、职位、电话、时区、创建时间、操作）
          </div>
        )}

        {/* 用户表格 */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: !isMobileView,
            showQuickJumper: !isMobileView,
            showTotal: !isMobileView ? (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条` : false,
            onChange: (page, pageSize) => {
              loadUsers(page, pageSize);
            },
            size: isMobileView ? 'small' : 'default',
          }}
          scroll={{ x: isMobileView ? 1200 : 1800 }}
          size={isMobileView ? 'small' : 'middle'}
        />
      </Card>

      {/* 创建Zoom用户弹窗 */}
      <Modal
        title="创建Zoom账号"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={isMobileView ? '95%' : 600}
        style={isMobileView ? { top: 20 } : {}}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateSubmit}
        >
          <Form.Item
            name="userId"
            label="选择用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select
              placeholder="请输入用户名、姓名或邮箱进行搜索"
              showSearch
              allowClear
              loading={userSearchLoading}
              filterOption={false}
              onSearch={handleUserSearch}
              optionLabelProp="label"
              notFoundContent={userSearchLoading ? '搜索中...' : '没有找到匹配的用户'}
              dropdownRender={(menu) => (
                <div>
                  {menu}
                  <div style={{
                    padding: '8px',
                    borderTop: '1px solid #f0f0f0',
                    color: '#999',
                    fontSize: '12px'
                  }}>
                    输入至少2个字符开始搜索
                  </div>
                </div>
              )}
            >
              {searchUsers.map(user => (
                <Option
                  key={user.id}
                  value={user.id}
                  label={`${user.fullName} (${user.username})`}
                >
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{user.fullName}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {user.username} - {user.email}
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="zoomAuthId"
            label="选择Zoom认证账号"
            rules={[{ required: true, message: '请选择Zoom认证账号' }]}
          >
            <Select
              placeholder="请选择Zoom认证账号"
              showSearch
              optionFilterProp="children"
            >
              {zoomAuthList.map(auth => (
                <Option key={auth.id} value={auth.id}>
                  <Space>
                    <KeyOutlined />
                    {auth.accountName} ({auth.primaryEmail})
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="email"
            label="Zoom邮箱"
            rules={[
              { required: true, message: '请输入Zoom邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="firstName"
                label="名字"
                rules={[{ required: true, message: '请输入名字' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="lastName"
                label="姓氏"
                rules={[{ required: true, message: '请输入姓氏' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="userType"
            label="用户类型"
            initialValue="BASIC"
          >
            <Select>
              <Option value="BASIC">基础版</Option>
              <Option value="LICENSED">专业版</Option>
              <Option value="ON_PREM">本地部署</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="accountUsage"
            label="账户用途"
          >
            <Select placeholder="请选择账户用途">
              <Option value="PUBLIC_MEETING">参会账号-公共</Option>
              <Option value="PRIVATE_MEETING">参会账号-专属</Option>
              <Option value="PUBLIC_HOST">开会账号-公共</Option>
              <Option value="PRIVATE_HOST">开会账号-专属</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="department"
                label="部门"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="jobTitle"
                label="职位"
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phoneNumber"
                label="电话"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="timezone"
                label="时区"
              >
                <Input placeholder="如: Asia/Shanghai" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="language"
            label="语言"
          >
            <Select placeholder="请选择语言">
              <Option value="zh-CN">中文</Option>
              <Option value="en-US">English</Option>
              <Option value="ja-JP">日本語</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={createLoading}
              >
                创建
              </Button>
              <Button onClick={() => setCreateModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑Zoom用户弹窗 */}
      <Modal
        title="编辑Zoom用户"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={isMobileView ? '95%' : 600}
        style={isMobileView ? { top: 20 } : {}}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditSubmit}
        >
          <Form.Item
            name="userId"
            label="关联用户"
          >
            <Select
              placeholder="请输入用户名、姓名或邮箱进行搜索（留空表示取消关联）"
              showSearch
              allowClear
              loading={editUserSearchLoading}
              filterOption={false}
              onSearch={handleEditUserSearch}
              optionLabelProp="label"
              notFoundContent={editUserSearchLoading ? '搜索中...' : '没有找到匹配的用户'}
              dropdownRender={(menu) => (
                <div>
                  {menu}
                  <div style={{
                    padding: '8px',
                    borderTop: '1px solid #f0f0f0',
                    color: '#999',
                    fontSize: '12px'
                  }}>
                    输入至少2个字符开始搜索，留空表示取消用户关联
                  </div>
                </div>
              )}
            >
              {editSearchUsers.map(user => (
                <Option
                  key={user.id}
                  value={user.id}
                  label={`${user.fullName} (${user.username})`}
                >
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{user.fullName}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {user.username} - {user.email}
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="email"
            label="Zoom邮箱"
            rules={[
              { required: true, message: '请输入Zoom邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="firstName"
                label="名字"
                rules={[{ required: true, message: '请输入名字' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="lastName"
                label="姓氏"
                rules={[{ required: true, message: '请输入姓氏' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="displayName"
            label="显示名称"
          >
            <Input />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="userType"
                label="用户类型"
              >
                <Select>
                  <Option value="BASIC">基础版</Option>
                  <Option value="LICENSED">专业版</Option>
                  <Option value="ON_PREM">本地部署</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
              >
                <Select>
                  <Option value="ACTIVE">活跃</Option>
                  <Option value="INACTIVE">非活跃</Option>
                  <Option value="PENDING">待激活</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="accountUsage"
            label="账户用途"
          >
            <Select placeholder="请选择账户用途">
              <Option value="PUBLIC_MEETING">参会账号-公共</Option>
              <Option value="PRIVATE_MEETING">参会账号-专属</Option>
              <Option value="PUBLIC_HOST">开会账号-公共</Option>
              <Option value="PRIVATE_HOST">开会账号-专属</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="department"
                label="部门"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="jobTitle"
                label="职位"
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phoneNumber"
                label="电话"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="timezone"
                label="时区"
              >
                <Input placeholder="如: Asia/Shanghai" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="language"
            label="语言"
          >
            <Select placeholder="请选择语言">
              <Option value="zh-CN">中文</Option>
              <Option value="en-US">English</Option>
              <Option value="ja-JP">日本語</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={editLoading}
              >
                更新
              </Button>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* PMI编辑弹窗 */}
      <Modal
        title="编辑原始PMI"
        open={pmiEditModalVisible}
        onCancel={() => setPmiEditModalVisible(false)}
        footer={null}
        width={isMobileView ? '95%' : 500}
      >
        <Form
          form={pmiEditForm}
          layout="vertical"
          onFinish={handlePmiEditSubmit}
        >
          <Form.Item
            name="originalPmi"
            label="原始PMI号码"
            rules={[
              { required: true, message: '请输入PMI号码' },
              {
                pattern: /^\d{10}$/,
                message: 'PMI必须是10位数字'
              },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();

                  // 检查第一位不能是0或1
                  if (value.charAt(0) === '0' || value.charAt(0) === '1') {
                    return Promise.reject(new Error('PMI不能以0或1开头'));
                  }



                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input
              placeholder="请输入10位PMI号码"
              maxLength={10}
              style={{ fontFamily: 'monospace' }}
              addonAfter={
                <Button
                  type="link"
                  icon={<ThunderboltOutlined />}
                  onClick={handleGeneratePmi}
                  loading={generatingPmi}
                  size="small"
                  title="生成随机PMI"
                >
                  生成
                </Button>
              }
            />
          </Form.Item>

          <div style={{
            background: '#f6f8fa',
            padding: '12px',
            borderRadius: '6px',
            marginBottom: '16px',
            fontSize: '12px',
            color: '#666'
          }}>
            <div><strong>PMI规则：</strong></div>
            <div>• 必须是10位数字</div>
            <div>• 不能以0或1开头</div>
            <div>• 修改后会调用Zoom API设置PMI</div>
          </div>

          <Form.Item style={{ marginBottom: 0 }}>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setPmiEditModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={pmiEditLoading}
              >
                确定更新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* PMI测试弹窗 */}
      <Modal
        title="测试PMI设置策略"
        open={pmiTestModalVisible}
        onCancel={() => setPmiTestModalVisible(false)}
        footer={null}
        width={isMobileView ? '95%' : 600}
      >
        <Form
          form={pmiTestForm}
          layout="vertical"
          onFinish={handlePmiTestSubmit}
        >
          <div style={{
            marginBottom: '16px',
            padding: '12px',
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '6px'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
              测试用户: {pmiTestingUser?.email}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              此功能用于测试优化后的PMI设置策略，验证Zoom安全设置逻辑
            </div>
          </div>

          <Form.Item
            name="pmi"
            label="测试PMI号码"
            rules={[
              { required: true, message: '请输入PMI号码' },
              { pattern: /^[2-9][0-9]{9}$/, message: 'PMI必须是10位数字，不以0、1开头' }
            ]}
          >
            <Input
              placeholder="请输入10位PMI号码"
              maxLength={10}
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="测试密码"
            help="留空表示无密码，将开启等候室；有密码则关闭等候室"
          >
            <Input.Password
              placeholder="请输入密码（可选）"
              maxLength={10}
            />
          </Form.Item>

          <div style={{
            marginBottom: '16px',
            fontSize: '12px',
            color: '#666',
            backgroundColor: '#f0f0f0',
            padding: '12px',
            borderRadius: '6px'
          }}>
            <div><strong>Zoom安全策略：</strong></div>
            <div>• 密码和等候室必须至少选择一项</div>
            <div>• 有密码时：设置密码，关闭等候室</div>
            <div>• 无密码时：开启等候室，密码为空</div>
            <div>• 测试完成后会显示具体的执行步骤</div>
          </div>

          <Form.Item style={{ marginBottom: 0 }}>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setPmiTestModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={pmiTestLoading}
              >
                开始测试
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 自定义样式 */}
      <style jsx>{`
        /* 响应式按钮布局优化 */
        @media (max-width: 992px) {
          :global(.zoom-user-actions) {
            justify-content: flex-start !important;
          }
        }

        @media (max-width: 768px) {
          :global(.ant-btn) {
            margin-bottom: 8px !important;
          }

          :global(.ant-space) {
            flex-wrap: wrap !important;
          }

          :global(.zoom-user-actions) {
            justify-content: flex-start !important;
            margin-top: 8px;
          }
        }

        @media (max-width: 576px) {
          :global(.zoom-user-actions .ant-btn) {
            flex: 1 1 auto !important;
            min-width: 120px !important;
            margin-bottom: 8px !important;
          }

          :global(.ant-btn-group .ant-btn) {
            flex: none !important;
            width: auto !important;
          }

          :global(.zoom-user-actions) {
            justify-content: stretch !important;
          }
        }
      `}</style>
    </div>
  );
};

export default ZoomUserManagement;
