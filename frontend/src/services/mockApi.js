/**
 * Mock API服务
 * 用于在后端服务不可用时提供模拟数据
 */

// 模拟任务数据
const mockTasks = [
  {
    id: 1,
    pmiWindowId: 101,
    taskType: 'PMI_WINDOW_OPEN',
    status: 'SCHEDULED',
    scheduledTime: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30分钟后
    actualExecutionTime: null,
    retryCount: 0,
    errorMessage: null,
    pmiNumber: 'PMI001',
    userName: '张三',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    pmiWindowId: 102,
    taskType: 'PMI_WINDOW_CLOSE',
    status: 'EXECUTING',
    scheduledTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5分钟前
    actualExecutionTime: new Date().toISOString(),
    retryCount: 0,
    errorMessage: null,
    pmiNumber: 'PMI002',
    userName: '李四',
    createdAt: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 3,
    pmiWindowId: 103,
    taskType: 'PMI_WINDOW_OPEN',
    status: 'COMPLETED',
    scheduledTime: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1小时前
    actualExecutionTime: new Date(Date.now() - 58 * 60 * 1000).toISOString(),
    retryCount: 0,
    errorMessage: null,
    pmiNumber: 'PMI003',
    userName: '王五',
    createdAt: new Date(Date.now() - 70 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 58 * 60 * 1000).toISOString()
  },
  {
    id: 4,
    pmiWindowId: 104,
    taskType: 'PMI_WINDOW_CLOSE',
    status: 'FAILED',
    scheduledTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前
    actualExecutionTime: new Date(Date.now() - 118 * 60 * 1000).toISOString(),
    retryCount: 2,
    errorMessage: '网络连接超时',
    pmiNumber: 'PMI004',
    userName: '赵六',
    createdAt: new Date(Date.now() - 130 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 115 * 60 * 1000).toISOString()
  },
  {
    id: 5,
    pmiWindowId: 105,
    taskType: 'PMI_WINDOW_OPEN',
    status: 'SCHEDULED',
    scheduledTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2小时后
    actualExecutionTime: null,
    retryCount: 0,
    errorMessage: null,
    pmiNumber: 'PMI005',
    userName: '孙七',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// 模拟统计数据
const mockStatistics = {
  totalTasks: 25,
  scheduledTasks: 8,
  executingTasks: 2,
  completedTasks: 12,
  failedTasks: 3,
  successRate: 80.0,
  tasksLast24Hours: 15,
  successfulTasksLast24Hours: 12,
  failedTasksLast24Hours: 3,
  tasksByType: {
    'PMI_WINDOW_OPEN': 13,
    'PMI_WINDOW_CLOSE': 12
  }
};

// 模拟延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Mock API服务
 */
export const mockPmiTaskApi = {
  
  /**
   * 获取PMI任务列表
   */
  getPmiTasks: async (params = {}) => {
    await delay(500); // 模拟网络延迟
    
    const { page = 1, size = 10, status, taskType } = params;
    
    let filteredTasks = [...mockTasks];
    
    // 状态筛选
    if (status) {
      filteredTasks = filteredTasks.filter(task => task.status === status);
    }
    
    // 类型筛选
    if (taskType) {
      filteredTasks = filteredTasks.filter(task => task.taskType === taskType);
    }
    
    // 分页
    const startIndex = (page - 1) * size;
    const endIndex = startIndex + size;
    const content = filteredTasks.slice(startIndex, endIndex);
    
    return {
      data: {
        content,
        totalElements: filteredTasks.length,
        totalPages: Math.ceil(filteredTasks.length / size),
        size,
        number: page - 1
      }
    };
  },

  /**
   * 获取任务详情
   */
  getTaskDetail: async (taskId) => {
    await delay(300);
    const task = mockTasks.find(t => t.id === parseInt(taskId));
    if (!task) {
      throw new Error('任务不存在');
    }
    return { data: task };
  },

  /**
   * 获取PMI任务统计
   */
  getTaskStatistics: async () => {
    await delay(400);
    return { data: mockStatistics };
  },

  /**
   * 获取即将执行的PMI任务
   */
  getUpcomingTasks: async (hours = 24) => {
    await delay(300);
    const now = new Date();
    const futureTime = new Date(now.getTime() + hours * 60 * 60 * 1000);
    
    const upcomingTasks = mockTasks.filter(task => {
      const scheduledTime = new Date(task.scheduledTime);
      return task.status === 'SCHEDULED' && 
             scheduledTime > now && 
             scheduledTime <= futureTime;
    });
    
    return { data: upcomingTasks };
  },

  /**
   * 手动执行PMI任务
   */
  executeTask: async (taskId) => {
    await delay(800);
    const taskIndex = mockTasks.findIndex(t => t.id === parseInt(taskId));
    if (taskIndex === -1) {
      throw new Error('任务不存在');
    }
    
    mockTasks[taskIndex].status = 'EXECUTING';
    mockTasks[taskIndex].actualExecutionTime = new Date().toISOString();
    mockTasks[taskIndex].updatedAt = new Date().toISOString();
    
    // 模拟执行过程
    setTimeout(() => {
      mockTasks[taskIndex].status = 'COMPLETED';
      mockTasks[taskIndex].updatedAt = new Date().toISOString();
    }, 3000);
    
    return { data: { message: '任务执行成功' } };
  },

  /**
   * 取消PMI任务
   */
  cancelTask: async (taskId) => {
    await delay(500);
    const taskIndex = mockTasks.findIndex(t => t.id === parseInt(taskId));
    if (taskIndex === -1) {
      throw new Error('任务不存在');
    }
    
    if (mockTasks[taskIndex].status !== 'SCHEDULED') {
      throw new Error('只能取消已调度的任务');
    }
    
    mockTasks[taskIndex].status = 'CANCELLED';
    mockTasks[taskIndex].updatedAt = new Date().toISOString();
    
    return { data: { message: '任务取消成功' } };
  },

  /**
   * 重新调度PMI任务
   */
  rescheduleTask: async (taskId, rescheduleData) => {
    await delay(600);
    const taskIndex = mockTasks.findIndex(t => t.id === parseInt(taskId));
    if (taskIndex === -1) {
      throw new Error('任务不存在');
    }
    
    mockTasks[taskIndex].scheduledTime = rescheduleData.newExecuteTime;
    mockTasks[taskIndex].updatedAt = new Date().toISOString();
    
    return { data: { message: '任务重新调度成功' } };
  },

  /**
   * 获取任务历史
   */
  getTaskHistory: async (params = {}) => {
    await delay(400);
    const completedTasks = mockTasks.filter(task => 
      task.status === 'COMPLETED' || task.status === 'FAILED'
    );
    return { data: { content: completedTasks } };
  }
};

/**
 * 检查是否使用Mock API
 */
export const shouldUseMockApi = () => {
  return process.env.NODE_ENV === 'development' && 
         process.env.REACT_APP_USE_MOCK_API === 'true';
};
