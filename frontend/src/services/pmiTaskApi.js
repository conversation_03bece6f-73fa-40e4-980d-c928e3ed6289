// PMI任务管理API服务
import api from './api';
import { mockPmiTaskApi, shouldUseMockApi } from './mockApi';

const PMI_TASK_API_BASE = '/pmi-scheduled-tasks';

/**
 * PMI任务管理API服务
 */
export const pmiTaskApi = {
  
  /**
   * 获取PMI任务列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 页大小
   * @param {string} params.status - 任务状态
   * @param {string} params.taskType - 任务类型
   */
  getPmiTasks: (params = {}) => {
    console.log('🔍 PMI任务API调用:', {
      useMockApi: shouldUseMockApi(),
      params,
      env: process.env.NODE_ENV,
      mockApiFlag: process.env.REACT_APP_USE_MOCK_API
    });

    if (shouldUseMockApi()) {
      console.log('📱 使用Mock API');
      return mockPmiTaskApi.getPmiTasks(params);
    }
    console.log('🌐 使用真实API');
    return api.get(PMI_TASK_API_BASE, { params });
  },

  /**
   * 获取任务详情
   * @param {number} taskId - 任务ID
   */
  getTaskDetail: (taskId) => {
    if (shouldUseMockApi()) {
      return mockPmiTaskApi.getTaskDetail(taskId);
    }
    return api.get(`${PMI_TASK_API_BASE}/${taskId}`);
  },

  /**
   * 手动执行PMI任务
   * @param {number} taskId - 任务ID
   */
  executeTask: (taskId) => {
    if (shouldUseMockApi()) {
      return mockPmiTaskApi.executeTask(taskId);
    }
    return api.post(`${PMI_TASK_API_BASE}/${taskId}/execute`);
  },

  /**
   * 取消PMI任务
   * @param {number} taskId - 任务ID
   */
  cancelTask: (taskId) => {
    if (shouldUseMockApi()) {
      return mockPmiTaskApi.cancelTask(taskId);
    }
    return api.delete(`${PMI_TASK_API_BASE}/${taskId}`);
  },

  /**
   * 重新调度PMI任务
   * @param {number} taskId - 任务ID
   * @param {Object} rescheduleData - 重新调度数据
   * @param {string} rescheduleData.newExecuteTime - 新执行时间
   * @param {string} rescheduleData.reason - 重新调度原因
   */
  rescheduleTask: (taskId, rescheduleData) => {
    if (shouldUseMockApi()) {
      return mockPmiTaskApi.rescheduleTask(taskId, rescheduleData);
    }
    return api.put(`${PMI_TASK_API_BASE}/${taskId}/reschedule`, rescheduleData);
  },

  /**
   * 获取PMI任务统计
   */
  getTaskStatistics: () => {
    if (shouldUseMockApi()) {
      return mockPmiTaskApi.getTaskStatistics();
    }
    return api.get(`${PMI_TASK_API_BASE}/statistics`);
  },

  /**
   * 获取即将执行的PMI任务
   * @param {number} hours - 未来几小时内的任务
   */
  getUpcomingTasks: (hours = 24) => {
    if (shouldUseMockApi()) {
      return mockPmiTaskApi.getUpcomingTasks(hours);
    }
    return api.get(`${PMI_TASK_API_BASE}/upcoming`, {
      params: { hours }
    });
  },

  /**
   * 获取PMI任务历史
   * @param {Object} params - 查询参数
   */
  getTaskHistory: (params = {}) => {
    if (shouldUseMockApi()) {
      return mockPmiTaskApi.getTaskHistory(params);
    }
    return api.get(`${PMI_TASK_API_BASE}/history`, { params });
  },

  /**
   * 获取窗口的任务
   * @param {number} windowId - 窗口ID
   */
  getWindowTasks: (windowId) => {
    return api.get(`${PMI_TASK_API_BASE}/window/${windowId}`);
  },

  /**
   * 批量取消任务
   * @param {Array<number>} taskIds - 任务ID列表
   */
  batchCancelTasks: (taskIds) => {
    return api.post(`${PMI_TASK_API_BASE}/batch/cancel`, taskIds);
  },

  /**
   * 批量重新调度任务
   * @param {Object} batchRescheduleData - 批量重新调度数据
   * @param {Array<number>} batchRescheduleData.taskIds - 任务ID列表
   * @param {string} batchRescheduleData.newExecuteTime - 新执行时间
   * @param {string} batchRescheduleData.reason - 重新调度原因
   */
  batchRescheduleTasks: (batchRescheduleData) => {
    return api.post(`${PMI_TASK_API_BASE}/batch/reschedule`, batchRescheduleData);
  },

  /**
   * 导出任务数据
   * @param {Object} exportRequest - 导出请求参数
   */
  exportTasks: (exportRequest) => {
    return api.post(`${PMI_TASK_API_BASE}/export`, exportRequest, {
      responseType: 'blob'
    });
  },

  /**
   * 获取任务执行趋势数据（移动端图表用）
   * @param {number} days - 天数
   */
  getTaskTrend: (days = 7) => {
    return api.get(`${PMI_TASK_API_BASE}/trend`, {
      params: { days }
    });
  },

  /**
   * 获取任务健康状态
   */
  getTaskHealth: () => {
    return api.get(`${PMI_TASK_API_BASE}/health`);
  }
};

/**
 * 任务状态常量
 */
export const TASK_STATUS = {
  SCHEDULED: 'SCHEDULED',
  EXECUTING: 'EXECUTING', 
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
};

/**
 * 任务类型常量
 */
export const TASK_TYPE = {
  PMI_WINDOW_OPEN: 'PMI_WINDOW_OPEN',
  PMI_WINDOW_CLOSE: 'PMI_WINDOW_CLOSE'
};

/**
 * 获取任务状态显示文本
 * @param {string} status - 任务状态
 */
export const getTaskStatusText = (status) => {
  const statusMap = {
    [TASK_STATUS.SCHEDULED]: '已调度',
    [TASK_STATUS.EXECUTING]: '执行中',
    [TASK_STATUS.COMPLETED]: '已完成',
    [TASK_STATUS.FAILED]: '失败',
    [TASK_STATUS.CANCELLED]: '已取消'
  };
  return statusMap[status] || status;
};

/**
 * 获取任务类型显示文本
 * @param {string} taskType - 任务类型
 */
export const getTaskTypeText = (taskType) => {
  const typeMap = {
    [TASK_TYPE.PMI_WINDOW_OPEN]: 'PMI开启',
    [TASK_TYPE.PMI_WINDOW_CLOSE]: 'PMI关闭'
  };
  return typeMap[taskType] || taskType;
};

/**
 * 获取任务状态颜色
 * @param {string} status - 任务状态
 */
export const getTaskStatusColor = (status) => {
  const colorMap = {
    [TASK_STATUS.SCHEDULED]: 'blue',
    [TASK_STATUS.EXECUTING]: 'orange', 
    [TASK_STATUS.COMPLETED]: 'green',
    [TASK_STATUS.FAILED]: 'red',
    [TASK_STATUS.CANCELLED]: 'gray'
  };
  return colorMap[status] || 'default';
};

/**
 * 检查用户是否有执行任务的权限
 * @param {Object} user - 用户对象
 */
export const canExecuteTask = (user) => {
  return user && user.roles && user.roles.includes('SUPER_ADMIN');
};

/**
 * 检查用户是否有取消任务的权限
 * @param {Object} user - 用户对象
 */
export const canCancelTask = (user) => {
  return user && user.roles && user.roles.includes('SUPER_ADMIN');
};

/**
 * 检查用户是否有重新调度任务的权限
 * @param {Object} user - 用户对象
 */
export const canRescheduleTask = (user) => {
  return user && user.roles && 
         (user.roles.includes('ADMIN') || user.roles.includes('SUPER_ADMIN'));
};

export default pmiTaskApi;
