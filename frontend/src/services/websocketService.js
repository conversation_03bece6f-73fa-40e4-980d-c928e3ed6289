import SockJS from 'sockjs-client';
import { Stomp } from '@stomp/stompjs';
import { message, notification } from 'antd';

/**
 * WebSocket服务
 * 用于PMI任务状态实时推送
 */
class WebSocketService {
  constructor() {
    this.stompClient = null;
    this.connected = false;
    this.subscriptions = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000; // 5秒
    this.callbacks = {
      onTaskStatusChange: [],
      onTaskStatistics: [],
      onSystemAlert: [],
      onConnect: [],
      onDisconnect: []
    };
  }

  /**
   * 连接WebSocket
   */
  connect() {
    if (this.connected) {
      console.log('WebSocket已连接');
      return;
    }

    try {
      const socket = new SockJS('/ws-monitoring');
      this.stompClient = Stomp.over(socket);
      
      // 禁用调试日志
      this.stompClient.debug = () => {};

      this.stompClient.connect(
        {},
        (frame) => {
          console.log('WebSocket连接成功:', frame);
          this.connected = true;
          this.reconnectAttempts = 0;
          
          // 订阅主题
          this.subscribeToTopics();
          
          // 触发连接回调
          this.callbacks.onConnect.forEach(callback => callback());
        },
        (error) => {
          console.error('WebSocket连接失败:', error);
          this.connected = false;
          this.handleReconnect();
        }
      );
    } catch (error) {
      console.error('WebSocket初始化失败:', error);
      this.handleReconnect();
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.stompClient && this.connected) {
      this.stompClient.disconnect(() => {
        console.log('WebSocket已断开连接');
        this.connected = false;
        
        // 触发断开连接回调
        this.callbacks.onDisconnect.forEach(callback => callback());
      });
    }
  }

  /**
   * 订阅主题
   */
  subscribeToTopics() {
    if (!this.stompClient || !this.connected) {
      return;
    }

    // 订阅任务状态变化
    const taskStatusSub = this.stompClient.subscribe('/topic/pmi-task-status', (message) => {
      try {
        const data = JSON.parse(message.body);
        this.handleTaskStatusMessage(data);
      } catch (error) {
        console.error('解析任务状态消息失败:', error);
      }
    });
    this.subscriptions.set('task-status', taskStatusSub);

    // 订阅任务统计
    const statisticsSub = this.stompClient.subscribe('/topic/pmi-task-statistics', (message) => {
      try {
        const data = JSON.parse(message.body);
        this.handleStatisticsMessage(data);
      } catch (error) {
        console.error('解析统计消息失败:', error);
      }
    });
    this.subscriptions.set('statistics', statisticsSub);

    // 订阅系统告警
    const alertSub = this.stompClient.subscribe('/topic/system-alert', (message) => {
      try {
        const data = JSON.parse(message.body);
        this.handleAlertMessage(data);
      } catch (error) {
        console.error('解析告警消息失败:', error);
      }
    });
    this.subscriptions.set('alerts', alertSub);
  }

  /**
   * 处理任务状态消息
   */
  handleTaskStatusMessage(data) {
    console.log('收到任务状态消息:', data);
    
    // 触发任务状态变化回调
    this.callbacks.onTaskStatusChange.forEach(callback => callback(data));
    
    // 显示状态变化通知
    if (data.type === 'TASK_STATUS_CHANGE') {
      this.showTaskStatusNotification(data);
    }
  }

  /**
   * 处理统计消息
   */
  handleStatisticsMessage(data) {
    console.log('收到统计消息:', data);
    
    // 触发统计更新回调
    this.callbacks.onTaskStatistics.forEach(callback => callback(data));
  }

  /**
   * 处理告警消息
   */
  handleAlertMessage(data) {
    console.log('收到告警消息:', data);
    
    // 触发告警回调
    this.callbacks.onSystemAlert.forEach(callback => callback(data));
    
    // 显示告警通知
    this.showAlertNotification(data);
  }

  /**
   * 显示任务状态变化通知
   */
  showTaskStatusNotification(data) {
    const { taskId, status, taskType } = data;
    const taskTypeText = taskType === 'PMI_WINDOW_OPEN' ? 'PMI开启' : 'PMI关闭';
    
    switch (status) {
      case 'COMPLETED':
        message.success(`${taskTypeText}任务执行成功 (ID: ${taskId})`);
        break;
      case 'FAILED':
        message.error(`${taskTypeText}任务执行失败 (ID: ${taskId})`);
        break;
      case 'EXECUTING':
        message.info(`${taskTypeText}任务开始执行 (ID: ${taskId})`);
        break;
      default:
        break;
    }
  }

  /**
   * 显示告警通知
   */
  showAlertNotification(data) {
    const { alertType, message: alertMessage, level } = data;
    
    const config = {
      message: '系统告警',
      description: alertMessage,
      duration: level === 'ERROR' ? 0 : 4.5, // 错误告警不自动关闭
    };

    switch (level) {
      case 'ERROR':
        notification.error(config);
        break;
      case 'WARN':
        notification.warning(config);
        break;
      case 'INFO':
        notification.info(config);
        break;
      default:
        notification.open(config);
        break;
    }
  }

  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`WebSocket重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectInterval);
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数');
      message.error('实时连接已断开，请刷新页面重试');
    }
  }

  /**
   * 注册回调函数
   */
  onTaskStatusChange(callback) {
    this.callbacks.onTaskStatusChange.push(callback);
  }

  onTaskStatistics(callback) {
    this.callbacks.onTaskStatistics.push(callback);
  }

  onSystemAlert(callback) {
    this.callbacks.onSystemAlert.push(callback);
  }

  onConnect(callback) {
    this.callbacks.onConnect.push(callback);
  }

  onDisconnect(callback) {
    this.callbacks.onDisconnect.push(callback);
  }

  /**
   * 移除回调函数
   */
  removeCallback(type, callback) {
    if (this.callbacks[type]) {
      const index = this.callbacks[type].indexOf(callback);
      if (index > -1) {
        this.callbacks[type].splice(index, 1);
      }
    }
  }

  /**
   * 获取连接状态
   */
  isConnected() {
    return this.connected;
  }
}

// 创建单例实例
const webSocketService = new WebSocketService();

export default webSocketService;
