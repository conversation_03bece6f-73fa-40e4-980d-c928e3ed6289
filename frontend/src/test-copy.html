<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试复制功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .meeting-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
        .copy-button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .copy-button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #e6f7ff;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>会议信息复制功能测试</h1>
    
    <div class="meeting-card">
        <h3>测试会议数据</h3>
        <p><strong>主题:</strong> 我的会议@2233</p>
        <p><strong>时间:</strong> 2025-08-11 23:00:00</p>
        <p><strong>时长:</strong> 60分钟</p>
        <p><strong>会议ID:</strong> 86129941267</p>
        <p><strong>密码:</strong> 397281</p>
        <p><strong>加入链接:</strong> https://us06web.zoom.us/j/86129941267?pwd=3x36dzXrVGjj6o3ZOMwrfthc0UhNao.1</p>
        
        <button class="copy-button" onclick="copyZoomInvitation()">复制Zoom邀请格式</button>
        <button class="copy-button" onclick="copySimpleFormat()">复制简洁格式</button>
        <button class="copy-button" onclick="copyActualMeeting()">测试实际会议(85331856926)</button>
    </div>
    
    <div id="result" class="result" style="display: none;"></div>

    <script>
        // 测试会议数据
        const testMeeting = {
            topic: '我的会议@2233',
            startTime: '2025-08-11T23:00:00',
            durationMinutes: 60,
            zoomMeetingId: '86129941267',
            password: '397281',
            joinUrl: 'https://us06web.zoom.us/j/86129941267?pwd=3x36dzXrVGjj6o3ZOMwrfthc0UhNao.1'
        };

        // 用户反馈的实际会议数据
        const actualMeeting = {
            topic: '会议@2256',
            startTime: '2025-08-11T23:00:00',
            durationMinutes: 60,
            zoomMeetingId: '85331856926',
            password: '2025',
            joinUrl: 'https://us06web.zoom.us/j/85331856926?pwd=jJOgWSySKZaDgS6sDw3gVaZIHFeAY0.1'
        };

        // 格式化会议信息为Zoom邀请格式
        function formatZoomInvitation(meeting) {
            // 格式化时间为中文格式
            const formatTime = (dateTime) => {
                if (!dateTime) return '未设置';
                const date = new Date(dateTime);
                const year = date.getFullYear();
                const month = date.getMonth() + 1;
                const day = date.getDate();
                const hour = date.getHours();
                const minute = date.getMinutes().toString().padStart(2, '0');
                return `${year}年${month}月${day}日 ${hour}:${minute} 北京，上海`;
            };

            // 格式化会议号为带空格的格式
            const formatMeetingId = (id) => {
                if (!id) return '未设置';
                // 将会议号按3位分组，用空格分隔
                return id.toString().replace(/(\d{3})(?=\d)/g, '$1 ');
            };

            // 生成加入说明链接（改进版本）
            const generateInstructionUrl = (meetingId, joinUrl) => {
                if (!meetingId) return '未设置';

                // 尝试从joinUrl中提取域名信息
                let baseUrl = 'https://zoom.us';
                if (joinUrl) {
                    try {
                        const url = new URL(joinUrl);
                        baseUrl = `${url.protocol}//${url.hostname}`;
                    } catch (e) {
                        // 如果解析失败，使用默认域名
                    }
                }

                // 生成基本的会议信息链接（不包含动态签名）
                return `${baseUrl}/j/${meetingId}`;
            };

            const invitationText = `Zoom 01邀请你参加已安排的Zoom会议。

主题: ${meeting.topic || '未命名会议'}
时间: ${formatTime(meeting.startTime)}
加入Zoom会议
${meeting.joinUrl || '未设置'}

会议号: ${formatMeetingId(meeting.zoomMeetingId)}
密码: ${meeting.password || '无'}

加入说明
${generateInstructionUrl(meeting.zoomMeetingId, meeting.joinUrl)}`;

            return invitationText;
        }

        // 格式化简洁格式
        function formatSimpleInfo(meeting) {
            const formatTime = (dateTime) => {
                if (!dateTime) return '未设置';
                const date = new Date(dateTime);
                return date.toLocaleString('zh-CN');
            };

            const duration = meeting.durationMinutes || meeting.duration ? `${meeting.durationMinutes || meeting.duration}` : '未设置';

            const invitationText = `
会议主题：${meeting.topic || '未命名会议'}
会议时间：${formatTime(meeting.startTime)}
持续时间：${duration} 分钟
会议ID：${meeting.zoomMeetingId || '未设置'}
会议密码：${meeting.password || '无'}
加入链接：${meeting.joinUrl || '未设置'}
            `.trim();

            return invitationText;
        }

        // 复制Zoom邀请格式
        function copyZoomInvitation() {
            const text = formatZoomInvitation(testMeeting);
            copyToClipboard(text, 'Zoom邀请格式');
        }

        // 复制简洁格式
        function copySimpleFormat() {
            const text = formatSimpleInfo(testMeeting);
            copyToClipboard(text, '简洁格式');
        }

        // 测试实际会议数据
        function copyActualMeeting() {
            const text = formatZoomInvitation(actualMeeting);
            copyToClipboard(text, '实际会议(85331856926)');
        }

        // 复制到剪贴板
        function copyToClipboard(text, format) {
            navigator.clipboard.writeText(text).then(() => {
                showResult(`${format}已复制到剪贴板！\n\n复制的内容：\n${text}`);
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showResult(`${format}已复制到剪贴板！\n\n复制的内容：\n${text}`);
            });
        }

        // 显示结果
        function showResult(text) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = text;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
