<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试会议Webhook更新</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 200px;
            font-family: monospace;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .template-buttons {
            margin-bottom: 15px;
        }
        .template-buttons button {
            background-color: #28a745;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .template-buttons button:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试会议Webhook更新功能</h1>
        <p>这个页面用于测试meeting.updated webhook事件的处理逻辑。</p>
    </div>

    <div class="container">
        <h2>Webhook测试</h2>
        
        <div class="form-group">
            <label>快速模板:</label>
            <div class="template-buttons">
                <button onclick="loadBasicUpdateTemplate()">基本信息更新</button>
                <button onclick="loadRecurringMeetingTemplate()">周期性会议</button>
                <button onclick="loadTimeChangeTemplate()">时间变更</button>
                <button onclick="loadPasswordChangeTemplate()">密码变更</button>
            </div>
        </div>

        <div class="form-group">
            <label for="meetingId">会议ID (用于测试):</label>
            <input type="text" id="meetingId" value="***********" placeholder="输入要测试的会议ID">
        </div>

        <div class="form-group">
            <label for="webhookPayload">Webhook Payload (JSON):</label>
            <textarea id="webhookPayload" placeholder="输入webhook JSON数据..."></textarea>
        </div>

        <button onclick="testMeetingUpdated()">发送测试Webhook</button>
        <button onclick="clearResult()">清除结果</button>

        <div id="result"></div>
    </div>

    <script>
        // 基本信息更新模板
        function loadBasicUpdateTemplate() {
            const meetingId = document.getElementById('meetingId').value || '***********';
            const template = {
                "event": "meeting.updated",
                "payload": {
                    "account_id": "account123",
                    "object": {
                        "id": meetingId,
                        "topic": "更新后的会议主题 - " + new Date().toLocaleString(),
                        "agenda": "更新后的会议议程内容",
                        "duration": 90,
                        "start_time": "2025-08-15T14:00:00Z",
                        "timezone": "America/New_York",
                        "join_url": "https://zoom.us/j/" + meetingId + "?pwd=newpwd",
                        "password": "newpwd123",
                        "type": 2
                    }
                }
            };
            document.getElementById('webhookPayload').value = JSON.stringify(template, null, 2);
        }

        // 周期性会议模板
        function loadRecurringMeetingTemplate() {
            const meetingId = document.getElementById('meetingId').value || '***********';
            const template = {
                "event": "meeting.updated",
                "payload": {
                    "account_id": "account123",
                    "object": {
                        "id": meetingId,
                        "topic": "周期性会议 - 每周例会",
                        "type": 8,
                        "duration": 60,
                        "recurrence": {
                            "type": 2,
                            "repeat_interval": 1,
                            "weekly_days": "1,3,5",
                            "end_times": 10
                        }
                    }
                }
            };
            document.getElementById('webhookPayload').value = JSON.stringify(template, null, 2);
        }

        // 时间变更模板
        function loadTimeChangeTemplate() {
            const meetingId = document.getElementById('meetingId').value || '***********';
            const newTime = new Date();
            newTime.setDate(newTime.getDate() + 7); // 一周后
            const template = {
                "event": "meeting.updated",
                "payload": {
                    "account_id": "account123",
                    "object": {
                        "id": meetingId,
                        "topic": "时间已变更的会议",
                        "start_time": newTime.toISOString(),
                        "duration": 120,
                        "timezone": "Asia/Shanghai"
                    }
                }
            };
            document.getElementById('webhookPayload').value = JSON.stringify(template, null, 2);
        }

        // 密码变更模板
        function loadPasswordChangeTemplate() {
            const meetingId = document.getElementById('meetingId').value || '***********';
            const template = {
                "event": "meeting.updated",
                "payload": {
                    "account_id": "account123",
                    "object": {
                        "id": meetingId,
                        "topic": "密码已更新的会议",
                        "password": "newpassword" + Math.floor(Math.random() * 1000),
                        "join_url": "https://zoom.us/j/" + meetingId + "?pwd=updated"
                    }
                }
            };
            document.getElementById('webhookPayload').value = JSON.stringify(template, null, 2);
        }

        // 测试会议更新webhook
        async function testMeetingUpdated() {
            const payload = document.getElementById('webhookPayload').value;
            const resultDiv = document.getElementById('result');

            if (!payload.trim()) {
                showResult('请输入webhook payload', false);
                return;
            }

            try {
                // 验证JSON格式
                JSON.parse(payload);
            } catch (e) {
                showResult('JSON格式错误: ' + e.message, false);
                return;
            }

            try {
                const response = await fetch('/api/webhooks/test/meeting-updated', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: payload
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult(`✅ 测试成功!\n\n响应数据:\n${JSON.stringify(result, null, 2)}`, true);
                } else {
                    showResult(`❌ 测试失败!\n\n错误信息:\n${JSON.stringify(result, null, 2)}`, false);
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, false);
            }
        }

        // 显示结果
        function showResult(message, isSuccess) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
        }

        // 清除结果
        function clearResult() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = 'result';
        }

        // 页面加载时加载默认模板
        window.onload = function() {
            loadBasicUpdateTemplate();
        };
    </script>
</body>
</html>
