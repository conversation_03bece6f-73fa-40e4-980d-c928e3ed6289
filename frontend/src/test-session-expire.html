<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session过期测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .danger {
            background-color: #ff4d4f;
        }
        .danger:hover {
            background-color: #ff7875;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f6f6f6;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Session过期处理测试</h1>
    
    <div class="test-section">
        <h3>测试说明</h3>
        <p>这个页面用于测试前端session过期时的处理机制。</p>
        <p>当点击下面的按钮时，会模拟不同的HTTP错误状态码，验证前端的错误处理逻辑。</p>
    </div>

    <div class="test-section">
        <h3>模拟403错误（Session过期）</h3>
        <p>点击此按钮会触发403错误，应该弹出"登录已过期"的确认对话框。</p>
        <button onclick="simulate403Error()">模拟403错误</button>
        <div id="result403" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>模拟401错误（未授权）</h3>
        <p>点击此按钮会触发401错误，应该直接跳转到登录页面。</p>
        <button onclick="simulate401Error()">模拟401错误</button>
        <div id="result401" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>模拟其他错误</h3>
        <p>点击此按钮会触发500错误，应该显示普通的错误消息。</p>
        <button onclick="simulate500Error()">模拟500错误</button>
        <div id="result500" class="result" style="display: none;"></div>
    </div>

    <script>
        // 模拟403错误
        function simulate403Error() {
            const result = document.getElementById('result403');
            result.style.display = 'block';
            result.innerHTML = '正在模拟403错误...';
            
            // 模拟发送一个会返回403的请求
            fetch('/api/test-403', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer invalid-token'
                }
            }).then(response => {
                if (response.status === 403) {
                    result.innerHTML = '403错误已触发，应该看到"登录已过期"的确认对话框';
                }
            }).catch(error => {
                result.innerHTML = '请求失败: ' + error.message;
            });
        }

        // 模拟401错误
        function simulate401Error() {
            const result = document.getElementById('result401');
            result.style.display = 'block';
            result.innerHTML = '正在模拟401错误...';
            
            fetch('/api/test-401', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer invalid-token'
                }
            }).then(response => {
                if (response.status === 401) {
                    result.innerHTML = '401错误已触发，应该直接跳转到登录页面';
                }
            }).catch(error => {
                result.innerHTML = '请求失败: ' + error.message;
            });
        }

        // 模拟500错误
        function simulate500Error() {
            const result = document.getElementById('result500');
            result.style.display = 'block';
            result.innerHTML = '正在模拟500错误...';
            
            fetch('/api/test-500', {
                method: 'GET'
            }).then(response => {
                if (response.status === 500) {
                    result.innerHTML = '500错误已触发，应该显示普通的错误消息';
                }
            }).catch(error => {
                result.innerHTML = '请求失败: ' + error.message;
            });
        }
    </script>
</body>
</html>
