/**
 * API路径测试工具
 * 用于验证API路径是否正确配置
 */

import { pmiTaskApi } from '../services/pmiTaskApi';

/**
 * 测试PMI任务API路径
 */
export const testPmiTaskApiPaths = async () => {
  const results = {
    baseUrl: window.location.origin,
    apiTests: []
  };

  // 测试统计API
  try {
    console.log('测试PMI任务统计API...');
    const response = await pmiTaskApi.getTaskStatistics();
    results.apiTests.push({
      name: '任务统计',
      path: '/api/pmi-scheduled-tasks/statistics',
      status: 'success',
      data: response.data
    });
  } catch (error) {
    results.apiTests.push({
      name: '任务统计',
      path: '/api/pmi-scheduled-tasks/statistics',
      status: 'error',
      error: error.message,
      response: error.response?.data
    });
  }

  // 测试即将执行的任务API
  try {
    console.log('测试即将执行的任务API...');
    const response = await pmiTaskApi.getUpcomingTasks(24);
    results.apiTests.push({
      name: '即将执行的任务',
      path: '/api/pmi-scheduled-tasks/upcoming',
      status: 'success',
      data: response.data
    });
  } catch (error) {
    results.apiTests.push({
      name: '即将执行的任务',
      path: '/api/pmi-scheduled-tasks/upcoming',
      status: 'error',
      error: error.message,
      response: error.response?.data
    });
  }

  // 测试任务列表API
  try {
    console.log('测试任务列表API...');
    const response = await pmiTaskApi.getPmiTasks({ page: 1, size: 10 });
    results.apiTests.push({
      name: '任务列表',
      path: '/api/pmi-scheduled-tasks',
      status: 'success',
      data: response.data
    });
  } catch (error) {
    results.apiTests.push({
      name: '任务列表',
      path: '/api/pmi-scheduled-tasks',
      status: 'error',
      error: error.message,
      response: error.response?.data
    });
  }

  return results;
};

/**
 * 在控制台显示API测试结果
 */
export const logApiTestResults = async () => {
  console.group('🔧 PMI任务API路径测试');
  
  const results = await testPmiTaskApiPaths();
  
  console.log('基础URL:', results.baseUrl);
  console.log('测试结果:');
  
  results.apiTests.forEach(test => {
    if (test.status === 'success') {
      console.log(`✅ ${test.name} (${test.path}): 成功`);
    } else {
      console.error(`❌ ${test.name} (${test.path}): 失败`);
      console.error('错误:', test.error);
      if (test.response) {
        console.error('响应:', test.response);
      }
    }
  });
  
  console.groupEnd();
  
  return results;
};

/**
 * 检查API路径配置
 */
export const checkApiConfiguration = () => {
  console.group('🔍 API配置检查');
  
  // 检查基础URL
  const baseUrl = window.location.origin;
  console.log('当前域名:', baseUrl);
  
  // 检查API路径
  const apiPaths = [
    '/api/pmi-scheduled-tasks',
    '/api/pmi-scheduled-tasks/statistics',
    '/api/pmi-scheduled-tasks/upcoming'
  ];
  
  console.log('预期API路径:');
  apiPaths.forEach(path => {
    console.log(`  ${baseUrl}${path}`);
  });
  
  console.groupEnd();
};

/**
 * 生成API测试报告
 */
export const generateApiTestReport = async () => {
  const results = await testPmiTaskApiPaths();
  
  const report = {
    timestamp: new Date().toISOString(),
    baseUrl: results.baseUrl,
    summary: {
      total: results.apiTests.length,
      success: results.apiTests.filter(t => t.status === 'success').length,
      failed: results.apiTests.filter(t => t.status === 'error').length
    },
    details: results.apiTests
  };
  
  console.log('📊 API测试报告:', report);
  
  return report;
};
