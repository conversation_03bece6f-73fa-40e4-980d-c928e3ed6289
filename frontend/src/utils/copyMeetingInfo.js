import { message } from 'antd';
import dayjs from 'dayjs';
import { meetingApi } from '../services/api';

// 邀请信息缓存，缓存时间5分钟
const invitationCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟

/**
 * 清理过期的缓存
 */
const cleanExpiredCache = () => {
  const now = Date.now();
  for (const [key, value] of invitationCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      invitationCache.delete(key);
    }
  }
};

/**
 * 获取缓存的邀请信息
 * @param {string} meetingId - 会议ID
 * @returns {string|null} 缓存的邀请信息
 */
const getCachedInvitation = (meetingId) => {
  cleanExpiredCache();
  const cached = invitationCache.get(meetingId);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.invitation;
  }
  return null;
};

/**
 * 缓存邀请信息
 * @param {string} meetingId - 会议ID
 * @param {string} invitation - 邀请信息
 */
const setCachedInvitation = (meetingId, invitation) => {
  invitationCache.set(meetingId, {
    invitation,
    timestamp: Date.now()
  });
};

/**
 * 简化邀请信息，移除iCalendar相关内容
 * @param {string} invitationText - 原始邀请信息
 * @returns {string} 简化后的邀请信息
 */
const simplifyInvitationText = (invitationText) => {
  if (!invitationText) return invitationText;

  // 移除iCalendar相关的内容
  let simplified = invitationText;

  // 移除iCalendar文件下载链接（包括多行内容）
  simplified = simplified.replace(/下载iCalendar文件[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');
  simplified = simplified.replace(/Download iCalendar file[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');

  // 移除包含"或者，复制此URL到您的日历应用程序中"的段落
  simplified = simplified.replace(/或者，复制此URL到您的日历应用程序中：[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');
  simplified = simplified.replace(/Or copy this URL into your calendar application:[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');

  // 移除iCalendar相关的URL（包括ics链接）
  simplified = simplified.replace(/https?:\/\/[^\s]*\.ics[^\s]*/gi, '');
  simplified = simplified.replace(/https?:\/\/[^\s]*icsToken[^\s]*/gi, '');

  // 移除包含"icalendar"、"ics"、"calendar"等关键词的行
  simplified = simplified.replace(/.*(?:icalendar|\.ics|calendar file|日历文件|icsToken).*\n?/gi, '');

  // 移除空行（只包含空白字符的行）
  simplified = simplified.replace(/^\s*\n/gm, '');

  // 移除多余的空行（超过2个连续换行符的情况）
  simplified = simplified.replace(/\n{3,}/g, '\n\n');

  // 移除开头和结尾的空白字符
  simplified = simplified.trim();

  return simplified;
};

/**
 * 检查是否为多天会议
 * @param {Object} meeting - 会议对象
 * @returns {boolean} 是否为多天会议
 */
const isMultiDayMeeting = (meeting) => {
  // 检查是否为周期性会议
  if (meeting.type === 8 || meeting.type === 9) { // RECURRING_FIXED_TIME 或 RECURRING_NO_FIXED_TIME
    return true;
  }

  // 检查是否有recurrence信息
  if (meeting.recurrence || meeting.isRecurring) {
    return true;
  }

  // 检查会议时长是否超过24小时（1440分钟）
  const duration = meeting.durationMinutes || meeting.duration;
  if (duration && duration > 1440) {
    return true;
  }

  return false;
};

/**
 * 统一的复制会议邀请信息方法
 * @param {Object} meeting - 会议对象
 * @param {Object} occurrence - occurrence对象（可选，用于周期性会议）
 * @returns {Promise<void>}
 */
export const copyMeetingInvitation = async (meeting, occurrence = null) => {
  try {
    let invitationText = null;
    const meetingId = meeting.id;

    // 首先检查缓存
    const cachedInvitation = getCachedInvitation(meetingId);
    if (cachedInvitation) {
      console.log('使用缓存的邀请信息');
      invitationText = cachedInvitation;
    } else {
      // 尝试从API获取最新的邀请信息
      try {
        console.log('正在获取最新的邀请信息...');
        const response = await meetingApi.getMeetingInvitation(meetingId);

        if (response.data.success && response.data.data && response.data.data.invitation) {
          invitationText = response.data.data.invitation;

          // 对于多天会议，简化邀请信息（移除iCalendar相关内容）
          if (isMultiDayMeeting(meeting)) {
            console.log('检测到多天会议，简化邀请信息');
            invitationText = simplifyInvitationText(invitationText);
          }

          // 缓存邀请信息
          setCachedInvitation(meetingId, invitationText);
          console.log('成功获取并缓存Zoom API邀请信息');
        } else {
          throw new Error(response.data.message || '获取邀请信息失败');
        }
      } catch (apiError) {
        console.warn('获取Zoom API邀请信息失败:', apiError);

        // 提供更详细的错误信息
        let errorMessage = '获取最新邀请信息失败';
        if (apiError.response) {
          const status = apiError.response.status;
          if (status === 404) {
            errorMessage = '会议不存在或已被删除';
          } else if (status === 401) {
            errorMessage = 'Zoom认证失效，请联系管理员';
          } else if (status >= 500) {
            errorMessage = '服务器暂时不可用，请稍后重试';
          } else {
            errorMessage = `获取邀请信息失败 (${status})`;
          }
        }

        message.warning(`${errorMessage}，将使用本地信息生成邀请`);
      }
    }

    // 格式化邀请信息（如果API调用失败，会使用默认格式）
    const finalInvitationText = formatZoomInvitation(meeting, occurrence, invitationText);

    // 复制到剪贴板
    await copyToClipboard(finalInvitationText);
    message.success('会议邀请信息已复制到剪贴板');

  } catch (error) {
    console.error('复制会议信息失败:', error);

    // 最终降级方案：使用本地信息
    try {
      const fallbackText = formatZoomInvitation(meeting, occurrence);
      await copyToClipboard(fallbackText);
      message.success('会议邀请信息已复制到剪贴板（使用本地信息）');
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError);
      message.error('复制失败，请检查浏览器权限或手动复制会议信息');
    }
  }
};

/**
 * 复制会议参会信息到剪贴板（简洁版本，参考会议管理界面）
 * @param {Object} meeting - 会议对象
 */
export const copyMeetingInfo = async (meeting) => {
  return copyMeetingInvitation(meeting);
};

/**
 * 复制文本到剪贴板的通用方法
 * @param {string} text - 要复制的文本
 * @returns {Promise<void>}
 */
const copyToClipboard = async (text) => {
  try {
    // 优先使用现代的 Clipboard API
    await navigator.clipboard.writeText(text);
  } catch (error) {
    // 降级方案：使用传统的复制方法
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand('copy');
    } finally {
      document.body.removeChild(textArea);
    }
  }
};

/**
 * 格式化会议信息为文本（简洁版本，参考会议管理界面）
 * @param {Object} meeting - 会议对象
 * @param {Object} occurrence - occurrence对象（可选）
 * @returns {string} 格式化后的会议信息
 */
const formatMeetingInfo = (meeting, occurrence = null) => {
  // 如果有occurrence信息，优先使用occurrence的时间
  let startTime;
  if (occurrence && occurrence.occurrenceStartTime) {
    startTime = dayjs(occurrence.occurrenceStartTime).format('YYYY-MM-DD HH:mm:ss');
  } else if (meeting.startTime) {
    startTime = dayjs(meeting.startTime).format('YYYY-MM-DD HH:mm:ss');
  } else {
    startTime = '未设置';
  }

  // 持续时间优先使用occurrence的，然后是meeting的
  const duration = (occurrence && occurrence.duration) ||
                  meeting.durationMinutes ||
                  meeting.duration ||
                  '未设置';

  const invitationText = `
会议主题：${meeting.topic || '未命名会议'}
会议时间：${startTime}
持续时间：${duration} 分钟
会议ID：${meeting.zoomMeetingId || '未设置'}
会议密码：${meeting.password || '无'}
加入链接：${meeting.joinUrl || '未设置'}
  `.trim();

  return invitationText;
};

/**
 * 格式化会议信息为Zoom邀请格式
 * @param {Object} meeting - 会议对象
 * @param {Object} occurrence - occurrence对象（可选）
 * @param {string} invitationText - 从Zoom API获取的邀请文本（可选）
 * @returns {string} Zoom邀请格式的会议信息
 */
export const formatZoomInvitation = (meeting, occurrence = null, invitationText = null) => {
  // 如果有从API获取的邀请文本，直接使用
  if (invitationText) {
    return invitationText;
  }

  // 否则使用默认格式
  // 格式化时间为中文格式
  const formatTime = (dateTime) => {
    if (!dateTime) return '未设置';
    const date = dayjs(dateTime);
    return date.format('YYYY年M月D日 HH:mm') + ' 北京，上海';
  };

  // 格式化会议号为带空格的格式
  const formatMeetingId = (id) => {
    if (!id) return '未设置';
    // 将会议号按3位分组，用空格分隔
    return id.toString().replace(/(\d{3})(?=\d)/g, '$1 ');
  };

  // 生成加入说明链接（降级方案，使用更合理的格式）
  const generateInstructionUrl = (meetingId, joinUrl) => {
    if (!meetingId) return '未设置';

    // 尝试从joinUrl中提取域名信息
    let baseUrl = 'https://zoom.us';
    if (joinUrl) {
      try {
        const url = new URL(joinUrl);
        baseUrl = `${url.protocol}//${url.hostname}`;
      } catch (e) {
        // 如果解析失败，使用默认域名
      }
    }

    // 生成基本的会议信息链接（不包含动态签名）
    return `${baseUrl}/j/${meetingId}`;
  };

  // 确定使用的时间（优先使用occurrence的时间）
  const meetingTime = (occurrence && occurrence.occurrenceStartTime) || meeting.startTime;

  const defaultInvitationText = `Zoom 01邀请你参加已安排的Zoom会议。

主题: ${meeting.topic || '未命名会议'}
时间: ${formatTime(meetingTime)}
加入Zoom会议
${meeting.joinUrl || '未设置'}

会议号: ${formatMeetingId(meeting.zoomMeetingId)}
密码: ${meeting.password || '无'}

加入说明
${generateInstructionUrl(meeting.zoomMeetingId, meeting.joinUrl)}`;

  return defaultInvitationText;
};

/**
 * 格式化简短的会议信息（用于快速分享）
 * @param {Object} meeting - 会议对象
 * @returns {string} 简短的会议信息
 */
export const formatShortMeetingInfo = (meeting) => {
  const startTime = meeting.startTime 
    ? dayjs(meeting.startTime).format('MM-DD HH:mm')
    : '待定';
  
  let info = `${meeting.topic || '会议'} `;
  info += `${startTime} `;
  info += `ID:${meeting.zoomMeetingId || '无'}`;
  
  if (meeting.password) {
    info += ` 密码:${meeting.password}`;
  }
  
  return info;
};
