import { message } from 'antd';
import dayjs from 'dayjs';

/**
 * 复制会议参会信息到剪贴板（简洁版本，参考会议管理界面）
 * @param {Object} meeting - 会议对象
 */
export const copyMeetingInfo = async (meeting) => {
  try {
    // 格式化会议信息
    const meetingInfo = formatMeetingInfo(meeting);
    
    // 复制到剪贴板
    await navigator.clipboard.writeText(meetingInfo);
    
    // 显示成功消息
    message.success('会议信息已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    
    // 降级方案：使用传统的复制方法
    try {
      const textArea = document.createElement('textarea');
      textArea.value = formatMeetingInfo(meeting);
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      
      message.success('会议信息已复制到剪贴板');
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError);
      message.error('复制失败，请手动复制会议信息');
    }
  }
};

/**
 * 格式化会议信息为文本（简洁版本，参考会议管理界面）
 * @param {Object} meeting - 会议对象
 * @returns {string} 格式化后的会议信息
 */
const formatMeetingInfo = (meeting) => {
  const startTime = meeting.startTime 
    ? dayjs(meeting.startTime).format('YYYY-MM-DD HH:mm:ss')
    : '未设置';
  
  const duration = meeting.duration ? `${meeting.duration}` : '未设置';
  
  const invitationText = `
会议主题：${meeting.topic || '未命名会议'}
会议时间：${startTime}
持续时间：${duration} 分钟
加入链接：${meeting.joinUrl || '未设置'}
会议密码：${meeting.password || '无'}
会议ID：${meeting.zoomMeetingId || '未设置'}
  `.trim();
  
  return invitationText;
};

/**
 * 格式化简短的会议信息（用于快速分享）
 * @param {Object} meeting - 会议对象
 * @returns {string} 简短的会议信息
 */
export const formatShortMeetingInfo = (meeting) => {
  const startTime = meeting.startTime 
    ? dayjs(meeting.startTime).format('MM-DD HH:mm')
    : '待定';
  
  let info = `${meeting.topic || '会议'} `;
  info += `${startTime} `;
  info += `ID:${meeting.zoomMeetingId || '无'}`;
  
  if (meeting.password) {
    info += ` 密码:${meeting.password}`;
  }
  
  return info;
};
