/**
 * 移动端工具函数
 */

/**
 * 检测是否为移动设备
 */
export const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * 检测屏幕尺寸是否为移动端
 */
export const isMobileScreen = () => {
  return window.innerWidth <= 768;
};

/**
 * 检测是否为触摸设备
 */
export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

/**
 * 获取设备类型
 */
export const getDeviceType = () => {
  if (isMobileDevice() || isMobileScreen()) {
    return 'mobile';
  }
  if (window.innerWidth <= 1024) {
    return 'tablet';
  }
  return 'desktop';
};

/**
 * 获取安全区域信息（用于适配刘海屏等）
 */
export const getSafeAreaInsets = () => {
  const style = getComputedStyle(document.documentElement);
  return {
    top: parseInt(style.getPropertyValue('--sat') || '0', 10),
    right: parseInt(style.getPropertyValue('--sar') || '0', 10),
    bottom: parseInt(style.getPropertyValue('--sab') || '0', 10),
    left: parseInt(style.getPropertyValue('--sal') || '0', 10),
  };
};

/**
 * 防抖函数
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 节流函数
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 复制文本到剪贴板
 */
export const copyToClipboard = async (text) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('复制失败:', error);
    return false;
  }
};

/**
 * 获取网络状态
 */
export const getNetworkStatus = () => {
  if ('connection' in navigator) {
    const connection = navigator.connection;
    return {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData
    };
  }
  return null;
};

/**
 * 检测是否为慢网络
 */
export const isSlowNetwork = () => {
  const networkStatus = getNetworkStatus();
  if (!networkStatus) return false;
  
  return networkStatus.effectiveType === 'slow-2g' || 
         networkStatus.effectiveType === '2g' ||
         networkStatus.saveData;
};

/**
 * 获取电池状态
 */
export const getBatteryStatus = async () => {
  try {
    if ('getBattery' in navigator) {
      const battery = await navigator.getBattery();
      return {
        level: battery.level,
        charging: battery.charging,
        chargingTime: battery.chargingTime,
        dischargingTime: battery.dischargingTime
      };
    }
  } catch (error) {
    console.warn('无法获取电池状态:', error);
  }
  return null;
};

/**
 * 检测是否为低电量模式
 */
export const isLowPowerMode = async () => {
  const battery = await getBatteryStatus();
  return battery && battery.level < 0.2 && !battery.charging;
};

/**
 * 移动端友好的时间格式化
 */
export const formatMobileTime = (date) => {
  const now = new Date();
  const target = new Date(date);
  const diffMs = now - target;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  if (diffMins < 1) return '刚刚';
  if (diffMins < 60) return `${diffMins}分钟前`;
  if (diffHours < 24) return `${diffHours}小时前`;
  if (diffDays < 7) return `${diffDays}天前`;
  
  // 超过一周显示具体日期
  const month = target.getMonth() + 1;
  const day = target.getDate();
  const hour = target.getHours();
  const minute = target.getMinutes();
  
  return `${month}月${day}日 ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
};

/**
 * 移动端友好的数字格式化
 */
export const formatMobileNumber = (num) => {
  if (num < 1000) return num.toString();
  if (num < 10000) return (num / 1000).toFixed(1) + 'K';
  if (num < 1000000) return (num / 10000).toFixed(1) + 'W';
  return (num / 1000000).toFixed(1) + 'M';
};

/**
 * 触觉反馈（如果支持）
 */
export const hapticFeedback = (type = 'light') => {
  try {
    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30],
        success: [10, 50, 10],
        error: [50, 50, 50],
        warning: [20, 20, 20]
      };
      navigator.vibrate(patterns[type] || patterns.light);
    }
  } catch (error) {
    console.warn('触觉反馈不支持:', error);
  }
};

/**
 * 检测设备方向
 */
export const getDeviceOrientation = () => {
  if (window.screen && window.screen.orientation) {
    return window.screen.orientation.angle;
  }
  return window.orientation || 0;
};

/**
 * 检测是否为横屏
 */
export const isLandscape = () => {
  const orientation = getDeviceOrientation();
  return Math.abs(orientation) === 90;
};

/**
 * 移动端滚动到顶部
 */
export const scrollToTop = (smooth = true) => {
  window.scrollTo({
    top: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};

/**
 * 移动端滚动到元素
 */
export const scrollToElement = (element, offset = 0) => {
  if (typeof element === 'string') {
    element = document.querySelector(element);
  }
  
  if (element) {
    const elementTop = element.offsetTop - offset;
    window.scrollTo({
      top: elementTop,
      behavior: 'smooth'
    });
  }
};

/**
 * 防止移动端双击缩放
 */
export const preventDoubleClickZoom = (element) => {
  let lastTouchEnd = 0;
  element.addEventListener('touchend', (event) => {
    const now = new Date().getTime();
    if (now - lastTouchEnd <= 300) {
      event.preventDefault();
    }
    lastTouchEnd = now;
  }, false);
};

/**
 * 移动端安全的事件监听器
 */
export const addMobileEventListener = (element, event, handler, options = {}) => {
  const defaultOptions = {
    passive: true,
    ...options
  };
  
  element.addEventListener(event, handler, defaultOptions);
  
  return () => {
    element.removeEventListener(event, handler, defaultOptions);
  };
};
