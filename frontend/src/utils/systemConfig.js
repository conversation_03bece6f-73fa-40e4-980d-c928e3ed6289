import { systemConfigApi } from '../services/api';

/**
 * 系统配置管理工具
 * 提供统一的配置获取和缓存机制
 */
class SystemConfigManager {
  constructor() {
    this.configCache = new Map();
    this.cacheExpiry = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    this.loadingPromises = new Map(); // 防止重复请求
  }

  /**
   * 获取单个配置值
   * @param {string} configKey - 配置键
   * @param {string} defaultValue - 默认值
   * @returns {Promise<string>} 配置值
   */
  async getConfig(configKey, defaultValue = '') {
    // 检查缓存
    if (this.isConfigCached(configKey)) {
      return this.configCache.get(configKey);
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(configKey)) {
      return await this.loadingPromises.get(configKey);
    }

    // 创建加载Promise
    const loadingPromise = this.loadConfigFromServer(configKey, defaultValue);
    this.loadingPromises.set(configKey, loadingPromise);

    try {
      const result = await loadingPromise;
      return result;
    } finally {
      this.loadingPromises.delete(configKey);
    }
  }

  /**
   * 批量获取配置
   * @param {string[]} configKeys - 配置键数组
   * @returns {Promise<Object>} 配置对象
   */
  async getConfigs(configKeys) {
    const configs = {};
    const uncachedKeys = [];

    // 检查缓存
    for (const key of configKeys) {
      if (this.isConfigCached(key)) {
        configs[key] = this.configCache.get(key);
      } else {
        uncachedKeys.push(key);
      }
    }

    // 加载未缓存的配置
    if (uncachedKeys.length > 0) {
      try {
        const response = await systemConfigApi.getConfigs();
        if (response.data.success) {
          const serverConfigs = {};
          response.data.data.content.forEach(config => {
            serverConfigs[config.configKey] = config.configValue;
          });

          // 更新缓存和结果
          for (const key of uncachedKeys) {
            const value = serverConfigs[key] || '';
            this.setConfigCache(key, value);
            configs[key] = value;
          }
        }
      } catch (error) {
        console.error('批量获取系统配置失败:', error);
        // 为未缓存的配置设置空值
        for (const key of uncachedKeys) {
          configs[key] = '';
        }
      }
    }

    return configs;
  }

  /**
   * 从服务器加载单个配置
   */
  async loadConfigFromServer(configKey, defaultValue) {
    try {
      const response = await systemConfigApi.getConfigValues(configKey);
      if (response.data.success && response.data.data[configKey]) {
        const value = response.data.data[configKey];
        this.setConfigCache(configKey, value);
        return value;
      }
    } catch (error) {
      console.error(`获取配置 ${configKey} 失败:`, error);
    }

    // 返回默认值并缓存
    this.setConfigCache(configKey, defaultValue);
    return defaultValue;
  }

  /**
   * 检查配置是否已缓存且未过期
   */
  isConfigCached(configKey) {
    if (!this.configCache.has(configKey)) {
      return false;
    }

    const expiry = this.cacheExpiry.get(configKey);
    if (!expiry || Date.now() > expiry) {
      this.configCache.delete(configKey);
      this.cacheExpiry.delete(configKey);
      return false;
    }

    return true;
  }

  /**
   * 设置配置缓存
   */
  setConfigCache(configKey, value) {
    this.configCache.set(configKey, value);
    this.cacheExpiry.set(configKey, Date.now() + this.cacheTimeout);
  }

  /**
   * 清除指定配置的缓存
   */
  clearConfigCache(configKey) {
    this.configCache.delete(configKey);
    this.cacheExpiry.delete(configKey);
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.configCache.clear();
    this.cacheExpiry.clear();
    this.loadingPromises.clear();
  }

  /**
   * 获取管理台基础域名
   */
  async getFrontendBaseUrl() {
    return await this.getConfig('frontend.domain.base_url', 'http://localhost:3000');
  }

  /**
   * 获取用户前端基础域名
   */
  async getUserFrontendBaseUrl() {
    return await this.getConfig('user_frontend.domain.base_url', 'http://localhost:3001');
  }

  /**
   * 获取WebSocket开关状态
   */
  async getWebSocketEnabled() {
    const value = await this.getConfig('dashboard.websocket.enabled', 'true');
    return value === 'true';
  }
}

// 创建全局实例
const systemConfigManager = new SystemConfigManager();

export default systemConfigManager;

// 导出便捷方法
export const getSystemConfig = (configKey, defaultValue) => 
  systemConfigManager.getConfig(configKey, defaultValue);

export const getSystemConfigs = (configKeys) => 
  systemConfigManager.getConfigs(configKeys);

export const getFrontendBaseUrl = () => 
  systemConfigManager.getFrontendBaseUrl();

export const getUserFrontendBaseUrl = () => 
  systemConfigManager.getUserFrontendBaseUrl();

export const getWebSocketEnabled = () => 
  systemConfigManager.getWebSocketEnabled();

export const clearConfigCache = (configKey) => 
  systemConfigManager.clearConfigCache(configKey);

export const clearAllConfigCache = () => 
  systemConfigManager.clearAllCache();
