/**
 * TraceId工具函数
 * 提供TraceId相关的工具方法和开发者增强功能
 */

// TraceId生成器
export const generateTraceId = () => {
  const now = new Date();
  // 使用本地时间生成时间戳，格式：yyyyMMddHHmmssSSS
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;

  const nodeId = '001'; // 前端节点ID
  const sequence = String(Math.floor(Math.random() * 1000000)).padStart(6, '0');
  const random = Math.random().toString(36).substring(2, 8);
  return `${timestamp}-${nodeId}-${sequence}-${random}`;
};

// 获取当前TraceId
export const getCurrentTraceId = () => {
  return sessionStorage.getItem('currentTraceId');
};

// 生成新的TraceId
export const generateNewTraceId = () => {
  const traceId = generateTraceId();
  sessionStorage.setItem('currentTraceId', traceId);
  console.log(`🆕 New TraceId generated: ${traceId}`);
  return traceId;
};

// 验证TraceId格式
export const isValidTraceId = (traceId) => {
  if (!traceId || typeof traceId !== 'string') {
    return false;
  }
  
  // 标准格式：yyyyMMddHHmmss-nnn-nnnnnn-xxxxxx
  // Webhook格式：WH-yyyyMMddHHmmss-SOURCE-EVENT-xxxxxx
  const parts = traceId.split('-');
  
  if (parts.length < 4) {
    return false;
  }
  
  // 检查时间戳部分
  const timestampPart = parts[0] === 'WH' ? parts[1] : parts[0];
  if (timestampPart.length !== 14) {
    return false;
  }
  
  // 检查时间戳是否为有效数字
  return /^\d{14}$/.test(timestampPart);
};

// 从TraceId中提取时间戳
export const extractTimestamp = (traceId) => {
  if (!isValidTraceId(traceId)) {
    return null;
  }
  
  try {
    const parts = traceId.split('-');
    const timestampPart = parts[0] === 'WH' ? parts[1] : parts[0];
    
    // 解析时间戳：yyyyMMddHHmmss
    const year = parseInt(timestampPart.substring(0, 4));
    const month = parseInt(timestampPart.substring(4, 6)) - 1; // 月份从0开始
    const day = parseInt(timestampPart.substring(6, 8));
    const hour = parseInt(timestampPart.substring(8, 10));
    const minute = parseInt(timestampPart.substring(10, 12));
    const second = parseInt(timestampPart.substring(12, 14));
    
    return new Date(year, month, day, hour, minute, second);
  } catch (error) {
    console.warn('无法从TraceId中提取时间戳:', traceId, error);
    return null;
  }
};

// 判断是否为Webhook TraceId
export const isWebhookTraceId = (traceId) => {
  return traceId && traceId.startsWith('WH-');
};

// 格式化TraceId显示
export const formatTraceIdForDisplay = (traceId, maxLength = 20) => {
  if (!traceId) return '';
  
  if (traceId.length <= maxLength) {
    return traceId;
  }
  
  // 显示前几位和后几位，中间用...省略
  const prefixLength = Math.floor((maxLength - 3) / 2);
  const suffixLength = maxLength - 3 - prefixLength;
  
  return `${traceId.substring(0, prefixLength)}...${traceId.substring(traceId.length - suffixLength)}`;
};

// 复制TraceId到剪贴板
export const copyTraceIdToClipboard = (traceId) => {
  if (!traceId) {
    console.warn('TraceId为空，无法复制');
    return Promise.reject(new Error('TraceId为空'));
  }
  
  if (navigator.clipboard && navigator.clipboard.writeText) {
    return navigator.clipboard.writeText(traceId);
  } else {
    // 降级方案
    return new Promise((resolve, reject) => {
      const textArea = document.createElement('textarea');
      textArea.value = traceId;
      document.body.appendChild(textArea);
      textArea.select();
      
      try {
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
          resolve();
        } else {
          reject(new Error('复制命令执行失败'));
        }
      } catch (err) {
        document.body.removeChild(textArea);
        reject(err);
      }
    });
  }
};

// 搜索日志（模拟功能，实际需要调用后端API）
export const searchLogs = (traceId) => {
  console.log(`🔍 Searching logs for TraceId: ${traceId}`);
  // 这里可以集成到日志查询系统
  // 实际实现时应该调用后端的日志搜索API
};

// 开发环境增强功能
if (process.env.NODE_ENV === 'development') {
  // 在window对象上暴露traceId相关方法
  window.traceUtils = {
    getCurrentTraceId,
    generateNewTraceId,
    searchLogs,
    isValidTraceId,
    extractTimestamp,
    isWebhookTraceId,
    formatTraceIdForDisplay,
    copyTraceIdToClipboard
  };
  
  // 添加快捷键支持
  document.addEventListener('keydown', (e) => {
    // Ctrl+Shift+T 显示当前TraceId
    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
      const traceId = getCurrentTraceId();
      if (traceId) {
        console.log(`📋 Current TraceId: ${traceId}`);
        copyTraceIdToClipboard(traceId).then(() => {
          console.log('✅ TraceId copied to clipboard');
        }).catch((err) => {
          console.error('❌ Failed to copy TraceId:', err);
        });
      } else {
        console.log('❌ No current TraceId found');
      }
    }
  });
  
  console.log('🔧 TraceId开发者工具已加载');
  console.log('💡 使用 window.traceUtils 访问TraceId工具方法');
  console.log('⌨️  使用 Ctrl+Shift+T 快速查看并复制当前TraceId');
}
