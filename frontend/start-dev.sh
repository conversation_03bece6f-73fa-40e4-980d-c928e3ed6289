#!/bin/bash

# 前端开发服务器启动脚本

echo "🎨 启动前端开发服务器"
echo "===================="

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装"
    exit 1
fi

echo "✓ Node.js版本: $(node --version)"
echo "✓ npm版本: $(npm --version)"

# 检查并安装依赖
if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
    echo ""
    echo "📦 安装/更新依赖..."
    npm install
fi

echo ""
echo "🚀 启动开发服务器..."

# 设置环境变量
export BROWSER=none
export PORT=3000
export REACT_APP_ENV=development

# 启动开发服务器
npm start
