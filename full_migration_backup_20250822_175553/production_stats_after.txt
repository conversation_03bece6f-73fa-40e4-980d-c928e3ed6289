step
=== 迁移后生产环境数据统计 ===
table_name	record_count
Table	Count
t_users	625
t_pmi_records	626
t_pmi_schedules	940
t_pmi_schedule_windows	1046
step
=== 数据完整性检查 ===
check_type	issue	count
Foreign Key Check	PMI Records without Users	0
Foreign Key Check	Schedules without PMI Records	0
Foreign Key Check	Windows without Schedules	0
Foreign Key Check	Windows without PMI Records	0
step
=== PMI 184 计划分布检查 ===
schedule_id	schedule_name	window_count
201	2023-06-25 ~ 2023-07-25 重复	1
287	2023-07-26 ~ 2023-08-26 重复	1
347	2023-08-25 ~ 2023-09-25 重复	1
434	2023-09-25 ~ 2023-10-25 重复	1
474	2023-10-25 ~ 2023-11-25 重复	1
519	2023-11-25 ~ 2023-12-25 重复	1
550	2023-12-26 ~ 2024-01-26 重复	1
572	2024-01-26 ~ 2024-02-26 重复	1
590	2024-02-26 ~ 2024-03-26 重复	1
638	2024-03-26 ~ 2024-04-26 重复	1
666	2024-04-26 ~ 2024-05-25 重复	1
690	2024-05-25 ~ 2024-06-25 重复	1
715	2024-06-25 ~ 2024-07-25 重复	1
739	2024-07-25 ~ 2024-08-25 重复	1
757	2024-08-25 ~ 2024-09-25 重复	1
771	2024-09-25 ~ 2024-10-25 重复	1
784	2024-10-25 ~ 2024-11-25 重复	1
815	2024-11-25 ~ 2024-12-25 重复	1
832	2024-12-25 ~ 2025-01-25 重复	1
845	2025-01-25 ~ 2025-02-25 重复	1
856	2025-02-25 ~ 2025-03-25 重复	1
872	2025-03-25 ~ 2025-04-25 重复	1
886	2025-04-25 ~ 2025-05-25 重复	1
900	2025-05-25 ~ 2025-06-25 重复	1
912	2025-06-25 ~ 2025-07-25 重复	1
927	2025-07-25 ~ 2025-08-25 重复	1
