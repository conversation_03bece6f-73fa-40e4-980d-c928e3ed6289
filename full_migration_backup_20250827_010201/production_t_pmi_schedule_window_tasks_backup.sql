-- MySQL dump 10.13  Distrib 5.7.18, for Linux (x86_64)
--
-- Host: localhost    Database: zoombusV
-- ------------------------------------------------------
-- Server version	5.7.18-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `t_pmi_schedule_window_tasks`
--

DROP TABLE IF EXISTS `t_pmi_schedule_window_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_pmi_schedule_window_tasks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `actual_execution_time` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `error_message` text,
  `pmi_window_id` bigint(20) NOT NULL,
  `retry_count` int(11) DEFAULT NULL,
  `scheduled_time` datetime(6) NOT NULL,
  `status` varchar(255) NOT NULL,
  `task_key` varchar(255) NOT NULL,
  `task_type` varchar(255) NOT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_cct3gb6jvdel8vkcyy18mkqmm` (`task_key`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_pmi_schedule_window_tasks`
--

LOCK TABLES `t_pmi_schedule_window_tasks` WRITE;
/*!40000 ALTER TABLE `t_pmi_schedule_window_tasks` DISABLE KEYS */;
INSERT INTO `t_pmi_schedule_window_tasks` (`id`, `actual_execution_time`, `created_at`, `error_message`, `pmi_window_id`, `retry_count`, `scheduled_time`, `status`, `task_key`, `task_type`, `updated_at`) VALUES (1,'2025-08-23 12:11:00.025580','2025-08-23 12:08:38.154874',NULL,2059,0,'2025-08-23 12:11:00.000000','COMPLETED','PMI_OPEN_2059_1755922118152','PMI_WINDOW_OPEN','2025-08-23 12:11:00.061494'),(2,'2025-08-23 13:11:00.007385','2025-08-23 12:08:38.157964',NULL,2059,0,'2025-08-23 13:11:00.000000','COMPLETED','PMI_CLOSE_2059_1755922118157','PMI_WINDOW_CLOSE','2025-08-23 13:11:00.029817'),(3,'2025-08-24 01:25:09.947261','2025-08-24 01:25:09.902487',NULL,2061,0,'2025-08-24 01:25:00.000000','COMPLETED','PMI_OPEN_2061_1755969909900','PMI_WINDOW_OPEN','2025-08-24 01:25:10.112930'),(4,'2025-08-24 04:25:00.005238','2025-08-24 01:25:09.906311',NULL,2061,0,'2025-08-24 04:25:00.000000','COMPLETED','PMI_CLOSE_2061_1755969909906','PMI_WINDOW_CLOSE','2025-08-24 04:25:00.017027'),(5,'2025-08-27 00:53:39.860431','2025-08-24 07:31:24.574530','任务过期，窗口已关闭，延迟3922分钟',2062,1,'2025-08-24 07:31:00.000000','FAILED','PMI_OPEN_2062_1755991884574','PMI_WINDOW_OPEN','2025-08-27 00:53:39.895083'),(6,'2025-08-24 08:31:00.004243','2025-08-24 07:31:24.575531',NULL,2062,0,'2025-08-24 08:31:00.000000','COMPLETED','PMI_CLOSE_2062_1755991884575','PMI_WINDOW_CLOSE','2025-08-24 08:31:00.007257'),(7,'2025-08-24 18:55:00.013526','2025-08-24 18:52:48.962539',NULL,2063,0,'2025-08-24 18:55:00.000000','COMPLETED','PMI_OPEN_2063_1756032768962','PMI_WINDOW_OPEN','2025-08-24 18:55:00.025659'),(8,'2025-08-24 19:55:00.004357','2025-08-24 18:52:48.963577',NULL,2063,0,'2025-08-24 19:55:00.000000','COMPLETED','PMI_CLOSE_2063_1756032768963','PMI_WINDOW_CLOSE','2025-08-24 19:55:00.013651'),(9,'2025-08-26 10:21:00.006292','2025-08-26 10:18:45.727994',NULL,2064,0,'2025-08-26 10:21:00.000000','COMPLETED','PMI_OPEN_2064_1756174725727','PMI_WINDOW_OPEN','2025-08-26 10:21:00.050582'),(10,'2025-08-26 11:21:00.009876','2025-08-26 10:18:45.734643',NULL,2064,0,'2025-08-26 11:21:00.000000','COMPLETED','PMI_CLOSE_2064_1756174725734','PMI_WINDOW_CLOSE','2025-08-26 11:21:00.038975'),(11,'2025-08-27 00:53:39.975060','2025-08-26 10:29:59.647433','任务过期，窗口已关闭，延迟1493分钟',2065,1,'2025-08-26 00:00:00.000000','FAILED','PMI_OPEN_2065_1756175399647','PMI_WINDOW_OPEN','2025-08-27 00:53:39.979099'),(12,'2025-08-26 23:59:00.003545','2025-08-26 10:29:59.648388',NULL,2065,0,'2025-08-26 23:59:00.000000','COMPLETED','PMI_CLOSE_2065_1756175399648','PMI_WINDOW_CLOSE','2025-08-26 23:59:00.006285');
/*!40000 ALTER TABLE `t_pmi_schedule_window_tasks` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-27  0:59:16
