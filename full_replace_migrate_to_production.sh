#!/bin/bash

# 完整替换式生产环境数据迁移脚本
# 清空生产环境目标表，然后完整迁移测试环境数据
# 迁移5张表：t_users, t_pmi_records, t_pmi_schedules, t_pmi_schedule_windows, t_pmi_schedule_window_tasks
# 执行日期: 2025-08-27

set -e

# 配置变量
MYSQL_CMD="/usr/local/mysql-8.0.11-macos10.13-x86_64/bin/mysql"
MYSQLDUMP_CMD="/usr/local/mysql-8.0.11-macos10.13-x86_64/bin/mysqldump"

TEST_DB_HOST="localhost"
TEST_DB_USER="root"
TEST_DB_PASS="nvshen2018"
TEST_DB_NAME="zoombusV"

PROD_SERVER="<EMAIL>"
PROD_DB_HOST="localhost"
PROD_DB_USER="root"
PROD_DB_PASS="nvshen2018"
PROD_DB_NAME="zoombusV"

# 需要迁移的表（按外键依赖顺序排列）
TABLES_TO_MIGRATE=(
    "t_pmi_schedule_window_tasks"  # 最后删除（依赖窗口表）
    "t_pmi_schedule_windows"       # 第二删除（有外键依赖）
    "t_pmi_schedules"              # 第三删除
    "t_pmi_records"                # 第四删除
    "t_users"                      # 最先删除（被其他表依赖）
)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./full_migration_backup_${TIMESTAMP}"
EXPORT_DIR="./full_migration_export_${TIMESTAMP}"

# 创建目录
create_directories() {
    log_info "创建工作目录..."
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$EXPORT_DIR"
    log_success "目录创建完成: $BACKUP_DIR, $EXPORT_DIR"
}

# 备份生产环境数据
backup_production_data() {
    log_info "备份生产环境数据..."
    
    cd "$BACKUP_DIR"
    
    # 备份每个表的数据
    for table in "${TABLES_TO_MIGRATE[@]}"; do
        log_info "备份生产环境表: $table"
        ssh "$PROD_SERVER" "mysqldump -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' \
            --single-transaction --complete-insert \
            '$PROD_DB_NAME' '$table'" > "production_${table}_backup.sql"
    done
    
    cd ..
    log_success "生产环境数据备份完成"
}

# 导出测试环境数据
export_test_data() {
    log_info "导出测试环境数据..."
    
    cd "$EXPORT_DIR"
    
    # 导出每个表的数据
    for table in "${TABLES_TO_MIGRATE[@]}"; do
        log_info "导出测试环境表: $table"
        "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
            --no-create-info --complete-insert --single-transaction \
            --where="1=1" "$TEST_DB_NAME" "$table" > "test_${table}.sql"
        
        # 检查导出的记录数
        local count=$(grep -c "INSERT INTO" "test_${table}.sql" 2>/dev/null || echo "0")
        log_info "测试环境 $table: $count 条记录"
    done
    
    cd ..
    log_success "测试环境数据导出完成"
}

# 获取生产环境当前数据统计
get_production_stats() {
    log_info "获取生产环境当前数据统计..."
    
    ssh "$PROD_SERVER" "mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' <<EOF
SELECT '=== 迁移前生产环境数据统计 ===' as step;
SELECT 
    'Table' as table_name,
    'Count' as record_count
UNION ALL
SELECT 't_users', COUNT(*) FROM t_users
UNION ALL
SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
UNION ALL
SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
UNION ALL
SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows
UNION ALL
SELECT 't_pmi_schedule_window_tasks', COUNT(*) FROM t_pmi_schedule_window_tasks;
EOF" > "$BACKUP_DIR/production_stats_before.txt"
    
    log_success "生产环境数据统计获取完成"
}

# 清空生产环境表数据
clear_production_tables() {
    log_info "清空生产环境表数据..."
    
    ssh "$PROD_SERVER" "mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' <<'EOF'
-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 按依赖关系顺序清空表（先清空依赖表）
DELETE FROM t_pmi_schedule_window_tasks;
DELETE FROM t_pmi_schedule_windows;
DELETE FROM t_pmi_schedules;
DELETE FROM t_pmi_records;
DELETE FROM t_users;

-- 重置自增ID
ALTER TABLE t_pmi_schedule_window_tasks AUTO_INCREMENT = 1;
ALTER TABLE t_pmi_schedule_windows AUTO_INCREMENT = 1;
ALTER TABLE t_pmi_schedules AUTO_INCREMENT = 1;
ALTER TABLE t_pmi_records AUTO_INCREMENT = 1;
ALTER TABLE t_users AUTO_INCREMENT = 1;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证清空结果
SELECT '=== 清空后数据统计 ===' as step;
SELECT 
    'Table' as table_name,
    'Count' as record_count
UNION ALL
SELECT 't_users', COUNT(*) FROM t_users
UNION ALL
SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
UNION ALL
SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
UNION ALL
SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows
UNION ALL
SELECT 't_pmi_schedule_window_tasks', COUNT(*) FROM t_pmi_schedule_window_tasks;
EOF"
    
    log_success "生产环境表数据清空完成"
}

# 上传并导入测试环境数据
import_test_data() {
    log_info "上传并导入测试环境数据到生产环境..."
    
    local remote_temp_dir="/tmp/full_migration_${TIMESTAMP}"
    
    # 在生产服务器创建临时目录
    ssh "$PROD_SERVER" "mkdir -p $remote_temp_dir"
    
    # 上传测试环境数据文件
    scp "$EXPORT_DIR"/test_*.sql "$PROD_SERVER:$remote_temp_dir/"
    
    # 在生产服务器上按正确顺序执行导入
    ssh "$PROD_SERVER" "
        echo '开始导入测试环境数据...'
        
        # 禁用外键检查
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            -e 'SET FOREIGN_KEY_CHECKS = 0;'
        
        # 按依赖关系顺序导入（先导入被依赖的表）
        echo '导入用户数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/test_t_users.sql'
        
        echo '导入PMI记录数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/test_t_pmi_records.sql'
        
        echo '导入PMI计划数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/test_t_pmi_schedules.sql'
        
        echo '导入PMI窗口数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/test_t_pmi_schedule_windows.sql'

        echo '导入PMI窗口任务数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/test_t_pmi_schedule_window_tasks.sql'

        # 启用外键检查
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            -e 'SET FOREIGN_KEY_CHECKS = 1;'
        
        echo '测试环境数据导入完成'
        
        # 清理远程临时文件
        rm -rf '$remote_temp_dir'
    "
    
    log_success "测试环境数据导入完成"
}

# 验证迁移结果
verify_migration() {
    log_info "验证迁移结果..."
    
    echo "=========================================="
    echo "迁移结果验证"
    echo "=========================================="
    
    # 获取迁移后生产环境数据统计
    ssh "$PROD_SERVER" "mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' <<EOF
SELECT '=== 迁移后生产环境数据统计 ===' as step;
SELECT 
    'Table' as table_name,
    'Count' as record_count
UNION ALL
SELECT 't_users', COUNT(*) FROM t_users
UNION ALL
SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
UNION ALL
SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
UNION ALL
SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows
UNION ALL
SELECT 't_pmi_schedule_window_tasks', COUNT(*) FROM t_pmi_schedule_window_tasks;

SELECT '=== 数据完整性检查 ===' as step;

-- 检查外键关联完整性
SELECT 
    'Foreign Key Check' as check_type,
    'PMI Records without Users' as issue,
    COUNT(*) as count
FROM t_pmi_records pr
LEFT JOIN t_users u ON pr.user_id = u.id
WHERE u.id IS NULL

UNION ALL

SELECT 
    'Foreign Key Check' as check_type,
    'Schedules without PMI Records' as issue,
    COUNT(*) as count
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_records pr ON ps.pmi_record_id = pr.id
WHERE pr.id IS NULL

UNION ALL

SELECT 
    'Foreign Key Check' as check_type,
    'Windows without Schedules' as issue,
    COUNT(*) as count
FROM t_pmi_schedule_windows psw
LEFT JOIN t_pmi_schedules ps ON psw.schedule_id = ps.id
WHERE ps.id IS NULL

UNION ALL

SELECT 
    'Foreign Key Check' as check_type,
    'Windows without PMI Records' as issue,
    COUNT(*) as count
FROM t_pmi_schedule_windows psw
LEFT JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE pr.id IS NULL

UNION ALL

SELECT
    'Foreign Key Check' as check_type,
    'Tasks without Windows' as issue,
    COUNT(*) as count
FROM t_pmi_schedule_window_tasks t
LEFT JOIN t_pmi_schedule_windows psw ON t.pmi_window_id = psw.id
WHERE psw.id IS NULL;

-- 检查任务状态分布
SELECT '=== 任务状态分布检查 ===' as step;
SELECT
    status,
    task_type,
    COUNT(*) as count
FROM t_pmi_schedule_window_tasks
GROUP BY status, task_type
ORDER BY status, task_type;

-- 检查PMI 184的计划分布
SELECT '=== PMI 184 计划分布检查 ===' as step;
SELECT 
    ps.id as schedule_id,
    ps.name as schedule_name,
    COUNT(psw.id) as window_count
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE ps.pmi_record_id = 184
GROUP BY ps.id, ps.name
ORDER BY ps.id;
EOF" > "$BACKUP_DIR/production_stats_after.txt"
    
    # 获取测试环境数据统计进行对比
    "$MYSQL_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" "$TEST_DB_NAME" -e "
    SELECT '=== 测试环境数据统计（对比参考） ===' as step;
    SELECT 
        'Table' as table_name,
        'Count' as record_count
    UNION ALL
    SELECT 't_users', COUNT(*) FROM t_users
    UNION ALL
    SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
    UNION ALL
    SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
    UNION ALL
    SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows
    UNION ALL
    SELECT 't_pmi_schedule_window_tasks', COUNT(*) FROM t_pmi_schedule_window_tasks;
    " > "$BACKUP_DIR/test_stats_reference.txt"
    
    # 显示对比结果
    echo "生产环境迁移后统计:"
    cat "$BACKUP_DIR/production_stats_after.txt"
    
    echo ""
    echo "测试环境统计（参考）:"
    cat "$BACKUP_DIR/test_stats_reference.txt"
    
    log_success "迁移结果验证完成"
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    if [ "$1" != "keep" ]; then
        rm -rf "$EXPORT_DIR"
        log_success "临时导出文件清理完成"
    else
        log_warning "保留导出文件: $EXPORT_DIR"
    fi
    
    log_warning "备份文件保留: $BACKUP_DIR"
}

# 主函数
main() {
    echo "=========================================="
    echo "完整替换式生产环境数据迁移脚本"
    echo "目标服务器: $PROD_SERVER"
    echo "=========================================="
    echo ""
    log_warning "⚠️  警告：此操作将完全清空生产环境的以下表："
    for table in "${TABLES_TO_MIGRATE[@]}"; do
        echo "   - $table"
    done
    echo ""
    log_warning "⚠️  然后用测试环境数据完全替换！"
    echo ""
    
    # 确认操作
    read -p "确认要执行完整替换迁移吗？这将清空生产环境数据！(yes/NO): " confirm
    if [[ ! $confirm =~ ^[Yy][Ee][Ss]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 二次确认
    read -p "再次确认：您真的要清空生产环境数据并替换吗？(yes/NO): " confirm2
    if [[ ! $confirm2 =~ ^[Yy][Ee][Ss]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行完整替换迁移步骤
    create_directories
    get_production_stats
    backup_production_data
    export_test_data
    clear_production_tables
    import_test_data
    verify_migration
    
    # 清理
    if [[ " $* " =~ " --keep-files " ]]; then
        cleanup "keep"
    else
        cleanup
    fi
    
    log_success "完整替换式数据迁移完成！"
    log_info "备份文件位置: $BACKUP_DIR"
}

# 错误处理
trap 'log_error "脚本执行失败，请检查备份文件进行恢复: $BACKUP_DIR"; exit 1' ERR

# 执行主函数
main "$@"
