# Git Push 脚本使用说明

本工程提供了两个Git推送脚本，用于在设置代理的情况下推送代码到远程仓库。

## 📁 脚本文件

### 1. `git-push.sh` - 完整功能版本
功能丰富的Git推送脚本，包含完整的错误处理和交互功能。

### 2. `git-push-simple.sh` - 简化版本
简单直接的推送脚本，适合快速使用。

## 🔧 代理配置

两个脚本都会自动设置以下代理：
```bash
export https_proxy=http://127.0.0.1:6690
export http_proxy=http://127.0.0.1:6690
export all_proxy=socks5://127.0.0.1:6690
```

## 🚀 使用方法

### 完整功能版本 (`git-push.sh`)

#### 基本使用
```bash
# 推送到main分支（默认）
./git-push.sh

# 推送到指定分支
./git-push.sh -b develop
./git-push.sh --branch feature/new-feature
```

#### 高级选项
```bash
# 跳过代理设置
./git-push.sh --no-proxy

# 强制提交当前更改
./git-push.sh -f
./git-push.sh --force-commit

# 查看帮助信息
./git-push.sh -h
./git-push.sh --help
```

#### 组合使用
```bash
# 强制提交并推送到develop分支
./git-push.sh -f -b develop

# 不使用代理推送到main分支
./git-push.sh --no-proxy
```

### 简化版本 (`git-push-simple.sh`)

```bash
# 推送到main分支
./git-push-simple.sh

# 推送到指定分支
./git-push-simple.sh develop
```

## ✨ 功能特性

### 完整功能版本特性

#### 🎨 彩色输出
- 🔵 信息提示（蓝色）
- 🟢 成功消息（绿色）
- 🟡 警告信息（黄色）
- 🔴 错误信息（红色）

#### 🔍 智能检查
- ✅ 检查是否在Git仓库中
- ✅ 检查未提交的更改
- ✅ 检查远程仓库连接
- ✅ 显示Git状态和最新提交

#### 🤝 交互功能
- 📝 提示提交未保存的更改
- 💬 自定义提交信息输入
- 🔄 自动添加所有文件选项
- ⚡ 默认提交信息（时间戳）

#### 🛡️ 错误处理
- 🔧 自动清理代理设置
- 🚨 详细的错误信息
- 🔄 失败时的回滚机制

### 简化版本特性
- ⚡ 快速执行
- 🎯 专注核心功能
- 📊 显示Git状态
- 🧹 自动清理代理

## 📋 使用场景

### 日常开发推送
```bash
# 快速推送（简化版）
./git-push-simple.sh

# 或者使用完整版
./git-push.sh
```

### 功能分支推送
```bash
# 推送功能分支
./git-push.sh -b feature/user-management

# 强制提交并推送
./git-push.sh -f -b feature/pmi-optimization
```

### 无代理环境
```bash
# 在不需要代理的环境中使用
./git-push.sh --no-proxy
```

## 🔧 自定义配置

### 修改代理设置
如需修改代理地址，请编辑脚本中的以下行：
```bash
export https_proxy=http://127.0.0.1:6690
export http_proxy=http://127.0.0.1:6690
export all_proxy=socks5://127.0.0.1:6690
```

### 修改默认分支
在脚本中找到以下行并修改：
```bash
local branch="main"  # 改为您的默认分支
```

## 🚨 注意事项

1. **代理设置**：脚本会临时设置系统代理，执行完成后会自动清理
2. **Git配置**：脚本会设置Git全局代理配置，完成后会清理
3. **权限要求**：确保脚本有执行权限（`chmod +x`）
4. **网络环境**：确保代理服务器正常运行
5. **Git仓库**：必须在Git仓库目录中执行

## 🐛 故障排除

### 常见问题

#### 1. 权限错误
```bash
chmod +x git-push.sh
chmod +x git-push-simple.sh
```

#### 2. 代理连接失败
- 检查代理服务器是否运行
- 确认代理端口号是否正确
- 尝试使用 `--no-proxy` 选项

#### 3. Git推送失败
- 检查远程仓库URL
- 确认Git认证信息
- 检查分支名称是否正确

#### 4. 网络超时
- 增加Git超时设置：
```bash
git config --global http.timeout 300
```

## 📝 日志和调试

### 启用详细输出
```bash
# 设置Git详细输出
export GIT_CURL_VERBOSE=1
export GIT_TRACE=1

# 然后运行脚本
./git-push.sh
```

### 查看代理状态
```bash
# 检查当前代理设置
echo "HTTP Proxy: $http_proxy"
echo "HTTPS Proxy: $https_proxy"
echo "All Proxy: $all_proxy"
```

## 🔄 更新和维护

定期检查和更新脚本：
1. 根据网络环境调整代理设置
2. 根据项目需求修改默认分支
3. 添加项目特定的检查逻辑
4. 优化错误处理和用户体验

---

**提示**：建议在首次使用前先在测试分支上验证脚本功能。
