#!/bin/bash

# 简化版Git Push脚本
# 设置代理并推送到远程仓库

echo "🚀 Git Push Script - 设置代理并推送代码"
echo "=========================================="

# 设置代理
echo "📡 设置网络代理..."
export https_proxy=http://127.0.0.1:6690
export http_proxy=http://127.0.0.1:6690
export all_proxy=socks5://127.0.0.1:6690

# 设置Git代理
git config --global http.proxy http://127.0.0.1:6690
git config --global https.proxy http://127.0.0.1:6690

echo "✅ 代理设置完成"

# 获取分支名称（默认为main）
BRANCH=${1:-main}

echo "📋 当前Git状态："
git status

echo ""
echo "🔄 推送到远程仓库 origin/$BRANCH..."

# 执行推送
if git push origin "$BRANCH"; then
    echo "✅ 推送成功！"
else
    echo "❌ 推送失败"
    exit 1
fi

# 清理代理设置
echo "🧹 清理代理设置..."
unset https_proxy
unset http_proxy
unset all_proxy
git config --global --unset http.proxy 2>/dev/null
git config --global --unset https.proxy 2>/dev/null

echo "✅ 完成！"
