#!/bin/bash

# Git Push Script with Proxy Configuration
# 用于推送代码到远程仓库的脚本，包含代理设置

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置代理
setup_proxy() {
    print_info "设置网络代理..."
    export https_proxy=http://127.0.0.1:6690
    export http_proxy=http://127.0.0.1:6690
    export all_proxy=socks5://127.0.0.1:6690
    
    # 设置Git代理
    git config --global http.proxy http://127.0.0.1:6690
    git config --global https.proxy http://127.0.0.1:6690
    
    print_success "代理设置完成"
    print_info "HTTP Proxy: $http_proxy"
    print_info "HTTPS Proxy: $https_proxy"
    print_info "All Proxy: $all_proxy"
}

# 清理代理设置
cleanup_proxy() {
    print_info "清理代理设置..."
    unset https_proxy
    unset http_proxy
    unset all_proxy
    
    # 清理Git代理
    git config --global --unset http.proxy 2>/dev/null
    git config --global --unset https.proxy 2>/dev/null
    
    print_success "代理设置已清理"
}

# 检查Git状态
check_git_status() {
    print_info "检查Git状态..."
    
    # 检查是否在Git仓库中
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "当前目录不是Git仓库"
        exit 1
    fi
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        print_warning "检测到未提交的更改："
        git status --porcelain
        echo
        read -p "是否要先提交这些更改？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            commit_changes
        else
            print_warning "跳过提交，继续推送..."
        fi
    else
        print_success "工作目录干净，没有未提交的更改"
    fi
}

# 提交更改
commit_changes() {
    print_info "准备提交更改..."
    
    # 显示当前状态
    git status
    echo
    
    # 询问是否添加所有文件
    read -p "是否添加所有更改的文件？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git add .
        print_success "已添加所有文件"
    else
        print_info "请手动添加需要提交的文件"
        return 1
    fi
    
    # 输入提交信息
    echo -n "请输入提交信息: "
    read commit_message
    
    if [ -z "$commit_message" ]; then
        commit_message="Update: $(date '+%Y-%m-%d %H:%M:%S')"
        print_warning "使用默认提交信息: $commit_message"
    fi
    
    # 执行提交
    if git commit -m "$commit_message"; then
        print_success "提交成功: $commit_message"
    else
        print_error "提交失败"
        return 1
    fi
}

# 推送到远程仓库
push_to_remote() {
    local branch=${1:-main}
    
    print_info "准备推送到远程仓库..."
    print_info "目标分支: origin/$branch"
    
    # 检查远程仓库连接
    print_info "检查远程仓库连接..."
    if ! git ls-remote origin > /dev/null 2>&1; then
        print_error "无法连接到远程仓库，请检查网络和代理设置"
        return 1
    fi
    
    print_success "远程仓库连接正常"
    
    # 执行推送
    print_info "正在推送到 origin/$branch..."
    if git push origin "$branch"; then
        print_success "推送成功！"
        
        # 显示最新的提交信息
        print_info "最新提交信息："
        git log --oneline -5
    else
        print_error "推送失败"
        return 1
    fi
}

# 主函数
main() {
    echo "======================================"
    echo "       Git Push Script v1.0"
    echo "======================================"
    echo
    
    # 解析命令行参数
    local branch="main"
    local skip_proxy=false
    local force_commit=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -b|--branch)
                branch="$2"
                shift 2
                ;;
            --no-proxy)
                skip_proxy=true
                shift
                ;;
            -f|--force-commit)
                force_commit=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  -b, --branch BRANCH    指定推送的分支 (默认: main)"
                echo "  --no-proxy            跳过代理设置"
                echo "  -f, --force-commit    强制提交当前更改"
                echo "  -h, --help            显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                echo "使用 -h 或 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
    
    # 设置错误处理
    set -e
    trap cleanup_proxy EXIT
    
    # 设置代理（如果需要）
    if [ "$skip_proxy" = false ]; then
        setup_proxy
    else
        print_warning "跳过代理设置"
    fi
    
    # 检查Git状态
    check_git_status
    
    # 强制提交（如果指定）
    if [ "$force_commit" = true ]; then
        commit_changes
    fi
    
    # 推送到远程仓库
    push_to_remote "$branch"
    
    print_success "所有操作完成！"
}

# 运行主函数
main "$@"
