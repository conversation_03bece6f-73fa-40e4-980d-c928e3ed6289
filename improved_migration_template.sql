-- 改进的移植脚本模板
-- 修复窗口创建逻辑，防止错误关闭问题
-- 创建日期: 2025-08-20

-- ========================================
-- 改进的窗口创建逻辑
-- ========================================

-- 问题分析：
-- 原始移植脚本的问题：
-- 1. 所有窗口的window_date都设置为移植日(CURDATE())
-- 2. 这导致在移植日23:59:59时，系统可能错误地认为这些窗口应该被关闭
-- 
-- 解决方案：
-- 1. 对于长期计划，window_date应该设置为计划的start_date
-- 2. 窗口状态应该基于正确的时间逻辑来判断
-- 3. 添加额外的验证和安全检查

-- 改进的窗口创建逻辑示例：
/*
INSERT INTO t_pmi_schedule_windows (
    schedule_id,
    pmi_record_id,
    window_date,
    end_date,
    start_time,
    end_time,
    status,
    zoom_user_id,
    created_at,
    updated_at
)
SELECT 
    schedule.id as schedule_id,
    schedule.pmi_record_id,
    -- 修复：使用计划的开始日期作为窗口日期，而不是移植日
    schedule.start_date as window_date,
    -- 窗口结束日期使用计划的结束日期
    schedule.end_date as end_date,
    '00:00:00' as start_time,
    '23:59:59' as end_time,
    -- 改进的状态判断逻辑
    CASE 
        -- 如果计划结束日期已过，则窗口为完成状态
        WHEN schedule.end_date < CURDATE() THEN 'COMPLETED'
        -- 如果计划结束日期是今天，且当前时间已过结束时间，则窗口为完成状态
        WHEN schedule.end_date = CURDATE() AND CURTIME() >= '23:59:59' THEN 'COMPLETED'
        -- 如果计划开始日期是今天或已过，且结束日期在未来，则窗口为活跃状态
        WHEN schedule.start_date <= CURDATE() AND schedule.end_date >= CURDATE() THEN 'ACTIVE'
        -- 如果计划开始日期在未来，则窗口为待开启状态
        WHEN schedule.start_date > CURDATE() THEN 'PENDING'
        -- 默认为待开启状态
        ELSE 'PENDING'
    END as status,
    NULL as zoom_user_id,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules schedule
JOIN t_pmi_records pmi ON schedule.pmi_record_id = pmi.id
WHERE pmi.billing_mode = 'LONG'
AND NOT EXISTS (
    SELECT 1 FROM t_pmi_schedule_windows w 
    WHERE w.schedule_id = schedule.id
);
*/

-- ========================================
-- 窗口关闭逻辑验证函数
-- ========================================

-- 创建一个函数来验证窗口关闭逻辑
DELIMITER //
DROP FUNCTION IF EXISTS validate_window_close_logic_v2 //
CREATE FUNCTION validate_window_close_logic_v2(
    p_window_id BIGINT,
    p_current_date DATE,
    p_current_time TIME
) RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_window_date DATE;
    DECLARE v_end_date DATE;
    DECLARE v_start_time TIME;
    DECLARE v_end_time TIME;
    DECLARE v_status VARCHAR(50);
    DECLARE v_should_close BOOLEAN DEFAULT FALSE;
    DECLARE v_reason VARCHAR(500);
    DECLARE v_condition1 BOOLEAN DEFAULT FALSE;
    DECLARE v_condition2 BOOLEAN DEFAULT FALSE;
    DECLARE v_condition3 BOOLEAN DEFAULT FALSE;
    DECLARE v_result JSON;
    
    -- 获取窗口信息
    SELECT window_date, end_date, start_time, end_time, status
    INTO v_window_date, v_end_date, v_start_time, v_end_time, v_status
    FROM t_pmi_schedule_windows
    WHERE id = p_window_id;
    
    -- 检查窗口是否存在
    IF v_window_date IS NULL THEN
        SET v_result = JSON_OBJECT(
            'window_id', p_window_id,
            'should_close', FALSE,
            'reason', 'WINDOW_NOT_FOUND',
            'error', TRUE
        );
        RETURN v_result;
    END IF;
    
    -- 检查窗口状态
    IF v_status != 'ACTIVE' THEN
        SET v_result = JSON_OBJECT(
            'window_id', p_window_id,
            'should_close', FALSE,
            'reason', CONCAT('WINDOW_NOT_ACTIVE: ', v_status),
            'status', v_status,
            'error', FALSE
        );
        RETURN v_result;
    END IF;
    
    -- 应用关闭逻辑
    -- 条件1：endDate IS NULL AND currentDate = windowDate AND currentTime >= endTime
    SET v_condition1 = (v_end_date IS NULL AND p_current_date = v_window_date AND p_current_time >= v_end_time);
    
    -- 条件2：endDate IS NOT NULL AND currentDate > endDate
    SET v_condition2 = (v_end_date IS NOT NULL AND p_current_date > v_end_date);
    
    -- 条件3：endDate IS NOT NULL AND currentDate = endDate AND currentTime >= endTime
    SET v_condition3 = (v_end_date IS NOT NULL AND p_current_date = v_end_date AND p_current_time >= v_end_time);
    
    SET v_should_close = (v_condition1 OR v_condition2 OR v_condition3);
    
    -- 确定关闭原因
    IF v_condition1 THEN
        SET v_reason = 'SINGLE_DAY_WINDOW_EXPIRED';
    ELSEIF v_condition2 THEN
        SET v_reason = 'MULTI_DAY_WINDOW_PAST_END_DATE';
    ELSEIF v_condition3 THEN
        SET v_reason = 'MULTI_DAY_WINDOW_END_TIME_REACHED';
    ELSE
        SET v_reason = 'WINDOW_NOT_EXPIRED';
    END IF;
    
    -- 额外的安全检查
    IF v_should_close AND v_end_date IS NOT NULL THEN
        -- 对于有end_date的窗口，确保当前日期确实超过了end_date或者在end_date当天且时间超过了end_time
        IF NOT (p_current_date > v_end_date OR (p_current_date = v_end_date AND p_current_time >= v_end_time)) THEN
            SET v_should_close = FALSE;
            SET v_reason = CONCAT(v_reason, '_BUT_SAFETY_CHECK_FAILED');
        END IF;
    END IF;
    
    SET v_result = JSON_OBJECT(
        'window_id', p_window_id,
        'should_close', v_should_close,
        'reason', v_reason,
        'condition1', v_condition1,
        'condition2', v_condition2,
        'condition3', v_condition3,
        'current_date', p_current_date,
        'current_time', p_current_time,
        'window_date', v_window_date,
        'end_date', v_end_date,
        'start_time', v_start_time,
        'end_time', v_end_time,
        'status', v_status,
        'error', FALSE
    );
    
    RETURN v_result;
END //
DELIMITER ;

-- ========================================
-- 移植前验证脚本
-- ========================================

-- 在执行移植前，运行此脚本验证数据的正确性
/*
-- 1. 检查计划数据的完整性
SELECT 
    'schedule_data_check' as check_type,
    COUNT(*) as total_schedules,
    COUNT(CASE WHEN start_date IS NULL THEN 1 END) as null_start_date,
    COUNT(CASE WHEN end_date IS NULL THEN 1 END) as null_end_date,
    COUNT(CASE WHEN start_date > end_date THEN 1 END) as invalid_date_range
FROM t_pmi_schedules;

-- 2. 检查即将创建的窗口状态分布
SELECT 
    'window_status_preview' as check_type,
    CASE 
        WHEN schedule.end_date < CURDATE() THEN 'COMPLETED'
        WHEN schedule.end_date = CURDATE() AND CURTIME() >= '23:59:59' THEN 'COMPLETED'
        WHEN schedule.start_date <= CURDATE() AND schedule.end_date >= CURDATE() THEN 'ACTIVE'
        WHEN schedule.start_date > CURDATE() THEN 'PENDING'
        ELSE 'PENDING'
    END as predicted_status,
    COUNT(*) as count
FROM t_pmi_schedules schedule
JOIN t_pmi_records pmi ON schedule.pmi_record_id = pmi.id
WHERE pmi.billing_mode = 'LONG'
GROUP BY predicted_status;

-- 3. 检查可能有问题的计划
SELECT 
    'problematic_schedules' as check_type,
    id,
    start_date,
    end_date,
    DATEDIFF(end_date, start_date) as duration_days,
    CASE 
        WHEN start_date > end_date THEN 'INVALID_DATE_RANGE'
        WHEN end_date < CURDATE() THEN 'ALREADY_EXPIRED'
        WHEN DATEDIFF(end_date, start_date) > 365 THEN 'VERY_LONG_DURATION'
        ELSE 'OK'
    END as issue
FROM t_pmi_schedules
WHERE start_date > end_date 
   OR end_date < CURDATE() 
   OR DATEDIFF(end_date, start_date) > 365
LIMIT 10;
*/

-- ========================================
-- 移植后验证脚本
-- ========================================

-- 在执行移植后，运行此脚本验证结果的正确性
/*
-- 1. 验证窗口状态的正确性
SELECT 
    'window_status_validation' as check_type,
    status,
    COUNT(*) as count,
    MIN(end_date) as min_end_date,
    MAX(end_date) as max_end_date
FROM t_pmi_schedule_windows
GROUP BY status;

-- 2. 检查可能被错误关闭的窗口
SELECT 
    'potentially_incorrect_windows' as check_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows
WHERE status = 'COMPLETED' 
AND end_date > CURDATE();

-- 3. 验证窗口关闭逻辑
SELECT 
    'close_logic_test' as check_type,
    id,
    validate_window_close_logic_v2(id, CURDATE(), CURTIME()) as validation_result
FROM t_pmi_schedule_windows
WHERE status = 'ACTIVE'
LIMIT 5;
*/

-- ========================================
-- 最佳实践建议
-- ========================================

/*
移植最佳实践：

1. 数据准备阶段：
   - 验证源数据的完整性和正确性
   - 检查日期范围的合理性
   - 确保计划的开始和结束日期逻辑正确

2. 窗口创建阶段：
   - 使用计划的start_date作为window_date，而不是移植日期
   - 基于正确的时间逻辑判断窗口状态
   - 添加额外的验证和安全检查

3. 移植后验证：
   - 检查窗口状态分布是否合理
   - 验证关闭逻辑是否正确
   - 确保没有窗口被错误关闭

4. 监控和日志：
   - 增强日志记录，便于问题排查
   - 添加监控告警，及时发现异常
   - 定期检查窗口状态的正确性

5. 回滚准备：
   - 在移植前备份相关数据
   - 准备回滚脚本
   - 测试回滚流程的可行性
*/
