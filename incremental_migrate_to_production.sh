#!/bin/bash

# 增量生产环境数据迁移脚本
# 只迁移测试环境中新增的数据到生产环境
# 执行日期: 2025-08-21

set -e

# 配置变量
MYSQL_CMD="/usr/local/mysql-8.0.11-macos10.13-x86_64/bin/mysql"
MYSQLDUMP_CMD="/usr/local/mysql-8.0.11-macos10.13-x86_64/bin/mysqldump"

TEST_DB_HOST="localhost"
TEST_DB_USER="root"
TEST_DB_PASS="nvshen2018"
TEST_DB_NAME="zoombusV"

PROD_SERVER="<EMAIL>"
PROD_DB_HOST="localhost"
PROD_DB_USER="root"
PROD_DB_PASS="nvshen2018"
PROD_DB_NAME="zoombusV"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./incremental_backup_${TIMESTAMP}"
EXPORT_DIR="./incremental_export_${TIMESTAMP}"

# 创建目录
create_directories() {
    log_info "创建工作目录..."
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$EXPORT_DIR"
    log_success "目录创建完成: $BACKUP_DIR, $EXPORT_DIR"
}

# 获取生产环境最大ID
get_production_max_ids() {
    log_info "获取生产环境最大ID..."
    
    ssh "$PROD_SERVER" "mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' <<EOF
SELECT 'Production Max IDs' as info_type;
SELECT 
    'users' as table_name,
    COALESCE(MAX(id), 0) as max_id
FROM t_users
UNION ALL
SELECT 
    'pmi_records' as table_name,
    COALESCE(MAX(id), 0) as max_id
FROM t_pmi_records
UNION ALL
SELECT 
    'schedules' as table_name,
    COALESCE(MAX(id), 0) as max_id
FROM t_pmi_schedules
UNION ALL
SELECT 
    'windows' as table_name,
    COALESCE(MAX(id), 0) as max_id
FROM t_pmi_schedule_windows;
EOF" > "$EXPORT_DIR/production_max_ids.txt"
    
    log_success "生产环境最大ID获取完成"
}

# 导出增量数据
export_incremental_data() {
    log_info "导出测试环境增量数据..."
    
    # 从生产环境最大ID文件中提取ID
    local max_user_id=$(grep "users" "$EXPORT_DIR/production_max_ids.txt" | awk '{print $2}')
    local max_pmi_id=$(grep "pmi_records" "$EXPORT_DIR/production_max_ids.txt" | awk '{print $2}')
    local max_schedule_id=$(grep "schedules" "$EXPORT_DIR/production_max_ids.txt" | awk '{print $2}')
    local max_window_id=$(grep "windows" "$EXPORT_DIR/production_max_ids.txt" | awk '{print $2}')
    
    log_info "生产环境最大ID: users=$max_user_id, pmi_records=$max_pmi_id, schedules=$max_schedule_id, windows=$max_window_id"
    
    cd "$EXPORT_DIR"
    
    # 导出新增用户数据
    log_info "导出新增用户数据 (ID > $max_user_id)..."
    "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
        --no-create-info --complete-insert --single-transaction \
        --where="id > $max_user_id" "$TEST_DB_NAME" t_users > incremental_t_users.sql
    
    # 导出新增PMI记录数据
    log_info "导出新增PMI记录数据 (ID > $max_pmi_id)..."
    "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
        --no-create-info --complete-insert --single-transaction \
        --where="id > $max_pmi_id" "$TEST_DB_NAME" t_pmi_records > incremental_t_pmi_records.sql
    
    # 导出新增PMI计划数据
    log_info "导出新增PMI计划数据 (ID > $max_schedule_id)..."
    "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
        --no-create-info --complete-insert --single-transaction \
        --where="id > $max_schedule_id" "$TEST_DB_NAME" t_pmi_schedules > incremental_t_pmi_schedules.sql
    
    # 导出新增PMI窗口数据
    log_info "导出新增PMI窗口数据 (ID > $max_window_id)..."
    "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
        --no-create-info --complete-insert --single-transaction \
        --where="id > $max_window_id" "$TEST_DB_NAME" t_pmi_schedule_windows > incremental_t_pmi_schedule_windows.sql
    
    cd ..
    
    # 检查导出的数据量
    log_info "检查导出的增量数据量..."
    for file in "$EXPORT_DIR"/incremental_*.sql; do
        local count=$(grep -c "INSERT INTO" "$file" 2>/dev/null || echo "0")
        local table_name=$(basename "$file" .sql | sed 's/incremental_//')
        log_info "$table_name: $count 条新记录"
    done
    
    log_success "增量数据导出完成"
}

# 上传并导入增量数据
import_incremental_data() {
    log_info "上传并导入增量数据到生产环境..."
    
    local remote_temp_dir="/tmp/incremental_migration_${TIMESTAMP}"
    
    # 在生产服务器创建临时目录
    ssh "$PROD_SERVER" "mkdir -p $remote_temp_dir"
    
    # 上传增量数据文件
    scp "$EXPORT_DIR"/incremental_*.sql "$PROD_SERVER:$remote_temp_dir/"
    
    # 在生产服务器上执行导入
    ssh "$PROD_SERVER" "
        echo '开始导入增量数据...'
        
        # 导入新增用户数据
        if [ -s '$remote_temp_dir/incremental_t_users.sql' ]; then
            echo '导入新增用户数据...'
            mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
                < '$remote_temp_dir/incremental_t_users.sql'
        fi
        
        # 导入新增PMI记录数据
        if [ -s '$remote_temp_dir/incremental_t_pmi_records.sql' ]; then
            echo '导入新增PMI记录数据...'
            mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
                < '$remote_temp_dir/incremental_t_pmi_records.sql'
        fi
        
        # 导入新增PMI计划数据
        if [ -s '$remote_temp_dir/incremental_t_pmi_schedules.sql' ]; then
            echo '导入新增PMI计划数据...'
            mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
                < '$remote_temp_dir/incremental_t_pmi_schedules.sql'
        fi
        
        # 导入新增PMI窗口数据
        if [ -s '$remote_temp_dir/incremental_t_pmi_schedule_windows.sql' ]; then
            echo '导入新增PMI窗口数据...'
            mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
                < '$remote_temp_dir/incremental_t_pmi_schedule_windows.sql'
        fi
        
        echo '增量数据导入完成'
        
        # 清理远程临时文件
        rm -rf '$remote_temp_dir'
    "
    
    log_success "增量数据导入完成"
}

# 验证增量迁移结果
verify_incremental_migration() {
    log_info "验证增量迁移结果..."
    
    echo "=========================================="
    echo "增量迁移前后对比"
    echo "=========================================="
    
    # 获取当前生产环境数据统计
    ssh "$PROD_SERVER" "mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' <<EOF
SELECT '=== 增量迁移后生产环境数据统计 ===' as step;
SELECT 
    'Table' as table_name,
    'Count' as record_count
UNION ALL
SELECT 't_users', COUNT(*) FROM t_users
UNION ALL
SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
UNION ALL
SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
UNION ALL
SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows;
EOF"
    
    log_success "增量迁移验证完成"
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    if [ "$1" != "keep" ]; then
        rm -rf "$EXPORT_DIR"
        log_success "临时文件清理完成"
    else
        log_warning "保留导出文件: $EXPORT_DIR"
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "增量生产环境数据迁移脚本"
    echo "目标服务器: $PROD_SERVER"
    echo "=========================================="
    
    # 确认操作
    read -p "确认要执行增量迁移吗？这将添加新数据到生产环境！(y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行增量迁移步骤
    create_directories
    get_production_max_ids
    export_incremental_data
    import_incremental_data
    verify_incremental_migration
    
    # 清理
    if [[ " $* " =~ " --keep-files " ]]; then
        cleanup "keep"
    else
        cleanup
    fi
    
    log_success "增量数据迁移完成！"
}

# 错误处理
trap 'log_error "脚本执行失败，正在清理..."; cleanup; exit 1' ERR

# 执行主函数
main "$@"
