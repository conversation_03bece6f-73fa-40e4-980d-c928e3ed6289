-- 插入JOIN_ACCOUNT_RENTAL类型的测试ZoomUser数据

-- 首先检查是否有ZoomAuth记录
SELECT COUNT(*) as zoom_auth_count FROM t_zoom_auths;

-- 插入JOIN_ACCOUNT_RENTAL类型的测试用户
INSERT INTO t_zoom_users (
    zoom_auth_id, 
    zoom_user_id, 
    email, 
    first_name, 
    last_name, 
    display_name, 
    user_type, 
    status, 
    account_usage, 
    in_use, 
    total_usage_count,
    total_usage_minutes,
    created_at, 
    updated_at
) VALUES
-- 第一批JOIN_ACCOUNT_RENTAL账号
(1, 'join_rental_001', '<EMAIL>', 'Join', 'Rental001', 'Join Rental 001', 'LICENSED', 'ACTIVE', 'JOIN_ACCOUNT_RENTAL', 0, 0, 0, NOW(), NOW()),
(1, 'join_rental_002', '<EMAIL>', 'Join', 'Rental002', 'Join Ren<PERSON> 002', 'LICENSED', 'ACTIVE', 'JOIN_ACCOUNT_RENTAL', 0, 0, 0, NOW(), NOW()),
(1, 'join_rental_003', '<EMAIL>', 'Join', 'Rental003', 'Join Rental 003', 'LICENSED', 'ACTIVE', 'JOIN_ACCOUNT_RENTAL', 0, 0, 0, NOW(), NOW()),
(1, 'join_rental_004', '<EMAIL>', 'Join', 'Rental004', 'Join Rental 004', 'LICENSED', 'ACTIVE', 'JOIN_ACCOUNT_RENTAL', 0, 0, 0, NOW(), NOW()),
(1, 'join_rental_005', '<EMAIL>', 'Join', 'Rental005', 'Join Rental 005', 'LICENSED', 'ACTIVE', 'JOIN_ACCOUNT_RENTAL', 0, 0, 0, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    account_usage = VALUES(account_usage),
    updated_at = NOW();

-- 验证插入结果
SELECT 
    id,
    zoom_user_id,
    email,
    user_type,
    status,
    account_usage,
    total_usage_count,
    total_usage_minutes
FROM t_zoom_users 
WHERE account_usage = 'JOIN_ACCOUNT_RENTAL'
ORDER BY id;

-- 统计各类型账号数量
SELECT 
    account_usage,
    user_type,
    status,
    COUNT(*) as count
FROM t_zoom_users 
GROUP BY account_usage, user_type, status
ORDER BY account_usage, user_type, status;
