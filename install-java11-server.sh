#!/bin/bash

# 在CentOS 7服务器上安装Java 11并配置多版本支持
# 确保不影响现有的Java 1.8应用

set -e

TARGET_SERVER="<EMAIL>"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔧 在服务器上安装Java 11多版本支持"
echo "目标服务器: $TARGET_SERVER"
echo ""

# 检查SSH连接
log_info "检查SSH连接..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes $TARGET_SERVER "echo 'SSH连接正常'" 2>/dev/null; then
    log_error "SSH连接失败"
    exit 1
fi
log_success "SSH连接正常"

# 检查当前Java版本
log_info "检查服务器当前Java版本..."
CURRENT_JAVA=$(ssh $TARGET_SERVER "java -version 2>&1 | head -1")
log_info "当前Java版本: $CURRENT_JAVA"

# 检查是否已安装Java 11
log_info "检查是否已安装Java 11..."
JAVA11_CHECK=$(ssh $TARGET_SERVER "rpm -qa | grep java-11-openjdk || echo 'not_found'")
if [ "$JAVA11_CHECK" != "not_found" ]; then
    log_warning "Java 11已安装: $JAVA11_CHECK"
    read -p "是否重新配置Java版本管理? (y/N): " reconfigure
    if [[ ! $reconfigure =~ ^[Yy]$ ]]; then
        log_info "跳过安装，直接配置"
        SKIP_INSTALL=true
    fi
fi

# 安装Java 11
if [ "$SKIP_INSTALL" != "true" ]; then
    log_info "安装OpenJDK 11..."
    
    # 创建安装脚本
    cat > /tmp/install_java11.sh << 'EOF'
#!/bin/bash
set -e

echo "更新yum缓存..."
yum update -y

echo "安装OpenJDK 11..."
yum install -y java-11-openjdk java-11-openjdk-devel

echo "验证安装..."
rpm -qa | grep java-11-openjdk
EOF
    
    # 上传并执行安装脚本
    scp /tmp/install_java11.sh $TARGET_SERVER:/tmp/
    ssh $TARGET_SERVER "chmod +x /tmp/install_java11.sh && /tmp/install_java11.sh"
    
    log_success "Java 11安装完成"
fi

# 配置Java版本管理
log_info "配置Java版本管理..."

cat > /tmp/configure_java_alternatives.sh << 'EOF'
#!/bin/bash
set -e

echo "查找Java安装路径..."
JAVA8_PATH=$(find /usr/lib/jvm -name "java-1.8.0-openjdk*" -type d | head -1)
JAVA11_PATH=$(find /usr/lib/jvm -name "java-11-openjdk*" -type d | head -1)

echo "Java 8 路径: $JAVA8_PATH"
echo "Java 11 路径: $JAVA11_PATH"

if [ -z "$JAVA8_PATH" ] || [ -z "$JAVA11_PATH" ]; then
    echo "错误: 无法找到Java安装路径"
    exit 1
fi

echo "配置alternatives..."

# 移除现有的java alternatives（如果存在）
alternatives --remove-all java 2>/dev/null || true
alternatives --remove-all javac 2>/dev/null || true

# 添加Java 8
alternatives --install /usr/bin/java java $JAVA8_PATH/bin/java 80
alternatives --install /usr/bin/javac javac $JAVA8_PATH/bin/javac 80

# 添加Java 11
alternatives --install /usr/bin/java java $JAVA11_PATH/bin/java 110
alternatives --install /usr/bin/javac javac $JAVA11_PATH/bin/javac 110

# 设置默认为Java 8（保持现有应用兼容性）
alternatives --set java $JAVA8_PATH/bin/java
alternatives --set javac $JAVA8_PATH/bin/javac

echo "当前Java版本配置:"
alternatives --display java

echo "验证当前Java版本:"
java -version

# 创建Java 11的环境变量文件
echo "创建Java 11环境变量文件..."
cat > /etc/profile.d/java11.sh << 'JAVA11_EOF'
# Java 11 环境变量
export JAVA11_HOME=$JAVA11_PATH
export PATH11=$JAVA11_HOME/bin:$PATH
JAVA11_EOF

# 创建Java版本切换脚本
cat > /usr/local/bin/switch-java << 'SWITCH_EOF'
#!/bin/bash
# Java版本切换脚本

case "$1" in
    8)
        alternatives --set java $JAVA8_PATH/bin/java
        alternatives --set javac $JAVA8_PATH/bin/javac
        export JAVA_HOME=$JAVA8_PATH
        echo "已切换到Java 8"
        java -version
        ;;
    11)
        alternatives --set java $JAVA11_PATH/bin/java
        alternatives --set javac $JAVA11_PATH/bin/javac
        export JAVA_HOME=$JAVA11_PATH
        echo "已切换到Java 11"
        java -version
        ;;
    *)
        echo "用法: switch-java {8|11}"
        echo "当前版本:"
        java -version
        ;;
esac
SWITCH_EOF

chmod +x /usr/local/bin/switch-java

echo "Java版本管理配置完成!"
echo ""
echo "使用方法:"
echo "  切换到Java 8: switch-java 8"
echo "  切换到Java 11: switch-java 11"
echo "  查看当前版本: java -version"
echo ""
echo "Java 11环境变量:"
echo "  JAVA11_HOME=$JAVA11_PATH"
echo "  可以直接使用: \$JAVA11_HOME/bin/java"
EOF

# 上传并执行配置脚本
scp /tmp/configure_java_alternatives.sh $TARGET_SERVER:/tmp/
ssh $TARGET_SERVER "chmod +x /tmp/configure_java_alternatives.sh && /tmp/configure_java_alternatives.sh"

log_success "Java版本管理配置完成"

# 验证配置
log_info "验证Java配置..."
ssh $TARGET_SERVER "echo '=== 当前默认Java版本 ==='; java -version"
ssh $TARGET_SERVER "echo '=== Java 11版本测试 ==='; /usr/local/bin/switch-java 11 > /dev/null 2>&1; java -version; /usr/local/bin/switch-java 8 > /dev/null 2>&1"

# 清理临时文件
rm -f /tmp/install_java11.sh /tmp/configure_java_alternatives.sh

log_success "Java 11安装和配置完成!"

echo ""
echo "📋 配置摘要:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "✅ Java 8 (默认): 保持现有应用兼容性"
echo "✅ Java 11: 可用于ZoomBus应用"
echo "✅ 版本切换脚本: switch-java {8|11}"
echo "✅ Java 11环境变量: JAVA11_HOME"
echo ""
echo "💡 使用建议:"
echo "   - 系统默认保持Java 8，确保现有应用正常运行"
echo "   - ZoomBus应用使用Java 11专用路径启动"
echo "   - 可以随时使用switch-java命令切换版本"
echo ""
echo "🔧 下一步:"
echo "   - 更新ZoomBus启动脚本使用Java 11"
echo "   - 测试ZoomBus应用启动"
