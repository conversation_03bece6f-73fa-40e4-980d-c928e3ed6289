#!/bin/bash

# ngrok安装脚本
# 支持macOS和Linux

echo "=== ngrok安装脚本 ==="

# 检测操作系统
OS=""
ARCH=""

if [[ "$OSTYPE" == "darwin"* ]]; then
    OS="darwin"
    if [[ $(uname -m) == "arm64" ]]; then
        ARCH="arm64"
    else
        ARCH="amd64"
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    if [[ $(uname -m) == "x86_64" ]]; then
        ARCH="amd64"
    elif [[ $(uname -m) == "aarch64" ]]; then
        ARCH="arm64"
    else
        ARCH="386"
    fi
else
    echo "❌ 不支持的操作系统: $OSTYPE"
    echo "请手动下载: https://ngrok.com/download"
    exit 1
fi

echo "检测到系统: $OS-$ARCH"

# 检查是否已安装
if command -v ngrok &> /dev/null; then
    echo "⚠ ngrok已安装，版本信息:"
    ngrok version 2>/dev/null || echo "版本检查失败"
    read -p "是否重新安装? (y/N): " reinstall
    if [[ ! "$reinstall" =~ ^[Yy]$ ]]; then
        echo "取消安装"
        exit 0
    fi
fi

# 创建临时目录
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

echo "下载ngrok..."

# 构造下载URL
DOWNLOAD_URL="https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-${OS}-${ARCH}.zip"

echo "下载地址: $DOWNLOAD_URL"

# 下载ngrok
if curl -L -o ngrok.zip "$DOWNLOAD_URL"; then
    echo "✓ 下载完成"
else
    echo "❌ 下载失败"
    echo "请手动下载: https://ngrok.com/download"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 解压
echo "解压ngrok..."
if unzip -q ngrok.zip; then
    echo "✓ 解压完成"
else
    echo "❌ 解压失败"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 安装到系统路径
echo "安装ngrok到 /usr/local/bin/..."

if [[ "$OS" == "darwin" ]]; then
    # macOS可能需要sudo
    if sudo mv ngrok /usr/local/bin/ && sudo chmod +x /usr/local/bin/ngrok; then
        echo "✓ 安装完成"
    else
        echo "❌ 安装失败，尝试安装到用户目录..."
        mkdir -p "$HOME/.local/bin"
        mv ngrok "$HOME/.local/bin/"
        chmod +x "$HOME/.local/bin/ngrok"
        echo "✓ 安装到 $HOME/.local/bin/ngrok"
        echo "请将 $HOME/.local/bin 添加到PATH环境变量"
    fi
else
    # Linux
    if sudo mv ngrok /usr/local/bin/ && sudo chmod +x /usr/local/bin/ngrok; then
        echo "✓ 安装完成"
    else
        echo "❌ 安装失败，尝试安装到用户目录..."
        mkdir -p "$HOME/.local/bin"
        mv ngrok "$HOME/.local/bin/"
        chmod +x "$HOME/.local/bin/ngrok"
        echo "✓ 安装到 $HOME/.local/bin/ngrok"
        echo "请将 $HOME/.local/bin 添加到PATH环境变量"
    fi
fi

# 清理临时文件
cd /
rm -rf "$TEMP_DIR"

# 验证安装
echo ""
echo "验证安装..."
if command -v ngrok &> /dev/null; then
    echo "✅ ngrok安装成功!"
    ngrok version
    echo ""
    echo "下一步:"
    echo "1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken"
    echo "2. 注册并获取认证token"
    echo "3. 运行: ngrok config add-authtoken YOUR_TOKEN"
    echo "4. 测试: ngrok http 8080"
else
    echo "❌ ngrok安装失败"
    echo "请检查PATH环境变量或手动下载安装"
fi

echo ""
echo "安装完成!"
