-- Join Account Rental 功能数据库迁移脚本
-- 版本: V20250807_001
-- 描述: 添加参会者账号短租功能相关表结构
-- 使用方法: mysql -u root -p zoombusV < join_account_rental_migration.sql

USE zoombusV;

-- 1. 系统配置表 (t_system_config)
-- 用于存储系统通用配置项，包括域名配置等
CREATE TABLE IF NOT EXISTS t_system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(500) COMMENT '配置描述',
    config_type VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型：STRING, NUMBER, BOOLEAN, JSON',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) DEFAULT 'SYSTEM' COMMENT '创建人',
    updated_by VARCHAR(50) DEFAULT 'SYSTEM' COMMENT '更新人',
    INDEX idx_config_key (config_key),
    INDEX idx_is_active (is_active),
    INDEX idx_config_type (config_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 2. Join Account Rental令牌表 (t_join_account_rental_tokens)
CREATE TABLE IF NOT EXISTS t_join_account_rental_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    token_number VARCHAR(6) UNIQUE NOT NULL COMMENT 'Token编号',
    batch_number VARCHAR(50) NOT NULL COMMENT '批次号',
    usage_days INT NOT NULL COMMENT '使用天数',
    status ENUM('PENDING', 'EXPORTED', 'RESERVED', 'ACTIVE', 'COMPLETED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '权益状态：待使用/已导出/已预约/使用中/已完成/已作废',
    export_status ENUM('NOT_EXPORTED', 'EXPORTED') DEFAULT 'NOT_EXPORTED' COMMENT '导出状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    exported_at TIMESTAMP NULL COMMENT '导出时间',
    reserved_at TIMESTAMP NULL COMMENT '预约时间',
    assigned_zoom_user_id BIGINT NULL COMMENT '分配的Zoom账号ID',
    assigned_zoom_user_email VARCHAR(100) NULL COMMENT '分配的Zoom账号邮箱',
    assigned_password VARCHAR(20) NULL COMMENT '分配的密码',
    window_start_time TIMESTAMP NULL COMMENT '窗口开始时间',
    window_end_time TIMESTAMP NULL COMMENT '窗口结束时间',
    cancelled_at TIMESTAMP NULL COMMENT '作废时间',
    cancelled_by VARCHAR(50) NULL COMMENT '作废操作人',
    exported_by VARCHAR(50) NULL COMMENT '导出操作人',
    remark TEXT NULL COMMENT '备注',
    INDEX idx_token_number (token_number),
    INDEX idx_batch_number (batch_number),
    INDEX idx_status (status),
    INDEX idx_export_status (export_status),
    INDEX idx_assigned_zoom_user_id (assigned_zoom_user_id),
    INDEX idx_window_time (window_start_time, window_end_time),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Join Account Rental令牌表';

-- 3. Join Account使用窗口表 (t_join_account_usage_windows)
CREATE TABLE IF NOT EXISTS t_join_account_usage_windows (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_user_id BIGINT NOT NULL COMMENT 'Zoom账号ID',
    token_number VARCHAR(6) NOT NULL COMMENT '关联的Token',
    start_time TIMESTAMP NOT NULL COMMENT '窗口开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '窗口结束时间',
    status ENUM('PENDING', 'ACTIVE', 'CLOSED') DEFAULT 'PENDING' COMMENT '窗口状态',
    opened_at TIMESTAMP NULL COMMENT '实际开启时间',
    closed_at TIMESTAMP NULL COMMENT '实际关闭时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_token_number (token_number),
    INDEX idx_time_range (start_time, end_time),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (zoom_user_id) REFERENCES t_zoom_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (token_number) REFERENCES t_join_account_rental_tokens(token_number) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Join Account使用窗口表';

-- 4. Join Account密码变更日志表 (t_join_account_password_logs)
CREATE TABLE IF NOT EXISTS t_join_account_password_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_user_id BIGINT NOT NULL COMMENT 'Zoom账号ID',
    old_password VARCHAR(100) NULL COMMENT '旧密码',
    new_password VARCHAR(100) NOT NULL COMMENT '新密码',
    change_type ENUM('WINDOW_OPEN', 'WINDOW_CLOSE', 'MANUAL') NOT NULL COMMENT '变更类型',
    window_id BIGINT NULL COMMENT '关联窗口ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
    created_by VARCHAR(50) DEFAULT 'SYSTEM' COMMENT '操作人',
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_change_type (change_type),
    INDEX idx_created_at (created_at),
    INDEX idx_window_id (window_id),
    FOREIGN KEY (zoom_user_id) REFERENCES t_zoom_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (window_id) REFERENCES t_join_account_usage_windows(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Join Account密码变更日志表';

-- 插入系统配置初始数据
INSERT IGNORE INTO t_system_config (config_key, config_value, description, config_type, created_by, updated_by) VALUES
('join_account.domain.base_url', 'https://zoombus.cn', 'Join Account Rental基础域名配置', 'STRING', 'SYSTEM', 'SYSTEM'),
('join_account.domain.path_prefix', 'rf', 'Join Account Rental链接路径前缀', 'STRING', 'SYSTEM', 'SYSTEM'),
('join_account.reservation.max_advance_days', '30', '用户预约的开始日期距离当前日期的最大天数', 'NUMBER', 'SYSTEM', 'SYSTEM'),
('join_account.algorithm.time_weight', '0.4', '账号选择算法中时间距离权重', 'NUMBER', 'SYSTEM', 'SYSTEM'),
('join_account.algorithm.load_weight', '0.4', '账号选择算法中负载均衡权重', 'NUMBER', 'SYSTEM', 'SYSTEM'),
('join_account.algorithm.history_weight', '0.2', '账号选择算法中历史使用权重', 'NUMBER', 'SYSTEM', 'SYSTEM');

-- 记录迁移版本信息
INSERT IGNORE INTO t_version_history (application_name, application_version, database_version, flyway_version, event_type, event_description, server_info)
VALUES ('zoombus', '1.0.0', '20250807_001', '20250807_001', 'MIGRATION', 'Join Account Rental功能数据库迁移完成', 'migration-script');

-- 显示迁移完成信息
SELECT 'Join Account Rental数据库迁移完成！' as message,
       '新增表数量: 4' as tables_added,
       '新增配置项数量: 6' as configs_added,
       NOW() as migrated_at;

-- 验证表结构
SELECT 
    TABLE_NAME as table_name,
    TABLE_COMMENT as table_comment
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'zoombusV' 
AND TABLE_NAME IN (
    't_system_config',
    't_join_account_rental_tokens', 
    't_join_account_usage_windows',
    't_join_account_password_logs'
)
ORDER BY TABLE_NAME;
