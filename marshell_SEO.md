# Marshell.cn SEO 优化建议（审查与落地方案）

最后更新：2025-08-12

## 概览与关键发现
- 站点具备 sitemap.xml，并在 robots.txt 中声明；收录基础具备。
- robots.txt 屏蔽了 .js 与 .css（Disallow: /.js$, /.css$），会影响搜索引擎渲染与质量评估，需立即修复。
- 首页与栏目页内容模块重复较多（如“玛西尔·行天下诚邀全球合伙人”等重复模块），需优化主次信息与标题层级，避免稀释主题。
- 产品与文章体系完善，但需系统化的标题/描述/结构化数据规范与内链策略，以覆盖高意图词：电动观光车/巡逻车/环卫车/高尔夫球车/洗地机等。
- 页面图像体量较大（大量展示图），建议统一启用次世代格式与懒加载，配合 CDN 与尺寸声明，提升 CWV（LCP/CLS/INP）。
- 友情链接较多，建议评估质量并对商业/互推链接添加 rel="nofollow"/"sponsored"；QQ 会话链接建议 nofollow。

## 技术 SEO 清单
1) robots.txt（高优先级，立即修复）
- 当前：
  - Disallow: /.js$
  - Disallow: /.css$
- 风险：阻止搜索引擎获取渲染必需资源，影响抓取与排名（Google 明确不建议）。
- 建议：允许抓取静态资源；仅屏蔽敏感目录与不需要索引的系统页。

示例（建议稿）：
```
User-agent: *
Disallow: /admin/
Disallow: /cart/
Disallow: /search?
Disallow: /*?*
Allow: /resource/
Allow: /images/
Allow: /css/
Allow: /js/
Sitemap: https://www.marshell.cn/sitemap.xml
```

2) Sitemap（高优先级）
- 保持更新 lastmod，新增/下线页面及时同步；总量大时拆分为索引 sitemap_index.xml。
- 按类型拆分（产品、文章、案例、帮助），独立频率与优先级。

3) 协议/主机名规范（中优先级）
- 强制 301 到 https + 首选主机（带/不带 www 二选一）。
- 统一末尾斜杠策略（建议目录型地址以/结尾，产品/文章为具体资源可不带/）。
- 每页设置 rel="canonical" 指向唯一规范 URL，避免列表翻页或参数页产生重复。

4) 核心网页指标 CWV（高优先级）
- 图片：
  - 统一输出 WebP/AVIF（保留 JPG/PNG 回退），按容器裁剪尺寸，提供 width/height 以防 CLS。
  - 列表页与首屏外资源统一 loading="lazy"，首屏关键图像使用优先加载与合适尺寸。
- JS/CSS：
  - 拆分关键 CSS（critical CSS）内联，其他延后；移除未使用的样式与脚本。
  - 延迟非关键 JS（defer/async），合并小文件减少请求数。
- CDN：静态资源走就近分发，启用 HTTP/2/3 与压缩（Gzip/Brotli）。

5) 移动端可用性（高优先级）
- 视口 meta、字重与行高、自适应图片；避免点击区域过小。
- 检查移动端 CLS/INP 异常模块（轮播/悬浮客服）。

6) 结构化数据（高优先级）
- 站点级：Organization、Website、BreadcrumbList。
- 产品页：Product（name、image、brand、sku、model、description、aggregateRating、offers）。
- 文章/资讯：Article/NewsArticle（headline、image、datePublished/Modified、author、publisher）。
- FAQ：在常见问题页使用 FAQPage。
- 联系方式：Organization -> ContactPoint（telephone: "************"，contactType: customer service）。

7) 国际/本地（中优先级）
- 若存在英文版或海外业务，建议启用 /en/ 并配置 hreflang（zh-CN / en-US）。
- 百度生态：开启主动推送/API 推送；站点适配熊掌号/智能小程序非必需但可评估。

8) 日志与监测（中优先级）
- 部署 Search Console（Google）与 百度搜索资源平台；提交站点地图，查看抓取异常。
- 安装 GA4/Clarity/百度统计（JS 不要被 robots 屏蔽）。
- 配置 404/500 正确状态码；软 404 自检；重定向统一用 301。

## 内容与信息架构
1) 关键词与页面映射（高优先级）
- 主词：电动观光车、电动巡逻车、电动环卫车、高尔夫球车、洗地机（手推/驾驶式）等。
- 中腰部：14座观光车、场内摆渡车、物业环卫车、机场巡逻车、园区清洁设备等。
- 长尾：型号词（如 DN-14M、DQX52、DQS20 等）+ 使用场景/参数/价格/对比/维保。
- 为每个核心品类提供 1 个权威落地页（聚合对比+选型指南+案例+FAQ），子型号页承接转化。

2) 标题/描述/Heading 规范（高优先级）
- 每页唯一 Title（≤60字节）+ Meta Description（80–120汉字，包含主词与卖点、电话/咨询）。
- H1 唯一、直指页面主题；H2/H3 承载特性/参数/应用场景/案例/FAQ。
- 避免同站不同页复用同一标题/描述；型号页标题含品牌+型号+类目+场景关键词。

3) 内链策略（高优先级）
- 首页与品类页指向关键型号页；文章与案例页回链至对应产品与品类页。
- 站内“相关推荐/热门型号/常见问题”组件，基于语义与销量/热度；控制每屏链接数量与可见性。
- 面包屑：站点全局启用，利于理解层级并输出 BreadcrumbList。

4) URL 规范（中优先级）
- 保持稳定、简短、可读（英文/拼音一致化），示例：/products/dn-14m/；尽量避免大写与中文参数。
- 产品、文章、案例建议分目录：/products/、/articles/、/cases/。

5) 图片与多媒体（中优先级）
- alt 文案包含型号与类目词；文件名与目录包含语义。
- 列表缩略图与详情大图分发不同尺寸，避免同图多处重复加载。

6) 重复与聚合（中优先级）
- 列表分页 rel="next/prev"（或仅 canonical 到第一页）；筛选参数页（如排序）添加 canonical 到基页。
- 标签/聚合页避免与品类页产生主题重叠；必要时 noindex,follow。

## 外链与声誉建设
- 友情链接区：
  - 审核外链质量（相关性/权威度）；商业互推加 rel="sponsored" 或 "nofollow"。
  - 出站链接开启新窗口并加 noopener；QQ 会话/客服链接加 nofollow。
- 媒体/展会/案例：沉淀企业新闻稿与权威背书链接；行业协会/政府项目案例争取引用。

## 信任与本地化要素
- 显示完整公司信息（已展示 ICP 号）；补充营业执照/认证/售后与质保条款落地页。
- 电话与咨询组件：tel: 链接、点击事件埋点；固定位置但避免遮挡内容与 CLS。

## 数据驱动与治理
- 建立 SEO 看板：收录量、有效展示、品牌词/品类词点击率、Top Landing Pages、转化路径。
- 每月内容与技术迭代节奏：新增型号页、补充案例与 FAQ、技术侧 CWV 追踪。

## 90 天落地路线图
- 0–2 周（关键修复）
  - 修正 robots.txt（解除 .js/.css 禁止）。
  - 部署 canonical、规范 301、站点地图检查与分卷。
  - 首页/核心品类页 CWV 优化（图片 WebP、懒加载、关键 CSS、脚本延迟）。
- 3–6 周（结构强化）
  - 上线结构化数据（Organization、Breadcrumb、Product、Article、FAQ）。
  - 完成品类权威页与前 20 个型号页改造（标题/描述/内链/FAQ/对比表）。
  - 部署 GA4/站长平台与事件埋点，搭建监控看板。
- 7–12 周（规模化与外链）
  - 扩展剩余型号页与案例沉淀；媒体合作与行业内容共创，获取高质量引用。
  - 复盘排名与 CVR，微调模板与内链权重分配。

## 模板示例
1) 产品页 Title/Description 模板
- Title：玛西尔[型号] [类目]｜[核心卖点1/适用场景]｜支持定制
- Description：玛西尔[型号][类目]，[关键参数]，[场景]优选。全国服务与质保，咨询电话 ************，获取报价与交期。

2) Canonical 与 Meta 示例
```
<link rel="canonical" href="https://www.marshell.cn/products/dn-14m/" />
<meta name="description" content="玛西尔DN-14M十四座电动观光车，动力强劲、续航持久，适用于景区/园区/厂区摆渡。全国联保，支持定制与批量采购。" />
```

3) Product JSON-LD 示例（精简）
```
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Product",
  "name": "玛西尔 DN-14M 十四座电动观光车",
  "image": [
    "https://www.marshell.cn/images/products/dn-14m/cover.webp"
  ],
  "brand": {"@type": "Brand", "name": "玛西尔"},
  "model": "DN-14M",
  "sku": "DN-14M",
  "description": "十四座电动观光车，适用于景区/园区/厂区摆渡，支持定制。",
  "offers": {
    "@type": "Offer",
    "priceCurrency": "CNY",
    "availability": "https://schema.org/InStock",
    "url": "https://www.marshell.cn/products/dn-14m/"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "reviewCount": "27"
  }
}
</script>
```

4) Article JSON-LD 示例（精简）
```
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "电动观光车价格解析与选型指南",
  "datePublished": "2025-03-14",
  "dateModified": "2025-03-14",
  "author": {"@type": "Organization", "name": "广东玛西尔电动科技有限公司"},
  "publisher": {"@type": "Organization", "name": "玛西尔",
    "logo": {"@type": "ImageObject", "url": "https://www.marshell.cn/resource/images/logo.png"}}
}
</script>
```

5) Breadcrumb JSON-LD 示例
```
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "产品中心",
    "item": "https://www.marshell.cn/product_index.html"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "电动观光车",
    "item": "https://www.marshell.cn/product_ddggc.html"
  },{
    "@type": "ListItem",
    "position": 3,
    "name": "DN-14M 十四座电动观光车",
    "item": "https://www.marshell.cn/products/dn-14m/"
  }]
}
</script>
```

## 监测与验收指标（KPI）
- 30/60/90 天：
  - 有效收录量、抓取异常数、sitemap 覆盖率 ≥ 95%。
  - 品类核心词与型号词平均排名提升 ≥ 20%；Top10 关键词数量逐月增长。
  - 自然流量/咨询线索（电话点击、表单提交、在线咨询）提升 ≥ 30%。
  - CWV 达标比例（Good）≥ 75%。

---
附：本建议基于公开页面（含 robots.txt 与 sitemap.xml）抽样审查，落地时需与前端/后端/内容团队协同校准实现细节与排期。
