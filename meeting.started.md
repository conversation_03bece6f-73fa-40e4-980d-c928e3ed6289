# Meeting.Started 事件处理流程

## 📋 概述

`meeting.started` 是Zoom Webhook中最重要的事件之一，标志着会议的实际开始。本文档详细梳理了ZoomBus系统中对该事件的完整处理流程，包括数据流转、业务逻辑、并发控制和错误处理。

## 🔄 处理流程架构

### 1. 事件接收层
```
Zoom Webhook → WebhookController → WebhookService
```

### 2. 业务处理层
```
WebhookService → ZoomMeetingService → 数据库操作
```

### 3. 支撑服务层
```
分布式锁 → 计费监控 → 用户分配 → 事件发布
```

## 📊 详细处理流程

### 阶段1: Webhook事件接收

#### 1.1 WebhookController接收
**文件**: `src/main/java/com/zoombus/controller/WebhookController.java`

```java
@PostMapping("/{accountId}")
public ResponseEntity<String> handleWebhook(@PathVariable String accountId, @RequestBody String payload) {
    // 1. 解析Webhook payload
    // 2. 验证事件类型
    // 3. 提取关键参数
    switch (eventType) {
        case "meeting.started":
            handleMeetingStarted(meetingUuid, meetingId, hostId, topic);
            break;
    }
}
```

**关键参数提取**:
- `meetingUuid`: 会议唯一标识符
- `meetingId`: Zoom会议ID（可能是PMI号码）
- `hostId`: 主持人Zoom用户ID
- `topic`: 会议主题

#### 1.2 WebhookService处理
**文件**: `src/main/java/com/zoombus/service/WebhookService.java`

```java
private void handleMeetingStarted(JsonNode eventData, WebhookEvent webhookEvent) {
    updateMeetingStatusFromWebhook(eventData, webhookEvent, Meeting.MeetingStatus.STARTED, "开始");
}
```

**功能**:
- 更新Meeting表中的会议状态为STARTED
- 记录Webhook事件到数据库
- 触发后续的ZoomMeeting处理流程

### 阶段2: 核心业务处理

#### 2.1 ZoomMeetingService主入口
**文件**: `src/main/java/com/zoombus/service/ZoomMeetingService.java`

```java
@Transactional
public void handleMeetingStarted(String meetingUuid, String meetingId, String hostId, String topic) {
    // 使用分布式锁确保并发安全
    distributedLockManager.executeWithMeetingUuidLock(
        meetingUuid,
        Duration.ofSeconds(30),
        () -> {
            doHandleMeetingStarted(meetingUuid, meetingId, hostId, topic);
            return null;
        }
    );
}
```

**并发控制**:
- 使用分布式锁防止同一会议的并发处理
- 锁定时间30秒，确保处理完成
- 基于meetingUuid进行锁定

#### 2.2 核心处理逻辑
```java
private void doHandleMeetingStarted(String meetingUuid, String meetingId, String hostId, String topic) {
    // 1. 参数验证
    validateMeetingStartedParams(meetingUuid, meetingId, hostId);
    
    // 2. 查找现有记录
    Optional<ZoomMeeting> existingMeeting = findExistingMeetingWithLock(meetingUuid, meetingId, hostId);
    
    // 3. 更新或创建记录
    if (existingMeeting.isPresent()) {
        updateExistingMeetingToStarted(existingMeeting.get(), meetingUuid, topic);
    } else {
        createNewMeetingRecord(meetingUuid, meetingId, hostId, topic);
    }
}
```

### 阶段3: 数据查找策略

#### 3.1 多重匹配策略
```java
private Optional<ZoomMeeting> findExistingMeetingWithLock(String meetingUuid, String meetingId, String hostId) {
    // 策略1: 优先通过UUID查找（最精确）
    if (meetingUuid != null && !meetingUuid.isEmpty()) {
        Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);
        if (meetingOpt.isPresent()) {
            return meetingOpt;
        }
    }
    
    // 策略2: 通过meetingId+hostId查找活跃会议
    if (meetingId != null && hostId != null) {
        List<ZoomMeeting> activeMeetings = zoomMeetingRepository
            .findByZoomMeetingIdAndHostIdAndStatusIn(
                meetingId, hostId,
                Arrays.asList(ZoomMeeting.MeetingStatus.WAITING, ZoomMeeting.MeetingStatus.STARTED)
            );
        
        if (!activeMeetings.isEmpty()) {
            // 选择最新创建的记录
            ZoomMeeting latestMeeting = activeMeetings.stream()
                .max(Comparator.comparing(ZoomMeeting::getCreatedAt))
                .orElse(activeMeetings.get(0));
            return Optional.of(latestMeeting);
        }
    }
    
    return Optional.empty();
}
```

**查找优先级**:
1. **UUID匹配** - 最精确，适用于已有记录的更新
2. **meetingId+hostId匹配** - 适用于PMI会议和预约会议
3. **时间排序** - 选择最新创建的记录，避免选择错误实例

### 阶段4: 记录更新处理

#### 4.1 更新现有记录
```java
private void updateExistingMeetingToStarted(ZoomMeeting meeting, String meetingUuid, String topic) {
    LocalDateTime now = LocalDateTime.now();
    boolean needsUpdate = false;
    ZoomMeeting.MeetingStatus originalStatus = meeting.getStatus();
    
    // 状态转换逻辑
    if (meeting.getStatus() == ZoomMeeting.MeetingStatus.WAITING) {
        meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
        meeting.setStartTime(now);
        needsUpdate = true;
    } else if (meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED) {
        // 幂等性处理：已经是STARTED状态，保持不变
    }
    
    // 更新其他字段
    if (meetingUuid != null && !meetingUuid.equals(meeting.getZoomMeetingUuid())) {
        meeting.setZoomMeetingUuid(meetingUuid);
        needsUpdate = true;
    }
    
    if (topic != null && !topic.equals(meeting.getTopic())) {
        meeting.setTopic(topic);
        needsUpdate = true;
    }
    
    // 保存和启动计费
    if (needsUpdate) {
        ZoomMeeting savedMeeting = zoomMeetingRepository.save(meeting);
        startBillingMonitorSafely(savedMeeting);
        publishMeetingStatusChangedEvent(savedMeeting, "会议开始事件");
    }
}
```

**状态转换**:
- `WAITING` → `STARTED`: 正常开始流程
- `STARTED` → `STARTED`: 幂等性处理，避免重复操作

### 阶段5: 新记录创建

#### 5.1 创建新会议记录
```java
private void createNewMeetingRecord(String meetingUuid, String meetingId, String hostId, String topic) {
    // 1. 防重复检查
    if (zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid.trim()).isPresent()) {
        log.warn("UUID {} 已存在，跳过创建新记录", meetingUuid);
        return;
    }
    
    // 2. 构建会议记录
    ZoomMeeting meeting = buildNewMeetingRecord(meetingUuid, meetingId, hostId, topic);
    
    // 3. 保存记录
    ZoomMeeting savedMeeting = zoomMeetingRepository.save(meeting);
    
    // 4. 启动计费监控
    startBillingMonitorSafely(savedMeeting);
    
    // 5. 发布事件
    publishMeetingStatusChangedEvent(savedMeeting, "Webhook创建会议");
}
```

#### 5.2 会议记录构建
```java
private ZoomMeeting buildNewMeetingRecord(String meetingUuid, String meetingId, String hostId, String topic) {
    ZoomMeeting meeting = new ZoomMeeting();
    meeting.setZoomMeetingUuid(meetingUuid.trim());
    meeting.setZoomMeetingId(meetingId.trim());
    meeting.setHostId(hostId);
    meeting.setTopic(topic != null ? topic : "Webhook会议 - " + meetingId);
    meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
    meeting.setStartTime(LocalDateTime.now());
    
    // 智能识别会议类型
    boolean isPmiMeeting = isPmiNumber(meetingId);
    boolean isScheduledMeeting = isScheduledMeeting(meetingId, hostId);
    
    if (isPmiMeeting) {
        // PMI会议处理
        setupPmiMeetingRecord(meeting, meetingId, hostId);
    } else if (isScheduledMeeting) {
        // 预约会议处理
        setupScheduledMeetingRecord(meeting, meetingId, hostId);
    } else {
        // 其他会议处理
        setupDefaultMeetingRecord(meeting);
    }
    
    return meeting;
}
```

### 阶段6: 支撑服务

#### 6.1 计费监控启动
```java
private void startBillingMonitorSafely(ZoomMeeting meeting) {
    try {
        if (meeting.getBillingMode() == PmiRecord.BillingMode.BY_TIME) {
            billingMonitorService.startBillingMonitor(meeting.getId());
            log.info("已启动会议 {} 的计费监控", meeting.getId());
        } else {
            log.info("会议 {} 使用按时段计费，无需启动实时监控", meeting.getId());
        }
    } catch (Exception e) {
        log.error("启动会议 {} 计费监控失败", meeting.getId(), e);
        // 计费监控失败不影响会议状态更新
    }
}
```

#### 6.2 ZoomUser分配
```java
private void assignZoomUserIfNeeded(ZoomMeeting meeting) {
    try {
        if (meeting.getPmiRecordId() != null) {
            PmiRecord pmiRecord = pmiRecordRepository.findById(meeting.getPmiRecordId()).orElse(null);
            if (pmiRecord != null) {
                ZoomUser assignedUser = zoomUserPmiService.assignZoomUserForMeeting(
                    meeting.getId(), pmiRecord.getPmiNumber());
                
                meeting.setAssignedZoomUserId(assignedUser.getId());
                meeting.setAssignedZoomUserEmail(assignedUser.getEmail());
            }
        }
    } catch (Exception e) {
        log.error("为会议 {} 分配ZoomUser失败", meeting.getId(), e);
        meeting.setAssignmentError("ZoomUser分配失败: " + e.getMessage());
    }
}
```

## 🔒 并发控制和安全性

### 1. 分布式锁机制
- **锁定范围**: 基于meetingUuid的分布式锁
- **锁定时间**: 30秒超时
- **锁定目的**: 防止同一会议的并发处理

### 2. 数据库事务
- **事务边界**: 整个处理流程在一个事务中
- **隔离级别**: 默认READ_COMMITTED
- **回滚策略**: 任何异常都会回滚整个事务

### 3. 幂等性保证
- **UUID检查**: 防止重复创建相同UUID的记录
- **状态检查**: STARTED状态的会议不会重复处理
- **参数验证**: 严格的输入参数验证

## 📊 数据流转

### 输入数据
```json
{
  "event": "meeting.started",
  "payload": {
    "object": {
      "uuid": "meeting-uuid-123",
      "id": "123456789",
      "host_id": "host-user-id",
      "topic": "会议主题"
    }
  }
}
```

### 数据库更新
1. **t_webhook_events**: 记录Webhook事件
2. **t_meetings**: 更新会议状态为STARTED
3. **t_zoom_meetings**: 创建或更新ZoomMeeting记录
4. **t_pmi_records**: 关联PMI记录（如适用）

### 输出结果
- 会议状态更新为STARTED
- 计费监控启动
- ZoomUser分配完成
- 事件发布到消息队列

## ⚠️ 异常处理

### 1. 参数验证异常
```java
private void validateMeetingStartedParams(String meetingUuid, String meetingId, String hostId) {
    if (meetingUuid == null || meetingUuid.trim().isEmpty()) {
        throw new IllegalArgumentException("meetingUuid不能为空");
    }
    if (meetingId == null || meetingId.trim().isEmpty()) {
        throw new IllegalArgumentException("meetingId不能为空");
    }
    if (hostId == null || hostId.trim().isEmpty()) {
        throw new IllegalArgumentException("hostId不能为空");
    }
}
```

### 2. 业务异常处理
- **重复UUID**: 跳过创建，记录警告日志
- **计费监控失败**: 记录错误，不影响主流程
- **ZoomUser分配失败**: 记录错误信息到数据库

### 3. 系统异常处理
- **数据库异常**: 事务回滚，返回500错误
- **分布式锁超时**: 抛出异常，记录错误日志
- **网络异常**: 重试机制（如适用）

## 📈 性能优化

### 1. 数据库优化
- **索引优化**: meetingUuid、meetingId+hostId组合索引
- **查询优化**: 使用最新创建时间排序
- **连接池**: 合理配置数据库连接池

### 2. 并发优化
- **锁粒度**: 基于meetingUuid的细粒度锁
- **锁时间**: 30秒超时，避免死锁
- **异步处理**: 计费监控等非关键操作异步执行

### 3. 内存优化
- **对象复用**: 避免不必要的对象创建
- **缓存策略**: 合理使用缓存减少数据库查询

## 🔍 监控和日志

### 1. 关键日志点
```java
log.info("处理会议开始事件: uuid={}, meetingId={}, hostId={}, topic={}", ...);
log.info("通过UUID找到会议记录: meetingId={}, uuid={}", ...);
log.info("会议状态更新: WAITING -> STARTED, meetingUuid={}, meetingId={}", ...);
log.info("从Webhook创建会议记录成功: id={}, meetingId={}, uuid={}, type={}", ...);
```

### 2. 性能监控
- **处理时间**: 记录每个阶段的处理时间
- **成功率**: 统计处理成功和失败的比例
- **并发度**: 监控同时处理的会议数量

### 3. 业务监控
- **会议创建数量**: 按时间段统计
- **PMI会议比例**: PMI vs 普通会议的比例
- **计费启动成功率**: 计费监控启动的成功率

---

**文档版本**: v2.0  
**最后更新**: 2025-08-05  
**维护者**: ZoomBus开发团队
