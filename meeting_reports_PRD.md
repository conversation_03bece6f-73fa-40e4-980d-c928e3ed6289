# 会议报告功能 PRD (Product Requirements Document)

## 📋 项目概述

### 项目背景
ZoomBus系统当前具备完整的会议管理功能，包括PMI管理、会议创建、状态跟踪等。为了提供更完善的会议服务，需要开发会议报告功能，通过Zoom Reports API获取详细的会议数据，并在管理台和用户端展示。

### 项目目标
1. **数据获取**：自动获取会议结束后的详细报告数据
2. **数据存储**：建立完整的会议报告数据模型
3. **数据展示**：在多个前端页面提供会议报告查看功能
4. **业务价值**：提升用户体验，支持会议分析和统计

## 🎯 功能需求

### 核心功能

#### 1. 会议报告数据获取
- **触发时机**：会议结束后自动获取，支持手动重新获取
- **数据源**：Zoom Reports API (`/report/meetings/{meetingId}`)
- **获取内容**：
  - 会议基本信息（主题、时间、时长等）
  - 参会人员列表（姓名、邮箱、加入/离开时间）
  - 会议统计数据（总参会人数、平均参会时长等）
  - 录制信息（如有）

#### 2. 数据存储设计
- **会议报告主表**：存储会议整体报告信息
- **参会人员表**：存储每个参会者的详细信息
- **与现有表关联**：通过zoom_meeting_uuid关联t_zoom_meetings表

#### 3. 管理台展示功能
- **会议列表页面**：添加"查看报告"按钮
- **会议详情页面**：展示完整的会议报告信息
- **报告详情弹窗**：参会人员列表、时长统计、图表展示
- **数据导出**：支持Excel格式导出会议报告

#### 4. 用户端展示功能
- **PMI使用页面**：展示该PMI的历史会议报告列表
- **会议主持页面**：展示当前会议的报告信息
- **移动端适配**：响应式设计，支持移动设备查看

## 🏗️ 技术架构

### 后端架构

#### 1. 数据库设计
```sql
-- 会议报告主表
CREATE TABLE t_meeting_reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    zoom_meeting_uuid VARCHAR(255) NOT NULL,
    zoom_meeting_id VARCHAR(255) NOT NULL,
    topic VARCHAR(500),
    start_time DATETIME,
    end_time DATETIME,
    duration_minutes INT,
    total_participants INT,
    unique_participants INT,
    has_recording BOOLEAN DEFAULT FALSE,
    report_data JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_zoom_meeting_uuid (zoom_meeting_uuid),
    INDEX idx_zoom_meeting_id (zoom_meeting_id)
);

-- 参会人员详情表
CREATE TABLE t_meeting_participants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    meeting_report_id BIGINT NOT NULL,
    participant_name VARCHAR(255),
    participant_email VARCHAR(255),
    join_time DATETIME,
    leave_time DATETIME,
    duration_minutes INT,
    user_type VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_report_id) REFERENCES t_meeting_reports(id)
);
```

#### 2. 实体类设计
- **MeetingReport**：会议报告主实体
- **MeetingParticipant**：参会人员实体
- **Repository接口**：数据访问层
- **Service层**：业务逻辑处理
- **Controller层**：API接口提供

#### 3. API集成
- **ZoomApiService扩展**：添加获取会议报告的方法
- **异常处理**：API调用失败的重试机制
- **数据解析**：JSON数据到实体的转换
- **缓存机制**：避免重复获取相同报告

### 前端架构

#### 1. 管理台组件
- **MeetingReportModal**：会议报告详情弹窗
- **ParticipantTable**：参会人员列表组件
- **ReportChart**：会议统计图表组件
- **ExportButton**：报告导出功能

#### 2. 用户端组件
- **MeetingReportList**：会议报告列表
- **ReportSummary**：报告摘要组件
- **MobileReportView**：移动端报告视图

## 📊 业务流程

### 1. 报告获取流程
```mermaid
graph TD
    A[会议结束] --> B[触发报告获取]
    B --> C[调用Zoom Reports API]
    C --> D{API调用成功?}
    D -->|是| E[解析报告数据]
    D -->|否| F[记录错误日志]
    E --> G[保存到数据库]
    G --> H[更新会议状态]
    F --> I[加入重试队列]
    I --> J[定时重试]
```

### 2. 数据展示流程
```mermaid
graph TD
    A[用户访问页面] --> B[查询会议报告]
    B --> C{报告存在?}
    C -->|是| D[展示报告数据]
    C -->|否| E[显示获取中状态]
    E --> F[触发报告获取]
    F --> G[实时更新页面]
```

## 🚀 实施计划

### 阶段1：基础架构搭建（2-3天）
- [ ] 数据库表结构设计和创建
- [ ] JPA实体类和Repository接口
- [ ] 基础的Service和Controller框架

### 阶段2：Zoom API集成（2-3天）
- [ ] 研究Zoom Reports API文档
- [ ] 实现API调用和数据解析
- [ ] 异常处理和重试机制
- [ ] 单元测试编写

### 阶段3：后端业务逻辑（3-4天）
- [ ] 会议报告获取和保存逻辑
- [ ] 与现有会议结束流程集成
- [ ] 定时任务补获取遗漏报告
- [ ] API接口开发和测试

### 阶段4：管理台前端开发（3-4天）
- [ ] 会议列表页面报告功能
- [ ] 会议报告详情弹窗
- [ ] 参会人员列表和统计图表
- [ ] 报告导出功能

### 阶段5：用户端前端开发（2-3天）
- [ ] PMI页面历史报告展示
- [ ] 会议主持页面报告功能
- [ ] 移动端适配和优化
- [ ] 多语言支持

### 阶段6：测试和优化（2-3天）
- [ ] 端到端功能测试
- [ ] 性能测试和优化
- [ ] 错误处理完善
- [ ] 用户体验优化

## 📋 验收标准

### 功能验收
1. **数据获取**：会议结束后能自动获取完整的报告数据
2. **数据准确性**：报告数据与Zoom原始数据一致
3. **界面展示**：各个页面能正确展示报告信息
4. **响应性能**：报告页面加载时间不超过3秒
5. **移动适配**：移动端显示效果良好

### 技术验收
1. **API稳定性**：Zoom API调用成功率>95%
2. **数据完整性**：报告数据保存完整，无丢失
3. **错误处理**：异常情况有合理的提示和处理
4. **代码质量**：代码规范，有充分的注释和测试

## 🔧 技术考虑

### 1. API限制处理
- Zoom Reports API有调用频率限制
- 实现合理的重试间隔和缓存机制
- 避免重复获取相同报告

### 2. 数据隐私
- 参会人员信息可能涉及隐私
- 考虑数据脱敏和权限控制
- 遵循数据保护相关法规

### 3. 性能优化
- 大型会议报告数据量较大
- 实现分页查询和懒加载
- 优化数据库查询性能

### 4. 扩展性
- 预留接口支持其他会议平台
- 支持自定义报告模板
- 考虑未来的统计分析需求

## 📈 预期收益

### 1. 用户价值
- 提供详细的会议数据分析
- 帮助用户了解会议效果
- 支持会议质量改进

### 2. 业务价值
- 增强产品竞争力
- 提升用户满意度
- 为后续功能提供数据基础

### 3. 技术价值
- 完善系统数据模型
- 提升API集成能力
- 积累报告分析经验

---

**文档版本**：v1.0  
**创建时间**：2025-01-15  
**负责人**：开发团队  
**审核状态**：待审核
