-- 简化版本的账户用途字段迁移脚本
-- 执行时间: 2025-07-18

USE zoombusV;

-- 直接添加 account_usage 列
-- 如果字段已存在，此语句会报错，但不会影响现有数据
ALTER TABLE t_zoom_users 
ADD COLUMN account_usage VARCHAR(50) NULL 
COMMENT '账户用途: PUBLIC_MEETING(参会账号-公共), PRIVATE_MEETING(参会账号-专属), PUBLIC_HOST(开会账号-公共), PRIVATE_HOST(开会账号-专属)';

-- 创建索引以提高查询性能
CREATE INDEX idx_zoom_users_account_usage ON t_zoom_users(account_usage);

-- 验证字段是否添加成功
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'zoombusV'
  AND TABLE_NAME = 't_zoom_users'
  AND COLUMN_NAME = 'account_usage';

-- 查看表结构
DESCRIBE t_zoom_users;
