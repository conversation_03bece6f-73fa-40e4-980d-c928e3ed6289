#!/bin/bash

# 移植数据导出导入自动化脚本
# 用于将测试环境的移植数据导入到生产环境
# 迁移5张表：t_users, t_pmi_records, t_pmi_schedules, t_pmi_schedule_windows, t_pmi_schedule_window_tasks
# 执行日期: 2025-08-27

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# MySQL命令路径
MYSQL_CMD="/usr/local/mysql-8.0.11-macos10.13-x86_64/bin/mysql"
MYSQLDUMP_CMD="/usr/local/mysql-8.0.11-macos10.13-x86_64/bin/mysqldump"

# 加载配置文件
CONFIG_FILE="./migration_config.conf"
if [ -f "$CONFIG_FILE" ]; then
    source "$CONFIG_FILE"
    log_info "已加载配置文件: $CONFIG_FILE"
else
    log_warning "配置文件不存在，使用默认配置: $CONFIG_FILE"
fi

# 默认配置变量（如果配置文件中没有定义）
TEST_DB_USER="${TEST_DB_USER:-root}"
TEST_DB_PASS="${TEST_DB_PASS:-nvshen2018}"
TEST_DB_NAME="${TEST_DB_NAME:-zoombusV}"
TEST_DB_HOST="${TEST_DB_HOST:-localhost}"

# 生产环境配置
PROD_SERVER="${PROD_SERVER:-<EMAIL>}"
PROD_DB_USER="${PROD_DB_USER:-root}"
PROD_DB_PASS="${PROD_DB_PASS:-nvshen2018}"
PROD_DB_NAME="${PROD_DB_NAME:-zoombusV}"
PROD_DB_HOST="${PROD_DB_HOST:-localhost}"

# SSH配置
SSH_KEY_PATH="${SSH_KEY_PATH:-}"
SSH_PORT="${SSH_PORT:-22}"

# 高级选项
MYSQL_CONNECT_TIMEOUT="${MYSQL_CONNECT_TIMEOUT:-30}"
SSH_CONNECT_TIMEOUT="${SSH_CONNECT_TIMEOUT:-10}"
TRUNCATE_BEFORE_IMPORT="${TRUNCATE_BEFORE_IMPORT:-false}"
CREATE_BACKUP_TABLES="${CREATE_BACKUP_TABLES:-true}"

# 时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./migration_backup_${TIMESTAMP}"
EXPORT_DIR="./migration_export_${TIMESTAMP}"



# 检查SSH连接
check_ssh_connection() {
    log_info "检查生产服务器SSH连接..."

    local ssh_cmd="ssh"
    if [ -n "$SSH_KEY_PATH" ]; then
        ssh_cmd="ssh -i $SSH_KEY_PATH"
    fi
    ssh_cmd="$ssh_cmd -p $SSH_PORT -o ConnectTimeout=10 -o BatchMode=yes"

    if $ssh_cmd "$PROD_SERVER" "echo 'SSH连接测试成功'" >/dev/null 2>&1; then
        log_success "生产服务器SSH连接正常"
        return 0
    else
        log_error "生产服务器SSH连接失败，请检查："
        log_error "1. 服务器地址: $PROD_SERVER"
        log_error "2. SSH端口: $SSH_PORT"
        log_error "3. SSH密钥配置"
        log_error "4. 网络连接"
        return 1
    fi
}

# 检查MySQL连接
check_mysql_connection() {
    local host=$1
    local user=$2
    local pass=$3
    local db=$4
    local env_name=$5
    local is_remote=$6

    log_info "检查${env_name}环境MySQL连接..."

    if [ "$is_remote" = "true" ]; then
        # 远程服务器MySQL连接检查
        local ssh_cmd="ssh"
        if [ -n "$SSH_KEY_PATH" ]; then
            ssh_cmd="ssh -i $SSH_KEY_PATH"
        fi
        ssh_cmd="$ssh_cmd -p $SSH_PORT"

        if $ssh_cmd "$PROD_SERVER" "mysql -h'$host' -u'$user' -p'$pass' -e 'USE $db; SELECT 1;'" >/dev/null 2>&1; then
            log_success "${env_name}环境MySQL连接正常"
            return 0
        else
            log_error "${env_name}环境MySQL连接失败"
            return 1
        fi
    else
        # 本地MySQL连接检查
        if "$MYSQL_CMD" -h"$host" -u"$user" -p"$pass" -e "USE $db; SELECT 1;" >/dev/null 2>&1; then
            log_success "${env_name}环境MySQL连接正常"
            return 0
        else
            log_error "${env_name}环境MySQL连接失败"
            return 1
        fi
    fi
}

# 创建目录
create_directories() {
    log_info "创建工作目录..."
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$EXPORT_DIR"
    log_success "目录创建完成: $BACKUP_DIR, $EXPORT_DIR"
}

# 导出测试环境数据
export_test_data() {
    log_info "开始从测试环境导出数据..."
    
    cd "$EXPORT_DIR"
    
    # 导出用户数据
    log_info "导出用户数据..."
    "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
        --no-create-info --complete-insert --single-transaction \
        --where="1=1" "$TEST_DB_NAME" t_users > migrated_t_users.sql

    # 导出PMI记录数据
    log_info "导出PMI记录数据..."
    "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
        --no-create-info --complete-insert --single-transaction \
        --where="1=1" "$TEST_DB_NAME" t_pmi_records > migrated_t_pmi_records.sql

    # 导出PMI计划数据
    log_info "导出PMI计划数据..."
    "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
        --no-create-info --complete-insert --single-transaction \
        --where="1=1" "$TEST_DB_NAME" t_pmi_schedules > migrated_t_pmi_schedules.sql

    # 导出PMI窗口数据
    log_info "导出PMI窗口数据..."
    "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
        --no-create-info --complete-insert --single-transaction \
        --where="1=1" "$TEST_DB_NAME" t_pmi_schedule_windows > migrated_t_pmi_schedule_windows.sql

    # 导出PMI窗口任务数据
    log_info "导出PMI窗口任务数据..."
    "$MYSQLDUMP_CMD" -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" \
        --no-create-info --complete-insert --single-transaction \
        --where="1=1" "$TEST_DB_NAME" t_pmi_schedule_window_tasks > migrated_t_pmi_schedule_window_tasks.sql

    cd ..
    log_success "数据导出完成，文件保存在: $EXPORT_DIR"
}

# 上传文件到生产服务器
upload_files_to_production() {
    log_info "上传导出文件到生产服务器..."

    local ssh_cmd="scp"
    if [ -n "$SSH_KEY_PATH" ]; then
        ssh_cmd="scp -i $SSH_KEY_PATH"
    fi
    ssh_cmd="$ssh_cmd -P $SSH_PORT"

    # 在生产服务器创建临时目录
    local remote_temp_dir="/tmp/migration_${TIMESTAMP}"
    local ssh_exec="ssh"
    if [ -n "$SSH_KEY_PATH" ]; then
        ssh_exec="ssh -i $SSH_KEY_PATH"
    fi
    ssh_exec="$ssh_exec -p $SSH_PORT"

    $ssh_exec "$PROD_SERVER" "mkdir -p $remote_temp_dir"

    # 上传所有导出文件
    $ssh_cmd "$EXPORT_DIR"/*.sql "$PROD_SERVER:$remote_temp_dir/"

    log_success "文件上传完成，远程路径: $remote_temp_dir"
    echo "$remote_temp_dir"  # 返回远程目录路径
}

# 备份生产环境数据
backup_production_data() {
    log_info "备份生产环境现有数据..."

    local ssh_cmd="ssh"
    if [ -n "$SSH_KEY_PATH" ]; then
        ssh_cmd="ssh -i $SSH_KEY_PATH"
    fi
    ssh_cmd="$ssh_cmd -p $SSH_PORT"

    local remote_backup_file="/tmp/production_backup_${TIMESTAMP}.sql"

    # 在生产服务器上执行备份
    $ssh_cmd "$PROD_SERVER" "mysqldump -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' \
        --single-transaction '$PROD_DB_NAME' \
        t_users t_pmi_records t_pmi_schedules t_pmi_schedule_windows t_pmi_schedule_window_tasks \
        > $remote_backup_file"

    # 下载备份文件到本地
    local scp_cmd="scp"
    if [ -n "$SSH_KEY_PATH" ]; then
        scp_cmd="scp -i $SSH_KEY_PATH"
    fi
    scp_cmd="$scp_cmd -P $SSH_PORT"

    $scp_cmd "$PROD_SERVER:$remote_backup_file" "$BACKUP_DIR/production_backup_${TIMESTAMP}.sql"

    log_success "生产环境数据备份完成: $BACKUP_DIR/production_backup_${TIMESTAMP}.sql"
    echo "$remote_backup_file"  # 返回远程备份文件路径
}

# 显示导入前统计
show_pre_import_stats() {
    log_info "导入前生产环境数据统计..."

    local ssh_cmd="ssh"
    if [ -n "$SSH_KEY_PATH" ]; then
        ssh_cmd="ssh -i $SSH_KEY_PATH"
    fi
    ssh_cmd="$ssh_cmd -p $SSH_PORT"

    $ssh_cmd "$PROD_SERVER" "mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' <<EOF
SELECT '=== 导入前数据统计 ===' as step;
SELECT
    'Table' as table_name,
    'Count' as record_count
UNION ALL
SELECT 't_users', COUNT(*) FROM t_users
UNION ALL
SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
UNION ALL
SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
UNION ALL
SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows
UNION ALL
SELECT 't_pmi_schedule_window_tasks', COUNT(*) FROM t_pmi_schedule_window_tasks;
EOF"
}

# 导入数据到生产环境
import_to_production() {
    log_info "开始导入数据到生产环境..."

    # 获取远程临时目录路径
    local remote_temp_dir="/tmp/migration_${TIMESTAMP}"

    local ssh_cmd="ssh"
    if [ -n "$SSH_KEY_PATH" ]; then
        ssh_cmd="ssh -i $SSH_KEY_PATH"
    fi
    ssh_cmd="$ssh_cmd -p $SSH_PORT"

    # 在生产服务器上执行导入
    $ssh_cmd "$PROD_SERVER" "
        # 临时禁用外键检查
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            -e 'SET FOREIGN_KEY_CHECKS = 0;'

        # 导入用户数据
        echo '导入用户数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/migrated_t_users.sql'

        # 导入PMI记录数据
        echo '导入PMI记录数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/migrated_t_pmi_records.sql'

        # 导入PMI计划数据
        echo '导入PMI计划数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/migrated_t_pmi_schedules.sql'

        # 导入PMI窗口数据
        echo '导入PMI窗口数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/migrated_t_pmi_schedule_windows.sql'

        # 导入PMI窗口任务数据
        echo '导入PMI窗口任务数据...'
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            < '$remote_temp_dir/migrated_t_pmi_schedule_window_tasks.sql'

        # 重新启用外键检查
        mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' \
            -e 'SET FOREIGN_KEY_CHECKS = 1;'

        echo '数据导入完成'
    "

    log_success "数据导入完成"
}

# 验证导入结果
validate_import() {
    log_info "验证导入结果..."

    local ssh_cmd="ssh"
    if [ -n "$SSH_KEY_PATH" ]; then
        ssh_cmd="ssh -i $SSH_KEY_PATH"
    fi
    ssh_cmd="$ssh_cmd -p $SSH_PORT"

    $ssh_cmd "$PROD_SERVER" "mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' <<EOF
SELECT '=== 导入后数据验证 ===' as step;

-- 表记录数统计
SELECT 
    'Table' as table_name,
    'Count' as record_count
UNION ALL
SELECT 't_users', COUNT(*) FROM t_users
UNION ALL
SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
UNION ALL
SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
UNION ALL
SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows
UNION ALL
SELECT 't_pmi_schedule_window_tasks', COUNT(*) FROM t_pmi_schedule_window_tasks;

-- PMI记录验证
SELECT 
    'PMI Records Validation' as check_type,
    COUNT(*) as total_pmi_records,
    COUNT(CASE WHEN billing_mode = 'LONG' THEN 1 END) as long_billing_count,
    COUNT(CASE WHEN billing_mode = 'BY_TIME' THEN 1 END) as by_time_billing_count
FROM t_pmi_records;

-- 窗口状态验证
SELECT 
    'Window Status Validation' as check_type,
    COUNT(*) as total_windows,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_windows,
    COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_windows,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_windows
FROM t_pmi_schedule_windows;

-- 关联关系验证
SELECT 
    'Relationship Validation' as check_type,
    'PMI-User' as relationship_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_relationships
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id
UNION ALL
SELECT 
    'Relationship Validation' as check_type,
    'Schedule-PMI' as relationship_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN p.id IS NOT NULL THEN 1 END) as valid_relationships
FROM t_pmi_schedules s
LEFT JOIN t_pmi_records p ON s.pmi_record_id = p.id
UNION ALL
SELECT 
    'Relationship Validation' as check_type,
    'Window-Schedule' as relationship_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN s.id IS NOT NULL THEN 1 END) as valid_relationships
FROM t_pmi_schedule_windows w
LEFT JOIN t_pmi_schedules s ON w.schedule_id = s.id
UNION ALL
SELECT
    'Relationship Validation' as check_type,
    'Task-Window' as relationship_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN w.id IS NOT NULL THEN 1 END) as valid_relationships
FROM t_pmi_schedule_window_tasks t
LEFT JOIN t_pmi_schedule_windows w ON t.pmi_window_id = w.id;

-- 任务状态验证
SELECT
    'Task Status Validation' as check_type,
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN status = 'SCHEDULED' THEN 1 END) as scheduled_tasks,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_tasks,
    COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_tasks,
    COUNT(CASE WHEN task_type = 'PMI_WINDOW_CLOSE' THEN 1 END) as close_tasks,
    COUNT(CASE WHEN task_type = 'PMI_WINDOW_OPEN' THEN 1 END) as open_tasks
FROM t_pmi_schedule_window_tasks;

SELECT 'Data validation completed!' as validation_result;
EOF"
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."

    # 清理生产服务器上的临时文件
    local ssh_cmd="ssh"
    if [ -n "$SSH_KEY_PATH" ]; then
        ssh_cmd="ssh -i $SSH_KEY_PATH"
    fi
    ssh_cmd="$ssh_cmd -p $SSH_PORT"

    local remote_temp_dir="/tmp/migration_${TIMESTAMP}"
    $ssh_cmd "$PROD_SERVER" "rm -rf $remote_temp_dir" 2>/dev/null || true

    # 清理本地临时文件
    if [ "$1" = "keep" ]; then
        log_warning "保留导出和备份文件: $EXPORT_DIR, $BACKUP_DIR"
        log_info "生产服务器临时文件已清理"
    else
        rm -rf "$EXPORT_DIR"
        log_success "本地和远程临时文件清理完成"
    fi
}

# 主函数
main() {
    log_info "开始执行数据迁移脚本..."
    log_info "时间戳: $TIMESTAMP"
    
    # 检查参数
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        echo "用法: $0 [选项]"
        echo "选项:"
        echo "  --export-only    仅导出数据，不导入"
        echo "  --import-only    仅导入数据，不导出（需要先有导出文件）"
        echo "  --keep-files     保留导出和备份文件"
        echo "  --ssh-key PATH   指定SSH私钥路径"
        echo "  --ssh-port PORT  指定SSH端口（默认22）"
        echo "  --help, -h       显示帮助信息"
        echo ""
        echo "生产服务器配置:"
        echo "  服务器地址: $PROD_SERVER"
        echo "  SSH端口: $SSH_PORT"
        echo "  数据库: $PROD_DB_NAME"
        exit 0
    fi

    # 处理SSH配置参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --ssh-key)
                SSH_KEY_PATH="$2"
                shift 2
                ;;
            --ssh-port)
                SSH_PORT="$2"
                shift 2
                ;;
            *)
                shift
                ;;
        esac
    done
    
    # 创建目录
    create_directories
    
    # 导出数据
    if [[ ! " $* " =~ " --import-only " ]]; then
        check_mysql_connection "$TEST_DB_HOST" "$TEST_DB_USER" "$TEST_DB_PASS" "$TEST_DB_NAME" "测试" "false"
        export_test_data
    fi

    # 导入数据
    if [[ ! " $* " =~ " --export-only " ]]; then
        # 检查SSH连接
        check_ssh_connection

        # 检查生产环境MySQL连接
        check_mysql_connection "$PROD_DB_HOST" "$PROD_DB_USER" "$PROD_DB_PASS" "$PROD_DB_NAME" "生产" "true"

        # 显示导入前统计
        show_pre_import_stats

        # 上传文件到生产服务器
        upload_files_to_production

        # 备份生产环境数据
        backup_production_data

        # 导入数据
        import_to_production

        # 验证导入结果
        validate_import
    fi
    
    # 清理
    if [[ " $* " =~ " --keep-files " ]]; then
        cleanup "keep"
    else
        cleanup
    fi
    
    log_success "数据迁移脚本执行完成！"
}

# 执行主函数
main "$@"
