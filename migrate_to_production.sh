#!/bin/bash

# 生产环境数据迁移脚本
# 将四张核心表迁移到生产环境 <EMAIL>
# 执行日期: 2025-08-20

set -e  # 遇到错误立即退出

# 配置变量
LOCAL_DB_USER="root"
LOCAL_DB_PASS="nvshen2018"
LOCAL_DB_NAME="zoombusV"

PROD_SERVER="<EMAIL>"
PROD_DB_USER="root"
PROD_DB_PASS="nvshen2018"
PROD_DB_NAME="zoombusv"

# 要迁移的表
TABLES=(
    "t_users"
    "t_pmi_records"
    "t_pmi_schedules"
    "t_pmi_schedule_windows"
)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查SSH连接
check_ssh_connection() {
    log_info "检查SSH连接到生产服务器..."
    
    if ssh -o ConnectTimeout=10 $PROD_SERVER "echo 'SSH连接成功'" > /dev/null 2>&1; then
        log_success "SSH连接正常"
    else
        log_error "无法连接到生产服务器 $PROD_SERVER"
        exit 1
    fi
}

# 检查本地数据库连接
check_local_db() {
    log_info "检查本地数据库连接..."
    
    if mysql -u$LOCAL_DB_USER -p$LOCAL_DB_PASS -e "USE $LOCAL_DB_NAME;" > /dev/null 2>&1; then
        log_success "本地数据库连接正常"
    else
        log_error "无法连接到本地数据库"
        exit 1
    fi
}

# 检查生产环境数据库连接
check_prod_db() {
    log_info "检查生产环境数据库连接..."
    
    if ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS -e 'USE $PROD_DB_NAME;'" > /dev/null 2>&1; then
        log_success "生产环境数据库连接正常"
    else
        log_error "无法连接到生产环境数据库"
        exit 1
    fi
}

# 检查本地表数据
check_local_tables() {
    log_info "检查本地表数据..."
    
    for table in "${TABLES[@]}"; do
        local count=$(mysql -u$LOCAL_DB_USER -p$LOCAL_DB_PASS $LOCAL_DB_NAME -e "SELECT COUNT(*) FROM $table;" -N 2>/dev/null || echo "0")
        log_info "本地表 $table: $count 条记录"
        
        if [ "$count" -eq 0 ]; then
            log_warning "本地表 $table 没有数据"
        fi
    done
}

# 备份生产环境数据
backup_prod_data() {
    log_info "备份生产环境数据..."
    
    local backup_dir="prod_backup_$(date +%Y%m%d_%H%M%S)"
    
    # 在生产服务器上创建备份目录
    ssh $PROD_SERVER "mkdir -p /tmp/$backup_dir"
    
    for table in "${TABLES[@]}"; do
        log_info "备份生产环境表: $table"
        
        ssh $PROD_SERVER "mysqldump -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME $table > /tmp/$backup_dir/${table}_backup.sql" || {
            log_error "备份表 $table 失败"
            exit 1
        }
    done
    
    log_success "生产环境数据备份完成，备份目录: /tmp/$backup_dir"
    echo "备份目录: /tmp/$backup_dir" > prod_backup_info.txt
}

# 清空生产环境目标表
clear_prod_tables() {
    log_info "清空生产环境目标表..."
    
    # 按依赖关系倒序删除数据
    local reverse_tables=(
        "t_pmi_schedule_windows"
        "t_pmi_schedules"
        "t_pmi_records"
        "t_users"
    )
    
    for table in "${reverse_tables[@]}"; do
        log_info "清空生产环境表: $table"
        
        ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME -e 'SET FOREIGN_KEY_CHECKS = 0; TRUNCATE TABLE $table; SET FOREIGN_KEY_CHECKS = 1;'" || {
            log_error "清空表 $table 失败"
            exit 1
        }
        
        local count=$(ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME -e 'SELECT COUNT(*) FROM $table;' -N")
        log_success "表 $table 已清空 (剩余记录: $count)"
    done
}

# 导出本地数据
export_local_data() {
    log_info "导出本地数据..."

    local export_dir="migration_export_$(date +%Y%m%d_%H%M%S)"
    mkdir -p $export_dir

    for table in "${TABLES[@]}"; do
        log_info "导出本地表: $table"

        mysqldump -u$LOCAL_DB_USER -p$LOCAL_DB_PASS \
            --single-transaction \
            --routines \
            --triggers \
            --no-create-info \
            --complete-insert \
            --extended-insert=FALSE \
            $LOCAL_DB_NAME $table > $export_dir/${table}.sql || {
            log_error "导出表 $table 失败"
            exit 1
        }

        local size=$(ls -lh $export_dir/${table}.sql | awk '{print $5}')
        log_success "表 $table 导出完成 (文件大小: $size)"
    done

    echo "导出目录: $export_dir" > local_export_info.txt
    log_success "本地数据导出完成，导出目录: $export_dir"
}

# 转换字符集格式
convert_charset_format() {
    log_info "转换导出文件字符集格式..."

    local export_dir=$(cat local_export_info.txt | cut -d: -f2 | xargs)

    for table in "${TABLES[@]}"; do
        log_info "转换表 $table 的字符集格式"

        local sql_file="$export_dir/${table}.sql"
        local temp_file="$export_dir/${table}_temp.sql"

        # 替换字符集声明
        sed -e 's/utf8mb4_0900_ai_ci/utf8mb4_general_ci/g' \
            -e 's/utf8mb4_unicode_ci/utf8mb4_general_ci/g' \
            -e 's/utf8mb4_unicode_520_ci/utf8mb4_general_ci/g' \
            -e 's/utf8mb4_bin/utf8mb4_general_ci/g' \
            "$sql_file" > "$temp_file" || {
            log_error "转换表 $table 字符集格式失败"
            exit 1
        }

        # 替换原文件
        mv "$temp_file" "$sql_file" || {
            log_error "替换表 $table 文件失败"
            exit 1
        }

        # 验证转换结果
        local old_count=$(grep -c "utf8mb4_0900_ai_ci" "$sql_file" || echo "0")
        if [ "$old_count" -eq 0 ]; then
            log_success "表 $table 字符集格式转换完成"
        else
            log_warning "表 $table 仍有 $old_count 个未转换的字符集声明"
        fi
    done

    log_success "字符集格式转换完成"
}

# 传输数据到生产环境
transfer_data() {
    log_info "传输数据到生产环境..."
    
    local export_dir=$(cat local_export_info.txt | cut -d: -f2 | xargs)
    local remote_dir="/tmp/migration_$(date +%Y%m%d_%H%M%S)"
    
    # 在生产服务器上创建临时目录
    ssh $PROD_SERVER "mkdir -p $remote_dir"
    
    for table in "${TABLES[@]}"; do
        log_info "传输表数据: $table"
        
        scp $export_dir/${table}.sql $PROD_SERVER:$remote_dir/ || {
            log_error "传输表 $table 数据失败"
            exit 1
        }
        
        log_success "表 $table 数据传输完成"
    done
    
    echo "远程目录: $remote_dir" > remote_transfer_info.txt
    log_success "数据传输完成，远程目录: $remote_dir"
}

# 导入数据到生产环境
import_to_prod() {
    log_info "导入数据到生产环境..."
    
    local remote_dir=$(cat remote_transfer_info.txt | cut -d: -f2 | xargs)
    
    # 按依赖关系顺序导入
    for table in "${TABLES[@]}"; do
        log_info "导入生产环境表: $table"
        
        ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME < $remote_dir/${table}.sql" || {
            log_error "导入表 $table 失败"
            exit 1
        }
        
        local count=$(ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME -e 'SELECT COUNT(*) FROM $table;' -N")
        log_success "表 $table 导入完成 (记录数: $count)"
    done
}

# 验证迁移结果
verify_migration() {
    log_info "验证迁移结果..."
    
    echo "=========================================="
    echo "迁移结果对比"
    echo "=========================================="
    
    for table in "${TABLES[@]}"; do
        local local_count=$(mysql -u$LOCAL_DB_USER -p$LOCAL_DB_PASS $LOCAL_DB_NAME -e "SELECT COUNT(*) FROM $table;" -N)
        local prod_count=$(ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME -e 'SELECT COUNT(*) FROM $table;' -N")
        
        echo "表 $table:"
        echo "  本地环境: $local_count 条记录"
        echo "  生产环境: $prod_count 条记录"
        
        if [ "$local_count" -eq "$prod_count" ]; then
            log_success "表 $table 迁移验证通过"
        else
            log_error "表 $table 迁移验证失败 - 记录数不匹配"
            exit 1
        fi
        echo ""
    done
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    if [ -f "local_export_info.txt" ]; then
        local export_dir=$(cat local_export_info.txt | cut -d: -f2 | xargs)
        if [ -d "$export_dir" ]; then
            rm -rf $export_dir
            log_success "清理本地导出目录: $export_dir"
        fi
        rm -f local_export_info.txt
    fi
    
    if [ -f "remote_transfer_info.txt" ]; then
        local remote_dir=$(cat remote_transfer_info.txt | cut -d: -f2 | xargs)
        ssh $PROD_SERVER "rm -rf $remote_dir" 2>/dev/null || true
        log_success "清理远程临时目录: $remote_dir"
        rm -f remote_transfer_info.txt
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "生产环境数据迁移脚本"
    echo "目标服务器: $PROD_SERVER"
    echo "迁移表: ${TABLES[*]}"
    echo "=========================================="
    
    # 确认操作
    read -p "确认要执行生产环境迁移吗？这将清空生产环境的目标表数据！(y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行迁移步骤
    check_ssh_connection
    check_local_db
    check_prod_db
    check_local_tables
    backup_prod_data
    clear_prod_tables
    export_local_data
    convert_charset_format
    transfer_data
    import_to_prod
    verify_migration
    cleanup
    
    log_success "生产环境数据迁移完成！"
    
    if [ -f "prod_backup_info.txt" ]; then
        local backup_dir=$(cat prod_backup_info.txt | cut -d: -f2 | xargs)
        log_info "生产环境备份保存在: $PROD_SERVER:$backup_dir"
        rm -f prod_backup_info.txt
    fi
}

# 错误处理
trap 'log_error "脚本执行失败，正在清理..."; cleanup; exit 1' ERR

# 执行主函数
main "$@"
