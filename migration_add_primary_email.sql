-- 数据库迁移脚本：为 t_zoom_auth 表添加 primary_email 字段
-- 执行时间：2024-07-19
-- 目的：修复刷新Token时的数据异常问题

-- 检查字段是否已存在，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 't_zoom_auth' 
         AND COLUMN_NAME = 'primary_email') = 0,
        'ALTER TABLE t_zoom_auth ADD COLUMN primary_email VARCHAR(255) NOT NULL DEFAULT \'\' COMMENT \'主账号邮箱\' AFTER zoom_account_id',
        'SELECT "primary_email column already exists" as message'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果字段是新添加的）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 't_zoom_auth' 
         AND INDEX_NAME = 'idx_primary_email') = 0,
        'ALTER TABLE t_zoom_auth ADD INDEX idx_primary_email (primary_email)',
        'SELECT "idx_primary_email index already exists" as message'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果有现有数据，需要更新 primary_email 字段
-- 这里假设可以从 account_name 或其他字段推导出邮箱
-- 实际使用时需要根据具体情况调整
UPDATE t_zoom_auth 
SET primary_email = CONCAT(account_name, '@example.com') 
WHERE primary_email = '' OR primary_email IS NULL;

-- 修改 scope 字段类型为 TEXT（解决数据太长的问题）
ALTER TABLE t_zoom_auth MODIFY COLUMN scope TEXT;

-- 验证迁移结果
SELECT
    'Migration completed' as status,
    COUNT(*) as total_records,
    COUNT(CASE WHEN primary_email != '' THEN 1 END) as records_with_email
FROM t_zoom_auth;
