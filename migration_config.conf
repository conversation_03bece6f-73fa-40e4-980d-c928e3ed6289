# 数据迁移配置文件
# 请根据实际环境修改以下配置

# ========================================
# 测试环境配置
# ========================================
TEST_DB_HOST="localhost"
TEST_DB_USER="root"
TEST_DB_PASS="nvshen2018"
TEST_DB_NAME="zoombusV"

# ========================================
# 生产环境配置
# ========================================
PROD_SERVER="<EMAIL>"
PROD_DB_HOST="localhost"  # 生产服务器上的数据库地址
PROD_DB_USER="root"
PROD_DB_PASS="nvshen2018"
PROD_DB_NAME="zoombusV"

# ========================================
# SSH连接配置
# ========================================
SSH_PORT="22"
SSH_KEY_PATH=""  # SSH私钥路径，留空则使用密码认证

# 示例SSH私钥配置：
# SSH_KEY_PATH="/Users/<USER>/.ssh/id_rsa"
# SSH_KEY_PATH="/home/<USER>/.ssh/production_key"

# ========================================
# 迁移选项配置
# ========================================
# 是否在导入前清空目标表（谨慎使用）
TRUNCATE_BEFORE_IMPORT="false"

# 是否创建备份表
CREATE_BACKUP_TABLES="true"

# 备份保留天数
BACKUP_RETENTION_DAYS="7"

# ========================================
# 高级选项
# ========================================
# MySQL连接超时时间（秒）
MYSQL_CONNECT_TIMEOUT="30"

# SSH连接超时时间（秒）
SSH_CONNECT_TIMEOUT="10"

# 并发导入（实验性功能，建议保持false）
PARALLEL_IMPORT="false"

# 导入时的批处理大小
BATCH_SIZE="1000"
