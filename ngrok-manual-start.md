# ngrok手动启动指南

## 🚨 问题分析

从测试结果看，ngrok启动脚本在执行过程中导致SSH连接断开。这可能是因为：

1. **网络冲突**: ngrok启动时可能影响了网络配置
2. **进程冲突**: 可能有其他ngrok进程在运行
3. **权限问题**: ngrok可能需要特定权限

## 🔧 手动启动方法

### 方法1: 直接命令启动

```bash
# 1. SSH连接到服务器
ssh <EMAIL>

# 2. 进入zoombus目录
cd /root/zoombus

# 3. 清理旧进程
pkill -f ngrok 2>/dev/null || true

# 4. 直接启动ngrok
ngrok http 8080 --domain=patient-correctly-pipefish.ngrok-free.app
```

### 方法2: 后台启动

```bash
# 1. SSH连接到服务器
ssh <EMAIL>

# 2. 进入zoombus目录
cd /root/zoombus

# 3. 清理旧进程
pkill -f ngrok

# 4. 后台启动
nohup ngrok http 8080 --domain=patient-correctly-pipefish.ngrok-free.app > ngrok.log 2>&1 &

# 5. 查看进程ID
echo $! > ngrok.pid
cat ngrok.pid

# 6. 检查日志
tail -f ngrok.log
```

### 方法3: screen会话启动

```bash
# 1. 安装screen（如果没有）
yum install screen -y

# 2. 创建screen会话
screen -S ngrok

# 3. 在screen中启动ngrok
cd /root/zoombus
ngrok http 8080 --domain=patient-correctly-pipefish.ngrok-free.app

# 4. 分离screen会话（按 Ctrl+A 然后按 D）

# 5. 重新连接screen会话
screen -r ngrok
```

## 🔍 故障排除

### 检查ngrok状态

```bash
# 检查ngrok进程
ps aux | grep ngrok | grep -v grep

# 检查端口占用
netstat -tlnp | grep :4040

# 检查ngrok配置
ngrok config check

# 测试ngrok版本
ngrok version
```

### 检查网络连接

```bash
# 测试ngrok.com连接
ping ngrok.com

# 测试DNS解析
nslookup patient-correctly-pipefish.ngrok-free.app

# 检查防火墙
iptables -L
```

### 查看日志

```bash
# 查看ngrok日志
tail -f /root/zoombus/ngrok.log

# 查看系统日志
journalctl -f | grep ngrok
```

## 📋 验证步骤

### 1. 确认ngrok启动成功

```bash
# 检查进程
ps aux | grep ngrok

# 检查控制台
curl http://localhost:4040/api/tunnels

# 测试隧道
curl https://patient-correctly-pipefish.ngrok-free.app/actuator/health
```

### 2. 测试webhook端点

```bash
# 测试验证端点
curl -X POST https://patient-correctly-pipefish.ngrok-free.app/api/webhooks/zoom/KNDMAXZ_SVGeAgOTaK_TEw \
  -H "Content-Type: application/json" \
  -d '{"event":"endpoint.url_validation","payload":{"plainToken":"test123"}}'

# 期望响应
{"plainToken":"test123"}
```

## 🎯 推荐方案

### 临时解决方案
使用**方法2（后台启动）**，这样可以避免SSH连接问题：

```bash
ssh <EMAIL> 'cd /root/zoombus && nohup ngrok http 8080 --domain=patient-correctly-pipefish.ngrok-free.app > ngrok.log 2>&1 & echo $! > ngrok.pid'
```

### 长期解决方案
1. **使用生产环境**: 推荐使用 `https://m.zoombus.com` 而不是ngrok
2. **systemd服务**: 将ngrok配置为系统服务
3. **Docker容器**: 在容器中运行ngrok

## 🔄 重启ngrok

### 停止ngrok
```bash
# 方法1: 使用PID文件
kill $(cat /root/zoombus/ngrok.pid)

# 方法2: 杀死所有ngrok进程
pkill -f ngrok

# 方法3: 强制终止
pkill -9 -f ngrok
```

### 启动ngrok
```bash
cd /root/zoombus
nohup ngrok http 8080 --domain=patient-correctly-pipefish.ngrok-free.app > ngrok.log 2>&1 &
echo $! > ngrok.pid
```

## 💡 使用建议

1. **开发测试**: 使用ngrok进行临时测试
2. **生产环境**: 使用固定域名 `https://m.zoombus.com`
3. **监控**: 定期检查ngrok进程状态
4. **备份方案**: 准备多种启动方法

## 🎉 成功标志

当ngrok成功启动时，您应该看到：

```
Session Status                online
Account                       your-account (Plan: Free)
Version                       3.25.0
Region                        United States (us)
Latency                       42ms
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://patient-correctly-pipefish.ngrok-free.app -> http://localhost:8080

Connections                   ttl     opn     rt1     rt5     p50     p90
                              0       0       0.00    0.00    0.00    0.00
```

此时您的开发环境webhook URL就可以正常使用了！
