-- 优化窗口逻辑：为单日窗口设置 end_date，统一窗口关闭判断逻辑
-- 执行日期: 2025-08-20

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 1. 检查当前数据状态
-- ========================================

SELECT '=== 当前窗口 end_date 状态检查 ===' as step;

SELECT 
    COUNT(*) as total_windows,
    COUNT(CASE WHEN end_date IS NULL THEN 1 END) as null_end_date_windows,
    COUNT(CASE WHEN end_date IS NOT NULL THEN 1 END) as has_end_date_windows
FROM t_pmi_schedule_windows;

-- 显示 end_date 为 NULL 的窗口详情（如果有的话）
SELECT '=== NULL end_date 窗口详情 ===' as step;

SELECT 
    id, 
    schedule_id,
    window_date, 
    end_date, 
    start_time, 
    end_time, 
    status,
    created_at
FROM t_pmi_schedule_windows 
WHERE end_date IS NULL
LIMIT 10;

-- ========================================
-- 2. 更新 NULL end_date 的窗口
-- ========================================

-- 为 end_date 为 NULL 的单日窗口设置 end_date = window_date
UPDATE t_pmi_schedule_windows 
SET 
    end_date = window_date,
    updated_at = NOW()
WHERE end_date IS NULL;

-- 记录更新结果
SELECT '=== 更新结果 ===' as step;

SELECT 
    ROW_COUNT() as updated_rows,
    'end_date 设置为 window_date' as operation;

-- ========================================
-- 3. 验证更新后的状态
-- ========================================

SELECT '=== 更新后状态验证 ===' as step;

SELECT 
    COUNT(*) as total_windows,
    COUNT(CASE WHEN end_date IS NULL THEN 1 END) as null_end_date_windows,
    COUNT(CASE WHEN end_date IS NOT NULL THEN 1 END) as has_end_date_windows,
    COUNT(CASE WHEN end_date = window_date THEN 1 END) as single_day_windows,
    COUNT(CASE WHEN end_date > window_date THEN 1 END) as multi_day_windows
FROM t_pmi_schedule_windows;

-- ========================================
-- 4. 测试优化后的查询逻辑
-- ========================================

SELECT '=== 测试优化后的查询逻辑 ===' as step;

-- 模拟查询需要关闭的窗口（使用优化后的逻辑）
-- 测试今天应该关闭的窗口
SELECT 
    'today_should_close' as test_case,
    COUNT(*) as window_count
FROM t_pmi_schedule_windows w 
WHERE w.status = 'ACTIVE' 
AND (CURDATE() > w.end_date OR (CURDATE() = w.end_date AND CURTIME() >= w.end_time));

-- 测试明天应该关闭的窗口
SELECT 
    'tomorrow_should_close' as test_case,
    COUNT(*) as window_count
FROM t_pmi_schedule_windows w 
WHERE w.status = 'ACTIVE' 
AND (DATE_ADD(CURDATE(), INTERVAL 1 DAY) > w.end_date OR 
     (DATE_ADD(CURDATE(), INTERVAL 1 DAY) = w.end_date AND '23:59:59' >= w.end_time));

-- 显示一些示例窗口的关闭判断
SELECT 
    'sample_windows_close_logic' as test_case,
    id,
    window_date,
    end_date,
    start_time,
    end_time,
    status,
    -- 今天是否应该关闭
    (CURDATE() > end_date OR (CURDATE() = end_date AND CURTIME() >= end_time)) as should_close_today,
    -- 在 end_date 当天 23:59:59 是否应该关闭
    (CURDATE() > end_date OR (CURDATE() = end_date AND '23:59:59' >= end_time)) as should_close_at_end_date
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE'
ORDER BY end_date
LIMIT 5;

-- 提交事务
COMMIT;

-- ========================================
-- 5. 总结报告
-- ========================================

SELECT '=== 优化完成总结 ===' as summary;

SELECT 
    '窗口逻辑优化' as item,
    '所有窗口现在都有 end_date，统一了关闭判断逻辑' as description
UNION ALL
SELECT 
    '查询简化' as item,
    '移除了对 endDate IS NULL 的复杂判断，统一使用 end_date 进行关闭判断' as description
UNION ALL
SELECT 
    '代码优化' as item,
    '简化了 PmiScheduleService、PmiStatusScheduler、PmiWindowCheckService 中的逻辑' as description
UNION ALL
SELECT 
    '数据一致性' as item,
    '确保了单日窗口和多日窗口使用相同的关闭判断逻辑' as description;

SELECT 'Window end_date optimization completed successfully!' as final_message;
