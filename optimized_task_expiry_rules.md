# PMI任务过期规则优化

## 🎯 **优化目标**

### **问题描述**
原有的任务过期规则过于简单：
- 所有过期任务（开启/关闭）都使用相同的5分钟规则
- 如果开启任务延迟超过5分钟就标记为失败
- 这导致即使窗口还没关闭，开启任务也可能被错误地标记为过期

### **优化需求**
- **开启任务**：只要还没到窗口关闭时间，就不应该过期
- **关闭任务**：保持原有的5分钟规则（因为关闭任务延迟太久没有意义）

## ✅ **优化方案**

### **新的过期规则**

#### **开启任务过期规则**
```java
// 检查是否还在窗口关闭时间之前
if (isOpenTaskStillValid(task, now)) {
    // 立即执行，无论延迟多久
    taskExecutor.executeOpenTask(task.getPmiWindowId(), task.getId());
} else {
    // 只有超过窗口关闭时间才标记为失败
    task.markAsFailed("任务过期，窗口已关闭");
}
```

#### **关闭任务过期规则**
```java
// 保持原有的5分钟规则
if (delayMinutes <= 5) {
    taskExecutor.executeCloseTask(task.getPmiWindowId(), task.getId());
} else {
    task.markAsFailed("任务过期，延迟" + delayMinutes + "分钟");
}
```

### **核心实现**

#### **1. 优化后的过期处理逻辑**
```java
private void handleExpiredPmiTask(PmiScheduleWindowTask task) {
    LocalDateTime now = LocalDateTime.now();
    long delayMinutes = Duration.between(task.getScheduledTime(), now).toMinutes();

    if (task.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
        // 开启任务：检查是否还在窗口关闭时间之前
        if (isOpenTaskStillValid(task, now)) {
            log.info("开启任务仍在有效期内，立即执行: taskId={}, delay={}分钟", 
                    task.getId(), delayMinutes);
            taskExecutor.executeOpenTask(task.getPmiWindowId(), task.getId());
        } else {
            log.warn("开启任务已超过窗口关闭时间，标记为失败: taskId={}", task.getId());
            task.markAsFailed("任务过期，窗口已关闭，延迟" + delayMinutes + "分钟");
            taskRepository.save(task);
        }
    } else {
        // 关闭任务：使用原有的5分钟规则
        if (delayMinutes <= 5) {
            taskExecutor.executeCloseTask(task.getPmiWindowId(), task.getId());
        } else {
            task.markAsFailed("任务过期，延迟" + delayMinutes + "分钟");
            taskRepository.save(task);
        }
    }
}
```

#### **2. 开启任务有效性检查**
```java
private boolean isOpenTaskStillValid(PmiScheduleWindowTask task, LocalDateTime now) {
    try {
        // 获取PMI窗口信息
        Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(task.getPmiWindowId());
        if (!windowOpt.isPresent()) {
            log.warn("PMI窗口不存在，开启任务无效: windowId={}", task.getPmiWindowId());
            return false;
        }

        PmiScheduleWindow window = windowOpt.get();
        LocalDateTime windowCloseTime = window.getEndDateTime();

        // 如果当前时间还在窗口关闭时间之前，则任务仍然有效
        boolean isValid = now.isBefore(windowCloseTime);
        
        log.debug("检查开启任务有效性: taskId={}, windowCloseTime={}, now={}, isValid={}", 
                task.getId(), windowCloseTime, now, isValid);
        
        return isValid;
        
    } catch (Exception e) {
        log.error("检查开启任务有效性失败: taskId={}", task.getId(), e);
        // 出现异常时，为了安全起见，认为任务无效
        return false;
    }
}
```

## 🎯 **优化效果**

### **场景对比**

#### **场景1：开启任务延迟10分钟，但窗口还有1小时才关闭**

**优化前**：
- ❌ 延迟10分钟 > 5分钟，标记为失败
- ❌ PMI窗口无法开启，用户无法使用

**优化后**：
- ✅ 检查窗口关闭时间，发现还有1小时
- ✅ 立即执行开启任务，PMI窗口正常开启

#### **场景2：开启任务延迟2小时，窗口已经关闭**

**优化前**：
- ❌ 延迟2小时 > 5分钟，标记为失败

**优化后**：
- ✅ 检查窗口关闭时间，发现已经过期
- ✅ 标记为失败，避免无意义的执行

#### **场景3：关闭任务延迟10分钟**

**优化前**：
- ❌ 延迟10分钟 > 5分钟，标记为失败

**优化后**：
- ✅ 保持原有规则，延迟10分钟 > 5分钟，标记为失败
- ✅ 关闭任务延迟太久确实没有意义

### **业务价值**

#### **1. 提高任务执行成功率**
- **开启任务**：只要窗口还没关闭，就能成功执行
- **用户体验**：减少因系统延迟导致的PMI窗口开启失败

#### **2. 更合理的失败判断**
- **有意义的失败**：只有真正无法执行的任务才标记为失败
- **减少误报**：避免因短暂延迟导致的错误失败标记

#### **3. 系统容错性增强**
- **网络延迟容忍**：能够容忍网络或系统临时延迟
- **服务重启恢复**：系统重启后能够正确处理积压的开启任务

## 📊 **测试场景**

### **测试用例1：正常延迟的开启任务**
```
窗口时间：2025-08-24 10:00:00 - 2025-08-24 12:00:00
开启任务计划时间：2025-08-24 10:00:00
实际执行时间：2025-08-24 10:15:00（延迟15分钟）
预期结果：✅ 立即执行（因为还没到12:00:00）
```

### **测试用例2：严重延迟的开启任务**
```
窗口时间：2025-08-24 10:00:00 - 2025-08-24 12:00:00
开启任务计划时间：2025-08-24 10:00:00
实际执行时间：2025-08-24 13:00:00（延迟3小时）
预期结果：❌ 标记为失败（因为窗口已在12:00:00关闭）
```

### **测试用例3：延迟的关闭任务**
```
关闭任务计划时间：2025-08-24 12:00:00
实际执行时间：2025-08-24 12:10:00（延迟10分钟）
预期结果：❌ 标记为失败（延迟超过5分钟）
```

### **测试用例4：轻微延迟的关闭任务**
```
关闭任务计划时间：2025-08-24 12:00:00
实际执行时间：2025-08-24 12:03:00（延迟3分钟）
预期结果：✅ 立即执行（延迟不超过5分钟）
```

## 🔧 **部署和验证**

### **1. 代码部署**
- ✅ 已修改 `DynamicTaskManagerImpl.handleExpiredPmiTask()` 方法
- ✅ 已添加 `isOpenTaskStillValid()` 方法
- ✅ 已优化过期规则逻辑

### **2. 功能验证**
1. **创建测试PMI窗口**：设置较长的窗口时间（如2小时）
2. **模拟延迟场景**：手动延迟开启任务执行
3. **检查执行结果**：验证任务是否按新规则执行

### **3. 日志监控**
关注以下日志：
```
开启任务仍在有效期内，立即执行: taskId=X, delay=Y分钟
开启任务已超过窗口关闭时间，标记为失败: taskId=X
检查开启任务有效性: taskId=X, windowCloseTime=Y, now=Z, isValid=true/false
```

## 🎉 **总结**

### **优化成果**
1. ✅ **开启任务过期规则优化**：基于窗口关闭时间而不是固定延迟时间
2. ✅ **关闭任务规则保持**：维持原有的5分钟规则
3. ✅ **系统容错性增强**：能够处理各种延迟场景
4. ✅ **业务逻辑更合理**：符合实际业务需求

### **预期改进**
- **任务成功率提升**：减少因系统延迟导致的开启任务失败
- **用户体验改善**：PMI窗口能够在更多情况下正常开启
- **系统稳定性增强**：更好地处理网络延迟和系统重启场景

## 🔄 **关闭任务过期规则优化**

### **新增优化需求**
- **关闭任务责任制**：如果PMI的当前窗口仍然是该任务对应的窗口，则关闭任务有责任关闭它，不应该过期
- **避免PMI无法关闭**：确保由当前窗口开启的PMI能够被正确关闭

### **关闭任务新规则**

#### **优化后的关闭任务过期逻辑**
```java
// 关闭任务：检查PMI的当前窗口是否仍然是该任务对应的窗口
if (isCloseTaskStillResponsible(task, now)) {
    // 立即执行，无论延迟多久
    taskExecutor.executeCloseTask(task.getPmiWindowId(), task.getId());
} else {
    // 只有当PMI当前窗口已变更时才标记为失败
    task.markAsFailed("任务过期，PMI当前窗口已变更");
}
```

#### **关闭任务责任检查**
```java
private boolean isCloseTaskStillResponsible(PmiScheduleWindowTask task, LocalDateTime now) {
    // 获取PMI窗口信息
    PmiScheduleWindow window = windowRepository.findById(task.getPmiWindowId());

    // 获取该窗口对应的PMI记录
    PmiRecord pmiRecord = getPmiRecordFromWindow(window);

    // 检查PMI的当前窗口是否仍然是该任务对应的窗口
    return Objects.equals(pmiRecord.getCurrentWindowId(), task.getPmiWindowId());
}
```

### **完整的优化规则对比**

#### **开启任务过期规则**
```java
// 优化前：固定5分钟规则
if (delayMinutes <= 5) {
    executeOpenTask();
} else {
    markAsFailed("延迟超过5分钟");
}

// 优化后：基于窗口关闭时间
if (now.isBefore(window.getEndDateTime())) {
    executeOpenTask(); // 只要窗口还没关闭就执行
} else {
    markAsFailed("窗口已关闭");
}
```

#### **关闭任务过期规则**
```java
// 优化前：固定5分钟规则
if (delayMinutes <= 5) {
    executeCloseTask();
} else {
    markAsFailed("延迟超过5分钟");
}

// 优化后：基于PMI当前窗口责任
if (pmiRecord.getCurrentWindowId().equals(task.getPmiWindowId())) {
    executeCloseTask(); // 只要仍有责任就执行
} else {
    markAsFailed("PMI当前窗口已变更");
}
```

### **关闭任务场景分析**

#### **场景1：关闭任务延迟1小时，但PMI仍在该窗口中**
```
窗口时间：2025-08-24 10:00:00 - 2025-08-24 12:00:00
关闭任务计划时间：2025-08-24 12:00:00
实际执行时间：2025-08-24 13:00:00（延迟1小时）
PMI当前窗口：仍然是该窗口ID
```

**优化前**：
- ❌ 延迟1小时 > 5分钟，标记为失败
- ❌ PMI无法关闭，可能导致计费问题

**优化后**：
- ✅ 检查PMI当前窗口，仍然是该窗口
- ✅ 立即执行关闭任务，PMI正常关闭

#### **场景2：关闭任务延迟，但PMI已切换到新窗口**
```
窗口A时间：2025-08-24 10:00:00 - 2025-08-24 12:00:00
窗口B时间：2025-08-24 11:30:00 - 2025-08-24 14:00:00
窗口A关闭任务计划时间：2025-08-24 12:00:00
实际执行时间：2025-08-24 13:00:00（延迟1小时）
PMI当前窗口：已切换到窗口B
```

**优化前**：
- ❌ 延迟1小时 > 5分钟，标记为失败

**优化后**：
- ✅ 检查PMI当前窗口，已切换到窗口B
- ✅ 标记为失败，避免错误关闭正在使用的PMI

#### **场景3：系统重启导致的关闭任务积压**
```
多个窗口的关闭任务因系统重启而积压
部分PMI仍在原窗口中，部分已切换到新窗口
```

**优化前**：
- ❌ 所有延迟超过5分钟的关闭任务都失败
- ❌ 部分PMI无法关闭，部分PMI被错误关闭

**优化后**：
- ✅ 只有仍有责任的关闭任务才执行
- ✅ 避免错误关闭，确保PMI状态正确

## 🎉 **最终总结**

### **完整的优化成果**
1. ✅ **开启任务过期规则优化**：基于窗口关闭时间，只要窗口还没关闭就不会过期
2. ✅ **关闭任务过期规则优化**：基于PMI当前窗口责任，只要仍有责任就不会过期
3. ✅ **系统容错性全面增强**：能够处理各种延迟和异常场景
4. ✅ **业务逻辑更加合理**：符合实际业务需求和用户期望

### **预期改进效果**
- **任务成功率大幅提升**：减少因系统延迟导致的任务失败
- **用户体验显著改善**：PMI窗口能够在更多情况下正常开启和关闭
- **系统稳定性增强**：更好地处理网络延迟、系统重启等场景
- **计费准确性提高**：确保PMI能够正确关闭，避免计费问题

**🎉 现在PMI任务的过期规则已经全面优化完成！开启任务和关闭任务都有了更合理的过期判断逻辑，大大提高了系统的可靠性和用户体验！**
