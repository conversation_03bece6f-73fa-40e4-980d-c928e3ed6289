-- PMI计费系统数据库迁移脚本
-- 基于accounting.md设计文档
-- 创建日期: 2025-07-31

USE zoombusV;

-- 1. 扩展t_pmi_records表，添加计费相关字段
-- 检查并添加列（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'billing_mode') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN billing_mode ENUM(''LONG'', ''BY_TIME'') DEFAULT ''BY_TIME'' COMMENT ''计费模式：LONG-按时段，BY_TIME-按时长''',
    'SELECT ''Column billing_mode already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加其他计费相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'current_window_id') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN current_window_id BIGINT NULL COMMENT ''当前关联的时间窗口ID''',
    'SELECT ''Column current_window_id already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'window_expire_time') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN window_expire_time DATETIME NULL COMMENT ''窗口到期时间''',
    'SELECT ''Column window_expire_time already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加按时长计费相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'total_minutes') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN total_minutes INT DEFAULT 0 COMMENT ''总时长(分钟)''',
    'SELECT ''Column total_minutes already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'available_minutes') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN available_minutes INT DEFAULT 0 COMMENT ''可用时长(分钟)''',
    'SELECT ''Column available_minutes already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'pending_deduct_minutes') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN pending_deduct_minutes INT DEFAULT 0 COMMENT ''待扣除时长(分钟)''',
    'SELECT ''Column pending_deduct_minutes already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'overdraft_minutes') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN overdraft_minutes INT DEFAULT 0 COMMENT ''超额时长(分钟)''',
    'SELECT ''Column overdraft_minutes already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'total_used_minutes') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN total_used_minutes INT DEFAULT 0 COMMENT ''总使用时长(分钟)''',
    'SELECT ''Column total_used_minutes already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'billing_status') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN billing_status ENUM(''ACTIVE'', ''SUSPENDED'', ''EXPIRED'') DEFAULT ''ACTIVE'' COMMENT ''计费状态''',
    'SELECT ''Column billing_status already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'last_billing_time') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN last_billing_time DATETIME NULL COMMENT ''最后计费时间''',
    'SELECT ''Column last_billing_time already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_pmi_records' AND COLUMN_NAME = 'billing_updated_at') = 0,
    'ALTER TABLE t_pmi_records ADD COLUMN billing_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    'SELECT ''Column billing_updated_at already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 创建Zoom会议实体表
CREATE TABLE t_zoom_meetings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- 会议基本信息
    pmi_record_id BIGINT NOT NULL COMMENT 'PMI记录ID',
    zoom_meeting_uuid VARCHAR(255) UNIQUE NOT NULL COMMENT 'Zoom会议UUID',
    zoom_meeting_id VARCHAR(50) NOT NULL COMMENT 'Zoom会议ID',
    
    -- 会议详情
    topic VARCHAR(255) COMMENT '会议主题',
    host_id VARCHAR(100) COMMENT '主持人ID',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration_minutes INT DEFAULT 0 COMMENT '会议时长(分钟)',
    
    -- 会议状态
    status ENUM('PENDING', 'USING', 'ENDED', 'SETTLED') DEFAULT 'PENDING' COMMENT '会议状态',
    
    -- 计费相关
    billing_mode ENUM('LONG', 'BY_TIME') COMMENT '计费模式',
    billed_minutes INT DEFAULT 0 COMMENT '已计费分钟数',
    is_settled BOOLEAN DEFAULT FALSE COMMENT '是否已结算',
    
    -- ZoomUser分配信息
    assigned_zoom_user_id BIGINT COMMENT '分配的ZoomUser ID',
    assigned_zoom_user_email VARCHAR(255) COMMENT '分配的ZoomUser邮箱',
    assignment_error TEXT COMMENT 'ZoomUser分配错误信息',
    
    -- 审计字段
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_pmi_record_id (pmi_record_id),
    INDEX idx_zoom_uuid (zoom_meeting_uuid),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_assigned_zoom_user_id (assigned_zoom_user_id),
    
    -- 外键约束
    FOREIGN KEY (pmi_record_id) REFERENCES t_pmi_records(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_zoom_user_id) REFERENCES t_zoom_accounts(id) ON DELETE SET NULL
);

-- 3. 创建PMI时长流水记录表
CREATE TABLE t_pmi_billing_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- 关联信息
    pmi_record_id BIGINT NOT NULL COMMENT 'PMI记录ID',
    zoom_meeting_id BIGINT NULL COMMENT '关联的Zoom会议ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 交易信息
    transaction_type ENUM('RECHARGE', 'DEDUCT', 'REFUND', 'ADJUSTMENT') NOT NULL COMMENT '交易类型',
    amount_minutes INT NOT NULL COMMENT '时长变动(分钟)',
    balance_before INT NOT NULL COMMENT '变动前余额',
    balance_after INT NOT NULL COMMENT '变动后余额',
    
    -- 业务信息
    description VARCHAR(500) COMMENT '交易描述',
    billing_period_start DATETIME COMMENT '计费周期开始时间',
    billing_period_end DATETIME COMMENT '计费周期结束时间',
    
    -- 状态信息
    status ENUM('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED') DEFAULT 'PENDING',
    
    -- 审计字段
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) COMMENT '创建人',
    
    -- 索引
    INDEX idx_pmi_record_id (pmi_record_id),
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_created_at (created_at),
    INDEX idx_zoom_meeting_id (zoom_meeting_id),
    
    -- 外键约束
    FOREIGN KEY (pmi_record_id) REFERENCES t_pmi_records(id),
    FOREIGN KEY (zoom_meeting_id) REFERENCES t_zoom_meetings(id),
    FOREIGN KEY (user_id) REFERENCES t_users(id)
);

-- 4. 扩展t_zoom_accounts表，添加PMI相关字段
-- 检查并添加列（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND COLUMN_NAME = 'original_pmi') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN original_pmi VARCHAR(50) COMMENT ''账号原始默认PMI''',
    'SELECT ''Column original_pmi already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND COLUMN_NAME = 'current_pmi') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN current_pmi VARCHAR(50) COMMENT ''当前使用的PMI''',
    'SELECT ''Column current_pmi already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND COLUMN_NAME = 'usage_status') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN usage_status ENUM(''AVAILABLE'', ''IN_USE'', ''MAINTENANCE'') DEFAULT ''AVAILABLE'' COMMENT ''使用状态''',
    'SELECT ''Column usage_status already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND COLUMN_NAME = 'current_meeting_id') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN current_meeting_id BIGINT NULL COMMENT ''当前会议ID''',
    'SELECT ''Column current_meeting_id already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND COLUMN_NAME = 'last_used_time') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN last_used_time DATETIME NULL COMMENT ''最后使用时间''',
    'SELECT ''Column last_used_time already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND COLUMN_NAME = 'total_usage_count') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN total_usage_count INT DEFAULT 0 COMMENT ''总使用次数''',
    'SELECT ''Column total_usage_count already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND COLUMN_NAME = 'total_usage_minutes') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN total_usage_minutes INT DEFAULT 0 COMMENT ''总使用时长(分钟)''',
    'SELECT ''Column total_usage_minutes already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND COLUMN_NAME = 'pmi_updated_at') = 0,
    'ALTER TABLE t_zoom_accounts ADD COLUMN pmi_updated_at DATETIME NULL COMMENT ''PMI最后更新时间''',
    'SELECT ''Column pmi_updated_at already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND INDEX_NAME = 'idx_usage_status') = 0,
    'ALTER TABLE t_zoom_accounts ADD INDEX idx_usage_status (usage_status)',
    'SELECT ''Index idx_usage_status already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND INDEX_NAME = 'idx_original_pmi') = 0,
    'ALTER TABLE t_zoom_accounts ADD INDEX idx_original_pmi (original_pmi)',
    'SELECT ''Index idx_original_pmi already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'zoombusV' AND TABLE_NAME = 't_zoom_accounts' AND INDEX_NAME = 'idx_current_pmi') = 0,
    'ALTER TABLE t_zoom_accounts ADD INDEX idx_current_pmi (current_pmi)',
    'SELECT ''Index idx_current_pmi already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 创建PMI计费配置表
CREATE TABLE t_pmi_billing_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,

    -- 配置信息
    config_name VARCHAR(100) UNIQUE NOT NULL COMMENT '配置名称',
    billing_unit_minutes INT DEFAULT 1 COMMENT '计费单位(分钟)',
    min_billing_minutes INT DEFAULT 1 COMMENT '最小计费时长',
    grace_period_minutes INT DEFAULT 0 COMMENT '免费时长(分钟)',

    -- 费率配置
    rate_per_minute DECIMAL(10,4) DEFAULT 0.0000 COMMENT '每分钟费率',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '货币单位',

    -- 状态
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    effective_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',

    -- 审计字段
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入默认计费配置
INSERT INTO t_pmi_billing_config (config_name, billing_unit_minutes, min_billing_minutes, grace_period_minutes, rate_per_minute, currency, is_active) 
VALUES ('默认计费配置', 1, 1, 0, 0.1000, 'CNY', TRUE);

COMMIT;
