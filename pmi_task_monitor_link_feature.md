# PMI任务监控页面链接功能实现

## 🎯 **功能需求**
在PMI任务监控页面的"PMI信息"列中添加到PMI管理页面的链接，使用路径模式的URL。

## ✅ **实现方案**

### **1. 后端数据结构优化**

#### **添加PMI记录ID字段**
在 `PmiScheduledTaskInfo` DTO中添加了 `pmiRecordId` 字段：

```java
// 关联信息
private Long pmiRecordId;  // 新增字段
private String pmiNumber;
private String pmiPassword;
private String userName;
private String scheduleName;
```

#### **服务层数据填充**
在 `PmiTaskManagementService.convertToTaskInfo()` 方法中设置PMI记录ID：

```java
// 获取关联的PMI信息
Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(task.getPmiWindowId());
if (windowOpt.isPresent()) {
    PmiScheduleWindow window = windowOpt.get();
    info.setPmiRecordId(window.getPmiRecordId()); // 设置PMI记录ID
    // ... 其他信息设置
}
```

### **2. 前端路由配置**

#### **添加新路由**
在 `App.js` 中添加支持PMI记录ID的路由：

```javascript
<Route path="pmi-management" element={<PmiManagement />} />
<Route path="pmi-management/:pmiRecordId" element={<PmiManagement />} />  // 新增路由
<Route path="pmi-management/user/:userId" element={<PmiManagement />} />
```

### **3. PMI任务监控页面链接**

#### **桌面端表格列**
修改 `PmiTaskMonitor.jsx` 中的PMI信息列：

```javascript
{
  title: 'PMI信息',
  key: 'pmiInfo',
  width: 200,
  render: (_, record) => (
    <div>
      <div>
        {record.pmiRecordId ? (
          <Link 
            to={`/pmi-management/${record.pmiRecordId}`}
            style={{ 
              fontWeight: 'bold',
              color: '#1890ff',
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
            onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
          >
            {record.pmiNumber}
          </Link>
        ) : (
          <span style={{ fontWeight: 'bold' }}>{record.pmiNumber}</span>
        )}
      </div>
      <div style={{ fontSize: '12px', color: '#666' }}>
        {record.userName}
      </div>
    </div>
  )
}
```

#### **移动端任务卡片**
修改 `MobileTaskCard.jsx` 中的PMI信息显示：

```javascript
{/* PMI信息 */}
{task.pmiNumber && (
  <div style={{ marginBottom: 4 }}>
    <Text type="secondary" style={{ fontSize: 12 }}>PMI: </Text>
    {task.pmiRecordId ? (
      <Link 
        to={`/pmi-management/${task.pmiRecordId}`}
        style={{ 
          fontSize: 12,
          fontWeight: 'bold',
          color: '#1890ff',
          textDecoration: 'none'
        }}
      >
        {task.pmiNumber}
      </Link>
    ) : (
      <Text strong style={{ fontSize: 12 }}>{task.pmiNumber}</Text>
    )}
    {task.userName && (
      <Text type="secondary" style={{ fontSize: 12, marginLeft: 8 }}>
        ({task.userName})
      </Text>
    )}
  </div>
)}
```

### **4. PMI管理页面路径参数处理**

#### **支持PMI记录ID路径参数**
修改 `PmiManagement.js` 中的 `useEffect`：

```javascript
const initializePage = async () => {
  // 处理路径参数和URL参数
  const userId = params.userId;
  const pmiRecordId = params.pmiRecordId;  // 新增
  const showCreateModal = location.pathname.includes('/create');

  // 处理URL搜索参数
  const urlParams = new URLSearchParams(location.search);
  const searchParam = urlParams.get('search');
  const pmiNumberParam = urlParams.get('pmiNumber');

  // 如果有pmiRecordId路径参数，优先使用它
  let finalSearchParam = searchParam;
  if (pmiRecordId) {
    // 通过PMI记录ID获取PMI号码进行搜索
    try {
      const pmiResponse = await pmiApi.getPmiRecordById(parseInt(pmiRecordId));
      if (pmiResponse.data && pmiResponse.data.pmiNumber) {
        finalSearchParam = pmiResponse.data.pmiNumber;
      }
    } catch (error) {
      console.error('Error loading PMI record:', error);
      message.error('PMI记录不存在');
    }
  } else if (pmiNumberParam) {
    finalSearchParam = pmiNumberParam;
  }
  
  // ... 其他逻辑
};
```

## 🎯 **功能特点**

### **1. 路径模式URL**
- ✅ 使用 `/pmi-management/{pmiRecordId}` 格式
- ✅ 符合RESTful设计规范
- ✅ URL更简洁美观

### **2. 精确定位**
- ✅ 使用PMI记录ID而不是PMI号码
- ✅ 避免模糊搜索，直接定位到具体记录
- ✅ 提高查找效率

### **3. 兼容性处理**
- ✅ 如果没有PMI记录ID，显示普通文本
- ✅ 支持错误处理，PMI记录不存在时显示错误信息
- ✅ 保持原有功能不受影响

### **4. 响应式设计**
- ✅ 桌面端和移动端都支持链接功能
- ✅ 移动端优化的触摸体验
- ✅ 一致的视觉效果

## 🔧 **测试方法**

### **1. 功能测试**
1. 打开PMI任务监控页面：`http://localhost:3000/pmi-task-monitor`
2. 查看"PMI信息"列中的PMI号码是否显示为蓝色链接
3. 点击链接，验证是否跳转到对应的PMI管理页面
4. 验证URL格式：`http://localhost:3000/pmi-management/{pmiRecordId}`

### **2. 移动端测试**
1. 在移动设备或开发者工具的移动模式下访问页面
2. 查看任务卡片中的PMI信息是否显示为链接
3. 点击链接验证跳转功能

### **3. 边界情况测试**
1. 测试没有PMI记录ID的任务（应显示普通文本）
2. 测试无效的PMI记录ID（应显示错误信息）
3. 测试网络错误情况的处理

## 📊 **预期效果**

### **用户体验提升**
- ✅ 一键从任务监控跳转到PMI管理
- ✅ 快速查看PMI详细信息
- ✅ 提高工作效率

### **数据一致性**
- ✅ 使用PMI记录ID确保精确匹配
- ✅ 避免PMI号码重复导致的问题
- ✅ 数据关联更加可靠

### **系统集成**
- ✅ 增强模块间的连接性
- ✅ 提供更好的导航体验
- ✅ 符合现代Web应用的交互模式

## 📝 **总结**

通过在PMI任务监控页面的"PMI信息"列中添加链接功能，用户可以直接点击PMI号码跳转到对应的PMI管理页面。该功能使用路径模式的URL（`/pmi-management/{pmiRecordId}`），通过PMI记录ID进行精确定位，提供了更好的用户体验和系统集成度。

**🎉 功能已完成实现，支持桌面端和移动端，具备完善的错误处理和兼容性保障！**
