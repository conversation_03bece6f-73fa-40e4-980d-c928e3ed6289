-- 回填修复：把已存在窗口的 window_date 修正为对应 schedule.start_date（仅修复有误可能性场景）
-- 执行小心：先检查，再执行更新

USE zoombusV;

-- 1) 预检：统计需要更新的条数
SELECT 'PRECHECK' AS step;
SELECT COUNT(*) AS total, COUNT(CASE WHEN w.window_date <> s.start_date THEN 1 END) AS need_update
FROM t_pmi_schedule_windows w
JOIN t_pmi_schedules s ON w.schedule_id = s.id;

-- 2) 列出样例（仅当存在不一致时）
SELECT 'SAMPLE' AS step;
SELECT w.id, w.window_date, s.start_date AS should_be, w.end_date
FROM t_pmi_schedule_windows w
JOIN t_pmi_schedules s ON w.schedule_id = s.id
WHERE w.window_date <> s.start_date
LIMIT 20;

-- 3) 仅在确认后执行更新（确保 end_date >= start_date 的正常场景）
-- 为了安全，这里加上保护条件：只更新 end_date >= start_date 的记录
UPDATE t_pmi_schedule_windows w
JOIN t_pmi_schedules s ON w.schedule_id = s.id
SET w.window_date = s.start_date
WHERE w.window_date <> s.start_date
  AND s.end_date >= s.start_date;

-- 4) 验证更新结果
SELECT 'POSTCHECK' AS step;
SELECT COUNT(*) AS total, COUNT(CASE WHEN w.window_date <> s.start_date THEN 1 END) AS need_update
FROM t_pmi_schedule_windows w
JOIN t_pmi_schedules s ON w.schedule_id = s.id;

