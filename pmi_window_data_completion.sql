-- PMI窗口数据补全脚本
-- 用途：关联t_pmi_schedule_windows补全t_pmi_records的current_window_id和window_expire_time字段
-- 执行日期: 2025-08-18

USE zoombusV;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 数据补全前的状态检查
-- ========================================
SELECT '=== 补全前数据状态检查 ===' as step;

-- 检查LONG类型PMI总数
SELECT 
    'LONG类型PMI总数' as item,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- 检查缺少current_window_id的LONG类型PMI
SELECT 
    '缺少current_window_id的LONG类型PMI' as item,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG' 
AND current_window_id IS NULL;

-- 检查缺少window_expire_time的LONG类型PMI
SELECT 
    '缺少window_expire_time的LONG类型PMI' as item,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG' 
AND window_expire_time IS NULL;

-- ========================================
-- 第一步：为有活跃窗口的LONG类型PMI补全字段
-- ========================================
SELECT '=== 第一步：补全有活跃窗口的PMI ===' as step;

UPDATE t_pmi_records pmi
JOIN (
    SELECT 
        w.pmi_record_id,
        w.id as window_id,
        CASE 
            WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
            ELSE TIMESTAMP(w.window_date, w.end_time)
        END as expire_time,
        ROW_NUMBER() OVER (PARTITION BY w.pmi_record_id ORDER BY w.actual_start_time DESC, w.created_at DESC) as rn
    FROM t_pmi_schedule_windows w
    WHERE w.status = 'ACTIVE'
) latest_active ON pmi.id = latest_active.pmi_record_id AND latest_active.rn = 1
SET 
    pmi.current_window_id = latest_active.window_id,
    pmi.window_expire_time = latest_active.expire_time,
    pmi.active_window_ids = JSON_ARRAY(latest_active.window_id)
WHERE pmi.billing_mode = 'LONG';

-- 显示第一步结果
SELECT 
    '第一步完成：有活跃窗口的PMI' as result,
    ROW_COUNT() as updated_count;

-- ========================================
-- 第二步：为没有活跃窗口但有待执行窗口的LONG类型PMI补全字段
-- ========================================
SELECT '=== 第二步：补全有待执行窗口的PMI ===' as step;

UPDATE t_pmi_records pmi
JOIN (
    SELECT 
        w.pmi_record_id,
        w.id as window_id,
        CASE 
            WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
            ELSE TIMESTAMP(w.window_date, w.end_time)
        END as expire_time,
        ROW_NUMBER() OVER (PARTITION BY w.pmi_record_id ORDER BY w.window_date ASC, w.start_time ASC) as rn
    FROM t_pmi_schedule_windows w
    WHERE w.status = 'PENDING'
    AND w.pmi_record_id NOT IN (
        SELECT DISTINCT pmi_record_id 
        FROM t_pmi_schedule_windows 
        WHERE status = 'ACTIVE'
    )
) latest_pending ON pmi.id = latest_pending.pmi_record_id AND latest_pending.rn = 1
SET 
    pmi.current_window_id = latest_pending.window_id,
    pmi.window_expire_time = latest_pending.expire_time
WHERE pmi.billing_mode = 'LONG'
AND pmi.current_window_id IS NULL;

-- 显示第二步结果
SELECT 
    '第二步完成：有待执行窗口的PMI' as result,
    ROW_COUNT() as updated_count;

-- ========================================
-- 第三步：为只有已完成窗口的LONG类型PMI补全字段
-- ========================================
SELECT '=== 第三步：补全只有已完成窗口的PMI ===' as step;

UPDATE t_pmi_records pmi
JOIN (
    SELECT 
        w.pmi_record_id,
        w.id as window_id,
        CASE 
            WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
            ELSE TIMESTAMP(w.window_date, w.end_time)
        END as expire_time,
        ROW_NUMBER() OVER (PARTITION BY w.pmi_record_id ORDER BY w.actual_end_time DESC, w.updated_at DESC) as rn
    FROM t_pmi_schedule_windows w
    WHERE w.status IN ('COMPLETED', 'MANUALLY_CLOSED')
    AND w.pmi_record_id NOT IN (
        SELECT DISTINCT pmi_record_id 
        FROM t_pmi_schedule_windows 
        WHERE status IN ('ACTIVE', 'PENDING')
    )
) latest_completed ON pmi.id = latest_completed.pmi_record_id AND latest_completed.rn = 1
SET 
    pmi.current_window_id = latest_completed.window_id,
    pmi.window_expire_time = latest_completed.expire_time
WHERE pmi.billing_mode = 'LONG'
AND pmi.current_window_id IS NULL;

-- 显示第三步结果
SELECT 
    '第三步完成：只有已完成窗口的PMI' as result,
    ROW_COUNT() as updated_count;

-- ========================================
-- 第四步：为没有任何窗口的LONG类型PMI创建默认窗口
-- ========================================
SELECT '=== 第四步：为没有窗口的PMI创建默认窗口 ===' as step;

-- 首先为没有窗口的LONG类型PMI创建计划（如果不存在）
INSERT INTO t_pmi_schedules (
    pmi_record_id,
    name,
    start_date,
    end_date,
    start_time,
    duration_minutes,
    repeat_type,
    is_all_day,
    status,
    created_at,
    updated_at
)
SELECT 
    pmi.id as pmi_record_id,
    CONCAT('长租计划-', pmi.pmi_number) as name,
    CURDATE() as start_date,
    DATE_ADD(CURDATE(), INTERVAL 30 DAY) as end_date,
    '00:00:00' as start_time,
    1440 as duration_minutes, -- 24小时
    'ONCE' as repeat_type,
    TRUE as is_all_day,
    'ACTIVE' as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_records pmi
WHERE pmi.billing_mode = 'LONG'
AND pmi.id NOT IN (
    SELECT DISTINCT pmi_record_id 
    FROM t_pmi_schedule_windows
)
AND pmi.id NOT IN (
    SELECT DISTINCT pmi_record_id 
    FROM t_pmi_schedules
);

-- 显示创建的计划数量
SELECT 
    '为没有计划的LONG类型PMI创建计划' as result,
    ROW_COUNT() as created_count;

-- 然后为这些PMI创建窗口
INSERT INTO t_pmi_schedule_windows (
    schedule_id,
    pmi_record_id,
    window_date,
    end_date,
    start_time,
    end_time,
    status,
    created_at,
    updated_at
)
SELECT
    s.id as schedule_id,
    s.pmi_record_id,
    s.start_date as window_date,
    s.end_date as end_date,
    s.start_time,
    '23:59:59' as end_time,
    CASE
        WHEN s.end_date < CURDATE() THEN 'COMPLETED'
        WHEN s.start_date <= CURDATE() AND s.end_date >= CURDATE() THEN 'ACTIVE'
        ELSE 'PENDING'
    END as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules s
JOIN t_pmi_records pmi ON s.pmi_record_id = pmi.id
WHERE pmi.billing_mode = 'LONG'
AND pmi.current_window_id IS NULL
AND NOT EXISTS (
    SELECT 1 FROM t_pmi_schedule_windows w
    WHERE w.schedule_id = s.id
);

-- 显示创建的窗口数量
SELECT 
    '为没有窗口的LONG类型PMI创建窗口' as result,
    ROW_COUNT() as created_count;

-- 最后更新这些PMI的窗口字段
UPDATE t_pmi_records pmi
JOIN (
    SELECT
        w.pmi_record_id,
        w.id as window_id,
        CASE
            WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
            ELSE TIMESTAMP(w.window_date, w.end_time)
        END as expire_time
    FROM t_pmi_schedule_windows w
    JOIN (
        SELECT id FROM t_pmi_records
        WHERE billing_mode = 'LONG' AND current_window_id IS NULL
    ) missing_pmi ON w.pmi_record_id = missing_pmi.id
) new_windows ON pmi.id = new_windows.pmi_record_id
SET
    pmi.current_window_id = new_windows.window_id,
    pmi.window_expire_time = new_windows.expire_time
WHERE pmi.billing_mode = 'LONG'
AND pmi.current_window_id IS NULL;

-- 显示第四步结果
SELECT 
    '第四步完成：为没有窗口的PMI创建并关联窗口' as result,
    ROW_COUNT() as updated_count;

-- ========================================
-- 数据补全后的状态检查
-- ========================================
SELECT '=== 补全后数据状态检查 ===' as step;

-- 检查补全后缺少current_window_id的LONG类型PMI
SELECT 
    '补全后仍缺少current_window_id的LONG类型PMI' as item,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG' 
AND current_window_id IS NULL;

-- 检查补全后缺少window_expire_time的LONG类型PMI
SELECT 
    '补全后仍缺少window_expire_time的LONG类型PMI' as item,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG' 
AND window_expire_time IS NULL;

-- 显示补全结果统计
SELECT 
    '成功补全的LONG类型PMI数量' as item,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG' 
AND current_window_id IS NOT NULL 
AND window_expire_time IS NOT NULL;

-- 显示按状态分组的窗口统计
SELECT 
    '窗口状态分布' as info,
    w.status,
    COUNT(*) as count
FROM t_pmi_schedule_windows w
JOIN t_pmi_records p ON w.pmi_record_id = p.id
WHERE p.billing_mode = 'LONG'
GROUP BY w.status
ORDER BY w.status;

-- 提交事务
COMMIT;

SELECT '=== PMI窗口数据补全完成 ===' as final_message;
