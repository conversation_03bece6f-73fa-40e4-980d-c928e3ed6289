-- 数据迁移后验证脚本
-- 用于验证迁移结果的完整性和正确性
-- 执行日期: 2025-08-13

USE zoombusV;

SELECT '=== 数据迁移后验证 ===' as title;

-- ========================================
-- 1. 数据量验证
-- ========================================

SELECT '--- 1. 数据量对比验证 ---' as section;

-- 用户数据量对比
SELECT 
    'users' as table_name,
    (SELECT COUNT(*) FROM old_t_wx_user) as source_count,
    (SELECT COUNT(*) FROM t_users) as target_count,
    (SELECT COUNT(*) FROM t_users) - (SELECT COUNT(*) FROM old_t_wx_user) as difference;

-- PMI数据量对比
SELECT 
    'pmi_records' as table_name,
    (SELECT COUNT(*) FROM old_t_zoom_pmi WHERE pmi IS NOT NULL AND pmi != '') as source_count,
    (SELECT COUNT(*) FROM t_pmi_records) as target_count,
    (SELECT COUNT(*) FROM t_pmi_records) - (SELECT COUNT(*) FROM old_t_zoom_pmi WHERE pmi IS NOT NULL AND pmi != '') as difference;

-- LONG类型PMI对比
SELECT 
    'long_pmi_records' as table_name,
    (SELECT COUNT(*) FROM old_t_zoom_pmi WHERE now_plan_type = 'LONG') as source_count,
    (SELECT COUNT(*) FROM t_pmi_records WHERE billing_mode = 'LONG') as target_count,
    (SELECT COUNT(*) FROM t_pmi_records WHERE billing_mode = 'LONG') - (SELECT COUNT(*) FROM old_t_zoom_pmi WHERE now_plan_type = 'LONG') as difference;

-- ========================================
-- 2. 数据完整性验证
-- ========================================

SELECT '--- 2. 数据完整性验证 ---' as section;

-- 验证所有LONG类型PMI都有对应的计划
SELECT 
    'long_pmi_with_schedules' as check_item,
    COUNT(*) as long_pmi_count,
    (SELECT COUNT(*) FROM t_pmi_schedules s JOIN t_pmi_records p ON s.pmi_record_id = p.id WHERE p.billing_mode = 'LONG') as schedules_count,
    CASE 
        WHEN COUNT(*) = (SELECT COUNT(*) FROM t_pmi_schedules s JOIN t_pmi_records p ON s.pmi_record_id = p.id WHERE p.billing_mode = 'LONG')
        THEN 'PASS'
        ELSE 'FAIL'
    END as result
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- 验证所有LONG类型PMI都有对应的窗口
SELECT 
    'long_pmi_with_windows' as check_item,
    COUNT(*) as long_pmi_count,
    (SELECT COUNT(DISTINCT w.pmi_record_id) FROM t_pmi_schedule_windows w JOIN t_pmi_records p ON w.pmi_record_id = p.id WHERE p.billing_mode = 'LONG') as windows_count,
    CASE 
        WHEN COUNT(*) = (SELECT COUNT(DISTINCT w.pmi_record_id) FROM t_pmi_schedule_windows w JOIN t_pmi_records p ON w.pmi_record_id = p.id WHERE p.billing_mode = 'LONG')
        THEN 'PASS'
        ELSE 'FAIL'
    END as result
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- 验证PMI号码格式
SELECT 
    'pmi_format_validation' as check_item,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN LENGTH(pmi_number) = 10 AND pmi_number REGEXP '^[0-9]+$' THEN 1 END) as valid_format_count,
    CASE 
        WHEN COUNT(*) = COUNT(CASE WHEN LENGTH(pmi_number) = 10 AND pmi_number REGEXP '^[0-9]+$' THEN 1 END)
        THEN 'PASS'
        ELSE 'FAIL'
    END as result
FROM t_pmi_records;

-- ========================================
-- 3. 时长数据验证
-- ========================================

SELECT '--- 3. 时长数据验证 ---' as section;

-- 验证LONG类型PMI的时长迁移
SELECT 
    'long_pmi_duration_migration' as validation,
    SUM(old_pmi.durationh * 60 + old_pmi.durationm) as old_total_minutes,
    (SELECT SUM(total_minutes) FROM t_pmi_records WHERE billing_mode = 'LONG') as new_total_minutes,
    SUM(old_pmi.durationh * 60 + old_pmi.durationm) - (SELECT SUM(total_minutes) FROM t_pmi_records WHERE billing_mode = 'LONG') as difference
FROM old_t_zoom_pmi old_pmi
WHERE old_pmi.now_plan_type = 'LONG';

-- 验证可用时长计算
SELECT 
    'available_minutes_calculation' as validation,
    billing_mode,
    COUNT(*) as record_count,
    SUM(total_minutes) as total_minutes_sum,
    SUM(available_minutes) as available_minutes_sum,
    SUM(total_minutes) - SUM(available_minutes) as frozen_minutes_sum
FROM t_pmi_records 
GROUP BY billing_mode;

-- ========================================
-- 4. 关联关系验证
-- ========================================

SELECT '--- 4. 关联关系验证 ---' as section;

-- 验证PMI记录与用户的关联
SELECT 
    'pmi_user_association' as check_item,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN user_id > 0 THEN 1 END) as associated_pmi,
    COUNT(CASE WHEN user_id = 1 THEN 1 END) as default_user_pmi,
    CASE 
        WHEN COUNT(CASE WHEN user_id = 1 THEN 1 END) = 0 THEN 'PASS'
        ELSE CONCAT('WARNING: ', COUNT(CASE WHEN user_id = 1 THEN 1 END), ' PMI records mapped to default user')
    END as result
FROM t_pmi_records;

-- 验证计划与PMI记录的关联
SELECT 
    'schedule_pmi_association' as check_item,
    COUNT(*) as total_schedules,
    COUNT(CASE WHEN pmi_record_id IS NOT NULL THEN 1 END) as associated_schedules,
    CASE 
        WHEN COUNT(*) = COUNT(CASE WHEN pmi_record_id IS NOT NULL THEN 1 END) THEN 'PASS'
        ELSE 'FAIL'
    END as result
FROM t_pmi_schedules;

-- 验证窗口与计划的关联
SELECT 
    'window_schedule_association' as check_item,
    COUNT(*) as total_windows,
    COUNT(CASE WHEN schedule_id IS NOT NULL THEN 1 END) as associated_windows,
    CASE 
        WHEN COUNT(*) = COUNT(CASE WHEN schedule_id IS NOT NULL THEN 1 END) THEN 'PASS'
        ELSE 'FAIL'
    END as result
FROM t_pmi_schedule_windows;

-- ========================================
-- 5. 业务逻辑验证
-- ========================================

SELECT '--- 5. 业务逻辑验证 ---' as section;

-- 验证LONG类型PMI的到期时间设置
SELECT 
    'long_pmi_expiry_validation' as check_item,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN window_expire_time IS NOT NULL THEN 1 END) as with_expiry_time,
    COUNT(CASE WHEN window_expire_time > NOW() THEN 1 END) as future_expiry,
    COUNT(CASE WHEN window_expire_time <= NOW() THEN 1 END) as past_expiry
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

-- 验证窗口状态分布
SELECT 
    'window_status_distribution' as info,
    status,
    COUNT(*) as count
FROM t_pmi_schedule_windows
GROUP BY status;

-- 验证计费状态分布
SELECT 
    'billing_status_distribution' as info,
    billing_status,
    COUNT(*) as count
FROM t_pmi_records
GROUP BY billing_status;

-- ========================================
-- 6. 数据质量检查
-- ========================================

SELECT '--- 6. 数据质量检查 ---' as section;

-- 检查重复的PMI号码
SELECT 
    'duplicate_pmi_numbers' as check_item,
    COUNT(*) as total_pmi,
    COUNT(DISTINCT pmi_number) as unique_pmi,
    COUNT(*) - COUNT(DISTINCT pmi_number) as duplicates,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT pmi_number) THEN 'PASS'
        ELSE 'FAIL'
    END as result
FROM t_pmi_records;

-- 检查重复的用户邮箱
SELECT 
    'duplicate_user_emails' as check_item,
    COUNT(*) as total_users,
    COUNT(DISTINCT email) as unique_emails,
    COUNT(*) - COUNT(DISTINCT email) as duplicates,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT email) THEN 'PASS'
        ELSE 'FAIL'
    END as result
FROM t_users;

-- 检查空值数据
SELECT 
    'null_pmi_passwords' as check_item,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN pmi_password IS NULL OR pmi_password = '' THEN 1 END) as null_passwords,
    CASE 
        WHEN COUNT(CASE WHEN pmi_password IS NULL OR pmi_password = '' THEN 1 END) = 0 THEN 'PASS'
        ELSE 'WARNING'
    END as result
FROM t_pmi_records;

-- ========================================
-- 7. 迁移成功率统计
-- ========================================

SELECT '--- 7. 迁移成功率统计 ---' as section;

-- 用户迁移成功率
SELECT 
    'user_migration_success_rate' as metric,
    ROUND(
        (SELECT COUNT(*) FROM t_users) * 100.0 / 
        NULLIF((SELECT COUNT(*) FROM old_t_wx_user), 0), 
        2
    ) as percentage;

-- PMI迁移成功率
SELECT 
    'pmi_migration_success_rate' as metric,
    ROUND(
        (SELECT COUNT(*) FROM t_pmi_records) * 100.0 / 
        NULLIF((SELECT COUNT(*) FROM old_t_zoom_pmi WHERE pmi IS NOT NULL AND pmi != ''), 0), 
        2
    ) as percentage;

-- LONG类型PMI完整迁移成功率（包括计划和窗口）
SELECT 
    'long_pmi_complete_migration_rate' as metric,
    ROUND(
        (SELECT COUNT(DISTINCT p.id) 
         FROM t_pmi_records p 
         JOIN t_pmi_schedules s ON p.id = s.pmi_record_id 
         JOIN t_pmi_schedule_windows w ON p.id = w.pmi_record_id 
         WHERE p.billing_mode = 'LONG') * 100.0 / 
        NULLIF((SELECT COUNT(*) FROM old_t_zoom_pmi WHERE now_plan_type = 'LONG'), 0), 
        2
    ) as percentage;

SELECT 'Post-migration validation completed!' as final_message;
