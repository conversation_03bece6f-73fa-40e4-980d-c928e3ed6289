-- 数据迁移前预检查脚本
-- 用于验证老系统数据的完整性和新系统的准备情况
-- 执行日期: 2025-08-13

USE zoombusV;

SELECT '=== 数据迁移前预检查 ===' as title;

-- ========================================
-- 1. 检查老系统数据完整性
-- ========================================

SELECT '--- 1. 老系统数据检查 ---' as section;

-- 检查老系统表是否存在
SELECT 
    'old_t_wx_user表存在性' as check_item,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL - 表不存在'
    END as result
FROM information_schema.tables 
WHERE table_schema = 'zoombusV' AND table_name = 'old_t_wx_user';

SELECT 
    'old_t_zoom_pmi表存在性' as check_item,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL - 表不存在'
    END as result
FROM information_schema.tables 
WHERE table_schema = 'zoombusV' AND table_name = 'old_t_zoom_pmi';

-- 检查老系统数据量
SELECT 'old_t_wx_user记录数' as item, COUNT(*) as count FROM old_t_wx_user;
SELECT 'old_t_zoom_pmi记录数' as item, COUNT(*) as count FROM old_t_zoom_pmi;

-- 检查老系统数据质量
SELECT 'old_t_wx_user中有效用户数' as item, 
       COUNT(*) as count 
FROM old_t_wx_user 
WHERE id IS NOT NULL AND (nick_name IS NOT NULL OR real_name IS NOT NULL);

SELECT 'old_t_zoom_pmi中有效PMI数' as item, 
       COUNT(*) as count 
FROM old_t_zoom_pmi 
WHERE pmi IS NOT NULL AND pmi != '';

-- 检查PMI类型分布
SELECT 'PMI类型分布' as item, now_plan_type, COUNT(*) as count 
FROM old_t_zoom_pmi 
GROUP BY now_plan_type;

-- 检查LONG类型PMI的时长数据
SELECT 'LONG类型PMI时长统计' as item;
SELECT 
    COUNT(*) as total_long_pmi,
    SUM(durationh * 60 + durationm) as total_minutes,
    SUM(long_frozen_durationh * 60 + long_frozen_durationm) as total_frozen_minutes,
    AVG(durationh * 60 + durationm) as avg_minutes_per_pmi
FROM old_t_zoom_pmi 
WHERE now_plan_type = 'LONG';

-- ========================================
-- 2. 检查新系统表结构
-- ========================================

SELECT '--- 2. 新系统表结构检查 ---' as section;

-- 检查目标表是否存在
SELECT 
    't_users表存在性' as check_item,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL - 表不存在'
    END as result
FROM information_schema.tables 
WHERE table_schema = 'zoombusV' AND table_name = 't_users';

SELECT 
    't_pmi_records表存在性' as check_item,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL - 表不存在'
    END as result
FROM information_schema.tables 
WHERE table_schema = 'zoombusV' AND table_name = 't_pmi_records';

SELECT 
    't_pmi_schedules表存在性' as check_item,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL - 表不存在'
    END as result
FROM information_schema.tables 
WHERE table_schema = 'zoombusV' AND table_name = 't_pmi_schedules';

SELECT 
    't_pmi_schedule_windows表存在性' as check_item,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL - 表不存在'
    END as result
FROM information_schema.tables 
WHERE table_schema = 'zoombusV' AND table_name = 't_pmi_schedule_windows';

-- 检查目标表当前数据量
SELECT 't_users当前记录数' as item, COUNT(*) as count FROM t_users;
SELECT 't_pmi_records当前记录数' as item, COUNT(*) as count FROM t_pmi_records;
SELECT 't_pmi_schedules当前记录数' as item, COUNT(*) as count FROM t_pmi_schedules;
SELECT 't_pmi_schedule_windows当前记录数' as item, COUNT(*) as count FROM t_pmi_schedule_windows;

-- ========================================
-- 3. 数据冲突检查
-- ========================================

SELECT '--- 3. 数据冲突检查 ---' as section;

-- 检查用户名冲突
SELECT 'potential_username_conflicts' as check_item, COUNT(*) as count
FROM (
    SELECT COALESCE(NULLIF(TRIM(real_name), ''), NULLIF(TRIM(nick_name), ''), CONCAT('user_', id)) as username
    FROM old_t_wx_user
) old_usernames
JOIN t_users existing ON old_usernames.username = existing.username;

-- 检查邮箱冲突
SELECT 'potential_email_conflicts' as check_item, COUNT(*) as count
FROM (
    SELECT COALESCE(NULLIF(TRIM(mobile), ''), CONCAT('user_', id, '@temp.com')) as email
    FROM old_t_wx_user
) old_emails
JOIN t_users existing ON old_emails.email = existing.email;

-- 检查PMI号码冲突
SELECT 'potential_pmi_conflicts' as check_item, COUNT(*) as count
FROM old_t_zoom_pmi old_pmi
JOIN t_pmi_records existing ON old_pmi.pmi = existing.pmi_number
WHERE old_pmi.pmi IS NOT NULL AND old_pmi.pmi != '';

-- ========================================
-- 4. 数据质量检查
-- ========================================

SELECT '--- 4. 数据质量检查 ---' as section;

-- 检查无效的PMI格式
SELECT 'invalid_pmi_format_count' as check_item, COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE pmi IS NOT NULL 
AND pmi != ''
AND (LENGTH(pmi) != 10 OR pmi NOT REGEXP '^[0-9]+$');

-- 显示无效PMI的样本
SELECT 'invalid_pmi_samples' as item, pmi, LENGTH(pmi) as length
FROM old_t_zoom_pmi 
WHERE pmi IS NOT NULL 
AND pmi != ''
AND (LENGTH(pmi) != 10 OR pmi NOT REGEXP '^[0-9]+$')
LIMIT 5;

-- 检查用户-PMI关联
SELECT 'orphaned_pmi_count' as check_item, COUNT(*) as count
FROM old_t_zoom_pmi pmi
LEFT JOIN old_t_wx_user user ON pmi.user_id = user.id
WHERE user.id IS NULL;

-- 检查LONG类型PMI的到期时间
SELECT 'long_pmi_with_invalid_expire_time' as check_item, COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE now_plan_type = 'LONG'
AND (plan_end_date_time IS NULL OR plan_end_date_time = '');

-- ========================================
-- 5. 预估迁移结果
-- ========================================

SELECT '--- 5. 预估迁移结果 ---' as section;

SELECT 'estimated_users_to_migrate' as item, COUNT(*) as count
FROM old_t_wx_user 
WHERE id IS NOT NULL;

SELECT 'estimated_pmi_to_migrate' as item, COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE pmi IS NOT NULL AND pmi != '';

SELECT 'estimated_long_schedules_to_create' as item, COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE now_plan_type = 'LONG' AND pmi IS NOT NULL AND pmi != '';

SELECT 'estimated_long_windows_to_create' as item, COUNT(*) as count
FROM old_t_zoom_pmi 
WHERE now_plan_type = 'LONG' AND pmi IS NOT NULL AND pmi != '';

-- ========================================
-- 6. 建议和警告
-- ========================================

SELECT '--- 6. 建议和警告 ---' as section;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM old_t_wx_user) = 0 THEN 'WARNING: 没有用户数据可迁移'
        WHEN (SELECT COUNT(*) FROM old_t_zoom_pmi) = 0 THEN 'WARNING: 没有PMI数据可迁移'
        WHEN (SELECT COUNT(*) FROM old_t_zoom_pmi WHERE now_plan_type = 'LONG') = 0 THEN 'INFO: 没有LONG类型PMI需要特殊处理'
        ELSE 'INFO: 数据检查完成，可以开始迁移'
    END as recommendation;

SELECT 'Pre-migration check completed!' as final_message;
