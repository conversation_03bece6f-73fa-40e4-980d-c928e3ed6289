-- 基于开发环境数据更新生产环境 magic_id 字段
-- 执行日期: 2025-08-14
-- 说明: 使用开发环境已更新好的 magic_id 数据来更新生产环境

USE zoombusV;

-- ========================================
-- 第一步：检查和添加magic_id字段
-- ========================================

-- 检查magic_id字段是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'magic_id字段已存在'
        ELSE 'magic_id字段不存在，需要添加'
    END as field_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 't_pmi_records' 
  AND COLUMN_NAME = 'magic_id';

-- 添加magic_id字段（如果不存在）
ALTER TABLE t_pmi_records 
ADD COLUMN magic_id VARCHAR(20) NULL COMMENT '魔法ID，用于公开访问的唯一标识' 
AFTER pmi_number;

-- 添加索引（如果不存在）
ALTER TABLE t_pmi_records 
ADD INDEX idx_magic_id (magic_id);

-- ========================================
-- 第二步：检查当前状态
-- ========================================

SELECT 
    COUNT(*) as total_records,
    COUNT(magic_id) as has_magic_id,
    COUNT(*) - COUNT(magic_id) as missing_magic_id
FROM t_pmi_records;

-- ========================================
-- 第三步：开始数据更新
-- ========================================

START TRANSACTION;

-- 使用单个UPDATE语句批量更新所有记录
-- 基于开发环境的 pmi_number -> magic_id 映射关系
UPDATE t_pmi_records
SET magic_id = CASE
        WHEN pmi_number = '8519274868' THEN '18519274868'
        WHEN pmi_number = '8690693518' THEN '8690693518'
        WHEN pmi_number = '6716931608' THEN '6716931608'
        WHEN pmi_number = '3725182727' THEN '3725182727'
        WHEN pmi_number = '7958296909' THEN '7958296909'
        WHEN pmi_number = '6817313310' THEN '6817313310'
        WHEN pmi_number = '9911269089' THEN '18911269089'
        WHEN pmi_number = '8073166209' THEN '8073166209'
        WHEN pmi_number = '8257342930' THEN '18257342930'
        WHEN pmi_number = '8677368326' THEN '13677368326'
        WHEN pmi_number = '9911161806' THEN '9911161806'
        WHEN pmi_number = '3751527570' THEN '3751527570'
        WHEN pmi_number = '6214380037' THEN '6214380037'
        WHEN pmi_number = '5131021001' THEN '5131021001'
        WHEN pmi_number = '5627021002' THEN '5627021002'
        WHEN pmi_number = '5427521003' THEN '5427521003'
        WHEN pmi_number = '5280121005' THEN '5280121005'
        WHEN pmi_number = '5281521006' THEN '5281521006'
        WHEN pmi_number = '5523311007' THEN '5523311007'
        WHEN pmi_number = '5562511008' THEN '5562511008'
