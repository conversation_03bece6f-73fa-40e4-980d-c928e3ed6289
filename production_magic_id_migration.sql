-- 正式环境 t_pmi_records 表 magic_id 字段补全脚本
-- 执行日期: 2025-08-14
-- 说明: 基于当前开发环境数据结构，为正式环境补全magic_id字段

USE zoombusV;

-- ========================================
-- 第一步：检查和添加magic_id字段
-- ========================================

-- 检查magic_id字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'zoombusV' 
  AND TABLE_NAME = 't_pmi_records' 
  AND COLUMN_NAME = 'magic_id';

-- 如果字段不存在，添加magic_id字段
-- 注意：如果字段已存在，这个语句会报错，可以忽略
ALTER TABLE t_pmi_records 
ADD COLUMN magic_id VARCHAR(20) NULL COMMENT '魔法ID，用于公开访问的唯一标识' 
AFTER pmi_number;

-- 为magic_id字段添加索引（如果不存在）
-- 注意：如果索引已存在，这个语句会报错，可以忽略
ALTER TABLE t_pmi_records 
ADD INDEX idx_magic_id (magic_id);

-- ========================================
-- 第二步：数据迁移前的状态检查
-- ========================================

-- 检查当前数据状态
SELECT '=== 迁移前数据状态检查 ===' as status_check;

SELECT 
    COUNT(*) as total_pmi_records,
    COUNT(magic_id) as has_magic_id,
    COUNT(*) - COUNT(magic_id) as missing_magic_id
FROM t_pmi_records;

-- 检查老系统数据状态
SELECT 
    COUNT(*) as total_old_pmi,
    COUNT(mg_id) as has_mg_id,
    COUNT(CASE WHEN mg_id IS NOT NULL AND mg_id != '' THEN 1 END) as valid_mg_id
FROM old_t_zoom_pmi;

-- ========================================
-- 第三步：开始数据迁移
-- ========================================

START TRANSACTION;

-- 步骤1：从老系统的mg_id更新magic_id
-- 优先使用老系统的mg_id，如果mg_id为空则使用pmi
UPDATE t_pmi_records pmi
JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
SET pmi.magic_id = COALESCE(
    NULLIF(TRIM(old_pmi.mg_id), ''),  -- 如果mg_id不为空且不是空字符串
    old_pmi.pmi                       -- 否则使用pmi号码
)
WHERE pmi.magic_id IS NULL;

-- 步骤2：对于在老系统中找不到对应记录的PMI，使用pmi_number作为magic_id
UPDATE t_pmi_records 
SET magic_id = pmi_number 
WHERE magic_id IS NULL;

-- ========================================
-- 第四步：数据验证和修复
-- ========================================

-- 验证更新结果
SELECT '=== 迁移后数据验证 ===' as validation;

SELECT 
    COUNT(*) as total_records,
    COUNT(magic_id) as has_magic_id,
    COUNT(*) - COUNT(magic_id) as still_missing,
    COUNT(CASE WHEN magic_id != pmi_number THEN 1 END) as different_from_pmi
FROM t_pmi_records;

-- 检查magic_id的长度分布
SELECT 
    LENGTH(magic_id) as magic_id_length,
    COUNT(*) as count
FROM t_pmi_records 
WHERE magic_id IS NOT NULL
GROUP BY LENGTH(magic_id)
ORDER BY magic_id_length;

-- 检查是否有重复的magic_id
SELECT 
    magic_id,
    COUNT(*) as duplicate_count
FROM t_pmi_records 
WHERE magic_id IS NOT NULL
GROUP BY magic_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 显示一些更新示例
SELECT '=== 更新示例 ===' as examples;
SELECT 
    pmi.id,
    pmi.pmi_number,
    pmi.magic_id,
    old_pmi.mg_id as old_mg_id,
    CASE 
        WHEN pmi.magic_id = pmi.pmi_number THEN '使用PMI号码'
        WHEN pmi.magic_id = old_pmi.mg_id THEN '使用老系统mg_id'
        ELSE '其他情况'
    END as source
FROM t_pmi_records pmi
LEFT JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
WHERE pmi.magic_id IS NOT NULL
ORDER BY pmi.id
LIMIT 20;

-- ========================================
-- 第五步：数据完整性检查
-- ========================================

-- 检查magic_id格式（应该是10-11位数字）
SELECT '=== 数据格式验证 ===' as format_check;

SELECT
    'magic_id格式异常的记录' as issue,
    COUNT(*) as count
FROM t_pmi_records
WHERE magic_id IS NOT NULL 
  AND (LENGTH(magic_id) < 10 OR LENGTH(magic_id) > 11 OR magic_id NOT REGEXP '^[0-9]+$');

-- 显示格式异常的记录
SELECT
    id, pmi_number, magic_id, LENGTH(magic_id) as length
FROM t_pmi_records
WHERE magic_id IS NOT NULL 
  AND (LENGTH(magic_id) < 10 OR LENGTH(magic_id) > 11 OR magic_id NOT REGEXP '^[0-9]+$')
LIMIT 10;

-- 检查magic_id唯一性
SELECT '=== 唯一性验证 ===' as uniqueness_check;

SELECT
    '重复magic_id的数量' as issue,
    COUNT(*) - COUNT(DISTINCT magic_id) as duplicate_count
FROM t_pmi_records
WHERE magic_id IS NOT NULL;

-- ========================================
-- 第六步：提交事务
-- ========================================

-- 如果所有验证都通过，提交事务
COMMIT;

-- ========================================
-- 第七步：最终报告
-- ========================================

SELECT '=== 最终迁移报告 ===' as final_report;

-- 总体统计
SELECT 
    'PMI记录总数' as metric,
    COUNT(*) as value
FROM t_pmi_records
UNION ALL
SELECT 
    '已设置magic_id的记录数' as metric,
    COUNT(magic_id) as value
FROM t_pmi_records
UNION ALL
SELECT 
    '使用老系统mg_id的记录数' as metric,
    COUNT(*) as value
FROM t_pmi_records pmi
JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
WHERE pmi.magic_id = old_pmi.mg_id
  AND old_pmi.mg_id IS NOT NULL 
  AND old_pmi.mg_id != ''
UNION ALL
SELECT 
    '使用PMI号码作为magic_id的记录数' as metric,
    COUNT(*) as value
FROM t_pmi_records
WHERE magic_id = pmi_number;

-- 按magic_id长度分组统计
SELECT '=== magic_id长度分布 ===' as length_distribution;
SELECT 
    CONCAT(LENGTH(magic_id), '位') as length_category,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_pmi_records WHERE magic_id IS NOT NULL), 2) as percentage
FROM t_pmi_records 
WHERE magic_id IS NOT NULL
GROUP BY LENGTH(magic_id)
ORDER BY LENGTH(magic_id);

SELECT 'Magic ID migration completed successfully!' as completion_message;

-- ========================================
-- 注意事项和后续操作建议
-- ========================================

/*
注意事项：
1. 执行前请备份数据库
2. 建议在测试环境先执行一遍
3. 如果发现重复的magic_id，需要手动处理
4. 执行后需要更新应用程序配置，确保使用magic_id而不是pmi_number

后续操作：
1. 更新应用程序代码，确保公开URL使用magic_id
2. 更新前端代码，确保API调用使用magic_id
3. 测试所有相关功能，特别是PMI访问功能
4. 如果有缓存，需要清理相关缓存

验证方法：
1. 检查所有PMI记录都有magic_id
2. 检查magic_id的唯一性
3. 测试通过magic_id访问PMI功能
4. 检查回退URL是否正确生成
*/
