-- 正式环境 magic_id 快速补全脚本
-- 执行日期: 2025-08-14
-- 说明: 快速为正式环境补全magic_id字段

USE zoombusV;

-- 检查当前状态
SELECT 
    COUNT(*) as total_records,
    COUNT(magic_id) as has_magic_id,
    COUNT(*) - COUNT(magic_id) as missing_magic_id
FROM t_pmi_records;

-- 开始事务
START TRANSACTION;

-- 添加magic_id字段（如果不存在）
ALTER TABLE t_pmi_records 
ADD COLUMN magic_id VARCHAR(20) NULL COMMENT '魔法ID，用于公开访问的唯一标识' 
AFTER pmi_number;

-- 添加索引（如果不存在）
ALTER TABLE t_pmi_records 
ADD INDEX idx_magic_id (magic_id);

-- 从老系统更新magic_id
UPDATE t_pmi_records pmi
JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
SET pmi.magic_id = COALESCE(
    NULLIF(TRIM(old_pmi.mg_id), ''),
    old_pmi.pmi
)
WHERE pmi.magic_id IS NULL;

-- 对于找不到对应记录的PMI，使用pmi_number
UPDATE t_pmi_records 
SET magic_id = pmi_number 
WHERE magic_id IS NULL;

-- 验证结果
SELECT 
    COUNT(*) as total_records,
    COUNT(magic_id) as has_magic_id,
    COUNT(*) - COUNT(magic_id) as still_missing,
    COUNT(CASE WHEN magic_id != pmi_number THEN 1 END) as different_from_pmi
FROM t_pmi_records;

-- 检查重复
SELECT 
    magic_id,
    COUNT(*) as duplicate_count
FROM t_pmi_records 
WHERE magic_id IS NOT NULL
GROUP BY magic_id
HAVING COUNT(*) > 1;

-- 提交事务
COMMIT;

SELECT 'Magic ID migration completed!' as result;
