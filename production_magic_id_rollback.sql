-- 正式环境 magic_id 回滚脚本
-- 执行日期: 2025-08-14
-- 说明: 如果需要回滚magic_id相关更改

USE zoombusV;

-- 检查当前状态
SELECT 
    COUNT(*) as total_records,
    COUNT(magic_id) as has_magic_id
FROM t_pmi_records;

-- 开始事务
START TRANSACTION;

-- 清空magic_id字段的值
UPDATE t_pmi_records SET magic_id = NULL;

-- 删除magic_id字段的索引
ALTER TABLE t_pmi_records DROP INDEX idx_magic_id;

-- 删除magic_id字段
ALTER TABLE t_pmi_records DROP COLUMN magic_id;

-- 验证回滚结果
SELECT 
    COLUMN_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'zoombusV' 
  AND TABLE_NAME = 't_pmi_records' 
  AND COLUMN_NAME = 'magic_id';

-- 提交事务
COMMIT;

SELECT 'Magic ID rollback completed!' as result;
