-- 生产环境 magic_id 简化更新脚本
-- 执行日期: 2025-08-14
-- 说明: 基于开发环境数据，只更新与pmi_number不同的magic_id

USE zoombusV;

-- 检查当前状态
SELECT 
    COUNT(*) as total_records,
    COUNT(magic_id) as has_magic_id,
    COUNT(*) - COUNT(magic_id) as missing_magic_id
FROM t_pmi_records;

-- 添加magic_id字段（如果不存在，如果已存在会报错可忽略）
ALTER TABLE t_pmi_records 
ADD COLUMN magic_id VARCHAR(20) NULL COMMENT '魔法ID，用于公开访问的唯一标识' 
AFTER pmi_number;

-- 添加索引（如果不存在，如果已存在会报错可忽略）
ALTER TABLE t_pmi_records 
ADD INDEX idx_magic_id (magic_id);

-- 开始事务
START TRANSACTION;

-- 第一步：先将所有记录的magic_id设置为pmi_number（默认值）
UPDATE t_pmi_records 
SET magic_id = pmi_number 
WHERE magic_id IS NULL;

-- 第二步：更新那些magic_id与pmi_number不同的记录
-- 这些是从老系统mg_id获取的特殊值
UPDATE t_pmi_records SET magic_id = '2023050318' WHERE pmi_number = '2024062168';
UPDATE t_pmi_records SET magic_id = '6051963025' WHERE pmi_number = '2051963024';
UPDATE t_pmi_records SET magic_id = '580275060' WHERE pmi_number = '2580275060';
UPDATE t_pmi_records SET magic_id = '2950613959' WHERE pmi_number = '2950613951';
UPDATE t_pmi_records SET magic_id = '13681576033' WHERE pmi_number = '3681576033';
UPDATE t_pmi_records SET magic_id = '3732225842' WHERE pmi_number = '3732225846';
UPDATE t_pmi_records SET magic_id = '852151937' WHERE pmi_number = '3852151937';
UPDATE t_pmi_records SET magic_id = '3857151019' WHERE pmi_number = '3857151015';
UPDATE t_pmi_records SET magic_id = '13966662767' WHERE pmi_number = '3966662768';
UPDATE t_pmi_records SET magic_id = '18810543507' WHERE pmi_number = '4621803943';
UPDATE t_pmi_records SET magic_id = '6831717375' WHERE pmi_number = '5331717375';
UPDATE t_pmi_records SET magic_id = '13535501624' WHERE pmi_number = '5535501624';
UPDATE t_pmi_records SET magic_id = '15602038871' WHERE pmi_number = '5602038871';
UPDATE t_pmi_records SET magic_id = '5713053573' WHERE pmi_number = '5713053575';
UPDATE t_pmi_records SET magic_id = '13933794495' WHERE pmi_number = '5724768898';
UPDATE t_pmi_records SET magic_id = '3792056051' WHERE pmi_number = '5792056051';
UPDATE t_pmi_records SET magic_id = '2869072505' WHERE pmi_number = '5869072505';
UPDATE t_pmi_records SET magic_id = '13910273789' WHERE pmi_number = '5910273789';
UPDATE t_pmi_records SET magic_id = '6913759085' WHERE pmi_number = '5913759085';
UPDATE t_pmi_records SET magic_id = '6917028536' WHERE pmi_number = '5917028536';
UPDATE t_pmi_records SET magic_id = '13957101322' WHERE pmi_number = '5957101322';
UPDATE t_pmi_records SET magic_id = '8030256738' WHERE pmi_number = '6030256738';
UPDATE t_pmi_records SET magic_id = '8038059080' WHERE pmi_number = '6038059080';
UPDATE t_pmi_records SET magic_id = '5096131790' WHERE pmi_number = '6096131790';
UPDATE t_pmi_records SET magic_id = '5153622642' WHERE pmi_number = '6153622642';
UPDATE t_pmi_records SET magic_id = '18001768335' WHERE pmi_number = '6181728062';
UPDATE t_pmi_records SET magic_id = '6201788598' WHERE pmi_number = '6201788596';
UPDATE t_pmi_records SET magic_id = '5202590281' WHERE pmi_number = '6202590281';
UPDATE t_pmi_records SET magic_id = '5309059362' WHERE pmi_number = '6309059362';
UPDATE t_pmi_records SET magic_id = '9336352693' WHERE pmi_number = '6336352696';
UPDATE t_pmi_records SET magic_id = '8380253925' WHERE pmi_number = '6380253925';
UPDATE t_pmi_records SET magic_id = '3518318181' WHERE pmi_number = '6518318181';
UPDATE t_pmi_records SET magic_id = '2535071006' WHERE pmi_number = '6535071006';
UPDATE t_pmi_records SET magic_id = '13683677009' WHERE pmi_number = '6683677009';
UPDATE t_pmi_records SET magic_id = '5731725205' WHERE pmi_number = '6731725205';
UPDATE t_pmi_records SET magic_id = '8828397526' WHERE pmi_number = '6828397526';
UPDATE t_pmi_records SET magic_id = '3930401591' WHERE pmi_number = '6930401591';
UPDATE t_pmi_records SET magic_id = '8011310071' WHERE pmi_number = '7011310071';
UPDATE t_pmi_records SET magic_id = '6090615917' WHERE pmi_number = '7090615917';
UPDATE t_pmi_records SET magic_id = '6317585371' WHERE pmi_number = '7317585371';
UPDATE t_pmi_records SET magic_id = '7371308617' WHERE pmi_number = '7371308615';
UPDATE t_pmi_records SET magic_id = '537057080' WHERE pmi_number = '7537057080';
UPDATE t_pmi_records SET magic_id = '13036201363' WHERE pmi_number = '8036201363';
UPDATE t_pmi_records SET magic_id = '3068285350' WHERE pmi_number = '8068285350';
UPDATE t_pmi_records SET magic_id = '15170086629' WHERE pmi_number = '8170086629';
UPDATE t_pmi_records SET magic_id = '3190936926' WHERE pmi_number = '8190936926';
UPDATE t_pmi_records SET magic_id = '9191375858' WHERE pmi_number = '8191375858';
UPDATE t_pmi_records SET magic_id = '18257342930' WHERE pmi_number = '8257342930';
UPDATE t_pmi_records SET magic_id = '7263159383' WHERE pmi_number = '8263159383';
UPDATE t_pmi_records SET magic_id = '9309736916' WHERE pmi_number = '8309736916';
UPDATE t_pmi_records SET magic_id = '6359352818' WHERE pmi_number = '8359352818';
UPDATE t_pmi_records SET magic_id = '18363603546' WHERE pmi_number = '8363603546';
UPDATE t_pmi_records SET magic_id = '6370805968' WHERE pmi_number = '8370805968';
UPDATE t_pmi_records SET magic_id = '13387008191' WHERE pmi_number = '8387008194';
UPDATE t_pmi_records SET magic_id = '7395791915' WHERE pmi_number = '8395791915';
UPDATE t_pmi_records SET magic_id = '7516029715' WHERE pmi_number = '8516029715';
UPDATE t_pmi_records SET magic_id = '18519274868' WHERE pmi_number = '8519274868';
UPDATE t_pmi_records SET magic_id = '13601122333' WHERE pmi_number = '8601122333';
UPDATE t_pmi_records SET magic_id = '6602230774' WHERE pmi_number = '8602230774';
UPDATE t_pmi_records SET magic_id = '13677368326' WHERE pmi_number = '8677368326';
UPDATE t_pmi_records SET magic_id = '9697152688' WHERE pmi_number = '8697152688';
UPDATE t_pmi_records SET magic_id = '7722441197' WHERE pmi_number = '8722441197';
UPDATE t_pmi_records SET magic_id = '9748213940' WHERE pmi_number = '8748213940';
UPDATE t_pmi_records SET magic_id = '13818066426' WHERE pmi_number = '8818066426';
UPDATE t_pmi_records SET magic_id = '8840147343-bak' WHERE pmi_number = '8840147348';
UPDATE t_pmi_records SET magic_id = '3858681010' WHERE pmi_number = '8858681010';
UPDATE t_pmi_records SET magic_id = '5903718186' WHERE pmi_number = '8903718186';
UPDATE t_pmi_records SET magic_id = '3910810326' WHERE pmi_number = '8910810326';
UPDATE t_pmi_records SET magic_id = '19916485409' WHERE pmi_number = '8916485409';
UPDATE t_pmi_records SET magic_id = '13981056456' WHERE pmi_number = '8981056456';
UPDATE t_pmi_records SET magic_id = '13986986516' WHERE pmi_number = '8986986516';
UPDATE t_pmi_records SET magic_id = '6050596068' WHERE pmi_number = '9050596068';
UPDATE t_pmi_records SET magic_id = '7052068296' WHERE pmi_number = '9052068296';
UPDATE t_pmi_records SET magic_id = '15086911802' WHERE pmi_number = '9086911808';
UPDATE t_pmi_records SET magic_id = '8153919620' WHERE pmi_number = '9153919620';
UPDATE t_pmi_records SET magic_id = '9271906175' WHERE pmi_number = '9271906174';
UPDATE t_pmi_records SET magic_id = '6350052501' WHERE pmi_number = '9350052501';
UPDATE t_pmi_records SET magic_id = '8616869266' WHERE pmi_number = '9616869266';
UPDATE t_pmi_records SET magic_id = '8622151925' WHERE pmi_number = '9622151925';
UPDATE t_pmi_records SET magic_id = '13277887637' WHERE pmi_number = '9666352579';
UPDATE t_pmi_records SET magic_id = '8800319135' WHERE pmi_number = '9800319136';
UPDATE t_pmi_records SET magic_id = '18817830083' WHERE pmi_number = '9817830083';
UPDATE t_pmi_records SET magic_id = '18818909720' WHERE pmi_number = '9818909720';
UPDATE t_pmi_records SET magic_id = '18911269089' WHERE pmi_number = '9911269089';

-- ========================================
-- 验证更新结果
-- ========================================

-- 检查更新后的状态
SELECT
    COUNT(*) as total_records,
    COUNT(magic_id) as has_magic_id,
    COUNT(*) - COUNT(magic_id) as still_missing,
    COUNT(CASE WHEN magic_id != pmi_number THEN 1 END) as different_from_pmi
FROM t_pmi_records;

-- 检查magic_id唯一性
SELECT
    magic_id,
    COUNT(*) as duplicate_count
FROM t_pmi_records
WHERE magic_id IS NOT NULL
GROUP BY magic_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 显示一些更新示例
SELECT
    'magic_id更新示例' as info,
    pmi_number,
    magic_id,
    CASE
        WHEN magic_id = pmi_number THEN '相同'
        ELSE '不同'
    END as comparison
FROM t_pmi_records
WHERE magic_id IS NOT NULL
ORDER BY
    CASE WHEN magic_id != pmi_number THEN 1 ELSE 2 END,
    pmi_number
LIMIT 20;

-- 提交事务
COMMIT;

SELECT 'Magic ID 更新完成！' as result;

-- ========================================
-- 后续验证建议
-- ========================================

/*
执行完成后请验证：
1. 所有记录都有magic_id值
2. magic_id没有重复
3. 测试通过magic_id访问PMI功能
4. 检查回退URL生成是否正确

测试URL示例：
- http://your-domain/m/18519274868 (对应PMI: 8519274868)
- http://your-domain/m/18911269089 (对应PMI: 9911269089)
*/
