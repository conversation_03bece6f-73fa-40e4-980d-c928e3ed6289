-- 正式环境 magic_id 验证脚本
-- 执行日期: 2025-08-14
-- 说明: 验证magic_id迁移的正确性

USE zoombusV;

-- ========================================
-- 基础数据验证
-- ========================================

SELECT '=== 基础数据统计 ===' as section;

-- 总体统计
SELECT 
    COUNT(*) as total_pmi_records,
    COUNT(magic_id) as has_magic_id,
    COUNT(*) - COUNT(magic_id) as missing_magic_id,
    ROUND(COUNT(magic_id) * 100.0 / COUNT(*), 2) as completion_percentage
FROM t_pmi_records;

-- ========================================
-- 数据质量验证
-- ========================================

SELECT '=== 数据质量检查 ===' as section;

-- 检查magic_id格式
SELECT 
    'magic_id格式正确' as check_item,
    COUNT(*) as count
FROM t_pmi_records
WHERE magic_id IS NOT NULL 
  AND LENGTH(magic_id) BETWEEN 10 AND 11 
  AND magic_id REGEXP '^[0-9]+$';

SELECT 
    'magic_id格式异常' as check_item,
    COUNT(*) as count
FROM t_pmi_records
WHERE magic_id IS NOT NULL 
  AND (LENGTH(magic_id) < 10 OR LENGTH(magic_id) > 11 OR magic_id NOT REGEXP '^[0-9]+$');

-- 检查唯一性
SELECT 
    'magic_id唯一性检查' as check_item,
    COUNT(DISTINCT magic_id) as unique_count,
    COUNT(magic_id) as total_count,
    CASE 
        WHEN COUNT(DISTINCT magic_id) = COUNT(magic_id) THEN '通过'
        ELSE '失败'
    END as result
FROM t_pmi_records
WHERE magic_id IS NOT NULL;

-- ========================================
-- 数据来源分析
-- ========================================

SELECT '=== 数据来源分析 ===' as section;

-- 使用老系统mg_id的记录
SELECT 
    '使用老系统mg_id' as source_type,
    COUNT(*) as count
FROM t_pmi_records pmi
JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
WHERE pmi.magic_id = old_pmi.mg_id
  AND old_pmi.mg_id IS NOT NULL 
  AND old_pmi.mg_id != ''
  AND old_pmi.mg_id != old_pmi.pmi;

-- 使用PMI号码的记录
SELECT 
    '使用PMI号码' as source_type,
    COUNT(*) as count
FROM t_pmi_records
WHERE magic_id = pmi_number;

-- magic_id长度分布
SELECT 
    CONCAT(LENGTH(magic_id), '位数字') as length_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_pmi_records WHERE magic_id IS NOT NULL), 2) as percentage
FROM t_pmi_records 
WHERE magic_id IS NOT NULL
GROUP BY LENGTH(magic_id)
ORDER BY LENGTH(magic_id);

-- ========================================
-- 重复数据检查
-- ========================================

SELECT '=== 重复数据检查 ===' as section;

-- 显示重复的magic_id
SELECT 
    magic_id,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id) as record_ids
FROM t_pmi_records 
WHERE magic_id IS NOT NULL
GROUP BY magic_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- ========================================
-- 示例数据展示
-- ========================================

SELECT '=== 示例数据 ===' as section;

-- 显示不同类型的magic_id示例
SELECT 
    pmi.id,
    pmi.pmi_number,
    pmi.magic_id,
    old_pmi.mg_id as old_mg_id,
    CASE 
        WHEN pmi.magic_id = pmi.pmi_number THEN 'PMI号码'
        WHEN pmi.magic_id = old_pmi.mg_id THEN '老系统mg_id'
        ELSE '其他'
    END as magic_id_source,
    LENGTH(pmi.magic_id) as magic_id_length
FROM t_pmi_records pmi
LEFT JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
WHERE pmi.magic_id IS NOT NULL
ORDER BY 
    CASE 
        WHEN pmi.magic_id != pmi.pmi_number THEN 1
        ELSE 2
    END,
    pmi.id
LIMIT 20;

-- ========================================
-- 功能验证建议
-- ========================================

SELECT '=== 功能验证建议 ===' as section;

-- 提供一些测试用的magic_id
SELECT 
    'PMI访问测试URL示例' as test_type,
    CONCAT('http://your-domain/m/', magic_id) as test_url,
    pmi_number,
    magic_id
FROM t_pmi_records 
WHERE magic_id IS NOT NULL
  AND status = 'ACTIVE'
ORDER BY RAND()
LIMIT 5;

-- ========================================
-- 验证总结
-- ========================================

SELECT '=== 验证总结 ===' as section;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM t_pmi_records WHERE magic_id IS NULL) = 0 
             AND (SELECT COUNT(DISTINCT magic_id) FROM t_pmi_records WHERE magic_id IS NOT NULL) = (SELECT COUNT(magic_id) FROM t_pmi_records WHERE magic_id IS NOT NULL)
             AND (SELECT COUNT(*) FROM t_pmi_records WHERE magic_id IS NOT NULL AND (LENGTH(magic_id) < 10 OR LENGTH(magic_id) > 11 OR magic_id NOT REGEXP '^[0-9]+$')) = 0
        THEN '✅ 验证通过 - magic_id迁移成功'
        ELSE '❌ 验证失败 - 存在问题需要处理'
    END as validation_result;

-- 问题汇总
SELECT 
    '缺失magic_id的记录数' as issue_type,
    COUNT(*) as count
FROM t_pmi_records 
WHERE magic_id IS NULL
UNION ALL
SELECT 
    '重复magic_id的记录数' as issue_type,
    COUNT(*) - COUNT(DISTINCT magic_id) as count
FROM t_pmi_records 
WHERE magic_id IS NOT NULL
UNION ALL
SELECT 
    '格式异常magic_id的记录数' as issue_type,
    COUNT(*) as count
FROM t_pmi_records
WHERE magic_id IS NOT NULL 
  AND (LENGTH(magic_id) < 10 OR LENGTH(magic_id) > 11 OR magic_id NOT REGEXP '^[0-9]+$');

SELECT '验证脚本执行完成' as completion_message;
