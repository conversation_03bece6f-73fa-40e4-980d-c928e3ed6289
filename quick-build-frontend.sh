#!/bin/bash

# 快速前端构建脚本 - 仅用于开发测试
# 跳过大部分检查，最大化构建速度

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 设置最激进的优化环境变量
export NODE_OPTIONS="--no-deprecation --max-old-space-size=8192"
export GENERATE_SOURCEMAP=false
export DISABLE_ESLINT_PLUGIN=true
export TSC_COMPILE_ON_ERROR=true
export FAST_REFRESH=false
export CI=true

echo "=== 快速前端构建 ==="
log_info "使用最激进的优化设置，跳过所有非必要检查"

# 构建管理端前端
if [ "$1" != "user" ]; then
    log_info "快速构建管理端前端..."
    cd frontend
    
    # 跳过依赖检查，直接构建
    npm run build --silent > /dev/null 2>&1 &
    BUILD_PID=$!
    
    # 简单进度显示
    while kill -0 $BUILD_PID 2>/dev/null; do
        echo -n "█"
        sleep 1
    done
    echo ""
    
    wait $BUILD_PID
    
    cd ..
    log_success "管理端前端构建完成"
fi

# 构建用户端前端
if [ "$1" != "admin" ]; then
    log_info "快速构建用户端前端..."
    cd user-frontend
    
    npm run build --silent > /dev/null 2>&1 &
    BUILD_PID=$!
    
    while kill -0 $BUILD_PID 2>/dev/null; do
        echo -n "█"
        sleep 1
    done
    echo ""
    
    wait $BUILD_PID
    
    cd ..
    log_success "用户端前端构建完成"
fi

log_success "🚀 快速构建完成！"
