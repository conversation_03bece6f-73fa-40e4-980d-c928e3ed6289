#!/bin/bash

# ZoomBus 快速部署脚本
# 用于快速构建和部署到生产服务器

set -e

# 配置
TARGET_SERVER="<EMAIL>"
BACKEND_DIR="/root/zoombus"
FRONTEND_DIR="/home/<USER>/m.zoombus.com/dist"
JAR_NAME="zoombus-1.0.0.jar"

# 设置Java 11环境
setup_java11_environment() {
    echo "🔧 检查并设置Java 11环境..."

    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        JAVA11_PATHS=(
            "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home"
            "/Library/Java/JavaVirtualMachines/jdk-11*/Contents/Home"
            "/Library/Java/JavaVirtualMachines/openjdk-11*/Contents/Home"
        )

        for path in "${JAVA11_PATHS[@]}"; do
            if [ -d "$path" ]; then
                export JAVA_HOME="$path"
                export PATH="$JAVA_HOME/bin:$PATH"
                echo "✅ 设置Java 11环境: $JAVA_HOME"
                break
            fi
        done

        # 尝试使用java_home工具
        if [ -z "$JAVA_HOME" ] && command -v /usr/libexec/java_home &> /dev/null; then
            JAVA11_HOME=$(/usr/libexec/java_home -v 11 2>/dev/null)
            if [ -n "$JAVA11_HOME" ]; then
                export JAVA_HOME="$JAVA11_HOME"
                export PATH="$JAVA_HOME/bin:$PATH"
                echo "✅ 通过java_home设置Java 11: $JAVA_HOME"
            fi
        fi
    else
        # Linux
        JAVA11_PATHS=(
            "/usr/lib/jvm/java-11-openjdk"
            "/usr/lib/jvm/java-11-openjdk-amd64"
            "/usr/lib/jvm/jdk-11"
        )

        for path in "${JAVA11_PATHS[@]}"; do
            if [ -d "$path" ]; then
                export JAVA_HOME="$path"
                export PATH="$JAVA_HOME/bin:$PATH"
                echo "✅ 设置Java 11环境: $JAVA_HOME"
                break
            fi
        done
    fi

    # 验证Java版本
    if ! command -v java &> /dev/null; then
        echo "❌ 未找到Java，请安装Java 11"
        exit 1
    fi

    JAVA_VERSION=$(java -version 2>&1 | head -1)
    if [[ "$JAVA_VERSION" == *"11."* ]] || [[ "$JAVA_VERSION" == *"version \"11"* ]]; then
        echo "✅ Java 11验证成功: $JAVA_VERSION"
    else
        echo "❌ Java版本不正确，需要Java 11。当前版本: $JAVA_VERSION"
        echo "❌ 请确保已安装Java 11并设置正确的JAVA_HOME"
        exit 1
    fi
}

echo "🚀 ZoomBus 快速部署"
echo "目标服务器: $TARGET_SERVER"
echo ""

# 设置Java 11环境
setup_java11_environment

# 1. 构建后端
echo "📦 构建后端..."
if [ -f "./mvnw" ]; then
    ./mvnw clean package -DskipTests -q
else
    mvn clean package -DskipTests -q
fi

if [ ! -f "target/$JAR_NAME" ]; then
    echo "❌ 后端构建失败"
    exit 1
fi
echo "✅ 后端构建完成"

# 2. 构建前端
echo "🎨 构建前端..."
cd frontend
npm run build > /dev/null 2>&1
if [ ! -d "build" ]; then
    echo "❌ 前端构建失败"
    exit 1
fi
cd ..
echo "✅ 前端构建完成"

# 3. 部署后端
echo "🚀 部署后端..."
ssh $TARGET_SERVER "mkdir -p $BACKEND_DIR"
ssh $TARGET_SERVER "pkill -f '$JAR_NAME' || true"
scp target/$JAR_NAME $TARGET_SERVER:$BACKEND_DIR/
echo "✅ 后端部署完成"

# 4. 部署前端
echo "🌐 部署前端..."
ssh $TARGET_SERVER "rm -rf $FRONTEND_DIR && mkdir -p $FRONTEND_DIR"
scp -r frontend/build/* $TARGET_SERVER:$FRONTEND_DIR/
echo "✅ 前端部署完成"

# 5. 启动服务
echo "🔄 启动服务..."

# 创建启动脚本
cat > /tmp/quick_start_zoombus.sh << 'EOF'
#!/bin/bash
cd /root/zoombus
JAVA11_PATH=$(find /usr/lib/jvm -name 'java-11-openjdk*' -type d 2>/dev/null | head -1)
if [ -n "$JAVA11_PATH" ]; then
    JAVA_CMD="$JAVA11_PATH/bin/java"
    echo "✅ 使用Java 11: $JAVA_CMD"
else
    echo "❌ 未找到Java 11，请先安装"
    exit 1
fi

echo "🚀 启动ZoomBus应用..."
nohup "$JAVA_CMD" -jar zoombus-1.0.0.jar > zoombus.log 2>&1 &
PID=$!
echo "📋 ZoomBus已启动，PID: $PID"
echo $PID > zoombus.pid
EOF

# 使用稳定启动脚本
ssh $TARGET_SERVER "/root/zoombus/stable_zoombus_start.sh"
rm -f /tmp/quick_start_zoombus.sh
sleep 5

# 检查服务状态
if ssh $TARGET_SERVER "pgrep -f '$JAR_NAME' > /dev/null"; then
    echo "✅ 服务启动成功"
else
    echo "⚠️  服务可能未完全启动，请检查日志"
fi

echo ""
echo "🎉 部署完成!"
echo "📋 检查命令:"
echo "   日志: ssh $TARGET_SERVER 'tail -f $BACKEND_DIR/zoombus.log'"
echo "   状态: ssh $TARGET_SERVER 'pgrep -f $JAR_NAME'"
