#!/bin/bash

# ZoomBus 快速开发启动脚本
# 简化版本，快速启动前后端开发环境

echo "🚀 ZoomBus 快速开发启动"
echo "======================="

# 检查基本工具
if ! command -v java &> /dev/null; then
    echo "❌ Java未安装"
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装"
    exit 1
fi

# 设置Maven命令
if [ -f "./mvnw" ]; then
    MVN_CMD="./mvnw"
else
    MVN_CMD="mvn"
fi

echo "✓ 环境检查通过"

# 清理函数
cleanup() {
    echo ""
    echo "🛑 停止所有服务..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    echo "✓ 服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup INT TERM

echo ""
echo "🔧 启动后端服务..."
$MVN_CMD spring-boot:run > backend.log 2>&1 &
BACKEND_PID=$!

echo "⏳ 等待后端启动..."
sleep 15

echo ""
echo "🎨 启动前端服务..."
cd frontend

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 设置环境变量并启动
export BROWSER=none
export PORT=3000
npm start > ../frontend.log 2>&1 &
FRONTEND_PID=$!

cd ..

echo ""
echo "🎉 启动完成!"
echo "==================="
echo "🚀 后端: http://localhost:8080"
echo "🎨 前端: http://localhost:3000 (推荐)"
echo "🗄️  数据库: http://localhost:8080/h2-console"
echo ""
echo "📝 日志文件:"
echo "   - 后端: backend.log"
echo "   - 前端: frontend.log"
echo ""
echo "💡 使用 http://localhost:3000 进行开发"
echo "   前端会自动代理API请求到后端"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
wait
