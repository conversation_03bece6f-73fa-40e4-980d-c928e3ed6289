#!/bin/bash

# 快速生产环境数据迁移脚本
# 将四张核心表快速迁移到生产环境 <EMAIL>
# 执行日期: 2025-08-20

set -e

# 配置变量
LOCAL_DB_USER="root"
LOCAL_DB_PASS="nvshen2018"
LOCAL_DB_NAME="zoombusV"
PROD_SERVER="<EMAIL>"
PROD_DB_USER="root"
PROD_DB_PASS="nvshen2018"
PROD_DB_NAME="zoombusv"

# 要迁移的表（按依赖关系排序）
TABLES=("t_users" "t_pmi_records" "t_pmi_schedules" "t_pmi_schedule_windows")

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=========================================="
echo "快速生产环境数据迁移"
echo "目标: $PROD_SERVER"
echo "表: ${TABLES[*]}"
echo -e "==========================================${NC}"

# 确认操作
read -p "确认执行生产环境迁移？将清空目标表数据！(y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

# 创建临时目录
TEMP_DIR="quick_migration_$(date +%Y%m%d_%H%M%S)"
mkdir -p $TEMP_DIR

echo -e "${BLUE}[1/6] 检查连接...${NC}"
# 检查SSH连接
ssh -o ConnectTimeout=10 $PROD_SERVER "echo 'SSH OK'" > /dev/null
echo "✓ SSH连接正常"

# 检查数据库连接
mysql -u$LOCAL_DB_USER -p$LOCAL_DB_PASS -e "USE $LOCAL_DB_NAME;" > /dev/null
echo "✓ 本地数据库连接正常"

ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS -e 'USE $PROD_DB_NAME;'" > /dev/null
echo "✓ 生产数据库连接正常"

echo -e "${BLUE}[2/6] 统计本地数据...${NC}"
for table in "${TABLES[@]}"; do
    count=$(mysql -u$LOCAL_DB_USER -p$LOCAL_DB_PASS $LOCAL_DB_NAME -e "SELECT COUNT(*) FROM $table;" -N)
    echo "  $table: $count 条记录"
done

echo -e "${BLUE}[3/6] 备份生产数据...${NC}"
BACKUP_DIR="/tmp/prod_backup_$(date +%Y%m%d_%H%M%S)"
ssh $PROD_SERVER "mkdir -p $BACKUP_DIR"

for table in "${TABLES[@]}"; do
    echo "  备份 $table..."
    ssh $PROD_SERVER "mysqldump -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME $table > $BACKUP_DIR/${table}.sql"
done
echo "✓ 生产数据备份完成: $PROD_SERVER:$BACKUP_DIR"

echo -e "${BLUE}[4/6] 清空生产表...${NC}"
# 按依赖关系倒序清空
REVERSE_TABLES=("t_pmi_schedule_windows" "t_pmi_schedules" "t_pmi_records" "t_users")
for table in "${REVERSE_TABLES[@]}"; do
    echo "  清空 $table..."
    ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME -e 'SET FOREIGN_KEY_CHECKS = 0; TRUNCATE TABLE $table; SET FOREIGN_KEY_CHECKS = 1;'"
done
echo "✓ 生产表清空完成"

echo -e "${BLUE}[5/7] 迁移数据...${NC}"
for table in "${TABLES[@]}"; do
    echo "  导出 $table..."

    # 导出本地数据
    mysqldump -u$LOCAL_DB_USER -p$LOCAL_DB_PASS \
        --single-transaction \
        --no-create-info \
        --complete-insert \
        $LOCAL_DB_NAME $table > $TEMP_DIR/${table}.sql

    echo "  转换字符集格式..."
    # 转换字符集格式以兼容生产环境
    sed -i.bak \
        -e 's/utf8mb4_0900_ai_ci/utf8mb4_general_ci/g' \
        -e 's/utf8mb4_unicode_ci/utf8mb4_general_ci/g' \
        -e 's/utf8mb4_unicode_520_ci/utf8mb4_general_ci/g' \
        -e 's/utf8mb4_bin/utf8mb4_general_ci/g' \
        $TEMP_DIR/${table}.sql

    echo "  传输到生产环境..."
    # 传输到生产环境
    scp $TEMP_DIR/${table}.sql $PROD_SERVER:/tmp/

    echo "  导入到生产环境..."
    # 导入到生产环境
    ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME < /tmp/${table}.sql"

    # 清理远程临时文件
    ssh $PROD_SERVER "rm -f /tmp/${table}.sql"
done
echo "✓ 数据迁移完成"

echo -e "${BLUE}[6/7] 验证结果...${NC}"
echo "迁移结果对比:"
for table in "${TABLES[@]}"; do
    local_count=$(mysql -u$LOCAL_DB_USER -p$LOCAL_DB_PASS $LOCAL_DB_NAME -e "SELECT COUNT(*) FROM $table;" -N)
    prod_count=$(ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME -e 'SELECT COUNT(*) FROM $table;' -N")
    
    if [ "$local_count" -eq "$prod_count" ]; then
        echo -e "  $table: ${GREEN}✓${NC} $local_count → $prod_count"
    else
        echo -e "  $table: ${RED}✗${NC} $local_count → $prod_count (不匹配!)"
        exit 1
    fi
done

# 清理本地临时文件
rm -rf $TEMP_DIR

echo -e "${GREEN}=========================================="
echo "✓ 生产环境迁移完成！"
echo "✓ 备份位置: $PROD_SERVER:$BACKUP_DIR"
echo -e "==========================================${NC}"

# 显示最终统计
echo "最终统计:"
for table in "${TABLES[@]}"; do
    count=$(ssh $PROD_SERVER "mysql -u$PROD_DB_USER -p$PROD_DB_PASS $PROD_DB_NAME -e 'SELECT COUNT(*) FROM $table;' -N")
    echo "  生产环境 $table: $count 条记录"
done
