#!/bin/bash

# ZoomBus 快速启动脚本
# 用于在生产服务器上快速启动ZoomBus应用

set -e

# 配置变量
JAR_NAME="zoombus-1.0.0.jar"
LOG_FILE="zoombus.log"
PID_FILE="zoombus.pid"
WORK_DIR="/root/zoombus"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 切换到工作目录
cd "$WORK_DIR" || {
    log_error "无法切换到工作目录: $WORK_DIR"
    exit 1
}

log_info "ZoomBus 快速启动脚本"
log_info "工作目录: $(pwd)"

# 停止现有进程
stop_existing_process() {
    log_info "检查并停止现有进程..."
    
    # 通过PID文件停止
    if [ -f "$PID_FILE" ]; then
        OLD_PID=$(cat "$PID_FILE")
        if ps -p "$OLD_PID" > /dev/null 2>&1; then
            log_info "停止现有进程 PID: $OLD_PID"
            kill "$OLD_PID" || true
            sleep 3
            
            # 强制杀死如果还在运行
            if ps -p "$OLD_PID" > /dev/null 2>&1; then
                log_warning "强制停止进程 PID: $OLD_PID"
                kill -9 "$OLD_PID" || true
                sleep 2
            fi
        fi
        rm -f "$PID_FILE"
    fi
    
    # 通过进程名停止
    EXISTING_PIDS=$(pgrep -f "$JAR_NAME" || true)
    if [ -n "$EXISTING_PIDS" ]; then
        log_info "发现现有进程，正在停止: $EXISTING_PIDS"
        pkill -f "$JAR_NAME" || true
        sleep 3
        
        # 强制杀死如果还在运行
        REMAINING_PIDS=$(pgrep -f "$JAR_NAME" || true)
        if [ -n "$REMAINING_PIDS" ]; then
            log_warning "强制停止剩余进程: $REMAINING_PIDS"
            pkill -9 -f "$JAR_NAME" || true
            sleep 2
        fi
    fi
    
    log_success "现有进程已停止"
}

# 查找Java 11
find_java11() {
    log_info "查找Java 11..."
    
    # 常见的Java 11路径
    JAVA11_PATHS=(
        "/usr/lib/jvm/java-11-openjdk"
        "/usr/lib/jvm/java-11-openjdk-amd64"
        "/usr/lib/jvm/jdk-11"
        "/usr/lib/jvm/java-11-oracle"
        "/usr/lib/jvm/java-1.11.0-openjdk"
        "/usr/lib/jvm/java-1.11.0-openjdk-amd64"
        "/opt/java/openjdk-11"
        "/opt/jdk-11"
        "/usr/java/jdk-11"
    )
    
    # 尝试查找Java 11
    for path in "${JAVA11_PATHS[@]}"; do
        if [ -d "$path" ]; then
            JAVA_CMD="$path/bin/java"
            if [ -x "$JAVA_CMD" ]; then
                log_success "找到Java 11: $JAVA_CMD"
                return 0
            fi
        fi
    done
    
    # 动态查找
    JAVA11_PATH=$(find /usr/lib/jvm -name 'java-11*' -type d 2>/dev/null | head -1)
    if [ -n "$JAVA11_PATH" ] && [ -x "$JAVA11_PATH/bin/java" ]; then
        JAVA_CMD="$JAVA11_PATH/bin/java"
        log_success "动态找到Java 11: $JAVA_CMD"
        return 0
    fi
    
    # 使用系统默认Java并验证版本
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -1)
        if [[ "$JAVA_VERSION" == *"11."* ]] || [[ "$JAVA_VERSION" == *"version \"11"* ]]; then
            JAVA_CMD="java"
            log_success "使用系统Java 11: $JAVA_CMD"
            return 0
        else
            log_warning "系统Java版本不是11: $JAVA_VERSION"
        fi
    fi
    
    log_error "未找到Java 11，请先安装Java 11"
    exit 1
}

# 验证环境
validate_environment() {
    log_info "验证启动环境..."
    
    # 检查JAR文件
    if [ ! -f "$JAR_NAME" ]; then
        log_error "JAR文件不存在: $JAR_NAME"
        exit 1
    fi
    log_success "JAR文件存在: $JAR_NAME"
    
    # 验证Java版本
    JAVA_VERSION=$("$JAVA_CMD" -version 2>&1 | head -1)
    log_info "Java版本: $JAVA_VERSION"
    
    if [[ "$JAVA_VERSION" == *"11."* ]] || [[ "$JAVA_VERSION" == *"version \"11"* ]]; then
        log_success "Java版本验证通过"
    else
        log_error "Java版本不正确，需要Java 11"
        exit 1
    fi
}

# 启动应用
start_application() {
    log_info "启动ZoomBus应用..."
    
    # 设置JVM参数
    JVM_OPTS="-Xms512m -Xmx1024m"
    JVM_OPTS="$JVM_OPTS -Dspring.profiles.active=production"
    JVM_OPTS="$JVM_OPTS -Dserver.port=8080"
    JVM_OPTS="$JVM_OPTS -Djava.awt.headless=true"
    JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
    JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Shanghai"
    
    log_info "JVM参数: $JVM_OPTS"
    
    # 启动应用
    nohup "$JAVA_CMD" $JVM_OPTS -jar "$JAR_NAME" > "$LOG_FILE" 2>&1 &
    PID=$!
    
    # 保存PID
    echo $PID > "$PID_FILE"
    log_success "ZoomBus已启动，PID: $PID"
    
    # 等待启动
    log_info "等待应用启动..."
    sleep 10
    
    # 检查进程状态
    if ps -p $PID > /dev/null 2>&1; then
        log_success "进程运行正常"
        
        # 检查端口监听
        sleep 5
        if netstat -tlnp 2>/dev/null | grep :8080 > /dev/null; then
            log_success "端口8080监听正常"
        else
            log_warning "端口8080暂未监听，应用可能还在启动中"
        fi
        
        # 显示最近的日志
        log_info "最近的启动日志:"
        tail -10 "$LOG_FILE" | while read line; do
            echo "  $line"
        done
        
    else
        log_error "进程启动失败"
        log_error "错误日志:"
        tail -20 "$LOG_FILE" | while read line; do
            echo "  $line"
        done
        exit 1
    fi
}

# 显示状态信息
show_status() {
    echo ""
    echo "=== ZoomBus 启动完成 ==="
    log_success "应用PID: $(cat $PID_FILE 2>/dev/null || echo '未知')"
    log_success "日志文件: $WORK_DIR/$LOG_FILE"
    log_success "PID文件: $WORK_DIR/$PID_FILE"
    echo ""
    echo "💡 管理命令:"
    echo "   查看日志: tail -f $WORK_DIR/$LOG_FILE"
    echo "   检查状态: ps -p \$(cat $WORK_DIR/$PID_FILE)"
    echo "   停止服务: kill \$(cat $WORK_DIR/$PID_FILE)"
    echo ""
}

# 主函数
main() {
    stop_existing_process
    find_java11
    validate_environment
    start_application
    show_status
    
    log_success "🎉 ZoomBus启动完成!"
}

# 执行主函数
main "$@"
