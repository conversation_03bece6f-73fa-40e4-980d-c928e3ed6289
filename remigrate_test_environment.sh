#!/bin/bash

# 重新执行测试环境迁移脚本
# 修复LONG类型PMI记录的窗口字段问题和magic_id问题
# 执行日期: 2025-08-20

set -e  # 遇到错误立即退出

# 配置变量
DB_USER="root"
DB_PASS="nvshen2018"
DB_NAME="zoombusV"
OLD_DATA_DIR="/Users/<USER>/vibeCoding/zoombus/old_data"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=========================================="
echo "重新执行测试环境迁移"
echo "修复LONG类型PMI和magic_id问题"
echo "=========================================="

# 确认操作
read -p "确认要重新执行测试环境迁移吗？这将清空并重新导入所有数据！(y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    log_info "操作已取消"
    exit 0
fi

log_info "步骤1: 备份当前数据"
BACKUP_DIR="backup_before_remigration_$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

mysqldump -u$DB_USER -p$DB_PASS $DB_NAME t_users > $BACKUP_DIR/t_users_backup.sql
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME t_pmi_records > $BACKUP_DIR/t_pmi_records_backup.sql
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME t_pmi_schedules > $BACKUP_DIR/t_pmi_schedules_backup.sql
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME t_pmi_schedule_windows > $BACKUP_DIR/t_pmi_schedule_windows_backup.sql

log_success "数据备份完成: $BACKUP_DIR"

log_info "步骤2: 清空目标表"
mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE t_pmi_schedule_windows;
TRUNCATE TABLE t_pmi_schedules;
TRUNCATE TABLE t_pmi_records;
TRUNCATE TABLE t_users;
SET FOREIGN_KEY_CHECKS = 1;
"
log_success "目标表清空完成"

log_info "步骤3: 重新导入用户数据"
mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
INSERT INTO t_users (username, full_name, email, phone, department, status, created_at, updated_at)
SELECT
    COALESCE(nick_name, CONCAT('user_', id)) as username,
    COALESCE(real_name, nick_name, CONCAT('User ', id)) as full_name,
    CASE
        WHEN mobile IS NOT NULL AND mobile != '' THEN mobile
        ELSE CONCAT('user_', id, '@example.com')
    END as email,  -- 生成唯一的email
    COALESCE(mobile, '') as phone,
    COALESCE(city, '') as department,  -- 使用city作为department
    CASE
        WHEN del_flag = 0 THEN 'ACTIVE'
        ELSE 'INACTIVE'
    END as status,
    COALESCE(create_time, NOW()) as created_at,
    COALESCE(update_time, NOW()) as updated_at
FROM old_t_wx_user
WHERE del_flag = 0;
"

USER_COUNT=$(mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "SELECT COUNT(*) FROM t_users;" -N)
log_success "用户数据导入完成: $USER_COUNT 条记录"

log_info "步骤4: 重新导入PMI记录（修复magic_id问题）"
mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
INSERT INTO t_pmi_records (
    pmi_number, magic_id, pmi_password, user_id, billing_mode,
    status, created_at, updated_at
)
SELECT
    otp.pmi as pmi_number,
    COALESCE(otp.mg_id, CONCAT('mg_', otp.id)) as magic_id,  -- 确保magic_id来自mg_id
    otp.pmi_password,
    u.id as user_id,
    CASE
        WHEN otp.now_plan_type = 'LONG' THEN 'LONG'
        ELSE 'BY_TIME'  -- 修正为正确的枚举值
    END as billing_mode,
    CASE
        WHEN otp.del_flag = 0 THEN 'ACTIVE'
        ELSE 'INACTIVE'
    END as status,
    COALESCE(otp.create_time, NOW()) as created_at,
    COALESCE(otp.update_time, NOW()) as updated_at
FROM old_t_zoom_pmi otp
JOIN old_t_wx_user owu ON otp.user_id = owu.id
JOIN t_users u ON COALESCE(owu.nick_name, CONCAT('user_', owu.id)) = u.username
WHERE otp.del_flag = 0;
"

PMI_COUNT=$(mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "SELECT COUNT(*) FROM t_pmi_records;" -N)
log_success "PMI记录导入完成: $PMI_COUNT 条记录"

log_info "步骤5: 导入PMI计划数据"
mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
INSERT INTO t_pmi_schedules (
    pmi_record_id, schedule_type, start_date, end_date, 
    is_active, created_at, updated_at
)
SELECT 
    pr.id as pmi_record_id,
    CASE 
        WHEN pr.billing_mode = 'LONG' THEN 'LONG_TERM'
        ELSE 'HOURLY'
    END as schedule_type,
    COALESCE(STR_TO_DATE(ozp.create_time, '%Y-%m-%d'), CURDATE()) as start_date,
    CASE 
        WHEN pr.billing_mode = 'LONG' THEN DATE_ADD(CURDATE(), INTERVAL 1 YEAR)
        ELSE DATE_ADD(STR_TO_DATE(ozp.plan_end_date_time, '%Y-%m-%d %H:%i:%s'), INTERVAL ozp.durationh HOUR)
    END as end_date,
    1 as is_active,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_records pr
JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi
JOIN old_t_zoom_plan ozpl ON ozp.id = ozpl.pmi_id
WHERE ozpl.del_flag = 0;
"

SCHEDULE_COUNT=$(mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "SELECT COUNT(*) FROM t_pmi_schedules;" -N)
log_success "PMI计划导入完成: $SCHEDULE_COUNT 条记录"

log_info "步骤6: 导入PMI窗口数据"
mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
INSERT INTO t_pmi_schedule_windows (
    schedule_id, window_start, window_end, status, created_at, updated_at
)
SELECT 
    ps.id as schedule_id,
    COALESCE(STR_TO_DATE(ozw.start_time, '%Y-%m-%d %H:%i:%s'), NOW()) as window_start,
    COALESCE(STR_TO_DATE(ozw.end_time, '%Y-%m-%d %H:%i:%s'), DATE_ADD(NOW(), INTERVAL 1 HOUR)) as window_end,
    CASE 
        WHEN ozw.status = 1 THEN 'ACTIVE'
        WHEN ozw.status = 2 THEN 'EXPIRED'
        ELSE 'PENDING'
    END as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules ps
JOIN t_pmi_records pr ON ps.pmi_record_id = pr.id
JOIN old_t_zoom_pmi ozp ON pr.pmi_number = ozp.pmi
JOIN old_t_zoom_plan ozpl ON ozp.id = ozpl.pmi_id
JOIN old_t_zoom_windows ozw ON ozpl.id = ozw.plan_id
WHERE ozw.del_flag = 0;
"

WINDOW_COUNT=$(mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "SELECT COUNT(*) FROM t_pmi_schedule_windows;" -N)
log_success "PMI窗口导入完成: $WINDOW_COUNT 条记录"

log_info "步骤7: 修复LONG类型PMI的窗口字段"
mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
-- 为没有窗口的LONG类型PMI创建默认窗口
INSERT INTO t_pmi_schedule_windows (
    schedule_id, window_start, window_end, status, created_at, updated_at
)
SELECT 
    ps.id as schedule_id,
    CURDATE() as window_start,
    DATE_ADD(CURDATE(), INTERVAL 1 YEAR) as window_end,
    'ACTIVE' as status,
    NOW() as created_at,
    NOW() as updated_at
FROM t_pmi_schedules ps
JOIN t_pmi_records pr ON ps.pmi_record_id = pr.id
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
WHERE pr.billing_mode = 'LONG'
AND ps.schedule_type = 'LONG_TERM'
AND psw.id IS NULL;

-- 更新LONG类型PMI记录的窗口字段
UPDATE t_pmi_records pr
JOIN t_pmi_schedules ps ON pr.id = ps.pmi_record_id
JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id
SET 
    pr.current_window_id = psw.id,
    pr.window_expire_time = psw.window_end,
    pr.updated_at = NOW()
WHERE pr.billing_mode = 'LONG'
AND ps.schedule_type = 'LONG_TERM'
AND psw.status = 'ACTIVE'
AND (pr.current_window_id IS NULL OR pr.window_expire_time IS NULL);
"

log_success "LONG类型PMI窗口字段修复完成"

log_info "步骤8: 验证迁移结果"
mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
SELECT '=== 迁移结果验证 ===' as verification;

SELECT 
    'Table Counts' as check_type,
    'Users' as table_name,
    COUNT(*) as record_count
FROM t_users
UNION ALL
SELECT 
    'Table Counts' as check_type,
    'PMI Records' as table_name,
    COUNT(*) as record_count
FROM t_pmi_records
UNION ALL
SELECT 
    'Table Counts' as check_type,
    'PMI Schedules' as table_name,
    COUNT(*) as record_count
FROM t_pmi_schedules
UNION ALL
SELECT 
    'Table Counts' as check_type,
    'Schedule Windows' as table_name,
    COUNT(*) as record_count
FROM t_pmi_schedule_windows;

SELECT 
    'LONG PMI Window Check' as check_type,
    COUNT(*) as total_long_pmi,
    COUNT(CASE WHEN current_window_id IS NULL THEN 1 END) as missing_window_id,
    COUNT(CASE WHEN window_expire_time IS NULL THEN 1 END) as missing_expire_time,
    COUNT(CASE WHEN current_window_id IS NOT NULL AND window_expire_time IS NOT NULL THEN 1 END) as fixed_records
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

SELECT 
    'Magic ID Check' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN magic_id IS NOT NULL AND magic_id != '' THEN 1 END) as has_magic_id,
    COUNT(CASE WHEN magic_id IS NULL OR magic_id = '' THEN 1 END) as missing_magic_id
FROM t_pmi_records;
"

log_success "=========================================="
log_success "测试环境重新迁移完成！"
log_success "备份位置: $BACKUP_DIR"
log_success "=========================================="

# 显示最终统计
echo ""
echo "最终数据统计:"
mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 'Users' as table_name, COUNT(*) as count FROM t_users
UNION ALL
SELECT 'PMI Records', COUNT(*) FROM t_pmi_records  
UNION ALL
SELECT 'PMI Schedules', COUNT(*) FROM t_pmi_schedules
UNION ALL
SELECT 'Schedule Windows', COUNT(*) FROM t_pmi_schedule_windows;
" -t

echo ""
echo "LONG类型PMI状态:"
mysql -u$DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    billing_mode,
    COUNT(*) as total,
    COUNT(CASE WHEN current_window_id IS NOT NULL THEN 1 END) as has_window,
    COUNT(CASE WHEN magic_id IS NOT NULL THEN 1 END) as has_magic_id
FROM t_pmi_records 
GROUP BY billing_mode;
" -t
