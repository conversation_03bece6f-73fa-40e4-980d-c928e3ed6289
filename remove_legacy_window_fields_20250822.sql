-- 删除PMI窗口表中的冗余字段
-- 执行日期: 2025-08-22
-- 目的: 简化系统复杂度，删除已被startDateTime和endDateTime替代的旧字段

USE zoombusV;

-- 1. 备份当前表结构
SELECT '=== 备份当前表结构 ===' as step;
CREATE TABLE IF NOT EXISTS t_pmi_schedule_windows_structure_backup_20250822 AS
SELECT * FROM t_pmi_schedule_windows LIMIT 0;

-- 2. 验证所有窗口都有新字段数据
SELECT '=== 验证数据完整性 ===' as step;
SELECT 
    '数据完整性检查' as check_type,
    COUNT(*) as total_windows,
    COUNT(CASE WHEN start_date_time IS NOT NULL THEN 1 END) as has_start_datetime,
    COUNT(CASE WHEN end_date_time IS NOT NULL THEN 1 END) as has_end_datetime,
    COUNT(CASE WHEN start_date_time IS NULL OR end_date_time IS NULL THEN 1 END) as missing_new_fields
FROM t_pmi_schedule_windows;

-- 3. 如果有缺失数据，停止执行
-- 注意：这个检查需要手动验证，如果missing_new_fields > 0，请不要继续执行

-- 4. 显示将要删除的字段
SELECT '=== 将要删除的字段 ===' as step;
DESCRIBE t_pmi_schedule_windows;

-- 5. 删除旧字段
SELECT '=== 开始删除旧字段 ===' as step;

-- 删除window_date字段
ALTER TABLE t_pmi_schedule_windows DROP COLUMN window_date;
SELECT 'window_date字段已删除' as status;

-- 删除end_date字段
ALTER TABLE t_pmi_schedule_windows DROP COLUMN end_date;
SELECT 'end_date字段已删除' as status;

-- 删除start_time字段
ALTER TABLE t_pmi_schedule_windows DROP COLUMN start_time;
SELECT 'start_time字段已删除' as status;

-- 删除end_time字段
ALTER TABLE t_pmi_schedule_windows DROP COLUMN end_time;
SELECT 'end_time字段已删除' as status;

-- 6. 验证删除结果
SELECT '=== 验证删除结果 ===' as step;
DESCRIBE t_pmi_schedule_windows;

-- 7. 检查表数据完整性
SELECT '=== 检查数据完整性 ===' as step;
SELECT 
    '删除后数据检查' as check_type,
    COUNT(*) as total_windows,
    COUNT(CASE WHEN start_date_time IS NOT NULL THEN 1 END) as has_start_datetime,
    COUNT(CASE WHEN end_date_time IS NOT NULL THEN 1 END) as has_end_datetime,
    COUNT(CASE WHEN start_date_time IS NULL OR end_date_time IS NULL THEN 1 END) as missing_datetime_fields
FROM t_pmi_schedule_windows;

-- 8. 显示优化后的表结构
SELECT '=== 优化后的表结构 ===' as step;
SHOW CREATE TABLE t_pmi_schedule_windows;

-- 9. 统计报告
SELECT '=== 删除操作统计报告 ===' as step;
SELECT 
    '字段删除完成' as operation,
    'window_date, end_date, start_time, end_time' as deleted_fields,
    'start_date_time, end_date_time' as remaining_datetime_fields,
    '简化了系统复杂度，避免了数据不一致问题' as benefit,
    NOW() as completed_at;

-- 10. 重要提醒
SELECT '=== 重要提醒 ===' as step;
SELECT 
    '请确保以下事项已完成：' as reminder_title
UNION ALL
SELECT '1. 后端代码已更新，不再使用旧字段' as reminder
UNION ALL
SELECT '2. 前端代码已更新，不再使用旧字段' as reminder
UNION ALL
SELECT '3. 所有相关服务已重启' as reminder
UNION ALL
SELECT '4. 功能测试已通过' as reminder
UNION ALL
SELECT '5. 如需回滚，请使用备份表恢复' as reminder;
