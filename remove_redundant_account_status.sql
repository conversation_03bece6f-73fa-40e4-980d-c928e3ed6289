-- 移除冗余的account_status字段
-- 执行时间：2025-08-03
-- 说明：account_status字段与usage_status功能重复，可以安全移除

USE zoombusV;

-- 1. 检查当前表结构
SELECT 'Current table structure:' as info;
DESCRIBE t_zoom_accounts;

-- 2. 检查两个状态字段的数据一致性
SELECT 'Status field comparison:' as info;
SELECT 
    status,
    usage_status,
    account_status,
    COUNT(*) as count
FROM t_zoom_accounts 
GROUP BY status, usage_status, account_status
ORDER BY count DESC;

-- 3. 确保usage_status字段有正确的值（基于account_status同步）
SELECT 'Syncing usage_status from account_status (if needed):' as info;
UPDATE t_zoom_accounts 
SET usage_status = CASE 
    WHEN account_status = 'IN_USE' THEN 'IN_USE'
    WHEN account_status = 'MAINTENANCE' THEN 'MAINTENANCE'
    ELSE 'AVAILABLE'
END
WHERE usage_status IS NULL 
   OR usage_status = ''
   OR (account_status IS NOT NULL AND account_status != '' AND usage_status != account_status);

-- 4. 显示同步后的状态
SELECT 'Status after sync:' as info;
SELECT 
    usage_status,
    account_status,
    COUNT(*) as count
FROM t_zoom_accounts 
GROUP BY usage_status, account_status
ORDER BY count DESC;

-- 5. 备份account_status字段的数据（可选）
-- CREATE TABLE t_zoom_accounts_account_status_backup AS
-- SELECT id, email, account_status, created_at 
-- FROM t_zoom_accounts 
-- WHERE account_status IS NOT NULL;

-- 6. 移除account_status字段的索引（如果存在）
SELECT 'Removing account_status index:' as info;
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = 'zoombusV' 
     AND TABLE_NAME = 't_zoom_accounts' 
     AND INDEX_NAME = 'idx_account_status') > 0,
    'ALTER TABLE t_zoom_accounts DROP INDEX idx_account_status',
    'SELECT ''idx_account_status index does not exist'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 移除account_status字段
SELECT 'Removing account_status column:' as info;
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'zoombusV' 
     AND TABLE_NAME = 't_zoom_accounts' 
     AND COLUMN_NAME = 'account_status') > 0,
    'ALTER TABLE t_zoom_accounts DROP COLUMN account_status',
    'SELECT ''account_status column does not exist'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. 验证最终表结构
SELECT 'Final table structure:' as info;
DESCRIBE t_zoom_accounts;

-- 9. 验证数据完整性
SELECT 'Final status distribution:' as info;
SELECT 
    status as zoom_status,
    usage_status as system_usage_status,
    COUNT(*) as count
FROM t_zoom_accounts 
GROUP BY status, usage_status
ORDER BY count DESC;

-- 10. 显示清理结果
SELECT 'Cleanup completed successfully!' as result;
SELECT 'Fields remaining:' as info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'zoombusV'
  AND TABLE_NAME = 't_zoom_accounts'
  AND COLUMN_NAME IN ('status', 'usage_status')
ORDER BY ORDINAL_POSITION;
