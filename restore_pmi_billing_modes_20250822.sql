-- 恢复PMI计费模式脚本
-- 修复日期: 2025-08-22
-- 问题: 被误关闭的窗口对应的PMI需要恢复到LONG计费模式

-- 备份当前PMI记录状态
CREATE TABLE IF NOT EXISTS t_pmi_records_backup_20250822 AS
SELECT * FROM t_pmi_records 
WHERE id IN (
    18, 68, 74, 112, 125, 165, 172, 176, 179, 184,
    199, 201, 209, 276, 279, 288, 298, 406, 484, 623
);

-- 显示备份信息
SELECT 
    '备份PMI记录完成' as status,
    COUNT(*) as backup_count
FROM t_pmi_records_backup_20250822;

-- 为每个PMI恢复LONG计费模式和窗口信息
UPDATE t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.id = w.pmi_record_id
SET 
    p.billing_mode = 'LONG',
    p.original_billing_mode = 'BY_TIME',
    p.current_window_id = w.id,
    p.window_expire_time = CASE 
        WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
        ELSE TIMESTAMP(w.window_date, w.end_time)
    END,
    p.active_window_ids = CONCAT('[', w.id, ']'),
    p.updated_at = NOW()
WHERE p.id IN (
    18, 68, 74, 112, 125, 165, 172, 176, 179, 184,
    199, 201, 209, 276, 279, 288, 298, 406, 484, 623
)
AND w.id IN (
    991, 985, 2048, 954, 1023, 1030, 907, 923, 892, 1031,
    989, 966, 983, 946, 962, 1013, 963, 2049, 927, 1026
)
AND w.status = 'ACTIVE';

-- 验证恢复结果
SELECT 
    p.id as pmi_record_id,
    p.pmi_number,
    p.billing_mode,
    p.current_window_id,
    p.window_expire_time,
    p.active_window_ids,
    w.id as window_id,
    w.window_date,
    w.end_date,
    w.end_time,
    w.status as window_status,
    CASE 
        WHEN w.end_date IS NOT NULL THEN TIMESTAMP(w.end_date, w.end_time)
        ELSE TIMESTAMP(w.window_date, w.end_time)
    END as calculated_expire_time
FROM t_pmi_records p
JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
WHERE p.id IN (
    18, 68, 74, 112, 125, 165, 172, 176, 179, 184,
    199, 201, 209, 276, 279, 288, 298, 406, 484, 623
)
ORDER BY p.id;

-- 统计恢复结果
SELECT 
    '恢复统计' as summary_type,
    COUNT(*) as total_restored_pmis,
    SUM(CASE WHEN billing_mode = 'LONG' THEN 1 ELSE 0 END) as long_billing_count,
    SUM(CASE WHEN current_window_id IS NOT NULL THEN 1 ELSE 0 END) as has_current_window_count,
    SUM(CASE WHEN window_expire_time IS NOT NULL THEN 1 ELSE 0 END) as has_expire_time_count
FROM t_pmi_records 
WHERE id IN (
    18, 68, 74, 112, 125, 165, 172, 176, 179, 184,
    199, 201, 209, 276, 279, 288, 298, 406, 484, 623
);
