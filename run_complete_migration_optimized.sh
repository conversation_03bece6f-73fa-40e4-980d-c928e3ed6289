#!/bin/bash

# 完整优化版数据迁移自动化脚本
# 从老系统4张表数据导入到测试环境，然后迁移到生产环境
# 执行日期: 2025-08-20

set -e  # 遇到错误立即退出

# 配置变量
DB_USER="root"
DB_PASS="nvshen2018"
DB_NAME="zoombusV"
OLD_DATA_DIR="/Users/<USER>/vibeCoding/zoombus/old_data"
MYSQL_CMD="/usr/local/mysql-8.0.11-macos10.13-x86_64/bin/mysql"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查文件是否存在
check_files() {
    log_info "检查老系统数据文件..."
    
    local files=(
        "$OLD_DATA_DIR/old_t_wx_user20250827.sql"
        "$OLD_DATA_DIR/old_t_zoom_pmi20250827.sql"
        "$OLD_DATA_DIR/old_t_zoom_plan20250827.sql"
        "$OLD_DATA_DIR/old_t_zoom_windows20250827.sql"
    )
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "文件不存在: $file"
            exit 1
        fi
        log_info "✓ 找到文件: $(basename "$file")"
    done
    
    log_success "所有老系统数据文件检查通过"
}

# 创建老系统数据表结构
create_old_tables() {
    log_info "创建老系统数据表结构..."
    
    "$MYSQL_CMD" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" <<'EOF'
-- 清理并重建老系统数据表
DROP TABLE IF EXISTS old_t_wx_user;
DROP TABLE IF EXISTS old_t_zoom_pmi;
DROP TABLE IF EXISTS old_t_zoom_plan;
DROP TABLE IF EXISTS old_t_zoom_windows;

-- 创建老系统用户表
CREATE TABLE old_t_wx_user (
    id VARCHAR(255) PRIMARY KEY,
    create_by VARCHAR(255),
    create_time DATETIME,
    del_flag INT(11),
    update_by VARCHAR(255),
    update_time DATETIME,
    avatar_url VARCHAR(255),
    city VARCHAR(255),
    country VARCHAR(255),
    gender VARCHAR(255),
    language VARCHAR(255),
    nick_name VARCHAR(255),
    open_id VARCHAR(255),
    province VARCHAR(255),
    union_id VARCHAR(255),
    duration DOUBLE,
    frozen_duration DOUBLE,
    long_frozen_duration DOUBLE,
    durationh INT(11) NOT NULL DEFAULT 0,
    durationm INT(11) NOT NULL DEFAULT 0,
    frozen_durationh INT(11) NOT NULL DEFAULT 0,
    frozen_durationm INT(11) NOT NULL DEFAULT 0,
    long_frozen_durationh INT(11) NOT NULL DEFAULT 0,
    long_frozen_durationm INT(11) NOT NULL DEFAULT 0,
    mobile VARCHAR(255),
    now_plan_id VARCHAR(255),
    now_plan_type VARCHAR(255),
    openid VARCHAR(255),
    plan_end_date_time VARCHAR(255),
    mp_openid VARCHAR(255),
    unionid VARCHAR(255),
    pmi VARCHAR(255),
    real_name VARCHAR(255),
    real_name_index VARCHAR(255),
    user_qr VARCHAR(255),
    is_premium VARCHAR(255),
    user_vcard VARCHAR(255),
    long_close_notify_flag VARCHAR(255),
    defaultpmi VARCHAR(255),
    zoom_account VARCHAR(255),
    meeting_capacity INT(11) NOT NULL,
    provider_code INT(11) NOT NULL,
    license_end_meeting VARCHAR(255),
    license_start_meeting VARCHAR(255),
    notify_mobile VARCHAR(255),
    host_way VARCHAR(255),
    zoom_password VARCHAR(255),
    telegram_id VARCHAR(255),
    telegram_name VARCHAR(255)
);

-- 创建老系统PMI表
CREATE TABLE old_t_zoom_pmi (
    id VARCHAR(255) PRIMARY KEY,
    create_by VARCHAR(255),
    create_time DATETIME(6),
    del_flag INT(11),
    update_by VARCHAR(255),
    update_time DATETIME(6),
    comments VARCHAR(255),
    durationh INT(11) NOT NULL,
    durationm INT(11) NOT NULL,
    frozen_durationh INT(11) NOT NULL,
    frozen_durationm INT(11) NOT NULL,
    has_plan VARCHAR(255),
    latest_windows_id VARCHAR(255),
    long_frozen_durationh INT(11) NOT NULL,
    long_frozen_durationm INT(11) NOT NULL,
    meeting_capacity INT(11) NOT NULL,
    now_plan_type VARCHAR(255),
    plan_end_date_time VARCHAR(255),
    pmi VARCHAR(255),
    use_status INT(11),
    user_id VARCHAR(255),
    mg_id VARCHAR(255),
    pmi_password VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    personal_meeting_url VARCHAR(255),
    usepmi VARCHAR(255),
    recording_notify_emails VARCHAR(255),
    health_check_date VARCHAR(255),
    health_check_status INT(11) NOT NULL,
    health_check_try_times INT(11) NOT NULL,
    jump_to_new VARCHAR(255),
    isvip VARCHAR(255),
    host_by_local_url INT(11) NOT NULL
);

-- 创建老系统计划表
CREATE TABLE old_t_zoom_plan (
    id VARCHAR(255) PRIMARY KEY,
    create_by VARCHAR(255),
    create_time DATETIME,
    del_flag INT(11),
    update_by VARCHAR(255),
    update_time DATETIME,
    comments VARCHAR(255),
    end_date VARCHAR(255),
    end_time VARCHAR(255),
    repet INT(11),
    start_date VARCHAR(255),
    start_time VARCHAR(255),
    status INT(11),
    day_of_week VARCHAR(255),
    day_of_month VARCHAR(255),
    all_day INT(11),
    order_id VARCHAR(255),
    lasted_time VARCHAR(255),
    amt VARCHAR(255),
    zoom_password VARCHAR(255),
    wangwang_nick_name VARCHAR(255),
    account_id VARCHAR(255),
    user_id VARCHAR(255),
    pre_windows_id VARCHAR(255),
    plan_type VARCHAR(255),
    zoompmiid VARCHAR(255)
);

-- 创建老系统窗口表
CREATE TABLE old_t_zoom_windows (
    id VARCHAR(255) PRIMARY KEY,
    create_by VARCHAR(255),
    create_time DATETIME,
    del_flag INT(11),
    update_by VARCHAR(255),
    update_time DATETIME,
    comments VARCHAR(255),
    end_time VARCHAR(255),
    plan_id VARCHAR(255),
    start_time VARCHAR(255),
    status INT(11),
    end_date VARCHAR(255),
    end_flag INT(11),
    start_date VARCHAR(255),
    start_flag INT(11),
    order_id VARCHAR(255),
    close_time VARCHAR(255),
    open_time VARCHAR(255),
    all_day INT(11),
    account_id VARCHAR(255),
    user_id VARCHAR(255),
    next_windows_id VARCHAR(255),
    pre_windows_id VARCHAR(255),
    windows_type VARCHAR(255),
    zoompmiid VARCHAR(255)
);

SELECT '老系统数据表创建完成' as status;
EOF
    
    log_success "老系统数据表结构创建完成"
}

# 导入老系统数据
import_old_data() {
    log_info "导入老系统数据到测试环境..."
    
    # 导入用户数据
    log_info "导入用户数据..."
    sed 's/INSERT INTO ``/INSERT INTO old_t_wx_user/' "$OLD_DATA_DIR/old_t_wx_user20250827.sql" | \
    "$MYSQL_CMD" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME"

    # 导入PMI数据
    log_info "导入PMI数据..."
    sed 's/INSERT INTO ``/INSERT INTO old_t_zoom_pmi/' "$OLD_DATA_DIR/old_t_zoom_pmi20250827.sql" | \
    "$MYSQL_CMD" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME"

    # 导入计划数据
    log_info "导入计划数据..."
    sed 's/INSERT INTO ``/INSERT INTO old_t_zoom_plan/' "$OLD_DATA_DIR/old_t_zoom_plan20250827.sql" | \
    "$MYSQL_CMD" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME"

    # 导入窗口数据
    log_info "导入窗口数据..."
    sed 's/INSERT INTO ``/INSERT INTO old_t_zoom_windows/' "$OLD_DATA_DIR/old_t_zoom_windows20250827.sql" | \
    "$MYSQL_CMD" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME"
    
    # 验证导入结果
    log_info "验证老系统数据导入结果..."
    "$MYSQL_CMD" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
    SELECT
        'Old Data Import Result' as check_type,
        (SELECT COUNT(*) FROM old_t_wx_user) as old_users_count,
        (SELECT COUNT(*) FROM old_t_zoom_pmi) as old_pmi_count,
        (SELECT COUNT(*) FROM old_t_zoom_plan) as old_plan_count,
        (SELECT COUNT(*) FROM old_t_zoom_windows) as old_windows_count;
    "
    
    log_success "老系统数据导入完成"
}

# 执行数据迁移
run_migration() {
    log_info "执行完整数据迁移..."

    "$MYSQL_CMD" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < data_migration_script_complete.sql

    log_success "数据迁移完成"
}

# 验证迁移结果
verify_migration() {
    log_info "验证迁移结果..."

    "$MYSQL_CMD" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
    SELECT '=== 迁移结果验证 ===' as step;

    SELECT
        'Migration Summary' as summary_type,
        (SELECT COUNT(*) FROM t_users) as total_users,
        (SELECT COUNT(*) FROM t_pmi_records) as total_pmi_records,
        (SELECT COUNT(*) FROM t_pmi_schedules) as total_schedules,
        (SELECT COUNT(*) FROM t_pmi_schedule_windows) as total_windows;

    SELECT
        'Billing Mode Distribution' as check_type,
        billing_mode,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_pmi_records), 2) as percentage
    FROM t_pmi_records
    GROUP BY billing_mode;

    SELECT
        'User Distribution Sample' as check_type,
        u.username,
        COUNT(p.id) as pmi_count
    FROM t_users u
    LEFT JOIN t_pmi_records p ON u.id = p.user_id
    GROUP BY u.id, u.username
    HAVING pmi_count > 0
    ORDER BY pmi_count DESC
    LIMIT 10;

    SELECT
        'Window Status Distribution' as check_type,
        status,
        COUNT(*) as count
    FROM t_pmi_schedule_windows
    GROUP BY status;

    SELECT
        'Close Task Status' as check_type,
        COUNT(*) as total_close_tasks,
        COUNT(CASE WHEN status = 'SCHEDULED' THEN 1 END) as scheduled_tasks,
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_tasks,
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_tasks
    FROM t_pmi_schedule_window_tasks
    WHERE task_type = 'PMI_WINDOW_CLOSE';

    SELECT
        'Active Window Task Coverage' as check_type,
        COUNT(*) as total_active_windows,
        COUNT(CASE WHEN close_task_id IS NOT NULL THEN 1 END) as windows_with_close_task,
        ROUND(COUNT(CASE WHEN close_task_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as coverage_percentage
    FROM t_pmi_schedule_windows
    WHERE status = 'ACTIVE';
    "

    log_success "迁移结果验证完成"
}

# 迁移到生产环境
migrate_to_production() {
    log_info "开始迁移到生产环境..."
    
    if [ -f "./migrate_data_to_production.sh" ]; then
        ./migrate_data_to_production.sh --keep-files
        log_success "生产环境迁移完成"
    else
        log_warning "生产环境迁移脚本不存在，跳过生产环境迁移"
    fi
}

# 主函数
main() {
    log_info "开始完整优化版数据迁移流程..."
    log_info "时间戳: $(date)"
    
    # 检查参数
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        echo "用法: $0 [选项]"
        echo "选项:"
        echo "  --test-only      仅执行测试环境迁移，不迁移到生产环境"
        echo "  --skip-import    跳过老系统数据导入，直接执行迁移"
        echo "  --help, -h       显示帮助信息"
        echo ""
        echo "功能特性:"
        echo "  - 从4张老系统表完整迁移数据"
        echo "  - 保留历史计划和窗口数据"
        echo "  - 邮箱字段可以为空，不生成伪造数据"
        echo "  - 准确的窗口开始时间和状态"
        exit 0
    fi
    
    # 检查文件
    check_files
    
    # 创建表结构和导入老系统数据
    if [ "$1" != "--skip-import" ]; then
        create_old_tables
        import_old_data
    fi
    
    # 执行迁移
    run_migration
    
    # 验证结果
    verify_migration
    
    # 迁移到生产环境
    if [ "$1" != "--test-only" ]; then
        migrate_to_production
    fi
    
    log_success "完整优化版数据迁移流程执行完成！"
    log_info "迁移特性："
    log_info "  ✓ 4张老系统表数据完整迁移"
    log_info "  ✓ 历史计划和窗口数据保留"
    log_info "  ✓ 邮箱字段优化（可为空）"
    log_info "  ✓ 准确的时间和状态信息"
    log_info "  ✓ 活跃窗口自动创建关闭任务"
}

# 执行主函数
main "$@"
