# Java后端开发脚本使用说明

本目录包含了用于Java后端开发的自动化脚本，确保代码修改能够及时编译和生效。

## 🚀 脚本列表

### 1. `dev-start.sh` - 开发环境启动
**用途**: 启动开发环境，启用DevTools热重载功能
**使用场景**: 首次启动开发环境或需要完全重启时

```bash
./scripts/dev-start.sh
```

**特性**:
- 自动停止现有应用
- 检查端口占用
- 编译项目
- 启用DevTools热重载
- 前台运行，便于查看日志

### 2. `dev-restart.sh` - 完全重启
**用途**: 停止应用、重新编译、重新启动
**使用场景**: 配置文件修改、依赖变更、或热重载失效时

```bash
./scripts/dev-restart.sh
```

**特性**:
- 优雅停止现有应用
- 清理编译并重新编译
- 后台启动应用
- 健康检查验证启动成功

### 3. `quick-compile.sh` - 快速编译
**用途**: 快速编译代码，依赖DevTools自动重载
**使用场景**: 代码修改后需要立即编译时

```bash
./scripts/quick-compile.sh
```

**特性**:
- 快速编译（跳过测试）
- 检查应用状态
- 提示DevTools重载状态

### 4. `watch-compile.sh` - 文件监控编译
**用途**: 监控Java文件变化，自动触发编译
**使用场景**: 需要自动化编译流程时

```bash
./scripts/watch-compile.sh
```

**特性**:
- 实时监控Java文件变化
- 自动触发编译
- 防抖机制避免频繁编译
- 需要安装fswatch

## 📋 使用流程

### 日常开发流程

1. **启动开发环境**:
   ```bash
   ./scripts/dev-start.sh
   ```

2. **修改代码**: 编辑Java源文件

3. **自动重载**: DevTools会自动检测类文件变化并重载

4. **手动编译** (如需要):
   ```bash
   ./scripts/quick-compile.sh
   ```

### 问题排查流程

1. **热重载失效**: 使用完全重启
   ```bash
   ./scripts/dev-restart.sh
   ```

2. **配置修改**: 重启应用
   ```bash
   ./scripts/dev-restart.sh
   ```

3. **依赖变更**: 重启应用
   ```bash
   ./scripts/dev-restart.sh
   ```

## ⚙️ 配置说明

### DevTools配置
位置: `src/main/resources/application-dev.yml`

```yaml
spring:
  devtools:
    restart:
      enabled: true
      exclude: static/**,public/**,templates/**
      additional-paths: src/main/java
    livereload:
      enabled: true
      port: 35729
```

### 环境变量
- `MAVEN_OPTS`: JVM参数配置
- `SPRING_PROFILES_ACTIVE`: 激活开发环境配置

## 🔧 依赖要求

### 必需依赖
- Java 11+
- Maven 3.6+
- MySQL 8.0+

### 可选依赖
- `fswatch`: 用于文件监控 (`brew install fswatch`)

## 📊 端口说明

- **8080**: 后端API服务
- **3000**: 管理台前端 (开发模式)
- **3001**: 用户前端 (开发模式)
- **35729**: DevTools LiveReload

## 🐛 常见问题

### 1. 端口被占用
```bash
# 查看端口占用
lsof -i :8080

# 杀死进程
kill -9 <PID>
```

### 2. 编译失败
```bash
# 清理并重新编译
./mvnw clean compile
```

### 3. 热重载不生效
```bash
# 完全重启
./scripts/dev-restart.sh
```

### 4. 内存不足
```bash
# 增加JVM内存
export MAVEN_OPTS="-Xmx2048m -Xms1024m"
```

## 📝 日志位置

- **应用日志**: `logs/zoombus-application.log`
- **启动日志**: `logs/app-startup.log`
- **错误日志**: 控制台输出

## 🎯 最佳实践

1. **使用开发环境配置**: 确保使用 `dev` profile
2. **定期重启**: 长时间开发后建议完全重启
3. **监控日志**: 关注控制台和日志文件的错误信息
4. **及时编译**: 修改代码后及时编译验证
5. **清理缓存**: 遇到问题时先尝试清理编译缓存
