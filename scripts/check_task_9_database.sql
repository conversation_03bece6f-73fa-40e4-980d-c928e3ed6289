-- 检查生产环境task 9的数据库状态
-- 使用方法: mysql -u root -p zoombusV < check_task_9_database.sql

-- 设置输出格式
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL';

SELECT '=======================================' AS '';
SELECT 'Task 9 数据库诊断报告' AS '';
SELECT '=======================================' AS '';

-- 1. 检查task 9的基本信息
SELECT '1. Task 9 基本信息:' AS '';
SELECT 
    id,
    pmi_window_id,
    task_type,
    scheduled_time,
    actual_execution_time,
    status,
    task_key,
    retry_count,
    error_message,
    created_at,
    updated_at
FROM t_pmi_schedule_window_tasks 
WHERE id = 9;

-- 2. 检查task 9相关的PMI窗口信息
SELECT '2. Task 9 关联的PMI窗口信息:' AS '';
SELECT 
    w.id as window_id,
    w.pmi_record_id,
    w.start_date_time,
    w.end_date_time,
    w.status as window_status,
    w.actual_start_time,
    w.actual_end_time,
    w.open_task_id,
    w.close_task_id,
    w.error_message as window_error
FROM t_pmi_schedule_windows w
WHERE w.id = (SELECT pmi_window_id FROM t_pmi_schedule_window_tasks WHERE id = 9);

-- 3. 检查所有相关任务的状态
SELECT '3. 相关窗口的所有任务:' AS '';
SELECT 
    t.id,
    t.task_type,
    t.scheduled_time,
    t.status,
    t.retry_count,
    t.error_message
FROM t_pmi_schedule_window_tasks t
WHERE t.pmi_window_id = (SELECT pmi_window_id FROM t_pmi_schedule_window_tasks WHERE id = 9)
ORDER BY t.id;

-- 4. 检查任务执行记录
SELECT '4. Task 9 相关的执行记录:' AS '';
SELECT 
    id,
    task_name,
    task_type,
    status,
    execution_time,
    completion_time,
    duration_ms,
    retry_count,
    error_message,
    created_at
FROM task_execution_record 
WHERE task_name LIKE '%9%' OR task_name LIKE '%task_9%'
ORDER BY execution_time DESC
LIMIT 10;

-- 5. 检查最近的任务执行情况
SELECT '5. 最近的PMI任务执行情况:' AS '';
SELECT 
    task_type,
    status,
    COUNT(*) as count,
    MAX(actual_execution_time) as last_execution,
    MIN(scheduled_time) as earliest_scheduled,
    MAX(scheduled_time) as latest_scheduled
FROM t_pmi_schedule_window_tasks 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY task_type, status
ORDER BY task_type, status;

-- 6. 检查失败的任务
SELECT '6. 最近失败的任务:' AS '';
SELECT 
    id,
    task_type,
    scheduled_time,
    actual_execution_time,
    retry_count,
    error_message,
    updated_at
FROM t_pmi_schedule_window_tasks 
WHERE status = 'FAILED' 
AND updated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY updated_at DESC
LIMIT 10;

-- 7. 检查调度状态的任务
SELECT '7. 当前调度状态的任务:' AS '';
SELECT 
    id,
    task_type,
    scheduled_time,
    status,
    TIMESTAMPDIFF(MINUTE, scheduled_time, NOW()) as minutes_overdue,
    retry_count,
    error_message
FROM t_pmi_schedule_window_tasks 
WHERE status = 'SCHEDULED'
ORDER BY scheduled_time;

-- 8. 检查执行中的任务
SELECT '8. 当前执行中的任务:' AS '';
SELECT 
    id,
    task_type,
    scheduled_time,
    actual_execution_time,
    TIMESTAMPDIFF(MINUTE, actual_execution_time, NOW()) as running_minutes,
    status
FROM t_pmi_schedule_window_tasks 
WHERE status = 'EXECUTING'
ORDER BY actual_execution_time;

-- 9. 检查任务统计
SELECT '9. 任务状态统计:' AS '';
SELECT 
    status,
    COUNT(*) as count,
    MIN(created_at) as earliest,
    MAX(created_at) as latest
FROM t_pmi_schedule_window_tasks 
GROUP BY status
ORDER BY count DESC;

-- 10. 检查PMI记录状态
SELECT '10. Task 9 关联的PMI记录状态:' AS '';
SELECT 
    p.id,
    p.pmi_number,
    p.status as pmi_status,
    p.last_used_at,
    p.created_at,
    p.updated_at
FROM t_pmi_records p
WHERE p.id = (
    SELECT w.pmi_record_id 
    FROM t_pmi_schedule_windows w 
    WHERE w.id = (SELECT pmi_window_id FROM t_pmi_schedule_window_tasks WHERE id = 9)
);

-- 11. 检查系统时间和时区
SELECT '11. 系统时间信息:' AS '';
SELECT 
    NOW() as current_time,
    @@system_time_zone as system_timezone,
    @@time_zone as session_timezone;

-- 12. 检查最近的任务执行记录
SELECT '12. 最近的任务执行记录:' AS '';
SELECT 
    task_name,
    status,
    execution_time,
    completion_time,
    duration_ms,
    error_message
FROM task_execution_record 
WHERE execution_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY execution_time DESC
LIMIT 20;

SELECT '=======================================' AS '';
SELECT '诊断完成' AS '';
SELECT '=======================================' AS '';

-- 恢复SQL模式
SET SQL_MODE=@OLD_SQL_MODE;
