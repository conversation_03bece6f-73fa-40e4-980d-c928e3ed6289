#!/bin/bash

# Java后端开发重启脚本
# 用于确保代码修改后能够及时编译和生效

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 设置Java 11环境
export JAVA_HOME="/Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home"
export PATH="$JAVA_HOME/bin:$PATH"

echo -e "${BLUE}🔄 Java后端开发重启脚本${NC}"
echo -e "${BLUE}项目目录: $PROJECT_ROOT${NC}"

# 函数：打印带时间戳的日志
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN:${NC} $1"
}

# 函数：检查Java进程
check_java_process() {
    local pid=$(ps aux | grep "java.*zoombus" | grep -v grep | awk '{print $2}' | head -1)
    if [ -n "$pid" ]; then
        echo "$pid"
    else
        echo ""
    fi
}

# 函数：停止Java应用
stop_java_app() {
    log "🛑 停止Java应用..."
    
    local pid=$(check_java_process)
    if [ -n "$pid" ]; then
        log "发现Java进程: $pid"
        kill -TERM "$pid"
        
        # 等待优雅关闭
        local count=0
        while [ $count -lt 30 ]; do
            if [ -z "$(check_java_process)" ]; then
                log "✅ Java应用已优雅关闭"
                return 0
            fi
            sleep 1
            count=$((count + 1))
        done
        
        # 强制关闭
        warn "优雅关闭超时，强制关闭..."
        kill -KILL "$pid" 2>/dev/null || true
        sleep 2
        
        if [ -z "$(check_java_process)" ]; then
            log "✅ Java应用已强制关闭"
        else
            error "❌ 无法关闭Java应用"
            return 1
        fi
    else
        log "ℹ️ 没有发现运行中的Java应用"
    fi
}

# 函数：编译项目
compile_project() {
    log "🔨 开始编译项目..."
    
    # 清理并编译
    if ./mvnw clean compile -DskipTests; then
        log "✅ 项目编译成功"
        return 0
    else
        error "❌ 项目编译失败"
        return 1
    fi
}

# 函数：启动Java应用
start_java_app() {
    log "🚀 启动Java应用..."
    
    # 后台启动应用
    nohup ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev > logs/app-startup.log 2>&1 &
    local java_pid=$!
    
    log "Java应用启动中，PID: $java_pid"
    
    # 等待应用启动
    local count=0
    while [ $count -lt 60 ]; do
        if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log "✅ Java应用启动成功，健康检查通过"
            return 0
        fi
        sleep 2
        count=$((count + 2))
        
        if [ $((count % 10)) -eq 0 ]; then
            log "等待应用启动... (${count}s)"
        fi
    done
    
    error "❌ Java应用启动超时"
    return 1
}

# 函数：检查端口占用
check_port() {
    local port=$1
    # 检查是否有LISTEN状态的进程占用端口
    local listening=$(lsof -i :$port | grep LISTEN)
    if [ -n "$listening" ]; then
        warn "端口 $port 被占用"
        echo "$listening"
        return 1
    else
        log "端口 $port 可用"
        return 0
    fi
}

# 主流程
main() {
    log "开始执行重启流程..."
    
    # 1. 停止现有应用
    stop_java_app
    
    # 2. 检查端口
    sleep 2
    if ! check_port 8080; then
        error "端口8080仍被占用，请手动处理"
        exit 1
    fi
    
    # 3. 编译项目
    if ! compile_project; then
        error "编译失败，停止重启流程"
        exit 1
    fi
    
    # 4. 启动应用
    if ! start_java_app; then
        error "启动失败"
        exit 1
    fi
    
    log "🎉 重启完成！"
    log "应用地址: http://localhost:8080"
    log "健康检查: http://localhost:8080/actuator/health"
    log "启动日志: tail -f logs/app-startup.log"
}

# 执行主流程
main "$@"
