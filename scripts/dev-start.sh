#!/bin/bash

# Java后端开发环境启动脚本
# 启用DevTools热重载功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 设置Java 11环境
export JAVA_HOME="/Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home"
export PATH="$JAVA_HOME/bin:$PATH"

echo -e "${BLUE}🚀 Java后端开发环境启动脚本${NC}"
echo -e "${BLUE}项目目录: $PROJECT_ROOT${NC}"

# 函数：打印带时间戳的日志
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN:${NC} $1"
}

# 函数：检查Java进程
check_java_process() {
    local pid=$(ps aux | grep "java.*zoombus" | grep -v grep | awk '{print $2}' | head -1)
    if [ -n "$pid" ]; then
        echo "$pid"
    else
        echo ""
    fi
}

# 函数：检查端口占用
check_port() {
    local port=$1
    # 检查是否有LISTEN状态的进程占用端口
    local listening=$(lsof -i :$port | grep LISTEN)
    if [ -n "$listening" ]; then
        warn "端口 $port 被占用"
        echo "$listening"
        return 1
    else
        log "端口 $port 可用"
        return 0
    fi
}

# 函数：停止现有应用
stop_existing_app() {
    local pid=$(check_java_process)
    if [ -n "$pid" ]; then
        log "发现运行中的Java应用，PID: $pid"
        log "正在停止现有应用..."
        kill -TERM "$pid"
        
        # 等待优雅关闭
        local count=0
        while [ $count -lt 15 ]; do
            if [ -z "$(check_java_process)" ]; then
                log "✅ 现有应用已停止"
                return 0
            fi
            sleep 1
            count=$((count + 1))
        done
        
        # 强制关闭
        warn "优雅关闭超时，强制关闭..."
        kill -KILL "$pid" 2>/dev/null || true
        sleep 2
    fi
}

# 函数：创建必要的目录
create_directories() {
    log "📁 创建必要的目录..."
    mkdir -p logs
    mkdir -p target/classes
}

# 函数：编译项目
compile_project() {
    log "🔨 编译项目..."
    
    if ./mvnw compile -DskipTests; then
        log "✅ 项目编译成功"
        return 0
    else
        error "❌ 项目编译失败"
        return 1
    fi
}

# 函数：启动开发环境
start_dev_environment() {
    log "🚀 启动开发环境..."
    
    # 设置JVM参数以支持热重载
    export MAVEN_OPTS="-Xmx1024m -Xms512m -XX:+UseG1GC"
    
    # 启动应用（前台运行，便于查看日志）
    log "启动Spring Boot应用（开发模式）..."
    log "DevTools热重载已启用"
    log "按 Ctrl+C 停止应用"
    
    ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev \
        -Dspring-boot.run.jvmArguments="-Xmx1024m -Xms512m -XX:+UseG1GC" \
        -Dspring.devtools.restart.enabled=true \
        -Dspring.devtools.livereload.enabled=true
}

# 主流程
main() {
    log "开始启动开发环境..."
    
    # 1. 停止现有应用
    stop_existing_app
    
    # 2. 检查端口
    if ! check_port 8080; then
        error "端口8080被占用，请手动处理后重试"
        exit 1
    fi
    
    # 3. 创建目录
    create_directories
    
    # 4. 编译项目
    if ! compile_project; then
        error "编译失败，停止启动流程"
        exit 1
    fi
    
    # 5. 启动开发环境
    start_dev_environment
}

# 信号处理
trap 'log "👋 停止开发环境"; exit 0' INT TERM

# 执行主流程
main "$@"
