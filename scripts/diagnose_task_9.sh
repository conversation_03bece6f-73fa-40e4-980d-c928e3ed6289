#!/bin/bash

# 诊断生产环境task 9没有执行的问题
# 使用方法: ./diagnose_task_9.sh [BASE_URL] [AUTH_TOKEN]

# 默认配置
BASE_URL=${1:-"http://localhost:8080"}
AUTH_TOKEN=${2:-""}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 构建请求头
build_headers() {
    if [ -n "$AUTH_TOKEN" ]; then
        echo "-H 'Authorization: Bearer $AUTH_TOKEN'"
    else
        echo ""
    fi
}

# API调用函数
call_api() {
    local endpoint=$1
    local method=${2:-"GET"}
    local headers=$(build_headers)
    
    log_info "调用API: $method $BASE_URL$endpoint"
    
    if [ -n "$headers" ]; then
        eval "curl -s -X $method $headers '$BASE_URL$endpoint'"
    else
        curl -s -X "$method" "$BASE_URL$endpoint"
    fi
}

# 检查API连通性
check_api_connectivity() {
    log_info "检查API连通性..."
    
    response=$(call_api "/actuator/health" 2>/dev/null)
    if [ $? -eq 0 ] && echo "$response" | grep -q "UP"; then
        log_success "API连通性正常"
        return 0
    else
        log_error "API连通性异常，请检查服务状态和网络连接"
        return 1
    fi
}

# 获取task 9的详细信息
get_task_9_details() {
    log_info "获取task 9的详细信息..."
    
    response=$(call_api "/api/pmi-scheduled-tasks/9")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "成功获取task 9信息"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "获取task 9信息失败"
        echo "$response"
    fi
}

# 获取所有PMI任务状态
get_all_pmi_tasks() {
    log_info "获取所有PMI任务状态..."
    
    response=$(call_api "/api/pmi-scheduled-tasks?page=1&size=50")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "成功获取PMI任务列表"
        
        # 提取task 9相关信息
        echo "$response" | jq '.data.items[] | select(.id == 9)' 2>/dev/null || {
            log_warn "未找到task 9，显示所有任务："
            echo "$response" | jq '.data.items[] | {id: .id, status: .status, taskType: .taskType, scheduledTime: .scheduledTime}' 2>/dev/null || echo "$response"
        }
    else
        log_error "获取PMI任务列表失败"
        echo "$response"
    fi
}

# 获取任务执行记录
get_task_execution_records() {
    log_info "获取任务执行记录..."
    
    response=$(call_api "/api/scheduled-tasks/records?page=0&size=20")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "成功获取任务执行记录"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "获取任务执行记录失败"
        echo "$response"
    fi
}

# 获取调度器监控信息
get_scheduler_monitor() {
    log_info "获取调度器监控信息..."
    
    response=$(call_api "/api/scheduler-monitor/overview")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "成功获取调度器监控信息"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "获取调度器监控信息失败"
        echo "$response"
    fi
}

# 获取PMI任务统计
get_pmi_task_statistics() {
    log_info "获取PMI任务统计..."
    
    response=$(call_api "/api/pmi-scheduled-tasks/statistics")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "成功获取PMI任务统计"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "获取PMI任务统计失败"
        echo "$response"
    fi
}

# 检查系统健康状态
check_system_health() {
    log_info "检查系统健康状态..."
    
    response=$(call_api "/api/scheduler-monitor/health")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "系统健康状态正常"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        log_error "系统健康状态异常"
        echo "$response"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "生产环境Task 9诊断工具"
    echo "========================================"
    echo "Base URL: $BASE_URL"
    echo "Auth Token: ${AUTH_TOKEN:+已设置}"
    echo "========================================"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl命令未找到，请安装curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warn "jq命令未找到，JSON输出将不会格式化"
    fi
    
    # 执行诊断步骤
    echo
    log_info "开始诊断..."
    echo
    
    # 1. 检查API连通性
    if ! check_api_connectivity; then
        exit 1
    fi
    echo
    
    # 2. 获取task 9详细信息
    get_task_9_details
    echo
    
    # 3. 获取所有PMI任务状态
    get_all_pmi_tasks
    echo
    
    # 4. 获取任务执行记录
    get_task_execution_records
    echo
    
    # 5. 获取调度器监控信息
    get_scheduler_monitor
    echo
    
    # 6. 获取PMI任务统计
    get_pmi_task_statistics
    echo
    
    # 7. 检查系统健康状态
    check_system_health
    echo
    
    log_success "诊断完成！"
    echo
    echo "========================================"
    echo "诊断建议："
    echo "1. 检查task 9的状态是否为SCHEDULED"
    echo "2. 检查scheduledTime是否已过期"
    echo "3. 检查是否有错误信息"
    echo "4. 检查调度器是否正常运行"
    echo "5. 检查系统资源和网络连接"
    echo "========================================"
}

# 执行主函数
main "$@"
