#!/bin/bash

# Java后端快速编译脚本
# 用于快速编译代码，依赖Spring Boot DevTools的热重载

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 设置Java 11环境
export JAVA_HOME="/Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home"
export PATH="$JAVA_HOME/bin:$PATH"

echo -e "${BLUE}⚡ Java后端快速编译脚本${NC}"

# 函数：打印带时间戳的日志
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

# 函数：快速编译
quick_compile() {
    log "🔨 开始快速编译..."
    
    # 只编译，不运行测试
    if ./mvnw compile -DskipTests -q; then
        log "✅ 快速编译成功"
        
        # 检查应用是否运行中
        if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log "🔄 应用运行中，DevTools将自动重载"
        else
            log "⚠️ 应用未运行，请使用 scripts/dev-restart.sh 启动"
        fi
        
        return 0
    else
        error "❌ 编译失败"
        return 1
    fi
}

# 主流程
main() {
    quick_compile
}

# 执行主流程
main "$@"
