#!/bin/bash

# Java后端文件监控编译脚本
# 监控Java文件变化，自动触发编译

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}👀 Java后端文件监控编译脚本${NC}"
echo -e "${BLUE}监控目录: src/main/java${NC}"

# 函数：打印带时间戳的日志
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN:${NC} $1"
}

# 检查fswatch是否安装
check_fswatch() {
    if ! command -v fswatch &> /dev/null; then
        error "fswatch 未安装"
        echo "请安装 fswatch:"
        echo "  macOS: brew install fswatch"
        echo "  Ubuntu: sudo apt-get install fswatch"
        echo "  CentOS: sudo yum install fswatch"
        exit 1
    fi
}

# 编译函数
compile_on_change() {
    local changed_file="$1"
    log "📁 检测到文件变化: $changed_file"
    
    # 防抖：等待100ms，避免频繁编译
    sleep 0.1
    
    log "🔨 开始编译..."
    if ./mvnw compile -DskipTests -q; then
        log "✅ 编译成功"
        
        # 检查应用状态
        if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log "🔄 DevTools将自动重载应用"
        else
            warn "⚠️ 应用未运行"
        fi
    else
        error "❌ 编译失败"
    fi
    
    echo "----------------------------------------"
}

# 主函数
main() {
    check_fswatch
    
    log "🚀 开始监控Java文件变化..."
    log "按 Ctrl+C 停止监控"
    
    # 监控src/main/java目录下的.java文件
    fswatch -o src/main/java --include=".*\.java$" | while read num; do
        compile_on_change "Java源文件"
    done
}

# 信号处理
trap 'log "👋 停止文件监控"; exit 0' INT TERM

# 执行主流程
main "$@"
