#!/bin/bash

# ZoomBus 服务器端控制脚本
# 用于在目标服务器上管理ZoomBus服务

BACKEND_DIR="/root/zoombus"
JAR_NAME="zoombus-1.0.0.jar"
PID_FILE="$BACKEND_DIR/zoombus.pid"
LOG_FILE="$BACKEND_DIR/zoombus.log"

# Java 11配置
JAVA11_HOME="/usr/lib/jvm/java-11-openjdk"
if [ -d "$JAVA11_HOME" ]; then
    JAVA_CMD="$JAVA11_HOME/bin/java"
else
    # 尝试查找Java 11安装路径
    JAVA11_PATH=$(find /usr/lib/jvm -name "java-11-openjdk*" -type d 2>/dev/null | head -1)
    if [ -n "$JAVA11_PATH" ]; then
        JAVA_CMD="$JAVA11_PATH/bin/java"
    else
        log_warning "未找到Java 11，使用系统默认Java"
        JAVA_CMD="java"
    fi
fi

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            log_success "ZoomBus 服务正在运行 (PID: $PID)"
            return 0
        else
            log_warning "PID文件存在但进程不存在，清理PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 检查是否有其他ZoomBus进程
    PID=$(pgrep -f "$JAR_NAME" | head -1)
    if [ -n "$PID" ]; then
        log_warning "发现ZoomBus进程但无PID文件 (PID: $PID)"
        echo $PID > "$PID_FILE"
        return 0
    fi
    
    log_info "ZoomBus 服务未运行"
    return 1
}

# 启动服务
start() {
    log_info "启动ZoomBus服务..."
    
    if status > /dev/null 2>&1; then
        log_warning "服务已在运行"
        return 1
    fi
    
    if [ ! -f "$BACKEND_DIR/$JAR_NAME" ]; then
        log_error "未找到jar文件: $BACKEND_DIR/$JAR_NAME"
        return 1
    fi
    
    cd "$BACKEND_DIR"
    log_info "使用Java: $JAVA_CMD"
    nohup "$JAVA_CMD" -jar "$JAR_NAME" > "$LOG_FILE" 2>&1 &
    PID=$!
    echo $PID > "$PID_FILE"
    
    sleep 3
    
    if ps -p $PID > /dev/null 2>&1; then
        log_success "ZoomBus 服务启动成功 (PID: $PID)"
    else
        log_error "ZoomBus 服务启动失败"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务
stop() {
    log_info "停止ZoomBus服务..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            kill $PID
            sleep 5
            
            if ps -p $PID > /dev/null 2>&1; then
                log_warning "正常停止失败，强制终止进程"
                kill -9 $PID
            fi
            
            rm -f "$PID_FILE"
            log_success "ZoomBus 服务已停止"
        else
            log_warning "PID文件存在但进程不存在"
            rm -f "$PID_FILE"
        fi
    else
        # 查找并停止所有ZoomBus进程
        PIDS=$(pgrep -f "$JAR_NAME")
        if [ -n "$PIDS" ]; then
            log_info "发现ZoomBus进程，正在停止..."
            pkill -f "$JAR_NAME"
            sleep 3
            
            # 检查是否还有进程
            PIDS=$(pgrep -f "$JAR_NAME")
            if [ -n "$PIDS" ]; then
                log_warning "强制终止残留进程"
                pkill -9 -f "$JAR_NAME"
            fi
            log_success "ZoomBus 服务已停止"
        else
            log_info "ZoomBus 服务未运行"
        fi
    fi
}

# 重启服务
restart() {
    log_info "重启ZoomBus服务..."
    stop
    sleep 2
    start
}

# 查看日志
logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        log_error "日志文件不存在: $LOG_FILE"
    fi
}

# 显示帮助
help() {
    echo "ZoomBus 服务控制脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|help}"
    echo ""
    echo "命令:"
    echo "  start   - 启动ZoomBus服务"
    echo "  stop    - 停止ZoomBus服务"
    echo "  restart - 重启ZoomBus服务"
    echo "  status  - 查看服务状态"
    echo "  logs    - 查看实时日志"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "文件位置:"
    echo "  JAR文件: $BACKEND_DIR/$JAR_NAME"
    echo "  PID文件: $PID_FILE"
    echo "  日志文件: $LOG_FILE"
}

# 主逻辑
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    help|--help|-h)
        help
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|help}"
        exit 1
        ;;
esac
