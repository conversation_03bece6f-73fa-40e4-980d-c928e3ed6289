#!/bin/bash

# 设置ngrok证书信任
# 将服务器证书添加到macOS钥匙串中

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}===========================================${NC}"
echo -e "${BLUE}    设置ngrok证书信任${NC}"
echo -e "${BLUE}===========================================${NC}"

# 检查证书文件是否存在
if [ ! -f "server_san.crt" ]; then
    echo -e "${RED}错误: 证书文件不存在: server_san.crt${NC}"
    exit 1
fi

echo -e "${YELLOW}将服务器证书添加到系统钥匙串...${NC}"

# 添加证书到系统钥匙串
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain server_san.crt

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 证书已成功添加到系统钥匙串${NC}"
    echo -e "${GREEN}现在可以使用 ./start-ngrok-simple.sh 启动ngrok${NC}"
else
    echo -e "${RED}✗ 添加证书失败${NC}"
    echo -e "${YELLOW}请手动运行以下命令:${NC}"
    echo -e "${BLUE}sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain server_san.crt${NC}"
fi

echo -e "${BLUE}===========================================${NC}"
echo -e "${YELLOW}注意: 这将信任自签名证书，仅用于测试环境${NC}"
echo -e "${BLUE}===========================================${NC}"
