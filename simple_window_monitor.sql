-- 简化的窗口监控脚本
-- 2025-08-18

-- 1. 创建监控表来记录窗口关闭操作
CREATE TABLE IF NOT EXISTS t_window_close_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    window_id BIGINT NOT NULL,
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型：CLOSE_ATTEMPT, CLOSE_SUCCESS, CLOSE_SKIP, CLOSE_ERROR',
    window_date DATE,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    status_before VARCHAR(50),
    status_after VARCHAR(50),
    current_date_param DATE NOT NULL,
    current_time_param TIME NOT NULL,
    condition1_match BOOLEAN,
    condition2_match BOOLEAN,
    condition3_match BOOLEAN,
    should_close BOOLEAN,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_window_id (window_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_at (created_at)
) COMMENT '窗口关闭操作日志表';

-- 2. 查询当前可能有问题的窗口
SELECT 
    '=== 当前状态分析 ===' as analysis_type,
    '' as details
UNION ALL
SELECT 
    '已完成但end_date是未来的窗口数量',
    CAST(COUNT(*) AS CHAR)
FROM t_pmi_schedule_windows 
WHERE status = 'COMPLETED' 
AND end_date > CURDATE()
UNION ALL
SELECT 
    '活跃状态的窗口数量',
    CAST(COUNT(*) AS CHAR)
FROM t_pmi_schedule_windows 
WHERE status = 'ACTIVE'
UNION ALL
SELECT 
    '今天应该关闭的活跃窗口数量',
    CAST(COUNT(*) AS CHAR)
FROM t_pmi_schedule_windows w
WHERE w.status = 'ACTIVE' 
AND ((w.end_date IS NULL AND CURDATE() = w.window_date AND CURTIME() >= w.end_time) OR 
     (w.end_date IS NOT NULL AND CURDATE() > w.end_date) OR 
     (w.end_date IS NOT NULL AND CURDATE() = w.end_date AND CURTIME() >= w.end_time));

-- 3. 显示最近被错误关闭的窗口详情
SELECT 
    '=== 最近被错误关闭的窗口 ===' as title,
    '' as id,
    '' as window_date,
    '' as end_date,
    '' as actual_end_time,
    '' as should_close_analysis
UNION ALL
SELECT 
    '窗口详情',
    CAST(w.id AS CHAR),
    CAST(w.window_date AS CHAR),
    CAST(w.end_date AS CHAR),
    CAST(w.actual_end_time AS CHAR),
    CASE 
        WHEN w.end_date IS NULL THEN 'NULL_END_DATE'
        WHEN w.actual_end_time IS NULL THEN 'NO_CLOSE_TIME'
        WHEN DATE(w.actual_end_time) > w.end_date THEN 'CLOSED_AFTER_END_DATE'
        WHEN DATE(w.actual_end_time) < w.end_date THEN 'CLOSED_BEFORE_END_DATE'
        WHEN DATE(w.actual_end_time) = w.end_date THEN 'CLOSED_ON_END_DATE'
        ELSE 'UNKNOWN'
    END
FROM t_pmi_schedule_windows w
WHERE w.status = 'COMPLETED'
AND w.end_date > CURDATE()
AND w.actual_end_time IS NOT NULL
ORDER BY w.actual_end_time DESC
LIMIT 10;

-- 4. 创建一个简单的验证查询
SELECT 
    '=== 验证查询示例 ===' as info
UNION ALL
SELECT 
    '使用以下查询验证特定窗口的关闭逻辑：' as info
UNION ALL
SELECT 
    'SELECT id, window_date, end_date, start_time, end_time, status,' as info
UNION ALL
SELECT 
    '  (end_date IS NULL AND ''2025-08-15'' = window_date AND ''01:00:00'' >= end_time) as condition1,' as info
UNION ALL
SELECT 
    '  (end_date IS NOT NULL AND ''2025-08-15'' > end_date) as condition2,' as info
UNION ALL
SELECT 
    '  (end_date IS NOT NULL AND ''2025-08-15'' = end_date AND ''01:00:00'' >= end_time) as condition3' as info
UNION ALL
SELECT 
    'FROM t_pmi_schedule_windows WHERE id = YOUR_WINDOW_ID;' as info;
