package com.zoombus.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 内容缓存过滤器
 * 为Webhook请求包装ContentCachingRequestWrapper，支持多次读取请求体
 */
@Component
@Order(1) // 确保在其他过滤器之前执行
@Slf4j
public class ContentCachingFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  Filter<PERSON>hain filterChain) throws ServletException, IOException {
        
        // 只对Webhook请求进行包装
        if (isWebhookRequest(request)) {
            // 包装请求以支持多次读取
            ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
            log.debug("为Webhook请求包装ContentCachingRequestWrapper: {}", request.getRequestURI());
            filterChain.doFilter(wrappedRequest, response);
        } else {
            // 非Webhook请求直接传递
            filterChain.doFilter(request, response);
        }
    }
    
    /**
     * 判断是否为Webhook请求
     */
    private boolean isWebhookRequest(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return uri != null && (uri.startsWith("/api/webhooks/") || uri.startsWith("/api/webhook/"));
    }
}
