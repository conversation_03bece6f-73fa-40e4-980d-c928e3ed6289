package com.zoombus.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * 数据库优化配置
 * 提供数据库查询和事务优化配置
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = "com.zoombus.repository")
public class DatabaseOptimizationConfig {

    /**
     * 配置事务管理器
     */
    @Bean
    public PlatformTransactionManager transactionManager(EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory);
        
        // 设置事务超时时间（秒）
        transactionManager.setDefaultTimeout(300); // 5分钟
        
        return transactionManager;
    }

    /**
     * 数据库连接池优化配置
     */
    @Configuration
    @ConditionalOnProperty(name = "zoombus.database.optimization.enabled", havingValue = "true", matchIfMissing = true)
    public static class ConnectionPoolOptimization {
        
        // 这里可以添加连接池优化配置
        // 例如 HikariCP 的优化设置
    }

    /**
     * JPA 查询优化配置
     */
    @Configuration
    @ConditionalOnProperty(name = "zoombus.database.jpa-optimization.enabled", havingValue = "true", matchIfMissing = true)
    public static class JpaOptimization {
        
        // 这里可以添加 JPA 查询优化配置
        // 例如批量操作、懒加载等设置
    }
}
