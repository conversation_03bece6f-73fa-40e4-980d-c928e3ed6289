package com.zoombus.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 立即执行配置类
 * 控制PMI窗口任务的立即执行行为
 */
@ConfigurationProperties(prefix = "pmi.task.scheduling.immediate-execution")
@Data
public class ImmediateExecutionConfig {
    
    /**
     * 是否启用立即执行机制
     * 默认启用
     */
    private boolean enabled = true;
    
    /**
     * 时间容差（分钟）
     * 在此范围内的任务将被视为"当前时间"并立即执行
     * 默认2分钟
     */
    private int toleranceMinutes = 2;
    
    /**
     * 最大延迟执行时间（分钟）
     * 超过此时间的过往任务将被标记为过期
     * 默认60分钟
     */
    private int maxDelayMinutes = 60;
    
    /**
     * 是否允许执行过往时间的任务
     * 默认允许
     */
    private boolean enablePastExecution = true;
    
    /**
     * 立即执行的线程池配置
     */
    private ThreadPool threadPool = new ThreadPool();
    
    /**
     * 监控配置
     */
    private Monitoring monitoring = new Monitoring();
    
    /**
     * 线程池配置
     */
    @Data
    public static class ThreadPool {
        /**
         * 核心线程数
         */
        private int coreSize = 5;
        
        /**
         * 最大线程数
         */
        private int maxSize = 10;
        
        /**
         * 队列容量
         */
        private int queueCapacity = 100;
        
        /**
         * 线程名前缀
         */
        private String threadNamePrefix = "immediate-task-";
        
        /**
         * 线程空闲时间（秒）
         */
        private int keepAliveSeconds = 60;
        
        /**
         * 是否允许核心线程超时
         */
        private boolean allowCoreThreadTimeOut = false;
    }
    
    /**
     * 监控配置
     */
    @Data
    public static class Monitoring {
        /**
         * 是否启用监控
         */
        private boolean enabled = true;
        
        /**
         * 指标收集间隔（秒）
         */
        private int metricsIntervalSeconds = 60;
        
        /**
         * 是否记录执行详情
         */
        private boolean recordExecutionDetails = true;
        
        /**
         * 告警配置
         */
        private Alert alert = new Alert();
    }
    
    /**
     * 告警配置
     */
    @Data
    public static class Alert {
        /**
         * 是否启用告警
         */
        private boolean enabled = true;
        
        /**
         * 失败率阈值（百分比）
         */
        private double failureRateThreshold = 5.0;
        
        /**
         * 过期任务数量阈值（每小时）
         */
        private int expiredTaskThreshold = 10;
        
        /**
         * 平均执行时间阈值（秒）
         */
        private int avgExecutionTimeThreshold = 5;
        
        /**
         * 队列满阈值（百分比）
         */
        private double queueFullThreshold = 80.0;
    }
    
    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (toleranceMinutes < 0) {
            throw new IllegalArgumentException("toleranceMinutes must be non-negative");
        }
        
        if (maxDelayMinutes < 0) {
            throw new IllegalArgumentException("maxDelayMinutes must be non-negative");
        }
        
        if (toleranceMinutes > maxDelayMinutes) {
            throw new IllegalArgumentException("toleranceMinutes cannot be greater than maxDelayMinutes");
        }
        
        if (threadPool.coreSize <= 0) {
            throw new IllegalArgumentException("threadPool.coreSize must be positive");
        }
        
        if (threadPool.maxSize < threadPool.coreSize) {
            throw new IllegalArgumentException("threadPool.maxSize cannot be less than coreSize");
        }
        
        if (threadPool.queueCapacity < 0) {
            throw new IllegalArgumentException("threadPool.queueCapacity must be non-negative");
        }
    }
    
    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format(
            "ImmediateExecutionConfig{enabled=%s, tolerance=%dmin, maxDelay=%dmin, " +
            "pastExecution=%s, threadPool=%d-%d, queue=%d}",
            enabled, toleranceMinutes, maxDelayMinutes, enablePastExecution,
            threadPool.coreSize, threadPool.maxSize, threadPool.queueCapacity
        );
    }
}
