package com.zoombus.config;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableEncryptableProperties
public class JasyptConfig {

    @Bean("jasyptStringEncryptor")
    public StringEncryptor stringEncryptor() {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();

        // 设置加密密钥，建议从环境变量获取
        config.setPassword(getEncryptorPassword());
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setStringOutputType("base64");

        encryptor.setConfig(config);
        return encryptor;
    }
    
    private String getEncryptorPassword() {
        // 优先从环境变量获取加密密钥，如果没有则使用默认值
        // 生产环境中应该通过环境变量 JASYPT_ENCRYPTOR_PASSWORD 设置
        return System.getenv("JASYPT_ENCRYPTOR_PASSWORD") != null 
            ? System.getenv("JASYPT_ENCRYPTOR_PASSWORD") 
            : "zoombus-secret-key-2024";
    }
}
