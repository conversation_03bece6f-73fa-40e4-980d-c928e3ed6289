package com.zoombus.config;

import com.zoombus.service.NetworkEnvironmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * 网络环境配置
 * 在应用启动时自动检测网络环境并配置代理
 */
@Configuration
@ConfigurationProperties(prefix = "zoombus.network")
@RequiredArgsConstructor
@Slf4j
@Order(1) // 确保在其他组件之前执行
public class NetworkEnvironmentConfig implements ApplicationRunner {

    private final NetworkEnvironmentService networkEnvironmentService;

    // 配置属性
    private boolean autoDetectEnabled = true;
    private boolean proxyEnabled = true;
    private String targetExternalIp = "**************";
    private String proxyHost = "127.0.0.1";
    private String proxyPort = "6690";

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("🚀 启动网络环境检测和配置...");
        
        if (!autoDetectEnabled) {
            log.info("ℹ️ 网络环境自动检测已禁用");
            return;
        }

        try {
            // 检测外网IP
            String externalIp = networkEnvironmentService.detectExternalIp();
            
            if (externalIp == null) {
                log.warn("⚠️ 无法检测外网IP，跳过代理配置");
                return;
            }

            // 配置代理（如果需要）
            if (proxyEnabled) {
                networkEnvironmentService.configureZoomApiProxy();
                
                // 测试Zoom API连接
                testZoomApiConnection();
            } else {
                log.info("ℹ️ 代理功能已禁用");
            }

            // 输出环境信息
            logEnvironmentInfo();

        } catch (Exception e) {
            log.error("❌ 网络环境配置失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 测试Zoom API连接
     */
    private void testZoomApiConnection() {
        try {
            log.info("🔗 测试Zoom API连接...");
            
            // 测试Zoom API基础连接
            boolean zoomApiConnected = networkEnvironmentService.testNetworkConnection("https://api.zoom.us");
            
            if (zoomApiConnected) {
                log.info("✅ Zoom API连接测试成功");
            } else {
                log.warn("⚠️ Zoom API连接测试失败，请检查网络配置");
            }

            // 测试其他重要服务
            boolean internetConnected = networkEnvironmentService.testNetworkConnection("https://www.google.com");
            log.info("🌐 互联网连接测试: {}", internetConnected ? "成功" : "失败");

        } catch (Exception e) {
            log.warn("网络连接测试异常: {}", e.getMessage());
        }
    }

    /**
     * 输出环境信息
     */
    private void logEnvironmentInfo() {
        log.info("📊 网络环境信息:");
        log.info("  - 当前外网IP: {}", networkEnvironmentService.getCurrentExternalIp());
        log.info("  - 目标环境IP: {}", targetExternalIp);
        log.info("  - 代理状态: {}", networkEnvironmentService.getProxyInfo());
        log.info("  - 自动检测: {}", autoDetectEnabled ? "启用" : "禁用");
        log.info("  - 代理功能: {}", proxyEnabled ? "启用" : "禁用");

        // 输出系统代理属性
        String httpProxyHost = System.getProperty("http.proxyHost");
        String httpsProxyHost = System.getProperty("https.proxyHost");
        if (httpProxyHost != null || httpsProxyHost != null) {
            log.info("  - 系统代理配置:");
            if (httpProxyHost != null) {
                log.info("    HTTP: {}:{}", httpProxyHost, System.getProperty("http.proxyPort"));
            }
            if (httpsProxyHost != null) {
                log.info("    HTTPS: {}:{}", httpsProxyHost, System.getProperty("https.proxyPort"));
            }
            String nonProxyHosts = System.getProperty("http.nonProxyHosts");
            if (nonProxyHosts != null) {
                log.info("    不使用代理: {}", nonProxyHosts);
            }
        }
    }

    // Getter和Setter方法用于配置属性绑定
    public boolean isAutoDetectEnabled() {
        return autoDetectEnabled;
    }

    public void setAutoDetectEnabled(boolean autoDetectEnabled) {
        this.autoDetectEnabled = autoDetectEnabled;
    }

    public boolean isProxyEnabled() {
        return proxyEnabled;
    }

    public void setProxyEnabled(boolean proxyEnabled) {
        this.proxyEnabled = proxyEnabled;
    }

    public String getTargetExternalIp() {
        return targetExternalIp;
    }

    public void setTargetExternalIp(String targetExternalIp) {
        this.targetExternalIp = targetExternalIp;
    }

    public String getProxyHost() {
        return proxyHost;
    }

    public void setProxyHost(String proxyHost) {
        this.proxyHost = proxyHost;
    }

    public String getProxyPort() {
        return proxyPort;
    }

    public void setProxyPort(String proxyPort) {
        this.proxyPort = proxyPort;
    }
}
