package com.zoombus.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * PMI任务调度配置
 */
@Configuration
@ConfigurationProperties(prefix = "pmi.task.scheduling")
@Data
public class PmiTaskSchedulingConfig {
    
    /**
     * 是否启用新的精准定时任务机制
     * true: 使用新的事件驱动精准调度机制
     * false: 使用原有的轮询机制
     */
    private boolean enablePreciseScheduling = false;
    
    /**
     * 是否启用任务监控
     */
    private boolean enableTaskMonitoring = true;
    
    /**
     * 任务清理配置
     */
    private TaskCleanup cleanup = new TaskCleanup();
    
    /**
     * 任务重试配置
     */
    private TaskRetry retry = new TaskRetry();
    
    /**
     * 性能配置
     */
    private Performance performance = new Performance();
    
    @Data
    public static class TaskCleanup {
        /**
         * 是否启用自动清理
         */
        private boolean enabled = true;
        
        /**
         * 保留已完成任务的天数
         */
        private int retentionDays = 30;
        
        /**
         * 清理任务执行时间（cron表达式）
         */
        private String cronExpression = "0 0 2 * * ?"; // 每天凌晨2点执行
    }
    
    @Data
    public static class TaskRetry {
        /**
         * 最大重试次数
         */
        private int maxRetryCount = 3;
        
        /**
         * 重试间隔（分钟）
         */
        private int retryIntervalMinutes = 5;
        
        /**
         * 是否启用指数退避
         */
        private boolean exponentialBackoff = true;
    }
    
    @Data
    public static class Performance {
        /**
         * 批量处理大小
         */
        private int batchSize = 100;
        
        /**
         * 线程池大小
         */
        private int threadPoolSize = 10;
        
        /**
         * 任务执行超时时间（分钟）
         */
        private int taskTimeoutMinutes = 30;
        
        /**
         * 是否启用任务缓存
         */
        private boolean enableTaskCache = true;
        
        /**
         * 缓存过期时间（分钟）
         */
        private int cacheExpirationMinutes = 10;
    }
}
