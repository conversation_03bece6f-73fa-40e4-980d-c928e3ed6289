package com.zoombus.config;

import com.zoombus.security.JwtAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {
    
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeRequests()
                // 允许登录接口无需认证
                .antMatchers("/api/auth/**").permitAll()
                // 允许H2控制台访问（仅开发环境）
                .antMatchers("/h2-console/**").permitAll()
                // 允许健康检查
                .antMatchers("/actuator/health").permitAll()
                // 允许Webhook接口（Zoom回调）
                .antMatchers("/api/webhooks/**").permitAll()
                .antMatchers("/api/webhook/**").permitAll()
                // 允许公共PMI接口（无需登录）
                .antMatchers("/api/public/**").permitAll()
                // Zoom认证管理接口临时允许所有用户访问（用于测试）
                .antMatchers("/api/zoom-auth/**").permitAll()
                // 数据库迁移接口临时允许访问（用于修复）
                .antMatchers("/api/migration/**").permitAll()
                // 数据迁移接口允许访问（用于数据迁移）
                .antMatchers("/api/data-migration/**").permitAll()
                // PMI报告接口允许访问（用于PMI统计）
                .antMatchers("/api/pmi-reports/**").permitAll()
                // 版本管理接口允许访问
                .antMatchers("/api/version/**").permitAll()
                // Zoom会议管理接口临时允许访问（用于测试）
                .antMatchers("/api/zoom-meetings/**").permitAll()
                // 会议邀请信息接口允许访问（用于复制功能）
                .antMatchers("/api/meetings/**/invitation").permitAll()
                // 会议UUID接口允许访问（用于会议主持人页面）
                .antMatchers("/api/meetings/uuid/**").permitAll()
                // 会议详情接口允许访问（用于会议主持人页面的场次信息）
                .antMatchers("/api/meetings/*/zoom-details").permitAll()
                // 主持人链接接口允许访问（用于会议主持人页面）
                .antMatchers("/api/meetings/uuid/*/host-url").permitAll()
                // 定时任务监控接口允许访问
                .antMatchers("/api/scheduler-monitor/**").permitAll()
                // 事务监控接口允许访问
                .antMatchers("/api/transaction-monitor/**").permitAll()
                // 会议报告接口允许访问（用于前端展示）
                .antMatchers("/api/meeting-reports/**").permitAll()
                // 测试端点允许访问（用于测试session过期处理）
                .antMatchers("/api/admin/test-**").permitAll()
                // PMI计划管理接口允许认证用户访问
                .antMatchers("/api/pmi-schedules/**").authenticated()
                // 其他所有API需要认证
                .antMatchers("/api/**").authenticated()
                // 静态资源允许访问
                .anyRequest().permitAll()
            .and()
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        
        // 允许H2控制台的frame
        http.headers().frameOptions().disable();
        
        return http.build();
    }
}
