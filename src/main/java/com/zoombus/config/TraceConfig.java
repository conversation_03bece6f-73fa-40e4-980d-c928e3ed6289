package com.zoombus.config;

import com.zoombus.trace.TraceIdInterceptor;
import com.zoombus.trace.TraceIdTaskDecorator;
import com.zoombus.trace.WebhookTraceInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.Executor;

/**
 * TraceId配置类
 * 配置TraceId相关的拦截器、异步任务执行器等
 */
@Configuration
@EnableAsync
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "app.trace.enabled", havingValue = "true", matchIfMissing = true)
public class TraceConfig implements WebMvcConfigurer {

    private final TraceIdInterceptor traceIdInterceptor;
    private final WebhookTraceInterceptor webhookTraceInterceptor;
    
    /**
     * 注册TraceId拦截器
     */
    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        // 注册通用TraceId拦截器
        registry.addInterceptor(traceIdInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/static/**",
                    "/css/**",
                    "/js/**",
                    "/images/**",
                    "/favicon.ico",
                    "/actuator/**",
                    "/api/webhooks/**"  // Webhook请求由专用拦截器处理
                );

        // 注册Webhook专用拦截器
        registry.addInterceptor(webhookTraceInterceptor)
                .addPathPatterns("/api/webhooks/**");

        log.info("TraceId拦截器已注册");
        log.info("Webhook TraceId拦截器已注册");
    }
    
    /**
     * 配置支持TraceId传递的异步任务执行器
     */
    @Bean(name = "traceTaskExecutor")
    public Executor traceTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("trace-async-");
        executor.setTaskDecorator(new TraceIdTaskDecorator());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        
        log.info("TraceId异步任务执行器已配置");
        return executor;
    }
    
    /**
     * 默认异步任务执行器，支持TraceId传递
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        return traceTaskExecutor();
    }
}
