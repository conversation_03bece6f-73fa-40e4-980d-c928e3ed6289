package com.zoombus.config;

import com.zoombus.service.NetworkEnvironmentService;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.PathResourceResolver;
import reactor.netty.http.client.HttpClient;

import java.io.IOException;
import java.time.Duration;

import java.util.concurrent.TimeUnit;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class WebClientConfig implements WebMvcConfigurer {

    private final NetworkEnvironmentService networkEnvironmentService;

    @Bean
    public WebClient.Builder webClientBuilder() {
        // 注意：代理配置现在由NetworkEnvironmentService在启动时处理
        // 这里保留Zoom域名的绕过配置，确保在需要代理的环境中Zoom API仍能正常访问
        configureZoomDomainBypass();

        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000) // 30秒连接超时
                .responseTimeout(Duration.ofSeconds(30)) // 30秒响应超时
                .doOnConnected(conn ->
                    conn.addHandlerLast(new ReadTimeoutHandler(30, TimeUnit.SECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(30, TimeUnit.SECONDS)))
                .doOnRequest((request, connection) ->
                    log.debug("WebClient请求: {} {}", request.method(), request.uri()))
                .doOnResponse((response, connection) ->
                    log.debug("WebClient响应: {} {}", response.status(), response.uri()));

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> {
                    configurer.defaultCodecs().maxInMemorySize(1024 * 1024); // 1MB
                });
    }

    /**
     * 配置Zoom域名绕过代理
     * 确保在需要代理的环境中，Zoom API仍能正常访问
     */
    private void configureZoomDomainBypass() {
        try {
            // 获取当前的nonProxyHosts配置
            String currentNonProxyHosts = System.getProperty("http.nonProxyHosts", "");

            // Zoom相关域名
            String zoomDomains = "*.zoom.us|zoom.us|api.zoom.us";

            // 如果还没有配置Zoom域名绕过，则添加
            if (!currentNonProxyHosts.contains("zoom.us")) {
                String newNonProxyHosts = currentNonProxyHosts.isEmpty() ?
                    zoomDomains : currentNonProxyHosts + "|" + zoomDomains;

                System.setProperty("http.nonProxyHosts", newNonProxyHosts);
                System.setProperty("https.nonProxyHosts", newNonProxyHosts);

                log.info("🔧 配置Zoom域名绕过代理: {}", newNonProxyHosts);
            } else {
                log.debug("Zoom域名绕过代理已配置");
            }

        } catch (Exception e) {
            log.warn("配置Zoom域名绕过代理失败: {}", e.getMessage());
        }
    }



    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 用户前端静态资源
        registry.addResourceHandler("/m/static/**")
                .addResourceLocations("classpath:/static-user/static/");

        registry.addResourceHandler("/m/assets/**")
                .addResourceLocations("classpath:/static-user/assets/");

        // 管理端静态资源 - 处理SPA路由
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/")
                .resourceChain(true)
                .addResolver(new PathResourceResolver() {
                    @Override
                    protected Resource getResource(String resourcePath, Resource location) throws IOException {
                        Resource requestedResource = location.createRelative(resourcePath);

                        // 如果请求的是API路径，不处理
                        if (resourcePath.startsWith("api/")) {
                            return null;
                        }

                        // 如果请求的是actuator路径，不处理
                        if (resourcePath.startsWith("actuator/")) {
                            return null;
                        }

                        // 如果请求的是h2-console路径，不处理
                        if (resourcePath.startsWith("h2-console/")) {
                            return null;
                        }

                        // 如果请求的是用户前端路径，不处理（由UserFrontendController处理）
                        if (resourcePath.startsWith("m/")) {
                            return null;
                        }

                        // 如果资源存在且可读，返回该资源
                        if (requestedResource.exists() && requestedResource.isReadable()) {
                            return requestedResource;
                        }

                        // 否则返回index.html（用于SPA路由）
                        return new ClassPathResource("/static/index.html");
                    }
                });
    }


}
