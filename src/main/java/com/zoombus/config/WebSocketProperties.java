package com.zoombus.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * WebSocket配置属性
 */
@Configuration
@ConfigurationProperties(prefix = "websocket")
@Data
public class WebSocketProperties {
    
    /**
     * WebSocket功能总开关
     */
    private boolean enabled = false;
    
    /**
     * 监控相关配置
     */
    private Monitoring monitoring = new Monitoring();
    
    @Data
    public static class Monitoring {
        /**
         * 监控数据推送开关
         */
        private boolean enabled = false;
        
        /**
         * 各种监控数据收集间隔配置
         */
        private Intervals intervals = new Intervals();
    }
    
    @Data
    public static class Intervals {
        /**
         * Zoom账号状态收集间隔（毫秒）
         */
        private long zoomAccount = 30000;
        
        /**
         * 会议状态收集间隔（毫秒）
         */
        private long meeting = 60000;
        
        /**
         * PMI状态收集间隔（毫秒）
         */
        private long pmi = 120000;
        
        /**
         * 系统性能收集间隔（毫秒）
         */
        private long system = 15000;
        
        /**
         * 综合数据推送间隔（毫秒）
         */
        private long comprehensive = 300000;
        
        /**
         * 告警检查间隔（毫秒）
         */
        private long alertCheck = 60000;
    }
}
