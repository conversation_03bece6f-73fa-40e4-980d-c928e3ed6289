package com.zoombus.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "zoom")
@Data
public class ZoomConfig {
    
    private String apiBaseUrl = "https://api.zoom.us/v2";
    private String accountId;
    private String clientId;
    private String clientSecret;
    private String webhookSecretToken;
    
    // JWT相关配置（如果使用JWT App）
    private String jwtApiKey;
    private String jwtApiSecret;
}
