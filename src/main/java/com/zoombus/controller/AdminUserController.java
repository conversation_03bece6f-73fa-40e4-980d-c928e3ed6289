package com.zoombus.controller;

import com.zoombus.dto.ChangePasswordRequest;
import com.zoombus.dto.CreateAdminUserRequest;
import com.zoombus.dto.UpdateAdminUserRequest;
import com.zoombus.entity.AdminUser;
import com.zoombus.security.AdminUserPrincipal;
import com.zoombus.service.AdminUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/admin-users")
@RequiredArgsConstructor
public class AdminUserController {
    
    private final AdminUserService adminUserService;
    
    /**
     * 创建管理员用户（仅超级管理员可操作）
     */
    @PostMapping
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<AdminUser> createAdminUser(@Valid @RequestBody CreateAdminUserRequest request) {
        AdminUser adminUser = adminUserService.createAdminUser(request);
        return ResponseEntity.ok(adminUser);
    }
    
    /**
     * 获取所有管理员用户（分页）
     */
    @GetMapping
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public ResponseEntity<Page<AdminUser>> getAllAdminUsers(Pageable pageable) {
        Page<AdminUser> adminUsers = adminUserService.getAllAdminUsers(pageable);
        return ResponseEntity.ok(adminUsers);
    }
    
    /**
     * 根据ID获取管理员用户
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public ResponseEntity<AdminUser> getAdminUserById(@PathVariable Long id) {
        return adminUserService.getAdminUserById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据状态获取管理员用户
     */
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public ResponseEntity<List<AdminUser>> getAdminUsersByStatus(@PathVariable AdminUser.AdminStatus status) {
        List<AdminUser> adminUsers = adminUserService.getAdminUsersByStatus(status);
        return ResponseEntity.ok(adminUsers);
    }
    
    /**
     * 根据角色获取管理员用户
     */
    @GetMapping("/role/{role}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<List<AdminUser>> getAdminUsersByRole(@PathVariable AdminUser.AdminRole role) {
        List<AdminUser> adminUsers = adminUserService.getAdminUsersByRole(role);
        return ResponseEntity.ok(adminUsers);
    }
    
    /**
     * 搜索管理员用户
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public ResponseEntity<List<AdminUser>> searchAdminUsers(@RequestParam String keyword) {
        List<AdminUser> adminUsers = adminUserService.searchAdminUsers(keyword);
        return ResponseEntity.ok(adminUsers);
    }
    
    /**
     * 更新管理员用户（仅超级管理员可操作）
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<AdminUser> updateAdminUser(@PathVariable Long id, 
                                                   @Valid @RequestBody UpdateAdminUserRequest request) {
        AdminUser adminUser = adminUserService.updateAdminUser(id, request);
        return ResponseEntity.ok(adminUser);
    }
    
    /**
     * 删除管理员用户（仅超级管理员可操作）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Void> deleteAdminUser(@PathVariable Long id) {
        adminUserService.deleteAdminUser(id);
        return ResponseEntity.ok().build();
    }
    
    /**
     * 修改密码（用户可以修改自己的密码）
     */
    @PutMapping("/{id}/password")
    @PreAuthorize("hasRole('SUPER_ADMIN') or (hasRole('ADMIN') and #id == authentication.principal.id) or (hasRole('OPERATOR') and #id == authentication.principal.id)")
    public ResponseEntity<Void> changePassword(@PathVariable Long id,
                                             @Valid @RequestBody ChangePasswordRequest request,
                                             @AuthenticationPrincipal AdminUserPrincipal currentUser) {
        adminUserService.changePassword(id, request.getOldPassword(), request.getNewPassword());
        return ResponseEntity.ok().build();
    }
    
    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/me")
    public ResponseEntity<AdminUser> getCurrentUser(@AuthenticationPrincipal AdminUserPrincipal currentUser) {
        return adminUserService.getAdminUserById(currentUser.getId())
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
}
