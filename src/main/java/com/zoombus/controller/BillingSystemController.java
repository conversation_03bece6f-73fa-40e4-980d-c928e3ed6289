package com.zoombus.controller;

import com.zoombus.service.BillingSystemInitializationService;
import com.zoombus.service.PmiBillingModeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 计费系统管理控制器
 */
@RestController
@RequestMapping("/api/billing-system")
@RequiredArgsConstructor
@Slf4j
public class BillingSystemController {
    
    private final BillingSystemInitializationService initializationService;
    private final PmiBillingModeService pmiBillingModeService;
    
    /**
     * 获取系统初始化状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getSystemStatus() {
        try {
            BillingSystemInitializationService.InitializationStatus status = 
                initializationService.getInitializationStatus();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", status);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 手动触发系统初始化
     */
    @PostMapping("/initialize")
    public ResponseEntity<Map<String, Object>> manualInitialize() {
        try {
            initializationService.manualInitialize();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "系统初始化完成");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("手动初始化失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "初始化失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 处理过期窗口
     */
    @PostMapping("/process-expired-windows")
    public ResponseEntity<Map<String, Object>> processExpiredWindows() {
        try {
            pmiBillingModeService.processExpiredWindows();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "过期窗口处理完成");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("处理过期窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "处理失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            BillingSystemInitializationService.InitializationStatus status = 
                initializationService.getInitializationStatus();
            
            Map<String, Object> healthData = new HashMap<>();
            healthData.put("healthy", status.isHealthy());
            healthData.put("summary", status.getSummary());
            healthData.put("timestamp", java.time.LocalDateTime.now());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", healthData);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "健康检查失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
