package com.zoombus.controller;

import com.zoombus.service.DataMigrationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据迁移API控制器
 * 提供数据迁移相关的管理接口
 */
@RestController
@RequestMapping("/api/data-migration")
@RequiredArgsConstructor
@Slf4j
public class DataMigrationController {

    private final DataMigrationService dataMigrationService;

    /**
     * 执行完整数据迁移
     */
    @PostMapping("/full")
    public ResponseEntity<Map<String, String>> performFullMigration() {
        log.info("开始执行完整数据迁移");
        
        try {
            dataMigrationService.performFullMigration();
            return ResponseEntity.ok(Map.of(
                "message", "完整数据迁移执行成功",
                "status", "success"
            ));
        } catch (Exception e) {
            log.error("完整数据迁移执行失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "message", "数据迁移执行失败: " + e.getMessage(),
                "status", "error"
            ));
        }
    }

    /**
     * 填充会议报告字段
     */
    @PostMapping("/fill-meeting-report-fields")
    public ResponseEntity<Map<String, String>> fillMeetingReportFields() {
        log.info("开始填充会议报告字段");
        
        try {
            dataMigrationService.fillMeetingReportFields();
            return ResponseEntity.ok(Map.of(
                "message", "会议报告字段填充成功",
                "status", "success"
            ));
        } catch (Exception e) {
            log.error("填充会议报告字段失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "message", "填充会议报告字段失败: " + e.getMessage(),
                "status", "error"
            ));
        }
    }

    /**
     * 初始化PMI统计数据
     */
    @PostMapping("/initialize-pmi-stats")
    public ResponseEntity<Map<String, String>> initializePmiStats() {
        log.info("开始初始化PMI统计数据");
        
        try {
            dataMigrationService.initializePmiStats();
            return ResponseEntity.ok(Map.of(
                "message", "PMI统计数据初始化成功",
                "status", "success"
            ));
        } catch (Exception e) {
            log.error("初始化PMI统计数据失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "message", "初始化PMI统计数据失败: " + e.getMessage(),
                "status", "error"
            ));
        }
    }

    /**
     * 获取数据迁移状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getMigrationStatus() {
        log.info("获取数据迁移状态");
        
        try {
            Map<String, Object> status = dataMigrationService.getMigrationStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("获取数据迁移状态失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
