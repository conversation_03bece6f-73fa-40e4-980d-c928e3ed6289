package com.zoombus.controller;

import com.zoombus.util.DatabaseFixUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据库诊断控制器
 */
@RestController
@RequestMapping("/api/database")
@RequiredArgsConstructor
@Slf4j
public class DatabaseDiagnosticsController {

    private final DatabaseFixUtil databaseFixUtil;

    /**
     * 获取数据库诊断信息
     */
    @GetMapping("/diagnostics")
    public ResponseEntity<Map<String, Object>> getDiagnostics() {
        try {
            Map<String, Object> diagnostics = databaseFixUtil.getDatabaseDiagnostics();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", diagnostics
            ));
        } catch (Exception e) {
            log.error("获取数据库诊断信息失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取诊断信息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 手动触发数据库修复
     */
    @PostMapping("/fix")
    public ResponseEntity<Map<String, Object>> fixDatabase() {
        try {
            databaseFixUtil.checkAndFixPmiTables();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "数据库修复完成"
            ));
        } catch (Exception e) {
            log.error("数据库修复失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "数据库修复失败: " + e.getMessage()
            ));
        }
    }
}
