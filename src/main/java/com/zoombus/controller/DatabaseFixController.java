package com.zoombus.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

/**
 * 数据库修复控制器
 * 用于临时修复数据库表结构问题
 */
@RestController
@RequestMapping("/api/admin/database")
public class DatabaseFixController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 修复t_zoom_meetings表的status字段长度
     */
    @PostMapping("/fix-status-column")
    public ResponseEntity<Map<String, Object>> fixStatusColumn() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 检查当前表结构
            String checkSql = "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH " +
                             "FROM INFORMATION_SCHEMA.COLUMNS " +
                             "WHERE TABLE_NAME = 'T_ZOOM_MEETINGS' AND COLUMN_NAME = 'STATUS'";
            
            List<Map<String, Object>> currentStructure = jdbcTemplate.queryForList(checkSql);
            result.put("currentStructure", currentStructure);
            
            // 2. 执行ALTER TABLE语句
            String alterSql = "ALTER TABLE t_zoom_meetings " +
                            "MODIFY COLUMN status VARCHAR(50) DEFAULT 'WAITING' " +
                            "COMMENT '状态：CREATING, CREATE_FAILED, WAITING, STARTED, ENDED, SETTLING, SETTLED, ERROR, DELETED'";
            
            jdbcTemplate.execute(alterSql);
            result.put("alterExecuted", true);
            result.put("alterSql", alterSql);
            
            // 3. 验证修复结果
            List<Map<String, Object>> newStructure = jdbcTemplate.queryForList(checkSql);
            result.put("newStructure", newStructure);
            
            // 4. 测试插入
            String testInsertSql = "INSERT INTO t_zoom_meetings " +
                                 "(zoom_meeting_uuid, zoom_meeting_id, host_id, topic, status, created_at, updated_at) " +
                                 "VALUES (?, ?, ?, ?, ?, NOW(), NOW())";
            
            String testUuid = "test-fix-" + System.currentTimeMillis();
            jdbcTemplate.update(testInsertSql, 
                testUuid, "999888777", "test-host", "数据库修复测试", "STARTED");
            
            result.put("testInserted", true);
            result.put("testUuid", testUuid);
            
            // 5. 查询测试记录
            String selectSql = "SELECT * FROM t_zoom_meetings WHERE zoom_meeting_uuid = ?";
            List<Map<String, Object>> testRecord = jdbcTemplate.queryForList(selectSql, testUuid);
            result.put("testRecord", testRecord);
            
            result.put("success", true);
            result.put("message", "数据库修复成功");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "数据库修复失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 检查表结构
     */
    @GetMapping("/check-table-structure")
    public ResponseEntity<Map<String, Object>> checkTableStructure() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查t_zoom_meetings表结构
            String sql = "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE, COLUMN_DEFAULT " +
                        "FROM INFORMATION_SCHEMA.COLUMNS " +
                        "WHERE TABLE_NAME = 'T_ZOOM_MEETINGS' " +
                        "ORDER BY ORDINAL_POSITION";
            
            List<Map<String, Object>> columns = jdbcTemplate.queryForList(sql);
            result.put("columns", columns);
            
            // 特别检查status字段
            String statusSql = "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE, COLUMN_DEFAULT " +
                              "FROM INFORMATION_SCHEMA.COLUMNS " +
                              "WHERE TABLE_NAME = 'T_ZOOM_MEETINGS' AND COLUMN_NAME = 'STATUS'";
            
            List<Map<String, Object>> statusColumn = jdbcTemplate.queryForList(statusSql);
            result.put("statusColumn", statusColumn);
            
            // 检查现有记录的status值
            String statusValuesSql = "SELECT DISTINCT status, COUNT(*) as count " +
                                   "FROM t_zoom_meetings " +
                                   "GROUP BY status " +
                                   "ORDER BY count DESC";
            
            List<Map<String, Object>> statusValues = jdbcTemplate.queryForList(statusValuesSql);
            result.put("statusValues", statusValues);
            
            result.put("success", true);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查表结构失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 修复CreationSource枚举值问题
     */
    @PostMapping("/fix-creation-source-enum")
    public ResponseEntity<Map<String, Object>> fixCreationSourceEnum() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 查看当前的creation_source值分布
            String checkSql = "SELECT creation_source, COUNT(*) as count FROM t_meetings GROUP BY creation_source";
            List<Map<String, Object>> currentDistribution = jdbcTemplate.queryForList(checkSql);
            result.put("beforeUpdate", currentDistribution);

            // 2. 将ZOOM_APP_WEBHOOK更新为UNKNOWN
            String updateSql = "UPDATE t_meetings SET creation_source = 'UNKNOWN' WHERE creation_source = 'ZOOM_APP_WEBHOOK'";
            int updatedCount = jdbcTemplate.update(updateSql);
            result.put("updatedCount", updatedCount);

            // 3. 验证更新结果
            List<Map<String, Object>> afterDistribution = jdbcTemplate.queryForList(checkSql);
            result.put("afterUpdate", afterDistribution);

            result.put("success", true);
            result.put("message", "CreationSource枚举值修复完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "修复CreationSource枚举值失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 清理测试数据
     */
    @DeleteMapping("/cleanup-test-data")
    public ResponseEntity<Map<String, Object>> cleanupTestData() {
        Map<String, Object> result = new HashMap<>();

        try {
            String deleteSql = "DELETE FROM t_zoom_meetings WHERE zoom_meeting_uuid LIKE 'test-fix-%'";
            int deletedCount = jdbcTemplate.update(deleteSql);

            result.put("success", true);
            result.put("deletedCount", deletedCount);
            result.put("message", "测试数据清理完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "清理测试数据失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }
}
