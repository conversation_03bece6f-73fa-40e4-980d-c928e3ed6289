package com.zoombus.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.sql.DataSource;
import javax.transaction.Transactional;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/migration")
@RequiredArgsConstructor
@Slf4j
public class DatabaseMigrationController {

    private final EntityManager entityManager;
    private final DataSource dataSource;
    
    /**
     * 修复 scope 字段长度问题
     */
    @PostMapping("/fix-scope-field")
    @Transactional
    public ResponseEntity<Map<String, Object>> fixScopeField() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 修改 scope 字段类型为 TEXT
            String sql = "ALTER TABLE t_zoom_auth MODIFY COLUMN scope TEXT";
            Query query = entityManager.createNativeQuery(sql);
            query.executeUpdate();
            
            log.info("Successfully modified scope field to TEXT type");
            
            result.put("success", true);
            result.put("message", "Scope field successfully modified to TEXT type");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Failed to modify scope field", e);
            
            result.put("success", false);
            result.put("message", "Failed to modify scope field: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 修复 scope 字段长度问题 - 简单版本
     */
    @PostMapping("/fix-scope-field-simple")
    public ResponseEntity<Map<String, Object>> fixScopeFieldSimple() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 直接使用JDBC连接执行DDL语句
            String sql = "ALTER TABLE t_zoom_auth MODIFY COLUMN scope LONGTEXT";

            try (Connection connection = dataSource.getConnection();
                 Statement statement = connection.createStatement()) {

                // 设置自动提交为true，这样DDL语句会立即执行
                connection.setAutoCommit(true);
                statement.executeUpdate(sql);

                log.info("Successfully modified scope field to LONGTEXT type using direct JDBC");

                result.put("success", true);
                result.put("message", "Scope field successfully modified to LONGTEXT type");

                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("Failed to modify scope field to LONGTEXT", e);

            result.put("success", false);
            result.put("message", "Failed to modify scope field: " + e.getMessage());

            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 修复 scope 字段长度问题 - 使用表重命名方式解决SDI错误
     */
    @PostMapping("/fix-scope-field-rename")
    public ResponseEntity<Map<String, Object>> fixScopeFieldWithRename() {
        Map<String, Object> result = new HashMap<>();

        try {
            try (Connection connection = dataSource.getConnection();
                 Statement statement = connection.createStatement()) {

                connection.setAutoCommit(false); // 使用事务

                log.info("开始使用表重命名方式修复scope字段");

                // 1. 创建新表结构（复制原表结构）
                String createNewTableSql = "CREATE TABLE t_zoom_auth_new LIKE t_zoom_auth";
                statement.executeUpdate(createNewTableSql);
                log.info("创建新表 t_zoom_auth_new 成功");

                // 2. 修改新表的scope字段类型
                String modifyScopeSql = "ALTER TABLE t_zoom_auth_new MODIFY COLUMN scope TEXT";
                statement.executeUpdate(modifyScopeSql);
                log.info("修改新表scope字段类型为TEXT成功");

                // 3. 复制数据到新表
                String copyDataSql = "INSERT INTO t_zoom_auth_new SELECT * FROM t_zoom_auth";
                int rowsCopied = statement.executeUpdate(copyDataSql);
                log.info("复制数据到新表成功，共复制 {} 行", rowsCopied);

                // 4. 删除外键约束
                String dropForeignKeySql = "ALTER TABLE t_zoom_accounts DROP FOREIGN KEY FKqvthiw22k43pegcovv723cwjo";
                try {
                    statement.executeUpdate(dropForeignKeySql);
                    log.info("删除外键约束成功");
                } catch (Exception e) {
                    log.warn("删除外键约束失败，可能不存在: {}", e.getMessage());
                }

                // 5. 重命名表
                String renameOldTableSql = "RENAME TABLE t_zoom_auth TO t_zoom_auth_old";
                statement.executeUpdate(renameOldTableSql);
                log.info("重命名原表为 t_zoom_auth_old 成功");

                String renameNewTableSql = "RENAME TABLE t_zoom_auth_new TO t_zoom_auth";
                statement.executeUpdate(renameNewTableSql);
                log.info("重命名新表为 t_zoom_auth 成功");

                // 6. 重新创建外键约束
                String addForeignKeySql = "ALTER TABLE t_zoom_accounts ADD CONSTRAINT FKqvthiw22k43pegcovv723cwjo FOREIGN KEY (zoom_auth_id) REFERENCES t_zoom_auth (id)";
                try {
                    statement.executeUpdate(addForeignKeySql);
                    log.info("重新创建外键约束成功");
                } catch (Exception e) {
                    log.warn("重新创建外键约束失败: {}", e.getMessage());
                }

                // 7. 删除旧表（可选，先保留以防万一）
                // String dropOldTableSql = "DROP TABLE t_zoom_auth_old";
                // statement.executeUpdate(dropOldTableSql);
                // log.info("删除旧表成功");

                connection.commit();
                log.info("所有操作完成，事务提交成功");

                result.put("success", true);
                result.put("message", "Scope field successfully fixed using table rename method. Old table preserved as t_zoom_auth_old");
                result.put("rowsCopied", rowsCopied);

                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("使用表重命名方式修复scope字段失败", e);

            result.put("success", false);
            result.put("message", "Failed to fix scope field using rename method: " + e.getMessage());

            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 修复 scope 字段长度问题 - 使用直接SQL方式
     */
    @PostMapping("/fix-scope-field-direct")
    public ResponseEntity<Map<String, Object>> fixScopeFieldDirect() {
        Map<String, Object> result = new HashMap<>();

        try {
            try (Connection connection = dataSource.getConnection();
                 Statement statement = connection.createStatement()) {

                log.info("开始使用直接SQL方式修复scope字段");

                // 1. 先禁用外键检查
                statement.executeUpdate("SET FOREIGN_KEY_CHECKS = 0");
                log.info("禁用外键检查成功");

                // 2. 尝试直接修改字段类型
                try {
                    String alterSql = "ALTER TABLE t_zoom_auth MODIFY COLUMN scope TEXT";
                    statement.executeUpdate(alterSql);
                    log.info("直接修改scope字段类型为TEXT成功");
                } catch (Exception e) {
                    log.warn("直接修改失败，尝试其他方法: {}", e.getMessage());

                    // 3. 如果直接修改失败，尝试添加新列然后复制数据
                    try {
                        // 添加新列
                        statement.executeUpdate("ALTER TABLE t_zoom_auth ADD COLUMN scope_temp TEXT");
                        log.info("添加临时列scope_temp成功");

                        // 复制数据
                        statement.executeUpdate("UPDATE t_zoom_auth SET scope_temp = scope");
                        log.info("复制数据到临时列成功");

                        // 删除原列
                        statement.executeUpdate("ALTER TABLE t_zoom_auth DROP COLUMN scope");
                        log.info("删除原scope列成功");

                        // 重命名新列
                        statement.executeUpdate("ALTER TABLE t_zoom_auth CHANGE COLUMN scope_temp scope TEXT");
                        log.info("重命名临时列为scope成功");

                    } catch (Exception e2) {
                        log.error("使用临时列方法也失败: {}", e2.getMessage());
                        throw e2;
                    }
                }

                // 4. 重新启用外键检查
                statement.executeUpdate("SET FOREIGN_KEY_CHECKS = 1");
                log.info("重新启用外键检查成功");

                log.info("scope字段修复完成");

                result.put("success", true);
                result.put("message", "Scope field successfully fixed using direct SQL method");

                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("使用直接SQL方式修复scope字段失败", e);

            result.put("success", false);
            result.put("message", "Failed to fix scope field using direct SQL: " + e.getMessage());

            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 修复 scope 字段长度问题 - 使用系统命令方式
     */
    @PostMapping("/fix-scope-field-system")
    public ResponseEntity<Map<String, Object>> fixScopeFieldWithSystem() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始使用系统命令方式修复scope字段");

            // 构建MySQL命令
            String[] command = {
                "mysql",
                "-h", "127.0.0.1",
                "-P", "3306",
                "-u", "root",
                "-pnvshen2018",
                "zoombusV",
                "-e", "SET FOREIGN_KEY_CHECKS = 0; ALTER TABLE t_zoom_auth MODIFY COLUMN scope TEXT; SET FOREIGN_KEY_CHECKS = 1;"
            };

            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);

            Process process = processBuilder.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            int exitCode = process.waitFor();

            if (exitCode == 0) {
                log.info("系统命令执行成功，scope字段修复完成");
                result.put("success", true);
                result.put("message", "Scope field successfully fixed using system command");
                result.put("output", output.toString());
                return ResponseEntity.ok(result);
            } else {
                log.error("系统命令执行失败，退出码: {}, 输出: {}", exitCode, output.toString());
                result.put("success", false);
                result.put("message", "System command failed with exit code: " + exitCode);
                result.put("output", output.toString());
                return ResponseEntity.badRequest().body(result);
            }

        } catch (Exception e) {
            log.error("使用系统命令方式修复scope字段失败", e);

            result.put("success", false);
            result.put("message", "Failed to fix scope field using system command: " + e.getMessage());

            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 临时修复 scope 字段长度问题 - 截断过长的值
     */
    @PostMapping("/fix-scope-field-truncate")
    public ResponseEntity<Map<String, Object>> fixScopeFieldWithTruncate() {
        Map<String, Object> result = new HashMap<>();

        try {
            try (Connection connection = dataSource.getConnection();
                 Statement statement = connection.createStatement()) {

                log.info("开始截断scope字段过长的值");

                // 截断scope字段的值到255个字符
                String updateSql = "UPDATE t_zoom_auth SET scope = LEFT(scope, 255) WHERE LENGTH(scope) > 255";
                int updatedRows = statement.executeUpdate(updateSql);

                log.info("成功截断了 {} 行记录的scope字段", updatedRows);

                result.put("success", true);
                result.put("message", "Successfully truncated scope field values");
                result.put("updatedRows", updatedRows);

                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("截断scope字段值失败", e);

            result.put("success", false);
            result.put("message", "Failed to truncate scope field values: " + e.getMessage());

            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 检查表结构
     */
    @PostMapping("/check-table-structure")
    public ResponseEntity<Map<String, Object>> checkTableStructure() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String sql = "DESCRIBE t_zoom_auth";
            Query query = entityManager.createNativeQuery(sql);
            
            @SuppressWarnings("unchecked")
            List<Object[]> tableStructure = query.getResultList();
            
            result.put("success", true);
            result.put("tableStructure", tableStructure);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Failed to check table structure", e);
            
            result.put("success", false);
            result.put("message", "Failed to check table structure: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }
}
