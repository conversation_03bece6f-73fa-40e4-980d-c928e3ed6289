package com.zoombus.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库测试控制器 - 用于检查数据库表创建状态
 */
@RestController
@RequestMapping("/api/database")
public class DatabaseTestController {

    @Autowired
    private DataSource dataSource;

    @GetMapping("/tables")
    public Map<String, Object> checkTables() {
        Map<String, Object> result = new HashMap<>();
        List<String> tables = new ArrayList<>();
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            // 获取数据库信息
            result.put("databaseName", metaData.getDatabaseProductName());
            result.put("databaseVersion", metaData.getDatabaseProductVersion());
            result.put("catalogName", connection.getCatalog());
            
            // 获取所有表
            ResultSet rs = metaData.getTables(connection.getCatalog(), null, "%", new String[]{"TABLE"});
            while (rs.next()) {
                String tableName = rs.getString("TABLE_NAME");
                tables.add(tableName);
            }
            rs.close();
            
            result.put("tables", tables);
            result.put("tableCount", tables.size());
            result.put("status", "success");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    @GetMapping("/connection")
    public Map<String, Object> checkConnection() {
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            result.put("connected", true);
            result.put("autoCommit", connection.getAutoCommit());
            result.put("catalog", connection.getCatalog());
            result.put("schema", connection.getSchema());
            result.put("status", "success");
            
        } catch (Exception e) {
            result.put("connected", false);
            result.put("status", "error");
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
