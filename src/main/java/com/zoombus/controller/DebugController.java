package com.zoombus.controller;

import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.repository.PmiScheduleWindowRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试控制器
 * 用于调试窗口关闭逻辑问题
 */
@Slf4j
@RestController
@RequestMapping("/api/debug")
@RequiredArgsConstructor
public class DebugController {

    private final PmiScheduleWindowRepository windowRepository;

    /**
     * 调试窗口关闭查询逻辑
     */
    @GetMapping("/windows/close-query")
    public ResponseEntity<Map<String, Object>> debugWindowsCloseQuery(
            @RequestParam(required = false) String currentDate,
            @RequestParam(required = false) String currentTime) {
        
        try {
            // 使用传入的参数或默认值
            LocalDate queryDate = currentDate != null ? LocalDate.parse(currentDate) : LocalDate.now();
            LocalTime queryTime = currentTime != null ? LocalTime.parse(currentTime) : LocalTime.now();
            
            log.info("调试窗口关闭查询，参数: currentDate={}, currentTime={}", queryDate, queryTime);

            // 执行查询（使用新的方法）
            LocalDateTime queryDateTime = LocalDateTime.of(queryDate, queryTime);
            List<PmiScheduleWindow> windowsToClose = windowRepository.findWindowsToClosePmi(queryDateTime);
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("queryDate", queryDate);
            response.put("queryTime", queryTime);
            response.put("windowsCount", windowsToClose.size());
            response.put("windows", windowsToClose);
            
            // 记录详细信息
            log.info("查询结果: 找到 {} 个需要关闭的窗口", windowsToClose.size());
            for (PmiScheduleWindow window : windowsToClose) {
                log.info("窗口详情: id={}, startDateTime={}, endDateTime={}, status={}",
                        window.getId(), window.getStartDateTime(), window.getEndDateTime(), window.getStatus());
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("调试窗口关闭查询失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 测试特定窗口的关闭条件
     */
    @GetMapping("/windows/{windowId}/close-condition")
    public ResponseEntity<Map<String, Object>> testWindowCloseCondition(
            @PathVariable Long windowId,
            @RequestParam(required = false) String currentDate,
            @RequestParam(required = false) String currentTime) {
        
        try {
            // 使用传入的参数或默认值
            LocalDate queryDate = currentDate != null ? LocalDate.parse(currentDate) : LocalDate.now();
            LocalTime queryTime = currentTime != null ? LocalTime.parse(currentTime) : LocalTime.now();
            
            log.info("测试窗口关闭条件，windowId={}, currentDate={}, currentTime={}", windowId, queryDate, queryTime);
            
            // 查找窗口
            PmiScheduleWindow window = windowRepository.findById(windowId).orElse(null);
            if (window == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "窗口不存在");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 分析关闭条件
            Map<String, Object> conditions = analyzeCloseConditions(window, queryDate, queryTime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("window", window);
            response.put("queryDate", queryDate);
            response.put("queryTime", queryTime);
            response.put("conditions", conditions);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("测试窗口关闭条件失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 分析窗口关闭条件
     */
    private Map<String, Object> analyzeCloseConditions(PmiScheduleWindow window, LocalDate currentDate, LocalTime currentTime) {
        Map<String, Object> conditions = new HashMap<>();
        
        // 基本条件：状态为ACTIVE
        boolean statusActive = "ACTIVE".equals(window.getStatus().name());
        conditions.put("statusActive", statusActive);
        
        // 使用新的精确时间字段进行检查
        LocalDateTime currentDateTime = LocalDateTime.of(currentDate, currentTime);
        LocalDateTime windowEndDateTime = window.getEndDateTime();

        // 检查窗口是否应该关闭
        boolean shouldClose = statusActive && windowEndDateTime != null && !currentDateTime.isBefore(windowEndDateTime);
        conditions.put("windowEndDateTime", windowEndDateTime);
        conditions.put("currentDateTime", currentDateTime);
        conditions.put("shouldCloseDetails", Map.of(
                "hasEndDateTime", windowEndDateTime != null,
                "currentNotBeforeEnd", windowEndDateTime != null && !currentDateTime.isBefore(windowEndDateTime)
        ));
        conditions.put("shouldClose", shouldClose);
        
        return conditions;
    }
}
