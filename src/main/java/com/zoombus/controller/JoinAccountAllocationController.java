package com.zoombus.controller;

import com.zoombus.entity.ZoomUser;
import com.zoombus.service.JoinAccountAllocationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Join Account智能分配控制器
 */
@RestController
@RequestMapping("/api/admin/join-account/allocation")
@RequiredArgsConstructor
@Slf4j
public class JoinAccountAllocationController {
    
    private final JoinAccountAllocationService allocationService;
    
    /**
     * 智能分配账号
     */
    @PostMapping("/allocate")
    public ResponseEntity<Map<String, Object>> allocateAccount(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            ZoomUser allocatedAccount = allocationService.allocateAccount(startTime, endTime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", allocatedAccount);
            response.put("message", "智能分配账号成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("智能分配账号失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "分配失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取分配统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getAllocationStatistics() {
        try {
            Map<String, Object> statistics = allocationService.getAllocationStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取分配统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 测试分配算法（仅用于调试）
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testAllocation(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "false") boolean dryRun) {
        
        try {
            if (dryRun) {
                // 干运行模式，只返回分配结果但不实际分配
                ZoomUser allocatedAccount = allocationService.allocateAccount(startTime, endTime);
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", allocatedAccount);
                response.put("message", "测试分配成功（干运行模式）");
                response.put("dryRun", true);
                
                return ResponseEntity.ok(response);
            } else {
                // 实际分配
                ZoomUser allocatedAccount = allocationService.allocateAccount(startTime, endTime);
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", allocatedAccount);
                response.put("message", "测试分配成功");
                response.put("dryRun", false);
                
                return ResponseEntity.ok(response);
            }
        } catch (Exception e) {
            log.error("测试分配失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "测试分配失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
