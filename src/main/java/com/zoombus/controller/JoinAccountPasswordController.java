package com.zoombus.controller;

import com.zoombus.entity.JoinAccountPasswordLog;
import com.zoombus.entity.ZoomUser;
import com.zoombus.service.JoinAccountPasswordLogService;
import com.zoombus.service.JoinAccountPasswordService;
import com.zoombus.service.ZoomUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Join Account密码管理控制器
 */
@RestController
@RequestMapping("/api/admin/join-account/passwords")
@RequiredArgsConstructor
@Slf4j
public class JoinAccountPasswordController {
    
    private final JoinAccountPasswordService passwordService;
    private final JoinAccountPasswordLogService passwordLogService;
    private final ZoomUserService zoomUserService;
    
    /**
     * 手动变更密码
     */
    @PostMapping("/change/{zoomUserId}")
    public ResponseEntity<Map<String, Object>> changePassword(
            @PathVariable Long zoomUserId,
            @RequestParam(required = false) String operator) {
        
        try {
            String newPassword = passwordService.changePasswordManually(zoomUserId, operator);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("newPassword", newPassword);
            response.put("message", "密码变更成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("手动变更密码失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "密码变更失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 批量重置密码
     */
    @PostMapping("/batch-reset")
    public ResponseEntity<Map<String, Object>> batchResetPasswords(
            @RequestBody List<Long> zoomUserIds,
            @RequestParam(required = false) String operator) {
        
        try {
            int successCount = passwordService.batchResetPasswords(zoomUserIds, operator);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("successCount", successCount);
            response.put("totalCount", zoomUserIds.size());
            response.put("message", "批量重置密码完成，成功数量: " + successCount);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量重置密码失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量重置密码失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 生成随机密码
     */
    @GetMapping("/generate")
    public ResponseEntity<Map<String, Object>> generatePassword(
            @RequestParam(defaultValue = "12") int length) {
        
        try {
            String password = passwordService.generatePassword(length);
            boolean isStrong = passwordService.isPasswordStrong(password);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("password", password);
            response.put("length", length);
            response.put("isStrong", isStrong);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("生成密码失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "生成密码失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 验证密码强度
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validatePassword(@RequestParam String password) {
        try {
            boolean isStrong = passwordService.isPasswordStrong(password);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("isStrong", isStrong);
            response.put("length", password.length());
            
            if (!isStrong) {
                response.put("message", "密码强度不足，建议包含大小写字母和数字，长度至少8位");
            } else {
                response.put("message", "密码强度良好");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("验证密码强度失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "验证失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 分页查询密码变更日志
     */
    @GetMapping("/logs")
    public ResponseEntity<Map<String, Object>> getPasswordLogs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) Long zoomUserId,
            @RequestParam(required = false) JoinAccountPasswordLog.ChangeType changeType,
            @RequestParam(required = false) Long windowId,
            @RequestParam(required = false) String createdBy,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<JoinAccountPasswordLog> logPage = passwordLogService.searchLogs(
                    zoomUserId, changeType, windowId, createdBy, startTime, endTime, pageable);

            // 为每个日志添加Zoom账号email信息
            Page<Map<String, Object>> enrichedLogPage = logPage.map(passwordLog -> {
                Map<String, Object> logData = new HashMap<>();
                logData.put("log", passwordLog);

                // 查找Zoom账号信息
                try {
                    ZoomUser zoomUser = zoomUserService.getZoomUserById(passwordLog.getZoomUserId()).orElse(null);
                    if (zoomUser != null) {
                        Map<String, Object> zoomUserData = new HashMap<>();
                        zoomUserData.put("id", zoomUser.getId());
                        zoomUserData.put("email", zoomUser.getEmail());
                        zoomUserData.put("firstName", zoomUser.getFirstName());
                        zoomUserData.put("lastName", zoomUser.getLastName());
                        logData.put("zoomUser", zoomUserData);
                    }
                } catch (Exception e) {
                    log.debug("获取Zoom账号信息失败: ZoomUserId={}", passwordLog.getZoomUserId(), e);
                }

                return logData;
            });

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", enrichedLogPage);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("查询密码变更日志失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据Zoom账号ID获取密码变更日志
     */
    @GetMapping("/logs/by-zoom-user/{zoomUserId}")
    public ResponseEntity<Map<String, Object>> getPasswordLogsByZoomUserId(
            @PathVariable Long zoomUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<JoinAccountPasswordLog> logPage = passwordLogService.getLogsByZoomUserId(zoomUserId, pageable);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", logPage);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("根据Zoom账号ID获取密码变更日志失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据窗口ID获取密码变更日志
     */
    @GetMapping("/logs/by-window/{windowId}")
    public ResponseEntity<Map<String, Object>> getPasswordLogsByWindowId(@PathVariable Long windowId) {
        try {
            List<JoinAccountPasswordLog> logs = passwordLogService.getLogsByWindowId(windowId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", logs);
            response.put("count", logs.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("根据窗口ID获取密码变更日志失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取密码变更统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getPasswordChangeStatistics() {
        try {
            Map<String, Object> statistics = passwordLogService.getPasswordChangeStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取密码变更统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取系统自动变更的日志
     */
    @GetMapping("/logs/system")
    public ResponseEntity<Map<String, Object>> getSystemChangeLogs() {
        try {
            List<JoinAccountPasswordLog> logs = passwordLogService.getSystemChangeLogs();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", logs);
            response.put("count", logs.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取系统自动变更日志失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取手动变更的日志
     */
    @GetMapping("/logs/manual")
    public ResponseEntity<Map<String, Object>> getManualChangeLogs() {
        try {
            List<JoinAccountPasswordLog> logs = passwordLogService.getManualChangeLogs();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", logs);
            response.put("count", logs.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取手动变更日志失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取频繁变更密码的账号
     */
    @GetMapping("/frequent-changes")
    public ResponseEntity<Map<String, Object>> getFrequentlyChangedAccounts(
            @RequestParam(defaultValue = "7") int days,
            @RequestParam(defaultValue = "5") long minCount) {
        
        try {
            LocalDateTime sinceTime = LocalDateTime.now().minusDays(days);
            List<Object[]> accounts = passwordLogService.getFrequentlyChangedAccounts(sinceTime, minCount);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", accounts);
            response.put("count", accounts.size());
            response.put("criteria", "最近" + days + "天内变更次数≥" + minCount + "次");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取频繁变更密码的账号失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
