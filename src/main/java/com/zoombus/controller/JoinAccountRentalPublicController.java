package com.zoombus.controller;

import com.zoombus.entity.JoinAccountPasswordLog;
import com.zoombus.entity.JoinAccountRentalToken;
import com.zoombus.entity.JoinAccountUsageWindow;
import com.zoombus.entity.ZoomUser;
import com.zoombus.service.JoinAccountAllocationService;
import com.zoombus.service.JoinAccountPasswordLogService;
import com.zoombus.service.JoinAccountPasswordService;
import com.zoombus.service.JoinAccountRentalTokenService;
import com.zoombus.service.JoinAccountUsageWindowService;
import com.zoombus.service.JoinAccountWindowSchedulerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Join Account Rental公共API控制器
 * 提供给客户端使用的公共接口
 */
@RestController
@RequestMapping("/api/public/join-account")
@RequiredArgsConstructor
@Slf4j
public class JoinAccountRentalPublicController {
    
    private final JoinAccountRentalTokenService tokenService;
    private final JoinAccountUsageWindowService windowService;
    private final JoinAccountAllocationService allocationService;
    private final JoinAccountPasswordService passwordService;
    private final JoinAccountPasswordLogService passwordLogService;
    private final JoinAccountWindowSchedulerService windowSchedulerService;
    
    /**
     * 测试密码生成（临时测试接口）
     */
    @PostMapping("/test-password")
    public ResponseEntity<Map<String, Object>> testPasswordGeneration() {
        try {
            String password = passwordService.generateJoinAccountPassword();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("password", password);
            response.put("message", "密码生成成功");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试密码生成失败", e);

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "密码生成失败: " + e.getMessage());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取Token信息
     */
    @GetMapping("/tokens/{tokenNumber}")
    public ResponseEntity<Map<String, Object>> getTokenInfo(@PathVariable String tokenNumber) {
        try {
            JoinAccountRentalToken token = tokenService.getTokenByNumber(tokenNumber)
                    .orElseThrow(() -> new IllegalArgumentException("权益链接不存在或已失效"));
            
            // 检查Token是否可以使用
            if (token.getStatus() == JoinAccountRentalToken.TokenStatus.CANCELLED) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "权益链接已作废");
                return ResponseEntity.badRequest().body(response);
            }
            
            Map<String, Object> tokenData = new HashMap<>();
            tokenData.put("tokenNumber", token.getTokenNumber());
            tokenData.put("usageDays", token.getUsageDays());
            tokenData.put("status", token.getStatus());
            tokenData.put("batchNumber", token.getBatchNumber());
            tokenData.put("createdAt", token.getCreatedAt());
            
            // 如果有使用窗口，添加窗口信息
            if (token.getStatus() == JoinAccountRentalToken.TokenStatus.RESERVED ||
                token.getStatus() == JoinAccountRentalToken.TokenStatus.ACTIVE ||
                token.getStatus() == JoinAccountRentalToken.TokenStatus.COMPLETED) {
                
                JoinAccountUsageWindow window = windowService.getWindowByToken(tokenNumber).orElse(null);
                if (window != null) {
                    tokenData.put("windowStartTime", window.getStartTime());
                    tokenData.put("windowEndTime", window.getEndTime());
                    tokenData.put("windowStatus", window.getStatus());
                    
                    // 如果账号已分配，添加账号信息（RESERVED和ACTIVE状态都显示）
                    if (token.getAssignedZoomUserId() != null &&
                        (token.getStatus() == JoinAccountRentalToken.TokenStatus.RESERVED ||
                         token.getStatus() == JoinAccountRentalToken.TokenStatus.ACTIVE)) {
                        
                        Map<String, Object> accountInfo = new HashMap<>();
                        accountInfo.put("email", token.getAssignedZoomUserEmail());
                        // 从Token中获取分配的密码
                        accountInfo.put("password", token.getAssignedPassword());
                        accountInfo.put("loginUrl", "https://zoom.us/signin");
                        tokenData.put("allocatedAccount", accountInfo);
                    }
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tokenData);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Token信息失败: tokenNumber={}", tokenNumber, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取权益信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 预约Token
     */
    @PostMapping("/tokens/{tokenNumber}/reserve")
    public ResponseEntity<Map<String, Object>> reserveToken(
            @PathVariable String tokenNumber,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        try {
            // 验证时间参数
            if (startTime == null || endTime == null) {
                throw new IllegalArgumentException("开始时间和结束时间不能为空");
            }
            
            if (startTime.isAfter(endTime)) {
                throw new IllegalArgumentException("开始时间不能晚于结束时间");
            }
            
            // 按自然日验证：允许选择当天及以后的日期
            LocalDate startDate = startTime.toLocalDate();
            LocalDate currentDate = LocalDate.now();
            if (startDate.isBefore(currentDate)) {
                throw new IllegalArgumentException("开始日期不能早于当前日期");
            }
            
            // 检查Token是否存在且可预约
            JoinAccountRentalToken token = tokenService.getTokenByNumber(tokenNumber)
                    .orElseThrow(() -> new IllegalArgumentException("权益链接不存在或已失效"));
            
            if (!tokenService.canReserveToken(tokenNumber)) {
                String currentStatus = token.getStatus().getDescription();
                throw new IllegalArgumentException("该权益链接当前无法预约，当前状态：" + currentStatus + "。只有待使用或已导出状态的权益链接才能预约。");
            }
            
            // 验证使用天数限制
            long daysDiff = java.time.Duration.between(startTime, endTime).toDays();
            if (daysDiff > token.getUsageDays()) {
                throw new IllegalArgumentException("使用时间不能超过" + token.getUsageDays() + "天");
            }
            
            // 智能分配账号
            ZoomUser allocatedAccount = allocationService.allocateAccount(startTime, endTime);

            // 生成预约密码（预约时就生成，但窗口开启时才会真正设置到Zoom账号）
            // 使用Join Account专用密码格式：Aa + 6位数字
            String reservedPassword = passwordService.generateJoinAccountPassword();

            // 预约Token
            JoinAccountRentalToken reservedToken = tokenService.reserveToken(
                    tokenNumber, allocatedAccount.getId(), allocatedAccount.getEmail(), startTime, reservedPassword, "PUBLIC_API");

            // 创建使用窗口
            JoinAccountUsageWindow window = windowService.createWindow(
                    allocatedAccount.getId(), tokenNumber, startTime, endTime);

            // 记录预约时的密码（用于窗口开启时设置）
            passwordLogService.logPasswordChange(
                    allocatedAccount.getId(),
                    null, // 旧密码为空
                    reservedPassword,
                    JoinAccountPasswordLog.ChangeType.RESERVATION,
                    window.getId(),
                    "SYSTEM");

            // 如果预约开始时间已到或已过，立即尝试开启窗口（避免等待定时任务）
            JoinAccountUsageWindow.WindowStatus actualWindowStatus = window.getStatus();
            try {
                if (!startTime.isAfter(LocalDateTime.now())) {
                    window = windowSchedulerService.manualOpenWindow(window.getId());
                    actualWindowStatus = window.getStatus();
                }
            } catch (Exception ex) {
                log.warn("预约后立即开启窗口失败，将由定时任务重试: windowId={}, token={}, err={}", window.getId(), tokenNumber, ex.getMessage());
                // 此处不抛出异常，允许预约成功，但前端需根据windowStatus真实状态展示
            }

            Map<String, Object> accountInfo = new HashMap<>();
            accountInfo.put("email", allocatedAccount.getEmail());
            accountInfo.put("password", reservedPassword);
            accountInfo.put("loginUrl", "https://zoom.us/signin");

            Map<String, Object> windowInfo = new HashMap<>();
            windowInfo.put("startTime", startTime);
            windowInfo.put("endTime", endTime);
            windowInfo.put("status", actualWindowStatus.name());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "预约成功");
            response.put("data", Map.of(
                    "tokenNumber", reservedToken.getTokenNumber(),
                    "windowId", window.getId(),
                    "allocatedAccount", accountInfo,
                    "usageWindow", windowInfo
            ));
            
            log.info("Token预约成功: tokenNumber={}, zoomUserId={}, startTime={}, endTime={}", 
                    tokenNumber, allocatedAccount.getId(), startTime, endTime);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Token预约失败: tokenNumber={}", tokenNumber, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "预约失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 激活Token（获取账号信息）
     */
    @PostMapping("/tokens/{tokenNumber}/activate")
    public ResponseEntity<Map<String, Object>> activateToken(@PathVariable String tokenNumber) {
        try {
            // 检查Token是否存在且可激活
            JoinAccountRentalToken token = tokenService.getTokenByNumber(tokenNumber)
                    .orElseThrow(() -> new IllegalArgumentException("权益链接不存在或已失效"));
            
            if (token.getStatus() != JoinAccountRentalToken.TokenStatus.RESERVED) {
                throw new IllegalArgumentException("该权益链接当前无法激活");
            }
            
            // 检查是否到了预约时间
            JoinAccountUsageWindow window = windowService.getWindowByToken(tokenNumber)
                    .orElseThrow(() -> new IllegalArgumentException("未找到对应的使用窗口"));
            
            if (LocalDateTime.now().isBefore(window.getStartTime())) {
                throw new IllegalArgumentException("还未到预约时间，请稍后再试");
            }
            
            // 激活Token
            JoinAccountRentalToken activatedToken = tokenService.activateToken(tokenNumber, "AUTO_GENERATED_PASSWORD");
            String password = "AUTO_GENERATED_PASSWORD"; // 这里应该从密码服务获取实际密码
            
            // 开启使用窗口
            windowService.openWindow(window.getId());
            
            Map<String, Object> accountInfo = new HashMap<>();
            accountInfo.put("email", token.getAssignedZoomUserEmail());
            accountInfo.put("password", password);
            accountInfo.put("loginUrl", "https://zoom.us/signin");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "账号激活成功");
            response.put("data", accountInfo);
            
            log.info("Token激活成功: tokenNumber={}, email={}", tokenNumber, token.getAssignedZoomUserEmail());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Token激活失败: tokenNumber={}", tokenNumber, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "激活失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取Token状态
     */
    @GetMapping("/tokens/{tokenNumber}/status")
    public ResponseEntity<Map<String, Object>> getTokenStatus(@PathVariable String tokenNumber) {
        try {
            JoinAccountRentalToken token = tokenService.getTokenByNumber(tokenNumber)
                    .orElseThrow(() -> new IllegalArgumentException("权益链接不存在或已失效"));
            
            Map<String, Object> statusData = new HashMap<>();
            statusData.put("tokenNumber", token.getTokenNumber());
            statusData.put("status", token.getStatus());
            statusData.put("canReserve", tokenService.canReserveToken(tokenNumber));
            
            // 如果有使用窗口，添加窗口状态
            JoinAccountUsageWindow window = windowService.getWindowByToken(tokenNumber).orElse(null);
            if (window != null) {
                statusData.put("windowStatus", window.getStatus());
                statusData.put("startTime", window.getStartTime());
                statusData.put("endTime", window.getEndTime());
                statusData.put("canActivate", 
                        token.getStatus() == JoinAccountRentalToken.TokenStatus.RESERVED &&
                        LocalDateTime.now().isAfter(window.getStartTime()));
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statusData);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Token状态失败: tokenNumber={}", tokenNumber, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 检查Token是否可以预约
     */
    @GetMapping("/tokens/{tokenNumber}/can-reserve")
    public ResponseEntity<Map<String, Object>> canReserveToken(@PathVariable String tokenNumber) {
        try {
            boolean canReserve = tokenService.canReserveToken(tokenNumber);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("canReserve", canReserve);
            
            if (!canReserve) {
                JoinAccountRentalToken token = tokenService.getTokenByNumber(tokenNumber).orElse(null);
                if (token != null) {
                    response.put("reason", "当前状态: " + token.getStatus());
                } else {
                    response.put("reason", "权益链接不存在");
                }
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("检查Token预约状态失败: tokenNumber={}", tokenNumber, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
