package com.zoombus.controller;

import com.zoombus.entity.JoinAccountRentalToken;
import com.zoombus.entity.JoinAccountUsageWindow;
import com.zoombus.service.JoinAccountRentalTokenService;
import com.zoombus.service.JoinAccountUsageWindowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Join Account Rental令牌控制器
 */
@RestController
@RequestMapping("/api/admin/join-account/tokens")
@RequiredArgsConstructor
@Slf4j
public class JoinAccountRentalTokenController {

    private final JoinAccountRentalTokenService tokenService;
    private final JoinAccountUsageWindowService windowService;
    
    /**
     * 批量生成权益链接
     */
    @PostMapping("/batch-generate")
    public ResponseEntity<Map<String, Object>> batchGenerateTokens(
            @RequestParam Integer usageDays,
            @RequestParam Integer quantity,
            @RequestParam(required = false) String remark,
            @RequestParam(required = false) String operator) {
        
        try {
            List<JoinAccountRentalToken> tokens = tokenService.batchGenerateTokens(
                    usageDays, quantity, remark, operator);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tokens);
            response.put("message", "批量生成权益链接成功，数量: " + tokens.size());
            response.put("generatedCount", tokens.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量生成权益链接失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量生成失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 分页查询权益链接
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getTokens(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String tokenNumber,
            @RequestParam(required = false) String batchNumber,
            @RequestParam(required = false) JoinAccountRentalToken.TokenStatus status,
            @RequestParam(required = false) JoinAccountRentalToken.ExportStatus exportStatus,
            @RequestParam(required = false) Integer usageDays,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {

        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ?
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<JoinAccountRentalToken> tokenPage = tokenService.searchTokens(
                    tokenNumber, batchNumber, status, exportStatus, usageDays, startTime, endTime, pageable);

            // 为每个token添加窗口信息
            Page<Map<String, Object>> enrichedTokenPage = tokenPage.map(token -> {
                Map<String, Object> tokenData = new HashMap<>();
                tokenData.put("token", token);
                tokenData.put("link", tokenService.generateTokenLink(token.getTokenNumber()));

                // 查找关联的使用窗口
                JoinAccountUsageWindow window = windowService.getWindowByToken(token.getTokenNumber()).orElse(null);
                if (window != null) {
                    Map<String, Object> windowData = new HashMap<>();
                    windowData.put("id", window.getId());
                    windowData.put("status", window.getStatus());
                    windowData.put("startTime", window.getStartTime());
                    windowData.put("endTime", window.getEndTime());
                    windowData.put("openedAt", window.getOpenedAt());
                    windowData.put("closedAt", window.getClosedAt());
                    windowData.put("errorMessage", window.getErrorMessage());
                    windowData.put("lastOperationError", window.getLastOperationError());
                    tokenData.put("window", windowData);
                }

                return tokenData;
            });

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", enrichedTokenPage);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("查询权益链接失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据ID获取权益链接详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getTokenById(@PathVariable Long id) {
        try {
            JoinAccountRentalToken token = tokenService.getTokenById(id)
                    .orElseThrow(() -> new IllegalArgumentException("权益链接不存在"));
            
            Map<String, Object> tokenWithLink = tokenService.getTokenWithLink(token);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tokenWithLink);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取权益链接详情失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取详情失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据Token编号获取权益链接详情
     */
    @GetMapping("/by-number/{tokenNumber}")
    public ResponseEntity<Map<String, Object>> getTokenByNumber(@PathVariable String tokenNumber) {
        try {
            JoinAccountRentalToken token = tokenService.getTokenByNumber(tokenNumber)
                    .orElseThrow(() -> new IllegalArgumentException("权益链接不存在"));
            
            Map<String, Object> tokenWithLink = tokenService.getTokenWithLink(token);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tokenWithLink);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取权益链接详情失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取详情失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取权益链接统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getTokenStatistics() {
        try {
            Map<String, Object> statistics = tokenService.getTokenStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取权益链接统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取可导出的权益链接
     */
    @GetMapping("/exportable")
    public ResponseEntity<Map<String, Object>> getExportableTokens() {
        try {
            List<JoinAccountRentalToken> tokens = tokenService.getExportableTokens();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tokens);
            response.put("count", tokens.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取可导出权益链接失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取可导出权益链接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取可预约的权益链接
     */
    @GetMapping("/reservable")
    public ResponseEntity<Map<String, Object>> getReservableTokens() {
        try {
            List<JoinAccountRentalToken> tokens = tokenService.getReservableTokens();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tokens);
            response.put("count", tokens.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取可预约权益链接失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取可预约权益链接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取可作废的权益链接
     */
    @GetMapping("/cancellable")
    public ResponseEntity<Map<String, Object>> getCancellableTokens() {
        try {
            List<JoinAccountRentalToken> tokens = tokenService.getCancellableTokens();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tokens);
            response.put("count", tokens.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取可作废权益链接失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取可作废权益链接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 检查Token是否可以预约
     */
    @GetMapping("/{tokenNumber}/can-reserve")
    public ResponseEntity<Map<String, Object>> canReserveToken(@PathVariable String tokenNumber) {
        try {
            boolean canReserve = tokenService.canReserveToken(tokenNumber);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("canReserve", canReserve);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("检查Token预约状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 生成权益链接
     */
    @GetMapping("/{tokenNumber}/link")
    public ResponseEntity<Map<String, Object>> generateTokenLink(@PathVariable String tokenNumber) {
        try {
            String link = tokenService.generateTokenLink(tokenNumber);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("link", link);
            response.put("tokenNumber", tokenNumber);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("生成权益链接失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "生成链接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量标记为已导出
     */
    @PutMapping("/mark-exported")
    public ResponseEntity<Map<String, Object>> markTokensAsExported(
            @RequestBody List<Long> tokenIds,
            @RequestParam(required = false) String operator) {

        try {
            List<JoinAccountRentalToken> tokens = tokenService.markTokensAsExported(tokenIds, operator);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tokens);
            response.put("message", "批量标记为已导出成功，数量: " + tokens.size());
            response.put("updatedCount", tokens.size());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量标记为已导出失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "标记失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量作废令牌
     */
    @PutMapping("/cancel")
    public ResponseEntity<Map<String, Object>> cancelTokens(
            @RequestBody List<Long> tokenIds,
            @RequestParam(required = false) String operator,
            @RequestParam(required = false) String reason) {

        try {
            List<JoinAccountRentalToken> tokens = tokenService.cancelTokens(tokenIds, operator, reason);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tokens);
            response.put("message", "批量作废令牌成功，数量: " + tokens.size());
            response.put("cancelledCount", tokens.size());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量作废令牌失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "作废失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 预约令牌
     */
    @PutMapping("/{tokenNumber}/reserve")
    public ResponseEntity<Map<String, Object>> reserveToken(
            @PathVariable String tokenNumber,
            @RequestParam Long zoomUserId,
            @RequestParam String zoomUserEmail,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) String operator) {

        try {
            // 生成预约密码
            String assignedPassword = "ADMIN_ASSIGNED"; // 管理员预约时的默认密码，可以后续修改
            JoinAccountRentalToken token = tokenService.reserveToken(
                    tokenNumber, zoomUserId, zoomUserEmail, startTime, assignedPassword, operator);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", token);
            response.put("message", "预约令牌成功");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("预约令牌失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "预约失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 激活令牌
     */
    @PutMapping("/{tokenNumber}/activate")
    public ResponseEntity<Map<String, Object>> activateToken(
            @PathVariable String tokenNumber,
            @RequestParam String password) {

        try {
            JoinAccountRentalToken token = tokenService.activateToken(tokenNumber, password);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", token);
            response.put("message", "激活令牌成功");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("激活令牌失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "激活失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 完成令牌
     */
    @PutMapping("/{tokenNumber}/complete")
    public ResponseEntity<Map<String, Object>> completeToken(@PathVariable String tokenNumber) {
        try {
            JoinAccountRentalToken token = tokenService.completeToken(tokenNumber);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", token);
            response.put("message", "完成令牌成功");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("完成令牌失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "完成失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量导出权益链接为Excel文件
     */
    @PostMapping("/export")
    public ResponseEntity<ByteArrayResource> exportTokensToExcel(
            @RequestBody List<Long> tokenIds,
            @RequestParam(required = false) String operator) {

        try {
            // 生成Excel文件
            ByteArrayResource resource = tokenService.exportTokensToExcel(tokenIds, operator);

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("权益链接_JAR_BATCH_%s.xlsx", timestamp);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);

        } catch (Exception e) {
            log.error("导出权益链接Excel失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }
}
