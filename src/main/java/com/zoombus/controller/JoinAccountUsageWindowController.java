package com.zoombus.controller;

import com.zoombus.entity.JoinAccountUsageWindow;
import com.zoombus.service.JoinAccountUsageWindowService;
import com.zoombus.service.JoinAccountWindowSchedulerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Join Account使用窗口控制器
 */
@RestController
@RequestMapping("/api/admin/join-account/windows")
@RequiredArgsConstructor
@Slf4j
public class JoinAccountUsageWindowController {

    private final JoinAccountUsageWindowService windowService;
    private final JoinAccountWindowSchedulerService windowSchedulerService;
    
    /**
     * 分页查询使用窗口
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getWindows(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "startTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) Long zoomUserId,
            @RequestParam(required = false) String tokenNumber,
            @RequestParam(required = false) JoinAccountUsageWindow.WindowStatus status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<JoinAccountUsageWindow> windowPage = windowService.searchWindows(
                    zoomUserId, tokenNumber, status, startTime, endTime, pageable);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", windowPage);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("查询使用窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据ID获取使用窗口详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getWindowById(@PathVariable Long id) {
        try {
            JoinAccountUsageWindow window = windowService.getWindowById(id)
                    .orElseThrow(() -> new IllegalArgumentException("使用窗口不存在"));
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", window);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取使用窗口详情失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取详情失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据Token编号获取使用窗口
     */
    @GetMapping("/by-token/{tokenNumber}")
    public ResponseEntity<Map<String, Object>> getWindowByToken(@PathVariable String tokenNumber) {
        try {
            JoinAccountUsageWindow window = windowService.getWindowByToken(tokenNumber)
                    .orElseThrow(() -> new IllegalArgumentException("该Token没有关联的使用窗口"));
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", window);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("根据Token获取使用窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据Zoom账号ID获取使用窗口
     */
    @GetMapping("/by-zoom-user/{zoomUserId}")
    public ResponseEntity<Map<String, Object>> getWindowsByZoomUserId(@PathVariable Long zoomUserId) {
        try {
            List<JoinAccountUsageWindow> windows = windowService.getWindowsByZoomUserId(zoomUserId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", windows);
            response.put("count", windows.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("根据Zoom账号ID获取使用窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 创建使用窗口
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createWindow(
            @RequestParam Long zoomUserId,
            @RequestParam String tokenNumber,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            JoinAccountUsageWindow window = windowService.createWindow(zoomUserId, tokenNumber, startTime, endTime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", window);
            response.put("message", "创建使用窗口成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("创建使用窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "创建失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 开启使用窗口
     */
    @PutMapping("/{id}/open")
    public ResponseEntity<Map<String, Object>> openWindow(@PathVariable Long id) {
        try {
            // 使用与定时任务一致的完整流程：开启窗口 + 激活令牌 + 变更密码 + 记录日志
            JoinAccountUsageWindow window = windowSchedulerService.manualOpenWindow(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", window);
            response.put("message", "开启使用窗口成功");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("开启使用窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "开启失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 关闭使用窗口
     */
    @PutMapping("/{id}/close")
    public ResponseEntity<Map<String, Object>> closeWindow(@PathVariable Long id) {
        try {
            JoinAccountUsageWindow window = windowService.closeWindow(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", window);
            response.put("message", "关闭使用窗口成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("关闭使用窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "关闭失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 清除窗口错误信息
     */
    @PutMapping("/{id}/clear-error")
    public ResponseEntity<Map<String, Object>> clearWindowError(@PathVariable Long id) {
        try {
            windowService.clearWindowError(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "错误信息已清除");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("清除窗口错误信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "清除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 删除使用窗口
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteWindow(@PathVariable Long id) {
        try {
            windowService.deleteWindow(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "删除使用窗口成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除使用窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取需要开启的窗口
     */
    @GetMapping("/to-open")
    public ResponseEntity<Map<String, Object>> getWindowsToOpen() {
        try {
            List<JoinAccountUsageWindow> windows = windowService.getWindowsToOpen();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", windows);
            response.put("count", windows.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取需要开启的窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取需要关闭的窗口
     */
    @GetMapping("/to-close")
    public ResponseEntity<Map<String, Object>> getWindowsToClose() {
        try {
            List<JoinAccountUsageWindow> windows = windowService.getWindowsToClose();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", windows);
            response.put("count", windows.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取需要关闭的窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取使用窗口统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getWindowStatistics() {
        try {
            Map<String, Object> statistics = windowService.getWindowStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取使用窗口统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取即将开启的窗口
     */
    @GetMapping("/upcoming")
    public ResponseEntity<Map<String, Object>> getUpcomingWindows(
            @RequestParam(defaultValue = "60") int minutes) {
        
        try {
            List<JoinAccountUsageWindow> windows = windowService.getUpcomingWindows(minutes);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", windows);
            response.put("count", windows.size());
            response.put("timeRange", minutes + "分钟内");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取即将开启的窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取即将关闭的窗口
     */
    @GetMapping("/expiring")
    public ResponseEntity<Map<String, Object>> getExpiringWindows(
            @RequestParam(defaultValue = "60") int minutes) {
        
        try {
            List<JoinAccountUsageWindow> windows = windowService.getExpiringWindows(minutes);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", windows);
            response.put("count", windows.size());
            response.put("timeRange", minutes + "分钟内");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取即将关闭的窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
