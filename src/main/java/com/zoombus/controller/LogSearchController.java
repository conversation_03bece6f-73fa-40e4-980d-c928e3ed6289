package com.zoombus.controller;

import com.zoombus.dto.PageResult;
import com.zoombus.dto.log.LogEntry;
import com.zoombus.dto.log.LogSearchRequest;
import com.zoombus.service.LogSearchService;
import com.zoombus.trace.TraceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志搜索控制器
 * 提供日志检索、调用链查看、日志导出等功能
 */
@RestController
@RequestMapping("/api/logs")
@RequiredArgsConstructor
@Slf4j
public class LogSearchController {
    
    private final LogSearchService logSearchService;
    
    /**
     * 搜索日志
     */
    @PostMapping("/search")
    public ResponseEntity<Map<String, Object>> searchLogs(@RequestBody LogSearchRequest request) {
        String traceId = TraceContext.getTraceId();
        log.info("收到日志检索请求: traceId={}, request={}", traceId, request);
        
        try {
            // 验证请求参数
            if (!request.isValid()) {
                log.warn("日志检索请求参数无效: traceId={}, request={}", traceId, request);
                return ResponseEntity.badRequest().body(createErrorResponse("请求参数无效，至少需要一个搜索条件"));
            }
            
            // 执行搜索
            PageResult<LogEntry> result = logSearchService.searchLogs(request);
            
            log.info("日志检索完成: traceId={}, resultCount={}", traceId, result.getTotalElements());
            
            // 构造响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result.getContent());
            response.put("total", result.getTotalElements());
            response.put("page", result.getCurrentPage());
            response.put("size", result.getPageSize());
            response.put("totalPages", result.getTotalPages());
            response.put("hasNext", result.isHasNext());
            response.put("hasPrevious", result.isHasPrevious());
            response.put("traceId", traceId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("日志检索失败: traceId={}", traceId, e);
            return ResponseEntity.internalServerError().body(createErrorResponse("日志检索失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取完整调用链
     */
    @GetMapping("/trace/{targetTraceId}")
    public ResponseEntity<List<LogEntry>> getTraceChain(@PathVariable String targetTraceId) {
        String traceId = TraceContext.getTraceId();
        log.info("获取调用链: currentTraceId={}, targetTraceId={}", traceId, targetTraceId);
        
        try {
            List<LogEntry> chain = logSearchService.getCompleteTraceChain(targetTraceId);
            
            log.info("调用链获取完成: currentTraceId={}, targetTraceId={}, chainLength={}", 
                    traceId, targetTraceId, chain.size());
            
            return ResponseEntity.ok(chain);
            
        } catch (Exception e) {
            log.error("获取调用链失败: currentTraceId={}, targetTraceId={}", traceId, targetTraceId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 导出日志
     */
    @GetMapping("/export")
    public ResponseEntity<Resource> exportLogs(
            @RequestParam String traceId,
            @RequestParam(required = false, defaultValue = "txt") String format) {
        
        String currentTraceId = TraceContext.getTraceId();
        log.info("导出日志: currentTraceId={}, targetTraceId={}, format={}", currentTraceId, traceId, format);
        
        try {
            Resource resource = logSearchService.exportLogs(traceId, format);
            
            String filename = String.format("logs_%s_%s.%s", 
                    traceId, 
                    System.currentTimeMillis(), 
                    format);
            
            log.info("日志导出完成: currentTraceId={}, targetTraceId={}, filename={}", 
                    currentTraceId, traceId, filename);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出日志失败: currentTraceId={}, targetTraceId={}", currentTraceId, traceId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取日志统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getLogStats() {
        String traceId = TraceContext.getTraceId();
        log.info("获取日志统计信息: traceId={}", traceId);
        
        try {
            // 这里可以实现日志统计逻辑
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalFiles", 0);
            stats.put("totalSize", 0);
            stats.put("oldestLog", null);
            stats.put("newestLog", null);
            stats.put("availableSources", List.of("APPLICATION", "WEBHOOK", "SCHEDULER", "API"));
            stats.put("availableLevels", List.of("ERROR", "WARN", "INFO", "DEBUG"));
            
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            log.error("获取日志统计信息失败: traceId={}", traceId, e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取统计信息失败"));
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "LogSearchService");
        health.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(health);
    }
    
    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        response.put("traceId", TraceContext.getTraceId());
        return response;
    }
}
