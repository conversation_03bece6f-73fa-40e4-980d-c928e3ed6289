package com.zoombus.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.dto.CreateMeetingRequest;
import com.zoombus.dto.UpdateMeetingRequest;
import com.zoombus.dto.DeleteMeetingRequest;
import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.dto.MeetingDetailWithTime;
import com.zoombus.entity.Meeting;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.entity.ZoomMeetingDetail;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.ZoomUserRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.service.MeetingService;
import com.zoombus.service.ZoomApiService;
import com.zoombus.service.ZoomAuthService;
import com.zoombus.service.ZoomMeetingDetailService;
import com.zoombus.service.ZoomMeetingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.ArrayList;
import java.util.HashMap;

@RestController
@RequestMapping("/api/meetings")
@RequiredArgsConstructor
@Slf4j
public class MeetingController {

    private final MeetingService meetingService;
    private final ZoomMeetingDetailService zoomMeetingDetailService;
    private final ZoomMeetingService zoomMeetingService;
    private final ZoomApiService zoomApiService;
    private final ZoomUserRepository zoomUserRepository;
    private final ZoomMeetingRepository zoomMeetingRepository;
    private final ZoomAuthService zoomAuthService;

    // 工具方法：创建错误响应
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("error", message);
        return result;
    }

    /**
     * 获取会议对应的ZoomAuth
     */
    private com.zoombus.entity.ZoomAuth getZoomAuthForMeeting(Meeting meeting) {
        if (meeting.getZoomUserId() == null || meeting.getZoomUserId().trim().isEmpty()) {
            throw new RuntimeException("会议没有关联的ZoomUser: meetingId=" + meeting.getId());
        }

        // 通过ZoomUserId查找ZoomUser，然后获取对应的ZoomAuth
        Optional<ZoomUser> zoomUserOpt = zoomUserRepository.findFirstByZoomUserId(meeting.getZoomUserId());
        if (!zoomUserOpt.isPresent()) {
            throw new RuntimeException("未找到ZoomUser: zoomUserId=" + meeting.getZoomUserId());
        }

        ZoomUser zoomUser = zoomUserOpt.get();
        if (zoomUser.getZoomAuth() == null) {
            throw new RuntimeException("ZoomUser没有关联的ZoomAuth: zoomUserId=" + meeting.getZoomUserId());
        }

        log.debug("找到会议对应的ZoomAuth: meetingId={}, zoomUserId={}, zoomAuthAccount={}",
                meeting.getId(), meeting.getZoomUserId(), zoomUser.getZoomAuth().getAccountName());
        return zoomUser.getZoomAuth();
    }
    
    /**
     * 创建会议
     */
    @PostMapping
    public ResponseEntity<Meeting> createMeeting(@Valid @RequestBody CreateMeetingRequest request) {
        log.info("收到创建会议请求: {}", request);
        log.info("=== 会议时长调试 ===");
        log.info("接收到的会议时长: {} 分钟", request.getDurationMinutes());
        log.info("会议时长字段类型: {}", request.getDurationMinutes() != null ? request.getDurationMinutes().getClass().getSimpleName() : "null");
        log.info("周期性会议参数: isRecurring={}, recurrenceType={}, repeatInterval={}, endType={}, endTimes={}",
                request.getIsRecurring(), request.getRecurrenceType(), request.getRepeatInterval(),
                request.getEndType(), request.getEndTimes());

        Meeting meeting = meetingService.createMeeting(request);
        return ResponseEntity.ok(meeting);
    }
    
    /**
     * 获取所有会议（分页）
     */
    @GetMapping
    public ResponseEntity<Page<Meeting>> getAllMeetings(Pageable pageable) {
        // 如果没有指定排序，默认按创建时间降序排列
        if (pageable.getSort().isUnsorted()) {
            pageable = PageRequest.of(
                pageable.getPageNumber(),
                pageable.getPageSize(),
                Sort.by(Sort.Direction.DESC, "createdAt")
            );
        }
        Page<Meeting> meetings = meetingService.getAllMeetings(pageable);
        return ResponseEntity.ok(meetings);
    }

    /**
     * 获取最近的会议详情（用于仪表板显示）
     */
    @GetMapping("/recent-details")
    public ResponseEntity<Page<ZoomMeetingDetail>> getRecentMeetingDetails(Pageable pageable) {
        Page<ZoomMeetingDetail> meetingDetails = zoomMeetingDetailService.getRecentMeetingDetails(pageable);
        return ResponseEntity.ok(meetingDetails);
    }

    /**
     * 获取最近一周的会议详情（用于仪表板显示，包含正确的开始时间）
     */
    @GetMapping("/recent-week-details")
    public ResponseEntity<Page<MeetingDetailWithTime>> getRecentWeekMeetingDetails(Pageable pageable) {
        Page<MeetingDetailWithTime> meetingDetails = zoomMeetingDetailService.getRecentWeekMeetingDetails(pageable);
        return ResponseEntity.ok(meetingDetails);
    }
    
    /**
     * 根据ID获取会议
     * 智能识别：支持数字ID（Meeting表主键）和字符串ID（zoom_meeting_id）
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getMeetingById(@PathVariable String id) {
        try {
            log.info("查询会议详情: id={}", id);

            // 首先尝试作为zoom_meeting_id查询ZoomMeeting（支持PMI会议）
            // 直接使用Repository方法，因为Service中可能没有这个方法
            List<ZoomMeeting> zoomMeetings = zoomMeetingRepository.findByZoomMeetingId(id);
            if (!zoomMeetings.isEmpty()) {
                // 优先返回活跃状态的会议，如果没有则返回最新的
                ZoomMeeting zoomMeeting = zoomMeetings.stream()
                    .filter(zm -> zm.getStatus() == ZoomMeeting.MeetingStatus.WAITING ||
                                 zm.getStatus() == ZoomMeeting.MeetingStatus.STARTED)
                    .findFirst()
                    .orElse(zoomMeetings.get(0));

                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", zoomMeeting);
                response.put("type", "ZoomMeeting");
                // 返回Zoom API格式的状态（started/waiting/ended）
                String zoomStatus = zoomMeeting.getStatus().toZoomStatus();
                response.put("status", zoomStatus != null ? zoomStatus : "waiting");

                log.info("找到ZoomMeeting记录: id={}, meetingId={}, status={}",
                        zoomMeeting.getId(), id, zoomMeeting.getStatus());
                return ResponseEntity.ok(response);
            }

            // 如果是数字ID，尝试作为Meeting表主键查询
            if (id.matches("\\d+")) {
                try {
                    Long meetingId = Long.parseLong(id);
                    Optional<Meeting> meetingOpt = meetingService.getMeetingById(meetingId);
                    if (meetingOpt.isPresent()) {
                        Meeting meeting = meetingOpt.get();
                        Map<String, Object> response = new HashMap<>();
                        response.put("success", true);
                        response.put("data", meeting);
                        response.put("type", "Meeting");
                        response.put("status", meeting.getStatus().name().toLowerCase());

                        log.info("找到Meeting记录: id={}, status={}", meetingId, meeting.getStatus());
                        return ResponseEntity.ok(response);
                    }
                } catch (NumberFormatException e) {
                    log.debug("ID不是有效数字，跳过Meeting查询: id={}", id);
                }
            }

            // 都不存在，返回404
            log.warn("未找到会议记录: id={}", id);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "会议不存在: " + id);
            return ResponseEntity.notFound().build();

        } catch (Exception e) {
            log.error("获取会议详情失败: id={}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取会议详情失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 根据Zoom会议ID获取会议
     */
    @GetMapping("/zoom-meeting/{zoomMeetingId}")
    public ResponseEntity<Meeting> getMeetingByZoomMeetingId(@PathVariable String zoomMeetingId) {
        return meetingService.getMeetingByZoomMeetingId(zoomMeetingId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据创建者用户ID获取会议
     */
    @GetMapping("/creator/{creatorUserId}")
    public ResponseEntity<List<Meeting>> getMeetingsByCreatorUserId(@PathVariable Long creatorUserId) {
        List<Meeting> meetings = meetingService.getMeetingsByCreatorUserId(creatorUserId);
        return ResponseEntity.ok(meetings);
    }

    /**
     * 根据UUID获取会议详情（用于会议主持人页面）
     */
    @GetMapping("/uuid/{meetingUuid}")
    public ResponseEntity<Meeting> getMeetingByUuid(@PathVariable String meetingUuid) {
        return meetingService.getMeetingByUuid(meetingUuid)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 获取最新的主持人链接（用于开始会议）
     */
    @PostMapping("/uuid/{meetingUuid}/host-url")
    public ResponseEntity<Map<String, Object>> getLatestHostUrl(@PathVariable String meetingUuid) {
        try {
            String hostUrl = meetingService.getLatestHostUrl(meetingUuid);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("hostUrl", hostUrl);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取最新主持人链接失败: meetingUuid={}", meetingUuid, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取主持人链接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据Zoom用户ID获取会议
     */
    @GetMapping("/zoom-user/{zoomUserId}")
    public ResponseEntity<List<Meeting>> getMeetingsByZoomUserId(@PathVariable String zoomUserId) {
        List<Meeting> meetings = meetingService.getMeetingsByZoomUserId(zoomUserId);
        return ResponseEntity.ok(meetings);
    }
    
    /**
     * 根据状态获取会议
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<List<Meeting>> getMeetingsByStatus(@PathVariable Meeting.MeetingStatus status) {
        List<Meeting> meetings = meetingService.getMeetingsByStatus(status);
        return ResponseEntity.ok(meetings);
    }
    
    /**
     * 根据创建来源获取会议
     */
    @GetMapping("/source/{creationSource}")
    public ResponseEntity<List<Meeting>> getMeetingsByCreationSource(@PathVariable Meeting.CreationSource creationSource) {
        List<Meeting> meetings = meetingService.getMeetingsByCreationSource(creationSource);
        return ResponseEntity.ok(meetings);
    }
    
    /**
     * 根据时间范围获取会议
     */
    @GetMapping("/time-range")
    public ResponseEntity<List<Meeting>> getMeetingsByTimeRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        List<Meeting> meetings = meetingService.getMeetingsByTimeRange(startDate, endDate);
        return ResponseEntity.ok(meetings);
    }
    
    /**
     * 根据创建者用户和时间范围获取会议
     */
    @GetMapping("/creator/{creatorUserId}/time-range")
    public ResponseEntity<List<Meeting>> getMeetingsByCreatorUserAndTimeRange(
            @PathVariable Long creatorUserId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        List<Meeting> meetings = meetingService.getMeetingsByCreatorUserAndTimeRange(creatorUserId, startDate, endDate);
        return ResponseEntity.ok(meetings);
    }
    
    /**
     * 更新会议信息
     * 基于Zoom API PATCH /meetings/{meetingId}
     */
    @PatchMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateMeeting(
            @PathVariable Long id,
            @RequestBody UpdateMeetingRequest request) {
        try {
            Meeting meeting = meetingService.getMeetingById(id)
                    .orElseThrow(() -> new RuntimeException("会议不存在: " + id));

            // 获取会议对应的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getZoomAuthForMeeting(meeting);

            ZoomApiResponse<JsonNode> response = zoomApiService.updateMeeting(
                    meeting.getZoomMeetingId(), request, zoomAuth);

            if (!response.isSuccess()) {
                return ResponseEntity.badRequest()
                        .body(createErrorResponse(response.getMessage()));
            }

            // 更新本地数据库中的会议记录
            // TODO: 实现更新本地会议记录的逻辑
            log.info("会议更新成功，会议ID: {}", id);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "会议更新成功");
            result.put("data", response.getData());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("更新会议失败", e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse("更新会议失败: " + e.getMessage()));
        }
    }

    /**
     * 更新会议状态
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<Meeting> updateMeetingStatus(@PathVariable Long id,
                                                      @RequestParam Meeting.MeetingStatus status) {
        Meeting meeting = meetingService.updateMeetingStatus(id, status);
        return ResponseEntity.ok(meeting);
    }
    
    /**
     * 同步会议信息
     */
    @PostMapping("/{id}/sync")
    public ResponseEntity<Meeting> syncMeetingInfo(@PathVariable Long id) {
        Meeting meeting = meetingService.syncMeetingInfo(id);
        return ResponseEntity.ok(meeting);
    }

    /**
     * 获取会议邀请信息
     */
    @GetMapping("/{id}/invitation")
    public ResponseEntity<Map<String, Object>> getMeetingInvitation(@PathVariable Long id) {
        try {
            Optional<Meeting> meetingOpt = meetingService.getMeetingById(id);
            if (!meetingOpt.isPresent()) {
                return ResponseEntity.badRequest()
                        .body(Map.of("success", false, "message", "会议不存在"));
            }

            Meeting meeting = meetingOpt.get();
            if (meeting.getZoomMeetingId() == null) {
                return ResponseEntity.badRequest()
                        .body(Map.of("success", false, "message", "Zoom会议ID为空"));
            }

            // 获取会议对应的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getZoomAuthForMeeting(meeting);

            // 调用Zoom API获取邀请信息（使用正确的ZoomAuth）
            var apiResponse = zoomApiService.getMeetingInvitation(meeting.getZoomMeetingId(), zoomAuth);

            if (!apiResponse.isSuccess()) {
                return ResponseEntity.ok(Map.of(
                        "success", false,
                        "message", "获取邀请信息失败: " + apiResponse.getMessage()
                ));
            }

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", apiResponse.getData()
            ));
        } catch (Exception e) {
            log.error("获取会议邀请信息失败", e);
            return ResponseEntity.ok(Map.of(
                    "success", false,
                    "message", "获取邀请信息失败: " + e.getMessage()
            ));
        }
    }
    
    /**
     * 删除会议
     * 基于Zoom API DELETE /meetings/{meetingId}
     * 即使Zoom API调用失败，也会继续删除本地记录
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteMeeting(@PathVariable Long id) {
        return deleteMeetingAdvanced(id, null);
    }

    /**
     * 删除会议（带参数）
     * 基于Zoom API DELETE /meetings/{meetingId}
     * 即使Zoom API调用失败，也会继续删除本地记录
     */
    @DeleteMapping("/{id}/advanced")
    public ResponseEntity<Map<String, Object>> deleteMeetingAdvanced(
            @PathVariable Long id,
            @RequestBody(required = false) DeleteMeetingRequest request) {
        try {
            Meeting meeting = meetingService.getMeetingById(id)
                    .orElseThrow(() -> new RuntimeException("会议不存在: " + id));

            String zoomApiMessage = null;
            boolean zoomApiSuccess = false;

            // 尝试调用Zoom API删除会议
            if (meeting.getZoomMeetingId() != null && !meeting.getZoomMeetingId().trim().isEmpty()) {
                try {
                    ZoomApiResponse<Void> response = zoomApiService.deleteMeeting(
                            meeting.getZoomMeetingId(), request);

                    if (response.isSuccess()) {
                        zoomApiSuccess = true;
                        zoomApiMessage = "Zoom API删除成功";
                        log.info("Zoom API删除会议成功: meetingId={}", meeting.getZoomMeetingId());
                    } else {
                        zoomApiMessage = "Zoom API删除失败: " + response.getMessage();
                        log.warn("Zoom API删除会议失败，但继续删除本地记录: {}", response.getMessage());
                    }
                } catch (Exception e) {
                    zoomApiMessage = "Zoom API调用异常: " + e.getMessage();
                    log.warn("Zoom API删除会议异常，但继续删除本地记录", e);
                }
            } else {
                zoomApiMessage = "会议没有Zoom会议ID，跳过Zoom API删除";
                log.info("会议没有Zoom会议ID，跳过Zoom API删除");
            }

            // 无论Zoom API是否成功，都删除本地数据库中的会议记录
            meetingService.deleteMeeting(id);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);

            if (zoomApiSuccess) {
                result.put("message", "会议删除成功");
            } else {
                result.put("message", "本地会议删除成功，但Zoom API删除失败: " + zoomApiMessage);
                result.put("warning", zoomApiMessage);
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("删除会议失败", e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse("删除会议失败: " + e.getMessage()));
        }
    }

    /**
     * 获取Zoom会议详情列表（支持周期性会议的多个详情记录，过滤已删除的occurrence）
     * 返回包含正确字段映射的DTO
     */
    @GetMapping("/{id}/zoom-details")
    public ResponseEntity<List<com.zoombus.dto.ZoomMeetingDetailWithMeeting>> getZoomMeetingDetails(@PathVariable Long id) {
        List<com.zoombus.dto.ZoomMeetingDetailWithMeeting> details = zoomMeetingDetailService.getActiveByMeetingIdWithMeeting(id);
        return ResponseEntity.ok(details);
    }

    /**
     * 获取特定occurrence的会议信息
     */
    @GetMapping("/{id}/occurrences/{occurrenceId}")
    public ResponseEntity<Map<String, Object>> getMeetingOccurrence(
            @PathVariable Long id,
            @PathVariable String occurrenceId) {
        try {
            Meeting meeting = meetingService.getMeetingById(id)
                    .orElseThrow(() -> new RuntimeException("会议不存在: " + id));

            ZoomApiResponse<JsonNode> response = zoomApiService.getMeetingOccurrence(
                    meeting.getZoomMeetingId(), occurrenceId);

            if (!response.isSuccess()) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("error", response.getMessage());
                return ResponseEntity.badRequest().body(errorResult);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", response.getData());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取会议occurrence失败", e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse(e.getMessage()));
        }
    }

    /**
     * 更新特定occurrence的会议信息
     */
    @PutMapping("/{id}/occurrences/{occurrenceId}")
    public ResponseEntity<Map<String, Object>> updateMeetingOccurrence(
            @PathVariable Long id,
            @PathVariable String occurrenceId,
            @RequestBody Map<String, Object> updateData) {
        try {
            Meeting meeting = meetingService.getMeetingById(id)
                    .orElseThrow(() -> new RuntimeException("会议不存在: " + id));

            // 获取会议对应的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getZoomAuthForMeeting(meeting);

            ZoomApiResponse<JsonNode> response = zoomApiService.updateMeetingOccurrence(
                    meeting.getZoomMeetingId(), occurrenceId, updateData, zoomAuth);

            if (!response.isSuccess()) {
                return ResponseEntity.badRequest()
                        .body(createErrorResponse(response.getMessage()));
            }

            // 更新本地数据库中的occurrence记录
            zoomMeetingDetailService.updateOccurrenceFromZoomResponse(id, occurrenceId, response.getData());

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "会议occurrence更新成功");
            result.put("data", response.getData());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("更新会议occurrence失败", e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse(e.getMessage()));
        }
    }

    /**
     * 删除特定occurrence的会议
     * 即使Zoom API调用失败，也会继续删除本地记录
     */
    @DeleteMapping("/{id}/occurrences/{occurrenceId}")
    public ResponseEntity<Map<String, Object>> deleteMeetingOccurrence(
            @PathVariable Long id,
            @PathVariable String occurrenceId) {
        try {
            Meeting meeting = meetingService.getMeetingById(id)
                    .orElseThrow(() -> new RuntimeException("会议不存在: " + id));

            String zoomApiMessage = null;
            boolean zoomApiSuccess = false;

            // 尝试调用Zoom API删除occurrence
            try {
                ZoomApiResponse<Void> response = zoomApiService.deleteMeetingOccurrence(
                        meeting.getZoomMeetingId(), occurrenceId);

                if (response.isSuccess()) {
                    zoomApiSuccess = true;
                    zoomApiMessage = "Zoom API删除occurrence成功";
                    log.info("Zoom API删除occurrence成功: meetingId={}, occurrenceId={}",
                            meeting.getZoomMeetingId(), occurrenceId);
                } else {
                    zoomApiMessage = "Zoom API删除occurrence失败: " + response.getMessage();
                    log.warn("Zoom API删除occurrence失败，但继续删除本地记录: {}", response.getMessage());
                }
            } catch (Exception e) {
                zoomApiMessage = "Zoom API调用异常: " + e.getMessage();
                log.warn("Zoom API删除occurrence异常，但继续删除本地记录", e);
            }

            // 无论Zoom API是否成功，都更新本地数据库中的occurrence状态
            zoomMeetingDetailService.markOccurrenceAsDeleted(id, occurrenceId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);

            if (zoomApiSuccess) {
                result.put("message", "会议occurrence删除成功");
            } else {
                result.put("message", "本地occurrence删除成功，但Zoom API删除失败: " + zoomApiMessage);
                result.put("warning", zoomApiMessage);
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("删除会议occurrence失败", e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse("删除会议occurrence失败: " + e.getMessage()));
        }
    }

    /**
     * 批量删除会议occurrences
     */
    @DeleteMapping("/{id}/occurrences/batch")
    public ResponseEntity<Map<String, Object>> batchDeleteMeetingOccurrences(
            @PathVariable Long id,
            @RequestBody Map<String, Object> requestBody) {
        try {
            @SuppressWarnings("unchecked")
            List<String> occurrenceIds = (List<String>) requestBody.get("occurrenceIds");

            if (occurrenceIds == null || occurrenceIds.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(createErrorResponse("occurrenceIds不能为空"));
            }

            Meeting meeting = meetingService.getMeetingById(id)
                    .orElseThrow(() -> new RuntimeException("会议不存在: " + id));

            int successCount = 0;
            int failCount = 0;
            List<String> failedOccurrences = new ArrayList<>();

            for (String occurrenceId : occurrenceIds) {
                boolean zoomApiSuccess = false;
                String errorMessage = null;

                try {
                    // 尝试调用Zoom API删除occurrence
                    ZoomApiResponse<Void> response = zoomApiService.deleteMeetingOccurrence(
                            meeting.getZoomMeetingId(), occurrenceId);

                    if (response.isSuccess()) {
                        zoomApiSuccess = true;
                        log.info("Zoom API删除occurrence成功: meetingId={}, occurrenceId={}",
                                meeting.getZoomMeetingId(), occurrenceId);
                    } else {
                        errorMessage = response.getMessage();
                        log.warn("Zoom API删除occurrence失败，但继续删除本地记录: occurrenceId={}, 错误: {}",
                                occurrenceId, errorMessage);
                    }
                } catch (Exception e) {
                    errorMessage = e.getMessage();
                    log.warn("Zoom API删除occurrence异常，但继续删除本地记录: occurrenceId={}", occurrenceId, e);
                }

                try {
                    // 无论Zoom API是否成功，都尝试更新本地数据库中的occurrence状态
                    zoomMeetingDetailService.markOccurrenceAsDeleted(id, occurrenceId);
                    successCount++;

                    if (!zoomApiSuccess) {
                        log.info("本地occurrence删除成功，但Zoom API失败: occurrenceId={}, 错误: {}",
                                occurrenceId, errorMessage);
                    }
                } catch (Exception e) {
                    failCount++;
                    failedOccurrences.add(occurrenceId);
                    log.error("本地删除occurrence失败: occurrenceId={}", occurrenceId, e);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", String.format("批量删除完成，成功: %d, 失败: %d", successCount, failCount));
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("failedOccurrences", failedOccurrences);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("批量删除会议occurrence失败", e);
            return ResponseEntity.badRequest()
                    .body(createErrorResponse(e.getMessage()));
        }
    }

    /**
     * 获取Zoom会议详情（兼容旧接口，返回第一个详情记录）
     */
    @GetMapping("/{id}/zoom-detail")
    public ResponseEntity<ZoomMeetingDetail> getZoomMeetingDetail(@PathVariable Long id) {
        Optional<ZoomMeetingDetail> detail = zoomMeetingDetailService.getByMeetingId(id);
        if (detail.isPresent()) {
            return ResponseEntity.ok(detail.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 手动触发会议状态同步（用于测试定时任务逻辑）
     */
    @PostMapping("/{zoomMeetingId}/sync-status")
    public ResponseEntity<Map<String, Object>> syncMeetingStatus(@PathVariable String zoomMeetingId) {
        try {
            log.info("手动触发会议状态同步: zoomMeetingId={}", zoomMeetingId);

            // 查找对应的ZoomMeeting记录
            List<ZoomMeeting> zoomMeetings = zoomMeetingRepository.findByZoomMeetingId(zoomMeetingId);
            if (zoomMeetings.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "未找到会议记录: " + zoomMeetingId);
                return ResponseEntity.notFound().build();
            }

            // 找到活跃状态的会议记录
            ZoomMeeting targetMeeting = zoomMeetings.stream()
                .filter(zm -> zm.getStatus() == ZoomMeeting.MeetingStatus.WAITING ||
                             zm.getStatus() == ZoomMeeting.MeetingStatus.STARTED)
                .findFirst()
                .orElse(zoomMeetings.get(0));

            log.info("找到目标会议记录: id={}, status={}", targetMeeting.getId(), targetMeeting.getStatus());

            // 调用Zoom API获取当前状态
            com.zoombus.entity.ZoomAuth zoomAuth = getZoomAuthForZoomMeeting(targetMeeting);
            ZoomApiResponse<JsonNode> response = zoomApiService.getMeeting(zoomMeetingId, zoomAuth);

            if (!response.isSuccess()) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "调用Zoom API失败: " + response.getMessage());
                result.put("errorCode", response.getErrorCode());
                return ResponseEntity.badRequest().body(result);
            }

            JsonNode meetingData = response.getData();
            String zoomStatus = meetingData.has("status") ? meetingData.get("status").asText() : "unknown";

            log.info("Zoom API返回状态: zoomMeetingId={}, 本地状态={}, Zoom状态={}",
                    zoomMeetingId, targetMeeting.getStatus(), zoomStatus);

            // 执行状态同步逻辑
            boolean statusChanged = false;
            String action = "无需处理";

            if ((targetMeeting.getStatus() == ZoomMeeting.MeetingStatus.WAITING) && "started".equals(zoomStatus)) {
                log.info("检测到会议已开始，执行状态同步: meetingId={}", targetMeeting.getId());
                try {
                    zoomMeetingService.handlePmiMeetingStarted(
                        targetMeeting.getZoomMeetingUuid(),
                        zoomMeetingId,
                        targetMeeting.getHostId(),
                        targetMeeting.getTopic()
                    );
                    statusChanged = true;
                    action = "WAITING -> STARTED";
                } catch (Exception e) {
                    log.error("状态同步失败", e);
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", false);
                    result.put("message", "状态同步失败: " + e.getMessage());
                    return ResponseEntity.internalServerError().body(result);
                }
            } else if ((targetMeeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED) && "ended".equals(zoomStatus)) {
                log.info("检测到会议已结束，执行状态同步: meetingId={}", targetMeeting.getId());
                try {
                    zoomMeetingService.handleMeetingEnded(targetMeeting.getZoomMeetingUuid());
                    statusChanged = true;
                    action = "STARTED -> ENDED";
                } catch (Exception e) {
                    log.error("状态同步失败", e);
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", false);
                    result.put("message", "状态同步失败: " + e.getMessage());
                    return ResponseEntity.internalServerError().body(result);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("zoomMeetingId", zoomMeetingId);
            result.put("meetingId", targetMeeting.getId());
            result.put("originalStatus", targetMeeting.getStatus());
            result.put("zoomStatus", zoomStatus);
            result.put("statusChanged", statusChanged);
            result.put("action", action);
            result.put("message", statusChanged ? "状态同步成功" : "状态一致，无需同步");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("手动状态同步失败: zoomMeetingId={}", zoomMeetingId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "同步失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取ZoomMeeting对应的ZoomAuth
     */
    private com.zoombus.entity.ZoomAuth getZoomAuthForZoomMeeting(ZoomMeeting meeting) {
        if (meeting.getAssignedZoomUserId() == null) {
            throw new RuntimeException("会议没有分配ZoomUser: meetingId=" + meeting.getId());
        }

        Optional<ZoomUser> zoomUserOpt = zoomUserRepository.findById(meeting.getAssignedZoomUserId());
        if (!zoomUserOpt.isPresent()) {
            throw new RuntimeException("未找到ZoomUser: id=" + meeting.getAssignedZoomUserId());
        }

        ZoomUser zoomUser = zoomUserOpt.get();
        if (zoomUser.getZoomAuth() == null) {
            throw new RuntimeException("ZoomUser没有关联的ZoomAuth: id=" + meeting.getAssignedZoomUserId());
        }

        log.debug("找到ZoomMeeting对应的ZoomAuth: meetingId={}, zoomUserId={}, zoomAuthAccount={}",
                meeting.getId(), meeting.getAssignedZoomUserId(), zoomUser.getZoomAuth().getAccountName());
        return zoomUser.getZoomAuth();
    }

    /**
     * 测试定时同步逻辑（模拟MeetingStatusSyncScheduler的逻辑）
     */
    @PostMapping("/{zoomMeetingId}/test-sync")
    public ResponseEntity<Map<String, Object>> testSyncLogic(@PathVariable String zoomMeetingId) {
        try {
            log.info("测试定时同步逻辑: zoomMeetingId={}", zoomMeetingId);

            // 查找对应的ZoomMeeting记录
            List<ZoomMeeting> zoomMeetings = zoomMeetingRepository.findByZoomMeetingId(zoomMeetingId);
            if (zoomMeetings.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "未找到会议记录: " + zoomMeetingId);
                return ResponseEntity.notFound().build();
            }

            // 找到WAITING状态的会议记录
            ZoomMeeting targetMeeting = zoomMeetings.stream()
                .filter(zm -> zm.getStatus() == ZoomMeeting.MeetingStatus.WAITING)
                .findFirst()
                .orElse(null);

            if (targetMeeting == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "未找到WAITING状态的会议记录: " + zoomMeetingId);
                return ResponseEntity.badRequest().body(response);
            }

            log.info("找到WAITING状态的会议记录: id={}, uuid={}",
                    targetMeeting.getId(), targetMeeting.getZoomMeetingUuid());

            // 调用Zoom API获取当前状态
            com.zoombus.entity.ZoomAuth zoomAuth = getZoomAuthForZoomMeeting(targetMeeting);
            ZoomApiResponse<JsonNode> response = zoomApiService.getMeeting(zoomMeetingId, zoomAuth);

            if (!response.isSuccess()) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "调用Zoom API失败: " + response.getMessage());
                result.put("errorCode", response.getErrorCode());
                return ResponseEntity.badRequest().body(result);
            }

            JsonNode meetingData = response.getData();
            String zoomStatus = meetingData.has("status") ? meetingData.get("status").asText() : "unknown";
            String realUuid = meetingData.has("uuid") ? meetingData.get("uuid").asText() : null;

            log.info("Zoom API返回: zoomMeetingId={}, 本地状态={}, Zoom状态={}, 真实UUID={}",
                    zoomMeetingId, targetMeeting.getStatus(), zoomStatus, realUuid);

            // 模拟定时任务的同步逻辑
            boolean statusChanged = false;
            String action = "无需处理";

            if ((targetMeeting.getStatus() == ZoomMeeting.MeetingStatus.WAITING) && "started".equals(zoomStatus)) {
                log.info("检测到会议已开始，执行状态同步: meetingId={}", targetMeeting.getId());

                // 直接更新当前记录的状态
                targetMeeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
                targetMeeting.setStartTime(LocalDateTime.now());

                // 如果有真实的UUID，更新UUID
                if (realUuid != null && !realUuid.trim().isEmpty()) {
                    String oldUuid = targetMeeting.getZoomMeetingUuid();
                    targetMeeting.setZoomMeetingUuid(realUuid);
                    log.info("更新会议UUID: meetingId={}, 旧UUID={}, 新UUID={}",
                            targetMeeting.getId(), oldUuid, realUuid);
                }

                // 更新主题（如果API返回了主题）
                if (meetingData.has("topic")) {
                    String apiTopic = meetingData.get("topic").asText();
                    if (apiTopic != null && !apiTopic.trim().isEmpty()) {
                        targetMeeting.setTopic(apiTopic);
                    }
                }

                // 保存更新
                zoomMeetingRepository.save(targetMeeting);

                statusChanged = true;
                action = "WAITING -> STARTED";
                log.info("会议状态同步成功: meetingId={}, zoomMeetingId={}",
                        targetMeeting.getId(), zoomMeetingId);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("zoomMeetingId", zoomMeetingId);
            result.put("meetingId", targetMeeting.getId());
            result.put("originalStatus", "WAITING");
            result.put("currentStatus", targetMeeting.getStatus());
            result.put("zoomStatus", zoomStatus);
            result.put("realUuid", realUuid);
            result.put("statusChanged", statusChanged);
            result.put("action", action);
            result.put("message", statusChanged ? "状态同步成功" : "状态一致，无需同步");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("测试同步逻辑失败: zoomMeetingId={}", zoomMeetingId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
