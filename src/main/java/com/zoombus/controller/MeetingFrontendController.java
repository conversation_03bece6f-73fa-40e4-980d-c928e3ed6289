package com.zoombus.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 会议前端控制器 - 处理用户端会议页面路由
 * 支持 /meeting/{meetingUuid} 直接访问
 */
@Controller
@RequestMapping("/meeting")
@Slf4j
public class MeetingFrontendController {
    
    /**
     * 处理 /meeting/{meetingUuid} 路由，返回用户前端应用
     */
    @GetMapping("/{meetingUuid}")
    public ResponseEntity<Resource> meetingPage(@PathVariable String meetingUuid) {
        log.info("访问用户会议页面: meetingUuid={}", meetingUuid);
        try {
            Resource resource = new ClassPathResource("static-user/index.html");
            if (resource.exists()) {
                return ResponseEntity.ok()
                        .contentType(MediaType.TEXT_HTML)
                        .body(resource);
            } else {
                log.warn("用户前端文件不存在，返回404");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("加载用户前端页面失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
