package com.zoombus.controller;

import com.zoombus.entity.MeetingReport;
import com.zoombus.entity.MeetingParticipant;
import com.zoombus.entity.MeetingReportTask;
import com.zoombus.service.MeetingReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 会议报告控制器
 * 提供会议报告相关的REST API接口
 */
@RestController
@RequestMapping("/api/meeting-reports")
@RequiredArgsConstructor
@Slf4j
public class MeetingReportController {
    
    private final MeetingReportService meetingReportService;
    
    /**
     * 分页获取会议报告列表
     */
    @GetMapping
    public ResponseEntity<Page<MeetingReport>> getReports(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "startTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String hostId,
            @RequestParam(required = false) Long pmiRecordId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) MeetingReport.FetchStatus fetchStatus) {
        
        log.info("获取会议报告列表: page={}, size={}, hostId={}, pmiRecordId={}", page, size, hostId, pmiRecordId);
        
        Sort sort = Sort.by(sortDir.equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC, sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<MeetingReport> reports = meetingReportService.getReportsWithFilters(
            hostId, pmiRecordId, startTime, endTime, keyword, fetchStatus, pageable);
        
        return ResponseEntity.ok(reports);
    }
    
    /**
     * 根据UUID获取会议报告详情（返回最新的一条）
     */
    @GetMapping("/uuid/{zoomMeetingUuid}")
    public ResponseEntity<MeetingReport> getReportByUuid(@PathVariable String zoomMeetingUuid) {
        log.info("获取会议报告详情: zoomMeetingUuid={}", zoomMeetingUuid);

        Optional<MeetingReport> report = meetingReportService.getReportByUuid(zoomMeetingUuid);

        return report.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 根据UUID获取所有会议报告记录（支持多条记录）
     */
    @GetMapping("/uuid/{zoomMeetingUuid}/all")
    public ResponseEntity<List<MeetingReport>> getAllReportsByUuid(@PathVariable String zoomMeetingUuid) {
        log.info("获取所有会议报告记录: zoomMeetingUuid={}", zoomMeetingUuid);

        List<MeetingReport> reports = meetingReportService.getAllReportsByUuid(zoomMeetingUuid);

        if (reports.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(reports);
    }
    
    /**
     * 根据会议ID获取会议报告详情（返回最新的一条）
     */
    @GetMapping("/meeting-id/{zoomMeetingId}")
    public ResponseEntity<MeetingReport> getReportByMeetingId(@PathVariable String zoomMeetingId) {
        log.info("获取会议报告详情: zoomMeetingId={}", zoomMeetingId);

        Optional<MeetingReport> report = meetingReportService.getReportByMeetingId(zoomMeetingId);

        return report.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 根据会议ID获取所有会议报告记录（支持多条记录）
     */
    @GetMapping("/meeting-id/{zoomMeetingId}/all")
    public ResponseEntity<List<MeetingReport>> getAllReportsByMeetingId(@PathVariable String zoomMeetingId) {
        log.info("获取所有会议报告记录: zoomMeetingId={}", zoomMeetingId);

        List<MeetingReport> reports = meetingReportService.getAllReportsByMeetingId(zoomMeetingId);

        if (reports.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(reports);
    }
    
    /**
     * 获取指定PMI的会议报告
     */
    @GetMapping("/pmi/{pmiRecordId}")
    public ResponseEntity<Page<MeetingReport>> getReportsByPmiRecordId(
            @PathVariable Long pmiRecordId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("获取PMI会议报告: pmiRecordId={}", pmiRecordId);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "startTime"));
        Page<MeetingReport> reports = meetingReportService.getReportsByPmiRecordId(pmiRecordId, pageable);
        
        return ResponseEntity.ok(reports);
    }
    
    /**
     * 获取指定主持人的会议报告
     */
    @GetMapping("/host/{hostId}")
    public ResponseEntity<Page<MeetingReport>> getReportsByHostId(
            @PathVariable String hostId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("获取主持人会议报告: hostId={}", hostId);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "startTime"));
        Page<MeetingReport> reports = meetingReportService.getReportsByHostId(hostId, pageable);
        
        return ResponseEntity.ok(reports);
    }
    
    /**
     * 获取最近的会议报告
     */
    @GetMapping("/recent")
    public ResponseEntity<Page<MeetingReport>> getRecentReports(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        log.info("获取最近的会议报告");
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MeetingReport> reports = meetingReportService.getRecentReports(pageable);
        
        return ResponseEntity.ok(reports);
    }
    
    /**
     * 获取会议报告统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getReportStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        log.info("获取会议报告统计: startTime={}, endTime={}", startTime, endTime);
        
        Map<String, Object> statistics = meetingReportService.getReportStatistics(startTime, endTime);
        
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 获取会议参会人员列表
     */
    @GetMapping("/{reportId}/participants")
    public ResponseEntity<Page<MeetingParticipant>> getParticipants(
            @PathVariable Long reportId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {
        
        log.info("获取会议参会人员: reportId={}", reportId);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "joinTime"));
        Page<MeetingParticipant> participants = meetingReportService.getParticipantsByReportId(reportId, pageable);
        
        return ResponseEntity.ok(participants);
    }
    
    /**
     * 获取参会人员统计信息
     */
    @GetMapping("/{reportId}/participants/statistics")
    public ResponseEntity<Map<String, Object>> getParticipantStatistics(@PathVariable Long reportId) {
        log.info("获取参会统计: reportId={}", reportId);
        
        Map<String, Object> statistics = meetingReportService.getParticipantStatistics(reportId, null, null);
        
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 手动触发会议报告获取（通过Zoom会议UUID）
     */
    @PostMapping("/fetch/{zoomMeetingUuid}")
    public ResponseEntity<Map<String, Object>> triggerReportFetch(@PathVariable String zoomMeetingUuid) {
        log.info("手动触发会议报告获取: zoomMeetingUuid={}", zoomMeetingUuid);

        try {
            meetingReportService.triggerReportFetch(zoomMeetingUuid);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会议报告获取已触发");
            response.put("zoomMeetingUuid", zoomMeetingUuid);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("触发会议报告获取失败", e);

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "触发失败: " + e.getMessage());
            response.put("zoomMeetingUuid", zoomMeetingUuid);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 手动触发会议报告获取（通过系统会议UUID）
     */
    @PostMapping("/fetch/meeting/{meetingUuid}")
    public ResponseEntity<Map<String, Object>> triggerReportFetchByMeetingUuid(@PathVariable String meetingUuid) {
        log.info("通过系统会议UUID手动触发会议报告获取: meetingUuid={}", meetingUuid);

        try {
            meetingReportService.triggerReportFetchByMeetingUuid(meetingUuid);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会议报告获取已触发");
            response.put("meetingUuid", meetingUuid);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("通过系统会议UUID触发会议报告获取失败", e);

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "触发失败: " + e.getMessage());
            response.put("meetingUuid", meetingUuid);

            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 创建会议报告获取任务
     */
    @PostMapping("/tasks")
    public ResponseEntity<MeetingReportTask> createReportTask(
            @RequestParam String zoomMeetingUuid,
            @RequestParam String zoomMeetingId) {
        
        log.info("创建会议报告获取任务: zoomMeetingUuid={}, zoomMeetingId={}", zoomMeetingUuid, zoomMeetingId);
        
        try {
            MeetingReportTask task = meetingReportService.createReportTask(zoomMeetingUuid, zoomMeetingId);
            return ResponseEntity.ok(task);
        } catch (Exception e) {
            log.error("创建会议报告获取任务失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 检查会议报告是否存在
     */
    @GetMapping("/exists/{zoomMeetingUuid}")
    public ResponseEntity<Map<String, Object>> checkReportExists(@PathVariable String zoomMeetingUuid) {
        log.debug("检查会议报告是否存在: zoomMeetingUuid={}", zoomMeetingUuid);
        
        boolean exists = meetingReportService.reportExists(zoomMeetingUuid);
        
        Map<String, Object> response = new HashMap<>();
        response.put("exists", exists);
        response.put("zoomMeetingUuid", zoomMeetingUuid);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 删除会议报告
     */
    @DeleteMapping("/{reportId}")
    public ResponseEntity<Map<String, Object>> deleteReport(@PathVariable Long reportId) {
        log.info("删除会议报告: reportId={}", reportId);
        
        try {
            meetingReportService.deleteReport(reportId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会议报告删除成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除会议报告失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量获取会议报告
     */
    @PostMapping("/batch")
    public ResponseEntity<List<MeetingReport>> getBatchReports(@RequestBody List<String> meetingUuids) {
        log.info("批量获取会议报告: count={}", meetingUuids.size());

        try {
            List<MeetingReport> reports = new ArrayList<>();
            for (String uuid : meetingUuids) {
                meetingReportService.getReportByUuid(uuid).ifPresent(reports::add);
            }

            return ResponseEntity.ok(reports);
        } catch (Exception e) {
            log.error("批量获取会议报告失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 导出会议报告（Excel格式）
     */
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportReports(
            @RequestParam(required = false) String hostId,
            @RequestParam(required = false) Long pmiRecordId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) MeetingReport.FetchStatus fetchStatus) {

        log.info("导出会议报告: hostId={}, pmiRecordId={}, startTime={}, endTime={}",
                hostId, pmiRecordId, startTime, endTime);

        try {
            // 获取所有符合条件的报告
            Pageable pageable = PageRequest.of(0, 10000, Sort.by(Sort.Direction.DESC, "startTime"));
            Page<MeetingReport> reports = meetingReportService.getReportsWithFilters(
                hostId, pmiRecordId, startTime, endTime, keyword, fetchStatus, pageable);

            // 这里应该实现Excel导出逻辑
            // byte[] excelData = excelExportService.exportMeetingReports(reports.getContent());

            // 临时返回JSON格式
            String jsonData = "会议报告导出功能待实现";
            byte[] data = jsonData.getBytes();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "meeting_reports.xlsx");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(data);

        } catch (Exception e) {
            log.error("导出会议报告失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取会议报告概览统计
     */
    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getReportsOverview(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {

        log.info("获取会议报告概览: startTime={}, endTime={}", startTime, endTime);

        try {
            Map<String, Object> overview = new HashMap<>();

            // 基础统计
            Map<String, Object> statistics = meetingReportService.getReportStatistics(startTime, endTime);
            overview.putAll(statistics);

            // 参会者统计
            Map<String, Object> participantStats = meetingReportService.getParticipantStatistics(null, startTime, endTime);
            overview.put("participantStatistics", participantStats);

            // 最近的报告
            Page<MeetingReport> recentReports = meetingReportService.getRecentReports(PageRequest.of(0, 5));
            overview.put("recentReports", recentReports.getContent());

            return ResponseEntity.ok(overview);
        } catch (Exception e) {
            log.error("获取会议报告概览失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 重新获取会议报告
     */
    @PostMapping("/refetch/{zoomMeetingUuid}")
    public ResponseEntity<Map<String, Object>> refetchReport(@PathVariable String zoomMeetingUuid) {
        log.info("重新获取会议报告: zoomMeetingUuid={}", zoomMeetingUuid);

        try {
            MeetingReport report = meetingReportService.triggerReportFetch(zoomMeetingUuid);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会议报告重新获取成功");
            response.put("report", report);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("重新获取会议报告失败", e);

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "重新获取失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取会议报告获取任务状态
     */
    @GetMapping("/tasks")
    public ResponseEntity<Page<MeetingReportTask>> getReportTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) MeetingReportTask.TaskStatus taskStatus) {

        log.info("获取会议报告任务列表: page={}, size={}, taskStatus={}", page, size, taskStatus);

        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));

            // 这里需要在MeetingReportService中添加相应的方法
            // Page<MeetingReportTask> tasks = meetingReportService.getReportTasks(taskStatus, pageable);

            // 临时返回空页面
            Page<MeetingReportTask> tasks = Page.empty(pageable);

            return ResponseEntity.ok(tasks);
        } catch (Exception e) {
            log.error("获取会议报告任务列表失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
}
