package com.zoombus.controller;

import com.zoombus.service.DatabaseMigrationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据库迁移管理控制器
 * 提供数据库迁移的管理接口
 */
@RestController
@RequestMapping("/api/migration")
@RequiredArgsConstructor
@Slf4j
public class MigrationController {

    private final DatabaseMigrationService migrationService;

    /**
     * 获取迁移状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getMigrationStatus() {
        try {
            DatabaseMigrationService.MigrationStatusInfo status = migrationService.getMigrationStatus();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "currentVersion", status.currentVersion,
                "hasPendingMigrations", status.hasPendingMigrations,
                "allMigrations", status.allMigrations,
                "pendingMigrations", status.pendingMigrations,
                "totalMigrations", status.allMigrations.size(),
                "pendingCount", status.pendingMigrations.size()
            ));
        } catch (Exception e) {
            log.error("获取迁移状态失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取迁移状态失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 手动执行数据库迁移
     */
    @PostMapping("/execute")
    public ResponseEntity<Map<String, Object>> executeMigration() {
        try {
            log.info("收到手动执行数据库迁移请求");
            
            DatabaseMigrationService.MigrationResult result = migrationService.manualMigrate();
            
            return ResponseEntity.ok(Map.of(
                "success", result.success,
                "message", result.message,
                "migrationsExecuted", result.migrationsExecuted,
                "beforeVersion", result.beforeVersion,
                "afterVersion", result.afterVersion
            ));
        } catch (Exception e) {
            log.error("手动执行数据库迁移失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "执行迁移失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 检查是否有待执行的迁移
     */
    @GetMapping("/pending")
    public ResponseEntity<Map<String, Object>> checkPendingMigrations() {
        try {
            DatabaseMigrationService.MigrationStatusInfo status = migrationService.getMigrationStatus();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "hasPendingMigrations", status.hasPendingMigrations,
                "pendingCount", status.pendingMigrations.size(),
                "pendingMigrations", status.pendingMigrations
            ));
        } catch (Exception e) {
            log.error("检查待执行迁移失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "检查待执行迁移失败: " + e.getMessage()
            ));
        }
    }
}
