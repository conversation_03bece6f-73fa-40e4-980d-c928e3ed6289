package com.zoombus.controller;

import com.zoombus.dto.MonitoringData;
import com.zoombus.service.MonitoringService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 监控数据API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/monitoring")
@RequiredArgsConstructor
public class MonitoringController {
    
    private final MonitoringService monitoringService;
    
    /**
     * 获取Zoom账号状态
     */
    @GetMapping("/zoom-account")
    public ResponseEntity<Map<String, Object>> getZoomAccountStatus() {
        try {
            MonitoringData.ZoomAccountStatus status = monitoringService.collectZoomAccountStatus();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", status);
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取Zoom账号状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取Zoom账号状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取会议状态
     */
    @GetMapping("/meeting")
    public ResponseEntity<Map<String, Object>> getMeetingStatus() {
        try {
            MonitoringData.MeetingStatus status = monitoringService.collectMeetingStatus();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", status);
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取会议状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取会议状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取PMI状态
     */
    @GetMapping("/pmi")
    public ResponseEntity<Map<String, Object>> getPmiStatus() {
        try {
            MonitoringData.PmiStatus status = monitoringService.collectPmiStatus();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", status);
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取PMI状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取PMI状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取系统性能
     */
    @GetMapping("/system")
    public ResponseEntity<Map<String, Object>> getSystemPerformance() {
        try {
            MonitoringData.SystemPerformance performance = monitoringService.collectSystemPerformance();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", performance);
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取系统性能失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取系统性能失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取综合监控数据
     */
    @GetMapping("/comprehensive")
    public ResponseEntity<Map<String, Object>> getComprehensiveData() {
        try {
            Map<String, Object> comprehensiveData = new HashMap<>();
            
            // 收集所有监控数据
            MonitoringData.ZoomAccountStatus zoomStatus = monitoringService.collectZoomAccountStatus();
            MonitoringData.MeetingStatus meetingStatus = monitoringService.collectMeetingStatus();
            MonitoringData.PmiStatus pmiStatus = monitoringService.collectPmiStatus();
            MonitoringData.SystemPerformance systemPerformance = monitoringService.collectSystemPerformance();
            
            comprehensiveData.put("zoomAccount", zoomStatus);
            comprehensiveData.put("meeting", meetingStatus);
            comprehensiveData.put("pmi", pmiStatus);
            comprehensiveData.put("system", systemPerformance);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", comprehensiveData);
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取综合监控数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取综合监控数据失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取历史趋势数据
     */
    @GetMapping("/trends")
    public ResponseEntity<Map<String, Object>> getTrendData(
            @RequestParam(defaultValue = "24") int hours) {
        try {
            // 这里可以实现历史数据查询逻辑
            // 从数据库或时序数据库中查询历史数据
            
            Map<String, Object> trendData = new HashMap<>();
            trendData.put("zoomUsageTrend", new Object[]{}); // 模拟数据
            trendData.put("meetingCountTrend", new Object[]{}); // 模拟数据
            trendData.put("pmiUsageTrend", new Object[]{}); // 模拟数据
            trendData.put("systemPerformanceTrend", new Object[]{}); // 模拟数据
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", trendData);
            response.put("hours", hours);
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取趋势数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取趋势数据失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 手动触发数据收集
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshData() {
        try {
            log.info("手动触发监控数据收集");
            
            // 强制刷新所有监控数据
            MonitoringData.ZoomAccountStatus zoomStatus = monitoringService.collectZoomAccountStatus();
            MonitoringData.MeetingStatus meetingStatus = monitoringService.collectMeetingStatus();
            MonitoringData.PmiStatus pmiStatus = monitoringService.collectPmiStatus();
            MonitoringData.SystemPerformance systemPerformance = monitoringService.collectSystemPerformance();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "监控数据刷新成功");
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("刷新监控数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "刷新监控数据失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
