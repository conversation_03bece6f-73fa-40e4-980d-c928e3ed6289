package com.zoombus.controller;

import com.zoombus.service.NetworkEnvironmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 网络环境管理控制器
 * 提供网络环境检测和代理配置的管理接口
 */
@RestController
@RequestMapping("/api/network")
@RequiredArgsConstructor
@Slf4j
public class NetworkEnvironmentController {

    private final NetworkEnvironmentService networkEnvironmentService;

    /**
     * 获取当前网络环境信息
     */
    @GetMapping("/environment")
    public ResponseEntity<Map<String, Object>> getNetworkEnvironment() {
        try {
            Map<String, Object> response = new HashMap<>();
            
            // 基本信息
            response.put("currentExternalIp", networkEnvironmentService.getCurrentExternalIp());
            response.put("proxyConfigured", networkEnvironmentService.isProxyConfigured());
            response.put("proxyInfo", networkEnvironmentService.getProxyInfo());
            
            // 系统代理配置
            Map<String, String> systemProxy = new HashMap<>();
            systemProxy.put("httpProxyHost", System.getProperty("http.proxyHost"));
            systemProxy.put("httpProxyPort", System.getProperty("http.proxyPort"));
            systemProxy.put("httpsProxyHost", System.getProperty("https.proxyHost"));
            systemProxy.put("httpsProxyPort", System.getProperty("https.proxyPort"));
            systemProxy.put("nonProxyHosts", System.getProperty("http.nonProxyHosts"));
            response.put("systemProxy", systemProxy);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取网络环境信息失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取网络环境信息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 重新检测外网IP
     */
    @PostMapping("/detect-ip")
    public ResponseEntity<Map<String, Object>> detectExternalIp() {
        try {
            log.info("手动触发外网IP检测");
            String externalIp = networkEnvironmentService.detectExternalIp();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("externalIp", externalIp);
            response.put("message", externalIp != null ? "检测成功" : "检测失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检测外网IP失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "检测外网IP失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 配置代理
     */
    @PostMapping("/configure-proxy")
    public ResponseEntity<Map<String, Object>> configureProxy() {
        try {
            log.info("手动触发代理配置");
            networkEnvironmentService.configureZoomApiProxy();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "代理配置完成",
                "proxyInfo", networkEnvironmentService.getProxyInfo()
            ));
            
        } catch (Exception e) {
            log.error("配置代理失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "配置代理失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 清除代理配置
     */
    @PostMapping("/clear-proxy")
    public ResponseEntity<Map<String, Object>> clearProxy() {
        try {
            log.info("手动清除代理配置");
            networkEnvironmentService.clearProxyConfiguration();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "代理配置已清除",
                "proxyInfo", networkEnvironmentService.getProxyInfo()
            ));
            
        } catch (Exception e) {
            log.error("清除代理配置失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "清除代理配置失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 测试网络连接
     */
    @PostMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection(@RequestParam String url) {
        try {
            log.info("测试网络连接: {}", url);
            boolean connected = networkEnvironmentService.testNetworkConnection(url);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "connected", connected,
                "url", url,
                "message", connected ? "连接成功" : "连接失败"
            ));
            
        } catch (Exception e) {
            log.error("测试网络连接失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "connected", false,
                "url", url,
                "message", "测试失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 测试Zoom API连接
     */
    @PostMapping("/test-zoom-api")
    public ResponseEntity<Map<String, Object>> testZoomApiConnection() {
        try {
            log.info("测试Zoom API连接");
            
            Map<String, Object> results = new HashMap<>();
            
            // 测试多个Zoom相关的URL
            String[] zoomUrls = {
                "https://api.zoom.us",
                "https://zoom.us",
                "https://marketplace.zoom.us"
            };
            
            for (String url : zoomUrls) {
                boolean connected = networkEnvironmentService.testNetworkConnection(url);
                results.put(url, connected);
            }
            
            // 计算总体连接状态
            boolean allConnected = results.values().stream()
                .allMatch(result -> (Boolean) result);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "allConnected", allConnected,
                "results", results,
                "message", allConnected ? "所有Zoom服务连接正常" : "部分Zoom服务连接失败"
            ));
            
        } catch (Exception e) {
            log.error("测试Zoom API连接失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "测试失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取网络诊断信息
     */
    @GetMapping("/diagnosis")
    public ResponseEntity<Map<String, Object>> getNetworkDiagnosis() {
        try {
            Map<String, Object> diagnosis = new HashMap<>();
            
            // 基本网络信息
            diagnosis.put("externalIp", networkEnvironmentService.getCurrentExternalIp());
            diagnosis.put("proxyConfigured", networkEnvironmentService.isProxyConfigured());
            
            // 系统属性
            Map<String, String> systemProperties = new HashMap<>();
            systemProperties.put("java.net.useSystemProxies", System.getProperty("java.net.useSystemProxies"));
            systemProperties.put("http.proxyHost", System.getProperty("http.proxyHost"));
            systemProperties.put("http.proxyPort", System.getProperty("http.proxyPort"));
            systemProperties.put("https.proxyHost", System.getProperty("https.proxyHost"));
            systemProperties.put("https.proxyPort", System.getProperty("https.proxyPort"));
            systemProperties.put("http.nonProxyHosts", System.getProperty("http.nonProxyHosts"));
            systemProperties.put("https.nonProxyHosts", System.getProperty("https.nonProxyHosts"));
            diagnosis.put("systemProperties", systemProperties);
            
            // 环境变量
            Map<String, String> envVars = new HashMap<>();
            envVars.put("HTTP_PROXY", System.getenv("HTTP_PROXY"));
            envVars.put("HTTPS_PROXY", System.getenv("HTTPS_PROXY"));
            envVars.put("NO_PROXY", System.getenv("NO_PROXY"));
            envVars.put("http_proxy", System.getenv("http_proxy"));
            envVars.put("https_proxy", System.getenv("https_proxy"));
            envVars.put("no_proxy", System.getenv("no_proxy"));
            diagnosis.put("environmentVariables", envVars);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "diagnosis", diagnosis
            ));
            
        } catch (Exception e) {
            log.error("获取网络诊断信息失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取诊断信息失败: " + e.getMessage()
            ));
        }
    }
}
