package com.zoombus.controller;

import com.zoombus.dto.PmiBillingRecordDTO;
import com.zoombus.entity.PmiBillingRecord;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.User;
import com.zoombus.repository.PmiBillingRecordRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.UserRepository;
import com.zoombus.service.BillingMonitorService;
import com.zoombus.service.MeetingSettlementService;
import com.zoombus.service.ZoomUserPmiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * PMI计费管理控制器
 */
@RestController
@RequestMapping("/api/pmi-billing")
@RequiredArgsConstructor
@Slf4j
public class PmiBillingController {

    private final PmiBillingRecordRepository billingRecordRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final UserRepository userRepository;
    private final BillingMonitorService billingMonitorService;
    private final MeetingSettlementService meetingSettlementService;
    private final ZoomUserPmiService zoomUserPmiService;
    
    /**
     * 获取计费记录列表
     */
    @GetMapping("/records")
    public ResponseEntity<Map<String, Object>> getBillingRecords(
            @RequestParam(required = false) Long pmiRecordId,
            @RequestParam(required = false) String pmiNumber,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Pageable pageable) {
        try {
            PmiBillingRecord.TransactionType type = null;
            if (transactionType != null && !transactionType.isEmpty()) {
                type = PmiBillingRecord.TransactionType.valueOf(transactionType);
            }

            PmiBillingRecord.RecordStatus recordStatus = null;
            if (status != null && !status.isEmpty()) {
                recordStatus = PmiBillingRecord.RecordStatus.valueOf(status);
            }

            // 如果提供了PMI号码，先查找对应的PMI记录ID
            if (pmiNumber != null && !pmiNumber.trim().isEmpty()) {
                Optional<PmiRecord> pmiRecord = pmiRecordRepository.findByPmiNumber(pmiNumber.trim());
                if (pmiRecord.isPresent()) {
                    pmiRecordId = pmiRecord.get().getId();
                } else {
                    // PMI号码不存在，返回空结果
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("data", Page.empty(pageable));
                    return ResponseEntity.ok(response);
                }
            }

            Page<PmiBillingRecord> records = billingRecordRepository.findRecordsWithFilters(
                pmiRecordId, userId, type, recordStatus, startDate, endDate, pageable);

            // 转换为DTO，包含PMI号码和用户名称
            List<PmiBillingRecordDTO> dtoList = records.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

            Page<PmiBillingRecordDTO> dtoPage = new PageImpl<>(dtoList, pageable, records.getTotalElements());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", dtoPage);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取计费记录失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 将PmiBillingRecord转换为DTO
     */
    private PmiBillingRecordDTO convertToDTO(PmiBillingRecord record) {
        // 获取PMI号码
        String pmiNumber = "";
        if (record.getPmiRecordId() != null) {
            Optional<PmiRecord> pmiRecord = pmiRecordRepository.findById(record.getPmiRecordId());
            if (pmiRecord.isPresent()) {
                pmiNumber = pmiRecord.get().getPmiNumber();
            }
        }

        // 获取用户名称和邮箱
        String userName = "";
        String userEmail = "";
        if (record.getUserId() != null) {
            Optional<User> user = userRepository.findById(record.getUserId());
            if (user.isPresent()) {
                userName = user.get().getFullName() != null ? user.get().getFullName() : user.get().getUsername();
                userEmail = user.get().getEmail();
            }
        }

        return PmiBillingRecordDTO.fromEntity(record, pmiNumber, userName, userEmail);
    }
    
    /**
     * 获取PMI的计费记录
     */
    @GetMapping("/records/pmi/{pmiRecordId}")
    public ResponseEntity<Map<String, Object>> getPmiBillingRecords(
            @PathVariable Long pmiRecordId,
            Pageable pageable) {
        try {
            Page<PmiBillingRecord> records = billingRecordRepository.findRecentRecordsByPmiRecordId(pmiRecordId, pageable);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", records);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取PMI计费记录失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取计费监控状态
     */
    @GetMapping("/monitor/status")
    public ResponseEntity<Map<String, Object>> getBillingMonitorStatus() {
        try {
            BillingMonitorService.BillingMonitorStatus status = billingMonitorService.getBillingMonitorStatus();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", status);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取计费监控状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 同步计费监控状态
     */
    @PostMapping("/monitor/sync")
    public ResponseEntity<Map<String, Object>> syncBillingMonitorStatus() {
        try {
            billingMonitorService.syncBillingMonitorStatus();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "计费监控状态已同步");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("同步计费监控状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "同步失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 批量结算会议
     */
    @PostMapping("/settlement/batch")
    public ResponseEntity<Map<String, Object>> batchSettleMeetings() {
        try {
            MeetingSettlementService.BatchSettlementResult result = meetingSettlementService.batchSettleMeetings();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            response.put("message", String.format("批量结算完成: 成功 %d, 失败 %d", 
                result.getSuccessCount(), result.getFailureCount()));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量结算会议失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量结算失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取ZoomUser使用统计
     */
    @GetMapping("/zoom-users/stats")
    public ResponseEntity<Map<String, Object>> getZoomUserStats() {
        try {
            ZoomUserPmiService.ZoomUserUsageStats stats = zoomUserPmiService.getZoomUserUsageStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取ZoomUser统计失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 强制释放所有ZoomUser
     */
    @PostMapping("/zoom-users/force-release")
    public ResponseEntity<Map<String, Object>> forceReleaseAllZoomUsers() {
        try {
            zoomUserPmiService.forceReleaseAllZoomUsers();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "已强制释放所有ZoomUser");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("强制释放ZoomUser失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "释放失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取计费统计数据
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getBillingStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            if (startDate == null) {
                startDate = LocalDateTime.now().minusDays(30); // 默认最近30天
            }
            if (endDate == null) {
                endDate = LocalDateTime.now();
            }
            
            var statistics = billingRecordRepository.getTransactionStatistics(startDate, endDate);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            response.put("period", Map.of("startDate", startDate, "endDate", endDate));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取计费统计失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
