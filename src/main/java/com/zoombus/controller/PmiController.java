package com.zoombus.controller;

import com.zoombus.dto.CreatePmiRequest;
import com.zoombus.dto.PmiRecordDTO;
import com.zoombus.dto.UsePmiRequest;
import com.zoombus.entity.PmiRecord;
import com.zoombus.service.PmiService;
import com.zoombus.service.PmiBillingModeService;
import com.zoombus.service.PmiRechargeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * PMI管理控制器
 */
@RestController
@RequestMapping("/api/pmi")
@RequiredArgsConstructor
@Slf4j
public class PmiController {
    
    private final PmiService pmiService;
    private final PmiBillingModeService pmiBillingModeService;
    private final PmiRechargeService pmiRechargeService;
    
    /**
     * 生成PMI
     */
    @PostMapping("/generate")
    public ResponseEntity<Map<String, Object>> generatePmi(
            @Valid @RequestBody CreatePmiRequest request,
            HttpServletRequest httpRequest) {
        try {
            log.info("收到生成PMI请求: {}", request);

            // 获取当前请求的域名
            String baseUrl = getBaseUrl(httpRequest);

            PmiRecord pmiRecord = pmiService.generatePmi(request.getUserId(), request.getCustomPassword(), baseUrl);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "PMI生成成功");
            response.put("data", pmiRecord);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("生成PMI失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "生成PMI失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 使用PMI开启会议室
     */
    @PostMapping("/use")
    public ResponseEntity<Map<String, Object>> usePmi(@Valid @RequestBody UsePmiRequest request) {
        try {
            log.info("收到使用PMI请求: {}", request);
            
            String hostUrl = pmiService.usePmi(request.getPmiNumber());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "PMI设置成功");
            response.put("hostUrl", hostUrl);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("使用PMI失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "使用PMI失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取所有PMI记录（分页）
     */
    @GetMapping
    public ResponseEntity<Page<PmiRecord>> getAllPmiRecords(
            @RequestParam(required = false) String billingMode,
            Pageable pageable) {
        Page<PmiRecord> pmiRecords = pmiService.getAllPmiRecords(billingMode, pageable);
        // 确保返回的数据包含窗口到期时间信息
        return ResponseEntity.ok(pmiRecords);
    }
    
    /**
     * 根据用户ID获取PMI记录（分页）
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Page<PmiRecord>> getUserPmiRecords(
            @PathVariable Long userId,
            @RequestParam(required = false) String billingMode,
            Pageable pageable) {
        Page<PmiRecord> pmiRecords = pmiService.getUserPmiRecords(userId, billingMode, pageable);
        return ResponseEntity.ok(pmiRecords);
    }
    
    /**
     * 根据ID获取PMI记录
     */
    @GetMapping("/{id}")
    public ResponseEntity<PmiRecordDTO> getPmiRecordById(@PathVariable Long id) {
        return pmiService.getPmiRecordWithUserInfo(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据PMI号码获取记录
     */
    @GetMapping("/number/{pmiNumber}")
    public ResponseEntity<PmiRecord> getPmiRecordByNumber(@PathVariable String pmiNumber) {
        return pmiService.getPmiRecordByNumber(pmiNumber)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 搜索PMI记录
     */
    @GetMapping("/search")
    public ResponseEntity<Page<PmiRecord>> searchPmiRecords(
            @RequestParam String keyword,
            @RequestParam(required = false) String billingMode,
            Pageable pageable) {
        Page<PmiRecord> pmiRecords = pmiService.searchPmiRecords(keyword, billingMode, pageable);
        return ResponseEntity.ok(pmiRecords);
    }
    
    /**
     * 获取PMI复制信息
     */
    @GetMapping("/{id}/copy-text")
    public ResponseEntity<Map<String, Object>> getPmiCopyText(@PathVariable Long id) {
        try {
            String copyText = pmiService.generatePmiCopyText(id);
            Optional<PmiRecord> pmiRecordOpt = pmiService.getPmiRecordById(id);

            if (!pmiRecordOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "PMI记录不存在");
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("copyText", copyText);
            response.put("pmiRecord", pmiRecordOpt.get());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取PMI复制信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取复制信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 更新PMI状态
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<PmiRecord> updatePmiStatus(@PathVariable Long id, 
                                                   @RequestParam PmiRecord.PmiStatus status) {
        try {
            PmiRecord pmiRecord = pmiService.updatePmiStatus(id, status);
            return ResponseEntity.ok(pmiRecord);
        } catch (Exception e) {
            log.error("更新PMI状态失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 删除PMI记录
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deletePmiRecord(@PathVariable Long id) {
        try {
            pmiService.deletePmiRecord(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "PMI记录删除成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("删除PMI记录失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除PMI记录失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取PMI统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getPmiStats() {
        try {
            long totalActive = pmiService.countActivePmiRecords();
            long inMeeting = pmiService.countPmiInMeeting();

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalActive", totalActive);
            stats.put("inMeeting", inMeeting);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取PMI统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取用户PMI统计信息
     */
    @GetMapping("/user/{userId}/stats")
    public ResponseEntity<Map<String, Object>> getUserPmiStats(@PathVariable Long userId) {
        try {
            long userPmiCount = pmiService.countUserPmiRecords(userId);

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", userPmiCount);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取用户PMI统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取用户统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 手动更新PMI窗口信息
     * 用于维护 current_window_id 和 window_expire_time 字段
     */
    @PostMapping("/update-window-info")
    public ResponseEntity<Map<String, Object>> updatePmiWindowInfo() {
        try {
            // 这里需要注入 PmiBillingModeService
            // pmiBillingModeService.updateAllLongModePmiWindowInfo();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "PMI窗口信息更新完成");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("更新PMI窗口信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新PMI窗口信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取当前请求的基础URL
     */
    private String getBaseUrl(HttpServletRequest request) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();

        StringBuilder baseUrl = new StringBuilder();
        baseUrl.append(scheme).append("://").append(serverName);

        // 只有在非标准端口时才添加端口号
        if ((scheme.equals("http") && serverPort != 80) ||
            (scheme.equals("https") && serverPort != 443)) {
            baseUrl.append(":").append(serverPort);
        }

        return baseUrl.toString();
    }

    // ========== 计费相关API ==========

    /**
     * 切换到按时段计费模式
     */
    @PostMapping("/{pmiRecordId}/billing/switch-to-long")
    public ResponseEntity<Map<String, Object>> switchToLongBilling(
            @PathVariable Long pmiRecordId,
            @RequestParam Long windowId,
            @RequestParam String expireTime) {
        try {
            java.time.LocalDateTime expireDateTime = java.time.LocalDateTime.parse(expireTime);
            pmiBillingModeService.switchToLongBilling(pmiRecordId, windowId, expireDateTime);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "已切换到按时段计费模式");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("切换到按时段计费失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "切换失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 切换到按时长计费模式
     */
    @PostMapping("/{pmiRecordId}/billing/switch-to-time")
    public ResponseEntity<Map<String, Object>> switchToTimeBilling(@PathVariable Long pmiRecordId) {
        try {
            pmiBillingModeService.switchToTimeBilling(pmiRecordId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "已切换到按时长计费模式");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("切换到按时长计费失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "切换失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 检查PMI是否可以开启会议
     */
    @GetMapping("/{pmiRecordId}/billing/can-start-meeting")
    public ResponseEntity<Map<String, Object>> canStartMeeting(@PathVariable Long pmiRecordId) {
        try {
            boolean canStart = pmiBillingModeService.canStartMeeting(pmiRecordId);
            String info = pmiBillingModeService.getBillingModeInfo(pmiRecordId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("canStartMeeting", canStart);
            response.put("billingInfo", info);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("检查会议开启权限失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * PMI充值预览
     */
    @GetMapping("/{pmiRecordId}/recharge/preview")
    public ResponseEntity<Map<String, Object>> getRechargePreview(
            @PathVariable Long pmiRecordId,
            @RequestParam int minutes) {
        try {
            PmiRechargeService.RechargePreview preview = pmiRechargeService.getRechargePreview(pmiRecordId, minutes);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", preview);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取充值预览失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取预览失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * PMI充值
     */
    @PostMapping("/{pmiRecordId}/recharge")
    public ResponseEntity<Map<String, Object>> rechargePmi(
            @PathVariable Long pmiRecordId,
            @RequestParam int minutes,
            @RequestParam(required = false) String description) {
        try {
            PmiRechargeService.RechargeResult result = pmiRechargeService.rechargePmi(pmiRecordId, minutes, description);

            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("data", result.getAllocation());
            response.put("billingRecord", result.getBillingRecord());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("PMI充值失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "充值失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
