package com.zoombus.controller;

import com.zoombus.service.PmiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PMI回退管理控制器
 */
@RestController
@RequestMapping("/api/admin/pmi")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
public class PmiFallbackController {

    private final PmiService pmiService;

    /**
     * 设置单个PMI的回退状态
     */
    @PutMapping("/{pmiId}/fallback")
    public ResponseEntity<Map<String, Object>> setPmiFallbackStatus(
            @PathVariable Long pmiId,
            @RequestBody Map<String, Object> request) {
        try {
            Boolean fallbackEnabled = (Boolean) request.get("fallbackEnabled");
            if (fallbackEnabled == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "fallbackEnabled参数不能为空"
                ));
            }

            pmiService.setPmiFallbackStatus(pmiId, fallbackEnabled);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "PMI回退状态设置成功");
            response.put("data", Map.of(
                "pmiId", pmiId,
                "fallbackEnabled", fallbackEnabled
            ));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("设置PMI回退状态失败: pmiId={}", pmiId, e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "设置PMI回退状态失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 批量设置PMI回退状态
     */
    @PutMapping("/batch-fallback")
    public ResponseEntity<Map<String, Object>> batchSetPmiFallbackStatus(
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> pmiIds = (List<Long>) request.get("pmiIds");
            Boolean fallbackEnabled = (Boolean) request.get("fallbackEnabled");

            if (pmiIds == null || pmiIds.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "pmiIds参数不能为空"
                ));
            }

            if (fallbackEnabled == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "fallbackEnabled参数不能为空"
                ));
            }

            pmiService.batchSetPmiFallbackStatus(pmiIds, fallbackEnabled);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "批量设置PMI回退状态成功");
            response.put("data", Map.of(
                "pmiIds", pmiIds,
                "fallbackEnabled", fallbackEnabled,
                "count", pmiIds.size()
            ));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("批量设置PMI回退状态失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "批量设置PMI回退状态失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 检查PMI是否需要回退
     */
    @GetMapping("/{pmiNumber}/fallback-status")
    public ResponseEntity<Map<String, Object>> checkPmiFallbackStatus(@PathVariable String pmiNumber) {
        try {
            boolean shouldFallback = pmiService.shouldFallback(pmiNumber);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "pmiNumber", pmiNumber,
                "shouldFallback", shouldFallback
            ));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("检查PMI回退状态失败: pmiNumber={}", pmiNumber, e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "检查PMI回退状态失败: " + e.getMessage()
            ));
        }
    }
}
