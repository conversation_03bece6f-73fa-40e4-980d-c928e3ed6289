package com.zoombus.controller;

import com.zoombus.entity.MeetingReport;
import com.zoombus.service.MeetingReportService;
import com.zoombus.service.PmiReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PMI报告API控制器
 * 提供PMI会议报告的查询和统计功能
 */
@RestController
@RequestMapping("/api/pmi-reports")
@RequiredArgsConstructor
@Slf4j
public class PmiReportController {

    private final PmiReportService pmiReportService;
    private final MeetingReportService meetingReportService;

    /**
     * 获取PMI概览数据
     */
    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getOverview() {
        log.info("获取PMI概览数据");
        
        try {
            Map<String, Object> overview = pmiReportService.getPmiOverview();
            return ResponseEntity.ok(overview);
        } catch (Exception e) {
            log.error("获取PMI概览数据失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取PMI列表（支持分页和筛选）
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getPmiList(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "lastUsedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String pmiNumber,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long zoomAuthId) {
        
        log.info("获取PMI列表 - page: {}, size: {}, sortBy: {}, sortDir: {}", page, size, sortBy, sortDir);
        
        try {
            // 获取PMI概览数据
            Map<String, Object> result = pmiReportService.getPmiOverview();
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取PMI列表失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 测试端点
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> test() {
        log.info("测试端点被调用");
        return ResponseEntity.ok(Map.of(
            "message", "测试成功",
            "timestamp", System.currentTimeMillis()
        ));
    }

    /**
     * 获取PMI统计数据
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getPmiStats() {
        log.info("获取PMI统计数据");

        try {
            Map<String, Object> stats = pmiReportService.getPmiStatsOverview();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取PMI统计数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", e.getMessage()));
        }
    }



    /**
     * 获取特定PMI的详细信息
     */
    @GetMapping("/{pmiNumber}")
    public ResponseEntity<Map<String, Object>> getPmiDetails(@PathVariable String pmiNumber) {
        log.info("获取PMI详细信息: {}", pmiNumber);
        
        try {
            Map<String, Object> details = pmiReportService.getPmiDetails(pmiNumber);
            if (details.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(details);
        } catch (Exception e) {
            log.error("获取PMI详细信息失败: {}", pmiNumber, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取特定PMI的会议历史
     */
    @GetMapping("/{pmiNumber}/meetings")
    public ResponseEntity<Map<String, Object>> getPmiMeetings(
            @PathVariable String pmiNumber,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "startTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        log.info("获取PMI会议历史: {} - page: {}, size: {}", pmiNumber, page, size);
        
        try {
            // 创建分页和排序参数
            Sort.Direction direction = "desc".equalsIgnoreCase(sortDir) ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            Map<String, Object> meetings = pmiReportService.getPmiMeetings(pmiNumber, pageable);
            return ResponseEntity.ok(meetings);
        } catch (Exception e) {
            log.error("获取PMI会议历史失败: {}", pmiNumber, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 更新PMI统计数据
     */
    @PostMapping("/update-stats")
    public ResponseEntity<Map<String, String>> updatePmiStats(
            @RequestParam(required = false) String pmiNumber) {
        
        if (pmiNumber != null) {
            log.info("更新特定PMI统计数据: {}", pmiNumber);
        } else {
            log.info("更新所有PMI统计数据");
        }
        
        try {
            if (pmiNumber != null) {
                pmiReportService.updatePmiStats(pmiNumber);
            } else {
                pmiReportService.updateAllPmiStats();
            }
            
            return ResponseEntity.ok(Map.of(
                "message", pmiNumber != null ? 
                    "PMI统计数据更新成功: " + pmiNumber : "所有PMI统计数据更新成功",
                "status", "success"
            ));
        } catch (Exception e) {
            log.error("更新PMI统计数据失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "message", "更新PMI统计数据失败: " + e.getMessage(),
                "status", "error"
            ));
        }
    }

    /**
     * 获取PMI使用趋势数据
     */
    @GetMapping("/trends")
    public ResponseEntity<Map<String, Object>> getPmiTrends(
            @RequestParam(defaultValue = "30") int days,
            @RequestParam(required = false) String pmiNumber) {
        
        log.info("获取PMI使用趋势数据 - days: {}, pmiNumber: {}", days, pmiNumber);
        
        try {
            Map<String, Object> trends = pmiReportService.getPmiTrends(days, pmiNumber);
            return ResponseEntity.ok(trends);
        } catch (Exception e) {
            log.error("获取PMI使用趋势数据失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取最活跃的PMI列表
     */
    @GetMapping("/most-active")
    public ResponseEntity<Map<String, Object>> getMostActivePmis(
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(defaultValue = "30") int days) {
        
        log.info("获取最活跃的PMI列表 - limit: {}, days: {}", limit, days);
        
        try {
            Map<String, Object> activePmis = pmiReportService.getMostActivePmis(limit, days);
            return ResponseEntity.ok(activePmis);
        } catch (Exception e) {
            log.error("获取最活跃的PMI列表失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 导出PMI报告数据
     */
    @GetMapping("/export")
    public ResponseEntity<Map<String, Object>> exportPmiReports(
            @RequestParam(required = false) String pmiNumber,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "json") String format) {
        
        log.info("导出PMI报告数据 - pmiNumber: {}, startDate: {}, endDate: {}, format: {}", 
                pmiNumber, startDate, endDate, format);
        
        try {
            Map<String, Object> exportData = pmiReportService.exportPmiReports(
                pmiNumber, startDate, endDate, format);
            return ResponseEntity.ok(exportData);
        } catch (Exception e) {
            log.error("导出PMI报告数据失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据PMI Record ID获取会议报告列表
     */
    @GetMapping("/pmi-record/{pmiRecordId}/reports")
    public ResponseEntity<Map<String, Object>> getMeetingReportsByPmiRecordId(@PathVariable Long pmiRecordId) {
        log.info("获取PMI Record会议报告: pmiRecordId={}", pmiRecordId);

        try {
            List<MeetingReport> reports = meetingReportService.getMeetingReportsByPmiRecordId(pmiRecordId);

            Map<String, Object> response = new HashMap<>();
            response.put("reports", reports);
            response.put("count", reports.size());
            response.put("hasReports", !reports.isEmpty());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取PMI Record会议报告失败: pmiRecordId={}", pmiRecordId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 根据PMI Record ID手动触发会议报告获取
     */
    @PostMapping("/pmi-record/{pmiRecordId}/fetch-reports")
    public ResponseEntity<Map<String, Object>> triggerReportFetchByPmiRecordId(@PathVariable Long pmiRecordId) {
        log.info("手动触发PMI Record会议报告获取: pmiRecordId={}", pmiRecordId);

        try {
            List<MeetingReport> reports = meetingReportService.triggerReportFetchByPmiRecordId(pmiRecordId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会议报告获取完成");
            response.put("reports", reports);
            response.put("count", reports.size());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("手动触发PMI Record会议报告获取失败: pmiRecordId={}", pmiRecordId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                        "success", false,
                        "error", e.getMessage(),
                        "message", "会议报告获取失败: " + e.getMessage()
                    ));
        }
    }
}
