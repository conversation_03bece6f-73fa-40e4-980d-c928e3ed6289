package com.zoombus.controller;

import com.zoombus.dto.BatchUpdateStatusRequest;
import com.zoombus.dto.PmiScheduleRequest;
import com.zoombus.dto.PmiScheduleResponse;
import com.zoombus.dto.WindowConflictResponse;
import com.zoombus.entity.PmiSchedule;
import com.zoombus.exception.WindowConflictException;
import com.zoombus.service.PmiScheduleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PMI计划管理控制器
 */
@RestController
@RequestMapping("/api/pmi-schedules")
@RequiredArgsConstructor
@Slf4j
public class PmiScheduleController {

    private final PmiScheduleService pmiScheduleService;
    
    /**
     * 创建PMI计划（忽略冲突，合并窗口）
     */
    @PostMapping("/create-with-merge")
    public ResponseEntity<Map<String, Object>> createScheduleWithMerge(@RequestBody PmiScheduleRequest request) {
        try {
            log.info("收到创建PMI计划请求（合并冲突窗口）: {}", request);

            PmiScheduleResponse schedule = pmiScheduleService.createScheduleWithMerge(request);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "计划创建成功，冲突窗口已合并");
            response.put("data", schedule);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("创建PMI计划（合并冲突窗口）失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "创建计划失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 创建PMI计划
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createSchedule(@Valid @RequestBody PmiScheduleRequest request) {
        try {
            log.info("收到创建PMI计划请求: {}", request);

            PmiScheduleResponse schedule = pmiScheduleService.createSchedule(request);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "计划创建成功");
            response.put("data", schedule);

            return ResponseEntity.ok(response);

        } catch (WindowConflictException e) {
            log.warn("创建PMI计划存在窗口冲突: {}", e.getMessage());

            // 返回冲突信息
            WindowConflictResponse conflictResponse = new WindowConflictResponse(
                e.getNewWindows(),
                e.getConflictWindows(),
                e.getConflictNewWindowIndexes()
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("conflict", true);
            response.put("message", e.getMessage());
            response.put("data", conflictResponse);

            return ResponseEntity.status(409).body(response); // 409 Conflict

        } catch (Exception e) {
            log.error("创建PMI计划失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("conflict", false);
            response.put("message", "创建计划失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新PMI计划
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateSchedule(
            @PathVariable Long id, 
            @Valid @RequestBody PmiScheduleRequest request) {
        try {
            log.info("收到更新PMI计划请求: id={}, request={}", id, request);
            
            PmiScheduleResponse schedule = pmiScheduleService.updateSchedule(id, request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "计划更新成功");
            response.put("data", schedule);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("更新PMI计划失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新计划失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 删除PMI计划
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteSchedule(@PathVariable Long id) {
        try {
            log.info("收到删除PMI计划请求: id={}", id);
            
            pmiScheduleService.deleteSchedule(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "计划删除成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("删除PMI计划失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除计划失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取PMI计划详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getSchedule(@PathVariable Long id) {
        try {
            PmiScheduleResponse schedule = pmiScheduleService.getSchedule(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", schedule);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取PMI计划失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取计划失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取PMI记录的计划列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getSchedules(
            @RequestParam(required = false) Long pmiRecordId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<PmiScheduleResponse> schedules = pmiScheduleService.searchSchedules(
                    pmiRecordId, keyword, status, startDate, endDate, pageable);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", schedules);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取PMI计划列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取计划列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 更新计划状态
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> updateScheduleStatus(
            @PathVariable Long id, 
            @RequestParam PmiSchedule.ScheduleStatus status) {
        try {
            log.info("收到更新计划状态请求: id={}, status={}", id, status);
            
            PmiScheduleResponse schedule = pmiScheduleService.updateScheduleStatus(id, status);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "状态更新成功");
            response.put("data", schedule);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("更新计划状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取计划的窗口列表
     */
    @GetMapping("/{id}/windows")
    public ResponseEntity<Map<String, Object>> getScheduleWindows(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "startDateTime") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<PmiScheduleResponse.PmiScheduleWindowResponse> windows = 
                    pmiScheduleService.getScheduleWindows(id, pageable);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", windows);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取计划窗口列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取窗口列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量删除计划
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchDeleteSchedules(@RequestBody List<Long> scheduleIds) {
        try {
            log.info("收到批量删除PMI计划请求: scheduleIds={}", scheduleIds);

            pmiScheduleService.batchDeleteSchedules(scheduleIds);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "批量删除成功，共删除 " + scheduleIds.size() + " 个计划");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("批量删除PMI计划失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量更新计划状态
     */
    @PutMapping("/batch/status")
    public ResponseEntity<Map<String, Object>> batchUpdateStatus(@RequestBody BatchUpdateStatusRequest request) {
        try {
            log.info("收到批量更新计划状态请求: scheduleIds={}, status={}",
                    request.getScheduleIds(), request.getStatus());

            pmiScheduleService.batchUpdateStatus(request.getScheduleIds(), request.getStatus());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "批量更新状态成功，共更新 " + request.getScheduleIds().size() + " 个计划");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("批量更新计划状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量更新状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
