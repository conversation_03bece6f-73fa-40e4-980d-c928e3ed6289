package com.zoombus.controller;

import com.zoombus.dto.PmiScheduleWindowRequest;
import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.service.PmiScheduleWindowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * PMI计划窗口管理控制器
 */
@RestController
@RequestMapping("/api/pmi-schedule-windows")
@RequiredArgsConstructor
@Slf4j
public class PmiScheduleWindowController {
    
    private final PmiScheduleWindowService pmiScheduleWindowService;
    
    /**
     * 更新计划窗口
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateWindow(
            @PathVariable Long id, 
            @Valid @RequestBody PmiScheduleWindowRequest request) {
        try {
            log.info("收到更新计划窗口请求: id={}, request={}", id, request);
            
            PmiScheduleWindow window = pmiScheduleWindowService.updateWindow(id, request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "窗口更新成功");
            response.put("data", window);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("更新计划窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新窗口失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 删除计划窗口
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteWindow(@PathVariable Long id) {
        try {
            log.info("收到删除计划窗口请求: id={}", id);
            
            pmiScheduleWindowService.deleteWindow(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "窗口删除成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("删除计划窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除窗口失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 关闭计划窗口
     */
    @PutMapping("/{id}/close")
    public ResponseEntity<Map<String, Object>> closeWindow(@PathVariable Long id) {
        try {
            log.info("收到关闭计划窗口请求: id={}", id);

            PmiScheduleWindow window = pmiScheduleWindowService.closeWindow(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "窗口关闭成功");
            response.put("data", window);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("关闭计划窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "关闭窗口失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 激活计划窗口（用于测试）
     */
    @PutMapping("/{id}/activate")
    public ResponseEntity<Map<String, Object>> activateWindow(@PathVariable Long id) {
        try {
            log.info("收到激活计划窗口请求: id={}", id);

            PmiScheduleWindow window = pmiScheduleWindowService.activateWindow(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "窗口激活成功");
            response.put("data", window);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("激活计划窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "激活窗口失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 延长计划窗口
     */
    @PutMapping("/{id}/extend")
    public ResponseEntity<Map<String, Object>> extendWindow(
            @PathVariable Long id,
            @RequestParam int minutes) {
        try {
            log.info("收到延长计划窗口请求: id={}, minutes={}", id, minutes);

            PmiScheduleWindow window = pmiScheduleWindowService.extendWindow(id, minutes);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "窗口延长成功");
            response.put("data", window);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("延长计划窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "延长窗口失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    
    /**
     * 获取计划窗口详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getWindow(@PathVariable Long id) {
        try {
            PmiScheduleWindow window = pmiScheduleWindowService.getWindow(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", window);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取计划窗口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取窗口失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
