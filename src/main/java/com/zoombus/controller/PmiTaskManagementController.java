package com.zoombus.controller;

import com.zoombus.dto.ApiResponse;
import com.zoombus.dto.BatchRescheduleRequest;
import com.zoombus.dto.PageResult;
import com.zoombus.dto.PmiScheduledTaskInfo;
import com.zoombus.dto.PmiTaskStatistics;
import com.zoombus.dto.RescheduleRequest;
import com.zoombus.service.PmiTaskManagementService;
import com.zoombus.service.PmiTaskExportService;
import com.zoombus.util.TaskDiagnosticTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * PMI任务管理控制器
 */
@RestController
@RequestMapping("/api/pmi-scheduled-tasks")
@RequiredArgsConstructor
@Slf4j
public class PmiTaskManagementController {

    private final PmiTaskManagementService taskManagementService;
    private final PmiTaskExportService exportService;
    private final TaskDiagnosticTool diagnosticTool;
    
    /**
     * 获取PMI任务列表
     */
    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ApiResponse<PageResult<PmiScheduledTaskInfo>> getPmiTasks(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String taskType) {
        
        log.info("获取PMI任务列表: page={}, size={}, status={}, taskType={}", page, size, status, taskType);
        
        PageResult<PmiScheduledTaskInfo> result = taskManagementService.getPmiTasks(page, size, status, taskType);
        return ApiResponse.success(result);
    }
    
    /**
     * 手动执行PMI任务
     */
    @PostMapping("/{id}/execute")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ApiResponse<String> executePmiTask(@PathVariable Long id) {
        log.info("手动执行PMI任务: taskId={}", id);

        taskManagementService.executePmiTask(id);
        return ApiResponse.success("任务执行成功");
    }
    
    /**
     * 取消PMI任务
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ApiResponse<String> cancelPmiTask(@PathVariable Long id) {
        log.info("取消PMI任务: taskId={}", id);

        taskManagementService.cancelPmiTask(id);
        return ApiResponse.success("任务取消成功");
    }
    
    /**
     * 重新调度PMI任务
     */
    @PutMapping("/{id}/reschedule")
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ApiResponse<String> reschedulePmiTask(
            @PathVariable Long id,
            @Valid @RequestBody RescheduleRequest request) {

        log.info("重新调度PMI任务: taskId={}, newTime={}", id, request.getNewExecuteTime());

        taskManagementService.reschedulePmiTask(id, request);
        return ApiResponse.success("任务重新调度成功");
    }
    
    /**
     * 获取PMI任务统计
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ApiResponse<PmiTaskStatistics> getPmiTaskStatistics() {
        log.info("获取PMI任务统计");
        
        PmiTaskStatistics statistics = taskManagementService.getPmiTaskStatistics();
        return ApiResponse.success(statistics);
    }
    
    /**
     * 获取即将执行的PMI任务
     */
    @GetMapping("/upcoming")
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ApiResponse<List<PmiScheduledTaskInfo>> getUpcomingPmiTasks(
            @RequestParam(defaultValue = "24") int hours) {
        
        log.info("获取即将执行的PMI任务: hours={}", hours);
        
        List<PmiScheduledTaskInfo> tasks = taskManagementService.getUpcomingPmiTasks(hours);
        return ApiResponse.success(tasks);
    }
    
    /**
     * 获取PMI任务历史
     */
    @GetMapping("/history")
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ApiResponse<PageResult<PmiScheduledTaskInfo>> getPmiTaskHistory(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        log.info("获取PMI任务历史: page={}, size={}", page, size);
        
        PageResult<PmiScheduledTaskInfo> result = taskManagementService.getPmiTaskHistory(page, size);
        return ApiResponse.success(result);
    }
    
    /**
     * 获取窗口的任务
     */
    @GetMapping("/window/{windowId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ApiResponse<List<PmiScheduledTaskInfo>> getWindowTasks(@PathVariable Long windowId) {
        log.info("获取窗口任务: windowId={}", windowId);
        
        List<PmiScheduledTaskInfo> tasks = taskManagementService.getWindowTasks(windowId);
        return ApiResponse.success(tasks);
    }
    
    /**
     * 批量取消任务
     */
    @PostMapping("/batch/cancel")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ApiResponse<String> batchCancelTasks(@RequestBody List<Long> taskIds) {
        log.info("批量取消任务: taskIds={}", taskIds);
        
        int cancelledCount = taskManagementService.batchCancelTasks(taskIds);
        return ApiResponse.success("成功取消 " + cancelledCount + " 个任务");
    }
    
    /**
     * 批量重新调度任务
     */
    @PostMapping("/batch/reschedule")
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ApiResponse<String> batchRescheduleTasks(@Valid @RequestBody BatchRescheduleRequest request) {
        log.info("批量重新调度任务: taskIds={}, newTime={}", 
                request.getTaskIds(), request.getNewExecuteTime());
        
        int rescheduledCount = taskManagementService.batchRescheduleTasks(request);
        return ApiResponse.success("成功重新调度 " + rescheduledCount + " 个任务");
    }
    
    /**
     * 获取任务详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ApiResponse<PmiScheduledTaskInfo> getTaskDetail(@PathVariable Long id) {
        log.info("获取任务详情: taskId={}", id);
        
        PmiScheduledTaskInfo task = taskManagementService.getTaskDetail(id);
        return ApiResponse.success(task);
    }

    /**
     * 导出PMI任务数据
     */
    @PostMapping("/export")
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ResponseEntity<byte[]> exportTasks(@RequestBody PmiTaskExportService.ExportRequest request) {
        log.info("导出PMI任务数据: {}", request);

        try {
            byte[] excelData = exportService.exportTasksToExcel(request);

            String filename = "pmi_tasks_" +
                    java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) +
                    ".xlsx";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setContentLength(excelData.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelData);

        } catch (Exception e) {
            log.error("导出PMI任务数据失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 诊断任务问题
     */
    @GetMapping("/{id}/diagnose")
    @PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
    public ApiResponse<Map<String, Object>> diagnoseTask(@PathVariable Long id) {
        log.info("诊断任务: taskId={}", id);

        try {
            Map<String, Object> diagnosis = diagnosticTool.diagnoseTask(id);
            return ApiResponse.success(diagnosis);
        } catch (Exception e) {
            log.error("诊断任务失败: taskId={}", id, e);
            return ApiResponse.error("诊断任务失败: " + e.getMessage());
        }
    }

    /**
     * 尝试修复任务
     */
    @PostMapping("/{id}/repair")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ApiResponse<Map<String, Object>> repairTask(@PathVariable Long id) {
        log.info("尝试修复任务: taskId={}", id);

        try {
            Map<String, Object> result = diagnosticTool.attemptTaskRepair(id);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("修复任务失败: taskId={}", id, e);
            return ApiResponse.error("修复任务失败: " + e.getMessage());
        }
    }
}
