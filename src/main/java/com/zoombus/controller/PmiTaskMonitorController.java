package com.zoombus.controller;

import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import com.zoombus.service.PmiTaskSchedulingMonitorService;
import com.zoombus.service.impl.DynamicTaskManagerImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PMI任务监控控制器
 * 提供任务状态监控和管理接口
 */
@RestController
@RequestMapping("/api/pmi-task-monitor")
@RequiredArgsConstructor
@Slf4j
public class PmiTaskMonitorController {

    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiTaskSchedulingMonitorService monitorService;
    private final DynamicTaskManagerImpl dynamicTaskManager;

    /**
     * 获取任务调度统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getTaskStats() {
        try {
            PmiTaskSchedulingMonitorService.TaskSchedulingStats stats = monitorService.getTaskSchedulingStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            response.put("scheduledTaskCount", dynamicTaskManager.getScheduledTaskCount());
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取长时间处于SCHEDULED状态的任务
     */
    @GetMapping("/stuck-scheduled")
    public ResponseEntity<Map<String, Object>> getStuckScheduledTasks(
            @RequestParam(defaultValue = "10") int minutes) {
        try {
            LocalDateTime threshold = LocalDateTime.now().minusMinutes(minutes);
            List<PmiScheduleWindowTask> stuckTasks = taskRepository.findStuckScheduledTasks(threshold);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stuckTasks);
            response.put("count", stuckTasks.size());
            response.put("threshold", threshold);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取长时间SCHEDULED任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取任务失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取过期的SCHEDULED任务
     */
    @GetMapping("/expired-scheduled")
    public ResponseEntity<Map<String, Object>> getExpiredScheduledTasks() {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<PmiScheduleWindowTask> expiredTasks = taskRepository.findExpiredScheduledTasks(now);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", expiredTasks);
            response.put("count", expiredTasks.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取过期SCHEDULED任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取任务失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取最近的任务列表
     */
    @GetMapping("/recent")
    public ResponseEntity<Map<String, Object>> getRecentTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<PmiScheduleWindowTask> taskPage;
            
            if (status != null && !status.isEmpty()) {
                PmiScheduleWindowTask.TaskStatus taskStatus = PmiScheduleWindowTask.TaskStatus.valueOf(status.toUpperCase());
                taskPage = taskRepository.findByStatus(taskStatus, pageable);
            } else {
                taskPage = taskRepository.findAll(pageable);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", taskPage);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取最近任务列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取任务列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 手动触发检查长时间SCHEDULED任务
     */
    @PostMapping("/check-stuck-scheduled")
    public ResponseEntity<Map<String, Object>> checkStuckScheduledTasks() {
        try {
            log.info("手动触发检查长时间SCHEDULED任务");
            monitorService.checkStuckScheduledTasks();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "检查完成");
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("手动检查长时间SCHEDULED任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 手动触发任务调度健康检查
     */
    @PostMapping("/health-check")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            log.info("手动触发任务调度健康检查");
            monitorService.checkTaskSchedulingHealth();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "健康检查完成");
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("手动健康检查失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "健康检查失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 检查指定任务是否被正确调度
     */
    @GetMapping("/task/{taskId}/scheduling-status")
    public ResponseEntity<Map<String, Object>> checkTaskSchedulingStatus(@PathVariable Long taskId) {
        try {
            PmiScheduleWindowTask task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
            
            boolean isProperlyScheduled = dynamicTaskManager.isTaskProperlyScheduled(task.getTaskKey());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "taskId", taskId,
                "taskKey", task.getTaskKey(),
                "status", task.getStatus(),
                "isProperlyScheduled", isProperlyScheduled,
                "scheduledTime", task.getScheduledTime()
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检查任务调度状态失败: taskId={}", taskId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 强制重新调度指定任务
     */
    @PostMapping("/task/{taskId}/reschedule")
    public ResponseEntity<Map<String, Object>> forceRescheduleTask(@PathVariable Long taskId) {
        try {
            PmiScheduleWindowTask task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
            
            if (task.getStatus() != PmiScheduleWindowTask.TaskStatus.SCHEDULED) {
                throw new IllegalStateException("只能重新调度SCHEDULED状态的任务");
            }
            
            boolean success = dynamicTaskManager.rescheduleStuckTask(task);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "重新调度成功" : "重新调度失败");
            response.put("taskId", taskId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("强制重新调度任务失败: taskId={}", taskId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "重新调度失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
