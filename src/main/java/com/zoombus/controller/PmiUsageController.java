package com.zoombus.controller;

import com.zoombus.entity.PmiRecord;
import com.zoombus.service.PmiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * PMI使用控制器
 * 提供用户通过PMI链接开启会议室的功能
 */
@RestController
@RequestMapping("/pmi")
@RequiredArgsConstructor
@Slf4j
public class PmiUsageController {
    
    private final PmiService pmiService;
    
    /**
     * 通过PMI号码开启会议室
     * 用户访问 /pmi/{pmiNumber} 时会调用此方法
     */
    @GetMapping("/{pmiNumber}")
    public RedirectView startPmiMeeting(@PathVariable String pmiNumber) {
        try {
            log.info("用户尝试开启PMI会议室: {}", pmiNumber);
            
            // 验证PMI号码格式
            if (!isValidPmiNumber(pmiNumber)) {
                log.warn("无效的PMI号码格式: {}", pmiNumber);
                return new RedirectView("/error?message=无效的PMI号码格式");
            }
            
            // 使用PMI开启会议室
            String hostUrl = pmiService.usePmi(pmiNumber);
            
            log.info("PMI会议室开启成功，重定向到主持链接: pmiNumber={}, hostUrl={}", pmiNumber, hostUrl);
            
            // 重定向到Zoom主持链接
            return new RedirectView(hostUrl);
            
        } catch (Exception e) {
            log.error("开启PMI会议室失败: pmiNumber={}, error={}", pmiNumber, e.getMessage(), e);
            return new RedirectView("/error?message=" + e.getMessage());
        }
    }
    
    /**
     * 获取PMI信息（API接口）
     */
    @GetMapping("/api/{pmiNumber}")
    public ResponseEntity<Map<String, Object>> getPmiInfo(@PathVariable String pmiNumber) {
        try {
            log.info("获取PMI信息: {}", pmiNumber);
            
            // 验证PMI号码格式
            if (!isValidPmiNumber(pmiNumber)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "无效的PMI号码格式");
                return ResponseEntity.badRequest().body(response);
            }
            
            Optional<PmiRecord> pmiRecordOpt = pmiService.getPmiRecordByNumber(pmiNumber);
            if (!pmiRecordOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "PMI不存在");
                return ResponseEntity.notFound().build();
            }
            
            PmiRecord pmiRecord = pmiRecordOpt.get();

            // 生成包含用户姓名的复制文本
            String copyText = pmiService.generatePmiCopyText(pmiRecord.getId());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", pmiRecord);
            response.put("copyText", copyText);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取PMI信息失败: pmiNumber={}", pmiNumber, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取PMI信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 启动PMI会议室（API接口）
     */
    @PostMapping("/api/{pmiNumber}/start")
    public ResponseEntity<Map<String, Object>> startPmiMeetingApi(@PathVariable String pmiNumber) {
        try {
            log.info("API启动PMI会议室: {}", pmiNumber);
            
            // 验证PMI号码格式
            if (!isValidPmiNumber(pmiNumber)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "无效的PMI号码格式");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 使用PMI开启会议室
            String hostUrl = pmiService.usePmi(pmiNumber);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "PMI会议室启动成功");
            response.put("hostUrl", hostUrl);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("API启动PMI会议室失败: pmiNumber={}", pmiNumber, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "启动PMI会议室失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 错误页面处理
     */
    @GetMapping("/error")
    public ResponseEntity<Map<String, Object>> handleError(@RequestParam(required = false) String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message != null ? message : "发生未知错误");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
    
    /**
     * 验证PMI号码格式
     */
    private boolean isValidPmiNumber(String pmiNumber) {
        if (pmiNumber == null || pmiNumber.length() != 10) {
            return false;
        }

        // 检查是否全为数字
        if (!pmiNumber.matches("\\d{10}")) {
            return false;
        }

        // 检查首位不能是0或1
        char firstDigit = pmiNumber.charAt(0);
        if (firstDigit == '0' || firstDigit == '1') {
            return false;
        }

        return true;
    }
}
