package com.zoombus.controller;

import com.zoombus.service.PmiWindowTaskRepairService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * PMI窗口任务修复控制器
 */
@RestController
@RequestMapping("/api/pmi-window-task-repair")
@RequiredArgsConstructor
@Slf4j
public class PmiWindowTaskRepairController {
    
    private final PmiWindowTaskRepairService repairService;
    
    /**
     * 检查需要修复的窗口数量
     */
    @GetMapping("/check")
    public ResponseEntity<Map<String, Object>> checkWindowsWithoutTasks() {
        try {
            int count = repairService.countWindowsWithoutTasks();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("windowsWithoutTasksCount", count);
            response.put("message", count > 0 ? 
                    String.format("发现 %d 个窗口没有任务", count) : 
                    "所有窗口都有完整的任务");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("检查窗口任务状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
    
    /**
     * 批量修复没有任务的窗口
     */
    @PostMapping("/repair-all")
    public ResponseEntity<Map<String, Object>> repairAllWindowsWithoutTasks() {
        try {
            int repairedCount = repairService.repairWindowsWithoutTasks();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("repairedCount", repairedCount);
            response.put("message", String.format("成功修复 %d 个窗口的任务", repairedCount));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量修复窗口任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "修复失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
    
    /**
     * 修复单个窗口的任务
     */
    @PostMapping("/repair/{windowId}")
    public ResponseEntity<Map<String, Object>> repairSingleWindow(@PathVariable Long windowId) {
        try {
            repairService.repairSingleWindow(windowId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("windowId", windowId);
            response.put("message", String.format("窗口 %d 的任务修复成功", windowId));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("修复窗口任务失败: windowId={}", windowId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("windowId", windowId);
            response.put("message", "修复失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
}
