package com.zoombus.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.repository.ZoomUserRepository;
import com.zoombus.service.ZoomApiService;
import com.zoombus.service.PmiSetupService;
import com.zoombus.service.SystemConfigService;
import com.zoombus.dto.ZoomApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 公共PMI使用控制器（无需登录）
 */
@RestController
@RequestMapping("/api/public/pmi")
@RequiredArgsConstructor
@Slf4j
public class PublicPmiController {

    private final PmiRecordRepository pmiRecordRepository;
    private final ZoomMeetingRepository zoomMeetingRepository;
    private final ZoomUserRepository zoomUserRepository;
    private final ZoomApiService zoomApiService;
    private final PmiSetupService pmiSetupService;
    private final SystemConfigService systemConfigService;

    // PMI并发控制锁 - 防止同一PMI同时开启多场会议
    private final ConcurrentHashMap<String, Object> pmiLocks = new ConcurrentHashMap<>();
    
    /**
     * 获取PMI基本信息
     * 注意：这里的参数实际上是magicId，为了保持URL兼容性仍命名为pmiNumber
     */
    @GetMapping("/{magicId}")
    public ResponseEntity<Map<String, Object>> getPmiInfo(@PathVariable String magicId) {
        log.info("获取PMI信息: magicId={}", magicId);
        try {
            Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findByMagicId(magicId);

            if (!pmiRecordOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "PMI不存在");
                return ResponseEntity.status(404).body(response);
            }
            
            PmiRecord pmiRecord = pmiRecordOpt.get();

            // 允许所有状态的PMI被查看，但在响应中包含状态信息
            Map<String, Object> pmiInfo = new HashMap<>();
            pmiInfo.put("pmiNumber", pmiRecord.getPmiNumber());
            pmiInfo.put("pmiPassword", pmiRecord.getPmiPassword());
            pmiInfo.put("hostUrl", pmiRecord.getHostUrl());
            pmiInfo.put("joinUrl", pmiRecord.getJoinUrl());
            pmiInfo.put("status", pmiRecord.getStatus());
            pmiInfo.put("statusDescription", pmiRecord.getStatus().getDescription());

            // 检查是否需要回退到老系统
            boolean fallbackEnabled = Boolean.TRUE.equals(pmiRecord.getFallbackEnabled());
            pmiInfo.put("fallbackEnabled", fallbackEnabled);

            if (fallbackEnabled) {
                // 获取老系统域名配置
                String oldDomainBaseUrl = systemConfigService.getConfigValue("user_frontend_old.domain.base_url", "");
                if (!oldDomainBaseUrl.isEmpty()) {
                    String fallbackUrl = oldDomainBaseUrl + "/m/" + pmiRecord.getMagicId();
                    pmiInfo.put("fallbackUrl", fallbackUrl);
                    log.info("PMI {} 启用回退，回退URL: {}", pmiRecord.getMagicId(), fallbackUrl);
                } else {
                    log.warn("PMI {} 启用了回退但老系统域名配置为空", pmiRecord.getMagicId());
                }
            }

            // 添加计费模式相关信息
            pmiInfo.put("billingMode", pmiRecord.getBillingMode());
            pmiInfo.put("billingModeDescription", pmiRecord.getBillingMode().getDescription());

            // 根据计费模式添加相应信息
            if (pmiRecord.getBillingMode() == PmiRecord.BillingMode.BY_TIME) {
                pmiInfo.put("totalMinutes", pmiRecord.getTotalMinutes());
                pmiInfo.put("availableMinutes", pmiRecord.getAvailableMinutes());
                pmiInfo.put("pendingDeductMinutes", pmiRecord.getPendingDeductMinutes());
                pmiInfo.put("overdraftMinutes", pmiRecord.getOverdraftMinutes());
                pmiInfo.put("totalUsedMinutes", pmiRecord.getTotalUsedMinutes());
                // 添加剩余可用时长
                pmiInfo.put("remainingMinutes", pmiRecord.getRemainingMinutes());
            } else if (pmiRecord.getBillingMode() == PmiRecord.BillingMode.LONG) {
                pmiInfo.put("currentWindowId", pmiRecord.getCurrentWindowId());
                pmiInfo.put("windowExpireTime", pmiRecord.getWindowExpireTime());
                // LONG模式下也提供基础的时长信息以便前端显示
                pmiInfo.put("availableMinutes", pmiRecord.getAvailableMinutes());
                pmiInfo.put("pendingDeductMinutes", pmiRecord.getPendingDeductMinutes());
                pmiInfo.put("remainingMinutes", pmiRecord.getRemainingMinutes());
            }

            // 检查是否有正在进行的会议
            boolean hasActiveMeeting = false;
            try {
                log.info("检查PMI {} (ID: {}) 的活跃会议", pmiRecord.getPmiNumber(), pmiRecord.getId());
                Optional<ZoomMeeting> activeMeetingOpt = zoomMeetingRepository
                    .findFirstByPmiRecordIdAndStatusInOrderByCreatedAtDesc(pmiRecord.getId(),
                        List.of(ZoomMeeting.MeetingStatus.WAITING, ZoomMeeting.MeetingStatus.STARTED));

                hasActiveMeeting = activeMeetingOpt.isPresent();
                log.info("PMI {} 活跃会议检查结果: hasActiveMeeting={}", pmiRecord.getPmiNumber(), hasActiveMeeting);

                if (hasActiveMeeting) {
                    ZoomMeeting activeMeeting = activeMeetingOpt.get();
                    pmiInfo.put("activeMeetingId", activeMeeting.getId());
                    pmiInfo.put("activeMeetingStatus", activeMeeting.getStatus());
                    pmiInfo.put("activeMeetingStartTime", activeMeeting.getStartTime());
                    pmiInfo.put("activeMeetingZoomId", activeMeeting.getZoomMeetingId());

                    // 当会议进行中时，使用会议记录中的动态主持人链接
                    if (activeMeeting.getStartUrl() != null && !activeMeeting.getStartUrl().trim().isEmpty()) {
                        pmiInfo.put("hostUrl", activeMeeting.getStartUrl());
                        log.info("PMI {} 会议进行中，使用动态主持人链接: {}", pmiRecord.getPmiNumber(), activeMeeting.getStartUrl());
                    } else {
                        log.warn("PMI {} 会议进行中但没有动态主持人链接，使用静态链接", pmiRecord.getPmiNumber());
                    }
                }
            } catch (Exception e) {
                log.error("检查活跃会议时出现异常: PMI={}, error={}", pmiRecord.getPmiNumber(), e.getMessage(), e);
                hasActiveMeeting = false;
            }

            pmiInfo.put("hasActiveMeeting", hasActiveMeeting);



            // 根据状态添加额外信息
            boolean canActivate = pmiRecord.getStatus() == PmiRecord.PmiStatus.ACTIVE && !hasActiveMeeting;
            pmiInfo.put("canActivate", canActivate);

            if (!canActivate) {
                String statusMessage = "";
                if (hasActiveMeeting) {
                    statusMessage = "此PMI正在进行会议中，请先结束当前会议";
                } else {
                    switch (pmiRecord.getStatus()) {
                        case INACTIVE:
                            statusMessage = "此PMI当前处于非活跃状态，无法直接开启会议室";
                            break;
                        case EXPIRED:
                            statusMessage = "此PMI已过期，无法开启会议室";
                            break;
                        default:
                            statusMessage = "此PMI状态不可用";
                            break;
                    }
                }
                pmiInfo.put("statusMessage", statusMessage);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", pmiInfo);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取PMI信息失败: magicId={}", magicId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取PMI信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 一键开启PMI
     * 使用同步锁防止同一PMI并发开启多场会议
     * 注意：这里的参数实际上是magicId，为了保持URL兼容性仍命名为pmiNumber
     */
    @PostMapping("/{magicId}/activate")
    public ResponseEntity<Map<String, Object>> activatePmi(@PathVariable String magicId) {
        // 获取PMI专用锁对象，防止并发开启
        Object pmiLock = pmiLocks.computeIfAbsent(magicId, k -> new Object());

        synchronized (pmiLock) {
            try {
                log.info("收到一键开启PMI请求: magicId={} (已获取并发锁)", magicId);

            // 查找PMI记录
            Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findByMagicId(magicId);
            
            if (!pmiRecordOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "PMI不存在");
                return ResponseEntity.status(404).body(response);
            }
            
            PmiRecord pmiRecord = pmiRecordOpt.get();
            
            if (pmiRecord.getStatus() != PmiRecord.PmiStatus.ACTIVE) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "PMI状态不可用");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 查找可用的Zoom用户（LICENSED + PUBLIC_HOST）
            log.info("开始查找可用的Zoom用户");
            Optional<ZoomUser> availableUser = zoomUserRepository.findFirstAvailablePublicHostUser();
            log.info("查找结果: {}", availableUser.isPresent() ? "找到可用用户" : "未找到可用用户");

            if (!availableUser.isPresent()) {
                // 调试信息：查看所有符合条件的用户
                List<ZoomUser> allUsers = zoomUserRepository.findAvailablePublicHostUsers(
                    ZoomUser.UsageStatus.AVAILABLE, ZoomUser.UserType.LICENSED, ZoomUser.AccountUsage.PUBLIC_HOST, ZoomUser.UserStatus.ACTIVE);
                log.warn("未找到可用的Zoom账号，符合条件的用户数量: {}", allUsers.size());

                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "当前没有可用的Zoom账号，请稍后重试");
                return ResponseEntity.badRequest().body(response);
            }
            
            ZoomUser zoomUser = availableUser.get();
            
            try {
                // 标记ZoomUser为使用中
                zoomUser.setUsageStatus(ZoomUser.UsageStatus.IN_USE);
                zoomUser.setCurrentMeetingId(null); // 会议ID稍后设置
                zoomUserRepository.save(zoomUser);

                // 添加调试日志确认PMI记录中的密码
                log.info("开始设置PMI - pmiNumber={}, pmiPassword={}, hasPassword={}",
                        pmiRecord.getPmiNumber(), pmiRecord.getPmiPassword(),
                        pmiRecord.getPmiPassword() != null && !pmiRecord.getPmiPassword().trim().isEmpty());

                // 使用PmiSetupService检测并设置PMI
                PmiSetupService.PmiSetupResult setupResult = pmiSetupService.detectAndSetupPmi(
                        zoomUser, pmiRecord.getPmiNumber(), pmiRecord.getPmiPassword());

                if (!setupResult.isSuccess()) {
                    // PMI设置失败，立即回收ZoomUser账号并恢复原始PMI
                    log.error("PMI设置失败，开始回收ZoomUser账号: userId={}, pmiNumber={}, error={}",
                            zoomUser.getZoomUserId(), pmiRecord.getPmiNumber(), setupResult.getMessage());

                    try {
                        // 调用ZoomAPI恢复原始PMI（使用ZoomUser对应的ZoomAuth）
                        zoomApiService.restoreUserOriginalPmi(zoomUser.getZoomUserId(), zoomUser.getOriginalPmi(), zoomUser.getZoomAuth());
                        log.info("ZoomUser原始PMI恢复成功: userId={}, originalPmi={}",
                                zoomUser.getZoomUserId(), zoomUser.getOriginalPmi());
                    } catch (Exception restoreException) {
                        log.error("恢复原始PMI失败: userId={}", zoomUser.getZoomUserId(), restoreException);
                    }

                    // 恢复ZoomUser状态
                    zoomUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
                    zoomUser.setCurrentMeetingId(null);
                    zoomUser.setCurrentPmi(zoomUser.getOriginalPmi());
                    zoomUserRepository.save(zoomUser);

                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "PMI设置失败，无法开启会议: " + setupResult.getMessage());
                    return ResponseEntity.badRequest().body(response);
                }

                // PMI设置成功，更新ZoomUser的当前PMI
                zoomUser.setCurrentPmi(pmiRecord.getPmiNumber());
                zoomUserRepository.save(zoomUser);
                log.info("ZoomUser {} 的当前PMI已更新为: {}", zoomUser.getEmail(), pmiRecord.getPmiNumber());

                ZoomApiResponse<JsonNode> apiResponse = setupResult.getApiResponse();

                // 检查是否已有活跃的会议记录（PENDING或USING状态）
                long activeCount = zoomMeetingRepository.countActiveMeetingsByPmiNumber(pmiRecord.getPmiNumber());

                ZoomMeeting meeting;
                String hostUrl;

                if (activeCount > 0) {
                    // 已有活跃会议，查找并返回现有的startURL
                    Optional<ZoomMeeting> existingMeeting = zoomMeetingRepository
                        .findActiveMeetingByPmiNumber(pmiRecord.getPmiNumber());

                    if (existingMeeting.isPresent()) {
                        meeting = existingMeeting.get();
                        // 对于已存在的会议，尝试从API响应获取链接，如果没有则使用生成方式
                        hostUrl = extractHostUrlFromApiResponse(apiResponse, meeting.getHostId(), pmiRecord.getPmiNumber(), zoomUser.getZoomAuth());

                        log.info("PMI {} 已有活跃会议记录 (状态: {}, ID: {})，直接返回startURL",
                                pmiRecord.getPmiNumber(), meeting.getStatus(), meeting.getId());
                    } else {
                        // 理论上不应该到这里，但为了安全起见
                        log.warn("PMI {} 计数显示有活跃会议但查询为空，创建新记录", pmiRecord.getPmiNumber());
                        meeting = createMeetingRecord(pmiRecord, zoomUser, apiResponse);
                        hostUrl = extractHostUrlFromApiResponse(apiResponse, zoomUser.getZoomUserId(), pmiRecord.getPmiNumber(), zoomUser.getZoomAuth());
                    }
                } else {
                    // 没有活跃会议，创建新的会议记录
                    // 更新PMI记录
                    pmiRecord.setCurrentZoomUserId(zoomUser.getId());
                    pmiRecord.setLastUsedAt(LocalDateTime.now());
                    pmiRecordRepository.save(pmiRecord);

                    // 创建ZoomMeeting记录
                    meeting = createMeetingRecord(pmiRecord, zoomUser, apiResponse);

                    // 更新ZoomUser的当前会议ID
                    zoomUser.setCurrentMeetingId(meeting.getId());
                    zoomUserRepository.save(zoomUser);

                    // 从API响应中提取主持人链接
                    hostUrl = extractHostUrlFromApiResponse(apiResponse, zoomUser.getZoomUserId(), pmiRecord.getPmiNumber(), zoomUser.getZoomAuth());

                    log.info("为PMI {} 创建新的会议记录，状态: PENDING, ID: {}",
                            pmiRecord.getPmiNumber(), meeting.getId());
                }
                
                Map<String, Object> result = new HashMap<>();
                result.put("pmiNumber", pmiRecord.getPmiNumber());
                result.put("pmiPassword", pmiRecord.getPmiPassword());
                result.put("hostUrl", hostUrl);
                result.put("joinUrl", pmiRecord.getJoinUrl());
                result.put("zoomUserEmail", zoomUser.getEmail());
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "PMI开启成功");
                response.put("data", result);
                
                log.info("PMI一键开启成功: magicId={}, pmiNumber={}, zoomUserId={}", magicId, pmiRecord.getPmiNumber(), zoomUser.getZoomUserId());
                
                return ResponseEntity.ok(response);
                
            } catch (Exception e) {
                // 出错时完全回收ZoomUser账号
                log.error("PMI开启过程中发生异常，开始完全回收ZoomUser账号: userId={}, magicId={}, pmiNumber={}",
                        zoomUser.getZoomUserId(), magicId, pmiRecord.getPmiNumber(), e);

                try {
                    // 调用ZoomAPI恢复原始PMI
                    zoomApiService.restoreUserOriginalPmi(zoomUser.getZoomUserId(), zoomUser.getOriginalPmi());
                    log.info("异常处理中ZoomUser原始PMI恢复成功: userId={}, originalPmi={}",
                            zoomUser.getZoomUserId(), zoomUser.getOriginalPmi());
                } catch (Exception restoreException) {
                    log.error("异常处理中恢复原始PMI失败: userId={}", zoomUser.getZoomUserId(), restoreException);
                }

                // 恢复ZoomUser状态
                zoomUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
                zoomUser.setCurrentMeetingId(null);
                zoomUser.setCurrentPmi(zoomUser.getOriginalPmi());
                zoomUserRepository.save(zoomUser);
                throw e;
            }
            
            } catch (Exception e) {
                log.error("一键开启PMI失败: magicId={}", magicId, e);
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "开启PMI失败: " + e.getMessage());
                return ResponseEntity.badRequest().body(response);
            } finally {
                // 清理锁对象，避免内存泄漏
                pmiLocks.remove(magicId);
                log.debug("释放PMI并发锁: {}", magicId);
            }
        }
    }
    
    /**
     * 获取PMI复制信息（公共版本）
     * 注意：这里的参数实际上是magicId，为了保持URL兼容性仍命名为pmiNumber
     */
    @GetMapping("/{magicId}/copy-text")
    public ResponseEntity<Map<String, Object>> getPmiCopyText(@PathVariable String magicId) {
        try {
            Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findByMagicId(magicId);
            
            if (!pmiRecordOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "PMI不存在");
                return ResponseEntity.notFound().build();
            }
            
            PmiRecord pmiRecord = pmiRecordOpt.get();
            String copyText = pmiRecord.generateCopyText();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("copyText", copyText);
            response.put("pmiNumber", pmiRecord.getPmiNumber());
            response.put("pmiPassword", pmiRecord.getPmiPassword());
            response.put("hostUrl", pmiRecord.getHostUrl());
            response.put("joinUrl", pmiRecord.getJoinUrl());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取PMI复制信息失败: magicId={}", magicId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取复制信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 从API响应中提取主持人链接
     */
    private String extractHostUrlFromApiResponse(ZoomApiResponse<JsonNode> apiResponse, String zoomUserId, String pmiNumber, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("从API响应中提取主持人链接: userId={}, pmiNumber={}", zoomUserId, pmiNumber);

            if (apiResponse != null && apiResponse.isSuccess() && apiResponse.getData() != null) {
                JsonNode userData = apiResponse.getData();

                // 1. 优先查找start_url字段
                if (userData.has("start_url")) {
                    String startUrl = userData.get("start_url").asText();
                    if (startUrl != null && !startUrl.trim().isEmpty()) {
                        log.info("从API响应中获取到start_url: {}", startUrl);
                        return startUrl;
                    }
                }

                log.info("API响应中没有start_url字段，尝试使用ZAK生成主持人链接");
            }

            // 如果API响应中没有start_url，使用ZAK生成正确的主持人链接
            return generateHostUrlWithZak(zoomUserId, pmiNumber, zoomAuth);

        } catch (Exception e) {
            log.error("从API响应中提取主持人链接失败，使用ZAK生成方式", e);
            return generateHostUrlWithZak(zoomUserId, pmiNumber, zoomAuth);
        }
    }

    /**
     * 使用ZAK生成主持人链接
     */
    private String generateHostUrlWithZak(String zoomUserId, String pmiNumber, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("开始获取ZAK生成主持人链接: userId={}, pmiNumber={}", zoomUserId, pmiNumber);

            // 获取用户的ZAK（使用指定的ZoomAuth）
            ZoomApiResponse<JsonNode> zakResponse = zoomApiService.getUserZak(zoomUserId, zoomAuth);

            if (zakResponse.isSuccess() && zakResponse.getData() != null) {
                JsonNode zakData = zakResponse.getData();
                if (zakData.has("token")) {
                    String zak = zakData.get("token").asText();
                    if (zak != null && !zak.trim().isEmpty()) {
                        String completeHostUrl = "https://zoom.us/s/" + pmiNumber + "?zak=" + zak;
                        log.info("使用ZAK生成完整的主持人链接成功: {}", completeHostUrl);
                        return completeHostUrl;
                    }
                }
            }

            log.warn("获取ZAK失败，使用默认主持人链接格式: {}", zakResponse.getMessage());
            return generateDefaultHostUrl(pmiNumber);

        } catch (Exception e) {
            log.error("生成ZAK主持人链接失败，使用默认格式", e);
            return generateDefaultHostUrl(pmiNumber);
        }
    }

    /**
     * 生成默认的主持人链接格式
     */
    private String generateDefaultHostUrl(String pmiNumber) {
        String defaultUrl = "https://zoom.us/s/" + pmiNumber;
        log.info("生成默认主持人链接: {}", defaultUrl);
        return defaultUrl;
    }

    /**
     * 生成完整的主持人链接（备用方案）
     */
    private String generateCompleteHostUrl(String zoomUserId, String pmiNumber, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("开始生成完整的主持人链接: userId={}, pmiNumber={}", zoomUserId, pmiNumber);

            // 首先尝试获取用户信息，从中提取主持人链接（使用指定的ZoomAuth）
            ZoomApiResponse<JsonNode> userInfoResponse = zoomApiService.getUserInfo(zoomUserId, zoomAuth);

            if (userInfoResponse.isSuccess() && userInfoResponse.getData() != null) {
                JsonNode userData = userInfoResponse.getData();

                // 尝试从用户信息中获取personal_meeting_url
                if (userData.has("personal_meeting_url")) {
                    String personalMeetingUrl = userData.get("personal_meeting_url").asText();
                    if (personalMeetingUrl != null && !personalMeetingUrl.trim().isEmpty()) {
                        // 将参会链接转换为主持人链接
                        String hostUrl = convertJoinUrlToHostUrl(personalMeetingUrl);
                        log.info("从用户信息中生成主持人链接成功: {}", hostUrl);
                        return hostUrl;
                    }
                }

                // 尝试从用户信息中获取start_url
                if (userData.has("start_url")) {
                    String startUrl = userData.get("start_url").asText();
                    if (startUrl != null && !startUrl.trim().isEmpty()) {
                        log.info("从用户信息中获取到start_url: {}", startUrl);
                        return startUrl;
                    }
                }
            }

            // 如果从用户信息中无法获取，尝试获取ZAK（使用指定的ZoomAuth）
            ZoomApiResponse<JsonNode> zakResponse = zoomApiService.getUserZak(zoomUserId, zoomAuth);

            if (zakResponse.isSuccess() && zakResponse.getData() != null) {
                JsonNode zakData = zakResponse.getData();
                if (zakData.has("token")) {
                    String zak = zakData.get("token").asText();
                    if (zak != null && !zak.trim().isEmpty()) {
                        String completeHostUrl = "https://zoom.us/s/" + pmiNumber + "?zak=" + zak;
                        log.info("使用ZAK生成完整的主持人链接成功: {}", completeHostUrl);
                        return completeHostUrl;
                    }
                }
            }

            log.warn("获取ZAK失败，使用默认主持人链接格式: {}", zakResponse.getMessage());
            return generateDefaultHostUrl(pmiNumber);

        } catch (Exception e) {
            log.error("生成完整主持人链接失败，使用默认格式", e);
            return generateDefaultHostUrl(pmiNumber);
        }
    }



    /**
     * 将参会链接转换为主持人链接
     */
    private String convertJoinUrlToHostUrl(String joinUrl) {
        try {
            // 将 zoom.us/j/ 替换为 zoom.us/s/
            if (joinUrl.contains("/j/")) {
                return joinUrl.replace("/j/", "/s/");
            }
            // 如果已经是主持人链接格式，直接返回
            return joinUrl;
        } catch (Exception e) {
            log.error("转换主持人链接失败", e);
            return joinUrl;
        }
    }



    /**
     * 创建ZoomMeeting记录
     * 注意：zoom_meeting_uuid暂时使用临时值，实际的UUID将在meeting.start事件中更新
     */
    private ZoomMeeting createMeetingRecord(PmiRecord pmiRecord, ZoomUser zoomUser, ZoomApiResponse<JsonNode> apiResponse) {
        ZoomMeeting meeting = new ZoomMeeting();
        meeting.setPmiRecordId(pmiRecord.getId());
        // 使用临时UUID，格式：pending-{pmiNumber}-{timestamp}
        meeting.setZoomMeetingUuid("pending-" + pmiRecord.getPmiNumber() + "-" + System.currentTimeMillis());
        meeting.setZoomMeetingId(pmiRecord.getPmiNumber()); // 关键：使用PMI号码作为meeting_id
        meeting.setTopic("PMI会议 - " + pmiRecord.getPmiNumber());
        meeting.setHostId(zoomUser.getZoomUserId()); // 关键：记录主持人ID
        meeting.setStatus(ZoomMeeting.MeetingStatus.WAITING);
        meeting.setBillingMode(pmiRecord.getBillingMode());
        meeting.setCreationSource(ZoomMeeting.CreationSource.PMI_MEETING); // 设置会议来源为PMI会议
        meeting.setAssignedZoomUserId(zoomUser.getId());
        meeting.setAssignedZoomUserEmail(zoomUser.getEmail());

        // 记录使用的ZoomAuth账号名称
        if (zoomUser.getZoomAuth() != null) {
            meeting.setZoomAuthAccountName(zoomUser.getZoomAuth().getAccountName());
        }

        // 从Zoom API响应中提取URL信息
        if (apiResponse != null && apiResponse.isSuccess() && apiResponse.getData() != null) {
            JsonNode userData = apiResponse.getData();

            // 提取joinUrl
            if (userData.has("personal_meeting_url")) {
                String joinUrl = userData.get("personal_meeting_url").asText();
                meeting.setJoinUrl(joinUrl);
                log.info("设置joinUrl: {}", joinUrl);
            }

            // 从API响应中提取startUrl（主持人链接）
            String startUrl = extractHostUrlFromApiResponse(apiResponse, zoomUser.getZoomUserId(), pmiRecord.getPmiNumber(), zoomUser.getZoomAuth());
            meeting.setStartUrl(startUrl);
            log.info("设置startUrl: {}", startUrl);
        }

        ZoomMeeting savedMeeting = zoomMeetingRepository.save(meeting);
        log.info("创建ZoomMeeting记录成功: meetingId={}, pmiNumber={}, zoomUser={}, hostId={}, startUrl={}, joinUrl={}",
                savedMeeting.getId(), pmiRecord.getPmiNumber(), zoomUser.getEmail(), zoomUser.getZoomUserId(),
                savedMeeting.getStartUrl(), savedMeeting.getJoinUrl());

        return savedMeeting;
    }

}
