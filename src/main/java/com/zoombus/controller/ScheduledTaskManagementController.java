package com.zoombus.controller;

import com.zoombus.entity.TaskExecutionRecord;
import com.zoombus.repository.TaskExecutionRecordRepository;
import com.zoombus.service.AdvancedTaskManagementService;
import com.zoombus.service.AsyncScheduledTaskExecutor;
import com.zoombus.service.BatchTaskExecutionService;
import com.zoombus.service.ScheduledTaskErrorHandler;
import com.zoombus.service.TaskAnalyticsService;
import com.zoombus.service.TaskExecutionCacheService;
import com.zoombus.service.TaskExecutionTracker;
import com.zoombus.service.TaskMonitoringService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 定时任务管理控制器
 * 提供定时任务的监控、管理和统计功能
 */
@RestController
@RequestMapping("/api/admin/scheduled-tasks")
@RequiredArgsConstructor
@Slf4j
public class ScheduledTaskManagementController {
    
    private final TaskExecutionRecordRepository taskExecutionRecordRepository;
    private final TaskExecutionCacheService cacheService;
    private final BatchTaskExecutionService batchService;
    private final AdvancedTaskManagementService advancedTaskService;
    private final TaskMonitoringService monitoringService;
    private final TaskAnalyticsService analyticsService;
    private final ScheduledTaskErrorHandler errorHandler;
    private final TaskExecutionTracker executionTracker;
    private final AsyncScheduledTaskExecutor asyncScheduledTaskExecutor;

    /**
     * 获取任务概览和统计信息（带缓存优化）
     */
    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getTaskOverview() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 尝试从缓存获取数据
            Map<String, Object> cachedOverview = cacheService.getCachedTaskOverview();
            if (cachedOverview != null) {
                log.debug("从缓存获取任务概览数据");
                result.put("success", true);
                result.put("data", cachedOverview);
                result.put("cached", true);
                return ResponseEntity.ok(result);
            }

            // 缓存未命中，从数据库获取
            log.debug("缓存未命中，从数据库获取任务概览数据");

            // 获取所有任务的基本信息
            List<Map<String, Object>> tasks = getTaskSummaryList();

            // 获取统计信息
            Map<String, Object> statistics = getOverallStatistics();

            Map<String, Object> overviewData = Map.of(
                    "tasks", tasks,
                    "statistics", statistics
            );

            // 缓存数据
            cacheService.cacheTaskOverview(overviewData);

            result.put("success", true);
            result.put("data", overviewData);
            result.put("cached", false);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务概览失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取任务概览失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取任务执行记录列表
     */
    @GetMapping("/records")
    public ResponseEntity<Page<TaskExecutionRecord>> getTaskExecutionRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String taskName,
            @RequestParam(required = false) TaskExecutionRecord.ExecutionStatus status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("executionTime").descending());
        
        Page<TaskExecutionRecord> records;
        
        if (taskName != null || status != null || startTime != null || endTime != null) {
            // 根据条件查询
            LocalDateTime start = startTime != null ? startTime : LocalDateTime.now().minusDays(7);
            LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
            
            if (taskName != null) {
                records = taskExecutionRecordRepository.findByTaskNameAndExecutionTimeBetween(
                        taskName, start, end, pageable);
            } else {
                records = taskExecutionRecordRepository.findByExecutionTimeBetween(start, end, pageable);
            }
        } else {
            // 查询所有记录
            records = taskExecutionRecordRepository.findAll(pageable);
        }
        
        return ResponseEntity.ok(records);
    }
    
    /**
     * 获取正在执行的任务
     */
    @GetMapping("/running")
    public ResponseEntity<Map<String, Object>> getRunningTasks() {
        try {
            Set<String> runningTaskNames = executionTracker.getRunningTasks();

            List<Map<String, Object>> runningTasks = new ArrayList<>();
            for (String taskName : runningTaskNames) {
                TaskExecutionTracker.TaskExecutionInfo info = executionTracker.getTaskExecutionInfo(taskName);
                Map<String, Object> task = new HashMap<>();
                task.put("taskName", taskName);
                if (info != null) {
                    task.put("executionId", info.getExecutionId());
                    task.put("startTime", info.getStartTime());
                    task.put("duration", info.getFormattedDuration());
                } else {
                    task.put("executionId", "exec_" + System.currentTimeMillis());
                    task.put("startTime", LocalDateTime.now().minusMinutes(1));
                    task.put("duration", "1分钟");
                }
                runningTasks.add(task);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", Map.of(
                    "count", runningTasks.size(),
                    "tasks", runningTasks
            ));

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取运行中任务失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取运行中任务失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 手动触发任务
     */
    @PostMapping("/{taskName}/trigger")
    public ResponseEntity<Map<String, Object>> triggerTask(@PathVariable String taskName) {
        try {
            // 这里可以根据任务名称调用相应的服务方法
            // 暂时返回成功响应
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "任务 " + taskName + " 已手动触发");
            result.put("taskName", taskName);
            result.put("timestamp", LocalDateTime.now());

            log.info("手动触发任务: {}", taskName);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("手动触发任务失败: {}", taskName, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "触发任务失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取任务历史记录
     */
    @GetMapping("/{taskName}/history")
    public ResponseEntity<Map<String, Object>> getTaskHistory(
            @PathVariable String taskName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {

        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("executionTime").descending());

            LocalDateTime start = startTime != null ? startTime : LocalDateTime.now().minusDays(7);
            LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();

            Page<TaskExecutionRecord> records = taskExecutionRecordRepository
                    .findByTaskNameAndExecutionTimeBetween(taskName, start, end, pageable);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", records);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务历史失败: {}", taskName, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取任务历史失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }



    /**
     * 获取任务健康状态报告
     */
    @GetMapping("/health/{taskName}")
    public ResponseEntity<ScheduledTaskErrorHandler.TaskHealthReport> getTaskHealthReport(
            @PathVariable String taskName,
            @RequestParam(defaultValue = "24") int hours) {
        
        ScheduledTaskErrorHandler.TaskHealthReport report = errorHandler.getTaskHealthReport(taskName, hours);
        return ResponseEntity.ok(report);
    }
    
    /**
     * 获取任务统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getTaskStatistics(
            @RequestParam(defaultValue = "24") int hours) {
        
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        LocalDateTime endTime = LocalDateTime.now();
        
        List<Object[]> statusCounts = taskExecutionRecordRepository
                .countByStatusAndExecutionTimeBetween(startTime, endTime);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("period", hours + " hours");
        statistics.put("startTime", startTime);
        statistics.put("endTime", endTime);
        
        Map<String, Long> statusMap = new HashMap<>();
        long totalCount = 0;
        
        for (Object[] row : statusCounts) {
            TaskExecutionRecord.ExecutionStatus status = (TaskExecutionRecord.ExecutionStatus) row[0];
            Long count = (Long) row[1];
            statusMap.put(status.name(), count);
            totalCount += count;
        }
        
        statistics.put("totalExecutions", totalCount);
        statistics.put("statusCounts", statusMap);
        
        // 计算成功率
        long successCount = statusMap.getOrDefault("SUCCESS", 0L);
        long failedCount = statusMap.getOrDefault("FAILED", 0L);
        double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;
        statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
        
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 获取失败率较高的任务
     */
    @GetMapping("/high-failure-tasks")
    public ResponseEntity<List<Map<String, Object>>> getHighFailureTasks(
            @RequestParam(defaultValue = "24") int hours,
            @RequestParam(defaultValue = "5") long threshold) {
        
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        
        List<Object[]> highFailureTasks = taskExecutionRecordRepository
                .findTasksWithHighFailureRate(startTime, threshold);
        
        List<Map<String, Object>> result = highFailureTasks.stream().map(row -> {
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("taskName", row[0]);
            taskInfo.put("failureCount", row[1]);
            return taskInfo;
        }).collect(java.util.stream.Collectors.toList());
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取执行时间最长的任务
     */
    @GetMapping("/longest-execution-tasks")
    public ResponseEntity<List<Map<String, Object>>> getLongestExecutionTasks(
            @RequestParam(defaultValue = "24") int hours) {
        
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        
        List<Object[]> longestTasks = taskExecutionRecordRepository
                .findTasksWithLongestAverageExecutionTime(startTime);
        
        List<Map<String, Object>> result = longestTasks.stream().map(row -> {
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("taskName", row[0]);
            taskInfo.put("averageDurationMs", row[1]);
            taskInfo.put("averageDurationFormatted", formatDuration((Double) row[1]));
            return taskInfo;
        }).collect(java.util.stream.Collectors.toList());
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 强制清理任务状态
     */
    @PostMapping("/cleanup/{taskName}")
    public ResponseEntity<Map<String, String>> forceCleanupTask(@PathVariable String taskName) {
        try {
            executionTracker.forceCleanupTask(taskName);
            
            Map<String, String> result = new HashMap<>();
            result.put("message", "任务状态已强制清理: " + taskName);
            result.put("status", "success");
            
            log.info("管理员强制清理任务状态: {}", taskName);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> result = new HashMap<>();
            result.put("message", "清理任务状态失败: " + e.getMessage());
            result.put("status", "error");
            
            log.error("强制清理任务状态失败: {}", taskName, e);
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 手动触发重试任务处理
     */
    @PostMapping("/process-retries")
    public ResponseEntity<Map<String, String>> processRetryTasks() {
        try {
            errorHandler.processRetryTasks();
            
            Map<String, String> result = new HashMap<>();
            result.put("message", "重试任务处理已触发");
            result.put("status", "success");
            
            log.info("管理员手动触发重试任务处理");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> result = new HashMap<>();
            result.put("message", "触发重试任务处理失败: " + e.getMessage());
            result.put("status", "error");
            
            log.error("手动触发重试任务处理失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 手动清理过期记录
     */
    @PostMapping("/cleanup-expired")
    public ResponseEntity<Map<String, String>> cleanupExpiredRecords() {
        try {
            errorHandler.cleanupExpiredRecords();
            
            Map<String, String> result = new HashMap<>();
            result.put("message", "过期记录清理已触发");
            result.put("status", "success");
            
            log.info("管理员手动触发过期记录清理");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> result = new HashMap<>();
            result.put("message", "清理过期记录失败: " + e.getMessage());
            result.put("status", "error");
            
            log.error("手动清理过期记录失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 格式化持续时间
     */
    private String formatDuration(Double durationMs) {
        if (durationMs == null) return "N/A";
        
        long ms = durationMs.longValue();
        long seconds = ms / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 获取任务摘要列表
     */
    private List<Map<String, Object>> getTaskSummaryList() {
        // 定义所有已知的任务（已移除PMI轮询任务，现在使用精准调度）
        String[] taskNames = {
                "refreshExpiredTokens", "dailyUserSync", "checkExpiredWindows",
                "checkBillingMonitorStatus", "batchSettleMeetings", "cleanupTemporaryPmiMeetings",
                "processPendingReportTasks", "processRetryReportTasks", "cleanupTimeoutReportTasks"
        };

        Map<String, String> taskTypeMap = Map.of(
                "refreshExpiredTokens", "ZOOM_TOKEN_REFRESH",
                "dailyUserSync", "DAILY_USER_SYNC",
                "checkExpiredWindows", "WINDOW_EXPIRY_CHECK",
                "checkBillingMonitorStatus", "BILLING_MONITOR_CHECK",
                "batchSettleMeetings", "BATCH_SETTLEMENT",
                "cleanupTemporaryPmiMeetings", "TEMP_MEETING_CLEANUP"
        );

        List<Map<String, Object>> tasks = new ArrayList<>();
        LocalDateTime last24Hours = LocalDateTime.now().minusHours(24);

        for (String taskName : taskNames) {
            Map<String, Object> task = new HashMap<>();
            task.put("taskName", taskName);
            task.put("taskType", taskTypeMap.get(taskName));

            // 获取最近的执行记录
            List<TaskExecutionRecord> recentRecords = taskExecutionRecordRepository
                    .findByTaskNameAndExecutionTimeBetween(taskName, last24Hours, LocalDateTime.now());

            if (!recentRecords.isEmpty()) {
                TaskExecutionRecord latestRecord = recentRecords.get(0);
                task.put("lastStatus", latestRecord.getStatus().toString());
                task.put("lastExecutionTime", latestRecord.getExecutionTime());
                task.put("lastCompletionTime", latestRecord.getCompletionTime());

                // 统计信息
                long totalExecutions = recentRecords.size();
                long successExecutions = recentRecords.stream()
                        .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.SUCCESS)
                        .count();
                long failedExecutions = recentRecords.stream()
                        .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                        .count();

                task.put("totalExecutions", totalExecutions);
                task.put("successExecutions", successExecutions);
                task.put("failedExecutions", failedExecutions);

                // 平均执行时间
                double avgDuration = recentRecords.stream()
                        .filter(r -> r.getDurationMs() != null)
                        .mapToLong(TaskExecutionRecord::getDurationMs)
                        .average()
                        .orElse(0.0);
                task.put("averageExecutionTime", Math.round(avgDuration));

                // 最后错误信息
                recentRecords.stream()
                        .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                        .findFirst()
                        .ifPresent(r -> task.put("lastErrorMessage", r.getErrorMessage()));
            } else {
                // 没有执行记录的任务
                task.put("lastStatus", "PENDING");
                task.put("totalExecutions", 0);
                task.put("successExecutions", 0);
                task.put("failedExecutions", 0);
                task.put("averageExecutionTime", 0);
            }

            tasks.add(task);
        }

        return tasks;
    }

    /**
     * 获取整体统计信息
     */
    private Map<String, Object> getOverallStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总任务数
        statistics.put("totalTasks", 9);

        // 运行中任务数
        Set<String> runningTasks = executionTracker.getRunningTasks();
        statistics.put("runningTasks", runningTasks.size());

        // 今日执行统计
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime now = LocalDateTime.now();

        Pageable pageable = PageRequest.of(0, 10000); // 获取足够多的记录用于统计
        Page<TaskExecutionRecord> todayRecordsPage = taskExecutionRecordRepository
                .findByExecutionTimeBetween(todayStart, now, pageable);
        List<TaskExecutionRecord> todayRecords = todayRecordsPage.getContent();

        long todayExecutions = todayRecords.size();
        long todaySuccessExecutions = todayRecords.stream()
                .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.SUCCESS)
                .count();

        double todaySuccessRate = todayExecutions > 0 ?
                (double) todaySuccessExecutions / todayExecutions * 100 : 0;

        statistics.put("todayExecutions", todayExecutions);
        statistics.put("todaySuccessRate", Math.round(todaySuccessRate * 100.0) / 100.0);

        return statistics;
    }

    /**
     * 缓存管理端点
     */
    @PostMapping("/cache/clear")
    public ResponseEntity<Map<String, Object>> clearCache(@RequestParam(required = false) String taskName) {
        try {
            if (taskName != null && !taskName.trim().isEmpty()) {
                cacheService.evictTaskCache(taskName);
                log.info("清除任务缓存: {}", taskName);
            } else {
                cacheService.clearAllCache();
                log.info("清除所有缓存");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", taskName != null ? "已清除任务缓存: " + taskName : "已清除所有缓存");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清除缓存失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "清除缓存失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache/stats")
    public ResponseEntity<Map<String, Object>> getCacheStats() {
        try {
            Map<String, Object> cacheStats = cacheService.getCacheStatistics();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", cacheStats);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取缓存统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 批量操作端点
     */
    @PostMapping("/batch/cleanup")
    public ResponseEntity<Map<String, Object>> batchCleanup() {
        try {
            batchService.cleanupAndOptimize();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "批量清理和优化完成");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量清理失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "批量清理失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    // ==================== 高级管理功能端点 ====================

    /**
     * 启用/禁用任务
     */
    @PostMapping("/{taskName}/toggle")
    public ResponseEntity<Map<String, Object>> toggleTask(
            @PathVariable String taskName,
            @RequestParam boolean enabled) {
        try {
            boolean success = enabled ?
                    advancedTaskService.enableTask(taskName) :
                    advancedTaskService.disableTask(taskName);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ?
                    (enabled ? "任务已启用" : "任务已禁用") :
                    "操作失败");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("切换任务状态失败: {}", taskName, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "操作失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 更新任务调度频率
     */
    @PostMapping("/{taskName}/schedule")
    public ResponseEntity<Map<String, Object>> updateTaskSchedule(
            @PathVariable String taskName,
            @RequestParam String cronExpression) {
        try {
            boolean success = advancedTaskService.updateTaskSchedule(taskName, cronExpression);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "调度频率已更新" : "更新失败");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新任务调度频率失败: {}", taskName, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取任务健康状态
     */
    @GetMapping("/{taskName}/health")
    public ResponseEntity<Map<String, Object>> getTaskHealth(@PathVariable String taskName) {
        try {
            Map<String, Object> healthStatus = advancedTaskService.getTaskHealthStatus(taskName);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", healthStatus);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务健康状态失败: {}", taskName, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取健康状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取任务执行趋势
     */
    @GetMapping("/{taskName}/trend")
    public ResponseEntity<Map<String, Object>> getTaskTrend(
            @PathVariable String taskName,
            @RequestParam(defaultValue = "7") int days) {
        try {
            Map<String, Object> trendData = analyticsService.getTaskExecutionTrend(taskName, days);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", trendData);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务执行趋势失败: {}", taskName, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取趋势数据失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取任务性能报告
     */
    @GetMapping("/{taskName}/performance")
    public ResponseEntity<Map<String, Object>> getTaskPerformance(
            @PathVariable String taskName,
            @RequestParam(defaultValue = "7") int days) {
        try {
            Map<String, Object> performanceReport = analyticsService.getTaskPerformanceReport(taskName, days);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", performanceReport);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务性能报告失败: {}", taskName, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取性能报告失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取任务健康度评分
     */
    @GetMapping("/{taskName}/score")
    public ResponseEntity<Map<String, Object>> getTaskHealthScore(@PathVariable String taskName) {
        try {
            Map<String, Object> healthScore = monitoringService.getTaskHealthScore(taskName);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", healthScore);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务健康度评分失败: {}", taskName, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取健康度评分失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取所有任务概览
     */
    @GetMapping("/analytics/overview")
    public ResponseEntity<Map<String, Object>> getAllTasksAnalytics(
            @RequestParam(defaultValue = "7") int days) {
        try {
            Map<String, Object> overview = analyticsService.getAllTasksOverview(days);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", overview);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取所有任务概览失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取概览失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取活跃告警
     */
    @GetMapping("/alerts/active")
    public ResponseEntity<Map<String, Object>> getActiveAlerts() {
        try {
            List<TaskMonitoringService.AlertRecord> activeAlerts = monitoringService.getActiveAlerts();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", activeAlerts);
            result.put("count", activeAlerts.size());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取活跃告警失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取告警失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }
}
