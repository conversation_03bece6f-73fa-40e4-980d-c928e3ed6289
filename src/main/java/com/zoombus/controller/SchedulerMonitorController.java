package com.zoombus.controller;

import com.zoombus.service.ScheduledTaskErrorHandler;
import com.zoombus.service.SchedulerMonitorService;
import com.zoombus.service.TaskExecutionTracker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 定时任务监控管理API
 */
@RestController
@RequestMapping("/api/scheduler-monitor")
@RequiredArgsConstructor
@Slf4j
public class SchedulerMonitorController {

    private final SchedulerMonitorService schedulerMonitorService;
    private final TaskExecutionTracker taskExecutionTracker;
    private final ScheduledTaskErrorHandler errorHandler;

    /**
     * 获取所有定时任务信息
     */
    @GetMapping("/tasks")
    public ResponseEntity<Map<String, Object>> getAllSchedulers() {
        try {
            List<SchedulerMonitorService.SchedulerInfo> schedulers = schedulerMonitorService.getAllSchedulerInfo();
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", schedulers,
                    "total", schedulers.size()
            ));
        } catch (Exception e) {
            log.error("获取定时任务信息失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "获取定时任务信息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取定时任务概览统计
     */
    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getSchedulerOverview() {
        try {
            SchedulerMonitorService.SchedulerOverview overview = schedulerMonitorService.getSchedulerOverview();
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", overview
            ));
        } catch (Exception e) {
            log.error("获取定时任务概览失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "获取定时任务概览失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 根据类别获取定时任务
     */
    @GetMapping("/tasks/category/{category}")
    public ResponseEntity<Map<String, Object>> getSchedulersByCategory(@PathVariable String category) {
        try {
            List<SchedulerMonitorService.SchedulerInfo> schedulers = 
                    schedulerMonitorService.getSchedulersByCategory(category);
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", schedulers,
                    "category", category,
                    "total", schedulers.size()
            ));
        } catch (Exception e) {
            log.error("获取分类定时任务信息失败: category={}", category, e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "获取分类定时任务信息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 手动触发会议状态同步任务
     */
    @PostMapping("/tasks/meeting-sync/trigger")
    public ResponseEntity<Map<String, Object>> triggerMeetingStatusSync() {
        try {
            Map<String, Object> result = schedulerMonitorService.triggerMeetingStatusSync();
            
            if ((Boolean) result.get("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }
        } catch (Exception e) {
            log.error("手动触发会议状态同步失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "手动触发失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getSystemHealth() {
        try {
            SchedulerMonitorService.SchedulerOverview overview = schedulerMonitorService.getSchedulerOverview();
            
            Map<String, Object> health = Map.of(
                    "status", overview.getSystemHealth(),
                    "totalTasks", overview.getTotalTasks(),
                    "activeTasks", overview.getActiveTasks(),
                    "errorTasks", overview.getErrorTasks(),
                    "lastUpdateTime", overview.getLastUpdateTime(),
                    "healthy", "健康".equals(overview.getSystemHealth())
            );
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", health
            ));
        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "获取系统健康状态失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取正在运行的任务（新增接口，兼容新的任务监控系统）
     */
    @GetMapping("/running-tasks")
    public ResponseEntity<Map<String, Object>> getRunningTasks() {
        try {
            Set<String> runningTaskNames = taskExecutionTracker.getRunningTasks();

            Map<String, Object> result = new HashMap<>();
            result.put("count", runningTaskNames.size());
            result.put("tasks", runningTaskNames.stream().map(taskName -> {
                TaskExecutionTracker.TaskExecutionInfo info = taskExecutionTracker.getTaskExecutionInfo(taskName);
                Map<String, Object> taskInfo = new HashMap<>();
                taskInfo.put("taskName", taskName);
                if (info != null) {
                    taskInfo.put("executionId", info.getExecutionId());
                    taskInfo.put("startTime", info.getStartTime());
                    taskInfo.put("duration", info.getFormattedDuration());
                }
                return taskInfo;
            }).collect(java.util.stream.Collectors.toList()));

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", result
            ));
        } catch (Exception e) {
            log.error("获取正在运行的任务失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "获取正在运行的任务失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取任务健康状态报告（新增接口）
     */
    @GetMapping("/task-health/{taskName}")
    public ResponseEntity<Map<String, Object>> getTaskHealthReport(
            @PathVariable String taskName,
            @RequestParam(defaultValue = "24") int hours) {
        try {
            ScheduledTaskErrorHandler.TaskHealthReport report = errorHandler.getTaskHealthReport(taskName, hours);

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", report
            ));
        } catch (Exception e) {
            log.error("获取任务健康状态报告失败: taskName={}", taskName, e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "获取任务健康状态报告失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 强制清理任务状态（新增接口）
     */
    @PostMapping("/cleanup-task/{taskName}")
    public ResponseEntity<Map<String, Object>> forceCleanupTask(@PathVariable String taskName) {
        try {
            taskExecutionTracker.forceCleanupTask(taskName);

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "任务状态已强制清理: " + taskName
            ));
        } catch (Exception e) {
            log.error("强制清理任务状态失败: taskName={}", taskName, e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "清理任务状态失败: " + e.getMessage()
            ));
        }
    }
}
