package com.zoombus.controller;

import com.zoombus.entity.SystemConfig;
import com.zoombus.service.SystemConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统配置控制器
 */
@RestController
@RequestMapping("/api/admin/system/config")
@RequiredArgsConstructor
@Slf4j
public class SystemConfigController {
    
    private final SystemConfigService systemConfigService;
    
    /**
     * 获取所有配置（分页）
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getConfigs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "configKey") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir,
            @RequestParam(required = false) String keyword) {
        
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<SystemConfig> configPage;
            if (keyword != null && !keyword.trim().isEmpty()) {
                configPage = systemConfigService.searchConfigs(keyword, pageable);
            } else {
                configPage = systemConfigService.getConfigs(pageable);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", configPage);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取系统配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取配置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据配置键获取配置值
     */
    @GetMapping("/value")
    public ResponseEntity<Map<String, Object>> getConfigValues(@RequestParam String keys) {
        try {
            String[] keyArray = keys.split(",");
            Map<String, String> configValues = new HashMap<>();
            
            for (String key : keyArray) {
                String value = systemConfigService.getConfigValue(key.trim());
                configValues.put(key.trim(), value);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", configValues);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取配置值失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取配置值失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据前缀获取配置
     */
    @GetMapping("/prefix/{prefix}")
    public ResponseEntity<Map<String, Object>> getConfigsByPrefix(@PathVariable String prefix) {
        try {
            List<SystemConfig> configs = systemConfigService.getConfigsByPrefix(prefix);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", configs);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("根据前缀获取配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取配置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 根据ID获取配置
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getConfigById(@PathVariable Long id) {
        try {
            SystemConfig config = systemConfigService.getConfigById(id)
                    .orElseThrow(() -> new IllegalArgumentException("配置不存在"));
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", config);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取配置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 创建配置
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createConfig(
            @Valid @RequestBody SystemConfig config,
            @RequestParam(required = false) String operator) {
        
        try {
            config.setCreatedBy(operator != null ? operator : "ADMIN");
            config.setUpdatedBy(operator != null ? operator : "ADMIN");
            
            SystemConfig savedConfig = systemConfigService.createConfig(config);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", savedConfig);
            response.put("message", "配置创建成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("创建配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "创建配置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 更新配置
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateConfig(
            @PathVariable Long id,
            @Valid @RequestBody SystemConfig config,
            @RequestParam(required = false) String operator) {
        
        try {
            config.setUpdatedBy(operator != null ? operator : "ADMIN");
            
            SystemConfig updatedConfig = systemConfigService.updateConfig(id, config);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedConfig);
            response.put("message", "配置更新成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新配置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 批量更新配置
     */
    @PutMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchUpdateConfigs(
            @RequestBody List<Map<String, String>> configs,
            @RequestParam(required = false) String operator) {
        
        try {
            int updatedCount = 0;
            for (Map<String, String> configMap : configs) {
                String key = configMap.get("key");
                String value = configMap.get("value");
                String description = configMap.get("description");
                String typeStr = configMap.get("type");
                
                SystemConfig.ConfigType configType = SystemConfig.ConfigType.STRING;
                if (typeStr != null) {
                    try {
                        configType = SystemConfig.ConfigType.valueOf(typeStr.toUpperCase());
                    } catch (IllegalArgumentException e) {
                        log.warn("无效的配置类型: {}, 使用默认类型STRING", typeStr);
                    }
                }
                
                systemConfigService.saveOrUpdateConfig(key, value, description, configType, operator);
                updatedCount++;
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "批量更新配置成功，更新数量: " + updatedCount);
            response.put("updatedCount", updatedCount);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量更新配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量更新配置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 删除配置
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteConfig(
            @PathVariable Long id,
            @RequestParam(required = false) String operator) {
        
        try {
            systemConfigService.deleteConfig(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "配置删除成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除配置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 启用/禁用配置
     */
    @PutMapping("/{id}/toggle")
    public ResponseEntity<Map<String, Object>> toggleConfigStatus(
            @PathVariable Long id,
            @RequestParam(required = false) String operator) {
        
        try {
            SystemConfig config = systemConfigService.toggleConfigStatus(id, operator);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", config);
            response.put("message", config.getIsActive() ? "配置已启用" : "配置已禁用");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("切换配置状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "切换配置状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取配置统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getConfigStatistics() {
        try {
            Map<String, Object> statistics = systemConfigService.getConfigStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取配置统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
