package com.zoombus.controller;

import com.zoombus.service.TempMeetingCleanupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 临时PMI会议清理管理接口
 * 提供手动清理和监控功能
 */
@RestController
@RequestMapping("/api/admin/temp-meetings")
@RequiredArgsConstructor
@Slf4j
public class TempMeetingCleanupController {

    private final TempMeetingCleanupService tempMeetingCleanupService;

    /**
     * 获取待清理的临时PMI会议数量
     */
    @GetMapping("/count")
    public ResponseEntity<Map<String, Object>> getTemporaryMeetingCount() {
        try {
            long count = tempMeetingCleanupService.getTemporaryMeetingCount();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "temporaryMeetingCount", count,
                "message", count > 0 ? "有 " + count + " 个临时PMI会议待清理" : "没有临时PMI会议需要清理"
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取临时PMI会议数量失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取临时PMI会议数量失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 手动触发临时PMI会议清理
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> manualCleanup() {
        try {
            log.info("管理员手动触发临时PMI会议清理");
            
            // 获取清理前的数量
            long beforeCount = tempMeetingCleanupService.getTemporaryMeetingCount();
            
            // 执行清理
            tempMeetingCleanupService.manualCleanup();
            
            // 获取清理后的数量
            long afterCount = tempMeetingCleanupService.getTemporaryMeetingCount();
            long cleanedCount = beforeCount - afterCount;
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "beforeCount", beforeCount,
                "afterCount", afterCount,
                "cleanedCount", cleanedCount,
                "message", "手动清理完成，清理了 " + cleanedCount + " 个临时PMI会议"
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("手动清理临时PMI会议失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "手动清理失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取清理服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getCleanupStatus() {
        try {
            long temporaryCount = tempMeetingCleanupService.getTemporaryMeetingCount();
            
            Map<String, Object> status = new HashMap<>();
            status.put("enabled", true);
            status.put("cleanupInterval", "10分钟");
            status.put("temporaryMeetingCount", temporaryCount);
            status.put("lastCleanupTime", "由定时任务自动执行");
            status.put("description", "每10分钟自动清理超过10分钟的临时PMI会议");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", status);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取清理服务状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取清理服务状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
