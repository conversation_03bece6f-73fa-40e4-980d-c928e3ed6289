package com.zoombus.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试控制器，用于模拟各种HTTP错误状态码和数据库测试
 */
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
@Slf4j
public class TestController {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 模拟403错误（权限不足/Session过期）
     */
    @GetMapping("/test-403")
    public ResponseEntity<?> test403() {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("timestamp", System.currentTimeMillis());
        errorResponse.put("status", 403);
        errorResponse.put("error", "Forbidden");
        errorResponse.put("message", "权限不足，无法访问该资源");
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    /**
     * 模拟401错误（未授权）
     */
    @GetMapping("/test-401")
    public ResponseEntity<?> test401() {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("timestamp", System.currentTimeMillis());
        errorResponse.put("status", 401);
        errorResponse.put("error", "Unauthorized");
        errorResponse.put("message", "认证失败，请重新登录");
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }

    /**
     * 模拟500错误（服务器内部错误）
     */
    @GetMapping("/test-500")
    public ResponseEntity<?> test500() {
        throw new RuntimeException("服务器内部错误");
    }

    /**
     * 正常的测试端点
     */
    @GetMapping("/test-normal")
    public ResponseEntity<Map<String, Object>> testNormal() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "测试请求成功");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 数据库连接测试
     */
    @GetMapping("/test-database")
    public ResponseEntity<Map<String, Object>> testDatabase() {
        Map<String, Object> response = new HashMap<>();
        try {
            // 测试基本数据库连接
            String result = jdbcTemplate.queryForObject("SELECT 'Database connection OK' as result", String.class);

            // 检查PMI相关表
            List<String> tables = jdbcTemplate.queryForList(
                "SELECT table_name FROM information_schema.tables " +
                "WHERE table_schema = DATABASE() AND table_name LIKE 't_pmi_%'",
                String.class
            );

            response.put("success", true);
            response.put("message", result);
            response.put("pmiTables", tables);
            response.put("timestamp", LocalDateTime.now());

        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            response.put("success", false);
            response.put("message", "数据库连接失败: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
        }

        return ResponseEntity.ok(response);
    }
}
