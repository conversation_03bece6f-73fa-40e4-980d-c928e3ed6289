package com.zoombus.controller;

import com.zoombus.service.TransactionMonitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 事务监控API
 */
@RestController
@RequestMapping("/api/transaction-monitor")
@RequiredArgsConstructor
@Slf4j
public class TransactionMonitorController {

    private final TransactionMonitorService transactionMonitorService;

    /**
     * 获取事务统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getTransactionStatistics() {
        try {
            TransactionMonitorService.TransactionStatistics stats = 
                    transactionMonitorService.getStatistics();
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", stats
            ));
        } catch (Exception e) {
            log.error("获取事务统计信息失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "获取事务统计信息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 重置事务统计信息
     */
    @PostMapping("/statistics/reset")
    public ResponseEntity<Map<String, Object>> resetTransactionStatistics() {
        try {
            transactionMonitorService.resetStatistics();
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "事务统计信息已重置"
            ));
        } catch (Exception e) {
            log.error("重置事务统计信息失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "重置事务统计信息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取事务健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getTransactionHealth() {
        try {
            TransactionMonitorService.TransactionStatistics stats = 
                    transactionMonitorService.getStatistics();
            
            // 评估健康状态
            String healthStatus = evaluateHealthStatus(stats);
            String healthLevel = getHealthLevel(stats);
            
            Map<String, Object> health = Map.of(
                    "status", healthStatus,
                    "level", healthLevel,
                    "successRate", stats.getSuccessRate(),
                    "rollbackRate", stats.getRollbackRate(),
                    "activeTransactions", stats.getActiveTransactions(),
                    "totalTransactions", stats.getTotalTransactions(),
                    "healthy", "健康".equals(healthStatus)
            );
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", health
            ));
        } catch (Exception e) {
            log.error("获取事务健康状态失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "获取事务健康状态失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 评估健康状态
     */
    private String evaluateHealthStatus(TransactionMonitorService.TransactionStatistics stats) {
        double rollbackRate = stats.getRollbackRate();
        double successRate = stats.getSuccessRate();
        
        if (rollbackRate > 30 || successRate < 70) {
            return "异常";
        } else if (rollbackRate > 10 || successRate < 90) {
            return "警告";
        } else {
            return "健康";
        }
    }

    /**
     * 获取健康等级
     */
    private String getHealthLevel(TransactionMonitorService.TransactionStatistics stats) {
        double successRate = stats.getSuccessRate();
        
        if (successRate >= 95) {
            return "优秀";
        } else if (successRate >= 90) {
            return "良好";
        } else if (successRate >= 80) {
            return "一般";
        } else if (successRate >= 70) {
            return "较差";
        } else {
            return "很差";
        }
    }
}
