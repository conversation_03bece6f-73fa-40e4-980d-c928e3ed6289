package com.zoombus.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 用户前端控制器 - 处理用户端PMI页面路由
 * 只支持 /m/{pmiNumber} 直接访问，不支持搜索功能
 */
@Controller
@RequestMapping("/m")
@Slf4j
public class UserFrontendController {
    
    /**
     * 处理 /m/{pmiNumber} 路由，返回用户前端应用
     */
    @GetMapping("/{pmiNumber}")
    public ResponseEntity<Resource> pmiPage(@PathVariable String pmiNumber) {
        log.info("访问用户PMI页面: pmiNumber={}", pmiNumber);
        try {
            Resource resource = new ClassPathResource("static-user/index.html");
            if (resource.exists()) {
                return ResponseEntity.ok()
                        .contentType(MediaType.TEXT_HTML)
                        .body(resource);
            } else {
                log.warn("用户前端文件不存在，返回404");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("加载用户前端页面失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
