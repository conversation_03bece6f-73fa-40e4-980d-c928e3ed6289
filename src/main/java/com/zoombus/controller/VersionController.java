package com.zoombus.controller;

import com.zoombus.service.DatabaseVersionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 版本管理控制器
 * 提供版本信息查询和管理接口
 */
@RestController
@RequestMapping("/api/version")
@RequiredArgsConstructor
@Slf4j
public class VersionController {

    private final DatabaseVersionService databaseVersionService;

    /**
     * 获取当前版本信息
     */
    @GetMapping("/current")
    public ResponseEntity<Map<String, Object>> getCurrentVersion() {
        try {
            Map<String, Object> versionInfo = databaseVersionService.getCurrentVersionInfo();
            return ResponseEntity.ok(versionInfo);
        } catch (Exception e) {
            log.error("获取当前版本信息失败", e);
            return ResponseEntity.ok(Map.of(
                "error", true,
                "message", "获取版本信息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取版本历史记录
     */
    @GetMapping("/history")
    public ResponseEntity<Map<String, Object>> getVersionHistory(
            @RequestParam(defaultValue = "20") int limit) {
        try {
            List<Map<String, Object>> history = databaseVersionService.getVersionHistory(limit);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", history,
                "total", history.size()
            ));
        } catch (Exception e) {
            log.error("获取版本历史失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取版本历史失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 手动记录版本信息
     */
    @PostMapping("/record")
    public ResponseEntity<Map<String, Object>> recordVersion(
            @RequestBody Map<String, String> request) {
        try {
            String appVersion = request.get("applicationVersion");
            String dbVersion = request.get("databaseVersion");
            String description = request.get("description");

            if (appVersion == null) {
                appVersion = databaseVersionService.getCurrentVersionInfo()
                    .get("applicationVersion").toString();
            }
            
            if (dbVersion == null) {
                dbVersion = databaseVersionService.getCurrentDatabaseVersion();
            }

            databaseVersionService.recordVersionInfo(
                appVersion, 
                dbVersion, 
                "MANUAL_RECORD", 
                description
            );

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "版本信息记录成功",
                "applicationVersion", appVersion,
                "databaseVersion", dbVersion
            ));
        } catch (Exception e) {
            log.error("手动记录版本信息失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "记录版本信息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 检查版本兼容性
     */
    @GetMapping("/compatibility")
    public ResponseEntity<Map<String, Object>> checkCompatibility(
            @RequestParam String requiredDbVersion) {
        try {
            boolean compatible = databaseVersionService.isVersionCompatible(requiredDbVersion);
            String currentDbVersion = databaseVersionService.getCurrentDatabaseVersion();
            
            return ResponseEntity.ok(Map.of(
                "compatible", compatible,
                "currentDatabaseVersion", currentDbVersion,
                "requiredDatabaseVersion", requiredDbVersion,
                "message", compatible ? "版本兼容" : "版本不兼容，需要数据库升级"
            ));
        } catch (Exception e) {
            log.error("检查版本兼容性失败", e);
            return ResponseEntity.ok(Map.of(
                "compatible", false,
                "error", true,
                "message", "版本兼容性检查失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取Flyway迁移状态
     */
    @GetMapping("/flyway/status")
    public ResponseEntity<Map<String, Object>> getFlywayStatus() {
        try {
            // 这里可以集成Flyway API来获取详细的迁移状态
            String currentVersion = databaseVersionService.getCurrentDatabaseVersion();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "currentVersion", currentVersion,
                "message", "Flyway状态获取成功"
            ));
        } catch (Exception e) {
            log.error("获取Flyway状态失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取Flyway状态失败: " + e.getMessage()
            ));
        }
    }
}
