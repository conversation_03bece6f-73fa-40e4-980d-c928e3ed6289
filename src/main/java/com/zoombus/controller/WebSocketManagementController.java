package com.zoombus.controller;

import com.zoombus.config.WebSocketProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket管理控制器
 * 用于运行时查看和控制WebSocket功能状态
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/websocket")
@RequiredArgsConstructor
public class WebSocketManagementController {
    
    private final WebSocketProperties webSocketProperties;
    
    /**
     * 获取WebSocket配置状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getWebSocketStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("enabled", webSocketProperties.isEnabled());
        status.put("monitoring", Map.of(
            "enabled", webSocketProperties.getMonitoring().isEnabled(),
            "intervals", Map.of(
                "zoomAccount", webSocketProperties.getMonitoring().getIntervals().getZoomAccount(),
                "meeting", webSocketProperties.getMonitoring().getIntervals().getMeeting(),
                "pmi", webSocketProperties.getMonitoring().getIntervals().getPmi(),
                "system", webSocketProperties.getMonitoring().getIntervals().getSystem(),
                "comprehensive", webSocketProperties.getMonitoring().getIntervals().getComprehensive(),
                "alertCheck", webSocketProperties.getMonitoring().getIntervals().getAlertCheck()
            )
        ));
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * 更新监控推送开关（运行时）
     */
    @PostMapping("/monitoring/toggle")
    public ResponseEntity<Map<String, Object>> toggleMonitoring(@RequestParam boolean enabled) {
        log.info("运行时切换WebSocket监控推送状态: {} -> {}", 
                webSocketProperties.getMonitoring().isEnabled(), enabled);
        
        webSocketProperties.getMonitoring().setEnabled(enabled);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("monitoring_enabled", enabled);
        result.put("message", enabled ? "WebSocket监控推送已启用" : "WebSocket监控推送已禁用");
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 更新监控间隔（运行时）
     */
    @PostMapping("/monitoring/intervals")
    public ResponseEntity<Map<String, Object>> updateIntervals(@RequestBody Map<String, Long> intervals) {
        log.info("运行时更新WebSocket监控间隔: {}", intervals);
        
        WebSocketProperties.Intervals currentIntervals = webSocketProperties.getMonitoring().getIntervals();
        
        if (intervals.containsKey("zoomAccount")) {
            currentIntervals.setZoomAccount(intervals.get("zoomAccount"));
        }
        if (intervals.containsKey("meeting")) {
            currentIntervals.setMeeting(intervals.get("meeting"));
        }
        if (intervals.containsKey("pmi")) {
            currentIntervals.setPmi(intervals.get("pmi"));
        }
        if (intervals.containsKey("system")) {
            currentIntervals.setSystem(intervals.get("system"));
        }
        if (intervals.containsKey("comprehensive")) {
            currentIntervals.setComprehensive(intervals.get("comprehensive"));
        }
        if (intervals.containsKey("alertCheck")) {
            currentIntervals.setAlertCheck(intervals.get("alertCheck"));
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("updated_intervals", intervals);
        result.put("current_intervals", Map.of(
            "zoomAccount", currentIntervals.getZoomAccount(),
            "meeting", currentIntervals.getMeeting(),
            "pmi", currentIntervals.getPmi(),
            "system", currentIntervals.getSystem(),
            "comprehensive", currentIntervals.getComprehensive(),
            "alertCheck", currentIntervals.getAlertCheck()
        ));
        result.put("message", "监控间隔已更新");
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取WebSocket连接统计
     */
    @GetMapping("/connections")
    public ResponseEntity<Map<String, Object>> getConnectionStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 这里可以添加实际的连接统计逻辑
        stats.put("websocket_enabled", webSocketProperties.isEnabled());
        stats.put("monitoring_enabled", webSocketProperties.getMonitoring().isEnabled());
        stats.put("active_connections", 0); // 实际实现时可以统计真实连接数
        stats.put("total_messages_sent", 0); // 实际实现时可以统计消息数量
        
        return ResponseEntity.ok(stats);
    }
}
