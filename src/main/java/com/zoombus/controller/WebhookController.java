package com.zoombus.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.entity.Meeting;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.WebhookEvent;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.MeetingRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.service.WebhookService;
import com.zoombus.service.ZoomMeetingEventService;
import com.zoombus.service.ZoomMeetingService;
import com.zoombus.service.WebhookMonitorService;
import com.zoombus.trace.TraceContext;
import com.zoombus.trace.TraceIdGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

@RestController
@RequestMapping("/api/webhooks")
@RequiredArgsConstructor
@Slf4j
public class WebhookController {

    private final WebhookService webhookService;
    private final ObjectMapper objectMapper;
    private final ZoomMeetingEventService zoomMeetingEventService;
    private final ZoomMeetingService zoomMeetingService;
    private final WebhookMonitorService webhookMonitorService;
    private final ZoomMeetingRepository zoomMeetingRepository;
    private final MeetingRepository meetingRepository;
    
    /**
     * 接收Zoom Webhook事件（支持多账号）
     * URL格式: /api/webhooks/zoom/{accountId}
     */
    @PostMapping("/zoom/{accountId}")
    public ResponseEntity<?> handleZoomWebhook(@PathVariable String accountId,
                                              @RequestBody String payload,
                                              @RequestHeader(value = "Authorization", required = false) String authorization) {

        String traceId = TraceContext.getTraceId();

        try {
            log.info("收到Zoom Webhook [账号ID: {}]: {}", accountId, payload);

            // 验证账号ID是否存在
            if (!webhookService.isValidAccountId(accountId)) {
                log.warn("无效的账号ID: {}", accountId);
                return ResponseEntity.status(404).body("Account not found");
            }

            // 验证Webhook签名 (暂时禁用用于测试)
            // if (!webhookService.verifyWebhookSignature(accountId, payload, authorization)) {
            //     log.warn("Webhook签名验证失败 [账号ID: {}]", accountId);
            //     return ResponseEntity.status(401).body("Unauthorized");
            // }
            log.info("跳过签名验证 (测试模式) [账号ID: {}]", accountId);

            JsonNode eventData = objectMapper.readTree(payload);
            String eventType = eventData.get("event").asText();

            // 增强TraceId包含事件信息
            String enhancedTraceId = enhanceWebhookTraceId(traceId, eventType, accountId, eventData);
            TraceContext.setTraceId(enhancedTraceId);

            // 特殊处理endpoint.url_validation事件
            if ("endpoint.url_validation".equals(eventType)) {
                JsonNode payloadNode = eventData.get("payload");
                if (payloadNode != null && payloadNode.has("plainToken")) {
                    String plainToken = payloadNode.get("plainToken").asText();
                    log.info("返回Zoom端点验证token [账号ID: {}]: {}", accountId, plainToken);

                    // 仍然处理事件以记录日志
                    webhookService.processWebhookEvent(accountId, eventType, eventData);

                    // 生成encryptedToken
                    String encryptedToken = webhookService.encryptToken(accountId, plainToken);
                    log.info("生成加密token [账号ID: {}]: plainToken={}, encryptedToken={}",
                            accountId, plainToken, encryptedToken);

                    // 根据Zoom官方文档，返回包含plainToken和encryptedToken的JSON响应
                    Map<String, Object> response = new HashMap<>();
                    response.put("plainToken", plainToken);
                    response.put("encryptedToken", encryptedToken);

                    return ResponseEntity.status(200)
                            .contentType(MediaType.APPLICATION_JSON)
                            .header("Cache-Control", "no-cache")
                            .body(response);
                }
            }

            // 处理其他事件（传入账号ID）
            webhookService.processWebhookEvent(accountId, eventType, eventData);

            // 处理会议相关事件
            handleMeetingEvents(eventType, eventData);

            return ResponseEntity.ok("OK");

        } catch (Exception e) {
            log.error("处理Zoom Webhook失败 [账号ID: {}]", accountId, e);
            return ResponseEntity.status(500).body("Internal Server Error");
        }
    }

    /**
     * 接收Zoom Webhook事件（兼容旧版本，使用默认账号）
     */
    @PostMapping("/zoom")
    public ResponseEntity<?> handleZoomWebhookLegacy(@RequestBody String payload,
                                                    @RequestHeader(value = "Authorization", required = false) String authorization) {
        try {
            log.info("收到Zoom Webhook (默认账号): {}", payload);

            JsonNode eventData = objectMapper.readTree(payload);
            String eventType = eventData.get("event").asText();

            // 特殊处理endpoint.url_validation事件
            if ("endpoint.url_validation".equals(eventType)) {
                JsonNode payloadNode = eventData.get("payload");
                if (payloadNode != null && payloadNode.has("plainToken")) {
                    String plainToken = payloadNode.get("plainToken").asText();
                    log.info("返回Zoom端点验证token (默认账号): {}", plainToken);

                    // 仍然处理事件以记录日志
                    webhookService.processWebhookEventLegacy(eventType, eventData);

                    // 生成encryptedToken
                    String encryptedToken = webhookService.encryptTokenLegacy(plainToken);
                    log.info("生成加密token (默认账号): plainToken={}, encryptedToken={}",
                            plainToken, encryptedToken);

                    // 根据Zoom官方文档，返回包含plainToken和encryptedToken的JSON响应
                    Map<String, Object> response = new HashMap<>();
                    response.put("plainToken", plainToken);
                    response.put("encryptedToken", encryptedToken);

                    return ResponseEntity.status(200)
                            .contentType(MediaType.APPLICATION_JSON)
                            .header("Cache-Control", "no-cache")
                            .body(response);
                }
            }

            // 使用默认账号处理其他事件
            webhookService.processWebhookEventLegacy(eventType, eventData);

            return ResponseEntity.ok("OK");

        } catch (Exception e) {
            log.error("处理Zoom Webhook失败 (默认账号)", e);
            return ResponseEntity.status(500).body("Internal Server Error");
        }
    }
    
    /**
     * Zoom Webhook验证端点（支持多账号）
     * URL格式: /api/webhooks/zoom/{accountId}/validate
     */
    @PostMapping("/zoom/{accountId}/validate")
    public ResponseEntity<String> validateZoomWebhook(@PathVariable String accountId,
                                                     @RequestBody String payload) {
        try {
            log.info("收到Zoom Webhook验证请求 [账号ID: {}]: {}", accountId, payload);

            // 验证账号ID是否存在
            if (!webhookService.isValidAccountId(accountId)) {
                log.warn("无效的账号ID: {}", accountId);
                return ResponseEntity.status(404).body("Account not found");
            }

            JsonNode eventData = objectMapper.readTree(payload);

            // 如果是验证请求，返回challenge
            if (eventData.has("event") && "endpoint.url_validation".equals(eventData.get("event").asText())) {
                JsonNode payloadNode = eventData.get("payload");
                if (payloadNode != null && payloadNode.has("plainToken")) {
                    String plainToken = payloadNode.get("plainToken").asText();

                    // 使用对应账号的Webhook Secret Token进行加密
                    String encryptedToken = webhookService.encryptToken(accountId, plainToken);

                    return ResponseEntity.ok().body("{\"plainToken\":\"" + plainToken + "\",\"encryptedToken\":\"" + encryptedToken + "\"}");
                }
            }

            return ResponseEntity.ok("OK");

        } catch (Exception e) {
            log.error("验证Zoom Webhook失败 [账号ID: {}]", accountId, e);
            return ResponseEntity.status(500).body("Internal Server Error");
        }
    }

    /**
     * Zoom Webhook验证端点（兼容旧版本）
     */
    @PostMapping("/zoom/validate")
    public ResponseEntity<String> validateZoomWebhookLegacy(@RequestBody String payload) {
        try {
            JsonNode eventData = objectMapper.readTree(payload);

            // 如果是验证请求，返回challenge
            if (eventData.has("event") && "endpoint.url_validation".equals(eventData.get("event").asText())) {
                JsonNode payloadNode = eventData.get("payload");
                if (payloadNode != null && payloadNode.has("plainToken")) {
                    String plainToken = payloadNode.get("plainToken").asText();

                    // 使用默认账号的Webhook Secret Token进行加密
                    String encryptedToken = webhookService.encryptTokenLegacy(plainToken);

                    return ResponseEntity.ok().body("{\"plainToken\":\"" + plainToken + "\",\"encryptedToken\":\"" + encryptedToken + "\"}");
                }
            }

            return ResponseEntity.ok("OK");

        } catch (Exception e) {
            log.error("验证Zoom Webhook失败 (默认账号)", e);
            return ResponseEntity.status(500).body("Internal Server Error");
        }
    }
    
    /**
     * 获取所有Webhook事件
     */
    @GetMapping("/events")
    public ResponseEntity<List<WebhookEvent>> getAllWebhookEvents() {
        List<WebhookEvent> events = webhookService.getAllWebhookEvents();
        return ResponseEntity.ok(events);
    }
    
    /**
     * 根据处理状态获取Webhook事件
     */
    @GetMapping("/events/status/{status}")
    public ResponseEntity<List<WebhookEvent>> getWebhookEventsByStatus(@PathVariable WebhookEvent.ProcessingStatus status) {
        List<WebhookEvent> events = webhookService.getWebhookEventsByStatus(status);
        return ResponseEntity.ok(events);
    }

    /**
     * 根据账号ID获取Webhook事件
     */
    @GetMapping("/events/account/{accountId}")
    public ResponseEntity<List<WebhookEvent>> getWebhookEventsByAccount(@PathVariable String accountId) {
        List<WebhookEvent> events = webhookService.getWebhookEventsByAccount(accountId);
        return ResponseEntity.ok(events);
    }

    /**
     * 获取所有账号的Webhook统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Object> getWebhookStats() {
        Object stats = webhookService.getWebhookStats();
        return ResponseEntity.ok(stats);
    }

    /**
     * 重放Webhook事件
     * 重新向后端发送指定的webhook事件
     */
    @PostMapping("/events/{eventId}/replay")
    public ResponseEntity<Map<String, Object>> replayWebhookEvent(@PathVariable Long eventId) {
        try {
            log.info("开始重放Webhook事件: {}", eventId);

            // 获取事件详情
            WebhookEvent event = webhookService.getWebhookEventById(eventId);
            if (event == null) {
                return ResponseEntity.status(404).body(Map.of(
                    "success", false,
                    "message", "事件不存在"
                ));
            }

            // 解析事件数据
            JsonNode eventData = objectMapper.readTree(event.getEventData());
            String eventType = event.getEventType();
            String accountId = event.getZoomAccountId();

            log.info("重放事件详情: eventType={}, accountId={}, eventId={}", eventType, accountId, eventId);

            // 重新处理事件
            if (accountId != null && !accountId.isEmpty()) {
                // 使用多账号处理方式
                webhookService.processWebhookEvent(accountId, eventType, eventData);
            } else {
                // 使用默认账号处理方式（兼容旧数据）
                webhookService.processWebhookEvent(eventType, eventData);
            }

            // 如果是会议相关事件，也重新处理会议事件
            if (eventType.startsWith("meeting.")) {
                handleMeetingEvents(eventType, eventData);
            }

            log.info("Webhook事件重放成功: {}", eventId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "事件重放成功",
                "eventId", eventId,
                "eventType", eventType
            ));

        } catch (Exception e) {
            log.error("重放Webhook事件失败: eventId={}", eventId, e);
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "重放失败: " + e.getMessage(),
                "eventId", eventId
            ));
        }
    }

    // ========== 会议事件处理方法 ==========

    /**
     * 处理会议相关事件
     */
    private void handleMeetingEvents(String eventType, JsonNode eventData) {
        try {
            JsonNode payload = eventData.get("payload");
            if (payload == null) {
                log.warn("Webhook payload为空，跳过会议事件处理");
                return;
            }

            JsonNode meetingObj = payload.get("object");
            if (meetingObj == null) {
                log.warn("会议对象为空，跳过会议事件处理");
                return;
            }

            // 提取会议信息
            String meetingUuid = meetingObj.has("uuid") ? meetingObj.get("uuid").asText() : null;
            String meetingId = meetingObj.has("id") ? meetingObj.get("id").asText() : null;
            String hostId = meetingObj.has("host_id") ? meetingObj.get("host_id").asText() : null;
            String topic = meetingObj.has("topic") ? meetingObj.get("topic").asText() : null;

            // 处理不同的事件类型
            boolean success = true;
            try {
                switch (eventType) {
                    case "meeting.started":
                        handleMeetingStarted(meetingUuid, meetingId, hostId, topic);
                        break;
                    case "meeting.ended":
                        handleMeetingEnded(meetingUuid, meetingId, hostId);
                        break;
                    case "meeting.participant_joined":
                        handleParticipantJoined(meetingUuid, payload);
                        break;
                    case "meeting.participant_left":
                        handleParticipantLeft(meetingUuid, payload);
                        break;
                    default:
                        // 非会议事件，不处理
                        return;
                }
            } catch (Exception e) {
                success = false;
                log.error("处理会议事件失败: event={}", eventType, e);
                throw e;
            } finally {
                // 记录Webhook事件统计
                webhookMonitorService.recordWebhookEvent(eventType, success);
            }

        } catch (Exception e) {
            log.error("处理会议事件异常: eventType={}", eventType, e);
        }
    }

    /**
     * 处理会议开始事件
     */
    private void handleMeetingStarted(String meetingUuid, String meetingId, String hostId, String topic) {
        try {
            log.info("处理会议开始事件: uuid={}, id={}, hostId={}, topic={}", meetingUuid, meetingId, hostId, topic);

            // 使用统一的会议开始处理逻辑（包含查找现有记录和创建新记录的完整逻辑）
            zoomMeetingService.handleMeetingStarted(meetingUuid, meetingId, hostId, topic);

            log.info("会议开始事件处理完成: {}", meetingUuid);

        } catch (Exception e) {
            log.error("处理会议开始事件失败: uuid={}", meetingUuid, e);
        }
    }

    /**
     * 处理会议结束事件
     */
    private void handleMeetingEnded(String meetingUuid, String meetingId, String hostId) {
        String operationId = UUID.randomUUID().toString().substring(0, 8);
        long startTime = System.currentTimeMillis();

        try {
            log.info("🎯 [{}] 开始处理会议结束事件: uuid={}, id={}, hostId={}",
                operationId, meetingUuid, meetingId, hostId);

            // 参数验证
            if (meetingUuid == null || meetingUuid.trim().isEmpty()) {
                log.error("❌ [{}] 会议UUID为空，无法处理结束事件", operationId);
                webhookMonitorService.recordWebhookEvent("meeting.ended", false);
                return;
            }

            // 查找对应的会议记录
            Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);

            if (meetingOpt.isPresent()) {
                ZoomMeeting meeting = meetingOpt.get();
                log.info("📋 [{}] 找到会议记录: meetingId={}, status={}, startTime={}",
                    operationId, meeting.getId(), meeting.getStatus(), meeting.getStartTime());

                // 检查会议状态，只处理STARTED状态的会议
                if (meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED) {
                    log.info("✅ [{}] 会议状态正确，开始完整结束流程: meetingId={}, status={}",
                            operationId, meeting.getId(), meeting.getStatus());

                    try {
                        // 复用Zoom会议看板的完整结束逻辑
                        // 这包括：状态更新、时长计算、结算、释放ZoomUser
                        zoomMeetingService.handleMeetingEnded(meetingUuid);

                        long duration = System.currentTimeMillis() - startTime;
                        log.info("🎉 [{}] 会议结束事件处理完成，已执行完整结算流程: meetingId={}, uuid={}, 耗时={}ms",
                                operationId, meeting.getId(), meetingUuid, duration);

                        // 记录成功指标
                        webhookMonitorService.recordWebhookEvent("meeting.ended", true);

                    } catch (Exception e) {
                        log.error("❌ [{}] 会议结束处理失败: meetingId={}, uuid={}, error={}",
                            operationId, meeting.getId(), meetingUuid, e.getMessage(), e);

                        // 记录失败指标
                        webhookMonitorService.recordWebhookEvent("meeting.ended", false);

                        // 发送告警（如果有告警服务）
                        try {
                            // alertService.sendAlert("会议结束处理失败",
                            //     Map.of("meetingId", meeting.getId(), "uuid", meetingUuid, "error", e.getMessage()));
                        } catch (Exception alertException) {
                            log.warn("发送告警失败: {}", alertException.getMessage());
                        }

                        throw e; // 重新抛出异常以便上层处理
                    }

                } else {
                    log.warn("⚠️ [{}] 会议状态不是STARTED，跳过处理: meetingId={}, status={}, uuid={}",
                            operationId, meeting.getId(), meeting.getStatus(), meetingUuid);

                    // 记录跳过的指标
                    webhookMonitorService.recordWebhookEvent("meeting.ended", true);
                }

            } else {
                log.warn("⚠️ [{}] 未找到对应的会议记录: uuid={}", operationId, meetingUuid);

                // 尝试使用meetingId和hostId查找
                if (meetingId != null && hostId != null) {
                    log.info("🔍 [{}] 尝试使用meetingId和hostId查找会议: meetingId={}, hostId={}",
                        operationId, meetingId, hostId);

                    try {
                        ZoomApiResponse<String> result = zoomMeetingEventService.handleMeetingEnded(
                                meetingId, hostId, meetingUuid, null);

                        if (result.isSuccess()) {
                            log.info("✅ [{}] 通过meetingId和hostId处理成功: {}", operationId, result.getMessage());
                            webhookMonitorService.recordWebhookEvent("meeting.ended", true);
                        } else {
                            log.warn("⚠️ [{}] 通过meetingId和hostId处理失败: {}", operationId, result.getMessage());
                            webhookMonitorService.recordWebhookEvent("meeting.ended", false);
                        }
                    } catch (Exception e) {
                        log.error("❌ [{}] 备用处理方式异常: meetingId={}, hostId={}, error={}",
                            operationId, meetingId, hostId, e.getMessage(), e);
                        webhookMonitorService.recordWebhookEvent("meeting.ended", false);
                    }
                } else {
                    log.error("❌ [{}] 无法处理会议结束事件，缺少必要参数: meetingId={}, hostId={}",
                        operationId, meetingId, hostId);
                    webhookMonitorService.recordWebhookEvent("meeting.ended", false);
                }
            }

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ [{}] 处理会议结束事件失败: uuid={}, 耗时={}ms, error={}",
                operationId, meetingUuid, duration, e.getMessage(), e);

            // 记录失败指标
            webhookMonitorService.recordWebhookEvent("meeting.ended", false);

            // 不重新抛出异常，避免影响Webhook响应
        } finally {
            long totalDuration = System.currentTimeMillis() - startTime;
            log.info("🏁 [{}] 会议结束事件处理完成: uuid={}, 总耗时={}ms",
                operationId, meetingUuid, totalDuration);
        }
    }

    /**
     * 处理参与者加入事件
     */
    private void handleParticipantJoined(String meetingUuid, JsonNode payload) {
        try {
            log.info("处理参与者加入事件: uuid={}", meetingUuid);
            // 可以在这里添加参与者加入的处理逻辑
        } catch (Exception e) {
            log.error("处理参与者加入事件失败: uuid={}", meetingUuid, e);
        }
    }

    /**
     * 处理参与者离开事件
     */
    private void handleParticipantLeft(String meetingUuid, JsonNode payload) {
        try {
            log.info("处理参与者离开事件: uuid={}", meetingUuid);
            // 可以在这里添加参与者离开的处理逻辑
        } catch (Exception e) {
            log.error("处理参与者离开事件失败: uuid={}", meetingUuid, e);
        }
    }

    // ========== 测试接口 ==========

    /**
     * 手动触发会议开始事件（用于测试）
     */
    @PostMapping("/test/meeting-started")
    public ResponseEntity<Map<String, Object>> testMeetingStarted(@RequestBody Map<String, String> request) {
        try {
            String meetingId = request.get("meetingId");
            String hostId = request.get("hostId");
            String meetingUuid = request.get("meetingUuid");
            String topic = request.get("topic");

            log.info("测试会议开始事件: meetingId={}, hostId={}, uuid={}, topic={}", meetingId, hostId, meetingUuid, topic);

            // 使用修复后的统一处理逻辑
            zoomMeetingService.handleMeetingStarted(meetingUuid, meetingId, hostId, topic);

            // 查询创建的记录
            Optional<ZoomMeeting> createdMeeting = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会议开始事件处理成功");

            if (createdMeeting.isPresent()) {
                ZoomMeeting meeting = createdMeeting.get();
                Map<String, Object> meetingData = new HashMap<>();
                meetingData.put("id", meeting.getId());
                meetingData.put("uuid", meeting.getZoomMeetingUuid());
                meetingData.put("meetingId", meeting.getZoomMeetingId());
                meetingData.put("pmiRecordId", meeting.getPmiRecordId());
                meetingData.put("topic", meeting.getTopic());
                meetingData.put("status", meeting.getStatus());
                meetingData.put("billingMode", meeting.getBillingMode());
                meetingData.put("startTime", meeting.getStartTime());

                // 判断会议类型
                String meetingType;
                if (meeting.getPmiRecordId() != null) {
                    meetingType = "PMI会议";
                } else {
                    // 检查是否是安排会议
                    Optional<Meeting> scheduledMeeting = meetingRepository.findByZoomMeetingId(meeting.getZoomMeetingId());
                    meetingType = scheduledMeeting.isPresent() ? "安排会议" : "其他会议";
                }
                meetingData.put("meetingType", meetingType);

                response.put("meeting", meetingData);
            } else {
                response.put("meeting", null);
                response.put("message", "会议记录未找到，可能处理失败");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("测试会议开始事件失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "测试失败: " + e.getMessage(),
                "data", "",
                "errorCode", "TEST_ERROR"
            ));
        }
    }

    /**
     * 诊断meeting.started处理问题（详细记录每个步骤）
     */
    @PostMapping("/debug/meeting-started")
    public ResponseEntity<Map<String, Object>> debugMeetingStarted(@RequestBody Map<String, String> request) {
        Map<String, Object> debugInfo = new HashMap<>();
        List<String> steps = new ArrayList<>();

        try {
            String meetingId = request.get("meetingId");
            String hostId = request.get("hostId");
            String meetingUuid = request.get("meetingUuid");
            String topic = request.get("topic");

            steps.add("1. 接收参数: meetingId=" + meetingId + ", hostId=" + hostId + ", uuid=" + meetingUuid + ", topic=" + topic);

            // 检查数据库表结构
            try {
                String tableStructure = zoomMeetingRepository.findAll().isEmpty() ? "表为空但结构正常" : "表有数据";
                steps.add("2. 数据库表检查: " + tableStructure);
            } catch (Exception e) {
                steps.add("2. 数据库表检查失败: " + e.getMessage());
                debugInfo.put("databaseError", e.getMessage());
            }

            // 检查必填字段
            if (meetingUuid == null || meetingUuid.trim().isEmpty()) {
                steps.add("3. 验证失败: meetingUuid为空");
                debugInfo.put("validationError", "meetingUuid为空");
                debugInfo.put("steps", steps);
                return ResponseEntity.ok(debugInfo);
            }
            if (meetingId == null || meetingId.trim().isEmpty()) {
                steps.add("3. 验证失败: meetingId为空");
                debugInfo.put("validationError", "meetingId为空");
                debugInfo.put("steps", steps);
                return ResponseEntity.ok(debugInfo);
            }
            steps.add("3. 字段验证通过");

            // 检查重复UUID
            Optional<ZoomMeeting> existingMeeting = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid.trim());
            if (existingMeeting.isPresent()) {
                steps.add("4. UUID重复检查: 发现重复UUID，记录ID=" + existingMeeting.get().getId());
                debugInfo.put("duplicateUuid", true);
                debugInfo.put("existingMeetingId", existingMeeting.get().getId());
            } else {
                steps.add("4. UUID重复检查: 无重复，可以创建");
                debugInfo.put("duplicateUuid", false);
            }

            // 尝试创建最简单的记录
            try {
                ZoomMeeting testMeeting = new ZoomMeeting();
                testMeeting.setZoomMeetingUuid(meetingUuid.trim());
                testMeeting.setZoomMeetingId(meetingId.trim());
                testMeeting.setHostId(hostId);
                testMeeting.setTopic(topic != null ? topic : "测试会议");
                testMeeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
                testMeeting.setStartTime(LocalDateTime.now());
                testMeeting.setPmiRecordId(null); // 明确设置为null
                testMeeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);

                steps.add("5. 创建ZoomMeeting对象成功");

                ZoomMeeting savedMeeting = zoomMeetingRepository.save(testMeeting);
                steps.add("6. 保存到数据库成功，ID=" + savedMeeting.getId());

                debugInfo.put("success", true);
                debugInfo.put("savedMeetingId", savedMeeting.getId());
                debugInfo.put("savedMeeting", Map.of(
                    "id", savedMeeting.getId(),
                    "uuid", savedMeeting.getZoomMeetingUuid(),
                    "meetingId", savedMeeting.getZoomMeetingId(),
                    "pmiRecordId", savedMeeting.getPmiRecordId(),
                    "status", savedMeeting.getStatus(),
                    "createdAt", savedMeeting.getCreatedAt()
                ));

            } catch (Exception e) {
                steps.add("5. 保存失败: " + e.getClass().getSimpleName() + " - " + e.getMessage());
                debugInfo.put("saveError", e.getMessage());
                debugInfo.put("errorType", e.getClass().getSimpleName());

                // 检查是否是约束违反
                if (e.getMessage().contains("constraint") || e.getMessage().contains("duplicate") || e.getMessage().contains("null")) {
                    debugInfo.put("constraintViolation", true);
                }
            }

        } catch (Exception e) {
            steps.add("处理异常: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            debugInfo.put("generalError", e.getMessage());
        }

        debugInfo.put("steps", steps);
        return ResponseEntity.ok(debugInfo);
    }

    /**
     * 检查数据库表结构和约束
     */
    @GetMapping("/debug/table-structure")
    public ResponseEntity<Map<String, Object>> checkTableStructure() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查表是否存在以及基本信息
            long totalCount = zoomMeetingRepository.count();
            result.put("totalRecords", totalCount);

            // 尝试查询最近的几条记录
            List<ZoomMeeting> recentMeetings = zoomMeetingRepository.findAll()
                .stream()
                .limit(3)
                .collect(java.util.stream.Collectors.toList());

            List<Map<String, Object>> recentData = new ArrayList<>();
            for (ZoomMeeting meeting : recentMeetings) {
                Map<String, Object> meetingInfo = new HashMap<>();
                meetingInfo.put("id", meeting.getId());
                meetingInfo.put("uuid", meeting.getZoomMeetingUuid());
                meetingInfo.put("meetingId", meeting.getZoomMeetingId());
                meetingInfo.put("pmiRecordId", meeting.getPmiRecordId());
                meetingInfo.put("status", meeting.getStatus());
                meetingInfo.put("createdAt", meeting.getCreatedAt());
                recentData.add(meetingInfo);
            }
            result.put("recentRecords", recentData);

            // 检查是否可以创建一个测试记录（不实际保存）
            try {
                ZoomMeeting testMeeting = new ZoomMeeting();
                testMeeting.setZoomMeetingUuid("test-structure-check-" + System.currentTimeMillis());
                testMeeting.setZoomMeetingId("test-meeting-id");
                testMeeting.setHostId("test-host");
                testMeeting.setTopic("结构检查测试");
                testMeeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
                testMeeting.setStartTime(LocalDateTime.now());
                testMeeting.setPmiRecordId(null);
                testMeeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);

                result.put("canCreateObject", true);
                result.put("testObjectCreated", "成功创建测试对象（未保存）");

            } catch (Exception e) {
                result.put("canCreateObject", false);
                result.put("createObjectError", e.getMessage());
            }

            result.put("success", true);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 手动触发会议结束事件（用于测试）
     */
    @PostMapping("/test/meeting-ended")
    public ResponseEntity<Map<String, Object>> testMeetingEnded(@RequestBody Map<String, String> request) {
        try {
            String meetingId = request.get("meetingId");
            String hostId = request.get("hostId");
            String meetingUuid = request.get("meetingUuid");

            log.info("测试会议结束事件: meetingId={}, hostId={}, uuid={}", meetingId, hostId, meetingUuid);

            // 调用修复后的会议结束处理逻辑
            handleMeetingEnded(meetingUuid, meetingId, hostId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "会议结束事件处理完成",
                "data", "已执行完整的结算和释放ZoomUser流程"
            ));

        } catch (Exception e) {
            log.error("测试会议结束事件失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "测试失败: " + e.getMessage(),
                "data", "",
                "errorCode", "TEST_ERROR"
            ));
        }
    }

    /**
     * 手动触发会议更新事件（用于测试）
     */
    @PostMapping("/test/meeting-updated")
    public ResponseEntity<Map<String, Object>> testMeetingUpdated(@RequestBody String webhookPayload) {
        try {
            log.info("测试会议更新事件: {}", webhookPayload);

            JsonNode eventData = objectMapper.readTree(webhookPayload);
            String eventType = eventData.get("event").asText();

            if (!"meeting.updated".equals(eventType)) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "事件类型必须是meeting.updated",
                    "eventType", eventType
                ));
            }

            // 处理webhook事件
            webhookService.processWebhookEventLegacy(eventType, eventData);

            // 提取会议信息用于响应
            JsonNode payload = eventData.get("payload");
            JsonNode meetingObject = payload != null ? payload.get("object") : null;
            String meetingId = meetingObject != null ? meetingObject.get("id").asText() : "unknown";
            String topic = meetingObject != null && meetingObject.has("topic") ?
                meetingObject.get("topic").asText() : "unknown";

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "会议更新事件处理完成",
                "meetingId", meetingId,
                "topic", topic,
                "eventType", eventType
            ));

        } catch (Exception e) {
            log.error("测试会议更新事件失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "测试失败: " + e.getMessage(),
                "errorCode", "TEST_ERROR"
            ));
        }
    }

    /**
     * 增强Webhook TraceId，添加事件和业务信息
     */
    private String enhanceWebhookTraceId(String originalTraceId, String eventType, String accountId, JsonNode eventData) {
        if (originalTraceId == null) {
            return null;
        }

        try {
            StringBuilder enhanced = new StringBuilder(originalTraceId);

            // 添加事件类型
            if (eventType != null && !eventType.isEmpty()) {
                String cleanEventType = eventType.replaceAll("[^a-zA-Z0-9_]", "").toUpperCase();
                if (cleanEventType.length() > 15) {
                    cleanEventType = cleanEventType.substring(0, 15);
                }
                enhanced.append("-").append(cleanEventType);
            }

            // 添加业务ID（会议ID、用户ID等）
            String businessId = extractBusinessId(eventData);
            if (businessId != null && !businessId.isEmpty()) {
                enhanced.append("-").append(businessId);
            }

            String result = enhanced.toString();
            log.debug("增强Webhook TraceId: {} -> {}", originalTraceId, result);
            return result;

        } catch (Exception e) {
            log.warn("增强Webhook TraceId失败: {}", originalTraceId, e);
            return originalTraceId;
        }
    }

    /**
     * 从事件数据中提取业务ID
     */
    private String extractBusinessId(JsonNode eventData) {
        try {
            // 尝试提取会议ID
            JsonNode payload = eventData.get("payload");
            if (payload != null) {
                JsonNode object = payload.get("object");
                if (object != null) {
                    // 会议ID
                    if (object.has("id")) {
                        String meetingId = object.get("id").asText();
                        return "M" + meetingId;
                    }

                    // 用户ID
                    if (object.has("user_id")) {
                        String userId = object.get("user_id").asText();
                        return "U" + userId.substring(0, Math.min(userId.length(), 8));
                    }

                    // PMI号码
                    if (object.has("pmi")) {
                        String pmi = object.get("pmi").asText();
                        return "P" + pmi;
                    }
                }
            }

            return null;

        } catch (Exception e) {
            log.debug("提取业务ID失败", e);
            return null;
        }
    }
}
