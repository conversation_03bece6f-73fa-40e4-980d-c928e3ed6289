package com.zoombus.controller;

import com.zoombus.dto.ApiResponse;
import com.zoombus.entity.ZoomApiLog;
import com.zoombus.service.ZoomApiLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Zoom API日志管理控制器
 */
@RestController
@RequestMapping("/api/admin/zoom-api-logs")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
public class ZoomApiLogController {
    
    private final ZoomApiLogService zoomApiLogService;
    
    /**
     * 分页查询API日志
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<ZoomApiLog>>> getLogs(
            @RequestParam(required = false) String businessType,
            @RequestParam(required = false) Boolean isSuccess,
            @RequestParam(required = false) String apiPath,
            @RequestParam(required = false) String zoomUserId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(required = false) Long minDuration,
            @RequestParam(required = false) Long maxDuration,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "requestTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        try {
            // 默认查询最近24小时的数据
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(1);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<ZoomApiLog> logs = zoomApiLogService.findLogs(
                    businessType, isSuccess, apiPath, zoomUserId, 
                    startTime, endTime, minDuration, maxDuration, pageable);
            
            return ResponseEntity.ok(ApiResponse.success(logs));
            
        } catch (Exception e) {
            log.error("查询API日志失败", e);
            return ResponseEntity.ok(ApiResponse.error("查询API日志失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取API调用统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            // 默认查询最近24小时的数据
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(1);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> stats = new HashMap<>();
            
            // 总体统计
            Object[] overallStats = zoomApiLogService.getOverallStats(startTime, endTime);
            if (overallStats != null && overallStats.length >= 6) {
                Map<String, Object> overall = new HashMap<>();
                overall.put("totalRequests", overallStats[0]);
                overall.put("successRequests", overallStats[1]);
                overall.put("avgDuration", overallStats[2]);
                overall.put("maxDuration", overallStats[3]);
                overall.put("minDuration", overallStats[4]);
                overall.put("slowRequests", overallStats[5]);
                
                // 计算成功率
                Long total = (Long) overallStats[0];
                Long success = (Long) overallStats[1];
                if (total > 0) {
                    overall.put("successRate", (double) success / total * 100);
                } else {
                    overall.put("successRate", 0.0);
                }
                
                stats.put("overall", overall);
            }
            
            // 按业务类型统计
            List<Object[]> businessTypeStats = zoomApiLogService.getStatsByBusinessType(startTime, endTime);
            stats.put("businessTypeStats", businessTypeStats);
            
            // 按小时统计
            List<Object[]> hourlyStats = zoomApiLogService.getStatsByHour(startTime, endTime);
            stats.put("hourlyStats", hourlyStats);
            
            // 响应状态码统计
            List<Object[]> statusStats = zoomApiLogService.getStatsByResponseStatus(startTime, endTime);
            stats.put("statusStats", statusStats);
            
            // 错误统计
            List<Object[]> errorStats = zoomApiLogService.getErrorStats(startTime, endTime);
            stats.put("errorStats", errorStats);
            
            // 慢请求统计（超过5秒）
            List<Object[]> slowRequestStats = zoomApiLogService.getSlowRequestStats(5000L, startTime, endTime);
            stats.put("slowRequestStats", slowRequestStats);
            
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            log.error("获取API统计信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取API统计信息失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取单个API日志详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ZoomApiLog>> getLogDetail(@PathVariable Long id) {
        try {
            ZoomApiLog log = zoomApiLogService.findById(id);
            if (log == null) {
                return ResponseEntity.ok(ApiResponse.error("API日志不存在"));
            }
            return ResponseEntity.ok(ApiResponse.success(log));
        } catch (Exception e) {
            log.error("获取API日志详情失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取API日志详情失败: " + e.getMessage()));
        }
    }
    
    /**
     * 清理过期日志
     */
    @DeleteMapping("/cleanup")
    public ResponseEntity<ApiResponse<String>> cleanupLogs(
            @RequestParam(defaultValue = "30") int daysToKeep) {
        try {
            zoomApiLogService.cleanupOldLogs(daysToKeep);
            return ResponseEntity.ok(ApiResponse.success("日志清理完成"));
        } catch (Exception e) {
            log.error("清理API日志失败", e);
            return ResponseEntity.ok(ApiResponse.error("清理API日志失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取业务类型列表
     */
    @GetMapping("/business-types")
    public ResponseEntity<ApiResponse<ZoomApiLog.BusinessType[]>> getBusinessTypes() {
        return ResponseEntity.ok(ApiResponse.success(ZoomApiLog.BusinessType.values()));
    }
    
    /**
     * 导出API日志（简化版本，返回CSV格式数据）
     */
    @GetMapping("/export")
    public ResponseEntity<String> exportLogs(
            @RequestParam(required = false) String businessType,
            @RequestParam(required = false) Boolean isSuccess,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "1000") int limit) {
        
        try {
            // 默认查询最近24小时的数据
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(1);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "requestTime"));
            Page<ZoomApiLog> logs = zoomApiLogService.findLogs(
                    businessType, isSuccess, null, null, 
                    startTime, endTime, null, null, pageable);
            
            StringBuilder csv = new StringBuilder();
            csv.append("请求时间,API路径,HTTP方法,业务类型,是否成功,响应状态码,耗时(ms),错误信息\n");
            
            for (ZoomApiLog apiLog : logs.getContent()) {
                csv.append(apiLog.getRequestTime()).append(",")
                   .append(apiLog.getApiPath()).append(",")
                   .append(apiLog.getApiMethod()).append(",")
                   .append(apiLog.getBusinessType()).append(",")
                   .append(apiLog.getIsSuccess()).append(",")
                   .append(apiLog.getResponseStatus()).append(",")
                   .append(apiLog.getDurationMs()).append(",")
                   .append(apiLog.getErrorMessage() != null ? apiLog.getErrorMessage().replace(",", ";") : "")
                   .append("\n");
            }
            
            return ResponseEntity.ok()
                    .header("Content-Type", "text/csv; charset=utf-8")
                    .header("Content-Disposition", "attachment; filename=zoom-api-logs.csv")
                    .body(csv.toString());
            
        } catch (Exception e) {
            log.error("导出API日志失败", e);
            return ResponseEntity.badRequest().body("导出失败: " + e.getMessage());
        }
    }
}
