package com.zoombus.controller;

import com.zoombus.dto.CreateZoomAuthRequest;
import com.zoombus.dto.SyncUsersResult;
import com.zoombus.dto.UpdateZoomAuthRequest;
import com.zoombus.dto.ZoomAuthEditResponse;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.service.ZoomAuthService;
import com.zoombus.service.ZoomUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/zoom-auth")
@RequiredArgsConstructor
public class ZoomAuthController {

    private final ZoomAuthService zoomAuthService;
    private final ZoomUserService zoomUserService;

    /**
     * 创建Zoom认证信息
     */
    @PostMapping
    public ResponseEntity<ZoomAuth> createZoomAuth(@Valid @RequestBody CreateZoomAuthRequest request) {
        ZoomAuth zoomAuth = zoomAuthService.createZoomAuth(request);
        return ResponseEntity.ok(zoomAuth);
    }

    /**
     * 获取所有认证信息（分页）
     */
    @GetMapping
    public ResponseEntity<Page<ZoomAuth>> getAllZoomAuths(Pageable pageable) {
        Page<ZoomAuth> zoomAuths = zoomAuthService.getAllZoomAuths(pageable);
        return ResponseEntity.ok(zoomAuths);
    }

    /**
     * 搜索认证信息（分页）
     */
    @GetMapping("/search")
    public ResponseEntity<Page<ZoomAuth>> searchZoomAuths(
            @RequestParam(required = false) String keyword,
            Pageable pageable) {
        Page<ZoomAuth> zoomAuths = zoomAuthService.searchZoomAuths(keyword, pageable);
        return ResponseEntity.ok(zoomAuths);
    }


    /**
     * 根据ID获取认证信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<ZoomAuth> getZoomAuthById(@PathVariable Long id) {
        return zoomAuthService.getZoomAuthById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 根据ID获取认证信息用于编辑（包含敏感字段）
     */
    @GetMapping("/{id}/edit")
    public ResponseEntity<ZoomAuthEditResponse> getZoomAuthForEdit(@PathVariable Long id) {
        return zoomAuthService.getZoomAuthById(id)
                .map(ZoomAuthEditResponse::fromEntity)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 根据账号名称获取认证信息
     */
    @GetMapping("/account/{accountName}")
    public ResponseEntity<ZoomAuth> getZoomAuthByAccountName(@PathVariable String accountName) {
        return zoomAuthService.getZoomAuthByAccountName(accountName)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 获取默认认证信息
     */
    @GetMapping("/default")
    public ResponseEntity<ZoomAuth> getDefaultZoomAuth() {
        return zoomAuthService.getDefaultZoomAuth()
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 更新认证信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<ZoomAuth> updateZoomAuth(@PathVariable Long id,
                                                  @Valid @RequestBody UpdateZoomAuthRequest request) {
        ZoomAuth zoomAuth = zoomAuthService.updateZoomAuth(id, request);
        return ResponseEntity.ok(zoomAuth);
    }

    /**
     * 删除认证信息
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteZoomAuth(@PathVariable Long id) {
        zoomAuthService.deleteZoomAuth(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 手动刷新token
     */
    @PostMapping("/{id}/refresh-token")
    public ResponseEntity<ZoomAuth> refreshToken(@PathVariable Long id) {
        ZoomAuth zoomAuth = zoomAuthService.refreshToken(id);
        return ResponseEntity.ok(zoomAuth);
    }

    /**
     * 更新认证状态
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<ZoomAuth> updateZoomAuthStatus(@PathVariable Long id,
                                                        @RequestParam ZoomAuth.AuthStatus status) {
        ZoomAuth zoomAuth = zoomAuthService.updateZoomAuthStatus(id, status);
        return ResponseEntity.ok(zoomAuth);
    }

    /**
     * 根据状态获取认证信息
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<List<ZoomAuth>> getZoomAuthsByStatus(@PathVariable ZoomAuth.AuthStatus status) {
        List<ZoomAuth> zoomAuths = zoomAuthService.getZoomAuthsByStatus(status);
        return ResponseEntity.ok(zoomAuths);
    }

    /**
     * 根据认证类型获取认证信息
     */
    @GetMapping("/auth-type/{authType}")
    public ResponseEntity<List<ZoomAuth>> getZoomAuthsByAuthType(@PathVariable ZoomAuth.AuthType authType) {
        List<ZoomAuth> zoomAuths = zoomAuthService.getZoomAuthsByAuthType(authType);
        return ResponseEntity.ok(zoomAuths);
    }

    /**
     * 获取认证统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Object> getZoomAuthStats() {
        Object stats = zoomAuthService.getZoomAuthStats();
        return ResponseEntity.ok(stats);
    }

    /**
     * 批量刷新所有即将过期的token
     */
    @PostMapping("/refresh-all")
    public ResponseEntity<String> refreshAllExpiredTokens() {
        zoomAuthService.refreshExpiredTokens();
        return ResponseEntity.ok("批量刷新任务已启动");
    }

    /**
     * 同步单个ZoomAuth的子账号用户信息
     */
    @PostMapping("/{id}/sync-users")
    public ResponseEntity<SyncUsersResult> syncUsers(@PathVariable Long id) {
        SyncUsersResult result = zoomUserService.syncUsersFromZoomApi(id);
        return ResponseEntity.ok(result);
    }

    /**
     * 批量同步多个ZoomAuth的子账号用户信息
     */
    @PostMapping("/batch-sync-users")
    public ResponseEntity<List<SyncUsersResult>> batchSyncUsers(@RequestBody List<Long> zoomAuthIds) {
        List<SyncUsersResult> results = zoomUserService.batchSyncUsersFromZoomApi(zoomAuthIds);
        return ResponseEntity.ok(results);
    }

    /**
     * 测试当前用户权限
     */
    @GetMapping("/test-auth")
    public ResponseEntity<Object> testAuth() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return ResponseEntity.ok(auth);
    }

}
