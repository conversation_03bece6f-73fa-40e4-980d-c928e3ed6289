package com.zoombus.controller;

import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.service.ZoomMeetingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Zoom会议管理控制器
 */
@RestController
@RequestMapping("/api/zoom-meetings")
@RequiredArgsConstructor
@Slf4j
public class ZoomMeetingController {
    
    private final ZoomMeetingService zoomMeetingService;
    
    /**
     * 获取活跃会议列表
     */
    @GetMapping("/active")
    public ResponseEntity<Map<String, Object>> getActiveMeetings(
            @RequestParam(required = false) String billingMode,
            @RequestParam(required = false) String keyword,
            Pageable pageable) {
        try {
            PmiRecord.BillingMode mode = null;
            if (billingMode != null && !billingMode.isEmpty()) {
                mode = PmiRecord.BillingMode.valueOf(billingMode);
            }
            
            Page<ZoomMeeting> meetings = zoomMeetingService.getActiveMeetingsWithFilters(mode, keyword, pageable);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", meetings);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取活跃会议列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取历史会议列表
     */
    @GetMapping("/history")
    public ResponseEntity<Map<String, Object>> getHistoryMeetings(
            @RequestParam(required = false) String billingMode,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Pageable pageable) {
        try {
            PmiRecord.BillingMode mode = null;
            if (billingMode != null && !billingMode.isEmpty()) {
                mode = PmiRecord.BillingMode.valueOf(billingMode);
            }
            
            Page<ZoomMeeting> meetings = zoomMeetingService.getHistoryMeetingsWithFilters(
                mode, keyword, startDate, endDate, pageable);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", meetings);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取历史会议列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取会议详情
     */
    @GetMapping("/{meetingId}")
    public ResponseEntity<Map<String, Object>> getMeetingDetail(@PathVariable Long meetingId) {
        try {
            ZoomMeeting meeting = zoomMeetingService.getMeetingById(meetingId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", meeting);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取会议详情失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 手动结束会议
     */
    @PostMapping("/{meetingId}/end")
    public ResponseEntity<Map<String, Object>> endMeeting(@PathVariable Long meetingId) {
        try {
            zoomMeetingService.endMeeting(meetingId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会议已结束");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("结束会议失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "结束失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取PMI会议统计
     */
    @GetMapping("/statistics/pmi/{pmiRecordId}")
    public ResponseEntity<Map<String, Object>> getPmiMeetingStatistics(@PathVariable Long pmiRecordId) {
        try {
            ZoomMeetingService.MeetingStatistics statistics = zoomMeetingService.getPmiMeetingStatistics(pmiRecordId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取PMI会议统计失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取会议看板数据
     */
    @GetMapping("/dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardData() {
        try {
            // 获取活跃会议数量
            Page<ZoomMeeting> activeMeetings = zoomMeetingService.getActiveMeetings(
                org.springframework.data.domain.PageRequest.of(0, 1));
            
            // 获取今日历史会议数量
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
            Page<ZoomMeeting> todayMeetings = zoomMeetingService.getHistoryMeetingsWithFilters(
                null, null, todayStart, todayEnd, 
                org.springframework.data.domain.PageRequest.of(0, 1));
            
            Map<String, Object> dashboardData = new HashMap<>();
            dashboardData.put("activeMeetingsCount", activeMeetings.getTotalElements());
            dashboardData.put("todayMeetingsCount", todayMeetings.getTotalElements());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", dashboardData);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取会议看板数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
