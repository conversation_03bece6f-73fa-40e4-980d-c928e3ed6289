package com.zoombus.controller;

import com.zoombus.entity.ZoomMeeting;
import com.zoombus.service.MeetingLifecycleManager;
import com.zoombus.service.ZoomMeetingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Zoom会议UUID控制器
 * 基于UUID进行会议操作，解决同一meeting_id多次开启的问题
 */
@RestController
@RequestMapping("/api/zoom-meetings-uuid")
@RequiredArgsConstructor
@Slf4j
public class ZoomMeetingUuidController {

    private final ZoomMeetingService zoomMeetingService;
    private final MeetingLifecycleManager meetingLifecycleManager;

    /**
     * 通过UUID获取会议详情
     */
    @GetMapping("/{meetingUuid}")
    public ResponseEntity<Map<String, Object>> getMeetingDetailByUuid(@PathVariable String meetingUuid) {
        try {
            Optional<ZoomMeeting> meetingOpt = zoomMeetingService.findByUuid(meetingUuid);
            
            if (!meetingOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "会议不存在");
                return ResponseEntity.notFound().build();
            }

            ZoomMeeting meeting = meetingOpt.get();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", buildMeetingResponse(meeting));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取会议详情失败: meetingUuid={}", meetingUuid, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取会议详情失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 通过UUID结束会议
     */
    @PostMapping("/{meetingUuid}/end")
    public ResponseEntity<Map<String, Object>> endMeetingByUuid(@PathVariable String meetingUuid) {
        try {
            log.info("收到结束会议请求: meetingUuid={}", meetingUuid);
            
            Optional<ZoomMeeting> meetingOpt = zoomMeetingService.findByUuid(meetingUuid);
            
            if (!meetingOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "会议不存在");
                return ResponseEntity.notFound().build();
            }

            ZoomMeeting meeting = meetingOpt.get();
            
            // 检查会议状态
            if (meeting.getStatus() != ZoomMeeting.MeetingStatus.STARTED &&
                meeting.getStatus() != ZoomMeeting.MeetingStatus.WAITING) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只能结束等待开始或正在进行中的会议，当前状态: " + meeting.getStatus());
                return ResponseEntity.badRequest().body(response);
            }

            // 使用UUID进行会议结束处理
            zoomMeetingService.endMeetingByUuid(meetingUuid);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会议已结束");
            response.put("meetingUuid", meetingUuid);
            response.put("meetingId", meeting.getZoomMeetingId());
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalStateException e) {
            log.warn("结束会议失败 - 状态错误: meetingUuid={}, error={}", meetingUuid, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            log.error("结束会议失败: meetingUuid={}", meetingUuid, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "结束会议失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 通过UUID更新会议状态
     */
    @PutMapping("/{meetingUuid}/status")
    public ResponseEntity<Map<String, Object>> updateMeetingStatusByUuid(
            @PathVariable String meetingUuid,
            @RequestParam ZoomMeeting.MeetingStatus newStatus,
            @RequestParam(required = false, defaultValue = "手动更新") String reason) {
        try {
            log.info("收到更新会议状态请求: meetingUuid={}, newStatus={}, reason={}", 
                    meetingUuid, newStatus, reason);
            
            boolean success = meetingLifecycleManager.updateMeetingStatusByUuid(meetingUuid, newStatus, reason);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "状态更新成功" : "状态更新失败");
            response.put("meetingUuid", meetingUuid);
            response.put("newStatus", newStatus);
            
            return ResponseEntity.ok(response);
            
        } catch (MeetingLifecycleManager.ResourceNotFoundException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "会议不存在");
            return ResponseEntity.notFound().build();
            
        } catch (IllegalStateException e) {
            log.warn("更新会议状态失败 - 状态转换错误: meetingUuid={}, newStatus={}, error={}", 
                    meetingUuid, newStatus, e.getMessage());
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            log.error("更新会议状态失败: meetingUuid={}, newStatus={}", meetingUuid, newStatus, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 构建会议响应数据
     */
    private Map<String, Object> buildMeetingResponse(ZoomMeeting meeting) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", meeting.getId());
        data.put("zoomMeetingId", meeting.getZoomMeetingId());
        data.put("zoomMeetingUuid", meeting.getZoomMeetingUuid());
        data.put("topic", meeting.getTopic());
        data.put("status", meeting.getStatus());
        data.put("hostId", meeting.getHostId());
        data.put("creationSource", meeting.getCreationSource());
        data.put("billingMode", meeting.getBillingMode());
        data.put("startTime", meeting.getStartTime());
        data.put("endTime", meeting.getEndTime());
        data.put("durationMinutes", meeting.getDurationMinutes());
        data.put("isSettled", meeting.getIsSettled());
        data.put("createdAt", meeting.getCreatedAt());
        data.put("updatedAt", meeting.getUpdatedAt());
        return data;
    }
}
