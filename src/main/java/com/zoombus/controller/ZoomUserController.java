package com.zoombus.controller;

import com.zoombus.dto.CreateZoomUserRequest;
import com.zoombus.dto.UpdateZoomUserRequest;
import com.zoombus.dto.SyncUsersResult;
import com.zoombus.entity.ZoomUser;
import com.zoombus.service.ZoomUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/zoom-users")
@RequiredArgsConstructor
@Slf4j
public class ZoomUserController {

    private final ZoomUserService zoomUserService;

    /**
     * 创建Zoom用户
     */
    @PostMapping
    public ResponseEntity<ZoomUser> createZoomUser(@Valid @RequestBody CreateZoomUserRequest request) {
        ZoomUser zoomUser = zoomUserService.createZoomUser(request);
        return ResponseEntity.ok(zoomUser);
    }

    /**
     * 更新Zoom用户
     */
    @PutMapping("/{id}")
    public ResponseEntity<ZoomUser> updateZoomUser(@PathVariable Long id,
                                                 @Valid @RequestBody UpdateZoomUserRequest request) {
        ZoomUser zoomUser = zoomUserService.updateZoomUser(id, request);
        return ResponseEntity.ok(zoomUser);
    }

    /**
     * 获取所有Zoom用户（分页）
     */
    @GetMapping
    public ResponseEntity<Page<ZoomUser>> getAllZoomUsers(Pageable pageable) {
        Page<ZoomUser> zoomUsers = zoomUserService.getAllZoomUsers(pageable);
        return ResponseEntity.ok(zoomUsers);
    }
    
    /**
     * 根据ZoomAuth获取用户列表（分页）
     */
    @GetMapping("/auth/{zoomAuthId}")
    public ResponseEntity<Page<ZoomUser>> getZoomUsersByAuth(@PathVariable Long zoomAuthId,
                                                           Pageable pageable) {
        Page<ZoomUser> zoomUsers = zoomUserService.getZoomUsersByAuth(zoomAuthId, pageable);
        return ResponseEntity.ok(zoomUsers);
    }

    /**
     * 根据用户ID获取Zoom用户列表（分页）
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Page<ZoomUser>> getZoomUsersByUserId(@PathVariable Long userId,
                                                             Pageable pageable) {
        Page<ZoomUser> zoomUsers = zoomUserService.getZoomUsersByUserId(userId, pageable);
        return ResponseEntity.ok(zoomUsers);
    }

    /**
     * 根据ZoomUser ID获取单个Zoom用户（用于从PMI管理页面跳转）
     */
    @GetMapping("/zoom-user/{zoomUserId}")
    public ResponseEntity<Page<ZoomUser>> getZoomUserByZoomUserId(@PathVariable Long zoomUserId,
                                                                Pageable pageable) {
        Page<ZoomUser> zoomUsers = zoomUserService.getZoomUserByZoomUserId(zoomUserId, pageable);
        return ResponseEntity.ok(zoomUsers);
    }
    
    /**
     * 根据ID获取Zoom用户
     */
    @GetMapping("/{id}")
    public ResponseEntity<ZoomUser> getZoomUserById(@PathVariable Long id) {
        return zoomUserService.getZoomUserById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据ZoomAuth和ZoomUserId获取用户
     */
    @GetMapping("/auth/{zoomAuthId}/zoom-user/{zoomUserId}")
    public ResponseEntity<ZoomUser> getZoomUserByZoomUserId(@PathVariable Long zoomAuthId,
                                                          @PathVariable String zoomUserId) {
        return zoomUserService.getZoomUserByZoomUserId(zoomAuthId, zoomUserId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据ZoomAuth和邮箱获取用户
     */
    @GetMapping("/auth/{zoomAuthId}/email/{email}")
    public ResponseEntity<ZoomUser> getZoomUserByEmail(@PathVariable Long zoomAuthId,
                                                     @PathVariable String email) {
        return zoomUserService.getZoomUserByEmail(zoomAuthId, email)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据状态获取用户列表
     */
    @GetMapping("/auth/{zoomAuthId}/status/{status}")
    public ResponseEntity<List<ZoomUser>> getZoomUsersByStatus(@PathVariable Long zoomAuthId,
                                                             @PathVariable ZoomUser.UserStatus status) {
        List<ZoomUser> zoomUsers = zoomUserService.getZoomUsersByStatus(zoomAuthId, status);
        return ResponseEntity.ok(zoomUsers);
    }
    
    /**
     * 根据用户类型获取用户列表
     */
    @GetMapping("/auth/{zoomAuthId}/user-type/{userType}")
    public ResponseEntity<List<ZoomUser>> getZoomUsersByUserType(@PathVariable Long zoomAuthId,
                                                               @PathVariable ZoomUser.UserType userType) {
        List<ZoomUser> zoomUsers = zoomUserService.getZoomUsersByUserType(zoomAuthId, userType);
        return ResponseEntity.ok(zoomUsers);
    }

    /**
     * 根据用户类型和账号用途获取用户列表（全局搜索）
     */
    @GetMapping("/user-type/{userType}/account-usage/{accountUsage}")
    public ResponseEntity<List<ZoomUser>> getZoomUsersByUserTypeAndAccountUsage(
            @PathVariable ZoomUser.UserType userType,
            @PathVariable ZoomUser.AccountUsage accountUsage) {
        List<ZoomUser> zoomUsers = zoomUserService.getZoomUsersByUserTypeAndAccountUsage(userType, accountUsage);
        return ResponseEntity.ok(zoomUsers);
    }

    /**
     * 根据用户类型和账号用途搜索用户（支持关键词搜索）
     */
    @GetMapping("/search/user-type/{userType}/account-usage/{accountUsage}")
    public ResponseEntity<Page<ZoomUser>> searchZoomUsersByUserTypeAndAccountUsage(
            @PathVariable ZoomUser.UserType userType,
            @PathVariable ZoomUser.AccountUsage accountUsage,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<ZoomUser> zoomUsers = zoomUserService.searchZoomUsersByUserTypeAndAccountUsage(userType, accountUsage, keyword, pageable);
        return ResponseEntity.ok(zoomUsers);
    }
    
    /**
     * 搜索用户（根据邮箱）
     */
    @GetMapping("/auth/{zoomAuthId}/search/email")
    public ResponseEntity<Page<ZoomUser>> searchZoomUsersByEmail(@PathVariable Long zoomAuthId,
                                                               @RequestParam String email,
                                                               Pageable pageable) {
        Page<ZoomUser> zoomUsers = zoomUserService.searchZoomUsersByEmail(zoomAuthId, email, pageable);
        return ResponseEntity.ok(zoomUsers);
    }

    /**
     * 搜索用户（根据姓名）
     */
    @GetMapping("/auth/{zoomAuthId}/search/name")
    public ResponseEntity<Page<ZoomUser>> searchZoomUsersByName(@PathVariable Long zoomAuthId,
                                                              @RequestParam String name,
                                                              Pageable pageable) {
        Page<ZoomUser> zoomUsers = zoomUserService.searchZoomUsersByName(zoomAuthId, name, pageable);
        return ResponseEntity.ok(zoomUsers);
    }

    /**
     * 全局搜索用户（根据邮箱）
     */
    @GetMapping("/search/email")
    public ResponseEntity<Page<ZoomUser>> globalSearchZoomUsersByEmail(@RequestParam String email,
                                                                     Pageable pageable) {
        Page<ZoomUser> zoomUsers = zoomUserService.globalSearchZoomUsersByEmail(email, pageable);
        return ResponseEntity.ok(zoomUsers);
    }

    /**
     * 全局搜索用户（根据姓名）
     */
    @GetMapping("/search/name")
    public ResponseEntity<Page<ZoomUser>> globalSearchZoomUsersByName(@RequestParam String name,
                                                                    Pageable pageable) {
        Page<ZoomUser> zoomUsers = zoomUserService.globalSearchZoomUsersByName(name, pageable);
        return ResponseEntity.ok(zoomUsers);
    }
    
    /**
     * 统计ZoomAuth下的用户数量
     */
    @GetMapping("/auth/{zoomAuthId}/count")
    public ResponseEntity<Long> countZoomUsersByAuth(@PathVariable Long zoomAuthId) {
        long count = zoomUserService.countZoomUsersByAuth(zoomAuthId);
        return ResponseEntity.ok(count);
    }
    
    /**
     * 统计各状态的用户数量
     */
    @GetMapping("/auth/{zoomAuthId}/stats/status")
    public ResponseEntity<List<Object[]>> countZoomUsersByStatus(@PathVariable Long zoomAuthId) {
        List<Object[]> stats = zoomUserService.countZoomUsersByStatus(zoomAuthId);
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 统计各用户类型的数量
     */
    @GetMapping("/auth/{zoomAuthId}/stats/user-type")
    public ResponseEntity<List<Object[]>> countZoomUsersByUserType(@PathVariable Long zoomAuthId) {
        List<Object[]> stats = zoomUserService.countZoomUsersByUserType(zoomAuthId);
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 同步Zoom用户信息
     */
    @PostMapping("/{id}/sync")
    public ResponseEntity<ZoomUser> syncZoomUserInfo(@PathVariable Long id) {
        ZoomUser zoomUser = zoomUserService.syncZoomUserInfo(id);
        return ResponseEntity.ok(zoomUser);
    }
    
    /**
     * 删除Zoom用户
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteZoomUser(@PathVariable Long id) {
        zoomUserService.deleteZoomUser(id);
        return ResponseEntity.ok().build();
    }
    
    /**
     * 批量删除Zoom用户
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Void> deleteZoomUsers(@RequestBody List<Long> ids) {
        zoomUserService.deleteZoomUsers(ids);
        return ResponseEntity.ok().build();
    }
    
    /**
     * 获取用户统计信息
     */
    @GetMapping("/auth/{zoomAuthId}/dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardStats(@PathVariable Long zoomAuthId) {
        long totalUsers = zoomUserService.countZoomUsersByAuth(zoomAuthId);
        List<Object[]> statusStats = zoomUserService.countZoomUsersByStatus(zoomAuthId);
        List<Object[]> typeStats = zoomUserService.countZoomUsersByUserType(zoomAuthId);

        Map<String, Object> dashboard = new HashMap<>();
        dashboard.put("totalUsers", totalUsers);
        dashboard.put("statusStats", statusStats);
        dashboard.put("typeStats", typeStats);

        return ResponseEntity.ok(dashboard);
    }

    /**
     * 获取全局用户统计信息
     */
    @GetMapping("/dashboard/global")
    public ResponseEntity<Map<String, Object>> getGlobalDashboardStats() {
        long totalUsers = zoomUserService.countAllZoomUsers();
        List<Object[]> statusStats = zoomUserService.countAllZoomUsersByStatus();
        List<Object[]> typeStats = zoomUserService.countAllZoomUsersByUserType();

        Map<String, Object> dashboard = new HashMap<>();
        dashboard.put("totalUsers", totalUsers);
        dashboard.put("statusStats", statusStats);
        dashboard.put("typeStats", typeStats);

        return ResponseEntity.ok(dashboard);
    }

    /**
     * 从Zoom API同步指定ZoomAuth的所有用户信息
     */
    @PostMapping("/auth/{zoomAuthId}/sync-from-api")
    public ResponseEntity<SyncUsersResult> syncUsersFromZoomApi(@PathVariable Long zoomAuthId) {
        SyncUsersResult result = zoomUserService.syncUsersFromZoomApi(zoomAuthId);
        return ResponseEntity.ok(result);
    }

    /**
     * 批量同步多个ZoomAuth的子账号用户信息
     */
    @PostMapping("/batch-sync-from-api")
    public ResponseEntity<List<SyncUsersResult>> batchSyncUsersFromZoomApi(@RequestBody List<Long> zoomAuthIds) {
        List<SyncUsersResult> results = zoomUserService.batchSyncUsersFromZoomApi(zoomAuthIds);
        return ResponseEntity.ok(results);
    }

    /**
     * 同步指定ZoomAuth的用户PMI信息
     * 专门用于补全original_pmi字段
     */
    @PostMapping("/auth/{zoomAuthId}/sync-pmi")
    public ResponseEntity<Map<String, Object>> syncUsersPmi(@PathVariable Long zoomAuthId) {
        try {
            log.info("开始同步ZoomAuth {} 的用户PMI信息", zoomAuthId);

            SyncUsersResult result = zoomUserService.syncUsersFromZoomApi(zoomAuthId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("data", result.getResult());
            response.put("message", result.isSuccess() ?
                "PMI同步完成，新增: " + result.getResult().getNewUsers() +
                ", 更新: " + result.getResult().getUpdatedUsers() +
                ", 跳过: " + result.getResult().getSkippedUsers() :
                result.getMessage());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("同步用户PMI信息失败，ZoomAuth ID: {}", zoomAuthId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "同步PMI信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 重置用户的host_key
     */
    @PostMapping("/{id}/reset-host-key")
    public ResponseEntity<Map<String, Object>> resetUserHostKey(@PathVariable Long id) {
        try {
            log.info("开始重置用户host_key，用户ID: {}", id);

            Map<String, Object> result = zoomUserService.resetUserHostKey(id);

            if ((Boolean) result.get("success")) {
                log.info("用户host_key重置成功，用户ID: {}", id);
                return ResponseEntity.ok(result);
            } else {
                log.warn("用户host_key重置失败，用户ID: {}, 原因: {}", id, result.get("message"));
                return ResponseEntity.badRequest().body(result);
            }

        } catch (Exception e) {
            log.error("重置用户host_key时发生异常，用户ID: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "重置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }






}
