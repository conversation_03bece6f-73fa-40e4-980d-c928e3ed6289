package com.zoombus.controller;

import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.service.ZoomUserPmiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * Zoom用户PMI管理控制器
 */
@RestController
@RequestMapping("/api/admin/zoom-users")
@RequiredArgsConstructor
@Slf4j
// 临时移除权限限制，用于测试回收功能
// @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
public class ZoomUserPmiController {
    
    private final ZoomUserPmiService zoomUserPmiService;
    
    /**
     * 生成随机PMI号码
     */
    @GetMapping("/generate-pmi")
    public ResponseEntity<Map<String, Object>> generatePmi() {
        try {
            String pmi = zoomUserPmiService.generateUniquePmi();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", Map.of("pmi", pmi),
                "message", "PMI生成成功"
            ));
        } catch (Exception e) {
            log.error("生成PMI失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "生成PMI失败: " + e.getMessage()
            ));
        }
    }
    
    /**
     * 验证PMI号码格式
     */
    @PostMapping("/validate-pmi")
    public ResponseEntity<Map<String, Object>> validatePmi(@RequestBody Map<String, String> request) {
        String pmi = request.get("pmi");
        
        if (pmi == null || pmi.trim().isEmpty()) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "PMI号码不能为空"
            ));
        }
        
        boolean isValid = zoomUserPmiService.isValidPmi(pmi);
        boolean isInUse = isValid && zoomUserPmiService.isPmiInUse(pmi);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "data", Map.of(
                "isValid", isValid,
                "isInUse", isInUse,
                "message", isValid ?
                    (isInUse ? "PMI号码已被使用" : "PMI号码格式正确") :
                    "PMI格式不正确：必须是10位数字，不以0、1开头"
            )
        ));
    }
    
    /**
     * 更新用户原始PMI
     */
    @PutMapping("/{userId}/original-pmi")
    public ResponseEntity<Map<String, Object>> updateOriginalPmi(
            @PathVariable @NotNull Long userId,
            @RequestBody Map<String, String> request) {
        
        String newPmi = request.get("pmi");
        if (newPmi == null || newPmi.trim().isEmpty()) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "PMI号码不能为空"
            ));
        }
        
        try {
            ZoomApiResponse<String> response = zoomUserPmiService.updateUserOriginalPmi(userId, newPmi);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", response.isSuccess());
            result.put("message", response.getMessage());
            if (response.getErrorCode() != null) {
                result.put("errorCode", response.getErrorCode());
            }
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("更新用户原始PMI失败: userId={}, pmi={}", userId, newPmi, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "更新PMI失败: " + e.getMessage()
            ));
        }
    }
    
    /**
     * 回收用户账号
     */
    @PostMapping("/{userId}/recycle")
    public ResponseEntity<Map<String, Object>> recycleAccount(@PathVariable @NotNull Long userId) {
        try {
            ZoomApiResponse<String> response = zoomUserPmiService.recycleUserAccount(userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", response.isSuccess());
            result.put("message", response.getMessage());
            if (response.getErrorCode() != null) {
                result.put("errorCode", response.getErrorCode());
            }
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("回收用户账号失败: userId={}", userId, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "回收账号失败: " + e.getMessage()
            ));
        }
    }
    
    /**
     * 测试优化后的PMI设置逻辑
     * 用于验证新的PMI设置流程：密码和等候室的安全策略
     */
    @PostMapping("/{userId}/test-optimized-pmi")
    public ResponseEntity<Map<String, Object>> testOptimizedPmiSetup(
            @PathVariable @NotNull Long userId,
            @RequestBody Map<String, String> request) {

        String targetPmi = request.get("pmi");
        String targetPassword = request.get("password");

        if (targetPmi == null || targetPmi.trim().isEmpty()) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "PMI号码不能为空"
            ));
        }

        try {
            log.info("测试优化后的PMI设置: userId={}, pmi={}, hasPassword={}",
                    userId, targetPmi, targetPassword != null && !targetPassword.trim().isEmpty());

            // 这里可以调用优化后的PMI设置逻辑
            // 暂时返回测试信息，显示会如何处理
            boolean hasPassword = targetPassword != null && !targetPassword.trim().isEmpty();

            Map<String, Object> testResult = new HashMap<>();
            testResult.put("userId", userId);
            testResult.put("targetPmi", targetPmi);
            testResult.put("hasPassword", hasPassword);

            if (hasPassword) {
                testResult.put("strategy", "设置密码，关闭等候室");
                testResult.put("steps", new String[]{
                    "1. 设置PMI号码: " + targetPmi,
                    "2. 设置密码: " + targetPassword,
                    "3. 关闭等候室",
                    "4. 验证PMI和密码设置"
                });
            } else {
                testResult.put("strategy", "无密码，开启等候室");
                testResult.put("steps", new String[]{
                    "1. 设置PMI号码: " + targetPmi,
                    "2. 开启等候室",
                    "3. 确保密码为空",
                    "4. 验证PMI设置和等候室状态"
                });
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", testResult,
                "message", "PMI设置策略测试完成"
            ));

        } catch (Exception e) {
            log.error("测试优化PMI设置失败: userId={}, pmi={}", userId, targetPmi, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "测试失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取用户PMI信息
     */
    @GetMapping("/{userId}/pmi-info")
    public ResponseEntity<Map<String, Object>> getUserPmiInfo(@PathVariable @NotNull Long userId) {
        try {
            // 这里需要实现获取用户PMI信息的逻辑
            // 暂时返回基本信息
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", Map.of(
                    "userId", userId,
                    "message", "功能开发中"
                )
            ));

        } catch (Exception e) {
            log.error("获取用户PMI信息失败: userId={}", userId, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取PMI信息失败: " + e.getMessage()
            ));
        }
    }
}
