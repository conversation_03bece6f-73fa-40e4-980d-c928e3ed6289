package com.zoombus.dto;

import lombok.Data;

import javax.validation.constraints.Future;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量重新调度请求DTO
 */
@Data
public class BatchRescheduleRequest {
    
    @NotNull(message = "任务ID列表不能为空")
    private List<Long> taskIds;
    
    @NotNull(message = "新执行时间不能为空")
    @Future(message = "新执行时间必须是未来时间")
    private LocalDateTime newExecuteTime;
    
    private String reason; // 重新调度原因
}
