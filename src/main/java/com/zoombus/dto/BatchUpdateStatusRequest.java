package com.zoombus.dto;

import com.zoombus.entity.PmiSchedule;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量更新计划状态请求
 */
@Data
public class BatchUpdateStatusRequest {
    
    @NotEmpty(message = "计划ID列表不能为空")
    private List<Long> scheduleIds;
    
    @NotNull(message = "状态不能为空")
    private PmiSchedule.ScheduleStatus status;
}
