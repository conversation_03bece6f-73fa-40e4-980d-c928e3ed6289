package com.zoombus.dto;

import com.zoombus.entity.AdminUser;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class CreateAdminUserRequest {
    
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;
    
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;
    
    @NotBlank(message = "姓名不能为空")
    @Size(max = 100, message = "姓名长度不能超过100个字符")
    private String fullName;
    
    @Size(max = 20, message = "电话号码长度不能超过20个字符")
    private String phone;
    
    @NotNull(message = "角色不能为空")
    private AdminUser.AdminRole role;
    
    private AdminUser.AdminStatus status = AdminUser.AdminStatus.ACTIVE;
}
