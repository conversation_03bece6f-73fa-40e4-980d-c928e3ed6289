package com.zoombus.dto;

import com.zoombus.entity.Meeting;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class CreateMeetingRequest {
    
    // 用户ID（可选）- 系统会自动查找对应的ZoomUser
    private Long userId;

    // ZoomUser ID（可选）- 直接指定ZoomUser进行会议创建
    private Long zoomUserIdForMeeting;
    
    @NotBlank(message = "会议主题不能为空")
    private String topic;
    
    private String agenda;
    
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    
    private Integer durationMinutes;
    
    private String timezone = "Asia/Shanghai";
    
    private String password;
    
    private Meeting.MeetingType type = Meeting.MeetingType.SCHEDULED;

    // 周期性会议相关字段
    private Boolean isRecurring = false;
    private Meeting.RecurrenceType recurrenceType;
    private Integer repeatInterval = 1;
    private String weeklyDays; // 每周重复时选择的星期几，如"1,3,5"

    // 每月重复相关字段
    private Meeting.MonthlyType monthlyType; // 每月重复方式
    private Integer monthlyDay; // 每月的日期或第几个
    private Integer monthlyWeekDay; // 每月的星期几

    private Meeting.EndType endType;
    private LocalDateTime endDateTime;
    private Integer endTimes;

    // 会议设置（基于Zoom API settings对象）
    private MeetingSettings settings;

    // 跟踪字段
    private TrackingField[] trackingFields;

    // 模板ID
    private String templateId;

    // 预安排会议
    private Boolean preSchedule = false;

    /**
     * 会议设置类
     */
    public static class MeetingSettings {
        // 音视频设置
        private Boolean hostVideo = true;
        private Boolean participantVideo = true;
        private String audio = "voip"; // both, telephony, voip
        private String autoRecording = "none"; // local, cloud, none

        // 会议控制
        private Boolean joinBeforeHost = false;
        private Integer jbhTime = 0;
        private Boolean muteUponEntry = true;
        private Boolean watermark = false;
        private Boolean usePmi = false;

        // 安全设置
        private Integer approvalType = 2; // 0=自动批准, 1=手动批准, 2=无需注册
        private Boolean enforceLogin = false;
        private String enforceLoginDomains = "";
        private String alternativeHosts = "";
        private Boolean waitingRoom = false;
        private Boolean meetingAuthentication = false;

        // 其他设置
        private Boolean allowMultipleDevices = false;
        private Boolean showShareButton = false;
        private Boolean registrantsConfirmationEmail = true;
        private Boolean emailNotification = true;
        private Boolean autoStartMeetingSummary = false;
        private Boolean autoStartAiCompanionQuestions = false;

        // Getters and Setters
        public Boolean getHostVideo() { return hostVideo; }
        public void setHostVideo(Boolean hostVideo) { this.hostVideo = hostVideo; }

        public Boolean getParticipantVideo() { return participantVideo; }
        public void setParticipantVideo(Boolean participantVideo) { this.participantVideo = participantVideo; }

        public String getAudio() { return audio; }
        public void setAudio(String audio) { this.audio = audio; }

        public String getAutoRecording() { return autoRecording; }
        public void setAutoRecording(String autoRecording) { this.autoRecording = autoRecording; }

        public Boolean getJoinBeforeHost() { return joinBeforeHost; }
        public void setJoinBeforeHost(Boolean joinBeforeHost) { this.joinBeforeHost = joinBeforeHost; }

        public Integer getJbhTime() { return jbhTime; }
        public void setJbhTime(Integer jbhTime) { this.jbhTime = jbhTime; }

        public Boolean getMuteUponEntry() { return muteUponEntry; }
        public void setMuteUponEntry(Boolean muteUponEntry) { this.muteUponEntry = muteUponEntry; }

        public Boolean getWatermark() { return watermark; }
        public void setWatermark(Boolean watermark) { this.watermark = watermark; }

        public Boolean getUsePmi() { return usePmi; }
        public void setUsePmi(Boolean usePmi) { this.usePmi = usePmi; }

        public Integer getApprovalType() { return approvalType; }
        public void setApprovalType(Integer approvalType) { this.approvalType = approvalType; }

        public Boolean getEnforceLogin() { return enforceLogin; }
        public void setEnforceLogin(Boolean enforceLogin) { this.enforceLogin = enforceLogin; }

        public String getEnforceLoginDomains() { return enforceLoginDomains; }
        public void setEnforceLoginDomains(String enforceLoginDomains) { this.enforceLoginDomains = enforceLoginDomains; }

        public String getAlternativeHosts() { return alternativeHosts; }
        public void setAlternativeHosts(String alternativeHosts) { this.alternativeHosts = alternativeHosts; }

        public Boolean getWaitingRoom() { return waitingRoom; }
        public void setWaitingRoom(Boolean waitingRoom) { this.waitingRoom = waitingRoom; }

        public Boolean getMeetingAuthentication() { return meetingAuthentication; }
        public void setMeetingAuthentication(Boolean meetingAuthentication) { this.meetingAuthentication = meetingAuthentication; }

        public Boolean getAllowMultipleDevices() { return allowMultipleDevices; }
        public void setAllowMultipleDevices(Boolean allowMultipleDevices) { this.allowMultipleDevices = allowMultipleDevices; }

        public Boolean getShowShareButton() { return showShareButton; }
        public void setShowShareButton(Boolean showShareButton) { this.showShareButton = showShareButton; }

        public Boolean getRegistrantsConfirmationEmail() { return registrantsConfirmationEmail; }
        public void setRegistrantsConfirmationEmail(Boolean registrantsConfirmationEmail) { this.registrantsConfirmationEmail = registrantsConfirmationEmail; }

        public Boolean getEmailNotification() { return emailNotification; }
        public void setEmailNotification(Boolean emailNotification) { this.emailNotification = emailNotification; }

        public Boolean getAutoStartMeetingSummary() { return autoStartMeetingSummary; }
        public void setAutoStartMeetingSummary(Boolean autoStartMeetingSummary) { this.autoStartMeetingSummary = autoStartMeetingSummary; }

        public Boolean getAutoStartAiCompanionQuestions() { return autoStartAiCompanionQuestions; }
        public void setAutoStartAiCompanionQuestions(Boolean autoStartAiCompanionQuestions) { this.autoStartAiCompanionQuestions = autoStartAiCompanionQuestions; }
    }

    /**
     * 跟踪字段类
     */
    public static class TrackingField {
        private String field;
        private String value;

        public String getField() { return field; }
        public void setField(String field) { this.field = field; }

        public String getValue() { return value; }
        public void setValue(String value) { this.value = value; }
    }

    // Getter and Setter methods (manually added due to Lombok compatibility issues)

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public Long getZoomUserIdForMeeting() { return zoomUserIdForMeeting; }
    public void setZoomUserIdForMeeting(Long zoomUserIdForMeeting) { this.zoomUserIdForMeeting = zoomUserIdForMeeting; }

    public String getTopic() { return topic; }
    public void setTopic(String topic) { this.topic = topic; }

    public String getAgenda() { return agenda; }
    public void setAgenda(String agenda) { this.agenda = agenda; }

    public LocalDateTime getStartTime() { return startTime; }
    public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }

    public Integer getDurationMinutes() { return durationMinutes; }
    public void setDurationMinutes(Integer durationMinutes) { this.durationMinutes = durationMinutes; }

    public String getTimezone() { return timezone; }
    public void setTimezone(String timezone) { this.timezone = timezone; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public Meeting.MeetingType getType() { return type; }
    public void setType(Meeting.MeetingType type) { this.type = type; }

    public Boolean getIsRecurring() { return isRecurring; }
    public void setIsRecurring(Boolean isRecurring) { this.isRecurring = isRecurring; }

    public Meeting.RecurrenceType getRecurrenceType() { return recurrenceType; }
    public void setRecurrenceType(Meeting.RecurrenceType recurrenceType) { this.recurrenceType = recurrenceType; }

    public Integer getRepeatInterval() { return repeatInterval; }
    public void setRepeatInterval(Integer repeatInterval) { this.repeatInterval = repeatInterval; }

    public String getWeeklyDays() { return weeklyDays; }
    public void setWeeklyDays(String weeklyDays) { this.weeklyDays = weeklyDays; }

    public Meeting.MonthlyType getMonthlyType() { return monthlyType; }
    public void setMonthlyType(Meeting.MonthlyType monthlyType) { this.monthlyType = monthlyType; }

    public Integer getMonthlyDay() { return monthlyDay; }
    public void setMonthlyDay(Integer monthlyDay) { this.monthlyDay = monthlyDay; }

    public Integer getMonthlyWeekDay() { return monthlyWeekDay; }
    public void setMonthlyWeekDay(Integer monthlyWeekDay) { this.monthlyWeekDay = monthlyWeekDay; }

    public Meeting.EndType getEndType() { return endType; }
    public void setEndType(Meeting.EndType endType) { this.endType = endType; }

    public LocalDateTime getEndDateTime() { return endDateTime; }
    public void setEndDateTime(LocalDateTime endDateTime) { this.endDateTime = endDateTime; }

    public Integer getEndTimes() { return endTimes; }
    public void setEndTimes(Integer endTimes) { this.endTimes = endTimes; }

    public MeetingSettings getSettings() { return settings; }
    public void setSettings(MeetingSettings settings) { this.settings = settings; }

    public TrackingField[] getTrackingFields() { return trackingFields; }
    public void setTrackingFields(TrackingField[] trackingFields) { this.trackingFields = trackingFields; }

    public String getTemplateId() { return templateId; }
    public void setTemplateId(String templateId) { this.templateId = templateId; }

    public Boolean getPreSchedule() { return preSchedule; }
    public void setPreSchedule(Boolean preSchedule) { this.preSchedule = preSchedule; }
}
