package com.zoombus.dto;

import com.zoombus.entity.ZoomAuth;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Email;

@Data
public class CreateZoomAuthRequest {
    
    @NotBlank(message = "账号名称不能为空")
    private String accountName;
    
    @NotBlank(message = "Zoom账号ID不能为空")
    private String zoomAccountId;

    @Email(message = "主账号邮箱格式不正确")
    @NotBlank(message = "主账号邮箱不能为空")
    private String primaryEmail;

    @NotBlank(message = "客户端ID不能为空")
    private String clientId;
    
    @NotBlank(message = "客户端密钥不能为空")
    private String clientSecret;
    
    @NotNull(message = "认证类型不能为空")
    private ZoomAuth.AuthType authType;
    
    private String webhookSecretToken;
    
    private String apiBaseUrl = "https://api.zoom.us/v2";
    
    private String description;
}
