package com.zoombus.dto;

import com.zoombus.entity.ZoomUser;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CreateZoomUserRequest {
    
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    @NotNull(message = "ZoomAuth ID不能为空")
    private Long zoomAuthId;
    
    @NotBlank(message = "Zoom邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @NotBlank(message = "名字不能为空")
    private String firstName;
    
    @NotBlank(message = "姓氏不能为空")
    private String lastName;
    
    private ZoomUser.UserType userType = ZoomUser.UserType.BASIC;
    
    private String department;
    
    private String jobTitle;
    
    private String phoneNumber;
    
    private String timezone;
    
    private String language;

    private ZoomUser.AccountUsage accountUsage;

    // Getter and Setter methods (manually added due to Lombok compatibility issues)
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public Long getZoomAuthId() { return zoomAuthId; }
    public void setZoomAuthId(Long zoomAuthId) { this.zoomAuthId = zoomAuthId; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public ZoomUser.UserType getUserType() { return userType; }
    public void setUserType(ZoomUser.UserType userType) { this.userType = userType; }

    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }

    public String getJobTitle() { return jobTitle; }
    public void setJobTitle(String jobTitle) { this.jobTitle = jobTitle; }

    public String getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }

    public String getTimezone() { return timezone; }
    public void setTimezone(String timezone) { this.timezone = timezone; }

    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }

    public ZoomUser.AccountUsage getAccountUsage() { return accountUsage; }
    public void setAccountUsage(ZoomUser.AccountUsage accountUsage) { this.accountUsage = accountUsage; }
}
