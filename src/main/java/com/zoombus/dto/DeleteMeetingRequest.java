package com.zoombus.dto;

import lombok.Data;

/**
 * 删除会议请求DTO
 * 基于Zoom API DELETE /meetings/{meetingId} 接口
 */
@Data
public class DeleteMeetingRequest {
    
    /**
     * 删除类型
     * - "meeting": 删除会议（默认）
     * - "occurrence": 删除特定的occurrence
     */
    private String scheduleForReminder = "meeting";
    
    /**
     * 取消会议提醒
     * true: 发送取消会议邮件给所有注册用户
     * false: 不发送取消邮件（默认）
     */
    private Boolean cancelMeetingReminder = false;
    
    /**
     * occurrence ID（仅当删除特定occurrence时需要）
     */
    private String occurrenceId;
    
    // Getter and Setter methods
    
    public String getScheduleForReminder() { return scheduleForReminder; }
    public void setScheduleForReminder(String scheduleForReminder) { this.scheduleForReminder = scheduleForReminder; }
    
    public Boolean getCancelMeetingReminder() { return cancelMeetingReminder; }
    public void setCancelMeetingReminder(Boolean cancelMeetingReminder) { this.cancelMeetingReminder = cancelMeetingReminder; }
    
    public String getOccurrenceId() { return occurrenceId; }
    public void setOccurrenceId(String occurrenceId) { this.occurrenceId = occurrenceId; }
}
