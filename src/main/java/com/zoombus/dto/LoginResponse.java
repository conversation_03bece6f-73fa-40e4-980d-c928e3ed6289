package com.zoombus.dto;

import com.zoombus.entity.AdminUser;
import lombok.Data;
import lombok.AllArgsConstructor;

@Data
@AllArgsConstructor
public class LoginResponse {
    
    private String token;
    private String tokenType = "Bearer";
    private AdminUserInfo userInfo;
    
    public LoginResponse(String token, AdminUserInfo userInfo) {
        this.token = token;
        this.userInfo = userInfo;
    }
    
    @Data
    @AllArgsConstructor
    public static class AdminUserInfo {
        private Long id;
        private String username;
        private String email;
        private String fullName;
        private AdminUser.AdminRole role;
        private AdminUser.AdminStatus status;
        
        public static AdminUserInfo fromAdminUser(AdminUser adminUser) {
            return new AdminUserInfo(
                adminUser.getId(),
                adminUser.getUsername(),
                adminUser.getEmail(),
                adminUser.getFullName(),
                adminUser.getRole(),
                adminUser.getStatus()
            );
        }
    }
}
