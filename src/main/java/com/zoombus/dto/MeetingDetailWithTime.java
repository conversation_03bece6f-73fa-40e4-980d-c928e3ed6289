package com.zoombus.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 包含开始时间的会议详情DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingDetailWithTime {
    
    private Long id;
    private Long meetingId;
    private String zoomMeetingId;
    private String uuid;
    private String topic;
    private Integer type;
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime; // 从Meeting表获取

    private String timezone;
    private Integer duration;
    private String hostId;
    private String hostEmail;
    private String joinUrl;
    private String startUrl;
    private String password;
    private String occurrenceId;
    private String occurrenceStatus;
    private LocalDateTime createdAt;
    
    /**
     * 从ZoomMeetingDetail、occurrenceStartTime和Meeting构造
     */
    public static MeetingDetailWithTime from(Object[] result) {
        // result[0] = ZoomMeetingDetail, result[1] = occurrenceStartTime, result[2] = Meeting
        if (result.length >= 3) {
            com.zoombus.entity.ZoomMeetingDetail detail = (com.zoombus.entity.ZoomMeetingDetail) result[0];
            LocalDateTime occurrenceStartTime = (LocalDateTime) result[1];
            com.zoombus.entity.Meeting meeting = (com.zoombus.entity.Meeting) result[2];

            return MeetingDetailWithTime.builder()
                    .id(detail.getId())
                    .meetingId(detail.getMeetingId())
                    .zoomMeetingId(detail.getZoomMeetingId())
                    .uuid(detail.getUuid())
                    // 从Meeting表获取已迁移的字段
                    .topic(meeting != null ? meeting.getTopic() : detail.getTopic())
                    .type(meeting != null ? meeting.getType().getValue() : detail.getType())
                    .status(detail.getStatus())
                    .startTime(occurrenceStartTime != null ? occurrenceStartTime : detail.getStartTime())
                    .timezone(meeting != null ? meeting.getTimezone() : detail.getTimezone())
                    .duration(meeting != null ? meeting.getDurationMinutes() : detail.getDuration())
                    .hostId(detail.getHostId())
                    .hostEmail(detail.getHostEmail())
                    .joinUrl(detail.getJoinUrl())
                    .startUrl(detail.getStartUrl())
                    .password(meeting != null ? meeting.getPassword() : detail.getPassword())
                    .occurrenceId(detail.getOccurrenceId())
                    .occurrenceStatus(detail.getOccurrenceStatus())
                    .createdAt(detail.getCreatedAt())
                    .build();
        }
        // 兼容旧版本查询结果（只有ZoomMeetingDetail和occurrenceStartTime）
        else if (result.length >= 2) {
            com.zoombus.entity.ZoomMeetingDetail detail = (com.zoombus.entity.ZoomMeetingDetail) result[0];
            LocalDateTime occurrenceStartTime = (LocalDateTime) result[1];

            return MeetingDetailWithTime.builder()
                    .id(detail.getId())
                    .meetingId(detail.getMeetingId())
                    .zoomMeetingId(detail.getZoomMeetingId())
                    .uuid(detail.getUuid())
                    // 使用已废弃的字段（向后兼容）
                    .topic(detail.getTopic())
                    .type(detail.getType())
                    .status(detail.getStatus())
                    .startTime(occurrenceStartTime != null ? occurrenceStartTime : detail.getStartTime())
                    .timezone(detail.getTimezone())
                    .duration(detail.getDuration())
                    .hostId(detail.getHostId())
                    .hostEmail(detail.getHostEmail())
                    .joinUrl(detail.getJoinUrl())
                    .startUrl(detail.getStartUrl())
                    .password(detail.getPassword())
                    .occurrenceId(detail.getOccurrenceId())
                    .occurrenceStatus(detail.getOccurrenceStatus())
                    .createdAt(detail.getCreatedAt())
                    .build();
        }
        return null;
    }
}
