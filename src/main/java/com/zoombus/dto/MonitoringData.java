package com.zoombus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 实时监控数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitoringData {
    
    /**
     * 数据类型
     */
    private String type;
    
    /**
     * 时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 数据内容
     */
    private Object data;
    
    /**
     * Zoom账号状态监控数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ZoomAccountStatus {
        private Long accountId;
        private String accountName;
        private String status;
        private Integer totalLicenses;
        private Integer usedLicenses;
        private Integer availableLicenses;
        private Double usageRate;
        private LocalDateTime lastUpdateTime;
        private String healthStatus; // HEALTHY, WARNING, ERROR
    }
    
    /**
     * 会议状态监控数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MeetingStatus {
        private Integer totalMeetings;
        private Integer activeMeetings;
        private Integer scheduledMeetings;
        private Integer completedMeetings;
        private List<ActiveMeeting> activeMeetingList;
        private LocalDateTime lastUpdateTime;
    }
    
    /**
     * 活跃会议详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActiveMeeting {
        private Long meetingId;
        private String meetingNumber;
        private String topic;
        private String hostName;
        private LocalDateTime startTime;
        private Integer participantCount;
        private String status;
        private Integer duration; // 分钟
    }
    
    /**
     * PMI使用状态
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PmiStatus {
        private Integer totalPmi;
        private Integer activePmi;
        private Integer inUsePmi;
        private Integer availablePmi;
        private Double usageRate;
        private LocalDateTime lastUpdateTime;
    }
    
    /**
     * 系统性能监控
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemPerformance {
        private Double cpuUsage;
        private Double memoryUsage;
        private Long databaseConnections;
        private Long redisConnections;
        private Integer activeUsers;
        private Long apiCallsPerMinute;
        private LocalDateTime lastUpdateTime;
    }
    
    /**
     * API调用统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApiCallStats {
        private Map<String, Integer> callCounts; // endpoint -> count
        private Map<String, Double> avgResponseTimes; // endpoint -> avg time
        private Map<String, Integer> errorCounts; // endpoint -> error count
        private Integer totalCalls;
        private Integer successCalls;
        private Integer errorCalls;
        private Double successRate;
        private LocalDateTime lastUpdateTime;
    }
    
    /**
     * 实时告警信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlertInfo {
        private String alertId;
        private String level; // INFO, WARNING, ERROR, CRITICAL
        private String title;
        private String message;
        private String source;
        private LocalDateTime alertTime;
        private Boolean acknowledged;
    }
}
