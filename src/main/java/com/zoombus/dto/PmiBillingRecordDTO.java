package com.zoombus.dto;

import com.zoombus.entity.PmiBillingRecord;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * PMI计费记录DTO，包含关联的PMI号码和用户名称信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmiBillingRecordDTO {
    
    private Long id;
    private Long pmiRecordId;
    private Long zoomMeetingId;
    private Long userId;
    
    // 关联信息
    private String pmiNumber;        // PMI号码
    private String userName;         // 用户名称
    private String userEmail;        // 用户邮箱
    
    // 交易信息
    private PmiBillingRecord.TransactionType transactionType;
    private Integer amountMinutes;
    private Integer balanceBefore;
    private Integer balanceAfter;
    
    // 业务信息
    private String description;
    private LocalDateTime billingPeriodStart;
    private LocalDateTime billingPeriodEnd;
    
    // 状态信息
    private PmiBillingRecord.RecordStatus status;
    
    // 审计字段
    private LocalDateTime createdAt;
    private String createdBy;
    
    /**
     * 从PmiBillingRecord实体转换为DTO
     */
    public static PmiBillingRecordDTO fromEntity(PmiBillingRecord record, String pmiNumber, String userName, String userEmail) {
        PmiBillingRecordDTO dto = new PmiBillingRecordDTO();
        dto.setId(record.getId());
        dto.setPmiRecordId(record.getPmiRecordId());
        dto.setZoomMeetingId(record.getZoomMeetingId());
        dto.setUserId(record.getUserId());
        
        // 设置关联信息
        dto.setPmiNumber(pmiNumber);
        dto.setUserName(userName);
        dto.setUserEmail(userEmail);
        
        // 设置交易信息
        dto.setTransactionType(record.getTransactionType());
        dto.setAmountMinutes(record.getAmountMinutes());
        dto.setBalanceBefore(record.getBalanceBefore());
        dto.setBalanceAfter(record.getBalanceAfter());
        
        // 设置业务信息
        dto.setDescription(record.getDescription());
        dto.setBillingPeriodStart(record.getBillingPeriodStart());
        dto.setBillingPeriodEnd(record.getBillingPeriodEnd());
        
        // 设置状态信息
        dto.setStatus(record.getStatus());
        
        // 设置审计字段
        dto.setCreatedAt(record.getCreatedAt());
        dto.setCreatedBy(record.getCreatedBy());
        
        return dto;
    }
    
    /**
     * 获取时长变动的符号表示
     */
    public String getAmountMinutesWithSign() {
        if (amountMinutes == null) {
            return "0";
        }
        
        if (transactionType == PmiBillingRecord.TransactionType.RECHARGE || 
            transactionType == PmiBillingRecord.TransactionType.REFUND) {
            return "+" + amountMinutes;
        } else {
            return "-" + Math.abs(amountMinutes);
        }
    }
    
    /**
     * 检查是否为充值记录
     */
    public boolean isRecharge() {
        return transactionType == PmiBillingRecord.TransactionType.RECHARGE;
    }
    
    /**
     * 检查是否为扣费记录
     */
    public boolean isDeduct() {
        return transactionType == PmiBillingRecord.TransactionType.DEDUCT;
    }
    
    /**
     * 检查记录是否已完成
     */
    public boolean isCompleted() {
        return status == PmiBillingRecord.RecordStatus.COMPLETED;
    }
}
