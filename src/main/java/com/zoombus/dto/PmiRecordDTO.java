package com.zoombus.dto;

import com.zoombus.entity.PmiRecord;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * PMI记录DTO，包含关联的用户信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmiRecordDTO {
    
    private Long id;
    private Long userId;
    private String pmiNumber;
    private String magicId;
    private String pmiPassword;
    private String hostUrl;
    private String joinUrl;
    private PmiRecord.PmiStatus status;
    private Long currentZoomUserId;
    private LocalDateTime lastUsedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 计费相关字段
    private PmiRecord.BillingMode billingMode;
    private PmiRecord.BillingMode originalBillingMode;
    private Long currentWindowId;
    private LocalDateTime windowExpireTime;
    private String activeWindowIds;
    private Integer totalMinutes;
    private Integer availableMinutes;
    private Integer pendingDeductMinutes;
    private Integer overdraftMinutes;
    private Integer totalUsedMinutes;
    private PmiRecord.BillingStatus billingStatus;
    private LocalDateTime billingUpdatedAt;
    private LocalDateTime lastBillingTime;
    private Boolean fallbackEnabled;
    
    // 关联的用户信息
    private String userFullName;        // 用户全名
    private String userEmail;           // 用户邮箱
    private String userName;            // 用户名
    
    /**
     * 从PmiRecord实体转换为DTO
     */
    public static PmiRecordDTO fromEntity(PmiRecord record, String userFullName, String userEmail, String userName) {
        PmiRecordDTO dto = new PmiRecordDTO();
        
        // 基本信息
        dto.setId(record.getId());
        dto.setUserId(record.getUserId());
        dto.setPmiNumber(record.getPmiNumber());
        dto.setMagicId(record.getMagicId());
        dto.setPmiPassword(record.getPmiPassword());
        dto.setHostUrl(record.getHostUrl());
        dto.setJoinUrl(record.getJoinUrl());
        dto.setStatus(record.getStatus());
        dto.setCurrentZoomUserId(record.getCurrentZoomUserId());
        dto.setLastUsedAt(record.getLastUsedAt());
        dto.setCreatedAt(record.getCreatedAt());
        dto.setUpdatedAt(record.getUpdatedAt());
        
        // 计费相关字段
        dto.setBillingMode(record.getBillingMode());
        dto.setOriginalBillingMode(record.getOriginalBillingMode());
        dto.setCurrentWindowId(record.getCurrentWindowId());
        dto.setWindowExpireTime(record.getWindowExpireTime());
        dto.setActiveWindowIds(record.getActiveWindowIds());
        dto.setTotalMinutes(record.getTotalMinutes());
        dto.setAvailableMinutes(record.getAvailableMinutes());
        dto.setPendingDeductMinutes(record.getPendingDeductMinutes());
        dto.setOverdraftMinutes(record.getOverdraftMinutes());
        dto.setTotalUsedMinutes(record.getTotalUsedMinutes());
        dto.setBillingStatus(record.getBillingStatus());
        dto.setBillingUpdatedAt(record.getBillingUpdatedAt());
        dto.setLastBillingTime(record.getLastBillingTime());
        dto.setFallbackEnabled(record.getFallbackEnabled());
        
        // 设置关联的用户信息
        dto.setUserFullName(userFullName);
        dto.setUserEmail(userEmail);
        dto.setUserName(userName);
        
        return dto;
    }
    
    /**
     * 从PmiRecord实体转换为DTO（不包含用户信息）
     */
    public static PmiRecordDTO fromEntity(PmiRecord record) {
        return fromEntity(record, null, null, null);
    }
    
    /**
     * 计算剩余可用时长（可用时长 - 待扣时长）
     */
    public int getRemainingMinutes() {
        int available = availableMinutes != null ? availableMinutes : 0;
        int pending = pendingDeductMinutes != null ? pendingDeductMinutes : 0;
        return Math.max(0, available - pending);
    }
}
