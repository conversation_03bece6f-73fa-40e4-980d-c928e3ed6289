package com.zoombus.dto;

import com.zoombus.entity.PmiSchedule;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * PMI计划请求DTO
 */
@Data
public class PmiScheduleRequest {
    
    @NotNull(message = "PMI记录ID不能为空")
    private Long pmiRecordId;
    
    @NotBlank(message = "计划名称不能为空")
    private String name;
    
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;
    
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;
    
    @NotNull(message = "重复类型不能为空")
    private PmiSchedule.RepeatType repeatType;

    /**
     * 按周重复时选择的星期几 (1-7, 1=周一, 7=周日)
     */
    private List<Integer> weekDays;

    /**
     * 按月重复时选择的日期 (1-31)
     */
    private List<Integer> monthDays;

    @NotNull(message = "是否全天不能为空")
    private Boolean isAllDay;
    
    private LocalTime startTime;
    
    private Integer durationMinutes;
    
    /**
     * 验证请求数据
     */
    public boolean isValid() {
        if (startDate == null || endDate == null) {
            return false;
        }
        
        if (startDate.isAfter(endDate)) {
            return false;
        }
        
        if (!isAllDay) {
            if (startTime == null || durationMinutes == null || durationMinutes <= 0) {
                return false;
            }
            
            // 检查时间是否合理（不超过24小时）
            if (durationMinutes > 24 * 60) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 转换为实体对象
     */
    public PmiSchedule toEntity() {
        PmiSchedule schedule = new PmiSchedule();
        schedule.setPmiRecordId(pmiRecordId);
        schedule.setName(name);
        schedule.setStartDate(startDate);
        schedule.setEndDate(endDate);
        schedule.setRepeatType(repeatType);

        // 处理按周重复的星期几选择
        if (weekDays != null && !weekDays.isEmpty()) {
            schedule.setWeekDays(weekDays.stream()
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse(null));
        }

        // 处理按月重复的日期选择
        if (monthDays != null && !monthDays.isEmpty()) {
            schedule.setMonthDays(monthDays.stream()
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse(null));
        }

        schedule.setIsAllDay(isAllDay);
        schedule.setStartTime(startTime);
        schedule.setDurationMinutes(durationMinutes);
        schedule.setStatus(PmiSchedule.ScheduleStatus.ACTIVE);
        return schedule;
    }
}
