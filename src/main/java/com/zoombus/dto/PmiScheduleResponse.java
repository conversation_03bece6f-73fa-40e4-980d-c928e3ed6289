package com.zoombus.dto;

import com.zoombus.entity.PmiSchedule;
import com.zoombus.entity.PmiScheduleWindow;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * PMI计划响应DTO
 */
@Data
public class PmiScheduleResponse {
    
    private Long id;
    private Long pmiRecordId;
    private String name;
    private LocalDate startDate;
    private LocalDate endDate;
    private PmiSchedule.RepeatType repeatType;
    private List<Integer> weekDays;
    private List<Integer> monthDays;
    private Boolean isAllDay;
    private LocalTime startTime;
    private Integer durationMinutes;
    private LocalTime endTime;
    private PmiSchedule.ScheduleStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 统计信息
    private Long totalWindows;
    private Long pendingWindows;
    private Long activeWindows;
    private Long completedWindows;
    private Long failedWindows;
    
    // 关联的窗口列表（可选）
    private List<PmiScheduleWindowResponse> windows;
    
    /**
     * 从实体对象创建响应DTO
     */
    public static PmiScheduleResponse fromEntity(PmiSchedule schedule) {
        PmiScheduleResponse response = new PmiScheduleResponse();
        response.setId(schedule.getId());
        response.setPmiRecordId(schedule.getPmiRecordId());
        response.setName(schedule.getName());
        response.setStartDate(schedule.getStartDate());
        response.setEndDate(schedule.getEndDate());
        response.setRepeatType(schedule.getRepeatType());

        // 处理按周重复的星期几选择
        if (schedule.getWeekDays() != null && !schedule.getWeekDays().isEmpty()) {
            response.setWeekDays(Arrays.stream(schedule.getWeekDays().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList()));
        }

        // 处理按月重复的日期选择
        if (schedule.getMonthDays() != null && !schedule.getMonthDays().isEmpty()) {
            response.setMonthDays(Arrays.stream(schedule.getMonthDays().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList()));
        }

        response.setIsAllDay(schedule.getIsAllDay());
        response.setStartTime(schedule.getStartTime());
        response.setDurationMinutes(schedule.getDurationMinutes());
        response.setEndTime(schedule.getEndTime());
        response.setStatus(schedule.getStatus());
        response.setCreatedAt(schedule.getCreatedAt());
        response.setUpdatedAt(schedule.getUpdatedAt());
        return response;
    }
    
    /**
     * PMI计划窗口响应DTO
     */
    @Data
    public static class PmiScheduleWindowResponse {
        private Long id;
        private Long scheduleId;
        private LocalDateTime startDateTime;
        private LocalDateTime endDateTime;
        private PmiScheduleWindow.WindowStatus status;
        private Long zoomUserId;
        private String errorMessage;
        private LocalDateTime actualStartTime;
        private LocalDateTime actualEndTime;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;

        // 任务信息
        private Long openTaskId;
        private Long closeTaskId;
        private TaskInfo openTask;
        private TaskInfo closeTask;
        
        /**
         * 从实体对象创建响应DTO
         */
        public static PmiScheduleWindowResponse fromEntity(PmiScheduleWindow window) {
            PmiScheduleWindowResponse response = new PmiScheduleWindowResponse();
            response.setId(window.getId());
            response.setScheduleId(window.getScheduleId());
            response.setStartDateTime(window.getStartDateTime());
            response.setEndDateTime(window.getEndDateTime());
            response.setStatus(window.getStatus());
            response.setZoomUserId(window.getZoomUserId());
            response.setErrorMessage(window.getErrorMessage());
            response.setActualStartTime(window.getActualStartTime());
            response.setActualEndTime(window.getActualEndTime());
            response.setCreatedAt(window.getCreatedAt());
            response.setUpdatedAt(window.getUpdatedAt());

            // 设置任务ID
            response.setOpenTaskId(window.getOpenTaskId());
            response.setCloseTaskId(window.getCloseTaskId());

            return response;
        }

        /**
         * 任务信息DTO
         */
        @Data
        public static class TaskInfo {
            private Long id;
            private String taskKey;
            private String taskType;
            private LocalDateTime scheduledTime;
            private String status;
            private LocalDateTime actualExecutionTime;
            private String errorMessage;
            private Integer retryCount;
            private LocalDateTime createdAt;
            private LocalDateTime updatedAt;
        }
    }
}
