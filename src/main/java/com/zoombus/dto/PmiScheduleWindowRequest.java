package com.zoombus.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * PMI计划窗口请求DTO
 */
@Data
public class PmiScheduleWindowRequest {

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startDateTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endDateTime;

    // 兼容旧字段，用于前端传递
    private LocalDate windowDate;
    private LocalTime startTime;
    private LocalTime endTime;
    private LocalDate endDate;

    /**
     * 从旧字段构建新字段
     */
    public void buildDateTimeFields() {
        if (windowDate != null && startTime != null) {
            this.startDateTime = LocalDateTime.of(windowDate, startTime);
        }

        if (endTime != null) {
            LocalDate endDateToUse = endDate != null ? endDate : windowDate;
            if (endDateToUse != null) {
                this.endDateTime = LocalDateTime.of(endDateToUse, endTime);
            }
        }
    }
}
