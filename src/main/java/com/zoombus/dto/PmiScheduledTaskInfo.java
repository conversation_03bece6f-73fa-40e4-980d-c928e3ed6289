package com.zoombus.dto;

import com.zoombus.entity.PmiScheduleWindowTask;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PMI定时任务信息DTO
 */
@Data
public class PmiScheduledTaskInfo {
    
    private Long id;
    private Long pmiWindowId;
    private String taskType;
    private LocalDateTime scheduledTime;
    private LocalDateTime actualExecutionTime;
    private String status;
    private String taskKey;
    private Integer retryCount;
    private String errorMessage;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 关联信息
    private Long pmiRecordId;
    private String pmiNumber;
    private String pmiPassword;
    private String userName;
    private String scheduleName;
    
    // 计算字段
    private Long delayMinutes; // 延迟分钟数
    private String statusDescription;
    private Boolean canCancel;
    private Boolean canReschedule;
    
    /**
     * 从实体转换为DTO
     */
    public static PmiScheduledTaskInfo fromEntity(PmiScheduleWindowTask task) {
        PmiScheduledTaskInfo dto = new PmiScheduledTaskInfo();
        dto.setId(task.getId());
        dto.setPmiWindowId(task.getPmiWindowId());
        dto.setTaskType(task.getTaskType().name());
        dto.setScheduledTime(task.getScheduledTime());
        dto.setActualExecutionTime(task.getActualExecutionTime());
        dto.setStatus(task.getStatus().name());
        dto.setTaskKey(task.getTaskKey());
        dto.setRetryCount(task.getRetryCount());
        dto.setErrorMessage(task.getErrorMessage());
        dto.setCreatedAt(task.getCreatedAt());
        dto.setUpdatedAt(task.getUpdatedAt());
        
        // 设置状态描述
        dto.setStatusDescription(task.getStatus().getDescription());
        
        // 设置操作权限
        dto.setCanCancel(task.canCancel());
        dto.setCanReschedule(task.canReschedule());
        
        // 计算延迟时间
        if (task.getActualExecutionTime() != null && task.getScheduledTime() != null) {
            long delayMinutes = java.time.Duration.between(
                task.getScheduledTime(), 
                task.getActualExecutionTime()
            ).toMinutes();
            dto.setDelayMinutes(delayMinutes);
        } else if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.SCHEDULED) {
            // 对于未执行的任务，计算当前时间与计划时间的差值
            long delayMinutes = java.time.Duration.between(
                task.getScheduledTime(), 
                LocalDateTime.now()
            ).toMinutes();
            if (delayMinutes > 0) {
                dto.setDelayMinutes(delayMinutes);
            }
        }
        
        return dto;
    }
    
    /**
     * 获取任务类型描述
     */
    public String getTaskTypeDescription() {
        if ("PMI_WINDOW_OPEN".equals(taskType)) {
            return "PMI窗口开启";
        } else if ("PMI_WINDOW_CLOSE".equals(taskType)) {
            return "PMI窗口关闭";
        }
        return taskType;
    }
    
    /**
     * 是否延迟执行
     */
    public boolean isDelayed() {
        return delayMinutes != null && delayMinutes > 0;
    }
    
    /**
     * 获取延迟描述
     */
    public String getDelayDescription() {
        if (delayMinutes == null || delayMinutes <= 0) {
            return "准时";
        }
        
        if (delayMinutes < 60) {
            return delayMinutes + "分钟";
        } else {
            long hours = delayMinutes / 60;
            long minutes = delayMinutes % 60;
            if (minutes == 0) {
                return hours + "小时";
            } else {
                return hours + "小时" + minutes + "分钟";
            }
        }
    }
}
