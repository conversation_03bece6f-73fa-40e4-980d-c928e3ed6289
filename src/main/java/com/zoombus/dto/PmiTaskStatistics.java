package com.zoombus.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * PMI任务统计DTO
 */
@Data
public class PmiTaskStatistics {
    
    private Long totalTasks;
    private Long scheduledTasks;
    private Long executingTasks;
    private Long completedTasks;
    private Long failedTasks;
    private Long cancelledTasks;
    
    private Double successRate;
    private Double averageExecutionTime;
    
    // 24小时内统计
    private Long tasksLast24Hours;
    private Long successfulTasksLast24Hours;
    private Long failedTasksLast24Hours;
    
    // 按类型统计
    private Map<String, Long> tasksByType;
    
    // 按小时统计（用于图表）
    private List<HourlyTaskStats> hourlyStats;
    
    /**
     * 计算成功率
     */
    public void calculateSuccessRate() {
        if (completedTasks != null && totalTasks != null && totalTasks > 0) {
            this.successRate = (completedTasks.doubleValue() / totalTasks.doubleValue()) * 100;
        } else {
            this.successRate = 0.0;
        }
    }
    
    /**
     * 计算24小时成功率
     */
    public Double getSuccessRateLast24Hours() {
        if (successfulTasksLast24Hours != null && tasksLast24Hours != null && tasksLast24Hours > 0) {
            return (successfulTasksLast24Hours.doubleValue() / tasksLast24Hours.doubleValue()) * 100;
        }
        return 0.0;
    }
    
    /**
     * 小时统计数据
     */
    @Data
    public static class HourlyTaskStats {
        private Integer hour;
        private Long totalTasks;
        private Long successfulTasks;
        private Long failedTasks;
        
        public HourlyTaskStats() {}
        
        public HourlyTaskStats(Integer hour, Long totalTasks, Long successfulTasks, Long failedTasks) {
            this.hour = hour;
            this.totalTasks = totalTasks;
            this.successfulTasks = successfulTasks;
            this.failedTasks = failedTasks;
        }
        
        /**
         * 计算成功率
         */
        public Double getSuccessRate() {
            if (totalTasks != null && totalTasks > 0) {
                return (successfulTasks.doubleValue() / totalTasks.doubleValue()) * 100;
            }
            return 0.0;
        }
    }
}
