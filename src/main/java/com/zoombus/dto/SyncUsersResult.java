package com.zoombus.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncUsersResult {
    
    private boolean success;
    private String message;
    private SyncStats result;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncStats {
        private int totalUsers;
        private int newUsers;
        private int updatedUsers;
        private int skippedUsers;
        private List<String> errors;
    }
    
    public static SyncUsersResult success(SyncStats stats) {
        return new SyncUsersResult(true, "同步成功", stats);
    }
    
    public static SyncUsersResult error(String message) {
        return new SyncUsersResult(false, message, null);
    }
}
