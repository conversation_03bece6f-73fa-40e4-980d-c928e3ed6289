package com.zoombus.dto;

import com.zoombus.entity.AdminUser;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class UpdateAdminUserRequest {
    
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @NotBlank(message = "姓名不能为空")
    @Size(max = 100, message = "姓名长度不能超过100个字符")
    private String fullName;
    
    @Size(max = 20, message = "电话号码长度不能超过20个字符")
    private String phone;
    
    @NotNull(message = "角色不能为空")
    private AdminUser.AdminRole role;
    
    @NotNull(message = "状态不能为空")
    private AdminUser.AdminStatus status;
}
