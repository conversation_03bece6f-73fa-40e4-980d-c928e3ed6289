package com.zoombus.dto;

import com.zoombus.entity.Meeting;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 更新会议请求DTO
 * 基于Zoom API PATCH /meetings/{meetingId} 接口
 */
@Data
public class UpdateMeetingRequest {
    
    // 基本会议信息
    private String topic;
    private String agenda;
    private LocalDateTime startTime;
    private Integer duration; // 会议时长（分钟）
    private String timezone = "Asia/Shanghai";
    private String password;
    
    // 会议类型
    private Meeting.MeetingType type;
    
    // 周期性会议设置
    private RecurrenceSettings recurrence;
    
    // 会议设置
    private MeetingSettings settings;
    
    // 跟踪字段
    private TrackingFields[] trackingFields;
    
    /**
     * 周期性会议设置
     */
    @Data
    public static class RecurrenceSettings {
        private Integer type; // 1=Daily, 2=Weekly, 3=Monthly
        private Integer repeatInterval = 1;
        private String weeklyDays; // 每周重复的星期几，如"1,3,5"
        private Integer monthlyDay; // 每月的日期
        private Integer monthlyWeek; // 每月的第几周
        private Integer monthlyWeekDay; // 每月的星期几
        private Integer endTimes; // 重复次数
        private LocalDateTime endDateTime; // 结束日期
    }
    
    /**
     * 会议设置
     */
    @Data
    public static class MeetingSettings {
        // 音视频设置
        private Boolean hostVideo = true; // 主持人视频
        private Boolean participantVideo = true; // 参与者视频
        private String audio = "voip"; // both, telephony, voip
        private String autoRecording = "none"; // local, cloud, none
        
        // 会议控制设置
        private Boolean joinBeforeHost = false; // 允许参与者在主持人之前加入
        private Integer jbhTime = 0; // 提前加入时间（分钟）
        private Boolean muteUponEntry = true; // 加入时静音
        private Boolean watermark = false; // 水印
        private Boolean usePmi = false; // 使用个人会议ID
        
        // 安全设置
        private Integer approvalType = 2; // 0=自动批准, 1=手动批准, 2=无需注册
        private Boolean enforceLogin = false; // 强制登录
        private String enforceLoginDomains = ""; // 登录域名限制
        private String alternativeHosts = ""; // 替代主持人
        private Boolean waitingRoom = false; // 等候室
        private Boolean meetingAuthentication = false; // 会议身份验证
        
        // 其他设置
        private String language = "zh-CN"; // 会议语言
        private Boolean allowMultipleDevices = false; // 允许多设备
        private Boolean showShareButton = false; // 显示分享按钮
        private Boolean registrantsConfirmationEmail = true; // 注册确认邮件
        private Boolean emailNotification = true; // 邮件通知
        private Boolean privateChat = true; // 私聊
        private Boolean autoStartMeetingSummary = false; // 自动开始会议摘要
        private Boolean autoStartAiCompanionQuestions = false; // 自动开始AI助手问题
    }
    
    /**
     * 跟踪字段
     */
    @Data
    public static class TrackingFields {
        private String field;
        private String value;
    }
    
    // Getter and Setter methods
    
    public String getTopic() { return topic; }
    public void setTopic(String topic) { this.topic = topic; }
    
    public String getAgenda() { return agenda; }
    public void setAgenda(String agenda) { this.agenda = agenda; }
    
    public LocalDateTime getStartTime() { return startTime; }
    public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
    
    public Integer getDuration() { return duration; }
    public void setDuration(Integer duration) { this.duration = duration; }
    
    public String getTimezone() { return timezone; }
    public void setTimezone(String timezone) { this.timezone = timezone; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public Meeting.MeetingType getType() { return type; }
    public void setType(Meeting.MeetingType type) { this.type = type; }
    
    public RecurrenceSettings getRecurrence() { return recurrence; }
    public void setRecurrence(RecurrenceSettings recurrence) { this.recurrence = recurrence; }
    
    public MeetingSettings getSettings() { return settings; }
    public void setSettings(MeetingSettings settings) { this.settings = settings; }
    
    public TrackingFields[] getTrackingFields() { return trackingFields; }
    public void setTrackingFields(TrackingFields[] trackingFields) { this.trackingFields = trackingFields; }
}
