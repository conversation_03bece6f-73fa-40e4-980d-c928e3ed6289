package com.zoombus.dto;

import com.zoombus.entity.ZoomUser;
import lombok.Data;

import javax.validation.constraints.Email;

@Data
public class UpdateZoomUserRequest {
    
    private Long userId; // 可以为空，表示取消关联
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    private String firstName;
    
    private String lastName;
    
    private String displayName;
    
    private ZoomUser.UserType userType;

    private ZoomUser.UserStatus status;
    
    private String department;
    
    private String jobTitle;
    
    private String phoneNumber;
    
    private String timezone;
    
    private String language;

    private ZoomUser.AccountUsage accountUsage;

    // Getter and Setter methods (manually added due to Lombok compatibility issues)
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }

    public ZoomUser.UserType getUserType() { return userType; }
    public void setUserType(ZoomUser.UserType userType) { this.userType = userType; }

    public ZoomUser.UserStatus getStatus() { return status; }
    public void setStatus(ZoomUser.UserStatus status) { this.status = status; }

    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }

    public String getJobTitle() { return jobTitle; }
    public void setJobTitle(String jobTitle) { this.jobTitle = jobTitle; }

    public String getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }

    public String getTimezone() { return timezone; }
    public void setTimezone(String timezone) { this.timezone = timezone; }

    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }

    public ZoomUser.AccountUsage getAccountUsage() { return accountUsage; }
    public void setAccountUsage(ZoomUser.AccountUsage accountUsage) { this.accountUsage = accountUsage; }
}
