package com.zoombus.dto;

import com.zoombus.entity.PmiScheduleWindow;
import java.util.List;
import java.util.Set;

/**
 * 窗口冲突响应DTO
 */
public class WindowConflictResponse {

    private List<PmiScheduleWindow> newWindows;
    private List<PmiScheduleWindow> conflictWindows;
    private Set<Integer> conflictNewWindowIndexes; // 新增窗口中存在冲突的窗口索引
    private int conflictCount;
    private String message;

    public WindowConflictResponse() {}

    public WindowConflictResponse(List<PmiScheduleWindow> newWindows, List<PmiScheduleWindow> conflictWindows, Set<Integer> conflictNewWindowIndexes) {
        this.newWindows = newWindows;
        this.conflictWindows = conflictWindows;
        this.conflictNewWindowIndexes = conflictNewWindowIndexes;
        this.conflictCount = conflictWindows.size();
        this.message = String.format("本次PMI计划安排存在如下%d条冲突窗口，请确认是否忽略冲突，合并窗口", conflictCount);
    }
    
    public List<PmiScheduleWindow> getNewWindows() {
        return newWindows;
    }
    
    public void setNewWindows(List<PmiScheduleWindow> newWindows) {
        this.newWindows = newWindows;
    }
    
    public List<PmiScheduleWindow> getConflictWindows() {
        return conflictWindows;
    }
    
    public void setConflictWindows(List<PmiScheduleWindow> conflictWindows) {
        this.conflictWindows = conflictWindows;
    }
    
    public int getConflictCount() {
        return conflictCount;
    }
    
    public void setConflictCount(int conflictCount) {
        this.conflictCount = conflictCount;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }

    public Set<Integer> getConflictNewWindowIndexes() {
        return conflictNewWindowIndexes;
    }

    public void setConflictNewWindowIndexes(Set<Integer> conflictNewWindowIndexes) {
        this.conflictNewWindowIndexes = conflictNewWindowIndexes;
    }
}
