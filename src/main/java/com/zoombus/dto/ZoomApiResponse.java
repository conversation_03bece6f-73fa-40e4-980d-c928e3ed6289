package com.zoombus.dto;

import lombok.Data;

@Data
public class ZoomApiResponse<T> {
    private boolean success;
    private String message;
    private T data;
    private String errorCode;
    /**
     * 实际HTTP状态码（用于调试与诊断, 可为空表示未获取）
     */
    private Integer httpStatus;
    
    public static <T> ZoomApiResponse<T> success(T data) {
        ZoomApiResponse<T> response = new ZoomApiResponse<>();
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    public static <T> ZoomApiResponse<T> success(T data, String message) {
        ZoomApiResponse<T> response = new ZoomApiResponse<>();
        response.setSuccess(true);
        response.setData(data);
        response.setMessage(message);
        return response;
    }

    public static ZoomApiResponse<String> successWithMessage(String message) {
        ZoomApiResponse<String> response = new ZoomApiResponse<>();
        response.setSuccess(true);
        response.setMessage(message);
        response.setData(message);
        return response;
    }
    
    public static <T> ZoomApiResponse<T> error(String message, String errorCode) {
        ZoomApiResponse<T> response = new ZoomApiResponse<>();
        response.setSuccess(false);
        response.setMessage(message);
        response.setErrorCode(errorCode);
        return response;
    }

    /**
     * 带状态码的成功响应
     */
    public static <T> ZoomApiResponse<T> successWithStatus(T data, int httpStatus) {
        ZoomApiResponse<T> response = success(data);
        response.setHttpStatus(httpStatus);
        return response;
    }

    /**
     * 带状态码的错误响应
     */
    public static <T> ZoomApiResponse<T> error(String message, String errorCode, int httpStatus) {
        ZoomApiResponse<T> response = error(message, errorCode);
        response.setHttpStatus(httpStatus);
        return response;
    }

    // Getter and Setter methods (manually added due to Lombok compatibility issues)
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public T getData() { return data; }
    public void setData(T data) { this.data = data; }

    public String getErrorCode() { return errorCode; }
    public void setErrorCode(String errorCode) { this.errorCode = errorCode; }

    public Integer getHttpStatus() { return httpStatus; }
    public void setHttpStatus(Integer httpStatus) { this.httpStatus = httpStatus; }
}
