package com.zoombus.dto;

import com.zoombus.entity.ZoomAuth;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ZoomAuth编辑响应DTO - 包含敏感字段用于编辑
 */
@Data
public class ZoomAuthEditResponse {
    
    private Long id;
    private String accountName;
    private String zoomAccountId;
    private String primaryEmail;
    private String clientId;
    private String clientSecret;  // 编辑时需要显示的敏感字段
    private ZoomAuth.AuthType authType;
    private ZoomAuth.AuthStatus status;
    private String webhookSecretToken;
    private String apiBaseUrl;
    private String description;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Token相关信息（只读，不用于编辑）
    private String tokenType;
    private Integer expiresIn;
    private LocalDateTime tokenIssuedAt;
    private LocalDateTime tokenExpiresAt;
    private String scope;
    private LocalDateTime lastRefreshAt;
    private Integer refreshCount;
    private String errorMessage;
    
    /**
     * 从ZoomAuth实体创建编辑响应DTO
     */
    public static ZoomAuthEditResponse fromEntity(ZoomAuth zoomAuth) {
        ZoomAuthEditResponse response = new ZoomAuthEditResponse();
        response.setId(zoomAuth.getId());
        response.setAccountName(zoomAuth.getAccountName());
        response.setZoomAccountId(zoomAuth.getZoomAccountId());
        response.setPrimaryEmail(zoomAuth.getPrimaryEmail());
        response.setClientId(zoomAuth.getClientId());
        response.setClientSecret(zoomAuth.getClientSecret());
        response.setAuthType(zoomAuth.getAuthType());
        response.setStatus(zoomAuth.getStatus());
        response.setWebhookSecretToken(zoomAuth.getWebhookSecretToken());
        response.setApiBaseUrl(zoomAuth.getApiBaseUrl());
        response.setDescription(zoomAuth.getDescription());
        response.setCreatedAt(zoomAuth.getCreatedAt());
        response.setUpdatedAt(zoomAuth.getUpdatedAt());
        
        // Token相关信息
        response.setTokenType(zoomAuth.getTokenType());
        response.setExpiresIn(zoomAuth.getExpiresIn());
        response.setTokenIssuedAt(zoomAuth.getTokenIssuedAt());
        response.setTokenExpiresAt(zoomAuth.getTokenExpiresAt());
        response.setScope(zoomAuth.getScope());
        response.setLastRefreshAt(zoomAuth.getLastRefreshAt());
        response.setRefreshCount(zoomAuth.getRefreshCount());
        response.setErrorMessage(zoomAuth.getErrorMessage());
        
        return response;
    }
}
