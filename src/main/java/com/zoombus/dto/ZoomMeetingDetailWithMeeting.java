package com.zoombus.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Zoom会议详情DTO，包含从Meeting表获取的正确字段
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZoomMeetingDetailWithMeeting {
    
    // ZoomMeetingDetail 的字段
    private Long id;
    private Long meetingId;
    private String zoomMeetingId;
    private String uuid;
    private String status;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    private String hostId;
    private String hostEmail;
    private String hostKey;
    private String joinUrl;
    private String startUrl;
    private String pmi;
    private String occurrenceId;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime occurrenceStartTime;
    
    private String occurrenceStatus;
    private String settings;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime zoomCreatedAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    private String trackingFields;
    private Boolean encrypted;
    private Boolean waitingRoom;
    private Boolean muteUponEntry;
    private Boolean joinBeforeHost;
    private String autoRecording;
    private String alternativeHosts;
    private Boolean isMainOccurrence;
    
    // 从Meeting表获取的字段
    private String topic;
    private Integer type;
    private String timezone;
    private Integer duration;
    private String password;
    private String agenda;
    
    /**
     * 从ZoomMeetingDetail和Meeting构造
     */
    public static ZoomMeetingDetailWithMeeting from(
            com.zoombus.entity.ZoomMeetingDetail detail, 
            com.zoombus.entity.Meeting meeting) {
        
        return ZoomMeetingDetailWithMeeting.builder()
                // ZoomMeetingDetail 的字段
                .id(detail.getId())
                .meetingId(detail.getMeetingId())
                .zoomMeetingId(detail.getZoomMeetingId())
                .uuid(detail.getUuid())
                .status(detail.getStatus())
                .startTime(detail.getStartTime())
                .hostId(detail.getHostId())
                .hostEmail(detail.getHostEmail())
                .hostKey(null) // 需要在Service层设置
                .joinUrl(detail.getJoinUrl())
                .startUrl(detail.getStartUrl())
                .pmi(detail.getPmi())
                .occurrenceId(detail.getOccurrenceId())
                .occurrenceStartTime(detail.getOccurrenceStartTime())
                .occurrenceStatus(detail.getOccurrenceStatus())
                .settings(detail.getSettings())
                .zoomCreatedAt(detail.getZoomCreatedAt())
                .createdAt(detail.getLocalCreatedAt())
                .updatedAt(detail.getLocalUpdatedAt())
                .trackingFields(detail.getTrackingFields())
                .encrypted(detail.getEncrypted())
                .waitingRoom(detail.getWaitingRoom())
                .muteUponEntry(detail.getMuteUponEntry())
                .joinBeforeHost(detail.getJoinBeforeHost())
                .autoRecording(detail.getAutoRecording())
                .alternativeHosts(detail.getAlternativeHosts())
                .isMainOccurrence(detail.getIsMainOccurrence())
                
                // 从Meeting表获取的字段
                .topic(meeting != null ? meeting.getTopic() : detail.getTopic())
                .type(meeting != null ? meeting.getType().getValue() : detail.getType())
                .timezone(meeting != null ? meeting.getTimezone() : detail.getTimezone())
                .duration(meeting != null ? meeting.getDurationMinutes() : detail.getDuration())
                .password(meeting != null ? meeting.getPassword() : detail.getPassword())
                .agenda(meeting != null ? meeting.getAgenda() : detail.getAgenda())
                
                .build();
    }
}
