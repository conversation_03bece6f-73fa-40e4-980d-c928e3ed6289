package com.zoombus.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Zoom会议参会者报告DTO
 * 对应Zoom API /report/meetings/{meetingId}/participants 的响应数据
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZoomMeetingParticipantsReportDto {
    
    @JsonProperty("page_count")
    private Integer pageCount;
    
    @JsonProperty("page_number")
    private Integer pageNumber;
    
    @JsonProperty("page_size")
    private Integer pageSize;
    
    @JsonProperty("total_records")
    private Integer totalRecords;
    
    @JsonProperty("next_page_token")
    private String nextPageToken;
    
    @JsonProperty("participants")
    private List<Participant> participants;
    
    /**
     * 参会者信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Participant {
        
        @JsonProperty("id")
        private String id;
        
        @JsonProperty("user_id")
        private String userId;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("user_email")
        private String userEmail;
        
        @JsonProperty("join_time")
        private String joinTime;
        
        @JsonProperty("leave_time")
        private String leaveTime;
        
        @JsonProperty("duration")
        private Integer duration;
        
        @JsonProperty("failover")
        private Boolean failover;
        
        @JsonProperty("status")
        private String status;
        
        @JsonProperty("customer_key")
        private String customerKey;
        
        @JsonProperty("registrant_id")
        private String registrantId;
        
        @JsonProperty("participant_user_id")
        private String participantUserId;
        
        @JsonProperty("bo_mtg_id")
        private String boMtgId;
        
        @JsonProperty("role")
        private String role;
        
        @JsonProperty("user_type")
        private Integer userType;
        
        @JsonProperty("recording_consent")
        private Boolean recordingConsent;
        
        @JsonProperty("in_waiting_room")
        private Boolean inWaitingRoom;
        
        @JsonProperty("attentiveness_score")
        private String attentivenessScore;
        
        @JsonProperty("focus_score")
        private String focusScore;
        
        @JsonProperty("sip_phone")
        private Boolean sipPhone;
        
        @JsonProperty("calling_country")
        private String callingCountry;
        
        @JsonProperty("phone_number")
        private String phoneNumber;
        
        @JsonProperty("country_name")
        private String countryName;
        
        @JsonProperty("city")
        private String city;
        
        @JsonProperty("network_type")
        private String networkType;
        
        @JsonProperty("join_count")
        private Integer joinCount;
        
        @JsonProperty("leave_count")
        private Integer leaveCount;
        
        @JsonProperty("share_application")
        private Boolean shareApplication;
        
        @JsonProperty("share_desktop")
        private Boolean shareDesktop;
        
        @JsonProperty("share_whiteboard")
        private Boolean shareWhiteboard;
        
        @JsonProperty("recording")
        private Boolean recording;
        
        @JsonProperty("pc_name")
        private String pcName;
        
        @JsonProperty("domain")
        private String domain;
        
        @JsonProperty("mac_addr")
        private String macAddr;
        
        @JsonProperty("harddisk_id")
        private String harddiskId;
        
        @JsonProperty("version")
        private String version;
        
        @JsonProperty("leave_reason")
        private String leaveReason;
        
        @JsonProperty("ip_address")
        private String ipAddress;
        
        @JsonProperty("location")
        private String location;
        
        @JsonProperty("data_center")
        private String dataCenter;
        
        @JsonProperty("connection_type")
        private String connectionType;
        
        @JsonProperty("microphone")
        private String microphone;
        
        @JsonProperty("speaker")
        private String speaker;
        
        @JsonProperty("camera")
        private String camera;
        
        @JsonProperty("audio_type")
        private String audioType;
        
        @JsonProperty("bo_data")
        private List<BoData> boData;
        
        /**
         * 分组会议数据
         */
        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class BoData {
            @JsonProperty("bo_mtg_id")
            private String boMtgId;
            
            @JsonProperty("bo_name")
            private String boName;
            
            @JsonProperty("bo_join_time")
            private String boJoinTime;
            
            @JsonProperty("bo_leave_time")
            private String boLeaveTime;
            
            @JsonProperty("bo_duration")
            private Integer boDuration;
        }
        
        /**
         * 转换加入时间为LocalDateTime
         */
        public LocalDateTime getJoinTimeAsLocalDateTime() {
            return parseZoomDateTime(joinTime);
        }
        
        /**
         * 转换离开时间为LocalDateTime
         */
        public LocalDateTime getLeaveTimeAsLocalDateTime() {
            return parseZoomDateTime(leaveTime);
        }
        
        /**
         * 解析Zoom API返回的时间格式
         */
        private LocalDateTime parseZoomDateTime(String dateTimeStr) {
            if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
                return null;
            }
            
            try {
                // 移除末尾的Z并解析
                String cleanDateTime = dateTimeStr.replace("Z", "");
                
                // 处理毫秒部分
                if (cleanDateTime.contains(".")) {
                    return LocalDateTime.parse(cleanDateTime, 
                        java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"));
                } else {
                    return LocalDateTime.parse(cleanDateTime, 
                        java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
                }
            } catch (Exception e) {
                try {
                    return LocalDateTime.parse(dateTimeStr, 
                        java.time.format.DateTimeFormatter.ISO_DATE_TIME);
                } catch (Exception ex) {
                    return null;
                }
            }
        }
        
        /**
         * 获取参会时长（分钟）
         */
        public Integer getDurationMinutes() {
            if (duration != null) {
                return duration;
            }
            
            // 如果没有直接的时长信息，尝试从加入和离开时间计算
            LocalDateTime join = getJoinTimeAsLocalDateTime();
            LocalDateTime leave = getLeaveTimeAsLocalDateTime();
            if (join != null && leave != null) {
                return (int) java.time.Duration.between(join, leave).toMinutes();
            }
            
            return null;
        }
        
        /**
         * 获取用户类型枚举
         */
        public com.zoombus.entity.MeetingParticipant.UserType getUserTypeEnum() {
            if (userType == null) {
                return com.zoombus.entity.MeetingParticipant.UserType.UNKNOWN;
            }
            
            switch (userType) {
                case 1: return com.zoombus.entity.MeetingParticipant.UserType.HOST;
                case 2: return com.zoombus.entity.MeetingParticipant.UserType.CO_HOST;
                case 3: return com.zoombus.entity.MeetingParticipant.UserType.PANELIST;
                case 0: 
                default: return com.zoombus.entity.MeetingParticipant.UserType.ATTENDEE;
            }
        }
        
        /**
         * 检查是否是主持人
         */
        public boolean isHost() {
            return userType != null && (userType == 1 || userType == 2);
        }
        
        /**
         * 检查是否还在会议中
         */
        public boolean isStillInMeeting() {
            return joinTime != null && (leaveTime == null || leaveTime.trim().isEmpty());
        }
        
        /**
         * 获取显示名称
         */
        public String getDisplayName() {
            if (name != null && !name.trim().isEmpty()) {
                return name;
            }
            if (userEmail != null && !userEmail.trim().isEmpty()) {
                return userEmail;
            }
            if (userId != null && !userId.trim().isEmpty()) {
                return userId;
            }
            return "未知用户";
        }
        
        /**
         * 获取连接方式描述
         */
        public String getConnectionDescription() {
            StringBuilder sb = new StringBuilder();
            
            if (audioType != null) {
                sb.append("音频: ").append(audioType);
            }
            
            if (Boolean.TRUE.equals(shareApplication)) {
                if (sb.length() > 0) sb.append(", ");
                sb.append("应用共享");
            }
            if (Boolean.TRUE.equals(shareDesktop)) {
                if (sb.length() > 0) sb.append(", ");
                sb.append("桌面共享");
            }
            if (Boolean.TRUE.equals(shareWhiteboard)) {
                if (sb.length() > 0) sb.append(", ");
                sb.append("白板共享");
            }
            if (Boolean.TRUE.equals(recording)) {
                if (sb.length() > 0) sb.append(", ");
                sb.append("录制");
            }
            if (Boolean.TRUE.equals(sipPhone)) {
                if (sb.length() > 0) sb.append(", ");
                sb.append("SIP电话");
            }
            
            return sb.length() > 0 ? sb.toString() : "标准连接";
        }
        
        /**
         * 获取地理位置信息
         */
        public String getLocationInfo() {
            StringBuilder sb = new StringBuilder();
            
            if (city != null && !city.trim().isEmpty()) {
                sb.append(city);
            }
            if (countryName != null && !countryName.trim().isEmpty()) {
                if (sb.length() > 0) sb.append(", ");
                sb.append(countryName);
            }
            if (location != null && !location.trim().isEmpty() && !location.equals(sb.toString())) {
                if (sb.length() > 0) sb.append(" (");
                sb.append(location);
                if (sb.toString().contains("(")) sb.append(")");
            }
            
            return sb.length() > 0 ? sb.toString() : "未知位置";
        }
        
        /**
         * 获取设备信息
         */
        public String getDeviceInfo() {
            StringBuilder sb = new StringBuilder();
            
            if (version != null && !version.trim().isEmpty()) {
                sb.append("版本: ").append(version);
            }
            if (pcName != null && !pcName.trim().isEmpty()) {
                if (sb.length() > 0) sb.append(", ");
                sb.append("设备: ").append(pcName);
            }
            if (networkType != null && !networkType.trim().isEmpty()) {
                if (sb.length() > 0) sb.append(", ");
                sb.append("网络: ").append(networkType);
            }
            
            return sb.length() > 0 ? sb.toString() : "未知设备";
        }
    }
}
