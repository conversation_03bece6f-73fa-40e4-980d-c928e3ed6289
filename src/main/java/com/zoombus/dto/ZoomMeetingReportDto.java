package com.zoombus.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Zoom会议报告DTO
 * 对应Zoom API /report/meetings/{meetingId} 的响应数据
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZoomMeetingReportDto {
    
    @JsonProperty("uuid")
    private String uuid;
    
    @JsonProperty("id")
    private Long id;
    
    @JsonProperty("topic")
    private String topic;
    
    @JsonProperty("type")
    private Integer type;
    
    @JsonProperty("start_time")
    private String startTime;
    
    @JsonProperty("end_time")
    private String endTime;
    
    @JsonProperty("duration")
    private Integer duration;
    
    @JsonProperty("total_minutes")
    private Integer totalMinutes;
    
    @JsonProperty("participants_count")
    private Integer participantsCount;
    
    @JsonProperty("tracking_fields")
    private List<TrackingField> trackingFields;
    
    @JsonProperty("dept")
    private String dept;
    
    @JsonProperty("source")
    private String source;
    
    @JsonProperty("user_name")
    private String userName;
    
    @JsonProperty("user_email")
    private String userEmail;
    
    @JsonProperty("host_id")
    private String hostId;
    
    @JsonProperty("custom_keys")
    private List<CustomKey> customKeys;
    
    @JsonProperty("has_pstn")
    private Boolean hasPstn;
    
    @JsonProperty("has_voip")
    private Boolean hasVoip;
    
    @JsonProperty("has_3rd_party_audio")
    private Boolean has3rdPartyAudio;
    
    @JsonProperty("has_video")
    private Boolean hasVideo;
    
    @JsonProperty("has_screen_share")
    private Boolean hasScreenShare;
    
    @JsonProperty("has_recording")
    private Boolean hasRecording;
    
    @JsonProperty("has_sip")
    private Boolean hasSip;
    
    @JsonProperty("has_archiving")
    private Boolean hasArchiving;
    
    /**
     * 跟踪字段
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TrackingField {
        @JsonProperty("field")
        private String field;
        
        @JsonProperty("value")
        private String value;
    }
    
    /**
     * 自定义键值
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CustomKey {
        @JsonProperty("key")
        private String key;
        
        @JsonProperty("value")
        private String value;
    }
    
    /**
     * 转换为LocalDateTime
     */
    public LocalDateTime getStartTimeAsLocalDateTime() {
        return parseZoomDateTime(startTime);
    }
    
    /**
     * 转换为LocalDateTime
     */
    public LocalDateTime getEndTimeAsLocalDateTime() {
        return parseZoomDateTime(endTime);
    }
    
    /**
     * 解析Zoom API返回的时间格式
     * 格式通常为: "2023-12-15T10:00:00Z" 或 "2023-12-15T10:00:00.000Z"
     */
    private LocalDateTime parseZoomDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 移除末尾的Z并解析
            String cleanDateTime = dateTimeStr.replace("Z", "");
            
            // 处理毫秒部分
            if (cleanDateTime.contains(".")) {
                // 有毫秒的情况: 2023-12-15T10:00:00.000
                return LocalDateTime.parse(cleanDateTime, 
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"));
            } else {
                // 没有毫秒的情况: 2023-12-15T10:00:00
                return LocalDateTime.parse(cleanDateTime, 
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
            }
        } catch (Exception e) {
            // 如果解析失败，尝试其他格式或返回null
            try {
                return LocalDateTime.parse(dateTimeStr, 
                    java.time.format.DateTimeFormatter.ISO_DATE_TIME);
            } catch (Exception ex) {
                return null;
            }
        }
    }
    
    /**
     * 获取会议时长（分钟）
     */
    public Integer getDurationMinutes() {
        if (duration != null) {
            return duration;
        }
        if (totalMinutes != null) {
            return totalMinutes;
        }
        
        // 如果没有直接的时长信息，尝试从开始和结束时间计算
        LocalDateTime start = getStartTimeAsLocalDateTime();
        LocalDateTime end = getEndTimeAsLocalDateTime();
        if (start != null && end != null) {
            return (int) java.time.Duration.between(start, end).toMinutes();
        }
        
        return null;
    }
    
    /**
     * 获取参会人数
     */
    public Integer getTotalParticipants() {
        return participantsCount != null ? participantsCount : 0;
    }
    
    /**
     * 检查是否有有效的时间信息
     */
    public boolean hasValidTimeInfo() {
        return startTime != null && !startTime.trim().isEmpty() &&
               endTime != null && !endTime.trim().isEmpty();
    }
    
    /**
     * 获取会议类型描述
     */
    public String getTypeDescription() {
        if (type == null) return "未知";
        
        switch (type) {
            case 1: return "即时会议";
            case 2: return "预定会议";
            case 3: return "定期会议（无固定时间）";
            case 8: return "定期会议（有固定时间）";
            default: return "其他类型(" + type + ")";
        }
    }
    
    /**
     * 获取数据源描述
     */
    public String getSourceDescription() {
        if (source == null) return "未知";
        
        switch (source.toLowerCase()) {
            case "zoom": return "Zoom客户端";
            case "web": return "Web浏览器";
            case "mobile": return "移动设备";
            case "phone": return "电话";
            case "sip": return "SIP设备";
            case "h323": return "H.323设备";
            default: return source;
        }
    }
    
    /**
     * 获取连接方式描述
     */
    public String getConnectionDescription() {
        StringBuilder sb = new StringBuilder();
        
        if (Boolean.TRUE.equals(hasVoip)) {
            sb.append("网络语音");
        }
        if (Boolean.TRUE.equals(hasPstn)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("电话");
        }
        if (Boolean.TRUE.equals(has3rdPartyAudio)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("第三方音频");
        }
        if (Boolean.TRUE.equals(hasVideo)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("视频");
        }
        if (Boolean.TRUE.equals(hasScreenShare)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("屏幕共享");
        }
        if (Boolean.TRUE.equals(hasRecording)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("录制");
        }
        if (Boolean.TRUE.equals(hasSip)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("SIP");
        }
        
        return sb.length() > 0 ? sb.toString() : "未知";
    }
}
