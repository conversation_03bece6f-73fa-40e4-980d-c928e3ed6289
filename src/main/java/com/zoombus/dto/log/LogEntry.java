package com.zoombus.dto.log;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * 日志条目DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogEntry {
    
    /**
     * 时间戳
     */
    private String timestamp;
    
    /**
     * 日志级别
     */
    private String level;
    
    /**
     * TraceId
     */
    private String traceId;
    
    /**
     * Logger名称
     */
    private String logger;
    
    /**
     * 日志消息
     */
    private String message;
    
    /**
     * 线程名称
     */
    private String thread;
    
    /**
     * 日志来源
     */
    private String source;
    
    /**
     * 上下文信息
     */
    private Map<String, Object> context;
    
    /**
     * 原始日志行
     */
    private String rawLine;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 行号
     */
    private Integer lineNumber;
}
