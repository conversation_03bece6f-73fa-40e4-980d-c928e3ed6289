package com.zoombus.dto.log;

import lombok.Data;

/**
 * 日志搜索请求DTO
 */
@Data
public class LogSearchRequest {
    
    /**
     * TraceId，支持完整匹配或部分匹配
     */
    private String traceId;
    
    /**
     * 时间范围 [startTime, endTime]
     */
    private String[] timeRange;
    
    /**
     * 日志级别：ERROR, WARN, INFO, DEBUG
     */
    private String logLevel;
    
    /**
     * 日志来源：APPLICATION, WEBHOOK, SCHEDULER, API
     */
    private String source;
    
    /**
     * 关键字搜索
     */
    private String keyword;
    
    /**
     * 页码，从1开始
     */
    private int page = 1;
    
    /**
     * 每页大小
     */
    private int size = 50;
    
    /**
     * 验证请求参数
     */
    public boolean isValid() {
        // 至少需要一个搜索条件
        return traceId != null && !traceId.trim().isEmpty() ||
               timeRange != null && timeRange.length == 2 ||
               logLevel != null && !logLevel.trim().isEmpty() ||
               source != null && !source.trim().isEmpty() ||
               keyword != null && !keyword.trim().isEmpty();
    }
}
