package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * Join Account密码变更日志实体类
 */
@Entity
@Table(name = "t_join_account_password_logs")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JoinAccountPasswordLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "Zoom账号ID不能为空")
    @Column(name = "zoom_user_id", nullable = false)
    private Long zoomUserId;
    
    @Size(max = 100, message = "旧密码长度不能超过100个字符")
    @Column(name = "old_password")
    private String oldPassword;
    
    @NotBlank(message = "新密码不能为空")
    @Size(max = 100, message = "新密码长度不能超过100个字符")
    @Column(name = "new_password", nullable = false)
    private String newPassword;
    
    @NotNull(message = "变更类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "change_type", nullable = false)
    private ChangeType changeType;
    
    @Column(name = "window_id")
    private Long windowId;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Size(max = 50, message = "操作人长度不能超过50个字符")
    @Column(name = "created_by")
    private String createdBy = "SYSTEM";
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    /**
     * 密码变更类型枚举
     */
    public enum ChangeType {
        RESERVATION("预约生成"),
        WINDOW_OPEN("窗口开启"),
        WINDOW_CLOSE("窗口关闭"),
        MANUAL("手动变更");
        
        private final String description;
        
        ChangeType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 获取脱敏的旧密码
     */
    public String getMaskedOldPassword() {
        return maskPassword(oldPassword);
    }
    
    /**
     * 获取脱敏的新密码
     */
    public String getMaskedNewPassword() {
        return maskPassword(newPassword);
    }
    
    /**
     * 密码脱敏处理
     * 显示前2位和后2位，中间用****替代
     */
    private String maskPassword(String password) {
        if (password == null || password.length() <= 4) {
            return "****";
        }
        
        if (password.length() <= 6) {
            return password.substring(0, 2) + "****";
        }
        
        return password.substring(0, 2) + "****" + password.substring(password.length() - 2);
    }
    
    /**
     * 检查是否为系统自动变更
     */
    public boolean isSystemChange() {
        return changeType == ChangeType.WINDOW_OPEN || changeType == ChangeType.WINDOW_CLOSE;
    }
    
    /**
     * 检查是否为手动变更
     */
    public boolean isManualChange() {
        return changeType == ChangeType.MANUAL;
    }
    
    /**
     * 检查是否关联了使用窗口
     */
    public boolean hasWindow() {
        return windowId != null;
    }
    
    /**
     * 获取变更类型的详细描述
     */
    public String getChangeTypeDescription() {
        switch (changeType) {
            case WINDOW_OPEN:
                return "系统自动开启使用窗口";
            case WINDOW_CLOSE:
                return "系统自动关闭使用窗口";
            case MANUAL:
                return "管理员手动变更";
            default:
                return changeType.getDescription();
        }
    }
    
    /**
     * 创建窗口开启日志
     */
    public static JoinAccountPasswordLog createWindowOpenLog(Long zoomUserId, String oldPassword, String newPassword, Long windowId) {
        JoinAccountPasswordLog log = new JoinAccountPasswordLog();
        log.setZoomUserId(zoomUserId);
        log.setOldPassword(oldPassword);
        log.setNewPassword(newPassword);
        log.setChangeType(ChangeType.WINDOW_OPEN);
        log.setWindowId(windowId);
        log.setCreatedBy("SYSTEM");
        return log;
    }
    
    /**
     * 创建窗口关闭日志
     */
    public static JoinAccountPasswordLog createWindowCloseLog(Long zoomUserId, String oldPassword, String newPassword, Long windowId) {
        JoinAccountPasswordLog log = new JoinAccountPasswordLog();
        log.setZoomUserId(zoomUserId);
        log.setOldPassword(oldPassword);
        log.setNewPassword(newPassword);
        log.setChangeType(ChangeType.WINDOW_CLOSE);
        log.setWindowId(windowId);
        log.setCreatedBy("SYSTEM");
        return log;
    }
    
    /**
     * 创建手动变更日志
     */
    public static JoinAccountPasswordLog createManualChangeLog(Long zoomUserId, String oldPassword, String newPassword, String operator) {
        JoinAccountPasswordLog log = new JoinAccountPasswordLog();
        log.setZoomUserId(zoomUserId);
        log.setOldPassword(oldPassword);
        log.setNewPassword(newPassword);
        log.setChangeType(ChangeType.MANUAL);
        log.setCreatedBy(operator != null ? operator : "ADMIN");
        return log;
    }
}
