package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * Join Account Rental令牌实体类
 */
@Entity
@Table(name = "t_join_account_rental_tokens")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JoinAccountRentalToken {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Token编号不能为空")
    @Size(min = 6, max = 6, message = "Token编号必须为6位")
    @Column(name = "token_number", nullable = false, unique = true)
    private String tokenNumber;
    
    @NotBlank(message = "批次号不能为空")
    @Size(max = 50, message = "批次号长度不能超过50个字符")
    @Column(name = "batch_number", nullable = false)
    private String batchNumber;
    
    @Min(value = 1, message = "使用天数必须大于0")
    @Column(name = "usage_days", nullable = false)
    private Integer usageDays;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private TokenStatus status = TokenStatus.PENDING;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "export_status")
    private ExportStatus exportStatus = ExportStatus.NOT_EXPORTED;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "exported_at")
    private LocalDateTime exportedAt;
    
    @Column(name = "reserved_at")
    private LocalDateTime reservedAt;
    
    @Column(name = "assigned_zoom_user_id")
    private Long assignedZoomUserId;
    
    @Size(max = 100, message = "分配的Zoom账号邮箱长度不能超过100个字符")
    @Column(name = "assigned_zoom_user_email")
    private String assignedZoomUserEmail;
    
    @Size(max = 20, message = "分配的密码长度不能超过20个字符")
    @Column(name = "assigned_password")
    private String assignedPassword;
    
    @Column(name = "window_start_time")
    private LocalDateTime windowStartTime;
    
    @Column(name = "window_end_time")
    private LocalDateTime windowEndTime;
    
    @Column(name = "cancelled_at")
    private LocalDateTime cancelledAt;
    
    @Size(max = 50, message = "作废操作人长度不能超过50个字符")
    @Column(name = "cancelled_by")
    private String cancelledBy;
    
    @Size(max = 50, message = "导出操作人长度不能超过50个字符")
    @Column(name = "exported_by")
    private String exportedBy;
    
    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    /**
     * 权益状态枚举
     */
    public enum TokenStatus {
        PENDING("待使用"),
        EXPORTED("已导出"),
        RESERVED("已预约"),
        ACTIVE("使用中"),
        COMPLETED("已完成"),
        CANCELLED("已作废");
        
        private final String description;
        
        TokenStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 导出状态枚举
     */
    public enum ExportStatus {
        NOT_EXPORTED("未导出"),
        EXPORTED("已导出");
        
        private final String description;
        
        ExportStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 生成权益链接
     */
    public String generateLink(String baseUrl, String pathPrefix) {
        if (baseUrl == null || pathPrefix == null || tokenNumber == null) {
            return null;
        }
        return baseUrl + "/" + pathPrefix + "/" + tokenNumber;
    }
    
    /**
     * 检查是否可以导出
     */
    public boolean canExport() {
        return status == TokenStatus.PENDING && exportStatus == ExportStatus.NOT_EXPORTED;
    }
    
    /**
     * 检查是否可以预约
     */
    public boolean canReserve() {
        return status == TokenStatus.PENDING || status == TokenStatus.EXPORTED;
    }
    
    /**
     * 检查是否可以作废
     */
    public boolean canCancel() {
        return status == TokenStatus.PENDING || status == TokenStatus.EXPORTED;
    }
    
    /**
     * 检查是否已分配账号
     */
    public boolean isAssigned() {
        return assignedZoomUserId != null && assignedZoomUserEmail != null;
    }
    
    /**
     * 检查是否在使用窗口内
     */
    public boolean isInWindow() {
        if (windowStartTime == null || windowEndTime == null) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        return !now.isBefore(windowStartTime) && !now.isAfter(windowEndTime);
    }
    
    /**
     * 计算结束日期
     */
    public LocalDateTime calculateEndTime(LocalDateTime startTime) {
        if (startTime == null || usageDays == null) {
            return null;
        }
        // 结束日期 = 开始日期 + 使用天数 - 1天，然后设置为当天的23:59:59
        return startTime.plusDays(usageDays - 1).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
    }
}
