package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * Join Account使用窗口实体类
 */
@Entity
@Table(name = "t_join_account_usage_windows")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JoinAccountUsageWindow {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "Zoom账号ID不能为空")
    @Column(name = "zoom_user_id", nullable = false)
    private Long zoomUserId;
    
    @NotBlank(message = "Token编号不能为空")
    @Size(min = 6, max = 6, message = "Token编号必须为6位")
    @Column(name = "token_number", nullable = false)
    private String tokenNumber;
    
    @NotNull(message = "窗口开始时间不能为空")
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    @NotNull(message = "窗口结束时间不能为空")
    @Column(name = "end_time", nullable = false)
    private LocalDateTime endTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private WindowStatus status = WindowStatus.PENDING;
    
    @Column(name = "opened_at")
    private LocalDateTime openedAt;
    
    @Column(name = "closed_at")
    private LocalDateTime closedAt;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "last_operation_error", columnDefinition = "TEXT")
    private String lastOperationError;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    /**
     * 窗口状态枚举
     */
    public enum WindowStatus {
        PENDING("待开启"),
        ACTIVE("使用中"),
        CLOSED("已关闭");
        
        private final String description;
        
        WindowStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 检查窗口是否需要开启
     */
    public boolean shouldOpen() {
        if (status != WindowStatus.PENDING) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        return !now.isBefore(startTime);
    }
    
    /**
     * 检查窗口是否需要关闭
     */
    public boolean shouldClose() {
        if (status != WindowStatus.ACTIVE) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        return !now.isBefore(endTime);
    }
    
    /**
     * 检查窗口是否在指定时间范围内重叠
     */
    public boolean overlapsWith(LocalDateTime otherStart, LocalDateTime otherEnd) {
        if (otherStart == null || otherEnd == null) {
            return false;
        }
        // 两个时间段重叠的条件：开始时间小于对方结束时间 且 结束时间大于对方开始时间
        return startTime.isBefore(otherEnd) && endTime.isAfter(otherStart);
    }
    
    /**
     * 检查窗口是否与另一个窗口重叠
     */
    public boolean overlapsWith(JoinAccountUsageWindow other) {
        if (other == null) {
            return false;
        }
        return overlapsWith(other.getStartTime(), other.getEndTime());
    }
    
    /**
     * 计算窗口持续时间（分钟）
     */
    public long getDurationMinutes() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return ChronoUnit.MINUTES.between(startTime, endTime);
    }
    
    /**
     * 计算实际使用时间（分钟）
     */
    public long getActualUsageMinutes() {
        if (openedAt == null) {
            return 0;
        }
        LocalDateTime actualEndTime = closedAt != null ? closedAt : LocalDateTime.now();
        return ChronoUnit.MINUTES.between(openedAt, actualEndTime);
    }
    
    /**
     * 检查窗口是否已过期
     */
    public boolean isExpired() {
        LocalDateTime now = LocalDateTime.now();
        return now.isAfter(endTime);
    }
    
    /**
     * 检查窗口是否正在进行中
     */
    public boolean isActive() {
        if (status != WindowStatus.ACTIVE) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        return !now.isBefore(startTime) && !now.isAfter(endTime);
    }
    
    /**
     * 获取窗口状态描述
     */
    public String getStatusDescription() {
        LocalDateTime now = LocalDateTime.now();
        
        switch (status) {
            case PENDING:
                if (now.isBefore(startTime)) {
                    return "等待开启";
                } else {
                    return "待开启（已到时间）";
                }
            case ACTIVE:
                if (now.isAfter(endTime)) {
                    return "使用中（已超时）";
                } else {
                    return "使用中";
                }
            case CLOSED:
                return "已关闭";
            default:
                return status.getDescription();
        }
    }
    
    /**
     * 获取距离开始时间的分钟数
     */
    public long getMinutesToStart() {
        if (startTime == null) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(startTime)) {
            return 0;
        }
        return ChronoUnit.MINUTES.between(now, startTime);
    }
    
    /**
     * 获取距离结束时间的分钟数
     */
    public long getMinutesToEnd() {
        if (endTime == null) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(endTime)) {
            return 0;
        }
        return ChronoUnit.MINUTES.between(now, endTime);
    }
}
