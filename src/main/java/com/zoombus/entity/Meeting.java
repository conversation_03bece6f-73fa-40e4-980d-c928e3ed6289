package com.zoombus.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "t_meetings")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Meeting {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 会议UUID，用于生成主持人页面链接
    @Column(name = "meeting_uuid", unique = true, nullable = false, length = 36)
    private String meetingUuid;

    // ZoomAccount已被移除，改为存储创建者信息
    @Column(name = "creator_user_id")
    private Long creatorUserId;

    @Column(name = "zoom_user_id")
    private String zoomUserId;
    
    // Zoom会议ID在创建阶段可以为空，创建成功后填入
    @Column(name = "zoom_meeting_id", unique = true, nullable = true)
    private String zoomMeetingId;
    
    @NotBlank(message = "会议主题不能为空")
    @Column(nullable = false)
    private String topic;
    
    @Column(columnDefinition = "TEXT")
    private String agenda;
    
    @NotNull(message = "开始时间不能为空")
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    @Column(name = "duration_minutes")
    private Integer durationMinutes;
    
    @Column(name = "timezone")
    private String timezone;
    
    @Column(name = "join_url", length = 1000)
    private String joinUrl;

    @Column(name = "start_url", length = 1000)
    private String startUrl;
    
    @Column(name = "meeting_password")
    private String password;

    // 主持人密钥（Host Key），用于会议控制权转移
    @Column(name = "host_key", length = 10)
    private String hostKey;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MeetingType type = MeetingType.SCHEDULED;

    // 周期性会议相关字段
    @Column(name = "is_recurring")
    private Boolean isRecurring = false;

    @Enumerated(EnumType.STRING)
    @Column(name = "recurrence_type")
    private RecurrenceType recurrenceType;

    @Column(name = "repeat_interval")
    private Integer repeatInterval = 1; // 重复间隔，默认1

    // 每周重复时选择的星期几，用逗号分隔，如"1,3,5"表示周一、周三、周五
    @Column(name = "weekly_days", length = 20)
    private String weeklyDays;

    // 每月重复的方式：DAY_OF_MONTH(按日期) 或 DAY_OF_WEEK(按星期)
    @Enumerated(EnumType.STRING)
    @Column(name = "monthly_type")
    private MonthlyType monthlyType;

    // 每月重复时的日期（1-31）或第几个（1-5，表示第1-5个）
    @Column(name = "monthly_day")
    private Integer monthlyDay;

    // 每月重复时的星期几（0-6，仅当monthlyType为DAY_OF_WEEK时使用）
    @Column(name = "monthly_week_day")
    private Integer monthlyWeekDay;

    @Enumerated(EnumType.STRING)
    @Column(name = "end_type")
    private EndType endType;

    @Column(name = "end_date_time")
    private LocalDateTime endDateTime;

    @Column(name = "end_times")
    private Integer endTimes;

    // 周期描述文字，用于前端显示
    @Column(name = "recurrence_description", length = 500)
    private String recurrenceDescription;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MeetingStatus status = MeetingStatus.SCHEDULED;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "creation_source", nullable = false)
    private CreationSource creationSource = CreationSource.ADMIN_PANEL;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 非持久化字段，用于前端显示
    @Transient
    private String hostEmail; // 主持人邮箱

    @Transient
    private Long hostZoomUserId; // 主持人ZoomUser ID

    @Transient
    private String creatorFullName; // 创建者姓名

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        // 如果没有设置UUID，自动生成一个
        if (meetingUuid == null || meetingUuid.trim().isEmpty()) {
            meetingUuid = UUID.randomUUID().toString();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
        // 验证业务逻辑：如果状态为SCHEDULED，zoomMeetingId不能为空
        validateZoomMeetingId();
    }

    /**
     * 验证Zoom会议ID的业务逻辑
     */
    private void validateZoomMeetingId() {
        if (status == MeetingStatus.SCHEDULED && (zoomMeetingId == null || zoomMeetingId.trim().isEmpty())) {
            throw new IllegalStateException("会议状态为SCHEDULED时，Zoom会议ID不能为空");
        }
    }
    
    public enum MeetingType {
        INSTANT(1), SCHEDULED(2), RECURRING_NO_FIXED_TIME(3), RECURRING_FIXED_TIME(8);
        
        private final int value;
        
        MeetingType(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
    }
    
    public enum MeetingStatus {
        CREATING,      // 正在创建中
        SCHEDULED,     // 已安排
        STARTED,       // 已开始
        ENDED,         // 已结束
        CANCELLED,     // 已取消
        CREATE_FAILED  // 创建失败
    }
    
    /**
     * 会议创建来源枚举
     * 与Zoom API webhook事件中的creation_source字段值保持一致
     */
    public enum CreationSource {
        /**
         * 管理台创建 - 通过ZoomBus管理台的会议安排功能创建
         */
        ADMIN_PANEL("admin_panel", "管理台", "通过ZoomBus管理台创建"),

        /**
         * Zoom桌面客户端创建 - 用户在Zoom桌面客户端创建
         */
        CLIENT("client", "Zoom客户端", "用户在Zoom桌面客户端创建"),

        /**
         * Zoom移动端创建 - 用户在Zoom移动应用创建
         */
        MOBILE("mobile", "移动端", "用户在Zoom移动应用创建"),

        /**
         * Zoom Web门户创建 - 用户在Zoom网页版创建
         * 对应webhook中的"web_portal"
         */
        WEB_PORTAL("web_portal", "网页版", "用户在Zoom网页版创建"),

        /**
         * API创建 - 通过Zoom API创建（包括第三方应用）
         */
        API("api", "API", "通过Zoom API创建"),

        /**
         * 电话创建 - 通过电话拨入创建
         */
        PHONE("phone", "电话", "通过电话拨入创建"),

        /**
         * 第三方集成创建 - 通过第三方应用集成创建（如Slack、Teams、Outlook等）
         */
        INTEGRATION("integration", "第三方集成", "通过第三方应用集成创建"),

        /**
         * 即时会议 - 立即开始的会议
         */
        INSTANT("instant", "即时会议", "立即开始的会议"),

        /**
         * PMI会议 - 使用个人会议室号码的会议
         */
        PMI("pmi", "PMI会议", "使用个人会议室号码的会议"),

        /**
         * 未知来源 - 无法确定创建来源或新的未识别来源
         */
        UNKNOWN("unknown", "未知", "无法确定创建来源"),

        /**
         * @deprecated 临时兼容旧数据，将在数据迁移后移除
         * 旧的Zoom应用Webhook值，应该迁移到UNKNOWN
         */
        @Deprecated
        ZOOM_APP_WEBHOOK("zoom_app_webhook", "Webhook同步", "通过webhook事件同步的会议（已废弃）");

        private final String apiValue;      // Zoom API中的实际值
        private final String displayName;   // 显示名称
        private final String description;   // 描述

        CreationSource(String apiValue, String displayName, String description) {
            this.apiValue = apiValue;
            this.displayName = displayName;
            this.description = description;
        }

        public String getApiValue() {
            return apiValue;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 根据Zoom API的creation_source字段值获取对应的枚举
         */
        public static CreationSource fromApiValue(String apiValue) {
            if (apiValue == null || apiValue.trim().isEmpty()) {
                return UNKNOWN;
            }

            for (CreationSource source : values()) {
                if (source.apiValue.equalsIgnoreCase(apiValue.trim())) {
                    return source;
                }
            }

            // 如果没有找到匹配的值，记录日志并返回UNKNOWN
            return UNKNOWN;
        }

        /**
         * 根据Zoom API的会议类型和创建方式推断创建来源（向后兼容）
         */
        public static CreationSource fromZoomMeetingType(int zoomType, boolean isPmi) {
            if (isPmi) {
                return PMI;
            }

            switch (zoomType) {
                case 1: // 即时会议
                    return INSTANT;
                case 2: // 预定会议
                    return CLIENT; // 默认认为是客户端创建
                case 3: // 无固定时间的周期性会议
                case 8: // 有固定时间的周期性会议
                    return CLIENT; // 默认认为是客户端创建
                default:
                    return UNKNOWN;
            }
        }

        /**
         * 判断是否为通过Zoom平台创建的会议
         */
        public boolean isZoomPlatformCreated() {
            return this == CLIENT || this == MOBILE || this == WEB_PORTAL ||
                   this == API || this == PMI || this == INSTANT ||
                   this == INTEGRATION || this == PHONE;
        }

        /**
         * 判断是否为系统内部创建的会议
         */
        public boolean isInternalCreated() {
            return this == ADMIN_PANEL;
        }
    }

    /**
     * 周期性会议重复类型
     */
    public enum RecurrenceType {
        DAILY("每天"),
        WEEKLY("每周"),
        MONTHLY("每月");

        private final String description;

        RecurrenceType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 周期性会议结束类型
     */
    public enum EndType {
        NO_END("无结束时间"),
        BY_DATE("直到"),
        BY_TIMES("个次数");

        private final String description;

        EndType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 每月重复类型
     */
    public enum MonthlyType {
        DAY_OF_MONTH("按日期"),    // 如每月21日
        DAY_OF_WEEK("按星期");     // 如每月第一个星期天

        private final String description;

        MonthlyType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }


}
