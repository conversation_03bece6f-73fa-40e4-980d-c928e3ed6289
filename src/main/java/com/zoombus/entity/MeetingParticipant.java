package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonBackReference;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 会议参会人员实体类
 * 存储会议中每个参会者的详细信息
 */
@Entity
@Table(name = "t_meeting_participants",
       indexes = {
           @Index(name = "idx_meeting_report_id", columnList = "meeting_report_id"),
           @Index(name = "idx_user_email", columnList = "user_email"),
           @Index(name = "idx_join_time", columnList = "join_time"),
           @Index(name = "idx_duration", columnList = "duration_minutes"),
           @Index(name = "idx_user_type", columnList = "user_type"),
           @Index(name = "idx_created_at", columnList = "created_at")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingParticipant {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "meeting_report_id", nullable = false)
    private Long meetingReportId;
    
    @Column(name = "participant_uuid", length = 255)
    private String participantUuid;
    
    @Column(name = "user_name", length = 255)
    private String participantName;

    @Column(name = "user_email", length = 255)
    private String participantEmail;
    
    @Column(name = "join_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime joinTime;
    
    @Column(name = "leave_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime leaveTime;
    
    @Column(name = "duration_minutes")
    private Integer durationMinutes;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "user_type")
    private UserType userType = UserType.ATTENDEE;
    
    @Column(name = "participant_user_id", length = 255)
    private String participantUserId;
    
    @Column(name = "registrant_id", length = 255)
    private String registrantId;
    
    @Column(name = "status", length = 50)
    private String status;
    
    @Column(name = "recording_consent")
    private Boolean recordingConsent;
    
    @Column(name = "in_waiting_room")
    private Boolean inWaitingRoom = false;
    
    @Column(name = "has_pstn")
    private Boolean hasPstn = false;
    
    @Column(name = "has_voip")
    private Boolean hasVoip = false;
    
    @Column(name = "has_3rd_party_audio")
    private Boolean has3rdPartyAudio = false;
    
    @Column(name = "has_video")
    private Boolean hasVideo = false;
    
    @Column(name = "has_screen_share")
    private Boolean hasScreenShare = false;
    
    @Column(name = "ip_address", length = 45)
    private String ipAddress;
    
    @Column(name = "location", length = 255)
    private String location;
    
    @Column(name = "network_type", length = 50)
    private String networkType;
    
    @Column(name = "microphone", length = 255)
    private String microphone;
    
    @Column(name = "speaker", length = 255)
    private String speaker;
    
    @Column(name = "camera", length = 255)
    private String camera;
    
    @Column(name = "data_center", length = 100)
    private String dataCenter;
    
    @Column(name = "connection_type", length = 50)
    private String connectionType;
    
    @Column(name = "join_count")
    private Integer joinCount = 1;
    
    @Column(name = "participant_data", columnDefinition = "JSON")
    private String participantData;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Column(name = "attentiveness_score")
    private Integer attentivenessScore;

    @Column(name = "failover")
    private Boolean failover = false;

    @Column(name = "role", length = 50)
    private String role;
    
    // 关联的会议报告（多对一关系）
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "meeting_report_id", insertable = false, updatable = false)
    @JsonBackReference
    private MeetingReport meetingReport;
    
    /**
     * 用户类型枚举
     */
    public enum UserType {
        HOST("主持人"),
        CO_HOST("联合主持人"),
        PANELIST("嘉宾"),
        ATTENDEE("参会者"),
        UNKNOWN("未知");
        
        private final String description;
        
        UserType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
        
        /**
         * 从字符串转换为枚举
         */
        public static UserType fromString(String value) {
            if (value == null || value.trim().isEmpty()) {
                return UNKNOWN;
            }
            
            String upperValue = value.toUpperCase();
            try {
                return UserType.valueOf(upperValue);
            } catch (IllegalArgumentException e) {
                // 处理一些常见的变体
                switch (upperValue) {
                    case "1":
                    case "HOST":
                        return HOST;
                    case "2":
                    case "CO-HOST":
                    case "COHOST":
                        return CO_HOST;
                    case "3":
                    case "PANELIST":
                        return PANELIST;
                    case "0":
                    case "ATTENDEE":
                    case "PARTICIPANT":
                        return ATTENDEE;
                    default:
                        return UNKNOWN;
                }
            }
        }
    }
    
    /**
     * 计算参会时长（如果加入和离开时间都存在）
     */
    public Integer calculateDurationMinutes() {
        if (joinTime != null && leaveTime != null) {
            return (int) java.time.Duration.between(joinTime, leaveTime).toMinutes();
        }
        return durationMinutes;
    }
    
    /**
     * 检查是否是主持人或联合主持人
     */
    public boolean isHost() {
        return userType == UserType.HOST || userType == UserType.CO_HOST;
    }
    
    /**
     * 检查是否还在会议中（没有离开时间）
     */
    public boolean isStillInMeeting() {
        return joinTime != null && leaveTime == null;
    }
    
    /**
     * 获取显示名称（优先使用姓名，其次邮箱，最后使用用户ID）
     */
    public String getDisplayName() {
        if (participantName != null && !participantName.trim().isEmpty()) {
            return participantName;
        }
        if (participantEmail != null && !participantEmail.trim().isEmpty()) {
            return participantEmail;
        }
        if (participantUserId != null && !participantUserId.trim().isEmpty()) {
            return participantUserId;
        }
        return "未知用户";
    }
    
    /**
     * 获取连接方式描述
     */
    public String getConnectionDescription() {
        StringBuilder sb = new StringBuilder();
        
        if (Boolean.TRUE.equals(hasVoip)) {
            sb.append("网络语音");
        }
        if (Boolean.TRUE.equals(hasPstn)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("电话");
        }
        if (Boolean.TRUE.equals(has3rdPartyAudio)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("第三方音频");
        }
        if (Boolean.TRUE.equals(hasVideo)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("视频");
        }
        if (Boolean.TRUE.equals(hasScreenShare)) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("屏幕共享");
        }
        
        return sb.length() > 0 ? sb.toString() : "未知";
    }
    
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return "MeetingParticipant{" +
                "id=" + id +
                ", meetingReportId=" + meetingReportId +
                ", participantName='" + participantName + '\'' +
                ", participantEmail='" + participantEmail + '\'' +
                ", joinTime=" + joinTime +
                ", leaveTime=" + leaveTime +
                ", durationMinutes=" + durationMinutes +
                ", userType=" + userType +
                ", createdAt=" + createdAt +
                '}';
    }
}
