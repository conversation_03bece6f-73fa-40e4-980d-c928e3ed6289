package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议报告实体类
 * 存储从Zoom Reports API获取的会议报告数据
 */
@Entity
@Table(name = "t_meeting_reports",
       indexes = {
           @Index(name = "idx_zoom_meeting_uuid", columnList = "zoom_meeting_uuid"),
           @Index(name = "idx_zoom_meeting_id", columnList = "zoom_meeting_id"),
           @Index(name = "idx_start_time", columnList = "start_time"),
           @Index(name = "idx_fetch_status", columnList = "fetch_status"),
           @Index(name = "idx_created_at", columnList = "created_at"),
           @Index(name = "idx_meeting_reports_pmi", columnList = "pmi_number"),
           @Index(name = "idx_meeting_reports_type", columnList = "meeting_type"),
           @Index(name = "idx_meeting_reports_auth", columnList = "zoom_auth_id"),
           @Index(name = "idx_meeting_reports_host", columnList = "host_user_id")
       },
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_zoom_meeting_uuid", columnNames = "zoom_meeting_uuid")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会议类型枚举
     */
    public enum MeetingType {
        SCHEDULED("预约会议"),
        PMI("PMI会议"),
        INSTANT("即时会议");

        private final String description;

        MeetingType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Zoom会议UUID不能为空")
    @Column(name = "zoom_meeting_uuid", nullable = false, unique = true, length = 255)
    private String zoomMeetingUuid;
    
    @NotBlank(message = "Zoom会议ID不能为空")
    @Column(name = "zoom_meeting_id", nullable = false, length = 255)
    private String zoomMeetingId;
    
    @Column(name = "topic", length = 500)
    private String topic;
    
    @Column(name = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    @Column(name = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    @Column(name = "duration_minutes")
    private Integer durationMinutes;
    
    @Column(name = "total_participants")
    private Integer totalParticipants = 0;
    
    @Column(name = "unique_participants")
    private Integer uniqueParticipants = 0;
    
    @Column(name = "has_recording")
    private Boolean hasRecording = false;
    
    @Column(name = "has_pstn")
    private Boolean hasPstn = false;
    
    @Column(name = "has_voip")
    private Boolean hasVoip = false;
    
    @Column(name = "has_3rd_party_audio")
    private Boolean has3rdPartyAudio = false;
    
    @Column(name = "has_video")
    private Boolean hasVideo = false;
    
    @Column(name = "has_screen_share")
    private Boolean hasScreenShare = false;

    @Column(name = "pmi_number", length = 20)
    private String pmiNumber;

    @Enumerated(EnumType.STRING)
    @Column(name = "meeting_type", length = 20)
    private MeetingType meetingType = MeetingType.SCHEDULED;

    @Column(name = "zoom_auth_id")
    private Long zoomAuthId;

    @Column(name = "host_user_id")
    private String hostUserId;

    @Column(name = "participant_count")
    private Integer participantCount = 0;

    @Column(name = "report_data", columnDefinition = "JSON")
    private String reportData;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "fetch_status", nullable = false)
    private FetchStatus fetchStatus = FetchStatus.PENDING;
    
    @Column(name = "fetch_error_message", columnDefinition = "TEXT")
    private String fetchErrorMessage;
    
    @Column(name = "fetch_retry_count")
    private Integer fetchRetryCount = 0;
    
    @Column(name = "last_fetch_attempt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastFetchAttempt;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    // 关联的参会人员列表（一对多关系）
    @OneToMany(mappedBy = "meetingReport", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<MeetingParticipant> participants;
    
    // 关联的Zoom会议记录（多对一关系）
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "zoom_meeting_uuid", referencedColumnName = "zoom_meeting_uuid", insertable = false, updatable = false)
    private ZoomMeeting zoomMeeting;
    
    /**
     * 报告获取状态枚举
     */
    public enum FetchStatus {
        PENDING("待获取"),
        SUCCESS("获取成功"),
        FAILED("获取失败"),
        RETRY("重试中");
        
        private final String description;
        
        FetchStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 计算会议时长（如果开始和结束时间都存在）
     */
    public Integer calculateDurationMinutes() {
        if (startTime != null && endTime != null) {
            return (int) java.time.Duration.between(startTime, endTime).toMinutes();
        }
        return durationMinutes;
    }
    
    /**
     * 检查是否需要重试获取报告
     */
    public boolean needsRetry() {
        return fetchStatus == FetchStatus.FAILED && 
               (fetchRetryCount == null || fetchRetryCount < 3);
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (fetchRetryCount == null) {
            fetchRetryCount = 0;
        }
        fetchRetryCount++;
        lastFetchAttempt = LocalDateTime.now();
    }
    
    /**
     * 标记获取成功
     */
    public void markFetchSuccess() {
        this.fetchStatus = FetchStatus.SUCCESS;
        this.fetchErrorMessage = null;
        this.lastFetchAttempt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 标记获取失败
     */
    public void markFetchFailed(String errorMessage) {
        this.fetchStatus = FetchStatus.FAILED;
        this.fetchErrorMessage = errorMessage;
        this.lastFetchAttempt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        incrementRetryCount();
    }
    
    /**
     * 获取参会人员数量
     */
    public int getParticipantCount() {
        return participants != null ? participants.size() : 0;
    }

    /**
     * 获取缓存的参会人数字段值
     */
    public Integer getParticipantCountField() {
        return participantCount;
    }

    /**
     * 设置缓存的参会人数字段值
     */
    public void setParticipantCountField(Integer participantCount) {
        this.participantCount = participantCount;
    }
    
    /**
     * 检查是否有有效的报告数据
     */
    public boolean hasValidReportData() {
        return fetchStatus == FetchStatus.SUCCESS && 
               reportData != null && 
               !reportData.trim().isEmpty();
    }
    
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return "MeetingReport{" +
                "id=" + id +
                ", zoomMeetingUuid='" + zoomMeetingUuid + '\'' +
                ", zoomMeetingId='" + zoomMeetingId + '\'' +
                ", topic='" + topic + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", durationMinutes=" + durationMinutes +
                ", totalParticipants=" + totalParticipants +
                ", uniqueParticipants=" + uniqueParticipants +
                ", fetchStatus=" + fetchStatus +
                ", createdAt=" + createdAt +
                '}';
    }
}
