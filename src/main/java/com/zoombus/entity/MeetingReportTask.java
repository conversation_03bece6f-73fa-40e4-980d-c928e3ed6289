package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会议报告获取任务实体类
 * 用于管理会议报告的异步获取任务，支持重试机制
 */
@Entity
@Table(name = "t_meeting_report_tasks",
       indexes = {
           @Index(name = "idx_zoom_meeting_uuid", columnList = "zoom_meeting_uuid"),
           @Index(name = "idx_task_status", columnList = "task_status"),
           @Index(name = "idx_scheduled_time", columnList = "scheduled_time"),
           @Index(name = "idx_priority", columnList = "priority"),
           @Index(name = "idx_created_at", columnList = "created_at")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MeetingReportTask implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Zoom会议UUID不能为空")
    @Column(name = "zoom_meeting_uuid", nullable = false, length = 255)
    private String zoomMeetingUuid;
    
    @NotBlank(message = "Zoom会议ID不能为空")
    @Column(name = "zoom_meeting_id", nullable = false, length = 255)
    private String zoomMeetingId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "task_status", nullable = false)
    private TaskStatus taskStatus = TaskStatus.PENDING;
    
    @Column(name = "priority")
    private Integer priority = 5;
    
    @Column(name = "scheduled_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime scheduledTime;
    
    @Column(name = "started_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startedTime;
    
    @Column(name = "completed_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedTime;
    
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    
    @Column(name = "max_retry_count")
    private Integer maxRetryCount = 3;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    @Column(name = "task_data", columnDefinition = "JSON")
    private String taskData;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        SUCCESS("成功"),
        FAILED("失败"),
        CANCELLED("已取消");
        
        private final String description;
        
        TaskStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 检查任务是否可以执行
     */
    public boolean canExecute() {
        return taskStatus == TaskStatus.PENDING && 
               (scheduledTime == null || scheduledTime.isBefore(LocalDateTime.now()));
    }
    
    /**
     * 检查是否需要重试
     */
    public boolean needsRetry() {
        return taskStatus == TaskStatus.FAILED && 
               retryCount < maxRetryCount;
    }
    
    /**
     * 开始执行任务
     */
    public void startExecution() {
        this.taskStatus = TaskStatus.PROCESSING;
        this.startedTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 标记任务成功
     */
    public void markSuccess() {
        this.taskStatus = TaskStatus.SUCCESS;
        this.completedTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.errorMessage = null;
    }
    
    /**
     * 标记任务失败
     */
    public void markFailed(String errorMessage) {
        this.taskStatus = TaskStatus.FAILED;
        this.completedTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.errorMessage = errorMessage;
        this.retryCount++;
    }
    
    /**
     * 取消任务
     */
    public void cancel(String reason) {
        this.taskStatus = TaskStatus.CANCELLED;
        this.completedTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.errorMessage = reason;
    }
    
    /**
     * 重置任务为待处理状态（用于重试）
     */
    public void resetForRetry() {
        if (needsRetry()) {
            this.taskStatus = TaskStatus.PENDING;
            this.startedTime = null;
            this.completedTime = null;
            this.updatedAt = LocalDateTime.now();
            
            // 计算下次重试时间（指数退避）
            int delayMinutes = (int) Math.pow(2, retryCount) * 5; // 5, 10, 20分钟
            this.scheduledTime = LocalDateTime.now().plusMinutes(delayMinutes);
        }
    }
    
    /**
     * 计算任务执行时长（分钟）
     */
    public Integer getExecutionDurationMinutes() {
        if (startedTime != null && completedTime != null) {
            return (int) java.time.Duration.between(startedTime, completedTime).toMinutes();
        }
        return null;
    }
    
    /**
     * 检查任务是否已完成（成功或失败且不需要重试）
     */
    public boolean isCompleted() {
        return taskStatus == TaskStatus.SUCCESS || 
               taskStatus == TaskStatus.CANCELLED ||
               (taskStatus == TaskStatus.FAILED && !needsRetry());
    }
    
    /**
     * 获取任务优先级描述
     */
    public String getPriorityDescription() {
        if (priority == null) return "普通";
        
        if (priority <= 2) return "高";
        if (priority <= 5) return "普通";
        if (priority <= 8) return "低";
        return "最低";
    }
    
    /**
     * 创建立即执行的任务
     */
    public static MeetingReportTask createImmediateTask(String zoomMeetingUuid, String zoomMeetingId) {
        MeetingReportTask task = new MeetingReportTask();
        task.setZoomMeetingUuid(zoomMeetingUuid);
        task.setZoomMeetingId(zoomMeetingId);
        task.setTaskStatus(TaskStatus.PENDING);
        task.setPriority(1); // 高优先级
        task.setScheduledTime(LocalDateTime.now());
        task.setRetryCount(0);
        task.setMaxRetryCount(3);
        return task;
    }
    
    /**
     * 创建延迟执行的任务
     */
    public static MeetingReportTask createDelayedTask(String zoomMeetingUuid, String zoomMeetingId, 
                                                     LocalDateTime scheduledTime, Integer priority) {
        MeetingReportTask task = new MeetingReportTask();
        task.setZoomMeetingUuid(zoomMeetingUuid);
        task.setZoomMeetingId(zoomMeetingId);
        task.setTaskStatus(TaskStatus.PENDING);
        task.setPriority(priority != null ? priority : 5);
        task.setScheduledTime(scheduledTime);
        task.setRetryCount(0);
        task.setMaxRetryCount(3);
        return task;
    }
    
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
        if (scheduledTime == null) {
            scheduledTime = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return "MeetingReportTask{" +
                "id=" + id +
                ", zoomMeetingUuid='" + zoomMeetingUuid + '\'' +
                ", zoomMeetingId='" + zoomMeetingId + '\'' +
                ", taskStatus=" + taskStatus +
                ", priority=" + priority +
                ", scheduledTime=" + scheduledTime +
                ", retryCount=" + retryCount +
                ", createdAt=" + createdAt +
                '}';
    }
}
