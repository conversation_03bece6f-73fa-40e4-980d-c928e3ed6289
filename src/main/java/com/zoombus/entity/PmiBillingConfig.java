package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * PMI计费配置实体类
 */
@Entity
@Table(name = "t_pmi_billing_config")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmiBillingConfig {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "配置名称不能为空")
    @Column(name = "config_name", nullable = false, unique = true)
    private String configName;
    
    @Min(value = 1, message = "计费单位必须大于0")
    @Column(name = "billing_unit_minutes")
    private Integer billingUnitMinutes = 1;
    
    @Min(value = 1, message = "最小计费时长必须大于0")
    @Column(name = "min_billing_minutes")
    private Integer minBillingMinutes = 1;
    
    @Min(value = 0, message = "免费时长不能小于0")
    @Column(name = "grace_period_minutes")
    private Integer gracePeriodMinutes = 0;
    
    @DecimalMin(value = "0.0", message = "费率不能小于0")
    @Column(name = "rate_per_minute", precision = 10, scale = 4)
    private BigDecimal ratePerMinute = BigDecimal.ZERO;
    
    @Column(name = "currency", length = 10)
    private String currency = "CNY";
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "effective_date")
    private LocalDateTime effectiveDate;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (effectiveDate == null) {
            effectiveDate = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 计算指定分钟数的费用
     */
    public BigDecimal calculateCost(int minutes) {
        if (minutes <= 0 || ratePerMinute == null) {
            return BigDecimal.ZERO;
        }
        
        // 扣除免费时长
        int billableMinutes = Math.max(0, minutes - gracePeriodMinutes);
        
        // 按计费单位向上取整
        int billingUnits = (int) Math.ceil((double) billableMinutes / billingUnitMinutes);
        int actualBillableMinutes = billingUnits * billingUnitMinutes;
        
        // 应用最小计费时长
        actualBillableMinutes = Math.max(actualBillableMinutes, minBillingMinutes);
        
        return ratePerMinute.multiply(BigDecimal.valueOf(actualBillableMinutes));
    }
    
    /**
     * 计算实际计费分钟数
     */
    public int calculateBillableMinutes(int actualMinutes) {
        if (actualMinutes <= 0) {
            return 0;
        }
        
        // 扣除免费时长
        int billableMinutes = Math.max(0, actualMinutes - gracePeriodMinutes);
        
        // 按计费单位向上取整
        int billingUnits = (int) Math.ceil((double) billableMinutes / billingUnitMinutes);
        int actualBillableMinutes = billingUnits * billingUnitMinutes;
        
        // 应用最小计费时长
        return Math.max(actualBillableMinutes, minBillingMinutes);
    }
    
    /**
     * 检查配置是否有效
     */
    public boolean isEffective() {
        return isActive && effectiveDate != null && effectiveDate.isBefore(LocalDateTime.now());
    }
    
    /**
     * 检查是否为免费配置
     */
    public boolean isFree() {
        return ratePerMinute == null || ratePerMinute.compareTo(BigDecimal.ZERO) == 0;
    }
    
    /**
     * 获取费率描述
     */
    public String getRateDescription() {
        if (isFree()) {
            return "免费";
        }
        return String.format("%.4f %s/分钟", ratePerMinute, currency);
    }
    
    /**
     * 获取计费规则描述
     */
    public String getBillingRuleDescription() {
        StringBuilder sb = new StringBuilder();
        
        if (gracePeriodMinutes > 0) {
            sb.append(String.format("免费时长: %d分钟; ", gracePeriodMinutes));
        }
        
        sb.append(String.format("计费单位: %d分钟; ", billingUnitMinutes));
        sb.append(String.format("最小计费: %d分钟; ", minBillingMinutes));
        sb.append(String.format("费率: %s", getRateDescription()));
        
        return sb.toString();
    }
}
