package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * PMI计费流水记录实体类
 */
@Entity
@Table(name = "t_pmi_billing_records")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmiBillingRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "PMI记录ID不能为空")
    @Column(name = "pmi_record_id", nullable = false)
    private Long pmiRecordId;
    
    @Column(name = "zoom_meeting_id")
    private Long zoomMeetingId;
    
    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false)
    private TransactionType transactionType;
    
    @NotNull(message = "时长变动不能为空")
    @Column(name = "amount_minutes", nullable = false)
    private Integer amountMinutes;
    
    @NotNull(message = "变动前余额不能为空")
    @Column(name = "balance_before", nullable = false)
    private Integer balanceBefore;
    
    @NotNull(message = "变动后余额不能为空")
    @Column(name = "balance_after", nullable = false)
    private Integer balanceAfter;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @Column(name = "billing_period_start")
    private LocalDateTime billingPeriodStart;
    
    @Column(name = "billing_period_end")
    private LocalDateTime billingPeriodEnd;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private RecordStatus status = RecordStatus.PENDING;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "created_by")
    private String createdBy;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    /**
     * 交易类型枚举
     */
    public enum TransactionType {
        RECHARGE("RECHARGE", "充值"),
        DEDUCT("DEDUCT", "扣费"),
        REFUND("REFUND", "退款"),
        ADJUSTMENT("ADJUSTMENT", "调整");
        
        private final String value;
        private final String description;
        
        TransactionType(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static TransactionType fromValue(String value) {
            for (TransactionType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            return DEDUCT;
        }
    }
    
    /**
     * 记录状态枚举
     */
    public enum RecordStatus {
        PENDING("PENDING", "待处理"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "失败"),
        CANCELLED("CANCELLED", "已取消");
        
        private final String value;
        private final String description;
        
        RecordStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static RecordStatus fromValue(String value) {
            for (RecordStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return PENDING;
        }
    }
    
    /**
     * 检查是否为充值记录
     */
    public boolean isRecharge() {
        return transactionType == TransactionType.RECHARGE;
    }
    
    /**
     * 检查是否为扣费记录
     */
    public boolean isDeduct() {
        return transactionType == TransactionType.DEDUCT;
    }
    
    /**
     * 检查是否为退款记录
     */
    public boolean isRefund() {
        return transactionType == TransactionType.REFUND;
    }
    
    /**
     * 检查记录是否已完成
     */
    public boolean isCompleted() {
        return status == RecordStatus.COMPLETED;
    }
    
    /**
     * 获取时长变动的符号表示
     */
    public String getAmountMinutesWithSign() {
        if (amountMinutes == null) {
            return "0";
        }
        
        if (transactionType == TransactionType.RECHARGE || transactionType == TransactionType.REFUND) {
            return "+" + amountMinutes;
        } else {
            return "-" + Math.abs(amountMinutes);
        }
    }
}
