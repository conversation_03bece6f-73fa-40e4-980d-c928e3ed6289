package com.zoombus.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * PMI会议统计实体类
 * 用于存储PMI级别的会议统计数据
 */
@Entity
@Table(name = "t_pmi_meeting_stats",
       indexes = {
           @Index(name = "idx_pmi_record_id", columnList = "pmi_record_id"),
           @Index(name = "idx_last_meeting_time", columnList = "last_meeting_time"),
           @Index(name = "idx_total_meetings", columnList = "total_meetings"),
           @Index(name = "idx_most_used_auth", columnList = "most_used_zoom_auth_id")
       },
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_pmi_number", columnNames = "pmi_number")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PmiMeetingStats implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "pmi_number", length = 20, nullable = false)
    private String pmiNumber;

    @Column(name = "pmi_record_id")
    private Long pmiRecordId;

    @Column(name = "total_meetings")
    private Integer totalMeetings = 0;

    @Column(name = "total_duration_minutes")
    private Integer totalDurationMinutes = 0;

    @Column(name = "total_participants")
    private Integer totalParticipants = 0;

    @Column(name = "unique_participants_count")
    private Integer uniqueParticipantsCount = 0;

    @Column(name = "avg_duration_minutes", precision = 10, scale = 2)
    private BigDecimal avgDurationMinutes = BigDecimal.ZERO;

    @Column(name = "avg_participants", precision = 10, scale = 2)
    private BigDecimal avgParticipants = BigDecimal.ZERO;

    @Column(name = "last_meeting_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastMeetingTime;

    @Column(name = "first_meeting_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstMeetingTime;

    @Column(name = "most_used_zoom_auth_id")
    private Long mostUsedZoomAuthId;

    @Column(name = "created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 关联的PMI记录（多对一关系）
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "pmi_record_id", insertable = false, updatable = false)
    private PmiRecord pmiRecord;

    // 关联的最常用Zoom主账号（多对一关系）
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "most_used_zoom_auth_id", insertable = false, updatable = false)
    private ZoomAuth mostUsedZoomAuth;

    /**
     * 计算平均会议时长
     */
    public BigDecimal calculateAvgDuration() {
        if (totalMeetings == null || totalMeetings == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(totalDurationMinutes).divide(BigDecimal.valueOf(totalMeetings), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算平均参会人数
     */
    public BigDecimal calculateAvgParticipants() {
        if (totalMeetings == null || totalMeetings == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(totalParticipants).divide(BigDecimal.valueOf(totalMeetings), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 更新统计数据
     */
    public void updateStats(Integer durationMinutes, Integer participants) {
        if (totalMeetings == null) totalMeetings = 0;
        if (totalDurationMinutes == null) totalDurationMinutes = 0;
        if (totalParticipants == null) totalParticipants = 0;

        totalMeetings++;
        totalDurationMinutes += (durationMinutes != null ? durationMinutes : 0);
        totalParticipants += (participants != null ? participants : 0);

        // 重新计算平均值
        avgDurationMinutes = calculateAvgDuration();
        avgParticipants = calculateAvgParticipants();
        updatedAt = LocalDateTime.now();
    }

    /**
     * 更新会议时间范围
     */
    public void updateMeetingTimeRange(LocalDateTime meetingTime) {
        if (meetingTime == null) return;

        if (firstMeetingTime == null || meetingTime.isBefore(firstMeetingTime)) {
            firstMeetingTime = meetingTime;
        }

        if (lastMeetingTime == null || meetingTime.isAfter(lastMeetingTime)) {
            lastMeetingTime = meetingTime;
        }
    }

    /**
     * 获取PMI使用天数
     */
    public Long getUsageDays() {
        if (firstMeetingTime == null || lastMeetingTime == null) {
            return 0L;
        }
        return java.time.Duration.between(firstMeetingTime, lastMeetingTime).toDays() + 1;
    }

    /**
     * 获取平均每天会议次数
     */
    public BigDecimal getAvgMeetingsPerDay() {
        Long usageDays = getUsageDays();
        if (usageDays == 0 || totalMeetings == null || totalMeetings == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(totalMeetings).divide(BigDecimal.valueOf(usageDays), 2, BigDecimal.ROUND_HALF_UP);
    }

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "PmiMeetingStats{" +
                "id=" + id +
                ", pmiNumber='" + pmiNumber + '\'' +
                ", totalMeetings=" + totalMeetings +
                ", totalDurationMinutes=" + totalDurationMinutes +
                ", totalParticipants=" + totalParticipants +
                ", avgDurationMinutes=" + avgDurationMinutes +
                ", avgParticipants=" + avgParticipants +
                ", lastMeetingTime=" + lastMeetingTime +
                ", firstMeetingTime=" + firstMeetingTime +
                ", createdAt=" + createdAt +
                '}';
    }
}
