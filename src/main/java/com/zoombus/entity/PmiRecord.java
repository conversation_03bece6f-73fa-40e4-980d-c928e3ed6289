package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * PMI记录实体类
 */
@Entity
@Table(name = "t_pmi_records")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmiRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @NotBlank(message = "PMI号码不能为空")
    @Pattern(regexp = "^[2-9][0-9]{9}$", message = "PMI号码格式不正确")
    @Column(name = "pmi_number", nullable = false, unique = true, length = 10)
    private String pmiNumber;

    @NotBlank(message = "魔链ID不能为空")
    @Column(name = "magic_id", nullable = false, unique = true, length = 50)
    private String magicId;
    
    @Column(name = "pmi_password", length = 50)
    private String pmiPassword;
    
    @Column(name = "host_url", columnDefinition = "TEXT")
    private String hostUrl;
    
    @Column(name = "join_url", columnDefinition = "TEXT")
    private String joinUrl;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PmiStatus status = PmiStatus.ACTIVE;
    
    @Column(name = "current_zoom_user_id")
    private Long currentZoomUserId;
    
    @Column(name = "last_used_at")
    private LocalDateTime lastUsedAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // ========== 计费相关字段 ==========

    @Enumerated(EnumType.STRING)
    @Column(name = "billing_mode")
    private BillingMode billingMode = BillingMode.BY_TIME;

    // 原始计费模式（用于窗口关闭后恢复）
    @Enumerated(EnumType.STRING)
    @Column(name = "original_billing_mode")
    private BillingMode originalBillingMode;

    // 按时段计费相关字段
    @Column(name = "current_window_id")
    private Long currentWindowId;

    @Column(name = "window_expire_time")
    private LocalDateTime windowExpireTime;

    // 活跃窗口ID列表（JSON格式存储）
    @Column(name = "active_window_ids", columnDefinition = "TEXT")
    private String activeWindowIds;

    // 按时长计费相关字段
    @Column(name = "total_minutes")
    private Integer totalMinutes = 0;

    @Column(name = "available_minutes")
    private Integer availableMinutes = 0;

    @Column(name = "pending_deduct_minutes")
    private Integer pendingDeductMinutes = 0;

    @Column(name = "overdraft_minutes")
    private Integer overdraftMinutes = 0;

    @Column(name = "total_used_minutes")
    private Integer totalUsedMinutes = 0;

    // 状态字段
    @Enumerated(EnumType.STRING)
    @Column(name = "billing_status")
    private BillingStatus billingStatus = BillingStatus.ACTIVE;

    @Column(name = "last_billing_time")
    private LocalDateTime lastBillingTime;

    @Column(name = "billing_updated_at")
    private LocalDateTime billingUpdatedAt;

    // ========== 回退功能相关字段 ==========

    /**
     * 是否启用回退到老系统
     */
    @Column(name = "fallback_enabled")
    private Boolean fallbackEnabled = false;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        billingUpdatedAt = LocalDateTime.now();
        if (fallbackEnabled == null) {
            fallbackEnabled = false;
        }
        // 如果magicId为空，则使用pmiNumber作为默认值
        if (magicId == null || magicId.trim().isEmpty()) {
            magicId = pmiNumber;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
        billingUpdatedAt = LocalDateTime.now();
    }

    /**
     * 计算剩余可用时长（可用时长 - 待扣时长）
     */
    public int getRemainingMinutes() {
        int available = availableMinutes != null ? availableMinutes : 0;
        int pending = pendingDeductMinutes != null ? pendingDeductMinutes : 0;
        return Math.max(0, available - pending);
    }
    
    /**
     * 计费模式枚举
     */
    public enum BillingMode {
        LONG("LONG", "按时段计费"),
        BY_TIME("BY_TIME", "按时长计费"),
        FREE("FREE", "免费");

        private final String value;
        private final String description;

        BillingMode(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static BillingMode fromValue(String value) {
            for (BillingMode mode : values()) {
                if (mode.value.equals(value)) {
                    return mode;
                }
            }
            return BY_TIME;
        }
    }

    /**
     * 计费状态枚举
     */
    public enum BillingStatus {
        ACTIVE("ACTIVE", "正常"),
        SUSPENDED("SUSPENDED", "暂停"),
        EXPIRED("EXPIRED", "已过期");

        private final String value;
        private final String description;

        BillingStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static BillingStatus fromValue(String value) {
            for (BillingStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return ACTIVE;
        }
    }

    /**
     * PMI状态枚举
     */
    public enum PmiStatus {
        ACTIVE("active", "活跃"),
        INACTIVE("inactive", "非活跃"),
        EXPIRED("expired", "已过期");
        
        private final String value;
        private final String description;
        
        PmiStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static PmiStatus fromValue(String value) {
            for (PmiStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return ACTIVE;
        }
    }
    
    /**
     * 生成复制信息文本
     */
    public String generateCopyText() {
        return generateCopyText("用户");
    }

    /**
     * 生成复制信息文本（带用户姓名）
     */
    public String generateCopyText(String userName) {
        StringBuilder sb = new StringBuilder();
        sb.append(userName != null && !userName.trim().isEmpty() ? userName : "用户").append("\n");
        sb.append("1、主持人在手机或者电脑浏览器里打开如下链接开启Zoom会议室：\n");
        sb.append("主持链接：").append(hostUrl != null ? hostUrl : "https://wxzoom.com/m/" + magicId).append("\n\n");
        sb.append("2、参会者通过如下会议号");

        // 只有当密码不为空时才添加密码信息
        if (pmiPassword != null && !pmiPassword.trim().isEmpty()) {
            sb.append("和密码");
        }
        sb.append("加入会议\n");
        sb.append("会议号：").append(pmiNumber).append("\n");

        // 只有当密码不为空时才显示密码行
        if (pmiPassword != null && !pmiPassword.trim().isEmpty()) {
            sb.append("会议密码：").append(pmiPassword).append("\n");
        }

        sb.append("参会邀请链接：").append(joinUrl != null ? joinUrl : generateDefaultJoinUrl()).append("\n\n");
        sb.append("注意：\n");
        sb.append("1>主持人开启会议室后，参会者才可以加入会议。请保管好您的主持链接，不要发给参会者。\n\n");
        sb.append("2>若主持或者加入失败，请从   https://zoom.us/download   安装新版Zoom客户端");
        return sb.toString();
    }
    
    /**
     * 生成默认的参会链接
     */
    private String generateDefaultJoinUrl() {
        // 根据PMI号码和密码生成默认的参会链接
        // 这里使用一个简化的格式，实际应该根据Zoom API返回的链接
        return String.format("https://us06web.zoom.us/j/%s?pwd=%s", pmiNumber, pmiPassword);
    }

    /**
     * 检查是否有活跃会议
     * 这个方法需要在Service层中实现，这里只是占位符
     */
    @Transient
    private Boolean hasActiveMeeting;

    /**
     * 当前活跃会议的Zoom用户ID
     */
    @Transient
    private Long activeMeetingZoomUserId;

    /**
     * 当前活跃会议的Zoom用户邮箱
     */
    @Transient
    private String activeMeetingZoomUserEmail;

    public Boolean getHasActiveMeeting() {
        return hasActiveMeeting;
    }

    public void setHasActiveMeeting(Boolean hasActiveMeeting) {
        this.hasActiveMeeting = hasActiveMeeting;
    }

    public Long getActiveMeetingZoomUserId() {
        return activeMeetingZoomUserId;
    }

    public void setActiveMeetingZoomUserId(Long activeMeetingZoomUserId) {
        this.activeMeetingZoomUserId = activeMeetingZoomUserId;
    }

    public String getActiveMeetingZoomUserEmail() {
        return activeMeetingZoomUserEmail;
    }

    public void setActiveMeetingZoomUserEmail(String activeMeetingZoomUserEmail) {
        this.activeMeetingZoomUserEmail = activeMeetingZoomUserEmail;
    }
}
