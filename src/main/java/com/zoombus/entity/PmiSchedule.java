package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * PMI计划实体类
 */
@Entity
@Table(name = "t_pmi_schedules")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmiSchedule {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "PMI记录ID不能为空")
    @Column(name = "pmi_record_id", nullable = false)
    private Long pmiRecordId;
    
    @NotBlank(message = "计划名称不能为空")
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @NotNull(message = "开始日期不能为空")
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;
    
    @NotNull(message = "结束日期不能为空")
    @Column(name = "end_date", nullable = false)
    private LocalDate endDate;
    
    @NotNull(message = "重复类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "repeat_type", nullable = false)
    private RepeatType repeatType;

    /**
     * 按周重复时选择的星期几 (1-7, 1=周一, 7=周日)
     * 存储为逗号分隔的字符串，如 "1,3,5" 表示周一、周三、周五
     */
    @Column(name = "week_days")
    private String weekDays;

    /**
     * 按月重复时选择的日期 (1-31)
     * 存储为逗号分隔的字符串，如 "1,15,31" 表示每月1号、15号、31号
     */
    @Column(name = "month_days")
    private String monthDays;

    @NotNull(message = "是否全天不能为空")
    @Column(name = "is_all_day", nullable = false)
    private Boolean isAllDay = false;
    
    @Column(name = "start_time")
    private LocalTime startTime;
    
    @Column(name = "duration_minutes")
    private Integer durationMinutes;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ScheduleStatus status = ScheduleStatus.ACTIVE;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 重复类型枚举
     */
    public enum RepeatType {
        ONCE("once", "仅一次"),
        DAILY("daily", "每天"),
        WEEKLY("weekly", "每周"),
        MONTHLY("monthly", "每月");

        private final String value;
        private final String description;

        RepeatType(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static RepeatType fromValue(String value) {
            for (RepeatType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            return ONCE;  // 默认改为ONCE，因为大部分数据是ONCE类型
        }
    }
    
    /**
     * 计划状态枚举
     */
    public enum ScheduleStatus {
        ACTIVE("active", "活跃"),
        INACTIVE("inactive", "非活跃"),
        COMPLETED("completed", "已完成");
        
        private final String value;
        private final String description;
        
        ScheduleStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static ScheduleStatus fromValue(String value) {
            for (ScheduleStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return ACTIVE;
        }
    }
    
    /**
     * 计算结束时间
     */
    public LocalTime getEndTime() {
        if (isAllDay || startTime == null || durationMinutes == null) {
            return null;
        }
        return startTime.plusMinutes(durationMinutes);
    }
    
    /**
     * 验证计划配置
     */
    public boolean isValid() {
        if (startDate == null || endDate == null) {
            return false;
        }
        
        if (startDate.isAfter(endDate)) {
            return false;
        }
        
        if (!isAllDay) {
            if (startTime == null || durationMinutes == null || durationMinutes <= 0) {
                return false;
            }
        }
        
        return true;
    }
}
