package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * PMI计划窗口实体类
 */
@Entity
@Table(name = "t_pmi_schedule_windows")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmiScheduleWindow {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "计划ID不能为空")
    @Column(name = "schedule_id", nullable = false)
    private Long scheduleId;

    @NotNull(message = "PMI记录ID不能为空")
    @Column(name = "pmi_record_id", nullable = false)
    private Long pmiRecordId;
    


    /**
     * 窗口开始时间（精确到秒）
     * 新字段：简化时间逻辑，替代 window_date + start_time 的组合
     */
    @Column(name = "start_date_time")
    private LocalDateTime startDateTime;

    /**
     * 窗口结束时间（精确到秒）
     * 新字段：简化时间逻辑，替代 end_date + end_time 的组合
     */
    @Column(name = "end_date_time")
    private LocalDateTime endDateTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private WindowStatus status = WindowStatus.PENDING;
    
    @Column(name = "zoom_user_id")
    private Long zoomUserId;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "actual_start_time")
    private LocalDateTime actualStartTime;

    @Column(name = "actual_end_time")
    private LocalDateTime actualEndTime;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "open_task_id")
    private Long openTaskId;

    @Column(name = "close_task_id")
    private Long closeTaskId;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 窗口状态枚举
     */
    public enum WindowStatus {
        PENDING("pending", "待执行"),
        ACTIVE("active", "执行中"),
        COMPLETED("completed", "已完成"),
        MANUALLY_CLOSED("manually_closed", "人工关闭"),
        FAILED("failed", "执行失败");
        
        private final String value;
        private final String description;
        
        WindowStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static WindowStatus fromValue(String value) {
            for (WindowStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return PENDING;
        }
    }
    
    /**
     * 检查窗口是否应该激活
     */
    public boolean shouldActivate(LocalDateTime currentTime) {
        if (status != WindowStatus.PENDING) {
            return false;
        }

        // 使用新的精确时间字段
        if (startDateTime == null) {
            return false;
        }
        // 提前5分钟激活
        return currentTime.isAfter(startDateTime.minusMinutes(5)) &&
               currentTime.isBefore(startDateTime.plusMinutes(5));
    }
    
    /**
     * 检查窗口是否应该完成
     */
    public boolean shouldComplete(LocalDateTime currentTime) {
        if (status != WindowStatus.ACTIVE) {
            return false;
        }

        // 使用新的精确时间字段
        if (endDateTime == null) {
            return false;
        }
        return currentTime.isAfter(endDateTime) || currentTime.equals(endDateTime);
    }
    
    /**
     * 获取窗口的开始时间
     */
    public LocalDateTime getStartDateTime() {
        return startDateTime;
    }

    /**
     * 获取窗口的结束时间
     */
    public LocalDateTime getEndDateTime() {
        return endDateTime;
    }
    
    /**
     * 检查窗口是否有效
     */
    public boolean isValid() {
        if (startDateTime == null || endDateTime == null) {
            return false;
        }

        return startDateTime.isBefore(endDateTime);
    }
}
