package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * PMI窗口定时任务实体类
 */
@Entity
@Table(name = "t_pmi_schedule_window_tasks")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmiScheduleWindowTask {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "PMI窗口ID不能为空")
    @Column(name = "pmi_window_id", nullable = false)
    private Long pmiWindowId;
    
    @NotNull(message = "任务类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "task_type", nullable = false)
    private TaskType taskType;
    
    @NotNull(message = "计划执行时间不能为空")
    @Column(name = "scheduled_time", nullable = false)
    private LocalDateTime scheduledTime;
    
    @Column(name = "actual_execution_time")
    private LocalDateTime actualExecutionTime;
    
    @NotNull(message = "任务状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TaskStatus status = TaskStatus.SCHEDULED;
    
    @NotNull(message = "任务键不能为空")
    @Column(name = "task_key", unique = true, nullable = false)
    private String taskKey;
    
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 任务类型枚举
     */
    public enum TaskType {
        PMI_WINDOW_OPEN("pmi_window_open", "PMI窗口开启"),
        PMI_WINDOW_CLOSE("pmi_window_close", "PMI窗口关闭");
        
        private final String value;
        private final String description;
        
        TaskType(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() { 
            return value; 
        }
        
        public String getDescription() { 
            return description; 
        }
    }
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        SCHEDULED("scheduled", "已调度"),
        EXECUTING("executing", "执行中"),
        COMPLETED("completed", "已完成"),
        FAILED("failed", "执行失败"),
        CANCELLED("cancelled", "已取消");
        
        private final String value;
        private final String description;
        
        TaskStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() { 
            return value; 
        }
        
        public String getDescription() { 
            return description; 
        }
    }
    
    /**
     * 检查任务是否可以取消
     */
    public boolean canCancel() {
        return status == TaskStatus.SCHEDULED;
    }
    
    /**
     * 检查任务是否可以重新调度
     */
    public boolean canReschedule() {
        return status == TaskStatus.SCHEDULED || status == TaskStatus.FAILED;
    }
    
    /**
     * 检查任务是否可以重试
     */
    public boolean canRetry() {
        return status == TaskStatus.FAILED && retryCount < 3; // 最多重试3次
    }
    
    /**
     * 标记任务为执行中
     */
    public void markAsExecuting() {
        this.status = TaskStatus.EXECUTING;
        this.actualExecutionTime = LocalDateTime.now();
    }
    
    /**
     * 标记任务为完成
     */
    public void markAsCompleted() {
        this.status = TaskStatus.COMPLETED;
        if (this.actualExecutionTime == null) {
            this.actualExecutionTime = LocalDateTime.now();
        }
    }
    
    /**
     * 标记任务为失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = TaskStatus.FAILED;
        this.errorMessage = errorMessage;
        this.retryCount = (this.retryCount == null ? 0 : this.retryCount) + 1;
        if (this.actualExecutionTime == null) {
            this.actualExecutionTime = LocalDateTime.now();
        }
    }
    
    /**
     * 标记任务为取消
     */
    public void markAsCancelled() {
        this.status = TaskStatus.CANCELLED;
    }
}
