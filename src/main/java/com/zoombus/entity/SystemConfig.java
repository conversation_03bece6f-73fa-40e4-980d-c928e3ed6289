package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 系统配置实体类
 */
@Entity
@Table(name = "t_system_config")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SystemConfig {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "配置键不能为空")
    @Size(max = 100, message = "配置键长度不能超过100个字符")
    @Column(name = "config_key", nullable = false, unique = true)
    private String configKey;
    
    @Column(name = "config_value", columnDefinition = "TEXT")
    private String configValue;
    
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    @Column(name = "description")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "config_type")
    private ConfigType configType = ConfigType.STRING;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Size(max = 50, message = "创建人长度不能超过50个字符")
    @Column(name = "created_by")
    private String createdBy = "SYSTEM";
    
    @Size(max = 50, message = "更新人长度不能超过50个字符")
    @Column(name = "updated_by")
    private String updatedBy = "SYSTEM";
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 配置类型枚举
     */
    public enum ConfigType {
        STRING("字符串"),
        NUMBER("数字"),
        BOOLEAN("布尔值"),
        JSON("JSON对象");
        
        private final String description;
        
        ConfigType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 获取配置值并转换为指定类型
     */
    public String getStringValue() {
        return configValue;
    }
    
    @JsonIgnore
    public Integer getIntValue() {
        if (configValue == null || configValue.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(configValue.trim());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("配置值 [" + configKey + "] 不是有效的整数: " + configValue);
        }
    }
    
    @JsonIgnore
    public Double getDoubleValue() {
        if (configValue == null || configValue.trim().isEmpty()) {
            return null;
        }
        try {
            return Double.parseDouble(configValue.trim());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("配置值 [" + configKey + "] 不是有效的小数: " + configValue);
        }
    }
    
    @JsonIgnore
    public Boolean getBooleanValue() {
        if (configValue == null || configValue.trim().isEmpty()) {
            return null;
        }
        String value = configValue.trim().toLowerCase();
        if ("true".equals(value) || "1".equals(value) || "yes".equals(value)) {
            return true;
        } else if ("false".equals(value) || "0".equals(value) || "no".equals(value)) {
            return false;
        } else {
            throw new IllegalArgumentException("配置值 [" + configKey + "] 不是有效的布尔值: " + configValue);
        }
    }
    
    /**
     * 设置配置值
     */
    public void setStringValue(String value) {
        this.configValue = value;
        this.configType = ConfigType.STRING;
    }
    
    public void setIntValue(Integer value) {
        this.configValue = value != null ? value.toString() : null;
        this.configType = ConfigType.NUMBER;
    }
    
    public void setDoubleValue(Double value) {
        this.configValue = value != null ? value.toString() : null;
        this.configType = ConfigType.NUMBER;
    }
    
    public void setBooleanValue(Boolean value) {
        this.configValue = value != null ? value.toString() : null;
        this.configType = ConfigType.BOOLEAN;
    }
    
    /**
     * 检查配置是否有效
     */
    public boolean isValid() {
        return isActive != null && isActive && configKey != null && !configKey.trim().isEmpty();
    }
}
