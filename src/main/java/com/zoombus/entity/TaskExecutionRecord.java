package com.zoombus.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 任务执行记录实体
 * 用于跟踪定时任务的执行状态和历史
 */
@Entity
@Table(name = "task_execution_record", indexes = {
    @Index(name = "idx_task_name_status", columnList = "task_name,status"),
    @Index(name = "idx_execution_time", columnList = "execution_time"),
    @Index(name = "idx_task_name_execution_time", columnList = "task_name,execution_time")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TaskExecutionRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 任务名称
     */
    @Column(name = "task_name", nullable = false, length = 100)
    private String taskName;
    
    /**
     * 任务类型
     */
    @Column(name = "task_type", length = 50)
    private String taskType;
    
    /**
     * 执行状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ExecutionStatus status;
    
    /**
     * 执行时间
     */
    @Column(name = "execution_time", nullable = false)
    private LocalDateTime executionTime;

    /**
     * 完成时间
     */
    @Column(name = "completion_time")
    private LocalDateTime completionTime;
    
    /**
     * 执行耗时（毫秒）
     */
    @Column(name = "duration_ms")
    private Long durationMs;
    
    /**
     * 处理的记录数量
     */
    @Column(name = "processed_count")
    private Integer processedCount;

    /**
     * 成功处理的记录数量
     */
    @Column(name = "success_count")
    private Integer successCount;

    /**
     * 失败处理的记录数量
     */
    @Column(name = "failure_count")
    private Integer failureCount;
    
    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * 错误堆栈信息
     */
    @Column(name = "error_stack", columnDefinition = "TEXT")
    private String errorStack;
    
    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    private Integer retryCount;
    
    /**
     * 最大重试次数
     */
    @Column(name = "max_retry_count")
    private Integer maxRetryCount;
    
    /**
     * 下次重试时间
     */
    @Column(name = "next_retry_time")
    private LocalDateTime nextRetryTime;
    
    /**
     * 任务参数（JSON格式）
     */
    @Column(name = "task_parameters", columnDefinition = "TEXT")
    private String taskParameters;

    /**
     * 执行结果（JSON格式）
     */
    @Column(name = "execution_result", columnDefinition = "TEXT")
    private String executionResult;
    
    /**
     * 执行节点信息
     */
    @Column(name = "execution_node", length = 100)
    private String executionNode;
    
    /**
     * 任务执行状态枚举
     */
    public enum ExecutionStatus {
        /**
         * 等待执行
         */
        PENDING,
        
        /**
         * 正在执行
         */
        RUNNING,
        
        /**
         * 执行成功
         */
        SUCCESS,
        
        /**
         * 执行失败
         */
        FAILED,
        
        /**
         * 等待重试
         */
        RETRY_PENDING,
        
        /**
         * 重试中
         */
        RETRYING,
        
        /**
         * 重试失败（达到最大重试次数）
         */
        RETRY_EXHAUSTED,
        
        /**
         * 任务被取消
         */
        CANCELLED,
        
        /**
         * 任务超时
         */
        TIMEOUT
    }
    
    /**
     * 计算执行耗时
     */
    public void calculateDuration() {
        if (executionTime != null && completionTime != null) {
            this.durationMs = java.time.Duration.between(executionTime, completionTime).toMillis();
        }
    }
    
    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return retryCount != null && maxRetryCount != null && retryCount < maxRetryCount;
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (retryCount == null) {
            retryCount = 0;
        }
        retryCount++;
    }
    
    /**
     * 检查是否为最终状态
     */
    public boolean isFinalStatus() {
        return status == ExecutionStatus.SUCCESS ||
               status == ExecutionStatus.FAILED ||
               status == ExecutionStatus.CANCELLED;
    }

    /**
     * 标记任务开始执行
     */
    public void markAsStarted() {
        this.status = ExecutionStatus.RUNNING;
        this.executionTime = LocalDateTime.now();
    }

    /**
     * 标记任务执行成功
     */
    public void markAsSuccess() {
        this.status = ExecutionStatus.SUCCESS;
        this.completionTime = LocalDateTime.now();
        calculateDuration();
    }

    /**
     * 标记任务执行失败
     */
    public void markAsFailed(String errorMessage, String errorStack) {
        this.status = ExecutionStatus.FAILED;
        this.completionTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
        this.errorStack = errorStack;
        calculateDuration();
    }

    /**
     * 标记任务重试
     */
    public void markAsRetrying(LocalDateTime nextRetryTime) {
        this.status = ExecutionStatus.RETRYING;
        incrementRetryCount();
        this.nextRetryTime = nextRetryTime;
    }
}
