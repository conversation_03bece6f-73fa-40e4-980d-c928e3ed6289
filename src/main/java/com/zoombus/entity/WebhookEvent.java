package com.zoombus.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Entity
@Table(name = "t_webhook_events")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WebhookEvent {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "事件类型不能为空")
    @Column(name = "event_type", nullable = false)
    private String eventType;
    
    @Column(name = "zoom_account_id")
    private String zoomAccountId;

    @Column(name = "zoom_meeting_id")
    private String zoomMeetingId;
    
    @Column(name = "event_data", columnDefinition = "TEXT")
    private String eventData;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "processing_status", nullable = false)
    private ProcessingStatus processingStatus = ProcessingStatus.PENDING;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    @Column(name = "processed_at")
    private LocalDateTime processedAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    public enum ProcessingStatus {
        PENDING, PROCESSED, FAILED, IGNORED
    }


}
