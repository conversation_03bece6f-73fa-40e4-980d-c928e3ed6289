package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Zoom API调用日志实体类
 */
@Entity
@Table(name = "t_zoom_api_logs",
       indexes = {
           @Index(name = "idx_request_id", columnList = "request_id"),
           @Index(name = "idx_api_path", columnList = "api_path"),
           @Index(name = "idx_request_time", columnList = "request_time"),
           @Index(name = "idx_business_type", columnList = "business_type"),
           @Index(name = "idx_business_id", columnList = "business_id"),
           @Index(name = "idx_zoom_user_id", columnList = "zoom_user_id"),
           @Index(name = "idx_is_success", columnList = "is_success"),
           @Index(name = "idx_response_status", columnList = "response_status"),
           @Index(name = "idx_duration", columnList = "duration_ms"),
           @Index(name = "idx_created_at", columnList = "created_at")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZoomApiLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    // 请求基本信息
    @NotBlank(message = "请求ID不能为空")
    @Column(name = "request_id", nullable = false, length = 64)
    private String requestId;
    
    @NotBlank(message = "API方法不能为空")
    @Column(name = "api_method", nullable = false, length = 10)
    private String apiMethod;
    
    @NotBlank(message = "API路径不能为空")
    @Column(name = "api_path", nullable = false, length = 500)
    private String apiPath;
    
    @NotBlank(message = "API URL不能为空")
    @Column(name = "api_url", nullable = false, length = 1000)
    private String apiUrl;
    
    // 请求内容
    @Column(name = "request_headers", columnDefinition = "TEXT")
    private String requestHeaders;
    
    @Column(name = "request_body", columnDefinition = "LONGTEXT")
    private String requestBody;
    
    // 响应内容
    @Column(name = "response_status")
    private Integer responseStatus;
    
    @Column(name = "response_headers", columnDefinition = "TEXT")
    private String responseHeaders;
    
    @Column(name = "response_body", columnDefinition = "LONGTEXT")
    private String responseBody;
    
    // 时间信息
    @NotNull(message = "请求时间不能为空")
    @Column(name = "request_time", nullable = false, columnDefinition = "DATETIME(3)")
    private LocalDateTime requestTime;
    
    @Column(name = "response_time", columnDefinition = "DATETIME(3)")
    private LocalDateTime responseTime;
    
    @Column(name = "duration_ms")
    private Long durationMs;
    
    // 结果信息
    @Column(name = "is_success")
    private Boolean isSuccess = false;
    
    @Column(name = "error_code", length = 50)
    private String errorCode;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    // 业务信息
    @Column(name = "business_type", length = 50)
    private String businessType;
    
    @Column(name = "business_id", length = 100)
    private String businessId;
    
    @Column(name = "zoom_user_id", length = 100)
    private String zoomUserId;

    @Column(name = "zoom_user_email", length = 255)
    private String zoomUserEmail;

    // 系统信息
    @Column(name = "client_ip", length = 45)
    private String clientIp;
    
    @Column(name = "user_agent", length = 500)
    private String userAgent;
    
    // 时间戳
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 业务类型枚举
     */
    public enum BusinessType {
        USER_INFO("USER_INFO", "获取用户信息"),
        UPDATE_PMI("UPDATE_PMI", "更新PMI设置"),
        CREATE_MEETING("CREATE_MEETING", "创建会议"),
        UPDATE_MEETING("UPDATE_MEETING", "更新会议"),
        DELETE_MEETING("DELETE_MEETING", "删除会议"),
        GET_MEETING("GET_MEETING", "获取会议信息"),
        CREATE_USER("CREATE_USER", "创建用户"),
        GET_USERS("GET_USERS", "获取用户列表"),
        GET_USER_ZAK("GET_USER_ZAK", "获取用户ZAK"),
        WEBHOOK("WEBHOOK", "Webhook回调"),
        OTHER("OTHER", "其他");
        
        private final String value;
        private final String description;
        
        BusinessType(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static BusinessType fromValue(String value) {
            for (BusinessType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            return OTHER;
        }
    }
    
    /**
     * 计算请求耗时
     */
    public void calculateDuration() {
        if (requestTime != null && responseTime != null) {
            this.durationMs = java.time.Duration.between(requestTime, responseTime).toMillis();
        }
    }
    
    /**
     * 检查是否成功
     */
    public boolean isSuccessful() {
        return Boolean.TRUE.equals(isSuccess) && responseStatus != null && responseStatus >= 200 && responseStatus < 300;
    }
    
    /**
     * 检查是否超时（超过30秒）
     */
    public boolean isTimeout() {
        return durationMs != null && durationMs > 30000;
    }
    
    /**
     * 检查是否慢请求（超过5秒）
     */
    public boolean isSlowRequest() {
        return durationMs != null && durationMs > 5000;
    }
}
