package com.zoombus.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Email;
import java.time.LocalDateTime;

@Entity
@Table(name = "t_zoom_auth")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZoomAuth {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "账号名称不能为空")
    @Column(name = "account_name", nullable = false, unique = true)
    private String accountName;
    
    @NotBlank(message = "Zoom账号ID不能为空")
    @Column(name = "zoom_account_id", nullable = false)
    private String zoomAccountId;

    @Email(message = "主账号邮箱格式不正确")
    @NotBlank(message = "主账号邮箱不能为空")
    @Column(name = "primary_email", nullable = false)
    private String primaryEmail;

    @NotBlank(message = "客户端ID不能为空")
    @Column(name = "client_id", nullable = false)
    private String clientId;
    
    @NotBlank(message = "客户端密钥不能为空")
    @Column(name = "client_secret", nullable = false)
    @JsonIgnore
    private String clientSecret;

    @Column(name = "access_token", columnDefinition = "TEXT")
    @JsonIgnore
    private String accessToken;

    @Column(name = "refresh_token", columnDefinition = "TEXT")
    @JsonIgnore
    private String refreshToken;
    
    @Column(name = "token_type")
    private String tokenType = "Bearer";
    
    @Column(name = "expires_in")
    private Integer expiresIn;
    
    @Column(name = "token_issued_at")
    private LocalDateTime tokenIssuedAt;
    
    @Column(name = "token_expires_at")
    private LocalDateTime tokenExpiresAt;
    
    @Lob
    @Column(name = "scope")
    private String scope;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "auth_type", nullable = false)
    private AuthType authType = AuthType.OAUTH2;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AuthStatus status = AuthStatus.ACTIVE;
    
    @Column(name = "last_refresh_at")
    private LocalDateTime lastRefreshAt;
    
    @Column(name = "refresh_count")
    private Integer refreshCount = 0;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    @Column(name = "webhook_secret_token")
    @JsonIgnore
    private String webhookSecretToken;
    
    @Column(name = "api_base_url")
    private String apiBaseUrl = "https://api.zoom.us/v2";
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查token是否过期
     */
    public boolean isTokenExpired() {
        if (tokenExpiresAt == null) {
            return true;
        }
        return LocalDateTime.now().isAfter(tokenExpiresAt);
    }
    
    /**
     * 检查token是否即将过期（10分钟内）
     */
    public boolean isTokenExpiringSoon() {
        if (tokenExpiresAt == null) {
            return true;
        }
        return LocalDateTime.now().plusMinutes(10).isAfter(tokenExpiresAt);
    }
    
    /**
     * 更新token信息
     */
    public void updateTokenInfo(String accessToken, String refreshToken, Integer expiresIn) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiresIn = expiresIn;
        this.tokenIssuedAt = LocalDateTime.now();
        this.tokenExpiresAt = LocalDateTime.now().plusSeconds(expiresIn);
        this.lastRefreshAt = LocalDateTime.now();
        this.refreshCount = (this.refreshCount == null ? 0 : this.refreshCount) + 1;
        this.errorMessage = null;
        this.status = AuthStatus.ACTIVE;
    }
    
    /**
     * 标记token刷新失败
     */
    public void markRefreshFailed(String errorMessage) {
        this.errorMessage = errorMessage;
        this.status = AuthStatus.ERROR;
    }
    
    public enum AuthType {
        OAUTH("OAuth"),  // 向后兼容旧数据
        OAUTH2("OAuth2.0"),
        JWT("JWT"),
        SERVER_TO_SERVER("Server-to-Server OAuth");

        private final String description;

        AuthType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
    
    public enum AuthStatus {
        ACTIVE("正常"),
        EXPIRED("已过期"),
        ERROR("错误"),
        DISABLED("已禁用");
        
        private final String description;
        
        AuthStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
