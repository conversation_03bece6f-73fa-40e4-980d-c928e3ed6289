package com.zoombus.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Zoom会议实体类
 */
@Entity
@Table(name = "t_zoom_meetings",
       indexes = {
           @Index(name = "idx_pmi_status_active",
                  columnList = "zoom_meeting_id, status",
                  unique = false) // 不设为unique，因为同一PMI可以有多个历史记录
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZoomMeeting implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "pmi_record_id", nullable = true)
    private Long pmiRecordId;
    
    @NotBlank(message = "Zoom会议UUID不能为空")
    @Column(name = "zoom_meeting_uuid", nullable = false, unique = true)
    private String zoomMeetingUuid;
    
    @NotBlank(message = "Zoom会议ID不能为空")
    @Column(name = "zoom_meeting_id", nullable = false)
    private String zoomMeetingId;
    
    @Column(name = "topic")
    private String topic;

    @Enumerated(EnumType.STRING)
    @Column(name = "creation_source")
    private CreationSource creationSource = CreationSource.UNKNOWN;

    @Column(name = "host_id")
    private String hostId;

    @Column(name = "start_url", columnDefinition = "TEXT")
    private String startUrl;

    @Column(name = "join_url", columnDefinition = "TEXT")
    private String joinUrl;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    @Column(name = "duration_minutes")
    private Integer durationMinutes = 0;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 50)
    private MeetingStatus status = MeetingStatus.WAITING;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "billing_mode")
    private PmiRecord.BillingMode billingMode = PmiRecord.BillingMode.FREE;
    
    @Column(name = "billed_minutes")
    private Integer billedMinutes = 0;
    
    @Column(name = "is_settled")
    private Boolean isSettled = false;
    
    // ZoomUser分配信息
    @Column(name = "assigned_zoom_user_id")
    private Long assignedZoomUserId;

    @Column(name = "assigned_zoom_user_email")
    private String assignedZoomUserEmail;

    // ZoomAuth账号信息
    @Column(name = "zoom_auth_account_name")
    private String zoomAuthAccountName;

    @Column(name = "assignment_error", columnDefinition = "TEXT")
    private String assignmentError;

    // 用户姓名（非持久化字段，用于前端显示）
    @Transient
    private String assignedUserFullName;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 会议状态枚举 - 基于Zoom API官方状态设计
     */
    public enum MeetingStatus {
        // === 本地创建阶段 ===
        CREATING("CREATING", "创建中", false, true),
        CREATE_FAILED("CREATE_FAILED", "创建失败", false, true),

        // === Zoom官方状态 ===
        WAITING("WAITING", "等待开始", true, false),    // 对应Zoom的waiting状态
        STARTED("STARTED", "进行中", true, false),      // 对应Zoom的started状态
        ENDED("ENDED", "已结束", true, false),          // 对应Zoom的ended状态

        // === 本地业务状态 ===
        SETTLING("SETTLING", "结算中", false, true),
        SETTLED("SETTLED", "已结算", false, true),

        // === 异常状态 ===
        ERROR("ERROR", "异常状态", false, true),
        DELETED("DELETED", "已删除", false, true);

        private final String value;
        private final String description;
        private final boolean isZoomStatus;      // 是否为Zoom官方状态
        private final boolean isLocalStatus;     // 是否为本地业务状态

        MeetingStatus(String value, String description, boolean isZoomStatus, boolean isLocalStatus) {
            this.value = value;
            this.description = description;
            this.isZoomStatus = isZoomStatus;
            this.isLocalStatus = isLocalStatus;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public boolean isZoomStatus() {
            return isZoomStatus;
        }

        public boolean isLocalStatus() {
            return isLocalStatus;
        }

        /**
         * 从Zoom API状态映射到本地状态
         */
        public static MeetingStatus fromZoomStatus(String zoomStatus) {
            switch (zoomStatus.toLowerCase()) {
                case "waiting":
                    return WAITING;
                case "started":
                    return STARTED;
                case "ended":
                    return ENDED;
                default:
                    return ERROR;
            }
        }

        /**
         * 转换为Zoom API状态
         */
        public String toZoomStatus() {
            switch (this) {
                case WAITING:
                    return "waiting";
                case STARTED:
                    return "started";
                case ENDED:
                    return "ended";
                default:
                    return null; // 本地状态无对应的Zoom状态
            }
        }

        public static MeetingStatus fromValue(String value) {
            for (MeetingStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return WAITING; // 默认为等待状态
        }

        /**
         * 判断是否为活跃状态（需要同步的状态）
         */
        public boolean isActive() {
            return this == WAITING || this == STARTED;
        }

        /**
         * 判断是否为终态
         */
        public boolean isFinalState() {
            return this == ENDED || this == SETTLED || this == DELETED || this == CREATE_FAILED;
        }
    }
    
    /**
     * 计算当前会议时长（分钟）
     */
    public int getCurrentDurationMinutes() {
        if (startTime == null) {
            return 0;
        }
        
        LocalDateTime endTimeToUse = endTime != null ? endTime : LocalDateTime.now();
        return (int) java.time.Duration.between(startTime, endTimeToUse).toMinutes();
    }
    
    /**
     * 检查会议是否正在进行中
     */
    public boolean isInProgress() {
        return status == MeetingStatus.STARTED;
    }
    
    /**
     * 检查会议是否已结束
     */
    public boolean isEnded() {
        return status == MeetingStatus.ENDED || status == MeetingStatus.SETTLED;
    }
    
    /**
     * 检查会议是否已结算
     */
    public boolean isSettledStatus() {
        return status == MeetingStatus.SETTLED;
    }

    /**
     * 会议来源枚举
     */
    public enum CreationSource {
        PMI_MEETING("PMI会议", "PMI会议"),
        ADMIN_PANEL("管理台", "管理台创建"),
        ZOOM_CLIENT("Zoom端", "Zoom客户端创建"),
        UNKNOWN("未知", "未知来源");

        private final String value;
        private final String description;

        CreationSource(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }
}
