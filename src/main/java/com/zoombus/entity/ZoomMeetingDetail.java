package com.zoombus.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = "t_zoom_meeting_details")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZoomMeetingDetail {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    // 关联的会议记录
    @NotNull(message = "会议ID不能为空")
    @Column(name = "meeting_id", nullable = false)
    private Long meetingId;
    
    // Zoom API 返回的原始会议ID
    @NotBlank(message = "Zoom会议ID不能为空")
    @Column(name = "zoom_meeting_id", nullable = false)
    private String zoomMeetingId;
    
    // Zoom API 返回的UUID
    @Column(name = "uuid")
    private String uuid;
    
    // 会议主题 - 已迁移到 t_meetings.topic，将在后续版本中移除
    @Deprecated
    @Column(name = "topic")
    private String topic;

    // 会议类型 - 已迁移到 t_meetings.type，将在后续版本中移除
    @Deprecated
    @Column(name = "type")
    private Integer type;
    
    // 会议状态
    @Column(name = "status")
    private String status;
    
    // 开始时间
    @Column(name = "start_time")
    private LocalDateTime startTime;
    
    // 持续时间（分钟）- 已迁移到 t_meetings.duration_minutes，将在后续版本中移除
    @Deprecated
    @Column(name = "duration")
    private Integer duration;

    // 时区 - 已迁移到 t_meetings.timezone，将在后续版本中移除
    @Deprecated
    @Column(name = "timezone")
    private String timezone;

    // 会议议程 - 已迁移到 t_meetings.agenda，将在后续版本中移除
    @Deprecated
    @Column(name = "agenda", columnDefinition = "TEXT")
    private String agenda;
    
    // 创建时间
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    // 主持人ID
    @Column(name = "host_id")
    private String hostId;
    
    // 主持人邮箱
    @Column(name = "host_email")
    private String hostEmail;
    
    // 加入URL
    @Column(name = "join_url", columnDefinition = "TEXT")
    private String joinUrl;
    
    // 主持人开始URL
    @Column(name = "start_url", columnDefinition = "TEXT")
    private String startUrl;
    
    // 会议密码 - 已迁移到 t_meetings.meeting_password，将在后续版本中移除
    @Deprecated
    @Column(name = "password")
    private String password;
    
    // 个人会议室ID
    @Column(name = "pmi")
    private String pmi;
    
    // 跟踪字段
    @Column(name = "tracking_fields", columnDefinition = "TEXT")
    private String trackingFields;
    
    // 会议设置（JSON格式）
    @Column(name = "settings", columnDefinition = "TEXT")
    private String settings;
    
    // 录制设置（JSON格式）- 已迁移到 t_meetings 的周期性字段，将在后续版本中移除
    @Deprecated
    @Column(name = "recurrence", columnDefinition = "TEXT")
    private String recurrence;
    
    // 是否已加密
    @Column(name = "encrypted")
    private Boolean encrypted;
    
    // 等候室设置
    @Column(name = "waiting_room")
    private Boolean waitingRoom;
    
    // 入会时静音
    @Column(name = "mute_upon_entry")
    private Boolean muteUponEntry;
    
    // 允许主持人之前加入
    @Column(name = "join_before_host")
    private Boolean joinBeforeHost;
    
    // 自动录制
    @Column(name = "auto_recording")
    private String autoRecording;
    
    // 替代主持人
    @Column(name = "alternative_hosts")
    private String alternativeHosts;
    
    // 创建时间戳
    @Column(name = "zoom_created_at")
    private LocalDateTime zoomCreatedAt;
    
    // 本地记录创建时间
    @Column(name = "local_created_at", nullable = false)
    private LocalDateTime localCreatedAt;
    
    // 本地记录更新时间
    @Column(name = "local_updated_at")
    private LocalDateTime localUpdatedAt;

    // 周期性会议相关字段
    // occurrence ID，用于标识周期性会议中的具体某一场会议
    @Column(name = "occurrence_id")
    private String occurrenceId;

    // 是否为主会议记录（周期性会议的主记录）
    @Column(name = "is_main_occurrence")
    private Boolean isMainOccurrence = false;

    // 会议实例的具体开始时间（对于周期性会议，每个occurrence有不同的开始时间）
    @Column(name = "occurrence_start_time")
    private LocalDateTime occurrenceStartTime;

    // 会议实例状态（available, deleted等）
    @Column(name = "occurrence_status")
    private String occurrenceStatus;

    @PrePersist
    protected void onCreate() {
        localCreatedAt = LocalDateTime.now();
        localUpdatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        localUpdatedAt = LocalDateTime.now();
    }
}
