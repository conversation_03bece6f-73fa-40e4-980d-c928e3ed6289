package com.zoombus.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Entity
@Table(name = "t_zoom_accounts")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZoomUser {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "zoom_auth_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private ZoomAuth zoomAuth;
                             
    @ManyToOne
    @JoinColumn(name = "user_id", nullable = true)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private User user;
    
    @NotBlank(message = "Zoom用户ID不能为空")
    @Column(name = "zoom_user_id", nullable = false)
    private String zoomUserId;
    
    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "邮箱不能为空")
    @Column(name = "email", nullable = false)
    private String email;
    
    @Column(name = "first_name")
    private String firstName;
    
    @Column(name = "last_name")
    private String lastName;
    
    @Column(name = "display_name")
    private String displayName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "user_type")
    private UserType userType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private UserStatus status;
    
    @Column(name = "department")
    private String department;
    
    @Column(name = "job_title")
    private String jobTitle;
    
    @Column(name = "phone_number")
    private String phoneNumber;
    
    @Column(name = "timezone")
    private String timezone;
    
    @Column(name = "language")
    private String language;

    @Enumerated(EnumType.STRING)
    @Column(name = "account_usage")
    private AccountUsage accountUsage;

    @Column(name = "zoom_created_at")
    private LocalDateTime zoomCreatedAt;

    @Column(name = "zoom_last_login_time")
    private LocalDateTime zoomLastLoginTime;

    // ========== PMI相关字段 ==========

    @Column(name = "original_pmi")
    private String originalPmi;

    @Column(name = "current_pmi")
    private String currentPmi;

    @Enumerated(EnumType.STRING)
    @Column(name = "usage_status")
    private UsageStatus usageStatus = UsageStatus.AVAILABLE;

    @Column(name = "current_meeting_id")
    private Long currentMeetingId;

    @Column(name = "last_used_time")
    private LocalDateTime lastUsedTime;

    @Column(name = "total_usage_count")
    private Integer totalUsageCount = 0;

    @Column(name = "total_usage_minutes")
    private Integer totalUsageMinutes = 0;

    @Column(name = "pmi_updated_at")
    private LocalDateTime pmiUpdatedAt;

    // ========== 主持人相关字段 ==========

    @Column(name = "host_key", length = 50)
    private String hostKey;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum UserType {
        BASIC(1, "基础版"),
        LICENSED(2, "专业版"),
        ON_PREM(3, "本地部署版");
        
        private final int value;
        private final String description;
        
        UserType(int value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public int getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static UserType fromValue(int value) {
            for (UserType type : values()) {
                if (type.value == value) {
                    return type;
                }
            }
            return BASIC;
        }
    }
    
    public enum UserStatus {
        ACTIVE("active", "活跃"),
        INACTIVE("inactive", "非活跃"),
        PENDING("pending", "待激活");
        
        private final String value;
        private final String description;
        
        UserStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static UserStatus fromValue(String value) {
            for (UserStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return ACTIVE;
        }
    }

    public enum AccountUsage {
        PUBLIC_MEETING("public_meeting", "参会账号-公共"),
        PRIVATE_MEETING("private_meeting", "参会账号-专属"),
        PUBLIC_HOST("public_host", "开会账号-公共"),
        PRIVATE_HOST("private_host", "开会账号-专属"),
        JOIN_ACCOUNT_RENTAL("join_account_rental", "参会账号短租");

        private final String value;
        private final String description;

        AccountUsage(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static AccountUsage fromValue(String value) {
            if (value == null) {
                return null;
            }
            for (AccountUsage usage : values()) {
                if (usage.value.equals(value)) {
                    return usage;
                }
            }
            return null;
        }
    }

    /**
     * 使用状态枚举
     */
    public enum UsageStatus {
        AVAILABLE("AVAILABLE", "可用"),
        IN_USE("IN_USE", "使用中"),
        MAINTENANCE("MAINTENANCE", "维护中");

        private final String value;
        private final String description;

        UsageStatus(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static UsageStatus fromValue(String value) {
            if (value == null) {
                return AVAILABLE;
            }
            for (UsageStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return AVAILABLE;
        }
    }

    /**
     * 检查是否可用
     */
    public boolean isAvailable() {
        return usageStatus == UsageStatus.AVAILABLE;
    }

    /**
     * 检查是否正在使用中
     */
    public boolean isInUse() {
        return usageStatus == UsageStatus.IN_USE;
    }

    /**
     * 检查是否在维护中
     */
    public boolean isInMaintenance() {
        return usageStatus == UsageStatus.MAINTENANCE;
    }

    /**
     * 更新使用统计
     */
    public void updateUsageStats(int additionalMinutes) {
        if (totalUsageCount == null) {
            totalUsageCount = 0;
        }
        if (totalUsageMinutes == null) {
            totalUsageMinutes = 0;
        }

        totalUsageCount++;
        totalUsageMinutes += additionalMinutes;
        lastUsedTime = LocalDateTime.now();
    }
}
