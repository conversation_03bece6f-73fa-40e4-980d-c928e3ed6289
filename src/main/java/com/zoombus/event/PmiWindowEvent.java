package com.zoombus.event;

import com.zoombus.entity.PmiScheduleWindow;
import org.springframework.context.ApplicationEvent;

/**
 * PMI窗口事件基类
 */
public abstract class PmiWindowEvent extends ApplicationEvent {
    
    private final PmiScheduleWindow window;
    private final EventType eventType;
    
    public PmiWindowEvent(Object source, PmiScheduleWindow window, EventType eventType) {
        super(source);
        this.window = window;
        this.eventType = eventType;
    }
    
    public PmiScheduleWindow getWindow() {
        return window;
    }
    
    public EventType getEventType() {
        return eventType;
    }
    
    /**
     * 事件类型枚举
     */
    public enum EventType {
        CREATED("窗口创建"),
        UPDATED("窗口更新"),
        DELETED("窗口删除"),
        STATUS_CHANGED("状态变更");
        
        private final String description;
        
        EventType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}


