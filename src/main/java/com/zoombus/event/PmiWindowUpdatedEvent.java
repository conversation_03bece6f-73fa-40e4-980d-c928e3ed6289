package com.zoombus.event;

import com.zoombus.entity.PmiScheduleWindow;

/**
 * PMI窗口更新事件
 */
public class PmiWindowUpdatedEvent extends PmiWindowEvent {
    
    private final PmiScheduleWindow oldWindow;
    
    public PmiWindowUpdatedEvent(Object source, PmiScheduleWindow newWindow, PmiScheduleWindow oldWindow) {
        super(source, newWindow, EventType.UPDATED);
        this.oldWindow = oldWindow;
    }
    
    public PmiScheduleWindow getOldWindow() {
        return oldWindow;
    }
}
