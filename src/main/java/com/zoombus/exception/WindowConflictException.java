package com.zoombus.exception;

import com.zoombus.entity.PmiScheduleWindow;
import java.util.List;
import java.util.Set;

/**
 * 窗口冲突异常
 */
public class WindowConflictException extends RuntimeException {

    private final List<PmiScheduleWindow> newWindows;
    private final List<PmiScheduleWindow> conflictWindows;
    private final Set<Integer> conflictNewWindowIndexes;

    public WindowConflictException(String message, List<PmiScheduleWindow> newWindows, List<PmiScheduleWindow> conflictWindows) {
        super(message);
        this.newWindows = newWindows;
        this.conflictWindows = conflictWindows;
        this.conflictNewWindowIndexes = null;
    }

    public WindowConflictException(String message, List<PmiScheduleWindow> newWindows, List<PmiScheduleWindow> conflictWindows, Set<Integer> conflictNewWindowIndexes) {
        super(message);
        this.newWindows = newWindows;
        this.conflictWindows = conflictWindows;
        this.conflictNewWindowIndexes = conflictNewWindowIndexes;
    }

    public List<PmiScheduleWindow> getNewWindows() {
        return newWindows;
    }

    public List<PmiScheduleWindow> getConflictWindows() {
        return conflictWindows;
    }

    public Set<Integer> getConflictNewWindowIndexes() {
        return conflictNewWindowIndexes;
    }
}
