package com.zoombus.repository;

import com.zoombus.entity.AdminUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AdminUserRepository extends JpaRepository<AdminUser, Long> {
    
    /**
     * 根据用户名查找管理员用户
     */
    Optional<AdminUser> findByUsername(String username);
    
    /**
     * 根据邮箱查找管理员用户
     */
    Optional<AdminUser> findByEmail(String email);
    
    /**
     * 根据状态查找管理员用户
     */
    List<AdminUser> findByStatus(AdminUser.AdminStatus status);
    
    /**
     * 根据角色查找管理员用户
     */
    List<AdminUser> findByRole(AdminUser.AdminRole role);
    
    /**
     * 根据状态分页查找管理员用户
     */
    Page<AdminUser> findByStatus(AdminUser.AdminStatus status, Pageable pageable);
    
    /**
     * 根据角色分页查找管理员用户
     */
    Page<AdminUser> findByRole(AdminUser.AdminRole role, Pageable pageable);
    
    /**
     * 搜索管理员用户（根据用户名或姓名）
     */
    @Query("SELECT a FROM AdminUser a WHERE a.username LIKE %:keyword% OR a.fullName LIKE %:keyword%")
    List<AdminUser> searchByKeyword(@Param("keyword") String keyword);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);
}
