package com.zoombus.repository;

import com.zoombus.entity.JoinAccountPasswordLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Join Account密码变更日志Repository接口
 */
@Repository
public interface JoinAccountPasswordLogRepository extends JpaRepository<JoinAccountPasswordLog, Long> {
    
    /**
     * 根据Zoom账号ID查找日志
     */
    List<JoinAccountPasswordLog> findByZoomUserId(Long zoomUserId);
    
    /**
     * 根据Zoom账号ID分页查找日志
     */
    Page<JoinAccountPasswordLog> findByZoomUserId(Long zoomUserId, Pageable pageable);
    
    /**
     * 根据变更类型查找日志
     */
    List<JoinAccountPasswordLog> findByChangeType(JoinAccountPasswordLog.ChangeType changeType);
    
    /**
     * 根据变更类型分页查找日志
     */
    Page<JoinAccountPasswordLog> findByChangeType(JoinAccountPasswordLog.ChangeType changeType, Pageable pageable);
    
    /**
     * 根据窗口ID查找日志
     */
    List<JoinAccountPasswordLog> findByWindowId(Long windowId);
    
    /**
     * 根据操作人查找日志
     */
    List<JoinAccountPasswordLog> findByCreatedBy(String createdBy);
    
    /**
     * 根据操作人分页查找日志
     */
    Page<JoinAccountPasswordLog> findByCreatedBy(String createdBy, Pageable pageable);
    
    /**
     * 根据创建时间范围查找日志
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE l.createdAt BETWEEN :startTime AND :endTime ORDER BY l.createdAt DESC")
    List<JoinAccountPasswordLog> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据创建时间范围分页查找日志
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE l.createdAt BETWEEN :startTime AND :endTime ORDER BY l.createdAt DESC")
    Page<JoinAccountPasswordLog> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, Pageable pageable);
    
    /**
     * 多条件查询
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE " +
           "(:zoomUserId IS NULL OR l.zoomUserId = :zoomUserId) AND " +
           "(:changeType IS NULL OR l.changeType = :changeType) AND " +
           "(:windowId IS NULL OR l.windowId = :windowId) AND " +
           "(:createdBy IS NULL OR l.createdBy = :createdBy) AND " +
           "(:startTime IS NULL OR l.createdAt >= :startTime) AND " +
           "(:endTime IS NULL OR l.createdAt <= :endTime) " +
           "ORDER BY l.createdAt DESC")
    Page<JoinAccountPasswordLog> findByConditions(
            @Param("zoomUserId") Long zoomUserId,
            @Param("changeType") JoinAccountPasswordLog.ChangeType changeType,
            @Param("windowId") Long windowId,
            @Param("createdBy") String createdBy,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable);
    
    /**
     * 查找账号的最新密码变更记录
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE l.zoomUserId = :zoomUserId ORDER BY l.createdAt DESC")
    List<JoinAccountPasswordLog> findByZoomUserIdOrderByCreatedAtDesc(@Param("zoomUserId") Long zoomUserId);

    /**
     * 查找账号的最新密码变更记录
     */
    default Optional<JoinAccountPasswordLog> findLatestByZoomUserId(Long zoomUserId) {
        List<JoinAccountPasswordLog> logs = findByZoomUserIdOrderByCreatedAtDesc(zoomUserId);
        return logs.isEmpty() ? Optional.empty() : Optional.of(logs.get(0));
    }
    
    /**
     * 查找账号在指定时间之后的密码变更记录
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE l.zoomUserId = :zoomUserId AND l.createdAt > :afterTime ORDER BY l.createdAt DESC")
    List<JoinAccountPasswordLog> findByZoomUserIdAfterTime(@Param("zoomUserId") Long zoomUserId, @Param("afterTime") LocalDateTime afterTime);
    
    /**
     * 统计各变更类型的数量
     */
    @Query("SELECT l.changeType, COUNT(l) FROM JoinAccountPasswordLog l GROUP BY l.changeType")
    List<Object[]> countByChangeType();
    
    /**
     * 统计各操作人的变更数量
     */
    @Query("SELECT l.createdBy, COUNT(l) FROM JoinAccountPasswordLog l GROUP BY l.createdBy ORDER BY COUNT(l) DESC")
    List<Object[]> countByCreatedBy();
    
    /**
     * 统计各Zoom账号的变更数量
     */
    @Query("SELECT l.zoomUserId, COUNT(l) FROM JoinAccountPasswordLog l GROUP BY l.zoomUserId ORDER BY COUNT(l) DESC")
    List<Object[]> countByZoomUserId();
    
    /**
     * 查找系统自动变更的日志
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE l.changeType IN ('WINDOW_OPEN', 'WINDOW_CLOSE') ORDER BY l.createdAt DESC")
    List<JoinAccountPasswordLog> findSystemChanges();
    
    /**
     * 查找手动变更的日志
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE l.changeType = 'MANUAL' ORDER BY l.createdAt DESC")
    List<JoinAccountPasswordLog> findManualChanges();
    
    /**
     * 查找有窗口关联的日志
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE l.windowId IS NOT NULL ORDER BY l.createdAt DESC")
    List<JoinAccountPasswordLog> findWithWindow();
    
    /**
     * 查找无窗口关联的日志
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE l.windowId IS NULL ORDER BY l.createdAt DESC")
    List<JoinAccountPasswordLog> findWithoutWindow();
    
    /**
     * 查找最近的密码变更日志
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l ORDER BY l.createdAt DESC")
    Page<JoinAccountPasswordLog> findRecentChanges(Pageable pageable);
    
    /**
     * 查找指定日期的密码变更日志
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE DATE(l.createdAt) = DATE(:date) ORDER BY l.createdAt DESC")
    List<JoinAccountPasswordLog> findByDate(@Param("date") LocalDateTime date);
    
    /**
     * 统计指定时间范围内的变更次数
     */
    @Query("SELECT COUNT(l) FROM JoinAccountPasswordLog l WHERE l.createdAt BETWEEN :startTime AND :endTime")
    long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计账号在指定时间范围内的变更次数
     */
    @Query("SELECT COUNT(l) FROM JoinAccountPasswordLog l WHERE l.zoomUserId = :zoomUserId AND l.createdAt BETWEEN :startTime AND :endTime")
    long countByZoomUserIdAndTimeRange(
            @Param("zoomUserId") Long zoomUserId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找频繁变更密码的账号
     */
    @Query("SELECT l.zoomUserId, COUNT(l) as changeCount FROM JoinAccountPasswordLog l WHERE l.createdAt >= :sinceTime GROUP BY l.zoomUserId HAVING COUNT(l) >= :minCount ORDER BY COUNT(l) DESC")
    List<Object[]> findFrequentlyChangedAccounts(@Param("sinceTime") LocalDateTime sinceTime, @Param("minCount") long minCount);
    
    /**
     * 查找窗口相关的密码变更日志
     */
    @Query("SELECT l FROM JoinAccountPasswordLog l WHERE l.windowId = :windowId ORDER BY l.createdAt")
    List<JoinAccountPasswordLog> findByWindowIdOrderByCreatedAt(@Param("windowId") Long windowId);
}
