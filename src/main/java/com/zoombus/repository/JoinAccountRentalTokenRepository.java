package com.zoombus.repository;

import com.zoombus.entity.JoinAccountRentalToken;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Join Account Rental令牌Repository接口
 */
@Repository
public interface JoinAccountRentalTokenRepository extends JpaRepository<JoinAccountRentalToken, Long> {
    
    /**
     * 根据Token编号查找
     */
    Optional<JoinAccountRentalToken> findByTokenNumber(String tokenNumber);
    
    /**
     * 根据批次号查找
     */
    List<JoinAccountRentalToken> findByBatchNumber(String batchNumber);
    
    /**
     * 根据批次号分页查找
     */
    Page<JoinAccountRentalToken> findByBatchNumber(String batchNumber, Pageable pageable);
    
    /**
     * 根据状态查找
     */
    List<JoinAccountRentalToken> findByStatus(JoinAccountRentalToken.TokenStatus status);
    
    /**
     * 根据状态分页查找
     */
    Page<JoinAccountRentalToken> findByStatus(JoinAccountRentalToken.TokenStatus status, Pageable pageable);
    
    /**
     * 根据导出状态查找
     */
    List<JoinAccountRentalToken> findByExportStatus(JoinAccountRentalToken.ExportStatus exportStatus);
    
    /**
     * 根据导出状态分页查找
     */
    Page<JoinAccountRentalToken> findByExportStatus(JoinAccountRentalToken.ExportStatus exportStatus, Pageable pageable);
    
    /**
     * 根据状态和导出状态查找
     */
    List<JoinAccountRentalToken> findByStatusAndExportStatus(
            JoinAccountRentalToken.TokenStatus status, 
            JoinAccountRentalToken.ExportStatus exportStatus);
    
    /**
     * 根据分配的Zoom账号ID查找
     */
    List<JoinAccountRentalToken> findByAssignedZoomUserId(Long zoomUserId);
    
    /**
     * 根据分配的Zoom账号邮箱查找
     */
    List<JoinAccountRentalToken> findByAssignedZoomUserEmail(String email);
    
    /**
     * 根据使用天数查找
     */
    List<JoinAccountRentalToken> findByUsageDays(Integer usageDays);
    
    /**
     * 根据创建时间范围查找
     */
    @Query("SELECT t FROM JoinAccountRentalToken t WHERE t.createdAt BETWEEN :startTime AND :endTime ORDER BY t.createdAt DESC")
    List<JoinAccountRentalToken> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据窗口时间范围查找
     */
    @Query("SELECT t FROM JoinAccountRentalToken t WHERE t.windowStartTime BETWEEN :startTime AND :endTime ORDER BY t.windowStartTime")
    List<JoinAccountRentalToken> findByWindowTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 多条件查询
     */
    @Query("SELECT t FROM JoinAccountRentalToken t WHERE " +
           "(:tokenNumber IS NULL OR t.tokenNumber LIKE %:tokenNumber%) AND " +
           "(:batchNumber IS NULL OR t.batchNumber = :batchNumber) AND " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:exportStatus IS NULL OR t.exportStatus = :exportStatus) AND " +
           "(:usageDays IS NULL OR t.usageDays = :usageDays) AND " +
           "(:startTime IS NULL OR t.createdAt >= :startTime) AND " +
           "(:endTime IS NULL OR t.createdAt <= :endTime) " +
           "ORDER BY t.createdAt DESC")
    Page<JoinAccountRentalToken> findByConditions(
            @Param("tokenNumber") String tokenNumber,
            @Param("batchNumber") String batchNumber,
            @Param("status") JoinAccountRentalToken.TokenStatus status,
            @Param("exportStatus") JoinAccountRentalToken.ExportStatus exportStatus,
            @Param("usageDays") Integer usageDays,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable);
    
    /**
     * 检查Token编号是否存在
     */
    boolean existsByTokenNumber(String tokenNumber);
    
    /**
     * 统计各状态的数量
     */
    @Query("SELECT t.status, COUNT(t) FROM JoinAccountRentalToken t GROUP BY t.status")
    List<Object[]> countByStatus();
    
    /**
     * 统计各导出状态的数量
     */
    @Query("SELECT t.exportStatus, COUNT(t) FROM JoinAccountRentalToken t GROUP BY t.exportStatus")
    List<Object[]> countByExportStatus();
    
    /**
     * 统计各批次的数量
     */
    @Query("SELECT t.batchNumber, COUNT(t) FROM JoinAccountRentalToken t GROUP BY t.batchNumber ORDER BY MAX(t.createdAt) DESC")
    List<Object[]> countByBatchNumber();
    
    /**
     * 查找可导出的令牌
     */
    @Query("SELECT t FROM JoinAccountRentalToken t WHERE t.status = 'PENDING' AND t.exportStatus = 'NOT_EXPORTED'")
    List<JoinAccountRentalToken> findExportableTokens();
    
    /**
     * 查找可预约的令牌
     */
    @Query("SELECT t FROM JoinAccountRentalToken t WHERE t.status IN ('PENDING', 'EXPORTED')")
    List<JoinAccountRentalToken> findReservableTokens();
    
    /**
     * 查找可作废的令牌
     */
    @Query("SELECT t FROM JoinAccountRentalToken t WHERE t.status IN ('PENDING', 'EXPORTED')")
    List<JoinAccountRentalToken> findCancellableTokens();
    
    /**
     * 查找需要开启窗口的令牌
     */
    @Query("SELECT t FROM JoinAccountRentalToken t WHERE t.status = 'RESERVED' AND t.windowStartTime <= :currentTime")
    List<JoinAccountRentalToken> findTokensToActivate(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 查找需要关闭窗口的令牌
     */
    @Query("SELECT t FROM JoinAccountRentalToken t WHERE t.status = 'ACTIVE' AND t.windowEndTime <= :currentTime")
    List<JoinAccountRentalToken> findTokensToComplete(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 根据ID列表查找
     */
    List<JoinAccountRentalToken> findByIdIn(List<Long> ids);
    
    /**
     * 根据Token编号列表查找
     */
    List<JoinAccountRentalToken> findByTokenNumberIn(List<String> tokenNumbers);
    
    /**
     * 查找最近创建的令牌
     */
    @Query("SELECT t FROM JoinAccountRentalToken t ORDER BY t.createdAt DESC")
    Page<JoinAccountRentalToken> findRecentlyCreated(Pageable pageable);
    
    /**
     * 查找即将到期的令牌
     */
    @Query("SELECT t FROM JoinAccountRentalToken t WHERE t.status = 'ACTIVE' AND t.windowEndTime BETWEEN :now AND :futureTime ORDER BY t.windowEndTime")
    List<JoinAccountRentalToken> findExpiringTokens(@Param("now") LocalDateTime now, @Param("futureTime") LocalDateTime futureTime);
}
