package com.zoombus.repository;

import com.zoombus.entity.JoinAccountUsageWindow;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Join Account使用窗口Repository接口
 */
@Repository
public interface JoinAccountUsageWindowRepository extends JpaRepository<JoinAccountUsageWindow, Long> {
    
    /**
     * 根据Zoom账号ID查找窗口
     */
    List<JoinAccountUsageWindow> findByZoomUserId(Long zoomUserId);
    
    /**
     * 根据Zoom账号ID分页查找窗口
     */
    Page<JoinAccountUsageWindow> findByZoomUserId(Long zoomUserId, Pageable pageable);
    
    /**
     * 根据Token编号查找窗口
     */
    List<JoinAccountUsageWindow> findByTokenNumber(String tokenNumber);
    
    /**
     * 根据Token编号查找唯一窗口
     */
    Optional<JoinAccountUsageWindow> findFirstByTokenNumber(String tokenNumber);
    
    /**
     * 根据状态查找窗口
     */
    List<JoinAccountUsageWindow> findByStatus(JoinAccountUsageWindow.WindowStatus status);
    
    /**
     * 根据状态分页查找窗口
     */
    Page<JoinAccountUsageWindow> findByStatus(JoinAccountUsageWindow.WindowStatus status, Pageable pageable);
    
    /**
     * 根据Zoom账号ID和状态查找窗口
     */
    List<JoinAccountUsageWindow> findByZoomUserIdAndStatus(Long zoomUserId, JoinAccountUsageWindow.WindowStatus status);
    
    /**
     * 根据时间范围查找窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.startTime <= :endTime AND w.endTime >= :startTime ORDER BY w.startTime")
    List<JoinAccountUsageWindow> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找与指定时间范围重叠的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.zoomUserId = :zoomUserId AND w.startTime < :endTime AND w.endTime > :startTime AND w.status IN ('PENDING', 'ACTIVE')")
    List<JoinAccountUsageWindow> findOverlappingWindows(
            @Param("zoomUserId") Long zoomUserId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找需要开启的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.status = 'PENDING' AND w.startTime <= :currentTime ORDER BY w.startTime")
    List<JoinAccountUsageWindow> findWindowsToOpen(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 查找需要关闭的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.status = 'ACTIVE' AND w.endTime <= :currentTime ORDER BY w.endTime")
    List<JoinAccountUsageWindow> findWindowsToClose(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 查找即将开启的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.status = 'PENDING' AND w.startTime BETWEEN :now AND :futureTime ORDER BY w.startTime")
    List<JoinAccountUsageWindow> findUpcomingWindows(@Param("now") LocalDateTime now, @Param("futureTime") LocalDateTime futureTime);
    
    /**
     * 查找即将关闭的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.status = 'ACTIVE' AND w.endTime BETWEEN :now AND :futureTime ORDER BY w.endTime")
    List<JoinAccountUsageWindow> findExpiringWindows(@Param("now") LocalDateTime now, @Param("futureTime") LocalDateTime futureTime);
    
    /**
     * 多条件查询
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE " +
           "(:zoomUserId IS NULL OR w.zoomUserId = :zoomUserId) AND " +
           "(:tokenNumber IS NULL OR w.tokenNumber = :tokenNumber) AND " +
           "(:status IS NULL OR w.status = :status) AND " +
           "(:startTime IS NULL OR w.startTime >= :startTime) AND " +
           "(:endTime IS NULL OR w.endTime <= :endTime) " +
           "ORDER BY w.startTime DESC")
    Page<JoinAccountUsageWindow> findByConditions(
            @Param("zoomUserId") Long zoomUserId,
            @Param("tokenNumber") String tokenNumber,
            @Param("status") JoinAccountUsageWindow.WindowStatus status,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable);
    
    /**
     * 统计各状态的窗口数量
     */
    @Query("SELECT w.status, COUNT(w) FROM JoinAccountUsageWindow w GROUP BY w.status")
    List<Object[]> countByStatus();
    
    /**
     * 统计各Zoom账号的窗口数量
     */
    @Query("SELECT w.zoomUserId, COUNT(w) FROM JoinAccountUsageWindow w GROUP BY w.zoomUserId ORDER BY COUNT(w) DESC")
    List<Object[]> countByZoomUserId();
    
    /**
     * 查找账号在指定时间范围内的窗口数量
     */
    @Query("SELECT COUNT(w) FROM JoinAccountUsageWindow w WHERE w.zoomUserId = :zoomUserId AND w.startTime BETWEEN :startTime AND :endTime AND w.status IN ('PENDING', 'ACTIVE')")
    long countByZoomUserIdAndTimeRange(
            @Param("zoomUserId") Long zoomUserId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找账号的下一个窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.zoomUserId = :zoomUserId AND w.startTime >= :currentTime AND w.status IN ('PENDING', 'ACTIVE') ORDER BY w.startTime")
    List<JoinAccountUsageWindow> findNextWindowsByZoomUserId(@Param("zoomUserId") Long zoomUserId, @Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找账号的下一个窗口
     */
    default Optional<JoinAccountUsageWindow> findNextWindowByZoomUserId(Long zoomUserId, LocalDateTime currentTime) {
        List<JoinAccountUsageWindow> windows = findNextWindowsByZoomUserId(zoomUserId, currentTime);
        return windows.isEmpty() ? Optional.empty() : Optional.of(windows.get(0));
    }
    
    /**
     * 查找账号当前活跃的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.zoomUserId = :zoomUserId AND w.status = 'ACTIVE' AND w.startTime <= :currentTime AND w.endTime >= :currentTime")
    Optional<JoinAccountUsageWindow> findCurrentActiveWindow(@Param("zoomUserId") Long zoomUserId, @Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 查找最近创建的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w ORDER BY w.createdAt DESC")
    Page<JoinAccountUsageWindow> findRecentlyCreated(Pageable pageable);
    
    /**
     * 查找最近开启的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.openedAt IS NOT NULL ORDER BY w.openedAt DESC")
    Page<JoinAccountUsageWindow> findRecentlyOpened(Pageable pageable);
    
    /**
     * 查找最近关闭的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.closedAt IS NOT NULL ORDER BY w.closedAt DESC")
    Page<JoinAccountUsageWindow> findRecentlyClosed(Pageable pageable);
    
    /**
     * 查找指定日期的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE DATE(w.startTime) = DATE(:date) ORDER BY w.startTime")
    List<JoinAccountUsageWindow> findByDate(@Param("date") LocalDateTime date);
    
    /**
     * 查找过期但未关闭的窗口
     */
    @Query("SELECT w FROM JoinAccountUsageWindow w WHERE w.status = 'ACTIVE' AND w.endTime < :currentTime")
    List<JoinAccountUsageWindow> findExpiredActiveWindows(@Param("currentTime") LocalDateTime currentTime);
}
