package com.zoombus.repository;

import com.zoombus.entity.MeetingParticipant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议参会人员数据访问接口
 */
@Repository
public interface MeetingParticipantRepository extends JpaRepository<MeetingParticipant, Long> {
    
    /**
     * 根据会议报告ID查找参会人员
     */
    List<MeetingParticipant> findByMeetingReportId(Long meetingReportId);
    
    /**
     * 分页查找指定会议的参会人员
     */
    Page<MeetingParticipant> findByMeetingReportId(Long meetingReportId, Pageable pageable);
    
    /**
     * 根据参会者邮箱查找参会记录
     */
    List<MeetingParticipant> findByParticipantEmail(String participantEmail);
    
    /**
     * 分页根据参会者邮箱查找参会记录
     */
    Page<MeetingParticipant> findByParticipantEmail(String participantEmail, Pageable pageable);
    
    /**
     * 根据参会者姓名模糊查询
     */
    @Query("SELECT mp FROM MeetingParticipant mp WHERE mp.participantName LIKE %:name%")
    List<MeetingParticipant> findByParticipantNameContaining(@Param("name") String name);
    
    /**
     * 根据用户类型查找参会人员
     */
    List<MeetingParticipant> findByUserType(MeetingParticipant.UserType userType);
    
    /**
     * 查找指定会议的主持人
     */
    @Query("SELECT mp FROM MeetingParticipant mp WHERE mp.meetingReportId = :meetingReportId " +
           "AND mp.userType IN ('HOST', 'CO_HOST')")
    List<MeetingParticipant> findHostsByMeetingReportId(@Param("meetingReportId") Long meetingReportId);
    
    /**
     * 查找指定时间范围内的参会记录
     */
    @Query("SELECT mp FROM MeetingParticipant mp WHERE mp.joinTime >= :startTime AND mp.joinTime <= :endTime")
    List<MeetingParticipant> findByJoinTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 分页查找指定时间范围内的参会记录
     */
    @Query("SELECT mp FROM MeetingParticipant mp WHERE mp.joinTime >= :startTime AND mp.joinTime <= :endTime")
    Page<MeetingParticipant> findByJoinTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                                  @Param("endTime") LocalDateTime endTime, 
                                                  Pageable pageable);
    
    /**
     * 查找参会时长超过指定分钟数的记录
     */
    @Query("SELECT mp FROM MeetingParticipant mp WHERE mp.durationMinutes >= :minDuration")
    List<MeetingParticipant> findByDurationMinutesGreaterThanEqual(@Param("minDuration") Integer minDuration);
    
    /**
     * 统计指定会议的参会人数
     */
    @Query("SELECT COUNT(mp) FROM MeetingParticipant mp WHERE mp.meetingReportId = :meetingReportId")
    Long countByMeetingReportId(@Param("meetingReportId") Long meetingReportId);
    
    /**
     * 统计指定会议的唯一参会人数（按邮箱去重）
     */
    @Query("SELECT COUNT(DISTINCT mp.participantEmail) FROM MeetingParticipant mp " +
           "WHERE mp.meetingReportId = :meetingReportId AND mp.participantEmail IS NOT NULL")
    Long countUniqueParticipantsByMeetingReportId(@Param("meetingReportId") Long meetingReportId);
    
    /**
     * 计算指定会议的平均参会时长
     */
    @Query("SELECT AVG(mp.durationMinutes) FROM MeetingParticipant mp " +
           "WHERE mp.meetingReportId = :meetingReportId AND mp.durationMinutes IS NOT NULL")
    Double getAverageDurationByMeetingReportId(@Param("meetingReportId") Long meetingReportId);
    
    /**
     * 查找指定参会者的历史参会记录
     */
    @Query("SELECT mp FROM MeetingParticipant mp " +
           "JOIN mp.meetingReport mr " +
           "WHERE mp.participantEmail = :email " +
           "ORDER BY mr.startTime DESC")
    List<MeetingParticipant> findParticipantHistory(@Param("email") String email);
    
    /**
     * 分页查找指定参会者的历史参会记录
     */
    @Query("SELECT mp FROM MeetingParticipant mp " +
           "JOIN mp.meetingReport mr " +
           "WHERE mp.participantEmail = :email " +
           "ORDER BY mr.startTime DESC")
    Page<MeetingParticipant> findParticipantHistory(@Param("email") String email, Pageable pageable);
    
    /**
     * 统计指定参会者的参会次数
     */
    @Query("SELECT COUNT(mp) FROM MeetingParticipant mp WHERE mp.participantEmail = :email")
    Long countParticipationsByEmail(@Param("email") String email);
    
    /**
     * 统计指定参会者的总参会时长
     */
    @Query("SELECT COALESCE(SUM(mp.durationMinutes), 0) FROM MeetingParticipant mp " +
           "WHERE mp.participantEmail = :email AND mp.durationMinutes IS NOT NULL")
    Long sumDurationByEmail(@Param("email") String email);
    
    /**
     * 查找使用特定连接方式的参会者
     */
    @Query("SELECT mp FROM MeetingParticipant mp WHERE " +
           "(:hasVideo IS NULL OR mp.hasVideo = :hasVideo) AND " +
           "(:hasPstn IS NULL OR mp.hasPstn = :hasPstn) AND " +
           "(:hasVoip IS NULL OR mp.hasVoip = :hasVoip)")
    List<MeetingParticipant> findByConnectionType(@Param("hasVideo") Boolean hasVideo,
                                                 @Param("hasPstn") Boolean hasPstn,
                                                 @Param("hasVoip") Boolean hasVoip);
    
    /**
     * 查找还在会议中的参会者（没有离开时间）
     */
    @Query("SELECT mp FROM MeetingParticipant mp WHERE mp.leaveTime IS NULL")
    List<MeetingParticipant> findActiveParticipants();
    
    /**
     * 查找指定会议中还在会议中的参会者
     */
    @Query("SELECT mp FROM MeetingParticipant mp WHERE mp.meetingReportId = :meetingReportId " +
           "AND mp.leaveTime IS NULL")
    List<MeetingParticipant> findActiveParticipantsByMeetingReportId(@Param("meetingReportId") Long meetingReportId);
    
    /**
     * 复合查询：根据多个条件查找参会记录
     */
    @Query("SELECT mp FROM MeetingParticipant mp " +
           "JOIN mp.meetingReport mr " +
           "WHERE (:meetingReportId IS NULL OR mp.meetingReportId = :meetingReportId) " +
           "AND (:email IS NULL OR mp.participantEmail LIKE %:email%) " +
           "AND (:name IS NULL OR mp.participantName LIKE %:name%) " +
           "AND (:userType IS NULL OR mp.userType = :userType) " +
           "AND (:startTime IS NULL OR mp.joinTime >= :startTime) " +
           "AND (:endTime IS NULL OR mp.joinTime <= :endTime) " +
           "AND (:minDuration IS NULL OR mp.durationMinutes >= :minDuration) " +
           "ORDER BY mp.joinTime DESC")
    Page<MeetingParticipant> findParticipantsWithFilters(@Param("meetingReportId") Long meetingReportId,
                                                        @Param("email") String email,
                                                        @Param("name") String name,
                                                        @Param("userType") MeetingParticipant.UserType userType,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime,
                                                        @Param("minDuration") Integer minDuration,
                                                        Pageable pageable);
    
    /**
     * 获取参会统计信息
     */
    @Query("SELECT new map(" +
           "COUNT(mp) as totalParticipations, " +
           "COUNT(DISTINCT mp.participantEmail) as uniqueParticipants, " +
           "COALESCE(AVG(mp.durationMinutes), 0) as avgDuration, " +
           "COALESCE(MAX(mp.durationMinutes), 0) as maxDuration, " +
           "COUNT(CASE WHEN mp.userType = 'HOST' THEN 1 END) as hostCount, " +
           "COUNT(CASE WHEN mp.hasVideo = true THEN 1 END) as videoParticipants" +
           ") FROM MeetingParticipant mp " +
           "WHERE (:meetingReportId IS NULL OR mp.meetingReportId = :meetingReportId) " +
           "AND (:startTime IS NULL OR mp.joinTime >= :startTime) " +
           "AND (:endTime IS NULL OR mp.joinTime <= :endTime)")
    List<Object> getParticipantStatistics(@Param("meetingReportId") Long meetingReportId,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找频繁参会者（参会次数排行）
     */
    @Query("SELECT mp.participantEmail, mp.participantName, COUNT(mp) as participationCount " +
           "FROM MeetingParticipant mp " +
           "WHERE mp.participantEmail IS NOT NULL " +
           "GROUP BY mp.participantEmail, mp.participantName " +
           "ORDER BY participationCount DESC")
    Page<Object[]> findFrequentParticipants(Pageable pageable);
    
    /**
     * 删除指定时间之前的参会记录（用于数据清理）
     */
    @Query("DELETE FROM MeetingParticipant mp WHERE mp.createdAt < :beforeTime")
    void deleteParticipantsCreatedBefore(@Param("beforeTime") LocalDateTime beforeTime);
}
