package com.zoombus.repository;

import com.zoombus.entity.MeetingReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 会议报告数据访问接口
 */
@Repository
public interface MeetingReportRepository extends JpaRepository<MeetingReport, Long> {
    
    /**
     * 根据Zoom会议UUID查找报告（返回最新的一条）
     */
    Optional<MeetingReport> findByZoomMeetingUuid(String zoomMeetingUuid);

    /**
     * 根据Zoom会议UUID查找所有报告（支持多条记录）
     */
    List<MeetingReport> findAllByZoomMeetingUuid(String zoomMeetingUuid);

    /**
     * 根据Zoom会议ID查找报告（返回最新的一条）
     */
    Optional<MeetingReport> findByZoomMeetingId(String zoomMeetingId);

    /**
     * 根据Zoom会议ID查找所有报告（支持多条记录）
     */
    List<MeetingReport> findAllByZoomMeetingId(String zoomMeetingId);
    
    /**
     * 检查报告是否存在
     */
    boolean existsByZoomMeetingUuid(String zoomMeetingUuid);
    
    /**
     * 根据获取状态查找报告
     */
    List<MeetingReport> findByFetchStatus(MeetingReport.FetchStatus fetchStatus);
    
    /**
     * 查找需要重试的报告
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.fetchStatus = 'FAILED' AND " +
           "(mr.fetchRetryCount IS NULL OR mr.fetchRetryCount < 3) AND " +
           "(mr.lastFetchAttempt IS NULL OR mr.lastFetchAttempt < :retryAfter)")
    List<MeetingReport> findReportsNeedingRetry(@Param("retryAfter") LocalDateTime retryAfter);
    
    /**
     * 根据时间范围查找报告
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.startTime >= :startTime AND mr.startTime <= :endTime")
    List<MeetingReport> findByStartTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                              @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据时间范围分页查找报告
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.startTime >= :startTime AND mr.startTime <= :endTime")
    Page<MeetingReport> findByStartTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                              @Param("endTime") LocalDateTime endTime, 
                                              Pageable pageable);
    
    /**
     * 查找指定PMI的会议报告
     */
    @Query("SELECT mr FROM MeetingReport mr " +
           "JOIN mr.zoomMeeting zm " +
           "WHERE zm.pmiRecordId = :pmiRecordId " +
           "ORDER BY mr.startTime DESC")
    List<MeetingReport> findByPmiRecordId(@Param("pmiRecordId") Long pmiRecordId);
    
    /**
     * 分页查找指定PMI的会议报告
     */
    @Query("SELECT mr FROM MeetingReport mr " +
           "JOIN mr.zoomMeeting zm " +
           "WHERE zm.pmiRecordId = :pmiRecordId " +
           "ORDER BY mr.startTime DESC")
    Page<MeetingReport> findByPmiRecordId(@Param("pmiRecordId") Long pmiRecordId, Pageable pageable);
    
    /**
     * 查找指定主持人的会议报告
     */
    @Query("SELECT mr FROM MeetingReport mr " +
           "JOIN mr.zoomMeeting zm " +
           "WHERE zm.hostId = :hostId " +
           "ORDER BY mr.startTime DESC")
    List<MeetingReport> findByHostId(@Param("hostId") String hostId);
    
    /**
     * 分页查找指定主持人的会议报告
     */
    @Query("SELECT mr FROM MeetingReport mr " +
           "JOIN mr.zoomMeeting zm " +
           "WHERE zm.hostId = :hostId " +
           "ORDER BY mr.startTime DESC")
    Page<MeetingReport> findByHostId(@Param("hostId") String hostId, Pageable pageable);
    
    /**
     * 查找最近的会议报告
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.fetchStatus = 'SUCCESS' " +
           "ORDER BY mr.startTime DESC")
    Page<MeetingReport> findRecentReports(Pageable pageable);
    
    /**
     * 统计指定时间范围内的会议数量
     */
    @Query("SELECT COUNT(mr) FROM MeetingReport mr WHERE mr.startTime >= :startTime AND mr.startTime <= :endTime")
    Long countByStartTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的参会人数
     */
    @Query("SELECT COALESCE(SUM(mr.totalParticipants), 0) FROM MeetingReport mr " +
           "WHERE mr.startTime >= :startTime AND mr.startTime <= :endTime")
    Long sumParticipantsByStartTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的会议总时长
     */
    @Query("SELECT COALESCE(SUM(mr.durationMinutes), 0) FROM MeetingReport mr " +
           "WHERE mr.startTime >= :startTime AND mr.startTime <= :endTime")
    Long sumDurationByStartTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                      @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找有录制的会议报告
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.hasRecording = true " +
           "ORDER BY mr.startTime DESC")
    List<MeetingReport> findReportsWithRecording();
    
    /**
     * 分页查找有录制的会议报告
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.hasRecording = true " +
           "ORDER BY mr.startTime DESC")
    Page<MeetingReport> findReportsWithRecording(Pageable pageable);
    
    /**
     * 根据主题模糊查询
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.topic LIKE %:keyword% " +
           "ORDER BY mr.startTime DESC")
    List<MeetingReport> findByTopicContaining(@Param("keyword") String keyword);
    
    /**
     * 分页根据主题模糊查询
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.topic LIKE %:keyword% " +
           "ORDER BY mr.startTime DESC")
    Page<MeetingReport> findByTopicContaining(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 复合查询：根据多个条件查找报告
     */
    @Query("SELECT mr FROM MeetingReport mr " +
           "LEFT JOIN mr.zoomMeeting zm " +
           "WHERE (:hostId IS NULL OR zm.hostId = :hostId) " +
           "AND (:pmiRecordId IS NULL OR zm.pmiRecordId = :pmiRecordId) " +
           "AND (:startTime IS NULL OR mr.startTime >= :startTime) " +
           "AND (:endTime IS NULL OR mr.startTime <= :endTime) " +
           "AND (:keyword IS NULL OR mr.topic LIKE %:keyword%) " +
           "AND (:fetchStatus IS NULL OR mr.fetchStatus = :fetchStatus) " +
           "ORDER BY mr.startTime DESC")
    Page<MeetingReport> findReportsWithFilters(@Param("hostId") String hostId,
                                              @Param("pmiRecordId") Long pmiRecordId,
                                              @Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime,
                                              @Param("keyword") String keyword,
                                              @Param("fetchStatus") MeetingReport.FetchStatus fetchStatus,
                                              Pageable pageable);
    
    /**
     * 获取会议报告统计信息
     */
    @Query("SELECT new map(" +
           "COUNT(mr) as totalReports, " +
           "COALESCE(SUM(mr.totalParticipants), 0) as totalParticipants, " +
           "COALESCE(SUM(mr.durationMinutes), 0) as totalDuration, " +
           "COALESCE(AVG(mr.durationMinutes), 0) as avgDuration, " +
           "COUNT(CASE WHEN mr.hasRecording = true THEN 1 END) as recordedMeetings" +
           ") FROM MeetingReport mr " +
           "WHERE (:startTime IS NULL OR mr.startTime >= :startTime) " +
           "AND (:endTime IS NULL OR mr.startTime <= :endTime)")
    List<Object> getReportStatistics(@Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的报告（用于数据清理）
     */
    @Query("DELETE FROM MeetingReport mr WHERE mr.createdAt < :beforeTime")
    void deleteReportsCreatedBefore(@Param("beforeTime") LocalDateTime beforeTime);

    // ========== 性能优化查询方法 ==========

    /**
     * 批量查询会议报告（优化版本）
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.zoomMeetingUuid IN :uuids")
    List<MeetingReport> findByZoomMeetingUuids(@Param("uuids") List<String> uuids);

    /**
     * 查询指定状态的报告数量
     */
    @Query("SELECT COUNT(mr) FROM MeetingReport mr WHERE mr.fetchStatus = :status")
    long countByFetchStatus(@Param("status") MeetingReport.FetchStatus status);

    /**
     * 查询最近的成功报告（用于缓存预热）
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.fetchStatus = 'SUCCESS' ORDER BY mr.createdAt DESC")
    List<MeetingReport> findRecentSuccessReports(Pageable pageable);

    /**
     * 查询指定时间范围内的报告统计信息（优化版本）
     */
    @Query("SELECT " +
           "COUNT(mr) as totalReports, " +
           "SUM(mr.totalParticipants) as totalParticipants, " +
           "SUM(mr.durationMinutes) as totalDuration, " +
           "AVG(mr.durationMinutes) as averageDuration, " +
           "SUM(CASE WHEN mr.hasRecording = true THEN 1 ELSE 0 END) as recordingCount, " +
           "SUM(CASE WHEN mr.hasVideo = true THEN 1 ELSE 0 END) as videoCount, " +
           "SUM(CASE WHEN mr.hasScreenShare = true THEN 1 ELSE 0 END) as screenShareCount " +
           "FROM MeetingReport mr " +
           "WHERE (:startTime IS NULL OR mr.startTime >= :startTime) " +
           "AND (:endTime IS NULL OR mr.startTime <= :endTime) " +
           "AND mr.fetchStatus = 'SUCCESS'")
    Object[] getStatisticsSummary(@Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);

    // ========== PMI 相关查询方法 ==========

    /**
     * 根据PMI号码查找所有会议报告
     */
    List<MeetingReport> findByPmiNumber(String pmiNumber);

    /**
     * 根据PMI号码分页查找会议报告
     */
    Page<MeetingReport> findByPmiNumber(String pmiNumber, Pageable pageable);

    /**
     * 根据会议类型查找报告
     */
    List<MeetingReport> findByMeetingType(MeetingReport.MeetingType meetingType);

    /**
     * 根据会议类型分页查找报告
     */
    Page<MeetingReport> findByMeetingType(MeetingReport.MeetingType meetingType, Pageable pageable);

    /**
     * 根据Zoom主账号ID查找报告
     */
    List<MeetingReport> findByZoomAuthId(Long zoomAuthId);

    /**
     * 根据Zoom主账号ID分页查找报告
     */
    Page<MeetingReport> findByZoomAuthId(Long zoomAuthId, Pageable pageable);

    /**
     * 根据主持人用户ID查找报告
     */
    List<MeetingReport> findByHostUserId(String hostUserId);

    /**
     * 根据主持人用户ID分页查找报告
     */
    Page<MeetingReport> findByHostUserId(String hostUserId, Pageable pageable);

    /**
     * 查找指定PMI在指定时间范围内的会议报告
     */
    @Query("SELECT mr FROM MeetingReport mr WHERE mr.pmiNumber = :pmiNumber " +
           "AND mr.startTime BETWEEN :startTime AND :endTime " +
           "ORDER BY mr.startTime DESC")
    List<MeetingReport> findByPmiNumberAndTimeRange(@Param("pmiNumber") String pmiNumber,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 获取PMI的会议统计信息
     */
    @Query("SELECT " +
           "COUNT(mr) as totalMeetings, " +
           "SUM(mr.durationMinutes) as totalDuration, " +
           "SUM(mr.totalParticipants) as totalParticipants, " +
           "AVG(mr.durationMinutes) as avgDuration, " +
           "AVG(mr.totalParticipants) as avgParticipants, " +
           "MIN(mr.startTime) as firstMeeting, " +
           "MAX(mr.startTime) as lastMeeting " +
           "FROM MeetingReport mr " +
           "WHERE mr.pmiNumber = :pmiNumber AND mr.fetchStatus = 'SUCCESS'")
    Object[] getPmiStatistics(@Param("pmiNumber") String pmiNumber);

    /**
     * 获取指定Zoom主账号的会议统计
     */
    @Query("SELECT " +
           "COUNT(mr) as totalMeetings, " +
           "SUM(mr.durationMinutes) as totalDuration, " +
           "SUM(mr.totalParticipants) as totalParticipants, " +
           "COUNT(DISTINCT mr.pmiNumber) as uniquePmis " +
           "FROM MeetingReport mr " +
           "WHERE mr.zoomAuthId = :zoomAuthId AND mr.fetchStatus = 'SUCCESS'")
    Object[] getZoomAuthStatistics(@Param("zoomAuthId") Long zoomAuthId);

    /**
     * 获取会议类型分布统计
     */
    @Query("SELECT mr.meetingType, COUNT(mr) as count " +
           "FROM MeetingReport mr " +
           "WHERE mr.fetchStatus = 'SUCCESS' " +
           "GROUP BY mr.meetingType")
    List<Object[]> getMeetingTypeDistribution();

    /**
     * 全量报告查询（支持多维度筛选）
     */
    @Query("SELECT mr FROM MeetingReport mr " +
           "WHERE (:zoomAuthId IS NULL OR mr.zoomAuthId = :zoomAuthId) " +
           "AND (:meetingType IS NULL OR mr.meetingType = :meetingType) " +
           "AND (:pmiNumber IS NULL OR mr.pmiNumber = :pmiNumber) " +
           "AND (:hostUserId IS NULL OR mr.hostUserId = :hostUserId) " +
           "AND (:startTime IS NULL OR mr.startTime >= :startTime) " +
           "AND (:endTime IS NULL OR mr.startTime <= :endTime) " +
           "AND (:keyword IS NULL OR mr.topic LIKE %:keyword%) " +
           "AND (:fetchStatus IS NULL OR mr.fetchStatus = :fetchStatus) " +
           "ORDER BY mr.startTime DESC")
    Page<MeetingReport> findAllReportsWithFilters(@Param("zoomAuthId") Long zoomAuthId,
                                                 @Param("meetingType") MeetingReport.MeetingType meetingType,
                                                 @Param("pmiNumber") String pmiNumber,
                                                 @Param("hostUserId") String hostUserId,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime,
                                                 @Param("keyword") String keyword,
                                                 @Param("fetchStatus") MeetingReport.FetchStatus fetchStatus,
                                                 Pageable pageable);

    /**
     * 获取全量报告统计概览
     */
    @Query("SELECT " +
           "COUNT(mr) as totalReports, " +
           "COUNT(DISTINCT mr.zoomAuthId) as totalAccounts, " +
           "COUNT(DISTINCT mr.pmiNumber) as totalPmis, " +
           "COUNT(DISTINCT mr.hostUserId) as totalHosts, " +
           "SUM(mr.durationMinutes) as totalDuration, " +
           "SUM(mr.totalParticipants) as totalParticipants " +
           "FROM MeetingReport mr " +
           "WHERE mr.fetchStatus = 'SUCCESS'")
    Object[] getOverallStatistics();

    /**
     * 获取最活跃的PMI列表
     */
    @Query("SELECT mr.pmiNumber, COUNT(mr) as meetingCount " +
           "FROM MeetingReport mr " +
           "WHERE mr.pmiNumber IS NOT NULL AND mr.fetchStatus = 'SUCCESS' " +
           "GROUP BY mr.pmiNumber " +
           "ORDER BY meetingCount DESC")
    List<Object[]> getMostActivePmis(Pageable pageable);

    /**
     * 获取最活跃的主持人列表
     */
    @Query("SELECT mr.hostUserId, COUNT(mr) as meetingCount " +
           "FROM MeetingReport mr " +
           "WHERE mr.hostUserId IS NOT NULL AND mr.fetchStatus = 'SUCCESS' " +
           "GROUP BY mr.hostUserId " +
           "ORDER BY meetingCount DESC")
    List<Object[]> getMostActiveHosts(Pageable pageable);

    // ========== 数据迁移相关查询方法 ==========

    /**
     * 统计有PMI号码的报告数量
     */
    long countByPmiNumberIsNotNull();

    /**
     * 统计有Zoom主账号ID的报告数量
     */
    long countByZoomAuthIdIsNotNull();

    /**
     * 统计有主持人用户ID的报告数量
     */
    long countByHostUserIdIsNotNull();

    // ========== PMI报告相关查询方法 ==========

    /**
     * 根据PMI号码查找会议报告（按开始时间倒序）
     */
    Page<MeetingReport> findByPmiNumberOrderByStartTimeDesc(String pmiNumber, Pageable pageable);

    /**
     * 根据PMI号码和时间范围查找会议报告
     */
    List<MeetingReport> findByPmiNumberAndStartTimeBetween(String pmiNumber, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找指定时间范围内有PMI号码的会议报告
     */
    List<MeetingReport> findByPmiNumberIsNotNullAndStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

}
