package com.zoombus.repository;

import com.zoombus.entity.MeetingReportTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 会议报告任务数据访问接口
 */
@Repository
public interface MeetingReportTaskRepository extends JpaRepository<MeetingReportTask, Long> {
    
    /**
     * 根据Zoom会议UUID查找任务
     */
    Optional<MeetingReportTask> findByZoomMeetingUuid(String zoomMeetingUuid);
    
    /**
     * 根据Zoom会议ID查找任务
     */
    Optional<MeetingReportTask> findByZoomMeetingId(String zoomMeetingId);
    
    /**
     * 检查任务是否存在
     */
    boolean existsByZoomMeetingUuid(String zoomMeetingUuid);
    
    /**
     * 根据任务状态查找任务
     */
    List<MeetingReportTask> findByTaskStatus(MeetingReportTask.TaskStatus taskStatus);
    
    /**
     * 分页根据任务状态查找任务
     */
    Page<MeetingReportTask> findByTaskStatus(MeetingReportTask.TaskStatus taskStatus, Pageable pageable);
    
    /**
     * 查找待执行的任务（状态为PENDING且计划时间已到）
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt WHERE mrt.taskStatus = 'PENDING' " +
           "AND (mrt.scheduledTime IS NULL OR mrt.scheduledTime <= :currentTime) " +
           "ORDER BY mrt.priority ASC, mrt.scheduledTime ASC")
    List<MeetingReportTask> findPendingTasks(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 分页查找待执行的任务
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt WHERE mrt.taskStatus = 'PENDING' " +
           "AND (mrt.scheduledTime IS NULL OR mrt.scheduledTime <= :currentTime) " +
           "ORDER BY mrt.priority ASC, mrt.scheduledTime ASC")
    Page<MeetingReportTask> findPendingTasks(@Param("currentTime") LocalDateTime currentTime, Pageable pageable);
    
    /**
     * 查找需要重试的任务
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt WHERE mrt.taskStatus = 'FAILED' " +
           "AND mrt.retryCount < mrt.maxRetryCount " +
           "ORDER BY mrt.priority ASC, mrt.updatedAt ASC")
    List<MeetingReportTask> findTasksNeedingRetry();
    
    /**
     * 查找正在处理的任务
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt WHERE mrt.taskStatus = 'PROCESSING'")
    List<MeetingReportTask> findProcessingTasks();
    
    /**
     * 查找超时的处理中任务（处理时间超过指定分钟数）
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt WHERE mrt.taskStatus = 'PROCESSING' " +
           "AND mrt.startedTime < :timeoutBefore")
    List<MeetingReportTask> findTimeoutTasks(@Param("timeoutBefore") LocalDateTime timeoutBefore);
    
    /**
     * 根据优先级查找任务
     */
    List<MeetingReportTask> findByPriorityLessThanEqualOrderByPriorityAscScheduledTimeAsc(Integer priority);
    
    /**
     * 查找指定时间范围内创建的任务
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt WHERE mrt.createdAt >= :startTime AND mrt.createdAt <= :endTime")
    List<MeetingReportTask> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, 
                                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 分页查找指定时间范围内创建的任务
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt WHERE mrt.createdAt >= :startTime AND mrt.createdAt <= :endTime")
    Page<MeetingReportTask> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, 
                                                  @Param("endTime") LocalDateTime endTime, 
                                                  Pageable pageable);
    
    /**
     * 统计各状态的任务数量
     */
    @Query("SELECT mrt.taskStatus, COUNT(mrt) FROM MeetingReportTask mrt GROUP BY mrt.taskStatus")
    List<Object[]> countTasksByStatus();
    
    /**
     * 统计指定时间范围内的任务数量
     */
    @Query("SELECT COUNT(mrt) FROM MeetingReportTask mrt WHERE mrt.createdAt >= :startTime AND mrt.createdAt <= :endTime")
    Long countByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, 
                                @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计成功率
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN mrt.taskStatus = 'SUCCESS' THEN 1 END) as successCount, " +
           "COUNT(CASE WHEN mrt.taskStatus = 'FAILED' THEN 1 END) as failedCount, " +
           "COUNT(mrt) as totalCount " +
           "FROM MeetingReportTask mrt " +
           "WHERE mrt.createdAt >= :startTime AND mrt.createdAt <= :endTime")
    List<Object[]> getSuccessRateStatistics(@Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找最近完成的任务
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt WHERE mrt.taskStatus IN ('SUCCESS', 'FAILED') " +
           "ORDER BY mrt.completedTime DESC")
    Page<MeetingReportTask> findRecentCompletedTasks(Pageable pageable);
    
    /**
     * 复合查询：根据多个条件查找任务
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt " +
           "WHERE (:taskStatus IS NULL OR mrt.taskStatus = :taskStatus) " +
           "AND (:priority IS NULL OR mrt.priority <= :priority) " +
           "AND (:startTime IS NULL OR mrt.createdAt >= :startTime) " +
           "AND (:endTime IS NULL OR mrt.createdAt <= :endTime) " +
           "AND (:zoomMeetingId IS NULL OR mrt.zoomMeetingId LIKE %:zoomMeetingId%) " +
           "ORDER BY mrt.priority ASC, mrt.createdAt DESC")
    Page<MeetingReportTask> findTasksWithFilters(@Param("taskStatus") MeetingReportTask.TaskStatus taskStatus,
                                                @Param("priority") Integer priority,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime,
                                                @Param("zoomMeetingId") String zoomMeetingId,
                                                Pageable pageable);
    
    /**
     * 获取任务执行统计信息
     */
    @Query("SELECT new map(" +
           "COUNT(mrt) as totalTasks, " +
           "COUNT(CASE WHEN mrt.taskStatus = 'SUCCESS' THEN 1 END) as successTasks, " +
           "COUNT(CASE WHEN mrt.taskStatus = 'FAILED' THEN 1 END) as failedTasks, " +
           "COUNT(CASE WHEN mrt.taskStatus = 'PENDING' THEN 1 END) as pendingTasks, " +
           "COUNT(CASE WHEN mrt.taskStatus = 'PROCESSING' THEN 1 END) as processingTasks, " +
           "COALESCE(AVG(mrt.retryCount), 0) as avgRetryCount" +
           ") FROM MeetingReportTask mrt " +
           "WHERE (:startTime IS NULL OR mrt.createdAt >= :startTime) " +
           "AND (:endTime IS NULL OR mrt.createdAt <= :endTime)")
    List<Object> getTaskStatistics(@Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找重试次数最多的任务
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt WHERE mrt.retryCount > 0 " +
           "ORDER BY mrt.retryCount DESC, mrt.updatedAt DESC")
    Page<MeetingReportTask> findTasksWithMostRetries(Pageable pageable);
    
    /**
     * 查找执行时间最长的任务
     */
    @Query("SELECT mrt FROM MeetingReportTask mrt " +
           "WHERE mrt.startedTime IS NOT NULL AND mrt.completedTime IS NOT NULL " +
           "ORDER BY TIMESTAMPDIFF(MINUTE, mrt.startedTime, mrt.completedTime) DESC")
    Page<MeetingReportTask> findLongestExecutionTasks(Pageable pageable);
    
    /**
     * 删除指定状态的旧任务
     */
    @Query("DELETE FROM MeetingReportTask mrt WHERE mrt.taskStatus = :taskStatus " +
           "AND mrt.createdAt < :beforeTime")
    void deleteTasksByStatusAndCreatedBefore(@Param("taskStatus") MeetingReportTask.TaskStatus taskStatus,
                                           @Param("beforeTime") LocalDateTime beforeTime);
    
    /**
     * 删除指定时间之前的已完成任务
     */
    @Query("DELETE FROM MeetingReportTask mrt WHERE mrt.taskStatus IN ('SUCCESS', 'FAILED', 'CANCELLED') " +
           "AND mrt.completedTime < :beforeTime")
    void deleteCompletedTasksBefore(@Param("beforeTime") LocalDateTime beforeTime);
    
    /**
     * 重置超时的处理中任务为待处理状态
     */
    @Query("UPDATE MeetingReportTask mrt SET mrt.taskStatus = 'PENDING', " +
           "mrt.startedTime = NULL, mrt.updatedAt = :currentTime " +
           "WHERE mrt.taskStatus = 'PROCESSING' AND mrt.startedTime < :timeoutBefore")
    int resetTimeoutTasks(@Param("timeoutBefore") LocalDateTime timeoutBefore,
                         @Param("currentTime") LocalDateTime currentTime);
}
