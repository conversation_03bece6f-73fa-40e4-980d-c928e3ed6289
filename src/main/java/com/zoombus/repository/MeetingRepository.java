package com.zoombus.repository;

import com.zoombus.entity.Meeting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface MeetingRepository extends JpaRepository<Meeting, Long> {
    
    Optional<Meeting> findByZoomMeetingId(String zoomMeetingId);

    Optional<Meeting> findByMeetingUuid(String meetingUuid);

    List<Meeting> findByCreatorUserId(Long creatorUserId);

    List<Meeting> findByZoomUserId(String zoomUserId);

    List<Meeting> findByStatus(Meeting.MeetingStatus status);

    List<Meeting> findByCreationSource(Meeting.CreationSource creationSource);

    @Query("SELECT m FROM Meeting m WHERE m.creatorUserId = :creatorUserId AND m.status = :status")
    List<Meeting> findByCreatorUserIdAndStatus(@Param("creatorUserId") Long creatorUserId,
                                               @Param("status") Meeting.MeetingStatus status);

    @Query("SELECT m FROM Meeting m WHERE m.startTime BETWEEN :startDate AND :endDate")
    List<Meeting> findByStartTimeBetween(@Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate);

    @Query("SELECT m FROM Meeting m WHERE m.creatorUserId = :creatorUserId AND m.startTime BETWEEN :startDate AND :endDate")
    List<Meeting> findByCreatorUserIdAndStartTimeBetween(@Param("creatorUserId") Long creatorUserId,
                                                        @Param("startDate") LocalDateTime startDate,
                                                        @Param("endDate") LocalDateTime endDate);
    
    boolean existsByZoomMeetingId(String zoomMeetingId);
}
