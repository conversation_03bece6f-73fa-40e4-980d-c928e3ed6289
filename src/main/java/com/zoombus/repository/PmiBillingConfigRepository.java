package com.zoombus.repository;

import com.zoombus.entity.PmiBillingConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * PMI计费配置Repository接口
 */
@Repository
public interface PmiBillingConfigRepository extends JpaRepository<PmiBillingConfig, Long> {
    
    /**
     * 根据配置名称查找配置
     */
    Optional<PmiBillingConfig> findByConfigName(String configName);
    
    /**
     * 查找所有活跃的配置
     */
    List<PmiBillingConfig> findByIsActiveTrue();
    
    /**
     * 查找所有非活跃的配置
     */
    List<PmiBillingConfig> findByIsActiveFalse();
    
    /**
     * 查找当前有效的配置
     */
    @Query("SELECT pbc FROM PmiBillingConfig pbc WHERE pbc.isActive = true " +
           "AND pbc.effectiveDate <= :currentTime ORDER BY pbc.effectiveDate DESC")
    List<PmiBillingConfig> findEffectiveConfigs(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 查找默认配置（最新的有效配置）
     */
    @Query("SELECT pbc FROM PmiBillingConfig pbc WHERE pbc.isActive = true " +
           "AND pbc.effectiveDate <= :currentTime ORDER BY pbc.effectiveDate DESC")
    Optional<PmiBillingConfig> findDefaultConfig(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 查找指定时间范围内生效的配置
     */
    @Query("SELECT pbc FROM PmiBillingConfig pbc WHERE pbc.isActive = true " +
           "AND pbc.effectiveDate >= :startDate AND pbc.effectiveDate <= :endDate " +
           "ORDER BY pbc.effectiveDate ASC")
    List<PmiBillingConfig> findConfigsByEffectiveDateBetween(@Param("startDate") LocalDateTime startDate,
                                                           @Param("endDate") LocalDateTime endDate);
    
    /**
     * 检查配置名称是否存在
     */
    boolean existsByConfigName(String configName);
    
    /**
     * 查找免费配置
     */
    @Query("SELECT pbc FROM PmiBillingConfig pbc WHERE pbc.isActive = true " +
           "AND (pbc.ratePerMinute IS NULL OR pbc.ratePerMinute = 0)")
    List<PmiBillingConfig> findFreeConfigs();
    
    /**
     * 查找付费配置
     */
    @Query("SELECT pbc FROM PmiBillingConfig pbc WHERE pbc.isActive = true " +
           "AND pbc.ratePerMinute IS NOT NULL AND pbc.ratePerMinute > 0")
    List<PmiBillingConfig> findPaidConfigs();
    
    /**
     * 根据货币类型查找配置
     */
    List<PmiBillingConfig> findByCurrency(String currency);
    
    /**
     * 查找最近创建的配置
     */
    @Query("SELECT pbc FROM PmiBillingConfig pbc ORDER BY pbc.createdAt DESC")
    List<PmiBillingConfig> findRecentConfigs();
    
    /**
     * 查找最近更新的配置
     */
    @Query("SELECT pbc FROM PmiBillingConfig pbc ORDER BY pbc.updatedAt DESC")
    List<PmiBillingConfig> findRecentlyUpdatedConfigs();
}
