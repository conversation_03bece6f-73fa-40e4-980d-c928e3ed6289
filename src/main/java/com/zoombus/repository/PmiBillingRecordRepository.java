package com.zoombus.repository;

import com.zoombus.entity.PmiBillingRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PMI计费记录Repository接口
 */
@Repository
public interface PmiBillingRecordRepository extends JpaRepository<PmiBillingRecord, Long> {
    
    /**
     * 根据PMI记录ID查找计费记录
     */
    List<PmiBillingRecord> findByPmiRecordId(Long pmiRecordId);
    
    /**
     * 根据PMI记录ID查找计费记录（分页）
     */
    Page<PmiBillingRecord> findByPmiRecordId(Long pmiRecordId, Pageable pageable);
    
    /**
     * 根据用户ID查找计费记录
     */
    List<PmiBillingRecord> findByUserId(Long userId);
    
    /**
     * 根据用户ID查找计费记录（分页）
     */
    Page<PmiBillingRecord> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据会议ID查找计费记录
     */
    List<PmiBillingRecord> findByZoomMeetingId(Long zoomMeetingId);
    
    /**
     * 根据交易类型查找记录
     */
    List<PmiBillingRecord> findByTransactionType(PmiBillingRecord.TransactionType transactionType);
    
    /**
     * 根据状态查找记录
     */
    List<PmiBillingRecord> findByStatus(PmiBillingRecord.RecordStatus status);
    
    /**
     * 查找指定时间范围内的记录
     */
    @Query("SELECT pbr FROM PmiBillingRecord pbr WHERE pbr.createdAt >= :startTime AND pbr.createdAt <= :endTime")
    List<PmiBillingRecord> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据PMI记录ID和交易类型查找记录
     */
    List<PmiBillingRecord> findByPmiRecordIdAndTransactionType(Long pmiRecordId, 
                                                              PmiBillingRecord.TransactionType transactionType);
    
    /**
     * 查找PMI的充值记录
     */
    @Query("SELECT pbr FROM PmiBillingRecord pbr WHERE pbr.pmiRecordId = :pmiRecordId " +
           "AND pbr.transactionType = 'RECHARGE' ORDER BY pbr.createdAt DESC")
    List<PmiBillingRecord> findRechargeRecordsByPmiRecordId(@Param("pmiRecordId") Long pmiRecordId);
    
    /**
     * 查找PMI的扣费记录
     */
    @Query("SELECT pbr FROM PmiBillingRecord pbr WHERE pbr.pmiRecordId = :pmiRecordId " +
           "AND pbr.transactionType = 'DEDUCT' ORDER BY pbr.createdAt DESC")
    List<PmiBillingRecord> findDeductRecordsByPmiRecordId(@Param("pmiRecordId") Long pmiRecordId);
    
    /**
     * 统计PMI的总充值金额
     */
    @Query("SELECT COALESCE(SUM(pbr.amountMinutes), 0) FROM PmiBillingRecord pbr " +
           "WHERE pbr.pmiRecordId = :pmiRecordId AND pbr.transactionType = 'RECHARGE' " +
           "AND pbr.status = 'COMPLETED'")
    long sumRechargeAmountByPmiRecordId(@Param("pmiRecordId") Long pmiRecordId);
    
    /**
     * 统计PMI的总扣费金额
     */
    @Query("SELECT COALESCE(SUM(pbr.amountMinutes), 0) FROM PmiBillingRecord pbr " +
           "WHERE pbr.pmiRecordId = :pmiRecordId AND pbr.transactionType = 'DEDUCT' " +
           "AND pbr.status = 'COMPLETED'")
    long sumDeductAmountByPmiRecordId(@Param("pmiRecordId") Long pmiRecordId);
    
    /**
     * 查找用户的最近交易记录
     */
    @Query("SELECT pbr FROM PmiBillingRecord pbr WHERE pbr.userId = :userId " +
           "ORDER BY pbr.createdAt DESC")
    Page<PmiBillingRecord> findRecentRecordsByUserId(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * 查找PMI的最近交易记录
     */
    @Query("SELECT pbr FROM PmiBillingRecord pbr WHERE pbr.pmiRecordId = :pmiRecordId " +
           "ORDER BY pbr.createdAt DESC")
    Page<PmiBillingRecord> findRecentRecordsByPmiRecordId(@Param("pmiRecordId") Long pmiRecordId, Pageable pageable);
    
    /**
     * 查找待处理的记录
     */
    @Query("SELECT pbr FROM PmiBillingRecord pbr WHERE pbr.status = 'PENDING' " +
           "ORDER BY pbr.createdAt ASC")
    List<PmiBillingRecord> findPendingRecords();
    
    /**
     * 查找失败的记录
     */
    @Query("SELECT pbr FROM PmiBillingRecord pbr WHERE pbr.status = 'FAILED' " +
           "ORDER BY pbr.createdAt DESC")
    List<PmiBillingRecord> findFailedRecords();
    
    /**
     * 复合条件查询计费记录
     */
    @Query("SELECT pbr FROM PmiBillingRecord pbr " +
           "WHERE (:pmiRecordId IS NULL OR pbr.pmiRecordId = :pmiRecordId) " +
           "AND (:userId IS NULL OR pbr.userId = :userId) " +
           "AND (:transactionType IS NULL OR pbr.transactionType = :transactionType) " +
           "AND (:status IS NULL OR pbr.status = :status) " +
           "AND (:startDate IS NULL OR pbr.createdAt >= :startDate) " +
           "AND (:endDate IS NULL OR pbr.createdAt <= :endDate) " +
           "ORDER BY pbr.createdAt DESC")
    Page<PmiBillingRecord> findRecordsWithFilters(@Param("pmiRecordId") Long pmiRecordId,
                                                 @Param("userId") Long userId,
                                                 @Param("transactionType") PmiBillingRecord.TransactionType transactionType,
                                                 @Param("status") PmiBillingRecord.RecordStatus status,
                                                 @Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate,
                                                 Pageable pageable);
    
    /**
     * 统计指定时间范围内的交易统计
     */
    @Query("SELECT pbr.transactionType, COUNT(pbr), SUM(pbr.amountMinutes) " +
           "FROM PmiBillingRecord pbr " +
           "WHERE pbr.createdAt >= :startDate AND pbr.createdAt <= :endDate " +
           "AND pbr.status = 'COMPLETED' " +
           "GROUP BY pbr.transactionType")
    List<Object[]> getTransactionStatistics(@Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);
    
    /**
     * 查找指定会议的计费记录
     */
    @Query("SELECT pbr FROM PmiBillingRecord pbr WHERE pbr.zoomMeetingId = :meetingId " +
           "ORDER BY pbr.createdAt ASC")
    List<PmiBillingRecord> findByMeetingIdOrderByCreatedAt(@Param("meetingId") Long meetingId);
}
