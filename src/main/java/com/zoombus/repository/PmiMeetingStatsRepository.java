package com.zoombus.repository;

import com.zoombus.entity.PmiMeetingStats;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * PMI会议统计数据访问层
 */
@Repository
public interface PmiMeetingStatsRepository extends JpaRepository<PmiMeetingStats, Long> {

    /**
     * 根据PMI号码查找统计信息
     */
    Optional<PmiMeetingStats> findByPmiNumber(String pmiNumber);

    /**
     * 根据PMI记录ID查找统计信息
     */
    Optional<PmiMeetingStats> findByPmiRecordId(Long pmiRecordId);

    /**
     * 查找指定时间范围内有活动的PMI统计
     */
    @Query("SELECT pms FROM PmiMeetingStats pms WHERE pms.lastMeetingTime BETWEEN :startTime AND :endTime")
    List<PmiMeetingStats> findByLastMeetingTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查找最活跃的PMI（按会议次数排序）
     */
    @Query("SELECT pms FROM PmiMeetingStats pms ORDER BY pms.totalMeetings DESC")
    Page<PmiMeetingStats> findMostActivePmis(Pageable pageable);

    /**
     * 查找使用时长最长的PMI
     */
    @Query("SELECT pms FROM PmiMeetingStats pms ORDER BY pms.totalDurationMinutes DESC")
    Page<PmiMeetingStats> findLongestUsedPmis(Pageable pageable);

    /**
     * 根据最常用的Zoom主账号查找PMI统计
     */
    List<PmiMeetingStats> findByMostUsedZoomAuthId(Long zoomAuthId);

    /**
     * 查找会议次数大于指定值的PMI
     */
    @Query("SELECT pms FROM PmiMeetingStats pms WHERE pms.totalMeetings >= :minMeetings")
    List<PmiMeetingStats> findByTotalMeetingsGreaterThanEqual(@Param("minMeetings") Integer minMeetings);

    /**
     * 查找总时长大于指定值的PMI
     */
    @Query("SELECT pms FROM PmiMeetingStats pms WHERE pms.totalDurationMinutes >= :minDuration")
    List<PmiMeetingStats> findByTotalDurationMinutesGreaterThanEqual(@Param("minDuration") Integer minDuration);

    /**
     * 获取PMI统计概览
     */
    @Query(value = "SELECT " +
           "COUNT(*) as totalPmis, " +
           "COALESCE(SUM(total_meetings), 0) as totalMeetings, " +
           "COALESCE(SUM(total_duration_minutes), 0) as totalDurationMinutes, " +
           "COALESCE(SUM(total_participants), 0) as totalParticipants, " +
           "COALESCE(AVG(avg_duration_minutes), 0) as avgDurationMinutes, " +
           "COALESCE(AVG(avg_participants), 0) as avgParticipants " +
           "FROM t_pmi_meeting_stats", nativeQuery = true)
    Object[] getOverviewStats();

    /**
     * 获取指定时间范围内的PMI活动统计
     */
    @Query("SELECT " +
           "COUNT(DISTINCT pms.pmiNumber) as activePmis, " +
           "SUM(pms.totalMeetings) as totalMeetings, " +
           "SUM(pms.totalDurationMinutes) as totalDurationMinutes " +
           "FROM PmiMeetingStats pms " +
           "WHERE pms.lastMeetingTime BETWEEN :startTime AND :endTime")
    Object[] getActivityStatsInTimeRange(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取按Zoom主账号分组的PMI统计
     */
    @Query("SELECT " +
           "pms.mostUsedZoomAuthId, " +
           "COUNT(pms) as pmiCount, " +
           "SUM(pms.totalMeetings) as totalMeetings, " +
           "SUM(pms.totalDurationMinutes) as totalDurationMinutes " +
           "FROM PmiMeetingStats pms " +
           "WHERE pms.mostUsedZoomAuthId IS NOT NULL " +
           "GROUP BY pms.mostUsedZoomAuthId")
    List<Object[]> getStatsByZoomAuth();

    /**
     * 查找最近活跃的PMI
     */
    @Query("SELECT pms FROM PmiMeetingStats pms " +
           "WHERE pms.lastMeetingTime >= :sinceTime " +
           "ORDER BY pms.lastMeetingTime DESC")
    List<PmiMeetingStats> findRecentlyActivePmis(@Param("sinceTime") LocalDateTime sinceTime);

    /**
     * 查找长时间未使用的PMI
     */
    @Query("SELECT pms FROM PmiMeetingStats pms " +
           "WHERE pms.lastMeetingTime < :beforeTime " +
           "ORDER BY pms.lastMeetingTime ASC")
    List<PmiMeetingStats> findInactivePmis(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 获取PMI使用趋势数据（按月统计）
     */
    @Query("SELECT " +
           "YEAR(pms.lastMeetingTime) as year, " +
           "MONTH(pms.lastMeetingTime) as month, " +
           "COUNT(DISTINCT pms.pmiNumber) as activePmis, " +
           "SUM(pms.totalMeetings) as totalMeetings, " +
           "SUM(pms.totalDurationMinutes) as totalDurationMinutes " +
           "FROM PmiMeetingStats pms " +
           "WHERE pms.lastMeetingTime >= :startTime " +
           "GROUP BY YEAR(pms.lastMeetingTime), MONTH(pms.lastMeetingTime) " +
           "ORDER BY year DESC, month DESC")
    List<Object[]> getPmiUsageTrend(@Param("startTime") LocalDateTime startTime);

    /**
     * 检查PMI号码是否存在
     */
    boolean existsByPmiNumber(String pmiNumber);

    /**
     * 删除指定PMI记录ID的统计数据
     */
    void deleteByPmiRecordId(Long pmiRecordId);

    /**
     * 查找需要更新的统计记录（基于最后更新时间）
     */
    @Query("SELECT pms FROM PmiMeetingStats pms WHERE pms.updatedAt < :beforeTime")
    List<PmiMeetingStats> findStaleStats(@Param("beforeTime") LocalDateTime beforeTime);
}
