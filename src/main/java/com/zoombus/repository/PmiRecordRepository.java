package com.zoombus.repository;

import com.zoombus.entity.PmiRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.persistence.LockModeType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * PMI记录Repository
 */
@Repository
public interface PmiRecordRepository extends JpaRepository<PmiRecord, Long> {
    
    /**
     * 根据PMI号码查找记录
     */
    Optional<PmiRecord> findByPmiNumber(String pmiNumber);

    /**
     * 根据魔链ID查找记录
     */
    Optional<PmiRecord> findByMagicId(String magicId);

    /**
     * 检查PMI号码是否存在
     */
    boolean existsByPmiNumber(String pmiNumber);

    /**
     * 检查魔链ID是否存在
     */
    boolean existsByMagicId(String magicId);
    
    /**
     * 根据用户ID查找PMI记录
     */
    List<PmiRecord> findByUserId(Long userId);
    
    /**
     * 根据用户ID查找PMI记录（分页）
     */
    Page<PmiRecord> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据状态查找PMI记录
     */
    List<PmiRecord> findByStatus(PmiRecord.PmiStatus status);
    
    /**
     * 根据用户ID和状态查找PMI记录
     */
    List<PmiRecord> findByUserIdAndStatus(Long userId, PmiRecord.PmiStatus status);
    
    /**
     * 根据当前使用的ZoomUser查找PMI记录
     */
    List<PmiRecord> findByCurrentZoomUserId(Long zoomUserId);
    
    /**
     * 统计用户的PMI数量
     */
    @Query("SELECT COUNT(p) FROM PmiRecord p WHERE p.userId = :userId")
    long countByUserId(@Param("userId") Long userId);
    
    /**
     * 统计活跃的PMI数量
     */
    @Query("SELECT COUNT(p) FROM PmiRecord p WHERE p.status = 'ACTIVE'")
    long countActiveRecords();
    
    /**
     * 查找用户的活跃PMI记录
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.userId = :userId AND p.status = 'ACTIVE' ORDER BY p.createdAt DESC")
    List<PmiRecord> findActiveRecordsByUserId(@Param("userId") Long userId);
    
    /**
     * 搜索PMI记录（支持PMI号码模糊查询）
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.pmiNumber LIKE %:keyword% OR " +
           "EXISTS (SELECT u FROM User u WHERE u.id = p.userId AND " +
           "(LOWER(u.fullName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%'))))")
    Page<PmiRecord> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 搜索PMI记录（支持PMI号码模糊查询和计费模式筛选）
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.billingMode = :billingMode AND " +
           "(p.pmiNumber LIKE %:keyword% OR " +
           "EXISTS (SELECT u FROM User u WHERE u.id = p.userId AND " +
           "(LOWER(u.fullName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%')))))")
    Page<PmiRecord> searchByKeywordAndBillingMode(@Param("keyword") String keyword,
                                                  @Param("billingMode") PmiRecord.BillingMode billingMode,
                                                  Pageable pageable);

    /**
     * 搜索PMI记录（支持PMI号码模糊查询和计费模式筛选，带自定义排序）
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.billingMode = :billingMode AND " +
           "(p.pmiNumber LIKE %:keyword% OR " +
           "EXISTS (SELECT u FROM User u WHERE u.id = p.userId AND " +
           "(LOWER(u.fullName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%'))))) ORDER BY " +
           "CASE WHEN p.billingMode = 'LONG' THEN p.windowExpireTime END ASC, " +
           "CASE WHEN p.billingMode = 'BY_TIME' THEN p.availableMinutes END DESC, " +
           "p.createdAt DESC")
    Page<PmiRecord> searchByKeywordAndBillingModeWithCustomSort(@Param("keyword") String keyword,
                                                               @Param("billingMode") PmiRecord.BillingMode billingMode,
                                                               Pageable pageable);

    /**
     * 获取所有PMI记录并按自定义规则排序
     * LONG类型优先，按到期日由近至远排序；BY_TIME按剩余可用时长由长到短排序
     */
    @Query("SELECT p FROM PmiRecord p ORDER BY " +
           "CASE WHEN p.billingMode = 'LONG' THEN 0 ELSE 1 END, " +
           "CASE WHEN p.billingMode = 'LONG' THEN p.windowExpireTime END ASC, " +
           "CASE WHEN p.billingMode = 'BY_TIME' THEN p.availableMinutes END DESC, " +
           "p.createdAt DESC")
    Page<PmiRecord> findAllWithCustomSort(Pageable pageable);

    // ========== 计费相关查询方法 ==========

    /**
     * 根据计费模式查找PMI记录
     */
    List<PmiRecord> findByBillingMode(PmiRecord.BillingMode billingMode);

    /**
     * 根据计费模式查找PMI记录（分页）
     */
    Page<PmiRecord> findByBillingMode(PmiRecord.BillingMode billingMode, Pageable pageable);

    /**
     * 根据计费模式查找PMI记录（分页，带自定义排序）
     * LONG类型按到期日由近至远排序，BY_TIME类型按剩余时长由长到短排序
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.billingMode = :billingMode ORDER BY " +
           "CASE WHEN p.billingMode = 'LONG' THEN p.windowExpireTime END ASC, " +
           "CASE WHEN p.billingMode = 'BY_TIME' THEN p.availableMinutes END DESC, " +
           "p.createdAt DESC")
    Page<PmiRecord> findByBillingModeWithCustomSort(@Param("billingMode") PmiRecord.BillingMode billingMode, Pageable pageable);

    /**
     * 根据用户ID和计费模式查找PMI记录（分页）
     */
    Page<PmiRecord> findByUserIdAndBillingMode(Long userId, PmiRecord.BillingMode billingMode, Pageable pageable);

    /**
     * 根据用户ID和计费模式查找PMI记录（分页，带自定义排序）
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.userId = :userId AND p.billingMode = :billingMode ORDER BY " +
           "CASE WHEN p.billingMode = 'LONG' THEN p.windowExpireTime END ASC, " +
           "CASE WHEN p.billingMode = 'BY_TIME' THEN p.availableMinutes END DESC, " +
           "p.createdAt DESC")
    Page<PmiRecord> findByUserIdAndBillingModeWithCustomSort(@Param("userId") Long userId,
                                                            @Param("billingMode") PmiRecord.BillingMode billingMode,
                                                            Pageable pageable);

    /**
     * 根据计费状态查找PMI记录
     */
    List<PmiRecord> findByBillingStatus(PmiRecord.BillingStatus billingStatus);

    /**
     * 查找按时段计费且窗口已过期的PMI记录
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.billingMode = 'LONG' " +
           "AND p.windowExpireTime IS NOT NULL AND p.windowExpireTime < :currentTime")
    List<PmiRecord> findByBillingModeAndWindowExpireTimeBefore(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找有超额时长的PMI记录
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.overdraftMinutes > 0")
    List<PmiRecord> findRecordsWithOverdraft();

    /**
     * 查找有待扣时长的PMI记录
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.pendingDeductMinutes > 0")
    List<PmiRecord> findRecordsWithPendingDeduct();

    /**
     * 查找可用时长不足的PMI记录
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.billingMode = 'BY_TIME' " +
           "AND p.availableMinutes <= :threshold")
    List<PmiRecord> findRecordsWithLowBalance(@Param("threshold") int threshold);

    /**
     * 查找按时长计费且余额为0的PMI记录
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.billingMode = 'BY_TIME' " +
           "AND p.availableMinutes = 0 AND p.overdraftMinutes = 0")
    List<PmiRecord> findRecordsWithZeroBalance();

    /**
     * 根据窗口ID查找PMI记录
     */
    List<PmiRecord> findByCurrentWindowId(Long windowId);

    /**
     * 统计按计费模式分组的PMI数量
     */
    @Query("SELECT p.billingMode, COUNT(p) FROM PmiRecord p GROUP BY p.billingMode")
    List<Object[]> countByBillingModeGrouped();

    /**
     * 统计按计费状态分组的PMI数量
     */
    @Query("SELECT p.billingStatus, COUNT(p) FROM PmiRecord p GROUP BY p.billingStatus")
    List<Object[]> countByBillingStatusGrouped();

    /**
     * 查找需要计费检查的PMI记录
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.billingStatus = 'ACTIVE' " +
           "AND (p.billingMode = 'BY_TIME' OR " +
           "(p.billingMode = 'LONG' AND p.windowExpireTime IS NOT NULL AND p.windowExpireTime > :currentTime))")
    List<PmiRecord> findRecordsNeedBillingCheck(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找用户的按时长计费PMI记录
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.userId = :userId AND p.billingMode = 'BY_TIME' " +
           "ORDER BY p.createdAt DESC")
    List<PmiRecord> findByTimeRecordsByUserId(@Param("userId") Long userId);

    /**
     * 查找用户的按时段计费PMI记录
     */
    @Query("SELECT p FROM PmiRecord p WHERE p.userId = :userId AND p.billingMode = 'LONG' " +
           "ORDER BY p.createdAt DESC")
    List<PmiRecord> findLongRecordsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的总可用时长
     */
    @Query("SELECT COALESCE(SUM(p.availableMinutes), 0) FROM PmiRecord p " +
           "WHERE p.userId = :userId AND p.billingMode = 'BY_TIME' AND p.billingStatus = 'ACTIVE'")
    long sumAvailableMinutesByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的总使用时长
     */
    @Query("SELECT COALESCE(SUM(p.totalUsedMinutes), 0) FROM PmiRecord p " +
           "WHERE p.userId = :userId AND p.billingStatus = 'ACTIVE'")
    long sumTotalUsedMinutesByUserId(@Param("userId") Long userId);

    /**
     * 使用悲观锁查找PMI记录（防止并发修改）
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT p FROM PmiRecord p WHERE p.id = :id")
    Optional<PmiRecord> findByIdForUpdate(@Param("id") Long id);

    /**
     * 根据状态统计PMI记录数量
     */
    long countByStatus(PmiRecord.PmiStatus status);
}
