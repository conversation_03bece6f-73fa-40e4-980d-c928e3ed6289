package com.zoombus.repository;

import com.zoombus.entity.PmiSchedule;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * PMI计划Repository
 */
@Repository
public interface PmiScheduleRepository extends JpaRepository<PmiSchedule, Long>, JpaSpecificationExecutor<PmiSchedule> {
    
    /**
     * 根据PMI记录ID查找计划
     */
    List<PmiSchedule> findByPmiRecordId(Long pmiRecordId);
    
    /**
     * 根据PMI记录ID分页查找计划
     */
    Page<PmiSchedule> findByPmiRecordId(Long pmiRecordId, Pageable pageable);
    
    /**
     * 根据状态查找计划
     */
    List<PmiSchedule> findByStatus(PmiSchedule.ScheduleStatus status);
    
    /**
     * 查找活跃的计划
     */
    List<PmiSchedule> findByStatusAndStartDateLessThanEqualAndEndDateGreaterThanEqual(
            PmiSchedule.ScheduleStatus status, LocalDate startDate, LocalDate endDate);
    
    /**
     * 根据PMI记录ID和状态查找计划
     */
    List<PmiSchedule> findByPmiRecordIdAndStatus(Long pmiRecordId, PmiSchedule.ScheduleStatus status);
    
    /**
     * 检查PMI记录是否有活跃计划
     */
    boolean existsByPmiRecordIdAndStatus(Long pmiRecordId, PmiSchedule.ScheduleStatus status);
    
    /**
     * 根据名称模糊查询
     */
    @Query("SELECT s FROM PmiSchedule s WHERE s.name LIKE %:keyword%")
    Page<PmiSchedule> findByNameContaining(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据PMI记录ID和名称模糊查询
     */
    @Query("SELECT s FROM PmiSchedule s WHERE s.pmiRecordId = :pmiRecordId AND s.name LIKE %:keyword%")
    Page<PmiSchedule> findByPmiRecordIdAndNameContaining(
            @Param("pmiRecordId") Long pmiRecordId, 
            @Param("keyword") String keyword, 
            Pageable pageable);
    
    /**
     * 统计PMI记录的计划数量
     */
    long countByPmiRecordId(Long pmiRecordId);
    
    /**
     * 统计活跃计划数量
     */
    long countByStatus(PmiSchedule.ScheduleStatus status);
    
    /**
     * 查找今天需要执行的计划
     */
    @Query("SELECT s FROM PmiSchedule s WHERE s.status = com.zoombus.entity.PmiSchedule$ScheduleStatus.ACTIVE " +
           "AND s.startDate <= :today AND s.endDate >= :today")
    List<PmiSchedule> findTodayActiveSchedules(@Param("today") LocalDate today);

    /**
     * 查找指定日期范围内的活跃计划
     */
    @Query("SELECT s FROM PmiSchedule s WHERE s.status = com.zoombus.entity.PmiSchedule$ScheduleStatus.ACTIVE " +
           "AND s.startDate <= :endDate AND s.endDate >= :startDate")
    List<PmiSchedule> findActiveSchedulesInDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    /**
     * 根据PMI记录ID和创建时间查找计划（用于幂等性检查）
     */
    List<PmiSchedule> findByPmiRecordIdAndCreatedAtAfter(Long pmiRecordId, LocalDateTime createdAt);
}
