package com.zoombus.repository;

import com.zoombus.entity.PmiScheduleWindow;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

/**
 * PMI计划窗口Repository
 */
@Repository
public interface PmiScheduleWindowRepository extends JpaRepository<PmiScheduleWindow, Long> {
    
    /**
     * 根据计划ID查找窗口
     */
    List<PmiScheduleWindow>findByScheduleId(Long scheduleId);
    
    /**
     * 根据计划ID分页查找窗口
     */
    Page<PmiScheduleWindow> findByScheduleId(Long scheduleId, Pageable pageable);
    
    /**
     * 根据状态查找窗口
     */
    List<PmiScheduleWindow> findByStatus(PmiScheduleWindow.WindowStatus status);
    
    /**
     * 根据计划ID和状态查找窗口
     */
    List<PmiScheduleWindow> findByScheduleIdAndStatus(Long scheduleId, PmiScheduleWindow.WindowStatus status);
    
    /**
     * 根据开始日期查找窗口
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE CAST(w.startDateTime AS date) = :date")
    List<PmiScheduleWindow> findByWindowDate(@Param("date") LocalDate date);

    /**
     * 根据日期和状态查找窗口
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE CAST(w.startDateTime AS date) = :date AND w.status = :status")
    List<PmiScheduleWindow> findByWindowDateAndStatus(@Param("date") LocalDate date, @Param("status") PmiScheduleWindow.WindowStatus status);
    
    /**
     * 根据Zoom用户ID查找窗口
     */
    List<PmiScheduleWindow> findByZoomUserId(Long zoomUserId);
    
    /**
     * 检查窗口是否存在
     */
    @Query("SELECT COUNT(w) > 0 FROM PmiScheduleWindow w WHERE w.scheduleId = :scheduleId " +
           "AND CAST(w.startDateTime AS date) = :date AND CAST(w.startDateTime AS time) = :time")
    boolean existsByScheduleIdAndWindowDateAndStartTime(
            @Param("scheduleId") Long scheduleId,
            @Param("date") LocalDate date,
            @Param("time") LocalTime time);
    
    /**
     * 查找指定时间范围内的窗口
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE CAST(w.startDateTime AS date) = :date " +
           "AND CAST(w.startDateTime AS time) <= :endTime AND CAST(w.endDateTime AS time) >= :startTime")
    List<PmiScheduleWindow> findWindowsInTimeRange(
            @Param("date") LocalDate date,
            @Param("startTime") LocalTime startTime,
            @Param("endTime") LocalTime endTime);
    

    
    /**
     * 查找今天的所有窗口
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE CAST(w.startDateTime AS date) = :today " +
           "ORDER BY w.startDateTime")
    List<PmiScheduleWindow> findTodayWindows(@Param("today") LocalDate today);
    
    /**
     * 查找指定日期范围内的窗口
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE CAST(w.startDateTime AS date) BETWEEN :startDate AND :endDate " +
           "ORDER BY w.startDateTime")
    List<PmiScheduleWindow> findWindowsInDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
    
    /**
     * 统计计划的窗口数量
     */
    long countByScheduleId(Long scheduleId);
    
    /**
     * 统计指定状态的窗口数量
     */
    long countByStatus(PmiScheduleWindow.WindowStatus status);
    
    /**
     * 统计计划在指定状态的窗口数量
     */
    long countByScheduleIdAndStatus(Long scheduleId, PmiScheduleWindow.WindowStatus status);
    
    /**
     * 删除计划的所有窗口
     */
    void deleteByScheduleId(Long scheduleId);
    
    /**
     * 查找冲突的窗口
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE w.scheduleId != :scheduleId " +
           "AND CAST(w.startDateTime AS date) = :windowDate " +
           "AND ((CAST(w.startDateTime AS time) <= :startTime AND CAST(w.endDateTime AS time) > :startTime) " +
           "OR (CAST(w.startDateTime AS time) < :endTime AND CAST(w.endDateTime AS time) >= :endTime) " +
           "OR (CAST(w.startDateTime AS time) >= :startTime AND CAST(w.endDateTime AS time) <= :endTime))")
    List<PmiScheduleWindow> findConflictingWindows(
            @Param("scheduleId") Long scheduleId,
            @Param("windowDate") LocalDate windowDate,
            @Param("startTime") LocalTime startTime,
            @Param("endTime") LocalTime endTime);

    /**
     * 查找需要开启PMI的窗口（重构：使用新的 startDateTime 字段）
     * 条件：状态为PENDING，且当前时间已到达窗口开启时间
     * 新逻辑：大大简化，直接比较 currentDateTime >= startDateTime
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE w.status = com.zoombus.entity.PmiScheduleWindow$WindowStatus.PENDING " +
           "AND (w.startDateTime IS NOT NULL AND :currentDateTime >= w.startDateTime)")
    List<PmiScheduleWindow> findWindowsToOpenPmi(
            @Param("currentDateTime") LocalDateTime currentDateTime);

    /**
     * 查找需要开启PMI的窗口（兼容旧字段的备用方法）
     * 用于数据迁移期间的兼容性 - 已废弃，保留用于向后兼容
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE w.status = com.zoombus.entity.PmiScheduleWindow$WindowStatus.PENDING " +
           "AND w.startDateTime IS NULL")
    List<PmiScheduleWindow> findWindowsToOpenPmiLegacy(
            @Param("currentDate") LocalDate currentDate,
            @Param("currentTime") LocalTime currentTime);

    /**
     * 查找需要关闭PMI的窗口（重构：使用新的 endDateTime 字段）
     * 条件：状态为ACTIVE，且当前时间超过窗口关闭时间
     * 新逻辑：大大简化，直接比较 currentDateTime >= endDateTime
     * 优先使用新字段，如果没有则回退到旧逻辑
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE w.status = com.zoombus.entity.PmiScheduleWindow$WindowStatus.ACTIVE " +
           "AND (w.endDateTime IS NOT NULL AND :currentDateTime >= w.endDateTime)")
    List<PmiScheduleWindow> findWindowsToClosePmi(
            @Param("currentDateTime") LocalDateTime currentDateTime);

    /**
     * 查找需要关闭PMI的窗口（兼容旧字段的备用方法）
     * 用于数据迁移期间的兼容性 - 已废弃，保留用于向后兼容
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE w.status = com.zoombus.entity.PmiScheduleWindow$WindowStatus.ACTIVE " +
           "AND w.endDateTime IS NULL")
    List<PmiScheduleWindow> findWindowsToClosePmiLegacy(
            @Param("currentDate") LocalDate currentDate,
            @Param("currentTime") LocalTime currentTime);

    /**
     * 查找需要关闭PMI的窗口（调试版本，带详细日志）
     * 重构：使用新的 endDateTime 字段，逻辑大大简化
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE w.status = com.zoombus.entity.PmiScheduleWindow$WindowStatus.ACTIVE " +
           "AND (w.endDateTime IS NOT NULL AND :currentDateTime >= w.endDateTime)")
    List<PmiScheduleWindow> findWindowsToClosePmiDebug(
            @Param("currentDateTime") LocalDateTime currentDateTime);

    /**
     * 根据PMI记录ID和状态查找窗口
     */
    List<PmiScheduleWindow> findByPmiRecordIdAndStatus(Long pmiRecordId, PmiScheduleWindow.WindowStatus status);

    /**
     * 根据PMI记录ID和状态列表查找窗口
     */
    List<PmiScheduleWindow> findByPmiRecordIdAndStatusIn(Long pmiRecordId, List<PmiScheduleWindow.WindowStatus> statuses);

    /**
     * 根据PMI记录ID、日期和状态列表查找窗口
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE w.pmiRecordId = :pmiRecordId " +
           "AND CAST(w.startDateTime AS date) = :date AND w.status IN :statuses")
    List<PmiScheduleWindow> findByPmiRecordIdAndWindowDateAndStatusIn(
            @Param("pmiRecordId") Long pmiRecordId,
            @Param("date") LocalDate date,
            @Param("statuses") List<PmiScheduleWindow.WindowStatus> statuses);

    /**
     * 查找重复窗口
     */
    @Query("SELECT w.pmiRecordId, w.startDateTime, w.endDateTime, COUNT(*) " +
           "FROM PmiScheduleWindow w " +
           "WHERE w.status IN ('PENDING', 'ACTIVE') " +
           "GROUP BY w.pmiRecordId, w.startDateTime, w.endDateTime " +
           "HAVING COUNT(*) > 1")
    List<Object[]> findDuplicateWindows();

    /**
     * 根据PMI记录ID、开始时间、结束时间和状态列表查找窗口
     */
    List<PmiScheduleWindow> findByPmiRecordIdAndStartDateTimeAndEndDateTimeAndStatusIn(
            Long pmiRecordId,
            LocalDateTime startDateTime,
            LocalDateTime endDateTime,
            List<PmiScheduleWindow.WindowStatus> statuses);

    /**
     * 查找没有任务的活跃窗口
     */
    @Query("SELECT w FROM PmiScheduleWindow w WHERE w.status IN ('PENDING', 'ACTIVE') " +
           "AND (w.openTaskId IS NULL OR w.closeTaskId IS NULL)")
    List<PmiScheduleWindow> findWindowsWithoutTasks();
}
