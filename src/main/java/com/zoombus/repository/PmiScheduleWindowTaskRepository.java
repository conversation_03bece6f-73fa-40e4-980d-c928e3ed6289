package com.zoombus.repository;

import com.zoombus.entity.PmiScheduleWindowTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * PMI窗口定时任务Repository
 */
@Repository
public interface PmiScheduleWindowTaskRepository extends JpaRepository<PmiScheduleWindowTask, Long> {
    
    /**
     * 根据PMI窗口ID查找任务
     */
    List<PmiScheduleWindowTask> findByPmiWindowId(Long pmiWindowId);
    
    /**
     * 根据任务状态查找任务
     */
    List<PmiScheduleWindowTask> findByStatus(PmiScheduleWindowTask.TaskStatus status);
    
    /**
     * 根据任务类型查找任务
     */
    List<PmiScheduleWindowTask> findByTaskType(PmiScheduleWindowTask.TaskType taskType);
    
    /**
     * 根据任务键查找任务
     */
    Optional<PmiScheduleWindowTask> findByTaskKey(String taskKey);
    
    /**
     * 查找指定时间范围内的任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.scheduledTime BETWEEN :startTime AND :endTime ORDER BY t.scheduledTime")
    List<PmiScheduleWindowTask> findTasksInTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找即将执行的任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.status = :status AND t.scheduledTime <= :maxTime ORDER BY t.scheduledTime")
    List<PmiScheduleWindowTask> findUpcomingTasks(
            @Param("status") PmiScheduleWindowTask.TaskStatus status,
            @Param("maxTime") LocalDateTime maxTime);
    
    /**
     * 查找过期的任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.status = :status AND t.scheduledTime < :currentTime")
    List<PmiScheduleWindowTask> findExpiredTasks(
            @Param("status") PmiScheduleWindowTask.TaskStatus status,
            @Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 统计任务数量按状态
     */
    @Query("SELECT t.status, COUNT(t) FROM PmiScheduleWindowTask t GROUP BY t.status")
    List<Object[]> countTasksByStatus();
    
    /**
     * 统计任务数量按类型
     */
    @Query("SELECT t.taskType, COUNT(t) FROM PmiScheduleWindowTask t GROUP BY t.taskType")
    List<Object[]> countTasksByType();
    
    /**
     * 删除PMI窗口的所有任务
     */
    void deleteByPmiWindowId(Long pmiWindowId);
    
    /**
     * 查找需要重试的失败任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.status = :status AND t.retryCount < :maxRetryCount")
    List<PmiScheduleWindowTask> findRetryableTasks(
            @Param("status") PmiScheduleWindowTask.TaskStatus status,
            @Param("maxRetryCount") Integer maxRetryCount);
    
    /**
     * 查找指定状态和类型的任务
     */
    List<PmiScheduleWindowTask> findByStatusAndTaskType(
            PmiScheduleWindowTask.TaskStatus status, 
            PmiScheduleWindowTask.TaskType taskType);
    
    /**
     * 查找指定PMI窗口和任务类型的任务
     */
    Optional<PmiScheduleWindowTask> findByPmiWindowIdAndTaskType(
            Long pmiWindowId, 
            PmiScheduleWindowTask.TaskType taskType);
    
    /**
     * 查找最近的任务执行记录
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.actualExecutionTime IS NOT NULL " +
           "ORDER BY t.actualExecutionTime DESC")
    List<PmiScheduleWindowTask> findRecentExecutedTasks();
    
    /**
     * 统计指定时间范围内的任务执行情况
     */
    @Query("SELECT COUNT(t), t.status FROM PmiScheduleWindowTask t " +
           "WHERE t.actualExecutionTime BETWEEN :startTime AND :endTime " +
           "GROUP BY t.status")
    List<Object[]> countTaskExecutionsByStatus(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找长时间运行的任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.status = :status " +
           "AND t.actualExecutionTime < :timeThreshold")
    List<PmiScheduleWindowTask> findLongRunningTasks(
            @Param("status") PmiScheduleWindowTask.TaskStatus status,
            @Param("timeThreshold") LocalDateTime timeThreshold);

    /**
     * 根据状态和任务类型分页查询
     */
    Page<PmiScheduleWindowTask> findByStatusAndTaskType(
            PmiScheduleWindowTask.TaskStatus status,
            PmiScheduleWindowTask.TaskType taskType,
            Pageable pageable);

    /**
     * 根据状态分页查询
     */
    Page<PmiScheduleWindowTask> findByStatus(PmiScheduleWindowTask.TaskStatus status, Pageable pageable);

    /**
     * 根据任务类型分页查询
     */
    Page<PmiScheduleWindowTask> findByTaskType(PmiScheduleWindowTask.TaskType taskType, Pageable pageable);

    /**
     * 统计指定状态的任务数量
     */
    long countByStatus(PmiScheduleWindowTask.TaskStatus status);

    /**
     * 查找有实际执行时间的任务（已执行的任务）
     */
    Page<PmiScheduleWindowTask> findByActualExecutionTimeIsNotNull(Pageable pageable);

    /**
     * 查找可重试的失败任务
     */
    @Query("SELECT t FROM PmiScheduleWindowTask t WHERE t.status = :status " +
           "AND t.retryCount < :maxRetryCount")
    List<PmiScheduleWindowTask> findRetryableTasks(
            @Param("status") PmiScheduleWindowTask.TaskStatus status,
            @Param("maxRetryCount") int maxRetryCount);


}
