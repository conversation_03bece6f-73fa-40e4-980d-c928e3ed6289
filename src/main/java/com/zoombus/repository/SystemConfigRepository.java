package com.zoombus.repository;

import com.zoombus.entity.SystemConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 系统配置Repository接口
 */
@Repository
public interface SystemConfigRepository extends JpaRepository<SystemConfig, Long> {
    
    /**
     * 根据配置键查找配置
     */
    Optional<SystemConfig> findByConfigKey(String configKey);
    
    /**
     * 根据配置键查找有效配置
     */
    @Query("SELECT sc FROM SystemConfig sc WHERE sc.configKey = :configKey AND sc.isActive = true")
    Optional<SystemConfig> findActiveByConfigKey(@Param("configKey") String configKey);
    
    /**
     * 查找所有有效配置
     */
    List<SystemConfig> findByIsActiveTrue();
    
    /**
     * 查找所有无效配置
     */
    List<SystemConfig> findByIsActiveFalse();
    
    /**
     * 根据配置类型查找配置
     */
    List<SystemConfig> findByConfigType(SystemConfig.ConfigType configType);
    
    /**
     * 根据配置键前缀查找配置
     */
    @Query("SELECT sc FROM SystemConfig sc WHERE sc.configKey LIKE :keyPrefix% AND sc.isActive = true ORDER BY sc.configKey")
    List<SystemConfig> findActiveByConfigKeyPrefix(@Param("keyPrefix") String keyPrefix);
    
    /**
     * 根据配置键前缀查找配置（包含无效配置）
     */
    @Query("SELECT sc FROM SystemConfig sc WHERE sc.configKey LIKE :keyPrefix% ORDER BY sc.configKey")
    List<SystemConfig> findByConfigKeyPrefix(@Param("keyPrefix") String keyPrefix);
    
    /**
     * 检查配置键是否存在
     */
    boolean existsByConfigKey(String configKey);
    
    /**
     * 根据配置键模糊搜索
     */
    @Query("SELECT sc FROM SystemConfig sc WHERE sc.configKey LIKE %:keyword% OR sc.description LIKE %:keyword% ORDER BY sc.configKey")
    Page<SystemConfig> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据配置键模糊搜索（仅有效配置）
     */
    @Query("SELECT sc FROM SystemConfig sc WHERE (sc.configKey LIKE %:keyword% OR sc.description LIKE %:keyword%) AND sc.isActive = true ORDER BY sc.configKey")
    Page<SystemConfig> searchActiveByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据创建人查找配置
     */
    List<SystemConfig> findByCreatedBy(String createdBy);
    
    /**
     * 根据更新人查找配置
     */
    List<SystemConfig> findByUpdatedBy(String updatedBy);
    
    /**
     * 查找最近创建的配置
     */
    @Query("SELECT sc FROM SystemConfig sc ORDER BY sc.createdAt DESC")
    Page<SystemConfig> findRecentlyCreated(Pageable pageable);
    
    /**
     * 查找最近更新的配置
     */
    @Query("SELECT sc FROM SystemConfig sc ORDER BY sc.updatedAt DESC")
    Page<SystemConfig> findRecentlyUpdated(Pageable pageable);
    
    /**
     * 统计配置数量
     */
    @Query("SELECT COUNT(sc) FROM SystemConfig sc WHERE sc.isActive = true")
    long countActiveConfigs();
    
    @Query("SELECT COUNT(sc) FROM SystemConfig sc WHERE sc.isActive = false")
    long countInactiveConfigs();
    
    /**
     * 根据配置类型统计数量
     */
    @Query("SELECT sc.configType, COUNT(sc) FROM SystemConfig sc WHERE sc.isActive = true GROUP BY sc.configType")
    List<Object[]> countByConfigType();
}
