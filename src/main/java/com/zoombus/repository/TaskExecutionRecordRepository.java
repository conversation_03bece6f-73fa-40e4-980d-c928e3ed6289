package com.zoombus.repository;

import com.zoombus.entity.TaskExecutionRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 任务执行记录仓库
 */
@Repository
public interface TaskExecutionRecordRepository extends JpaRepository<TaskExecutionRecord, Long> {
    
    /**
     * 根据任务名称查找最新的执行记录
     */
    Optional<TaskExecutionRecord> findFirstByTaskNameOrderByExecutionTimeDesc(String taskName);
    
    /**
     * 根据任务名称和状态查找记录
     */
    List<TaskExecutionRecord> findByTaskNameAndStatus(String taskName, TaskExecutionRecord.ExecutionStatus status);
    
    /**
     * 查找正在运行的任务
     */
    @Query("SELECT t FROM TaskExecutionRecord t WHERE t.status IN ('RUNNING', 'RETRYING')")
    List<TaskExecutionRecord> findRunningTasks();
    
    /**
     * 查找需要重试的任务
     */
    @Query("SELECT t FROM TaskExecutionRecord t WHERE t.status = 'RETRYING' AND t.nextRetryTime <= :currentTime")
    List<TaskExecutionRecord> findTasksNeedingRetry(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 查找指定时间范围内的执行记录
     */
    @Query("SELECT t FROM TaskExecutionRecord t WHERE t.executionTime BETWEEN :startTime AND :endTime ORDER BY t.executionTime DESC")
    Page<TaskExecutionRecord> findByExecutionTimeBetween(
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime, 
        Pageable pageable
    );
    
    /**
     * 查找指定任务名称的执行历史
     */
    Page<TaskExecutionRecord> findByTaskNameOrderByExecutionTimeDesc(String taskName, Pageable pageable);

    /**
     * 查找指定任务在指定时间范围内的执行记录
     */
    @Query("SELECT t FROM TaskExecutionRecord t WHERE t.taskName = :taskName AND t.executionTime BETWEEN :startTime AND :endTime ORDER BY t.executionTime DESC")
    List<TaskExecutionRecord> findByTaskNameAndExecutionTimeBetween(
        @Param("taskName") String taskName,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 查找指定任务在指定时间范围内的执行记录（分页）
     */
    @Query("SELECT t FROM TaskExecutionRecord t WHERE t.taskName = :taskName AND t.executionTime BETWEEN :startTime AND :endTime ORDER BY t.executionTime DESC")
    Page<TaskExecutionRecord> findByTaskNameAndExecutionTimeBetween(
        @Param("taskName") String taskName,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime,
        Pageable pageable);
    
    /**
     * 统计指定时间范围内的任务执行情况
     */
    @Query("SELECT t.status, COUNT(t) FROM TaskExecutionRecord t WHERE t.executionTime BETWEEN :startTime AND :endTime GROUP BY t.status")
    List<Object[]> countByStatusInTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定任务的执行情况
     */
    @Query("SELECT t.status, COUNT(t) FROM TaskExecutionRecord t WHERE t.taskName = :taskName AND t.executionTime BETWEEN :startTime AND :endTime GROUP BY t.status")
    List<Object[]> countByTaskNameAndStatusInTimeRange(
        @Param("taskName") String taskName,
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime
    );
    
    /**
     * 查找长时间运行的任务
     */
    @Query("SELECT t FROM TaskExecutionRecord t WHERE t.status IN ('RUNNING', 'RETRYING') AND t.executionTime < :timeThreshold")
    List<TaskExecutionRecord> findLongRunningTasks(@Param("timeThreshold") LocalDateTime timeThreshold);
    
    /**
     * 查找失败次数较多的任务
     */
    @Query("SELECT t.taskName, COUNT(t) as failureCount FROM TaskExecutionRecord t WHERE t.status = 'FAILED' AND t.executionTime >= :since GROUP BY t.taskName HAVING COUNT(t) >= :threshold ORDER BY failureCount DESC")
    List<Object[]> findFrequentlyFailingTasks(@Param("since") LocalDateTime since, @Param("threshold") long threshold);
    
    /**
     * 清理过期的执行记录
     */
    @Modifying
    @Query("DELETE FROM TaskExecutionRecord t WHERE t.executionTime < :cutoffTime AND t.status IN ('SUCCESS', 'FAILED', 'RETRY_EXHAUSTED', 'CANCELLED')")
    int deleteOldRecords(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 更新任务状态
     */
    @Modifying
    @Query("UPDATE TaskExecutionRecord t SET t.status = :newStatus, t.completionTime = :completionTime WHERE t.id = :id")
    int updateTaskStatus(@Param("id") Long id, @Param("newStatus") TaskExecutionRecord.ExecutionStatus newStatus, @Param("completionTime") LocalDateTime completionTime);
    
    /**
     * 检查任务是否正在运行
     */
    @Query("SELECT COUNT(t) > 0 FROM TaskExecutionRecord t WHERE t.taskName = :taskName AND t.status IN ('RUNNING', 'RETRYING')")
    boolean isTaskRunning(@Param("taskName") String taskName);
    
    /**
     * 获取任务的平均执行时间
     */
    @Query("SELECT AVG(t.durationMs) FROM TaskExecutionRecord t WHERE t.taskName = :taskName AND t.status = 'SUCCESS' AND t.executionTime >= :since")
    Double getAverageExecutionTime(@Param("taskName") String taskName, @Param("since") LocalDateTime since);
    
    /**
     * 获取任务的成功率
     */
    @Query("SELECT " +
           "CAST(SUM(CASE WHEN t.status = 'SUCCESS' THEN 1 ELSE 0 END) AS double) / COUNT(t) * 100 " +
           "FROM TaskExecutionRecord t WHERE t.taskName = :taskName AND t.executionTime >= :since")
    Double getTaskSuccessRate(@Param("taskName") String taskName, @Param("since") LocalDateTime since);

    // 添加缺失的方法别名
    default void deleteByExecutionTimeBefore(LocalDateTime cutoffTime) {
        deleteOldRecords(cutoffTime);
    }

    default List<Object[]> countByStatusAndExecutionTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return countByStatusInTimeRange(startTime, endTime);
    }

    default List<Object[]> countByTaskNameAndStatusAndExecutionTimeBetween(String taskName, LocalDateTime startTime, LocalDateTime endTime) {
        return countByTaskNameAndStatusInTimeRange(taskName, startTime, endTime);
    }

    default List<Object[]> findTasksWithHighFailureRate(LocalDateTime startTime, long threshold) {
        return findFrequentlyFailingTasks(startTime, threshold);
    }

    default List<Object[]> findTasksWithLongestAverageExecutionTime(LocalDateTime startTime) {
        // 这个方法需要实现，暂时返回空列表
        return java.util.Collections.emptyList();
    }

    // 批量操作方法

    /**
     * 批量更新任务状态
     */
    @Modifying
    @Query("UPDATE TaskExecutionRecord t SET t.status = :status WHERE t.id IN :ids")
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") TaskExecutionRecord.ExecutionStatus status);

    /**
     * 批量删除过期记录
     */
    @Modifying
    @Query(value = "DELETE FROM task_execution_record WHERE execution_time < :cutoffTime LIMIT :batchSize", nativeQuery = true)
    int deleteExpiredRecordsBatch(@Param("cutoffTime") LocalDateTime cutoffTime, @Param("batchSize") int batchSize);

    /**
     * 根据记录ID查找任务名称
     */
    @Query("SELECT DISTINCT t.taskName FROM TaskExecutionRecord t WHERE t.id IN :ids")
    List<String> findTaskNamesByIds(@Param("ids") List<Long> ids);

    /**
     * 批量获取任务统计信息
     */
    @Query("SELECT t.taskName, t.status, COUNT(t) FROM TaskExecutionRecord t WHERE t.taskName IN :taskNames AND t.executionTime BETWEEN :startTime AND :endTime GROUP BY t.taskName, t.status")
    List<Object[]> batchGetTaskStatistics(
            @Param("taskNames") List<String> taskNames,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
}
