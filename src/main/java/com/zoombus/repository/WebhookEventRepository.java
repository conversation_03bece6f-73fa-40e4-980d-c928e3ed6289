package com.zoombus.repository;

import com.zoombus.entity.WebhookEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface WebhookEventRepository extends JpaRepository<WebhookEvent, Long> {
    
    List<WebhookEvent> findByEventType(String eventType);

    List<WebhookEvent> findByProcessingStatus(WebhookEvent.ProcessingStatus processingStatus);

    List<WebhookEvent> findByZoomAccountId(String zoomAccountId);

    List<WebhookEvent> findByZoomMeetingId(String zoomMeetingId);

    List<WebhookEvent> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<WebhookEvent> findByEventTypeAndProcessingStatus(String eventType, WebhookEvent.ProcessingStatus processingStatus);

    /**
     * 获取所有事件，按创建时间倒序排列（最新的在前）
     */
    List<WebhookEvent> findAllByOrderByCreatedAtDesc();

    /**
     * 根据处理状态获取事件，按创建时间倒序排列
     */
    List<WebhookEvent> findByProcessingStatusOrderByCreatedAtDesc(WebhookEvent.ProcessingStatus processingStatus);

    /**
     * 根据Zoom账号ID获取事件，按创建时间倒序排列
     */
    List<WebhookEvent> findByZoomAccountIdOrderByCreatedAtDesc(String zoomAccountId);

    /**
     * 根据事件类型获取事件，按创建时间倒序排列
     */
    List<WebhookEvent> findByEventTypeOrderByCreatedAtDesc(String eventType);



    /**
     * 获取最近的10个事件
     */
    List<WebhookEvent> findTop10ByOrderByCreatedAtDesc();

    /**
     * 根据Zoom账号ID和处理状态查找事件
     */
    List<WebhookEvent> findByZoomAccountIdAndProcessingStatus(String zoomAccountId, WebhookEvent.ProcessingStatus processingStatus);

    /**
     * 根据Zoom账号ID和事件类型查找事件
     */
    List<WebhookEvent> findByZoomAccountIdAndEventType(String zoomAccountId, String eventType);
}
