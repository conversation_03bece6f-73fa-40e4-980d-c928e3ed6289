package com.zoombus.repository;

import com.zoombus.entity.ZoomApiLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Zoom API日志Repository
 */
@Repository
public interface ZoomApiLogRepository extends JpaRepository<ZoomApiLog, Long> {
    
    /**
     * 根据请求ID查找日志
     */
    Optional<ZoomApiLog> findByRequestId(String requestId);
    
    /**
     * 根据业务类型查找日志
     */
    Page<ZoomApiLog> findByBusinessType(String businessType, Pageable pageable);
    
    /**
     * 根据业务ID查找日志
     */
    Page<ZoomApiLog> findByBusinessId(String businessId, Pageable pageable);
    
    /**
     * 根据ZoomUser ID查找日志
     */
    Page<ZoomApiLog> findByZoomUserId(String zoomUserId, Pageable pageable);
    
    /**
     * 根据成功状态查找日志
     */
    Page<ZoomApiLog> findByIsSuccess(Boolean isSuccess, Pageable pageable);
    
    /**
     * 根据时间范围查找日志
     */
    Page<ZoomApiLog> findByRequestTimeBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据API路径查找日志
     */
    Page<ZoomApiLog> findByApiPathContaining(String apiPath, Pageable pageable);
    
    /**
     * 根据响应状态码查找日志
     */
    Page<ZoomApiLog> findByResponseStatus(Integer responseStatus, Pageable pageable);
    
    /**
     * 查找慢请求（耗时超过指定毫秒数）
     */
    Page<ZoomApiLog> findByDurationMsGreaterThan(Long durationMs, Pageable pageable);
    
    /**
     * 复合查询 - 根据多个条件查找日志
     */
    @Query("SELECT l FROM ZoomApiLog l WHERE " +
           "(:businessType IS NULL OR l.businessType = :businessType) AND " +
           "(:isSuccess IS NULL OR l.isSuccess = :isSuccess) AND " +
           "(:apiPath IS NULL OR l.apiPath LIKE %:apiPath%) AND " +
           "(:zoomUserId IS NULL OR l.zoomUserId = :zoomUserId) AND " +
           "(:startTime IS NULL OR l.requestTime >= :startTime) AND " +
           "(:endTime IS NULL OR l.requestTime <= :endTime) AND " +
           "(:minDuration IS NULL OR l.durationMs >= :minDuration) AND " +
           "(:maxDuration IS NULL OR l.durationMs <= :maxDuration)")
    Page<ZoomApiLog> findByConditions(
            @Param("businessType") String businessType,
            @Param("isSuccess") Boolean isSuccess,
            @Param("apiPath") String apiPath,
            @Param("zoomUserId") String zoomUserId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("minDuration") Long minDuration,
            @Param("maxDuration") Long maxDuration,
            Pageable pageable);
    
    /**
     * 统计查询 - 按业务类型统计
     */
    @Query("SELECT l.businessType, COUNT(l), " +
           "SUM(CASE WHEN l.isSuccess = true THEN 1 ELSE 0 END), " +
           "AVG(l.durationMs), MAX(l.durationMs), MIN(l.durationMs) " +
           "FROM ZoomApiLog l " +
           "WHERE l.requestTime >= :startTime AND l.requestTime <= :endTime " +
           "GROUP BY l.businessType")
    List<Object[]> getStatsByBusinessType(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计查询 - 按小时统计
     */
    @Query("SELECT FUNCTION('DATE_FORMAT', l.requestTime, '%Y-%m-%d %H:00:00'), COUNT(l), " +
           "SUM(CASE WHEN l.isSuccess = true THEN 1 ELSE 0 END), " +
           "AVG(l.durationMs) " +
           "FROM ZoomApiLog l " +
           "WHERE l.requestTime >= :startTime AND l.requestTime <= :endTime " +
           "GROUP BY FUNCTION('DATE_FORMAT', l.requestTime, '%Y-%m-%d %H:00:00') " +
           "ORDER BY FUNCTION('DATE_FORMAT', l.requestTime, '%Y-%m-%d %H:00:00')")
    List<Object[]> getStatsByHour(@Param("startTime") LocalDateTime startTime, 
                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计查询 - 按响应状态码统计
     */
    @Query("SELECT l.responseStatus, COUNT(l) " +
           "FROM ZoomApiLog l " +
           "WHERE l.requestTime >= :startTime AND l.requestTime <= :endTime " +
           "GROUP BY l.responseStatus " +
           "ORDER BY COUNT(l) DESC")
    List<Object[]> getStatsByResponseStatus(@Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取错误日志统计
     */
    @Query("SELECT l.errorCode, COUNT(l), l.errorMessage " +
           "FROM ZoomApiLog l " +
           "WHERE l.isSuccess = false AND l.requestTime >= :startTime AND l.requestTime <= :endTime " +
           "GROUP BY l.errorCode, l.errorMessage " +
           "ORDER BY COUNT(l) DESC")
    List<Object[]> getErrorStats(@Param("startTime") LocalDateTime startTime, 
                                 @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取慢请求统计
     */
    @Query("SELECT l.apiPath, COUNT(l), AVG(l.durationMs), MAX(l.durationMs) " +
           "FROM ZoomApiLog l " +
           "WHERE l.durationMs > :minDuration AND l.requestTime >= :startTime AND l.requestTime <= :endTime " +
           "GROUP BY l.apiPath " +
           "ORDER BY AVG(l.durationMs) DESC")
    List<Object[]> getSlowRequestStats(@Param("minDuration") Long minDuration,
                                       @Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取总体统计信息
     */
    @Query("SELECT COUNT(l), " +
           "SUM(CASE WHEN l.isSuccess = true THEN 1 ELSE 0 END), " +
           "AVG(l.durationMs), " +
           "MAX(l.durationMs), " +
           "MIN(l.durationMs), " +
           "SUM(CASE WHEN l.durationMs > 5000 THEN 1 ELSE 0 END) " +
           "FROM ZoomApiLog l " +
           "WHERE l.requestTime >= :startTime AND l.requestTime <= :endTime")
    Object[] getOverallStats(@Param("startTime") LocalDateTime startTime, 
                            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的日志（用于日志清理）
     */
    void deleteByRequestTimeBefore(LocalDateTime cutoffTime);
    
    /**
     * 统计指定时间范围内的日志数量
     */
    long countByRequestTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
}
