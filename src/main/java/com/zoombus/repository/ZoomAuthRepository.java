package com.zoombus.repository;

import com.zoombus.entity.ZoomAuth;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ZoomAuthRepository extends JpaRepository<ZoomAuth, Long> {
    
    /**
     * 根据账号名称查找
     */
    Optional<ZoomAuth> findByAccountName(String accountName);
    
    /**
     * 根据Zoom账号ID查找
     */
    Optional<ZoomAuth> findByZoomAccountId(String zoomAccountId);
    
    /**
     * 根据客户端ID查找
     */
    Optional<ZoomAuth> findByClientId(String clientId);

    /**
     * 根据主账号邮箱查找
     */
    Optional<ZoomAuth> findByPrimaryEmail(String primaryEmail);

    /**
     * 检查账号名称是否存在
     */
    boolean existsByAccountName(String accountName);
    
    /**
     * 检查Zoom账号ID是否存在
     */
    boolean existsByZoomAccountId(String zoomAccountId);
    
    /**
     * 检查客户端ID是否存在
     */
    boolean existsByClientId(String clientId);

    /**
     * 检查主账号邮箱是否存在
     */
    boolean existsByPrimaryEmail(String primaryEmail);

    /**
     * 根据状态查找
     */
    List<ZoomAuth> findByStatus(ZoomAuth.AuthStatus status);
    
    /**
     * 根据认证类型查找
     */
    List<ZoomAuth> findByAuthType(ZoomAuth.AuthType authType);
    
    /**
     * 根据状态分页查找
     */
    Page<ZoomAuth> findByStatus(ZoomAuth.AuthStatus status, Pageable pageable);
    
    /**
     * 根据认证类型分页查找
     */
    Page<ZoomAuth> findByAuthType(ZoomAuth.AuthType authType, Pageable pageable);
    
    /**
     * 查找所有活跃的认证信息
     */
    List<ZoomAuth> findByStatusOrderByCreatedAtDesc(ZoomAuth.AuthStatus status);
    
    /**
     * 查找token即将过期的认证信息
     */
    @Query("SELECT za FROM ZoomAuth za WHERE za.status = :status AND za.tokenExpiresAt <= :expiryTime")
    List<ZoomAuth> findTokensExpiringSoon(@Param("status") ZoomAuth.AuthStatus status, 
                                         @Param("expiryTime") LocalDateTime expiryTime);
    
    /**
     * 查找已过期的token
     */
    @Query("SELECT za FROM ZoomAuth za WHERE za.status = :status AND za.tokenExpiresAt <= :currentTime")
    List<ZoomAuth> findExpiredTokens(@Param("status") ZoomAuth.AuthStatus status, 
                                    @Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 查找需要刷新的token（即将过期或已过期的活跃认证）
     */
    @Query("SELECT za FROM ZoomAuth za WHERE za.status = 'ACTIVE' AND " +
           "(za.tokenExpiresAt IS NULL OR za.tokenExpiresAt <= :refreshTime)")
    List<ZoomAuth> findTokensNeedingRefresh(@Param("refreshTime") LocalDateTime refreshTime);
    
    /**
     * 根据账号名称模糊查询
     */
    Page<ZoomAuth> findByAccountNameContainingIgnoreCase(String accountName, Pageable pageable);
    
    /**
     * 根据描述模糊查询
     */
    Page<ZoomAuth> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
    
    /**
     * 组合查询：根据状态和账号名称
     */
    Page<ZoomAuth> findByStatusAndAccountNameContainingIgnoreCase(
            ZoomAuth.AuthStatus status, String accountName, Pageable pageable);
    
    /**
     * 获取默认的认证信息（第一个活跃的）
     */
    @Query(value = "SELECT * FROM t_zoom_auth WHERE status = 'ACTIVE' ORDER BY created_at ASC LIMIT 1", nativeQuery = true)
    Optional<ZoomAuth> findFirstActiveAuth();
    
    /**
     * 统计各状态的认证数量
     */
    @Query("SELECT za.status, COUNT(za) FROM ZoomAuth za GROUP BY za.status")
    List<Object[]> countByStatus();
    
    /**
     * 统计各认证类型的数量
     */
    @Query("SELECT za.authType, COUNT(za) FROM ZoomAuth za GROUP BY za.authType")
    List<Object[]> countByAuthType();
}
