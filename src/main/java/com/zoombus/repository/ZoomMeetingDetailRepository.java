package com.zoombus.repository;

import com.zoombus.entity.ZoomMeetingDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ZoomMeetingDetailRepository extends JpaRepository<ZoomMeetingDetail, Long> {
    
    /**
     * 根据会议ID查找Zoom会议详情列表（支持一对多关系，如周期性会议）
     */
    List<ZoomMeetingDetail> findByMeetingId(Long meetingId);

    /**
     * 根据会议ID查找未删除的Zoom会议详情列表（智能过滤场次记录）
     * 逻辑：
     * 1. 如果有occurrence_start_time不为空的记录，只返回这些记录（周期性会议的具体场次）
     * 2. 如果没有occurrence_start_time不为空的记录，返回主记录（单场次会议）
     */
    @Query("SELECT zmd FROM ZoomMeetingDetail zmd WHERE zmd.meetingId = :meetingId AND (zmd.occurrenceStatus IS NULL OR zmd.occurrenceStatus != 'deleted') AND zmd.occurrenceStartTime IS NOT NULL")
    List<ZoomMeetingDetail> findActiveByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 根据会议ID查找第一个Zoom会议详情（兼容旧代码）
     */
    @Query(value = "SELECT * FROM t_zoom_meeting_details WHERE meeting_id = :meetingId ORDER BY local_created_at ASC LIMIT 1", nativeQuery = true)
    Optional<ZoomMeetingDetail> findFirstByMeetingId(@Param("meetingId") Long meetingId);
    
    /**
     * 根据Zoom会议ID查找详情
     */
    Optional<ZoomMeetingDetail> findByZoomMeetingId(String zoomMeetingId);

    /**
     * 根据会议ID和occurrence ID查找特定的occurrence记录
     */
    Optional<ZoomMeetingDetail> findByMeetingIdAndOccurrenceId(Long meetingId, String occurrenceId);
    
    /**
     * 根据主持人ID查找会议详情列表
     */
    List<ZoomMeetingDetail> findByHostId(String hostId);
    
    /**
     * 根据主持人邮箱查找会议详情列表
     */
    List<ZoomMeetingDetail> findByHostEmail(String hostEmail);
    
    /**
     * 根据会议状态查找详情列表
     */
    List<ZoomMeetingDetail> findByStatus(String status);
    
    /**
     * 根据会议类型查找详情列表（已废弃，type字段已迁移到Meeting表）
     * 请使用 Meeting 表的查询方法
     */
    @Deprecated
    List<ZoomMeetingDetail> findByType(Integer type);
    
    /**
     * 检查指定会议ID是否已有Zoom详情记录
     */
    boolean existsByMeetingId(Long meetingId);
    
    /**
     * 检查指定Zoom会议ID是否已存在
     */
    boolean existsByZoomMeetingId(String zoomMeetingId);
    
    /**
     * 根据多个会议ID批量查找Zoom会议详情
     */
    @Query("SELECT zmd FROM ZoomMeetingDetail zmd WHERE zmd.meetingId IN :meetingIds")
    List<ZoomMeetingDetail> findByMeetingIdIn(@Param("meetingIds") List<Long> meetingIds);
    
    /**
     * 删除指定会议ID的Zoom详情记录
     */
    void deleteByMeetingId(Long meetingId);
    
    /**
     * 根据Zoom会议ID删除详情记录
     */
    void deleteByZoomMeetingId(String zoomMeetingId);

    /**
     * 获取最近的会议详情（分页，按ID倒序）
     */
    Page<ZoomMeetingDetail> findAllByOrderByIdDesc(Pageable pageable);

    /**
     * 获取最近一周的会议详情（按开始时间正序，优先展示最快开始的会议，排除周期性会议主记录）
     * 时间范围：当日0点0分至之后的7天时间内
     * 包含Meeting表信息以获取已迁移的字段
     */
    @Query("SELECT zmd, zmd.occurrenceStartTime, m FROM ZoomMeetingDetail zmd " +
           "LEFT JOIN Meeting m ON zmd.meetingId = m.id " +
           "WHERE NOT (m.type = 8 AND zmd.isMainOccurrence = true) " +
           "AND (zmd.occurrenceStartTime >= :startTime AND zmd.occurrenceStartTime <= :endTime) " +
           "ORDER BY CASE " +
           "  WHEN zmd.occurrenceStartTime IS NULL THEN 1 " +
           "  ELSE 0 " +
           "END, " +
           "zmd.occurrenceStartTime ASC")
    Page<Object[]> findRecentWeekMeetingsWithTime(@Param("startTime") java.time.LocalDateTime startTime,
                                                 @Param("endTime") java.time.LocalDateTime endTime,
                                                 Pageable pageable);
}
