package com.zoombus.repository;

import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import javax.persistence.LockModeType;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Zoom会议Repository接口
 */
@Repository
public interface ZoomMeetingRepository extends JpaRepository<ZoomMeeting, Long> {
    
    /**
     * 根据Zoom会议UUID查找会议
     */
    Optional<ZoomMeeting> findByZoomMeetingUuid(String zoomMeetingUuid);

    /**
     * 通过PMI号码和主持人ID查找WAITING状态的会议记录
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.zoomMeetingId = :meetingId AND zm.hostId = :hostId AND zm.status = 'WAITING'")
    Optional<ZoomMeeting> findWaitingMeetingByMeetingIdAndHostId(@Param("meetingId") String meetingId,
                                                                @Param("hostId") String hostId);

    /**
     * 通过PMI号码查找活跃状态的会议记录（WAITING或STARTED）
     * 用于防止同一PMI同时开启多场会议
     * 只返回最新的一条活跃记录
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.zoomMeetingId = :pmiNumber AND zm.status IN ('WAITING', 'STARTED') ORDER BY zm.createdAt DESC")
    Optional<ZoomMeeting> findActiveMeetingByPmiNumber(@Param("pmiNumber") String pmiNumber);

    /**
     * 检查PMI是否有活跃的会议记录
     */
    @Query("SELECT COUNT(zm) FROM ZoomMeeting zm WHERE zm.zoomMeetingId = :pmiNumber AND zm.status IN ('WAITING', 'STARTED')")
    long countActiveMeetingsByPmiNumber(@Param("pmiNumber") String pmiNumber);

    /**
     * 查找超过指定时间的临时PMI会议
     * 用于定期清理临时创建的PMI会议
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.createdAt < :cutoffTime AND zm.topic LIKE 'PMI会议 -%' AND zm.topic LIKE '%(临时)%' AND zm.status = 'WAITING' ORDER BY zm.createdAt ASC")
    List<ZoomMeeting> findTemporaryPmiMeetingsOlderThan(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 根据Zoom会议ID查找会议
     */
    List<ZoomMeeting> findByZoomMeetingId(String zoomMeetingId);
    
    /**
     * 根据PMI记录ID查找会议
     */
    List<ZoomMeeting> findByPmiRecordId(Long pmiRecordId);

    /**
     * 根据状态查找会议
     */
    List<ZoomMeeting> findByStatus(ZoomMeeting.MeetingStatus status);

    /**
     * 根据meetingId、hostId和状态列表查找会议（用于Webhook处理）
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.zoomMeetingId = :meetingId " +
           "AND zm.hostId = :hostId AND zm.status IN :statuses " +
           "ORDER BY zm.createdAt DESC")
    List<ZoomMeeting> findByZoomMeetingIdAndHostIdAndStatusIn(
        @Param("meetingId") String meetingId,
        @Param("hostId") String hostId,
        @Param("statuses") List<ZoomMeeting.MeetingStatus> statuses
    );
    
    /**
     * 查找正在进行中的会议
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.status = 'STARTED'")
    List<ZoomMeeting> findActiveMeetings();
    
    /**
     * 根据PMI记录ID和状态查找会议
     */
    List<ZoomMeeting> findByPmiRecordIdAndStatus(Long pmiRecordId, ZoomMeeting.MeetingStatus status);

    /**
     * 根据PMI记录ID和状态列表查找会议（返回第一个匹配的）
     */
    Optional<ZoomMeeting> findFirstByPmiRecordIdAndStatusInOrderByCreatedAtDesc(Long pmiRecordId, List<ZoomMeeting.MeetingStatus> statuses);
    
    /**
     * 查找指定时间范围内的会议
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.startTime >= :startTime AND zm.startTime <= :endTime")
    List<ZoomMeeting> findByStartTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找未结算的会议
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.isSettled = false AND zm.status IN ('ENDED', 'SETTLED')")
    List<ZoomMeeting> findUnsettledMeetings();
    
    /**
     * 根据分配的ZoomUser查找会议
     */
    List<ZoomMeeting> findByAssignedZoomUserId(Long assignedZoomUserId);
    
    /**
     * 查找活跃会议（分页）- 包含WAITING和STARTED状态
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.status IN ('WAITING', 'STARTED') ORDER BY zm.startTime DESC")
    Page<ZoomMeeting> findActiveMeetings(Pageable pageable);

    /**
     * 查询需要状态同步的活跃会议
     * 包括WAITING和STARTED状态的会议，且有有效的Zoom会议ID
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.status IN ('WAITING', 'STARTED') " +
           "AND zm.zoomMeetingId IS NOT NULL " +
           "AND zm.zoomMeetingId != '' " +
           "AND zm.zoomMeetingId NOT LIKE 'test-%' " +
           "AND zm.zoomMeetingId NOT LIKE 'pending-%' " +
           "ORDER BY zm.updatedAt ASC")
    List<ZoomMeeting> findActiveMeetingsForSync();

    /**
     * 使用悲观锁查询会议记录，避免并发冲突
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.id = :id")
    Optional<ZoomMeeting> findByIdWithLock(@Param("id") Long id);

    /**
     * 查找历史会议（分页）- 包含ENDED和SETTLED状态
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.status IN ('ENDED', 'SETTLED') ORDER BY zm.startTime DESC")
    Page<ZoomMeeting> findHistoryMeetings(Pageable pageable);
    
    /**
     * 根据计费模式查找会议（分页）
     */
    Page<ZoomMeeting> findByBillingMode(PmiRecord.BillingMode billingMode, Pageable pageable);
    
    /**
     * 根据关键词搜索会议（分页）
     */
    @Query("SELECT zm FROM ZoomMeeting zm " +
           "LEFT JOIN PmiRecord pr ON zm.pmiRecordId = pr.id " +
           "WHERE (zm.topic LIKE %:keyword% OR pr.pmiNumber LIKE %:keyword%) " +
           "ORDER BY zm.startTime DESC")
    Page<ZoomMeeting> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 复合条件查询活跃会议 - 包含WAITING和STARTED状态
     */
    @Query("SELECT zm FROM ZoomMeeting zm " +
           "LEFT JOIN PmiRecord pr ON zm.pmiRecordId = pr.id " +
           "WHERE zm.status IN ('WAITING', 'STARTED') " +
           "AND (:billingMode IS NULL OR zm.billingMode = :billingMode) " +
           "AND (:keyword IS NULL OR :keyword = '' OR zm.topic LIKE %:keyword% OR pr.pmiNumber LIKE %:keyword%) " +
           "ORDER BY zm.startTime DESC")
    Page<ZoomMeeting> findActiveMeetingsWithFilters(@Param("billingMode") PmiRecord.BillingMode billingMode,
                                                   @Param("keyword") String keyword,
                                                   Pageable pageable);
    
    /**
     * 复合条件查询历史会议
     */
    @Query("SELECT zm FROM ZoomMeeting zm " +
           "LEFT JOIN PmiRecord pr ON zm.pmiRecordId = pr.id " +
           "WHERE zm.status IN ('ENDED', 'SETTLED') " +
           "AND (:billingMode IS NULL OR zm.billingMode = :billingMode) " +
           "AND (:keyword IS NULL OR :keyword = '' OR zm.topic LIKE %:keyword% OR pr.pmiNumber LIKE %:keyword%) " +
           "AND (:startDate IS NULL OR zm.startTime >= :startDate) " +
           "AND (:endDate IS NULL OR zm.startTime <= :endDate) " +
           "ORDER BY zm.startTime DESC")
    Page<ZoomMeeting> findHistoryMeetingsWithFilters(@Param("billingMode") PmiRecord.BillingMode billingMode,
                                                    @Param("keyword") String keyword,
                                                    @Param("startDate") LocalDateTime startDate,
                                                    @Param("endDate") LocalDateTime endDate,
                                                    Pageable pageable);
    
    /**
     * 统计指定PMI的会议数量
     */
    @Query("SELECT COUNT(zm) FROM ZoomMeeting zm WHERE zm.pmiRecordId = :pmiRecordId")
    long countByPmiRecordId(@Param("pmiRecordId") Long pmiRecordId);
    
    /**
     * 统计指定PMI的总使用时长
     */
    @Query("SELECT COALESCE(SUM(zm.durationMinutes), 0) FROM ZoomMeeting zm " +
           "WHERE zm.pmiRecordId = :pmiRecordId AND zm.status IN ('ENDED', 'SETTLED')")
    long sumDurationMinutesByPmiRecordId(@Param("pmiRecordId") Long pmiRecordId);
    
    /**
     * 查找需要结算的会议
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.status = 'ENDED' AND zm.isSettled = false")
    List<ZoomMeeting> findMeetingsNeedSettlement();

    /**
     * 统计指定状态下的不同PMI记录数量
     */
    @Query("SELECT COUNT(DISTINCT zm.pmiRecordId) FROM ZoomMeeting zm WHERE zm.status = :status AND zm.pmiRecordId IS NOT NULL")
    long countDistinctPmiRecordIdByStatus(@Param("status") ZoomMeeting.MeetingStatus status);

    /**
     * 根据PMI记录ID和状态查找活跃会议（返回单个结果）
     */
    @Query("SELECT zm FROM ZoomMeeting zm WHERE zm.pmiRecordId = :pmiRecordId AND zm.status = :status")
    Optional<ZoomMeeting> findActiveMeetingByPmiRecordIdAndStatus(@Param("pmiRecordId") Long pmiRecordId, @Param("status") ZoomMeeting.MeetingStatus status);
}
