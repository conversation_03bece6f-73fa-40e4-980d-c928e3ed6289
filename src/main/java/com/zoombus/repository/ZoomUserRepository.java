package com.zoombus.repository;

import com.zoombus.entity.ZoomAuth;
import com.zoombus.entity.ZoomUser;
import com.zoombus.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ZoomUserRepository extends JpaRepository<ZoomUser, Long> {
    
    /**
     * 根据ZoomAuth和ZoomUserId查找用户
     */
    Optional<ZoomUser> findByZoomAuthAndZoomUserId(ZoomAuth zoomAuth, String zoomUserId);

    /**
     * 根据ZoomUserId查找用户（可能有多个，因为不同ZoomAuth下可能有相同的zoomUserId）
     */
    List<ZoomUser> findByZoomUserId(String zoomUserId);

    /**
     * 根据ZoomUserId查找第一个用户
     */
    Optional<ZoomUser> findFirstByZoomUserId(String zoomUserId);

    /**
     * 根据邮箱查找用户
     */
    Optional<ZoomUser> findByEmail(String email);

    /**
     * 根据ZoomAuth和邮箱查找用户
     */
    Optional<ZoomUser> findByZoomAuthAndEmail(ZoomAuth zoomAuth, String email);

    /**
     * 根据User查找ZoomUser
     */
    List<ZoomUser> findByUser(User user);

    /**
     * 根据User查找ZoomUser（分页）
     */
    Page<ZoomUser> findByUser(User user, Pageable pageable);

    /**
     * 根据ZoomAuth查找所有用户
     */
    List<ZoomUser> findByZoomAuth(ZoomAuth zoomAuth);

    /**
     * 根据ZoomAuth查找所有用户（分页）
     */
    Page<ZoomUser> findByZoomAuth(ZoomAuth zoomAuth, Pageable pageable);
    
    /**
     * 根据ZoomAuth和状态查找用户
     */
    List<ZoomUser> findByZoomAuthAndStatus(ZoomAuth zoomAuth, ZoomUser.UserStatus status);

    /**
     * 根据ZoomAuth和用户类型查找用户
     */
    List<ZoomUser> findByZoomAuthAndUserType(ZoomAuth zoomAuth, ZoomUser.UserType userType);

    /**
     * 根据ZoomAuth、用户类型和账号用途查找用户
     */
    List<ZoomUser> findByZoomAuthAndUserTypeAndAccountUsage(ZoomAuth zoomAuth, ZoomUser.UserType userType, ZoomUser.AccountUsage accountUsage);

    /**
     * 根据用户类型和账号用途查找用户（全局搜索）
     */
    List<ZoomUser> findByUserTypeAndAccountUsage(ZoomUser.UserType userType, ZoomUser.AccountUsage accountUsage);

    /**
     * 根据用户类型、状态和账号用途查找用户（全局搜索）
     */
    List<ZoomUser> findByUserTypeAndStatusAndAccountUsage(ZoomUser.UserType userType, ZoomUser.UserStatus status, ZoomUser.AccountUsage accountUsage);

    /**
     * 根据状态和账号用途查找用户（全局搜索，包括所有用户类型）
     */
    List<ZoomUser> findByStatusAndAccountUsage(ZoomUser.UserStatus status, ZoomUser.AccountUsage accountUsage);

    /**
     * 根据用户类型和账号用途搜索用户（支持邮箱和姓名模糊查询）
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.userType = :userType AND zu.accountUsage = :accountUsage AND " +
           "(LOWER(zu.email) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(zu.firstName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(zu.lastName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(zu.displayName) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<ZoomUser> searchByUserTypeAndAccountUsageAndKeyword(@Param("userType") ZoomUser.UserType userType,
                                                           @Param("accountUsage") ZoomUser.AccountUsage accountUsage,
                                                           @Param("keyword") String keyword,
                                                           Pageable pageable);
    
    /**
     * 检查ZoomAuth下是否存在指定的ZoomUserId
     */
    boolean existsByZoomAuthAndZoomUserId(ZoomAuth zoomAuth, String zoomUserId);

    /**
     * 查找可用的ZoomUser（未被使用中，LICENSED类型，PUBLIC_HOST用途）
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.usageStatus = :usageStatus AND zu.userType = :userType AND zu.accountUsage = :accountUsage AND zu.status = :status")
    List<ZoomUser> findAvailablePublicHostUsers(@Param("usageStatus") ZoomUser.UsageStatus usageStatus,
                                               @Param("userType") ZoomUser.UserType userType,
                                               @Param("accountUsage") ZoomUser.AccountUsage accountUsage,
                                               @Param("status") ZoomUser.UserStatus status);

    /**
     * 查找可用的ZoomUser（跨所有ZoomAuth，按最后使用时间排序实现负载均衡）
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.usageStatus = :usageStatus AND zu.userType = :userType AND zu.accountUsage = :accountUsage AND zu.status = :status ORDER BY zu.lastUsedTime ASC NULLS FIRST, zu.zoomAuth.id, zu.id")
    List<ZoomUser> findAvailablePublicHostUsersOrdered(@Param("usageStatus") ZoomUser.UsageStatus usageStatus,
                                                      @Param("userType") ZoomUser.UserType userType,
                                                      @Param("accountUsage") ZoomUser.AccountUsage accountUsage,
                                                      @Param("status") ZoomUser.UserStatus status);

    /**
     * 查找第一个可用的ZoomUser（跨所有ZoomAuth查找，负载均衡）
     */
    default Optional<ZoomUser> findFirstAvailablePublicHostUser() {
        List<ZoomUser> users = findAvailablePublicHostUsersOrdered(
            ZoomUser.UsageStatus.AVAILABLE, // 使用AVAILABLE状态
            ZoomUser.UserType.LICENSED,
            ZoomUser.AccountUsage.PUBLIC_HOST,
            ZoomUser.UserStatus.ACTIVE
        );
        return users.isEmpty() ? Optional.empty() : Optional.of(users.get(0));
    }
    
    /**
     * 检查ZoomAuth下是否存在指定的邮箱
     */
    boolean existsByZoomAuthAndEmail(ZoomAuth zoomAuth, String email);
    
    /**
     * 统计ZoomAuth下的用户数量
     */
    long countByZoomAuth(ZoomAuth zoomAuth);

    /**
     * 统计ZoomAuth下各状态的用户数量
     */
    @Query("SELECT zu.status, COUNT(zu) FROM ZoomUser zu WHERE zu.zoomAuth = :zoomAuth GROUP BY zu.status")
    List<Object[]> countByZoomAuthGroupByStatus(@Param("zoomAuth") ZoomAuth zoomAuth);

    /**
     * 统计ZoomAuth下各用户类型的数量
     */
    @Query("SELECT zu.userType, COUNT(zu) FROM ZoomUser zu WHERE zu.zoomAuth = :zoomAuth GROUP BY zu.userType")
    List<Object[]> countByZoomAuthGroupByUserType(@Param("zoomAuth") ZoomAuth zoomAuth);

    /**
     * 根据邮箱模糊查询用户
     */
    Page<ZoomUser> findByZoomAuthAndEmailContainingIgnoreCase(ZoomAuth zoomAuth, String email, Pageable pageable);

    /**
     * 根据姓名模糊查询用户
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.zoomAuth = :zoomAuth AND " +
           "(LOWER(zu.firstName) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
           "LOWER(zu.lastName) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
           "LOWER(zu.displayName) LIKE LOWER(CONCAT('%', :name, '%')))")
    Page<ZoomUser> findByZoomAuthAndNameContaining(@Param("zoomAuth") ZoomAuth zoomAuth,
                                                  @Param("name") String name,
                                                  Pageable pageable);

    /**
     * 全局根据邮箱模糊查询用户
     */
    Page<ZoomUser> findByEmailContainingIgnoreCase(String email, Pageable pageable);

    /**
     * 全局根据姓名模糊查询用户
     */
    Page<ZoomUser> findByDisplayNameContainingIgnoreCaseOrFirstNameContainingIgnoreCaseOrLastNameContainingIgnoreCase(
            String displayName, String firstName, String lastName, Pageable pageable);

    /**
     * 统计所有用户各状态的数量
     */
    @Query("SELECT zu.status, COUNT(zu) FROM ZoomUser zu GROUP BY zu.status")
    List<Object[]> countAllGroupByStatus();

    /**
     * 统计所有用户各类型的数量
     */
    @Query("SELECT zu.userType, COUNT(zu) FROM ZoomUser zu GROUP BY zu.userType")
    List<Object[]> countAllGroupByUserType();

    // ========== PMI相关查询方法 ==========

    /**
     * 根据使用状态查找ZoomUser
     */
    List<ZoomUser> findByUsageStatus(ZoomUser.UsageStatus usageStatus);

    /**
     * 根据当前会议ID查找ZoomUser
     */
    List<ZoomUser> findByCurrentMeetingId(Long currentMeetingId);

    /**
     * 查找原始PMI为空的ZoomUser
     */
    List<ZoomUser> findByOriginalPmiIsNull();

    /**
     * 根据原始PMI查找ZoomUser
     */
    List<ZoomUser> findByOriginalPmi(String originalPmi);

    /**
     * 根据当前PMI查找ZoomUser
     */
    List<ZoomUser> findByCurrentPmi(String currentPmi);

    /**
     * 统计各使用状态的ZoomUser数量
     */
    long countByUsageStatus(ZoomUser.UsageStatus usageStatus);

    /**
     * 查找可用的ZoomUser（新版本，基于UsageStatus）
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.usageStatus = 'AVAILABLE' " +
           "AND zu.status = 'ACTIVE' AND zu.userType = 'LICENSED' " +
           "ORDER BY zu.lastUsedTime ASC NULLS FIRST")
    List<ZoomUser> findAvailableZoomUsers();

    /**
     * 查找使用中的ZoomUser
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.usageStatus = 'IN_USE' " +
           "ORDER BY zu.lastUsedTime DESC")
    List<ZoomUser> findInUseZoomUsers();

    /**
     * 查找维护中的ZoomUser
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.usageStatus = 'MAINTENANCE' " +
           "ORDER BY zu.pmiUpdatedAt DESC")
    List<ZoomUser> findMaintenanceZoomUsers();

    /**
     * 统计各使用状态的数量
     */
    @Query("SELECT zu.usageStatus, COUNT(zu) FROM ZoomUser zu GROUP BY zu.usageStatus")
    List<Object[]> countAllGroupByUsageStatus();

    /**
     * 查找PMI不一致的ZoomUser（当前PMI与原始PMI不同）
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.originalPmi IS NOT NULL " +
           "AND zu.currentPmi IS NOT NULL AND zu.originalPmi != zu.currentPmi")
    List<ZoomUser> findZoomUsersWithDifferentPmi();

    /**
     * 查找长时间使用中的ZoomUser
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.usageStatus = 'IN_USE' " +
           "AND zu.lastUsedTime < :threshold")
    List<ZoomUser> findLongTimeInUseZoomUsers(@Param("threshold") java.time.LocalDateTime threshold);

    /**
     * 检查原始PMI是否已存在
     */
    boolean existsByOriginalPmi(String originalPmi);

    /**
     * 检查当前PMI是否已存在
     */
    boolean existsByCurrentPmi(String currentPmi);

    /**
     * 获取所有用户，优先显示专业版且使用中的用户
     * 排序规则：
     * 1. 专业版(LICENSED) + 使用中(IN_USE) - 最高优先级
     * 2. 专业版(LICENSED) + 其他状态
     * 3. 其他用户类型
     * 4. 在同等条件下，按最后使用时间倒序
     */
    @Query("SELECT zu FROM ZoomUser zu ORDER BY " +
           "CASE " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'IN_USE' THEN 1 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'AVAILABLE' THEN 2 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'MAINTENANCE' THEN 3 " +
           "  WHEN zu.userType = 'LICENSED' THEN 4 " +
           "  ELSE 5 " +
           "END ASC, " +
           "zu.lastUsedTime DESC NULLS LAST")
    Page<ZoomUser> findAllWithPrioritySort(Pageable pageable);

    /**
     * 根据ZoomAuth获取用户，优先显示专业版且使用中的用户
     * 排序规则同上
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.zoomAuth = :zoomAuth ORDER BY " +
           "CASE " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'IN_USE' THEN 1 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'AVAILABLE' THEN 2 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'MAINTENANCE' THEN 3 " +
           "  WHEN zu.userType = 'LICENSED' THEN 4 " +
           "  ELSE 5 " +
           "END ASC, " +
           "zu.lastUsedTime DESC NULLS LAST")
    Page<ZoomUser> findByZoomAuthWithPrioritySort(@Param("zoomAuth") ZoomAuth zoomAuth, Pageable pageable);

    /**
     * 根据ZoomAuth和邮箱搜索用户，优先显示专业版且使用中的用户
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.zoomAuth = :zoomAuth AND " +
           "LOWER(zu.email) LIKE LOWER(CONCAT('%', :email, '%')) ORDER BY " +
           "CASE " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'IN_USE' THEN 1 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'AVAILABLE' THEN 2 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'MAINTENANCE' THEN 3 " +
           "  WHEN zu.userType = 'LICENSED' THEN 4 " +
           "  ELSE 5 " +
           "END ASC, " +
           "zu.lastUsedTime DESC NULLS LAST")
    Page<ZoomUser> searchByZoomAuthAndEmailWithPrioritySort(@Param("zoomAuth") ZoomAuth zoomAuth,
                                                           @Param("email") String email,
                                                           Pageable pageable);

    /**
     * 根据ZoomAuth和姓名搜索用户，优先显示专业版且使用中的用户
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE zu.zoomAuth = :zoomAuth AND " +
           "(LOWER(zu.firstName) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
           "LOWER(zu.lastName) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
           "LOWER(zu.displayName) LIKE LOWER(CONCAT('%', :name, '%'))) ORDER BY " +
           "CASE " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'IN_USE' THEN 1 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'AVAILABLE' THEN 2 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'MAINTENANCE' THEN 3 " +
           "  WHEN zu.userType = 'LICENSED' THEN 4 " +
           "  ELSE 5 " +
           "END ASC, " +
           "zu.lastUsedTime DESC NULLS LAST")
    Page<ZoomUser> searchByZoomAuthAndNameWithPrioritySort(@Param("zoomAuth") ZoomAuth zoomAuth,
                                                          @Param("name") String name,
                                                          Pageable pageable);

    /**
     * 全局搜索用户（根据邮箱），优先显示专业版且使用中的用户
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE LOWER(zu.email) LIKE LOWER(CONCAT('%', :email, '%')) ORDER BY " +
           "CASE " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'IN_USE' THEN 1 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'AVAILABLE' THEN 2 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'MAINTENANCE' THEN 3 " +
           "  WHEN zu.userType = 'LICENSED' THEN 4 " +
           "  ELSE 5 " +
           "END ASC, " +
           "zu.lastUsedTime DESC NULLS LAST")
    Page<ZoomUser> globalSearchByEmailWithPrioritySort(@Param("email") String email, Pageable pageable);

    /**
     * 全局搜索用户（根据姓名），优先显示专业版且使用中的用户
     */
    @Query("SELECT zu FROM ZoomUser zu WHERE " +
           "(LOWER(zu.firstName) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
           "LOWER(zu.lastName) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
           "LOWER(zu.displayName) LIKE LOWER(CONCAT('%', :name, '%'))) ORDER BY " +
           "CASE " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'IN_USE' THEN 1 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'AVAILABLE' THEN 2 " +
           "  WHEN zu.userType = 'LICENSED' AND zu.usageStatus = 'MAINTENANCE' THEN 3 " +
           "  WHEN zu.userType = 'LICENSED' THEN 4 " +
           "  ELSE 5 " +
           "END ASC, " +
           "zu.lastUsedTime DESC NULLS LAST")
    Page<ZoomUser> globalSearchByNameWithPrioritySort(@Param("name") String name, Pageable pageable);
}
