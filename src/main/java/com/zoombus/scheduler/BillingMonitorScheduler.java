package com.zoombus.scheduler;

import com.zoombus.service.AsyncScheduledTaskExecutor;
import com.zoombus.service.BillingMonitorService;
import com.zoombus.service.MeetingSettlementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 计费监控调度器
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "zoombus.billing.monitor-scheduler.enabled", havingValue = "true", matchIfMissing = true)
public class BillingMonitorScheduler {
    
    private final BillingMonitorService billingMonitorService;
    private final MeetingSettlementService meetingSettlementService;
    private final AsyncScheduledTaskExecutor asyncTaskExecutor;

    /**
     * 每5分钟检查一次计费监控状态
     * 优化后：使用异步执行，独立事务管理，增强错误处理
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void checkBillingMonitorStatus() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "checkBillingMonitorStatus",
                "BILLING_MONITOR_CHECK",
                this::doCheckBillingMonitorStatus,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(2, 60000) // 最多重试2次，间隔1分钟
        );
    }

    /**
     * 执行计费监控状态检查的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doCheckBillingMonitorStatus() {
        BillingMonitorService.BillingMonitorStatus status = billingMonitorService.getBillingMonitorStatus();

        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
        result.setProcessedCount(1);

        if (!status.isHealthy()) {
            log.warn("计费监控状态异常: {}", status.getStatusDescription());
            try {
                // 尝试同步状态
                billingMonitorService.syncBillingMonitorStatus();
                result.setSuccessCount(1);
                result.setFailedCount(0);
                result.setResultData("状态异常已修复: " + status.getStatusDescription());
            } catch (Exception e) {
                result.setSuccessCount(0);
                result.setFailedCount(1);
                result.setResultData("状态异常修复失败: " + e.getMessage());
                throw e;
            }
        } else {
            log.debug("计费监控状态正常: {}", status.getStatusDescription());
            result.setSuccessCount(1);
            result.setFailedCount(0);
            result.setResultData("状态正常: " + status.getStatusDescription());
        }

        return result;
    }
    
    /**
     * 每10分钟执行一次批量结算
     * 优化后：使用异步执行，独立事务管理，增强错误处理
     */
    @Scheduled(fixedRate = 600000) // 每10分钟执行一次
    public void batchSettleMeetings() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "batchSettleMeetings",
                "BATCH_SETTLEMENT",
                this::doBatchSettleMeetings,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(2, 120000) // 最多重试2次，间隔2分钟
        );
    }

    /**
     * 执行批量结算的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doBatchSettleMeetings() {
        log.debug("开始批量结算未结算的会议");

        MeetingSettlementService.BatchSettlementResult settlementResult = meetingSettlementService.batchSettleMeetings();

        if (settlementResult.getTotalCount() > 0) {
            log.info("批量结算完成: 总数={}, 成功={}, 失败={}",
                settlementResult.getTotalCount(), settlementResult.getSuccessCount(), settlementResult.getFailureCount());
        }

        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
        result.setProcessedCount(settlementResult.getTotalCount());
        result.setSuccessCount(settlementResult.getSuccessCount());
        result.setFailedCount(settlementResult.getFailureCount());
        result.setResultData(String.format("批量结算: 总数=%d, 成功=%d, 失败=%d",
                settlementResult.getTotalCount(), settlementResult.getSuccessCount(), settlementResult.getFailureCount()));

        return result;
    }
    
    /**
     * 每小时执行一次计费监控健康检查
     * 优化后：使用异步执行，避免阻塞调度器
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void hourlyHealthCheck() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "hourlyHealthCheck",
                "HOURLY_HEALTH_CHECK",
                this::doHourlyHealthCheck,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(1, 300000) // 最多重试1次，间隔5分钟
        );
    }

    /**
     * 执行每小时健康检查的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doHourlyHealthCheck() {
        log.info("开始每小时计费监控健康检查");

        BillingMonitorService.BillingMonitorStatus status = billingMonitorService.getBillingMonitorStatus();
        log.info("计费监控状态: {}", status.getStatusDescription());

        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
        result.setProcessedCount(1);

        // 如果状态异常，强制同步
        if (!status.isHealthy()) {
            log.warn("检测到计费监控状态异常，执行强制同步");
            try {
                billingMonitorService.syncBillingMonitorStatus();
                result.setSuccessCount(1);
                result.setFailedCount(0);
                result.setResultData("状态异常已修复");
            } catch (Exception e) {
                result.setSuccessCount(0);
                result.setFailedCount(1);
                result.setResultData("状态修复失败: " + e.getMessage());
                throw e;
            }
        } else {
            result.setSuccessCount(1);
            result.setFailedCount(0);
            result.setResultData("状态正常");
        }

        log.info("完成每小时计费监控健康检查");
        return result;
    }

    /**
     * 每天凌晨2点执行一次深度检查和清理
     * 优化后：使用异步执行，避免阻塞调度器
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void dailyDeepCheck() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "dailyDeepCheck",
                "DAILY_DEEP_CHECK",
                this::doDailyDeepCheck,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(1, 600000) // 最多重试1次，间隔10分钟
        );
    }

    /**
     * 执行每日深度检查的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doDailyDeepCheck() {
        log.info("开始每日深度检查和清理");

        try {
            // 强制同步计费监控状态
            billingMonitorService.syncBillingMonitorStatus();

            // 批量结算所有未结算的会议
            MeetingSettlementService.BatchSettlementResult settlementResult = meetingSettlementService.batchSettleMeetings();
            log.info("每日批量结算结果: 总数={}, 成功={}, 失败={}",
                settlementResult.getTotalCount(), settlementResult.getSuccessCount(), settlementResult.getFailureCount());

            log.info("完成每日深度检查和清理");

            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                    new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(1 + settlementResult.getTotalCount());
            result.setSuccessCount(1 + settlementResult.getSuccessCount());
            result.setFailedCount(settlementResult.getFailureCount());
            result.setResultData(String.format("深度检查完成，结算: 总数=%d, 成功=%d, 失败=%d",
                    settlementResult.getTotalCount(), settlementResult.getSuccessCount(), settlementResult.getFailureCount()));

            return result;
        } catch (Exception e) {
            log.error("每日深度检查时发生错误", e);
            throw e;
        }
    }
}
