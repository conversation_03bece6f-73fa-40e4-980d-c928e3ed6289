package com.zoombus.scheduler;

import com.zoombus.dto.SyncUsersResult;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.repository.ZoomAuthRepository;
import com.zoombus.service.AsyncScheduledTaskExecutor;
import com.zoombus.service.ZoomUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 每日用户同步调度器
 * 每日0点定时全量从Zoom同步各个zoomAuth对应的ZoomUser信息
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "zoombus.daily-sync.enabled", havingValue = "true", matchIfMissing = true)
public class DailyUserSyncScheduler {
    
    private final ZoomUserService zoomUserService;
    private final ZoomAuthRepository zoomAuthRepository;
    private final AsyncScheduledTaskExecutor asyncTaskExecutor;

    /**
     * 每日0点执行全量用户同步
     * cron表达式: 秒 分 时 日 月 周
     * 0 0 0 * * ? 表示每天0点0分0秒执行
     * 优化后：使用异步执行，独立事务管理，增强错误处理
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void dailyUserSync() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "dailyUserSync",
                "DAILY_USER_SYNC",
                this::doDailyUserSync,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(1, 300000) // 最多重试1次，间隔5分钟
        );
    }

    /**
     * 执行每日用户同步的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doDailyUserSync() {
        log.info("🕛 开始执行每日0点全量用户同步任务");
        
        // 获取所有活跃的ZoomAuth
        List<ZoomAuth> activeZoomAuths = zoomAuthRepository.findByStatus(ZoomAuth.AuthStatus.ACTIVE);

        if (activeZoomAuths.isEmpty()) {
            log.warn("⚠️ 没有找到活跃的ZoomAuth，跳过用户同步");
            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                    new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(0);
            result.setSuccessCount(0);
            result.setFailedCount(0);
            result.setResultData("没有活跃的ZoomAuth");
            return result;
        }

        log.info("📊 找到 {} 个活跃的ZoomAuth，开始同步用户信息", activeZoomAuths.size());

        int totalSuccess = 0;
        int totalFailed = 0;
        int totalUsers = 0;

        // 遍历每个ZoomAuth进行用户同步
        for (ZoomAuth zoomAuth : activeZoomAuths) {
            try {
                log.info("🔄 开始同步ZoomAuth: {} ({})", zoomAuth.getAccountName(), zoomAuth.getId());

                // 调用用户同步服务
                SyncUsersResult syncResult = zoomUserService.syncUsersFromZoomApi(zoomAuth.getId());

                if (syncResult.isSuccess()) {
                    totalSuccess++;
                    totalUsers += syncResult.getResult().getTotalUsers();
                    log.info("✅ ZoomAuth {} 用户同步成功: 总用户: {}, 新增: {}, 更新: {}, 跳过: {}",
                            zoomAuth.getAccountName(),
                            syncResult.getResult().getTotalUsers(),
                            syncResult.getResult().getNewUsers(),
                            syncResult.getResult().getUpdatedUsers(),
                            syncResult.getResult().getSkippedUsers());
                } else {
                    totalFailed++;
                    log.error("❌ ZoomAuth {} 用户同步失败: {}",
                            zoomAuth.getAccountName(), syncResult.getMessage());
                }

            } catch (Exception e) {
                totalFailed++;
                log.error("❌ ZoomAuth {} 用户同步时发生异常: {}",
                        zoomAuth.getAccountName(), e.getMessage(), e);
            }

            // 在每个ZoomAuth同步之间稍作延迟，避免API限流
            try {
                Thread.sleep(2000); // 延迟2秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("⚠️ 同步延迟被中断");
                break; // 中断时退出循环
            }
        }

        log.info("🎉 每日用户同步任务完成 - 总计: {}, 成功: {}, 失败: {}, 同步用户数: {}",
                activeZoomAuths.size(), totalSuccess, totalFailed, totalUsers);

        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
        result.setProcessedCount(activeZoomAuths.size());
        result.setSuccessCount(totalSuccess);
        result.setFailedCount(totalFailed);
        result.setResultData(String.format("同步用户数: %d", totalUsers));

        return result;
    }
    
    /**
     * 手动触发用户同步（用于测试）
     * 每分钟检查一次是否需要手动触发
     * 优化后：使用异步执行，避免阻塞调度器
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkManualTrigger() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "checkManualTrigger",
                "MANUAL_TRIGGER_CHECK",
                this::doCheckManualTrigger,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.defaultConfig()
        );
    }

    /**
     * 执行手动触发检查的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doCheckManualTrigger() {
        // 这里可以添加手动触发逻辑，比如检查某个配置文件或数据库标志
        // 暂时留空，仅用于将来扩展

        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
        result.setProcessedCount(0);
        result.setSuccessCount(0);
        result.setFailedCount(0);
        result.setResultData("手动触发检查完成");

        return result;
    }
}
