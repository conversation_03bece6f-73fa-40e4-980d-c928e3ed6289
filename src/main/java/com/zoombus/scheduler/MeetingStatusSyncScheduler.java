package com.zoombus.scheduler;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.repository.ZoomUserRepository;
import com.zoombus.service.AsyncScheduledTaskExecutor;
import com.zoombus.service.ZoomApiService;
import com.zoombus.service.ZoomAuthService;
import com.zoombus.service.ZoomMeetingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 会议状态同步定时任务
 * 每3分钟轮询活跃状态的会议，同步Zoom端状态
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MeetingStatusSyncScheduler {

    private final ZoomMeetingRepository zoomMeetingRepository;
    private final ZoomUserRepository zoomUserRepository;
    private final ZoomApiService zoomApiService;
    private final ZoomAuthService zoomAuthService;
    private final ZoomMeetingService zoomMeetingService;
    private final AsyncScheduledTaskExecutor asyncTaskExecutor;

    /**
     * 获取会议对应的ZoomAuth
     */
    private com.zoombus.entity.ZoomAuth getZoomAuthForMeeting(ZoomMeeting meeting) {
        if (meeting.getAssignedZoomUserId() == null) {
            throw new RuntimeException("会议没有关联的ZoomUser: meetingId=" + meeting.getId());
        }

        // 通过assignedZoomUserId查找ZoomUser，然后获取对应的ZoomAuth
        Optional<ZoomUser> zoomUserOpt = zoomUserRepository.findById(meeting.getAssignedZoomUserId());
        if (!zoomUserOpt.isPresent()) {
            throw new RuntimeException("未找到ZoomUser: zoomUserId=" + meeting.getAssignedZoomUserId());
        }

        ZoomUser zoomUser = zoomUserOpt.get();
        if (zoomUser.getZoomAuth() == null) {
            throw new RuntimeException("ZoomUser没有关联的ZoomAuth: zoomUserId=" + meeting.getAssignedZoomUserId());
        }

        log.debug("找到会议对应的ZoomAuth: meetingId={}, zoomUserId={}, zoomAuthAccount={}",
                meeting.getId(), meeting.getAssignedZoomUserId(), zoomUser.getZoomAuth().getAccountName());
        return zoomUser.getZoomAuth();
    }

    /**
     * 每3分钟执行一次会议状态同步
     * 优化后：使用异步执行，独立事务管理，增强错误处理
     */
    @Scheduled(fixedDelay = 180000) // 3分钟 = 180秒 = 180000毫秒
    public void syncMeetingStatus() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "syncMeetingStatus",
                "MEETING_STATUS_SYNC",
                this::doSyncMeetingStatus,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(2, 60000) // 最多重试2次，间隔1分钟
        );
    }

    /**
     * 执行会议状态同步的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doSyncMeetingStatus() {
        log.info("开始执行会议状态同步任务");

        // 查询所有活跃状态的会议（PENDING和USING）
        List<ZoomMeeting> activeMeetings = zoomMeetingRepository.findActiveMeetingsForSync();

        if (activeMeetings.isEmpty()) {
            log.info("没有需要同步的活跃会议");
            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                    new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(0);
            result.setSuccessCount(0);
            result.setFailedCount(0);
            result.setResultData("没有需要同步的活跃会议");
            return result;
        }

        log.info("找到 {} 个活跃会议需要同步状态", activeMeetings.size());

        int syncCount = 0;
        int changedCount = 0;
        int errorCount = 0;

        for (ZoomMeeting meeting : activeMeetings) {
            try {
                boolean statusChanged = syncSingleMeetingStatus(meeting);
                syncCount++;
                if (statusChanged) {
                    changedCount++;
                }

                // 避免API调用过于频繁，每次调用间隔100ms
                Thread.sleep(100);

            } catch (Exception e) {
                log.error("同步会议状态失败: meetingId={}, zoomMeetingId={}",
                        meeting.getId(), meeting.getZoomMeetingId(), e);
                errorCount++;
            }
        }

        log.info("会议状态同步任务完成: 总数={}, 成功={}, 状态变更={}, 失败={}",
                syncCount, syncCount - errorCount, changedCount, errorCount);

        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
        result.setProcessedCount(syncCount);
        result.setSuccessCount(syncCount - errorCount);
        result.setFailedCount(errorCount);
        result.setResultData(String.format("同步完成: 总数=%d, 成功=%d, 状态变更=%d, 失败=%d",
                syncCount, syncCount - errorCount, changedCount, errorCount));

        return result;
    }

    /**
     * 同步单个会议的状态
     * 
     * @param meeting 会议记录
     * @return true表示状态发生了变更
     */
    private boolean syncSingleMeetingStatus(ZoomMeeting meeting) {
        String zoomMeetingId = meeting.getZoomMeetingId();
        
        // 跳过测试会议和无效的会议ID
        if (zoomMeetingId == null || zoomMeetingId.isEmpty() || 
            zoomMeetingId.startsWith("test-") || zoomMeetingId.startsWith("pending-")) {
            log.debug("跳过测试会议或无效会议ID: meetingId={}, zoomMeetingId={}", 
                    meeting.getId(), zoomMeetingId);
            return false;
        }

        try {
            log.debug("查询Zoom会议状态: meetingId={}, zoomMeetingId={}, currentStatus={}",
                    meeting.getId(), zoomMeetingId, meeting.getStatus());

            // 获取会议对应的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getZoomAuthForMeeting(meeting);

            // 使用正确的ZoomAuth调用API
            ZoomApiResponse<JsonNode> response = zoomApiService.getMeeting(zoomMeetingId, zoomAuth);

            if (!response.isSuccess()) {
                String errorCode = response.getErrorCode();
                
                // 会议不存在，可能已被删除
                if ("3001".equals(errorCode) || "404".equals(errorCode)) {
                    log.info("Zoom会议不存在，标记为已结束: meetingId={}, zoomMeetingId={}", 
                            meeting.getId(), zoomMeetingId);
                    
                    if (meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED) {
                        // 执行会议结束流程
                        zoomMeetingService.handleMeetingEnded(meeting.getZoomMeetingUuid());
                        return true;
                    }
                }
                
                log.warn("查询Zoom会议状态失败: meetingId={}, zoomMeetingId={}, error={}", 
                        meeting.getId(), zoomMeetingId, response.getMessage());
                return false;
            }

            JsonNode meetingData = response.getData();
            
            // 检查会议状态
            if (!meetingData.has("status")) {
                log.warn("Zoom会议响应中没有状态字段: meetingId={}, zoomMeetingId={}", 
                        meeting.getId(), zoomMeetingId);
                return false;
            }

            String zoomStatus = meetingData.get("status").asText();
            ZoomMeeting.MeetingStatus currentStatus = meeting.getStatus();

            log.debug("会议状态对比: meetingId={}, 本地状态={}, Zoom状态={}", 
                    meeting.getId(), currentStatus, zoomStatus);

            // 状态同步逻辑
            boolean statusChanged = false;

            // 情况1: 本地WAITING，Zoom端已started
            if ((currentStatus == ZoomMeeting.MeetingStatus.WAITING) && "started".equals(zoomStatus)) {
                log.info("检测到会议已开始: meetingId={}, zoomMeetingId={}, 本地:{} -> Zoom:{}",
                        meeting.getId(), zoomMeetingId, currentStatus, zoomStatus);

                // 直接更新当前记录的状态，不需要重新查找
                try {
                    // 获取Zoom API返回的真实UUID
                    String realMeetingUuid = meetingData.has("uuid") ? meetingData.get("uuid").asText() : null;

                    // 更新会议状态
                    meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
                    meeting.setStartTime(LocalDateTime.now());

                    // 如果有真实的UUID，更新UUID
                    if (realMeetingUuid != null && !realMeetingUuid.trim().isEmpty()) {
                        meeting.setZoomMeetingUuid(realMeetingUuid);
                        log.info("更新会议UUID: meetingId={}, 旧UUID={}, 新UUID={}",
                                meeting.getId(), meeting.getZoomMeetingUuid(), realMeetingUuid);
                    }

                    // 更新主题（如果API返回了主题）
                    if (meetingData.has("topic")) {
                        String apiTopic = meetingData.get("topic").asText();
                        if (apiTopic != null && !apiTopic.trim().isEmpty()) {
                            meeting.setTopic(apiTopic);
                        }
                    }

                    // 保存更新
                    zoomMeetingRepository.save(meeting);

                    log.info("会议状态同步成功: meetingId={}, zoomMeetingId={}, WAITING -> STARTED",
                            meeting.getId(), zoomMeetingId);
                    statusChanged = true;

                } catch (Exception e) {
                    log.error("处理会议开始状态同步失败: meetingId={}", meeting.getId(), e);
                }
            }
            
            // 情况2: 本地STARTED，Zoom端已ended
            else if ((currentStatus == ZoomMeeting.MeetingStatus.STARTED) && "ended".equals(zoomStatus)) {
                log.info("检测到会议已结束: meetingId={}, zoomMeetingId={}, 本地:{} -> Zoom:{}",
                        meeting.getId(), zoomMeetingId, currentStatus, zoomStatus);

                // 执行类似meeting.ended事件的处理
                try {
                    // 检查会议是否已经被手动结束
                    ZoomMeeting refreshedMeeting = zoomMeetingRepository.findById(meeting.getId()).orElse(null);
                    if (refreshedMeeting != null && refreshedMeeting.getStatus() == ZoomMeeting.MeetingStatus.ENDED) {
                        log.info("会议已被手动结束，跳过自动处理: meetingId={}", meeting.getId());
                        return false;
                    }

                    zoomMeetingService.handleMeetingEnded(meeting.getZoomMeetingUuid());
                    statusChanged = true;
                } catch (Exception e) {
                    log.error("处理会议结束状态同步失败: meetingId={}", meeting.getId(), e);
                }
            }

            // 情况3: 本地STARTED，Zoom端waiting → 需要结束会议（会议被重置或异常结束）
            else if ((currentStatus == ZoomMeeting.MeetingStatus.STARTED) && "waiting".equals(zoomStatus)) {
                log.info("检测到会议状态异常(本地STARTED但Zoom端waiting)，需要结束会议: meetingId={}, zoomMeetingId={}, 本地:{} -> Zoom:{}",
                        meeting.getId(), zoomMeetingId, currentStatus, zoomStatus);

                // 执行会议结束处理
                try {
                    // 检查会议是否已经被手动结束
                    ZoomMeeting refreshedMeeting = zoomMeetingRepository.findById(meeting.getId()).orElse(null);
                    if (refreshedMeeting != null && refreshedMeeting.getStatus() == ZoomMeeting.MeetingStatus.ENDED) {
                        log.info("会议已被手动结束，跳过自动处理: meetingId={}", meeting.getId());
                        return false;
                    }

                    zoomMeetingService.handleMeetingEnded(meeting.getZoomMeetingUuid());
                    statusChanged = true;
                } catch (Exception e) {
                    log.error("处理会议状态异常同步失败: meetingId={}", meeting.getId(), e);
                }
            }
            
            // 情况3: 状态一致，无需处理
            else {
                log.debug("会议状态一致，无需同步: meetingId={}, status={}", 
                        meeting.getId(), currentStatus);
            }

            return statusChanged;

        } catch (Exception e) {
            log.error("同步会议状态异常: meetingId={}, zoomMeetingId={}", 
                    meeting.getId(), zoomMeetingId, e);
            throw e;
        }
    }

}
