package com.zoombus.scheduler;

import com.zoombus.config.WebSocketProperties;
import com.zoombus.dto.MonitoringData;
import com.zoombus.service.MonitoringService;
import com.zoombus.service.WebSocketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 监控数据收集定时任务
 * 只有在websocket.monitoring.enabled=true时才启用
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "websocket.monitoring.enabled", havingValue = "true")
@RequiredArgsConstructor
public class MonitoringScheduler {

    private final MonitoringService monitoringService;
    private final WebSocketService webSocketService;
    private final WebSocketProperties webSocketProperties;
    
    /**
     * 收集Zoom账号状态（间隔可配置，默认30秒）
     */
    @Scheduled(fixedRateString = "#{@webSocketProperties.monitoring.intervals.zoomAccount}")
    public void collectZoomAccountStatus() {
        try {
            log.debug("开始收集Zoom账号状态");
            MonitoringData.ZoomAccountStatus status = monitoringService.collectZoomAccountStatus();
            webSocketService.pushZoomAccountStatus(status);
            log.debug("Zoom账号状态收集完成");
        } catch (Exception e) {
            log.error("收集Zoom账号状态失败", e);
        }
    }

    /**
     * 收集会议状态（间隔可配置，默认1分钟）
     */
    @Scheduled(fixedRateString = "#{@webSocketProperties.monitoring.intervals.meeting}")
    public void collectMeetingStatus() {
        try {
            log.debug("开始收集会议状态");
            MonitoringData.MeetingStatus status = monitoringService.collectMeetingStatus();
            webSocketService.pushMeetingStatus(status);
            log.debug("会议状态收集完成");
        } catch (Exception e) {
            log.error("收集会议状态失败", e);
        }
    }
    
    /**
     * 收集PMI状态（间隔可配置，默认2分钟）
     */
    @Scheduled(fixedRateString = "#{@webSocketProperties.monitoring.intervals.pmi}")
    public void collectPmiStatus() {
        try {
            log.debug("开始收集PMI状态");
            MonitoringData.PmiStatus status = monitoringService.collectPmiStatus();
            webSocketService.pushPmiStatus(status);
            log.debug("PMI状态收集完成");
        } catch (Exception e) {
            log.error("收集PMI状态失败", e);
        }
    }

    /**
     * 收集系统性能（间隔可配置，默认15秒）
     */
    @Scheduled(fixedRateString = "#{@webSocketProperties.monitoring.intervals.system}")
    public void collectSystemPerformance() {
        try {
            log.debug("开始收集系统性能");
            MonitoringData.SystemPerformance performance = monitoringService.collectSystemPerformance();
            webSocketService.pushSystemPerformance(performance);
            log.debug("系统性能收集完成");
        } catch (Exception e) {
            log.error("收集系统性能失败", e);
        }
    }
    
    /**
     * 推送综合监控数据（间隔可配置，默认5分钟）
     */
    @Scheduled(fixedRateString = "#{@webSocketProperties.monitoring.intervals.comprehensive}")
    public void pushComprehensiveData() {
        try {
            log.debug("开始推送综合监控数据");
            
            // 收集所有监控数据
            Map<String, Object> comprehensiveData = new HashMap<>();
            
            // 获取缓存的数据或重新收集
            MonitoringData.ZoomAccountStatus zoomStatus = monitoringService.getCachedData(
                    "monitoring:zoom_account_status", MonitoringData.ZoomAccountStatus.class);
            if (zoomStatus == null) {
                zoomStatus = monitoringService.collectZoomAccountStatus();
            }
            
            MonitoringData.MeetingStatus meetingStatus = monitoringService.getCachedData(
                    "monitoring:meeting_status", MonitoringData.MeetingStatus.class);
            if (meetingStatus == null) {
                meetingStatus = monitoringService.collectMeetingStatus();
            }
            
            MonitoringData.PmiStatus pmiStatus = monitoringService.getCachedData(
                    "monitoring:pmi_status", MonitoringData.PmiStatus.class);
            if (pmiStatus == null) {
                pmiStatus = monitoringService.collectPmiStatus();
            }
            
            MonitoringData.SystemPerformance systemPerformance = monitoringService.getCachedData(
                    "monitoring:system_performance", MonitoringData.SystemPerformance.class);
            if (systemPerformance == null) {
                systemPerformance = monitoringService.collectSystemPerformance();
            }
            
            comprehensiveData.put("zoomAccount", zoomStatus);
            comprehensiveData.put("meeting", meetingStatus);
            comprehensiveData.put("pmi", pmiStatus);
            comprehensiveData.put("system", systemPerformance);
            comprehensiveData.put("timestamp", LocalDateTime.now());
            
            webSocketService.pushComprehensiveData(comprehensiveData);
            log.debug("综合监控数据推送完成");
            
        } catch (Exception e) {
            log.error("推送综合监控数据失败", e);
        }
    }
    
    /**
     * 检查告警条件（间隔可配置，默认1分钟）
     */
    @Scheduled(fixedRateString = "#{@webSocketProperties.monitoring.intervals.alertCheck}")
    public void checkAlerts() {
        try {
            log.debug("开始检查告警条件");
            
            // 检查Zoom账号使用率告警
            MonitoringData.ZoomAccountStatus zoomStatus = monitoringService.getCachedData(
                    "monitoring:zoom_account_status", MonitoringData.ZoomAccountStatus.class);
            
            if (zoomStatus != null) {
                if (zoomStatus.getUsageRate() > 90) {
                    MonitoringData.AlertInfo alert = MonitoringData.AlertInfo.builder()
                            .alertId("ZOOM_USAGE_CRITICAL_" + System.currentTimeMillis())
                            .level("CRITICAL")
                            .title("Zoom许可证使用率过高")
                            .message(String.format("Zoom账号 %s 的许可证使用率已达到 %.1f%%，请及时处理", 
                                    zoomStatus.getAccountName(), zoomStatus.getUsageRate()))
                            .source("ZOOM_MONITOR")
                            .alertTime(LocalDateTime.now())
                            .acknowledged(false)
                            .build();
                    
                    webSocketService.pushAlert(alert);
                } else if (zoomStatus.getUsageRate() > 80) {
                    MonitoringData.AlertInfo alert = MonitoringData.AlertInfo.builder()
                            .alertId("ZOOM_USAGE_WARNING_" + System.currentTimeMillis())
                            .level("WARNING")
                            .title("Zoom许可证使用率较高")
                            .message(String.format("Zoom账号 %s 的许可证使用率已达到 %.1f%%", 
                                    zoomStatus.getAccountName(), zoomStatus.getUsageRate()))
                            .source("ZOOM_MONITOR")
                            .alertTime(LocalDateTime.now())
                            .acknowledged(false)
                            .build();
                    
                    webSocketService.pushAlert(alert);
                }
            }
            
            // 检查系统性能告警
            MonitoringData.SystemPerformance systemPerformance = monitoringService.getCachedData(
                    "monitoring:system_performance", MonitoringData.SystemPerformance.class);
            
            if (systemPerformance != null) {
                if (systemPerformance.getCpuUsage() > 80) {
                    MonitoringData.AlertInfo alert = MonitoringData.AlertInfo.builder()
                            .alertId("CPU_USAGE_HIGH_" + System.currentTimeMillis())
                            .level("WARNING")
                            .title("CPU使用率过高")
                            .message(String.format("系统CPU使用率已达到 %.1f%%", systemPerformance.getCpuUsage()))
                            .source("SYSTEM_MONITOR")
                            .alertTime(LocalDateTime.now())
                            .acknowledged(false)
                            .build();
                    
                    webSocketService.pushAlert(alert);
                }
                
                if (systemPerformance.getMemoryUsage() > 85) {
                    MonitoringData.AlertInfo alert = MonitoringData.AlertInfo.builder()
                            .alertId("MEMORY_USAGE_HIGH_" + System.currentTimeMillis())
                            .level("WARNING")
                            .title("内存使用率过高")
                            .message(String.format("系统内存使用率已达到 %.1f%%", systemPerformance.getMemoryUsage()))
                            .source("SYSTEM_MONITOR")
                            .alertTime(LocalDateTime.now())
                            .acknowledged(false)
                            .build();
                    
                    webSocketService.pushAlert(alert);
                }
            }
            
            log.debug("告警条件检查完成");
            
        } catch (Exception e) {
            log.error("检查告警条件失败", e);
        }
    }
}
