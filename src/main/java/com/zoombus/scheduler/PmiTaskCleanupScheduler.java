package com.zoombus.scheduler;

import com.zoombus.config.PmiTaskSchedulingConfig;
import com.zoombus.service.DynamicTaskManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * PMI任务清理定时调度器
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(prefix = "pmi.task.scheduling.cleanup", name = "enabled", havingValue = "true", matchIfMissing = true)
public class PmiTaskCleanupScheduler {
    
    private final DynamicTaskManager dynamicTaskManager;
    private final PmiTaskSchedulingConfig schedulingConfig;
    
    /**
     * 定期清理过期的任务记录
     * 默认每天凌晨2点执行
     */
    @Scheduled(cron = "${pmi.task.scheduling.cleanup.cron-expression:0 0 2 * * ?}")
    public void cleanupExpiredTasks() {
        if (!schedulingConfig.getCleanup().isEnabled()) {
            log.debug("任务清理功能已禁用");
            return;
        }
        
        try {
            log.info("开始清理过期的PMI任务记录");
            
            int retentionDays = schedulingConfig.getCleanup().getRetentionDays();
            int cleanedCount = dynamicTaskManager.cleanupExpiredTasks(retentionDays);
            
            log.info("PMI任务清理完成: 清理数量={}, 保留天数={}", cleanedCount, retentionDays);
            
        } catch (Exception e) {
            log.error("清理过期PMI任务失败", e);
        }
    }
    
    /**
     * 定期检查调度器健康状态
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void checkSchedulerHealth() {
        if (!schedulingConfig.isEnableTaskMonitoring()) {
            return;
        }
        
        try {
            DynamicTaskManager.SchedulerStatus status = dynamicTaskManager.getSchedulerStatus();
            
            if (!status.isActive()) {
                log.warn("PMI任务调度器状态异常: {}", status.getStatus());
            } else {
                log.debug("PMI任务调度器状态正常: 调度任务={}, 运行任务={}", 
                         status.getTotalScheduledTasks(), status.getRunningTasks());
            }
            
            // 检查是否有过多的运行任务
            if (status.getRunningTasks() > 50) {
                log.warn("运行中的PMI任务过多: {}, 可能存在任务堆积", status.getRunningTasks());
            }
            
        } catch (Exception e) {
            log.error("检查PMI任务调度器健康状态失败", e);
        }
    }
}
