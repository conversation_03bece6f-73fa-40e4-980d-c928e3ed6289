package com.zoombus.scheduler;

import com.zoombus.service.PmiBillingModeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * PMI窗口信息维护定时任务
 * 负责维护 current_window_id 和 window_expire_time 字段
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PmiWindowInfoScheduler {

    private final PmiBillingModeService pmiBillingModeService;

    /**
     * 每小时更新一次所有LONG模式PMI的窗口信息
     * 确保 current_window_id 和 window_expire_time 字段的准确性
     */
    @Scheduled(cron = "0 0 * * * *") // 每小时的0分0秒执行
    public void updatePmiWindowInfo() {
        try {
            log.info("开始定时更新PMI窗口信息");
            pmiBillingModeService.updateAllLongModePmiWindowInfo();
            log.info("PMI窗口信息定时更新完成");
        } catch (Exception e) {
            log.error("PMI窗口信息定时更新失败", e);
        }
    }

    /**
     * 每天凌晨2点执行一次完整的窗口信息同步
     * 确保数据一致性
     */
    @Scheduled(cron = "0 0 2 * * *") // 每天凌晨2点执行
    public void syncPmiWindowInfo() {
        try {
            log.info("开始每日PMI窗口信息同步");
            pmiBillingModeService.updateAllLongModePmiWindowInfo();
            log.info("每日PMI窗口信息同步完成");
        } catch (Exception e) {
            log.error("每日PMI窗口信息同步失败", e);
        }
    }
}
