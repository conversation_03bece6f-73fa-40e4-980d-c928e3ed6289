package com.zoombus.scheduler;

import com.zoombus.service.ScheduledTaskErrorHandler;
import com.zoombus.service.TaskExecutionTracker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 重试任务调度器
 * 负责处理失败任务的重试和清理工作
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "zoombus.scheduled-task.retry-scheduler.enabled", havingValue = "true", matchIfMissing = true)
public class RetryTaskScheduler {
    
    private final ScheduledTaskErrorHandler errorHandler;
    private final TaskExecutionTracker executionTracker;
    
    /**
     * 每5分钟处理一次需要重试的任务
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void processRetryTasks() {
        try {
            log.debug("开始处理需要重试的任务");
            errorHandler.processRetryTasks();
            log.debug("完成处理需要重试的任务");
        } catch (Exception e) {
            log.error("处理重试任务时发生异常", e);
        }
    }
    
    /**
     * 每10分钟清理一次超时的任务
     */
    @Scheduled(fixedRate = 600000) // 10分钟
    public void cleanupTimeoutTasks() {
        try {
            log.debug("开始清理超时任务");
            executionTracker.cleanupTimeoutTasks();
            log.debug("完成清理超时任务");
        } catch (Exception e) {
            log.error("清理超时任务时发生异常", e);
        }
    }
    
    /**
     * 每天凌晨2点清理过期的执行记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredRecords() {
        try {
            log.info("开始清理过期的任务执行记录");
            errorHandler.cleanupExpiredRecords();
            log.info("完成清理过期的任务执行记录");
        } catch (Exception e) {
            log.error("清理过期记录时发生异常", e);
        }
    }
    
    /**
     * 每小时生成一次任务健康状态报告
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void generateHealthReport() {
        try {
            log.debug("开始生成任务健康状态报告");
            
            // 获取正在执行的任务
            var runningTasks = executionTracker.getRunningTasks();
            
            if (!runningTasks.isEmpty()) {
                log.info("当前正在执行的任务数量: {}", runningTasks.size());
                
                for (String taskName : runningTasks) {
                    var executionInfo = executionTracker.getTaskExecutionInfo(taskName);
                    if (executionInfo != null) {
                        log.info("正在执行的任务: {} (ID: {}), 执行时长: {}", 
                                taskName, executionInfo.getExecutionId(), executionInfo.getFormattedDuration());
                    }
                }
            }
            
            // 生成主要任务的健康报告
            generateTaskHealthReports();
            
            log.debug("完成生成任务健康状态报告");
        } catch (Exception e) {
            log.error("生成健康状态报告时发生异常", e);
        }
    }
    
    /**
     * 生成主要任务的健康报告
     */
    private void generateTaskHealthReports() {
        String[] mainTasks = {
                "refreshExpiredTokens",
                "dailyUserSync",
                "checkExpiredWindows",
                "hourlyHealthCheck"
        };
        
        for (String taskName : mainTasks) {
            try {
                var healthReport = errorHandler.getTaskHealthReport(taskName, 24); // 最近24小时
                
                if (healthReport.getSuccessCount() > 0 || healthReport.getFailedCount() > 0) {
                    if (healthReport.isHealthy()) {
                        log.info("任务 {} 健康状态良好: 成功率 {:.1f}%, 成功 {}, 失败 {}", 
                                taskName, healthReport.getSuccessRate(), 
                                healthReport.getSuccessCount(), healthReport.getFailedCount());
                    } else {
                        log.warn("任务 {} 健康状态异常: 成功率 {:.1f}%, 成功 {}, 失败 {}", 
                                taskName, healthReport.getSuccessRate(), 
                                healthReport.getSuccessCount(), healthReport.getFailedCount());
                    }
                }
            } catch (Exception e) {
                log.error("生成任务 {} 的健康报告时发生异常", taskName, e);
            }
        }
    }
}
