package com.zoombus.scheduler;

import com.zoombus.service.AsyncScheduledTaskExecutor;
import com.zoombus.service.PmiBillingModeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 窗口到期检查调度器
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "zoombus.billing.window-expiry-scheduler.enabled", havingValue = "true", matchIfMissing = true)
public class WindowExpiryScheduler {
    
    private final PmiBillingModeService pmiBillingModeService;
    private final AsyncScheduledTaskExecutor asyncTaskExecutor;

    /**
     * 每分钟检查一次过期的时间窗口
     * 优化后：使用异步执行，独立事务管理，增强错误处理
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void checkExpiredWindows() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "checkExpiredWindows",
                "WINDOW_EXPIRY_CHECK",
                this::doCheckExpiredWindows,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(2, 30000) // 最多重试2次，间隔30秒
        );
    }

    /**
     * 执行过期窗口检查的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doCheckExpiredWindows() {
        log.debug("开始检查过期的时间窗口");

        try {
            pmiBillingModeService.processExpiredWindows();
            log.debug("完成检查过期的时间窗口");

            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                    new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(1);
            result.setSuccessCount(1);
            result.setFailedCount(0);
            result.setResultData("过期窗口检查完成");

            return result;
        } catch (Exception e) {
            log.error("检查过期时间窗口时发生错误", e);
            throw e; // 重新抛出异常，让错误处理器处理
        }
    }

    /**
     * 每小时执行一次详细检查
     * 优化后：使用异步执行，避免阻塞调度器
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void hourlyWindowCheck() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "hourlyWindowCheck",
                "HOURLY_WINDOW_CHECK",
                this::doHourlyWindowCheck,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(1, 60000) // 最多重试1次，间隔1分钟
        );
    }

    /**
     * 执行每小时窗口检查的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doHourlyWindowCheck() {
        log.info("开始每小时窗口检查");

        try {
            pmiBillingModeService.processExpiredWindows();
            log.info("完成每小时窗口检查");

            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                    new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(1);
            result.setSuccessCount(1);
            result.setFailedCount(0);
            result.setResultData("每小时窗口检查完成");

            return result;
        } catch (Exception e) {
            log.error("每小时窗口检查时发生错误", e);
            throw e; // 重新抛出异常，让错误处理器处理
        }
    }
}
