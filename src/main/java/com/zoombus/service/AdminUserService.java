package com.zoombus.service;

import com.zoombus.dto.CreateAdminUserRequest;
import com.zoombus.dto.UpdateAdminUserRequest;
import com.zoombus.entity.AdminUser;
import com.zoombus.exception.ResourceNotFoundException;
import com.zoombus.repository.AdminUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminUserService {
    
    private final AdminUserRepository adminUserRepository;
    private final PasswordEncoder passwordEncoder;
    
    /**
     * 创建管理员用户
     */
    @Transactional
    public AdminUser createAdminUser(CreateAdminUserRequest request) {
        log.info("创建管理员用户: {}", request.getUsername());
        
        // 检查用户名是否已存在
        if (adminUserRepository.existsByUsername(request.getUsername())) {
            throw new IllegalArgumentException("用户名已存在: " + request.getUsername());
        }
        
        // 检查邮箱是否已存在
        if (adminUserRepository.existsByEmail(request.getEmail())) {
            throw new IllegalArgumentException("邮箱已存在: " + request.getEmail());
        }
        
        AdminUser adminUser = new AdminUser();
        adminUser.setUsername(request.getUsername());
        adminUser.setEmail(request.getEmail());
        adminUser.setPassword(passwordEncoder.encode(request.getPassword()));
        adminUser.setFullName(request.getFullName());
        adminUser.setPhone(request.getPhone());
        adminUser.setRole(request.getRole());
        adminUser.setStatus(request.getStatus());
        
        AdminUser savedUser = adminUserRepository.save(adminUser);
        log.info("管理员用户创建成功: {}", savedUser.getUsername());
        
        return savedUser;
    }
    
    /**
     * 根据用户名查找管理员用户
     */
    @Transactional(readOnly = true)
    public Optional<AdminUser> findByUsername(String username) {
        return adminUserRepository.findByUsername(username);
    }
    
    /**
     * 根据ID获取管理员用户
     */
    @Transactional(readOnly = true)
    public Optional<AdminUser> getAdminUserById(Long id) {
        return adminUserRepository.findById(id);
    }
    
    /**
     * 获取所有管理员用户（分页）
     */
    @Transactional(readOnly = true)
    public Page<AdminUser> getAllAdminUsers(Pageable pageable) {
        return adminUserRepository.findAll(pageable);
    }
    
    /**
     * 根据状态获取管理员用户
     */
    @Transactional(readOnly = true)
    public List<AdminUser> getAdminUsersByStatus(AdminUser.AdminStatus status) {
        return adminUserRepository.findByStatus(status);
    }
    
    /**
     * 根据角色获取管理员用户
     */
    @Transactional(readOnly = true)
    public List<AdminUser> getAdminUsersByRole(AdminUser.AdminRole role) {
        return adminUserRepository.findByRole(role);
    }
    
    /**
     * 搜索管理员用户
     */
    @Transactional(readOnly = true)
    public List<AdminUser> searchAdminUsers(String keyword) {
        return adminUserRepository.searchByKeyword(keyword);
    }
    
    /**
     * 更新管理员用户
     */
    @Transactional
    public AdminUser updateAdminUser(Long id, UpdateAdminUserRequest request) {
        log.info("更新管理员用户: {}", id);
        
        AdminUser adminUser = adminUserRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("管理员用户不存在: " + id));
        
        // 检查邮箱是否被其他用户使用
        Optional<AdminUser> existingUser = adminUserRepository.findByEmail(request.getEmail());
        if (existingUser.isPresent() && !existingUser.get().getId().equals(id)) {
            throw new IllegalArgumentException("邮箱已被其他用户使用: " + request.getEmail());
        }
        
        adminUser.setEmail(request.getEmail());
        adminUser.setFullName(request.getFullName());
        adminUser.setPhone(request.getPhone());
        adminUser.setRole(request.getRole());
        adminUser.setStatus(request.getStatus());
        
        AdminUser updatedUser = adminUserRepository.save(adminUser);
        log.info("管理员用户更新成功: {}", updatedUser.getUsername());
        
        return updatedUser;
    }
    
    /**
     * 删除管理员用户
     */
    @Transactional
    public void deleteAdminUser(Long id) {
        log.info("删除管理员用户: {}", id);
        
        AdminUser adminUser = adminUserRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("管理员用户不存在: " + id));
        
        adminUserRepository.delete(adminUser);
        log.info("管理员用户删除成功: {}", adminUser.getUsername());
    }
    
    /**
     * 修改密码
     */
    @Transactional
    public void changePassword(Long id, String oldPassword, String newPassword) {
        log.info("修改管理员用户密码: {}", id);
        
        AdminUser adminUser = adminUserRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("管理员用户不存在: " + id));
        
        // 验证原密码
        if (!passwordEncoder.matches(oldPassword, adminUser.getPassword())) {
            throw new IllegalArgumentException("原密码不正确");
        }
        
        adminUser.setPassword(passwordEncoder.encode(newPassword));
        adminUserRepository.save(adminUser);
        
        log.info("管理员用户密码修改成功: {}", adminUser.getUsername());
    }
    
    /**
     * 更新最后登录时间
     */
    @Transactional
    public void updateLastLoginTime(Long id) {
        AdminUser adminUser = adminUserRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("管理员用户不存在: " + id));
        
        adminUser.setLastLoginAt(LocalDateTime.now());
        adminUserRepository.save(adminUser);
    }
}
