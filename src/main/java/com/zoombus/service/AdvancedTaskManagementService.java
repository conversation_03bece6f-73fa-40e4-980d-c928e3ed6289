package com.zoombus.service;

import com.zoombus.entity.TaskExecutionRecord;
import com.zoombus.repository.TaskExecutionRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

/**
 * 高级任务管理服务
 * 提供任务调度管理、依赖管理、监控告警等高级功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdvancedTaskManagementService {

    private final TaskExecutionRecordRepository taskExecutionRecordRepository;
    private final TaskExecutionCacheService cacheService;
    private final TaskScheduler taskScheduler;

    // 任务调度状态管理
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    private final Map<String, TaskConfig> taskConfigs = new ConcurrentHashMap<>();
    private final Map<String, TaskDependency> taskDependencies = new ConcurrentHashMap<>();

    /**
     * 任务配置类
     */
    public static class TaskConfig {
        private String taskName;
        private String cronExpression;
        private boolean enabled;
        private int maxRetryCount;
        private long timeoutMs;
        private Map<String, Object> parameters;

        // 构造函数和getter/setter
        public TaskConfig(String taskName, String cronExpression) {
            this.taskName = taskName;
            this.cronExpression = cronExpression;
            this.enabled = true;
            this.maxRetryCount = 3;
            this.timeoutMs = 300000; // 5分钟默认超时
            this.parameters = new HashMap<>();
        }

        // Getters and Setters
        public String getTaskName() { return taskName; }
        public void setTaskName(String taskName) { this.taskName = taskName; }
        public String getCronExpression() { return cronExpression; }
        public void setCronExpression(String cronExpression) { this.cronExpression = cronExpression; }
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public int getMaxRetryCount() { return maxRetryCount; }
        public void setMaxRetryCount(int maxRetryCount) { this.maxRetryCount = maxRetryCount; }
        public long getTimeoutMs() { return timeoutMs; }
        public void setTimeoutMs(long timeoutMs) { this.timeoutMs = timeoutMs; }
        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
    }

    /**
     * 任务依赖类
     */
    public static class TaskDependency {
        private String taskName;
        private List<String> dependsOn;
        private boolean waitForAll;

        public TaskDependency(String taskName) {
            this.taskName = taskName;
            this.dependsOn = new ArrayList<>();
            this.waitForAll = true;
        }

        // Getters and Setters
        public String getTaskName() { return taskName; }
        public void setTaskName(String taskName) { this.taskName = taskName; }
        public List<String> getDependsOn() { return dependsOn; }
        public void setDependsOn(List<String> dependsOn) { this.dependsOn = dependsOn; }
        public boolean isWaitForAll() { return waitForAll; }
        public void setWaitForAll(boolean waitForAll) { this.waitForAll = waitForAll; }
    }

    /**
     * 启用任务
     */
    public boolean enableTask(String taskName) {
        try {
            TaskConfig config = taskConfigs.get(taskName);
            if (config == null) {
                log.warn("任务配置不存在: {}", taskName);
                return false;
            }

            config.setEnabled(true);
            
            // 如果任务已经在运行，先停止
            stopTask(taskName);
            
            // 重新调度任务
            scheduleTask(config);
            
            log.info("任务已启用: {}", taskName);
            return true;
        } catch (Exception e) {
            log.error("启用任务失败: {}", taskName, e);
            return false;
        }
    }

    /**
     * 禁用任务
     */
    public boolean disableTask(String taskName) {
        try {
            TaskConfig config = taskConfigs.get(taskName);
            if (config == null) {
                log.warn("任务配置不存在: {}", taskName);
                return false;
            }

            config.setEnabled(false);
            
            // 停止任务调度
            stopTask(taskName);
            
            log.info("任务已禁用: {}", taskName);
            return true;
        } catch (Exception e) {
            log.error("禁用任务失败: {}", taskName, e);
            return false;
        }
    }

    /**
     * 更新任务调度频率
     */
    public boolean updateTaskSchedule(String taskName, String newCronExpression) {
        try {
            // 验证Cron表达式
            if (!isValidCronExpression(newCronExpression)) {
                log.error("无效的Cron表达式: {}", newCronExpression);
                return false;
            }

            TaskConfig config = taskConfigs.get(taskName);
            if (config == null) {
                log.warn("任务配置不存在: {}", taskName);
                return false;
            }

            // 停止当前调度
            stopTask(taskName);
            
            // 更新配置
            config.setCronExpression(newCronExpression);
            
            // 如果任务是启用状态，重新调度
            if (config.isEnabled()) {
                scheduleTask(config);
            }
            
            log.info("任务调度频率已更新: {} -> {}", taskName, newCronExpression);
            return true;
        } catch (Exception e) {
            log.error("更新任务调度频率失败: {}", taskName, e);
            return false;
        }
    }

    /**
     * 添加任务依赖
     */
    public boolean addTaskDependency(String taskName, String dependsOnTask) {
        try {
            TaskDependency dependency = taskDependencies.computeIfAbsent(taskName, TaskDependency::new);
            
            if (!dependency.getDependsOn().contains(dependsOnTask)) {
                dependency.getDependsOn().add(dependsOnTask);
                log.info("添加任务依赖: {} 依赖于 {}", taskName, dependsOnTask);
                return true;
            }
            
            log.warn("任务依赖已存在: {} -> {}", taskName, dependsOnTask);
            return false;
        } catch (Exception e) {
            log.error("添加任务依赖失败: {} -> {}", taskName, dependsOnTask, e);
            return false;
        }
    }

    /**
     * 移除任务依赖
     */
    public boolean removeTaskDependency(String taskName, String dependsOnTask) {
        try {
            TaskDependency dependency = taskDependencies.get(taskName);
            if (dependency != null) {
                boolean removed = dependency.getDependsOn().remove(dependsOnTask);
                if (removed) {
                    log.info("移除任务依赖: {} 不再依赖于 {}", taskName, dependsOnTask);
                    return true;
                }
            }
            
            log.warn("任务依赖不存在: {} -> {}", taskName, dependsOnTask);
            return false;
        } catch (Exception e) {
            log.error("移除任务依赖失败: {} -> {}", taskName, dependsOnTask, e);
            return false;
        }
    }

    /**
     * 检查任务依赖是否满足
     */
    public boolean checkTaskDependencies(String taskName) {
        TaskDependency dependency = taskDependencies.get(taskName);
        if (dependency == null || dependency.getDependsOn().isEmpty()) {
            return true; // 没有依赖，可以执行
        }

        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime checkTime = now.minusHours(1); // 检查最近1小时内的执行情况

            for (String dependentTask : dependency.getDependsOn()) {
                Optional<TaskExecutionRecord> lastExecution = taskExecutionRecordRepository
                        .findFirstByTaskNameOrderByExecutionTimeDesc(dependentTask);
                
                if (lastExecution.isEmpty()) {
                    log.debug("依赖任务 {} 没有执行记录", dependentTask);
                    if (dependency.isWaitForAll()) {
                        return false;
                    }
                    continue;
                }

                TaskExecutionRecord record = lastExecution.get();
                
                // 检查依赖任务是否在指定时间内成功执行
                if (record.getExecutionTime().isBefore(checkTime) || 
                    record.getStatus() != TaskExecutionRecord.ExecutionStatus.SUCCESS) {
                    log.debug("依赖任务 {} 未满足条件: 执行时间={}, 状态={}", 
                            dependentTask, record.getExecutionTime(), record.getStatus());
                    if (dependency.isWaitForAll()) {
                        return false;
                    }
                }
            }

            return true;
        } catch (Exception e) {
            log.error("检查任务依赖失败: {}", taskName, e);
            return false;
        }
    }

    /**
     * 获取任务健康状态
     */
    public Map<String, Object> getTaskHealthStatus(String taskName) {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 基本信息
            TaskConfig config = taskConfigs.get(taskName);
            health.put("taskName", taskName);
            health.put("enabled", config != null ? config.isEnabled() : false);
            health.put("scheduled", scheduledTasks.containsKey(taskName));
            
            // 执行统计
            LocalDateTime since = LocalDateTime.now().minusDays(7);
            List<TaskExecutionRecord> recentRecords = taskExecutionRecordRepository
                    .findByTaskNameAndExecutionTimeBetween(taskName, since, LocalDateTime.now());
            
            long totalExecutions = recentRecords.size();
            long successCount = recentRecords.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.SUCCESS)
                    .count();
            long failureCount = recentRecords.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                    .count();
            
            health.put("totalExecutions", totalExecutions);
            health.put("successCount", successCount);
            health.put("failureCount", failureCount);
            health.put("successRate", totalExecutions > 0 ? (double) successCount / totalExecutions : 0.0);
            
            // 平均执行时间
            OptionalDouble avgDuration = recentRecords.stream()
                    .filter(r -> r.getDurationMs() != null)
                    .mapToLong(TaskExecutionRecord::getDurationMs)
                    .average();
            health.put("avgDurationMs", avgDuration.orElse(0.0));
            
            // 最后执行时间
            Optional<TaskExecutionRecord> lastExecution = taskExecutionRecordRepository
                    .findFirstByTaskNameOrderByExecutionTimeDesc(taskName);
            health.put("lastExecutionTime", lastExecution.map(TaskExecutionRecord::getExecutionTime).orElse(null));
            health.put("lastExecutionStatus", lastExecution.map(r -> r.getStatus().name()).orElse(null));
            
            // 依赖状态
            TaskDependency dependency = taskDependencies.get(taskName);
            health.put("hasDependencies", dependency != null && !dependency.getDependsOn().isEmpty());
            health.put("dependenciesMet", checkTaskDependencies(taskName));
            
            return health;
        } catch (Exception e) {
            log.error("获取任务健康状态失败: {}", taskName, e);
            return Map.of("error", "获取健康状态失败: " + e.getMessage());
        }
    }

    // 私有辅助方法
    private void scheduleTask(TaskConfig config) {
        // 这里应该实现实际的任务调度逻辑
        // 由于这是一个示例，我们只是记录日志
        log.info("调度任务: {} with cron: {}", config.getTaskName(), config.getCronExpression());
    }

    private void stopTask(String taskName) {
        ScheduledFuture<?> future = scheduledTasks.remove(taskName);
        if (future != null) {
            future.cancel(false);
            log.debug("停止任务调度: {}", taskName);
        }
    }

    private boolean isValidCronExpression(String cronExpression) {
        try {
            CronExpression.parse(cronExpression);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
