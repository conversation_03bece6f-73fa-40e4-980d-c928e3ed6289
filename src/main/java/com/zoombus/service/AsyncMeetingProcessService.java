package com.zoombus.service;

import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.ZoomMeetingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 异步会议处理服务
 * 独立的服务类，确保@Async注解生效
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AsyncMeetingProcessService {

    private final BillingMonitorService billingMonitorService;
    private final MeetingSettlementService meetingSettlementService;
    private final ZoomUserPmiService zoomUserPmiService;
    private final ZoomMeetingRepository zoomMeetingRepository;
    private final MeetingReportService meetingReportService;

    /**
     * 异步处理会议结束后的操作（通过UUID）
     *
     * @param meetingUuid 会议UUID
     */
    @Async
    public void asyncProcessMeetingEndByUuid(String meetingUuid) {
        log.info("🔄 开始异步处理会议结束: meetingUuid={}, thread={}",
                meetingUuid, Thread.currentThread().getName());

        try {
            // 查找会议记录
            Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);
            if (!meetingOpt.isPresent()) {
                log.warn("未找到会议记录，跳过异步处理: meetingUuid={}", meetingUuid);
                return;
            }

            ZoomMeeting meeting = meetingOpt.get();
            Long meetingId = meeting.getId();

            // 停止计费监控
            log.debug("停止计费监控: meetingUuid={}, meetingId={}", meetingUuid, meetingId);
            billingMonitorService.stopBillingMonitor(meetingId);
            log.info("计费监控已停止: meetingUuid={}, meetingId={}", meetingUuid, meetingId);

            // 执行结算
            log.debug("开始执行结算: meetingUuid={}, meetingId={}", meetingUuid, meetingId);
            meetingSettlementService.settleMeeting(meetingId);
            log.info("会议结算完成: meetingUuid={}, meetingId={}", meetingUuid, meetingId);

            // 释放ZoomUser账号（仅PMI类型会议需要释放）
            if (isPmiMeeting(meeting)) {
                log.debug("开始释放PMI会议ZoomUser账号: meetingUuid={}, meetingId={}, creationSource={}",
                        meetingUuid, meetingId, meeting.getCreationSource());
                zoomUserPmiService.releaseZoomUser(meetingId);
                log.info("PMI会议ZoomUser账号已释放: meetingUuid={}, meetingId={}, creationSource={}",
                        meetingUuid, meetingId, meeting.getCreationSource());
            } else {
                log.info("非PMI会议，跳过ZoomUser账号释放: meetingUuid={}, meetingId={}, creationSource={}",
                        meetingUuid, meetingId, meeting.getCreationSource());
            }

            // 触发会议报告获取（延迟5分钟，确保Zoom API数据已准备好）
            triggerMeetingReportFetch(meetingUuid, meeting.getZoomMeetingId());

            log.info("✅ 会议结束异步处理完成: meetingUuid={}, meetingId={}, thread={}",
                    meetingUuid, meetingId, Thread.currentThread().getName());

        } catch (Exception e) {
            log.error("❌ 会议结束异步处理异常: meetingUuid={}, thread={}, error={}",
                    meetingUuid, Thread.currentThread().getName(), e.getMessage(), e);

            // 异步处理失败不应该影响主流程，只记录错误
        }
    }

    /**
     * 异步处理会议结束后的操作（通过数据库ID - 已废弃）
     * @deprecated 使用 asyncProcessMeetingEndByUuid 替代
     *
     * @param meetingId 会议ID
     */
    @Deprecated
    @Async
    public void asyncProcessMeetingEnd(Long meetingId) {
        log.info("🔄 开始异步处理会议结束: meetingId={}, thread={}",
                meetingId, Thread.currentThread().getName());

        try {
            // 停止计费监控
            log.debug("停止计费监控: meetingId={}", meetingId);
            billingMonitorService.stopBillingMonitor(meetingId);
            log.info("计费监控已停止: meetingId={}", meetingId);

            // 执行结算
            log.debug("开始执行结算: meetingId={}", meetingId);
            meetingSettlementService.settleMeeting(meetingId);
            log.info("会议结算完成: meetingId={}", meetingId);

            // 释放ZoomUser账号（仅PMI类型会议需要释放）
            Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository.findById(meetingId);
            if (meetingOpt.isPresent()) {
                ZoomMeeting meeting = meetingOpt.get();
                if (isPmiMeeting(meeting)) {
                    log.debug("开始释放PMI会议ZoomUser账号: meetingId={}, creationSource={}",
                            meetingId, meeting.getCreationSource());
                    zoomUserPmiService.releaseZoomUser(meetingId);
                    log.info("PMI会议ZoomUser账号已释放: meetingId={}, creationSource={}",
                            meetingId, meeting.getCreationSource());
                } else {
                    log.info("非PMI会议，跳过ZoomUser账号释放: meetingId={}, creationSource={}",
                            meetingId, meeting.getCreationSource());
                }
            } else {
                log.warn("未找到会议记录，跳过ZoomUser账号释放: meetingId={}", meetingId);
            }

            log.info("✅ 会议结束异步处理完成: meetingId={}, thread={}",
                    meetingId, Thread.currentThread().getName());

        } catch (Exception e) {
            log.error("❌ 会议结束异步处理异常: meetingId={}, thread={}, error={}",
                    meetingId, Thread.currentThread().getName(), e.getMessage(), e);

            // 异步处理失败不应该影响主流程，只记录错误
        }
    }

    /**
     * 判断是否为PMI会议
     */
    private boolean isPmiMeeting(ZoomMeeting meeting) {
        return meeting.getCreationSource() == ZoomMeeting.CreationSource.PMI_MEETING;
    }

    /**
     * 触发会议报告获取
     */
    private void triggerMeetingReportFetch(String meetingUuid, String zoomMeetingId) {
        try {
            log.info("触发会议报告获取: meetingUuid={}, zoomMeetingId={}", meetingUuid, zoomMeetingId);

            // 创建延迟执行的报告获取任务（延迟5分钟，确保Zoom API数据已准备好）
            LocalDateTime scheduledTime = LocalDateTime.now().plusMinutes(5);
            meetingReportService.createDelayedReportTask(meetingUuid, zoomMeetingId, scheduledTime, 3);

            log.info("会议报告获取任务已创建: meetingUuid={}, scheduledTime={}", meetingUuid, scheduledTime);

        } catch (Exception e) {
            log.error("触发会议报告获取失败: meetingUuid={}, zoomMeetingId={}", meetingUuid, zoomMeetingId, e);
            // 不抛出异常，避免影响主流程
        }
    }
}
