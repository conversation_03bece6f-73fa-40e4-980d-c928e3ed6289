package com.zoombus.service;

import com.zoombus.entity.TaskExecutionRecord;
import com.zoombus.repository.TaskExecutionRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

/**
 * 异步定时任务执行器
 * 提供定时任务的异步执行、事务隔离、错误处理等功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AsyncScheduledTaskExecutor {
    
    private final TaskExecutionRecordRepository taskExecutionRecordRepository;
    private final ScheduledTaskErrorHandler errorHandler;
    private final TaskExecutionTracker executionTracker;
    
    /**
     * 异步执行定时任务
     * 
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param taskLogic 任务执行逻辑
     * @return CompletableFuture<TaskExecutionRecord>
     */
    @Async("scheduledTaskExecutor")
    public CompletableFuture<TaskExecutionRecord> executeAsync(
            String taskName, 
            String taskType, 
            Supplier<TaskExecutionResult> taskLogic) {
        
        return executeAsync(taskName, taskType, taskLogic, TaskExecutionConfig.defaultConfig());
    }
    
    /**
     * 异步执行定时任务（带配置）
     * 
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param taskLogic 任务执行逻辑
     * @param config 执行配置
     * @return CompletableFuture<TaskExecutionRecord>
     */
    @Async("scheduledTaskExecutor")
    public CompletableFuture<TaskExecutionRecord> executeAsync(
            String taskName, 
            String taskType, 
            Supplier<TaskExecutionResult> taskLogic,
            TaskExecutionConfig config) {
        
        TaskExecutionRecord record = null;
        try {
            // 检查是否已有相同任务在执行
            if (config.isPreventConcurrentExecution() && executionTracker.isTaskRunning(taskName)) {
                log.warn("任务 {} 已在执行中，跳过本次执行", taskName);
                return CompletableFuture.completedFuture(null);
            }
            
            // 创建执行记录
            record = createExecutionRecord(taskName, taskType, config);
            record = saveExecutionRecord(record);
            
            // 标记任务开始执行
            executionTracker.markTaskAsRunning(taskName, record.getId());
            record.markAsStarted();
            record = updateExecutionRecord(record);
            
            log.info("开始执行定时任务: {} (ID: {})", taskName, record.getId());
            
            // 执行任务逻辑
            TaskExecutionResult result = taskLogic.get();
            
            // 更新执行结果
            record.markAsSuccess();
            record.setProcessedCount(result.getProcessedCount());
            record.setSuccessCount(result.getSuccessCount());
            record.setFailureCount(result.getFailedCount());
            record.setExecutionResult(result.toJson());

            record = updateExecutionRecord(record);

            log.info("定时任务执行成功: {} (ID: {}), 耗时: {}ms, 处理: {}, 成功: {}, 失败: {}",
                    taskName, record.getId(), record.getDurationMs(),
                    record.getProcessedCount(), record.getSuccessCount(), record.getFailureCount());
            
            return CompletableFuture.completedFuture(record);
            
        } catch (Exception e) {
            log.error("定时任务执行失败: {} (ID: {})", taskName, record != null ? record.getId() : "N/A", e);
            
            if (record != null) {
                record.markAsFailed(e.getMessage(), getStackTrace(e));
                record = updateExecutionRecord(record);
                
                // 处理重试逻辑
                if (config.getMaxRetryCount() > 0 && record.canRetry()) {
                    errorHandler.handleTaskFailure(record, config);
                }
            }
            
            return CompletableFuture.completedFuture(record);
            
        } finally {
            if (record != null) {
                executionTracker.markTaskAsCompleted(taskName, record.getId());
            }
        }
    }
    
    /**
     * 在独立事务中保存执行记录
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TaskExecutionRecord saveExecutionRecord(TaskExecutionRecord record) {
        return taskExecutionRecordRepository.save(record);
    }
    
    /**
     * 在独立事务中更新执行记录
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TaskExecutionRecord updateExecutionRecord(TaskExecutionRecord record) {
        return taskExecutionRecordRepository.save(record);
    }
    
    /**
     * 创建执行记录
     */
    private TaskExecutionRecord createExecutionRecord(String taskName, String taskType, TaskExecutionConfig config) {
        return TaskExecutionRecord.builder()
                .taskName(taskName)
                .taskType(taskType)
                .status(TaskExecutionRecord.ExecutionStatus.PENDING)
                .maxRetryCount(config.getMaxRetryCount())
                .executionTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 获取异常堆栈信息
     */
    private String getStackTrace(Exception e) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }
    
    /**
     * 任务执行结果
     */
    public static class TaskExecutionResult {
        private int processedCount = 0;
        private int successCount = 0;
        private int failedCount = 0;
        private String resultData;
        
        public TaskExecutionResult() {}
        
        public TaskExecutionResult(int processedCount, int successCount, int failedCount) {
            this.processedCount = processedCount;
            this.successCount = successCount;
            this.failedCount = failedCount;
        }
        
        // Getters and Setters
        public int getProcessedCount() { return processedCount; }
        public void setProcessedCount(int processedCount) { this.processedCount = processedCount; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public String getResultData() { return resultData; }
        public void setResultData(String resultData) { this.resultData = resultData; }
        
        public String toJson() {
            return String.format("{\"processedCount\":%d,\"successCount\":%d,\"failedCount\":%d,\"resultData\":\"%s\"}", 
                    processedCount, successCount, failedCount, resultData != null ? resultData : "");
        }
    }
    
    /**
     * 任务执行配置
     */
    public static class TaskExecutionConfig {
        private int maxRetryCount = 3;
        private long retryDelayMs = 60000; // 1分钟
        private boolean preventConcurrentExecution = true;
        private long timeoutMs = 300000; // 5分钟
        
        public static TaskExecutionConfig defaultConfig() {
            return new TaskExecutionConfig();
        }
        
        public static TaskExecutionConfig withRetry(int maxRetryCount, long retryDelayMs) {
            TaskExecutionConfig config = new TaskExecutionConfig();
            config.maxRetryCount = maxRetryCount;
            config.retryDelayMs = retryDelayMs;
            return config;
        }
        
        // Getters and Setters
        public int getMaxRetryCount() { return maxRetryCount; }
        public void setMaxRetryCount(int maxRetryCount) { this.maxRetryCount = maxRetryCount; }
        
        public long getRetryDelayMs() { return retryDelayMs; }
        public void setRetryDelayMs(long retryDelayMs) { this.retryDelayMs = retryDelayMs; }
        
        public boolean isPreventConcurrentExecution() { return preventConcurrentExecution; }
        public void setPreventConcurrentExecution(boolean preventConcurrentExecution) { this.preventConcurrentExecution = preventConcurrentExecution; }
        
        public long getTimeoutMs() { return timeoutMs; }
        public void setTimeoutMs(long timeoutMs) { this.timeoutMs = timeoutMs; }
    }
}
