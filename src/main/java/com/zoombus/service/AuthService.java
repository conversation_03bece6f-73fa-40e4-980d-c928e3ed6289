package com.zoombus.service;

import com.zoombus.dto.LoginRequest;
import com.zoombus.dto.LoginResponse;
import com.zoombus.entity.AdminUser;
import com.zoombus.security.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {
    
    private final AdminUserService adminUserService;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider tokenProvider;
    
    /**
     * 管理员登录
     */
    @Transactional
    public LoginResponse login(LoginRequest request) {
        log.info("管理员登录尝试: {}", request.getUsername());
        
        // 查找用户
        AdminUser adminUser = adminUserService.findByUsername(request.getUsername())
                .orElseThrow(() -> new IllegalArgumentException("用户名或密码错误"));
        
        // 检查用户状态
        if (adminUser.getStatus() != AdminUser.AdminStatus.ACTIVE) {
            throw new IllegalArgumentException("账号已被禁用，请联系管理员");
        }
        
        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), adminUser.getPassword())) {
            throw new IllegalArgumentException("用户名或密码错误");
        }
        
        // 更新最后登录时间
        adminUserService.updateLastLoginTime(adminUser.getId());
        
        // 生成JWT token
        String token = tokenProvider.generateToken(
                adminUser.getUsername(),
                adminUser.getId(),
                adminUser.getRole().name()
        );
        
        log.info("管理员登录成功: {}", adminUser.getUsername());
        
        return new LoginResponse(token, LoginResponse.AdminUserInfo.fromAdminUser(adminUser));
    }
}
