package com.zoombus.service;

import com.zoombus.entity.TaskExecutionRecord;
import com.zoombus.repository.TaskExecutionRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 批量任务执行服务
 * 提供批量插入、更新和查询优化功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BatchTaskExecutionService {

    private final TaskExecutionRecordRepository taskExecutionRecordRepository;
    private final TaskExecutionCacheService cacheService;

    // 批量操作的默认大小
    private static final int DEFAULT_BATCH_SIZE = 100;
    private static final int MAX_BATCH_SIZE = 1000;

    /**
     * 批量保存任务执行记录
     */
    @Transactional
    public List<TaskExecutionRecord> batchSaveTaskRecords(List<TaskExecutionRecord> records) {
        if (records == null || records.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 限制批量大小
            if (records.size() > MAX_BATCH_SIZE) {
                log.warn("批量保存记录数量超过限制: {} > {}, 将分批处理", records.size(), MAX_BATCH_SIZE);
                return batchSaveInChunks(records, MAX_BATCH_SIZE);
            }

            log.info("批量保存任务执行记录: count={}", records.size());
            List<TaskExecutionRecord> savedRecords = taskExecutionRecordRepository.saveAll(records);

            // 异步更新缓存
            asyncUpdateCacheAfterBatchSave(savedRecords);

            return savedRecords;
        } catch (Exception e) {
            log.error("批量保存任务执行记录失败", e);
            throw e;
        }
    }

    /**
     * 分块批量保存
     */
    private List<TaskExecutionRecord> batchSaveInChunks(List<TaskExecutionRecord> records, int chunkSize) {
        List<TaskExecutionRecord> allSavedRecords = new ArrayList<>();
        
        for (int i = 0; i < records.size(); i += chunkSize) {
            int endIndex = Math.min(i + chunkSize, records.size());
            List<TaskExecutionRecord> chunk = records.subList(i, endIndex);
            
            log.debug("保存批次 {}/{}: 记录数={}", (i / chunkSize) + 1, 
                    (records.size() + chunkSize - 1) / chunkSize, chunk.size());
            
            List<TaskExecutionRecord> savedChunk = taskExecutionRecordRepository.saveAll(chunk);
            allSavedRecords.addAll(savedChunk);
        }
        
        // 异步更新缓存
        asyncUpdateCacheAfterBatchSave(allSavedRecords);
        
        return allSavedRecords;
    }

    /**
     * 批量更新任务执行记录状态
     */
    @Transactional
    public int batchUpdateTaskStatus(List<Long> recordIds, TaskExecutionRecord.ExecutionStatus newStatus) {
        if (recordIds == null || recordIds.isEmpty()) {
            return 0;
        }

        try {
            log.info("批量更新任务状态: recordIds={}, newStatus={}", recordIds.size(), newStatus);
            
            // 分批更新以避免SQL语句过长
            int totalUpdated = 0;
            int batchSize = DEFAULT_BATCH_SIZE;
            
            for (int i = 0; i < recordIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, recordIds.size());
                List<Long> batchIds = recordIds.subList(i, endIndex);
                
                int updated = taskExecutionRecordRepository.batchUpdateStatus(batchIds, newStatus);
                totalUpdated += updated;
                
                log.debug("批量更新状态批次完成: updated={}", updated);
            }

            // 清除相关缓存
            asyncClearRelatedCache(recordIds);

            return totalUpdated;
        } catch (Exception e) {
            log.error("批量更新任务状态失败", e);
            throw e;
        }
    }

    /**
     * 批量删除过期的任务执行记录
     */
    @Transactional
    public int batchDeleteExpiredRecords(LocalDateTime cutoffTime, int batchSize) {
        if (batchSize <= 0 || batchSize > MAX_BATCH_SIZE) {
            batchSize = DEFAULT_BATCH_SIZE;
        }

        try {
            log.info("开始批量删除过期记录: cutoffTime={}, batchSize={}", cutoffTime, batchSize);
            
            int totalDeleted = 0;
            int deletedInBatch;
            
            do {
                // 分批删除以避免长时间锁表
                deletedInBatch = taskExecutionRecordRepository.deleteExpiredRecordsBatch(cutoffTime, batchSize);
                totalDeleted += deletedInBatch;
                
                log.debug("删除过期记录批次完成: deleted={}", deletedInBatch);
                
                // 短暂休息以减少数据库压力
                if (deletedInBatch > 0) {
                    try {
                        Thread.sleep(100); // 休息100ms
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } while (deletedInBatch > 0);

            log.info("批量删除过期记录完成: totalDeleted={}", totalDeleted);
            
            // 清除所有缓存
            cacheService.clearAllTaskCache();
            
            return totalDeleted;
        } catch (Exception e) {
            log.error("批量删除过期记录失败", e);
            throw e;
        }
    }

    /**
     * 批量查询任务执行记录（带缓存优化）
     */
    public List<TaskExecutionRecord> batchQueryTaskRecords(List<Long> recordIds) {
        if (recordIds == null || recordIds.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<TaskExecutionRecord> results = new ArrayList<>();
            List<Long> uncachedIds = new ArrayList<>();

            // 先从缓存中获取
            for (Long recordId : recordIds) {
                TaskExecutionRecord cached = cacheService.getCachedTaskRecord(recordId);
                if (cached != null) {
                    results.add(cached);
                } else {
                    uncachedIds.add(recordId);
                }
            }

            // 查询未缓存的记录
            if (!uncachedIds.isEmpty()) {
                List<TaskExecutionRecord> uncachedRecords = taskExecutionRecordRepository.findAllById(uncachedIds);
                results.addAll(uncachedRecords);

                // 异步缓存查询结果
                asyncCacheQueryResults(uncachedRecords);
            }

            log.debug("批量查询任务记录: total={}, cached={}, queried={}", 
                    recordIds.size(), recordIds.size() - uncachedIds.size(), uncachedIds.size());

            return results;
        } catch (Exception e) {
            log.error("批量查询任务记录失败", e);
            throw e;
        }
    }

    /**
     * 获取任务执行统计信息（带缓存）
     */
    public List<Object[]> batchGetTaskStatistics(List<String> taskNames, LocalDateTime startTime, LocalDateTime endTime) {
        if (taskNames == null || taskNames.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 分批查询以避免SQL语句过长
            List<Object[]> allResults = new ArrayList<>();
            int batchSize = DEFAULT_BATCH_SIZE;
            
            for (int i = 0; i < taskNames.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, taskNames.size());
                List<String> batchTaskNames = taskNames.subList(i, endIndex);
                
                List<Object[]> batchResults = taskExecutionRecordRepository
                        .batchGetTaskStatistics(batchTaskNames, startTime, endTime);
                allResults.addAll(batchResults);
            }

            log.debug("批量获取任务统计: taskCount={}, resultCount={}", taskNames.size(), allResults.size());
            return allResults;
        } catch (Exception e) {
            log.error("批量获取任务统计失败", e);
            throw e;
        }
    }

    /**
     * 异步更新缓存（批量保存后）
     */
    @Async
    protected CompletableFuture<Void> asyncUpdateCacheAfterBatchSave(List<TaskExecutionRecord> savedRecords) {
        try {
            // 缓存新保存的记录
            for (TaskExecutionRecord record : savedRecords) {
                cacheService.cacheTaskRecord(record);
            }

            // 按任务名分组，更新最近记录缓存
            savedRecords.stream()
                    .collect(java.util.stream.Collectors.groupingBy(TaskExecutionRecord::getTaskName))
                    .forEach((taskName, records) -> {
                        // 清除旧的最近记录缓存，让下次查询时重新加载
                        cacheService.clearTaskCache(taskName);
                    });

            log.debug("异步更新缓存完成: recordCount={}", savedRecords.size());
        } catch (Exception e) {
            log.error("异步更新缓存失败", e);
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 异步清除相关缓存
     */
    @Async
    protected CompletableFuture<Void> asyncClearRelatedCache(List<Long> recordIds) {
        try {
            // 查询受影响的任务名
            List<String> affectedTaskNames = taskExecutionRecordRepository.findTaskNamesByIds(recordIds);
            
            // 清除相关任务的缓存
            for (String taskName : affectedTaskNames) {
                cacheService.clearTaskCache(taskName);
            }

            log.debug("异步清除相关缓存完成: recordIds={}, affectedTasks={}", 
                    recordIds.size(), affectedTaskNames.size());
        } catch (Exception e) {
            log.error("异步清除相关缓存失败", e);
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 异步缓存查询结果
     */
    @Async
    protected CompletableFuture<Void> asyncCacheQueryResults(List<TaskExecutionRecord> records) {
        try {
            for (TaskExecutionRecord record : records) {
                cacheService.cacheTaskRecord(record);
            }
            log.debug("异步缓存查询结果完成: recordCount={}", records.size());
        } catch (Exception e) {
            log.error("异步缓存查询结果失败", e);
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 数据清理和优化
     */
    @Transactional
    public void cleanupAndOptimize() {
        try {
            log.info("开始数据清理和优化");

            // 清理30天前的数据
            LocalDateTime cleanupDate = LocalDateTime.now().minusDays(30);
            int deletedCount = batchDeleteExpiredRecords(cleanupDate);

            // 清理缓存
            cacheService.cleanExpiredCache();

            log.info("数据清理完成，删除了 {} 条过期记录", deletedCount);
        } catch (Exception e) {
            log.error("数据清理和优化失败", e);
        }
    }

    /**
     * 批量删除过期的任务执行记录
     */
    @Transactional
    public int batchDeleteExpiredRecords(LocalDateTime beforeDate) {
        try {
            // 使用Repository的批量删除方法
            int deletedCount = taskExecutionRecordRepository.deleteOldRecords(beforeDate);

            // 清除相关缓存
            cacheService.clearAllCache();

            log.info("删除了 {} 条过期的任务执行记录", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("批量删除过期任务执行记录失败", e);
            throw e;
        }
    }
}
