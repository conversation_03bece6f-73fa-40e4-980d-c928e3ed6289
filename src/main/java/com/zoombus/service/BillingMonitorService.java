package com.zoombus.service;

import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * 实时计费监控服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BillingMonitorService {
    
    private final ZoomMeetingRepository zoomMeetingRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final TaskScheduler taskScheduler;
    
    private final Map<Long, ScheduledFuture<?>> billingTasks = new ConcurrentHashMap<>();
    
    /**
     * 开始计费监控
     */
    public void startBillingMonitor(Long meetingId) {
        // 检查是否已经在监控中
        if (billingTasks.containsKey(meetingId)) {
            log.warn("会议 {} 已经在计费监控中", meetingId);
            return;
        }
        
        ScheduledFuture<?> task = taskScheduler.scheduleAtFixedRate(
            () -> processBillingTick(meetingId),
            Duration.ofMinutes(1)
        );
        
        billingTasks.put(meetingId, task);
        log.info("开始监控会议 {} 的计费", meetingId);
    }
    
    /**
     * 停止计费监控
     */
    public void stopBillingMonitor(Long meetingId) {
        ScheduledFuture<?> task = billingTasks.remove(meetingId);
        if (task != null) {
            task.cancel(false);
            log.info("停止监控会议 {} 的计费", meetingId);
        }
    }
    
    /**
     * 处理每分钟计费
     */
    @Transactional
    public void processBillingTick(Long meetingId) {
        try {
            ZoomMeeting meeting = zoomMeetingRepository.findById(meetingId)
                .orElse(null);
            
            if (meeting == null || meeting.getStatus() != ZoomMeeting.MeetingStatus.STARTED) {
                stopBillingMonitor(meetingId);
                return;
            }
            
            // 增加会议计费分钟数
            meeting.setBilledMinutes(meeting.getBilledMinutes() + 1);
            zoomMeetingRepository.save(meeting);
            
            // 增加PMI待扣时长
            PmiRecord pmiRecord = pmiRecordRepository.findById(meeting.getPmiRecordId())
                .orElse(null);
            if (pmiRecord != null) {
                int currentPending = pmiRecord.getPendingDeductMinutes() != null ? 
                    pmiRecord.getPendingDeductMinutes() : 0;
                pmiRecord.setPendingDeductMinutes(currentPending + 1);
                pmiRecordRepository.save(pmiRecord);
            }
            
            log.debug("会议 {} 计费 +1 分钟，总计费: {} 分钟", 
                    meetingId, meeting.getBilledMinutes());
            
        } catch (Exception e) {
            log.error("处理会议 {} 计费时发生错误", meetingId, e);
        }
    }
    
    /**
     * 获取当前监控的会议数量
     */
    public int getMonitoringMeetingsCount() {
        return billingTasks.size();
    }
    
    /**
     * 获取所有正在监控的会议ID
     */
    public java.util.Set<Long> getMonitoringMeetingIds() {
        return billingTasks.keySet();
    }
    
    /**
     * 停止所有计费监控
     */
    public void stopAllBillingMonitors() {
        billingTasks.forEach((meetingId, task) -> {
            task.cancel(false);
            log.info("停止监控会议 {} 的计费", meetingId);
        });
        billingTasks.clear();
        log.info("已停止所有计费监控");
    }
    
    /**
     * 重启计费监控（系统启动时调用）
     */
    public void restartBillingMonitors() {
        // 查找所有正在进行中的会议
        var activeMeetings = zoomMeetingRepository.findActiveMeetings();
        
        for (ZoomMeeting meeting : activeMeetings) {
            if (meeting.getBillingMode() == PmiRecord.BillingMode.BY_TIME) {
                startBillingMonitor(meeting.getId());
                log.info("重启会议 {} 的计费监控", meeting.getId());
            }
        }
        
        log.info("重启了 {} 个会议的计费监控", activeMeetings.size());
    }
    
    /**
     * 检查计费监控状态
     */
    public BillingMonitorStatus getBillingMonitorStatus() {
        int totalMonitoring = billingTasks.size();
        int activeMeetings = zoomMeetingRepository.findActiveMeetings().size();
        
        return new BillingMonitorStatus(totalMonitoring, activeMeetings);
    }
    
    /**
     * 计费监控状态类
     */
    public static class BillingMonitorStatus {
        private final int monitoringCount;
        private final int activeMeetingsCount;
        
        public BillingMonitorStatus(int monitoringCount, int activeMeetingsCount) {
            this.monitoringCount = monitoringCount;
            this.activeMeetingsCount = activeMeetingsCount;
        }
        
        public int getMonitoringCount() {
            return monitoringCount;
        }
        
        public int getActiveMeetingsCount() {
            return activeMeetingsCount;
        }
        
        public boolean isHealthy() {
            return monitoringCount == activeMeetingsCount;
        }
        
        public String getStatusDescription() {
            if (isHealthy()) {
                return String.format("正常 - 监控中: %d, 活跃会议: %d", monitoringCount, activeMeetingsCount);
            } else {
                return String.format("异常 - 监控中: %d, 活跃会议: %d", monitoringCount, activeMeetingsCount);
            }
        }
    }
    
    /**
     * 强制同步计费监控状态
     */
    @Transactional
    public void syncBillingMonitorStatus() {
        // 停止所有当前监控
        stopAllBillingMonitors();
        
        // 重新启动监控
        restartBillingMonitors();
        
        log.info("已强制同步计费监控状态");
    }
    
    /**
     * 手动触发计费（测试用）
     */
    @Transactional
    public void manualBillingTick(Long meetingId) {
        log.info("手动触发会议 {} 的计费", meetingId);
        processBillingTick(meetingId);
    }
}
