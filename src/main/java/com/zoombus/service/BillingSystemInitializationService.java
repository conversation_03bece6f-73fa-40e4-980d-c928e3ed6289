package com.zoombus.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 计费系统初始化服务
 * 在应用启动时执行必要的初始化操作
 */
@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "zoombus.billing.auto-init.enabled", havingValue = "true", matchIfMissing = true)
public class BillingSystemInitializationService implements ApplicationRunner {
    
    private final BillingMonitorService billingMonitorService;
    private final ZoomUserPmiService zoomUserPmiService;
    private final PmiBillingModeService pmiBillingModeService;
    private final MeetingSettlementService meetingSettlementService;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化计费系统...");
        
        try {
            // 1. 处理过期的时间窗口
            processExpiredWindows();
            
            // 2. 初始化ZoomUser的原始PMI
            initializeZoomUserPmis();
            
            // 3. 重启计费监控
            restartBillingMonitors();
            
            // 4. 结算未结算的会议
            settleUnfinishedMeetings();
            
            log.info("计费系统初始化完成");
            
        } catch (Exception e) {
            log.error("计费系统初始化失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }
    
    /**
     * 处理过期的时间窗口
     */
    private void processExpiredWindows() {
        try {
            log.info("处理过期的时间窗口...");
            pmiBillingModeService.processExpiredWindows();
            log.info("过期时间窗口处理完成");
        } catch (Exception e) {
            log.error("处理过期时间窗口失败", e);
        }
    }
    
    /**
     * 初始化ZoomUser的原始PMI
     */
    private void initializeZoomUserPmis() {
        try {
            log.info("初始化ZoomUser原始PMI...");
            zoomUserPmiService.initializeAllOriginalPmis();
            log.info("ZoomUser原始PMI初始化完成");
        } catch (Exception e) {
            log.error("初始化ZoomUser原始PMI失败", e);
        }
    }
    
    /**
     * 重启计费监控
     */
    private void restartBillingMonitors() {
        try {
            log.info("重启计费监控...");
            billingMonitorService.restartBillingMonitors();
            
            BillingMonitorService.BillingMonitorStatus status = billingMonitorService.getBillingMonitorStatus();
            log.info("计费监控重启完成: {}", status.getStatusDescription());
        } catch (Exception e) {
            log.error("重启计费监控失败", e);
        }
    }
    
    /**
     * 结算未结算的会议
     */
    private void settleUnfinishedMeetings() {
        try {
            log.info("结算未结算的会议...");
            MeetingSettlementService.BatchSettlementResult result = meetingSettlementService.batchSettleMeetings();
            
            if (result.getTotalCount() > 0) {
                log.info("未结算会议处理完成: 总数={}, 成功={}, 失败={}", 
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
            } else {
                log.info("没有需要结算的会议");
            }
        } catch (Exception e) {
            log.error("结算未结算会议失败", e);
        }
    }
    
    /**
     * 手动触发初始化（用于管理接口）
     */
    public void manualInitialize() {
        log.info("手动触发计费系统初始化");
        
        try {
            run(null);
        } catch (Exception e) {
            log.error("手动初始化失败", e);
            throw new RuntimeException("手动初始化失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取初始化状态
     */
    public InitializationStatus getInitializationStatus() {
        try {
            // 检查计费监控状态
            BillingMonitorService.BillingMonitorStatus monitorStatus = billingMonitorService.getBillingMonitorStatus();
            
            // 检查ZoomUser状态
            ZoomUserPmiService.ZoomUserUsageStats userStats = zoomUserPmiService.getZoomUserUsageStats();
            
            boolean isHealthy = monitorStatus.isHealthy() && userStats.isHealthy();
            
            return new InitializationStatus(
                isHealthy,
                monitorStatus.getStatusDescription(),
                userStats.getTotalUsers(),
                userStats.getAvailableUsers(),
                userStats.getInUseUsers()
            );
            
        } catch (Exception e) {
            log.error("获取初始化状态失败", e);
            return new InitializationStatus(false, "获取状态失败: " + e.getMessage(), 0, 0, 0);
        }
    }
    
    /**
     * 初始化状态类
     */
    public static class InitializationStatus {
        private final boolean healthy;
        private final String statusDescription;
        private final long totalZoomUsers;
        private final long availableZoomUsers;
        private final long inUseZoomUsers;
        
        public InitializationStatus(boolean healthy, String statusDescription, 
                                  long totalZoomUsers, long availableZoomUsers, long inUseZoomUsers) {
            this.healthy = healthy;
            this.statusDescription = statusDescription;
            this.totalZoomUsers = totalZoomUsers;
            this.availableZoomUsers = availableZoomUsers;
            this.inUseZoomUsers = inUseZoomUsers;
        }
        
        public boolean isHealthy() { return healthy; }
        public String getStatusDescription() { return statusDescription; }
        public long getTotalZoomUsers() { return totalZoomUsers; }
        public long getAvailableZoomUsers() { return availableZoomUsers; }
        public long getInUseZoomUsers() { return inUseZoomUsers; }
        
        public String getSummary() {
            return String.format("健康状态: %s, ZoomUser: 总数=%d, 可用=%d, 使用中=%d, 状态: %s",
                healthy ? "正常" : "异常", totalZoomUsers, availableZoomUsers, inUseZoomUsers, statusDescription);
        }
    }
}
