package com.zoombus.service;

import com.zoombus.entity.AdminUser;
import com.zoombus.repository.AdminUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitializationService implements ApplicationRunner {
    
    private final AdminUserRepository adminUserRepository;
    private final PasswordEncoder passwordEncoder;
    
    @Override
    @Transactional
    public void run(ApplicationArguments args) throws Exception {
        initializeDefaultAdminUser();
    }
    
    /**
     * 初始化默认管理员用户
     */
    private void initializeDefaultAdminUser() {
        String defaultUsername = "admin";
        
        // 检查是否已存在默认管理员
        if (adminUserRepository.existsByUsername(defaultUsername)) {
            log.info("默认管理员用户已存在，跳过初始化");
            return;
        }
        
        // 创建默认管理员用户
        AdminUser defaultAdmin = new AdminUser();
        defaultAdmin.setUsername(defaultUsername);
        defaultAdmin.setEmail("<EMAIL>");
        defaultAdmin.setPassword(passwordEncoder.encode("admin123"));
        defaultAdmin.setFullName("系统管理员");
        defaultAdmin.setRole(AdminUser.AdminRole.SUPER_ADMIN);
        defaultAdmin.setStatus(AdminUser.AdminStatus.ACTIVE);
        
        adminUserRepository.save(defaultAdmin);
        
        log.info("默认管理员用户创建成功: {} / admin123", defaultUsername);
    }
}
