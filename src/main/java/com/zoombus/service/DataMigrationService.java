package com.zoombus.service;

import com.zoombus.entity.MeetingReport;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.MeetingReportRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomAuthRepository;
import com.zoombus.repository.ZoomUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * 数据迁移服务
 * 用于填充会议报告的新增字段和初始化PMI统计数据
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataMigrationService {

    private final MeetingReportRepository meetingReportRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final ZoomAuthRepository zoomAuthRepository;
    private final ZoomUserRepository zoomUserRepository;
    private final PmiReportService pmiReportService;

    // PMI号码的正则表达式（通常是10-11位数字）
    private static final Pattern PMI_PATTERN = Pattern.compile("^\\d{10,11}$");

    /**
     * 执行完整的数据迁移
     */
    @Transactional
    public void performFullMigration() {
        log.info("开始执行完整数据迁移");
        
        try {
            // 1. 填充会议报告的新字段
            fillMeetingReportFields();
            
            // 2. 初始化PMI统计数据
            initializePmiStats();
            
            log.info("完整数据迁移执行成功");
            
        } catch (Exception e) {
            log.error("数据迁移执行失败", e);
            throw new RuntimeException("数据迁移失败", e);
        }
    }

    /**
     * 填充会议报告的新增字段
     */
    @Transactional
    public void fillMeetingReportFields() {
        log.info("开始填充会议报告新增字段");
        
        List<MeetingReport> reports = meetingReportRepository.findAll();
        log.info("找到{}条会议报告需要处理", reports.size());
        
        int updatedCount = 0;
        
        for (MeetingReport report : reports) {
            try {
                boolean updated = false;
                
                // 1. 尝试从报告数据中提取PMI号码
                if (report.getPmiNumber() == null) {
                    String pmiNumber = extractPmiFromReport(report);
                    if (pmiNumber != null) {
                        report.setPmiNumber(pmiNumber);
                        updated = true;
                        log.debug("为报告{}设置PMI号码: {}", report.getId(), pmiNumber);
                    }
                }
                
                // 2. 确定会议类型
                if (report.getMeetingType() == null || report.getMeetingType() == MeetingReport.MeetingType.SCHEDULED) {
                    MeetingReport.MeetingType meetingType = determineMeetingType(report);
                    report.setMeetingType(meetingType);
                    updated = true;
                    log.debug("为报告{}设置会议类型: {}", report.getId(), meetingType);
                }
                
                // 3. 尝试关联Zoom主账号
                if (report.getZoomAuthId() == null) {
                    Long zoomAuthId = findZoomAuthId(report);
                    if (zoomAuthId != null) {
                        report.setZoomAuthId(zoomAuthId);
                        updated = true;
                        log.debug("为报告{}设置Zoom主账号ID: {}", report.getId(), zoomAuthId);
                    }
                }
                
                // 4. 提取主持人用户ID
                if (report.getHostUserId() == null) {
                    String hostUserId = extractHostUserId(report);
                    if (hostUserId != null) {
                        report.setHostUserId(hostUserId);
                        updated = true;
                        log.debug("为报告{}设置主持人用户ID: {}", report.getId(), hostUserId);
                    }
                }
                
                // 5. 设置参会人数缓存
                if (report.getParticipantCountField() == null || Integer.valueOf(0).equals(report.getParticipantCountField())) {
                    Integer participantCount = report.getTotalParticipants() != null ?
                        report.getTotalParticipants() : 0;
                    report.setParticipantCountField(participantCount);
                    updated = true;
                }
                
                if (updated) {
                    meetingReportRepository.save(report);
                    updatedCount++;
                }
                
            } catch (Exception e) {
                log.error("处理会议报告{}时出错", report.getId(), e);
                // 继续处理其他报告
            }
        }
        
        log.info("会议报告字段填充完成，更新了{}条记录", updatedCount);
    }

    /**
     * 从会议报告中提取PMI号码
     */
    private String extractPmiFromReport(MeetingReport report) {
        try {
            // 1. 从zoom_meeting_id中提取（如果是PMI格式）
            String meetingId = report.getZoomMeetingId();
            if (meetingId != null && PMI_PATTERN.matcher(meetingId).matches()) {
                return meetingId;
            }
            
            // 2. 从报告数据JSON中提取
            // 暂时跳过JSON解析，因为需要添加JSON解析库
            
            // 3. 从主题中提取PMI（如果主题包含PMI信息）
            String topic = report.getTopic();
            if (topic != null && topic.toLowerCase().contains("pmi")) {
                // 这里可以添加更复杂的PMI提取逻辑
            }
            
        } catch (Exception e) {
            log.warn("从报告{}提取PMI号码时出错", report.getId(), e);
        }
        
        return null;
    }

    /**
     * 确定会议类型
     */
    private MeetingReport.MeetingType determineMeetingType(MeetingReport report) {
        // 1. 如果有PMI号码，则为PMI会议
        if (report.getPmiNumber() != null) {
            return MeetingReport.MeetingType.PMI;
        }
        
        // 2. 检查zoom_meeting_id是否为PMI格式
        String meetingId = report.getZoomMeetingId();
        if (meetingId != null && PMI_PATTERN.matcher(meetingId).matches()) {
            return MeetingReport.MeetingType.PMI;
        }
        
        // 3. 从报告数据中判断
        // 暂时跳过JSON解析
        
        // 默认为预约会议
        return MeetingReport.MeetingType.SCHEDULED;
    }

    /**
     * 查找关联的Zoom主账号ID
     */
    private Long findZoomAuthId(MeetingReport report) {
        try {
            // 1. 从报告数据中提取主持人信息
            String hostEmail = extractHostEmail(report);
            String hostUserId = extractHostUserId(report);
            
            // 2. 根据主持人信息查找Zoom用户
            ZoomUser zoomUser = null;
            
            if (hostEmail != null) {
                Optional<ZoomUser> userOpt = zoomUserRepository.findByEmail(hostEmail);
                if (userOpt.isPresent()) {
                    zoomUser = userOpt.get();
                }
            }
            
            if (zoomUser == null && hostUserId != null) {
                Optional<ZoomUser> userOpt = zoomUserRepository.findFirstByZoomUserId(hostUserId);
                if (userOpt.isPresent()) {
                    zoomUser = userOpt.get();
                }
            }

            // 3. 返回关联的Zoom主账号ID
            if (zoomUser != null && zoomUser.getZoomAuth() != null) {
                return zoomUser.getZoomAuth().getId();
            }

            // 4. 如果找不到具体用户，返回第一个活跃的Zoom主账号（作为默认值）
            List<ZoomAuth> activeAuths = zoomAuthRepository.findByStatus(ZoomAuth.AuthStatus.ACTIVE);
            if (!activeAuths.isEmpty()) {
                return activeAuths.get(0).getId();
            }
            
        } catch (Exception e) {
            log.warn("为报告{}查找Zoom主账号ID时出错", report.getId(), e);
        }
        
        return null;
    }

    /**
     * 提取主持人邮箱
     */
    private String extractHostEmail(MeetingReport report) {
        // 暂时跳过JSON解析，可以从其他字段获取
        return null;
    }

    /**
     * 提取主持人用户ID
     */
    private String extractHostUserId(MeetingReport report) {
        // 暂时跳过JSON解析，可以从其他字段获取
        return null;
    }

    /**
     * 初始化PMI统计数据
     */
    @Transactional
    public void initializePmiStats() {
        log.info("开始初始化PMI统计数据");
        
        try {
            // 使用PMI报告服务的批量更新功能
            pmiReportService.updateAllPmiStats();
            log.info("PMI统计数据初始化完成");
            
        } catch (Exception e) {
            log.error("初始化PMI统计数据失败", e);
            throw new RuntimeException("初始化PMI统计数据失败", e);
        }
    }

    /**
     * 获取数据迁移状态
     */
    public Map<String, Object> getMigrationStatus() {
        long totalReports = meetingReportRepository.count();
        long reportsWithPmi = meetingReportRepository.countByPmiNumberIsNotNull();
        long reportsWithZoomAuth = meetingReportRepository.countByZoomAuthIdIsNotNull();
        long reportsWithHostUser = meetingReportRepository.countByHostUserIdIsNotNull();
        
        return Map.of(
            "totalReports", totalReports,
            "reportsWithPmi", reportsWithPmi,
            "reportsWithZoomAuth", reportsWithZoomAuth,
            "reportsWithHostUser", reportsWithHostUser,
            "pmiCoverage", totalReports > 0 ? (double) reportsWithPmi / totalReports * 100 : 0,
            "zoomAuthCoverage", totalReports > 0 ? (double) reportsWithZoomAuth / totalReports * 100 : 0,
            "hostUserCoverage", totalReports > 0 ? (double) reportsWithHostUser / totalReports * 100 : 0
        );
    }
}
