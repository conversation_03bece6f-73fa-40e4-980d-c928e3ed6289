package com.zoombus.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.MigrationInfo;
import org.flywaydb.core.api.MigrationInfoService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据库迁移服务
 * 负责自动执行Flyway数据库迁移
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DatabaseMigrationService {

    private final DataSource dataSource;
    private final DatabaseVersionService databaseVersionService;

    @Value("${spring.flyway.enabled:true}")
    private boolean flywayEnabled;

    @Value("${spring.flyway.auto-migrate:true}")
    private boolean autoMigrate;

    @Value("${spring.flyway.baseline-on-migrate:true}")
    private boolean baselineOnMigrate;

    /**
     * 应用启动时自动执行数据库迁移
     */
    @EventListener(ApplicationReadyEvent.class)
    @Order(1) // 确保在DatabaseVersionService之前执行
    public void autoMigrate() {
        if (!flywayEnabled || !autoMigrate) {
            log.info("Flyway自动迁移已禁用");
            return;
        }

        try {
            log.info("开始执行数据库自动迁移...");
            
            String beforeVersion = databaseVersionService.getCurrentDatabaseVersion();
            
            Flyway flyway = createFlyway();
            
            // 获取迁移信息
            MigrationInfoService infoService = flyway.info();
            MigrationInfo[] pendingMigrations = infoService.pending();
            
            if (pendingMigrations.length == 0) {
                log.info("没有待执行的数据库迁移");
                return;
            }

            log.info("发现 {} 个待执行的迁移:", pendingMigrations.length);
            for (MigrationInfo migration : pendingMigrations) {
                log.info("  - {}: {}", migration.getVersion(), migration.getDescription());
            }

            // 执行迁移
            int migrationsExecuted = flyway.migrate().migrationsExecuted;
            
            String afterVersion = databaseVersionService.getCurrentDatabaseVersion();
            
            log.info("数据库迁移完成: 执行了 {} 个迁移, 版本从 {} 升级到 {}", 
                    migrationsExecuted, beforeVersion, afterVersion);

            // 记录迁移事件
            String description = String.format("自动迁移: 执行了%d个迁移脚本, 从版本%s升级到%s", 
                    migrationsExecuted, beforeVersion, afterVersion);
            
            databaseVersionService.recordVersionInfo(
                databaseVersionService.getCurrentVersionInfo().get("applicationVersion").toString(),
                afterVersion,
                "DATABASE_MIGRATION",
                description
            );

        } catch (Exception e) {
            log.error("数据库自动迁移失败", e);
            
            // 记录迁移失败事件
            databaseVersionService.recordVersionInfo(
                databaseVersionService.getCurrentVersionInfo().get("applicationVersion").toString(),
                "migration-failed",
                "DATABASE_MIGRATION",
                "自动迁移失败: " + e.getMessage()
            );
            
            throw new RuntimeException("数据库迁移失败，应用启动中止", e);
        }
    }

    /**
     * 手动执行数据库迁移
     */
    public MigrationResult manualMigrate() {
        try {
            log.info("开始执行手动数据库迁移...");
            
            String beforeVersion = databaseVersionService.getCurrentDatabaseVersion();
            
            Flyway flyway = createFlyway();
            
            // 获取迁移信息
            MigrationInfoService infoService = flyway.info();
            MigrationInfo[] pendingMigrations = infoService.pending();
            
            if (pendingMigrations.length == 0) {
                return new MigrationResult(true, "没有待执行的数据库迁移", 0, beforeVersion, beforeVersion);
            }

            // 执行迁移
            int migrationsExecuted = flyway.migrate().migrationsExecuted;
            String afterVersion = databaseVersionService.getCurrentDatabaseVersion();
            
            log.info("手动数据库迁移完成: 执行了 {} 个迁移", migrationsExecuted);

            // 记录迁移事件
            String description = String.format("手动迁移: 执行了%d个迁移脚本", migrationsExecuted);
            databaseVersionService.recordVersionInfo(
                databaseVersionService.getCurrentVersionInfo().get("applicationVersion").toString(),
                afterVersion,
                "DATABASE_MIGRATION",
                description
            );

            return new MigrationResult(true, "迁移成功", migrationsExecuted, beforeVersion, afterVersion);

        } catch (Exception e) {
            log.error("手动数据库迁移失败", e);
            String errorMessage = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
            return new MigrationResult(false, "迁移失败: " + errorMessage, 0, null, null);
        }
    }

    /**
     * 获取迁移状态信息
     */
    public MigrationStatusInfo getMigrationStatus() {
        try {
            Flyway flyway = createFlyway();
            MigrationInfoService infoService = flyway.info();
            
            MigrationInfo[] all = infoService.all();
            MigrationInfo[] pending = infoService.pending();
            MigrationInfo current = infoService.current();
            
            List<MigrationDetail> allMigrations = Arrays.stream(all)
                .map(this::toMigrationDetail)
                .collect(Collectors.toList());
            
            List<MigrationDetail> pendingMigrations = Arrays.stream(pending)
                .map(this::toMigrationDetail)
                .collect(Collectors.toList());
            
            return new MigrationStatusInfo(
                current != null ? current.getVersion().toString() : "none",
                allMigrations,
                pendingMigrations,
                pending.length > 0
            );
            
        } catch (Exception e) {
            log.error("获取迁移状态失败", e);
            throw new RuntimeException("获取迁移状态失败", e);
        }
    }

    /**
     * 创建Flyway实例
     */
    private Flyway createFlyway() {
        return Flyway.configure()
            .dataSource(dataSource)
            .locations("classpath:db/migration")
            .baselineOnMigrate(baselineOnMigrate)
            .validateOnMigrate(false)  // 暂时禁用验证以解决迁移问题
            .cleanDisabled(true)
            .load();
    }

    /**
     * 转换迁移信息
     */
    private MigrationDetail toMigrationDetail(MigrationInfo info) {
        return new MigrationDetail(
            info.getVersion() != null ? info.getVersion().toString() : "unknown",
            info.getDescription(),
            info.getState().getDisplayName(),
            info.getInstalledOn(),
            info.getExecutionTime()
        );
    }

    /**
     * 迁移结果
     */
    public static class MigrationResult {
        public final boolean success;
        public final String message;
        public final int migrationsExecuted;
        public final String beforeVersion;
        public final String afterVersion;

        public MigrationResult(boolean success, String message, int migrationsExecuted, 
                             String beforeVersion, String afterVersion) {
            this.success = success;
            this.message = message;
            this.migrationsExecuted = migrationsExecuted;
            this.beforeVersion = beforeVersion;
            this.afterVersion = afterVersion;
        }
    }

    /**
     * 迁移状态信息
     */
    public static class MigrationStatusInfo {
        public final String currentVersion;
        public final List<MigrationDetail> allMigrations;
        public final List<MigrationDetail> pendingMigrations;
        public final boolean hasPendingMigrations;

        public MigrationStatusInfo(String currentVersion, List<MigrationDetail> allMigrations,
                                 List<MigrationDetail> pendingMigrations, boolean hasPendingMigrations) {
            this.currentVersion = currentVersion;
            this.allMigrations = allMigrations;
            this.pendingMigrations = pendingMigrations;
            this.hasPendingMigrations = hasPendingMigrations;
        }
    }

    /**
     * 迁移详情
     */
    public static class MigrationDetail {
        public final String version;
        public final String description;
        public final String state;
        public final java.util.Date installedOn;
        public final Integer executionTime;

        public MigrationDetail(String version, String description, String state,
                             java.util.Date installedOn, Integer executionTime) {
            this.version = version;
            this.description = description;
            this.state = state;
            this.installedOn = installedOn;
            this.executionTime = executionTime;
        }
    }
}
