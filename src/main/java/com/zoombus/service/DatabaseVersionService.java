package com.zoombus.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据库版本管理服务
 * 负责记录和管理应用程序版本与数据库版本的对应关系
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DatabaseVersionService {

    private final JdbcTemplate jdbcTemplate;

    @Value("${spring.application.version:1.0.0}")
    private String applicationVersion;

    @Value("${spring.application.name:zoombus}")
    private String applicationName;

    /**
     * 应用启动后初始化版本管理表
     */
    @PostConstruct
    public void initVersionTable() {
        try {
            createVersionTableIfNotExists();
            log.info("数据库版本管理表初始化完成");
        } catch (Exception e) {
            log.error("初始化数据库版本管理表失败", e);
        }
    }

    /**
     * 应用启动完成后记录版本信息
     */
    @EventListener(ApplicationReadyEvent.class)
    public void recordApplicationStartup() {
        try {
            String dbVersion = getCurrentDatabaseVersion();
            recordVersionInfo(applicationVersion, dbVersion, "APPLICATION_STARTUP");
            log.info("应用启动版本记录完成: 应用版本={}, 数据库版本={}", applicationVersion, dbVersion);
        } catch (Exception e) {
            log.error("记录应用启动版本信息失败", e);
        }
    }

    /**
     * 创建版本管理表（如果不存在）
     */
    private void createVersionTableIfNotExists() {
        String createTableSql = "CREATE TABLE IF NOT EXISTS t_version_history (" +
            "id BIGINT AUTO_INCREMENT PRIMARY KEY," +
            "application_name VARCHAR(100) NOT NULL COMMENT '应用名称'," +
            "application_version VARCHAR(50) NOT NULL COMMENT '应用版本'," +
            "database_version VARCHAR(50) NOT NULL COMMENT '数据库版本'," +
            "flyway_version VARCHAR(50) COMMENT 'Flyway迁移版本'," +
            "event_type VARCHAR(50) NOT NULL COMMENT '事件类型：APPLICATION_STARTUP, DATABASE_MIGRATION, MANUAL_RECORD'," +
            "event_description TEXT COMMENT '事件描述'," +
            "server_info VARCHAR(200) COMMENT '服务器信息'," +
            "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
            "INDEX idx_app_version (application_name, application_version)," +
            "INDEX idx_db_version (database_version)," +
            "INDEX idx_created_at (created_at)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用和数据库版本历史记录表'";
        
        jdbcTemplate.execute(createTableSql);
    }

    /**
     * 获取当前数据库版本（基于Flyway）
     */
    public String getCurrentDatabaseVersion() {
        try {
            // 检查flyway_schema_history表是否存在
            String checkTableSql = "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = DATABASE() " +
                "AND table_name = 'flyway_schema_history'";
            
            Integer tableCount = jdbcTemplate.queryForObject(checkTableSql, Integer.class);
            
            if (tableCount != null && tableCount > 0) {
                // 获取最新的Flyway版本
                String flywayVersionSql = "SELECT version FROM flyway_schema_history " +
                    "WHERE success = 1 " +
                    "ORDER BY installed_rank DESC " +
                    "LIMIT 1";
                
                String version = jdbcTemplate.queryForObject(flywayVersionSql, String.class);
                return version != null ? version : "unknown";
            } else {
                return "no-flyway";
            }
        } catch (Exception e) {
            log.warn("获取数据库版本失败: {}", e.getMessage());
            return "error";
        }
    }

    /**
     * 记录版本信息
     */
    public void recordVersionInfo(String appVersion, String dbVersion, String eventType) {
        recordVersionInfo(appVersion, dbVersion, eventType, null);
    }

    /**
     * 记录版本信息（带描述）
     */
    public void recordVersionInfo(String appVersion, String dbVersion, String eventType, String description) {
        try {
            String serverInfo = getServerInfo();
            String flywayVersion = getCurrentDatabaseVersion();
            
            String insertSql = "INSERT INTO t_version_history " +
                "(application_name, application_version, database_version, flyway_version, " +
                "event_type, event_description, server_info, created_at) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            jdbcTemplate.update(insertSql, 
                applicationName, 
                appVersion, 
                dbVersion, 
                flywayVersion,
                eventType, 
                description, 
                serverInfo, 
                LocalDateTime.now()
            );
            
            log.info("版本信息记录成功: {}@{} -> DB:{}", appVersion, eventType, dbVersion);
        } catch (Exception e) {
            log.error("记录版本信息失败", e);
        }
    }

    /**
     * 获取服务器信息
     */
    private String getServerInfo() {
        try {
            String hostname = System.getenv("HOSTNAME");
            if (hostname == null) {
                hostname = System.getProperty("user.name") + "@" + 
                          java.net.InetAddress.getLocalHost().getHostName();
            }
            return hostname;
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 获取版本历史记录
     */
    public List<Map<String, Object>> getVersionHistory(int limit) {
        String sql = "SELECT " +
            "id, " +
            "application_name, " +
            "application_version, " +
            "database_version, " +
            "flyway_version, " +
            "event_type, " +
            "event_description, " +
            "server_info, " +
            "created_at " +
            "FROM t_version_history " +
            "ORDER BY created_at DESC " +
            "LIMIT ?";
        
        return jdbcTemplate.queryForList(sql, limit);
    }

    /**
     * 获取当前版本信息
     */
    public Map<String, Object> getCurrentVersionInfo() {
        String dbVersion = getCurrentDatabaseVersion();
        String serverInfo = getServerInfo();
        
        return Map.of(
            "applicationName", applicationName,
            "applicationVersion", applicationVersion,
            "databaseVersion", dbVersion,
            "serverInfo", serverInfo,
            "timestamp", LocalDateTime.now()
        );
    }

    /**
     * 检查版本兼容性
     */
    public boolean isVersionCompatible(String requiredDbVersion) {
        String currentDbVersion = getCurrentDatabaseVersion();
        
        try {
            // 简单的版本比较逻辑，可以根据需要扩展
            return compareVersions(currentDbVersion, requiredDbVersion) >= 0;
        } catch (Exception e) {
            log.warn("版本兼容性检查失败: current={}, required={}", currentDbVersion, requiredDbVersion);
            return false;
        }
    }

    /**
     * 版本比较（简单实现）
     */
    private int compareVersions(String version1, String version2) {
        if (version1.equals(version2)) return 0;
        if ("unknown".equals(version1) || "error".equals(version1)) return -1;
        if ("unknown".equals(version2) || "error".equals(version2)) return 1;
        
        // 简单的字符串比较，实际项目中可能需要更复杂的版本比较逻辑
        return version1.compareTo(version2);
    }
}
