package com.zoombus.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式锁管理器
 * 用于解决会议状态并发修改问题
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DistributedLockManager {

    private final RedisTemplate<String, String> redisTemplate;
    
    // Lua脚本确保原子性释放锁
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";
    
    private final DefaultRedisScript<Long> unlockScript = new DefaultRedisScript<>(UNLOCK_SCRIPT, Long.class);

    /**
     * 获取会议UUID锁
     */
    public boolean acquireMeetingUuidLock(String meetingUuid, Duration timeout) {
        String lockKey = buildMeetingUuidLockKey(meetingUuid);
        String lockValue = generateLockValue();

        Boolean acquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, timeout);

        if (Boolean.TRUE.equals(acquired)) {
            log.debug("获取会议UUID锁成功: meetingUuid={}, lockKey={}", meetingUuid, lockKey);
            return true;
        } else {
            log.debug("获取会议UUID锁失败: meetingUuid={}, lockKey={}", meetingUuid, lockKey);
            return false;
        }
    }

    /**
     * 释放会议UUID锁
     */
    public boolean releaseMeetingUuidLock(String meetingUuid, String lockValue) {
        String lockKey = buildMeetingUuidLockKey(meetingUuid);

        Long result = redisTemplate.execute(unlockScript,
            Collections.singletonList(lockKey), lockValue);

        boolean released = result != null && result == 1L;
        if (released) {
            log.debug("释放会议UUID锁成功: meetingUuid={}, lockKey={}", meetingUuid, lockKey);
        } else {
            log.debug("释放会议UUID锁失败: meetingUuid={}, lockKey={}", meetingUuid, lockKey);
        }

        return released;
    }

    /**
     * 获取会议状态锁（已废弃）
     * @deprecated 使用 acquireMeetingUuidLock 替代
     */
    @Deprecated
    public boolean acquireMeetingStatusLock(Long meetingId, Duration timeout) {
        String lockKey = buildMeetingStatusLockKey(meetingId);
        String lockValue = generateLockValue();

        Boolean acquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, timeout);

        if (Boolean.TRUE.equals(acquired)) {
            log.debug("获取会议状态锁成功: meetingId={}, lockKey={}", meetingId, lockKey);
            return true;
        } else {
            log.debug("获取会议状态锁失败: meetingId={}, lockKey={}", meetingId, lockKey);
            return false;
        }
    }

    /**
     * 释放会议状态锁
     */
    public boolean releaseMeetingStatusLock(Long meetingId, String lockValue) {
        String lockKey = buildMeetingStatusLockKey(meetingId);
        
        Long result = redisTemplate.execute(unlockScript, 
            Collections.singletonList(lockKey), lockValue);
            
        boolean released = result != null && result == 1L;
        if (released) {
            log.debug("释放会议状态锁成功: meetingId={}, lockKey={}", meetingId, lockKey);
        } else {
            log.debug("释放会议状态锁失败: meetingId={}, lockKey={}", meetingId, lockKey);
        }
        
        return released;
    }

    /**
     * 带锁执行操作（通过UUID）
     */
    public <T> T executeWithMeetingUuidLock(String meetingUuid, Duration timeout, Supplier<T> operation) {
        String lockKey = buildMeetingUuidLockKey(meetingUuid);
        String lockValue = generateLockValue();

        // 尝试获取锁
        Boolean acquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, timeout);

        if (!Boolean.TRUE.equals(acquired)) {
            throw new ConcurrentModificationException(
                String.format("无法获取会议UUID锁: meetingUuid=%s", meetingUuid));
        }

        try {
            log.debug("执行带UUID锁操作: meetingUuid={}, lockKey={}", meetingUuid, lockKey);
            return operation.get();
        } finally {
            // 释放锁
            releaseMeetingUuidLock(meetingUuid, lockValue);
        }
    }

    /**
     * 带锁执行操作（通过数据库ID - 已废弃）
     * @deprecated 使用 executeWithMeetingUuidLock 替代
     */
    @Deprecated
    public <T> T executeWithMeetingStatusLock(Long meetingId, Duration timeout, Supplier<T> operation) {
        String lockKey = buildMeetingStatusLockKey(meetingId);
        String lockValue = generateLockValue();

        // 尝试获取锁
        Boolean acquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, timeout);

        if (!Boolean.TRUE.equals(acquired)) {
            throw new ConcurrentModificationException(
                String.format("无法获取会议状态锁: meetingId=%d", meetingId));
        }

        try {
            log.debug("执行带锁操作: meetingId={}, lockKey={}", meetingId, lockKey);
            return operation.get();
        } finally {
            // 释放锁
            releaseMeetingStatusLock(meetingId, lockValue);
        }
    }

    /**
     * 带锁执行操作（无返回值）
     */
    public void executeWithMeetingStatusLock(Long meetingId, Duration timeout, Runnable operation) {
        executeWithMeetingStatusLock(meetingId, timeout, () -> {
            operation.run();
            return null;
        });
    }

    /**
     * 尝试带锁执行操作（不抛异常）
     */
    public <T> T tryExecuteWithMeetingStatusLock(Long meetingId, Duration timeout, 
                                                Supplier<T> operation, T defaultValue) {
        try {
            return executeWithMeetingStatusLock(meetingId, timeout, operation);
        } catch (ConcurrentModificationException e) {
            log.warn("获取锁失败，返回默认值: meetingId={}, error={}", meetingId, e.getMessage());
            return defaultValue;
        }
    }

    /**
     * 获取PMI分配锁（防止同一PMI被重复分配）
     */
    public boolean acquirePmiAssignmentLock(String pmiNumber, Duration timeout) {
        String lockKey = buildPmiAssignmentLockKey(pmiNumber);
        String lockValue = generateLockValue();
        
        Boolean acquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, timeout);
            
        if (Boolean.TRUE.equals(acquired)) {
            log.debug("获取PMI分配锁成功: pmiNumber={}, lockKey={}", pmiNumber, lockKey);
            return true;
        } else {
            log.debug("获取PMI分配锁失败: pmiNumber={}, lockKey={}", pmiNumber, lockKey);
            return false;
        }
    }

    /**
     * 释放PMI分配锁
     */
    public boolean releasePmiAssignmentLock(String pmiNumber, String lockValue) {
        String lockKey = buildPmiAssignmentLockKey(pmiNumber);
        
        Long result = redisTemplate.execute(unlockScript, 
            Collections.singletonList(lockKey), lockValue);
            
        return result != null && result == 1L;
    }

    /**
     * 带PMI分配锁执行操作
     */
    public <T> T executeWithPmiAssignmentLock(String pmiNumber, Duration timeout, Supplier<T> operation) {
        String lockKey = buildPmiAssignmentLockKey(pmiNumber);
        String lockValue = generateLockValue();
        
        Boolean acquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, timeout);
            
        if (!Boolean.TRUE.equals(acquired)) {
            throw new ConcurrentModificationException(
                String.format("PMI正在被其他进程使用: pmiNumber=%s", pmiNumber));
        }
        
        try {
            log.debug("执行带PMI锁操作: pmiNumber={}, lockKey={}", pmiNumber, lockKey);
            return operation.get();
        } finally {
            releasePmiAssignmentLock(pmiNumber, lockValue);
        }
    }

    /**
     * 检查锁是否存在
     */
    public boolean isLockExists(String lockKey) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(lockKey));
    }

    /**
     * 构建会议UUID锁键
     */
    private String buildMeetingUuidLockKey(String meetingUuid) {
        return String.format("lock:meeting:uuid:%s", meetingUuid);
    }

    /**
     * 构建会议状态锁键（已废弃）
     * @deprecated 使用 buildMeetingUuidLockKey 替代
     */
    @Deprecated
    private String buildMeetingStatusLockKey(Long meetingId) {
        return String.format("lock:meeting:status:%d", meetingId);
    }

    /**
     * 构建PMI分配锁键
     */
    private String buildPmiAssignmentLockKey(String pmiNumber) {
        return String.format("lock:pmi:assignment:%s", pmiNumber);
    }

    /**
     * 生成锁值
     */
    private String generateLockValue() {
        return UUID.randomUUID().toString();
    }

    /**
     * 并发修改异常
     */
    public static class ConcurrentModificationException extends RuntimeException {
        public ConcurrentModificationException(String message) {
            super(message);
        }
    }
}
