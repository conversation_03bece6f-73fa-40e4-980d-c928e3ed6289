package com.zoombus.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁服务
 * 基于Redis实现，用于防止并发操作导致的数据不一致
 */
@Slf4j
@Service
public class DistributedLockService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String LOCK_PREFIX = "meeting:lock:";
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) " +
        "else return 0 end";

    /**
     * 尝试获取锁
     *
     * @param lockKey 锁的键
     * @param expireTime 锁的过期时间（秒）
     * @return 锁的值（用于释放锁），如果获取失败返回null
     */
    public String tryLock(String lockKey, long expireTime) {
        String lockValue = UUID.randomUUID().toString();
        String fullKey = LOCK_PREFIX + lockKey;
        
        try {
            Boolean success = redisTemplate.opsForValue()
                .setIfAbsent(fullKey, lockValue, Duration.ofSeconds(expireTime));
            
            if (Boolean.TRUE.equals(success)) {
                log.debug("获取分布式锁成功: key={}, value={}, expireTime={}s", 
                    fullKey, lockValue, expireTime);
                return lockValue;
            } else {
                log.debug("获取分布式锁失败，锁已被占用: key={}", fullKey);
                return null;
            }
        } catch (Exception e) {
            log.error("获取分布式锁异常: key={}", fullKey, e);
            return null;
        }
    }

    /**
     * 释放锁
     *
     * @param lockKey 锁的键
     * @param lockValue 锁的值
     * @return 是否释放成功
     */
    public boolean releaseLock(String lockKey, String lockValue) {
        String fullKey = LOCK_PREFIX + lockKey;
        
        try {
            DefaultRedisScript<Long> script = new DefaultRedisScript<>();
            script.setScriptText(UNLOCK_SCRIPT);
            script.setResultType(Long.class);
            
            Long result = redisTemplate.execute(script, 
                Collections.singletonList(fullKey), lockValue);
            
            boolean success = result != null && result > 0;
            if (success) {
                log.debug("释放分布式锁成功: key={}, value={}", fullKey, lockValue);
            } else {
                log.warn("释放分布式锁失败，锁可能已过期或被其他线程释放: key={}, value={}", 
                    fullKey, lockValue);
            }
            return success;
        } catch (Exception e) {
            log.error("释放分布式锁异常: key={}, value={}", fullKey, lockValue, e);
            return false;
        }
    }

    /**
     * 带重试的锁获取
     *
     * @param lockKey 锁的键
     * @param expireTime 锁的过期时间（秒）
     * @param retryTimes 重试次数
     * @param retryInterval 重试间隔（毫秒）
     * @return 锁的值，如果获取失败返回null
     */
    public String tryLockWithRetry(String lockKey, long expireTime, 
                                   int retryTimes, long retryInterval) {
        for (int i = 0; i <= retryTimes; i++) {
            String lockValue = tryLock(lockKey, expireTime);
            if (lockValue != null) {
                return lockValue;
            }
            
            if (i < retryTimes) {
                try {
                    Thread.sleep(retryInterval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("锁重试被中断: key={}", lockKey);
                    break;
                }
            }
        }
        
        log.warn("获取分布式锁失败，已重试{}次: key={}", retryTimes, lockKey);
        return null;
    }

    /**
     * 执行带锁的操作
     *
     * @param lockKey 锁的键
     * @param expireTime 锁的过期时间（秒）
     * @param operation 要执行的操作
     * @return 操作是否成功执行
     */
    public boolean executeWithLock(String lockKey, long expireTime, Runnable operation) {
        String lockValue = tryLock(lockKey, expireTime);
        if (lockValue == null) {
            return false;
        }
        
        try {
            operation.run();
            return true;
        } catch (Exception e) {
            log.error("执行带锁操作异常: key={}", lockKey, e);
            throw e;
        } finally {
            releaseLock(lockKey, lockValue);
        }
    }

    /**
     * 检查锁是否存在
     *
     * @param lockKey 锁的键
     * @return 锁是否存在
     */
    public boolean isLocked(String lockKey) {
        String fullKey = LOCK_PREFIX + lockKey;
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(fullKey));
        } catch (Exception e) {
            log.error("检查锁状态异常: key={}", fullKey, e);
            return false;
        }
    }

    /**
     * 获取锁的剩余过期时间
     *
     * @param lockKey 锁的键
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示锁不存在
     */
    public long getLockTtl(String lockKey) {
        String fullKey = LOCK_PREFIX + lockKey;
        try {
            return redisTemplate.getExpire(fullKey, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取锁过期时间异常: key={}", fullKey, e);
            return -2;
        }
    }

    /**
     * 生成会议结束锁的键
     *
     * @param meetingUuid 会议UUID
     * @return 锁的键
     */
    public static String getMeetingEndLockKey(String meetingUuid) {
        return "meeting:end:" + meetingUuid;
    }

    /**
     * 生成会议开始锁的键
     *
     * @param meetingId 会议ID
     * @param hostId 主持人ID
     * @return 锁的键
     */
    public static String getMeetingStartLockKey(String meetingId, String hostId) {
        return "meeting:start:" + meetingId + ":" + hostId;
    }
}
