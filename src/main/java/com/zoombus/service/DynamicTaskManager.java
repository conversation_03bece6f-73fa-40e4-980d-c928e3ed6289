package com.zoombus.service;

import com.zoombus.entity.PmiScheduleWindowTask;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 动态任务管理器接口
 * 负责PMI窗口任务的动态调度和管理
 */
public interface DynamicTaskManager {
    
    /**
     * 调度PMI窗口开启任务
     *
     * @param windowId PMI窗口ID
     * @param executeTime 执行时间
     * @return 任务ID
     */
    Long schedulePmiWindowOpenTask(Long windowId, LocalDateTime executeTime);

    /**
     * 调度PMI窗口关闭任务
     *
     * @param windowId PMI窗口ID
     * @param executeTime 执行时间
     * @return 任务ID
     */
    Long schedulePmiWindowCloseTask(Long windowId, LocalDateTime executeTime);
    
    /**
     * 取消任务
     * 
     * @param taskKey 任务键
     * @return 是否成功取消
     */
    boolean cancelTask(String taskKey);
    
    /**
     * 重新调度任务
     *
     * @param oldTaskKey 原任务键
     * @param newExecuteTime 新执行时间
     * @return 新任务ID
     */
    Long rescheduleTask(String oldTaskKey, LocalDateTime newExecuteTime);
    
    /**
     * 获取即将执行的任务列表
     * 
     * @param hours 未来几小时内的任务
     * @return 任务列表
     */
    List<ScheduledTaskInfo> getUpcomingTasks(int hours);
    
    /**
     * 获取任务状态
     * 
     * @param taskKey 任务键
     * @return 任务状态
     */
    TaskStatus getTaskStatus(String taskKey);
    
    /**
     * 批量取消任务
     * 
     * @param taskKeys 任务键列表
     * @return 成功取消的任务数量
     */
    int batchCancelTasks(List<String> taskKeys);
    
    /**
     * 系统启动时恢复未完成的任务
     */
    void initializePmiScheduledTasks();
    
    /**
     * 清理过期的任务记录
     * 
     * @param daysToKeep 保留天数
     * @return 清理的任务数量
     */
    int cleanupExpiredTasks(int daysToKeep);
    
    /**
     * 获取调度器状态
     * 
     * @return 调度器状态信息
     */
    SchedulerStatus getSchedulerStatus();
    
    /**
     * 调度任务信息
     */
    class ScheduledTaskInfo {
        private String taskKey;
        private Long pmiWindowId;
        private PmiScheduleWindowTask.TaskType taskType;
        private LocalDateTime scheduledTime;
        private PmiScheduleWindowTask.TaskStatus status;
        private String pmiNumber;
        private String userName;
        
        // 构造函数、getter和setter
        public ScheduledTaskInfo() {}
        
        public ScheduledTaskInfo(String taskKey, Long pmiWindowId, 
                               PmiScheduleWindowTask.TaskType taskType, 
                               LocalDateTime scheduledTime, 
                               PmiScheduleWindowTask.TaskStatus status) {
            this.taskKey = taskKey;
            this.pmiWindowId = pmiWindowId;
            this.taskType = taskType;
            this.scheduledTime = scheduledTime;
            this.status = status;
        }
        
        // Getters and Setters
        public String getTaskKey() { return taskKey; }
        public void setTaskKey(String taskKey) { this.taskKey = taskKey; }
        
        public Long getPmiWindowId() { return pmiWindowId; }
        public void setPmiWindowId(Long pmiWindowId) { this.pmiWindowId = pmiWindowId; }
        
        public PmiScheduleWindowTask.TaskType getTaskType() { return taskType; }
        public void setTaskType(PmiScheduleWindowTask.TaskType taskType) { this.taskType = taskType; }
        
        public LocalDateTime getScheduledTime() { return scheduledTime; }
        public void setScheduledTime(LocalDateTime scheduledTime) { this.scheduledTime = scheduledTime; }
        
        public PmiScheduleWindowTask.TaskStatus getStatus() { return status; }
        public void setStatus(PmiScheduleWindowTask.TaskStatus status) { this.status = status; }
        
        public String getPmiNumber() { return pmiNumber; }
        public void setPmiNumber(String pmiNumber) { this.pmiNumber = pmiNumber; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
    }
    
    /**
     * 任务状态
     */
    enum TaskStatus {
        SCHEDULED, EXECUTING, COMPLETED, FAILED, CANCELLED
    }
    
    /**
     * 调度器状态
     */
    class SchedulerStatus {
        private boolean active;
        private int totalScheduledTasks;
        private int runningTasks;
        private LocalDateTime lastHealthCheck;
        private String status;
        
        public SchedulerStatus() {}
        
        public SchedulerStatus(boolean active, int totalScheduledTasks, 
                             int runningTasks, LocalDateTime lastHealthCheck, String status) {
            this.active = active;
            this.totalScheduledTasks = totalScheduledTasks;
            this.runningTasks = runningTasks;
            this.lastHealthCheck = lastHealthCheck;
            this.status = status;
        }
        
        // Getters and Setters
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
        
        public int getTotalScheduledTasks() { return totalScheduledTasks; }
        public void setTotalScheduledTasks(int totalScheduledTasks) { this.totalScheduledTasks = totalScheduledTasks; }
        
        public int getRunningTasks() { return runningTasks; }
        public void setRunningTasks(int runningTasks) { this.runningTasks = runningTasks; }
        
        public LocalDateTime getLastHealthCheck() { return lastHealthCheck; }
        public void setLastHealthCheck(LocalDateTime lastHealthCheck) { this.lastHealthCheck = lastHealthCheck; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}
