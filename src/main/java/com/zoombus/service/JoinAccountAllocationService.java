package com.zoombus.service;

import com.zoombus.entity.JoinAccountUsageWindow;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.JoinAccountUsageWindowRepository;
import com.zoombus.repository.ZoomUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Join Account智能分配服务
 * 实现负载均衡优化版的账号分配算法
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JoinAccountAllocationService {
    
    private final ZoomUserRepository zoomUserRepository;
    private final JoinAccountUsageWindowRepository windowRepository;
    private final SystemConfigService systemConfigService;
    
    /**
     * 智能分配账号
     * 考虑时间距离、负载均衡和历史使用情况
     */
    public ZoomUser allocateAccount(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }
        if (!startTime.isBefore(endTime)) {
            throw new IllegalArgumentException("开始时间必须早于结束时间");
        }
        
        // 获取所有可用的专业版账号
        List<ZoomUser> availableAccounts = getAvailableAccounts();
        if (availableAccounts.isEmpty()) {
            throw new RuntimeException("没有可用的专业版账号");
        }
        
        // 过滤掉在指定时间段内有冲突的账号
        List<ZoomUser> nonConflictingAccounts = filterNonConflictingAccounts(availableAccounts, startTime, endTime);
        if (nonConflictingAccounts.isEmpty()) {
            throw new RuntimeException("在指定时间段内没有可用的账号");
        }
        
        // 计算每个账号的分配分数
        List<AccountScore> accountScores = calculateAccountScores(nonConflictingAccounts, startTime, endTime);
        
        // 按分数排序，选择最优账号
        AccountScore bestAccount = accountScores.stream()
                .min(Comparator.comparingDouble(AccountScore::getTotalScore))
                .orElseThrow(() -> new RuntimeException("无法计算账号分配分数"));
        
        log.info("智能分配账号完成: 账号={}, 分数={}, 时间距离分数={}, 负载均衡分数={}, 历史使用分数={}", 
                bestAccount.getAccount().getEmail(), 
                bestAccount.getTotalScore(),
                bestAccount.getTimeDistanceScore(),
                bestAccount.getLoadBalanceScore(),
                bestAccount.getHistoryUsageScore());
        
        return bestAccount.getAccount();
    }
    
    /**
     * 获取所有可用的账号（LICENSED和BASIC类型）
     */
    private List<ZoomUser> getAvailableAccounts() {
        // 查找所有PUBLIC_MEETING类型的活跃账号（包括LICENSED和BASIC）
        return zoomUserRepository.findByStatusAndAccountUsage(
                ZoomUser.UserStatus.ACTIVE,
                ZoomUser.AccountUsage.PUBLIC_MEETING
        );
    }
    
    /**
     * 过滤掉在指定时间段内有冲突的账号
     */
    private List<ZoomUser> filterNonConflictingAccounts(List<ZoomUser> accounts, LocalDateTime startTime, LocalDateTime endTime) {
        return accounts.stream()
                .filter(account -> !hasTimeConflict(account.getId(), startTime, endTime))
                .collect(Collectors.toList());
    }
    
    /**
     * 检查账号在指定时间段内是否有冲突
     */
    private boolean hasTimeConflict(Long zoomUserId, LocalDateTime startTime, LocalDateTime endTime) {
        List<JoinAccountUsageWindow> overlappingWindows = windowRepository.findOverlappingWindows(zoomUserId, startTime, endTime);
        return !overlappingWindows.isEmpty();
    }
    
    /**
     * 计算每个账号的分配分数
     */
    private List<AccountScore> calculateAccountScores(List<ZoomUser> accounts, LocalDateTime startTime, LocalDateTime endTime) {
        // 获取算法权重配置
        double timeWeight = systemConfigService.getDoubleConfigValue("join_account.algorithm.time_weight", 0.4);
        double loadWeight = systemConfigService.getDoubleConfigValue("join_account.algorithm.load_weight", 0.4);
        double historyWeight = systemConfigService.getDoubleConfigValue("join_account.algorithm.history_weight", 0.2);
        
        List<AccountScore> scores = new ArrayList<>();
        
        for (ZoomUser account : accounts) {
            // 计算时间距离分数
            double timeDistanceScore = calculateTimeDistanceScore(account, startTime);
            
            // 计算负载均衡分数
            double loadBalanceScore = calculateLoadBalanceScore(account, startTime, endTime);
            
            // 计算历史使用分数
            double historyUsageScore = calculateHistoryUsageScore(account);
            
            // 计算总分数（分数越低越好）
            double totalScore = timeDistanceScore * timeWeight + 
                               loadBalanceScore * loadWeight + 
                               historyUsageScore * historyWeight;
            
            AccountScore accountScore = new AccountScore(
                    account, totalScore, timeDistanceScore, loadBalanceScore, historyUsageScore);
            scores.add(accountScore);
        }
        
        return scores;
    }
    
    /**
     * 计算时间距离分数
     * 优先选择距离当前时间较近的空闲时间段的账号
     */
    private double calculateTimeDistanceScore(ZoomUser account, LocalDateTime startTime) {
        // 获取账号最近的使用窗口
        Optional<JoinAccountUsageWindow> lastWindow = getLastUsageWindow(account.getId());
        
        if (lastWindow.isEmpty()) {
            // 如果没有使用历史，返回较低的分数（优先级较高）
            return 0.1;
        }
        
        LocalDateTime lastEndTime = lastWindow.get().getEndTime();
        long minutesSinceLastUse = ChronoUnit.MINUTES.between(lastEndTime, startTime);
        
        // 距离越远，分数越高（优先级越低）
        // 使用对数函数平滑分数增长
        return Math.log(Math.max(1, minutesSinceLastUse / 60.0)) / 10.0; // 转换为小时并取对数
    }
    
    /**
     * 计算负载均衡分数
     * 优先选择使用频率较低的账号
     */
    private double calculateLoadBalanceScore(ZoomUser account, LocalDateTime startTime, LocalDateTime endTime) {
        // 计算账号在过去30天内的使用次数
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        long recentUsageCount = windowRepository.countByZoomUserIdAndTimeRange(
                account.getId(), thirtyDaysAgo, LocalDateTime.now());
        
        // 计算账号在未来30天内的预约次数
        LocalDateTime thirtyDaysLater = LocalDateTime.now().plusDays(30);
        long futureReservationCount = windowRepository.countByZoomUserIdAndTimeRange(
                account.getId(), LocalDateTime.now(), thirtyDaysLater);
        
        // 总负载 = 历史使用次数 + 未来预约次数
        long totalLoad = recentUsageCount + futureReservationCount;
        
        // 使用次数越多，分数越高（优先级越低）
        return totalLoad * 0.1;
    }
    
    /**
     * 计算历史使用分数
     * 考虑账号的总体使用情况和稳定性
     */
    private double calculateHistoryUsageScore(ZoomUser account) {
        // 获取账号的总使用统计
        Integer totalUsageCount = account.getTotalUsageCount();
        Integer totalUsageMinutes = account.getTotalUsageMinutes();
        
        if (totalUsageCount == null || totalUsageCount == 0) {
            // 新账号给予中等分数
            return 0.5;
        }
        
        // 计算平均使用时长
        double avgUsageMinutes = totalUsageMinutes != null ? 
                (double) totalUsageMinutes / totalUsageCount : 0;
        
        // 使用次数适中且平均时长合理的账号分数较低（优先级较高）
        double usageCountScore = Math.abs(totalUsageCount - 50) / 100.0; // 理想使用次数为50次
        double avgTimeScore = Math.abs(avgUsageMinutes - 60) / 120.0; // 理想平均时长为60分钟
        
        return (usageCountScore + avgTimeScore) / 2.0;
    }
    
    /**
     * 获取账号的最后使用窗口
     */
    private Optional<JoinAccountUsageWindow> getLastUsageWindow(Long zoomUserId) {
        List<JoinAccountUsageWindow> windows = windowRepository.findByZoomUserId(zoomUserId);
        return windows.stream()
                .filter(window -> window.getEndTime() != null)
                .max(Comparator.comparing(JoinAccountUsageWindow::getEndTime));
    }
    
    /**
     * 获取账号分配统计信息
     */
    public Map<String, Object> getAllocationStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 可用账号统计
        List<ZoomUser> availableAccounts = getAvailableAccounts();
        stats.put("totalAvailableAccounts", availableAccounts.size());
        
        // 当前使用中的账号数量
        LocalDateTime now = LocalDateTime.now();
        long activeAccountsCount = availableAccounts.stream()
                .mapToLong(account -> windowRepository.countByZoomUserIdAndTimeRange(
                        account.getId(), now.minusMinutes(1), now.plusMinutes(1)))
                .sum();
        stats.put("currentActiveAccounts", activeAccountsCount);
        
        // 负载分布统计
        Map<String, Long> loadDistribution = new HashMap<>();
        LocalDateTime thirtyDaysAgo = now.minusDays(30);
        
        for (ZoomUser account : availableAccounts) {
            long usageCount = windowRepository.countByZoomUserIdAndTimeRange(
                    account.getId(), thirtyDaysAgo, now);
            String loadLevel = getLoadLevel(usageCount);
            loadDistribution.merge(loadLevel, 1L, Long::sum);
        }
        stats.put("loadDistribution", loadDistribution);
        
        return stats;
    }
    
    /**
     * 根据使用次数获取负载等级
     */
    private String getLoadLevel(long usageCount) {
        if (usageCount == 0) return "空闲";
        if (usageCount <= 10) return "轻度";
        if (usageCount <= 30) return "中度";
        if (usageCount <= 50) return "重度";
        return "超载";
    }
    
    /**
     * 账号分数内部类
     */
    private static class AccountScore {
        private final ZoomUser account;
        private final double totalScore;
        private final double timeDistanceScore;
        private final double loadBalanceScore;
        private final double historyUsageScore;
        
        public AccountScore(ZoomUser account, double totalScore, double timeDistanceScore, 
                           double loadBalanceScore, double historyUsageScore) {
            this.account = account;
            this.totalScore = totalScore;
            this.timeDistanceScore = timeDistanceScore;
            this.loadBalanceScore = loadBalanceScore;
            this.historyUsageScore = historyUsageScore;
        }
        
        public ZoomUser getAccount() { return account; }
        public double getTotalScore() { return totalScore; }
        public double getTimeDistanceScore() { return timeDistanceScore; }
        public double getLoadBalanceScore() { return loadBalanceScore; }
        public double getHistoryUsageScore() { return historyUsageScore; }
    }
}
