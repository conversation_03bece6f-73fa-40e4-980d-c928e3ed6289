package com.zoombus.service;

import com.zoombus.entity.JoinAccountPasswordLog;
import com.zoombus.repository.JoinAccountPasswordLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Join Account密码变更日志服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JoinAccountPasswordLogService {
    
    private final JoinAccountPasswordLogRepository passwordLogRepository;
    
    /**
     * 记录密码变更日志
     */
    @Transactional
    public JoinAccountPasswordLog logPasswordChange(
            Long zoomUserId, 
            String oldPassword, 
            String newPassword, 
            JoinAccountPasswordLog.ChangeType changeType, 
            Long windowId, 
            String operator) {
        
        if (zoomUserId == null) {
            throw new IllegalArgumentException("Zoom账号ID不能为空");
        }
        if (newPassword == null || newPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("新密码不能为空");
        }
        if (changeType == null) {
            throw new IllegalArgumentException("变更类型不能为空");
        }
        
        JoinAccountPasswordLog passwordLog = new JoinAccountPasswordLog();
        passwordLog.setZoomUserId(zoomUserId);
        passwordLog.setOldPassword(oldPassword);
        passwordLog.setNewPassword(newPassword);
        passwordLog.setChangeType(changeType);
        passwordLog.setWindowId(windowId);
        passwordLog.setCreatedBy(operator != null ? operator : "SYSTEM");

        JoinAccountPasswordLog savedLog = passwordLogRepository.save(passwordLog);
        log.info("记录密码变更日志: ID={}, ZoomUserId={}, 变更类型={}, 操作人={}",
                savedLog.getId(), zoomUserId, changeType, operator);
        
        return savedLog;
    }
    
    /**
     * 记录窗口开启密码变更
     */
    @Transactional
    public JoinAccountPasswordLog logWindowOpenPasswordChange(Long zoomUserId, String oldPassword, String newPassword, Long windowId) {
        return logPasswordChange(zoomUserId, oldPassword, newPassword, 
                JoinAccountPasswordLog.ChangeType.WINDOW_OPEN, windowId, "SYSTEM");
    }
    
    /**
     * 记录窗口关闭密码变更
     */
    @Transactional
    public JoinAccountPasswordLog logWindowClosePasswordChange(Long zoomUserId, String oldPassword, String newPassword, Long windowId) {
        return logPasswordChange(zoomUserId, oldPassword, newPassword, 
                JoinAccountPasswordLog.ChangeType.WINDOW_CLOSE, windowId, "SYSTEM");
    }
    
    /**
     * 记录手动密码变更
     */
    @Transactional
    public JoinAccountPasswordLog logManualPasswordChange(Long zoomUserId, String oldPassword, String newPassword, String operator) {
        return logPasswordChange(zoomUserId, oldPassword, newPassword, 
                JoinAccountPasswordLog.ChangeType.MANUAL, null, operator);
    }
    
    /**
     * 根据ID获取日志
     */
    public Optional<JoinAccountPasswordLog> getLogById(Long id) {
        return passwordLogRepository.findById(id);
    }
    
    /**
     * 根据Zoom账号ID获取日志
     */
    public List<JoinAccountPasswordLog> getLogsByZoomUserId(Long zoomUserId) {
        return passwordLogRepository.findByZoomUserId(zoomUserId);
    }
    
    /**
     * 根据Zoom账号ID分页获取日志
     */
    public Page<JoinAccountPasswordLog> getLogsByZoomUserId(Long zoomUserId, Pageable pageable) {
        return passwordLogRepository.findByZoomUserId(zoomUserId, pageable);
    }
    
    /**
     * 分页查询日志
     */
    public Page<JoinAccountPasswordLog> getLogs(Pageable pageable) {
        return passwordLogRepository.findAll(pageable);
    }
    
    /**
     * 多条件查询日志
     */
    public Page<JoinAccountPasswordLog> searchLogs(
            Long zoomUserId,
            JoinAccountPasswordLog.ChangeType changeType,
            Long windowId,
            String createdBy,
            LocalDateTime startTime,
            LocalDateTime endTime,
            Pageable pageable) {
        
        return passwordLogRepository.findByConditions(
                zoomUserId, changeType, windowId, createdBy, startTime, endTime, pageable);
    }
    
    /**
     * 获取账号的最新密码变更记录
     */
    public Optional<JoinAccountPasswordLog> getLatestLogByZoomUserId(Long zoomUserId) {
        return passwordLogRepository.findLatestByZoomUserId(zoomUserId);
    }
    
    /**
     * 获取账号在指定时间之后的密码变更记录
     */
    public List<JoinAccountPasswordLog> getLogsByZoomUserIdAfterTime(Long zoomUserId, LocalDateTime afterTime) {
        return passwordLogRepository.findByZoomUserIdAfterTime(zoomUserId, afterTime);
    }
    
    /**
     * 根据窗口ID获取相关的密码变更日志
     */
    public List<JoinAccountPasswordLog> getLogsByWindowId(Long windowId) {
        return passwordLogRepository.findByWindowIdOrderByCreatedAt(windowId);
    }
    
    /**
     * 获取系统自动变更的日志
     */
    public List<JoinAccountPasswordLog> getSystemChangeLogs() {
        return passwordLogRepository.findSystemChanges();
    }
    
    /**
     * 获取手动变更的日志
     */
    public List<JoinAccountPasswordLog> getManualChangeLogs() {
        return passwordLogRepository.findManualChanges();
    }
    
    /**
     * 获取密码变更统计信息
     */
    public Map<String, Object> getPasswordChangeStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总数统计
        stats.put("totalCount", passwordLogRepository.count());
        
        // 变更类型统计
        List<Object[]> changeTypeStats = passwordLogRepository.countByChangeType();
        Map<String, Long> changeTypeCountMap = new HashMap<>();
        for (Object[] stat : changeTypeStats) {
            changeTypeCountMap.put(stat[0].toString(), (Long) stat[1]);
        }
        stats.put("changeTypeStats", changeTypeCountMap);
        
        // 操作人统计
        List<Object[]> operatorStats = passwordLogRepository.countByCreatedBy();
        stats.put("operatorStats", operatorStats);
        
        // 账号变更统计
        List<Object[]> userStats = passwordLogRepository.countByZoomUserId();
        stats.put("userStats", userStats);
        
        return stats;
    }
    
    /**
     * 获取指定时间范围内的变更次数
     */
    public long getChangeCountByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return passwordLogRepository.countByTimeRange(startTime, endTime);
    }
    
    /**
     * 获取账号在指定时间范围内的变更次数
     */
    public long getChangeCountByZoomUserIdAndTimeRange(Long zoomUserId, LocalDateTime startTime, LocalDateTime endTime) {
        return passwordLogRepository.countByZoomUserIdAndTimeRange(zoomUserId, startTime, endTime);
    }
    
    /**
     * 获取频繁变更密码的账号
     */
    public List<Object[]> getFrequentlyChangedAccounts(LocalDateTime sinceTime, long minCount) {
        return passwordLogRepository.findFrequentlyChangedAccounts(sinceTime, minCount);
    }
    
    /**
     * 获取最近的密码变更日志
     */
    public Page<JoinAccountPasswordLog> getRecentChanges(Pageable pageable) {
        return passwordLogRepository.findRecentChanges(pageable);
    }
    
    /**
     * 获取指定日期的密码变更日志
     */
    public List<JoinAccountPasswordLog> getLogsByDate(LocalDateTime date) {
        return passwordLogRepository.findByDate(date);
    }
    
    /**
     * 删除日志（谨慎使用）
     */
    @Transactional
    public void deleteLog(Long logId) {
        JoinAccountPasswordLog passwordLog = passwordLogRepository.findById(logId)
                .orElseThrow(() -> new IllegalArgumentException("密码变更日志不存在: " + logId));

        log.info("删除密码变更日志: ID={}, ZoomUserId={}, 变更类型={}",
                logId, passwordLog.getZoomUserId(), passwordLog.getChangeType());

        passwordLogRepository.delete(passwordLog);
    }
    
    /**
     * 批量删除指定时间之前的日志（用于数据清理）
     */
    @Transactional
    public int deleteLogsBefore(LocalDateTime beforeTime) {
        List<JoinAccountPasswordLog> logsToDelete = passwordLogRepository.findByCreatedAtBetween(
                LocalDateTime.of(2000, 1, 1, 0, 0), beforeTime);
        
        int deleteCount = logsToDelete.size();
        passwordLogRepository.deleteAll(logsToDelete);
        
        log.info("批量删除密码变更日志: 删除数量={}, 截止时间={}", deleteCount, beforeTime);
        return deleteCount;
    }
}
