package com.zoombus.service;

import com.zoombus.entity.JoinAccountPasswordLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.Random;

/**
 * Join Account密码管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JoinAccountPasswordService {
    
    private final JoinAccountPasswordLogService passwordLogService;
    private final ZoomApiService zoomApiService;
    private final JoinAccountUsageWindowService windowService;
    
    // 密码字符集（排除容易混淆的字符）
    private static final String PASSWORD_CHARS = "ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789";
    private static final int DEFAULT_PASSWORD_LENGTH = 12;
    
    private final Random random = new SecureRandom();
    
    /**
     * 生成随机密码
     */
    public String generatePassword() {
        return generatePassword(DEFAULT_PASSWORD_LENGTH);
    }

    /**
     * 生成Join Account Rental专用密码
     * 格式：Aa + 6位不含4的数字
     */
    public String generateJoinAccountPassword() {
        StringBuilder password = new StringBuilder("Aa");
        // 可用数字：0,1,2,3,5,6,7,8,9 (不含4)
        int[] availableDigits = {0, 1, 2, 3, 5, 6, 7, 8, 9};

        for (int i = 0; i < 6; i++) {
            int randomIndex = random.nextInt(availableDigits.length);
            password.append(availableDigits[randomIndex]);
        }

        log.info("生成Join Account密码: {}", password.toString());
        return password.toString();
    }
    
    /**
     * 生成指定长度的随机密码
     */
    public String generatePassword(int length) {
        if (length < 8) {
            throw new IllegalArgumentException("密码长度不能少于8位");
        }
        if (length > 20) {
            throw new IllegalArgumentException("密码长度不能超过20位");
        }
        
        StringBuilder password = new StringBuilder();
        for (int i = 0; i < length; i++) {
            password.append(PASSWORD_CHARS.charAt(random.nextInt(PASSWORD_CHARS.length())));
        }
        
        return password.toString();
    }
    
    /**
     * 为窗口开启变更密码
     */
    @Transactional
    public String changePasswordForWindowOpen(Long zoomUserId, String newPassword, Long windowId) {
        try {
            // 获取当前密码（如果有的话）
            String oldPassword = getCurrentPassword(zoomUserId);
            
            // 调用Zoom API更新密码（如果失败会抛出异常）
            updateZoomUserPassword(zoomUserId, newPassword);
            
            // 记录密码变更日志
            passwordLogService.logWindowOpenPasswordChange(zoomUserId, oldPassword, newPassword, windowId);
            
            log.info("窗口开启密码变更成功: ZoomUserId={}, WindowId={}", zoomUserId, windowId);
            return newPassword;
            
        } catch (Exception e) {
            log.error("窗口开启密码变更失败: ZoomUserId={}, WindowId={}", zoomUserId, windowId, e);

            // 记录错误信息到窗口实体
            try {
                windowService.recordWindowError(windowId, "开启窗口密码变更失败: " + e.getMessage());
            } catch (Exception recordError) {
                log.error("记录窗口错误信息失败: {}", recordError.getMessage());
            }

            throw new RuntimeException("密码变更失败: " + e.getMessage());
        }
    }
    
    /**
     * 为窗口关闭变更密码
     */
    @Transactional
    public String changePasswordForWindowClose(Long zoomUserId, String newPassword, Long windowId) {
        try {
            // 获取当前密码
            String oldPassword = getCurrentPassword(zoomUserId);
            
            // 调用Zoom API更新密码（如果失败会抛出异常）
            updateZoomUserPassword(zoomUserId, newPassword);
            
            // 记录密码变更日志
            passwordLogService.logWindowClosePasswordChange(zoomUserId, oldPassword, newPassword, windowId);
            
            log.info("窗口关闭密码变更成功: ZoomUserId={}, WindowId={}", zoomUserId, windowId);
            return newPassword;
            
        } catch (Exception e) {
            log.error("窗口关闭密码变更失败: ZoomUserId={}, WindowId={}", zoomUserId, windowId, e);

            // 记录错误信息到窗口实体
            try {
                windowService.recordWindowError(windowId, "关闭窗口密码变更失败: " + e.getMessage());
            } catch (Exception recordError) {
                log.error("记录窗口错误信息失败: {}", recordError.getMessage());
            }

            throw new RuntimeException("密码变更失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动变更密码
     */
    @Transactional
    public String changePasswordManually(Long zoomUserId, String operator) {
        try {
            // 获取当前密码
            String oldPassword = getCurrentPassword(zoomUserId);
            
            // 生成新密码
            String newPassword = generatePassword();
            
            // 调用Zoom API更新密码（如果失败会抛出异常）
            updateZoomUserPassword(zoomUserId, newPassword);
            
            // 记录密码变更日志
            passwordLogService.logManualPasswordChange(zoomUserId, oldPassword, newPassword, operator);
            
            log.info("手动密码变更成功: ZoomUserId={}, Operator={}", zoomUserId, operator);
            return newPassword;
            
        } catch (Exception e) {
            log.error("手动密码变更失败: ZoomUserId={}, Operator={}", zoomUserId, operator, e);
            throw new RuntimeException("密码变更失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前密码（从最近的密码变更日志中获取）
     */
    private String getCurrentPassword(Long zoomUserId) {
        return passwordLogService.getLatestLogByZoomUserId(zoomUserId)
                .map(JoinAccountPasswordLog::getNewPassword)
                .orElse(null);
    }
    
    /**
     * 调用Zoom API更新用户密码
     */
    private boolean updateZoomUserPassword(Long zoomUserId, String newPassword) {
        try {
            log.info("开始调用Zoom API更新密码: ZoomUserId={}, NewPassword={}", zoomUserId, maskPassword(newPassword));

            // 调用Zoom API服务更新密码（现在会抛出异常而不是返回false）
            boolean success = zoomApiService.updateUserPassword(zoomUserId, newPassword);

            log.info("Zoom API更新密码成功: ZoomUserId={}", zoomUserId);
            return success;

        } catch (Exception e) {
            log.error("调用Zoom API更新密码异常: ZoomUserId={}, Error={}", zoomUserId, e.getMessage());
            // 重新抛出异常，保留详细的错误信息
            throw new RuntimeException("Zoom API更新密码失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 密码脱敏
     */
    private String maskPassword(String password) {
        if (password == null || password.length() <= 4) {
            return "****";
        }
        return password.substring(0, 2) + "****" + password.substring(password.length() - 2);
    }
    
    /**
     * 验证密码强度
     */
    public boolean isPasswordStrong(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }
        
        boolean hasUpper = false;
        boolean hasLower = false;
        boolean hasDigit = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            }
        }
        
        return hasUpper && hasLower && hasDigit;
    }
    
    /**
     * 批量重置密码
     */
    @Transactional
    public int batchResetPasswords(java.util.List<Long> zoomUserIds, String operator) {
        int successCount = 0;
        
        for (Long zoomUserId : zoomUserIds) {
            try {
                changePasswordManually(zoomUserId, operator);
                successCount++;
            } catch (Exception e) {
                log.error("批量重置密码失败: ZoomUserId={}", zoomUserId, e);
            }
        }
        
        log.info("批量重置密码完成: 成功={}, 总数={}, 操作人={}", successCount, zoomUserIds.size(), operator);
        return successCount;
    }
}
