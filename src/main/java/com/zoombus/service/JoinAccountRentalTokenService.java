package com.zoombus.service;

import com.zoombus.entity.JoinAccountRentalToken;
import com.zoombus.repository.JoinAccountRentalTokenRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Join Account Rental令牌服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JoinAccountRentalTokenService {
    
    private final JoinAccountRentalTokenRepository tokenRepository;
    private final SystemConfigService systemConfigService;
    
    /**
     * 批量生成权益链接
     */
    @Transactional
    public List<JoinAccountRentalToken> batchGenerateTokens(Integer usageDays, Integer quantity, String remark, String operator) {
        if (usageDays == null || usageDays <= 0) {
            throw new IllegalArgumentException("使用天数必须大于0");
        }
        if (quantity == null || quantity <= 0) {
            throw new IllegalArgumentException("生成数量必须大于0");
        }
        if (quantity > 1000) {
            throw new IllegalArgumentException("单次生成数量不能超过1000个");
        }
        
        // 生成批次号
        String batchNumber = generateBatchNumber();
        
        List<JoinAccountRentalToken> tokens = new ArrayList<>();
        Set<String> generatedTokenNumbers = new HashSet<>();
        
        for (int i = 0; i < quantity; i++) {
            String tokenNumber = generateUniqueTokenNumber(generatedTokenNumbers);
            generatedTokenNumbers.add(tokenNumber);
            
            JoinAccountRentalToken token = new JoinAccountRentalToken();
            token.setTokenNumber(tokenNumber);
            token.setBatchNumber(batchNumber);
            token.setUsageDays(usageDays);
            token.setStatus(JoinAccountRentalToken.TokenStatus.PENDING);
            token.setExportStatus(JoinAccountRentalToken.ExportStatus.NOT_EXPORTED);
            token.setRemark(remark);
            
            tokens.add(token);
        }
        
        List<JoinAccountRentalToken> savedTokens = tokenRepository.saveAll(tokens);
        log.info("批量生成权益链接完成: 批次号={}, 数量={}, 使用天数={}, 操作人={}", 
                batchNumber, quantity, usageDays, operator);
        
        return savedTokens;
    }
    
    /**
     * 生成批次号
     * 格式：JAR_BATCH_YYYYMMDD_HHMMSS
     */
    private String generateBatchNumber() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        return "JAR_BATCH_" + now.format(formatter);
    }
    
    /**
     * 生成唯一的Token编号
     * 规则：6位数字字母组合，字母大写，数字不含4
     */
    private String generateUniqueTokenNumber(Set<String> existingNumbers) {
        String tokenNumber;
        int maxAttempts = 1000;
        int attempts = 0;
        
        do {
            tokenNumber = generateTokenNumber();
            attempts++;
            
            if (attempts > maxAttempts) {
                throw new RuntimeException("生成唯一Token编号失败，请稍后重试");
            }
        } while (existingNumbers.contains(tokenNumber) || tokenRepository.existsByTokenNumber(tokenNumber));
        
        return tokenNumber;
    }
    
    /**
     * 生成Token编号
     * 6位数字字母组合，字母大写，数字不含4
     */
    private String generateTokenNumber() {
        String chars = "ABCDEFGHJKLMNPQRSTUVWXYZ0123567890"; // 排除I、O、4
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < 6; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * 根据ID获取令牌
     */
    public Optional<JoinAccountRentalToken> getTokenById(Long id) {
        return tokenRepository.findById(id);
    }
    
    /**
     * 根据Token编号获取令牌
     */
    public Optional<JoinAccountRentalToken> getTokenByNumber(String tokenNumber) {
        return tokenRepository.findByTokenNumber(tokenNumber);
    }
    
    /**
     * 分页查询令牌
     */
    public Page<JoinAccountRentalToken> getTokens(Pageable pageable) {
        return tokenRepository.findAll(pageable);
    }
    
    /**
     * 多条件查询令牌
     */
    public Page<JoinAccountRentalToken> searchTokens(
            String tokenNumber,
            String batchNumber,
            JoinAccountRentalToken.TokenStatus status,
            JoinAccountRentalToken.ExportStatus exportStatus,
            Integer usageDays,
            LocalDateTime startTime,
            LocalDateTime endTime,
            Pageable pageable) {

        return tokenRepository.findByConditions(
                tokenNumber, batchNumber, status, exportStatus, usageDays, startTime, endTime, pageable);
    }
    
    /**
     * 获取令牌统计信息
     */
    public Map<String, Object> getTokenStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总数统计
        stats.put("totalCount", tokenRepository.count());
        
        // 状态统计
        List<Object[]> statusStats = tokenRepository.countByStatus();
        Map<String, Long> statusCountMap = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusCountMap.put(stat[0].toString(), (Long) stat[1]);
        }
        stats.put("statusStats", statusCountMap);
        
        // 导出状态统计
        List<Object[]> exportStats = tokenRepository.countByExportStatus();
        Map<String, Long> exportCountMap = new HashMap<>();
        for (Object[] stat : exportStats) {
            exportCountMap.put(stat[0].toString(), (Long) stat[1]);
        }
        stats.put("exportStats", exportCountMap);
        
        // 批次统计
        List<Object[]> batchStats = tokenRepository.countByBatchNumber();
        stats.put("batchStats", batchStats);
        
        return stats;
    }
    
    /**
     * 生成权益链接
     */
    public String generateTokenLink(String tokenNumber) {
        String baseUrl = systemConfigService.getConfigValue("join_account.domain.base_url", "https://zoombus.cn");
        String pathPrefix = systemConfigService.getConfigValue("join_account.domain.path_prefix", "rf");
        
        return baseUrl + "/" + pathPrefix + "/" + tokenNumber;
    }
    
    /**
     * 为令牌生成完整链接信息
     */
    public Map<String, Object> getTokenWithLink(JoinAccountRentalToken token) {
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("link", generateTokenLink(token.getTokenNumber()));
        return result;
    }
    
    /**
     * 检查令牌是否可以预约
     */
    public boolean canReserveToken(String tokenNumber) {
        Optional<JoinAccountRentalToken> tokenOpt = getTokenByNumber(tokenNumber);
        return tokenOpt.isPresent() && tokenOpt.get().canReserve();
    }
    
    /**
     * 检查令牌是否可以导出
     */
    public boolean canExportToken(Long tokenId) {
        Optional<JoinAccountRentalToken> tokenOpt = getTokenById(tokenId);
        return tokenOpt.isPresent() && tokenOpt.get().canExport();
    }
    
    /**
     * 检查令牌是否可以作废
     */
    public boolean canCancelToken(Long tokenId) {
        Optional<JoinAccountRentalToken> tokenOpt = getTokenById(tokenId);
        return tokenOpt.isPresent() && tokenOpt.get().canCancel();
    }
    
    /**
     * 获取可导出的令牌
     */
    public List<JoinAccountRentalToken> getExportableTokens() {
        return tokenRepository.findExportableTokens();
    }
    
    /**
     * 获取可预约的令牌
     */
    public List<JoinAccountRentalToken> getReservableTokens() {
        return tokenRepository.findReservableTokens();
    }
    
    /**
     * 获取可作废的令牌
     */
    public List<JoinAccountRentalToken> getCancellableTokens() {
        return tokenRepository.findCancellableTokens();
    }
    
    /**
     * 根据ID列表获取令牌
     */
    public List<JoinAccountRentalToken> getTokensByIds(List<Long> ids) {
        return tokenRepository.findByIdIn(ids);
    }

    /**
     * 标记令牌为已导出
     */
    @Transactional
    public List<JoinAccountRentalToken> markTokensAsExported(List<Long> tokenIds, String operator) {
        List<JoinAccountRentalToken> tokens = getTokensByIds(tokenIds);

        for (JoinAccountRentalToken token : tokens) {
            if (!token.canExport()) {
                throw new IllegalStateException("令牌 " + token.getTokenNumber() + " 不能导出");
            }

            token.setStatus(JoinAccountRentalToken.TokenStatus.EXPORTED);
            token.setExportStatus(JoinAccountRentalToken.ExportStatus.EXPORTED);
            token.setExportedAt(LocalDateTime.now());
            token.setExportedBy(operator != null ? operator : "ADMIN");
        }

        List<JoinAccountRentalToken> savedTokens = tokenRepository.saveAll(tokens);
        log.info("批量标记令牌为已导出: 数量={}, 操作人={}", tokens.size(), operator);

        return savedTokens;
    }

    /**
     * 作废令牌
     */
    @Transactional
    public List<JoinAccountRentalToken> cancelTokens(List<Long> tokenIds, String operator, String reason) {
        List<JoinAccountRentalToken> tokens = getTokensByIds(tokenIds);

        for (JoinAccountRentalToken token : tokens) {
            if (!token.canCancel()) {
                throw new IllegalStateException("令牌 " + token.getTokenNumber() + " 不能作废");
            }

            token.setStatus(JoinAccountRentalToken.TokenStatus.CANCELLED);
            token.setCancelledAt(LocalDateTime.now());
            token.setCancelledBy(operator != null ? operator : "ADMIN");
            if (reason != null && !reason.trim().isEmpty()) {
                String currentRemark = token.getRemark();
                String newRemark = (currentRemark != null ? currentRemark + "; " : "") + "作废原因: " + reason;
                token.setRemark(newRemark);
            }
        }

        List<JoinAccountRentalToken> savedTokens = tokenRepository.saveAll(tokens);
        log.info("批量作废令牌: 数量={}, 操作人={}, 原因={}", tokens.size(), operator, reason);

        return savedTokens;
    }

    /**
     * 预约令牌（分配账号和设置使用窗口）
     */
    @Transactional
    public JoinAccountRentalToken reserveToken(String tokenNumber, Long zoomUserId, String zoomUserEmail,
                                               LocalDateTime startTime, String assignedPassword, String operator) {
        JoinAccountRentalToken token = getTokenByNumber(tokenNumber)
                .orElseThrow(() -> new IllegalArgumentException("令牌不存在: " + tokenNumber));

        if (!token.canReserve()) {
            throw new IllegalStateException("令牌不能预约: " + tokenNumber);
        }

        // 计算结束时间
        LocalDateTime endTime = token.calculateEndTime(startTime);

        // 更新令牌状态
        token.setStatus(JoinAccountRentalToken.TokenStatus.RESERVED);
        token.setReservedAt(LocalDateTime.now());
        token.setAssignedZoomUserId(zoomUserId);
        token.setAssignedZoomUserEmail(zoomUserEmail);
        token.setAssignedPassword(assignedPassword);
        token.setWindowStartTime(startTime);
        token.setWindowEndTime(endTime);

        JoinAccountRentalToken savedToken = tokenRepository.save(token);
        log.info("预约令牌成功: Token={}, ZoomUserId={}, 窗口时间={} - {}, 操作人={}",
                tokenNumber, zoomUserId, startTime, endTime, operator);

        return savedToken;
    }

    /**
     * 激活令牌（开始使用窗口）
     */
    @Transactional
    public JoinAccountRentalToken activateToken(String tokenNumber, String password) {
        JoinAccountRentalToken token = getTokenByNumber(tokenNumber)
                .orElseThrow(() -> new IllegalArgumentException("令牌不存在: " + tokenNumber));

        if (token.getStatus() != JoinAccountRentalToken.TokenStatus.RESERVED) {
            throw new IllegalStateException("只能激活已预约的令牌: " + tokenNumber);
        }

        // 检查是否到了开始时间
        LocalDateTime now = LocalDateTime.now();
        if (token.getWindowStartTime() != null && now.isBefore(token.getWindowStartTime())) {
            throw new IllegalStateException("令牌使用窗口尚未开始: " + tokenNumber);
        }

        token.setStatus(JoinAccountRentalToken.TokenStatus.ACTIVE);
        token.setAssignedPassword(password);

        JoinAccountRentalToken savedToken = tokenRepository.save(token);
        log.info("激活令牌成功: Token={}, ZoomUserId={}", tokenNumber, token.getAssignedZoomUserId());

        return savedToken;
    }

    /**
     * 完成令牌（结束使用窗口）
     */
    @Transactional
    public JoinAccountRentalToken completeToken(String tokenNumber) {
        JoinAccountRentalToken token = getTokenByNumber(tokenNumber)
                .orElseThrow(() -> new IllegalArgumentException("令牌不存在: " + tokenNumber));

        if (token.getStatus() != JoinAccountRentalToken.TokenStatus.ACTIVE) {
            throw new IllegalStateException("只能完成活跃状态的令牌: " + tokenNumber);
        }

        token.setStatus(JoinAccountRentalToken.TokenStatus.COMPLETED);

        JoinAccountRentalToken savedToken = tokenRepository.save(token);
        log.info("完成令牌成功: Token={}, ZoomUserId={}", tokenNumber, token.getAssignedZoomUserId());

        return savedToken;
    }

    /**
     * 获取需要激活的令牌
     */
    public List<JoinAccountRentalToken> getTokensToActivate() {
        LocalDateTime currentTime = LocalDateTime.now();
        return tokenRepository.findTokensToActivate(currentTime);
    }

    /**
     * 获取需要完成的令牌
     */
    public List<JoinAccountRentalToken> getTokensToComplete() {
        LocalDateTime currentTime = LocalDateTime.now();
        return tokenRepository.findTokensToComplete(currentTime);
    }

    /**
     * 导出权益链接为Excel文件
     */
    @Transactional
    public ByteArrayResource exportTokensToExcel(List<Long> tokenIds, String operator) {
        try {
            // 获取要导出的令牌
            List<JoinAccountRentalToken> tokens = getTokensByIds(tokenIds);

            // 验证令牌是否可以导出
            for (JoinAccountRentalToken token : tokens) {
                if (!token.canExport()) {
                    throw new IllegalStateException("令牌 " + token.getTokenNumber() + " 不能导出");
                }
            }

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("权益链接");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            Cell headerCell = headerRow.createCell(0);
            headerCell.setCellValue("权益链接");

            // 创建标题样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerStyle.setFont(headerFont);
            headerCell.setCellStyle(headerStyle);

            // 填充数据行
            int rowNum = 1;
            for (JoinAccountRentalToken token : tokens) {
                Row row = sheet.createRow(rowNum++);
                Cell cell = row.createCell(0);
                String link = generateTokenLink(token.getTokenNumber());
                cell.setCellValue(link);
            }

            // 自动调整列宽
            sheet.autoSizeColumn(0);

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();

            // 标记令牌为已导出
            markTokensAsExported(tokenIds, operator);

            log.info("成功导出 {} 个权益链接到Excel文件", tokens.size());
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (Exception e) {
            log.error("导出权益链接Excel失败", e);
            throw new RuntimeException("导出Excel失败: " + e.getMessage(), e);
        }
    }
}
