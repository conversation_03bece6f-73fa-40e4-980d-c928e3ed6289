package com.zoombus.service;

import com.zoombus.entity.JoinAccountUsageWindow;
import com.zoombus.repository.JoinAccountUsageWindowRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Join Account使用窗口服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JoinAccountUsageWindowService {
    
    private final JoinAccountUsageWindowRepository windowRepository;
    
    /**
     * 创建使用窗口
     */
    @Transactional
    public JoinAccountUsageWindow createWindow(Long zoomUserId, String tokenNumber, LocalDateTime startTime, LocalDateTime endTime) {
        if (zoomUserId == null) {
            throw new IllegalArgumentException("Zoom账号ID不能为空");
        }
        if (tokenNumber == null || tokenNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Token编号不能为空");
        }
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("窗口开始时间和结束时间不能为空");
        }
        if (!startTime.isBefore(endTime)) {
            throw new IllegalArgumentException("窗口开始时间必须早于结束时间");
        }
        
        // 检查时间冲突
        List<JoinAccountUsageWindow> overlappingWindows = windowRepository.findOverlappingWindows(zoomUserId, startTime, endTime);
        if (!overlappingWindows.isEmpty()) {
            throw new IllegalArgumentException("该账号在指定时间段内已有使用窗口");
        }
        
        JoinAccountUsageWindow window = new JoinAccountUsageWindow();
        window.setZoomUserId(zoomUserId);
        window.setTokenNumber(tokenNumber);
        window.setStartTime(startTime);
        window.setEndTime(endTime);
        window.setStatus(JoinAccountUsageWindow.WindowStatus.PENDING);
        
        JoinAccountUsageWindow savedWindow = windowRepository.save(window);
        log.info("创建使用窗口: ID={}, ZoomUserId={}, Token={}, 时间={} - {}", 
                savedWindow.getId(), zoomUserId, tokenNumber, startTime, endTime);
        
        return savedWindow;
    }
    
    /**
     * 根据ID获取窗口
     */
    public Optional<JoinAccountUsageWindow> getWindowById(Long id) {
        return windowRepository.findById(id);
    }
    
    /**
     * 根据Token编号获取窗口
     */
    public Optional<JoinAccountUsageWindow> getWindowByToken(String tokenNumber) {
        return windowRepository.findFirstByTokenNumber(tokenNumber);
    }
    
    /**
     * 根据Zoom账号ID获取窗口
     */
    public List<JoinAccountUsageWindow> getWindowsByZoomUserId(Long zoomUserId) {
        return windowRepository.findByZoomUserId(zoomUserId);
    }
    
    /**
     * 分页查询窗口
     */
    public Page<JoinAccountUsageWindow> getWindows(Pageable pageable) {
        return windowRepository.findAll(pageable);
    }
    
    /**
     * 多条件查询窗口
     */
    public Page<JoinAccountUsageWindow> searchWindows(
            Long zoomUserId,
            String tokenNumber,
            JoinAccountUsageWindow.WindowStatus status,
            LocalDateTime startTime,
            LocalDateTime endTime,
            Pageable pageable) {
        
        return windowRepository.findByConditions(zoomUserId, tokenNumber, status, startTime, endTime, pageable);
    }
    
    /**
     * 获取需要开启的窗口
     */
    public List<JoinAccountUsageWindow> getWindowsToOpen() {
        LocalDateTime currentTime = LocalDateTime.now();
        return windowRepository.findWindowsToOpen(currentTime);
    }
    
    /**
     * 获取需要关闭的窗口
     */
    public List<JoinAccountUsageWindow> getWindowsToClose() {
        LocalDateTime currentTime = LocalDateTime.now();
        return windowRepository.findWindowsToClose(currentTime);
    }
    
    /**
     * 开启窗口
     */
    @Transactional
    public JoinAccountUsageWindow openWindow(Long windowId) {
        JoinAccountUsageWindow window = windowRepository.findById(windowId)
                .orElseThrow(() -> new IllegalArgumentException("窗口不存在: " + windowId));
        
        if (window.getStatus() != JoinAccountUsageWindow.WindowStatus.PENDING) {
            throw new IllegalStateException("只能开启待开启状态的窗口");
        }
        
        window.setStatus(JoinAccountUsageWindow.WindowStatus.ACTIVE);
        window.setOpenedAt(LocalDateTime.now());
        
        JoinAccountUsageWindow savedWindow = windowRepository.save(window);
        log.info("开启使用窗口: ID={}, ZoomUserId={}, Token={}", 
                windowId, window.getZoomUserId(), window.getTokenNumber());
        
        return savedWindow;
    }
    
    /**
     * 关闭窗口
     */
    @Transactional
    public JoinAccountUsageWindow closeWindow(Long windowId) {
        JoinAccountUsageWindow window = windowRepository.findById(windowId)
                .orElseThrow(() -> new IllegalArgumentException("窗口不存在: " + windowId));
        
        if (window.getStatus() != JoinAccountUsageWindow.WindowStatus.ACTIVE) {
            throw new IllegalStateException("只能关闭活跃状态的窗口");
        }
        
        window.setStatus(JoinAccountUsageWindow.WindowStatus.CLOSED);
        window.setClosedAt(LocalDateTime.now());
        
        JoinAccountUsageWindow savedWindow = windowRepository.save(window);
        log.info("关闭使用窗口: ID={}, ZoomUserId={}, Token={}, 实际使用时长={}分钟", 
                windowId, window.getZoomUserId(), window.getTokenNumber(), window.getActualUsageMinutes());
        
        return savedWindow;
    }
    
    /**
     * 检查账号是否有重叠的窗口
     */
    public boolean hasOverlappingWindows(Long zoomUserId, LocalDateTime startTime, LocalDateTime endTime) {
        List<JoinAccountUsageWindow> overlappingWindows = windowRepository.findOverlappingWindows(zoomUserId, startTime, endTime);
        return !overlappingWindows.isEmpty();
    }
    
    /**
     * 获取账号的下一个窗口
     */
    public Optional<JoinAccountUsageWindow> getNextWindow(Long zoomUserId) {
        LocalDateTime currentTime = LocalDateTime.now();
        return windowRepository.findNextWindowByZoomUserId(zoomUserId, currentTime);
    }
    
    /**
     * 获取账号当前活跃的窗口
     */
    public Optional<JoinAccountUsageWindow> getCurrentActiveWindow(Long zoomUserId) {
        LocalDateTime currentTime = LocalDateTime.now();
        return windowRepository.findCurrentActiveWindow(zoomUserId, currentTime);
    }
    
    /**
     * 获取窗口统计信息
     */
    public Map<String, Object> getWindowStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总数统计
        stats.put("totalCount", windowRepository.count());
        
        // 状态统计
        List<Object[]> statusStats = windowRepository.countByStatus();
        Map<String, Long> statusCountMap = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusCountMap.put(stat[0].toString(), (Long) stat[1]);
        }
        stats.put("statusStats", statusCountMap);
        
        // 账号使用统计
        List<Object[]> userStats = windowRepository.countByZoomUserId();
        stats.put("userStats", userStats);

        return stats;
    }

    /**
     * 记录窗口错误信息
     */
    @Transactional(propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    public void recordWindowError(Long windowId, String errorMessage) {
        JoinAccountUsageWindow window = windowRepository.findById(windowId)
                .orElseThrow(() -> new IllegalArgumentException("窗口不存在: " + windowId));

        window.setLastOperationError(errorMessage);

        // 如果之前没有错误信息，设置为当前错误
        if (window.getErrorMessage() == null || window.getErrorMessage().trim().isEmpty()) {
            window.setErrorMessage(errorMessage);
        } else {
            // 如果已有错误信息，追加新的错误信息
            window.setErrorMessage(window.getErrorMessage() + "; " + errorMessage);
        }

        windowRepository.save(window);
        log.info("记录窗口错误信息: ID={}, 错误={}", windowId, errorMessage);
    }

    /**
     * 清除窗口错误信息
     */
    @Transactional(propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    public void clearWindowError(Long windowId) {
        JoinAccountUsageWindow window = windowRepository.findById(windowId)
                .orElseThrow(() -> new IllegalArgumentException("窗口不存在: " + windowId));

        window.setErrorMessage(null);
        window.setLastOperationError(null);
        windowRepository.save(window);
        log.info("清除窗口错误信息: ID={}", windowId);
    }
    
    /**
     * 获取即将开启的窗口
     */
    public List<JoinAccountUsageWindow> getUpcomingWindows(int minutes) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureTime = now.plusMinutes(minutes);
        return windowRepository.findUpcomingWindows(now, futureTime);
    }
    
    /**
     * 获取即将关闭的窗口
     */
    public List<JoinAccountUsageWindow> getExpiringWindows(int minutes) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureTime = now.plusMinutes(minutes);
        return windowRepository.findExpiringWindows(now, futureTime);
    }
    
    /**
     * 获取过期但未关闭的窗口
     */
    public List<JoinAccountUsageWindow> getExpiredActiveWindows() {
        LocalDateTime currentTime = LocalDateTime.now();
        return windowRepository.findExpiredActiveWindows(currentTime);
    }
    
    /**
     * 删除窗口
     */
    @Transactional
    public void deleteWindow(Long windowId) {
        JoinAccountUsageWindow window = windowRepository.findById(windowId)
                .orElseThrow(() -> new IllegalArgumentException("窗口不存在: " + windowId));
        
        log.info("删除使用窗口: ID={}, ZoomUserId={}, Token={}", 
                windowId, window.getZoomUserId(), window.getTokenNumber());
        
        windowRepository.delete(window);
    }
}
