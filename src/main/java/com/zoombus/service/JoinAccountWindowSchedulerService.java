package com.zoombus.service;

import com.zoombus.entity.JoinAccountUsageWindow;
import com.zoombus.entity.JoinAccountRentalToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Join Account窗口定时任务调度服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JoinAccountWindowSchedulerService {
    
    private final JoinAccountUsageWindowService windowService;
    private final JoinAccountRentalTokenService tokenService;
    private final JoinAccountPasswordService passwordService;
    
    /**
     * 定时检查并开启使用窗口
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000) // 60秒
    @Transactional
    public void processWindowsToOpen() {
        try {
            List<JoinAccountUsageWindow> windowsToOpen = windowService.getWindowsToOpen();
            
            if (windowsToOpen.isEmpty()) {
                log.debug("没有需要开启的使用窗口");
                return;
            }
            
            log.info("开始处理需要开启的使用窗口，数量: {}", windowsToOpen.size());
            
            int successCount = 0;
            int failureCount = 0;
            
            for (JoinAccountUsageWindow window : windowsToOpen) {
                try {
                    // 开启窗口
                    windowService.openWindow(window.getId());
                    
                    // 激活对应的令牌
                    String tokenNumber = window.getTokenNumber();

                    // 获取预约时生成的密码
                    JoinAccountRentalToken token = tokenService.getTokenByNumber(tokenNumber)
                            .orElseThrow(() -> new IllegalArgumentException("令牌不存在: " + tokenNumber));
                    String reservedPassword = token.getAssignedPassword();

                    if (reservedPassword == null || reservedPassword.trim().isEmpty()) {
                        throw new IllegalStateException("令牌未设置预约密码: " + tokenNumber);
                    }

                    // 使用预约时生成的密码激活令牌
                    tokenService.activateToken(tokenNumber, reservedPassword);

                    // 调用Zoom API设置密码并记录日志
                    passwordService.changePasswordForWindowOpen(
                            window.getZoomUserId(), reservedPassword, window.getId());
                    
                    successCount++;
                    log.info("成功开启使用窗口: ID={}, Token={}, ZoomUserId={}", 
                            window.getId(), tokenNumber, window.getZoomUserId());
                    
                } catch (Exception e) {
                    failureCount++;
                    log.error("开启使用窗口失败: ID={}, Token={}, 错误: {}",
                            window.getId(), window.getTokenNumber(), e.getMessage(), e);

                    // 记录错误信息到窗口实体
                    try {
                        windowService.recordWindowError(window.getId(), "开启窗口失败: " + e.getMessage());
                    } catch (Exception recordError) {
                        log.error("记录窗口错误信息失败: {}", recordError.getMessage());
                    }
                }
            }
            
            log.info("使用窗口开启处理完成: 成功={}, 失败={}", successCount, failureCount);
            
        } catch (Exception e) {
            log.error("处理使用窗口开启任务失败", e);
        }
    }
    
    /**
     * 定时检查并关闭使用窗口
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000) // 60秒
    @Transactional
    public void processWindowsToClose() {
        try {
            List<JoinAccountUsageWindow> windowsToClose = windowService.getWindowsToClose();
            
            if (windowsToClose.isEmpty()) {
                log.debug("没有需要关闭的使用窗口");
                return;
            }
            
            log.info("开始处理需要关闭的使用窗口，数量: {}", windowsToClose.size());
            
            int successCount = 0;
            int failureCount = 0;
            
            for (JoinAccountUsageWindow window : windowsToClose) {
                try {
                    // 关闭窗口
                    windowService.closeWindow(window.getId());
                    
                    // 完成对应的令牌
                    String tokenNumber = window.getTokenNumber();
                    tokenService.completeToken(tokenNumber);
                    
                    // 重置密码
                    String newPassword = passwordService.generatePassword();
                    passwordService.changePasswordForWindowClose(
                            window.getZoomUserId(), newPassword, window.getId());
                    
                    successCount++;
                    log.info("成功关闭使用窗口: ID={}, Token={}, ZoomUserId={}, 实际使用时长={}分钟", 
                            window.getId(), tokenNumber, window.getZoomUserId(), window.getActualUsageMinutes());
                    
                } catch (Exception e) {
                    failureCount++;
                    log.error("关闭使用窗口失败: ID={}, Token={}, 错误: {}",
                            window.getId(), window.getTokenNumber(), e.getMessage(), e);

                    // 记录错误信息到窗口实体
                    try {
                        windowService.recordWindowError(window.getId(), "关闭窗口失败: " + e.getMessage());
                    } catch (Exception recordError) {
                        log.error("记录窗口错误信息失败: {}", recordError.getMessage());
                    }
                }
            }
            
            log.info("使用窗口关闭处理完成: 成功={}, 失败={}", successCount, failureCount);
            
        } catch (Exception e) {
            log.error("处理使用窗口关闭任务失败", e);
        }
    }

    /**
     * 手动开启指定窗口：执行与定时任务相同的完整流程
     */
    @Transactional
    public JoinAccountUsageWindow manualOpenWindow(Long windowId) {
        try {
            // 1) 开启窗口（PENDING -> ACTIVE，写入 openedAt）
            JoinAccountUsageWindow window = windowService.openWindow(windowId);

            // 2) 激活对应令牌（使用预约时生成的密码）
            String tokenNumber = window.getTokenNumber();
            JoinAccountRentalToken token = tokenService.getTokenByNumber(tokenNumber)
                    .orElseThrow(() -> new IllegalArgumentException("令牌不存在: " + tokenNumber));
            String reservedPassword = token.getAssignedPassword();
            if (reservedPassword == null || reservedPassword.trim().isEmpty()) {
                throw new IllegalStateException("令牌未设置预约密码: " + tokenNumber);
            }
            tokenService.activateToken(tokenNumber, reservedPassword);

            // 3) 调用Zoom API设置密码并记录日志
            passwordService.changePasswordForWindowOpen(
                    window.getZoomUserId(), reservedPassword, window.getId());

            log.info("手动开启使用窗口成功: ID={}, Token={}, ZoomUserId={}",
                    window.getId(), tokenNumber, window.getZoomUserId());
            return window;
        } catch (Exception e) {
            log.error("手动开启使用窗口失败: ID={}, 错误: {}", windowId, e.getMessage(), e);

            // 记录错误信息到窗口实体
            try {
                windowService.recordWindowError(windowId, "手动开启窗口失败: " + e.getMessage());
            } catch (Exception recordError) {
                log.error("记录窗口错误信息失败: {}", recordError.getMessage());
            }

            throw e;
        }
    }

    /**
     * 定时清理过期的窗口
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    @Transactional
    public void cleanupExpiredWindows() {
        try {
            List<JoinAccountUsageWindow> expiredWindows = windowService.getExpiredActiveWindows();
            
            if (expiredWindows.isEmpty()) {
                log.debug("没有过期的活跃窗口需要清理");
                return;
            }
            
            log.warn("发现过期的活跃窗口，数量: {}", expiredWindows.size());
            
            int cleanedCount = 0;
            
            for (JoinAccountUsageWindow window : expiredWindows) {
                try {
                    // 强制关闭过期窗口
                    windowService.closeWindow(window.getId());
                    
                    // 完成对应的令牌
                    String tokenNumber = window.getTokenNumber();
                    tokenService.completeToken(tokenNumber);
                    
                    // 重置密码
                    String newPassword = passwordService.generatePassword();
                    passwordService.changePasswordForWindowClose(
                            window.getZoomUserId(), newPassword, window.getId());
                    
                    cleanedCount++;
                    log.warn("强制关闭过期窗口: ID={}, Token={}, ZoomUserId={}, 超时时长={}分钟", 
                            window.getId(), tokenNumber, window.getZoomUserId(), 
                            window.getActualUsageMinutes());
                    
                } catch (Exception e) {
                    log.error("清理过期窗口失败: ID={}, Token={}, 错误: {}", 
                            window.getId(), window.getTokenNumber(), e.getMessage(), e);
                }
            }
            
            log.warn("过期窗口清理完成: 清理数量={}", cleanedCount);
            
        } catch (Exception e) {
            log.error("清理过期窗口任务失败", e);
        }
    }
    
    /**
     * 定时检查即将开启的窗口（提前通知）
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 600000) // 10分钟
    public void checkUpcomingWindows() {
        try {
            // 检查未来30分钟内即将开启的窗口
            List<JoinAccountUsageWindow> upcomingWindows = windowService.getUpcomingWindows(30);
            
            if (!upcomingWindows.isEmpty()) {
                log.info("即将开启的使用窗口数量: {}", upcomingWindows.size());
                
                for (JoinAccountUsageWindow window : upcomingWindows) {
                    long minutesToStart = window.getMinutesToStart();
                    log.info("窗口即将开启: ID={}, Token={}, ZoomUserId={}, 距离开始还有{}分钟", 
                            window.getId(), window.getTokenNumber(), window.getZoomUserId(), minutesToStart);
                }
            }
            
            // 检查未来30分钟内即将关闭的窗口
            List<JoinAccountUsageWindow> expiringWindows = windowService.getExpiringWindows(30);
            
            if (!expiringWindows.isEmpty()) {
                log.info("即将关闭的使用窗口数量: {}", expiringWindows.size());
                
                for (JoinAccountUsageWindow window : expiringWindows) {
                    long minutesToEnd = window.getMinutesToEnd();
                    log.info("窗口即将关闭: ID={}, Token={}, ZoomUserId={}, 距离结束还有{}分钟", 
                            window.getId(), window.getTokenNumber(), window.getZoomUserId(), minutesToEnd);
                }
            }
            
        } catch (Exception e) {
            log.error("检查即将开启/关闭的窗口失败", e);
        }
    }
    
    /**
     * 定时统计窗口使用情况
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateDailyWindowStatistics() {
        try {
            log.info("开始生成每日窗口使用统计");
            
            // 获取窗口统计信息
            var statistics = windowService.getWindowStatistics();
            
            log.info("每日窗口统计: {}", statistics);
            
            // 这里可以扩展为发送统计报告、存储到数据库等
            
        } catch (Exception e) {
            log.error("生成每日窗口统计失败", e);
        }
    }
}
