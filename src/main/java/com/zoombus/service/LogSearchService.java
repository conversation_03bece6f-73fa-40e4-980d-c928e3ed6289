package com.zoombus.service;

import com.zoombus.dto.PageResult;
import com.zoombus.dto.log.LogEntry;
import com.zoombus.dto.log.LogSearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 日志搜索服务
 * 提供日志文件搜索、解析和导出功能
 */
@Service
@Slf4j
public class LogSearchService {
    
    @Value("${app.logs.directory:/var/logs/app}")
    private String logDirectory;
    
    @Value("${app.logs.max-search-days:30}")
    private int maxSearchDays;
    
    // 日志行解析正则表达式
    // 格式：2025-08-23 14:30:25.123 [thread] LEVEL [traceId] logger - message
    private static final Pattern LOG_PATTERN = Pattern.compile(
        "^(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3})\\s+" +  // timestamp
        "\\[([^\\]]+)\\]\\s+" +                                        // thread
        "(\\w+)\\s+" +                                                 // level
        "\\[([^\\]]*?)\\]\\s+" +                                       // traceId
        "([^\\s]+)\\s+-\\s+" +                                         // logger
        "(.*)$"                                                        // message
    );
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 搜索日志
     */
    public PageResult<LogEntry> searchLogs(LogSearchRequest request) {
        log.info("开始搜索日志: {}", request);
        
        if (!request.isValid()) {
            log.warn("搜索请求参数无效: {}", request);
            return new PageResult<>(Collections.emptyList(), 0, 0, request.getPage(), request.getSize());
        }
        
        try {
            List<LogEntry> allEntries = new ArrayList<>();
            
            // 获取日志文件列表
            List<File> logFiles = getLogFiles(request.getTimeRange());
            log.info("找到 {} 个日志文件", logFiles.size());
            
            // 搜索每个日志文件
            for (File logFile : logFiles) {
                List<LogEntry> entries = searchInFile(logFile, request);
                allEntries.addAll(entries);
            }
            
            // 排序：按时间倒序
            allEntries.sort((a, b) -> b.getTimestamp().compareTo(a.getTimestamp()));
            
            // 分页
            return paginateResults(allEntries, request.getPage(), request.getSize());
            
        } catch (Exception e) {
            log.error("搜索日志失败", e);
            return new PageResult<>(Collections.emptyList(), 0, 0, request.getPage(), request.getSize());
        }
    }
    
    /**
     * 获取完整调用链
     */
    public List<LogEntry> getCompleteTraceChain(String traceId) {
        log.info("获取调用链: {}", traceId);
        
        LogSearchRequest request = new LogSearchRequest();
        request.setTraceId(traceId);
        request.setTimeRange(getRecentTimeRange()); // 最近24小时
        request.setPage(1);
        request.setSize(10000); // 获取所有相关日志
        
        PageResult<LogEntry> result = searchLogs(request);
        return result.getContent();
    }
    
    /**
     * 导出日志
     */
    public Resource exportLogs(String traceId, String format) {
        log.info("导出日志: traceId={}, format={}", traceId, format);
        
        List<LogEntry> logs = getCompleteTraceChain(traceId);
        
        StringBuilder content = new StringBuilder();
        content.append("# 日志导出报告\n");
        content.append("# TraceId: ").append(traceId).append("\n");
        content.append("# 导出时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        content.append("# 总记录数: ").append(logs.size()).append("\n");
        content.append("# ").append("=".repeat(80)).append("\n\n");
        
        for (LogEntry log : logs) {
            content.append(log.getRawLine() != null ? log.getRawLine() : formatLogEntry(log)).append("\n");
        }
        
        return new ByteArrayResource(content.toString().getBytes());
    }
    
    /**
     * 获取日志文件列表
     */
    private List<File> getLogFiles(String[] timeRange) {
        List<File> files = new ArrayList<>();
        
        try {
            Path logPath = Paths.get(logDirectory);
            if (!Files.exists(logPath)) {
                log.warn("日志目录不存在: {}", logDirectory);
                return files;
            }
            
            // 获取所有.log文件
            Files.walk(logPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".log"))
                    .map(Path::toFile)
                    .filter(file -> isFileInTimeRange(file, timeRange))
                    .sorted((a, b) -> Long.compare(b.lastModified(), a.lastModified()))
                    .forEach(files::add);
                    
        } catch (IOException e) {
            log.error("获取日志文件列表失败", e);
        }
        
        return files;
    }
    
    /**
     * 在文件中搜索日志
     */
    private List<LogEntry> searchInFile(File file, LogSearchRequest request) {
        List<LogEntry> entries = new ArrayList<>();
        
        try (BufferedReader reader = Files.newBufferedReader(file.toPath())) {
            String line;
            int lineNumber = 0;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                LogEntry entry = parseLogLine(line, file.getName(), lineNumber);
                
                if (entry != null && matchesSearchCriteria(entry, request)) {
                    entries.add(entry);
                }
            }
            
        } catch (IOException e) {
            log.error("读取日志文件失败: {}", file.getName(), e);
        }
        
        return entries;
    }
    
    /**
     * 解析日志行
     */
    private LogEntry parseLogLine(String line, String fileName, int lineNumber) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }
        
        // 尝试解析结构化日志
        Matcher matcher = LOG_PATTERN.matcher(line);
        if (matcher.matches()) {
            return LogEntry.builder()
                    .timestamp(matcher.group(1))
                    .thread(matcher.group(2))
                    .level(matcher.group(3))
                    .traceId(matcher.group(4))
                    .logger(matcher.group(5))
                    .message(matcher.group(6))
                    .source(determineSource(fileName))
                    .rawLine(line)
                    .fileName(fileName)
                    .lineNumber(lineNumber)
                    .build();
        }
        
        // 如果无法解析，创建简单的日志条目
        return LogEntry.builder()
                .timestamp(extractTimestampFromLine(line))
                .level("INFO")
                .traceId("")
                .logger("unknown")
                .message(line)
                .source(determineSource(fileName))
                .rawLine(line)
                .fileName(fileName)
                .lineNumber(lineNumber)
                .build();
    }
    
    /**
     * 检查日志条目是否匹配搜索条件
     */
    private boolean matchesSearchCriteria(LogEntry entry, LogSearchRequest request) {
        // TraceId匹配
        if (request.getTraceId() != null && !request.getTraceId().trim().isEmpty()) {
            String traceId = entry.getTraceId();
            if (traceId == null || !traceId.toLowerCase().contains(request.getTraceId().toLowerCase())) {
                return false;
            }
        }
        
        // 日志级别匹配
        if (request.getLogLevel() != null && !request.getLogLevel().trim().isEmpty()) {
            if (!request.getLogLevel().equalsIgnoreCase(entry.getLevel())) {
                return false;
            }
        }
        
        // 来源匹配
        if (request.getSource() != null && !request.getSource().trim().isEmpty()) {
            if (!request.getSource().equalsIgnoreCase(entry.getSource())) {
                return false;
            }
        }
        
        // 关键字匹配
        if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
            String keyword = request.getKeyword().toLowerCase();
            String message = entry.getMessage();
            if (message == null || !message.toLowerCase().contains(keyword)) {
                return false;
            }
        }
        
        // 时间范围匹配
        if (request.getTimeRange() != null && request.getTimeRange().length == 2) {
            if (!isInTimeRange(entry.getTimestamp(), request.getTimeRange())) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 分页处理
     */
    private PageResult<LogEntry> paginateResults(List<LogEntry> allEntries, int page, int size) {
        int total = allEntries.size();
        int totalPages = (int) Math.ceil((double) total / size);
        
        int start = (page - 1) * size;
        int end = Math.min(start + size, total);
        
        List<LogEntry> pageData = start < total ? allEntries.subList(start, end) : Collections.emptyList();
        
        return new PageResult<>(pageData, total, totalPages, page, size);
    }
    
    /**
     * 判断文件是否在时间范围内
     */
    private boolean isFileInTimeRange(File file, String[] timeRange) {
        if (timeRange == null || timeRange.length != 2) {
            return true; // 没有时间限制
        }
        
        // 简单实现：检查文件修改时间
        long fileTime = file.lastModified();
        try {
            long startTime = DATE_FORMAT.parse(timeRange[0]).getTime();
            long endTime = DATE_FORMAT.parse(timeRange[1]).getTime();
            return fileTime >= startTime && fileTime <= endTime;
        } catch (ParseException e) {
            log.warn("解析时间范围失败: {}", Arrays.toString(timeRange), e);
            return true;
        }
    }
    
    /**
     * 检查时间戳是否在范围内
     */
    private boolean isInTimeRange(String timestamp, String[] timeRange) {
        if (timestamp == null || timeRange == null || timeRange.length != 2) {
            return true;
        }
        
        try {
            Date logTime = DATE_FORMAT.parse(timestamp);
            Date startTime = DATE_FORMAT.parse(timeRange[0]);
            Date endTime = DATE_FORMAT.parse(timeRange[1]);
            
            return !logTime.before(startTime) && !logTime.after(endTime);
        } catch (ParseException e) {
            log.warn("解析时间失败: timestamp={}, timeRange={}", timestamp, Arrays.toString(timeRange), e);
            return true;
        }
    }
    
    /**
     * 从文件名确定日志来源
     */
    private String determineSource(String fileName) {
        if (fileName.contains("webhook")) {
            return "WEBHOOK";
        } else if (fileName.contains("scheduler")) {
            return "SCHEDULER";
        } else if (fileName.contains("api")) {
            return "API";
        } else {
            return "APPLICATION";
        }
    }
    
    /**
     * 从日志行中提取时间戳
     */
    private String extractTimestampFromLine(String line) {
        // 尝试提取时间戳
        Pattern timestampPattern = Pattern.compile("^(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3})");
        Matcher matcher = timestampPattern.matcher(line);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        // 如果没有找到，返回当前时间
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }
    
    /**
     * 格式化日志条目
     */
    private String formatLogEntry(LogEntry entry) {
        return String.format("%s [%s] %s [%s] %s - %s",
                entry.getTimestamp(),
                entry.getThread() != null ? entry.getThread() : "main",
                entry.getLevel(),
                entry.getTraceId() != null ? entry.getTraceId() : "",
                entry.getLogger(),
                entry.getMessage());
    }
    
    /**
     * 获取最近时间范围
     */
    private String[] getRecentTimeRange() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesterday = now.minusDays(1);
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return new String[]{
                yesterday.format(formatter),
                now.format(formatter)
        };
    }
}
