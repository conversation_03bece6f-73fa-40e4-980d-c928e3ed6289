package com.zoombus.service;

import com.zoombus.entity.ZoomMeeting;
import com.zoombus.entity.ZoomMeeting.MeetingStatus;
import com.zoombus.repository.ZoomMeetingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 会议生命周期管理服务
 * 负责会议状态的安全转换和生命周期管理
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MeetingLifecycleManager {

    private final ZoomMeetingRepository zoomMeetingRepository;
    private final MeetingStatusTransitionValidator statusTransitionValidator;
    private final DistributedLockManager distributedLockManager;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 安全地更新会议状态（通过UUID）
     */
    @Transactional
    public boolean updateMeetingStatusByUuid(String meetingUuid, MeetingStatus newStatus, String reason) {
        return updateMeetingStatusByUuid(meetingUuid, newStatus, reason, null);
    }

    /**
     * 安全地更新会议状态（通过UUID，带额外操作）
     */
    @Transactional
    public boolean updateMeetingStatusByUuid(String meetingUuid, MeetingStatus newStatus, String reason,
                                           Runnable additionalOperation) {
        return distributedLockManager.executeWithMeetingUuidLock(
            meetingUuid,
            Duration.ofSeconds(30),
            () -> doUpdateMeetingStatusByUuid(meetingUuid, newStatus, reason, additionalOperation)
        );
    }

    /**
     * 安全地更新会议状态（通过数据库ID - 已废弃，仅用于向后兼容）
     * @deprecated 使用 updateMeetingStatusByUuid 替代
     */
    @Deprecated
    @Transactional
    public boolean updateMeetingStatus(Long meetingId, MeetingStatus newStatus, String reason) {
        return updateMeetingStatus(meetingId, newStatus, reason, null);
    }

    /**
     * 安全地更新会议状态（通过数据库ID，带额外操作 - 已废弃）
     * @deprecated 使用 updateMeetingStatusByUuid 替代
     */
    @Deprecated
    @Transactional
    public boolean updateMeetingStatus(Long meetingId, MeetingStatus newStatus, String reason,
                                     Runnable additionalOperation) {
        return distributedLockManager.executeWithMeetingStatusLock(
            meetingId,
            Duration.ofSeconds(30),
            () -> doUpdateMeetingStatus(meetingId, newStatus, reason, additionalOperation)
        );
    }

    /**
     * 尝试更新会议状态（通过UUID，不抛异常）
     */
    public boolean tryUpdateMeetingStatusByUuid(String meetingUuid, MeetingStatus newStatus, String reason) {
        try {
            return updateMeetingStatusByUuid(meetingUuid, newStatus, reason);
        } catch (DistributedLockManager.ConcurrentModificationException e) {
            log.warn("会议状态正在被其他进程修改，跳过更新: meetingUuid={}, newStatus={}, reason={}",
                    meetingUuid, newStatus, reason);
            return false;
        } catch (Exception e) {
            log.error("更新会议状态失败: meetingUuid={}, newStatus={}, reason={}",
                    meetingUuid, newStatus, reason, e);
            return false;
        }
    }

    /**
     * 尝试更新会议状态（通过数据库ID，不抛异常 - 已废弃）
     * @deprecated 使用 tryUpdateMeetingStatusByUuid 替代
     */
    @Deprecated
    public boolean tryUpdateMeetingStatus(Long meetingId, MeetingStatus newStatus, String reason) {
        try {
            return updateMeetingStatus(meetingId, newStatus, reason);
        } catch (DistributedLockManager.ConcurrentModificationException e) {
            log.warn("会议状态正在被其他进程修改，跳过更新: meetingId={}, newStatus={}, reason={}",
                    meetingId, newStatus, reason);
            return false;
        } catch (Exception e) {
            log.error("更新会议状态失败: meetingId={}, newStatus={}, reason={}",
                    meetingId, newStatus, reason, e);
            return false;
        }
    }

    /**
     * 执行状态更新的核心逻辑（通过UUID）
     */
    private boolean doUpdateMeetingStatusByUuid(String meetingUuid, MeetingStatus newStatus, String reason,
                                              Runnable additionalOperation) {
        ZoomMeeting meeting = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid)
            .orElseThrow(() -> new ResourceNotFoundException("会议不存在: " + meetingUuid));

        MeetingStatus oldStatus = meeting.getStatus();

        // 如果状态相同，直接返回成功（幂等操作）
        if (oldStatus == newStatus) {
            log.debug("会议状态未变化，跳过更新: meetingUuid={}, status={}", meetingUuid, newStatus);
            return true;
        }

        // 验证状态转换
        statusTransitionValidator.validateTransitionOrThrow(oldStatus, newStatus,
            String.format("meetingUuid=%s, reason=%s", meetingUuid, reason));

        // 记录状态变更
        log.info("会议状态转换: meetingUuid={}, meetingId={}, {} -> {}, reason={}",
                meetingUuid, meeting.getId(), oldStatus, newStatus, reason);

        // 更新状态
        meeting.setStatus(newStatus);
        meeting.setUpdatedAt(LocalDateTime.now());

        // 根据状态设置特定字段
        updateStatusSpecificFields(meeting, newStatus);

        // 保存到数据库
        zoomMeetingRepository.save(meeting);

        // 执行额外操作
        if (additionalOperation != null) {
            try {
                additionalOperation.run();
            } catch (Exception e) {
                log.error("执行额外操作失败: meetingUuid={}, newStatus={}", meetingUuid, newStatus, e);
                // 不抛异常，避免影响主流程
            }
        }

        // 发布状态变更事件
        publishStatusChangeEvent(meeting, oldStatus, newStatus, reason);

        return true;
    }

    /**
     * 执行状态更新的核心逻辑（通过数据库ID - 已废弃）
     * @deprecated 使用 doUpdateMeetingStatusByUuid 替代
     */
    @Deprecated
    private boolean doUpdateMeetingStatus(Long meetingId, MeetingStatus newStatus, String reason,
                                        Runnable additionalOperation) {
        ZoomMeeting meeting = zoomMeetingRepository.findById(meetingId)
            .orElseThrow(() -> new ResourceNotFoundException("会议不存在: " + meetingId));

        MeetingStatus oldStatus = meeting.getStatus();

        // 如果状态相同，直接返回成功（幂等操作）
        if (oldStatus == newStatus) {
            log.debug("会议状态未变化，跳过更新: meetingId={}, status={}", meetingId, newStatus);
            return true;
        }

        // 验证状态转换
        statusTransitionValidator.validateTransitionOrThrow(oldStatus, newStatus,
            String.format("meetingId=%d, reason=%s", meetingId, reason));

        // 记录状态变更
        log.info("会议状态转换: meetingId={}, {} -> {}, reason={}",
                meetingId, oldStatus, newStatus, reason);

        // 更新状态
        meeting.setStatus(newStatus);
        meeting.setUpdatedAt(LocalDateTime.now());

        // 根据状态设置特定字段
        updateStatusSpecificFields(meeting, newStatus);

        // 保存到数据库
        zoomMeetingRepository.save(meeting);

        // 执行额外操作
        if (additionalOperation != null) {
            try {
                additionalOperation.run();
            } catch (Exception e) {
                log.error("执行额外操作失败: meetingId={}, newStatus={}", meetingId, newStatus, e);
                // 不抛异常，避免影响主流程
            }
        }

        // 发布状态变更事件
        publishStatusChangeEvent(meeting, oldStatus, newStatus, reason);

        return true;
    }

    /**
     * 根据状态更新特定字段
     */
    private void updateStatusSpecificFields(ZoomMeeting meeting, MeetingStatus newStatus) {
        LocalDateTime now = LocalDateTime.now();
        
        switch (newStatus) {
            case STARTED:
                if (meeting.getStartTime() == null) {
                    meeting.setStartTime(now);
                }
                break;
                
            case ENDED:
                if (meeting.getEndTime() == null) {
                    meeting.setEndTime(now);
                }
                // 计算会议时长
                if (meeting.getStartTime() != null) {
                    Duration duration = Duration.between(meeting.getStartTime(), now);
                    meeting.setDurationMinutes((int) duration.toMinutes());
                }
                break;
                
            case SETTLED:
                meeting.setIsSettled(true);
                break;
                
            default:
                // 其他状态不需要特殊处理
                break;
        }
    }

    /**
     * 发布状态变更事件
     */
    private void publishStatusChangeEvent(ZoomMeeting meeting, MeetingStatus oldStatus, 
                                        MeetingStatus newStatus, String reason) {
        try {
            MeetingStatusChangedEvent event = new MeetingStatusChangedEvent(
                meeting.getId(),
                meeting.getZoomMeetingId(),
                meeting.getZoomMeetingUuid(),
                oldStatus,
                newStatus,
                reason,
                LocalDateTime.now()
            );
            
            eventPublisher.publishEvent(event);
            log.debug("发布状态变更事件: meetingId={}, {} -> {}", 
                    meeting.getId(), oldStatus, newStatus);
                    
        } catch (Exception e) {
            log.error("发布状态变更事件失败: meetingId={}", meeting.getId(), e);
            // 不抛异常，避免影响主流程
        }
    }

    /**
     * 批量更新会议状态
     */
    @Transactional
    public int batchUpdateMeetingStatus(Iterable<Long> meetingIds, MeetingStatus newStatus, String reason) {
        int successCount = 0;
        
        for (Long meetingId : meetingIds) {
            try {
                if (updateMeetingStatus(meetingId, newStatus, reason)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量更新会议状态失败: meetingId={}, newStatus={}", meetingId, newStatus, e);
            }
        }
        
        log.info("批量更新会议状态完成: 成功={}, 状态={}, 原因={}", successCount, newStatus, reason);
        return successCount;
    }

    /**
     * 获取会议当前状态（带锁保护）
     */
    public MeetingStatus getMeetingStatusSafely(Long meetingId) {
        return distributedLockManager.tryExecuteWithMeetingStatusLock(
            meetingId,
            Duration.ofSeconds(5),
            () -> {
                ZoomMeeting meeting = zoomMeetingRepository.findById(meetingId)
                    .orElseThrow(() -> new ResourceNotFoundException("会议不存在: " + meetingId));
                return meeting.getStatus();
            },
            MeetingStatus.ERROR // 默认返回错误状态
        );
    }

    /**
     * 检查状态转换是否合法
     */
    public boolean canTransitionTo(Long meetingId, MeetingStatus targetStatus) {
        try {
            ZoomMeeting meeting = zoomMeetingRepository.findById(meetingId)
                .orElse(null);
            if (meeting == null) {
                return false;
            }
            
            return statusTransitionValidator.isValidTransition(meeting.getStatus(), targetStatus);
        } catch (Exception e) {
            log.error("检查状态转换失败: meetingId={}, targetStatus={}", meetingId, targetStatus, e);
            return false;
        }
    }

    /**
     * 资源未找到异常
     */
    public static class ResourceNotFoundException extends RuntimeException {
        public ResourceNotFoundException(String message) {
            super(message);
        }
    }
}
