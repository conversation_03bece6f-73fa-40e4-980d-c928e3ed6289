package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.dto.ZoomMeetingParticipantsReportDto;
import com.zoombus.dto.ZoomMeetingReportDto;
import com.zoombus.entity.MeetingParticipant;
import com.zoombus.entity.MeetingReport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 会议报告数据转换服务
 * 负责将Zoom API返回的数据转换为系统实体
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MeetingReportDataConverter {
    
    private final ObjectMapper objectMapper;
    
    /**
     * 将Zoom API的JsonNode转换为MeetingReport实体
     */
    public MeetingReport convertToMeetingReport(JsonNode reportData, String zoomMeetingUuid, String zoomMeetingId) {
        try {
            log.debug("转换会议报告数据: zoomMeetingUuid={}", zoomMeetingUuid);
            
            // 将JsonNode转换为DTO
            ZoomMeetingReportDto dto = objectMapper.treeToValue(reportData, ZoomMeetingReportDto.class);
            
            // 创建MeetingReport实体
            MeetingReport report = new MeetingReport();
            report.setZoomMeetingUuid(zoomMeetingUuid);
            report.setZoomMeetingId(zoomMeetingId);
            
            // 基本信息
            report.setTopic(dto.getTopic());
            report.setStartTime(dto.getStartTimeAsLocalDateTime());
            report.setEndTime(dto.getEndTimeAsLocalDateTime());
            report.setDurationMinutes(dto.getDurationMinutes());
            report.setTotalParticipants(dto.getTotalParticipants());
            report.setUniqueParticipants(dto.getTotalParticipants()); // 初始值，后续会更新
            
            // 功能特性
            report.setHasRecording(dto.getHasRecording() != null ? dto.getHasRecording() : false);
            report.setHasPstn(dto.getHasPstn() != null ? dto.getHasPstn() : false);
            report.setHasVoip(dto.getHasVoip() != null ? dto.getHasVoip() : false);
            report.setHas3rdPartyAudio(dto.getHas3rdPartyAudio() != null ? dto.getHas3rdPartyAudio() : false);
            report.setHasVideo(dto.getHasVideo() != null ? dto.getHasVideo() : false);
            report.setHasScreenShare(dto.getHasScreenShare() != null ? dto.getHasScreenShare() : false);
            
            // 保存原始数据
            report.setReportData(objectMapper.writeValueAsString(reportData));
            
            // 设置获取状态
            report.setFetchStatus(MeetingReport.FetchStatus.SUCCESS);
            report.setCreatedAt(LocalDateTime.now());
            report.setUpdatedAt(LocalDateTime.now());
            
            log.debug("会议报告转换完成: topic={}, duration={}", report.getTopic(), report.getDurationMinutes());
            return report;
            
        } catch (Exception e) {
            log.error("转换会议报告数据失败: zoomMeetingUuid={}", zoomMeetingUuid, e);
            throw new RuntimeException("转换会议报告数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 将Zoom API的JsonNode转换为MeetingParticipant实体列表
     */
    public List<MeetingParticipant> convertToMeetingParticipants(JsonNode participantsData, Long meetingReportId) {
        try {
            log.debug("转换参会者数据: meetingReportId={}", meetingReportId);
            
            List<MeetingParticipant> participants = new ArrayList<>();
            
            // 将JsonNode转换为DTO
            ZoomMeetingParticipantsReportDto dto = objectMapper.treeToValue(participantsData, ZoomMeetingParticipantsReportDto.class);
            
            if (dto.getParticipants() != null) {
                for (ZoomMeetingParticipantsReportDto.Participant participantDto : dto.getParticipants()) {
                    MeetingParticipant participant = convertSingleParticipant(participantDto, meetingReportId);
                    participants.add(participant);
                }
            }
            
            log.debug("参会者数据转换完成: count={}", participants.size());
            return participants;
            
        } catch (Exception e) {
            log.error("转换参会者数据失败: meetingReportId={}", meetingReportId, e);
            throw new RuntimeException("转换参会者数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 转换单个参会者数据
     */
    private MeetingParticipant convertSingleParticipant(ZoomMeetingParticipantsReportDto.Participant dto, Long meetingReportId) {
        try {
            MeetingParticipant participant = new MeetingParticipant();
            
            // 基本信息
            participant.setMeetingReportId(meetingReportId);
            participant.setParticipantUuid(dto.getId());
            participant.setParticipantName(dto.getName());
            participant.setParticipantEmail(dto.getUserEmail());
            participant.setParticipantUserId(dto.getUserId());
            participant.setRegistrantId(dto.getRegistrantId());
            
            // 时间信息
            participant.setJoinTime(dto.getJoinTimeAsLocalDateTime());
            participant.setLeaveTime(dto.getLeaveTimeAsLocalDateTime());
            participant.setDurationMinutes(dto.getDurationMinutes());
            
            // 用户类型和状态
            participant.setUserType(dto.getUserTypeEnum());
            participant.setStatus(dto.getStatus());
            participant.setRecordingConsent(dto.getRecordingConsent());
            participant.setInWaitingRoom(dto.getInWaitingRoom());
            
            // 连接信息
            participant.setHasPstn(dto.getSipPhone() != null ? dto.getSipPhone() : false);
            participant.setHasVoip(dto.getAudioType() != null && dto.getAudioType().toLowerCase().contains("voip"));
            participant.setHas3rdPartyAudio(dto.getAudioType() != null && dto.getAudioType().toLowerCase().contains("3rd"));
            participant.setHasVideo(dto.getCamera() != null && !dto.getCamera().trim().isEmpty());
            participant.setHasScreenShare(
                (dto.getShareApplication() != null && dto.getShareApplication()) ||
                (dto.getShareDesktop() != null && dto.getShareDesktop()) ||
                (dto.getShareWhiteboard() != null && dto.getShareWhiteboard())
            );
            
            // 网络和设备信息
            participant.setIpAddress(dto.getIpAddress());
            participant.setLocation(dto.getLocationInfo());
            participant.setNetworkType(dto.getNetworkType());
            participant.setMicrophone(dto.getMicrophone());
            participant.setSpeaker(dto.getSpeaker());
            participant.setCamera(dto.getCamera());
            participant.setDataCenter(dto.getDataCenter());
            participant.setConnectionType(dto.getConnectionType());
            
            // 其他信息
            participant.setJoinCount(dto.getJoinCount() != null ? dto.getJoinCount() : 1);
            
            // 保存原始数据
            participant.setParticipantData(objectMapper.writeValueAsString(dto));
            participant.setCreatedAt(LocalDateTime.now());
            
            return participant;
            
        } catch (Exception e) {
            log.error("转换单个参会者数据失败: participantId={}", dto.getId(), e);
            throw new RuntimeException("转换单个参会者数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 更新会议报告的参会者统计信息
     */
    public void updateReportParticipantStatistics(MeetingReport report, List<MeetingParticipant> participants) {
        if (participants == null || participants.isEmpty()) {
            return;
        }
        
        try {
            log.debug("更新会议报告参会者统计: reportId={}, participantCount={}", 
                     report.getId(), participants.size());
            
            // 总参会人次
            report.setTotalParticipants(participants.size());
            
            // 唯一参会人数（按邮箱去重）
            long uniqueCount = participants.stream()
                .filter(p -> p.getParticipantEmail() != null && !p.getParticipantEmail().trim().isEmpty())
                .map(MeetingParticipant::getParticipantEmail)
                .distinct()
                .count();
            
            // 如果没有邮箱信息，则按姓名去重
            if (uniqueCount == 0) {
                uniqueCount = participants.stream()
                    .filter(p -> p.getParticipantName() != null && !p.getParticipantName().trim().isEmpty())
                    .map(MeetingParticipant::getParticipantName)
                    .distinct()
                    .count();
            }
            
            // 如果还是0，则使用总人次
            if (uniqueCount == 0) {
                uniqueCount = participants.size();
            }
            
            report.setUniqueParticipants((int) uniqueCount);
            
            // 更新功能使用统计
            boolean hasVideo = participants.stream().anyMatch(p -> Boolean.TRUE.equals(p.getHasVideo()));
            boolean hasScreenShare = participants.stream().anyMatch(p -> Boolean.TRUE.equals(p.getHasScreenShare()));
            boolean hasPstn = participants.stream().anyMatch(p -> Boolean.TRUE.equals(p.getHasPstn()));
            boolean hasVoip = participants.stream().anyMatch(p -> Boolean.TRUE.equals(p.getHasVoip()));
            boolean has3rdPartyAudio = participants.stream().anyMatch(p -> Boolean.TRUE.equals(p.getHas3rdPartyAudio()));
            
            // 只有在原始报告数据中没有这些信息时才更新
            if (report.getHasVideo() == null || !report.getHasVideo()) {
                report.setHasVideo(hasVideo);
            }
            if (report.getHasScreenShare() == null || !report.getHasScreenShare()) {
                report.setHasScreenShare(hasScreenShare);
            }
            if (report.getHasPstn() == null || !report.getHasPstn()) {
                report.setHasPstn(hasPstn);
            }
            if (report.getHasVoip() == null || !report.getHasVoip()) {
                report.setHasVoip(hasVoip);
            }
            if (report.getHas3rdPartyAudio() == null || !report.getHas3rdPartyAudio()) {
                report.setHas3rdPartyAudio(has3rdPartyAudio);
            }
            
            report.setUpdatedAt(LocalDateTime.now());
            
            log.debug("会议报告统计更新完成: totalParticipants={}, uniqueParticipants={}", 
                     report.getTotalParticipants(), report.getUniqueParticipants());
            
        } catch (Exception e) {
            log.error("更新会议报告参会者统计失败: reportId={}", report.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }
    
    /**
     * 验证转换后的数据
     */
    public boolean validateMeetingReport(MeetingReport report) {
        if (report == null) {
            log.warn("会议报告为空");
            return false;
        }
        
        if (report.getZoomMeetingUuid() == null || report.getZoomMeetingUuid().trim().isEmpty()) {
            log.warn("会议UUID为空");
            return false;
        }
        
        if (report.getZoomMeetingId() == null || report.getZoomMeetingId().trim().isEmpty()) {
            log.warn("会议ID为空");
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证参会者数据
     */
    public boolean validateMeetingParticipants(List<MeetingParticipant> participants) {
        if (participants == null) {
            log.warn("参会者列表为空");
            return false;
        }
        
        for (MeetingParticipant participant : participants) {
            if (participant.getMeetingReportId() == null) {
                log.warn("参会者的会议报告ID为空");
                return false;
            }
        }
        
        return true;
    }
}
