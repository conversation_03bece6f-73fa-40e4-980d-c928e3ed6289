package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.entity.MeetingParticipant;
import com.zoombus.entity.MeetingReport;
import com.zoombus.entity.MeetingReportTask;
import com.zoombus.repository.MeetingReportRepository;
import com.zoombus.repository.MeetingParticipantRepository;
import com.zoombus.repository.MeetingReportTaskRepository;
import com.zoombus.dto.ZoomApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 会议报告获取服务
 * 负责从Zoom API获取会议报告并保存到数据库
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MeetingReportFetchService {
    
    private final ZoomApiService zoomApiService;
    private final MeetingReportDataConverter dataConverter;
    private final MeetingReportRepository meetingReportRepository;
    private final MeetingParticipantRepository meetingParticipantRepository;
    private final MeetingReportTaskRepository meetingReportTaskRepository;
    
    /**
     * 获取并保存会议报告（带重试机制）
     */
    @Retryable(
        value = {Exception.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 5000, multiplier = 2)
    )
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MeetingReport fetchAndSaveMeetingReport(String zoomMeetingUuid, String zoomMeetingId) {
        log.info("开始获取会议报告: zoomMeetingUuid={}, zoomMeetingId={}", zoomMeetingUuid, zoomMeetingId);
        
        try {
            // 检查是否已存在报告
            Optional<MeetingReport> existingReport = meetingReportRepository.findByZoomMeetingUuid(zoomMeetingUuid);
            if (existingReport.isPresent() && existingReport.get().getFetchStatus() == MeetingReport.FetchStatus.SUCCESS) {
                log.info("会议报告已存在: zoomMeetingUuid={}", zoomMeetingUuid);
                return existingReport.get();
            }

            // 创建或更新报告记录
            MeetingReport report = existingReport.orElse(new MeetingReport());
            report.setZoomMeetingUuid(zoomMeetingUuid);
            report.setZoomMeetingId(zoomMeetingId);
            report.setFetchStatus(MeetingReport.FetchStatus.PENDING);
            report.setLastFetchAttempt(LocalDateTime.now());

            if (report.getId() == null) {
                report.setCreatedAt(LocalDateTime.now());
            }
            report.setUpdatedAt(LocalDateTime.now());

            try {
                report = meetingReportRepository.save(report);
            } catch (Exception e) {
                // 处理唯一约束冲突，重新查询已存在的报告
                if (e.getMessage() != null && e.getMessage().contains("uk_zoom_meeting_uuid")) {
                    log.warn("检测到唯一约束冲突，重新查询已存在的报告: zoomMeetingUuid={}", zoomMeetingUuid);
                    Optional<MeetingReport> conflictReport = meetingReportRepository.findByZoomMeetingUuid(zoomMeetingUuid);
                    if (conflictReport.isPresent()) {
                        return conflictReport.get();
                    }
                }
                throw e;
            }
            
            // 获取会议基本报告
            MeetingReport finalReport = fetchMeetingBasicReport(report);
            
            // 获取参会者报告
            List<MeetingParticipant> participants = fetchMeetingParticipantsReport(finalReport);
            
            // 更新统计信息
            dataConverter.updateReportParticipantStatistics(finalReport, participants);
            
            // 保存最终报告
            finalReport.markFetchSuccess();
            finalReport = meetingReportRepository.save(finalReport);
            
            log.info("会议报告获取成功: zoomMeetingUuid={}, participantCount={}", 
                    zoomMeetingUuid, participants.size());
            
            return finalReport;
            
        } catch (Exception e) {
            log.error("获取会议报告失败: zoomMeetingUuid={}", zoomMeetingUuid, e);

            // 更新失败状态
            Optional<MeetingReport> reportOpt = meetingReportRepository.findByZoomMeetingUuid(zoomMeetingUuid);
            if (reportOpt.isPresent()) {
                MeetingReport report = reportOpt.get();

                String errorMessage = e.getMessage();
                if (errorMessage == null) {
                    errorMessage = "未知错误: " + e.getClass().getSimpleName();
                }

                // 根据错误类型提供更友好的错误信息
                if (errorMessage.contains("404") || errorMessage.contains("not found")) {
                    errorMessage = "会议报告尚未生成，请稍后重试";
                } else if (errorMessage.contains("401") || errorMessage.contains("unauthorized")) {
                    errorMessage = "Zoom API授权失败，请检查配置";
                } else if (errorMessage.contains("timeout")) {
                    errorMessage = "获取报告超时，请稍后重试";
                }

                report.markFetchFailed(errorMessage);
                meetingReportRepository.save(report);
            }

            throw new MeetingReportFetchException("获取会议报告失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取会议基本报告
     */
    private MeetingReport fetchMeetingBasicReport(MeetingReport report) {
        log.debug("获取会议基本报告: zoomMeetingUuid={}", report.getZoomMeetingUuid());
        
        try {
            ZoomApiResponse<JsonNode> response = zoomApiService.getMeetingReport(report.getZoomMeetingUuid());

            if (!response.isSuccess()) {
                throw new MeetingReportFetchException("获取会议基本报告失败: " + response.getMessage());
            }
            
            JsonNode reportData = response.getData();
            if (reportData == null) {
                throw new MeetingReportFetchException("会议基本报告数据为空");
            }
            
            // 转换数据
            MeetingReport convertedReport = dataConverter.convertToMeetingReport(
                reportData, report.getZoomMeetingUuid(), report.getZoomMeetingId());
            
            // 保留原有的ID和创建时间
            convertedReport.setId(report.getId());
            convertedReport.setCreatedAt(report.getCreatedAt());
            
            // 验证数据
            if (!dataConverter.validateMeetingReport(convertedReport)) {
                throw new MeetingReportFetchException("会议基本报告数据验证失败");
            }
            
            return convertedReport;
            
        } catch (Exception e) {
            log.error("获取会议基本报告异常: zoomMeetingId={}", report.getZoomMeetingId(), e);
            throw new MeetingReportFetchException("获取会议基本报告异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取会议参会者报告
     */
    private List<MeetingParticipant> fetchMeetingParticipantsReport(MeetingReport report) {
        log.debug("获取会议参会者报告: zoomMeetingUuid={}", report.getZoomMeetingUuid());
        
        try {
            ZoomApiResponse<JsonNode> response = zoomApiService.getCompleteMeetingParticipantsReport(report.getZoomMeetingUuid());

            if (!response.isSuccess()) {
                throw new MeetingReportFetchException("获取会议参会者报告失败: " + response.getMessage());
            }

            JsonNode participantsData = response.getData();
            if (participantsData == null) {
                log.warn("会议参会者报告数据为空: zoomMeetingUuid={}", report.getZoomMeetingUuid());
                return List.of(); // 返回空列表而不是抛出异常
            }
            
            // 转换数据
            List<MeetingParticipant> participants = dataConverter.convertToMeetingParticipants(
                participantsData, report.getId());
            
            // 验证数据
            if (!dataConverter.validateMeetingParticipants(participants)) {
                throw new MeetingReportFetchException("会议参会者报告数据验证失败");
            }
            
            // 删除旧的参会者记录（如果存在）
            meetingParticipantRepository.deleteAll(
                meetingParticipantRepository.findByMeetingReportId(report.getId()));
            
            // 保存新的参会者记录
            if (!participants.isEmpty()) {
                participants = meetingParticipantRepository.saveAll(participants);
            }
            
            log.debug("会议参会者报告获取成功: count={}", participants.size());
            return participants;
            
        } catch (Exception e) {
            log.error("获取会议参会者报告异常: zoomMeetingId={}", report.getZoomMeetingId(), e);
            throw new MeetingReportFetchException("获取会议参会者报告异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 处理报告获取任务
     */
    @Transactional
    public void processReportTask(MeetingReportTask task) {
        log.info("处理会议报告获取任务: taskId={}, zoomMeetingUuid={}", 
                task.getId(), task.getZoomMeetingUuid());
        
        try {
            // 更新任务状态为处理中
            task.startExecution();
            meetingReportTaskRepository.save(task);
            
            // 获取会议报告
            MeetingReport report = fetchAndSaveMeetingReport(
                task.getZoomMeetingUuid(), task.getZoomMeetingId());
            
            // 标记任务成功
            task.markSuccess();
            meetingReportTaskRepository.save(task);
            
            log.info("会议报告获取任务处理成功: taskId={}, reportId={}", task.getId(), report.getId());
            
        } catch (Exception e) {
            log.error("处理会议报告获取任务失败: taskId={}", task.getId(), e);
            
            // 标记任务失败
            task.markFailed(e.getMessage());
            meetingReportTaskRepository.save(task);
            
            // 如果需要重试，重置任务状态
            if (task.needsRetry()) {
                task.resetForRetry();
                meetingReportTaskRepository.save(task);
                log.info("会议报告获取任务已安排重试: taskId={}, retryCount={}, scheduledTime={}", 
                        task.getId(), task.getRetryCount(), task.getScheduledTime());
            }
        }
    }
    
    /**
     * 批量处理待处理的任务
     */
    @Transactional
    public void processPendingTasks() {
        log.debug("开始处理待处理的会议报告获取任务");
        
        List<MeetingReportTask> pendingTasks = meetingReportTaskRepository.findPendingTasks(LocalDateTime.now());
        
        log.info("找到待处理任务数量: {}", pendingTasks.size());
        
        for (MeetingReportTask task : pendingTasks) {
            try {
                processReportTask(task);
            } catch (Exception e) {
                log.error("处理任务异常: taskId={}", task.getId(), e);
                // 继续处理下一个任务
            }
        }
        
        log.debug("待处理任务处理完成");
    }
    
    /**
     * 处理需要重试的任务
     */
    @Transactional
    public void processRetryTasks() {
        log.debug("开始处理需要重试的会议报告获取任务");
        
        List<MeetingReportTask> retryTasks = meetingReportTaskRepository.findTasksNeedingRetry();
        
        log.info("找到需要重试任务数量: {}", retryTasks.size());
        
        for (MeetingReportTask task : retryTasks) {
            try {
                // 重置任务状态
                task.resetForRetry();
                meetingReportTaskRepository.save(task);
                
                log.info("任务已重置为重试状态: taskId={}, scheduledTime={}", 
                        task.getId(), task.getScheduledTime());
            } catch (Exception e) {
                log.error("重置重试任务异常: taskId={}", task.getId(), e);
            }
        }
        
        log.debug("重试任务处理完成");
    }
    
    /**
     * 清理超时的处理中任务
     */
    @Transactional
    public void cleanupTimeoutTasks() {
        log.debug("开始清理超时的处理中任务");
        
        // 超过30分钟的处理中任务视为超时
        LocalDateTime timeoutBefore = LocalDateTime.now().minusMinutes(30);
        
        int resetCount = meetingReportTaskRepository.resetTimeoutTasks(timeoutBefore, LocalDateTime.now());
        
        if (resetCount > 0) {
            log.info("已重置超时任务数量: {}", resetCount);
        }
        
        log.debug("超时任务清理完成");
    }
    
    /**
     * 会议报告获取异常
     */
    public static class MeetingReportFetchException extends RuntimeException {
        public MeetingReportFetchException(String message) {
            super(message);
        }
        
        public MeetingReportFetchException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
