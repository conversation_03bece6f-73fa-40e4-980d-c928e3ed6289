package com.zoombus.service;

import com.zoombus.entity.MeetingReport;
import com.zoombus.repository.MeetingReportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 会议报告管理服务
 * 提供全量会议报告的管理和统计功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MeetingReportManagementService {

    private final MeetingReportRepository meetingReportRepository;

    /**
     * 全量查询会议报告（支持多维度筛选）
     */
    public Page<MeetingReport> getAllReportsWithFilters(Long zoomAuthId,
                                                       MeetingReport.MeetingType meetingType,
                                                       String pmiNumber,
                                                       String hostUserId,
                                                       LocalDateTime startTime,
                                                       LocalDateTime endTime,
                                                       String keyword,
                                                       MeetingReport.FetchStatus fetchStatus,
                                                       Pageable pageable) {
        log.debug("全量查询会议报告: zoomAuthId={}, meetingType={}, pmiNumber={}, hostUserId={}, " +
                 "startTime={}, endTime={}, keyword={}, fetchStatus={}, page={}",
                 zoomAuthId, meetingType, pmiNumber, hostUserId, startTime, endTime, 
                 keyword, fetchStatus, pageable.getPageNumber());

        return meetingReportRepository.findAllReportsWithFilters(
                zoomAuthId, meetingType, pmiNumber, hostUserId,
                startTime, endTime, keyword, fetchStatus, pageable);
    }

    /**
     * 获取全量报告统计概览
     */
    public Map<String, Object> getOverallStatistics() {
        log.debug("获取全量报告统计概览");
        
        Object[] stats = meetingReportRepository.getOverallStatistics();
        
        return Map.of(
            "totalReports", stats[0] != null ? stats[0] : 0,
            "totalAccounts", stats[1] != null ? stats[1] : 0,
            "totalPmis", stats[2] != null ? stats[2] : 0,
            "totalHosts", stats[3] != null ? stats[3] : 0,
            "totalDuration", stats[4] != null ? stats[4] : 0,
            "totalParticipants", stats[5] != null ? stats[5] : 0
        );
    }

    /**
     * 获取指定时间范围的统计信息
     */
    public Map<String, Object> getStatisticsInTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("获取时间范围统计: startTime={}, endTime={}", startTime, endTime);
        
        Object[] stats = meetingReportRepository.getStatisticsSummary(startTime, endTime);
        
        return Map.of(
            "totalReports", stats[0] != null ? stats[0] : 0,
            "totalParticipants", stats[1] != null ? stats[1] : 0,
            "totalDuration", stats[2] != null ? stats[2] : 0,
            "averageDuration", stats[3] != null ? stats[3] : 0.0,
            "recordingCount", stats[4] != null ? stats[4] : 0,
            "videoCount", stats[5] != null ? stats[5] : 0,
            "screenShareCount", stats[6] != null ? stats[6] : 0
        );
    }

    /**
     * 获取会议类型分布统计
     */
    public List<Map<String, Object>> getMeetingTypeDistribution() {
        log.debug("获取会议类型分布统计");
        
        List<Object[]> distribution = meetingReportRepository.getMeetingTypeDistribution();
        
        return distribution.stream()
                .map(row -> Map.of(
                    "meetingType", row[0] != null ? row[0].toString() : "UNKNOWN",
                    "count", row[1] != null ? row[1] : 0
                ))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取最活跃的PMI列表
     */
    public List<Map<String, Object>> getMostActivePmis(int limit) {
        log.debug("获取最活跃PMI列表: limit={}", limit);
        
        Pageable pageable = org.springframework.data.domain.PageRequest.of(0, limit);
        List<Object[]> activePmis = meetingReportRepository.getMostActivePmis(pageable);
        
        return activePmis.stream()
                .map(row -> Map.of(
                    "pmiNumber", row[0] != null ? row[0].toString() : "",
                    "meetingCount", row[1] != null ? row[1] : 0
                ))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取最活跃的主持人列表
     */
    public List<Map<String, Object>> getMostActiveHosts(int limit) {
        log.debug("获取最活跃主持人列表: limit={}", limit);
        
        Pageable pageable = org.springframework.data.domain.PageRequest.of(0, limit);
        List<Object[]> activeHosts = meetingReportRepository.getMostActiveHosts(pageable);
        
        return activeHosts.stream()
                .map(row -> Map.of(
                    "hostUserId", row[0] != null ? row[0].toString() : "",
                    "meetingCount", row[1] != null ? row[1] : 0
                ))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据Zoom主账号ID获取会议报告
     */
    public Page<MeetingReport> getReportsByZoomAuth(Long zoomAuthId, Pageable pageable) {
        log.debug("根据Zoom主账号获取会议报告: zoomAuthId={}, page={}", zoomAuthId, pageable.getPageNumber());
        return meetingReportRepository.findByZoomAuthId(zoomAuthId, pageable);
    }

    /**
     * 根据会议类型获取会议报告
     */
    public Page<MeetingReport> getReportsByMeetingType(MeetingReport.MeetingType meetingType, Pageable pageable) {
        log.debug("根据会议类型获取会议报告: meetingType={}, page={}", meetingType, pageable.getPageNumber());
        return meetingReportRepository.findByMeetingType(meetingType, pageable);
    }

    /**
     * 根据主持人用户ID获取会议报告
     */
    public Page<MeetingReport> getReportsByHostUserId(String hostUserId, Pageable pageable) {
        log.debug("根据主持人获取会议报告: hostUserId={}, page={}", hostUserId, pageable.getPageNumber());
        return meetingReportRepository.findByHostUserId(hostUserId, pageable);
    }

    /**
     * 获取指定Zoom主账号的统计信息
     */
    public Map<String, Object> getZoomAuthStatistics(Long zoomAuthId) {
        log.debug("获取Zoom主账号统计: zoomAuthId={}", zoomAuthId);
        
        Object[] stats = meetingReportRepository.getZoomAuthStatistics(zoomAuthId);
        
        return Map.of(
            "zoomAuthId", zoomAuthId,
            "totalMeetings", stats[0] != null ? stats[0] : 0,
            "totalDuration", stats[1] != null ? stats[1] : 0,
            "totalParticipants", stats[2] != null ? stats[2] : 0,
            "uniquePmis", stats[3] != null ? stats[3] : 0
        );
    }

    /**
     * 批量删除会议报告
     */
    @Transactional
    public void batchDeleteReports(List<Long> reportIds) {
        log.info("批量删除会议报告: count={}", reportIds.size());
        
        try {
            for (Long reportId : reportIds) {
                Optional<MeetingReport> reportOpt = meetingReportRepository.findById(reportId);
                if (reportOpt.isPresent()) {
                    meetingReportRepository.deleteById(reportId);
                    log.debug("删除会议报告: id={}", reportId);
                } else {
                    log.warn("会议报告不存在: id={}", reportId);
                }
            }
            
            log.info("批量删除会议报告完成: count={}", reportIds.size());
            
        } catch (Exception e) {
            log.error("批量删除会议报告失败", e);
            throw new RuntimeException("批量删除会议报告失败", e);
        }
    }

    /**
     * 批量导出会议报告数据
     */
    public List<MeetingReport> batchExportReports(List<Long> reportIds) {
        log.info("批量导出会议报告: count={}", reportIds.size());
        
        try {
            List<MeetingReport> reports = meetingReportRepository.findAllById(reportIds);
            log.info("批量导出会议报告完成: found={}, requested={}", reports.size(), reportIds.size());
            return reports;
            
        } catch (Exception e) {
            log.error("批量导出会议报告失败", e);
            throw new RuntimeException("批量导出会议报告失败", e);
        }
    }

    /**
     * 清理指定时间之前的会议报告
     */
    @Transactional
    public void cleanupOldReports(LocalDateTime beforeTime) {
        log.info("清理旧会议报告: beforeTime={}", beforeTime);
        
        try {
            meetingReportRepository.deleteReportsCreatedBefore(beforeTime);
            log.info("清理旧会议报告完成: beforeTime={}", beforeTime);
            
        } catch (Exception e) {
            log.error("清理旧会议报告失败", e);
            throw new RuntimeException("清理旧会议报告失败", e);
        }
    }

    /**
     * 获取报告状态分布统计
     */
    public Map<String, Long> getReportStatusDistribution() {
        log.debug("获取报告状态分布统计");
        
        Map<String, Long> distribution = new java.util.HashMap<>();
        
        for (MeetingReport.FetchStatus status : MeetingReport.FetchStatus.values()) {
            long count = meetingReportRepository.countByFetchStatus(status);
            distribution.put(status.name(), count);
        }
        
        return distribution;
    }

    /**
     * 获取最近的成功报告
     */
    public List<MeetingReport> getRecentSuccessReports(int limit) {
        log.debug("获取最近成功报告: limit={}", limit);
        
        Pageable pageable = org.springframework.data.domain.PageRequest.of(0, limit);
        return meetingReportRepository.findRecentSuccessReports(pageable);
    }

    /**
     * 搜索会议报告（根据主题关键词）
     */
    public Page<MeetingReport> searchReportsByTopic(String keyword, Pageable pageable) {
        log.debug("搜索会议报告: keyword={}, page={}", keyword, pageable.getPageNumber());
        return meetingReportRepository.findByTopicContaining(keyword, pageable);
    }

    /**
     * 获取有录制的会议报告
     */
    public Page<MeetingReport> getReportsWithRecording(Pageable pageable) {
        log.debug("获取有录制的会议报告: page={}", pageable.getPageNumber());
        return meetingReportRepository.findReportsWithRecording(pageable);
    }

    /**
     * 获取系统整体健康状态
     */
    public Map<String, Object> getSystemHealthStatus() {
        log.debug("获取系统健康状态");
        
        long totalReports = meetingReportRepository.count();
        long successReports = meetingReportRepository.countByFetchStatus(MeetingReport.FetchStatus.SUCCESS);
        long failedReports = meetingReportRepository.countByFetchStatus(MeetingReport.FetchStatus.FAILED);
        long pendingReports = meetingReportRepository.countByFetchStatus(MeetingReport.FetchStatus.PENDING);
        
        double successRate = totalReports > 0 ? (double) successReports / totalReports * 100 : 0.0;
        
        return Map.of(
            "totalReports", totalReports,
            "successReports", successReports,
            "failedReports", failedReports,
            "pendingReports", pendingReports,
            "successRate", Math.round(successRate * 100.0) / 100.0
        );
    }
}
