package com.zoombus.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 会议报告定时任务服务
 * 负责定时处理会议报告获取任务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "zoombus.meeting-report.scheduler.enabled", havingValue = "true", matchIfMissing = true)
public class MeetingReportScheduledService {

    private final MeetingReportFetchService meetingReportFetchService;
    private final MeetingReportService meetingReportService;
    private final AsyncScheduledTaskExecutor asyncTaskExecutor;
    
    /**
     * 每分钟处理一次待处理的任务
     */
    @Scheduled(fixedRate = 60000) // 60秒
    public void processPendingTasks() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "processPendingReportTasks",
                "MEETING_REPORT_PENDING",
                this::doProcessPendingTasks,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(2, 60000) // 最多重试2次，间隔1分钟
        );
    }
    
    /**
     * 每5分钟处理一次需要重试的任务
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void processRetryTasks() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "processRetryReportTasks",
                "MEETING_REPORT_RETRY",
                this::doProcessRetryTasks,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(1, 300000) // 最多重试1次，间隔5分钟
        );
    }
    
    /**
     * 每30分钟清理一次超时任务
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void cleanupTimeoutTasks() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "cleanupTimeoutReportTasks",
                "MEETING_REPORT_CLEANUP",
                this::doCleanupTimeoutTasks,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.defaultConfig()
        );
    }
    
    /**
     * 每天凌晨2点清理旧数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldData() {
        try {
            log.info("开始清理旧的会议报告数据");
            
            // 清理90天前的数据
            java.time.LocalDateTime beforeTime = java.time.LocalDateTime.now().minusDays(90);

            // 调用MeetingReportService的清理方法
            meetingReportService.cleanupOldReports(beforeTime);
            
            log.info("旧数据清理完成");
        } catch (Exception e) {
            log.error("清理旧数据异常", e);
        }
    }

    /**
     * 每小时检查一次失败的报告获取任务
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void checkFailedReports() {
        try {
            log.debug("检查失败的会议报告获取任务");

            // 查找失败的报告并尝试重新获取
            // 可以考虑重新获取1小时前失败的报告
            // java.time.LocalDateTime retryAfter = java.time.LocalDateTime.now().minusHours(1);

            // 这里可以添加检查逻辑，比如统计失败率、发送告警等
            log.debug("失败报告检查完成");

        } catch (Exception e) {
            log.error("检查失败报告异常", e);
        }
    }

    /**
     * 每天凌晨1点生成会议报告统计
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateDailyStatistics() {
        try {
            log.info("开始生成每日会议报告统计");

            java.time.LocalDateTime yesterday = java.time.LocalDateTime.now().minusDays(1);
            java.time.LocalDateTime startOfDay = yesterday.withHour(0).withMinute(0).withSecond(0);
            java.time.LocalDateTime endOfDay = yesterday.withHour(23).withMinute(59).withSecond(59);

            // 获取昨天的统计数据
            var statistics = meetingReportService.getReportStatistics(startOfDay, endOfDay);

            log.info("每日统计生成完成: 日期={}, 会议数量={}, 总参会人数={}, 总时长={}分钟",
                    yesterday.toLocalDate(),
                    statistics.get("totalReports"),
                    statistics.get("totalParticipants"),
                    statistics.get("totalDuration"));

        } catch (Exception e) {
            log.error("生成每日统计异常", e);
        }
    }

    /**
     * 执行待处理任务的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doProcessPendingTasks() {
        try {
            log.debug("开始处理待处理的会议报告获取任务");
            meetingReportFetchService.processPendingTasks();

            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(1);
            result.setSuccessCount(1);
            result.setFailedCount(0);
            result.setResultData("待处理任务处理完成");

            return result;
        } catch (Exception e) {
            log.error("处理待处理任务失败", e);

            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(1);
            result.setSuccessCount(0);
            result.setFailedCount(1);
            result.setResultData("处理失败: " + e.getMessage());

            throw new RuntimeException("处理待处理任务失败", e);
        }
    }

    /**
     * 执行重试任务的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doProcessRetryTasks() {
        try {
            log.debug("开始处理需要重试的会议报告获取任务");
            meetingReportFetchService.processRetryTasks();

            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(1);
            result.setSuccessCount(1);
            result.setFailedCount(0);
            result.setResultData("重试任务处理完成");

            return result;
        } catch (Exception e) {
            log.error("处理重试任务失败", e);

            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(1);
            result.setSuccessCount(0);
            result.setFailedCount(1);
            result.setResultData("处理失败: " + e.getMessage());

            throw new RuntimeException("处理重试任务失败", e);
        }
    }

    /**
     * 执行清理超时任务的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doCleanupTimeoutTasks() {
        try {
            log.debug("开始清理超时的处理中任务");
            meetingReportFetchService.cleanupTimeoutTasks();

            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(1);
            result.setSuccessCount(1);
            result.setFailedCount(0);
            result.setResultData("超时任务清理完成");

            return result;
        } catch (Exception e) {
            log.error("清理超时任务失败", e);

            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(1);
            result.setSuccessCount(0);
            result.setFailedCount(1);
            result.setResultData("清理失败: " + e.getMessage());

            throw new RuntimeException("清理超时任务失败", e);
        }
    }
}
