package com.zoombus.service;

import com.zoombus.entity.Meeting;
import com.zoombus.entity.MeetingReport;
import com.zoombus.entity.MeetingParticipant;
import com.zoombus.entity.MeetingReportTask;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.MeetingRepository;
import com.zoombus.repository.MeetingReportRepository;
import com.zoombus.repository.MeetingParticipantRepository;
import com.zoombus.repository.MeetingReportTaskRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

/**
 * 会议报告服务类
 * 提供会议报告的核心业务逻辑
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MeetingReportService {
    
    private final MeetingReportRepository meetingReportRepository;
    private final MeetingParticipantRepository meetingParticipantRepository;
    private final MeetingReportTaskRepository meetingReportTaskRepository;
    private final MeetingReportFetchService meetingReportFetchService;
    private final ZoomMeetingRepository zoomMeetingRepository;
    private final MeetingRepository meetingRepository;
    private final PmiRecordRepository pmiRecordRepository;
    
    /**
     * 根据UUID获取会议报告
     * 支持两种UUID类型：
     * 1. Zoom真实UUID（t_meeting_reports.zoom_meeting_uuid）
     * 2. 系统内部UUID（t_meetings.meeting_uuid）
     */
    public Optional<MeetingReport> getReportByUuid(String uuid) {
        log.info("获取会议报告: uuid={}", uuid);

        // 策略1: 首先尝试直接查找（假设传入的是Zoom真实UUID）
        Optional<MeetingReport> report = meetingReportRepository.findByZoomMeetingUuid(uuid);
        if (report.isPresent()) {
            log.info("通过Zoom UUID找到会议报告: uuid={}, reportId={}", uuid, report.get().getId());
            return report;
        }

        // 策略2: 尝试作为系统内部UUID处理
        log.info("尝试作为系统内部UUID处理: uuid={}", uuid);
        Optional<Meeting> meetingOpt = meetingRepository.findByMeetingUuid(uuid);
        if (meetingOpt.isPresent()) {
            Meeting meeting = meetingOpt.get();
            String zoomMeetingId = meeting.getZoomMeetingId();
            log.info("在t_meetings表中找到记录: uuid={}, zoomMeetingId={}", uuid, zoomMeetingId);

            if (zoomMeetingId != null && !zoomMeetingId.trim().isEmpty()) {
                // 通过zoom_meeting_id查找真正的zoom_meeting_uuid
                List<ZoomMeeting> zoomMeetings = zoomMeetingRepository.findByZoomMeetingId(zoomMeetingId);
                log.info("查找Zoom会议记录: zoomMeetingId={}, 找到{}条记录", zoomMeetingId, zoomMeetings.size());
                if (!zoomMeetings.isEmpty()) {
                    // 尝试每个Zoom UUID，直到找到对应的会议报告
                    for (ZoomMeeting zoomMeeting : zoomMeetings) {
                        String actualZoomMeetingUuid = zoomMeeting.getZoomMeetingUuid();
                        log.info("尝试Zoom UUID: zoomMeetingId={}, zoomMeetingUuid={}", zoomMeetingId, actualZoomMeetingUuid);

                        // 使用这个Zoom UUID查找报告
                        report = meetingReportRepository.findByZoomMeetingUuid(actualZoomMeetingUuid);
                        if (report.isPresent()) {
                            log.info("通过系统UUID找到会议报告: inputUuid={}, zoomMeetingUuid={}, reportId={}",
                                    uuid, actualZoomMeetingUuid, report.get().getId());
                            return report;
                        } else {
                            log.info("使用Zoom UUID未找到会议报告: zoomMeetingUuid={}", actualZoomMeetingUuid);
                        }
                    }
                    log.info("尝试了所有Zoom UUID都未找到会议报告: zoomMeetingId={}", zoomMeetingId);
                } else {
                    log.info("未找到对应的Zoom会议记录: zoomMeetingId={}", zoomMeetingId);
                }
            } else {
                log.info("会议记录中缺少Zoom会议ID: meetingUuid={}", uuid);
            }
        } else {
            log.info("在t_meetings表中未找到记录: uuid={}", uuid);
        }

        log.info("未找到会议报告: uuid={}", uuid);
        return Optional.empty();
    }
    
    /**
     * 根据会议ID获取会议报告（返回最新的一条）
     */
    public Optional<MeetingReport> getReportByMeetingId(String zoomMeetingId) {
        log.debug("获取会议报告: zoomMeetingId={}", zoomMeetingId);
        return meetingReportRepository.findByZoomMeetingId(zoomMeetingId);
    }

    /**
     * 根据UUID获取所有会议报告记录（支持多条记录）
     */
    public List<MeetingReport> getAllReportsByUuid(String uuid) {
        log.info("获取所有会议报告记录: uuid={}", uuid);

        // 策略1: 首先尝试直接查找（假设传入的是Zoom真实UUID）
        List<MeetingReport> reports = meetingReportRepository.findAllByZoomMeetingUuid(uuid);
        if (!reports.isEmpty()) {
            log.info("通过Zoom UUID找到{}条会议报告: uuid={}", reports.size(), uuid);
            return reports;
        }

        // 策略2: 假设传入的是系统内部UUID，查找对应的Zoom会议记录
        Optional<Meeting> meeting = meetingRepository.findByMeetingUuid(uuid);
        if (meeting.isPresent()) {
            String zoomMeetingId = meeting.get().getZoomMeetingId();
            if (zoomMeetingId != null && !zoomMeetingId.isEmpty()) {
                log.info("通过系统UUID找到Zoom会议ID: inputUuid={}, zoomMeetingId={}", uuid, zoomMeetingId);

                // 获取该会议ID的所有报告
                reports = meetingReportRepository.findAllByZoomMeetingId(zoomMeetingId);
                if (!reports.isEmpty()) {
                    log.info("通过系统UUID找到{}条会议报告: inputUuid={}, zoomMeetingId={}",
                            reports.size(), uuid, zoomMeetingId);
                    return reports;
                }
            }
        }

        log.info("未找到会议报告: uuid={}", uuid);
        return new ArrayList<>();
    }

    /**
     * 根据会议ID获取所有会议报告记录（支持多条记录）
     */
    public List<MeetingReport> getAllReportsByMeetingId(String zoomMeetingId) {
        log.debug("获取所有会议报告记录: zoomMeetingId={}", zoomMeetingId);
        return meetingReportRepository.findAllByZoomMeetingId(zoomMeetingId);
    }
    
    /**
     * 检查会议报告是否存在
     */
    public boolean reportExists(String zoomMeetingUuid) {
        return meetingReportRepository.existsByZoomMeetingUuid(zoomMeetingUuid);
    }
    
    /**
     * 分页获取会议报告列表
     */
    public Page<MeetingReport> getReports(Pageable pageable) {
        log.debug("分页获取会议报告列表: pageable={}", pageable);
        return meetingReportRepository.findAll(pageable);
    }
    
    /**
     * 根据条件筛选会议报告
     */
    public Page<MeetingReport> getReportsWithFilters(String hostId, Long pmiRecordId, 
                                                    LocalDateTime startTime, LocalDateTime endTime,
                                                    String keyword, MeetingReport.FetchStatus fetchStatus,
                                                    Pageable pageable) {
        log.debug("筛选会议报告: hostId={}, pmiRecordId={}, startTime={}, endTime={}, keyword={}, fetchStatus={}", 
                 hostId, pmiRecordId, startTime, endTime, keyword, fetchStatus);
        
        return meetingReportRepository.findReportsWithFilters(
            hostId, pmiRecordId, startTime, endTime, keyword, fetchStatus, pageable);
    }
    
    /**
     * 获取指定PMI的会议报告
     */
    public Page<MeetingReport> getReportsByPmiRecordId(Long pmiRecordId, Pageable pageable) {
        log.debug("获取PMI会议报告: pmiRecordId={}", pmiRecordId);
        return meetingReportRepository.findByPmiRecordId(pmiRecordId, pageable);
    }
    
    /**
     * 获取指定主持人的会议报告
     */
    public Page<MeetingReport> getReportsByHostId(String hostId, Pageable pageable) {
        log.debug("获取主持人会议报告: hostId={}", hostId);
        return meetingReportRepository.findByHostId(hostId, pageable);
    }
    
    /**
     * 获取最近的会议报告
     */
    public Page<MeetingReport> getRecentReports(Pageable pageable) {
        log.debug("获取最近的会议报告");
        return meetingReportRepository.findRecentReports(pageable);
    }
    
    /**
     * 获取会议报告统计信息
     */
    public Map<String, Object> getReportStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("获取会议报告统计: startTime={}, endTime={}", startTime, endTime);
        
        List<Object> stats = meetingReportRepository.getReportStatistics(startTime, endTime);
        if (!stats.isEmpty() && stats.get(0) instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) stats.get(0);
            return result;
        }
        
        // 返回默认统计信息
        return Map.of(
            "totalReports", 0L,
            "totalParticipants", 0L,
            "totalDuration", 0L,
            "avgDuration", 0.0,
            "recordedMeetings", 0L
        );
    }
    
    /**
     * 获取会议参会人员列表
     */
    public Page<MeetingParticipant> getParticipantsByReportId(Long meetingReportId, Pageable pageable) {
        log.debug("获取会议参会人员: meetingReportId={}", meetingReportId);
        return meetingParticipantRepository.findByMeetingReportId(meetingReportId, pageable);
    }
    
    /**
     * 获取参会人员统计信息
     */
    public Map<String, Object> getParticipantStatistics(Long meetingReportId, 
                                                       LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("获取参会统计: meetingReportId={}, startTime={}, endTime={}", 
                 meetingReportId, startTime, endTime);
        
        List<Object> stats = meetingParticipantRepository.getParticipantStatistics(
            meetingReportId, startTime, endTime);
        
        if (!stats.isEmpty() && stats.get(0) instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) stats.get(0);
            return result;
        }
        
        // 返回默认统计信息
        return Map.of(
            "totalParticipations", 0L,
            "uniqueParticipants", 0L,
            "avgDuration", 0.0,
            "maxDuration", 0L,
            "hostCount", 0L,
            "videoParticipants", 0L
        );
    }
    
    /**
     * 创建会议报告获取任务
     */
    @Transactional
    public MeetingReportTask createReportTask(String zoomMeetingUuid, String zoomMeetingId) {
        log.info("创建会议报告获取任务: zoomMeetingUuid={}, zoomMeetingId={}", zoomMeetingUuid, zoomMeetingId);
        
        // 检查是否已存在任务
        Optional<MeetingReportTask> existingTask = meetingReportTaskRepository.findByZoomMeetingUuid(zoomMeetingUuid);
        if (existingTask.isPresent()) {
            MeetingReportTask task = existingTask.get();
            if (task.getTaskStatus() == MeetingReportTask.TaskStatus.PENDING || 
                task.getTaskStatus() == MeetingReportTask.TaskStatus.PROCESSING) {
                log.info("会议报告获取任务已存在: taskId={}", task.getId());
                return task;
            }
        }
        
        // 创建新任务
        MeetingReportTask task = MeetingReportTask.createImmediateTask(zoomMeetingUuid, zoomMeetingId);
        task = meetingReportTaskRepository.save(task);
        
        log.info("会议报告获取任务创建成功: taskId={}", task.getId());
        return task;
    }
    
    /**
     * 创建延迟执行的会议报告获取任务
     */
    @Transactional
    public MeetingReportTask createDelayedReportTask(String zoomMeetingUuid, String zoomMeetingId, 
                                                    LocalDateTime scheduledTime, Integer priority) {
        log.info("创建延迟会议报告获取任务: zoomMeetingUuid={}, scheduledTime={}", zoomMeetingUuid, scheduledTime);
        
        // 检查是否已存在任务
        if (meetingReportTaskRepository.existsByZoomMeetingUuid(zoomMeetingUuid)) {
            log.warn("会议报告获取任务已存在: zoomMeetingUuid={}", zoomMeetingUuid);
            return meetingReportTaskRepository.findByZoomMeetingUuid(zoomMeetingUuid).orElse(null);
        }
        
        // 创建延迟任务
        MeetingReportTask task = MeetingReportTask.createDelayedTask(
            zoomMeetingUuid, zoomMeetingId, scheduledTime, priority);
        task = meetingReportTaskRepository.save(task);
        
        log.info("延迟会议报告获取任务创建成功: taskId={}, scheduledTime={}", task.getId(), scheduledTime);
        return task;
    }
    
    /**
     * 获取待处理的报告任务
     */
    public List<MeetingReportTask> getPendingTasks() {
        return meetingReportTaskRepository.findPendingTasks(LocalDateTime.now());
    }
    
    /**
     * 获取需要重试的报告任务
     */
    public List<MeetingReportTask> getTasksNeedingRetry() {
        return meetingReportTaskRepository.findTasksNeedingRetry();
    }
    
    /**
     * 手动触发会议报告获取
     * 支持两种UUID类型：
     * 1. Zoom真实UUID（t_zoom_meetings.zoom_meeting_uuid）
     * 2. 系统内部UUID（t_meetings.meeting_uuid）
     */
    @Transactional
    public MeetingReport triggerReportFetch(String uuid) {
        log.info("手动触发会议报告获取: uuid={}", uuid);

        try {
            String actualZoomMeetingUuid = null;
            String actualZoomMeetingId = null;

            // 策略1: 首先尝试在 t_zoom_meetings 表中查找（Zoom真实UUID）
            Optional<ZoomMeeting> zoomMeetingOpt = zoomMeetingRepository.findByZoomMeetingUuid(uuid);
            if (zoomMeetingOpt.isPresent()) {
                actualZoomMeetingUuid = uuid;
                actualZoomMeetingId = zoomMeetingOpt.get().getZoomMeetingId();
                log.info("在t_zoom_meetings表中找到记录: uuid={}, zoomMeetingId={}", uuid, actualZoomMeetingId);
            } else {
                // 策略2: 尝试在 t_meetings 表中查找（系统内部UUID）
                Optional<Meeting> meetingOpt = meetingRepository.findByMeetingUuid(uuid);
                if (meetingOpt.isPresent()) {
                    Meeting meeting = meetingOpt.get();
                    actualZoomMeetingId = meeting.getZoomMeetingId();

                    if (actualZoomMeetingId != null && !actualZoomMeetingId.trim().isEmpty()) {
                        // 通过zoom_meeting_id查找真正的zoom_meeting_uuid
                        List<ZoomMeeting> zoomMeetings = zoomMeetingRepository.findByZoomMeetingId(actualZoomMeetingId);
                        log.info("查找Zoom会议记录: zoomMeetingId={}, 找到{}条记录", actualZoomMeetingId, zoomMeetings.size());

                        if (!zoomMeetings.isEmpty()) {
                            // 尝试每个Zoom UUID，找到有对应会议报告的那个
                            ZoomMeeting selectedZoomMeeting = null;
                            for (ZoomMeeting zoomMeeting : zoomMeetings) {
                                String candidateUuid = zoomMeeting.getZoomMeetingUuid();
                                log.info("检查Zoom UUID是否有对应报告: zoomMeetingId={}, zoomMeetingUuid={}", actualZoomMeetingId, candidateUuid);

                                // 检查这个UUID是否有对应的会议报告
                                Optional<MeetingReport> existingReport = meetingReportRepository.findByZoomMeetingUuid(candidateUuid);
                                if (existingReport.isPresent()) {
                                    selectedZoomMeeting = zoomMeeting;
                                    log.info("找到有对应报告的Zoom UUID: zoomMeetingUuid={}, reportId={}", candidateUuid, existingReport.get().getId());
                                    break;
                                }
                            }

                            // 如果没有找到有报告的UUID，使用最新的记录
                            if (selectedZoomMeeting == null) {
                                selectedZoomMeeting = zoomMeetings.stream()
                                    .max((m1, m2) -> m1.getCreatedAt().compareTo(m2.getCreatedAt()))
                                    .orElse(zoomMeetings.get(0));
                                log.info("未找到有对应报告的UUID，使用最新记录: zoomMeetingUuid={}", selectedZoomMeeting.getZoomMeetingUuid());
                            }

                            actualZoomMeetingUuid = selectedZoomMeeting.getZoomMeetingUuid();
                            log.info("在t_meetings表中找到记录: uuid={}, zoomMeetingId={}, zoomMeetingUuid={}",
                                    uuid, actualZoomMeetingId, actualZoomMeetingUuid);
                        } else {
                            throw new IllegalArgumentException("未找到对应的Zoom会议记录: zoomMeetingId=" + actualZoomMeetingId);
                        }
                    } else {
                        throw new IllegalArgumentException("会议记录中缺少Zoom会议ID: meetingUuid=" + uuid);
                    }
                } else {
                    throw new IllegalArgumentException("未找到对应的会议记录: uuid=" + uuid);
                }
            }

            // 调用报告获取服务
            MeetingReport report = meetingReportFetchService.fetchAndSaveMeetingReport(actualZoomMeetingUuid, actualZoomMeetingId);

            log.info("会议报告获取成功: inputUuid={}, zoomMeetingUuid={}, zoomMeetingId={}, reportId={}",
                    uuid, actualZoomMeetingUuid, actualZoomMeetingId, report.getId());
            return report;

        } catch (Exception e) {
            log.error("手动触发会议报告获取失败: uuid={}", uuid, e);
            throw new RuntimeException("会议报告获取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通过系统会议UUID手动触发会议报告获取
     */
    @Transactional
    public MeetingReport triggerReportFetchByMeetingUuid(String meetingUuid) {
        log.info("通过系统会议UUID手动触发会议报告获取: meetingUuid={}", meetingUuid);

        try {
            // 通过系统会议UUID查找会议记录
            Optional<Meeting> meetingOpt = meetingRepository.findByMeetingUuid(meetingUuid);
            if (!meetingOpt.isPresent()) {
                throw new IllegalArgumentException("未找到对应的会议记录: " + meetingUuid);
            }

            Meeting meeting = meetingOpt.get();
            String zoomMeetingId = meeting.getZoomMeetingId();
            if (zoomMeetingId == null || zoomMeetingId.trim().isEmpty()) {
                throw new IllegalArgumentException("会议记录中缺少Zoom会议ID: " + meetingUuid);
            }

            // 查找对应的Zoom会议UUID
            List<ZoomMeeting> zoomMeetings = zoomMeetingRepository.findByZoomMeetingId(zoomMeetingId);
            if (zoomMeetings.isEmpty()) {
                throw new IllegalArgumentException("未找到对应的Zoom会议记录: " + zoomMeetingId);
            }

            log.info("查找Zoom会议记录: zoomMeetingId={}, 找到{}条记录", zoomMeetingId, zoomMeetings.size());

            // 优先选择有对应会议报告的UUID，如果没有则选择最新的
            ZoomMeeting selectedZoomMeeting = null;
            for (ZoomMeeting zoomMeeting : zoomMeetings) {
                String candidateUuid = zoomMeeting.getZoomMeetingUuid();
                log.info("检查Zoom UUID是否有对应报告: zoomMeetingId={}, zoomMeetingUuid={}", zoomMeetingId, candidateUuid);

                // 检查这个UUID是否有对应的会议报告
                Optional<MeetingReport> existingReport = meetingReportRepository.findByZoomMeetingUuid(candidateUuid);
                if (existingReport.isPresent()) {
                    selectedZoomMeeting = zoomMeeting;
                    log.info("找到有对应报告的Zoom UUID，将基于此UUID获取最新报告: zoomMeetingUuid={}, reportId={}", candidateUuid, existingReport.get().getId());
                    break;
                }
            }

            // 如果没有找到有报告的UUID，使用最新的记录
            if (selectedZoomMeeting == null) {
                selectedZoomMeeting = zoomMeetings.stream()
                    .max((m1, m2) -> m1.getCreatedAt().compareTo(m2.getCreatedAt()))
                    .orElse(zoomMeetings.get(0));
                log.info("未找到有对应报告的UUID，使用最新记录: zoomMeetingUuid={}", selectedZoomMeeting.getZoomMeetingUuid());
            }

            String zoomMeetingUuid = selectedZoomMeeting.getZoomMeetingUuid();

            // 调用报告获取服务
            MeetingReport report = meetingReportFetchService.fetchAndSaveMeetingReport(zoomMeetingUuid, zoomMeetingId);

            log.info("通过系统会议UUID获取会议报告成功: meetingUuid={}, zoomMeetingId={}, reportId={}",
                    meetingUuid, zoomMeetingId, report.getId());
            return report;

        } catch (Exception e) {
            log.error("通过系统会议UUID手动触发会议报告获取失败: meetingUuid={}", meetingUuid, e);
            throw new RuntimeException("会议报告获取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通过PMI Record ID手动触发会议报告获取
     * 获取该PMI下最近的会议记录并触发报告获取
     */
    @Transactional
    public List<MeetingReport> triggerReportFetchByPmiRecordId(Long pmiRecordId) {
        log.info("通过PMI Record ID手动触发会议报告获取: pmiRecordId={}", pmiRecordId);

        try {
            // 查找PMI记录
            Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(pmiRecordId);
            if (pmiRecordOpt.isEmpty()) {
                throw new IllegalArgumentException("未找到对应的PMI记录: pmiRecordId=" + pmiRecordId);
            }

            PmiRecord pmiRecord = pmiRecordOpt.get();
            String pmiNumber = pmiRecord.getPmiNumber();

            log.info("找到PMI记录: pmiRecordId={}, pmiNumber={}", pmiRecordId, pmiNumber);

            // 查找该PMI下的所有Zoom会议记录
            List<ZoomMeeting> zoomMeetings = zoomMeetingRepository.findByPmiRecordId(pmiRecordId);

            if (zoomMeetings.isEmpty()) {
                throw new IllegalArgumentException("未找到PMI对应的会议记录: pmiNumber=" + pmiNumber);
            }

            log.info("找到{}条PMI会议记录: pmiNumber={}", zoomMeetings.size(), pmiNumber);

            List<MeetingReport> reports = new ArrayList<>();
            int successCount = 0;
            int failureCount = 0;

            // 为每个会议记录尝试获取报告（按创建时间倒序，最多处理最近的10条记录）
            List<ZoomMeeting> recentMeetings = zoomMeetings.stream()
                    .sorted((m1, m2) -> m2.getCreatedAt().compareTo(m1.getCreatedAt())) // 按创建时间倒序
                    .limit(10)
                    .collect(Collectors.toList());

            for (ZoomMeeting zoomMeeting : recentMeetings) {
                try {
                    String zoomMeetingUuid = zoomMeeting.getZoomMeetingUuid();
                    String zoomMeetingId = zoomMeeting.getZoomMeetingId();

                    // 检查是否已经有报告
                    Optional<MeetingReport> existingReport = meetingReportRepository.findByZoomMeetingUuid(zoomMeetingUuid);
                    if (existingReport.isPresent()) {
                        log.info("会议报告已存在，跳过: zoomMeetingUuid={}", zoomMeetingUuid);
                        reports.add(existingReport.get());
                        continue;
                    }

                    // 调用报告获取服务
                    MeetingReport report = meetingReportFetchService.fetchAndSaveMeetingReport(zoomMeetingUuid, zoomMeetingId);
                    reports.add(report);
                    successCount++;

                    log.info("PMI会议报告获取成功: pmiRecordId={}, zoomMeetingUuid={}, reportId={}",
                            pmiRecordId, zoomMeetingUuid, report.getId());

                } catch (Exception e) {
                    failureCount++;
                    log.warn("PMI会议报告获取失败: pmiRecordId={}, zoomMeetingUuid={}, error={}",
                            pmiRecordId, zoomMeeting.getZoomMeetingUuid(), e.getMessage());
                }
            }

            log.info("PMI会议报告获取完成: pmiRecordId={}, 成功={}, 失败={}, 总计={}",
                    pmiRecordId, successCount, failureCount, reports.size());

            return reports;

        } catch (Exception e) {
            log.error("通过PMI Record ID触发会议报告获取失败: pmiRecordId={}", pmiRecordId, e);
            throw new RuntimeException("PMI会议报告获取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据PMI Record ID获取会议报告列表
     */
    @Transactional(readOnly = true)
    public List<MeetingReport> getMeetingReportsByPmiRecordId(Long pmiRecordId) {
        log.info("根据PMI Record ID获取会议报告: pmiRecordId={}", pmiRecordId);

        try {
            // 查找PMI记录
            Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(pmiRecordId);
            if (pmiRecordOpt.isEmpty()) {
                log.warn("未找到PMI记录: pmiRecordId={}", pmiRecordId);
                return new ArrayList<>();
            }

            PmiRecord pmiRecord = pmiRecordOpt.get();
            String pmiNumber = pmiRecord.getPmiNumber();

            // 查找该PMI下的所有会议报告
            List<MeetingReport> reports = meetingReportRepository.findByPmiNumber(pmiNumber);

            log.info("找到{}条PMI会议报告: pmiRecordId={}, pmiNumber={}", reports.size(), pmiRecordId, pmiNumber);

            return reports;

        } catch (Exception e) {
            log.error("根据PMI Record ID获取会议报告失败: pmiRecordId={}", pmiRecordId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 保存会议报告（将在后续实现）
     */
    @Transactional
    public MeetingReport saveReport(MeetingReport report) {
        log.info("保存会议报告: zoomMeetingUuid={}", report.getZoomMeetingUuid());
        return meetingReportRepository.save(report);
    }
    
    /**
     * 批量保存参会人员（将在后续实现）
     */
    @Transactional
    public List<MeetingParticipant> saveParticipants(List<MeetingParticipant> participants) {
        log.info("批量保存参会人员: count={}", participants.size());
        return meetingParticipantRepository.saveAll(participants);
    }
    
    /**
     * 删除会议报告
     */
    @Transactional
    public void deleteReport(Long reportId) {
        log.info("删除会议报告: reportId={}", reportId);
        meetingReportRepository.deleteById(reportId);
    }
    
    /**
     * 清理旧的报告数据
     */
    @Transactional
    public void cleanupOldReports(LocalDateTime beforeTime) {
        log.info("清理旧的报告数据: beforeTime={}", beforeTime);

        // 清理参会人员记录
        meetingParticipantRepository.deleteParticipantsCreatedBefore(beforeTime);

        // 清理报告记录
        meetingReportRepository.deleteReportsCreatedBefore(beforeTime);

        // 清理已完成的任务
        meetingReportTaskRepository.deleteCompletedTasksBefore(beforeTime);

        log.info("旧报告数据清理完成");
    }

    /**
     * 根据Zoom会议UUID查找对应的会议ID
     */
    private String findZoomMeetingId(String zoomMeetingUuid) {
        log.debug("查找会议ID: zoomMeetingUuid={}", zoomMeetingUuid);

        Optional<ZoomMeeting> zoomMeetingOpt = zoomMeetingRepository.findByZoomMeetingUuid(zoomMeetingUuid);
        if (zoomMeetingOpt.isPresent()) {
            String meetingId = zoomMeetingOpt.get().getZoomMeetingId();
            log.debug("找到会议ID: zoomMeetingUuid={}, zoomMeetingId={}", zoomMeetingUuid, meetingId);
            return meetingId;
        }

        log.warn("未找到对应的会议记录: zoomMeetingUuid={}", zoomMeetingUuid);
        return null;
    }
}
