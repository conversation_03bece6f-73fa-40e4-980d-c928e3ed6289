package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.dto.CreateMeetingRequest;
import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.entity.Meeting;
import com.zoombus.entity.User;
import com.zoombus.entity.ZoomMeetingDetail;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.MeetingRepository;
import com.zoombus.repository.ZoomUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MeetingService {

    private final MeetingRepository meetingRepository;
    private final ZoomUserService zoomUserService;
    private final ZoomUserRepository zoomUserRepository;
    private final ZoomApiService zoomApiService;
    private final UserService userService;
    private final ZoomAuthService zoomAuthService;
    private final ZoomMeetingDetailService zoomMeetingDetailService;

    /**
     * 获取会议对应的ZoomAuth
     */
    private com.zoombus.entity.ZoomAuth getZoomAuthForMeeting(Meeting meeting) {
        if (meeting.getZoomUserId() == null || meeting.getZoomUserId().trim().isEmpty()) {
            throw new RuntimeException("会议没有关联的ZoomUser: meetingId=" + meeting.getId());
        }

        // 通过ZoomUserId查找ZoomUser，然后获取对应的ZoomAuth
        Optional<ZoomUser> zoomUserOpt = zoomUserRepository.findFirstByZoomUserId(meeting.getZoomUserId());
        if (!zoomUserOpt.isPresent()) {
            throw new RuntimeException("未找到ZoomUser: zoomUserId=" + meeting.getZoomUserId());
        }

        ZoomUser zoomUser = zoomUserOpt.get();
        if (zoomUser.getZoomAuth() == null) {
            throw new RuntimeException("ZoomUser没有关联的ZoomAuth: zoomUserId=" + meeting.getZoomUserId());
        }

        log.debug("找到会议对应的ZoomAuth: meetingId={}, zoomUserId={}, zoomAuthAccount={}",
                meeting.getId(), meeting.getZoomUserId(), zoomUser.getZoomAuth().getAccountName());
        return zoomUser.getZoomAuth();
    }

    /**
     * 创建会议
     */
    public Meeting createMeeting(CreateMeetingRequest request) {
        ZoomUser zoomUser;

        // 支持两种方式：通过userId查找ZoomUser，或直接指定ZoomUser ID
        if (request.getZoomUserIdForMeeting() != null) {
            // 直接通过ZoomUser ID查找
            zoomUser = zoomUserService.getZoomUserById(request.getZoomUserIdForMeeting())
                    .orElseThrow(() -> new RuntimeException("ZoomUser不存在: " + request.getZoomUserIdForMeeting()));
        } else if (request.getUserId() != null) {
            // 通过用户ID查找对应的ZoomUser（支持多个ZoomAuth下的用户）
            List<ZoomUser> userZoomUsers = zoomUserService.getZoomUsersByUserId(request.getUserId());
            if (userZoomUsers.isEmpty()) {
                throw new RuntimeException("用户没有关联的Zoom用户: " + request.getUserId());
            }
            // 优先选择LICENSED类型且为PUBLIC_HOST的用户
            zoomUser = userZoomUsers.stream()
                    .filter(zu -> zu.getUserType() == ZoomUser.UserType.LICENSED &&
                                 zu.getAccountUsage() == ZoomUser.AccountUsage.PUBLIC_HOST)
                    .findFirst()
                    .orElse(userZoomUsers.get(0)); // 如果没有符合条件的，使用第一个
        } else {
            throw new RuntimeException("必须提供用户ID或ZoomUser ID");
        }

        // 第一步：先保存基本会议信息到t_meetings，状态为CREATING
        Meeting meeting = new Meeting();
        // 设置创建者信息
        if (request.getUserId() != null) {
            meeting.setCreatorUserId(request.getUserId());
        } else if (zoomUser.getUser() != null) {
            meeting.setCreatorUserId(zoomUser.getUser().getId());
        }
        meeting.setZoomUserId(zoomUser.getZoomUserId());
        meeting.setZoomMeetingId(null); // 暂时为空，等Zoom API成功后填入
        meeting.setTopic(request.getTopic());
        meeting.setAgenda(request.getAgenda());
        meeting.setStartTime(request.getStartTime());
        meeting.setDurationMinutes(request.getDurationMinutes());
        meeting.setTimezone(request.getTimezone());
        meeting.setPassword(request.getPassword());

        // 根据是否为周期性会议设置正确的会议类型
        if (request.getIsRecurring() != null && request.getIsRecurring()) {
            meeting.setType(Meeting.MeetingType.RECURRING_FIXED_TIME); // 周期性会议使用类型8
            request.setType(Meeting.MeetingType.RECURRING_FIXED_TIME); // 同时更新request，确保Zoom API调用使用正确的类型
        } else {
            meeting.setType(request.getType()); // 非周期性会议使用原始类型
        }

        meeting.setStatus(Meeting.MeetingStatus.CREATING); // 设置为创建中状态
        meeting.setCreationSource(Meeting.CreationSource.ADMIN_PANEL);

        // 设置周期性会议相关字段
        meeting.setIsRecurring(request.getIsRecurring());
        meeting.setRecurrenceType(request.getRecurrenceType());
        meeting.setRepeatInterval(request.getRepeatInterval());
        meeting.setWeeklyDays(request.getWeeklyDays());
        meeting.setMonthlyType(request.getMonthlyType());
        meeting.setMonthlyDay(request.getMonthlyDay());
        meeting.setMonthlyWeekDay(request.getMonthlyWeekDay());
        meeting.setEndType(request.getEndType());
        meeting.setEndDateTime(request.getEndDateTime());
        meeting.setEndTimes(request.getEndTimes());

        // 生成周期描述
        if (request.getIsRecurring() != null && request.getIsRecurring()) {
            meeting.setRecurrenceDescription(generateRecurrenceDescription(request));
        }

        // 保存到数据库
        meeting = meetingRepository.save(meeting);
        log.info("Meeting saved to database with ID: {}, status: CREATING", meeting.getId());

        try {
            // 第二步：获取访问令牌
            String accessToken = zoomAuthService.getValidAccessToken();

            // 第三步：调用Zoom API创建会议
            ZoomApiResponse<JsonNode> apiResponse = zoomApiService.createMeeting(zoomUser.getZoomUserId(), request, accessToken);

            if (!apiResponse.isSuccess()) {
                // Zoom API调用失败，更新状态为CREATE_FAILED
                meeting.setStatus(Meeting.MeetingStatus.CREATE_FAILED);
                meetingRepository.save(meeting);
                throw new RuntimeException("创建Zoom会议失败: " + apiResponse.getMessage());
            }

            // 第三步：解析Zoom API响应并更新会议信息
            JsonNode meetingData = apiResponse.getData();
            String zoomMeetingId = meetingData.get("id").asText();

            // 更新Meeting实体
            meeting.setZoomMeetingId(zoomMeetingId);
            if (meetingData.has("join_url")) {
                meeting.setJoinUrl(meetingData.get("join_url").asText());
            }
            if (meetingData.has("start_url")) {
                meeting.setStartUrl(meetingData.get("start_url").asText());
            }
            if (meetingData.has("password")) {
                meeting.setPassword(meetingData.get("password").asText());
            }

            // 提取主持人密钥（如果存在）
            if (meetingData.has("settings")) {
                JsonNode settings = meetingData.get("settings");
                if (settings.has("host_key")) {
                    meeting.setHostKey(settings.get("host_key").asText());
                }
            }

            meeting.setStatus(Meeting.MeetingStatus.SCHEDULED); // 更新状态为已安排
            meeting = meetingRepository.save(meeting);

            // 第四步：保存Zoom API返回的详细信息到专门的entity（支持周期性会议的多个occurrence）
            List<ZoomMeetingDetail> savedDetails = zoomMeetingDetailService.saveZoomMeetingDetailWithOccurrences(meeting.getId(), meetingData);
            log.info("保存会议详情完成，共保存 {} 条记录", savedDetails.size());

            log.info("Meeting created successfully. Meeting ID: {}, Zoom Meeting ID: {}",
                    meeting.getId(), zoomMeetingId);

            return meeting;

        } catch (Exception e) {
            // 发生异常时，更新状态为CREATE_FAILED
            meeting.setStatus(Meeting.MeetingStatus.CREATE_FAILED);
            meetingRepository.save(meeting);
            log.error("Failed to create zoom meeting for meeting ID: {}", meeting.getId(), e);
            throw e;
        }
    }
    
    /**
     * 根据ID获取会议
     */
    @Transactional(readOnly = true)
    public Optional<Meeting> getMeetingById(Long id) {
        return meetingRepository.findById(id);
    }
    
    /**
     * 根据Zoom会议ID获取会议
     */
    @Transactional(readOnly = true)
    public Optional<Meeting> getMeetingByZoomMeetingId(String zoomMeetingId) {
        return meetingRepository.findByZoomMeetingId(zoomMeetingId);
    }

    /**
     * 根据UUID获取会议
     */
    public Optional<Meeting> getMeetingByUuid(String meetingUuid) {
        return meetingRepository.findByMeetingUuid(meetingUuid);
    }

    /**
     * 获取最新的主持人链接
     */
    public String getLatestHostUrl(String meetingUuid) {
        Meeting meeting = getMeetingByUuid(meetingUuid)
                .orElseThrow(() -> new IllegalArgumentException("会议不存在: " + meetingUuid));

        if (meeting.getZoomMeetingId() == null) {
            throw new IllegalStateException("会议尚未创建成功，无法获取主持人链接");
        }

        try {
            // 获取会议对应的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getZoomAuthForMeeting(meeting);

            // 从Zoom API获取最新的会议信息（使用正确的ZoomAuth）
            ZoomApiResponse<JsonNode> apiResponse = zoomApiService.getMeeting(meeting.getZoomMeetingId(), zoomAuth);

            if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                JsonNode meetingData = apiResponse.getData();
                if (meetingData.has("start_url")) {
                    String latestStartUrl = meetingData.get("start_url").asText();
                    log.info("从Zoom API获取到最新的主持人链接: meetingId={}, zoomAuth={}",
                            meeting.getId(), zoomAuth.getAccountName());
                    return latestStartUrl;
                }
            }

            // 如果API调用失败，返回本地保存的链接
            log.warn("无法从Zoom API获取最新链接，使用本地保存的链接: meetingId={}, error={}",
                    meeting.getId(), apiResponse.getMessage());
            return meeting.getStartUrl();

        } catch (Exception e) {
            log.error("获取最新主持人链接失败，使用本地保存的链接: meetingId={}", meeting.getId(), e);
            return meeting.getStartUrl();
        }
    }
    
    /**
     * 获取所有会议
     */
    @Transactional(readOnly = true)
    public Page<Meeting> getAllMeetings(Pageable pageable) {
        Page<Meeting> meetings = meetingRepository.findAll(pageable);
        // 补充主持人邮箱和创建者姓名信息
        enrichMeetingsWithUserInfo(meetings.getContent());
        return meetings;
    }
    
    /**
     * 根据创建者用户ID获取会议
     */
    @Transactional(readOnly = true)
    public List<Meeting> getMeetingsByCreatorUserId(Long creatorUserId) {
        return meetingRepository.findByCreatorUserId(creatorUserId);
    }

    /**
     * 根据Zoom用户ID获取会议
     */
    @Transactional(readOnly = true)
    public List<Meeting> getMeetingsByZoomUserId(String zoomUserId) {
        return meetingRepository.findByZoomUserId(zoomUserId);
    }
    
    /**
     * 根据状态获取会议
     */
    @Transactional(readOnly = true)
    public List<Meeting> getMeetingsByStatus(Meeting.MeetingStatus status) {
        return meetingRepository.findByStatus(status);
    }
    
    /**
     * 根据创建来源获取会议
     */
    @Transactional(readOnly = true)
    public List<Meeting> getMeetingsByCreationSource(Meeting.CreationSource creationSource) {
        return meetingRepository.findByCreationSource(creationSource);
    }
    
    /**
     * 根据时间范围获取会议
     */
    @Transactional(readOnly = true)
    public List<Meeting> getMeetingsByTimeRange(LocalDateTime startDate, LocalDateTime endDate) {
        List<Meeting> meetings = meetingRepository.findByStartTimeBetween(startDate, endDate);
        // 补充主持人邮箱和创建者姓名信息
        enrichMeetingsWithUserInfo(meetings);
        return meetings;
    }

    /**
     * 为会议列表补充用户信息
     */
    private void enrichMeetingsWithUserInfo(List<Meeting> meetings) {
        if (meetings == null || meetings.isEmpty()) {
            return;
        }

        for (Meeting meeting : meetings) {
            try {
                // 补充主持人邮箱和ZoomUser ID信息
                if (meeting.getZoomUserId() != null) {
                    List<ZoomUser> zoomUsers = zoomUserRepository.findByZoomUserId(meeting.getZoomUserId());
                    if (!zoomUsers.isEmpty()) {
                        ZoomUser zoomUser = zoomUsers.get(0);
                        meeting.setHostEmail(zoomUser.getEmail());
                        meeting.setHostZoomUserId(zoomUser.getId());
                    }
                }

                // 补充创建者姓名信息
                if (meeting.getCreatorUserId() != null) {
                    Optional<User> userOpt = userService.getUserById(meeting.getCreatorUserId());
                    if (userOpt.isPresent()) {
                        meeting.setCreatorFullName(userOpt.get().getFullName());
                    }
                }
            } catch (Exception e) {
                log.warn("补充会议用户信息失败: meetingId={}, error={}",
                    meeting.getId(), e.getMessage());
            }
        }
    }
    
    /**
     * 根据创建者用户和时间范围获取会议
     */
    @Transactional(readOnly = true)
    public List<Meeting> getMeetingsByCreatorUserAndTimeRange(Long creatorUserId,
                                                             LocalDateTime startDate,
                                                             LocalDateTime endDate) {
        return meetingRepository.findByCreatorUserIdAndStartTimeBetween(creatorUserId, startDate, endDate);
    }
    
    /**
     * 更新会议状态
     */
    public Meeting updateMeetingStatus(Long id, Meeting.MeetingStatus status) {
        Meeting meeting = meetingRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("会议不存在: " + id));
        
        meeting.setStatus(status);
        Meeting updatedMeeting = meetingRepository.save(meeting);
        log.info("会议状态更新成功: {} -> {}", updatedMeeting.getZoomMeetingId(), status);
        return updatedMeeting;
    }
    
    /**
     * 同步会议信息
     */
    public Meeting syncMeetingInfo(Long id) {
        Meeting meeting = meetingRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("会议不存在: " + id));

        // 获取会议对应的ZoomAuth
        com.zoombus.entity.ZoomAuth zoomAuth = getZoomAuthForMeeting(meeting);

        // 从Zoom API获取最新会议信息（使用正确的ZoomAuth）
        ZoomApiResponse<JsonNode> apiResponse = zoomApiService.getMeeting(meeting.getZoomMeetingId(), zoomAuth);
        if (!apiResponse.isSuccess()) {
            throw new RuntimeException("获取Zoom会议信息失败: " + apiResponse.getMessage() +
                    " (ZoomAuth: " + zoomAuth.getAccountName() + ")");
        }

        JsonNode meetingData = apiResponse.getData();

        // 更新本地信息
        meeting.setTopic(meetingData.get("topic").asText());
        if (meetingData.has("agenda")) {
            meeting.setAgenda(meetingData.get("agenda").asText());
        }
        if (meetingData.has("duration")) {
            meeting.setDurationMinutes(meetingData.get("duration").asInt());
        }
        if (meetingData.has("start_time")) {
            String startTimeStr = meetingData.get("start_time").asText();
            try {
                meeting.setStartTime(LocalDateTime.parse(startTimeStr, DateTimeFormatter.ISO_DATE_TIME));
            } catch (Exception e) {
                log.warn("解析会议开始时间失败: {}", startTimeStr, e);
            }
        }
        if (meetingData.has("join_url")) {
            meeting.setJoinUrl(meetingData.get("join_url").asText());
        }
        if (meetingData.has("password")) {
            meeting.setPassword(meetingData.get("password").asText());
        }

        Meeting updatedMeeting = meetingRepository.save(meeting);
        log.info("会议信息同步成功: {}", updatedMeeting.getZoomMeetingId());
        return updatedMeeting;
    }

    /**
     * 从Webhook数据同步会议信息（增强版）
     * 专门处理meeting.updated webhook事件
     */
    public Meeting syncMeetingInfoFromWebhook(String zoomMeetingId, JsonNode webhookData) {
        log.info("开始从Webhook同步会议信息: meetingId={}", zoomMeetingId);

        Optional<Meeting> meetingOpt = getMeetingByZoomMeetingId(zoomMeetingId);
        if (!meetingOpt.isPresent()) {
            log.warn("未找到对应的会议记录: {}", zoomMeetingId);
            return null;
        }

        Meeting meeting = meetingOpt.get();
        boolean hasChanges = false;

        try {
            // 从webhook payload中提取会议对象
            JsonNode payload = webhookData.get("payload");
            if (payload == null) {
                log.warn("Webhook数据缺少payload");
                return meeting;
            }

            JsonNode meetingObject = payload.get("object");
            if (meetingObject == null) {
                log.warn("Webhook数据缺少meeting object");
                return meeting;
            }

            // 开始更新会议信息

            // 更新基本信息
            if (meetingObject.has("topic")) {
                String newTopic = meetingObject.get("topic").asText();
                if (!newTopic.equals(meeting.getTopic())) {
                    log.info("会议主题更新: {} -> {}", meeting.getTopic(), newTopic);
                    meeting.setTopic(newTopic);
                    hasChanges = true;
                }
            }

            if (meetingObject.has("agenda")) {
                String newAgenda = meetingObject.get("agenda").asText();
                if (!Objects.equals(newAgenda, meeting.getAgenda())) {
                    log.info("会议议程更新: {} -> {}", meeting.getAgenda(), newAgenda);
                    meeting.setAgenda(newAgenda);
                    hasChanges = true;
                }
            }

            if (meetingObject.has("duration")) {
                Integer newDuration = meetingObject.get("duration").asInt();
                if (!Objects.equals(newDuration, meeting.getDurationMinutes())) {
                    log.info("会议时长更新: {} -> {}", meeting.getDurationMinutes(), newDuration);
                    meeting.setDurationMinutes(newDuration);
                    hasChanges = true;
                }
            }

            if (meetingObject.has("start_time")) {
                String startTimeStr = meetingObject.get("start_time").asText();
                try {
                    LocalDateTime newStartTime = LocalDateTime.parse(startTimeStr, DateTimeFormatter.ISO_DATE_TIME);
                    if (!newStartTime.equals(meeting.getStartTime())) {
                        log.info("会议开始时间更新: {} -> {}", meeting.getStartTime(), newStartTime);
                        meeting.setStartTime(newStartTime);
                        hasChanges = true;
                    }
                } catch (Exception e) {
                    log.warn("解析会议开始时间失败: {}", startTimeStr, e);
                }
            }

            if (meetingObject.has("timezone")) {
                String newTimezone = meetingObject.get("timezone").asText();
                if (!Objects.equals(newTimezone, meeting.getTimezone())) {
                    log.info("会议时区更新: {} -> {}", meeting.getTimezone(), newTimezone);
                    meeting.setTimezone(newTimezone);
                    hasChanges = true;
                }
            }

            if (meetingObject.has("join_url")) {
                String newJoinUrl = meetingObject.get("join_url").asText();
                if (!Objects.equals(newJoinUrl, meeting.getJoinUrl())) {
                    log.info("会议加入链接更新");
                    meeting.setJoinUrl(newJoinUrl);
                    hasChanges = true;
                }
            }

            if (meetingObject.has("password")) {
                String newPassword = meetingObject.get("password").asText();
                if (!Objects.equals(newPassword, meeting.getPassword())) {
                    log.info("会议密码更新");
                    meeting.setPassword(newPassword);
                    hasChanges = true;
                }
            }

            // 更新会议类型
            if (meetingObject.has("type")) {
                int zoomType = meetingObject.get("type").asInt();
                Meeting.MeetingType newType = convertZoomTypeToMeetingType(zoomType);
                if (newType != meeting.getType()) {
                    log.info("会议类型更新: {} -> {}", meeting.getType(), newType);
                    meeting.setType(newType);
                    hasChanges = true;
                }
            }

            // 处理周期性会议设置
            if (meetingObject.has("recurrence")) {
                hasChanges |= updateRecurrenceSettings(meeting, meetingObject.get("recurrence"));
            }

            // 同时更新ZoomMeetingDetail表
            boolean zoomDetailUpdated = updateZoomMeetingDetailFromWebhook(zoomMeetingId, meetingObject);

            // 如果有变更，保存到数据库
            if (hasChanges || zoomDetailUpdated) {
                Meeting updatedMeeting = meetingRepository.save(meeting);
                log.info("会议信息Webhook同步完成: meetingId={}, Meeting表更新={}, ZoomDetail表更新={}",
                    zoomMeetingId, hasChanges, zoomDetailUpdated);
                return updatedMeeting;
            } else {
                log.info("会议信息Webhook同步完成: meetingId={}, 无变更", zoomMeetingId);
                return meeting;
            }

        } catch (Exception e) {
            log.error("从Webhook同步会议信息失败: meetingId={}", zoomMeetingId, e);
            throw new RuntimeException("同步会议信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将Zoom会议类型转换为本地会议类型
     */
    private Meeting.MeetingType convertZoomTypeToMeetingType(int zoomType) {
        switch (zoomType) {
            case 1:
                return Meeting.MeetingType.INSTANT;
            case 2:
                return Meeting.MeetingType.SCHEDULED;
            case 3:
                return Meeting.MeetingType.RECURRING_NO_FIXED_TIME;
            case 8:
                return Meeting.MeetingType.RECURRING_FIXED_TIME;
            default:
                log.warn("未知的Zoom会议类型: {}, 默认为SCHEDULED", zoomType);
                return Meeting.MeetingType.SCHEDULED;
        }
    }

    /**
     * 更新周期性会议设置
     */
    private boolean updateRecurrenceSettings(Meeting meeting, JsonNode recurrenceNode) {
        boolean hasChanges = false;

        try {
            // 标记为周期性会议
            if (!Boolean.TRUE.equals(meeting.getIsRecurring())) {
                meeting.setIsRecurring(true);
                hasChanges = true;
                log.info("会议标记为周期性会议");
            }

            // 更新重复类型
            if (recurrenceNode.has("type")) {
                int recurrenceType = recurrenceNode.get("type").asInt();
                Meeting.RecurrenceType newRecurrenceType = convertZoomRecurrenceType(recurrenceType);
                if (newRecurrenceType != meeting.getRecurrenceType()) {
                    log.info("周期类型更新: {} -> {}", meeting.getRecurrenceType(), newRecurrenceType);
                    meeting.setRecurrenceType(newRecurrenceType);
                    hasChanges = true;
                }
            }

            // 更新重复间隔
            if (recurrenceNode.has("repeat_interval")) {
                Integer newInterval = recurrenceNode.get("repeat_interval").asInt();
                if (!Objects.equals(newInterval, meeting.getRepeatInterval())) {
                    log.info("重复间隔更新: {} -> {}", meeting.getRepeatInterval(), newInterval);
                    meeting.setRepeatInterval(newInterval);
                    hasChanges = true;
                }
            }

            // 更新每周重复的天数
            if (recurrenceNode.has("weekly_days")) {
                String newWeeklyDays = recurrenceNode.get("weekly_days").asText();
                if (!Objects.equals(newWeeklyDays, meeting.getWeeklyDays())) {
                    log.info("每周重复天数更新: {} -> {}", meeting.getWeeklyDays(), newWeeklyDays);
                    meeting.setWeeklyDays(newWeeklyDays);
                    hasChanges = true;
                }
            }

            // 更新结束类型和时间
            if (recurrenceNode.has("end_date_time")) {
                String endDateTimeStr = recurrenceNode.get("end_date_time").asText();
                try {
                    LocalDateTime newEndDateTime = LocalDateTime.parse(endDateTimeStr, DateTimeFormatter.ISO_DATE_TIME);
                    if (!Objects.equals(newEndDateTime, meeting.getEndDateTime())) {
                        log.info("周期结束时间更新: {} -> {}", meeting.getEndDateTime(), newEndDateTime);
                        meeting.setEndDateTime(newEndDateTime);
                        meeting.setEndType(Meeting.EndType.BY_DATE);
                        hasChanges = true;
                    }
                } catch (Exception e) {
                    log.warn("解析周期结束时间失败: {}", endDateTimeStr, e);
                }
            }

            if (recurrenceNode.has("end_times")) {
                Integer newEndTimes = recurrenceNode.get("end_times").asInt();
                if (!Objects.equals(newEndTimes, meeting.getEndTimes())) {
                    log.info("周期结束次数更新: {} -> {}", meeting.getEndTimes(), newEndTimes);
                    meeting.setEndTimes(newEndTimes);
                    meeting.setEndType(Meeting.EndType.BY_TIMES);
                    hasChanges = true;
                }
            }

        } catch (Exception e) {
            log.warn("更新周期性会议设置失败", e);
        }

        return hasChanges;
    }

    /**
     * 转换Zoom周期类型
     */
    private Meeting.RecurrenceType convertZoomRecurrenceType(int zoomRecurrenceType) {
        switch (zoomRecurrenceType) {
            case 1:
                return Meeting.RecurrenceType.DAILY;
            case 2:
                return Meeting.RecurrenceType.WEEKLY;
            case 3:
                return Meeting.RecurrenceType.MONTHLY;
            default:
                log.warn("未知的Zoom周期类型: {}, 默认为WEEKLY", zoomRecurrenceType);
                return Meeting.RecurrenceType.WEEKLY;
        }
    }

    /**
     * 从Webhook数据更新ZoomMeetingDetail表
     */
    private boolean updateZoomMeetingDetailFromWebhook(String zoomMeetingId, JsonNode meetingObject) {
        try {
            Optional<ZoomMeetingDetail> detailOpt = zoomMeetingDetailService.getByZoomMeetingId(zoomMeetingId);
            if (!detailOpt.isPresent()) {
                log.warn("未找到对应的ZoomMeetingDetail记录: {}", zoomMeetingId);
                return false;
            }

            ZoomMeetingDetail detail = detailOpt.get();
            boolean hasChanges = false;

            // 注意：topic, duration, password 字段已迁移到 t_meetings 表
            // 这些字段的更新现在在 syncMeetingInfoFromWebhook 方法中处理

            // 更新加入链接
            if (meetingObject.has("join_url")) {
                String newJoinUrl = meetingObject.get("join_url").asText();
                if (!Objects.equals(newJoinUrl, detail.getJoinUrl())) {
                    log.info("ZoomMeetingDetail加入链接已更新");
                    detail.setJoinUrl(newJoinUrl);
                    hasChanges = true;
                }
            }

            // 更新开始时间
            if (meetingObject.has("start_time")) {
                String startTimeStr = meetingObject.get("start_time").asText();
                try {
                    LocalDateTime newStartTime = LocalDateTime.parse(startTimeStr, DateTimeFormatter.ISO_DATE_TIME);
                    if (!Objects.equals(newStartTime, detail.getStartTime())) {
                        log.info("ZoomMeetingDetail开始时间更新: {} -> {}", detail.getStartTime(), newStartTime);
                        detail.setStartTime(newStartTime);
                        hasChanges = true;
                    }
                } catch (Exception e) {
                    log.warn("解析ZoomMeetingDetail开始时间失败: {}", startTimeStr, e);
                }
            }

            // 注意：timezone, agenda, type 字段已迁移到 t_meetings 表
            // 这些字段的更新现在在 syncMeetingInfoFromWebhook 方法中处理

            // 如果有变更，保存到数据库
            if (hasChanges) {
                // 通过ZoomMeetingDetailService保存，这样会触发@PreUpdate
                zoomMeetingDetailService.save(detail);
                log.info("ZoomMeetingDetail更新完成: meetingId={}", zoomMeetingId);
                return true;
            } else {
                log.debug("ZoomMeetingDetail无变更: meetingId={}", zoomMeetingId);
                return false;
            }

        } catch (Exception e) {
            log.error("更新ZoomMeetingDetail失败: meetingId={}", zoomMeetingId, e);
            return false;
        }
    }
    
    /**
     * 删除会议
     */
    @Transactional
    public void deleteMeeting(Long id) {
        Meeting meeting = meetingRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("会议不存在: " + id));

        log.info("开始删除会议: ID={}, ZoomMeetingId={}, Topic={}",
                id, meeting.getZoomMeetingId(), meeting.getTopic());

        try {
            // 第一步：调用Zoom API删除会议实例
            if (meeting.getZoomMeetingId() != null && !meeting.getZoomMeetingId().trim().isEmpty()) {
                log.info("调用Zoom API删除会议: {}", meeting.getZoomMeetingId());
                ZoomApiResponse<Void> apiResponse = zoomApiService.deleteMeeting(meeting.getZoomMeetingId());

                if (!apiResponse.isSuccess()) {
                    log.warn("Zoom API删除会议失败，但继续删除本地记录: {}", apiResponse.getMessage());
                    // 不抛出异常，继续删除本地记录
                } else {
                    log.info("Zoom API删除会议成功: {}", meeting.getZoomMeetingId());
                }
            } else {
                log.info("会议没有Zoom会议ID，跳过Zoom API删除");
            }

            // 第二步：删除相关的meeting details记录
            log.info("删除相关的meeting details记录: meetingId={}", id);
            zoomMeetingDetailService.deleteByMeetingId(id);

            // 第三步：删除主会议记录
            meetingRepository.delete(meeting);

            log.info("会议删除完成: ID={}, ZoomMeetingId={}", id, meeting.getZoomMeetingId());

        } catch (Exception e) {
            log.error("删除会议过程中发生异常: ID={}, ZoomMeetingId={}", id, meeting.getZoomMeetingId(), e);
            throw new RuntimeException("删除会议失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成周期性会议描述
     */
    private String generateRecurrenceDescription(CreateMeetingRequest request) {
        if (request.getIsRecurring() == null || !request.getIsRecurring()) {
            return null;
        }

        StringBuilder description = new StringBuilder();

        // 重复类型和间隔
        String recurrenceTypeText = "";
        if (request.getRecurrenceType() != null) {
            switch (request.getRecurrenceType()) {
                case DAILY:
                    if (request.getRepeatInterval() != null && request.getRepeatInterval() > 1) {
                        recurrenceTypeText = "每" + request.getRepeatInterval() + "天";
                    } else {
                        recurrenceTypeText = "每天";
                    }
                    break;
                case WEEKLY:
                    if (request.getRepeatInterval() != null && request.getRepeatInterval() > 1) {
                        recurrenceTypeText = "每" + request.getRepeatInterval() + "周";
                    } else {
                        recurrenceTypeText = "每周";
                    }

                    // 添加星期几的描述
                    if (request.getWeeklyDays() != null && !request.getWeeklyDays().trim().isEmpty()) {
                        String[] dayNumbers = request.getWeeklyDays().split(",");
                        StringBuilder dayNames = new StringBuilder();
                        for (String dayNum : dayNumbers) {
                            try {
                                int day = Integer.parseInt(dayNum.trim());
                                String dayName = "";
                                switch (day) {
                                    case 0: dayName = "星期日"; break;
                                    case 1: dayName = "星期一"; break;
                                    case 2: dayName = "星期二"; break;
                                    case 3: dayName = "星期三"; break;
                                    case 4: dayName = "星期四"; break;
                                    case 5: dayName = "星期五"; break;
                                    case 6: dayName = "星期六"; break;
                                }
                                if (!dayName.isEmpty()) {
                                    if (dayNames.length() > 0) {
                                        dayNames.append("、");
                                    }
                                    dayNames.append(dayName);
                                }
                            } catch (NumberFormatException e) {
                                // 忽略无效的数字
                            }
                        }
                        if (dayNames.length() > 0) {
                            recurrenceTypeText += "，在" + dayNames.toString();
                        }
                    }
                    break;
                case MONTHLY:
                    if (request.getRepeatInterval() != null && request.getRepeatInterval() > 1) {
                        recurrenceTypeText = "每" + request.getRepeatInterval() + "个月";
                    } else {
                        recurrenceTypeText = "每月";
                    }

                    // 添加每月重复的详细描述
                    if (request.getMonthlyType() != null) {
                        if (request.getMonthlyType() == Meeting.MonthlyType.DAY_OF_MONTH && request.getMonthlyDay() != null) {
                            recurrenceTypeText += "，第" + request.getMonthlyDay() + "天";
                        } else if (request.getMonthlyType() == Meeting.MonthlyType.DAY_OF_WEEK
                                   && request.getMonthlyDay() != null && request.getMonthlyWeekDay() != null) {
                            String weekOrder = "";
                            switch (request.getMonthlyDay()) {
                                case 1: weekOrder = "第一"; break;
                                case 2: weekOrder = "第二"; break;
                                case 3: weekOrder = "第三"; break;
                                case 4: weekOrder = "第四"; break;
                                case 5: weekOrder = "最后一"; break;
                            }

                            String weekDayName = "";
                            switch (request.getMonthlyWeekDay()) {
                                case 0: weekDayName = "星期天"; break;
                                case 1: weekDayName = "星期一"; break;
                                case 2: weekDayName = "星期二"; break;
                                case 3: weekDayName = "星期三"; break;
                                case 4: weekDayName = "星期四"; break;
                                case 5: weekDayName = "星期五"; break;
                                case 6: weekDayName = "星期六"; break;
                            }

                            if (!weekOrder.isEmpty() && !weekDayName.isEmpty()) {
                                recurrenceTypeText += "，" + weekOrder + weekDayName;
                            }
                        }
                    }
                    break;
            }
        }
        description.append(recurrenceTypeText);

        // 结束条件
        if (request.getEndType() != null) {
            switch (request.getEndType()) {
                case NO_END:
                    description.append("，无结束时间");
                    break;
                case BY_DATE:
                    if (request.getEndDateTime() != null) {
                        description.append("，直至")
                                  .append(request.getEndDateTime().toLocalDate().toString());
                    }
                    break;
                case BY_TIMES:
                    if (request.getEndTimes() != null) {
                        description.append("，").append(request.getEndTimes()).append("个会议");
                    }
                    break;
            }
        }

        return description.toString();
    }
}
