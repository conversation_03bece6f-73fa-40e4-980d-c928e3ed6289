package com.zoombus.service;

import com.zoombus.entity.PmiBillingRecord;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.exception.ResourceNotFoundException;
import com.zoombus.repository.PmiBillingRecordRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 会议结算服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MeetingSettlementService {
    
    private final ZoomMeetingRepository zoomMeetingRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final PmiBillingRecordRepository billingRecordRepository;
    
    /**
     * 结算会议
     */
    @Transactional
    public SettlementResult settleMeeting(Long meetingId) {
        ZoomMeeting meeting = zoomMeetingRepository.findById(meetingId)
            .orElseThrow(() -> new ResourceNotFoundException("会议记录不存在"));
        
        if (meeting.isSettledStatus()) {
            log.warn("会议 {} 已经结算过了", meetingId);
            return new SettlementResult(false, "会议已经结算过了", null);
        }

        // 检查是否有有效的PMI记录ID
        if (meeting.getPmiRecordId() == null) {
            log.warn("会议 {} 没有关联的PMI记录，跳过结算", meetingId);
            return new SettlementResult(true, "会议没有关联PMI记录，无需结算", null);
        }

        PmiRecord pmiRecord = pmiRecordRepository.findById(meeting.getPmiRecordId())
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));
        
        // 根据计费模式进行结算
        SettlementResult result;
        if (meeting.getBillingMode() == PmiRecord.BillingMode.LONG) {
            result = settleLongBillingMeeting(meeting, pmiRecord);
        } else {
            result = settleTimeBillingMeeting(meeting, pmiRecord);
        }
        
        // 更新会议状态
        if (result.isSuccess()) {
            meeting.setIsSettled(true);
            meeting.setStatus(ZoomMeeting.MeetingStatus.SETTLED);
            zoomMeetingRepository.save(meeting);
        }
        
        log.info("会议 {} 结算完成: {}", meetingId, result.getMessage());
        return result;
    }
    
    /**
     * 结算按时段计费的会议
     */
    private SettlementResult settleLongBillingMeeting(ZoomMeeting meeting, PmiRecord pmiRecord) {
        // 按时段计费不需要扣费，只需要记录使用情况
        int usedMinutes = meeting.getDurationMinutes();
        
        // 更新PMI使用统计
        int currentUsed = pmiRecord.getTotalUsedMinutes() != null ? pmiRecord.getTotalUsedMinutes() : 0;
        pmiRecord.setTotalUsedMinutes(currentUsed + usedMinutes);
        pmiRecord.setLastBillingTime(LocalDateTime.now());
        pmiRecordRepository.save(pmiRecord);
        
        // 创建使用记录（不扣费）
        PmiBillingRecord record = new PmiBillingRecord();
        record.setPmiRecordId(pmiRecord.getId());
        record.setZoomMeetingId(meeting.getId());
        record.setUserId(pmiRecord.getUserId());
        record.setTransactionType(PmiBillingRecord.TransactionType.DEDUCT);
        record.setAmountMinutes(0); // 按时段计费不扣费
        record.setBalanceBefore(pmiRecord.getAvailableMinutes() != null ? pmiRecord.getAvailableMinutes() : 0);
        record.setBalanceAfter(pmiRecord.getAvailableMinutes() != null ? pmiRecord.getAvailableMinutes() : 0);
        record.setDescription(String.format("按时段计费会议使用 %d 分钟（不扣费）", usedMinutes));
        record.setBillingPeriodStart(meeting.getStartTime());
        record.setBillingPeriodEnd(meeting.getEndTime());
        record.setStatus(PmiBillingRecord.RecordStatus.COMPLETED);
        record.setCreatedBy("SYSTEM");
        
        billingRecordRepository.save(record);
        
        return new SettlementResult(true, 
            String.format("按时段计费会议结算完成，使用时长: %d 分钟", usedMinutes), record);
    }
    
    /**
     * 结算按时长计费的会议
     */
    private SettlementResult settleTimeBillingMeeting(ZoomMeeting meeting, PmiRecord pmiRecord) {
        int pendingDeductMinutes = pmiRecord.getPendingDeductMinutes() != null ? 
            pmiRecord.getPendingDeductMinutes() : 0;
        
        if (pendingDeductMinutes <= 0) {
            return new SettlementResult(true, "没有待扣除的时长", null);
        }
        
        int availableMinutes = pmiRecord.getAvailableMinutes() != null ? 
            pmiRecord.getAvailableMinutes() : 0;
        int overdraftMinutes = pmiRecord.getOverdraftMinutes() != null ? 
            pmiRecord.getOverdraftMinutes() : 0;
        
        int balanceBefore = availableMinutes;
        int actualDeducted = 0;
        int newOverdraft = overdraftMinutes;
        
        if (availableMinutes >= pendingDeductMinutes) {
            // 可用时长足够
            availableMinutes -= pendingDeductMinutes;
            actualDeducted = pendingDeductMinutes;
        } else {
            // 可用时长不足，产生超额
            int shortfall = pendingDeductMinutes - availableMinutes;
            actualDeducted = availableMinutes;
            availableMinutes = 0;
            newOverdraft += shortfall;
        }
        
        // 更新PMI记录
        pmiRecord.setAvailableMinutes(availableMinutes);
        pmiRecord.setOverdraftMinutes(newOverdraft);
        pmiRecord.setPendingDeductMinutes(0); // 清零待扣时长

        int currentUsed = pmiRecord.getTotalUsedMinutes() != null ? pmiRecord.getTotalUsedMinutes() : 0;
        pmiRecord.setTotalUsedMinutes(currentUsed + pendingDeductMinutes);
        pmiRecord.setLastBillingTime(LocalDateTime.now());

        // 检查剩余可用余额，如果不大于0则修改PMI状态为未激活
        if (availableMinutes <= 0) {
            log.info("PMI {} 剩余可用余额不大于0（{}分钟），将状态修改为未激活",
                pmiRecord.getId(), availableMinutes);
            pmiRecord.setStatus(PmiRecord.PmiStatus.INACTIVE);
        }

        pmiRecordRepository.save(pmiRecord);
        
        // 创建计费记录
        PmiBillingRecord record = new PmiBillingRecord();
        record.setPmiRecordId(pmiRecord.getId());
        record.setZoomMeetingId(meeting.getId());
        record.setUserId(pmiRecord.getUserId());
        record.setTransactionType(PmiBillingRecord.TransactionType.DEDUCT);
        record.setAmountMinutes(-pendingDeductMinutes); // 负数表示扣费
        record.setBalanceBefore(balanceBefore);
        record.setBalanceAfter(availableMinutes);
        record.setDescription(String.format("会议计费扣除 %d 分钟", pendingDeductMinutes));
        record.setBillingPeriodStart(meeting.getStartTime());
        record.setBillingPeriodEnd(meeting.getEndTime());
        record.setStatus(PmiBillingRecord.RecordStatus.COMPLETED);
        record.setCreatedBy("SYSTEM");
        
        billingRecordRepository.save(record);
        
        String message;
        if (newOverdraft > overdraftMinutes) {
            message = String.format("会议结算完成，扣除 %d 分钟，产生超额 %d 分钟",
                pendingDeductMinutes, newOverdraft - overdraftMinutes);
        } else {
            message = String.format("会议结算完成，扣除 %d 分钟，剩余 %d 分钟",
                pendingDeductMinutes, availableMinutes);
        }

        // 如果PMI状态被修改为未激活，在消息中说明
        if (availableMinutes <= 0) {
            message += "，PMI已自动设置为未激活状态";
        }

        return new SettlementResult(true, message, record);
    }
    
    /**
     * 批量结算未结算的会议
     */
    @Transactional
    public BatchSettlementResult batchSettleMeetings() {
        var unsettledMeetings = zoomMeetingRepository.findMeetingsNeedSettlement();
        
        int successCount = 0;
        int failureCount = 0;
        
        for (ZoomMeeting meeting : unsettledMeetings) {
            try {
                SettlementResult result = settleMeeting(meeting.getId());
                if (result.isSuccess()) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                log.error("批量结算会议 {} 失败", meeting.getId(), e);
                failureCount++;
            }
        }
        
        log.info("批量结算完成: 成功 {}, 失败 {}", successCount, failureCount);
        return new BatchSettlementResult(successCount, failureCount);
    }
    
    /**
     * 结算结果类
     */
    public static class SettlementResult {
        private final boolean success;
        private final String message;
        private final PmiBillingRecord billingRecord;
        
        public SettlementResult(boolean success, String message, PmiBillingRecord billingRecord) {
            this.success = success;
            this.message = message;
            this.billingRecord = billingRecord;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public PmiBillingRecord getBillingRecord() {
            return billingRecord;
        }
    }
    
    /**
     * 批量结算结果类
     */
    public static class BatchSettlementResult {
        private final int successCount;
        private final int failureCount;
        
        public BatchSettlementResult(int successCount, int failureCount) {
            this.successCount = successCount;
            this.failureCount = failureCount;
        }
        
        public int getSuccessCount() {
            return successCount;
        }
        
        public int getFailureCount() {
            return failureCount;
        }
        
        public int getTotalCount() {
            return successCount + failureCount;
        }
        
        public boolean isAllSuccess() {
            return failureCount == 0;
        }
    }
}
