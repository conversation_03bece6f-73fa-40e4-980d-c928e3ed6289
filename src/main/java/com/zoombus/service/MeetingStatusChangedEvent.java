package com.zoombus.service;

import com.zoombus.entity.ZoomMeeting.MeetingStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 会议状态变更事件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MeetingStatusChangedEvent {
    
    private Long meetingId;
    private String zoomMeetingId;
    private String zoomMeetingUuid;
    private MeetingStatus oldStatus;
    private MeetingStatus newStatus;
    private String reason;
    private LocalDateTime timestamp;
    
    /**
     * 判断是否为Zoom状态同步事件
     */
    public boolean isZoomStatusSync() {
        return oldStatus.isZoomStatus() && newStatus.isZoomStatus();
    }
    
    /**
     * 判断是否为业务流程转换事件
     */
    public boolean isBusinessTransition() {
        return (oldStatus.isZoomStatus() && newStatus.isLocalStatus()) || 
               (oldStatus.isLocalStatus() && newStatus.isLocalStatus());
    }
    
    /**
     * 判断是否为会议开始事件
     */
    public boolean isMeetingStarted() {
        return newStatus == MeetingStatus.STARTED;
    }
    
    /**
     * 判断是否为会议结束事件
     */
    public boolean isMeetingEnded() {
        return newStatus == MeetingStatus.ENDED;
    }
    
    /**
     * 判断是否为会议结算事件
     */
    public boolean isMeetingSettled() {
        return newStatus == MeetingStatus.SETTLED;
    }
}
