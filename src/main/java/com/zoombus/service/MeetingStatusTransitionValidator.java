package com.zoombus.service;

import com.zoombus.entity.ZoomMeeting.MeetingStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * 会议状态转换验证器
 * 基于Zoom API官方状态和业务逻辑设计状态转换规则
 */
@Component
@Slf4j
public class MeetingStatusTransitionValidator {

    /**
     * 定义合法的状态转换规则
     * 基于Zoom API状态机和业务流程设计
     */
    private static final Map<MeetingStatus, Set<MeetingStatus>> VALID_TRANSITIONS = Map.of(
        // === 创建阶段 ===
        MeetingStatus.CREATING, Set.of(
            MeetingStatus.WAITING,      // 创建成功，等待开始
            MeetingStatus.CREATE_FAILED // 创建失败
        ),
        
        MeetingStatus.CREATE_FAILED, Set.of(
            MeetingStatus.CREATING,     // 重新尝试创建
            MeetingStatus.DELETED       // 删除失败的记录
        ),
        
        // === Zoom官方状态转换 ===
        MeetingStatus.WAITING, Set.of(
            MeetingStatus.STARTED,      // 会议开始
            MeetingStatus.ENDED,        // 直接结束（未开始就结束）
            MeetingStatus.DELETED,      // 删除会议
            MeetingStatus.ERROR         // 异常状态
        ),
        
        MeetingStatus.STARTED, Set.of(
            MeetingStatus.ENDED,        // 会议结束
            MeetingStatus.ERROR         // 异常状态
        ),
        
        MeetingStatus.ENDED, Set.of(
            MeetingStatus.SETTLING,     // 开始结算
            MeetingStatus.SETTLED,      // 直接标记为已结算（无需结算的情况）
            MeetingStatus.DELETED       // 删除记录
        ),
        
        // === 业务状态转换 ===
        MeetingStatus.SETTLING, Set.of(
            MeetingStatus.SETTLED,      // 结算完成
            MeetingStatus.ERROR         // 结算失败
        ),
        
        MeetingStatus.SETTLED, Set.of(
            MeetingStatus.DELETED       // 删除已结算的记录
        ),
        
        // === 异常状态处理 ===
        MeetingStatus.ERROR, Set.of(
            MeetingStatus.WAITING,      // 恢复到等待状态
            MeetingStatus.ENDED,        // 强制结束
            MeetingStatus.DELETED       // 删除异常记录
        ),
        
        // === 删除状态（终态） ===
        MeetingStatus.DELETED, Set.of()  // 删除状态不能转换到其他状态
    );

    /**
     * 验证状态转换是否合法
     */
    public boolean isValidTransition(MeetingStatus from, MeetingStatus to) {
        if (from == null || to == null) {
            log.warn("状态转换验证失败：状态不能为null, from={}, to={}", from, to);
            return false;
        }
        
        if (from == to) {
            // 相同状态的转换总是合法的（幂等操作）
            return true;
        }
        
        Set<MeetingStatus> validTargets = VALID_TRANSITIONS.get(from);
        if (validTargets == null) {
            log.warn("未定义的源状态转换规则: {}", from);
            return false;
        }
        
        boolean isValid = validTargets.contains(to);
        if (!isValid) {
            log.warn("非法的状态转换: {} -> {}, 合法的目标状态: {}", from, to, validTargets);
        }
        
        return isValid;
    }

    /**
     * 获取指定状态的所有合法目标状态
     */
    public Set<MeetingStatus> getValidTargetStatuses(MeetingStatus from) {
        return VALID_TRANSITIONS.getOrDefault(from, Set.of());
    }

    /**
     * 检查状态转换是否为Zoom状态同步
     */
    public boolean isZoomStatusSync(MeetingStatus from, MeetingStatus to) {
        return from.isZoomStatus() && to.isZoomStatus();
    }

    /**
     * 检查状态转换是否为业务流程转换
     */
    public boolean isBusinessTransition(MeetingStatus from, MeetingStatus to) {
        return (from.isZoomStatus() && to.isLocalStatus()) || 
               (from.isLocalStatus() && to.isLocalStatus());
    }

    /**
     * 验证并抛出异常（用于严格验证场景）
     */
    public void validateTransitionOrThrow(MeetingStatus from, MeetingStatus to, String context) {
        if (!isValidTransition(from, to)) {
            throw new IllegalStateException(
                String.format("非法的状态转换 [%s]: %s -> %s", context, from, to));
        }
    }

    /**
     * 获取状态转换的建议
     */
    public String getTransitionAdvice(MeetingStatus from, MeetingStatus to) {
        if (isValidTransition(from, to)) {
            return "状态转换合法";
        }
        
        Set<MeetingStatus> validTargets = getValidTargetStatuses(from);
        if (validTargets.isEmpty()) {
            return String.format("状态 %s 已是终态，不能转换到其他状态", from);
        }
        
        return String.format("状态 %s 只能转换到: %s", from, validTargets);
    }
}
