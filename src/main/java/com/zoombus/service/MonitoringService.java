package com.zoombus.service;

import com.zoombus.dto.MonitoringData;
import com.zoombus.entity.Meeting;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.MeetingRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomAuthRepository;
import com.zoombus.repository.ZoomUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 实时监控数据收集服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MonitoringService {
    
    private final ZoomAuthRepository zoomAuthRepository;
    private final ZoomUserRepository zoomUserRepository;
    private final MeetingRepository meetingRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final ZoomApiService zoomApiService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String MONITORING_KEY_PREFIX = "monitoring:";
    private static final String ZOOM_ACCOUNT_STATUS_KEY = MONITORING_KEY_PREFIX + "zoom_account_status";
    private static final String MEETING_STATUS_KEY = MONITORING_KEY_PREFIX + "meeting_status";
    private static final String PMI_STATUS_KEY = MONITORING_KEY_PREFIX + "pmi_status";
    private static final String SYSTEM_PERFORMANCE_KEY = MONITORING_KEY_PREFIX + "system_performance";
    private static final String API_STATS_KEY = MONITORING_KEY_PREFIX + "api_stats";
    
    /**
     * 收集Zoom账号状态数据
     */
    public MonitoringData.ZoomAccountStatus collectZoomAccountStatus() {
        try {
            List<ZoomAuth> activeAccounts = zoomAuthRepository.findByStatus(ZoomAuth.AuthStatus.ACTIVE);
            
            if (activeAccounts.isEmpty()) {
                return MonitoringData.ZoomAccountStatus.builder()
                        .accountId(0L)
                        .accountName("无活跃账号")
                        .status("INACTIVE")
                        .totalLicenses(0)
                        .usedLicenses(0)
                        .availableLicenses(0)
                        .usageRate(0.0)
                        .healthStatus("ERROR")
                        .lastUpdateTime(LocalDateTime.now())
                        .build();
            }
            
            // 获取主要账号信息
            ZoomAuth primaryAccount = activeAccounts.get(0);
            
            // 统计许可证使用情况
            List<ZoomUser> allUsers = zoomUserRepository.findByZoomAuthAndStatus(primaryAccount, ZoomUser.UserStatus.ACTIVE);
            List<ZoomUser> licensedUsers = allUsers.stream()
                    .filter(user -> ZoomUser.UserType.LICENSED.equals(user.getUserType()))
                    .collect(Collectors.toList());
            
            int totalLicenses = 100; // 这里应该从Zoom API获取实际许可证数量
            int usedLicenses = licensedUsers.size();
            int availableLicenses = totalLicenses - usedLicenses;
            double usageRate = totalLicenses > 0 ? (double) usedLicenses / totalLicenses * 100 : 0;
            
            // 判断健康状态
            String healthStatus = "HEALTHY";
            if (usageRate > 90) {
                healthStatus = "ERROR";
            } else if (usageRate > 80) {
                healthStatus = "WARNING";
            }
            
            MonitoringData.ZoomAccountStatus status = MonitoringData.ZoomAccountStatus.builder()
                    .accountId(primaryAccount.getId())
                    .accountName(primaryAccount.getAccountName())
                    .status(primaryAccount.getStatus().toString())
                    .totalLicenses(totalLicenses)
                    .usedLicenses(usedLicenses)
                    .availableLicenses(availableLicenses)
                    .usageRate(usageRate)
                    .healthStatus(healthStatus)
                    .lastUpdateTime(LocalDateTime.now())
                    .build();
            
            // 缓存数据
            redisTemplate.opsForValue().set(ZOOM_ACCOUNT_STATUS_KEY, status, 5, TimeUnit.MINUTES);
            
            return status;
            
        } catch (Exception e) {
            log.error("收集Zoom账号状态失败", e);
            return MonitoringData.ZoomAccountStatus.builder()
                    .healthStatus("ERROR")
                    .lastUpdateTime(LocalDateTime.now())
                    .build();
        }
    }
    
    /**
     * 收集会议状态数据
     */
    public MonitoringData.MeetingStatus collectMeetingStatus() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
            
            // 统计今日会议
            List<Meeting> todayMeetings = meetingRepository.findByStartTimeBetween(todayStart, now);
            
            int totalMeetings = todayMeetings.size();
            int activeMeetings = 0; // 这里需要调用Zoom API获取实际进行中的会议
            int scheduledMeetings = (int) todayMeetings.stream()
                    .filter(meeting -> meeting.getStartTime().isAfter(now))
                    .count();
            int completedMeetings = totalMeetings - scheduledMeetings - activeMeetings;
            
            // 获取活跃会议列表（模拟数据，实际需要从Zoom API获取）
            List<MonitoringData.ActiveMeeting> activeMeetingList = new ArrayList<>();
            
            MonitoringData.MeetingStatus status = MonitoringData.MeetingStatus.builder()
                    .totalMeetings(totalMeetings)
                    .activeMeetings(activeMeetings)
                    .scheduledMeetings(scheduledMeetings)
                    .completedMeetings(completedMeetings)
                    .activeMeetingList(activeMeetingList)
                    .lastUpdateTime(now)
                    .build();
            
            // 缓存数据
            redisTemplate.opsForValue().set(MEETING_STATUS_KEY, status, 2, TimeUnit.MINUTES);
            
            return status;
            
        } catch (Exception e) {
            log.error("收集会议状态失败", e);
            return MonitoringData.MeetingStatus.builder()
                    .lastUpdateTime(LocalDateTime.now())
                    .build();
        }
    }
    
    /**
     * 收集PMI使用状态
     */
    public MonitoringData.PmiStatus collectPmiStatus() {
        try {
            List<PmiRecord> allPmi = pmiRecordRepository.findByStatus(PmiRecord.PmiStatus.ACTIVE);
            List<PmiRecord> inUsePmi = allPmi.stream()
                    .filter(pmi -> pmi.getLastUsedAt() != null && 
                            pmi.getLastUsedAt().isAfter(LocalDateTime.now().minusHours(2)))
                    .collect(Collectors.toList());
            
            int totalPmi = allPmi.size();
            int activePmi = totalPmi;
            int inUse = inUsePmi.size();
            int available = totalPmi - inUse;
            double usageRate = totalPmi > 0 ? (double) inUse / totalPmi * 100 : 0;
            
            MonitoringData.PmiStatus status = MonitoringData.PmiStatus.builder()
                    .totalPmi(totalPmi)
                    .activePmi(activePmi)
                    .inUsePmi(inUse)
                    .availablePmi(available)
                    .usageRate(usageRate)
                    .lastUpdateTime(LocalDateTime.now())
                    .build();
            
            // 缓存数据
            redisTemplate.opsForValue().set(PMI_STATUS_KEY, status, 3, TimeUnit.MINUTES);
            
            return status;
            
        } catch (Exception e) {
            log.error("收集PMI状态失败", e);
            return MonitoringData.PmiStatus.builder()
                    .lastUpdateTime(LocalDateTime.now())
                    .build();
        }
    }
    
    /**
     * 收集系统性能数据
     */
    public MonitoringData.SystemPerformance collectSystemPerformance() {
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();

            // 使用模拟数据，避免JVM访问限制问题
            double cpuUsage = Math.random() * 20 + 20; // 模拟20-40%的CPU使用率
            long totalMemory = memoryBean.getHeapMemoryUsage().getMax();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            double memoryUsage = totalMemory > 0 ? (double) usedMemory / totalMemory * 100 : 0;
            
            // 这里可以添加数据库连接池、Redis连接等监控
            long databaseConnections = 10; // 模拟数据
            long redisConnections = 5; // 模拟数据
            int activeUsers = 1; // 模拟数据
            long apiCallsPerMinute = 50; // 模拟数据
            
            MonitoringData.SystemPerformance performance = MonitoringData.SystemPerformance.builder()
                    .cpuUsage(cpuUsage)
                    .memoryUsage(memoryUsage)
                    .databaseConnections(databaseConnections)
                    .redisConnections(redisConnections)
                    .activeUsers(activeUsers)
                    .apiCallsPerMinute(apiCallsPerMinute)
                    .lastUpdateTime(LocalDateTime.now())
                    .build();
            
            // 缓存数据
            redisTemplate.opsForValue().set(SYSTEM_PERFORMANCE_KEY, performance, 1, TimeUnit.MINUTES);
            
            return performance;
            
        } catch (Exception e) {
            log.error("收集系统性能数据失败", e);
            return MonitoringData.SystemPerformance.builder()
                    .lastUpdateTime(LocalDateTime.now())
                    .build();
        }
    }
    
    /**
     * 获取缓存的监控数据
     */
    public <T> T getCachedData(String key, Class<T> clazz) {
        try {
            Object data = redisTemplate.opsForValue().get(key);
            if (data != null && clazz.isInstance(data)) {
                return clazz.cast(data);
            }
        } catch (Exception e) {
            log.warn("获取缓存数据失败: {}", key, e);
        }
        return null;
    }
}
