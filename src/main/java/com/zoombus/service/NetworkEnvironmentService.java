package com.zoombus.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.util.concurrent.TimeUnit;

/**
 * 网络环境检测服务
 * 用于检测当前环境的外网IP并决定是否需要设置代理
 */
@Service
@Slf4j
public class NetworkEnvironmentService {

    // 目标外网IP，需要使用代理的环境
    private static final String TARGET_EXTERNAL_IP = "**************";
    
    // 代理配置
    private static final String PROXY_HOST = "127.0.0.1";
    private static final String PROXY_PORT = "6690";
    private static final String PROXY_URL = "http://" + PROXY_HOST + ":" + PROXY_PORT;

    // 检测外网IP的服务列表（按优先级排序）
    private static final String[] IP_CHECK_SERVICES = {
        "https://api.ipify.org",
        "https://ipinfo.io/ip",
        "https://icanhazip.com",
        "https://ident.me",
        "https://checkip.amazonaws.com"
    };

    private String currentExternalIp = null;
    private boolean proxyConfigured = false;

    /**
     * 检测当前环境的外网IP
     */
    public String detectExternalIp() {
        log.info("🌐 开始检测当前环境外网IP...");
        
        for (String service : IP_CHECK_SERVICES) {
            try {
                String ip = getExternalIpFromService(service);
                if (ip != null && !ip.trim().isEmpty()) {
                    currentExternalIp = ip.trim();
                    log.info("✅ 检测到外网IP: {} (通过服务: {})", currentExternalIp, service);
                    return currentExternalIp;
                }
            } catch (Exception e) {
                log.warn("⚠️ 通过服务 {} 检测外网IP失败: {}", service, e.getMessage());
            }
        }
        
        log.error("❌ 所有IP检测服务都失败，无法获取外网IP");
        return null;
    }

    /**
     * 从指定服务获取外网IP
     */
    private String getExternalIpFromService(String serviceUrl) throws Exception {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(serviceUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5秒连接超时
            connection.setReadTimeout(5000);    // 5秒读取超时
            connection.setRequestProperty("User-Agent", "ZoomBus/1.0");

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream()))) {
                    return reader.readLine();
                }
            } else {
                throw new Exception("HTTP响应码: " + responseCode);
            }
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 检查是否需要配置代理
     */
    public boolean shouldConfigureProxy() {
        if (currentExternalIp == null) {
            currentExternalIp = detectExternalIp();
        }
        
        boolean needProxy = TARGET_EXTERNAL_IP.equals(currentExternalIp);
        log.info("🔍 代理配置检查: 当前IP={}, 目标IP={}, 需要代理={}", 
            currentExternalIp, TARGET_EXTERNAL_IP, needProxy);
        
        return needProxy;
    }

    /**
     * 配置Zoom API代理
     */
    public void configureZoomApiProxy() {
        if (proxyConfigured) {
            log.info("ℹ️ 代理已配置，跳过重复配置");
            return;
        }

        if (shouldConfigureProxy()) {
            log.info("🔧 检测到目标环境IP ({}), 开始配置Zoom API代理...", TARGET_EXTERNAL_IP);
            log.info("📋 代理配置详情:");
            log.info("   - 目标环境IP: {}", TARGET_EXTERNAL_IP);
            log.info("   - 当前外网IP: {}", currentExternalIp);
            log.info("   - 代理地址: {}:{}", PROXY_HOST, PROXY_PORT);
            
            // 设置HTTP代理
            System.setProperty("http.proxyHost", PROXY_HOST);
            System.setProperty("http.proxyPort", PROXY_PORT);
            System.setProperty("https.proxyHost", PROXY_HOST);
            System.setProperty("https.proxyPort", PROXY_PORT);
            
            // 设置代理认证（如果需要）
            // System.setProperty("http.proxyUser", "username");
            // System.setProperty("http.proxyPassword", "password");
            
            // 设置不使用代理的主机列表（保留原有的Zoom域名绕过配置）
            String nonProxyHosts = System.getProperty("http.nonProxyHosts", "");
            if (!nonProxyHosts.contains("localhost")) {
                nonProxyHosts = nonProxyHosts.isEmpty() ? 
                    "localhost|127.0.0.1|*.local" : 
                    nonProxyHosts + "|localhost|127.0.0.1|*.local";
                System.setProperty("http.nonProxyHosts", nonProxyHosts);
                System.setProperty("https.nonProxyHosts", nonProxyHosts);
            }
            
            proxyConfigured = true;
            
            log.info("✅ Zoom API代理配置完成:");
            log.info("   - HTTP代理: {}:{}", PROXY_HOST, PROXY_PORT);
            log.info("   - HTTPS代理: {}:{}", PROXY_HOST, PROXY_PORT);
            log.info("   - 不使用代理的主机: {}", nonProxyHosts);
            
            // 验证代理配置
            verifyProxyConfiguration();
            
        } else {
            log.info("ℹ️ 当前环境不需要配置代理 (当前IP: {})", currentExternalIp);
            
            // 确保清除可能存在的代理配置
            clearProxyConfiguration();
        }
    }

    /**
     * 清除代理配置
     */
    public void clearProxyConfiguration() {
        log.info("🧹 清除代理配置...");
        
        System.clearProperty("http.proxyHost");
        System.clearProperty("http.proxyPort");
        System.clearProperty("https.proxyHost");
        System.clearProperty("https.proxyPort");
        System.clearProperty("http.proxyUser");
        System.clearProperty("http.proxyPassword");
        
        proxyConfigured = false;
        log.info("✅ 代理配置已清除");
    }

    /**
     * 验证代理配置
     */
    private void verifyProxyConfiguration() {
        try {
            log.info("🔍 验证代理配置...");
            
            String httpProxyHost = System.getProperty("http.proxyHost");
            String httpProxyPort = System.getProperty("http.proxyPort");
            String httpsProxyHost = System.getProperty("https.proxyHost");
            String httpsProxyPort = System.getProperty("https.proxyPort");
            
            log.info("当前系统代理配置:");
            log.info("  - http.proxyHost: {}", httpProxyHost);
            log.info("  - http.proxyPort: {}", httpProxyPort);
            log.info("  - https.proxyHost: {}", httpsProxyHost);
            log.info("  - https.proxyPort: {}", httpsProxyPort);
            
            if (PROXY_HOST.equals(httpProxyHost) && PROXY_PORT.equals(httpProxyPort)) {
                log.info("✅ 代理配置验证成功");
            } else {
                log.warn("⚠️ 代理配置可能不正确");
            }
            
        } catch (Exception e) {
            log.error("❌ 验证代理配置时发生异常", e);
        }
    }

    /**
     * 获取当前外网IP
     */
    public String getCurrentExternalIp() {
        return currentExternalIp;
    }

    /**
     * 检查代理是否已配置
     */
    public boolean isProxyConfigured() {
        return proxyConfigured;
    }

    /**
     * 获取代理配置信息
     */
    public String getProxyInfo() {
        if (proxyConfigured) {
            return String.format("代理已配置: %s (当前IP: %s)", PROXY_URL, currentExternalIp);
        } else {
            return String.format("代理未配置 (当前IP: %s)", currentExternalIp);
        }
    }

    /**
     * 测试网络连接
     */
    public boolean testNetworkConnection(String testUrl) {
        try {
            log.info("🔗 测试网络连接: {}", testUrl);
            
            URL url = new URL(testUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            boolean success = responseCode >= 200 && responseCode < 400;
            
            log.info("网络连接测试结果: {} (响应码: {})", success ? "成功" : "失败", responseCode);
            return success;
            
        } catch (Exception e) {
            log.warn("网络连接测试失败: {}", e.getMessage());
            return false;
        }
    }
}
