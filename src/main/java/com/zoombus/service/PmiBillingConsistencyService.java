package com.zoombus.service;

import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.PmiScheduleWindowRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PMI计费一致性检查和修复服务
 * 用于检查和修复计费模式与窗口状态的不一致问题
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiBillingConsistencyService {

    private final PmiRecordRepository pmiRecordRepository;
    private final PmiScheduleWindowRepository windowRepository;
    private final PmiBillingModeService pmiBillingModeService;

    /**
     * 定期检查计费模式一致性（每小时执行一次）
     */
    @Scheduled(cron = "0 0 * * * ?")
    @Transactional
    public void checkBillingConsistency() {
        log.info("开始执行PMI计费模式一致性检查");
        
        int fixedCount = 0;
        
        // 检查所有LONG模式的PMI
        List<PmiRecord> longBillingPmis = pmiRecordRepository.findByBillingMode(PmiRecord.BillingMode.LONG);
        
        for (PmiRecord pmi : longBillingPmis) {
            try {
                if (checkAndFixLongBillingPmi(pmi)) {
                    fixedCount++;
                }
            } catch (Exception e) {
                log.error("检查PMI {} 计费一致性时发生错误", pmi.getId(), e);
            }
        }
        
        log.info("PMI计费模式一致性检查完成，修复了 {} 个不一致的记录", fixedCount);
    }

    /**
     * 检查和修复LONG计费模式的PMI
     */
    private boolean checkAndFixLongBillingPmi(PmiRecord pmi) {
        // 检查是否有活跃窗口
        List<PmiScheduleWindow> activeWindows = windowRepository.findByPmiRecordIdAndStatus(
                pmi.getId(), PmiScheduleWindow.WindowStatus.ACTIVE);
        
        LocalDateTime now = LocalDateTime.now();
        
        if (activeWindows.isEmpty()) {
            // 没有活跃窗口但是LONG模式，需要修复
            log.warn("PMI {} 是LONG计费模式但没有活跃窗口，需要修复", pmi.getId());
            pmiBillingModeService.switchToOriginalBilling(pmi.getId());
            return true;
            
        } else {
            // 有活跃窗口，检查是否过期
            boolean hasValidWindow = false;

            for (PmiScheduleWindow window : activeWindows) {
                // 使用新的精确时间字段
                LocalDateTime windowEnd = window.getEndDateTime();

                if (windowEnd == null) {
                    log.warn("PMI {} 窗口 {} 缺少结束时间数据，跳过检查", pmi.getId(), window.getId());
                    continue;
                }

                // 增强日志记录，便于问题追踪
                log.debug("检查PMI {} 窗口 {} 过期时间: 计算的过期时间={}, 当前时间={}",
                    pmi.getId(), window.getId(), windowEnd, now);

                if (windowEnd.isAfter(now)) {
                    hasValidWindow = true;
                    log.debug("PMI {} 窗口 {} 仍在有效期内，过期时间: {}", pmi.getId(), window.getId(), windowEnd);
                    break;
                } else {
                    log.debug("PMI {} 窗口 {} 已过期，过期时间: {}, 当前时间: {}", pmi.getId(), window.getId(), windowEnd, now);
                }
            }

            if (!hasValidWindow) {
                // 所有窗口都已过期，需要关闭
                log.warn("PMI {} 的所有活跃窗口都已过期，需要关闭。活跃窗口数量: {}", pmi.getId(), activeWindows.size());

                // 关闭所有过期窗口，并记录详细信息
                for (PmiScheduleWindow window : activeWindows) {
                    LocalDateTime windowEnd = window.getEndDateTime();

                    log.info("关闭过期窗口: PMI={}, 窗口ID={}, 开始时间={}, 结束时间={}, 当前时间={}",
                        pmi.getId(), window.getId(), window.getStartDateTime(), windowEnd, now);

                    window.setStatus(PmiScheduleWindow.WindowStatus.COMPLETED);
                    window.setActualEndTime(now);
                }
                windowRepository.saveAll(activeWindows);

                // 恢复计费模式
                pmiBillingModeService.switchToOriginalBilling(pmi.getId());
                log.info("PMI {} 所有窗口已关闭，已恢复到原始计费模式", pmi.getId());
                return true;
            }
        }
        
        return false;
    }

    /**
     * 手动触发一致性检查
     */
    @Transactional
    public void manualConsistencyCheck() {
        log.info("手动触发PMI计费模式一致性检查");
        checkBillingConsistency();
    }

    /**
     * 检查特定PMI的一致性
     */
    @Transactional
    public boolean checkPmiConsistency(Long pmiRecordId) {
        PmiRecord pmi = pmiRecordRepository.findById(pmiRecordId).orElse(null);
        if (pmi == null) {
            log.warn("PMI记录不存在: {}", pmiRecordId);
            return false;
        }
        
        if (pmi.getBillingMode() == PmiRecord.BillingMode.LONG) {
            return checkAndFixLongBillingPmi(pmi);
        }
        
        return false;
    }
}
