package com.zoombus.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.exception.ResourceNotFoundException;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.PmiScheduleWindowRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * PMI计费模式管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiBillingModeService {

    private final PmiRecordRepository pmiRecordRepository;
    private final PmiScheduleWindowRepository windowRepository;
    private final ZoomMeetingRepository zoomMeetingRepository;
    private final BillingMonitorService billingMonitorService;
    private final ObjectMapper objectMapper;
    
    /**
     * 开启时间窗口，切换到按时段计费（改进版本）
     */
    @Transactional(isolation = Isolation.SERIALIZABLE)
    public void switchToLongBilling(Long pmiRecordId, Long windowId, LocalDateTime expireTime) {
        // 使用悲观锁防止并发问题
        PmiRecord pmiRecord = pmiRecordRepository.findByIdForUpdate(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

        PmiRecord.BillingMode previousMode = pmiRecord.getBillingMode();

        // 获取当前活跃窗口列表
        Set<Long> activeWindowIds = getActiveWindowIds(pmiRecord);

        // 如果不是LONG模式，保存原始计费模式
        if (pmiRecord.getBillingMode() != PmiRecord.BillingMode.LONG) {
            pmiRecord.setOriginalBillingMode(pmiRecord.getBillingMode());
            log.info("PMI {} 保存原始计费模式: {}", pmiRecordId, pmiRecord.getBillingMode());
        }

        // 添加新窗口到活跃窗口列表
        activeWindowIds.add(windowId);

        // 计算最晚的到期时间
        LocalDateTime latestExpireTime = calculateLatestExpireTime(pmiRecordId, activeWindowIds, expireTime);

        // 切换到按时段计费并激活PMI
        pmiRecord.setBillingMode(PmiRecord.BillingMode.LONG);
        pmiRecord.setCurrentWindowId(windowId); // 记录最新的窗口ID
        pmiRecord.setWindowExpireTime(latestExpireTime);
        pmiRecord.setBillingStatus(PmiRecord.BillingStatus.ACTIVE);
        pmiRecord.setActiveWindowIds(serializeWindowIds(activeWindowIds));

        // 确保PMI状态为ACTIVE（窗口开启时PMI应该被激活）
        if (pmiRecord.getStatus() != PmiRecord.PmiStatus.ACTIVE) {
            pmiRecord.setStatus(PmiRecord.PmiStatus.ACTIVE);
            log.info("PMI {} 开启窗口时激活PMI状态: {} -> ACTIVE", pmiRecordId, pmiRecord.getStatus());
        }

        pmiRecordRepository.save(pmiRecord);

        // 只有在首次切换到LONG模式时才处理活跃会议
        if (previousMode != PmiRecord.BillingMode.LONG) {
            handleActiveMeetingsBillingModeChange(pmiRecordId, previousMode, PmiRecord.BillingMode.LONG);
        }

        log.info("PMI {} 切换到按时段计费模式，窗口ID: {}, 活跃窗口数: {}, 最晚到期时间: {}, PMI状态: ACTIVE",
                pmiRecordId, windowId, activeWindowIds.size(), latestExpireTime);
    }
    
    /**
     * 关闭时间窗口，恢复到原始计费模式（改进版本）
     */
    @Transactional(isolation = Isolation.SERIALIZABLE)
    public void switchToOriginalBilling(Long pmiRecordId) {
        // 使用悲观锁防止并发问题
        PmiRecord pmiRecord = pmiRecordRepository.findByIdForUpdate(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

        PmiRecord.BillingMode previousMode = pmiRecord.getBillingMode();
        PmiRecord.BillingMode originalMode = pmiRecord.getOriginalBillingMode();

        // 如果没有保存原始模式，默认使用BY_TIME
        if (originalMode == null) {
            originalMode = PmiRecord.BillingMode.BY_TIME;
            log.warn("PMI {} 没有保存原始计费模式，默认使用BY_TIME", pmiRecordId);
        }

        // 恢复到原始计费模式
        pmiRecord.setBillingMode(originalMode);
        pmiRecord.setOriginalBillingMode(null); // 清除原始模式记录
        pmiRecord.setCurrentWindowId(null);
        pmiRecord.setWindowExpireTime(null);
        pmiRecord.setActiveWindowIds(null); // 清除活跃窗口列表

        pmiRecordRepository.save(pmiRecord);

        // 处理活跃会议的计费状态切换
        handleActiveMeetingsBillingModeChange(pmiRecordId, previousMode, originalMode);

        log.info("PMI {} 恢复到原始计费模式: {}", pmiRecordId, originalMode);
    }

    /**
     * 关闭单个窗口（改进版本）
     */
    @Transactional(isolation = Isolation.SERIALIZABLE)
    public void closeWindow(Long pmiRecordId, Long windowId) {
        // 使用悲观锁防止并发问题
        PmiRecord pmiRecord = pmiRecordRepository.findByIdForUpdate(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

        // 获取当前活跃窗口列表
        Set<Long> activeWindowIds = getActiveWindowIds(pmiRecord);

        // 移除关闭的窗口
        activeWindowIds.remove(windowId);

        if (activeWindowIds.isEmpty()) {
            // 没有活跃窗口，恢复到原始计费模式
            switchToOriginalBilling(pmiRecordId);
        } else {
            // 还有其他活跃窗口，更新窗口列表和到期时间
            LocalDateTime latestExpireTime = calculateLatestExpireTime(pmiRecordId, activeWindowIds, null);
            pmiRecord.setWindowExpireTime(latestExpireTime);
            pmiRecord.setActiveWindowIds(serializeWindowIds(activeWindowIds));
            pmiRecordRepository.save(pmiRecord);

            log.info("PMI {} 关闭窗口 {}，还有 {} 个活跃窗口", pmiRecordId, windowId, activeWindowIds.size());
        }
    }

    /**
     * 兼容旧版本的方法
     */
    @Transactional
    public void switchToTimeBilling(Long pmiRecordId) {
        switchToOriginalBilling(pmiRecordId);
    }
    
    /**
     * 检查并处理过期的时间窗口
     * 修复：只有当所有关联的窗口都真正过期时，才切换计费模式
     */
    @Transactional
    public void processExpiredWindows() {
        LocalDateTime now = LocalDateTime.now();

        // 查找所有LONG模式的PMI记录
        var longModePmis = pmiRecordRepository.findByBillingMode(PmiRecord.BillingMode.LONG);

        int processedCount = 0;
        for (PmiRecord pmi : longModePmis) {
            try {
                // 检查PMI的所有活跃窗口是否真的都过期了
                boolean hasActiveWindows = checkIfPmiHasActiveWindows(pmi.getId());

                if (!hasActiveWindows) {
                    // 只有当PMI没有任何活跃窗口时，才切换到原始计费模式
                    switchToTimeBilling(pmi.getId());
                    log.info("PMI {} 所有窗口已过期，自动切换到按时长计费", pmi.getId());
                    processedCount++;
                } else {
                    // 如果还有活跃窗口，更新PMI的窗口信息
                    updatePmiWindowInfo(pmi.getId());
                    log.debug("PMI {} 仍有活跃窗口，更新窗口信息", pmi.getId());
                }
            } catch (Exception e) {
                log.error("处理PMI {} 窗口到期时发生错误", pmi.getId(), e);
            }
        }

        if (processedCount > 0) {
            log.info("处理了 {} 个真正过期的PMI窗口", processedCount);
        }
    }

    /**
     * 检查PMI是否还有活跃的窗口
     * @param pmiRecordId PMI记录ID
     * @return true如果有活跃窗口，false如果没有
     */
    private boolean checkIfPmiHasActiveWindows(Long pmiRecordId) {
        List<PmiScheduleWindow> activeWindows = windowRepository.findByPmiRecordIdAndStatus(
                pmiRecordId, PmiScheduleWindow.WindowStatus.ACTIVE);

        // 进一步检查这些"活跃"窗口是否真的还在有效期内
        LocalDateTime now = LocalDateTime.now();
        return activeWindows.stream().anyMatch(window -> {
            LocalDateTime windowExpireTime = window.getEndDateTime();
            return windowExpireTime != null && windowExpireTime.isAfter(now);
        });
    }
    
    /**
     * 检查PMI是否可以开启会议
     */
    public boolean canStartMeeting(Long pmiRecordId) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));
        
        // 检查计费状态
        if (pmiRecord.getBillingStatus() != PmiRecord.BillingStatus.ACTIVE) {
            log.warn("PMI {} 计费状态不是ACTIVE，无法开启会议", pmiRecordId);
            return false;
        }
        
        // 根据计费模式检查
        if (pmiRecord.getBillingMode() == PmiRecord.BillingMode.LONG) {
            return canStartMeetingForLongBilling(pmiRecord);
        } else {
            return canStartMeetingForTimeBilling(pmiRecord);
        }
    }
    
    /**
     * 检查按时段计费模式是否可以开启会议
     */
    private boolean canStartMeetingForLongBilling(PmiRecord pmiRecord) {
        // 检查时间窗口是否有效
        if (pmiRecord.getWindowExpireTime() == null) {
            log.warn("PMI {} 按时段计费但没有窗口到期时间", pmiRecord.getId());
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (pmiRecord.getWindowExpireTime().isBefore(now)) {
            log.warn("PMI {} 时间窗口已过期", pmiRecord.getId());
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查按时长计费模式是否可以开启会议
     */
    private boolean canStartMeetingForTimeBilling(PmiRecord pmiRecord) {
        // 检查是否有超额时长
        if (pmiRecord.getOverdraftMinutes() != null && pmiRecord.getOverdraftMinutes() > 0) {
            log.warn("PMI {} 有超额时长 {} 分钟，需要先结清", 
                    pmiRecord.getId(), pmiRecord.getOverdraftMinutes());
            return false;
        }
        
        // 检查可用时长
        if (pmiRecord.getAvailableMinutes() == null || pmiRecord.getAvailableMinutes() <= 0) {
            log.warn("PMI {} 可用时长不足", pmiRecord.getId());
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取PMI计费模式信息
     */
    public String getBillingModeInfo(Long pmiRecordId) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));
        
        StringBuilder info = new StringBuilder();
        info.append("计费模式: ").append(pmiRecord.getBillingMode().getDescription()).append("\n");
        info.append("计费状态: ").append(pmiRecord.getBillingStatus().getDescription()).append("\n");
        
        if (pmiRecord.getBillingMode() == PmiRecord.BillingMode.LONG) {
            info.append("窗口到期时间: ");
            if (pmiRecord.getWindowExpireTime() != null) {
                info.append(pmiRecord.getWindowExpireTime());
            } else {
                info.append("未设置");
            }
            info.append("\n");
        } else {
            info.append("总时长: ").append(pmiRecord.getTotalMinutes()).append(" 分钟\n");
            info.append("可用时长: ").append(pmiRecord.getAvailableMinutes()).append(" 分钟\n");
            info.append("待扣时长: ").append(pmiRecord.getPendingDeductMinutes()).append(" 分钟\n");
            info.append("超额时长: ").append(pmiRecord.getOverdraftMinutes()).append(" 分钟\n");
            info.append("总使用时长: ").append(pmiRecord.getTotalUsedMinutes()).append(" 分钟\n");
        }
        
        return info.toString();
    }
    
    /**
     * 强制切换计费模式（管理员操作）
     */
    @Transactional
    public void forceSwitchBillingMode(Long pmiRecordId, PmiRecord.BillingMode targetMode, String reason) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));
        
        PmiRecord.BillingMode originalMode = pmiRecord.getBillingMode();
        
        if (targetMode == PmiRecord.BillingMode.LONG) {
            // 切换到按时段计费，但不设置窗口信息
            pmiRecord.setBillingMode(PmiRecord.BillingMode.LONG);
            pmiRecord.setCurrentWindowId(null);
            pmiRecord.setWindowExpireTime(null);
        } else {
            // 切换到按时长计费
            pmiRecord.setBillingMode(PmiRecord.BillingMode.BY_TIME);
            pmiRecord.setCurrentWindowId(null);
            pmiRecord.setWindowExpireTime(null);
        }
        
        pmiRecordRepository.save(pmiRecord);
        
        log.warn("管理员强制切换PMI {} 计费模式：{} -> {}，原因：{}",
                pmiRecordId, originalMode, targetMode, reason);
    }

    /**
     * 处理活跃会议的计费模式切换
     */
    private void handleActiveMeetingsBillingModeChange(Long pmiRecordId,
                                                      PmiRecord.BillingMode fromMode,
                                                      PmiRecord.BillingMode toMode) {
        // 查找该PMI的所有活跃会议
        List<ZoomMeeting> activeMeetings = zoomMeetingRepository.findByPmiRecordIdAndStatus(
                pmiRecordId, ZoomMeeting.MeetingStatus.STARTED);

        if (activeMeetings.isEmpty()) {
            log.debug("PMI {} 没有活跃会议，无需处理计费状态切换", pmiRecordId);
            return;
        }

        log.info("PMI {} 有 {} 个活跃会议需要处理计费模式切换: {} -> {}",
                pmiRecordId, activeMeetings.size(), fromMode, toMode);

        for (ZoomMeeting meeting : activeMeetings) {
            // 更新会议的计费模式
            meeting.setBillingMode(toMode);
            zoomMeetingRepository.save(meeting);

            // 根据计费模式切换处理计费监控
            if (fromMode == PmiRecord.BillingMode.BY_TIME && toMode == PmiRecord.BillingMode.LONG) {
                // 从BY_TIME切换到LONG：暂停计费监控
                billingMonitorService.stopBillingMonitor(meeting.getId());
                log.info("会议 {} 从BY_TIME切换到LONG，暂停计费监控", meeting.getId());

            } else if (fromMode == PmiRecord.BillingMode.LONG && toMode == PmiRecord.BillingMode.BY_TIME) {
                // 从LONG切换到BY_TIME：启动或恢复计费监控
                billingMonitorService.startBillingMonitor(meeting.getId());
                log.info("会议 {} 从LONG切换到BY_TIME，启动计费监控", meeting.getId());

            } else if (fromMode == PmiRecord.BillingMode.FREE && toMode == PmiRecord.BillingMode.BY_TIME) {
                // 从FREE切换到BY_TIME：启动计费监控
                billingMonitorService.startBillingMonitor(meeting.getId());
                log.info("会议 {} 从FREE切换到BY_TIME，启动计费监控", meeting.getId());

            } else if (toMode == PmiRecord.BillingMode.FREE) {
                // 切换到FREE：停止计费监控
                billingMonitorService.stopBillingMonitor(meeting.getId());
                log.info("会议 {} 切换到FREE模式，停止计费监控", meeting.getId());
            }
        }

        log.info("完成PMI {} 的 {} 个活跃会议计费模式切换处理", pmiRecordId, activeMeetings.size());
    }

    /**
     * 获取活跃窗口ID列表
     */
    private Set<Long> getActiveWindowIds(PmiRecord pmiRecord) {
        String activeWindowIdsStr = pmiRecord.getActiveWindowIds();
        if (activeWindowIdsStr == null || activeWindowIdsStr.trim().isEmpty()) {
            return new HashSet<>();
        }

        try {
            TypeReference<Set<Long>> typeRef = new TypeReference<Set<Long>>() {};
            return objectMapper.readValue(activeWindowIdsStr, typeRef);
        } catch (Exception e) {
            log.warn("解析活跃窗口ID列表失败: {}, 返回空列表", activeWindowIdsStr, e);
            return new HashSet<>();
        }
    }

    /**
     * 序列化窗口ID列表
     */
    private String serializeWindowIds(Set<Long> windowIds) {
        if (windowIds == null || windowIds.isEmpty()) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(windowIds);
        } catch (Exception e) {
            log.error("序列化窗口ID列表失败: {}", windowIds, e);
            return null;
        }
    }

    /**
     * 计算最晚的到期时间
     */
    private LocalDateTime calculateLatestExpireTime(Long pmiRecordId, Set<Long> activeWindowIds, LocalDateTime newExpireTime) {
        if (activeWindowIds.isEmpty()) {
            return newExpireTime;
        }

        // 查询所有活跃窗口
        List<PmiScheduleWindow> activeWindows = windowRepository.findAllById(activeWindowIds);

        // 计算所有窗口的到期时间，取最晚的
        LocalDateTime latestExpireTime = newExpireTime;

        for (PmiScheduleWindow window : activeWindows) {
            LocalDateTime windowExpireTime = window.getEndDateTime();

            if (windowExpireTime != null && (latestExpireTime == null || windowExpireTime.isAfter(latestExpireTime))) {
                latestExpireTime = windowExpireTime;
            }
        }

        return latestExpireTime;
    }

    /**
     * 更新PMI记录的窗口信息
     * 用于维护 current_window_id 和 window_expire_time 字段
     */
    @Transactional
    public void updatePmiWindowInfo(Long pmiRecordId) {
        log.debug("更新PMI {} 的窗口信息", pmiRecordId);

        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
                .orElseThrow(() -> new RuntimeException("PMI记录不存在: " + pmiRecordId));

        if (pmiRecord.getBillingMode() != PmiRecord.BillingMode.LONG) {
            // 非LONG模式，清空窗口信息
            pmiRecord.setCurrentWindowId(null);
            pmiRecord.setWindowExpireTime(null);
            pmiRecord.setActiveWindowIds(null);
            pmiRecordRepository.save(pmiRecord);
            return;
        }

        // 查找当前活跃的窗口
        List<PmiScheduleWindow> activeWindows = windowRepository.findByPmiRecordIdAndStatus(
                pmiRecordId, PmiScheduleWindow.WindowStatus.ACTIVE);

        if (activeWindows.isEmpty()) {
            // 没有活跃窗口，清空相关字段
            pmiRecord.setCurrentWindowId(null);
            pmiRecord.setWindowExpireTime(null);
            pmiRecord.setActiveWindowIds(null);
            log.info("PMI {} 没有活跃窗口，已清空窗口信息", pmiRecordId);
        } else {
            // 选择最新激活的窗口作为当前窗口
            PmiScheduleWindow currentWindow = activeWindows.stream()
                    .max((w1, w2) -> {
                        if (w1.getActualStartTime() != null && w2.getActualStartTime() != null) {
                            return w1.getActualStartTime().compareTo(w2.getActualStartTime());
                        } else if (w1.getActualStartTime() != null) {
                            return 1;
                        } else if (w2.getActualStartTime() != null) {
                            return -1;
                        } else {
                            return w1.getCreatedAt().compareTo(w2.getCreatedAt());
                        }
                    })
                    .orElse(activeWindows.get(0));

            // 计算窗口到期时间
            LocalDateTime expireTime = currentWindow.getEndDateTime();

            // 更新PMI记录
            pmiRecord.setCurrentWindowId(currentWindow.getId());
            pmiRecord.setWindowExpireTime(expireTime);

            // 更新活跃窗口ID列表
            Set<Long> activeWindowIds = activeWindows.stream()
                    .map(PmiScheduleWindow::getId)
                    .collect(Collectors.toSet());
            pmiRecord.setActiveWindowIds(serializeWindowIds(activeWindowIds));

            log.info("PMI {} 窗口信息已更新，当前窗口: {}, 到期时间: {}",
                    pmiRecordId, currentWindow.getId(), expireTime);
        }

        pmiRecordRepository.save(pmiRecord);
    }

    /**
     * 批量更新所有LONG模式PMI的窗口信息
     */
    @Transactional
    public void updateAllLongModePmiWindowInfo() {
        log.info("开始批量更新所有LONG模式PMI的窗口信息");

        List<PmiRecord> longModePmis = pmiRecordRepository.findByBillingMode(PmiRecord.BillingMode.LONG);

        int updatedCount = 0;
        for (PmiRecord pmiRecord : longModePmis) {
            try {
                updatePmiWindowInfo(pmiRecord.getId());
                updatedCount++;
            } catch (Exception e) {
                log.error("更新PMI {} 窗口信息失败", pmiRecord.getId(), e);
            }
        }

        log.info("批量更新完成，总计: {}, 成功: {}", longModePmis.size(), updatedCount);
    }
}
