package com.zoombus.service;

import com.zoombus.entity.PmiBillingRecord;
import com.zoombus.entity.PmiRecord;
import com.zoombus.exception.ResourceNotFoundException;
import com.zoombus.repository.PmiBillingRecordRepository;
import com.zoombus.repository.PmiRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * PMI充值服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiRechargeService {
    
    private final PmiRecordRepository pmiRecordRepository;
    private final PmiBillingRecordRepository billingRecordRepository;
    
    /**
     * PMI充值
     */
    @Transactional
    public RechargeResult rechargePmi(Long pmiRecordId, int rechargeMinutes, String description) {
        if (rechargeMinutes <= 0) {
            throw new IllegalArgumentException("充值时长必须大于0");
        }
        
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));
        
        if (pmiRecord.getBillingMode() != PmiRecord.BillingMode.BY_TIME) {
            throw new IllegalStateException("只有按时长计费的PMI才能充值");
        }
        
        // 计算充值分配
        RechargeAllocation allocation = calculateRechargeAllocation(pmiRecord, rechargeMinutes);
        
        // 执行充值
        executeRecharge(pmiRecord, allocation);
        
        // 创建充值记录
        PmiBillingRecord record = createRechargeRecord(pmiRecord, rechargeMinutes, 
            allocation.getBalanceBefore(), allocation.getBalanceAfter(), description);
        
        log.info("PMI {} 充值成功: {} 分钟, 分配: {}", pmiRecordId, rechargeMinutes, allocation);
        
        return new RechargeResult(true, "充值成功", allocation, record);
    }
    
    /**
     * 计算充值分配策略
     */
    private RechargeAllocation calculateRechargeAllocation(PmiRecord pmiRecord, int rechargeMinutes) {
        int overdraft = pmiRecord.getOverdraftMinutes() != null ? pmiRecord.getOverdraftMinutes() : 0;
        int pendingDeduct = pmiRecord.getPendingDeductMinutes() != null ? pmiRecord.getPendingDeductMinutes() : 0;
        int currentAvailable = pmiRecord.getAvailableMinutes() != null ? pmiRecord.getAvailableMinutes() : 0;
        int currentTotal = pmiRecord.getTotalMinutes() != null ? pmiRecord.getTotalMinutes() : 0;
        
        int remainingRecharge = rechargeMinutes;
        int overdraftSettled = 0;
        int pendingDeductSettled = 0;
        int actualAdded = 0;
        
        // 优先结清超额时长
        if (overdraft > 0 && remainingRecharge > 0) {
            overdraftSettled = Math.min(remainingRecharge, overdraft);
            remainingRecharge -= overdraftSettled;
        }
        
        // 再结清待扣时长
        if (pendingDeduct > 0 && remainingRecharge > 0) {
            pendingDeductSettled = Math.min(remainingRecharge, pendingDeduct);
            remainingRecharge -= pendingDeductSettled;
        }
        
        // 剩余的加入可用时长
        if (remainingRecharge > 0) {
            actualAdded = remainingRecharge;
        }
        
        int finalAvailable = currentAvailable + actualAdded;
        int finalTotal = currentTotal + actualAdded;
        int finalOverdraft = overdraft - overdraftSettled;
        int finalPendingDeduct = pendingDeduct - pendingDeductSettled;
        
        return new RechargeAllocation(
            rechargeMinutes, overdraftSettled, pendingDeductSettled, actualAdded,
            currentAvailable, finalAvailable, currentTotal, finalTotal,
            overdraft, finalOverdraft, pendingDeduct, finalPendingDeduct
        );
    }
    
    /**
     * 执行充值操作
     */
    private void executeRecharge(PmiRecord pmiRecord, RechargeAllocation allocation) {
        // 更新PMI记录
        pmiRecord.setAvailableMinutes(allocation.getFinalAvailable());
        pmiRecord.setTotalMinutes(allocation.getFinalTotal());
        pmiRecord.setOverdraftMinutes(allocation.getFinalOverdraft());
        pmiRecord.setPendingDeductMinutes(allocation.getFinalPendingDeduct());
        pmiRecord.setLastBillingTime(LocalDateTime.now());

        // 检查并激活PMI状态：如果可用时长大于0则激活PMI为活跃状态
        checkAndActivatePmi(pmiRecord, allocation.getFinalAvailable(), "充值后");

        pmiRecordRepository.save(pmiRecord);
    }
    
    /**
     * 创建充值记录
     */
    private PmiBillingRecord createRechargeRecord(PmiRecord pmiRecord, int rechargeMinutes, 
                                                 int balanceBefore, int balanceAfter, String description) {
        PmiBillingRecord record = new PmiBillingRecord();
        record.setPmiRecordId(pmiRecord.getId());
        record.setUserId(pmiRecord.getUserId());
        record.setTransactionType(PmiBillingRecord.TransactionType.RECHARGE);
        record.setAmountMinutes(rechargeMinutes);
        record.setBalanceBefore(balanceBefore);
        record.setBalanceAfter(balanceAfter);
        record.setDescription(description != null ? description : String.format("充值 %d 分钟", rechargeMinutes));
        record.setStatus(PmiBillingRecord.RecordStatus.COMPLETED);
        record.setCreatedBy("USER");
        
        return billingRecordRepository.save(record);
    }

    /**
     * 检查并激活PMI状态
     * 如果可用时长大于0且当前状态不是ACTIVE，则激活PMI为活跃状态
     */
    private void checkAndActivatePmi(PmiRecord pmiRecord, int availableMinutes, String context) {
        if (availableMinutes > 0 && pmiRecord.getStatus() != PmiRecord.PmiStatus.ACTIVE) {
            PmiRecord.PmiStatus oldStatus = pmiRecord.getStatus();
            pmiRecord.setStatus(PmiRecord.PmiStatus.ACTIVE);
            log.info("PMI {} {}可用时长大于0({} 分钟)，激活PMI状态：{} -> ACTIVE",
                    pmiRecord.getId(), context, availableMinutes, oldStatus);
        }
    }

    /**
     * 获取充值预览
     */
    public RechargePreview getRechargePreview(Long pmiRecordId, int rechargeMinutes) {
        if (rechargeMinutes <= 0) {
            throw new IllegalArgumentException("充值时长必须大于0");
        }
        
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));
        
        if (pmiRecord.getBillingMode() != PmiRecord.BillingMode.BY_TIME) {
            throw new IllegalStateException("只有按时长计费的PMI才能充值");
        }
        
        RechargeAllocation allocation = calculateRechargeAllocation(pmiRecord, rechargeMinutes);
        
        return new RechargePreview(
            pmiRecord.getId(),
            rechargeMinutes,
            allocation,
            allocation.getFinalOverdraft() == 0 // 是否可以开启会议
        );
    }
    
    /**
     * 充值分配策略类
     */
    public static class RechargeAllocation {
        private final int rechargeAmount;
        private final int overdraftSettled;
        private final int pendingDeductSettled;
        private final int actualAdded;
        
        private final int balanceBefore;
        private final int balanceAfter;
        private final int totalBefore;
        private final int totalAfter;
        
        private final int overdraftBefore;
        private final int overdraftAfter;
        private final int pendingDeductBefore;
        private final int pendingDeductAfter;
        
        public RechargeAllocation(int rechargeAmount, int overdraftSettled, int pendingDeductSettled, int actualAdded,
                                int balanceBefore, int balanceAfter, int totalBefore, int totalAfter,
                                int overdraftBefore, int overdraftAfter, int pendingDeductBefore, int pendingDeductAfter) {
            this.rechargeAmount = rechargeAmount;
            this.overdraftSettled = overdraftSettled;
            this.pendingDeductSettled = pendingDeductSettled;
            this.actualAdded = actualAdded;
            this.balanceBefore = balanceBefore;
            this.balanceAfter = balanceAfter;
            this.totalBefore = totalBefore;
            this.totalAfter = totalAfter;
            this.overdraftBefore = overdraftBefore;
            this.overdraftAfter = overdraftAfter;
            this.pendingDeductBefore = pendingDeductBefore;
            this.pendingDeductAfter = pendingDeductAfter;
        }
        
        // Getters
        public int getRechargeAmount() { return rechargeAmount; }
        public int getOverdraftSettled() { return overdraftSettled; }
        public int getPendingDeductSettled() { return pendingDeductSettled; }
        public int getActualAdded() { return actualAdded; }
        public int getBalanceBefore() { return balanceBefore; }
        public int getBalanceAfter() { return balanceAfter; }
        public int getTotalBefore() { return totalBefore; }
        public int getTotalAfter() { return totalAfter; }
        public int getOverdraftBefore() { return overdraftBefore; }
        public int getOverdraftAfter() { return overdraftAfter; }
        public int getPendingDeductBefore() { return pendingDeductBefore; }
        public int getPendingDeductAfter() { return pendingDeductAfter; }
        
        public int getFinalAvailable() { return balanceAfter; }
        public int getFinalTotal() { return totalAfter; }
        public int getFinalOverdraft() { return overdraftAfter; }
        public int getFinalPendingDeduct() { return pendingDeductAfter; }
        
        @Override
        public String toString() {
            return String.format("充值%d分钟[超额结清:%d, 待扣结清:%d, 实际增加:%d]", 
                rechargeAmount, overdraftSettled, pendingDeductSettled, actualAdded);
        }
    }
    
    /**
     * 充值预览类
     */
    public static class RechargePreview {
        private final Long pmiRecordId;
        private final int rechargeMinutes;
        private final RechargeAllocation allocation;
        private final boolean canStartMeeting;
        
        public RechargePreview(Long pmiRecordId, int rechargeMinutes, RechargeAllocation allocation, boolean canStartMeeting) {
            this.pmiRecordId = pmiRecordId;
            this.rechargeMinutes = rechargeMinutes;
            this.allocation = allocation;
            this.canStartMeeting = canStartMeeting;
        }
        
        public Long getPmiRecordId() { return pmiRecordId; }
        public int getRechargeMinutes() { return rechargeMinutes; }
        public RechargeAllocation getAllocation() { return allocation; }
        public boolean isCanStartMeeting() { return canStartMeeting; }
    }
    
    /**
     * 充值结果类
     */
    public static class RechargeResult {
        private final boolean success;
        private final String message;
        private final RechargeAllocation allocation;
        private final PmiBillingRecord billingRecord;
        
        public RechargeResult(boolean success, String message, RechargeAllocation allocation, PmiBillingRecord billingRecord) {
            this.success = success;
            this.message = message;
            this.allocation = allocation;
            this.billingRecord = billingRecord;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public RechargeAllocation getAllocation() { return allocation; }
        public PmiBillingRecord getBillingRecord() { return billingRecord; }
    }
}
