package com.zoombus.service;

import com.zoombus.entity.MeetingReport;
import com.zoombus.entity.PmiMeetingStats;
import com.zoombus.entity.PmiRecord;
import com.zoombus.repository.MeetingReportRepository;
import com.zoombus.repository.PmiMeetingStatsRepository;
import com.zoombus.repository.PmiRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PMI会议报告服务
 * 提供PMI级别的会议报告聚合和统计功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiReportService {

    private final MeetingReportRepository meetingReportRepository;
    private final PmiMeetingStatsRepository pmiMeetingStatsRepository;
    private final PmiRecordRepository pmiRecordRepository;

    /**
     * 获取指定PMI的所有会议报告
     */
    public List<MeetingReport> getPmiMeetingReports(String pmiNumber) {
        log.debug("获取PMI会议报告: pmiNumber={}", pmiNumber);
        return meetingReportRepository.findByPmiNumber(pmiNumber);
    }

    /**
     * 分页获取指定PMI的会议报告
     */
    public Page<MeetingReport> getPmiMeetingReports(String pmiNumber, Pageable pageable) {
        log.debug("分页获取PMI会议报告: pmiNumber={}, page={}", pmiNumber, pageable.getPageNumber());
        return meetingReportRepository.findByPmiNumber(pmiNumber, pageable);
    }

    /**
     * 获取PMI会议统计信息
     */
    public Optional<PmiMeetingStats> getPmiMeetingStats(String pmiNumber) {
        log.debug("获取PMI会议统计: pmiNumber={}", pmiNumber);
        return pmiMeetingStatsRepository.findByPmiNumber(pmiNumber);
    }

    /**
     * 获取PMI会议时间线（按时间排序的会议列表）
     */
    public List<MeetingReport> getPmiMeetingTimeline(String pmiNumber) {
        log.debug("获取PMI会议时间线: pmiNumber={}", pmiNumber);
        List<MeetingReport> reports = meetingReportRepository.findByPmiNumber(pmiNumber);
        return reports.stream()
                .filter(report -> report.getFetchStatus() == MeetingReport.FetchStatus.SUCCESS)
                .sorted((r1, r2) -> r2.getStartTime().compareTo(r1.getStartTime()))
                .collect(Collectors.toList());
    }

    /**
     * 获取指定时间范围内PMI的会议报告
     */
    public List<MeetingReport> getPmiMeetingReportsInTimeRange(String pmiNumber, 
                                                              LocalDateTime startTime, 
                                                              LocalDateTime endTime) {
        log.debug("获取PMI时间范围内会议报告: pmiNumber={}, startTime={}, endTime={}", 
                 pmiNumber, startTime, endTime);
        return meetingReportRepository.findByPmiNumberAndTimeRange(pmiNumber, startTime, endTime);
    }

    /**
     * 获取PMI详细统计信息（包含计算统计）
     */
    public Map<String, Object> getPmiDetailedStats(String pmiNumber) {
        log.debug("获取PMI详细统计: pmiNumber={}", pmiNumber);
        
        // 从数据库获取统计信息
        Object[] stats = meetingReportRepository.getPmiStatistics(pmiNumber);
        
        Map<String, Object> result = Map.of(
            "pmiNumber", pmiNumber,
            "totalMeetings", stats[0] != null ? stats[0] : 0,
            "totalDuration", stats[1] != null ? stats[1] : 0,
            "totalParticipants", stats[2] != null ? stats[2] : 0,
            "avgDuration", stats[3] != null ? stats[3] : 0.0,
            "avgParticipants", stats[4] != null ? stats[4] : 0.0,
            "firstMeeting", stats[5],
            "lastMeeting", stats[6]
        );
        
        log.debug("PMI统计结果: {}", result);
        return result;
    }

    /**
     * 获取最活跃的PMI列表
     */
    public Page<PmiMeetingStats> getMostActivePmis(Pageable pageable) {
        log.debug("获取最活跃PMI列表: page={}", pageable.getPageNumber());
        return pmiMeetingStatsRepository.findMostActivePmis(pageable);
    }

    /**
     * 获取使用时长最长的PMI列表
     */
    public Page<PmiMeetingStats> getLongestUsedPmis(Pageable pageable) {
        log.debug("获取使用时长最长PMI列表: page={}", pageable.getPageNumber());
        return pmiMeetingStatsRepository.findLongestUsedPmis(pageable);
    }

    /**
     * 获取指定Zoom主账号下的PMI统计
     */
    public List<PmiMeetingStats> getPmiStatsByZoomAuth(Long zoomAuthId) {
        log.debug("获取Zoom主账号PMI统计: zoomAuthId={}", zoomAuthId);
        return pmiMeetingStatsRepository.findByMostUsedZoomAuthId(zoomAuthId);
    }

    /**
     * 获取最近活跃的PMI
     */
    public List<PmiMeetingStats> getRecentlyActivePmis(int days) {
        LocalDateTime sinceTime = LocalDateTime.now().minusDays(days);
        log.debug("获取最近活跃PMI: days={}, sinceTime={}", days, sinceTime);
        return pmiMeetingStatsRepository.findRecentlyActivePmis(sinceTime);
    }

    /**
     * 获取长时间未使用的PMI
     */
    public List<PmiMeetingStats> getInactivePmis(int days) {
        LocalDateTime beforeTime = LocalDateTime.now().minusDays(days);
        log.debug("获取长时间未使用PMI: days={}, beforeTime={}", days, beforeTime);
        return pmiMeetingStatsRepository.findInactivePmis(beforeTime);
    }

    /**
     * 获取PMI使用趋势数据
     */
    public List<Object[]> getPmiUsageTrend(int months) {
        LocalDateTime startTime = LocalDateTime.now().minusMonths(months);
        log.debug("获取PMI使用趋势: months={}, startTime={}", months, startTime);
        return pmiMeetingStatsRepository.getPmiUsageTrend(startTime);
    }

    /**
     * 手动更新PMI统计数据
     */
    @Transactional
    public void updatePmiStats(String pmiNumber) {
        log.info("手动更新PMI统计: pmiNumber={}", pmiNumber);
        
        try {
            // 获取PMI的所有成功会议报告
            List<MeetingReport> reports = meetingReportRepository.findByPmiNumber(pmiNumber)
                    .stream()
                    .filter(report -> report.getFetchStatus() == MeetingReport.FetchStatus.SUCCESS)
                    .collect(Collectors.toList());
            
            if (reports.isEmpty()) {
                log.warn("PMI没有成功的会议报告: pmiNumber={}", pmiNumber);
                return;
            }
            
            // 计算统计数据
            int totalMeetings = reports.size();
            int totalDuration = reports.stream().mapToInt(r -> r.getDurationMinutes() != null ? r.getDurationMinutes() : 0).sum();
            int totalParticipants = reports.stream().mapToInt(r -> r.getTotalParticipants() != null ? r.getTotalParticipants() : 0).sum();
            
            LocalDateTime firstMeeting = reports.stream()
                    .map(MeetingReport::getStartTime)
                    .filter(time -> time != null)
                    .min(LocalDateTime::compareTo)
                    .orElse(null);
            
            LocalDateTime lastMeeting = reports.stream()
                    .map(MeetingReport::getStartTime)
                    .filter(time -> time != null)
                    .max(LocalDateTime::compareTo)
                    .orElse(null);
            
            // 找到最常用的Zoom主账号
            Long mostUsedZoomAuthId = reports.stream()
                    .filter(r -> r.getZoomAuthId() != null)
                    .collect(Collectors.groupingBy(MeetingReport::getZoomAuthId, Collectors.counting()))
                    .entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse(null);
            
            // 获取或创建统计记录
            PmiMeetingStats stats = pmiMeetingStatsRepository.findByPmiNumber(pmiNumber)
                    .orElse(new PmiMeetingStats());
            
            // 更新统计数据
            stats.setPmiNumber(pmiNumber);
            stats.setTotalMeetings(totalMeetings);
            stats.setTotalDurationMinutes(totalDuration);
            stats.setTotalParticipants(totalParticipants);
            stats.setAvgDurationMinutes(stats.calculateAvgDuration());
            stats.setAvgParticipants(stats.calculateAvgParticipants());
            stats.setFirstMeetingTime(firstMeeting);
            stats.setLastMeetingTime(lastMeeting);
            stats.setMostUsedZoomAuthId(mostUsedZoomAuthId);
            
            // 关联PMI记录
            if (stats.getPmiRecordId() == null) {
                Optional<PmiRecord> pmiRecord = pmiRecordRepository.findByPmiNumber(pmiNumber);
                pmiRecord.ifPresent(record -> stats.setPmiRecordId(record.getId()));
            }
            
            pmiMeetingStatsRepository.save(stats);
            
            log.info("PMI统计更新完成: pmiNumber={}, totalMeetings={}, totalDuration={}", 
                    pmiNumber, totalMeetings, totalDuration);
            
        } catch (Exception e) {
            log.error("更新PMI统计失败: pmiNumber={}", pmiNumber, e);
            throw new RuntimeException("更新PMI统计失败", e);
        }
    }

    /**
     * 批量更新所有PMI统计数据
     */
    @Transactional
    public void updateAllPmiStats() {
        log.info("开始批量更新所有PMI统计数据");
        
        try {
            // 获取所有有会议报告的PMI号码
            List<String> pmiNumbers = meetingReportRepository.findAll()
                    .stream()
                    .map(MeetingReport::getPmiNumber)
                    .filter(pmiNumber -> pmiNumber != null && !pmiNumber.trim().isEmpty())
                    .distinct()
                    .collect(Collectors.toList());
            
            log.info("发现{}个PMI需要更新统计", pmiNumbers.size());
            
            for (String pmiNumber : pmiNumbers) {
                try {
                    updatePmiStats(pmiNumber);
                } catch (Exception e) {
                    log.error("更新PMI统计失败: pmiNumber={}", pmiNumber, e);
                    // 继续处理其他PMI
                }
            }
            
            log.info("批量更新PMI统计完成");
            
        } catch (Exception e) {
            log.error("批量更新PMI统计失败", e);
            throw new RuntimeException("批量更新PMI统计失败", e);
        }
    }

    /**
     * 获取PMI统计概览
     */
    public Map<String, Object> getPmiStatsOverview() {
        log.info("获取PMI统计概览");

        try {
            Object[] queryResult = pmiMeetingStatsRepository.getOverviewStats();
            log.info("查询结果: {}", Arrays.toString(queryResult));

            if (queryResult == null || queryResult.length == 0) {
                log.warn("查询结果为null或空，返回默认值");
                return Map.of(
                    "totalPmis", 0,
                    "totalMeetings", 0,
                    "totalDurationMinutes", 0,
                    "totalParticipants", 0,
                    "avgDurationMinutes", 0.0,
                    "avgParticipants", 0.0
                );
            }

            // Repository返回的是包含Object[]的数组，需要取第一个元素
            Object[] overviewStats = (Object[]) queryResult[0];
            log.info("处理查询结果，实际数据数组长度: {}", overviewStats.length);

            // 安全地转换数值类型
            Long totalPmis = convertToLong(overviewStats[0]);
            Long totalMeetings = convertToLong(overviewStats[1]);
            Long totalDurationMinutes = convertToLong(overviewStats[2]);
            Long totalParticipants = convertToLong(overviewStats[3]);
            Double avgDurationMinutes = convertToDouble(overviewStats[4]);
            Double avgParticipants = convertToDouble(overviewStats[5]);

            Map<String, Object> result = Map.of(
                "totalPmis", totalPmis,
                "totalMeetings", totalMeetings,
                "totalDurationMinutes", totalDurationMinutes,
                "totalParticipants", totalParticipants,
                "avgDurationMinutes", avgDurationMinutes,
                "avgParticipants", avgParticipants
            );

            log.info("返回结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("获取PMI统计概览失败", e);
            throw new RuntimeException("获取PMI统计概览失败", e);
        }
    }

    private Long convertToLong(Object value) {
        if (value == null) return 0L;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Integer) return ((Integer) value).longValue();
        if (value instanceof BigInteger) return ((BigInteger) value).longValue();
        if (value instanceof BigDecimal) return ((BigDecimal) value).longValue();
        return Long.valueOf(value.toString());
    }

    private Double convertToDouble(Object value) {
        if (value == null) return 0.0;
        if (value instanceof Double) return (Double) value;
        if (value instanceof Float) return ((Float) value).doubleValue();
        if (value instanceof BigDecimal) return ((BigDecimal) value).doubleValue();
        return Double.valueOf(value.toString());
    }

    /**
     * 检查PMI统计数据一致性
     */
    public List<Map<String, Object>> checkPmiStatsConsistency() {
        log.info("检查PMI统计数据一致性");

        // 这里可以调用存储过程或者实现Java版本的一致性检查
        // 暂时返回空列表，后续可以完善
        return List.of();
    }

    /**
     * 获取PMI概览数据
     */
    public Map<String, Object> getPmiOverview() {
        log.info("获取PMI概览数据");

        try {
            // 获取基本统计数据
            long totalPmis = pmiMeetingStatsRepository.count();
            long totalMeetings = meetingReportRepository.countByPmiNumberIsNotNull();
            long activePmis = pmiRecordRepository.countByStatus(PmiRecord.PmiStatus.ACTIVE);

            // 获取最近30天的会议数据
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            List<MeetingReport> recentMeetings = meetingReportRepository
                    .findByPmiNumberIsNotNullAndStartTimeBetween(thirtyDaysAgo, LocalDateTime.now());

            // 计算总时长
            int totalDurationMinutes = recentMeetings.stream()
                    .mapToInt(m -> m.getDurationMinutes() != null ? m.getDurationMinutes() : 0)
                    .sum();

            // 计算总参与人数
            int totalParticipants = recentMeetings.stream()
                    .mapToInt(MeetingReport::getParticipantCount)
                    .sum();

            // 获取最活跃的PMI（最近30天）
            Map<String, Long> pmiMeetingCounts = recentMeetings.stream()
                    .filter(m -> m.getPmiNumber() != null)
                    .collect(Collectors.groupingBy(
                            MeetingReport::getPmiNumber,
                            Collectors.counting()
                    ));

            String mostActivePmi = pmiMeetingCounts.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse(null);

            Map<String, Object> result = new HashMap<>();
            result.put("totalPmis", totalPmis);
            result.put("activePmis", activePmis);
            result.put("totalMeetings", totalMeetings);
            result.put("recentMeetings30Days", recentMeetings.size());
            result.put("totalDurationMinutes30Days", totalDurationMinutes);
            result.put("totalParticipants30Days", totalParticipants);
            result.put("avgMeetingDuration", recentMeetings.isEmpty() ? 0 : totalDurationMinutes / recentMeetings.size());
            result.put("avgParticipantsPerMeeting", recentMeetings.isEmpty() ? 0 : totalParticipants / recentMeetings.size());
            result.put("mostActivePmi", mostActivePmi != null ? mostActivePmi : "");
            result.put("mostActivePmiMeetings", mostActivePmi != null ? pmiMeetingCounts.get(mostActivePmi) : 0);

            return result;

        } catch (Exception e) {
            log.error("获取PMI概览数据失败", e);
            throw new RuntimeException("获取PMI概览数据失败", e);
        }
    }

    /**
     * 获取特定PMI的详细信息
     */
    public Map<String, Object> getPmiDetails(String pmiNumber) {
        log.info("获取PMI详细信息: {}", pmiNumber);

        try {
            // 获取PMI统计数据
            Optional<PmiMeetingStats> statsOpt = pmiMeetingStatsRepository.findByPmiNumber(pmiNumber);
            if (statsOpt.isEmpty()) {
                return Map.of();
            }

            PmiMeetingStats stats = statsOpt.get();

            // 获取PMI记录信息
            Map<String, Object> pmiInfo = new HashMap<>();
            if (stats.getPmiRecordId() != null) {
                Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(stats.getPmiRecordId());
                if (pmiRecordOpt.isPresent()) {
                    PmiRecord pmiRecord = pmiRecordOpt.get();
                    pmiInfo.put("id", pmiRecord.getId());
                    pmiInfo.put("status", pmiRecord.getStatus());
                    pmiInfo.put("createdAt", pmiRecord.getCreatedAt());
                    pmiInfo.put("updatedAt", pmiRecord.getUpdatedAt());
                }
            }

            // 获取最近的会议
            List<MeetingReport> recentMeetings = meetingReportRepository
                    .findByPmiNumberOrderByStartTimeDesc(pmiNumber, PageRequest.of(0, 5))
                    .getContent();

            Map<String, Object> result = new HashMap<>();
            result.put("pmiNumber", pmiNumber);

            Map<String, Object> statsMap = new HashMap<>();
            statsMap.put("totalMeetings", stats.getTotalMeetings());
            statsMap.put("totalDurationMinutes", stats.getTotalDurationMinutes());
            statsMap.put("totalParticipants", stats.getTotalParticipants());
            statsMap.put("avgDurationMinutes", stats.getAvgDurationMinutes());
            statsMap.put("avgParticipants", stats.getAvgParticipants());
            statsMap.put("firstMeetingTime", stats.getFirstMeetingTime());
            statsMap.put("lastMeetingTime", stats.getLastMeetingTime());
            statsMap.put("mostUsedZoomAuthId", stats.getMostUsedZoomAuthId());
            result.put("stats", statsMap);

            result.put("pmiInfo", pmiInfo);

            List<Map<String, Object>> recentMeetingsList = recentMeetings.stream().map(meeting -> {
                Map<String, Object> meetingMap = new HashMap<>();
                meetingMap.put("id", meeting.getId());
                meetingMap.put("topic", meeting.getTopic());
                meetingMap.put("startTime", meeting.getStartTime());
                meetingMap.put("endTime", meeting.getEndTime());
                meetingMap.put("duration", meeting.getDurationMinutes() != null ? meeting.getDurationMinutes() : 0);
                meetingMap.put("participantCount", meeting.getParticipantCount());
                return meetingMap;
            }).collect(Collectors.toList());
            result.put("recentMeetings", recentMeetingsList);

            return result;

        } catch (Exception e) {
            log.error("获取PMI详细信息失败: {}", pmiNumber, e);
            throw new RuntimeException("获取PMI详细信息失败", e);
        }
    }

    /**
     * 获取特定PMI的会议历史
     */
    public Map<String, Object> getPmiMeetings(String pmiNumber, Pageable pageable) {
        log.info("获取PMI会议历史: {}", pmiNumber);

        try {
            Page<MeetingReport> meetings = meetingReportRepository
                    .findByPmiNumberOrderByStartTimeDesc(pmiNumber, pageable);

            return Map.of(
                "content", meetings.getContent().stream().map(meeting -> Map.of(
                    "id", meeting.getId(),
                    "topic", meeting.getTopic(),
                    "startTime", meeting.getStartTime(),
                    "endTime", meeting.getEndTime(),
                    "duration", meeting.getDurationMinutes() != null ? meeting.getDurationMinutes() : 0,
                    "participantCount", meeting.getParticipantCount(),
                    "hasRecording", meeting.getHasRecording() != null ? meeting.getHasRecording() : false,
                    "meetingType", meeting.getMeetingType(),
                    "fetchStatus", meeting.getFetchStatus()
                )).collect(Collectors.toList()),
                "totalElements", meetings.getTotalElements(),
                "totalPages", meetings.getTotalPages(),
                "size", meetings.getSize(),
                "number", meetings.getNumber()
            );

        } catch (Exception e) {
            log.error("获取PMI会议历史失败: {}", pmiNumber, e);
            throw new RuntimeException("获取PMI会议历史失败", e);
        }
    }

    /**
     * 获取最活跃的PMI列表
     */
    public Map<String, Object> getMostActivePmis(int limit, int days) {
        log.info("获取最活跃的PMI列表 - limit: {}, days: {}", limit, days);

        try {
            LocalDateTime startDate = LocalDateTime.now().minusDays(days);

            // 这里可以实现更复杂的查询逻辑
            // 暂时返回基本数据
            return Map.of(
                "activePmis", List.of(),
                "period", days,
                "limit", limit
            );

        } catch (Exception e) {
            log.error("获取最活跃的PMI列表失败", e);
            throw new RuntimeException("获取最活跃的PMI列表失败", e);
        }
    }

    /**
     * 导出PMI报告数据
     */
    public Map<String, Object> exportPmiReports(String pmiNumber, String startDate, String endDate, String format) {
        log.info("导出PMI报告数据 - pmiNumber: {}, format: {}", pmiNumber, format);

        try {
            // 这里可以实现导出逻辑
            // 暂时返回基本数据
            return Map.of(
                "message", "导出功能开发中",
                "format", format,
                "pmiNumber", pmiNumber != null ? pmiNumber : "all"
            );

        } catch (Exception e) {
            log.error("导出PMI报告数据失败", e);
            throw new RuntimeException("导出PMI报告数据失败", e);
        }
    }

    /**
     * 获取PMI使用趋势数据
     */
    public Map<String, Object> getPmiTrends(int days, String pmiNumber) {
        log.info("获取PMI使用趋势数据 - days: {}, pmiNumber: {}", days, pmiNumber);

        try {
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(days);

            List<MeetingReport> meetings;
            if (pmiNumber != null) {
                meetings = meetingReportRepository.findByPmiNumberAndStartTimeBetween(
                        pmiNumber, startDate, endDate);
            } else {
                meetings = meetingReportRepository.findByPmiNumberIsNotNullAndStartTimeBetween(
                        startDate, endDate);
            }

            // 按日期分组统计
            Map<LocalDate, List<MeetingReport>> dailyMeetings = meetings.stream()
                    .collect(Collectors.groupingBy(meeting -> meeting.getStartTime().toLocalDate()));

            List<Map<String, Object>> trendData = new ArrayList<>();
            for (int i = 0; i < days; i++) {
                LocalDate date = endDate.minusDays(i).toLocalDate();
                List<MeetingReport> dayMeetings = dailyMeetings.getOrDefault(date, List.of());

                trendData.add(Map.of(
                    "date", date,
                    "meetingCount", dayMeetings.size(),
                    "totalDuration", dayMeetings.stream()
                            .mapToInt(m -> m.getDurationMinutes() != null ? m.getDurationMinutes() : 0)
                            .sum(),
                    "totalParticipants", dayMeetings.stream()
                            .mapToInt(MeetingReport::getParticipantCount)
                            .sum()
                ));
            }

            return Map.of(
                "trends", trendData,
                "summary", Map.of(
                    "totalMeetings", meetings.size(),
                    "totalDuration", meetings.stream()
                            .mapToInt(m -> m.getDurationMinutes() != null ? m.getDurationMinutes() : 0)
                            .sum(),
                    "avgDailyMeetings", meetings.size() / (double) days
                )
            );

        } catch (Exception e) {
            log.error("获取PMI使用趋势数据失败", e);
            throw new RuntimeException("获取PMI使用趋势数据失败", e);
        }
    }
}
