package com.zoombus.service;

import com.zoombus.dto.PmiScheduleRequest;
import com.zoombus.dto.PmiScheduleResponse;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.PmiSchedule;
import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.event.PmiWindowCreatedEvent;
import com.zoombus.event.PmiWindowDeletedEvent;
import com.zoombus.exception.WindowConflictException;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.PmiScheduleRepository;
import com.zoombus.repository.PmiScheduleWindowRepository;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * PMI计划管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiScheduleService {
    
    private final PmiScheduleRepository pmiScheduleRepository;
    private final PmiScheduleWindowRepository pmiScheduleWindowRepository;
    private final PmiScheduleWindowTaskRepository pmiScheduleWindowTaskRepository;
    private final PmiRecordRepository pmiRecordRepository;
    // 移除不再使用的 pmiWindowCheckService，因为我们不再立即检查窗口
    // private final PmiWindowCheckService pmiWindowCheckService;
    private final PmiService pmiService;
    private final ApplicationEventPublisher eventPublisher;
    
    /**
     * 创建PMI计划
     */
    @Transactional
    public PmiScheduleResponse createSchedule(PmiScheduleRequest request) {
        log.info("开始创建PMI计划: pmiRecordId={}, repeatType={}, startDate={}, endDate={}, isAllDay={}",
                request.getPmiRecordId(), request.getRepeatType(), request.getStartDate(),
                request.getEndDate(), request.getIsAllDay());

        // 验证请求数据
        if (!request.isValid()) {
            throw new RuntimeException("计划配置无效");
        }

        // 验证PMI记录是否存在
        Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(request.getPmiRecordId());
        if (!pmiRecordOpt.isPresent()) {
            throw new RuntimeException("PMI记录不存在");
        }

        // 幂等性检查：检查是否已存在相同的计划
        PmiSchedule existingSchedule = findExistingSchedule(request);
        if (existingSchedule != null) {
            log.warn("检测到重复的计划创建请求，返回已存在的计划: scheduleId={}, request={}",
                    existingSchedule.getId(), request);
            return buildScheduleResponse(existingSchedule);
        }

        // 创建临时计划对象用于生成窗口
        PmiSchedule tempSchedule = request.toEntity();
        tempSchedule.setId(-1L); // 临时ID，表示还未保存

        // 生成新窗口列表（不保存到数据库）
        List<PmiScheduleWindow> newWindows = generateWindowsForSchedule(tempSchedule);
        log.info("为计划生成了 {} 个窗口", newWindows.size());

        // 检测窗口冲突
        ConflictDetectionResult conflictResult = detectWindowConflictsWithIndexes(request.getPmiRecordId(), newWindows);

        if (!conflictResult.getConflictWindows().isEmpty()) {
            // 存在冲突，抛出冲突异常
            throw new WindowConflictException(
                String.format("本次PMI计划安排存在%d条冲突窗口", conflictResult.getConflictWindows().size()),
                newWindows,
                conflictResult.getConflictWindows(),
                conflictResult.getConflictNewWindowIndexes()
            );
        }

        // 没有冲突，正常创建计划
        PmiSchedule schedule = pmiScheduleRepository.save(tempSchedule);

        // 保存窗口到数据库
        log.info("开始保存 {} 个窗口到数据库", newWindows.size());
        for (int i = 0; i < newWindows.size(); i++) {
            PmiScheduleWindow window = newWindows.get(i);
            window.setScheduleId(schedule.getId());
            window.setCreatedAt(LocalDateTime.now());
            window.setUpdatedAt(LocalDateTime.now());
            log.info("准备保存第 {} 个窗口: startDateTime={}, endDateTime={}",
                    i + 1, window.getStartDateTime(), window.getEndDateTime());
        }

        List<PmiScheduleWindow> savedWindows;
        try {
            savedWindows = pmiScheduleWindowRepository.saveAll(newWindows);
            log.info("成功保存了 {} 个窗口到数据库", savedWindows.size());
        } catch (Exception e) {
            log.error("保存PMI窗口失败: scheduleId={}, windowCount={}", schedule.getId(), newWindows.size(), e);
            throw new RuntimeException("创建计划失败: " + e.getMessage(), e);
        }

        // 在事务提交后发布窗口创建事件
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                for (PmiScheduleWindow window : savedWindows) {
                    try {
                        eventPublisher.publishEvent(new PmiWindowCreatedEvent(PmiScheduleService.this, window));
                        log.debug("发布PMI窗口创建事件: windowId={}", window.getId());
                    } catch (Exception e) {
                        log.warn("发布PMI窗口创建事件失败: windowId={}", window.getId(), e);
                    }
                }
            }
        });

        log.info("创建PMI计划成功: scheduleId={}, pmiRecordId={}, 窗口数量={}",
                schedule.getId(), request.getPmiRecordId(), savedWindows.size());

        // 注释掉立即检查逻辑，让task系统统一处理窗口开启，确保有完整的task记录
        // 这样可以避免重复开启和缺少task记录的问题
        // try {
        //     pmiWindowCheckService.checkAndOpenPmiWindows();
        //     log.info("计划创建后立即触发窗口检查完成: scheduleId={}", schedule.getId());
        // } catch (Exception e) {
        //     log.error("计划创建后触发窗口检查失败: scheduleId={}", schedule.getId(), e);
        // }

        return buildScheduleResponse(schedule);
    }

    /**
     * 创建PMI计划（合并冲突窗口）
     */
    @Transactional
    public PmiScheduleResponse createScheduleWithMerge(PmiScheduleRequest request) {
        // 验证请求数据
        if (!request.isValid()) {
            throw new RuntimeException("计划配置无效");
        }

        // 验证PMI记录是否存在
        Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(request.getPmiRecordId());
        if (!pmiRecordOpt.isPresent()) {
            throw new RuntimeException("PMI记录不存在");
        }

        // 创建临时计划对象用于生成窗口
        PmiSchedule tempSchedule = request.toEntity();
        tempSchedule.setId(-1L); // 临时ID，表示还未保存

        // 生成新窗口列表（不保存到数据库）
        List<PmiScheduleWindow> newWindows = generateWindowsForSchedule(tempSchedule);

        // 检测窗口冲突
        List<PmiScheduleWindow> conflictWindows = detectWindowConflicts(request.getPmiRecordId(), newWindows);

        // 合并冲突窗口
        if (!conflictWindows.isEmpty()) {
            mergeConflictWindows(newWindows, conflictWindows);
        }

        // 创建计划
        PmiSchedule schedule = pmiScheduleRepository.save(tempSchedule);

        // 保存窗口到数据库
        for (PmiScheduleWindow window : newWindows) {
            window.setScheduleId(schedule.getId());
            window.setCreatedAt(LocalDateTime.now());
            window.setUpdatedAt(LocalDateTime.now());
        }
        List<PmiScheduleWindow> savedWindows = pmiScheduleWindowRepository.saveAll(newWindows);

        // 在事务提交后发布窗口创建事件
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                for (PmiScheduleWindow window : savedWindows) {
                    try {
                        eventPublisher.publishEvent(new PmiWindowCreatedEvent(PmiScheduleService.this, window));
                        log.debug("发布PMI窗口创建事件: windowId={}", window.getId());
                    } catch (Exception e) {
                        log.warn("发布PMI窗口创建事件失败: windowId={}", window.getId(), e);
                    }
                }
            }
        });

        log.info("创建PMI计划成功（已合并冲突窗口）: scheduleId={}, pmiRecordId={}, 合并冲突数={}, 窗口数量={}",
                schedule.getId(), request.getPmiRecordId(), conflictWindows.size(), savedWindows.size());

        // 注释掉立即检查逻辑，让task系统统一处理窗口开启，确保有完整的task记录
        // 这样可以避免重复开启和缺少task记录的问题
        // try {
        //     pmiWindowCheckService.checkAndOpenPmiWindows();
        //     log.info("计划创建后立即触发窗口检查完成: scheduleId={}", schedule.getId());
        // } catch (Exception e) {
        //     log.error("计划创建后触发窗口检查失败: scheduleId={}", schedule.getId(), e);
        // }

        return buildScheduleResponse(schedule);
    }

    /**
     * 更新PMI计划
     */
    @Transactional
    public PmiScheduleResponse updateSchedule(Long scheduleId, PmiScheduleRequest request) {
        // 查找现有计划
        PmiSchedule schedule = pmiScheduleRepository.findById(scheduleId)
                .orElseThrow(() -> new RuntimeException("计划不存在"));
        
        // 验证请求数据
        if (!request.isValid()) {
            throw new RuntimeException("计划配置无效");
        }
        
        // 更新计划信息
        schedule.setName(request.getName());
        schedule.setStartDate(request.getStartDate());
        schedule.setEndDate(request.getEndDate());
        schedule.setRepeatType(request.getRepeatType());
        schedule.setIsAllDay(request.getIsAllDay());
        schedule.setStartTime(request.getStartTime());
        schedule.setDurationMinutes(request.getDurationMinutes());
        
        schedule = pmiScheduleRepository.save(schedule);
        
        // 重新生成窗口
        pmiScheduleWindowRepository.deleteByScheduleId(scheduleId);
        generateWindows(schedule);

        log.info("更新PMI计划成功: scheduleId={}", scheduleId);

        // 注释掉立即检查逻辑，让task系统统一处理窗口开启，确保有完整的task记录
        // 这样可以避免重复开启和缺少task记录的问题
        // try {
        //     pmiWindowCheckService.checkAndOpenPmiWindows();
        //     log.info("计划更新后立即触发窗口检查完成: scheduleId={}", scheduleId);
        // } catch (Exception e) {
        //     log.error("计划更新后触发窗口检查失败: scheduleId={}", scheduleId, e);
        //     // 不抛出异常，避免影响计划更新的成功响应
        // }

        return buildScheduleResponse(schedule);
    }
    
    /**
     * 删除PMI计划
     */
    @Transactional
    public void deleteSchedule(Long scheduleId) {
        PmiSchedule schedule = pmiScheduleRepository.findById(scheduleId)
                .orElseThrow(() -> new RuntimeException("计划不存在"));

        // 检查是否有执行中的窗口
        List<PmiScheduleWindow> activeWindows = pmiScheduleWindowRepository
                .findByScheduleIdAndStatus(scheduleId, PmiScheduleWindow.WindowStatus.ACTIVE);

        if (!activeWindows.isEmpty()) {
            throw new IllegalStateException("当前计划存在执行中的窗口，禁止删除！");
        }

        Long pmiRecordId = schedule.getPmiRecordId();

        // 获取要删除的窗口列表，用于发布删除事件
        List<PmiScheduleWindow> windowsToDelete = pmiScheduleWindowRepository.findByScheduleId(scheduleId);

        // 删除所有窗口
        pmiScheduleWindowRepository.deleteByScheduleId(scheduleId);

        // 发布窗口删除事件
        for (PmiScheduleWindow window : windowsToDelete) {
            try {
                eventPublisher.publishEvent(new PmiWindowDeletedEvent(this, window));
                log.debug("发布PMI窗口删除事件: windowId={}", window.getId());
            } catch (Exception e) {
                log.warn("发布PMI窗口删除事件失败: windowId={}", window.getId(), e);
            }
        }

        // 删除计划
        pmiScheduleRepository.delete(schedule);

        // 检查是否需要关闭PMI（删除计划后可能导致PMI没有其他活跃窗口）
        checkAndClosePmi(pmiRecordId);

        log.info("删除PMI计划成功: scheduleId={}", scheduleId);
    }
    
    /**
     * 获取PMI计划详情
     */
    public PmiScheduleResponse getSchedule(Long scheduleId) {
        PmiSchedule schedule = pmiScheduleRepository.findById(scheduleId)
                .orElseThrow(() -> new RuntimeException("计划不存在"));
        
        return buildScheduleResponse(schedule);
    }
    
    /**
     * 获取PMI记录的所有计划
     */
    public Page<PmiScheduleResponse> getSchedulesByPmiRecord(Long pmiRecordId, Pageable pageable) {
        Page<PmiSchedule> schedules = pmiScheduleRepository.findByPmiRecordId(pmiRecordId, pageable);
        return schedules.map(this::buildScheduleResponse);
    }
    
    /**
     * 搜索计划
     */
    public Page<PmiScheduleResponse> searchSchedules(Long pmiRecordId, String keyword,
            String status, String startDate, String endDate, Pageable pageable) {

        // 构建查询条件
        Specification<PmiSchedule> spec = Specification.where(null);

        // PMI记录ID筛选
        if (pmiRecordId != null) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("pmiRecordId"), pmiRecordId));
        }

        // 关键词搜索
        if (keyword != null && !keyword.trim().isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.like(root.get("name"), "%" + keyword + "%"));
        }

        // 状态筛选
        if (status != null && !status.trim().isEmpty()) {
            try {
                PmiSchedule.ScheduleStatus scheduleStatus = PmiSchedule.ScheduleStatus.valueOf(status);
                spec = spec.and((root, query, cb) -> cb.equal(root.get("status"), scheduleStatus));
            } catch (IllegalArgumentException e) {
                log.warn("无效的状态值: {}", status);
            }
        }

        // 日期范围筛选（基于创建时间）
        if (startDate != null && !startDate.trim().isEmpty()) {
            try {
                LocalDate start = LocalDate.parse(startDate);
                spec = spec.and((root, query, cb) ->
                    cb.greaterThanOrEqualTo(root.get("createdAt"), start.atStartOfDay()));
            } catch (Exception e) {
                log.warn("无效的开始日期: {}", startDate);
            }
        }

        if (endDate != null && !endDate.trim().isEmpty()) {
            try {
                LocalDate end = LocalDate.parse(endDate);
                spec = spec.and((root, query, cb) ->
                    cb.lessThanOrEqualTo(root.get("createdAt"), end.atTime(23, 59, 59)));
            } catch (Exception e) {
                log.warn("无效的结束日期: {}", endDate);
            }
        }

        Page<PmiSchedule> schedules = pmiScheduleRepository.findAll(spec, pageable);
        return schedules.map(this::buildScheduleResponse);
    }
    
    /**
     * 更新计划状态
     */
    @Transactional
    public PmiScheduleResponse updateScheduleStatus(Long scheduleId, PmiSchedule.ScheduleStatus status) {
        PmiSchedule schedule = pmiScheduleRepository.findById(scheduleId)
                .orElseThrow(() -> new RuntimeException("计划不存在"));
        
        schedule.setStatus(status);
        schedule = pmiScheduleRepository.save(schedule);
        
        log.info("更新计划状态成功: scheduleId={}, status={}", scheduleId, status);
        
        return buildScheduleResponse(schedule);
    }
    
    /**
     * 获取计划的窗口列表
     */
    public Page<PmiScheduleResponse.PmiScheduleWindowResponse> getScheduleWindows(Long scheduleId, Pageable pageable) {
        Page<PmiScheduleWindow> windows = pmiScheduleWindowRepository.findByScheduleId(scheduleId, pageable);
        return windows.map(this::buildWindowResponseWithTasks);
    }

    /**
     * 构建包含任务信息的窗口响应
     */
    private PmiScheduleResponse.PmiScheduleWindowResponse buildWindowResponseWithTasks(PmiScheduleWindow window) {
        PmiScheduleResponse.PmiScheduleWindowResponse response =
                PmiScheduleResponse.PmiScheduleWindowResponse.fromEntity(window);

        // 获取任务信息
        if (window.getOpenTaskId() != null) {
            Optional<PmiScheduleWindowTask> openTask = pmiScheduleWindowTaskRepository.findById(window.getOpenTaskId());
            if (openTask.isPresent()) {
                response.setOpenTask(buildTaskInfo(openTask.get()));
            }
        }

        if (window.getCloseTaskId() != null) {
            Optional<PmiScheduleWindowTask> closeTask = pmiScheduleWindowTaskRepository.findById(window.getCloseTaskId());
            if (closeTask.isPresent()) {
                response.setCloseTask(buildTaskInfo(closeTask.get()));
            }
        }

        return response;
    }

    /**
     * 构建任务信息
     */
    private PmiScheduleResponse.PmiScheduleWindowResponse.TaskInfo buildTaskInfo(PmiScheduleWindowTask task) {
        PmiScheduleResponse.PmiScheduleWindowResponse.TaskInfo taskInfo =
                new PmiScheduleResponse.PmiScheduleWindowResponse.TaskInfo();
        taskInfo.setId(task.getId());
        taskInfo.setTaskKey(task.getTaskKey());
        taskInfo.setTaskType(task.getTaskType().name());
        taskInfo.setScheduledTime(task.getScheduledTime());
        taskInfo.setStatus(task.getStatus().name());
        taskInfo.setActualExecutionTime(task.getActualExecutionTime());
        taskInfo.setErrorMessage(task.getErrorMessage());
        taskInfo.setRetryCount(task.getRetryCount());
        taskInfo.setCreatedAt(task.getCreatedAt());
        taskInfo.setUpdatedAt(task.getUpdatedAt());
        return taskInfo;
    }

    /**
     * 生成计划窗口
     */
    private void generateWindows(PmiSchedule schedule) {
        List<PmiScheduleWindow> windows = generateWindowsForSchedule(schedule);

        if (!windows.isEmpty()) {
            pmiScheduleWindowRepository.saveAll(windows);
            log.info("为计划生成窗口: scheduleId={}, windowCount={}, isAllDay={}",
                    schedule.getId(), windows.size(), schedule.getIsAllDay());
        }
    }

    /**
     * 为计划生成窗口列表（不保存到数据库）
     */
    private List<PmiScheduleWindow> generateWindowsForSchedule(PmiSchedule schedule) {
        log.info("开始生成窗口: repeatType={}, startDate={}, endDate={}, isAllDay={}",
                schedule.getRepeatType(), schedule.getStartDate(), schedule.getEndDate(), schedule.getIsAllDay());

        List<PmiScheduleWindow> windows = new ArrayList<>();

        if (schedule.getIsAllDay()) {
            // 全天计划：合并连续日期为窗口
            windows = generateAllDayWindows(schedule);
        } else {
            // 非全天计划：每个日期一个窗口
            windows = generateRegularWindows(schedule);
        }

        log.info("生成窗口完成: 共生成 {} 个窗口", windows.size());
        return windows;
    }

    /**
     * 生成全天计划窗口（合并连续日期）
     */
    private List<PmiScheduleWindow> generateAllDayWindows(PmiSchedule schedule) {
        List<PmiScheduleWindow> windows = new ArrayList<>();
        List<LocalDate> allDates = new ArrayList<>();

        // 收集所有符合条件的日期
        LocalDate currentDate = schedule.getStartDate();
        LocalDate endDate = schedule.getEndDate();

        while (!currentDate.isAfter(endDate)) {
            if (shouldCreateWindow(schedule, currentDate)) {
                allDates.add(currentDate);
            }
            currentDate = getNextDate(currentDate, schedule.getRepeatType());
        }

        // 将连续日期合并为窗口
        if (!allDates.isEmpty()) {
            List<List<LocalDate>> consecutiveGroups = groupConsecutiveDates(allDates);

            for (List<LocalDate> group : consecutiveGroups) {
                PmiScheduleWindow window = createAllDayWindow(schedule, group.get(0), group.get(group.size() - 1));
                windows.add(window);
            }
        }

        return windows;
    }

    /**
     * 生成常规计划窗口（每个日期一个窗口）
     */
    private List<PmiScheduleWindow> generateRegularWindows(PmiSchedule schedule) {
        List<PmiScheduleWindow> windows = new ArrayList<>();
        LocalDate currentDate = schedule.getStartDate();
        LocalDate endDate = schedule.getEndDate();

        while (!currentDate.isAfter(endDate)) {
            if (shouldCreateWindow(schedule, currentDate)) {
                PmiScheduleWindow window = createWindow(schedule, currentDate);
                windows.add(window);
            }
            currentDate = getNextDate(currentDate, schedule.getRepeatType());
        }

        return windows;
    }

    /**
     * 将连续日期分组
     */
    private List<List<LocalDate>> groupConsecutiveDates(List<LocalDate> dates) {
        List<List<LocalDate>> groups = new ArrayList<>();
        if (dates.isEmpty()) {
            return groups;
        }

        // 排序日期
        dates.sort(LocalDate::compareTo);

        List<LocalDate> currentGroup = new ArrayList<>();
        currentGroup.add(dates.get(0));

        for (int i = 1; i < dates.size(); i++) {
            LocalDate current = dates.get(i);
            LocalDate previous = dates.get(i - 1);

            // 如果当前日期与前一个日期连续，加入当前组
            if (current.equals(previous.plusDays(1))) {
                currentGroup.add(current);
            } else {
                // 否则开始新组
                groups.add(new ArrayList<>(currentGroup));
                currentGroup.clear();
                currentGroup.add(current);
            }
        }

        // 添加最后一组
        if (!currentGroup.isEmpty()) {
            groups.add(currentGroup);
        }

        return groups;
    }

    /**
     * 创建全天窗口（支持日期范围）
     * 重构：使用新的 startDateTime 和 endDateTime 字段
     */
    private PmiScheduleWindow createAllDayWindow(PmiSchedule schedule, LocalDate startDate, LocalDate endDate) {
        PmiScheduleWindow window = new PmiScheduleWindow();
        window.setScheduleId(schedule.getId());
        window.setPmiRecordId(schedule.getPmiRecordId());
        // window.setWindowDate(startDate);
        // window.setEndDate(endDate);

        LocalTime startTime = LocalTime.of(0, 0);
        LocalTime endTime = LocalTime.of(23, 59);

        // window.setStartTime(startTime);
        // window.setEndTime(endTime);

        // 设置新的精确时间字段
        window.setStartDateTime(LocalDateTime.of(startDate, startTime));
        window.setEndDateTime(LocalDateTime.of(endDate, endTime));

        window.setStatus(PmiScheduleWindow.WindowStatus.PENDING);

        log.debug("创建全天窗口范围: startDateTime={}, endDateTime={}",
                 window.getStartDateTime(), window.getEndDateTime());

        return window;
    }

    /**
     * 创建单个窗口
     * 重构：使用新的 startDateTime 和 endDateTime 字段，逻辑大大简化
     */
    private PmiScheduleWindow createWindow(PmiSchedule schedule, LocalDate date) {
        PmiScheduleWindow window = new PmiScheduleWindow();
        window.setScheduleId(schedule.getId());
        window.setPmiRecordId(schedule.getPmiRecordId());
        // window.setWindowDate(date);

        if (schedule.getIsAllDay()) {
            // 全天窗口：从当天00:00到23:59
            LocalTime startTime = LocalTime.of(0, 0);
            LocalTime endTime = LocalTime.of(23, 59);

            // 设置精确时间字段
            window.setStartDateTime(LocalDateTime.of(date, startTime));
            window.setEndDateTime(LocalDateTime.of(date, endTime));

            log.debug("创建全天窗口: startDateTime={}, endDateTime={}",
                     window.getStartDateTime(), window.getEndDateTime());
        } else {
            LocalTime startTime = schedule.getStartTime();

            // 计算精确的开始和结束时间
            LocalDateTime startDateTime = LocalDateTime.of(date, startTime);
            LocalDateTime endDateTime = LocalDateTime.of(date, startTime).plusMinutes(schedule.getDurationMinutes());

            // 设置精确时间字段
            window.setStartDateTime(startDateTime);
            window.setEndDateTime(endDateTime);

            log.debug("创建窗口: startDateTime={}, endDateTime={}",
                     startDateTime, endDateTime);
        }

        window.setStatus(PmiScheduleWindow.WindowStatus.PENDING);
        return window;
    }

    /**
     * 判断是否应该在指定日期创建窗口
     */
    private boolean shouldCreateWindow(PmiSchedule schedule, LocalDate date) {
        LocalDate startDate = schedule.getStartDate();

        switch (schedule.getRepeatType()) {
            case ONCE:
                // 仅一次：只在开始日期创建
                return date.equals(startDate);
            case DAILY:
                return true; // 每天都创建
            case WEEKLY:
                // 按周重复：检查是否在选择的星期几
                if (schedule.getWeekDays() != null && !schedule.getWeekDays().trim().isEmpty()) {
                    String[] weekDays = schedule.getWeekDays().split(",");
                    int dayOfWeek = date.getDayOfWeek().getValue(); // 1=周一, 7=周日
                    for (String weekDay : weekDays) {
                        try {
                            if (Integer.parseInt(weekDay.trim()) == dayOfWeek) {
                                return true;
                            }
                        } catch (NumberFormatException e) {
                            log.warn("无效的星期几配置: {}", weekDay);
                        }
                    }
                    return false;
                } else {
                    // 兼容旧版本：每周的同一天创建
                    return date.getDayOfWeek().equals(startDate.getDayOfWeek());
                }
            case MONTHLY:
                // 按月重复：检查是否在选择的日期
                if (schedule.getMonthDays() != null && !schedule.getMonthDays().trim().isEmpty()) {
                    String[] monthDays = schedule.getMonthDays().split(",");
                    int dayOfMonth = date.getDayOfMonth();
                    for (String monthDay : monthDays) {
                        try {
                            if (Integer.parseInt(monthDay.trim()) == dayOfMonth) {
                                return true;
                            }
                        } catch (NumberFormatException e) {
                            log.warn("无效的月份日期配置: {}", monthDay);
                        }
                    }
                    return false;
                } else {
                    // 兼容旧版本：每月的同一天创建
                    return date.getDayOfMonth() == startDate.getDayOfMonth();
                }
            default:
                return false;
        }
    }

    /**
     * 获取下一个日期
     */
    private LocalDate getNextDate(LocalDate currentDate, PmiSchedule.RepeatType repeatType) {
        switch (repeatType) {
            case ONCE:
                // 仅一次：直接跳到结束日期之后，结束循环
                return currentDate.plusYears(1);  // 返回一个很远的日期来结束循环
            case DAILY:
                return currentDate.plusDays(1);
            case WEEKLY:
                // 按周重复：每天递增，在shouldCreateWindow中判断是否匹配
                return currentDate.plusDays(1);
            case MONTHLY:
                // 按月重复：每天递增，在shouldCreateWindow中判断是否匹配
                return currentDate.plusDays(1);
            default:
                return currentDate.plusDays(1);
        }
    }

    /**
     * 构建计划响应对象
     */
    private PmiScheduleResponse buildScheduleResponse(PmiSchedule schedule) {
        PmiScheduleResponse response = PmiScheduleResponse.fromEntity(schedule);

        // 添加统计信息
        response.setTotalWindows(pmiScheduleWindowRepository.countByScheduleId(schedule.getId()));
        response.setPendingWindows(pmiScheduleWindowRepository.countByScheduleIdAndStatus(
                schedule.getId(), PmiScheduleWindow.WindowStatus.PENDING));
        response.setActiveWindows(pmiScheduleWindowRepository.countByScheduleIdAndStatus(
                schedule.getId(), PmiScheduleWindow.WindowStatus.ACTIVE));

        // 统计已完成的窗口（包括正常完成和人工关闭）
        long completedWindows = pmiScheduleWindowRepository.countByScheduleIdAndStatus(
                schedule.getId(), PmiScheduleWindow.WindowStatus.COMPLETED);
        long manuallyClosedWindows = pmiScheduleWindowRepository.countByScheduleIdAndStatus(
                schedule.getId(), PmiScheduleWindow.WindowStatus.MANUALLY_CLOSED);
        response.setCompletedWindows(completedWindows + manuallyClosedWindows);

        response.setFailedWindows(pmiScheduleWindowRepository.countByScheduleIdAndStatus(
                schedule.getId(), PmiScheduleWindow.WindowStatus.FAILED));

        return response;
    }

    /**
     * 获取今天需要处理的窗口
     */
    public List<PmiScheduleWindow> getTodayWindows() {
        return pmiScheduleWindowRepository.findTodayWindows(LocalDate.now());
    }



    /**
     * 批量删除计划
     */
    @Transactional
    public void batchDeleteSchedules(List<Long> scheduleIds) {
        if (scheduleIds == null || scheduleIds.isEmpty()) {
            throw new RuntimeException("计划ID列表不能为空");
        }

        // 先检查所有计划是否有执行中的窗口
        List<String> schedulesWithActiveWindows = new ArrayList<>();
        for (Long scheduleId : scheduleIds) {
            List<PmiScheduleWindow> activeWindows = pmiScheduleWindowRepository
                    .findByScheduleIdAndStatus(scheduleId, PmiScheduleWindow.WindowStatus.ACTIVE);

            if (!activeWindows.isEmpty()) {
                PmiSchedule schedule = pmiScheduleRepository.findById(scheduleId).orElse(null);
                String scheduleName = schedule != null ? schedule.getName() : "计划ID:" + scheduleId;
                schedulesWithActiveWindows.add(scheduleName);
            }
        }

        // 如果有任何计划存在执行中的窗口，则抛出异常
        if (!schedulesWithActiveWindows.isEmpty()) {
            String errorMessage = "以下计划存在执行中的窗口，禁止删除：" + String.join("、", schedulesWithActiveWindows);
            throw new IllegalStateException(errorMessage);
        }

        // 收集所有需要检查的PMI记录ID
        Set<Long> pmiRecordIds = new HashSet<>();

        for (Long scheduleId : scheduleIds) {
            PmiSchedule schedule = pmiScheduleRepository.findById(scheduleId).orElse(null);
            if (schedule != null) {
                pmiRecordIds.add(schedule.getPmiRecordId());

                // 删除所有窗口
                pmiScheduleWindowRepository.deleteByScheduleId(scheduleId);

                // 删除计划
                pmiScheduleRepository.delete(schedule);

                log.info("删除PMI计划成功: scheduleId={}", scheduleId);
            }
        }

        // 批量检查所有相关的PMI状态
        for (Long pmiRecordId : pmiRecordIds) {
            checkAndClosePmi(pmiRecordId);
        }

        log.info("批量删除计划成功，共删除 {} 个计划", scheduleIds.size());
    }

    /**
     * 检查并关闭PMI
     * 如果PMI记录下没有活跃的窗口，则将PMI状态设置为非活跃
     */
    private void checkAndClosePmi(Long pmiRecordId) {
        try {
            // 查找该PMI记录下所有活跃的窗口
            List<PmiScheduleWindow> activeWindows = pmiScheduleWindowRepository
                    .findByPmiRecordIdAndStatus(pmiRecordId, PmiScheduleWindow.WindowStatus.ACTIVE);

            // 如果没有活跃的窗口，则关闭PMI
            if (activeWindows.isEmpty()) {
                log.info("PMI记录 {} 下没有活跃窗口，准备关闭PMI", pmiRecordId);

                PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId).orElse(null);
                if (pmiRecord != null && pmiRecord.getStatus() == PmiRecord.PmiStatus.ACTIVE) {
                    // 调用PMI服务关闭PMI
                    pmiService.deactivatePmi(pmiRecord.getId());
                    log.info("PMI记录 {} 已关闭", pmiRecordId);
                }
            } else {
                log.info("PMI记录 {} 下还有 {} 个活跃窗口，不关闭PMI", pmiRecordId, activeWindows.size());
            }
        } catch (Exception e) {
            log.error("检查并关闭PMI失败: pmiRecordId={}", pmiRecordId, e);
        }
    }

    /**
     * 批量更新计划状态
     */
    @Transactional
    public void batchUpdateStatus(List<Long> scheduleIds, PmiSchedule.ScheduleStatus status) {
        if (scheduleIds == null || scheduleIds.isEmpty()) {
            throw new RuntimeException("计划ID列表不能为空");
        }

        if (status == null) {
            throw new RuntimeException("状态不能为空");
        }

        List<PmiSchedule> schedules = pmiScheduleRepository.findAllById(scheduleIds);

        if (schedules.size() != scheduleIds.size()) {
            throw new RuntimeException("部分计划不存在");
        }

        for (PmiSchedule schedule : schedules) {
            schedule.setStatus(status);
        }

        pmiScheduleRepository.saveAll(schedules);

        log.info("批量更新计划状态成功，共更新 {} 个计划为 {}", scheduleIds.size(), status);
    }

    /**
     * 冲突检测结果内部类
     */
    private static class ConflictDetectionResult {
        private final List<PmiScheduleWindow> conflictWindows;
        private final Set<Integer> conflictNewWindowIndexes;

        public ConflictDetectionResult(List<PmiScheduleWindow> conflictWindows, Set<Integer> conflictNewWindowIndexes) {
            this.conflictWindows = conflictWindows;
            this.conflictNewWindowIndexes = conflictNewWindowIndexes;
        }

        public List<PmiScheduleWindow> getConflictWindows() {
            return conflictWindows;
        }

        public Set<Integer> getConflictNewWindowIndexes() {
            return conflictNewWindowIndexes;
        }
    }

    /**
     * 检测窗口冲突并返回冲突的新窗口索引
     */
    private ConflictDetectionResult detectWindowConflictsWithIndexes(Long pmiRecordId, List<PmiScheduleWindow> newWindows) {
        List<PmiScheduleWindow> conflictWindows = new ArrayList<>();
        Set<Integer> conflictNewWindowIndexes = new HashSet<>();

        // 获取该PMI下所有"待执行"和"执行中"状态的窗口
        List<PmiScheduleWindow> existingWindows = pmiScheduleWindowRepository
            .findByPmiRecordIdAndStatusIn(pmiRecordId,
                Arrays.asList(PmiScheduleWindow.WindowStatus.PENDING, PmiScheduleWindow.WindowStatus.ACTIVE));

        // 检查每个新窗口是否与现有窗口冲突
        for (int i = 0; i < newWindows.size(); i++) {
            PmiScheduleWindow newWindow = newWindows.get(i);
            boolean hasConflict = false;

            for (PmiScheduleWindow existingWindow : existingWindows) {
                if (isWindowConflict(newWindow, existingWindow)) {
                    if (!conflictWindows.contains(existingWindow)) {
                        conflictWindows.add(existingWindow);
                    }
                    hasConflict = true;
                }
            }

            if (hasConflict) {
                conflictNewWindowIndexes.add(i);
            }
        }

        return new ConflictDetectionResult(conflictWindows, conflictNewWindowIndexes);
    }

    /**
     * 检测窗口冲突
     */
    private List<PmiScheduleWindow> detectWindowConflicts(Long pmiRecordId, List<PmiScheduleWindow> newWindows) {
        List<PmiScheduleWindow> conflictWindows = new ArrayList<>();

        // 获取该PMI下所有"待执行"和"执行中"状态的窗口
        List<PmiScheduleWindow> existingWindows = pmiScheduleWindowRepository
            .findByPmiRecordIdAndStatusIn(pmiRecordId,
                Arrays.asList(PmiScheduleWindow.WindowStatus.PENDING, PmiScheduleWindow.WindowStatus.ACTIVE));

        // 检查每个新窗口是否与现有窗口冲突
        for (PmiScheduleWindow newWindow : newWindows) {
            for (PmiScheduleWindow existingWindow : existingWindows) {
                if (isWindowConflict(newWindow, existingWindow)) {
                    if (!conflictWindows.contains(existingWindow)) {
                        conflictWindows.add(existingWindow);
                    }
                }
            }
        }

        return conflictWindows;
    }

    /**
     * 判断两个窗口是否冲突
     */
    private boolean isWindowConflict(PmiScheduleWindow window1, PmiScheduleWindow window2) {
        // 获取窗口的精确时间范围
        LocalDateTime start1 = window1.getStartDateTime();
        LocalDateTime end1 = window1.getEndDateTime();
        LocalDateTime start2 = window2.getStartDateTime();
        LocalDateTime end2 = window2.getEndDateTime();

        // 检查是否有空值
        if (start1 == null || end1 == null || start2 == null || end2 == null) {
            return false; // 如果有空值，认为不冲突
        }

        // 检查时间范围是否重叠
        return start1.isBefore(end2) && start2.isBefore(end1);
    }

    /**
     * 合并冲突窗口
     * 修复重复窗口问题：确保合并逻辑正确，避免重复保存窗口，并处理重复的冲突窗口
     */
    private void mergeConflictWindows(List<PmiScheduleWindow> newWindows, List<PmiScheduleWindow> conflictWindows) {
        // 首先删除重复的冲突窗口
        removeDuplicateConflictWindows(conflictWindows);

        // 使用迭代器来安全地移除元素，避免ConcurrentModificationException
        Iterator<PmiScheduleWindow> newWindowIterator = newWindows.iterator();

        while (newWindowIterator.hasNext()) {
            PmiScheduleWindow newWindow = newWindowIterator.next();
            List<PmiScheduleWindow> conflictingWindows = new ArrayList<>();

            // 收集所有与这个新窗口冲突的窗口
            for (PmiScheduleWindow conflictWindow : conflictWindows) {
                if (isWindowConflict(newWindow, conflictWindow)) {
                    conflictingWindows.add(conflictWindow);
                }
            }

            if (!conflictingWindows.isEmpty()) {
                // 选择第一个冲突窗口进行合并
                PmiScheduleWindow targetWindow = conflictingWindows.get(0);

                // 计算合并后的时间范围（取并集）
                LocalDateTime mergedStart = targetWindow.getStartDateTime();
                LocalDateTime mergedEnd = targetWindow.getEndDateTime();

                LocalDateTime newStart = newWindow.getStartDateTime();
                LocalDateTime newEnd = newWindow.getEndDateTime();

                if (newStart != null && (mergedStart == null || newStart.isBefore(mergedStart))) {
                    mergedStart = newStart;
                }
                if (newEnd != null && (mergedEnd == null || newEnd.isAfter(mergedEnd))) {
                    mergedEnd = newEnd;
                }

                // 更新目标窗口的时间范围
                targetWindow.setStartDateTime(mergedStart);
                targetWindow.setEndDateTime(mergedEnd);
                targetWindow.setUpdatedAt(LocalDateTime.now());

                // 保存更新后的目标窗口
                pmiScheduleWindowRepository.save(targetWindow);

                log.info("合并冲突窗口: targetWindowId={}, 新窗口时间={} ~ {}, 合并后时间={} ~ {}",
                        targetWindow.getId(), newStart, newEnd, mergedStart, mergedEnd);

                // 删除其他重复的冲突窗口
                for (int i = 1; i < conflictingWindows.size(); i++) {
                    PmiScheduleWindow duplicateWindow = conflictingWindows.get(i);
                    pmiScheduleWindowRepository.delete(duplicateWindow);
                    conflictWindows.remove(duplicateWindow);
                    log.info("删除重复的冲突窗口: windowId={}", duplicateWindow.getId());
                }

                // 从新窗口列表中移除已合并的窗口
                newWindowIterator.remove();
                log.debug("移除已合并的新窗口: {} ~ {}", newWindow.getStartDateTime(), newWindow.getEndDateTime());
            }
        }

        log.info("窗口合并完成: 剩余新窗口数量={}, 更新的冲突窗口数量={}", newWindows.size(), conflictWindows.size());
    }

    /**
     * 删除重复的冲突窗口
     * 检测并删除具有相同时间范围的重复窗口
     */
    private void removeDuplicateConflictWindows(List<PmiScheduleWindow> conflictWindows) {
        Map<String, List<PmiScheduleWindow>> timeRangeGroups = new HashMap<>();

        // 按时间范围分组
        for (PmiScheduleWindow window : conflictWindows) {
            String timeRangeKey = window.getStartDateTime() + "_" + window.getEndDateTime();
            timeRangeGroups.computeIfAbsent(timeRangeKey, k -> new ArrayList<>()).add(window);
        }

        // 删除重复的窗口，每个时间范围只保留一个
        for (Map.Entry<String, List<PmiScheduleWindow>> entry : timeRangeGroups.entrySet()) {
            List<PmiScheduleWindow> windows = entry.getValue();
            if (windows.size() > 1) {
                // 保留第一个，删除其他的
                for (int i = 1; i < windows.size(); i++) {
                    PmiScheduleWindow duplicateWindow = windows.get(i);
                    pmiScheduleWindowRepository.delete(duplicateWindow);
                    conflictWindows.remove(duplicateWindow);
                    log.info("删除重复的冲突窗口: windowId={}, 时间范围={} ~ {}",
                            duplicateWindow.getId(), duplicateWindow.getStartDateTime(), duplicateWindow.getEndDateTime());
                }
            }
        }
    }

    /**
     * 查找已存在的相同计划（用于幂等性检查）
     */
    private PmiSchedule findExistingSchedule(PmiScheduleRequest request) {
        // 查找最近5分钟内创建的相同计划
        LocalDateTime fiveMinutesAgo = LocalDateTime.now().minusMinutes(5);

        List<PmiSchedule> recentSchedules = pmiScheduleRepository.findByPmiRecordIdAndCreatedAtAfter(
                request.getPmiRecordId(), fiveMinutesAgo);

        for (PmiSchedule schedule : recentSchedules) {
            if (isSameScheduleRequest(schedule, request)) {
                return schedule;
            }
        }

        return null;
    }

    /**
     * 判断计划是否与请求相同
     */
    private boolean isSameScheduleRequest(PmiSchedule schedule, PmiScheduleRequest request) {
        // 比较关键字段
        boolean basicFieldsMatch = Objects.equals(schedule.getPmiRecordId(), request.getPmiRecordId()) &&
               Objects.equals(schedule.getStartDate(), request.getStartDate()) &&
               Objects.equals(schedule.getEndDate(), request.getEndDate()) &&
               Objects.equals(schedule.getRepeatType(), request.getRepeatType()) &&
               Objects.equals(schedule.getIsAllDay(), request.getIsAllDay()) &&
               Objects.equals(schedule.getStartTime(), request.getStartTime()) &&
               Objects.equals(schedule.getDurationMinutes(), request.getDurationMinutes());

        if (!basicFieldsMatch) {
            return false;
        }

        // 比较weekDays（需要转换类型）
        String requestWeekDays = null;
        if (request.getWeekDays() != null && !request.getWeekDays().isEmpty()) {
            requestWeekDays = request.getWeekDays().stream()
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse(null);
        }

        // 比较monthDays（需要转换类型）
        String requestMonthDays = null;
        if (request.getMonthDays() != null && !request.getMonthDays().isEmpty()) {
            requestMonthDays = request.getMonthDays().stream()
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse(null);
        }

        return Objects.equals(schedule.getWeekDays(), requestWeekDays) &&
               Objects.equals(schedule.getMonthDays(), requestMonthDays);
    }
}
