package com.zoombus.service;

import com.zoombus.dto.PmiScheduleWindowRequest;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.PmiSchedule;
import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.event.PmiWindowUpdatedEvent;
import com.zoombus.exception.ResourceNotFoundException;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.PmiScheduleRepository;
import com.zoombus.repository.PmiScheduleWindowRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * PMI计划窗口服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiScheduleWindowService {
    
    private final PmiScheduleWindowRepository windowRepository;
    private final PmiScheduleRepository scheduleRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final PmiService pmiService;
    private final PmiTaskManagementService pmiTaskManagementService;
    private final ApplicationEventPublisher eventPublisher;
    
    /**
     * 获取窗口详情
     */
    public PmiScheduleWindow getWindow(Long id) {
        return windowRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("计划窗口不存在: " + id));
    }
    
    /**
     * 更新窗口
     */
    @Transactional
    public PmiScheduleWindow updateWindow(Long id, PmiScheduleWindowRequest request) {
        log.info("更新计划窗口: id={}, request={}", id, request);
        
        PmiScheduleWindow window = getWindow(id);

        // 检查窗口状态，只有待执行的窗口才能编辑
        if (window.getStatus() != PmiScheduleWindow.WindowStatus.PENDING) {
            throw new IllegalStateException("只有待执行状态的窗口才能编辑");
        }

        // 保存旧窗口信息用于事件发布
        PmiScheduleWindow oldWindow = new PmiScheduleWindow();
        oldWindow.setId(window.getId());
        oldWindow.setStartDateTime(window.getStartDateTime());
        oldWindow.setEndDateTime(window.getEndDateTime());
        oldWindow.setStatus(window.getStatus());
        oldWindow.setPmiRecordId(window.getPmiRecordId());
        oldWindow.setScheduleId(window.getScheduleId());

        // 构建新的时间字段
        request.buildDateTimeFields();

        // 更新窗口信息
        if (request.getStartDateTime() != null) {
            window.setStartDateTime(request.getStartDateTime());
        }
        if (request.getEndDateTime() != null) {
            window.setEndDateTime(request.getEndDateTime());
        }
        window.setUpdatedAt(LocalDateTime.now());

        // 验证窗口时间的有效性
        if (!window.isValid()) {
            throw new IllegalArgumentException("窗口时间设置无效：开始时间必须早于结束时间");
        }

        PmiScheduleWindow savedWindow = windowRepository.save(window);

        // 发布窗口更新事件
        try {
            eventPublisher.publishEvent(new PmiWindowUpdatedEvent(this, savedWindow, oldWindow));
            log.debug("发布PMI窗口更新事件: windowId={}", savedWindow.getId());
        } catch (Exception e) {
            log.warn("发布PMI窗口更新事件失败: windowId={}", savedWindow.getId(), e);
        }

        return savedWindow;
    }
    
    /**
     * 删除窗口
     */
    @Transactional
    public void deleteWindow(Long id) {
        log.info("删除计划窗口: id={}", id);

        PmiScheduleWindow window = getWindow(id);
        Long pmiRecordId = window.getPmiRecordId();

        // 检查窗口状态，只有待执行的窗口才能删除
        if (window.getStatus() != PmiScheduleWindow.WindowStatus.PENDING) {
            throw new IllegalStateException("只有待执行状态的窗口才能删除");
        }

        windowRepository.delete(window);

        // 检查是否需要关闭PMI（删除窗口后可能导致PMI没有其他活跃窗口）
        checkAndClosePmi(pmiRecordId);

        log.info("计划窗口删除成功: id={}", id);
    }
    
    /**
     * 关闭窗口
     */
    @Transactional
    public PmiScheduleWindow closeWindow(Long id) {
        log.info("关闭计划窗口: id={}", id);

        PmiScheduleWindow window = getWindow(id);

        // 检查窗口状态，只有执行中的窗口才能关闭
        if (window.getStatus() != PmiScheduleWindow.WindowStatus.ACTIVE) {
            throw new IllegalStateException("只有执行中状态的窗口才能关闭");
        }

        // 取消对应的关闭任务（如果存在）
        if (window.getCloseTaskId() != null) {
            try {
                pmiTaskManagementService.cancelPmiTask(window.getCloseTaskId());
                log.info("已取消窗口关闭任务: windowId={}, closeTaskId={}", id, window.getCloseTaskId());
            } catch (Exception e) {
                log.warn("取消窗口关闭任务失败: windowId={}, closeTaskId={}, error={}",
                        id, window.getCloseTaskId(), e.getMessage());
                // 不抛出异常，继续执行窗口关闭操作
            }
        } else {
            log.warn("窗口没有关联的关闭任务: windowId={}", id);
        }

        // 更新窗口状态为人工关闭，并记录实际关闭时间
        window.setStatus(PmiScheduleWindow.WindowStatus.MANUALLY_CLOSED);
        window.setActualEndTime(LocalDateTime.now());
        window.setUpdatedAt(LocalDateTime.now());

        PmiScheduleWindow savedWindow = windowRepository.save(window);

        // 检查是否需要关闭PMI
        checkAndClosePmi(window.getPmiRecordId());

        // 检查是否需要将计划状态置为完成
        checkAndCompleteSchedule(window.getScheduleId());

        log.info("计划窗口关闭成功: id={}", id);

        return savedWindow;
    }

    /**
     * 激活窗口（用于测试）
     */
    @Transactional
    public PmiScheduleWindow activateWindow(Long id) {
        log.info("激活计划窗口: id={}", id);

        PmiScheduleWindow window = getWindow(id);

        // 检查窗口状态，只有待执行的窗口才能激活
        if (window.getStatus() != PmiScheduleWindow.WindowStatus.PENDING) {
            throw new IllegalStateException("只有待执行状态的窗口才能激活");
        }

        // 更新窗口状态为活跃，并记录实际开始时间
        window.setStatus(PmiScheduleWindow.WindowStatus.ACTIVE);
        window.setActualStartTime(LocalDateTime.now());
        window.setUpdatedAt(LocalDateTime.now());

        PmiScheduleWindow savedWindow = windowRepository.save(window);

        log.info("计划窗口激活成功: id={}", id);

        return savedWindow;
    }

    /**
     * 延长窗口
     */
    @Transactional
    public PmiScheduleWindow extendWindow(Long id, int extendMinutes) {
        log.info("延长计划窗口: id={}, extendMinutes={}", id, extendMinutes);

        PmiScheduleWindow window = getWindow(id);

        // 检查窗口状态，只有执行中的窗口才能延长
        if (window.getStatus() != PmiScheduleWindow.WindowStatus.ACTIVE) {
            throw new IllegalStateException("只有执行中状态的窗口才能延长");
        }

        // 检查延长时间是否合理（支持更长的延长时间）
        if (extendMinutes <= 0 || extendMinutes > 525600) { // 最多延长1年
            throw new IllegalArgumentException("延长时间必须在1分钟到1年之间");
        }

        // 对于长时间延长，使用日期时间计算而不是简单的时间计算
        LocalDateTime currentEndDateTime = window.getEndDateTime();
        LocalDateTime newEndDateTime = currentEndDateTime.plusMinutes(extendMinutes);

        // 设置新的结束时间
        window.setEndDateTime(newEndDateTime);

        // 如果延长后跨天，记录日志
        if (!newEndDateTime.toLocalDate().equals(window.getStartDateTime().toLocalDate())) {
            log.info("窗口延长跨天: 原日期={}, 新结束日期时间={}", window.getStartDateTime().toLocalDate(), newEndDateTime);
        } else {
            log.info("同天延长: 新结束时间={}", newEndDateTime);
        }

        // 检查时间冲突（只检查同一天的冲突）
        checkTimeConflict(window.getPmiRecordId(), window.getStartDateTime().toLocalDate(),
                         window.getStartDateTime().toLocalTime(), window.getEndDateTime().toLocalTime(), window.getId());

        window.setUpdatedAt(LocalDateTime.now());

        PmiScheduleWindow savedWindow = windowRepository.save(window);

        log.info("计划窗口延长成功: id={}, 新结束时间={}", id, window.getEndDateTime());

        return savedWindow;
    }
    
    /**
     * 检查时间冲突
     */
    private void checkTimeConflict(Long pmiRecordId, LocalDate windowDate,
                                  LocalTime startTime, LocalTime endTime, Long excludeWindowId) {
        // 查找同一PMI在同一天的其他有效窗口
        List<PmiScheduleWindow> existingWindows = windowRepository
                .findByPmiRecordIdAndWindowDateAndStatusIn(
                    pmiRecordId,
                    windowDate,
                    Arrays.asList(PmiScheduleWindow.WindowStatus.PENDING, PmiScheduleWindow.WindowStatus.ACTIVE)
                );

        // 排除当前窗口
        if (excludeWindowId != null) {
            existingWindows = existingWindows.stream()
                    .filter(w -> !w.getId().equals(excludeWindowId))
                    .collect(Collectors.toList());
        }

        // 检查时间重叠
        for (PmiScheduleWindow existingWindow : existingWindows) {
            if (isTimeOverlap(startTime, endTime, existingWindow.getStartDateTime().toLocalTime(), existingWindow.getEndDateTime().toLocalTime())) {
                throw new IllegalArgumentException(
                    String.format("时间冲突：与窗口ID %d (%s-%s) 存在重叠",
                        existingWindow.getId(),
                        existingWindow.getStartDateTime(),
                        existingWindow.getEndDateTime())
                );
            }
        }
    }

    /**
     * 检查两个时间段是否重叠
     */
    private boolean isTimeOverlap(LocalTime start1, LocalTime end1, LocalTime start2, LocalTime end2) {
        return start1.isBefore(end2) && end1.isAfter(start2);
    }

    /**
     * 检查并关闭PMI
     * 如果PMI记录下没有活跃的窗口，则将PMI状态设置为非活跃
     */
    private void checkAndClosePmi(Long pmiRecordId) {
        try {
            // 查找该PMI记录下所有活跃的窗口
            List<PmiScheduleWindow> activeWindows = windowRepository
                    .findByPmiRecordIdAndStatus(pmiRecordId, PmiScheduleWindow.WindowStatus.ACTIVE);

            // 如果没有活跃的窗口，则关闭PMI
            if (activeWindows.isEmpty()) {
                log.info("PMI记录 {} 下没有活跃窗口，准备关闭PMI", pmiRecordId);

                PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
                        .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在: " + pmiRecordId));

                // 调用PMI服务关闭PMI
                pmiService.deactivatePmi(pmiRecord.getId());

                log.info("PMI记录 {} 已关闭", pmiRecordId);
            } else {
                log.info("PMI记录 {} 下还有 {} 个活跃窗口，不关闭PMI", pmiRecordId, activeWindows.size());
            }

        } catch (Exception e) {
            log.error("检查并关闭PMI失败: pmiRecordId={}", pmiRecordId, e);
            // 不抛出异常，避免影响窗口关闭操作
        }
    }

    /**
     * 检查并完成计划
     * 如果计划下的所有窗口都已完成（包括正常完成和人工关闭），则将计划状态设置为完成
     */
    private void checkAndCompleteSchedule(Long scheduleId) {
        try {
            // 统计计划的总窗口数
            long totalWindows = windowRepository.countByScheduleId(scheduleId);

            if (totalWindows == 0) {
                log.debug("计划 {} 没有窗口，跳过完成检查", scheduleId);
                return;
            }

            // 统计已完成的窗口数（包括正常完成和人工关闭）
            long completedWindows = windowRepository.countByScheduleIdAndStatus(
                    scheduleId, PmiScheduleWindow.WindowStatus.COMPLETED);
            long manuallyClosedWindows = windowRepository.countByScheduleIdAndStatus(
                    scheduleId, PmiScheduleWindow.WindowStatus.MANUALLY_CLOSED);

            long finishedWindows = completedWindows + manuallyClosedWindows;

            // 如果所有窗口都已完成，则更新计划状态为完成
            if (finishedWindows == totalWindows) {
                PmiSchedule schedule = scheduleRepository.findById(scheduleId)
                        .orElseThrow(() -> new ResourceNotFoundException("计划不存在: " + scheduleId));

                // 只有活跃状态的计划才需要更新为完成
                if (schedule.getStatus() == PmiSchedule.ScheduleStatus.ACTIVE) {
                    schedule.setStatus(PmiSchedule.ScheduleStatus.COMPLETED);
                    scheduleRepository.save(schedule);

                    log.info("计划已完成，更新状态: scheduleId={}, name={}, totalWindows={}, completedWindows={}, manuallyClosedWindows={}",
                            scheduleId, schedule.getName(), totalWindows, completedWindows, manuallyClosedWindows);
                }
            } else {
                log.debug("计划 {} 还有未完成的窗口: 总数={}, 已完成={}, 人工关闭={}, 剩余={}",
                        scheduleId, totalWindows, completedWindows, manuallyClosedWindows,
                        totalWindows - finishedWindows);
            }

        } catch (Exception e) {
            log.error("检查并完成计划失败: scheduleId={}", scheduleId, e);
            // 不抛出异常，避免影响窗口关闭操作
        }
    }
}
