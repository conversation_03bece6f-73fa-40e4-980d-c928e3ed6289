package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.dto.PmiRecordDTO;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.User;
import com.zoombus.entity.ZoomUser;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.UserRepository;
import com.zoombus.repository.ZoomUserRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.util.PmiGenerator;
import com.zoombus.dto.ZoomApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

/**
 * PMI管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiService {
    
    private final PmiRecordRepository pmiRecordRepository;
    private final ZoomUserRepository zoomUserRepository;
    private final UserRepository userRepository;
    private final ZoomApiService zoomApiService;
    private final PmiSetupService pmiSetupService;
    private final ZoomMeetingRepository zoomMeetingRepository;
    
    /**
     * 生成PMI（兼容性方法，使用默认域名）
     */
    @Transactional
    public PmiRecord generatePmi(Long userId, String customPassword) {
        return generatePmi(userId, customPassword, "http://localhost:8080");
    }

    /**
     * 为用户生成PMI（带域名参数）
     */
    @Transactional
    public PmiRecord generatePmi(Long userId, String customPassword, String baseUrl) {
        // 验证用户是否存在
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));
        
        // 生成唯一的PMI号码
        String pmiNumber;
        int attempts = 0;
        do {
            pmiNumber = PmiGenerator.generatePmi();
            attempts++;
            if (attempts > 100) {
                throw new RuntimeException("生成PMI号码失败，请重试");
            }
        } while (pmiRecordRepository.existsByPmiNumber(pmiNumber) || pmiRecordRepository.existsByMagicId(pmiNumber));
        
        // 生成密码
        String password = customPassword != null && !customPassword.trim().isEmpty() 
                ? customPassword.trim() 
                : PmiGenerator.generatePmiPassword();
        
        // 查找可用的ZoomUser
        Optional<ZoomUser> availableUser = zoomUserRepository.findFirstAvailablePublicHostUser();
        if (!availableUser.isPresent()) {
            throw new RuntimeException("当前没有可用的Zoom账号，请稍后重试");
        }

        ZoomUser zoomUser = availableUser.get();

        try {
            // 标记ZoomUser为使用中
            zoomUser.setUsageStatus(ZoomUser.UsageStatus.IN_USE);
            zoomUser.setCurrentMeetingId(null); // 这里可以设置具体的会议ID
            zoomUserRepository.save(zoomUser);

            // 使用PmiSetupService检测并设置PMI
            PmiSetupService.PmiSetupResult setupResult = pmiSetupService.detectAndSetupPmi(
                    zoomUser, pmiNumber, password);

            if (!setupResult.isSuccess()) {
                // 设置失败，恢复ZoomUser状态
                zoomUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
                zoomUser.setCurrentMeetingId(null);
                zoomUser.setCurrentPmi(zoomUser.getOriginalPmi()); // 恢复原始PMI
                zoomUserRepository.save(zoomUser);
                throw new RuntimeException(setupResult.getMessage());
            }

            // PMI设置成功，更新ZoomUser的当前PMI
            zoomUser.setCurrentPmi(pmiNumber);
            zoomUserRepository.save(zoomUser);
            log.info("ZoomUser {} 的当前PMI已更新为: {}", zoomUser.getEmail(), pmiNumber);

            ZoomApiResponse<JsonNode> apiResponse = setupResult.getApiResponse();

            // 解析API响应获取链接
            JsonNode responseData = apiResponse.getData();
            String hostUrl = generateHostUrl(pmiNumber, baseUrl); // 使用当前域名
            String joinUrl = extractJoinUrlFromResponse(responseData, pmiNumber, password);

            // 创建PMI记录
            PmiRecord pmiRecord = new PmiRecord();
            pmiRecord.setUserId(userId);
            pmiRecord.setPmiNumber(pmiNumber);
            pmiRecord.setPmiPassword(password);
            pmiRecord.setHostUrl(hostUrl);
            pmiRecord.setJoinUrl(joinUrl);
            pmiRecord.setStatus(PmiRecord.PmiStatus.ACTIVE);
            pmiRecord.setCurrentZoomUserId(zoomUser.getId());

            PmiRecord savedRecord = pmiRecordRepository.save(pmiRecord);

            // 设置成功后，恢复ZoomUser为可用状态
            zoomUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
            zoomUser.setCurrentMeetingId(null);
            zoomUserRepository.save(zoomUser);

            log.info("PMI生成成功: userId={}, pmiNumber={}, zoomUserId={}",
                    userId, pmiNumber, zoomUser.getZoomUserId());

            return savedRecord;

        } catch (Exception e) {
            // 发生异常时，确保恢复ZoomUser状态
            zoomUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
            zoomUser.setCurrentMeetingId(null);
            zoomUser.setCurrentPmi(zoomUser.getOriginalPmi()); // 恢复原始PMI
            zoomUserRepository.save(zoomUser);
            throw e;
        }
    }
    
    /**
     * 使用PMI开启会议室
     */
    @Transactional
    public String usePmi(String pmiNumber) {
        // 查找PMI记录
        PmiRecord pmiRecord = pmiRecordRepository.findByPmiNumber(pmiNumber)
                .orElseThrow(() -> new RuntimeException("PMI不存在: " + pmiNumber));
        
        if (pmiRecord.getStatus() != PmiRecord.PmiStatus.ACTIVE) {
            throw new RuntimeException("PMI状态不可用: " + pmiRecord.getStatus());
        }
        
        // 查找可用的ZoomUser
        Optional<ZoomUser> availableUser = zoomUserRepository.findFirstAvailablePublicHostUser();
        if (!availableUser.isPresent()) {
            throw new RuntimeException("当前没有可用的Zoom账号，请稍后重试");
        }

        ZoomUser zoomUser = availableUser.get();

        try {
            // 标记ZoomUser为使用中
            zoomUser.setUsageStatus(ZoomUser.UsageStatus.IN_USE);
            zoomUser.setCurrentMeetingId(null); // 这里可以设置具体的会议ID
            zoomUserRepository.save(zoomUser);

            // 使用PmiSetupService检测并设置PMI
            PmiSetupService.PmiSetupResult setupResult = pmiSetupService.detectAndSetupPmi(
                    zoomUser, pmiNumber, pmiRecord.getPmiPassword());

            if (!setupResult.isSuccess()) {
                // 设置失败，立即回收ZoomUser账号并恢复原始PMI
                log.error("PMI设置失败，开始回收ZoomUser账号: userId={}, error={}",
                        zoomUser.getZoomUserId(), setupResult.getMessage());

                try {
                    // 调用ZoomAPI恢复原始PMI（使用ZoomUser对应的ZoomAuth）
                    zoomApiService.restoreUserOriginalPmi(zoomUser.getZoomUserId(), zoomUser.getOriginalPmi(), zoomUser.getZoomAuth());
                    log.info("ZoomUser原始PMI恢复成功: userId={}, originalPmi={}",
                            zoomUser.getZoomUserId(), zoomUser.getOriginalPmi());
                } catch (Exception restoreException) {
                    log.error("恢复原始PMI失败: userId={}", zoomUser.getZoomUserId(), restoreException);
                }

                // 恢复ZoomUser状态
                zoomUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
                zoomUser.setCurrentMeetingId(null);
                zoomUser.setCurrentPmi(zoomUser.getOriginalPmi());
                zoomUserRepository.save(zoomUser);

                // 给用户明确的错误信息
                throw new RuntimeException("PMI设置失败，无法开启会议: " + setupResult.getMessage());
            }

            // PMI设置成功，更新ZoomUser的当前PMI
            zoomUser.setCurrentPmi(pmiNumber);
            zoomUserRepository.save(zoomUser);
            log.info("ZoomUser {} 的当前PMI已更新为: {}", zoomUser.getEmail(), pmiNumber);

            // 更新PMI记录
            pmiRecord.setCurrentZoomUserId(zoomUser.getId());
            pmiRecord.setLastUsedAt(LocalDateTime.now());
            pmiRecordRepository.save(pmiRecord);

            log.info("PMI使用成功: pmiNumber={}, zoomUserId={}", pmiNumber, zoomUser.getZoomUserId());

            // 返回主持人链接
            return pmiRecord.getHostUrl();

        } catch (Exception e) {
            // 发生异常时，确保完全回收ZoomUser账号
            log.error("PMI使用过程中发生异常，开始完全回收ZoomUser账号: userId={}",
                    zoomUser.getZoomUserId(), e);

            try {
                // 调用ZoomAPI恢复原始PMI（使用ZoomUser对应的ZoomAuth）
                zoomApiService.restoreUserOriginalPmi(zoomUser.getZoomUserId(), zoomUser.getOriginalPmi(), zoomUser.getZoomAuth());
                log.info("异常处理中ZoomUser原始PMI恢复成功: userId={}, originalPmi={}",
                        zoomUser.getZoomUserId(), zoomUser.getOriginalPmi());
            } catch (Exception restoreException) {
                log.error("异常处理中恢复原始PMI失败: userId={}", zoomUser.getZoomUserId(), restoreException);
            }

            // 恢复ZoomUser状态
            zoomUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
            zoomUser.setCurrentMeetingId(null);
            zoomUser.setCurrentPmi(zoomUser.getOriginalPmi());
            zoomUserRepository.save(zoomUser);

            // 重新抛出异常，给用户明确的错误信息
            throw new RuntimeException("PMI会议开启失败，账号已回收: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取用户的PMI列表
     */
    @Transactional(readOnly = true)
    public Page<PmiRecord> getUserPmiRecords(Long userId, Pageable pageable) {
        return pmiRecordRepository.findByUserId(userId, pageable);
    }

    /**
     * 获取用户的PMI列表（支持计费类型筛选）
     */
    @Transactional(readOnly = true)
    public Page<PmiRecord> getUserPmiRecords(Long userId, String billingMode, Pageable pageable) {
        if (billingMode != null && !billingMode.trim().isEmpty()) {
            try {
                PmiRecord.BillingMode mode = PmiRecord.BillingMode.valueOf(billingMode.trim().toUpperCase());
                // 使用带自定义排序的筛选方法
                return pmiRecordRepository.findByUserIdAndBillingModeWithCustomSort(userId, mode, pageable);
            } catch (IllegalArgumentException e) {
                log.warn("无效的计费模式: {}", billingMode);
                return pmiRecordRepository.findByUserId(userId, pageable);
            }
        }
        return getUserPmiRecords(userId, pageable);
    }
    
    /**
     * 获取所有PMI记录
     */
    @Transactional(readOnly = true)
    public Page<PmiRecord> getAllPmiRecords(Pageable pageable) {
        Page<PmiRecord> pmiRecords = pmiRecordRepository.findAll(pageable);

        // 为每个PMI记录填充会议状态和Zoom用户信息
        pmiRecords.getContent().forEach(this::enrichPmiRecordWithMeetingInfo);

        return pmiRecords;
    }

    /**
     * 获取所有PMI记录（支持计费类型筛选和排序）
     */
    @Transactional(readOnly = true)
    public Page<PmiRecord> getAllPmiRecords(String billingMode, Pageable pageable) {
        Page<PmiRecord> pmiRecords;

        if (billingMode != null && !billingMode.trim().isEmpty()) {
            try {
                PmiRecord.BillingMode mode = PmiRecord.BillingMode.valueOf(billingMode.trim().toUpperCase());
                // 使用带自定义排序的筛选方法
                pmiRecords = pmiRecordRepository.findByBillingModeWithCustomSort(mode, pageable);
            } catch (IllegalArgumentException e) {
                log.warn("无效的计费模式: {}", billingMode);
                pmiRecords = getAllPmiRecords(pageable);
            }
        } else {
            // 默认排序：LONG类型优先，按到期日由近至远；BY_TIME按剩余时长由长到短
            pmiRecords = pmiRecordRepository.findAllWithCustomSort(pageable);
        }

        // 为每个PMI记录填充会议状态和Zoom用户信息
        pmiRecords.getContent().forEach(this::enrichPmiRecordWithMeetingInfo);

        return pmiRecords;
    }

    /**
     * 为PMI记录填充会议状态和Zoom用户信息
     */
    private void enrichPmiRecordWithMeetingInfo(PmiRecord pmiRecord) {
        // 查找当前PMI的活跃会议
        Optional<ZoomMeeting> activeMeeting = zoomMeetingRepository
            .findActiveMeetingByPmiRecordIdAndStatus(pmiRecord.getId(), ZoomMeeting.MeetingStatus.STARTED);

        if (activeMeeting.isPresent()) {
            ZoomMeeting meeting = activeMeeting.get();
            pmiRecord.setHasActiveMeeting(true);
            pmiRecord.setActiveMeetingZoomUserId(meeting.getAssignedZoomUserId());
            pmiRecord.setActiveMeetingZoomUserEmail(meeting.getAssignedZoomUserEmail());
        } else {
            pmiRecord.setHasActiveMeeting(false);
            pmiRecord.setActiveMeetingZoomUserId(null);
            pmiRecord.setActiveMeetingZoomUserEmail(null);
        }
    }
    
    /**
     * 根据ID获取PMI记录
     */
    @Transactional(readOnly = true)
    public Optional<PmiRecord> getPmiRecordById(Long id) {
        return pmiRecordRepository.findById(id);
    }

    /**
     * 根据ID获取PMI记录（包含用户信息）
     */
    @Transactional(readOnly = true)
    public Optional<PmiRecordDTO> getPmiRecordWithUserInfo(Long id) {
        Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(id);
        if (!pmiRecordOpt.isPresent()) {
            return Optional.empty();
        }

        PmiRecord pmiRecord = pmiRecordOpt.get();

        // 获取用户信息
        String userFullName = null;
        String userEmail = null;
        String userName = null;

        if (pmiRecord.getUserId() != null) {
            Optional<User> userOpt = userRepository.findById(pmiRecord.getUserId());
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                userFullName = user.getFullName();
                userEmail = user.getEmail();
                userName = user.getUsername();
            }
        }

        PmiRecordDTO dto = PmiRecordDTO.fromEntity(pmiRecord, userFullName, userEmail, userName);
        return Optional.of(dto);
    }
    
    /**
     * 根据PMI号码获取记录
     */
    @Transactional(readOnly = true)
    public Optional<PmiRecord> getPmiRecordByNumber(String pmiNumber) {
        return pmiRecordRepository.findByPmiNumber(pmiNumber);
    }

    /**
     * 根据魔链ID获取记录
     */
    @Transactional(readOnly = true)
    public Optional<PmiRecord> getPmiRecordByMagicId(String magicId) {
        return pmiRecordRepository.findByMagicId(magicId);
    }
    
    /**
     * 搜索PMI记录
     */
    @Transactional(readOnly = true)
    public Page<PmiRecord> searchPmiRecords(String keyword, Pageable pageable) {
        return pmiRecordRepository.searchByKeyword(keyword, pageable);
    }

    /**
     * 搜索PMI记录（支持计费类型筛选）
     */
    @Transactional(readOnly = true)
    public Page<PmiRecord> searchPmiRecords(String keyword, String billingMode, Pageable pageable) {
        if (billingMode != null && !billingMode.trim().isEmpty()) {
            try {
                PmiRecord.BillingMode mode = PmiRecord.BillingMode.valueOf(billingMode.trim().toUpperCase());
                // 使用带自定义排序的搜索方法
                return pmiRecordRepository.searchByKeywordAndBillingModeWithCustomSort(keyword, mode, pageable);
            } catch (IllegalArgumentException e) {
                log.warn("无效的计费模式: {}", billingMode);
                return searchPmiRecords(keyword, pageable);
            }
        }
        return searchPmiRecords(keyword, pageable);
    }
    
    /**
     * 更新PMI状态
     */
    @Transactional
    public PmiRecord updatePmiStatus(Long id, PmiRecord.PmiStatus status) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("PMI记录不存在: " + id));

        pmiRecord.setStatus(status);
        PmiRecord updatedRecord = pmiRecordRepository.save(pmiRecord);

        log.info("PMI状态更新成功: id={}, status={}", id, status);
        return updatedRecord;
    }

    /**
     * 停用PMI（设置为非活跃状态）
     */
    @Transactional
    public PmiRecord deactivatePmi(Long id) {
        log.info("停用PMI: id={}", id);
        return updatePmiStatus(id, PmiRecord.PmiStatus.INACTIVE);
    }
    
    /**
     * 删除PMI记录
     */
    @Transactional
    public void deletePmiRecord(Long id) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("PMI记录不存在: " + id));
        
        pmiRecordRepository.delete(pmiRecord);
        log.info("PMI记录删除成功: id={}, pmiNumber={}", id, pmiRecord.getPmiNumber());
    }
    
    /**
     * 生成主持人链接（使用当前域名）
     */
    private String generateHostUrl(String pmiNumber, String baseUrl) {
        return baseUrl + "/pmi/" + pmiNumber;
    }
    
    /**
     * 从Zoom API响应中提取参会链接
     */
    private String extractJoinUrlFromResponse(JsonNode responseData, String pmiNumber, String password) {
        try {
            // 尝试从API响应中获取join_url（直接在根级别）
            if (responseData != null && responseData.has("join_url")) {
                String joinUrl = responseData.get("join_url").asText();
                if (joinUrl != null && !joinUrl.trim().isEmpty()) {
                    log.info("从Zoom API响应中获取到参会链接: {}", joinUrl);
                    return joinUrl;
                }
            }

            // 尝试从pmi字段中获取join_url
            if (responseData != null && responseData.has("pmi")) {
                JsonNode pmiNode = responseData.get("pmi");
                if (pmiNode != null && pmiNode.has("join_url")) {
                    String joinUrl = pmiNode.get("join_url").asText();
                    if (joinUrl != null && !joinUrl.trim().isEmpty()) {
                        log.info("从Zoom API响应的pmi字段中获取到参会链接: {}", joinUrl);
                        return joinUrl;
                    }
                }
            }

            // 尝试从personal_meeting_url字段获取
            if (responseData != null && responseData.has("personal_meeting_url")) {
                String joinUrl = responseData.get("personal_meeting_url").asText();
                if (joinUrl != null && !joinUrl.trim().isEmpty()) {
                    log.info("从Zoom API响应的personal_meeting_url字段中获取到参会链接: {}", joinUrl);
                    return joinUrl;
                }
            }

            // 如果API响应中没有找到任何join_url，则生成默认链接
            log.warn("Zoom API响应中未找到join_url，响应内容: {}", responseData);
            log.warn("使用默认格式生成参会链接");
            return generateDefaultJoinUrl(pmiNumber, password);

        } catch (Exception e) {
            log.error("解析Zoom API响应中的参会链接失败，使用默认格式", e);
            return generateDefaultJoinUrl(pmiNumber, password);
        }
    }

    /**
     * 生成默认参会链接（备用方案）
     */
    private String generateDefaultJoinUrl(String pmiNumber, String password) {
        // Zoom的pwd参数需要Base64编码
        String encodedPassword = Base64.getEncoder().encodeToString(password.getBytes());
        return String.format("https://us06web.zoom.us/j/%s?pwd=%s", pmiNumber, encodedPassword);
    }
    
    /**
     * 统计用户PMI数量
     */
    @Transactional(readOnly = true)
    public long countUserPmiRecords(Long userId) {
        return pmiRecordRepository.countByUserId(userId);
    }
    
    /**
     * 统计活跃PMI数量
     */
    @Transactional(readOnly = true)
    public long countActivePmiRecords() {
        return pmiRecordRepository.countActiveRecords();
    }

    /**
     * 统计会议中的PMI数量
     */
    @Transactional(readOnly = true)
    public long countPmiInMeeting() {
        // 查询状态为STARTED的会议对应的PMI数量
        return zoomMeetingRepository.countDistinctPmiRecordIdByStatus(ZoomMeeting.MeetingStatus.STARTED);
    }

    /**
     * 生成PMI复制文本（包含用户姓名）
     */
    @Transactional(readOnly = true)
    public String generatePmiCopyText(Long pmiId) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiId)
                .orElseThrow(() -> new RuntimeException("PMI记录不存在: " + pmiId));

        // 查询用户信息
        User user = userRepository.findById(pmiRecord.getUserId())
                .orElse(null);

        String userName = "用户"; // 默认值
        if (user != null && user.getFullName() != null && !user.getFullName().trim().isEmpty()) {
            userName = user.getFullName();
        }

        return pmiRecord.generateCopyText(userName);
    }

    /**
     * 设置PMI回退状态
     */
    @Transactional
    public void setPmiFallbackStatus(Long pmiId, boolean fallbackEnabled) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiId)
                .orElseThrow(() -> new RuntimeException("PMI记录不存在: " + pmiId));

        pmiRecord.setFallbackEnabled(fallbackEnabled);
        pmiRecordRepository.save(pmiRecord);

        log.info("PMI {} 回退状态已设置为: {}", pmiRecord.getPmiNumber(), fallbackEnabled);
    }

    /**
     * 批量设置PMI回退状态
     */
    @Transactional
    public void batchSetPmiFallbackStatus(List<Long> pmiIds, boolean fallbackEnabled) {
        List<PmiRecord> pmiRecords = pmiRecordRepository.findAllById(pmiIds);

        for (PmiRecord pmiRecord : pmiRecords) {
            pmiRecord.setFallbackEnabled(fallbackEnabled);
        }

        pmiRecordRepository.saveAll(pmiRecords);

        log.info("批量设置 {} 个PMI的回退状态为: {}", pmiRecords.size(), fallbackEnabled);
    }

    /**
     * 检查PMI是否需要回退
     */
    @Transactional(readOnly = true)
    public boolean shouldFallback(String pmiNumber) {
        return pmiRecordRepository.findByPmiNumber(pmiNumber)
                .map(pmi -> Boolean.TRUE.equals(pmi.getFallbackEnabled()))
                .orElse(false);
    }
}
