package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.entity.ZoomUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * PMI设置服务 - 提供PMI检测和设置的通用逻辑
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiSetupService {

    private final ZoomApiService zoomApiService;

    /**
     * PMI设置结果
     */
    public static class PmiSetupResult {
        private final boolean success;
        private final boolean needUpdate;
        private final String message;
        private final ZoomApiResponse<JsonNode> apiResponse;

        public PmiSetupResult(boolean success, boolean needUpdate, String message, ZoomApiResponse<JsonNode> apiResponse) {
            this.success = success;
            this.needUpdate = needUpdate;
            this.message = message;
            this.apiResponse = apiResponse;
        }

        public boolean isSuccess() { return success; }
        public boolean isNeedUpdate() { return needUpdate; }
        public String getMessage() { return message; }
        public ZoomApiResponse<JsonNode> getApiResponse() { return apiResponse; }

        public static PmiSetupResult success(boolean needUpdate, ZoomApiResponse<JsonNode> apiResponse) {
            return new PmiSetupResult(true, needUpdate, "PMI设置成功", apiResponse);
        }

        public static PmiSetupResult error(String message) {
            return new PmiSetupResult(false, false, message, null);
        }
    }

    /**
     * 检测并设置PMI - 通用方法
     * 如果ZoomUser的当前PMI已经是目标PMI，则无需调用设置API
     * 
     * @param zoomUser 目标ZoomUser
     * @param targetPmiNumber 目标PMI号码
     * @param targetPmiPassword 目标PMI密码
     * @return PMI设置结果
     */
    public PmiSetupResult detectAndSetupPmi(ZoomUser zoomUser, String targetPmiNumber, String targetPmiPassword) {
        try {
            log.info("开始检测并设置PMI: userId={}, targetPmi={}", zoomUser.getZoomUserId(), targetPmiNumber);

            // 1. 获取ZoomUser当前信息（使用ZoomUser对应的ZoomAuth）
            ZoomApiResponse<JsonNode> currentUserInfoResponse = zoomApiService.getUserInfo(zoomUser.getZoomUserId(), zoomUser.getZoomAuth());
            if (!currentUserInfoResponse.isSuccess()) {
                String errorMsg = "获取用户信息失败: " + currentUserInfoResponse.getMessage();
                log.error(errorMsg);
                return PmiSetupResult.error(errorMsg);
            }

            // 2. 检测当前PMI是否已经是目标PMI
            boolean needUpdatePmi = true;
            JsonNode userData = currentUserInfoResponse.getData();
            String currentPmi = null;

            // 尝试多个可能的PMI字段名
            if (userData.has("personal_meeting_id")) {
                currentPmi = userData.get("personal_meeting_id").asText();
            } else if (userData.has("pmi")) {
                currentPmi = userData.get("pmi").asText();
            } else if (userData.has("personal_meeting_url")) {
                // 从personal_meeting_url中提取PMI号码
                String personalMeetingUrl = userData.get("personal_meeting_url").asText();
                currentPmi = extractPmiFromUrl(personalMeetingUrl);
            }

            if (currentPmi != null && currentPmi.equals(targetPmiNumber)) {
                log.info("账号当前PMI已经是目标PMI，无需设置: userId={}, currentPmi={}, targetPmi={}",
                        zoomUser.getZoomUserId(), currentPmi, targetPmiNumber);
                needUpdatePmi = false;
            } else {
                log.info("账号当前PMI与目标PMI不同，需要设置: userId={}, currentPmi={}, targetPmi={}",
                        zoomUser.getZoomUserId(), currentPmi, targetPmiNumber);
            }

            // 3. 根据检测结果决定是否调用设置API
            ZoomApiResponse<JsonNode> apiResponse;

            if (needUpdatePmi) {
                log.info("开始设置PMI: userId={}, pmi={}", zoomUser.getZoomUserId(), targetPmiNumber);

                // 记录原始PMI，用于回退
                String originalPmi = zoomUser.getOriginalPmi();

                try {
                    // 添加调试日志确认密码参数传递
                    log.info("调用updateUserPmi - userId={}, pmiNumber={}, pmiPassword={}, hasPassword={}",
                            zoomUser.getZoomUserId(), targetPmiNumber, targetPmiPassword,
                            targetPmiPassword != null && !targetPmiPassword.trim().isEmpty());

                    // 调用Zoom API设置PMI（使用ZoomUser对应的ZoomAuth）
                    apiResponse = zoomApiService.updateUserPmi(
                            zoomUser.getZoomUserId(),
                            targetPmiNumber,
                            targetPmiPassword,
                            zoomUser.getZoomAuth());

                    if (!apiResponse.isSuccess()) {
                        String errorMsg = "设置PMI失败: " + apiResponse.getMessage();
                        log.error(errorMsg);
                        return PmiSetupResult.error(errorMsg);
                    }

                    log.info("PMI设置API调用成功: userId={}, pmi={}", zoomUser.getZoomUserId(), targetPmiNumber);

                } catch (Exception e) {
                    // PMI设置失败，需要回退到原始PMI
                    log.error("PMI设置过程中发生异常，开始回退: userId={}, originalPmi={}",
                            zoomUser.getZoomUserId(), originalPmi, e);

                    try {
                        // 回退到原始PMI
                        zoomApiService.restoreUserOriginalPmi(zoomUser.getZoomUserId(), originalPmi);
                        log.info("PMI回退成功: userId={}, restoredPmi={}", zoomUser.getZoomUserId(), originalPmi);
                    } catch (Exception rollbackException) {
                        log.error("PMI回退失败: userId={}", zoomUser.getZoomUserId(), rollbackException);
                    }

                    String errorMsg = "PMI设置验证失败，已回退到原始PMI: " + e.getMessage();
                    return PmiSetupResult.error(errorMsg);
                }

                // PMI设置成功后，尝试获取PMI会议信息以获取start_url（使用ZoomUser对应的ZoomAuth）
                log.info("尝试获取PMI会议信息以获取start_url");
                ZoomApiResponse<JsonNode> pmiMeetingResponse = zoomApiService.getUserPmiMeeting(zoomUser.getZoomUserId(), zoomUser.getZoomAuth());
                if (pmiMeetingResponse.isSuccess()) {
                    log.info("成功获取PMI会议信息，返回包含start_url的响应");
                    apiResponse = pmiMeetingResponse;
                } else {
                    log.warn("获取PMI会议信息失败，尝试创建临时PMI会议: {}", pmiMeetingResponse.getMessage());
                    // 备用方案：创建临时PMI会议（使用ZoomUser对应的ZoomAuth）
                    ZoomApiResponse<JsonNode> tempMeetingResponse = zoomApiService.createPmiMeeting(zoomUser.getZoomUserId(), targetPmiNumber, zoomUser.getZoomAuth());
                    if (tempMeetingResponse.isSuccess()) {
                        log.info("成功创建临时PMI会议作为备用方案");
                        apiResponse = tempMeetingResponse;
                    } else {
                        log.warn("创建临时PMI会议也失败，使用PMI设置响应: {}", tempMeetingResponse.getMessage());
                        // apiResponse 保持为 PMI设置的响应
                    }
                }
            } else {
                log.info("PMI无需设置，使用当前用户信息: userId={}", zoomUser.getZoomUserId());
                // 使用当前用户信息作为API响应
                apiResponse = currentUserInfoResponse;
            }

            return PmiSetupResult.success(needUpdatePmi, apiResponse);

        } catch (Exception e) {
            String errorMsg = "PMI检测和设置过程中发生异常: " + e.getMessage();
            log.error(errorMsg, e);
            return PmiSetupResult.error(errorMsg);
        }
    }

    /**
     * 从PMI URL中提取PMI号码
     */
    private String extractPmiFromUrl(String personalMeetingUrl) {
        if (personalMeetingUrl == null || personalMeetingUrl.isEmpty()) {
            return null;
        }

        try {
            // personal_meeting_url格式通常是: https://zoom.us/j/1234567890
            // 或者: https://company.zoom.us/j/1234567890
            String[] parts = personalMeetingUrl.split("/j/");
            if (parts.length >= 2) {
                String pmiPart = parts[1];
                // 移除可能的查询参数
                if (pmiPart.contains("?")) {
                    pmiPart = pmiPart.split("\\?")[0];
                }
                return pmiPart;
            }
        } catch (Exception e) {
            log.warn("从URL提取PMI号码失败: url={}, error={}", personalMeetingUrl, e.getMessage());
        }

        return null;
    }

    /**
     * 检测ZoomUser当前PMI
     * 
     * @param zoomUser 目标ZoomUser
     * @return 当前PMI号码，如果获取失败返回null
     */
    public String detectCurrentPmi(ZoomUser zoomUser) {
        try {
            ZoomApiResponse<JsonNode> userInfoResponse = zoomApiService.getUserInfo(zoomUser.getZoomUserId(), zoomUser.getZoomAuth());
            if (!userInfoResponse.isSuccess()) {
                log.error("获取用户信息失败: {}", userInfoResponse.getMessage());
                return null;
            }

            JsonNode userData = userInfoResponse.getData();
            String currentPmi = null;

            if (userData.has("pmi")) {
                currentPmi = userData.get("pmi").asText();
            } else if (userData.has("personal_meeting_url")) {
                String personalMeetingUrl = userData.get("personal_meeting_url").asText();
                currentPmi = extractPmiFromUrl(personalMeetingUrl);
            }

            log.info("检测到ZoomUser当前PMI: userId={}, currentPmi={}", zoomUser.getZoomUserId(), currentPmi);
            return currentPmi;

        } catch (Exception e) {
            log.error("检测ZoomUser当前PMI失败: userId={}, error={}", zoomUser.getZoomUserId(), e.getMessage());
            return null;
        }
    }
}
