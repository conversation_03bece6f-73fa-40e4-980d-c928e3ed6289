package com.zoombus.service;

import com.zoombus.config.PmiTaskSchedulingConfig;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PMI任务监控告警服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiTaskAlertService {
    
    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiTaskSchedulingConfig schedulingConfig;
    private final DynamicTaskManager dynamicTaskManager;
    
    @Autowired(required = false)
    private PmiTaskWebSocketService webSocketService;
    
    // 告警阈值
    private static final int FAILURE_RATE_THRESHOLD = 20; // 失败率超过20%告警
    private static final int PENDING_TASKS_THRESHOLD = 50; // 待执行任务超过50个告警
    private static final int RUNNING_TASKS_THRESHOLD = 10; // 运行中任务超过10个告警
    
    /**
     * 定期检查系统健康状态并发送告警
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void checkSystemHealth() {
        try {
            log.debug("开始检查PMI任务系统健康状态");
            
            // 检查任务失败率
            checkTaskFailureRate();
            
            // 检查任务堆积情况
            checkTaskBacklog();
            
            // 检查调度器状态
            checkSchedulerHealth();
            
            // 检查即将执行的任务
            checkUpcomingTasks();
            
        } catch (Exception e) {
            log.error("检查PMI任务系统健康状态失败", e);
            sendAlert("HEALTH_CHECK_ERROR", "系统健康检查失败: " + e.getMessage(), "ERROR");
        }
    }
    
    /**
     * 检查任务失败率
     */
    private void checkTaskFailureRate() {
        try {
            // 检查最近1小时的任务执行情况
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            LocalDateTime now = LocalDateTime.now();
            
            List<Object[]> stats = taskRepository.countTaskExecutionsByStatus(oneHourAgo, now);
            
            long totalTasks = 0;
            long failedTasks = 0;
            
            for (Object[] stat : stats) {
                Long count = (Long) stat[0];
                PmiScheduleWindowTask.TaskStatus status = (PmiScheduleWindowTask.TaskStatus) stat[1];
                
                totalTasks += count;
                if (status == PmiScheduleWindowTask.TaskStatus.FAILED) {
                    failedTasks += count;
                }
            }
            
            if (totalTasks > 0) {
                double failureRate = (double) failedTasks / totalTasks * 100;
                
                if (failureRate > FAILURE_RATE_THRESHOLD) {
                    String message = String.format("PMI任务失败率过高: %.1f%% (%d/%d), 最近1小时", 
                            failureRate, failedTasks, totalTasks);
                    sendAlert("HIGH_FAILURE_RATE", message, "ERROR");
                }
            }
            
        } catch (Exception e) {
            log.error("检查任务失败率失败", e);
        }
    }
    
    /**
     * 检查任务堆积情况
     */
    private void checkTaskBacklog() {
        try {
            // 检查待执行任务数量
            long pendingTasks = taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.SCHEDULED);
            if (pendingTasks > PENDING_TASKS_THRESHOLD) {
                String message = String.format("PMI待执行任务过多: %d个任务等待执行", pendingTasks);
                sendAlert("TASK_BACKLOG", message, "WARN");
            }
            
            // 检查运行中任务数量
            long runningTasks = taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.EXECUTING);
            if (runningTasks > RUNNING_TASKS_THRESHOLD) {
                String message = String.format("PMI运行中任务过多: %d个任务正在执行", runningTasks);
                sendAlert("TOO_MANY_RUNNING_TASKS", message, "WARN");
            }
            
        } catch (Exception e) {
            log.error("检查任务堆积情况失败", e);
        }
    }
    
    /**
     * 检查调度器健康状态
     */
    private void checkSchedulerHealth() {
        try {
            DynamicTaskManager.SchedulerStatus status = dynamicTaskManager.getSchedulerStatus();
            
            if (!status.isActive()) {
                String message = String.format("PMI任务调度器状态异常: %s", status.getStatus());
                sendAlert("SCHEDULER_UNHEALTHY", message, "ERROR");
            }
            
            // 推送调度器健康状态
            if (webSocketService != null) {
                webSocketService.pushSchedulerHealth(
                        status.isActive(), 
                        status.getStatus(), 
                        status.getTotalScheduledTasks(), 
                        status.getRunningTasks()
                );
            }
            
        } catch (Exception e) {
            log.error("检查调度器健康状态失败", e);
        }
    }
    
    /**
     * 检查即将执行的任务
     */
    private void checkUpcomingTasks() {
        try {
            // 检查未来10分钟内即将执行的任务
            LocalDateTime tenMinutesLater = LocalDateTime.now().plusMinutes(10);
            List<PmiScheduleWindowTask> upcomingTasks = taskRepository.findUpcomingTasks(
                    PmiScheduleWindowTask.TaskStatus.SCHEDULED, tenMinutesLater);
            
            for (PmiScheduleWindowTask task : upcomingTasks) {
                LocalDateTime scheduledTime = task.getScheduledTime();
                long minutesUntilExecution = java.time.Duration.between(
                        LocalDateTime.now(), scheduledTime).toMinutes();
                
                // 只对5分钟内即将执行的重要任务发送提醒
                if (minutesUntilExecution <= 5 && minutesUntilExecution > 0) {
                    if (webSocketService != null) {
                        webSocketService.pushUpcomingTaskReminder(task, (int) minutesUntilExecution);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("检查即将执行的任务失败", e);
        }
    }
    
    /**
     * 发送告警
     */
    private void sendAlert(String alertType, String message, String level) {
        log.warn("PMI任务系统告警: type={}, level={}, message={}", alertType, level, message);
        
        // 通过WebSocket推送告警
        if (webSocketService != null) {
            webSocketService.pushSystemAlert(alertType, message, level);
        }
        
        // 这里可以集成其他告警渠道，如邮件、短信、钉钉等
        // sendEmailAlert(alertType, message, level);
        // sendSmsAlert(alertType, message, level);
        // sendDingTalkAlert(alertType, message, level);
    }
    
    /**
     * 手动触发健康检查
     */
    public void triggerHealthCheck() {
        log.info("手动触发PMI任务系统健康检查");
        checkSystemHealth();
    }
    
    /**
     * 获取告警配置
     */
    public AlertConfiguration getAlertConfiguration() {
        return new AlertConfiguration(
                FAILURE_RATE_THRESHOLD,
                PENDING_TASKS_THRESHOLD,
                RUNNING_TASKS_THRESHOLD,
                schedulingConfig.isEnableTaskMonitoring()
        );
    }
    
    /**
     * 告警配置
     */
    public static class AlertConfiguration {
        private final int failureRateThreshold;
        private final int pendingTasksThreshold;
        private final int runningTasksThreshold;
        private final boolean monitoringEnabled;
        
        public AlertConfiguration(int failureRateThreshold, int pendingTasksThreshold, 
                                int runningTasksThreshold, boolean monitoringEnabled) {
            this.failureRateThreshold = failureRateThreshold;
            this.pendingTasksThreshold = pendingTasksThreshold;
            this.runningTasksThreshold = runningTasksThreshold;
            this.monitoringEnabled = monitoringEnabled;
        }
        
        public int getFailureRateThreshold() { return failureRateThreshold; }
        public int getPendingTasksThreshold() { return pendingTasksThreshold; }
        public int getRunningTasksThreshold() { return runningTasksThreshold; }
        public boolean isMonitoringEnabled() { return monitoringEnabled; }
    }
}
