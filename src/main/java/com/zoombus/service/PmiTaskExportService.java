package com.zoombus.service;

import com.zoombus.dto.PmiScheduledTaskInfo;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * PMI任务数据导出服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiTaskExportService {
    
    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiTaskManagementService taskManagementService;
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 导出任务数据为Excel
     */
    public byte[] exportTasksToExcel(ExportRequest request) throws IOException {
        log.info("开始导出PMI任务数据: {}", request);
        
        List<PmiScheduledTaskInfo> tasks = getTasksForExport(request);
        
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            // 创建工作表
            Sheet sheet = workbook.createSheet("PMI任务数据");
            
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // 创建表头
            createHeader(sheet, headerStyle);
            
            // 填充数据
            fillData(sheet, tasks, dataStyle);
            
            // 自动调整列宽
            autoSizeColumns(sheet);
            
            workbook.write(outputStream);
            
            log.info("PMI任务数据导出完成: 记录数={}", tasks.size());
            return outputStream.toByteArray();
        }
    }
    
    /**
     * 获取要导出的任务数据
     */
    private List<PmiScheduledTaskInfo> getTasksForExport(ExportRequest request) {
        if (request.getTaskIds() != null && !request.getTaskIds().isEmpty()) {
            // 导出指定任务
            return request.getTaskIds().stream()
                    .map(taskManagementService::getTaskDetail)
                    .collect(java.util.stream.Collectors.toList());
        } else {
            // 根据条件导出
            return taskManagementService.getPmiTasks(
                    1, 
                    Integer.MAX_VALUE, 
                    request.getStatus(), 
                    request.getTaskType()
            ).getContent();
        }
    }
    
    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        
        return style;
    }
    
    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        return style;
    }
    
    /**
     * 创建表头
     */
    private void createHeader(Sheet sheet, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "任务ID", "PMI窗口ID", "任务类型", "PMI号码", "用户名", 
            "计划执行时间", "实际执行时间", "状态", "重试次数", "错误信息", "创建时间"
        };
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }
    
    /**
     * 填充数据
     */
    private void fillData(Sheet sheet, List<PmiScheduledTaskInfo> tasks, CellStyle dataStyle) {
        int rowNum = 1;
        
        for (PmiScheduledTaskInfo task : tasks) {
            Row row = sheet.createRow(rowNum++);
            
            // 任务ID
            Cell cell0 = row.createCell(0);
            cell0.setCellValue(task.getId());
            cell0.setCellStyle(dataStyle);
            
            // PMI窗口ID
            Cell cell1 = row.createCell(1);
            cell1.setCellValue(task.getPmiWindowId());
            cell1.setCellStyle(dataStyle);
            
            // 任务类型
            Cell cell2 = row.createCell(2);
            cell2.setCellValue(getTaskTypeText(task.getTaskType()));
            cell2.setCellStyle(dataStyle);
            
            // PMI号码
            Cell cell3 = row.createCell(3);
            cell3.setCellValue(task.getPmiNumber() != null ? task.getPmiNumber() : "");
            cell3.setCellStyle(dataStyle);
            
            // 用户名
            Cell cell4 = row.createCell(4);
            cell4.setCellValue(task.getUserName() != null ? task.getUserName() : "");
            cell4.setCellStyle(dataStyle);
            
            // 计划执行时间
            Cell cell5 = row.createCell(5);
            cell5.setCellValue(task.getScheduledTime() != null ? 
                    task.getScheduledTime().format(DATE_FORMATTER) : "");
            cell5.setCellStyle(dataStyle);
            
            // 实际执行时间
            Cell cell6 = row.createCell(6);
            cell6.setCellValue(task.getActualExecutionTime() != null ? 
                    task.getActualExecutionTime().format(DATE_FORMATTER) : "");
            cell6.setCellStyle(dataStyle);
            
            // 状态
            Cell cell7 = row.createCell(7);
            cell7.setCellValue(getStatusText(task.getStatus()));
            cell7.setCellStyle(dataStyle);
            
            // 重试次数
            Cell cell8 = row.createCell(8);
            cell8.setCellValue(task.getRetryCount() != null ? task.getRetryCount() : 0);
            cell8.setCellStyle(dataStyle);
            
            // 错误信息
            Cell cell9 = row.createCell(9);
            cell9.setCellValue(task.getErrorMessage() != null ? task.getErrorMessage() : "");
            cell9.setCellStyle(dataStyle);
            
            // 创建时间
            Cell cell10 = row.createCell(10);
            cell10.setCellValue(task.getCreatedAt() != null ? 
                    task.getCreatedAt().format(DATE_FORMATTER) : "");
            cell10.setCellStyle(dataStyle);
        }
    }
    
    /**
     * 自动调整列宽
     */
    private void autoSizeColumns(Sheet sheet) {
        for (int i = 0; i < 11; i++) {
            sheet.autoSizeColumn(i);
            // 设置最大列宽，避免过宽
            int columnWidth = sheet.getColumnWidth(i);
            if (columnWidth > 15000) {
                sheet.setColumnWidth(i, 15000);
            }
        }
    }
    
    /**
     * 获取任务类型文本
     */
    private String getTaskTypeText(String taskType) {
        if (taskType == null) return "";
        switch (taskType) {
            case "PMI_WINDOW_OPEN":
                return "PMI开启";
            case "PMI_WINDOW_CLOSE":
                return "PMI关闭";
            default:
                return taskType;
        }
    }
    
    /**
     * 获取状态文本
     */
    private String getStatusText(String status) {
        if (status == null) return "";
        switch (status) {
            case "SCHEDULED":
                return "已调度";
            case "EXECUTING":
                return "执行中";
            case "COMPLETED":
                return "已完成";
            case "FAILED":
                return "失败";
            case "CANCELLED":
                return "已取消";
            default:
                return status;
        }
    }
    
    /**
     * 导出请求
     */
    public static class ExportRequest {
        private List<Long> taskIds;
        private String status;
        private String taskType;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        
        // Getters and Setters
        public List<Long> getTaskIds() { return taskIds; }
        public void setTaskIds(List<Long> taskIds) { this.taskIds = taskIds; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getTaskType() { return taskType; }
        public void setTaskType(String taskType) { this.taskType = taskType; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        @Override
        public String toString() {
            return String.format("ExportRequest{taskIds=%s, status='%s', taskType='%s', startTime=%s, endTime=%s}",
                    taskIds, status, taskType, startTime, endTime);
        }
    }
}
