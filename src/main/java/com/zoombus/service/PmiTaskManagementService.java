package com.zoombus.service;

import com.zoombus.dto.*;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.PmiScheduleWindowRepository;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PMI任务管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiTaskManagementService {
    
    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiScheduleWindowRepository windowRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final DynamicTaskManager dynamicTaskManager;
    private final PmiWindowTaskExecutor taskExecutor;
    
    /**
     * 获取PMI任务列表
     */
    public PageResult<PmiScheduledTaskInfo> getPmiTasks(int page, int size, String status, String taskType) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        
        Page<PmiScheduleWindowTask> taskPage;
        
        if (status != null && taskType != null) {
            PmiScheduleWindowTask.TaskStatus taskStatus = PmiScheduleWindowTask.TaskStatus.valueOf(status);
            PmiScheduleWindowTask.TaskType type = PmiScheduleWindowTask.TaskType.valueOf(taskType);
            taskPage = taskRepository.findByStatusAndTaskType(taskStatus, type, pageable);
        } else if (status != null) {
            PmiScheduleWindowTask.TaskStatus taskStatus = PmiScheduleWindowTask.TaskStatus.valueOf(status);
            taskPage = taskRepository.findByStatus(taskStatus, pageable);
        } else if (taskType != null) {
            PmiScheduleWindowTask.TaskType type = PmiScheduleWindowTask.TaskType.valueOf(taskType);
            taskPage = taskRepository.findByTaskType(type, pageable);
        } else {
            taskPage = taskRepository.findAll(pageable);
        }
        
        List<PmiScheduledTaskInfo> taskInfos = taskPage.getContent().stream()
                .map(this::convertToTaskInfo)
                .collect(Collectors.toList());
        
        return new PageResult<>(
                taskInfos,
                taskPage.getTotalElements(),
                taskPage.getTotalPages(),
                page,
                size
        );
    }
    
    /**
     * 手动执行PMI任务
     */
    @Transactional
    public void executePmiTask(Long taskId) {
        PmiScheduleWindowTask task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
        
        if (task.getStatus() != PmiScheduleWindowTask.TaskStatus.SCHEDULED) {
            throw new IllegalStateException("只能执行已调度状态的任务");
        }
        
        // 立即执行任务
        if (task.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
            taskExecutor.executeOpenTask(task.getPmiWindowId(), task.getId());
        } else {
            taskExecutor.executeCloseTask(task.getPmiWindowId(), task.getId());
        }
        
        log.info("手动执行PMI任务成功: taskId={}, type={}", taskId, task.getTaskType());
    }
    
    /**
     * 取消PMI任务
     */
    @Transactional
    public void cancelPmiTask(Long taskId) {
        PmiScheduleWindowTask task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
        
        if (!task.canCancel()) {
            throw new IllegalStateException("当前任务状态不允许取消");
        }
        
        // 取消调度的任务
        boolean cancelled = dynamicTaskManager.cancelTask(task.getTaskKey());
        
        if (cancelled) {
            log.info("取消PMI任务成功: taskId={}", taskId);
        } else {
            log.warn("取消PMI任务失败: taskId={}", taskId);
            throw new RuntimeException("取消任务失败");
        }
    }
    
    /**
     * 重新调度PMI任务
     */
    @Transactional
    public void reschedulePmiTask(Long taskId, RescheduleRequest request) {
        PmiScheduleWindowTask task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
        
        if (!task.canReschedule()) {
            throw new IllegalStateException("当前任务状态不允许重新调度");
        }
        
        // 重新调度任务（这会创建新的任务记录）
        Long newTaskId = dynamicTaskManager.rescheduleTask(task.getTaskKey(), request.getNewExecuteTime());

        // 标记原任务为已取消
        task.setStatus(PmiScheduleWindowTask.TaskStatus.CANCELLED);
        task.setErrorMessage("任务已重新调度，新任务ID: " + newTaskId);
        taskRepository.save(task);
        
        log.info("重新调度PMI任务成功: taskId={}, newTime={}", taskId, request.getNewExecuteTime());
    }
    
    /**
     * 获取PMI任务统计
     */
    public PmiTaskStatistics getPmiTaskStatistics() {
        PmiTaskStatistics statistics = new PmiTaskStatistics();
        
        // 基础统计
        statistics.setTotalTasks(taskRepository.count());
        statistics.setScheduledTasks(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.SCHEDULED));
        statistics.setExecutingTasks(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.EXECUTING));
        statistics.setCompletedTasks(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.COMPLETED));
        statistics.setFailedTasks(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.FAILED));
        statistics.setCancelledTasks(taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.CANCELLED));
        
        // 计算成功率
        statistics.calculateSuccessRate();
        
        // 24小时内统计
        LocalDateTime last24Hours = LocalDateTime.now().minusHours(24);
        List<Object[]> last24HoursStats = taskRepository.countTaskExecutionsByStatus(
                last24Hours, LocalDateTime.now());
        
        long tasksLast24Hours = 0;
        long successfulLast24Hours = 0;
        long failedLast24Hours = 0;
        
        for (Object[] stat : last24HoursStats) {
            Long count = (Long) stat[0];
            PmiScheduleWindowTask.TaskStatus status = (PmiScheduleWindowTask.TaskStatus) stat[1];
            
            tasksLast24Hours += count;
            if (status == PmiScheduleWindowTask.TaskStatus.COMPLETED) {
                successfulLast24Hours += count;
            } else if (status == PmiScheduleWindowTask.TaskStatus.FAILED) {
                failedLast24Hours += count;
            }
        }
        
        statistics.setTasksLast24Hours(tasksLast24Hours);
        statistics.setSuccessfulTasksLast24Hours(successfulLast24Hours);
        statistics.setFailedTasksLast24Hours(failedLast24Hours);
        
        // 按类型统计
        List<Object[]> typeStats = taskRepository.countTasksByType();
        Map<String, Long> tasksByType = new HashMap<>();
        for (Object[] stat : typeStats) {
            PmiScheduleWindowTask.TaskType type = (PmiScheduleWindowTask.TaskType) stat[0];
            Long count = (Long) stat[1];
            tasksByType.put(type.name(), count);
        }
        statistics.setTasksByType(tasksByType);
        
        return statistics;
    }
    
    /**
     * 获取即将执行的PMI任务
     */
    public List<PmiScheduledTaskInfo> getUpcomingPmiTasks(int hours) {
        LocalDateTime maxTime = LocalDateTime.now().plusHours(hours);
        List<PmiScheduleWindowTask> tasks = taskRepository.findUpcomingTasks(
                PmiScheduleWindowTask.TaskStatus.SCHEDULED, maxTime);
        
        return tasks.stream()
                .map(this::convertToTaskInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取PMI任务历史
     */
    public PageResult<PmiScheduledTaskInfo> getPmiTaskHistory(int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "actualExecutionTime"));
        
        Page<PmiScheduleWindowTask> taskPage = taskRepository.findByActualExecutionTimeIsNotNull(pageable);
        
        List<PmiScheduledTaskInfo> taskInfos = taskPage.getContent().stream()
                .map(this::convertToTaskInfo)
                .collect(Collectors.toList());
        
        return new PageResult<>(
                taskInfos,
                taskPage.getTotalElements(),
                taskPage.getTotalPages(),
                page,
                size
        );
    }
    
    /**
     * 获取窗口的任务
     */
    public List<PmiScheduledTaskInfo> getWindowTasks(Long windowId) {
        List<PmiScheduleWindowTask> tasks = taskRepository.findByPmiWindowId(windowId);
        
        return tasks.stream()
                .map(this::convertToTaskInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * 批量取消任务
     */
    @Transactional
    public int batchCancelTasks(List<Long> taskIds) {
        int cancelledCount = 0;
        
        for (Long taskId : taskIds) {
            try {
                cancelPmiTask(taskId);
                cancelledCount++;
            } catch (Exception e) {
                log.warn("批量取消任务失败: taskId={}", taskId, e);
            }
        }
        
        return cancelledCount;
    }
    
    /**
     * 批量重新调度任务
     */
    @Transactional
    public int batchRescheduleTasks(BatchRescheduleRequest request) {
        int rescheduledCount = 0;
        
        RescheduleRequest rescheduleRequest = new RescheduleRequest();
        rescheduleRequest.setNewExecuteTime(request.getNewExecuteTime());
        rescheduleRequest.setReason(request.getReason());
        
        for (Long taskId : request.getTaskIds()) {
            try {
                reschedulePmiTask(taskId, rescheduleRequest);
                rescheduledCount++;
            } catch (Exception e) {
                log.warn("批量重新调度任务失败: taskId={}", taskId, e);
            }
        }
        
        return rescheduledCount;
    }
    
    /**
     * 获取任务详情
     */
    public PmiScheduledTaskInfo getTaskDetail(Long taskId) {
        PmiScheduleWindowTask task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
        
        return convertToTaskInfo(task);
    }

    /**
     * 转换任务实体为DTO
     */
    private PmiScheduledTaskInfo convertToTaskInfo(PmiScheduleWindowTask task) {
        PmiScheduledTaskInfo info = PmiScheduledTaskInfo.fromEntity(task);

        // 获取关联的PMI信息
        try {
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(task.getPmiWindowId());
            if (windowOpt.isPresent()) {
                PmiScheduleWindow window = windowOpt.get();
                info.setPmiRecordId(window.getPmiRecordId()); // 设置PMI记录ID
                Optional<PmiRecord> pmiOpt = pmiRecordRepository.findById(window.getPmiRecordId());
                if (pmiOpt.isPresent()) {
                    PmiRecord pmi = pmiOpt.get();
                    info.setPmiNumber(pmi.getPmiNumber());
                    info.setPmiPassword(pmi.getPmiPassword());
                    // 这里可以根据需要添加用户名信息
                    // info.setUserName(pmi.getUserName());
                }
            }
        } catch (Exception e) {
            log.warn("获取PMI信息失败: windowId={}", task.getPmiWindowId(), e);
        }

        return info;
    }
}
