package com.zoombus.service;

import com.zoombus.config.PmiTaskSchedulingConfig;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PMI任务重试服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiTaskRetryService {
    
    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiWindowTaskExecutor taskExecutor;
    private final PmiTaskSchedulingConfig schedulingConfig;

    @Autowired(required = false)
    private PmiTaskWebSocketService webSocketService;
    
    /**
     * 定期检查并重试失败的任务
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    @Transactional
    public void retryFailedTasks() {
        try {
            int maxRetryCount = schedulingConfig.getRetry().getMaxRetryCount();
            List<PmiScheduleWindowTask> retryableTasks = taskRepository.findRetryableTasks(
                    PmiScheduleWindowTask.TaskStatus.FAILED, maxRetryCount);
            
            if (retryableTasks.isEmpty()) {
                log.debug("没有需要重试的PMI任务");
                return;
            }
            
            log.info("开始重试失败的PMI任务: 数量={}", retryableTasks.size());
            
            int successCount = 0;
            int failedCount = 0;
            
            for (PmiScheduleWindowTask task : retryableTasks) {
                try {
                    // 检查是否应该重试（基于指数退避策略）
                    if (shouldRetryTask(task)) {
                        retryTask(task);
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("重试PMI任务失败: taskId={}", task.getId(), e);
                    failedCount++;
                }
            }
            
            log.info("PMI任务重试完成: 成功={}, 失败={}", successCount, failedCount);
            
        } catch (Exception e) {
            log.error("PMI任务重试服务执行失败", e);
        }
    }
    
    /**
     * 判断任务是否应该重试
     */
    private boolean shouldRetryTask(PmiScheduleWindowTask task) {
        if (task.getRetryCount() >= schedulingConfig.getRetry().getMaxRetryCount()) {
            return false;
        }
        
        // 如果启用了指数退避，计算下次重试时间
        if (schedulingConfig.getRetry().isExponentialBackoff()) {
            LocalDateTime nextRetryTime = calculateNextRetryTime(task);
            return LocalDateTime.now().isAfter(nextRetryTime);
        } else {
            // 固定间隔重试
            int intervalMinutes = schedulingConfig.getRetry().getRetryIntervalMinutes();
            LocalDateTime nextRetryTime = task.getUpdatedAt().plusMinutes(intervalMinutes);
            return LocalDateTime.now().isAfter(nextRetryTime);
        }
    }
    
    /**
     * 计算下次重试时间（指数退避策略）
     */
    private LocalDateTime calculateNextRetryTime(PmiScheduleWindowTask task) {
        int baseIntervalMinutes = schedulingConfig.getRetry().getRetryIntervalMinutes();
        int retryCount = task.getRetryCount();
        
        // 指数退避：2^retryCount * baseInterval
        long delayMinutes = (long) (Math.pow(2, retryCount) * baseIntervalMinutes);
        
        // 最大延迟不超过4小时
        delayMinutes = Math.min(delayMinutes, 240);
        
        return task.getUpdatedAt().plusMinutes(delayMinutes);
    }
    
    /**
     * 重试任务
     */
    private void retryTask(PmiScheduleWindowTask task) {
        log.info("重试PMI任务: taskId={}, type={}, retryCount={}",
                task.getId(), task.getTaskType(), task.getRetryCount());

        // 推送重试告警
        if (webSocketService != null) {
            webSocketService.pushTaskRetryAlert(task);
        }

        try {
            // 更新任务状态为执行中
            task.setStatus(PmiScheduleWindowTask.TaskStatus.EXECUTING);
            task.setActualExecutionTime(LocalDateTime.now());
            task.setErrorMessage(null);
            taskRepository.save(task);
            
            // 执行任务
            if (task.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                taskExecutor.executeOpenTask(task.getPmiWindowId(), task.getId());
            } else {
                taskExecutor.executeCloseTask(task.getPmiWindowId(), task.getId());
            }
            
            log.info("PMI任务重试成功: taskId={}", task.getId());
            
        } catch (Exception e) {
            log.error("PMI任务重试执行失败: taskId={}", task.getId(), e);
            
            // 更新失败状态
            task.markAsFailed("重试失败: " + e.getMessage());
            taskRepository.save(task);
        }
    }
    
    /**
     * 手动重试指定任务
     */
    @Transactional
    public boolean manualRetryTask(Long taskId) {
        try {
            PmiScheduleWindowTask task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
            
            if (task.getStatus() != PmiScheduleWindowTask.TaskStatus.FAILED) {
                throw new IllegalStateException("只能重试失败状态的任务");
            }
            
            if (task.getRetryCount() >= schedulingConfig.getRetry().getMaxRetryCount()) {
                throw new IllegalStateException("任务重试次数已达上限");
            }
            
            retryTask(task);
            return true;
            
        } catch (Exception e) {
            log.error("手动重试PMI任务失败: taskId={}", taskId, e);
            return false;
        }
    }
    
    /**
     * 重置任务重试次数
     */
    @Transactional
    public void resetTaskRetryCount(Long taskId) {
        PmiScheduleWindowTask task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
        
        task.setRetryCount(0);
        task.setErrorMessage(null);
        taskRepository.save(task);
        
        log.info("重置PMI任务重试次数: taskId={}", taskId);
    }
}
