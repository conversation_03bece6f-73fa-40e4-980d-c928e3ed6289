package com.zoombus.service;

import com.zoombus.config.PmiTaskSchedulingConfig;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PMI任务超时处理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiTaskTimeoutService {
    
    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiTaskSchedulingConfig schedulingConfig;

    @Autowired(required = false)
    private PmiTaskWebSocketService webSocketService;
    
    /**
     * 定期检查超时的任务
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 600000) // 10分钟
    @Transactional
    public void checkTimeoutTasks() {
        try {
            int timeoutMinutes = schedulingConfig.getPerformance().getTaskTimeoutMinutes();
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);
            
            // 查找长时间运行的任务
            List<PmiScheduleWindowTask> timeoutTasks = taskRepository.findLongRunningTasks(
                    PmiScheduleWindowTask.TaskStatus.EXECUTING, timeoutThreshold);
            
            if (timeoutTasks.isEmpty()) {
                log.debug("没有超时的PMI任务");
                return;
            }
            
            log.warn("发现超时的PMI任务: 数量={}, 超时阈值={}分钟", timeoutTasks.size(), timeoutMinutes);
            
            for (PmiScheduleWindowTask task : timeoutTasks) {
                handleTimeoutTask(task);
            }
            
        } catch (Exception e) {
            log.error("检查PMI任务超时失败", e);
        }
    }
    
    /**
     * 处理超时任务
     */
    private void handleTimeoutTask(PmiScheduleWindowTask task) {
        try {
            long runningMinutes = java.time.Duration.between(
                    task.getActualExecutionTime(), LocalDateTime.now()).toMinutes();
            
            log.warn("处理超时PMI任务: taskId={}, type={}, 运行时间={}分钟", 
                    task.getId(), task.getTaskType(), runningMinutes);
            
            // 标记任务为失败
            String errorMessage = String.format("任务执行超时，运行时间: %d分钟", runningMinutes);
            task.markAsFailed(errorMessage);
            taskRepository.save(task);

            // 推送超时告警
            if (webSocketService != null) {
                webSocketService.pushTaskTimeoutAlert(task, runningMinutes);
                webSocketService.pushTaskStatusChange(task);
            }

            log.info("超时PMI任务已标记为失败: taskId={}", task.getId());
            
        } catch (Exception e) {
            log.error("处理超时PMI任务失败: taskId={}", task.getId(), e);
        }
    }
    
    /**
     * 检查指定任务是否超时
     */
    public boolean isTaskTimeout(Long taskId) {
        try {
            PmiScheduleWindowTask task = taskRepository.findById(taskId).orElse(null);
            if (task == null || task.getStatus() != PmiScheduleWindowTask.TaskStatus.EXECUTING) {
                return false;
            }
            
            if (task.getActualExecutionTime() == null) {
                return false;
            }
            
            int timeoutMinutes = schedulingConfig.getPerformance().getTaskTimeoutMinutes();
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);
            
            return task.getActualExecutionTime().isBefore(timeoutThreshold);
            
        } catch (Exception e) {
            log.error("检查PMI任务超时状态失败: taskId={}", taskId, e);
            return false;
        }
    }
    
    /**
     * 手动标记任务为超时
     */
    @Transactional
    public boolean markTaskAsTimeout(Long taskId, String reason) {
        try {
            PmiScheduleWindowTask task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
            
            if (task.getStatus() != PmiScheduleWindowTask.TaskStatus.EXECUTING) {
                throw new IllegalStateException("只能标记执行中的任务为超时");
            }
            
            String errorMessage = "手动标记为超时";
            if (reason != null && !reason.trim().isEmpty()) {
                errorMessage += ": " + reason;
            }
            
            task.markAsFailed(errorMessage);
            taskRepository.save(task);
            
            log.info("手动标记PMI任务为超时: taskId={}, reason={}", taskId, reason);
            return true;
            
        } catch (Exception e) {
            log.error("手动标记PMI任务超时失败: taskId={}", taskId, e);
            return false;
        }
    }
    
    /**
     * 获取当前运行时间超过阈值的任务统计
     */
    public TaskTimeoutStatistics getTimeoutStatistics() {
        try {
            int timeoutMinutes = schedulingConfig.getPerformance().getTaskTimeoutMinutes();
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);
            
            List<PmiScheduleWindowTask> timeoutTasks = taskRepository.findLongRunningTasks(
                    PmiScheduleWindowTask.TaskStatus.EXECUTING, timeoutThreshold);
            
            List<PmiScheduleWindowTask> allExecutingTasks = taskRepository.findByStatus(
                    PmiScheduleWindowTask.TaskStatus.EXECUTING);
            
            return new TaskTimeoutStatistics(
                    allExecutingTasks.size(),
                    timeoutTasks.size(),
                    timeoutMinutes
            );
            
        } catch (Exception e) {
            log.error("获取PMI任务超时统计失败", e);
            return new TaskTimeoutStatistics(0, 0, 0);
        }
    }
    
    /**
     * 任务超时统计
     */
    public static class TaskTimeoutStatistics {
        private final int totalExecutingTasks;
        private final int timeoutTasks;
        private final int timeoutThresholdMinutes;
        
        public TaskTimeoutStatistics(int totalExecutingTasks, int timeoutTasks, int timeoutThresholdMinutes) {
            this.totalExecutingTasks = totalExecutingTasks;
            this.timeoutTasks = timeoutTasks;
            this.timeoutThresholdMinutes = timeoutThresholdMinutes;
        }
        
        public int getTotalExecutingTasks() { return totalExecutingTasks; }
        public int getTimeoutTasks() { return timeoutTasks; }
        public int getTimeoutThresholdMinutes() { return timeoutThresholdMinutes; }
        
        public double getTimeoutRate() {
            if (totalExecutingTasks == 0) return 0.0;
            return (double) timeoutTasks / totalExecutingTasks * 100;
        }
    }
}
