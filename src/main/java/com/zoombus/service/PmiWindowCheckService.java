package com.zoombus.service;

import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.PmiSchedule;
import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.PmiScheduleRepository;
import com.zoombus.repository.PmiScheduleWindowRepository;
import com.zoombus.service.PmiBillingModeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * PMI窗口检查服务
 * 提供手动触发窗口检查的功能，用于在创建计划后立即检查是否有需要开启的窗口
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiWindowCheckService {

    private final PmiScheduleWindowRepository windowRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final PmiScheduleRepository pmiScheduleRepository;
    private final PmiBillingModeService pmiBillingModeService;
    
    /**
     * 立即检查并处理需要开启的PMI窗口
     * 用于在创建计划后立即触发检查，确保即时生效的窗口能尽快开启
     */
    @Transactional
    public void checkAndOpenPmiWindows() {
        LocalDate currentDate = LocalDate.now();
        LocalTime currentTime = LocalTime.now();
        
        log.info("手动触发PMI窗口检查，当前时间: {} {}", currentDate, currentTime);
        
        // 开启需要激活的PMI
        openPmiWindows(currentDate, currentTime);
        
        // 关闭需要停用的PMI
        closePmiWindows(currentDate, currentTime);
        
        // 更新计划状态
        updateScheduleStatus();
    }
    
    /**
     * 开启PMI窗口
     */
    private void openPmiWindows(LocalDate currentDate, LocalTime currentTime) {
        LocalDateTime currentDateTime = LocalDateTime.of(currentDate, currentTime);

        // 优先使用新的查询方法
        List<PmiScheduleWindow> windowsToOpen = windowRepository.findWindowsToOpenPmi(currentDateTime);

        // 如果新方法没有结果，尝试兼容旧字段的方法
        if (windowsToOpen.isEmpty()) {
            windowsToOpen = windowRepository.findWindowsToOpenPmiLegacy(currentDate, currentTime);
        }
        
        if (windowsToOpen.isEmpty()) {
            log.debug("没有需要开启的PMI窗口");
            return;
        }
        
        log.info("找到 {} 个需要开启的PMI窗口", windowsToOpen.size());
        
        // 获取需要激活的PMI记录ID
        Set<Long> pmiRecordIds = windowsToOpen.stream()
                .map(PmiScheduleWindow::getPmiRecordId)
                .collect(Collectors.toSet());
        
        // 批量查询PMI记录
        List<PmiRecord> pmiRecords = pmiRecordRepository.findAllById(pmiRecordIds);
        
        // 更新PMI状态为ACTIVE
        for (PmiRecord pmiRecord : pmiRecords) {
            if (pmiRecord.getStatus() != PmiRecord.PmiStatus.ACTIVE) {
                pmiRecord.setStatus(PmiRecord.PmiStatus.ACTIVE);
                log.info("激活PMI: id={}, pmiNumber={}", pmiRecord.getId(), pmiRecord.getPmiNumber());
            }

            // 为每个PMI找到对应的窗口并设置计费模式为LONG
            for (PmiScheduleWindow window : windowsToOpen) {
                if (window.getPmiRecordId().equals(pmiRecord.getId())) {
                    // 使用精确的窗口到期时间
                    LocalDateTime windowExpireTime = window.getEndDateTime();

                    // 切换到LONG计费模式
                    pmiBillingModeService.switchToLongBilling(pmiRecord.getId(), window.getId(), windowExpireTime);
                    log.info("PMI {} 开启窗口时切换到LONG计费模式，窗口到期时间: {}",
                            pmiRecord.getId(), windowExpireTime);
                    break;
                }
            }
        }

        // 更新窗口状态为ACTIVE，并记录实际开启时间
        LocalDateTime actualStartTime = LocalDateTime.now();
        for (PmiScheduleWindow window : windowsToOpen) {
            window.setStatus(PmiScheduleWindow.WindowStatus.ACTIVE);
            window.setActualStartTime(actualStartTime);
            log.info("开启PMI窗口: windowId={}, pmiRecordId={}, startDateTime={}, 实际开启时间={}",
                    window.getId(), window.getPmiRecordId(), window.getStartDateTime(), actualStartTime);
        }

        // 保存更新
        windowRepository.saveAll(windowsToOpen);
        pmiRecordRepository.saveAll(pmiRecords);

        log.info("成功开启 {} 个PMI窗口，激活 {} 个PMI", windowsToOpen.size(), pmiRecords.size());
    }
    
    /**
     * 关闭PMI窗口
     */
    private void closePmiWindows(LocalDate currentDate, LocalTime currentTime) {
        LocalDateTime currentDateTime = LocalDateTime.of(currentDate, currentTime);

        // 优先使用新的查询方法
        List<PmiScheduleWindow> windowsToClose = windowRepository.findWindowsToClosePmi(currentDateTime);

        // 如果新方法没有结果，尝试兼容旧字段的方法
        if (windowsToClose.isEmpty()) {
            windowsToClose = windowRepository.findWindowsToClosePmiLegacy(currentDate, currentTime);
        }
        
        if (windowsToClose.isEmpty()) {
            log.debug("没有需要关闭的PMI窗口");
            return;
        }
        
        log.info("找到 {} 个需要关闭的PMI窗口", windowsToClose.size());
        
        // 获取需要检查的PMI记录ID
        Set<Long> pmiRecordIds = windowsToClose.stream()
                .map(PmiScheduleWindow::getPmiRecordId)
                .collect(Collectors.toSet());
        
        // 更新窗口状态为COMPLETED，添加验证逻辑
        for (PmiScheduleWindow window : windowsToClose) {
            // 添加额外的验证逻辑，防止错误关闭
            boolean shouldClose = validateWindowShouldClose(window, currentDateTime);
            if (!shouldClose) {
                log.error("窗口验证失败，跳过关闭: windowId={}, startDateTime={}, endDateTime={}, currentDateTime={}",
                        window.getId(), window.getStartDateTime(), window.getEndDateTime(), currentDateTime);
                continue;
            }

            window.setStatus(PmiScheduleWindow.WindowStatus.COMPLETED);
            log.info("关闭PMI窗口: windowId={}, pmiRecordId={}, endDateTime={}, currentDate={}, currentTime={}",
                    window.getId(), window.getPmiRecordId(), window.getEndDateTime(), currentDate, currentTime);
        }
        
        windowRepository.saveAll(windowsToClose);
        
        // 检查每个PMI是否还有其他活跃窗口，如果没有则切换计费模式
        for (Long pmiRecordId : pmiRecordIds) {
            List<PmiScheduleWindow> activeWindows = windowRepository.findByPmiRecordIdAndStatus(
                    pmiRecordId, PmiScheduleWindow.WindowStatus.ACTIVE);

            if (activeWindows.isEmpty()) {
                // 没有活跃窗口，需要切换计费模式
                PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId).orElse(null);
                if (pmiRecord != null && pmiRecord.getStatus() == PmiRecord.PmiStatus.ACTIVE) {
                    // 恢复到原始计费模式
                    pmiBillingModeService.switchToOriginalBilling(pmiRecord.getId());

                    // 根据原始计费模式和可用时长决定PMI状态
                    PmiRecord updatedRecord = pmiRecordRepository.findById(pmiRecord.getId()).orElse(null);
                    if (updatedRecord != null) {
                        if (updatedRecord.getBillingMode() == PmiRecord.BillingMode.FREE) {
                            // FREE模式保持ACTIVE状态
                            log.info("PMI {} 关闭窗口后恢复到FREE模式，保持ACTIVE状态", pmiRecord.getId());
                        } else if (updatedRecord.getBillingMode() == PmiRecord.BillingMode.BY_TIME) {
                            // BY_TIME模式根据可用时长决定状态
                            if (updatedRecord.getAvailableMinutes() != null && updatedRecord.getAvailableMinutes() > 0) {
                                log.info("PMI {} 关闭窗口后恢复到BY_TIME模式，可用时长: {} 分钟",
                                        pmiRecord.getId(), updatedRecord.getAvailableMinutes());
                            } else {
                                // 没有可用时长，设置为INACTIVE状态
                                updatedRecord.setStatus(PmiRecord.PmiStatus.INACTIVE);
                                pmiRecordRepository.save(updatedRecord);
                                log.info("PMI {} 关闭窗口后恢复到BY_TIME模式但无可用时长，设置为INACTIVE状态",
                                        pmiRecord.getId());
                            }
                        }
                    }
                }
            } else {
                log.debug("PMI {} 还有 {} 个活跃窗口，保持ACTIVE状态",
                        pmiRecordId, activeWindows.size());
            }
        }
        
        log.info("成功关闭 {} 个PMI窗口", windowsToClose.size());
    }

    /**
     * 验证窗口是否应该被关闭
     * 重构版本：使用新的 endDateTime 字段，逻辑大大简化
     */
    private boolean validateWindowShouldClose(PmiScheduleWindow window, LocalDateTime currentDateTime) {
        // 新逻辑：直接比较 currentDateTime 和 endDateTime
        LocalDateTime windowEndDateTime = window.getEndDateTime();
        boolean shouldClose = currentDateTime.isAfter(windowEndDateTime) || currentDateTime.equals(windowEndDateTime);

        // 记录详细的验证信息
        log.info("窗口关闭验证: windowId={}, startDateTime={}, endDateTime={}, " +
                "currentDateTime={}, shouldClose={}",
                window.getId(), window.getStartDateTime(), windowEndDateTime,
                currentDateTime, shouldClose);

        return shouldClose;
    }

    /**
     * 验证窗口是否应该被关闭（兼容旧字段的方法）
     * 用于数据迁移期间的兼容性
     */
    private boolean validateWindowShouldCloseLegacy(PmiScheduleWindow window, LocalDate currentDate, LocalTime currentTime) {
        // 使用新的精确时间字段进行验证
        LocalDateTime currentDateTime = LocalDateTime.of(currentDate, currentTime);
        LocalDateTime windowEndDateTime = window.getEndDateTime();

        if (windowEndDateTime == null) {
            log.warn("窗口 {} 缺少结束时间数据，无法验证", window.getId());
            return false;
        }

        boolean shouldClose = !currentDateTime.isBefore(windowEndDateTime);

        // 记录详细的验证信息
        log.info("窗口关闭验证: windowId={}, endDateTime={}, currentDateTime={}, shouldClose={}",
                window.getId(), windowEndDateTime, currentDateTime, shouldClose);

        return shouldClose;
    }

    /**
     * 更新计划状态
     */
    private void updateScheduleStatus() {
        try {
            // 查找所有活跃的计划
            List<PmiSchedule> activeSchedules = pmiScheduleRepository.findByStatus(PmiSchedule.ScheduleStatus.ACTIVE);

            for (PmiSchedule schedule : activeSchedules) {
                // 检查计划的所有窗口是否都已完成
                long totalWindows = windowRepository.countByScheduleId(schedule.getId());

                if (totalWindows == 0) {
                    continue; // 没有窗口的计划跳过
                }

                // 统计已完成的窗口数（包括正常完成和人工关闭）
                long completedWindows = windowRepository.countByScheduleIdAndStatus(
                        schedule.getId(), PmiScheduleWindow.WindowStatus.COMPLETED);
                long manuallyClosedWindows = windowRepository.countByScheduleIdAndStatus(
                        schedule.getId(), PmiScheduleWindow.WindowStatus.MANUALLY_CLOSED);

                long finishedWindows = completedWindows + manuallyClosedWindows;

                // 如果所有窗口都已完成，则更新计划状态为完成
                if (finishedWindows == totalWindows) {
                    schedule.setStatus(PmiSchedule.ScheduleStatus.COMPLETED);
                    pmiScheduleRepository.save(schedule);
                    log.info("计划已完成，更新状态: scheduleId={}, name={}, totalWindows={}, completedWindows={}, manuallyClosedWindows={}",
                            schedule.getId(), schedule.getName(), totalWindows, completedWindows, manuallyClosedWindows);
                }
            }
            
        } catch (Exception e) {
            log.error("更新计划状态失败", e);
        }
    }
}
