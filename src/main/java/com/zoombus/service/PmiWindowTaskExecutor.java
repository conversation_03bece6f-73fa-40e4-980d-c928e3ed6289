package com.zoombus.service;

/**
 * PMI窗口任务执行器接口
 * 负责实际执行PMI窗口的开启和关闭逻辑
 */
public interface PmiWindowTaskExecutor {
    
    /**
     * 执行PMI窗口开启任务
     * 
     * @param windowId PMI窗口ID
     * @param taskId 任务ID
     */
    void executeOpenTask(Long windowId, Long taskId);
    
    /**
     * 执行PMI窗口关闭任务
     * 
     * @param windowId PMI窗口ID
     * @param taskId 任务ID
     */
    void executeCloseTask(Long windowId, Long taskId);
}
