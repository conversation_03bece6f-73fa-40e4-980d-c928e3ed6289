package com.zoombus.service;

import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.repository.PmiScheduleWindowRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * PMI窗口任务修复服务
 * 用于修复没有关联任务的窗口
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiWindowTaskRepairService {
    
    private final PmiScheduleWindowRepository windowRepository;
    private final DynamicTaskManager dynamicTaskManager;
    
    /**
     * 批量修复没有任务的窗口
     */
    @Transactional
    public int repairWindowsWithoutTasks() {
        log.info("开始批量修复没有任务的窗口");
        
        // 查找所有没有任务的活跃窗口
        List<PmiScheduleWindow> windowsWithoutTasks = windowRepository.findWindowsWithoutTasks();
        
        if (windowsWithoutTasks.isEmpty()) {
            log.info("没有发现需要修复的窗口");
            return 0;
        }
        
        log.info("发现 {} 个窗口需要修复任务", windowsWithoutTasks.size());
        
        int successCount = 0;
        int failedCount = 0;
        
        for (PmiScheduleWindow window : windowsWithoutTasks) {
            try {
                repairSingleWindow(window);
                successCount++;
                log.info("窗口任务修复成功: windowId={}", window.getId());
            } catch (Exception e) {
                failedCount++;
                log.error("窗口任务修复失败: windowId={}", window.getId(), e);
            }
        }
        
        log.info("批量修复完成: 总数={}, 成功={}, 失败={}", 
                windowsWithoutTasks.size(), successCount, failedCount);
        
        return successCount;
    }
    
    /**
     * 修复单个窗口的任务
     */
    @Transactional
    public void repairSingleWindow(Long windowId) {
        PmiScheduleWindow window = windowRepository.findById(windowId)
                .orElseThrow(() -> new RuntimeException("窗口不存在: " + windowId));
        
        repairSingleWindow(window);
    }
    
    /**
     * 修复单个窗口的任务
     */
    private void repairSingleWindow(PmiScheduleWindow window) {
        log.info("开始修复窗口任务: windowId={}, startTime={}, endTime={}", 
                window.getId(), window.getStartDateTime(), window.getEndDateTime());
        
        boolean needUpdate = false;
        
        // 创建开启任务（如果不存在）
        if (window.getOpenTaskId() == null) {
            try {
                Long openTaskId = dynamicTaskManager.schedulePmiWindowOpenTask(
                        window.getId(), window.getStartDateTime());
                if (openTaskId != null) {
                    window.setOpenTaskId(openTaskId);
                    needUpdate = true;
                    log.info("创建开启任务成功: windowId={}, taskId={}", window.getId(), openTaskId);
                }
            } catch (Exception e) {
                log.error("创建开启任务失败: windowId={}", window.getId(), e);
                throw e;
            }
        }
        
        // 创建关闭任务（如果不存在）
        if (window.getCloseTaskId() == null) {
            try {
                Long closeTaskId = dynamicTaskManager.schedulePmiWindowCloseTask(
                        window.getId(), window.getEndDateTime());
                if (closeTaskId != null) {
                    window.setCloseTaskId(closeTaskId);
                    needUpdate = true;
                    log.info("创建关闭任务成功: windowId={}, taskId={}", window.getId(), closeTaskId);
                }
            } catch (Exception e) {
                log.error("创建关闭任务失败: windowId={}", window.getId(), e);
                throw e;
            }
        }
        
        // 保存更新
        if (needUpdate) {
            windowRepository.save(window);
            log.info("窗口任务ID更新成功: windowId={}, openTaskId={}, closeTaskId={}", 
                    window.getId(), window.getOpenTaskId(), window.getCloseTaskId());
        } else {
            log.info("窗口已有完整任务，无需修复: windowId={}", window.getId());
        }
    }
    
    /**
     * 检查系统中没有任务的窗口数量
     */
    public int countWindowsWithoutTasks() {
        List<PmiScheduleWindow> windows = windowRepository.findWindowsWithoutTasks();
        return windows.size();
    }
}
