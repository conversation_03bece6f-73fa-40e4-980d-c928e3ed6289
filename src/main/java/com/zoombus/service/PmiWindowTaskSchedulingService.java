package com.zoombus.service;

import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.event.PmiWindowCreatedEvent;
import com.zoombus.event.PmiWindowDeletedEvent;
import com.zoombus.event.PmiWindowUpdatedEvent;
import com.zoombus.repository.PmiScheduleWindowRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * PMI窗口任务调度服务
 * 监听PMI窗口事件，自动创建和管理相关的定时任务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiWindowTaskSchedulingService {
    
    private final DynamicTaskManager dynamicTaskManager;
    private final PmiScheduleWindowRepository windowRepository;
    
    /**
     * 处理PMI窗口创建事件
     */
    @EventListener
    @Async
    @Transactional
    public void handlePmiWindowCreated(PmiWindowCreatedEvent event) {
        PmiScheduleWindow eventWindow = event.getWindow();
        log.info("处理PMI窗口创建事件: windowId={}", eventWindow.getId());

        try {
            // 重新从数据库加载窗口对象，避免使用事件中的detached对象
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(eventWindow.getId());
            if (!windowOpt.isPresent()) {
                log.warn("PMI窗口不存在，跳过任务创建: windowId={}", eventWindow.getId());
                return;
            }

            PmiScheduleWindow window = windowOpt.get();

            // 检查是否已经创建过任务，避免重复处理
            if (window.getOpenTaskId() != null || window.getCloseTaskId() != null) {
                log.info("PMI窗口已有任务，跳过重复创建: windowId={}, openTaskId={}, closeTaskId={}",
                        window.getId(), window.getOpenTaskId(), window.getCloseTaskId());
                return;
            }

            // 创建开启任务
            Long openTaskId = dynamicTaskManager.schedulePmiWindowOpenTask(
                window.getId(), window.getStartDateTime());

            // 创建关闭任务
            Long closeTaskId = dynamicTaskManager.schedulePmiWindowCloseTask(
                window.getId(), window.getEndDateTime());

            // 更新窗口记录，保存任务ID
            boolean needUpdate = false;
            if (openTaskId != null) {
                window.setOpenTaskId(openTaskId);
                needUpdate = true;
            }
            if (closeTaskId != null) {
                window.setCloseTaskId(closeTaskId);
                needUpdate = true;
            }

            // 只有在需要更新任务ID时才保存
            if (needUpdate) {
                windowRepository.save(window);
                log.info("PMI窗口任务ID更新成功: windowId={}, openTaskId={}, closeTaskId={}",
                        window.getId(), window.getOpenTaskId(), window.getCloseTaskId());
            }

            log.info("PMI窗口任务创建成功: windowId={}, openTaskId={}, closeTaskId={}",
                    window.getId(), openTaskId, closeTaskId);

        } catch (Exception e) {
            log.error("处理PMI窗口创建事件失败: windowId={}", eventWindow.getId(), e);
        }
    }
    
    /**
     * 处理PMI窗口更新事件
     */
    @EventListener
    @Async
    @Transactional
    public void handlePmiWindowUpdated(PmiWindowUpdatedEvent event) {
        PmiScheduleWindow eventNewWindow = event.getWindow();
        PmiScheduleWindow oldWindow = event.getOldWindow();

        log.info("处理PMI窗口更新事件: windowId={}", eventNewWindow.getId());

        try {
            // 重新从数据库加载窗口对象，避免使用事件中的detached对象
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(eventNewWindow.getId());
            if (!windowOpt.isPresent()) {
                log.warn("PMI窗口不存在，跳过任务更新: windowId={}", eventNewWindow.getId());
                return;
            }

            PmiScheduleWindow newWindow = windowOpt.get();

            // 检查时间是否发生变化
            boolean timeChanged = !newWindow.getStartDateTime().equals(oldWindow.getStartDateTime()) ||
                                !newWindow.getEndDateTime().equals(oldWindow.getEndDateTime());

            if (timeChanged) {
                log.info("PMI窗口时间发生变化，重新调度任务: windowId={}", newWindow.getId());

                // 取消原有任务（如果存在）
                cancelExistingTasks(oldWindow);

                // 创建新任务
                Long openTaskId = dynamicTaskManager.schedulePmiWindowOpenTask(
                    newWindow.getId(), newWindow.getStartDateTime());

                Long closeTaskId = dynamicTaskManager.schedulePmiWindowCloseTask(
                    newWindow.getId(), newWindow.getEndDateTime());

                // 更新窗口记录，保存任务ID
                boolean needUpdate = false;
                if (openTaskId != null) {
                    newWindow.setOpenTaskId(openTaskId);
                    needUpdate = true;
                }
                if (closeTaskId != null) {
                    newWindow.setCloseTaskId(closeTaskId);
                    needUpdate = true;
                }

                // 只有在需要更新任务ID时才保存
                if (needUpdate) {
                    windowRepository.save(newWindow);
                }
                
                log.info("PMI窗口任务重新调度成功: windowId={}, openTaskId={}, closeTaskId={}",
                        newWindow.getId(), openTaskId, closeTaskId);
            }
            
        } catch (Exception e) {
            log.error("处理PMI窗口更新事件失败: windowId={}", eventNewWindow.getId(), e);
        }
    }
    
    /**
     * 处理PMI窗口删除事件
     */
    @EventListener
    @Async
    @Transactional
    public void handlePmiWindowDeleted(PmiWindowDeletedEvent event) {
        PmiScheduleWindow window = event.getWindow();
        log.info("处理PMI窗口删除事件: windowId={}", window.getId());
        
        try {
            // 取消相关任务
            cancelExistingTasks(window);
            
            log.info("PMI窗口删除任务处理成功: windowId={}", window.getId());
            
        } catch (Exception e) {
            log.error("处理PMI窗口删除事件失败: windowId={}", window.getId(), e);
        }
    }
    
    /**
     * 批量创建窗口任务
     */
    @Transactional
    public void batchCreateWindowTasks(List<PmiScheduleWindow> windows) {
        log.info("批量创建PMI窗口任务: 数量={}", windows.size());
        
        int successCount = 0;
        int failedCount = 0;
        
        for (PmiScheduleWindow window : windows) {
            try {
                // 检查是否已经有任务
                if (window.getOpenTaskId() != null || window.getCloseTaskId() != null) {
                    log.warn("PMI窗口已有任务，跳过: windowId={}", window.getId());
                    continue;
                }
                
                // 创建任务
                Long openTaskId = dynamicTaskManager.schedulePmiWindowOpenTask(
                    window.getId(), window.getStartDateTime());

                Long closeTaskId = dynamicTaskManager.schedulePmiWindowCloseTask(
                    window.getId(), window.getEndDateTime());

                // 更新窗口记录，保存任务ID
                if (openTaskId != null) {
                    window.setOpenTaskId(openTaskId);
                }
                if (closeTaskId != null) {
                    window.setCloseTaskId(closeTaskId);
                }
                windowRepository.save(window);
                
                successCount++;
                
            } catch (Exception e) {
                log.error("批量创建任务失败: windowId={}", window.getId(), e);
                failedCount++;
            }
        }
        
        log.info("批量创建PMI窗口任务完成: 成功={}, 失败={}", successCount, failedCount);
    }
    
    /**
     * 重新调度窗口任务
     */
    @Transactional
    public void rescheduleWindowTasks(Long windowId) {
        log.info("重新调度PMI窗口任务: windowId={}", windowId);
        
        try {
            PmiScheduleWindow window = windowRepository.findById(windowId)
                .orElseThrow(() -> new RuntimeException("PMI窗口不存在: " + windowId));
            
            // 取消现有任务
            cancelExistingTasks(window);
            
            // 创建新任务
            Long openTaskId = dynamicTaskManager.schedulePmiWindowOpenTask(
                window.getId(), window.getStartDateTime());

            Long closeTaskId = dynamicTaskManager.schedulePmiWindowCloseTask(
                window.getId(), window.getEndDateTime());

            // 更新窗口记录，保存任务ID
            if (openTaskId != null) {
                window.setOpenTaskId(openTaskId);
            }
            if (closeTaskId != null) {
                window.setCloseTaskId(closeTaskId);
            }
            windowRepository.save(window);
            
            log.info("PMI窗口任务重新调度成功: windowId={}", windowId);
            
        } catch (Exception e) {
            log.error("重新调度PMI窗口任务失败: windowId={}", windowId, e);
            throw new RuntimeException("重新调度任务失败", e);
        }
    }
    
    /**
     * 取消现有任务
     */
    private void cancelExistingTasks(PmiScheduleWindow window) {
        // 这里需要根据实际的任务键来取消任务
        // 由于我们简化了任务ID的处理，这里只是示例
        log.info("取消PMI窗口现有任务: windowId={}", window.getId());
        
        // 实际实现中，应该根据任务键来取消任务
        // 这里需要查询数据库获取实际的任务键
        try {
            // 查找并取消开启任务
            // 查找并取消关闭任务
            // 具体实现需要根据实际的任务键管理策略
            
        } catch (Exception e) {
            log.warn("取消现有任务时发生错误: windowId={}", window.getId(), e);
        }
    }
}
