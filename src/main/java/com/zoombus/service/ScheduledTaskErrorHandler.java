package com.zoombus.service;

import com.zoombus.entity.TaskExecutionRecord;
import com.zoombus.repository.TaskExecutionRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 定时任务错误处理器
 * 负责处理任务执行失败、重试逻辑、告警通知等
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ScheduledTaskErrorHandler {
    
    private final TaskExecutionRecordRepository taskExecutionRecordRepository;
    
    /**
     * 处理任务执行失败
     * 
     * @param record 执行记录
     * @param config 执行配置
     */
    @Async("errorHandlingExecutor")
    public CompletableFuture<Void> handleTaskFailure(
            TaskExecutionRecord record, 
            AsyncScheduledTaskExecutor.TaskExecutionConfig config) {
        
        try {
            log.info("处理任务执行失败: {} (ID: {}), 重试次数: {}/{}", 
                    record.getTaskName(), record.getId(), record.getRetryCount(), config.getMaxRetryCount());
            
            // 检查是否可以重试
            if (record.canRetry()) {
                scheduleRetry(record, config);
            } else {
                handleFinalFailure(record);
            }
            
            // 检查是否需要发送告警
            checkAndSendAlert(record);
            
        } catch (Exception e) {
            log.error("处理任务失败时发生异常: {} (ID: {})", record.getTaskName(), record.getId(), e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 安排重试
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void scheduleRetry(TaskExecutionRecord record, AsyncScheduledTaskExecutor.TaskExecutionConfig config) {
        // 计算下次重试时间（指数退避）
        long delayMs = calculateRetryDelay(record.getRetryCount(), config.getRetryDelayMs());
        LocalDateTime nextRetryTime = LocalDateTime.now().plusSeconds(delayMs / 1000);
        
        record.markAsRetrying(nextRetryTime);
        taskExecutionRecordRepository.save(record);
        
        log.info("任务 {} (ID: {}) 已安排重试，下次重试时间: {}", 
                record.getTaskName(), record.getId(), nextRetryTime);
    }
    
    /**
     * 处理最终失败
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void handleFinalFailure(TaskExecutionRecord record) {
        log.error("任务 {} (ID: {}) 达到最大重试次数，标记为最终失败", 
                record.getTaskName(), record.getId());
        
        // 可以在这里添加额外的失败处理逻辑
        // 比如发送告警、记录到特殊的失败队列等
    }
    
    /**
     * 检查并发送告警
     */
    private void checkAndSendAlert(TaskExecutionRecord record) {
        try {
            // 检查是否需要发送告警
            if (shouldSendAlert(record)) {
                sendTaskFailureAlert(record);
            }
        } catch (Exception e) {
            log.error("发送告警时发生异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 判断是否需要发送告警
     */
    private boolean shouldSendAlert(TaskExecutionRecord record) {
        // 连续失败3次或达到最大重试次数时发送告警
        if (record.getRetryCount() >= 3 || !record.canRetry()) {
            return true;
        }
        
        // 检查最近1小时内的失败次数
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        List<TaskExecutionRecord> recentFailures = taskExecutionRecordRepository
                .findByTaskNameAndExecutionTimeBetween(record.getTaskName(), oneHourAgo, LocalDateTime.now())
                .stream()
                .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                .collect(java.util.stream.Collectors.toList());
        
        return recentFailures.size() >= 5; // 1小时内失败5次以上
    }
    
    /**
     * 发送任务失败告警
     */
    private void sendTaskFailureAlert(TaskExecutionRecord record) {
        log.warn("🚨 定时任务失败告警: 任务={}, ID={}, 重试次数={}, 错误信息={}", 
                record.getTaskName(), record.getId(), record.getRetryCount(), record.getErrorMessage());
        
        // TODO: 集成实际的告警系统（邮件、短信、钉钉等）
        // alertService.sendTaskFailureAlert(record);
    }
    
    /**
     * 计算重试延迟时间（指数退避）
     */
    private long calculateRetryDelay(int retryCount, long baseDelayMs) {
        // 指数退避：baseDelay * 2^retryCount，最大不超过30分钟
        long delay = (long) (baseDelayMs * Math.pow(2, retryCount));
        return Math.min(delay, 30 * 60 * 1000); // 最大30分钟
    }
    
    /**
     * 处理需要重试的任务
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processRetryTasks() {
        try {
            List<TaskExecutionRecord> retryTasks = taskExecutionRecordRepository
                    .findTasksNeedingRetry(LocalDateTime.now());
            
            if (!retryTasks.isEmpty()) {
                log.info("发现 {} 个需要重试的任务", retryTasks.size());
                
                for (TaskExecutionRecord task : retryTasks) {
                    try {
                        // 重置任务状态为待执行
                        task.setStatus(TaskExecutionRecord.ExecutionStatus.PENDING);
                        task.setExecutionTime(LocalDateTime.now());
                        taskExecutionRecordRepository.save(task);
                        
                        log.info("任务 {} (ID: {}) 已重置为待执行状态", task.getTaskName(), task.getId());
                        
                        // TODO: 触发任务重新执行
                        // 这里需要根据具体的任务类型来触发重新执行
                        
                    } catch (Exception e) {
                        log.error("处理重试任务时发生异常: {} (ID: {})", task.getTaskName(), task.getId(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理重试任务时发生异常", e);
        }
    }
    
    /**
     * 清理过期的执行记录
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void cleanupExpiredRecords() {
        try {
            // 删除30天前的记录
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
            taskExecutionRecordRepository.deleteByExecutionTimeBefore(cutoffTime);
            
            log.info("已清理 {} 之前的任务执行记录", cutoffTime);
        } catch (Exception e) {
            log.error("清理过期记录时发生异常", e);
        }
    }
    
    /**
     * 获取任务健康状态报告
     */
    public TaskHealthReport getTaskHealthReport(String taskName, int hours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        LocalDateTime endTime = LocalDateTime.now();
        
        List<Object[]> statusCounts = taskExecutionRecordRepository
                .countByTaskNameAndStatusAndExecutionTimeBetween(taskName, startTime, endTime);
        
        TaskHealthReport report = new TaskHealthReport(taskName, startTime, endTime);
        
        for (Object[] row : statusCounts) {
            TaskExecutionRecord.ExecutionStatus status = (TaskExecutionRecord.ExecutionStatus) row[0];
            Long count = (Long) row[1];
            
            switch (status) {
                case SUCCESS:
                    report.setSuccessCount(count.intValue());
                    break;
                case FAILED:
                    report.setFailedCount(count.intValue());
                    break;
                case RUNNING:
                    report.setRunningCount(count.intValue());
                    break;
                case RETRYING:
                    report.setRetryingCount(count.intValue());
                    break;
            }
        }
        
        return report;
    }
    
    /**
     * 任务健康状态报告
     */
    public static class TaskHealthReport {
        private String taskName;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private int successCount = 0;
        private int failedCount = 0;
        private int runningCount = 0;
        private int retryingCount = 0;
        
        public TaskHealthReport(String taskName, LocalDateTime startTime, LocalDateTime endTime) {
            this.taskName = taskName;
            this.startTime = startTime;
            this.endTime = endTime;
        }
        
        public double getSuccessRate() {
            int total = successCount + failedCount;
            return total > 0 ? (double) successCount / total * 100 : 0;
        }
        
        public boolean isHealthy() {
            return getSuccessRate() >= 90 && failedCount <= 5;
        }
        
        // Getters and Setters
        public String getTaskName() { return taskName; }
        public LocalDateTime getStartTime() { return startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        public int getRunningCount() { return runningCount; }
        public void setRunningCount(int runningCount) { this.runningCount = runningCount; }
        public int getRetryingCount() { return retryingCount; }
        public void setRetryingCount(int retryingCount) { this.retryingCount = retryingCount; }
    }
}
