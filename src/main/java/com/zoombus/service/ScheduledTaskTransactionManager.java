package com.zoombus.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.function.Supplier;

/**
 * 定时任务专用事务管理器
 * 提供独立的事务管理，避免定时任务影响主业务事务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ScheduledTaskTransactionManager {
    
    private final PlatformTransactionManager transactionManager;
    
    /**
     * 在独立事务中执行操作
     * 
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeInNewTransaction(Supplier<T> operation) {
        return executeInNewTransaction(operation, TransactionDefinition.ISOLATION_READ_COMMITTED, 30);
    }
    
    /**
     * 在独立事务中执行操作（带隔离级别和超时设置）
     * 
     * @param operation 要执行的操作
     * @param isolationLevel 事务隔离级别
     * @param timeoutSeconds 事务超时时间（秒）
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeInNewTransaction(Supplier<T> operation, int isolationLevel, int timeoutSeconds) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        def.setIsolationLevel(isolationLevel);
        def.setTimeout(timeoutSeconds);
        def.setReadOnly(false);
        
        TransactionStatus status = transactionManager.getTransaction(def);
        
        try {
            T result = operation.get();
            transactionManager.commit(status);
            return result;
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw new RuntimeException("定时任务事务执行失败", e);
        }
    }
    
    /**
     * 在只读事务中执行查询操作
     * 
     * @param operation 要执行的查询操作
     * @param <T> 返回类型
     * @return 查询结果
     */
    public <T> T executeInReadOnlyTransaction(Supplier<T> operation) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        def.setReadOnly(true);
        def.setTimeout(10); // 查询操作超时时间较短
        
        TransactionStatus status = transactionManager.getTransaction(def);
        
        try {
            T result = operation.get();
            transactionManager.commit(status);
            return result;
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw new RuntimeException("定时任务查询事务执行失败", e);
        }
    }
    
    /**
     * 批量执行操作（每个操作在独立事务中）
     * 
     * @param operations 操作列表
     * @param batchSize 批次大小
     * @return 批量执行结果
     */
    public BatchExecutionResult executeBatch(java.util.List<Supplier<Void>> operations, int batchSize) {
        BatchExecutionResult result = new BatchExecutionResult();
        
        for (int i = 0; i < operations.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, operations.size());
            java.util.List<Supplier<Void>> batch = operations.subList(i, endIndex);
            
            for (Supplier<Void> operation : batch) {
                try {
                    executeInNewTransaction(() -> {
                        operation.get();
                        return null;
                    });
                    result.incrementSuccess();
                } catch (Exception e) {
                    result.incrementFailure();
                    result.addError(e.getMessage());
                    log.error("批量操作中的单个操作执行失败", e);
                }
            }
            
            // 批次间稍作延迟，避免数据库压力过大
            if (endIndex < operations.size()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        return result;
    }
    
    /**
     * 执行需要重试的操作
     * 
     * @param operation 要执行的操作
     * @param maxRetries 最大重试次数
     * @param retryDelayMs 重试间隔（毫秒）
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeWithRetry(Supplier<T> operation, int maxRetries, long retryDelayMs) {
        Exception lastException = null;
        
        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return executeInNewTransaction(operation);
            } catch (Exception e) {
                lastException = e;
                
                if (attempt < maxRetries) {
                    log.warn("定时任务操作执行失败，第 {} 次重试: {}", attempt + 1, e.getMessage());
                    
                    try {
                        Thread.sleep(retryDelayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                } else {
                    log.error("定时任务操作执行失败，已达到最大重试次数 {}", maxRetries, e);
                }
            }
        }
        
        throw new RuntimeException("定时任务操作执行失败，已达到最大重试次数", lastException);
    }
    
    /**
     * 批量执行结果
     */
    public static class BatchExecutionResult {
        private int successCount = 0;
        private int failureCount = 0;
        private java.util.List<String> errors = new java.util.ArrayList<>();
        
        public void incrementSuccess() {
            successCount++;
        }
        
        public void incrementFailure() {
            failureCount++;
        }
        
        public void addError(String error) {
            errors.add(error);
        }
        
        public int getTotalCount() {
            return successCount + failureCount;
        }
        
        public double getSuccessRate() {
            int total = getTotalCount();
            return total > 0 ? (double) successCount / total * 100 : 0;
        }
        
        public boolean isAllSuccess() {
            return failureCount == 0 && successCount > 0;
        }
        
        public boolean hasFailures() {
            return failureCount > 0;
        }
        
        // Getters
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public java.util.List<String> getErrors() { return errors; }
        
        @Override
        public String toString() {
            return String.format("BatchExecutionResult{total=%d, success=%d, failure=%d, successRate=%.2f%%}", 
                    getTotalCount(), successCount, failureCount, getSuccessRate());
        }
    }
    
    /**
     * 事务配置构建器
     */
    public static class TransactionConfigBuilder {
        private int propagationBehavior = TransactionDefinition.PROPAGATION_REQUIRES_NEW;
        private int isolationLevel = TransactionDefinition.ISOLATION_READ_COMMITTED;
        private int timeout = 30;
        private boolean readOnly = false;
        
        public TransactionConfigBuilder propagation(int propagationBehavior) {
            this.propagationBehavior = propagationBehavior;
            return this;
        }
        
        public TransactionConfigBuilder isolation(int isolationLevel) {
            this.isolationLevel = isolationLevel;
            return this;
        }
        
        public TransactionConfigBuilder timeout(int timeout) {
            this.timeout = timeout;
            return this;
        }
        
        public TransactionConfigBuilder readOnly(boolean readOnly) {
            this.readOnly = readOnly;
            return this;
        }
        
        public DefaultTransactionDefinition build() {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(propagationBehavior);
            def.setIsolationLevel(isolationLevel);
            def.setTimeout(timeout);
            def.setReadOnly(readOnly);
            return def;
        }
    }
    
    /**
     * 创建事务配置构建器
     */
    public static TransactionConfigBuilder configBuilder() {
        return new TransactionConfigBuilder();
    }
    
    /**
     * 使用自定义事务配置执行操作
     */
    public <T> T executeWithConfig(Supplier<T> operation, DefaultTransactionDefinition config) {
        TransactionStatus status = transactionManager.getTransaction(config);
        
        try {
            T result = operation.get();
            transactionManager.commit(status);
            return result;
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw new RuntimeException("定时任务事务执行失败", e);
        }
    }
}
