package com.zoombus.service;

import com.zoombus.entity.TaskExecutionRecord;
import com.zoombus.repository.TaskExecutionRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 定时任务监控服务
 * 统一管理和监控系统中的所有定时任务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SchedulerMonitorService {

    // 新增：集成新的任务监控系统
    private final TaskExecutionRecordRepository taskExecutionRecordRepository;
    private final TaskExecutionTracker taskExecutionTracker;
    private final ScheduledTaskErrorHandler errorHandler;

    /**
     * 获取所有定时任务的状态信息
     */
    public List<SchedulerInfo> getAllSchedulerInfo() {
        List<SchedulerInfo> schedulers = new ArrayList<>();

        try {
            // 1. 会议状态同步任务（使用新的任务执行记录系统）
            schedulers.add(createSchedulerInfoFromTaskRecord("syncMeetingStatus",
                    "会议状态同步", "每3分钟轮询活跃会议状态，同步Zoom端状态变化", "每3分钟执行", "会议管理", "高"));

            // 注意：PMI窗口激活和完成任务已移除，现在使用精准调度模式

            // 5. Token刷新任务（使用新的任务执行记录系统）
            schedulers.add(createSchedulerInfoFromTaskRecord("refreshExpiredTokens",
                    "Token刷新", "每分钟检查并刷新过期的Zoom Token", "每分钟执行", "认证管理", "高"));

            // 6. 每日用户同步（使用新的任务执行记录系统）
            schedulers.add(createSchedulerInfoFromTaskRecord("dailyUserSync",
                    "每日用户同步", "每日0点全量同步Zoom用户信息", "每日0点执行", "用户管理", "中"));

            // 7. 窗口过期检查（使用新的任务执行记录系统）
            schedulers.add(createSchedulerInfoFromTaskRecord("checkExpiredWindows",
                    "窗口过期检查", "每分钟检查过期的时间窗口", "每分钟执行", "时间管理", "中"));

            // 8. 计费监控检查（使用新的任务执行记录系统）
            schedulers.add(createSchedulerInfoFromTaskRecord("checkBillingMonitorStatus",
                    "计费监控检查", "每5分钟检查计费监控状态", "每5分钟执行", "计费管理", "高"));

            // 9. 批量结算（使用新的任务执行记录系统）
            schedulers.add(createSchedulerInfoFromTaskRecord("batchSettleMeetings",
                    "批量结算", "每10分钟执行批量会议结算", "每10分钟执行", "计费管理", "高"));

            // 10. 临时会议清理（使用新的任务执行记录系统）
            schedulers.add(createSchedulerInfoFromTaskRecord("cleanupTemporaryPmiMeetings",
                    "临时会议清理", "每10分钟清理过期的临时PMI会议", "每10分钟执行", "数据清理", "低"));



        } catch (Exception e) {
            log.error("获取定时任务信息失败", e);
        }

        return schedulers;
    }

    /**
     * 获取定时任务统计概览
     */
    public SchedulerOverview getSchedulerOverview() {
        List<SchedulerInfo> schedulers = getAllSchedulerInfo();
        
        long totalTasks = schedulers.size();
        long runningTasks = schedulers.stream().mapToLong(s -> s.isRunning() ? 1 : 0).sum();
        long activeTasks = schedulers.stream().mapToLong(s -> "活跃".equals(s.getStatus()) || "运行中".equals(s.getStatus()) ? 1 : 0).sum();
        long errorTasks = schedulers.stream().mapToLong(s -> s.getLastError() != null ? 1 : 0).sum();

        return SchedulerOverview.builder()
                .totalTasks(totalTasks)
                .runningTasks(runningTasks)
                .activeTasks(activeTasks)
                .errorTasks(errorTasks)
                .lastUpdateTime(LocalDateTime.now())
                .systemHealth(errorTasks == 0 ? "健康" : errorTasks < totalTasks / 2 ? "警告" : "异常")
                .build();
    }

    /**
     * 获取特定类别的定时任务
     */
    public List<SchedulerInfo> getSchedulersByCategory(String category) {
        return getAllSchedulerInfo().stream()
                .filter(scheduler -> category.equals(scheduler.getCategory()))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 手动触发会议状态同步（用于测试）
     */
    public Map<String, Object> triggerMeetingStatusSync() {
        try {
            log.info("手动触发会议状态同步任务");
            // TODO: 集成新的异步任务系统后实现
            
            return Map.of(
                    "success", true,
                    "message", "会议状态同步任务已手动触发",
                    "timestamp", LocalDateTime.now()
            );
        } catch (Exception e) {
            log.error("手动触发会议状态同步失败", e);
            return Map.of(
                    "success", false,
                    "message", "触发失败: " + e.getMessage(),
                    "timestamp", LocalDateTime.now()
            );
        }
    }

    /**
     * 计算成功率
     */
    private String calculateSuccessRate(int success, int total) {
        if (total == 0) return "N/A";
        return String.format("%.1f%%", (success * 100.0) / total);
    }

    /**
     * 定时任务信息
     */
    @lombok.Builder
    @lombok.Data
    public static class SchedulerInfo {
        private String name;
        private String description;
        private String schedule;
        private String category;
        private String priority;
        private String status;
        private boolean isRunning;
        private LocalDateTime lastExecutionTime;
        private LocalDateTime nextExecutionTime;
        private Integer totalExecutions;
        private Integer successExecutions;
        private Integer errorExecutions;
        private String lastError;
        private Map<String, String> customMetrics;
    }

    /**
     * 定时任务概览
     */
    @lombok.Builder
    @lombok.Data
    public static class SchedulerOverview {
        private long totalTasks;
        private long runningTasks;
        private long activeTasks;
        private long errorTasks;
        private String systemHealth;
        private LocalDateTime lastUpdateTime;
    }

    /**
     * 从任务执行记录创建 SchedulerInfo
     */
    private SchedulerInfo createSchedulerInfoFromTaskRecord(String taskName, String displayName,
            String description, String schedule, String category, String priority) {
        try {
            // 获取任务的最新执行记录
            Optional<TaskExecutionRecord> latestRecord = taskExecutionRecordRepository
                    .findFirstByTaskNameOrderByExecutionTimeDesc(taskName);

            // 获取任务运行状态
            boolean isRunning = taskExecutionTracker.isTaskRunning(taskName);
            TaskExecutionTracker.TaskExecutionInfo runningInfo = null;
            if (isRunning) {
                runningInfo = taskExecutionTracker.getTaskExecutionInfo(taskName);
            }

            // 获取最近24小时的统计信息
            LocalDateTime last24Hours = LocalDateTime.now().minusHours(24);
            List<TaskExecutionRecord> recentRecords = taskExecutionRecordRepository
                    .findByTaskNameAndExecutionTimeBetween(taskName, last24Hours, LocalDateTime.now());

            // 计算统计信息
            int totalExecutions = recentRecords.size();
            int successExecutions = (int) recentRecords.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.SUCCESS)
                    .count();
            int errorExecutions = (int) recentRecords.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                    .count();

            // 获取最后一次错误信息
            String lastError = recentRecords.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                    .findFirst()
                    .map(TaskExecutionRecord::getErrorMessage)
                    .orElse(null);

            // 构建自定义指标
            Map<String, String> customMetrics = new HashMap<>();
            customMetrics.put("成功率", calculateSuccessRate(successExecutions, totalExecutions));
            customMetrics.put("24小时执行次数", String.valueOf(totalExecutions));

            if (runningInfo != null) {
                customMetrics.put("当前执行时长", runningInfo.getFormattedDuration());
            }

            // 计算下次执行时间（简化处理）
            LocalDateTime nextExecutionTime = null;
            if (latestRecord.isPresent() && !isRunning) {
                // 根据不同的调度频率估算下次执行时间
                if (schedule.contains("分钟")) {
                    nextExecutionTime = latestRecord.get().getExecutionTime().plusMinutes(1);
                } else if (schedule.contains("小时")) {
                    nextExecutionTime = latestRecord.get().getExecutionTime().plusHours(1);
                } else if (schedule.contains("每日")) {
                    nextExecutionTime = latestRecord.get().getExecutionTime().plusDays(1);
                }
            }

            return SchedulerInfo.builder()
                    .name(displayName)
                    .description(description)
                    .schedule(schedule)
                    .isRunning(isRunning)
                    .lastExecutionTime(latestRecord.map(TaskExecutionRecord::getExecutionTime).orElse(null))
                    .nextExecutionTime(nextExecutionTime)
                    .totalExecutions(totalExecutions)
                    .successExecutions(successExecutions)
                    .errorExecutions(errorExecutions)
                    .lastError(lastError)
                    .category(category)
                    .priority(priority)
                    .status(isRunning ? "运行中" : (errorExecutions > 0 ? "异常" : "正常"))
                    .customMetrics(customMetrics)
                    .build();

        } catch (Exception e) {
            log.error("创建任务 {} 的监控信息失败", taskName, e);
            // 返回基础信息
            return SchedulerInfo.builder()
                    .name(displayName)
                    .description(description)
                    .schedule(schedule)
                    .category(category)
                    .priority(priority)
                    .status("未知")
                    .isRunning(false)
                    .totalExecutions(0)
                    .successExecutions(0)
                    .errorExecutions(0)
                    .customMetrics(Map.of("状态", "数据获取失败"))
                    .build();
        }
    }
}
