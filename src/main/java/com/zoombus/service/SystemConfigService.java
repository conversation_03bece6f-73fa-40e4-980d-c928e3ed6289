package com.zoombus.service;

import com.zoombus.entity.SystemConfig;
import com.zoombus.repository.SystemConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 系统配置服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SystemConfigService {
    
    private final SystemConfigRepository systemConfigRepository;
    
    /**
     * 获取所有配置
     */
    public List<SystemConfig> getAllConfigs() {
        return systemConfigRepository.findAll();
    }
    
    /**
     * 获取所有有效配置
     */
    public List<SystemConfig> getAllActiveConfigs() {
        return systemConfigRepository.findByIsActiveTrue();
    }
    
    /**
     * 分页获取配置
     */
    public Page<SystemConfig> getConfigs(Pageable pageable) {
        return systemConfigRepository.findAll(pageable);
    }
    
    /**
     * 根据ID获取配置
     */
    public Optional<SystemConfig> getConfigById(Long id) {
        return systemConfigRepository.findById(id);
    }
    
    /**
     * 根据配置键获取配置
     */
    public Optional<SystemConfig> getConfigByKey(String configKey) {
        return systemConfigRepository.findByConfigKey(configKey);
    }
    
    /**
     * 根据配置键获取有效配置
     */
    public Optional<SystemConfig> getActiveConfigByKey(String configKey) {
        return systemConfigRepository.findActiveByConfigKey(configKey);
    }
    
    /**
     * 获取配置值（字符串）
     */
    public String getConfigValue(String configKey) {
        return getActiveConfigByKey(configKey)
                .map(SystemConfig::getStringValue)
                .orElse(null);
    }
    
    /**
     * 获取配置值（字符串，带默认值）
     */
    public String getConfigValue(String configKey, String defaultValue) {
        return getActiveConfigByKey(configKey)
                .map(SystemConfig::getStringValue)
                .orElse(defaultValue);
    }
    
    /**
     * 获取配置值（整数）
     */
    public Integer getIntConfigValue(String configKey) {
        return getActiveConfigByKey(configKey)
                .map(SystemConfig::getIntValue)
                .orElse(null);
    }
    
    /**
     * 获取配置值（整数，带默认值）
     */
    public Integer getIntConfigValue(String configKey, Integer defaultValue) {
        return getActiveConfigByKey(configKey)
                .map(SystemConfig::getIntValue)
                .orElse(defaultValue);
    }
    
    /**
     * 获取配置值（小数）
     */
    public Double getDoubleConfigValue(String configKey) {
        return getActiveConfigByKey(configKey)
                .map(SystemConfig::getDoubleValue)
                .orElse(null);
    }
    
    /**
     * 获取配置值（小数，带默认值）
     */
    public Double getDoubleConfigValue(String configKey, Double defaultValue) {
        return getActiveConfigByKey(configKey)
                .map(SystemConfig::getDoubleValue)
                .orElse(defaultValue);
    }
    
    /**
     * 获取配置值（布尔值）
     */
    public Boolean getBooleanConfigValue(String configKey) {
        return getActiveConfigByKey(configKey)
                .map(SystemConfig::getBooleanValue)
                .orElse(null);
    }
    
    /**
     * 获取配置值（布尔值，带默认值）
     */
    public Boolean getBooleanConfigValue(String configKey, Boolean defaultValue) {
        return getActiveConfigByKey(configKey)
                .map(SystemConfig::getBooleanValue)
                .orElse(defaultValue);
    }
    
    /**
     * 根据前缀获取配置
     */
    public List<SystemConfig> getConfigsByPrefix(String keyPrefix) {
        return systemConfigRepository.findActiveByConfigKeyPrefix(keyPrefix);
    }
    
    /**
     * 根据前缀获取配置映射
     */
    public Map<String, String> getConfigMapByPrefix(String keyPrefix) {
        List<SystemConfig> configs = getConfigsByPrefix(keyPrefix);
        Map<String, String> configMap = new HashMap<>();
        for (SystemConfig config : configs) {
            configMap.put(config.getConfigKey(), config.getStringValue());
        }
        return configMap;
    }
    
    /**
     * 创建或更新配置
     */
    @Transactional
    public SystemConfig saveOrUpdateConfig(String configKey, String configValue, String description, SystemConfig.ConfigType configType, String operator) {
        Optional<SystemConfig> existingConfig = systemConfigRepository.findByConfigKey(configKey);
        
        SystemConfig config;
        if (existingConfig.isPresent()) {
            config = existingConfig.get();
            config.setConfigValue(configValue);
            config.setDescription(description);
            config.setConfigType(configType);
            config.setUpdatedBy(operator != null ? operator : "SYSTEM");
            log.info("更新系统配置: {} = {}", configKey, configValue);
        } else {
            config = new SystemConfig();
            config.setConfigKey(configKey);
            config.setConfigValue(configValue);
            config.setDescription(description);
            config.setConfigType(configType);
            config.setCreatedBy(operator != null ? operator : "SYSTEM");
            config.setUpdatedBy(operator != null ? operator : "SYSTEM");
            log.info("创建系统配置: {} = {}", configKey, configValue);
        }
        
        return systemConfigRepository.save(config);
    }
    
    /**
     * 创建配置
     */
    @Transactional
    public SystemConfig createConfig(SystemConfig config) {
        if (systemConfigRepository.existsByConfigKey(config.getConfigKey())) {
            throw new IllegalArgumentException("配置键已存在: " + config.getConfigKey());
        }
        log.info("创建系统配置: {} = {}", config.getConfigKey(), config.getConfigValue());
        return systemConfigRepository.save(config);
    }
    
    /**
     * 更新配置
     */
    @Transactional
    public SystemConfig updateConfig(Long id, SystemConfig config) {
        SystemConfig existingConfig = systemConfigRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("配置不存在: " + id));
        
        // 检查配置键是否被其他配置使用
        if (!existingConfig.getConfigKey().equals(config.getConfigKey())) {
            if (systemConfigRepository.existsByConfigKey(config.getConfigKey())) {
                throw new IllegalArgumentException("配置键已存在: " + config.getConfigKey());
            }
        }
        
        existingConfig.setConfigKey(config.getConfigKey());
        existingConfig.setConfigValue(config.getConfigValue());
        existingConfig.setDescription(config.getDescription());
        existingConfig.setConfigType(config.getConfigType());
        existingConfig.setIsActive(config.getIsActive());
        existingConfig.setUpdatedBy(config.getUpdatedBy());
        
        log.info("更新系统配置: {} = {}", existingConfig.getConfigKey(), existingConfig.getConfigValue());
        return systemConfigRepository.save(existingConfig);
    }
    
    /**
     * 删除配置
     */
    @Transactional
    public void deleteConfig(Long id) {
        SystemConfig config = systemConfigRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("配置不存在: " + id));
        
        log.info("删除系统配置: {}", config.getConfigKey());
        systemConfigRepository.delete(config);
    }
    
    /**
     * 启用/禁用配置
     */
    @Transactional
    public SystemConfig toggleConfigStatus(Long id, String operator) {
        SystemConfig config = systemConfigRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("配置不存在: " + id));
        
        config.setIsActive(!config.getIsActive());
        config.setUpdatedBy(operator != null ? operator : "SYSTEM");
        
        log.info("{}系统配置: {}", config.getIsActive() ? "启用" : "禁用", config.getConfigKey());
        return systemConfigRepository.save(config);
    }
    
    /**
     * 搜索配置
     */
    public Page<SystemConfig> searchConfigs(String keyword, Pageable pageable) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return systemConfigRepository.findAll(pageable);
        }
        return systemConfigRepository.searchByKeyword(keyword.trim(), pageable);
    }
    
    /**
     * 获取配置统计信息
     */
    public Map<String, Object> getConfigStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCount", systemConfigRepository.count());
        stats.put("activeCount", systemConfigRepository.countActiveConfigs());
        stats.put("inactiveCount", systemConfigRepository.countInactiveConfigs());
        
        List<Object[]> typeStats = systemConfigRepository.countByConfigType();
        Map<String, Long> typeCountMap = new HashMap<>();
        for (Object[] stat : typeStats) {
            typeCountMap.put(stat[0].toString(), (Long) stat[1]);
        }
        stats.put("typeStats", typeCountMap);
        
        return stats;
    }
}
