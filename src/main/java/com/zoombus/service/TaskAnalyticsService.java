package com.zoombus.service;

import com.zoombus.entity.TaskExecutionRecord;
import com.zoombus.repository.TaskExecutionRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务分析统计服务
 * 提供任务执行趋势分析、性能报告和可视化数据
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TaskAnalyticsService {

    private final TaskExecutionRecordRepository taskExecutionRecordRepository;

    /**
     * 获取任务执行趋势数据
     */
    public Map<String, Object> getTaskExecutionTrend(String taskName, int days) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            
            List<TaskExecutionRecord> records = taskExecutionRecordRepository
                    .findByTaskNameAndExecutionTimeBetween(taskName, startTime, endTime);
            
            // 按天分组统计
            Map<String, Map<String, Long>> dailyStats = records.stream()
                    .collect(Collectors.groupingBy(
                            record -> record.getExecutionTime().toLocalDate().toString(),
                            Collectors.groupingBy(
                                    record -> record.getStatus().name(),
                                    Collectors.counting()
                            )
                    ));
            
            // 构建趋势数据
            List<Map<String, Object>> trendData = new ArrayList<>();
            for (int i = days - 1; i >= 0; i--) {
                LocalDateTime date = endTime.minusDays(i);
                String dateStr = date.toLocalDate().toString();
                
                Map<String, Long> dayStats = dailyStats.getOrDefault(dateStr, new HashMap<>());
                
                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", dateStr);
                dayData.put("total", dayStats.values().stream().mapToLong(Long::longValue).sum());
                dayData.put("success", dayStats.getOrDefault("SUCCESS", 0L));
                dayData.put("failed", dayStats.getOrDefault("FAILED", 0L));
                dayData.put("running", dayStats.getOrDefault("RUNNING", 0L));
                
                trendData.add(dayData);
            }
            
            return Map.of(
                    "taskName", taskName,
                    "period", days + "天",
                    "trendData", trendData,
                    "totalRecords", records.size()
            );
        } catch (Exception e) {
            log.error("获取任务执行趋势失败: {}", taskName, e);
            return Map.of("error", "获取趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务性能报告
     */
    public Map<String, Object> getTaskPerformanceReport(String taskName, int days) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            
            List<TaskExecutionRecord> records = taskExecutionRecordRepository
                    .findByTaskNameAndExecutionTimeBetween(taskName, startTime, endTime);
            
            if (records.isEmpty()) {
                return Map.of("message", "指定时间段内无执行记录");
            }

            // 执行时间统计
            List<Long> durations = records.stream()
                    .filter(r -> r.getDurationMs() != null)
                    .map(TaskExecutionRecord::getDurationMs)
                    .sorted()
                    .collect(Collectors.toList());
            
            Map<String, Object> performanceStats = new HashMap<>();
            
            if (!durations.isEmpty()) {
                performanceStats.put("minDuration", durations.get(0));
                performanceStats.put("maxDuration", durations.get(durations.size() - 1));
                performanceStats.put("avgDuration", durations.stream().mapToLong(Long::longValue).average().orElse(0.0));
                performanceStats.put("medianDuration", getMedian(durations));
                performanceStats.put("p95Duration", getPercentile(durations, 95));
                performanceStats.put("p99Duration", getPercentile(durations, 99));
            }
            
            // 成功率统计
            long totalCount = records.size();
            long successCount = records.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.SUCCESS)
                    .count();
            long failedCount = records.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                    .count();
            
            performanceStats.put("totalExecutions", totalCount);
            performanceStats.put("successCount", successCount);
            performanceStats.put("failedCount", failedCount);
            performanceStats.put("successRate", totalCount > 0 ? (double) successCount / totalCount : 0.0);
            
            // 错误分析
            Map<String, Long> errorStats = records.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                    .filter(r -> r.getErrorMessage() != null)
                    .collect(Collectors.groupingBy(
                            r -> extractErrorType(r.getErrorMessage()),
                            Collectors.counting()
                    ));
            
            performanceStats.put("errorTypes", errorStats);
            
            // 执行频率分析
            if (records.size() > 1) {
                List<TaskExecutionRecord> sortedRecords = records.stream()
                        .sorted(Comparator.comparing(TaskExecutionRecord::getExecutionTime))
                        .collect(Collectors.toList());
                
                List<Long> intervals = new ArrayList<>();
                for (int i = 1; i < sortedRecords.size(); i++) {
                    long interval = java.time.Duration.between(
                            sortedRecords.get(i-1).getExecutionTime(),
                            sortedRecords.get(i).getExecutionTime()
                    ).toMinutes();
                    intervals.add(interval);
                }
                
                if (!intervals.isEmpty()) {
                    performanceStats.put("avgInterval", intervals.stream().mapToLong(Long::longValue).average().orElse(0.0));
                    performanceStats.put("minInterval", Collections.min(intervals));
                    performanceStats.put("maxInterval", Collections.max(intervals));
                }
            }
            
            return Map.of(
                    "taskName", taskName,
                    "period", days + "天",
                    "reportTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                    "performance", performanceStats
            );
        } catch (Exception e) {
            log.error("获取任务性能报告失败: {}", taskName, e);
            return Map.of("error", "获取性能报告失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有任务的概览统计
     */
    public Map<String, Object> getAllTasksOverview(int days) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            
            // 获取所有记录，使用大的页面大小
            Pageable pageable = PageRequest.of(0, 10000);
            List<TaskExecutionRecord> allRecords = taskExecutionRecordRepository
                    .findByExecutionTimeBetween(startTime, endTime, pageable)
                    .getContent();
            
            // 按任务名分组
            Map<String, List<TaskExecutionRecord>> taskGroups = allRecords.stream()
                    .collect(Collectors.groupingBy(TaskExecutionRecord::getTaskName));
            
            List<Map<String, Object>> taskSummaries = new ArrayList<>();
            
            for (Map.Entry<String, List<TaskExecutionRecord>> entry : taskGroups.entrySet()) {
                String taskName = entry.getKey();
                List<TaskExecutionRecord> records = entry.getValue();
                
                long totalCount = records.size();
                long successCount = records.stream()
                        .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.SUCCESS)
                        .count();
                long failedCount = records.stream()
                        .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                        .count();
                
                OptionalDouble avgDuration = records.stream()
                        .filter(r -> r.getDurationMs() != null)
                        .mapToLong(TaskExecutionRecord::getDurationMs)
                        .average();
                
                Optional<TaskExecutionRecord> lastExecution = records.stream()
                        .max(Comparator.comparing(TaskExecutionRecord::getExecutionTime));
                
                Map<String, Object> taskSummary = new HashMap<>();
                taskSummary.put("taskName", taskName);
                taskSummary.put("totalExecutions", totalCount);
                taskSummary.put("successCount", successCount);
                taskSummary.put("failedCount", failedCount);
                taskSummary.put("successRate", totalCount > 0 ? (double) successCount / totalCount : 0.0);
                taskSummary.put("avgDuration", avgDuration.orElse(0.0));
                taskSummary.put("lastExecutionTime", lastExecution.map(TaskExecutionRecord::getExecutionTime).orElse(null));
                taskSummary.put("lastExecutionStatus", lastExecution.map(r -> r.getStatus().name()).orElse(null));
                
                taskSummaries.add(taskSummary);
            }
            
            // 按成功率排序
            taskSummaries.sort((t1, t2) -> Double.compare(
                    (Double) t2.get("successRate"), 
                    (Double) t1.get("successRate")
            ));
            
            // 全局统计
            long globalTotal = allRecords.size();
            long globalSuccess = allRecords.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.SUCCESS)
                    .count();
            long globalFailed = allRecords.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                    .count();
            
            Map<String, Object> globalStats = new HashMap<>();
            globalStats.put("totalTasks", taskGroups.size());
            globalStats.put("totalExecutions", globalTotal);
            globalStats.put("globalSuccessCount", globalSuccess);
            globalStats.put("globalFailedCount", globalFailed);
            globalStats.put("globalSuccessRate", globalTotal > 0 ? (double) globalSuccess / globalTotal : 0.0);
            
            return Map.of(
                    "period", days + "天",
                    "reportTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                    "globalStats", globalStats,
                    "taskSummaries", taskSummaries
            );
        } catch (Exception e) {
            log.error("获取所有任务概览失败", e);
            return Map.of("error", "获取概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务执行热力图数据
     */
    public Map<String, Object> getTaskExecutionHeatmap(String taskName, int days) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            
            List<TaskExecutionRecord> records = taskExecutionRecordRepository
                    .findByTaskNameAndExecutionTimeBetween(taskName, startTime, endTime);
            
            // 按小时分组统计
            Map<String, Map<Integer, Long>> dailyHourlyStats = records.stream()
                    .collect(Collectors.groupingBy(
                            record -> record.getExecutionTime().toLocalDate().toString(),
                            Collectors.groupingBy(
                                    record -> record.getExecutionTime().getHour(),
                                    Collectors.counting()
                            )
                    ));
            
            List<Map<String, Object>> heatmapData = new ArrayList<>();
            
            for (int i = days - 1; i >= 0; i--) {
                LocalDateTime date = endTime.minusDays(i);
                String dateStr = date.toLocalDate().toString();
                
                Map<Integer, Long> hourlyStats = dailyHourlyStats.getOrDefault(dateStr, new HashMap<>());
                
                for (int hour = 0; hour < 24; hour++) {
                    Map<String, Object> dataPoint = new HashMap<>();
                    dataPoint.put("date", dateStr);
                    dataPoint.put("hour", hour);
                    dataPoint.put("count", hourlyStats.getOrDefault(hour, 0L));
                    heatmapData.add(dataPoint);
                }
            }
            
            return Map.of(
                    "taskName", taskName,
                    "period", days + "天",
                    "heatmapData", heatmapData
            );
        } catch (Exception e) {
            log.error("获取任务执行热力图失败: {}", taskName, e);
            return Map.of("error", "获取热力图数据失败: " + e.getMessage());
        }
    }

    // 辅助方法
    private double getMedian(List<Long> sortedList) {
        int size = sortedList.size();
        if (size % 2 == 0) {
            return (sortedList.get(size / 2 - 1) + sortedList.get(size / 2)) / 2.0;
        } else {
            return sortedList.get(size / 2);
        }
    }

    private double getPercentile(List<Long> sortedList, int percentile) {
        int index = (int) Math.ceil(sortedList.size() * percentile / 100.0) - 1;
        return sortedList.get(Math.max(0, Math.min(index, sortedList.size() - 1)));
    }

    private String extractErrorType(String errorMessage) {
        if (errorMessage == null) {
            return "Unknown";
        }
        
        // 简单的错误类型提取逻辑
        if (errorMessage.contains("timeout") || errorMessage.contains("Timeout")) {
            return "Timeout";
        } else if (errorMessage.contains("connection") || errorMessage.contains("Connection")) {
            return "Connection";
        } else if (errorMessage.contains("sql") || errorMessage.contains("SQL")) {
            return "Database";
        } else if (errorMessage.contains("null") || errorMessage.contains("Null")) {
            return "NullPointer";
        } else {
            return "Other";
        }
    }
}
