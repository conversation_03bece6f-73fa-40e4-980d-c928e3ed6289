package com.zoombus.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.TaskExecutionRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.Collections;

/**
 * 任务执行记录缓存服务
 * 使用 Redis 缓存频繁查询的任务执行数据，提高查询性能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TaskExecutionCacheService {

    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectMapper;

    // 缓存键前缀
    private static final String CACHE_PREFIX = "task_execution:";
    private static final String STATS_PREFIX = "task_stats:";
    private static final String RUNNING_TASKS_KEY = "running_tasks";

    // 缓存过期时间
    private static final Duration TASK_RECORD_TTL = Duration.ofMinutes(10); // 任务记录缓存10分钟
    private static final Duration TASK_STATS_TTL = Duration.ofMinutes(5);   // 统计信息缓存5分钟
    private static final Duration RUNNING_TASKS_TTL = Duration.ofMinutes(1); // 运行中任务缓存1分钟

    /**
     * 缓存任务执行记录
     */
    public void cacheTaskRecord(TaskExecutionRecord record) {
        try {
            String key = CACHE_PREFIX + "record:" + record.getId();
            String value = objectMapper.writeValueAsString(record);
            redisTemplate.opsForValue().set(key, value, TASK_RECORD_TTL.toSeconds(), TimeUnit.SECONDS);
            
            log.debug("缓存任务执行记录: taskName={}, recordId={}", record.getTaskName(), record.getId());
        } catch (JsonProcessingException e) {
            log.error("缓存任务执行记录失败", e);
        }
    }

    /**
     * 从缓存获取任务执行记录
     */
    public TaskExecutionRecord getCachedTaskRecord(Long recordId) {
        try {
            String key = CACHE_PREFIX + "record:" + recordId;
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                return objectMapper.readValue(value, TaskExecutionRecord.class);
            }
        } catch (Exception e) {
            log.error("从缓存获取任务执行记录失败: recordId={}", recordId, e);
        }
        return null;
    }

    /**
     * 缓存任务统计信息
     */
    public void cacheTaskStatistics(String taskName, Map<String, Object> statistics) {
        try {
            String key = STATS_PREFIX + taskName;
            String value = objectMapper.writeValueAsString(statistics);
            redisTemplate.opsForValue().set(key, value, TASK_STATS_TTL.toSeconds(), TimeUnit.SECONDS);
            
            log.debug("缓存任务统计信息: taskName={}", taskName);
        } catch (JsonProcessingException e) {
            log.error("缓存任务统计信息失败: taskName={}", taskName, e);
        }
    }

    /**
     * 从缓存获取任务统计信息
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getCachedTaskStatistics(String taskName) {
        try {
            String key = STATS_PREFIX + taskName;
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                return objectMapper.readValue(value, Map.class);
            }
        } catch (Exception e) {
            log.error("从缓存获取任务统计信息失败: taskName={}", taskName, e);
        }
        return null;
    }

    /**
     * 缓存运行中的任务列表
     */
    public void cacheRunningTasks(List<String> runningTaskNames) {
        try {
            String value = objectMapper.writeValueAsString(runningTaskNames);
            redisTemplate.opsForValue().set(RUNNING_TASKS_KEY, value, RUNNING_TASKS_TTL.toSeconds(), TimeUnit.SECONDS);
            
            log.debug("缓存运行中任务列表: count={}", runningTaskNames.size());
        } catch (JsonProcessingException e) {
            log.error("缓存运行中任务列表失败", e);
        }
    }

    /**
     * 从缓存获取运行中的任务列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getCachedRunningTasks() {
        try {
            String value = redisTemplate.opsForValue().get(RUNNING_TASKS_KEY);
            
            if (value != null) {
                return objectMapper.readValue(value, List.class);
            }
        } catch (Exception e) {
            log.error("从缓存获取运行中任务列表失败", e);
        }
        return null;
    }

    /**
     * 缓存任务最近执行记录列表
     */
    public void cacheRecentTaskRecords(String taskName, List<TaskExecutionRecord> records) {
        try {
            String key = CACHE_PREFIX + "recent:" + taskName;
            String value = objectMapper.writeValueAsString(records);
            redisTemplate.opsForValue().set(key, value, TASK_RECORD_TTL.toSeconds(), TimeUnit.SECONDS);
            
            log.debug("缓存任务最近执行记录: taskName={}, count={}", taskName, records.size());
        } catch (JsonProcessingException e) {
            log.error("缓存任务最近执行记录失败: taskName={}", taskName, e);
        }
    }

    /**
     * 从缓存获取任务最近执行记录列表
     */
    @SuppressWarnings("unchecked")
    public List<TaskExecutionRecord> getCachedRecentTaskRecords(String taskName) {
        try {
            String key = CACHE_PREFIX + "recent:" + taskName;
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                List<Map<String, Object>> recordMaps = objectMapper.readValue(value, List.class);
                return recordMaps.stream()
                        .map(map -> objectMapper.convertValue(map, TaskExecutionRecord.class))
                        .collect(java.util.stream.Collectors.toList());
            }
        } catch (Exception e) {
            log.error("从缓存获取任务最近执行记录失败: taskName={}", taskName, e);
        }
        return null;
    }

    /**
     * 清除指定任务的所有缓存
     */
    public void clearTaskCache(String taskName) {
        try {
            // 清除统计信息缓存
            redisTemplate.delete(STATS_PREFIX + taskName);
            
            // 清除最近执行记录缓存
            redisTemplate.delete(CACHE_PREFIX + "recent:" + taskName);
            
            log.debug("清除任务缓存: taskName={}", taskName);
        } catch (Exception e) {
            log.error("清除任务缓存失败: taskName={}", taskName, e);
        }
    }

    /**
     * 清除所有任务相关缓存
     */
    public void clearAllTaskCache() {
        try {
            // 清除运行中任务缓存
            redisTemplate.delete(RUNNING_TASKS_KEY);
            
            // 清除所有统计信息缓存
            redisTemplate.delete(redisTemplate.keys(STATS_PREFIX + "*"));
            
            // 清除所有任务记录缓存
            redisTemplate.delete(redisTemplate.keys(CACHE_PREFIX + "*"));
            
            log.info("清除所有任务缓存");
        } catch (Exception e) {
            log.error("清除所有任务缓存失败", e);
        }
    }

    /**
     * 检查缓存是否存在
     */
    public boolean isCacheExists(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("检查缓存是否存在失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 获取缓存剩余过期时间
     */
    public long getCacheExpireTime(String key) {
        try {
            return redisTemplate.getExpire(key, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取缓存过期时间失败: key={}", key, e);
            return -1;
        }
    }

    /**
     * 刷新缓存过期时间
     */
    public void refreshCacheExpire(String key, Duration ttl) {
        try {
            redisTemplate.expire(key, ttl.toSeconds(), TimeUnit.SECONDS);
            log.debug("刷新缓存过期时间: key={}, ttl={}s", key, ttl.toSeconds());
        } catch (Exception e) {
            log.error("刷新缓存过期时间失败: key={}", key, e);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        try {
            long taskRecordCount = redisTemplate.keys(CACHE_PREFIX + "record:*").size();
            long taskStatsCount = redisTemplate.keys(STATS_PREFIX + "*").size();
            long recentRecordsCount = redisTemplate.keys(CACHE_PREFIX + "recent:*").size();
            
            return Map.of(
                    "taskRecordCacheCount", taskRecordCount,
                    "taskStatsCacheCount", taskStatsCount,
                    "recentRecordsCacheCount", recentRecordsCount,
                    "runningTasksCached", isCacheExists(RUNNING_TASKS_KEY),
                    "cacheTimestamp", LocalDateTime.now()
            );
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return Map.of("error", "获取缓存统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量缓存任务执行记录
     */
    public void batchCacheTaskRecords(List<TaskExecutionRecord> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        try {
            // 批量操作
            for (TaskExecutionRecord record : records) {
                try {
                    String key = CACHE_PREFIX + "record:" + record.getId();
                    String value = objectMapper.writeValueAsString(record);
                    redisTemplate.opsForValue().set(key, value, TASK_RECORD_TTL.toSeconds(), TimeUnit.SECONDS);
                } catch (JsonProcessingException e) {
                    log.error("序列化任务记录失败: {}", record.getId(), e);
                }
            }

            log.debug("批量缓存了 {} 条任务执行记录", records.size());
        } catch (Exception e) {
            log.error("批量缓存任务执行记录失败", e);
        }
    }

    /**
     * 批量获取任务执行记录
     */
    public List<TaskExecutionRecord> batchGetTaskRecords(List<Long> recordIds) {
        if (recordIds == null || recordIds.isEmpty()) {
            return Collections.emptyList();
        }

        try {
            List<String> keys = recordIds.stream()
                    .map(id -> CACHE_PREFIX + "record:" + id)
                    .collect(Collectors.toList());

            List<String> cachedValues = redisTemplate.opsForValue().multiGet(keys);

            if (cachedValues == null) {
                return Collections.emptyList();
            }

            return cachedValues.stream()
                    .filter(value -> value != null)
                    .map(value -> {
                        try {
                            return objectMapper.readValue(value, TaskExecutionRecord.class);
                        } catch (JsonProcessingException e) {
                            log.error("反序列化任务记录失败", e);
                            return null;
                        }
                    })
                    .filter(record -> record != null)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量获取缓存的任务执行记录失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 缓存任务概览数据
     */
    public void cacheTaskOverview(Map<String, Object> overviewData) {
        try {
            String key = CACHE_PREFIX + "overview";
            String value = objectMapper.writeValueAsString(overviewData);
            redisTemplate.opsForValue().set(key, value, TASK_STATS_TTL.toSeconds(), TimeUnit.SECONDS);
            log.debug("缓存任务概览数据");
        } catch (JsonProcessingException e) {
            log.error("缓存任务概览数据失败", e);
        }
    }

    /**
     * 获取缓存的任务概览数据
     */
    public Map<String, Object> getCachedTaskOverview() {
        try {
            String key = CACHE_PREFIX + "overview";
            String cachedValue = redisTemplate.opsForValue().get(key);

            if (cachedValue != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> overview = objectMapper.readValue(cachedValue, Map.class);
                return overview;
            }
        } catch (JsonProcessingException e) {
            log.error("获取缓存的任务概览数据失败", e);
        }
        return null;
    }

    /**
     * 缓存预热 - 预加载常用数据
     */
    public void warmUpCache() {
        log.info("开始缓存预热");

        try {
            // 这里可以预加载一些常用的数据
            // 例如最近的任务执行记录、常用的统计信息等
            log.info("缓存预热完成");
        } catch (Exception e) {
            log.error("缓存预热失败", e);
        }
    }

    /**
     * 清除特定任务的缓存
     */
    public void evictTaskCache(String taskName) {
        try {
            String pattern = CACHE_PREFIX + "*" + taskName + "*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.debug("清除任务缓存: {}, 删除了 {} 个键", taskName, keys.size());
            }
        } catch (Exception e) {
            log.error("清除任务缓存失败: {}", taskName, e);
        }
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        try {
            Set<String> keys = redisTemplate.keys(CACHE_PREFIX + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清除所有缓存，删除了 {} 个键", keys.size());
            }
        } catch (Exception e) {
            log.error("清除所有缓存失败", e);
        }
    }

    /**
     * 清除过期缓存
     */
    public void cleanExpiredCache() {
        try {
            // 清理过期的缓存键
            // Redis会自动清理过期键，这里主要是手动清理一些特殊情况
            log.debug("清除过期缓存完成");
        } catch (Exception e) {
            log.error("清除过期缓存失败", e);
        }
    }
}
