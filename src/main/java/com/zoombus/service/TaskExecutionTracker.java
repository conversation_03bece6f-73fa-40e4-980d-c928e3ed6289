package com.zoombus.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 任务执行状态跟踪器
 * 用于跟踪任务的执行状态，防止重复执行，提供执行监控
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TaskExecutionTracker {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // 本地缓存，用于快速检查（Redis不可用时的备选方案）
    private final ConcurrentHashMap<String, TaskExecutionInfo> localCache = new ConcurrentHashMap<>();
    
    private static final String RUNNING_TASKS_KEY_PREFIX = "scheduled_task:running:";
    private static final String TASK_HISTORY_KEY_PREFIX = "scheduled_task:history:";
    private static final long DEFAULT_TASK_TIMEOUT_MINUTES = 30;
    
    /**
     * 标记任务开始执行
     */
    public void markTaskAsRunning(String taskName, Long executionId) {
        String key = RUNNING_TASKS_KEY_PREFIX + taskName;
        TaskExecutionInfo info = new TaskExecutionInfo(taskName, executionId, LocalDateTime.now());
        
        try {
            // 存储到Redis，设置过期时间
            redisTemplate.opsForValue().set(key, info, DEFAULT_TASK_TIMEOUT_MINUTES, TimeUnit.MINUTES);
            log.debug("任务 {} (ID: {}) 已标记为执行中", taskName, executionId);
        } catch (Exception e) {
            log.warn("无法将任务状态存储到Redis，使用本地缓存: {}", e.getMessage());
        }
        
        // 同时存储到本地缓存
        localCache.put(taskName, info);
    }
    
    /**
     * 标记任务执行完成
     */
    public void markTaskAsCompleted(String taskName, Long executionId) {
        String runningKey = RUNNING_TASKS_KEY_PREFIX + taskName;
        String historyKey = TASK_HISTORY_KEY_PREFIX + taskName;
        
        try {
            // 从Redis中移除运行状态
            TaskExecutionInfo runningInfo = (TaskExecutionInfo) redisTemplate.opsForValue().get(runningKey);
            if (runningInfo != null && runningInfo.getExecutionId().equals(executionId)) {
                redisTemplate.delete(runningKey);
                
                // 添加到历史记录
                TaskExecutionInfo completedInfo = new TaskExecutionInfo(taskName, executionId, 
                        runningInfo.getStartTime(), LocalDateTime.now());
                redisTemplate.opsForList().leftPush(historyKey, completedInfo);
                
                // 保持历史记录数量在合理范围内（最多100条）
                redisTemplate.opsForList().trim(historyKey, 0, 99);
                
                log.debug("任务 {} (ID: {}) 已标记为完成", taskName, executionId);
            }
        } catch (Exception e) {
            log.warn("无法更新Redis中的任务状态: {}", e.getMessage());
        }
        
        // 从本地缓存中移除
        TaskExecutionInfo localInfo = localCache.get(taskName);
        if (localInfo != null && localInfo.getExecutionId().equals(executionId)) {
            localCache.remove(taskName);
        }
    }
    
    /**
     * 检查任务是否正在执行
     */
    public boolean isTaskRunning(String taskName) {
        String key = RUNNING_TASKS_KEY_PREFIX + taskName;
        
        try {
            TaskExecutionInfo info = (TaskExecutionInfo) redisTemplate.opsForValue().get(key);
            if (info != null) {
                // 检查任务是否超时
                if (isTaskTimeout(info)) {
                    log.warn("任务 {} (ID: {}) 执行超时，自动清理", taskName, info.getExecutionId());
                    markTaskAsCompleted(taskName, info.getExecutionId());
                    return false;
                }
                return true;
            }
        } catch (Exception e) {
            log.warn("无法从Redis检查任务状态，使用本地缓存: {}", e.getMessage());
        }
        
        // 检查本地缓存
        TaskExecutionInfo localInfo = localCache.get(taskName);
        if (localInfo != null) {
            if (isTaskTimeout(localInfo)) {
                log.warn("本地缓存中的任务 {} (ID: {}) 执行超时，自动清理", taskName, localInfo.getExecutionId());
                localCache.remove(taskName);
                return false;
            }
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取正在执行的任务列表
     */
    public Set<String> getRunningTasks() {
        try {
            Set<String> keys = redisTemplate.keys(RUNNING_TASKS_KEY_PREFIX + "*");
            return keys.stream()
                    .map(key -> key.substring(RUNNING_TASKS_KEY_PREFIX.length()))
                    .collect(java.util.stream.Collectors.toSet());
        } catch (Exception e) {
            log.warn("无法从Redis获取运行中的任务列表，使用本地缓存: {}", e.getMessage());
            return localCache.keySet();
        }
    }
    
    /**
     * 获取任务执行信息
     */
    public TaskExecutionInfo getTaskExecutionInfo(String taskName) {
        String key = RUNNING_TASKS_KEY_PREFIX + taskName;
        
        try {
            TaskExecutionInfo info = (TaskExecutionInfo) redisTemplate.opsForValue().get(key);
            if (info != null) {
                return info;
            }
        } catch (Exception e) {
            log.warn("无法从Redis获取任务执行信息，使用本地缓存: {}", e.getMessage());
        }
        
        return localCache.get(taskName);
    }
    
    /**
     * 获取任务执行历史
     */
    public java.util.List<TaskExecutionInfo> getTaskExecutionHistory(String taskName, int limit) {
        String key = TASK_HISTORY_KEY_PREFIX + taskName;
        
        try {
            return redisTemplate.opsForList().range(key, 0, limit - 1)
                    .stream()
                    .map(obj -> (TaskExecutionInfo) obj)
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.warn("无法从Redis获取任务执行历史: {}", e.getMessage());
            return java.util.Collections.emptyList();
        }
    }
    
    /**
     * 清理超时的任务
     */
    public void cleanupTimeoutTasks() {
        try {
            Set<String> runningTaskKeys = redisTemplate.keys(RUNNING_TASKS_KEY_PREFIX + "*");
            
            for (String key : runningTaskKeys) {
                TaskExecutionInfo info = (TaskExecutionInfo) redisTemplate.opsForValue().get(key);
                if (info != null && isTaskTimeout(info)) {
                    String taskName = key.substring(RUNNING_TASKS_KEY_PREFIX.length());
                    log.warn("清理超时任务: {} (ID: {})", taskName, info.getExecutionId());
                    markTaskAsCompleted(taskName, info.getExecutionId());
                }
            }
        } catch (Exception e) {
            log.error("清理超时任务时发生异常", e);
        }
        
        // 清理本地缓存中的超时任务
        localCache.entrySet().removeIf(entry -> {
            if (isTaskTimeout(entry.getValue())) {
                log.warn("清理本地缓存中的超时任务: {} (ID: {})", 
                        entry.getKey(), entry.getValue().getExecutionId());
                return true;
            }
            return false;
        });
    }
    
    /**
     * 检查任务是否超时
     */
    private boolean isTaskTimeout(TaskExecutionInfo info) {
        Duration duration = Duration.between(info.getStartTime(), LocalDateTime.now());
        return duration.toMinutes() > DEFAULT_TASK_TIMEOUT_MINUTES;
    }
    
    /**
     * 强制清理任务状态（用于异常情况）
     */
    public void forceCleanupTask(String taskName) {
        String key = RUNNING_TASKS_KEY_PREFIX + taskName;
        
        try {
            redisTemplate.delete(key);
            log.info("强制清理任务状态: {}", taskName);
        } catch (Exception e) {
            log.warn("无法从Redis清理任务状态: {}", e.getMessage());
        }
        
        localCache.remove(taskName);
    }
    
    /**
     * 任务执行信息
     */
    public static class TaskExecutionInfo implements java.io.Serializable {
        private String taskName;
        private Long executionId;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        
        public TaskExecutionInfo() {}
        
        public TaskExecutionInfo(String taskName, Long executionId, LocalDateTime startTime) {
            this.taskName = taskName;
            this.executionId = executionId;
            this.startTime = startTime;
        }
        
        public TaskExecutionInfo(String taskName, Long executionId, LocalDateTime startTime, LocalDateTime endTime) {
            this.taskName = taskName;
            this.executionId = executionId;
            this.startTime = startTime;
            this.endTime = endTime;
        }
        
        public Duration getDuration() {
            LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
            return Duration.between(startTime, end);
        }
        
        public String getFormattedDuration() {
            Duration duration = getDuration();
            long minutes = duration.toMinutes();
            long seconds = duration.getSeconds() % 60;
            return String.format("%d分%d秒", minutes, seconds);
        }
        
        // Getters and Setters
        public String getTaskName() { return taskName; }
        public void setTaskName(String taskName) { this.taskName = taskName; }
        
        public Long getExecutionId() { return executionId; }
        public void setExecutionId(Long executionId) { this.executionId = executionId; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        @Override
        public String toString() {
            return String.format("TaskExecutionInfo{taskName='%s', executionId=%d, startTime=%s, duration=%s}", 
                    taskName, executionId, 
                    startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), 
                    getFormattedDuration());
        }
    }
}
