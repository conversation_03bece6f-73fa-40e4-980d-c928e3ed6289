package com.zoombus.service;

import com.zoombus.entity.TaskExecutionRecord;
import com.zoombus.repository.TaskExecutionRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.OptionalDouble;

/**
 * 任务监控告警服务
 * 提供任务失败告警、性能监控、健康检查等功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TaskMonitoringService {

    private final TaskExecutionRecordRepository taskExecutionRecordRepository;
    private final TaskExecutionCacheService cacheService;

    // 告警规则配置
    private final Map<String, AlertRule> alertRules = new ConcurrentHashMap<>();
    
    // 告警历史记录
    private final Map<String, List<AlertRecord>> alertHistory = new ConcurrentHashMap<>();

    /**
     * 告警规则类
     */
    public static class AlertRule {
        private String taskName;
        private AlertType type;
        private double threshold;
        private int timeWindowMinutes;
        private boolean enabled;
        private String notificationChannel;

        public AlertRule(String taskName, AlertType type, double threshold) {
            this.taskName = taskName;
            this.type = type;
            this.threshold = threshold;
            this.timeWindowMinutes = 60; // 默认1小时时间窗口
            this.enabled = true;
            this.notificationChannel = "log"; // 默认日志通知
        }

        // Getters and Setters
        public String getTaskName() { return taskName; }
        public void setTaskName(String taskName) { this.taskName = taskName; }
        public AlertType getType() { return type; }
        public void setType(AlertType type) { this.type = type; }
        public double getThreshold() { return threshold; }
        public void setThreshold(double threshold) { this.threshold = threshold; }
        public int getTimeWindowMinutes() { return timeWindowMinutes; }
        public void setTimeWindowMinutes(int timeWindowMinutes) { this.timeWindowMinutes = timeWindowMinutes; }
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public String getNotificationChannel() { return notificationChannel; }
        public void setNotificationChannel(String notificationChannel) { this.notificationChannel = notificationChannel; }
    }

    /**
     * 告警类型枚举
     */
    public enum AlertType {
        FAILURE_RATE,      // 失败率告警
        EXECUTION_TIME,    // 执行时间告警
        NO_EXECUTION,      // 未执行告警
        CONSECUTIVE_FAILURES // 连续失败告警
    }

    /**
     * 告警记录类
     */
    public static class AlertRecord {
        private String taskName;
        private AlertType type;
        private String message;
        private LocalDateTime alertTime;
        private String severity;
        private boolean resolved;

        public AlertRecord(String taskName, AlertType type, String message, String severity) {
            this.taskName = taskName;
            this.type = type;
            this.message = message;
            this.alertTime = LocalDateTime.now();
            this.severity = severity;
            this.resolved = false;
        }

        // Getters and Setters
        public String getTaskName() { return taskName; }
        public void setTaskName(String taskName) { this.taskName = taskName; }
        public AlertType getType() { return type; }
        public void setType(AlertType type) { this.type = type; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public LocalDateTime getAlertTime() { return alertTime; }
        public void setAlertTime(LocalDateTime alertTime) { this.alertTime = alertTime; }
        public String getSeverity() { return severity; }
        public void setSeverity(String severity) { this.severity = severity; }
        public boolean isResolved() { return resolved; }
        public void setResolved(boolean resolved) { this.resolved = resolved; }
    }

    /**
     * 添加告警规则
     */
    public void addAlertRule(String taskName, AlertType type, double threshold) {
        String ruleKey = taskName + "_" + type.name();
        AlertRule rule = new AlertRule(taskName, type, threshold);
        alertRules.put(ruleKey, rule);
        log.info("添加告警规则: {} - {} (阈值: {})", taskName, type, threshold);
    }

    /**
     * 移除告警规则
     */
    public void removeAlertRule(String taskName, AlertType type) {
        String ruleKey = taskName + "_" + type.name();
        AlertRule removed = alertRules.remove(ruleKey);
        if (removed != null) {
            log.info("移除告警规则: {} - {}", taskName, type);
        }
    }

    /**
     * 检查所有任务的告警条件
     */
    @Async
    public void checkAllAlerts() {
        try {
            log.debug("开始检查所有任务的告警条件");
            
            for (AlertRule rule : alertRules.values()) {
                if (rule.isEnabled()) {
                    checkTaskAlert(rule);
                }
            }
            
            log.debug("完成所有任务的告警检查");
        } catch (Exception e) {
            log.error("检查告警条件失败", e);
        }
    }

    /**
     * 检查单个任务的告警条件
     */
    private void checkTaskAlert(AlertRule rule) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusMinutes(rule.getTimeWindowMinutes());
            
            List<TaskExecutionRecord> records = taskExecutionRecordRepository
                    .findByTaskNameAndExecutionTimeBetween(rule.getTaskName(), startTime, endTime);
            
            switch (rule.getType()) {
                case FAILURE_RATE:
                    checkFailureRateAlert(rule, records);
                    break;
                case EXECUTION_TIME:
                    checkExecutionTimeAlert(rule, records);
                    break;
                case NO_EXECUTION:
                    checkNoExecutionAlert(rule, records);
                    break;
                case CONSECUTIVE_FAILURES:
                    checkConsecutiveFailuresAlert(rule, records);
                    break;
            }
        } catch (Exception e) {
            log.error("检查任务告警失败: {}", rule.getTaskName(), e);
        }
    }

    /**
     * 检查失败率告警
     */
    private void checkFailureRateAlert(AlertRule rule, List<TaskExecutionRecord> records) {
        if (records.isEmpty()) {
            return;
        }

        long totalCount = records.size();
        long failureCount = records.stream()
                .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED)
                .count();
        
        double failureRate = (double) failureCount / totalCount;
        
        if (failureRate > rule.getThreshold()) {
            String message = String.format("任务 %s 失败率过高: %.2f%% (阈值: %.2f%%, 时间窗口: %d分钟)", 
                    rule.getTaskName(), failureRate * 100, rule.getThreshold() * 100, rule.getTimeWindowMinutes());
            
            triggerAlert(rule.getTaskName(), AlertType.FAILURE_RATE, message, "HIGH");
        }
    }

    /**
     * 检查执行时间告警
     */
    private void checkExecutionTimeAlert(AlertRule rule, List<TaskExecutionRecord> records) {
        OptionalDouble avgDuration = records.stream()
                .filter(r -> r.getDurationMs() != null)
                .mapToLong(TaskExecutionRecord::getDurationMs)
                .average();
        
        if (avgDuration.isPresent() && avgDuration.getAsDouble() > rule.getThreshold()) {
            String message = String.format("任务 %s 平均执行时间过长: %.2f毫秒 (阈值: %.2f毫秒)", 
                    rule.getTaskName(), avgDuration.getAsDouble(), rule.getThreshold());
            
            triggerAlert(rule.getTaskName(), AlertType.EXECUTION_TIME, message, "MEDIUM");
        }
    }

    /**
     * 检查未执行告警
     */
    private void checkNoExecutionAlert(AlertRule rule, List<TaskExecutionRecord> records) {
        if (records.isEmpty()) {
            String message = String.format("任务 %s 在过去 %d 分钟内未执行", 
                    rule.getTaskName(), rule.getTimeWindowMinutes());
            
            triggerAlert(rule.getTaskName(), AlertType.NO_EXECUTION, message, "HIGH");
        }
    }

    /**
     * 检查连续失败告警
     */
    private void checkConsecutiveFailuresAlert(AlertRule rule, List<TaskExecutionRecord> records) {
        if (records.isEmpty()) {
            return;
        }

        // 按执行时间排序，获取最近的记录
        List<TaskExecutionRecord> sortedRecords = records.stream()
                .sorted((r1, r2) -> r2.getExecutionTime().compareTo(r1.getExecutionTime()))
                .collect(Collectors.toList());
        
        int consecutiveFailures = 0;
        for (TaskExecutionRecord record : sortedRecords) {
            if (record.getStatus() == TaskExecutionRecord.ExecutionStatus.FAILED) {
                consecutiveFailures++;
            } else {
                break;
            }
        }
        
        if (consecutiveFailures >= rule.getThreshold()) {
            String message = String.format("任务 %s 连续失败 %d 次 (阈值: %.0f次)", 
                    rule.getTaskName(), consecutiveFailures, rule.getThreshold());
            
            triggerAlert(rule.getTaskName(), AlertType.CONSECUTIVE_FAILURES, message, "CRITICAL");
        }
    }

    /**
     * 触发告警
     */
    private void triggerAlert(String taskName, AlertType type, String message, String severity) {
        AlertRecord alert = new AlertRecord(taskName, type, message, severity);
        
        // 记录告警历史
        alertHistory.computeIfAbsent(taskName, k -> new ArrayList<>()).add(alert);
        
        // 发送通知
        sendNotification(alert);
        
        log.warn("触发告警: {} - {} - {}", taskName, type, message);
    }

    /**
     * 发送通知
     */
    private void sendNotification(AlertRecord alert) {
        // 这里可以实现各种通知方式：邮件、短信、钉钉、企业微信等
        // 目前只是记录日志
        log.warn("【告警通知】任务: {}, 类型: {}, 级别: {}, 消息: {}", 
                alert.getTaskName(), alert.getType(), alert.getSeverity(), alert.getMessage());
    }

    /**
     * 获取告警历史
     */
    public List<AlertRecord> getAlertHistory(String taskName, int limit) {
        List<AlertRecord> history = alertHistory.getOrDefault(taskName, new ArrayList<>());
        return history.stream()
                .sorted((a1, a2) -> a2.getAlertTime().compareTo(a1.getAlertTime()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有活跃告警
     */
    public List<AlertRecord> getActiveAlerts() {
        return alertHistory.values().stream()
                .flatMap(List::stream)
                .filter(alert -> !alert.isResolved())
                .sorted((a1, a2) -> a2.getAlertTime().compareTo(a1.getAlertTime()))
                .collect(Collectors.toList());
    }

    /**
     * 解决告警
     */
    public boolean resolveAlert(String taskName, AlertType type, LocalDateTime alertTime) {
        List<AlertRecord> taskAlerts = alertHistory.get(taskName);
        if (taskAlerts != null) {
            for (AlertRecord alert : taskAlerts) {
                if (alert.getType() == type && alert.getAlertTime().equals(alertTime)) {
                    alert.setResolved(true);
                    log.info("告警已解决: {} - {} - {}", taskName, type, alertTime);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取任务健康度评分
     */
    public Map<String, Object> getTaskHealthScore(String taskName) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(7); // 最近7天
            
            List<TaskExecutionRecord> records = taskExecutionRecordRepository
                    .findByTaskNameAndExecutionTimeBetween(taskName, startTime, endTime);
            
            if (records.isEmpty()) {
                return Map.of("score", 0, "level", "UNKNOWN", "message", "无执行记录");
            }

            // 计算各项指标
            long totalCount = records.size();
            long successCount = records.stream()
                    .filter(r -> r.getStatus() == TaskExecutionRecord.ExecutionStatus.SUCCESS)
                    .count();
            
            double successRate = (double) successCount / totalCount;
            
            // 计算平均执行时间
            OptionalDouble avgDuration = records.stream()
                    .filter(r -> r.getDurationMs() != null)
                    .mapToLong(TaskExecutionRecord::getDurationMs)
                    .average();
            
            // 检查最近的执行情况
            Optional<TaskExecutionRecord> lastExecution = records.stream()
                    .max(Comparator.comparing(TaskExecutionRecord::getExecutionTime));
            
            // 计算健康度评分 (0-100)
            double score = 0;
            
            // 成功率权重 60%
            score += successRate * 60;
            
            // 最近执行状态权重 25%
            if (lastExecution.isPresent() && 
                lastExecution.get().getStatus() == TaskExecutionRecord.ExecutionStatus.SUCCESS) {
                score += 25;
            }
            
            // 执行频率权重 15%
            long hoursSpan = java.time.Duration.between(startTime, endTime).toHours();
            double executionFrequency = (double) totalCount / hoursSpan;
            if (executionFrequency > 0.1) { // 每10小时至少执行一次
                score += 15;
            }
            
            // 确定健康等级
            String level;
            if (score >= 90) {
                level = "EXCELLENT";
            } else if (score >= 75) {
                level = "GOOD";
            } else if (score >= 60) {
                level = "FAIR";
            } else if (score >= 40) {
                level = "POOR";
            } else {
                level = "CRITICAL";
            }
            
            return Map.of(
                "score", Math.round(score),
                "level", level,
                "successRate", successRate,
                "totalExecutions", totalCount,
                "avgDurationMs", avgDuration.orElse(0.0),
                "lastExecutionStatus", lastExecution.map(r -> r.getStatus().name()).orElse("UNKNOWN")
            );
        } catch (Exception e) {
            log.error("计算任务健康度评分失败: {}", taskName, e);
            return Map.of("score", 0, "level", "ERROR", "message", "计算失败: " + e.getMessage());
        }
    }
}
