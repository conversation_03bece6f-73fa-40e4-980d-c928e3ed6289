package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.ZoomMeetingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 临时PMI会议清理服务
 * 定期清理超过10分钟的临时PMI会议，避免Zoom账号中积累过多无用会议
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TempMeetingCleanupService {

    private final ZoomMeetingRepository zoomMeetingRepository;
    private final ZoomApiService zoomApiService;
    private final AsyncScheduledTaskExecutor asyncTaskExecutor;

    /**
     * 定期清理临时PMI会议
     * 每10分钟执行一次，清理超过10分钟的临时PMI会议
     * 优化后：使用异步执行，独立事务管理，增强错误处理
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600,000毫秒
    public void cleanupTemporaryPmiMeetings() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "cleanupTemporaryPmiMeetings",
                "TEMP_MEETING_CLEANUP",
                this::doCleanupTemporaryPmiMeetings,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(1, 300000) // 最多重试1次，间隔5分钟
        );
    }

    /**
     * 执行临时PMI会议清理的具体逻辑
     */
    @Transactional
    private AsyncScheduledTaskExecutor.TaskExecutionResult doCleanupTemporaryPmiMeetings() {
        log.info("开始清理临时PMI会议");

        // 查找超过10分钟的临时PMI会议
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(10);
        List<ZoomMeeting> tempMeetings = zoomMeetingRepository.findTemporaryPmiMeetingsOlderThan(cutoffTime);

        if (tempMeetings.isEmpty()) {
            log.debug("没有需要清理的临时PMI会议");
            AsyncScheduledTaskExecutor.TaskExecutionResult result =
                    new AsyncScheduledTaskExecutor.TaskExecutionResult();
            result.setProcessedCount(0);
            result.setSuccessCount(0);
            result.setFailedCount(0);
            result.setResultData("没有需要清理的临时PMI会议");
            return result;
        }

        log.info("找到 {} 个需要清理的临时PMI会议", tempMeetings.size());

        int successCount = 0;
        int failureCount = 0;

        for (ZoomMeeting meeting : tempMeetings) {
            try {
                // 检查是否是临时PMI会议
                if (isTemporaryPmiMeeting(meeting)) {
                    // 删除Zoom平台上的会议
                    ZoomApiResponse<Void> deleteResponse = zoomApiService.deleteMeeting(
                            meeting.getZoomMeetingId(),
                            null // 不需要ZoomAuth，使用默认的
                    );

                    if (deleteResponse.isSuccess()) {
                        // 更新数据库中的会议状态
                        meeting.setStatus(ZoomMeeting.MeetingStatus.DELETED);
                        meeting.setUpdatedAt(LocalDateTime.now());
                        zoomMeetingRepository.save(meeting);

                        successCount++;
                        log.debug("成功清理临时PMI会议: meetingId={}, zoomMeetingId={}",
                                meeting.getId(), meeting.getZoomMeetingId());
                    } else {
                        failureCount++;
                        log.warn("删除临时PMI会议失败: meetingId={}, zoomMeetingId={}, error={}",
                                meeting.getId(), meeting.getZoomMeetingId(), deleteResponse.getMessage());
                    }
                } else {
                    log.debug("跳过非临时PMI会议: meetingId={}, topic={}",
                            meeting.getId(), meeting.getTopic());
                }
            } catch (Exception e) {
                failureCount++;
                log.error("清理临时PMI会议异常: meetingId={}, zoomMeetingId={}",
                        meeting.getId(), meeting.getZoomMeetingId(), e);
            }
        }

        log.info("临时PMI会议清理完成: 成功 {}, 失败 {}", successCount, failureCount);

        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
        result.setProcessedCount(tempMeetings.size());
        result.setSuccessCount(successCount);
        result.setFailedCount(failureCount);
        result.setResultData(String.format("清理完成: 处理=%d, 成功=%d, 失败=%d",
                tempMeetings.size(), successCount, failureCount));

        return result;
    }

    /**
     * 判断是否是临时PMI会议
     */
    private boolean isTemporaryPmiMeeting(ZoomMeeting meeting) {
        // 判断条件：
        // 1. 会议主题包含"PMI会议 -"且包含"(临时)"标识
        // 2. 会议状态为PENDING（未开始）
        // 3. 创建时间超过10分钟

        if (meeting.getTopic() == null) {
            return false;
        }

        boolean isTopicMatch = meeting.getTopic().startsWith("PMI会议 -") &&
                              meeting.getTopic().contains("(临时)");
        boolean isPending = meeting.getStatus() != null && meeting.getStatus().toString().equals("PENDING");
        boolean isOldEnough = meeting.getCreatedAt() != null &&
                             meeting.getCreatedAt().isBefore(LocalDateTime.now().minusMinutes(10));

        return isTopicMatch && isPending && isOldEnough;
    }

    /**
     * 手动触发清理（用于测试或紧急清理）
     */
    public void manualCleanup() {
        log.info("手动触发临时PMI会议清理");
        cleanupTemporaryPmiMeetings();
    }

    /**
     * 获取待清理的临时PMI会议数量
     */
    public long getTemporaryMeetingCount() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(10);
        List<ZoomMeeting> tempMeetings = zoomMeetingRepository.findTemporaryPmiMeetingsOlderThan(cutoffTime);
        return tempMeetings.stream()
                .filter(this::isTemporaryPmiMeeting)
                .count();
    }
}
