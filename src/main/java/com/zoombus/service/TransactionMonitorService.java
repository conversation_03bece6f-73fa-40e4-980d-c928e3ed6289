package com.zoombus.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 事务监控服务
 * 监控事务执行情况，记录异常和性能指标
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionMonitorService {

    // 统计指标
    private final AtomicLong totalTransactions = new AtomicLong(0);
    private final AtomicLong successfulTransactions = new AtomicLong(0);
    private final AtomicLong failedTransactions = new AtomicLong(0);
    private final AtomicLong rollbackTransactions = new AtomicLong(0);
    private final AtomicInteger activeTransactions = new AtomicInteger(0);

    /**
     * 监控事务开始
     */
    public void onTransactionBegin(String operationName) {
        totalTransactions.incrementAndGet();
        activeTransactions.incrementAndGet();
        
        log.debug("事务开始: operation={}, activeCount={}", 
                operationName, activeTransactions.get());
    }

    /**
     * 记录事务成功
     */
    public void recordTransactionSuccess(String operationName) {
        successfulTransactions.incrementAndGet();
        activeTransactions.decrementAndGet();

        log.debug("事务提交成功: operation={}, successCount={}, activeCount={}",
                operationName, successfulTransactions.get(), activeTransactions.get());
    }

    /**
     * 记录事务回滚
     */
    public void recordTransactionRollback(String operationName, Exception error) {
        rollbackTransactions.incrementAndGet();
        activeTransactions.decrementAndGet();

        log.error("事务回滚检测: operation={}, rollbackCount={}, activeCount={}, error={}",
                operationName, rollbackTransactions.get(), activeTransactions.get(), error.getMessage());

        // 发送告警（如果回滚率过高）
        checkRollbackRate();
    }

    /**
     * 记录事务失败
     */
    public void recordTransactionFailure(String operationName, Exception error) {
        failedTransactions.incrementAndGet();
        activeTransactions.decrementAndGet();

        log.error("事务失败: operation={}, failedCount={}, activeCount={}, error={}",
                operationName, failedTransactions.get(), activeTransactions.get(), error.getMessage());
    }

    /**
     * 检查回滚率并发送告警
     */
    private void checkRollbackRate() {
        long total = totalTransactions.get();
        long rollbacks = rollbackTransactions.get();
        
        if (total > 10) { // 至少有10个事务才开始检查
            double rollbackRate = (double) rollbacks / total * 100;
            
            if (rollbackRate > 20) { // 回滚率超过20%发送告警
                log.error("⚠️ 事务回滚率过高: {}% (回滚数: {}, 总数: {})", 
                        String.format("%.2f", rollbackRate), rollbacks, total);
                
                // 这里可以集成告警系统
                sendHighRollbackRateAlert(rollbackRate, rollbacks, total);
            }
        }
    }

    /**
     * 发送高回滚率告警
     */
    private void sendHighRollbackRateAlert(double rollbackRate, long rollbacks, long total) {
        // 可以集成邮件、短信、钉钉等告警系统
        log.error("🚨 系统告警: 事务回滚率异常 - 回滚率: {}%, 回滚数: {}, 总事务数: {}, 时间: {}", 
                String.format("%.2f", rollbackRate), rollbacks, total, LocalDateTime.now());
    }

    /**
     * 获取事务统计信息
     */
    public TransactionStatistics getStatistics() {
        return TransactionStatistics.builder()
                .totalTransactions(totalTransactions.get())
                .successfulTransactions(successfulTransactions.get())
                .failedTransactions(failedTransactions.get())
                .rollbackTransactions(rollbackTransactions.get())
                .activeTransactions(activeTransactions.get())
                .successRate(getSuccessRate())
                .rollbackRate(getRollbackRate())
                .lastUpdateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 计算成功率
     */
    private double getSuccessRate() {
        long total = totalTransactions.get();
        if (total == 0) return 0.0;
        return (double) successfulTransactions.get() / total * 100;
    }

    /**
     * 计算回滚率
     */
    private double getRollbackRate() {
        long total = totalTransactions.get();
        if (total == 0) return 0.0;
        return (double) rollbackTransactions.get() / total * 100;
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalTransactions.set(0);
        successfulTransactions.set(0);
        failedTransactions.set(0);
        rollbackTransactions.set(0);
        activeTransactions.set(0);
        
        log.info("事务统计信息已重置");
    }

    /**
     * 事务统计信息
     */
    @lombok.Builder
    @lombok.Data
    public static class TransactionStatistics {
        private long totalTransactions;
        private long successfulTransactions;
        private long failedTransactions;
        private long rollbackTransactions;
        private int activeTransactions;
        private double successRate;
        private double rollbackRate;
        private LocalDateTime lastUpdateTime;
    }
}
