package com.zoombus.service;

import com.zoombus.config.WebSocketProperties;
import com.zoombus.dto.MonitoringData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * WebSocket实时推送服务
 * 只有在websocket.enabled=true时才启用
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "websocket.enabled", havingValue = "true")
@RequiredArgsConstructor
public class WebSocketService {

    private final SimpMessagingTemplate messagingTemplate;
    private final WebSocketProperties webSocketProperties;
    
    /**
     * 推送Zoom账号状态更新
     */
    public void pushZoomAccountStatus(MonitoringData.ZoomAccountStatus status) {
        if (!webSocketProperties.getMonitoring().isEnabled()) {
            log.trace("WebSocket监控推送已禁用，跳过Zoom账号状态推送");
            return;
        }

        try {
            MonitoringData data = MonitoringData.builder()
                    .type("ZOOM_ACCOUNT_STATUS")
                    .timestamp(LocalDateTime.now())
                    .data(status)
                    .build();

            messagingTemplate.convertAndSend("/topic/monitoring/zoom-account", data);
            log.debug("推送Zoom账号状态更新: {}", status.getAccountName());
        } catch (Exception e) {
            log.error("推送Zoom账号状态失败", e);
        }
    }
    
    /**
     * 推送会议状态更新
     */
    public void pushMeetingStatus(MonitoringData.MeetingStatus status) {
        if (!webSocketProperties.getMonitoring().isEnabled()) {
            log.trace("WebSocket监控推送已禁用，跳过会议状态推送");
            return;
        }

        try {
            MonitoringData data = MonitoringData.builder()
                    .type("MEETING_STATUS")
                    .timestamp(LocalDateTime.now())
                    .data(status)
                    .build();

            messagingTemplate.convertAndSend("/topic/monitoring/meeting", data);
            log.debug("推送会议状态更新: 总计{}个会议", status.getTotalMeetings());
        } catch (Exception e) {
            log.error("推送会议状态失败", e);
        }
    }

    /**
     * 推送PMI状态更新
     */
    public void pushPmiStatus(MonitoringData.PmiStatus status) {
        if (!webSocketProperties.getMonitoring().isEnabled()) {
            log.trace("WebSocket监控推送已禁用，跳过PMI状态推送");
            return;
        }

        try {
            MonitoringData data = MonitoringData.builder()
                    .type("PMI_STATUS")
                    .timestamp(LocalDateTime.now())
                    .data(status)
                    .build();

            messagingTemplate.convertAndSend("/topic/monitoring/pmi", data);
            log.debug("推送PMI状态更新: 使用率{}%", status.getUsageRate());
        } catch (Exception e) {
            log.error("推送PMI状态失败", e);
        }
    }
    
    /**
     * 推送系统性能更新
     */
    public void pushSystemPerformance(MonitoringData.SystemPerformance performance) {
        if (!webSocketProperties.getMonitoring().isEnabled()) {
            log.trace("WebSocket监控推送已禁用，跳过系统性能推送");
            return;
        }

        try {
            MonitoringData data = MonitoringData.builder()
                    .type("SYSTEM_PERFORMANCE")
                    .timestamp(LocalDateTime.now())
                    .data(performance)
                    .build();

            messagingTemplate.convertAndSend("/topic/monitoring/system", data);
            log.debug("推送系统性能更新: CPU{}%, 内存{}%",
                    performance.getCpuUsage(), performance.getMemoryUsage());
        } catch (Exception e) {
            log.error("推送系统性能数据失败", e);
        }
    }
    
    /**
     * 推送告警信息
     */
    public void pushAlert(MonitoringData.AlertInfo alert) {
        if (!webSocketProperties.getMonitoring().isEnabled()) {
            log.trace("WebSocket监控推送已禁用，跳过告警信息推送");
            return;
        }

        try {
            MonitoringData data = MonitoringData.builder()
                    .type("ALERT")
                    .timestamp(LocalDateTime.now())
                    .data(alert)
                    .build();

            messagingTemplate.convertAndSend("/topic/monitoring/alert", data);
            log.info("推送告警信息: {} - {}", alert.getLevel(), alert.getTitle());
        } catch (Exception e) {
            log.error("推送告警信息失败", e);
        }
    }

    /**
     * 推送API调用统计
     */
    public void pushApiStats(MonitoringData.ApiCallStats stats) {
        if (!webSocketProperties.getMonitoring().isEnabled()) {
            log.trace("WebSocket监控推送已禁用，跳过API统计推送");
            return;
        }

        try {
            MonitoringData data = MonitoringData.builder()
                    .type("API_STATS")
                    .timestamp(LocalDateTime.now())
                    .data(stats)
                    .build();

            messagingTemplate.convertAndSend("/topic/monitoring/api", data);
            log.debug("推送API统计更新: 成功率{}%", stats.getSuccessRate());
        } catch (Exception e) {
            log.error("推送API统计失败", e);
        }
    }

    /**
     * 推送综合监控数据
     */
    public void pushComprehensiveData(Object data) {
        if (!webSocketProperties.getMonitoring().isEnabled()) {
            log.trace("WebSocket监控推送已禁用，跳过综合监控数据推送");
            return;
        }

        try {
            MonitoringData monitoringData = MonitoringData.builder()
                    .type("COMPREHENSIVE")
                    .timestamp(LocalDateTime.now())
                    .data(data)
                    .build();

            messagingTemplate.convertAndSend("/topic/monitoring/comprehensive", monitoringData);
            log.debug("推送综合监控数据更新");
        } catch (Exception e) {
            log.error("推送综合监控数据失败", e);
        }
    }
}
