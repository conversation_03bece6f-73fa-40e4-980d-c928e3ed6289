package com.zoombus.service;

import com.zoombus.dto.MonitoringData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * WebSocket服务的空实现
 * 当WebSocket功能禁用时使用，避免其他服务调用WebSocket时出错
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "websocket.enabled", havingValue = "false", matchIfMissing = true)
public class WebSocketServiceStub {
    
    /**
     * 推送Zoom账号状态更新（空实现）
     */
    public void pushZoomAccountStatus(MonitoringData.ZoomAccountStatus status) {
        log.trace("WebSocket功能已禁用，跳过Zoom账号状态推送");
    }
    
    /**
     * 推送会议状态更新（空实现）
     */
    public void pushMeetingStatus(MonitoringData.MeetingStatus status) {
        log.trace("WebSocket功能已禁用，跳过会议状态推送");
    }
    
    /**
     * 推送PMI状态更新（空实现）
     */
    public void pushPmiStatus(MonitoringData.PmiStatus status) {
        log.trace("WebSocket功能已禁用，跳过PMI状态推送");
    }
    
    /**
     * 推送系统性能更新（空实现）
     */
    public void pushSystemPerformance(MonitoringData.SystemPerformance performance) {
        log.trace("WebSocket功能已禁用，跳过系统性能推送");
    }
    
    /**
     * 推送告警信息（空实现）
     */
    public void pushAlert(MonitoringData.AlertInfo alert) {
        log.trace("WebSocket功能已禁用，跳过告警信息推送");
    }
    
    /**
     * 推送API调用统计（空实现）
     */
    public void pushApiStats(MonitoringData.ApiCallStats stats) {
        log.trace("WebSocket功能已禁用，跳过API统计推送");
    }
    
    /**
     * 推送综合监控数据（空实现）
     */
    public void pushComprehensiveData(Object data) {
        log.trace("WebSocket功能已禁用，跳过综合监控数据推送");
    }
}
