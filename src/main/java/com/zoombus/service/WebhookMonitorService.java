package com.zoombus.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Webhook监控服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WebhookMonitorService {
    
    private final AtomicLong totalWebhookCount = new AtomicLong(0);
    private final AtomicLong successfulWebhookCount = new AtomicLong(0);
    private final AtomicLong failedWebhookCount = new AtomicLong(0);
    
    private final Map<String, AtomicLong> eventTypeCounters = new ConcurrentHashMap<>();
    private final Map<String, LocalDateTime> lastEventTimes = new ConcurrentHashMap<>();
    
    /**
     * 记录Webhook事件
     */
    public void recordWebhookEvent(String eventType, boolean success) {
        totalWebhookCount.incrementAndGet();
        
        if (success) {
            successfulWebhookCount.incrementAndGet();
        } else {
            failedWebhookCount.incrementAndGet();
        }
        
        // 记录事件类型统计
        eventTypeCounters.computeIfAbsent(eventType, k -> new AtomicLong(0)).incrementAndGet();
        lastEventTimes.put(eventType, LocalDateTime.now());
        
        log.debug("记录Webhook事件: type={}, success={}, total={}", 
                eventType, success, totalWebhookCount.get());
    }
    
    /**
     * 获取Webhook统计信息
     */
    public WebhookStatistics getWebhookStatistics() {
        return new WebhookStatistics(
            totalWebhookCount.get(),
            successfulWebhookCount.get(),
            failedWebhookCount.get(),
            new ConcurrentHashMap<>(eventTypeCounters),
            new ConcurrentHashMap<>(lastEventTimes)
        );
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalWebhookCount.set(0);
        successfulWebhookCount.set(0);
        failedWebhookCount.set(0);
        eventTypeCounters.clear();
        lastEventTimes.clear();
        
        log.info("Webhook统计信息已重置");
    }
    
    /**
     * Webhook统计信息类
     */
    public static class WebhookStatistics {
        private final long totalCount;
        private final long successCount;
        private final long failureCount;
        private final Map<String, AtomicLong> eventTypeCounts;
        private final Map<String, LocalDateTime> lastEventTimes;
        
        public WebhookStatistics(long totalCount, long successCount, long failureCount,
                               Map<String, AtomicLong> eventTypeCounts,
                               Map<String, LocalDateTime> lastEventTimes) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.eventTypeCounts = eventTypeCounts;
            this.lastEventTimes = lastEventTimes;
        }
        
        public long getTotalCount() {
            return totalCount;
        }
        
        public long getSuccessCount() {
            return successCount;
        }
        
        public long getFailureCount() {
            return failureCount;
        }
        
        public double getSuccessRate() {
            return totalCount > 0 ? (double) successCount / totalCount * 100 : 0.0;
        }
        
        public Map<String, Long> getEventTypeCounts() {
            Map<String, Long> result = new ConcurrentHashMap<>();
            eventTypeCounts.forEach((key, value) -> result.put(key, value.get()));
            return result;
        }
        
        public Map<String, LocalDateTime> getLastEventTimes() {
            return new ConcurrentHashMap<>(lastEventTimes);
        }
        
        public String getSummary() {
            return String.format("总计: %d, 成功: %d, 失败: %d, 成功率: %.2f%%",
                    totalCount, successCount, failureCount, getSuccessRate());
        }
        
        public boolean isHealthy() {
            return getSuccessRate() >= 95.0; // 成功率95%以上认为健康
        }
    }
}
