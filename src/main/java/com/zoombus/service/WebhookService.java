package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.entity.Meeting;
import com.zoombus.entity.WebhookEvent;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.entity.ZoomUser;

import com.zoombus.repository.WebhookEventRepository;
import com.zoombus.repository.MeetingRepository;
import com.zoombus.repository.ZoomUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class WebhookService {

    // Zoom 会议类型常量
    private static final int ZOOM_INSTANT_MEETING = 1;
    private static final int ZOOM_SCHEDULED_MEETING = 2;
    private static final int ZOOM_RECURRING_NO_FIXED_TIME = 3;
    private static final int ZOOM_RECURRING_FIXED_TIME = 8;

    private final WebhookEventRepository webhookEventRepository;
    private final MeetingRepository meetingRepository;
    private final ZoomUserRepository zoomUserRepository;
    private final MeetingService meetingService;
    private final ZoomAuthService zoomAuthService;
    private final ZoomUserService zoomUserService;
    private final ZoomMeetingDetailService zoomMeetingDetailService;
    private final ZoomApiService zoomApiService;
    
    /**
     * 处理Zoom Webhook事件（改进版本 - 确保事件记录不会因业务事务回滚而丢失）
     */
    public void processWebhookEvent(String eventType, JsonNode eventData) {
        // 第一步：立即保存原始事件数据（独立事务）
        WebhookEvent webhookEvent = saveWebhookEventRecord(eventType, eventData);

        // 第二步：处理业务逻辑
        processWebhookEventBusiness(webhookEvent, eventType, eventData);
    }

    /**
     * 立即保存Webhook事件记录（独立事务，确保不会因业务失败而回滚）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public WebhookEvent saveWebhookEventRecord(String eventType, JsonNode eventData) {
        try {
            WebhookEvent webhookEvent = new WebhookEvent();
            webhookEvent.setEventType(eventType);
            webhookEvent.setEventData(eventData.toString());
            webhookEvent.setProcessingStatus(WebhookEvent.ProcessingStatus.PENDING);

            WebhookEvent savedEvent = webhookEventRepository.save(webhookEvent);
            log.debug("Webhook事件记录已保存: eventType={}, eventId={}", eventType, savedEvent.getId());
            return savedEvent;
        } catch (Exception e) {
            log.error("保存Webhook事件记录失败: eventType={}", eventType, e);
            // 创建一个临时对象，避免后续处理出错
            WebhookEvent tempEvent = new WebhookEvent();
            tempEvent.setEventType(eventType);
            tempEvent.setEventData(eventData.toString());
            tempEvent.setProcessingStatus(WebhookEvent.ProcessingStatus.FAILED);
            tempEvent.setErrorMessage("保存事件记录失败: " + e.getMessage());
            return tempEvent;
        }
    }

    /**
     * 处理Webhook事件的业务逻辑
     */
    private void processWebhookEventBusiness(WebhookEvent webhookEvent, String eventType, JsonNode eventData) {

        try {
            // 根据事件类型处理
            switch (eventType) {
                case "endpoint.url_validation":
                    handleEndpointUrlValidationLegacy(eventData, webhookEvent);
                    break;
                case "meeting.created":
                    handleMeetingCreated(eventData, webhookEvent);
                    break;
                case "meeting.updated":
                    handleMeetingUpdated(eventData, webhookEvent);
                    break;
                case "meeting.deleted":
                    handleMeetingDeleted(eventData, webhookEvent);
                    break;
                case "meeting.started":
                    handleMeetingStarted(eventData, webhookEvent);
                    break;
                case "meeting.ended":
                    handleMeetingEnded(eventData, webhookEvent);
                    break;
                case "user.created":
                    handleUserCreated(eventData, webhookEvent);
                    break;
                case "user.updated":
                    handleUserUpdated(eventData, webhookEvent);
                    break;
                case "user.deleted":
                    handleUserDeleted(eventData, webhookEvent);
                    break;
                default:
                    log.info("未处理的事件类型: {}", eventType);
                    updateWebhookEventStatus(webhookEvent.getId(), WebhookEvent.ProcessingStatus.IGNORED, null);
                    return; // 直接返回，不需要更新状态
            }

            // 业务处理成功，更新状态为已处理
            updateWebhookEventStatus(webhookEvent.getId(), WebhookEvent.ProcessingStatus.PROCESSED, null);

        } catch (Exception e) {
            log.error("处理Webhook事件失败: eventType={}, eventId={}", eventType, webhookEvent.getId(), e);
            // 业务处理失败，更新状态为失败
            updateWebhookEventStatus(webhookEvent.getId(), WebhookEvent.ProcessingStatus.FAILED, e.getMessage());
        }
    }

    /**
     * 更新Webhook事件状态（独立事务，确保状态更新不会因其他事务回滚而丢失）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateWebhookEventStatus(Long eventId, WebhookEvent.ProcessingStatus status, String errorMessage) {
        try {
            if (eventId == null) {
                log.warn("事件ID为空，无法更新状态");
                return;
            }

            Optional<WebhookEvent> eventOpt = webhookEventRepository.findById(eventId);
            if (eventOpt.isPresent()) {
                WebhookEvent event = eventOpt.get();
                event.setProcessingStatus(status);
                if (status == WebhookEvent.ProcessingStatus.PROCESSED) {
                    event.setProcessedAt(LocalDateTime.now());
                }
                if (errorMessage != null) {
                    event.setErrorMessage(errorMessage);
                }
                webhookEventRepository.save(event);
                log.debug("Webhook事件状态已更新: eventId={}, status={}", eventId, status);
            } else {
                log.warn("未找到Webhook事件记录: eventId={}", eventId);
            }
        } catch (Exception e) {
            log.error("更新Webhook事件状态失败: eventId={}, status={}", eventId, status, e);
            // 状态更新失败不抛出异常，避免影响主流程
        }
    }
    
    /**
     * 处理会议创建事件
     */
    private void handleMeetingCreated(JsonNode eventData, WebhookEvent webhookEvent) {
        // 使用默认账号处理（向后兼容）
        Optional<ZoomAuth> defaultAuthOpt = zoomAuthService.getDefaultZoomAuth();
        if (!defaultAuthOpt.isPresent()) {
            log.error("未找到默认认证信息，无法处理会议创建事件");
            return;
        }
        handleMeetingCreatedWithAuth(eventData, webhookEvent, defaultAuthOpt.get());
    }

    /**
     * 处理会议创建事件（带认证信息）
     */
    private void handleMeetingCreatedWithAuth(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        try {
            JsonNode payload = eventData.get("payload");
            if (payload == null) {
                log.warn("Webhook事件缺少payload数据 [账号: {}]", zoomAuth.getAccountName());
                return;
            }

            JsonNode object = payload.get("object");
            if (object == null) {
                log.warn("Webhook事件缺少object数据 [账号: {}]", zoomAuth.getAccountName());
                return;
            }

            String meetingId = object.get("id").asText();
            String hostId = object.has("host_id") ? object.get("host_id").asText() : null;

            log.info("处理会议创建事件 [账号: {}, 会议ID: {}, 主持人ID: {}]",
                    zoomAuth.getAccountName(), meetingId, hostId);

            webhookEvent.setZoomMeetingId(meetingId);

            // 检查会议是否已存在
            if (meetingService.getMeetingByZoomMeetingId(meetingId).isPresent()) {
                log.info("会议已存在，跳过创建 [账号: {}, 会议ID: {}]", zoomAuth.getAccountName(), meetingId);
                return;
            }

            // 查找主持人用户
            ZoomUser hostUser = null;
            if (hostId != null) {
                Optional<ZoomUser> hostUserOpt = zoomUserService.getZoomUserByZoomUserId(zoomAuth.getId(), hostId);
                if (hostUserOpt.isPresent()) {
                    hostUser = hostUserOpt.get();
                    log.debug("找到主持人用户: {} [{}]", hostUser.getEmail(), hostId);
                } else {
                    log.warn("未找到主持人用户 [账号: {}, 主持人ID: {}]", zoomAuth.getAccountName(), hostId);
                }
            }

            // 创建会议记录
            Meeting meeting = createMeetingFromWebhookData(object, zoomAuth, hostUser);

            // 直接保存会议实体
            Meeting savedMeeting = meetingRepository.save(meeting);
            log.info("通过Webhook创建会议成功 [账号: {}, 会议: {}, ID: {}]",
                    zoomAuth.getAccountName(), savedMeeting.getTopic(), savedMeeting.getId());

            // 注意：meeting.created事件只记录会议安排，不创建ZoomMeeting记录
            // ZoomMeeting记录将在meeting.started事件时创建，确保只记录实际召开的会议
            log.info("📝 会议已安排但未开始，等待meeting.started事件创建ZoomMeeting记录: meetingId={}",
                    object.get("id").asText());

            // 查询并保存会议详情
            queryAndSaveMeetingDetails(savedMeeting, zoomAuth);

        } catch (Exception e) {
            log.error("处理会议创建事件失败 [账号: {}]", zoomAuth.getAccountName(), e);
            throw e;
        }
    }
    
    /**
     * 处理会议更新事件
     */
    private void handleMeetingUpdated(JsonNode eventData, WebhookEvent webhookEvent) {
        String meetingId = extractMeetingIdFromWebhook(eventData);
        if (meetingId == null) {
            log.warn("无法从Webhook数据中提取会议ID");
            return;
        }

        webhookEvent.setZoomMeetingId(meetingId);

        try {
            // 使用增强的Webhook同步方法
            Meeting updatedMeeting = meetingService.syncMeetingInfoFromWebhook(meetingId, eventData);

            if (updatedMeeting != null) {
                log.info("通过Webhook更新会议成功: meetingId={}, topic={}",
                    meetingId, updatedMeeting.getTopic());
            } else {
                log.warn("未找到对应的会议记录: {}", meetingId);
            }

        } catch (Exception e) {
            log.error("处理会议更新Webhook事件失败: meetingId={}", meetingId, e);
            throw e; // 重新抛出异常以标记处理失败
        }
    }
    
    /**
     * 处理会议删除事件
     */
    private void handleMeetingDeleted(JsonNode eventData, WebhookEvent webhookEvent) {
        String meetingId = extractMeetingIdFromWebhook(eventData);
        if (meetingId == null) {
            return;
        }

        webhookEvent.setZoomMeetingId(meetingId);

        meetingService.getMeetingByZoomMeetingId(meetingId)
                .ifPresent(meeting -> {
                    meetingService.updateMeetingStatus(meeting.getId(), Meeting.MeetingStatus.CANCELLED);
                    log.info("通过Webhook取消会议成功: {}", meetingId);
                });
    }
    
    /**
     * 处理会议开始事件
     */
    private void handleMeetingStarted(JsonNode eventData, WebhookEvent webhookEvent) {
        updateMeetingStatusFromWebhook(eventData, webhookEvent, Meeting.MeetingStatus.STARTED, "开始");
    }

    /**
     * 处理会议结束事件
     */
    private void handleMeetingEnded(JsonNode eventData, WebhookEvent webhookEvent) {
        updateMeetingStatusFromWebhook(eventData, webhookEvent, Meeting.MeetingStatus.ENDED, "结束");
    }
    
    /**
     * 处理用户创建事件
     */
    private void handleUserCreated(JsonNode eventData, WebhookEvent webhookEvent) {
        // 这里可以处理用户创建事件，比如同步用户信息
        log.info("收到用户创建事件");
    }
    
    /**
     * 处理用户更新事件
     */
    private void handleUserUpdated(JsonNode eventData, WebhookEvent webhookEvent) {
        try {
            // 提取用户信息
            JsonNode payload = eventData.get("payload");
            if (payload == null) {
                log.warn("用户更新事件缺少payload数据");
                return;
            }

            JsonNode object = payload.get("object");
            JsonNode oldObject = payload.get("old_object");

            if (object == null) {
                log.warn("用户更新事件缺少object数据");
                return;
            }

            String zoomUserId = object.get("id").asText();
            String email = object.has("email") ? object.get("email").asText() : null;

            log.info("收到用户更新事件: userId={}, email={}", zoomUserId, email);

            // 检查是否有PMI变化
            boolean pmiChanged = false;
            String newPmi = null;
            String oldPmi = null;

            if (object.has("pmi") && !object.get("pmi").isNull()) {
                newPmi = object.get("pmi").asText();
            }

            if (oldObject != null && oldObject.has("pmi") && !oldObject.get("pmi").isNull()) {
                oldPmi = oldObject.get("pmi").asText();
            }

            // 判断PMI是否发生变化
            if (newPmi != null && oldPmi != null && !newPmi.equals(oldPmi)) {
                pmiChanged = true;
                log.info("检测到用户PMI变化: userId={}, oldPmi={}, newPmi={}", zoomUserId, oldPmi, newPmi);
            } else if (newPmi != null && oldPmi == null) {
                pmiChanged = true;
                log.info("检测到用户新增PMI: userId={}, newPmi={}", zoomUserId, newPmi);
            }

            // 如果PMI发生变化，同步更新本地数据
            if (pmiChanged && newPmi != null) {
                syncUserPmiFromWebhook(zoomUserId, newPmi, object);
            } else {
                log.debug("用户更新事件未涉及PMI变化，跳过PMI同步: userId={}", zoomUserId);
            }

        } catch (Exception e) {
            log.error("处理用户更新事件失败", e);
            throw e;
        }
    }

    /**
     * 从Webhook事件同步用户PMI
     */
    private void syncUserPmiFromWebhook(String zoomUserId, String newPmi, JsonNode userObject) {
        try {
            // 查找对应的ZoomUser（可能有多个，取第一个）
            List<ZoomUser> zoomUsers = zoomUserRepository.findByZoomUserId(zoomUserId);

            if (!zoomUsers.isEmpty()) {
                // 如果有多个，优先处理第一个，并记录日志
                if (zoomUsers.size() > 1) {
                    log.info("找到多个ZoomUser记录: zoomUserId={}, count={}, 将处理第一个",
                        zoomUserId, zoomUsers.size());
                }

                ZoomUser zoomUser = zoomUsers.get(0);
                String currentPmi = zoomUser.getCurrentPmi();

                // 保护original_pmi，只更新current_pmi
                String originalPmi = zoomUser.getOriginalPmi();
                if (originalPmi == null || originalPmi.trim().isEmpty()) {
                    // 如果是新用户，设置original_pmi
                    zoomUser.setOriginalPmi(newPmi);
                    log.info("为用户 {} 设置原始PMI: {}", zoomUser.getEmail(), newPmi);
                }

                // 始终更新current_pmi以与Zoom端保持一致
                if (!newPmi.equals(currentPmi)) {
                    zoomUser.setCurrentPmi(newPmi);
                    zoomUser.setPmiUpdatedAt(LocalDateTime.now());

                    // 保存更新
                    zoomUserRepository.save(zoomUser);

                    log.info("通过Webhook同步用户PMI: userId={}, email={}, oldPmi={}, newPmi={}",
                        zoomUserId, zoomUser.getEmail(), currentPmi, newPmi);
                } else {
                    log.debug("用户PMI无变化，跳过更新: userId={}, pmi={}", zoomUserId, currentPmi);
                }
            } else {
                log.warn("未找到对应的ZoomUser记录，无法同步PMI: zoomUserId={}", zoomUserId);

                // 可以考虑创建新用户记录，但这里先记录警告
                // 因为通常用户应该已经通过其他方式同步到本地数据库
            }

        } catch (Exception e) {
            log.error("从Webhook同步用户PMI失败: zoomUserId={}, newPmi={}", zoomUserId, newPmi, e);
        }
    }
    
    /**
     * 处理用户删除事件
     */
    private void handleUserDeleted(JsonNode eventData, WebhookEvent webhookEvent) {
        // 这里可以处理用户删除事件
        log.info("收到用户删除事件");
    }
    
    /**
     * 转换Zoom会议类型
     */
    private Meeting.MeetingType getMeetingTypeFromZoomType(int zoomType) {
        switch (zoomType) {
            case ZOOM_INSTANT_MEETING:
                return Meeting.MeetingType.INSTANT;
            case ZOOM_SCHEDULED_MEETING:
                return Meeting.MeetingType.SCHEDULED;
            case ZOOM_RECURRING_NO_FIXED_TIME:
                return Meeting.MeetingType.RECURRING_NO_FIXED_TIME;
            case ZOOM_RECURRING_FIXED_TIME:
                return Meeting.MeetingType.RECURRING_FIXED_TIME;
            default:
                return Meeting.MeetingType.SCHEDULED;
        }
    }

    /**
     * 从Webhook数据中提取会议ID
     */
    private String extractMeetingIdFromWebhook(JsonNode eventData) {
        JsonNode payload = eventData.get("payload");
        if (payload == null) {
            log.warn("Webhook事件缺少payload数据");
            return null;
        }

        JsonNode object = payload.get("object");
        if (object == null) {
            log.warn("Webhook事件缺少object数据");
            return null;
        }

        return object.get("id").asText();
    }

    /**
     * 判断是否为PMI会议
     */
    private boolean isPmiMeeting(JsonNode object) {
        // PMI会议的特征：
        // 1. 会议ID通常是固定的个人会议室号码
        // 2. 会议类型可能是4（虽然文档中没有明确说明）
        // 3. 会议ID长度通常较短（9-11位）
        if (object.has("id")) {
            String meetingId = object.get("id").asText();
            // PMI会议ID通常是9-11位数字
            if (meetingId.matches("\\d{9,11}")) {
                return true;
            }
        }

        // 检查会议类型是否为4（PMI类型）
        if (object.has("type")) {
            int type = object.get("type").asInt();
            if (type == 4) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从Webhook数据创建会议对象
     */
    private Meeting createMeetingFromWebhookData(JsonNode object, ZoomAuth zoomAuth, ZoomUser hostUser) {
        Meeting meeting = new Meeting();

        // 基本信息
        String meetingId = object.get("id").asText();
        meeting.setZoomMeetingId(meetingId);
        meeting.setTopic(object.has("topic") ? object.get("topic").asText() : "未命名会议");
        meeting.setType(getMeetingTypeFromZoomType(object.has("type") ? object.get("type").asInt() : 2));
        meeting.setStatus(Meeting.MeetingStatus.SCHEDULED);
        // 优先从webhook数据中获取creation_source字段
        if (object.has("creation_source")) {
            String creationSourceValue = object.get("creation_source").asText();
            meeting.setCreationSource(Meeting.CreationSource.fromApiValue(creationSourceValue));
            log.debug("从webhook数据获取创建来源: {}", creationSourceValue);
        } else {
            // 如果没有creation_source字段，根据会议类型和是否为PMI会议推断创建来源
            int zoomType = object.has("type") ? object.get("type").asInt() : 2;
            boolean isPmi = isPmiMeeting(object);
            meeting.setCreationSource(Meeting.CreationSource.fromZoomMeetingType(zoomType, isPmi));
            log.debug("根据会议类型推断创建来源: type={}, isPmi={}, result={}",
                     zoomType, isPmi, meeting.getCreationSource());
        }

        // 用户关联
        if (hostUser != null) {
            meeting.setZoomUserId(hostUser.getZoomUserId());
            if (hostUser.getUser() != null) {
                meeting.setCreatorUserId(hostUser.getUser().getId());
            }
        } else if (object.has("host_id")) {
            meeting.setZoomUserId(object.get("host_id").asText());
        }

        // 设置详细字段
        setDetailedMeetingFields(meeting, object, zoomAuth);

        return meeting;
    }

    /**
     * 从Webhook数据创建会议对象（兼容旧版本）
     */
    private Meeting createMeetingFromWebhookData(String hostId, String meetingId, JsonNode object) {
        // 为了向后兼容，创建一个临时的ZoomAuth对象
        Optional<ZoomAuth> defaultAuthOpt = zoomAuthService.getDefaultZoomAuth();
        ZoomAuth zoomAuth = defaultAuthOpt.orElse(new ZoomAuth());
        if (zoomAuth.getZoomAccountId() == null) {
            zoomAuth.setZoomAccountId("default");
        }

        return createMeetingFromWebhookData(object, zoomAuth, null);
    }

    /**
     * 设置会议的详细字段（新版本）
     */
    private void setDetailedMeetingFields(Meeting meeting, JsonNode object, ZoomAuth zoomAuth) {
        // 基本信息
        if (object.has("agenda")) {
            meeting.setAgenda(object.get("agenda").asText());
        }
        if (object.has("duration")) {
            meeting.setDurationMinutes(object.get("duration").asInt());
        }
        if (object.has("start_time")) {
            parseAndSetStartTime(meeting, object.get("start_time").asText());
        }
        if (object.has("timezone")) {
            meeting.setTimezone(object.get("timezone").asText());
        }
        if (object.has("join_url")) {
            meeting.setJoinUrl(object.get("join_url").asText());
        }
        if (object.has("start_url")) {
            meeting.setStartUrl(object.get("start_url").asText());
        }
        if (object.has("password")) {
            meeting.setPassword(object.get("password").asText());
        }

        // 会议设置
        if (object.has("settings")) {
            JsonNode settings = object.get("settings");
            parseMeetingSettings(meeting, settings);
        }

        // 周期性会议信息
        if (object.has("recurrence")) {
            JsonNode recurrence = object.get("recurrence");
            parseRecurrenceSettings(meeting, recurrence);
        }

        log.debug("设置会议详细字段完成 [账号: {}, 会议ID: {}]",
                zoomAuth.getAccountName(), meeting.getZoomMeetingId());
    }

    /**
     * 解析会议设置
     */
    private void parseMeetingSettings(Meeting meeting, JsonNode settings) {
        try {
            // 这里可以根据需要解析更多的会议设置
            // 目前Meeting实体可能没有对应的字段，所以先记录日志
            log.debug("解析会议设置: {}", settings.toString());

            // 如果有需要的设置字段，可以在这里解析
            // 例如：waiting_room, join_before_host, mute_upon_entry等

        } catch (Exception e) {
            log.warn("解析会议设置失败: {}", e.getMessage());
        }
    }

    /**
     * 解析周期性会议设置
     */
    private void parseRecurrenceSettings(Meeting meeting, JsonNode recurrence) {
        try {
            // 这里可以根据需要解析周期性会议信息
            // 目前Meeting实体可能没有对应的字段，所以先记录日志
            log.debug("解析周期性会议设置: {}", recurrence.toString());

            // 如果有需要的周期性字段，可以在这里解析
            // 例如：type, repeat_interval, end_date_time等

        } catch (Exception e) {
            log.warn("解析周期性会议设置失败: {}", e.getMessage());
        }
    }

    /**
     * 设置会议的可选字段（兼容旧版本）
     */
    private void setOptionalMeetingFields(Meeting meeting, JsonNode object) {
        if (object.has("agenda")) {
            meeting.setAgenda(object.get("agenda").asText());
        }
        if (object.has("duration")) {
            meeting.setDurationMinutes(object.get("duration").asInt());
        }
        if (object.has("start_time")) {
            parseAndSetStartTime(meeting, object.get("start_time").asText());
        }
        if (object.has("timezone")) {
            meeting.setTimezone(object.get("timezone").asText());
        }
        if (object.has("join_url")) {
            meeting.setJoinUrl(object.get("join_url").asText());
        }
        if (object.has("password")) {
            meeting.setPassword(object.get("password").asText());
        }
    }

    /**
     * 解析并设置会议开始时间
     */
    private void parseAndSetStartTime(Meeting meeting, String startTimeStr) {
        try {
            meeting.setStartTime(LocalDateTime.parse(startTimeStr, DateTimeFormatter.ISO_DATE_TIME));
        } catch (Exception e) {
            log.warn("解析会议开始时间失败: {}", startTimeStr, e);
        }
    }

    /**
     * 通用的会议状态更新方法
     */
    private void updateMeetingStatusFromWebhook(JsonNode eventData, WebhookEvent webhookEvent,
                                               Meeting.MeetingStatus status, String statusDescription) {
        String meetingId = extractMeetingIdFromWebhook(eventData);
        if (meetingId == null) {
            return;
        }

        webhookEvent.setZoomMeetingId(meetingId);

        meetingService.getMeetingByZoomMeetingId(meetingId)
                .ifPresent(meeting -> {
                    meetingService.updateMeetingStatus(meeting.getId(), status);
                    log.info("通过Webhook标记会议{}: {}", statusDescription, meetingId);
                });
    }
    
    /**
     * 获取所有Webhook事件，按接收时间倒序排列（最新的在前）
     */
    @Transactional(readOnly = true)
    public List<WebhookEvent> getAllWebhookEvents() {
        return webhookEventRepository.findAllByOrderByCreatedAtDesc();
    }

    /**
     * 根据处理状态获取事件，按接收时间倒序排列（最新的在前）
     */
    @Transactional(readOnly = true)
    public List<WebhookEvent> getWebhookEventsByStatus(WebhookEvent.ProcessingStatus status) {
        return webhookEventRepository.findByProcessingStatusOrderByCreatedAtDesc(status);
    }

    /**
     * 根据账号ID获取事件，按接收时间倒序排列（最新的在前）
     */
    @Transactional(readOnly = true)
    public List<WebhookEvent> getWebhookEventsByAccount(String accountId) {
        return webhookEventRepository.findByZoomAccountIdOrderByCreatedAtDesc(accountId);
    }

    /**
     * 根据ID获取单个Webhook事件
     */
    @Transactional(readOnly = true)
    public WebhookEvent getWebhookEventById(Long eventId) {
        return webhookEventRepository.findById(eventId).orElse(null);
    }



    /**
     * 获取Webhook统计信息
     */
    @Transactional(readOnly = true)
    public Object getWebhookStats() {
        // 这里可以返回各种统计信息
        return Map.of(
            "totalEvents", webhookEventRepository.count(),
            "eventsByStatus", getEventCountByStatus(),
            "eventsByAccount", getEventCountByAccount(),
            "recentEvents", webhookEventRepository.findTop10ByOrderByCreatedAtDesc()
        );
    }

    /**
     * 获取按状态分组的事件数量
     */
    private Map<String, Long> getEventCountByStatus() {
        List<WebhookEvent> allEvents = webhookEventRepository.findAll();
        return allEvents.stream()
                .collect(Collectors.groupingBy(
                    event -> event.getProcessingStatus().toString(),
                    Collectors.counting()
                ));
    }

    /**
     * 获取按账号分组的事件数量
     */
    private Map<String, Long> getEventCountByAccount() {
        List<WebhookEvent> allEvents = webhookEventRepository.findAll();
        return allEvents.stream()
                .filter(event -> event.getZoomAccountId() != null)
                .collect(Collectors.groupingBy(
                    WebhookEvent::getZoomAccountId,
                    Collectors.counting()
                ));
    }

    // ==================== 多账号支持方法 ====================

    /**
     * 验证账号ID是否有效
     */
    public boolean isValidAccountId(String accountId) {
        return zoomAuthService.getZoomAuthByZoomAccountId(accountId).isPresent();
    }

    /**
     * 验证账号名称是否有效（保留兼容性）
     */
    public boolean isValidAccountName(String accountName) {
        return zoomAuthService.getZoomAuthByAccountName(accountName).isPresent();
    }

    /**
     * 处理Zoom Webhook事件（支持多账号，改进版本 - 确保事件记录不会因业务事务回滚而丢失）
     */
    public void processWebhookEvent(String accountId, String eventType, JsonNode eventData) {
        // 获取对应的ZoomAuth
        Optional<ZoomAuth> zoomAuthOpt = zoomAuthService.getZoomAuthByZoomAccountId(accountId);
        if (!zoomAuthOpt.isPresent()) {
            log.error("未找到账号ID: {}", accountId);
            return;
        }

        ZoomAuth zoomAuth = zoomAuthOpt.get();

        // 第一步：立即保存原始事件数据（独立事务）
        WebhookEvent webhookEvent = saveWebhookEventRecordWithAccount(accountId, eventType, eventData, zoomAuth);

        // 第二步：处理业务逻辑
        processWebhookEventBusinessWithAccount(webhookEvent, eventType, eventData, zoomAuth);
    }

    /**
     * 立即保存Webhook事件记录（多账号版本，独立事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public WebhookEvent saveWebhookEventRecordWithAccount(String accountId, String eventType, JsonNode eventData, ZoomAuth zoomAuth) {
        try {
            WebhookEvent webhookEvent = new WebhookEvent();
            webhookEvent.setEventType(eventType);
            webhookEvent.setEventData(eventData.toString());
            webhookEvent.setProcessingStatus(WebhookEvent.ProcessingStatus.PENDING);
            webhookEvent.setZoomAccountId(zoomAuth.getZoomAccountId());

            WebhookEvent savedEvent = webhookEventRepository.save(webhookEvent);
            log.debug("Webhook事件记录已保存 [账号ID: {}]: eventType={}, eventId={}", accountId, eventType, savedEvent.getId());
            return savedEvent;
        } catch (Exception e) {
            log.error("保存Webhook事件记录失败 [账号ID: {}]: eventType={}", accountId, eventType, e);
            // 创建一个临时对象，避免后续处理出错
            WebhookEvent tempEvent = new WebhookEvent();
            tempEvent.setEventType(eventType);
            tempEvent.setEventData(eventData.toString());
            tempEvent.setProcessingStatus(WebhookEvent.ProcessingStatus.FAILED);
            tempEvent.setZoomAccountId(zoomAuth.getZoomAccountId());
            tempEvent.setErrorMessage("保存事件记录失败: " + e.getMessage());
            return tempEvent;
        }
    }

    /**
     * 处理Webhook事件的业务逻辑（多账号版本）
     */
    private void processWebhookEventBusinessWithAccount(WebhookEvent webhookEvent, String eventType, JsonNode eventData, ZoomAuth zoomAuth) {

        try {
            // 根据事件类型处理
            switch (eventType) {
                case "endpoint.url_validation":
                    handleEndpointUrlValidation(eventData, webhookEvent, zoomAuth);
                    break;
                case "meeting.created":
                    handleMeetingCreatedWithAccount(eventData, webhookEvent, zoomAuth);
                    break;
                case "meeting.updated":
                    handleMeetingUpdatedWithAccount(eventData, webhookEvent, zoomAuth);
                    break;
                case "meeting.deleted":
                    handleMeetingDeletedWithAccount(eventData, webhookEvent, zoomAuth);
                    break;
                case "meeting.started":
                    handleMeetingStartedWithAccount(eventData, webhookEvent, zoomAuth);
                    break;
                case "meeting.ended":
                    handleMeetingEndedWithAccount(eventData, webhookEvent, zoomAuth);
                    break;
                case "user.created":
                    handleUserCreatedWithAccount(eventData, webhookEvent, zoomAuth);
                    break;
                case "user.updated":
                    handleUserUpdatedWithAccount(eventData, webhookEvent, zoomAuth);
                    break;
                case "user.deleted":
                    handleUserDeletedWithAccount(eventData, webhookEvent, zoomAuth);
                    break;
                default:
                    log.info("未处理的事件类型 [账号ID: {}]: {}", zoomAuth.getZoomAccountId(), eventType);
                    updateWebhookEventStatus(webhookEvent.getId(), WebhookEvent.ProcessingStatus.IGNORED, null);
                    return; // 直接返回，不需要更新状态
            }

            // 业务处理成功，更新状态为已处理
            updateWebhookEventStatus(webhookEvent.getId(), WebhookEvent.ProcessingStatus.PROCESSED, null);

        } catch (Exception e) {
            log.error("处理Webhook事件失败 [账号ID: {}]: eventType={}, eventId={}", zoomAuth.getZoomAccountId(), eventType, webhookEvent.getId(), e);
            // 业务处理失败，更新状态为失败
            updateWebhookEventStatus(webhookEvent.getId(), WebhookEvent.ProcessingStatus.FAILED, e.getMessage());
        }
    }

    /**
     * 处理Zoom Webhook事件（兼容旧版本）
     */
    public void processWebhookEventLegacy(String eventType, JsonNode eventData) {
        // 使用原有的处理逻辑
        processWebhookEvent(eventType, eventData);
    }

    /**
     * 验证Webhook签名（使用账号ID）
     */
    public boolean verifyWebhookSignature(String accountId, String payload, String authorization) {
        try {
            Optional<ZoomAuth> zoomAuthOpt = zoomAuthService.getZoomAuthByZoomAccountId(accountId);
            if (!zoomAuthOpt.isPresent()) {
                log.error("未找到账号ID: {}", accountId);
                return false;
            }

            ZoomAuth zoomAuth = zoomAuthOpt.get();
            String webhookSecret = zoomAuth.getWebhookSecretToken();

            if (webhookSecret == null || webhookSecret.isEmpty()) {
                log.warn("账号ID {} 未配置Webhook Secret Token", accountId);
                return true; // 如果没有配置secret，暂时允许通过
            }

            // 实现Zoom Webhook签名验证
            return verifySignature(payload, authorization, webhookSecret);

        } catch (Exception e) {
            log.error("验证Webhook签名失败 [账号ID: {}]", accountId, e);
            return false;
        }
    }

    /**
     * 加密Token（使用账号ID）
     */
    public String encryptToken(String accountId, String plainToken) {
        try {
            Optional<ZoomAuth> zoomAuthOpt = zoomAuthService.getZoomAuthByZoomAccountId(accountId);
            if (!zoomAuthOpt.isPresent()) {
                log.error("未找到账号ID: {}", accountId);
                return plainToken;
            }

            ZoomAuth zoomAuth = zoomAuthOpt.get();
            String webhookSecret = zoomAuth.getWebhookSecretToken();

            if (webhookSecret == null || webhookSecret.isEmpty()) {
                log.warn("账号ID {} 未配置Webhook Secret Token", accountId);
                return plainToken;
            }

            return encryptTokenWithSecret(plainToken, webhookSecret);

        } catch (Exception e) {
            log.error("加密Token失败 [账号ID: {}]", accountId, e);
            return plainToken;
        }
    }

    /**
     * 加密Token（兼容旧版本）
     */
    public String encryptTokenLegacy(String plainToken) {
        try {
            Optional<ZoomAuth> defaultAuthOpt = zoomAuthService.getDefaultZoomAuth();
            if (!defaultAuthOpt.isPresent()) {
                log.warn("未找到默认认证信息");
                return plainToken;
            }

            ZoomAuth zoomAuth = defaultAuthOpt.get();
            String webhookSecret = zoomAuth.getWebhookSecretToken();

            if (webhookSecret == null || webhookSecret.isEmpty()) {
                log.warn("默认账号未配置Webhook Secret Token");
                return plainToken;
            }

            return encryptTokenWithSecret(plainToken, webhookSecret);

        } catch (Exception e) {
            log.error("加密Token失败 (默认账号)", e);
            return plainToken;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证签名
     */
    private boolean verifySignature(String payload, String authorization, String webhookSecret) {
        try {
            if (authorization == null || !authorization.startsWith("Bearer ")) {
                return false;
            }

            String signature = authorization.substring(7); // 移除 "Bearer " 前缀

            // 使用HMAC-SHA256验证签名
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(webhookSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);

            byte[] hash = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));
            String expectedSignature = Base64.getEncoder().encodeToString(hash);

            return signature.equals(expectedSignature);

        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("验证签名时发生错误", e);
            return false;
        }
    }

    /**
     * 使用Secret加密Token
     */
    private String encryptTokenWithSecret(String plainToken, String webhookSecret) {
        try {
            // 使用HMAC-SHA256加密
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(webhookSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);

            byte[] hash = mac.doFinal(plainToken.getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串（符合Zoom的格式要求）
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("加密Token时发生错误", e);
            return plainToken;
        }
    }

    // ==================== 多账号版本的事件处理方法 ====================

    /**
     * 处理会议创建事件（多账号版本）
     */
    private void handleMeetingCreatedWithAccount(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        // 使用指定的ZoomAuth处理
        handleMeetingCreatedWithAuth(eventData, webhookEvent, zoomAuth);
    }

    /**
     * 处理会议更新事件（多账号版本）
     */
    private void handleMeetingUpdatedWithAccount(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        try {
            handleMeetingUpdated(eventData, webhookEvent);
            log.info("通过Webhook更新会议成功 [账号: {}]", zoomAuth.getAccountName());
        } catch (Exception e) {
            log.error("处理会议更新Webhook事件失败 [账号: {}]", zoomAuth.getAccountName(), e);
            throw e;
        }
    }

    /**
     * 处理会议删除事件（多账号版本）
     */
    private void handleMeetingDeletedWithAccount(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        handleMeetingDeleted(eventData, webhookEvent);
        log.info("通过Webhook删除会议成功 [账号: {}]", zoomAuth.getAccountName());
    }

    /**
     * 处理会议开始事件（多账号版本）
     */
    private void handleMeetingStartedWithAccount(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        handleMeetingStarted(eventData, webhookEvent);
        log.info("通过Webhook标记会议开始 [账号: {}]", zoomAuth.getAccountName());
    }

    /**
     * 处理会议结束事件（多账号版本）
     */
    private void handleMeetingEndedWithAccount(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        handleMeetingEnded(eventData, webhookEvent);
        log.info("通过Webhook标记会议结束 [账号: {}]", zoomAuth.getAccountName());
    }

    /**
     * 处理用户创建事件（多账号版本）
     */
    private void handleUserCreatedWithAccount(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        handleUserCreated(eventData, webhookEvent);
        log.info("通过Webhook创建用户成功 [账号: {}]", zoomAuth.getAccountName());
    }

    /**
     * 处理用户更新事件（多账号版本）
     */
    private void handleUserUpdatedWithAccount(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        handleUserUpdated(eventData, webhookEvent);
        log.info("通过Webhook更新用户成功 [账号: {}]", zoomAuth.getAccountName());
    }

    /**
     * 处理用户删除事件（多账号版本）
     */
    private void handleUserDeletedWithAccount(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        handleUserDeleted(eventData, webhookEvent);
        log.info("通过Webhook删除用户成功 [账号: {}]", zoomAuth.getAccountName());
    }

    /**
     * 查询并保存会议详情
     */
    private void queryAndSaveMeetingDetails(Meeting meeting, ZoomAuth zoomAuth) {
        try {
            String meetingId = meeting.getZoomMeetingId();
            if (meetingId == null || meetingId.trim().isEmpty()) {
                log.warn("会议的Zoom会议ID为空，无法查询详情 [账号: {}, 会议ID: {}]",
                        zoomAuth.getAccountName(), meeting.getId());
                return;
            }

            log.info("开始查询会议详情 [账号: {}, Zoom会议ID: {}]", zoomAuth.getAccountName(), meetingId);

            // 调用Zoom API查询会议详情
            var apiResponse = zoomApiService.getMeeting(meetingId, zoomAuth);

            if (!apiResponse.isSuccess()) {
                log.error("查询Zoom会议详情失败 [账号: {}, Zoom会议ID: {}]: {}",
                         zoomAuth.getAccountName(), meetingId, apiResponse.getMessage());
                return;
            }

            JsonNode meetingDetails = apiResponse.getData();
            log.info("成功获取会议详情 [账号: {}, Zoom会议ID: {}]", zoomAuth.getAccountName(), meetingId);

            // 保存会议详情到数据库
            zoomMeetingDetailService.saveZoomMeetingDetailWithOccurrences(meeting.getId(), meetingDetails);

            log.info("成功保存会议详情到数据库 [账号: {}, 会议ID: {}, Zoom会议ID: {}]",
                    zoomAuth.getAccountName(), meeting.getId(), meetingId);

        } catch (Exception e) {
            log.error("查询并保存会议详情失败 [账号: {}, 会议ID: {}, Zoom会议ID: {}]",
                     zoomAuth.getAccountName(), meeting.getId(), meeting.getZoomMeetingId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理Zoom端点URL验证事件
     */
    private void handleEndpointUrlValidation(JsonNode eventData, WebhookEvent webhookEvent, ZoomAuth zoomAuth) {
        try {
            log.info("处理Zoom端点URL验证事件 [账号: {}]", zoomAuth.getAccountName());

            // 获取plainToken
            JsonNode payload = eventData.get("payload");
            if (payload != null && payload.has("plainToken")) {
                String plainToken = payload.get("plainToken").asText();
                log.info("收到Zoom端点验证token [账号: {}]: {}", zoomAuth.getAccountName(), plainToken);

                // 记录验证成功
                log.info("Zoom端点URL验证成功 [账号: {}]，token将由Controller返回", zoomAuth.getAccountName());
            } else {
                log.warn("Zoom端点验证事件缺少plainToken [账号: {}]", zoomAuth.getAccountName());
            }

        } catch (Exception e) {
            log.error("处理Zoom端点URL验证事件失败 [账号: {}]", zoomAuth.getAccountName(), e);
            throw e;
        }
    }

    /**
     * 处理Zoom端点URL验证事件（兼容旧版本）
     */
    private void handleEndpointUrlValidationLegacy(JsonNode eventData, WebhookEvent webhookEvent) {
        try {
            log.info("处理Zoom端点URL验证事件 (默认账号)");

            // 获取plainToken
            JsonNode payload = eventData.get("payload");
            if (payload != null && payload.has("plainToken")) {
                String plainToken = payload.get("plainToken").asText();
                log.info("收到Zoom端点验证token (默认账号): {}", plainToken);

                // 记录验证成功
                log.info("Zoom端点URL验证成功 (默认账号)，token将由Controller返回");
            } else {
                log.warn("Zoom端点验证事件缺少plainToken (默认账号)");
            }

        } catch (Exception e) {
            log.error("处理Zoom端点URL验证事件失败 (默认账号)", e);
            throw e;
        }
    }






}
