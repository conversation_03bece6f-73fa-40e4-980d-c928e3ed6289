package com.zoombus.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.ZoomApiLog;
import com.zoombus.repository.ZoomApiLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Zoom API日志记录服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ZoomApiLogService {
    
    private final ZoomApiLogRepository zoomApiLogRepository;
    private final ObjectMapper objectMapper;
    
    /**
     * 创建API调用日志记录
     */
    public ZoomApiLog createApiLog(String method, String path, String url,
                                   String businessType, String businessId, String zoomUserId, String zoomUserEmail) {
        ZoomApiLog apiLog = new ZoomApiLog();
        apiLog.setRequestId(generateRequestId());
        apiLog.setApiMethod(method);
        apiLog.setApiPath(path);
        apiLog.setApiUrl(url);
        apiLog.setBusinessType(businessType);
        apiLog.setBusinessId(businessId);
        apiLog.setZoomUserId(zoomUserId);
        apiLog.setZoomUserEmail(zoomUserEmail);
        apiLog.setRequestTime(LocalDateTime.now());
        apiLog.setIsSuccess(false); // 默认为失败，成功时再更新

        return apiLog;
    }

    /**
     * 创建API调用日志记录（兼容旧版本，不包含email）
     */
    public ZoomApiLog createApiLog(String method, String path, String url,
                                   String businessType, String businessId, String zoomUserId) {
        return createApiLog(method, path, url, businessType, businessId, zoomUserId, null);
    }
    
    /**
     * 记录请求信息
     */
    public void recordRequest(ZoomApiLog apiLog, HttpHeaders headers, Object requestBody) {
        try {
            // 记录请求头（过滤敏感信息）
            Map<String, String> filteredHeaders = filterSensitiveHeaders(headers);
            apiLog.setRequestHeaders(objectMapper.writeValueAsString(filteredHeaders));
            
            // 记录请求体
            if (requestBody != null) {
                String requestBodyStr = objectMapper.writeValueAsString(requestBody);
                apiLog.setRequestBody(requestBodyStr);
            }
        } catch (Exception e) {
            log.warn("记录请求信息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 记录响应信息
     */
    public void recordResponse(ZoomApiLog apiLog, int statusCode, HttpHeaders headers, String responseBody) {
        try {
            apiLog.setResponseTime(LocalDateTime.now());
            apiLog.setResponseStatus(statusCode);
            apiLog.calculateDuration();
            
            // 记录响应头
            if (headers != null) {
                Map<String, String> headerMap = new HashMap<>();
                headers.forEach((key, values) -> {
                    if (!values.isEmpty()) {
                        headerMap.put(key, values.get(0));
                    }
                });
                apiLog.setResponseHeaders(objectMapper.writeValueAsString(headerMap));
            }
            
            // 记录响应体
            apiLog.setResponseBody(responseBody);
            
            // 判断是否成功
            apiLog.setIsSuccess(statusCode >= 200 && statusCode < 300);
            
        } catch (Exception e) {
            log.warn("记录响应信息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 记录错误信息
     */
    public void recordError(ZoomApiLog apiLog, Exception exception) {
        try {
            apiLog.setResponseTime(LocalDateTime.now());
            apiLog.calculateDuration();
            apiLog.setIsSuccess(false);
            
            if (exception instanceof WebClientResponseException) {
                WebClientResponseException webEx = (WebClientResponseException) exception;
                apiLog.setResponseStatus(webEx.getRawStatusCode());
                apiLog.setErrorCode(String.valueOf(webEx.getRawStatusCode()));
                apiLog.setErrorMessage(webEx.getMessage());
                apiLog.setResponseBody(webEx.getResponseBodyAsString());
            } else {
                apiLog.setErrorCode("INTERNAL_ERROR");
                apiLog.setErrorMessage(exception.getMessage());
            }
        } catch (Exception e) {
            log.warn("记录错误信息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 异步保存日志（使用独立事务，避免业务事务回滚影响日志记录）
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveLogAsync(ZoomApiLog apiLog) {
        try {
            zoomApiLogRepository.save(apiLog);
            log.debug("异步保存API调用日志成功: {}", apiLog.getApiPath());
        } catch (Exception e) {
            log.error("异步保存API调用日志失败: path={}, error={}", apiLog.getApiPath(), e.getMessage(), e);
            // 日志记录失败不应该影响主业务流程，所以不抛出异常
        }
    }
    
    /**
     * 同步保存日志（使用独立事务，避免业务事务回滚影响日志记录）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ZoomApiLog saveLog(ZoomApiLog apiLog) {
        try {
            ZoomApiLog savedLog = zoomApiLogRepository.save(apiLog);
            log.debug("同步保存API调用日志成功: {}", apiLog.getApiPath());
            return savedLog;
        } catch (Exception e) {
            log.error("同步保存API调用日志失败: path={}, error={}", apiLog.getApiPath(), e.getMessage(), e);
            // 日志记录失败不应该影响主业务流程，返回原对象
            return apiLog;
        }
    }
    
    /**
     * 根据ID查找日志
     */
    public ZoomApiLog findById(Long id) {
        return zoomApiLogRepository.findById(id).orElse(null);
    }

    /**
     * 分页查询日志
     */
    public Page<ZoomApiLog> findLogs(String businessType, Boolean isSuccess, String apiPath,
                                     String zoomUserId, LocalDateTime startTime, LocalDateTime endTime,
                                     Long minDuration, Long maxDuration, Pageable pageable) {
        return zoomApiLogRepository.findByConditions(
                businessType, isSuccess, apiPath, zoomUserId,
                startTime, endTime, minDuration, maxDuration, pageable);
    }
    
    /**
     * 获取业务类型统计
     */
    public List<Object[]> getStatsByBusinessType(LocalDateTime startTime, LocalDateTime endTime) {
        return zoomApiLogRepository.getStatsByBusinessType(startTime, endTime);
    }
    
    /**
     * 获取按小时统计
     */
    public List<Object[]> getStatsByHour(LocalDateTime startTime, LocalDateTime endTime) {
        return zoomApiLogRepository.getStatsByHour(startTime, endTime);
    }
    
    /**
     * 获取响应状态码统计
     */
    public List<Object[]> getStatsByResponseStatus(LocalDateTime startTime, LocalDateTime endTime) {
        return zoomApiLogRepository.getStatsByResponseStatus(startTime, endTime);
    }
    
    /**
     * 获取错误统计
     */
    public List<Object[]> getErrorStats(LocalDateTime startTime, LocalDateTime endTime) {
        return zoomApiLogRepository.getErrorStats(startTime, endTime);
    }
    
    /**
     * 获取慢请求统计
     */
    public List<Object[]> getSlowRequestStats(Long minDuration, LocalDateTime startTime, LocalDateTime endTime) {
        return zoomApiLogRepository.getSlowRequestStats(minDuration, startTime, endTime);
    }
    
    /**
     * 获取总体统计
     */
    public Object[] getOverallStats(LocalDateTime startTime, LocalDateTime endTime) {
        return zoomApiLogRepository.getOverallStats(startTime, endTime);
    }
    
    /**
     * 清理过期日志
     */
    @Transactional
    public void cleanupOldLogs(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        try {
            zoomApiLogRepository.deleteByRequestTimeBefore(cutoffTime);
            log.info("清理{}天前的API日志完成", daysToKeep);
        } catch (Exception e) {
            log.error("清理API日志失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 过滤敏感请求头信息
     */
    private Map<String, String> filterSensitiveHeaders(HttpHeaders headers) {
        Map<String, String> filtered = new HashMap<>();
        if (headers != null) {
            headers.forEach((key, values) -> {
                if (!isSensitiveHeader(key) && !values.isEmpty()) {
                    filtered.put(key, values.get(0));
                }
            });
        }
        return filtered;
    }
    
    /**
     * 判断是否为敏感请求头
     */
    private boolean isSensitiveHeader(String headerName) {
        if (headerName == null) return true;
        String lowerName = headerName.toLowerCase();
        return lowerName.contains("authorization") || 
               lowerName.contains("token") || 
               lowerName.contains("secret") ||
               lowerName.contains("password") ||
               lowerName.contains("key");
    }
    
    /**
     * 从HttpServletRequest提取客户端信息
     */
    public void recordClientInfo(ZoomApiLog apiLog, HttpServletRequest request) {
        if (request != null) {
            apiLog.setClientIp(getClientIpAddress(request));
            apiLog.setUserAgent(request.getHeader("User-Agent"));
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 记录API调用日志（用于密码修改等API调用，使用独立事务）
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logApiCall(String method, String apiUrl, String requestBody, String responseBody,
                          int statusCode, LocalDateTime startTime, LocalDateTime endTime,
                          String description, String zoomUserId) {
        try {
            ZoomApiLog apiLog = new ZoomApiLog();
            apiLog.setRequestId(generateRequestId());
            apiLog.setApiMethod(method);
            apiLog.setApiPath(extractPathFromUrl(apiUrl));
            apiLog.setApiUrl(apiUrl);
            apiLog.setBusinessType("PASSWORD_MANAGEMENT");
            apiLog.setBusinessId("PWD_" + System.currentTimeMillis());
            apiLog.setZoomUserId(zoomUserId);
            apiLog.setRequestTime(startTime);
            apiLog.setResponseTime(endTime);
            apiLog.setRequestBody(requestBody);
            apiLog.setResponseBody(responseBody);
            apiLog.setResponseStatus(statusCode);
            apiLog.setIsSuccess(statusCode >= 200 && statusCode < 300);

            // 设置错误信息（如果失败）
            if (statusCode < 200 || statusCode >= 300) {
                apiLog.setErrorMessage(description);
            }

            // 计算响应时间
            if (startTime != null && endTime != null) {
                long responseTimeMs = java.time.Duration.between(startTime, endTime).toMillis();
                apiLog.setDurationMs(responseTimeMs);
            }

            zoomApiLogRepository.save(apiLog);

            log.debug("记录Zoom API调用日志: Method={}, URL={}, Status={}, Success={}",
                    method, apiUrl, statusCode, apiLog.getIsSuccess());

        } catch (Exception e) {
            log.error("记录Zoom API调用日志失败: Method={}, URL={}", method, apiUrl, e);
        }
    }

    /**
     * 从完整URL中提取路径
     */
    private String extractPathFromUrl(String fullUrl) {
        if (fullUrl == null) {
            return null;
        }

        try {
            // 如果是相对路径，直接返回
            if (fullUrl.startsWith("/")) {
                return fullUrl;
            }

            // 如果是完整URL，提取路径部分
            if (fullUrl.startsWith("http")) {
                int pathStart = fullUrl.indexOf("/", 8); // 跳过 "https://"
                if (pathStart > 0) {
                    return fullUrl.substring(pathStart);
                }
            }

            return fullUrl;
        } catch (Exception e) {
            log.warn("提取URL路径失败: {}", fullUrl, e);
            return fullUrl;
        }
    }
}
