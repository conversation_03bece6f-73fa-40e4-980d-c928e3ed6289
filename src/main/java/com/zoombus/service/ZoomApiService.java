package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.config.ZoomConfig;
import com.zoombus.dto.CreateMeetingRequest;
import com.zoombus.dto.CreateZoomUserRequest;
import com.zoombus.dto.UpdateMeetingRequest;
import com.zoombus.dto.DeleteMeetingRequest;
import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.entity.Meeting;
import com.zoombus.entity.ZoomApiLog;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.ZoomUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class ZoomApiService {

    private final ZoomConfig zoomConfig;
    private final WebClient.Builder webClientBuilder;
    private final ObjectMapper objectMapper;
    private final com.zoombus.repository.ZoomAuthRepository zoomAuthRepository;
    private final ZoomUserRepository zoomUserRepository;
    private final ZoomApiLogService zoomApiLogService;
    
    private WebClient getWebClient(String accessToken) {
        return getWebClient(accessToken, zoomConfig.getApiBaseUrl());
    }

    private WebClient getWebClient(String accessToken, String apiBaseUrl) {
        return webClientBuilder
                .baseUrl(apiBaseUrl)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    /**
     * 带日志记录的API调用方法
     */
    private <T> ZoomApiResponse<T> executeApiCallWithLogging(
            String method, String path, Object requestBody, Class<T> responseType,
            String businessType, String businessId, String zoomUserId,
            com.zoombus.entity.ZoomAuth zoomAuth) {

        // 获取用户email（如果zoomUserId不为空）
        String zoomUserEmail = null;
        if (zoomUserId != null && !zoomUserId.isEmpty()) {
            try {
                // 从数据库中查找用户email
                zoomUserEmail = findZoomUserEmailById(zoomUserId);
            } catch (Exception e) {
                log.debug("获取用户email失败: {}", e.getMessage());
            }
        }

        // 创建API日志记录
        ZoomApiLog apiLog = zoomApiLogService.createApiLog(
                method, path, zoomAuth.getApiBaseUrl() + path,
                businessType, businessId, zoomUserId, zoomUserEmail);

        try {
            WebClient webClient = getWebClientWithAuth(zoomAuth);

            // 构建请求头用于记录
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.setBearerAuth(zoomAuth.getAccessToken());
            requestHeaders.setContentType(MediaType.APPLICATION_JSON);

            // 添加自定义请求头用于追踪
            requestHeaders.add("X-ZoomAuth-Account", zoomAuth.getAccountName());
            requestHeaders.add("X-ZoomAuth-Id", String.valueOf(zoomAuth.getId()));
            requestHeaders.add("X-Business-Type", businessType);
            if (businessId != null) {
                requestHeaders.add("X-Business-Id", businessId);
            }

            // 记录请求信息（包含完整的请求头）
            zoomApiLogService.recordRequest(apiLog, requestHeaders, requestBody);

            // 在日志中显示关键请求头信息
            log.info("🔗 Zoom API调用开始: {} {} | ZoomAuth: {} (ID: {}) | BusinessType: {} | BusinessId: {}",
                    method, path, zoomAuth.getAccountName(), zoomAuth.getId(), businessType, businessId);

            HttpMethod httpMethod = HttpMethod.resolve(method.toUpperCase());
            if (httpMethod == null) {
                throw new IllegalArgumentException("不支持的HTTP方法: " + method);
            }

            // 构建请求并捕获响应（含状态码、headers、body）
            Mono<ZoomApiResponse<T>> mono = webClient.method(httpMethod)
                    .uri(path)
                    .headers(h -> {
                        // 添加自定义追踪头到实际请求中
                        h.add("X-ZoomAuth-Account", zoomAuth.getAccountName());
                        h.add("X-ZoomAuth-Id", String.valueOf(zoomAuth.getId()));
                        h.add("X-Business-Type", businessType);
                        if (businessId != null) {
                            h.add("X-Business-Id", businessId);
                        }
                    })
                    .body((requestBody != null && (httpMethod == HttpMethod.POST || httpMethod == HttpMethod.PUT || httpMethod == HttpMethod.PATCH))
                                    ? Mono.just(requestBody)
                                    : Mono.empty(), Object.class)
                    .exchangeToMono((ClientResponse clientResponse) -> {
                        int statusCode = clientResponse.rawStatusCode();
                        HttpHeaders respHeaders = clientResponse.headers().asHttpHeaders();
                        boolean hasBody = statusCode != 204 && responseType != Void.class;
                        boolean isSuccess = statusCode >= 200 && statusCode < 300;

                        if (!hasBody) {
                            // 无响应体（例如204 No Content），根据状态码判断成功或失败
                            zoomApiLogService.recordResponse(apiLog, statusCode, respHeaders, null);
                            zoomApiLogService.saveLogAsync(apiLog);
                            if (isSuccess) {
                                return Mono.just(ZoomApiResponse.successWithStatus(null, statusCode));
                            } else {
                                return Mono.just(ZoomApiResponse.error("API调用失败，状态码: " + statusCode,
                                    String.valueOf(statusCode), statusCode));
                            }
                        }
                        return clientResponse.bodyToMono(responseType).map(bodyObj -> {
                            String rawBodyString;
                            try {
                                rawBodyString = bodyObj != null ? objectMapper.writeValueAsString(bodyObj) : null;
                            } catch (Exception ex) {
                                rawBodyString = bodyObj != null ? bodyObj.toString() : null;
                            }
                            zoomApiLogService.recordResponse(apiLog, statusCode, respHeaders, rawBodyString);
                            zoomApiLogService.saveLogAsync(apiLog);

                            // 记录响应详情
                            log.info("✅ Zoom API调用完成: {} {} | 状态码: {} | ZoomAuth: {} | 响应大小: {} bytes",
                                    method, path, statusCode, zoomAuth.getAccountName(),
                                    rawBodyString != null ? rawBodyString.length() : 0);

                            if (isSuccess) {
                                return ZoomApiResponse.successWithStatus(bodyObj, statusCode);
                            } else {
                                // 从响应体中提取错误信息
                                String errorMessage = "API调用失败，状态码: " + statusCode;
                                String errorCode = String.valueOf(statusCode);
                                if (rawBodyString != null) {
                                    try {
                                        JsonNode errorNode = objectMapper.readTree(rawBodyString);
                                        if (errorNode.has("message")) {
                                            errorMessage = errorNode.get("message").asText();
                                        }
                                        if (errorNode.has("code")) {
                                            errorCode = errorNode.get("code").asText();
                                        }
                                    } catch (Exception e) {
                                        log.debug("解析错误响应失败: {}", e.getMessage());
                                    }
                                }
                                return ZoomApiResponse.error(errorMessage, errorCode, statusCode);
                            }
                        });
                    });

            return mono.block();

        } catch (WebClientResponseException e) {
            // 记录错误响应
            zoomApiLogService.recordError(apiLog, e);
            zoomApiLogService.saveLogAsync(apiLog);

            log.error("❌ Zoom API调用失败: {} {} | 状态码: {} | ZoomAuth: {} (ID: {}) | 响应: {}",
                    method, path, e.getRawStatusCode(), zoomAuth.getAccountName(), zoomAuth.getId(),
                    e.getResponseBodyAsString());
        return ZoomApiResponse.error("API调用失败: " + e.getMessage(),
            String.valueOf(e.getRawStatusCode()), e.getRawStatusCode());

        } catch (Exception e) {
            // 记录其他错误
            zoomApiLogService.recordError(apiLog, e);
            zoomApiLogService.saveLogAsync(apiLog);

            log.error("💥 Zoom API调用异常: {} {} | ZoomAuth: {} (ID: {}) | 异常: {}",
                    method, path, zoomAuth.getAccountName(), zoomAuth.getId(), e.getMessage(), e);
        return ZoomApiResponse.error("API调用异常: " + e.getMessage(), "INTERNAL_ERROR", 500);
        }
    }



    /**
     * 获取默认的ZoomAuth
     */
    private com.zoombus.entity.ZoomAuth getDefaultZoomAuth() {
        return zoomAuthRepository.findFirstActiveAuth()
                .orElseThrow(() -> new RuntimeException("未找到可用的Zoom认证信息"));
    }

    /**
     * 使用指定的ZoomAuth获取访问令牌
     */
    private String getAccessTokenFromAuth(com.zoombus.entity.ZoomAuth zoomAuth) {
        if (zoomAuth == null || zoomAuth.getAccessToken() == null) {
            throw new RuntimeException("ZoomAuth或访问令牌为空");
        }
        return zoomAuth.getAccessToken();
    }

    /**
     * 使用指定的ZoomAuth创建WebClient
     */
    private WebClient getWebClientWithAuth(com.zoombus.entity.ZoomAuth zoomAuth) {
        String accessToken = getAccessTokenFromAuth(zoomAuth);
        String apiBaseUrl = zoomAuth.getApiBaseUrl() != null ? zoomAuth.getApiBaseUrl() : zoomConfig.getApiBaseUrl();

        return webClientBuilder
                .baseUrl(apiBaseUrl)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }


    
    /**
     * 创建Zoom用户 (使用默认认证)
     */
    public ZoomApiResponse<JsonNode> createUser(CreateZoomUserRequest request) {
        try {
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("email", request.getEmail());
            userInfo.put("type", request.getUserType().getValue());
            userInfo.put("first_name", request.getFirstName());
            userInfo.put("last_name", request.getLastName());

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("action", "create");
            requestBody.put("user_info", userInfo);

            // 这个方法需要 accessToken 参数，暂时抛出异常
            throw new UnsupportedOperationException("请使用 createUser(request, accessToken) 方法");
        } catch (WebClientResponseException e) {
            log.error("创建Zoom用户失败: {}", e.getResponseBodyAsString(), e);
            return ZoomApiResponse.error("创建用户失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("创建Zoom用户异常", e);
            return ZoomApiResponse.error("创建用户异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 创建Zoom用户（使用指定的访问令牌）
     */
    public ZoomApiResponse<JsonNode> createUser(CreateZoomUserRequest request, String accessToken) {
        try {
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("email", request.getEmail());
            userInfo.put("type", request.getUserType().getValue());
            userInfo.put("first_name", request.getFirstName());
            userInfo.put("last_name", request.getLastName());

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("action", "create");
            requestBody.put("user_info", userInfo);

            JsonNode response = getWebClient(accessToken)
                    .post()
                    .uri("/users")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(JsonNode.class)
                    .block();

            log.info("Zoom用户创建成功: {}", response);
            return ZoomApiResponse.success(response);
        } catch (WebClientResponseException e) {
            log.error("创建Zoom用户失败: {}", e.getResponseBodyAsString(), e);
            return ZoomApiResponse.error("创建用户失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("创建Zoom用户异常", e);
            return ZoomApiResponse.error("创建用户异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 使用指定的ZoomAuth创建Zoom用户
     */
    public ZoomApiResponse<JsonNode> createUserWithAuth(com.zoombus.dto.CreateZoomUserRequest request, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("email", request.getEmail());
            userInfo.put("type", request.getUserType().getValue());
            userInfo.put("first_name", request.getFirstName());
            userInfo.put("last_name", request.getLastName());

            // 添加可选字段
            if (request.getDepartment() != null && !request.getDepartment().trim().isEmpty()) {
                userInfo.put("dept", request.getDepartment());
            }
            if (request.getJobTitle() != null && !request.getJobTitle().trim().isEmpty()) {
                userInfo.put("job_title", request.getJobTitle());
            }
            if (request.getPhoneNumber() != null && !request.getPhoneNumber().trim().isEmpty()) {
                userInfo.put("phone_number", request.getPhoneNumber());
            }
            if (request.getTimezone() != null && !request.getTimezone().trim().isEmpty()) {
                userInfo.put("timezone", request.getTimezone());
            }
            if (request.getLanguage() != null && !request.getLanguage().trim().isEmpty()) {
                userInfo.put("language", request.getLanguage());
            }

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("action", "create");
            requestBody.put("user_info", userInfo);

            // 使用带日志记录的API调用方法
            ZoomApiResponse<JsonNode> response = executeApiCallWithLogging(
                    "POST",
                    "/users",
                    requestBody,
                    JsonNode.class,
                    "CREATE_USER",
                    request.getEmail(),
                    null,
                    zoomAuth
            );

            if (response.isSuccess()) {
                log.info("Zoom用户创建成功，账号: {}, 邮箱: {}", zoomAuth.getAccountName(), request.getEmail());
            }
            return response;
            
        } catch (WebClientResponseException e) {
            log.error("创建Zoom用户失败: {}", e.getResponseBodyAsString(), e);
            return ZoomApiResponse.error("创建Zoom用户失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("创建Zoom用户异常", e);
            return ZoomApiResponse.error("创建Zoom用户异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }
    
    /**
     * 创建会议
     */
    public ZoomApiResponse<JsonNode> createMeeting(String zoomUserId, CreateMeetingRequest request) {
        try {
            Map<String, Object> requestBody = new HashMap<>();

            log.info("=== Zoom API 请求参数调试 ===");
            log.info("request.getTopic(): {}", request.getTopic());
            log.info("request.getType(): {}", request.getType());
            log.info("request.getStartTime(): {}", request.getStartTime());
            log.info("request.getDurationMinutes(): {}", request.getDurationMinutes());
            log.info("request.getTimezone(): {}", request.getTimezone());
            log.info("request.getAgenda(): {}", request.getAgenda());

            requestBody.put("topic", request.getTopic());
            requestBody.put("type", request.getType().getValue());
            requestBody.put("start_time", request.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            requestBody.put("duration", request.getDurationMinutes());
            requestBody.put("timezone", request.getTimezone());
            requestBody.put("agenda", request.getAgenda());

            log.info("=== 添加基础字段后的requestBody ===");
            log.info("requestBody after basic fields: {}", requestBody);
            
            if (request.getPassword() != null && !request.getPassword().isEmpty()) {
                requestBody.put("password", request.getPassword());
            }
            
            // 会议设置
            Map<String, Object> settings = new HashMap<>();
            settings.put("join_before_host", true);
            settings.put("mute_upon_entry", true);
            settings.put("waiting_room", false);
            requestBody.put("settings", settings);
            
            // 这个方法需要 accessToken 参数，暂时抛出异常
            throw new UnsupportedOperationException("请使用 createMeeting(zoomUserId, request, accessToken) 方法");
            
        } catch (WebClientResponseException e) {
            log.error("创建Zoom会议失败: {}", e.getResponseBodyAsString(), e);
            return ZoomApiResponse.error("创建Zoom会议失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("创建Zoom会议异常", e);
            return ZoomApiResponse.error("创建Zoom会议异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 创建Zoom会议（使用指定的访问令牌）
     */
    public ZoomApiResponse<JsonNode> createMeeting(String zoomUserId, CreateMeetingRequest request, String accessToken) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("topic", request.getTopic());
            requestBody.put("type", request.getType().getValue());

            // 预定会议和周期性会议都需要开始时间、时长和时区
            if (request.getType().getValue() == 2 || request.getType().getValue() == 8) { // SCHEDULED 或 RECURRING_FIXED_TIME
                log.info("=== 添加会议时间相关字段 ===");
                log.info("会议类型: {} (值: {})", request.getType(), request.getType().getValue());
                log.info("开始时间: {}", request.getStartTime());
                log.info("时长: {} 分钟", request.getDurationMinutes());
                log.info("时区: {}", request.getTimezone());

                // 将LocalDateTime转换为ISO 8601格式的字符串
                // 注意：request.getStartTime()是东八区时间，需要转换为UTC时间发送给Zoom API
                LocalDateTime localTime = request.getStartTime();
                ZonedDateTime shanghaiTime = localTime.atZone(ZoneId.of("Asia/Shanghai"));
                ZonedDateTime utcTime = shanghaiTime.withZoneSameInstant(ZoneOffset.UTC);
                String startTimeStr = utcTime.format(DateTimeFormatter.ISO_INSTANT);

                log.info("时间转换: 东八区 {} -> UTC {}", localTime, startTimeStr);
                requestBody.put("start_time", startTimeStr);
                requestBody.put("duration", request.getDurationMinutes());
                requestBody.put("timezone", request.getTimezone());

                log.info("已添加字段到requestBody: start_time={}, duration={}, timezone={}",
                        startTimeStr, request.getDurationMinutes(), request.getTimezone());
            } else {
                log.info("会议类型 {} 不需要时间相关字段", request.getType());
            }

            if (request.getPassword() != null && !request.getPassword().trim().isEmpty()) {
                requestBody.put("password", request.getPassword());
            }
            if (request.getAgenda() != null && !request.getAgenda().trim().isEmpty()) {
                requestBody.put("agenda", request.getAgenda());
            }

            // 使用默认设置
            Map<String, Object> settings = new HashMap<>();
            settings.put("host_video", true);
            settings.put("participant_video", true);
            settings.put("join_before_host", false);
            settings.put("mute_upon_entry", true);
            settings.put("waiting_room", false);
            settings.put("auto_recording", "none");
            requestBody.put("settings", settings);

            // 处理周期性会议设置
            if (request.getIsRecurring() != null && request.getIsRecurring()) {
                log.info("处理周期性会议设置: isRecurring={}, recurrenceType={}, repeatInterval={}",
                        request.getIsRecurring(), request.getRecurrenceType(), request.getRepeatInterval());

                Map<String, Object> recurrence = new HashMap<>();

                // 设置重复类型
                if (request.getRecurrenceType() != null) {
                    switch (request.getRecurrenceType()) {
                        case DAILY:
                            recurrence.put("type", 1);
                            break;
                        case WEEKLY:
                            recurrence.put("type", 2);
                            break;
                        case MONTHLY:
                            recurrence.put("type", 3);
                            break;
                    }
                }

                // 设置重复间隔
                if (request.getRepeatInterval() != null) {
                    recurrence.put("repeat_interval", request.getRepeatInterval());
                    log.info("设置重复间隔: {}", request.getRepeatInterval());
                } else {
                    log.warn("重复间隔为null，使用默认值1");
                    recurrence.put("repeat_interval", 1);
                }

                // 处理每周重复的星期几设置
                if (request.getRecurrenceType() == Meeting.RecurrenceType.WEEKLY && request.getWeeklyDays() != null) {
                    // 转换星期几格式：我们的系统 0=周日,1=周一...6=周六 -> Zoom API 1=周日,2=周一...7=周六
                    String[] ourDays = request.getWeeklyDays().split(",");
                    StringBuilder zoomDays = new StringBuilder();
                    for (String day : ourDays) {
                        try {
                            int ourDay = Integer.parseInt(day.trim());
                            int zoomDay = ourDay == 0 ? 1 : ourDay + 1; // 0->1, 1->2, 2->3, ..., 6->7
                            if (zoomDays.length() > 0) {
                                zoomDays.append(",");
                            }
                            zoomDays.append(zoomDay);
                        } catch (NumberFormatException e) {
                            log.warn("无效的星期几数字: {}", day);
                        }
                    }
                    recurrence.put("weekly_days", zoomDays.toString());
                    log.info("设置每周重复的星期几: 原始={}, 转换后={}", request.getWeeklyDays(), zoomDays.toString());
                }

                // 处理每月重复的设置
                if (request.getRecurrenceType() == Meeting.RecurrenceType.MONTHLY) {
                    if (request.getMonthlyType() == Meeting.MonthlyType.DAY_OF_MONTH && request.getMonthlyDay() != null) {
                        recurrence.put("monthly_day", request.getMonthlyDay());
                        log.info("设置每月重复的日期: {}", request.getMonthlyDay());
                    } else if (request.getMonthlyType() == Meeting.MonthlyType.DAY_OF_WEEK
                               && request.getMonthlyDay() != null && request.getMonthlyWeekDay() != null) {
                        recurrence.put("monthly_week", request.getMonthlyDay());
                        recurrence.put("monthly_week_day", request.getMonthlyWeekDay() + 1); // Zoom API使用1-7表示周日-周六
                        log.info("设置每月重复的第{}个星期{}: week={}, weekDay={}",
                                request.getMonthlyDay(), request.getMonthlyWeekDay(),
                                request.getMonthlyDay(), request.getMonthlyWeekDay() + 1);
                    }
                }

                // 设置结束条件
                if (request.getEndType() != null) {
                    switch (request.getEndType()) {
                        case NO_END:
                            // 不设置结束条件
                            break;
                        case BY_DATE:
                            if (request.getEndDateTime() != null) {
                                // 计算从开始时间到结束时间的总场次数
                                LocalDateTime startTime = request.getStartTime();
                                LocalDateTime endTime = request.getEndDateTime();
                                int repeatInterval = request.getRepeatInterval() != null ? request.getRepeatInterval() : 1;
                                int totalOccurrences;

                                if (request.getRecurrenceType() == Meeting.RecurrenceType.DAILY) {
                                    // 按天计算
                                    long totalDays = ChronoUnit.DAYS.between(startTime.toLocalDate(), endTime.toLocalDate());
                                    totalOccurrences = (int) Math.ceil((double) totalDays / repeatInterval) + 1;
                                } else if (request.getRecurrenceType() == Meeting.RecurrenceType.WEEKLY) {
                                    // 按周计算
                                    long totalWeeks = ChronoUnit.WEEKS.between(startTime.toLocalDate(), endTime.toLocalDate());
                                    totalOccurrences = (int) Math.ceil((double) totalWeeks / repeatInterval) + 1;
                                } else if (request.getRecurrenceType() == Meeting.RecurrenceType.MONTHLY) {
                                    // 按月计算
                                    long totalMonths = ChronoUnit.MONTHS.between(startTime.toLocalDate(), endTime.toLocalDate());
                                    totalOccurrences = (int) Math.ceil((double) totalMonths / repeatInterval) + 1;
                                } else {
                                    // 默认按天计算
                                    long totalDays = ChronoUnit.DAYS.between(startTime.toLocalDate(), endTime.toLocalDate());
                                    totalOccurrences = (int) Math.ceil((double) totalDays / repeatInterval) + 1;
                                }

                                log.info("=== 周期结束时间调试 ===");
                                log.info("开始时间: {}", startTime);
                                log.info("结束时间: {}", endTime);
                                log.info("重复类型: {}", request.getRecurrenceType());
                                log.info("重复间隔: {}", repeatInterval);
                                log.info("计算的总场次数: {}", totalOccurrences);

                                // 尝试使用end_times而不是end_date_time，因为Zoom API可能对end_date_time有限制
                                recurrence.put("end_times", totalOccurrences);
                                log.info("使用end_times参数: {}", totalOccurrences);
                            }
                            break;
                        case BY_TIMES:
                            if (request.getEndTimes() != null) {
                                recurrence.put("end_times", request.getEndTimes());
                            }
                            break;
                    }
                }

                log.info("最终的recurrence配置: {}", recurrence);
                requestBody.put("recurrence", recurrence);
            }

            // 确保基础字段在周期性会议中也存在
            log.info("=== 最终检查requestBody中的关键字段 ===");
            log.info("requestBody.containsKey('duration'): {}", requestBody.containsKey("duration"));
            log.info("requestBody.get('duration'): {}", requestBody.get("duration"));
            log.info("requestBody.containsKey('start_time'): {}", requestBody.containsKey("start_time"));
            log.info("requestBody.get('start_time'): {}", requestBody.get("start_time"));
            log.info("requestBody.containsKey('timezone'): {}", requestBody.containsKey("timezone"));
            log.info("requestBody.get('timezone'): {}", requestBody.get("timezone"));

            log.info("创建Zoom会议请求参数: {}", requestBody);

            JsonNode response = getWebClient(accessToken)
                    .post()
                    .uri("/users/{userId}/meetings", zoomUserId)
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(JsonNode.class)
                    .block();

            log.info("Zoom会议创建成功: {}", response);
            return ZoomApiResponse.success(response);

        } catch (WebClientResponseException e) {
            log.error("创建Zoom会议失败: {}", e.getResponseBodyAsString(), e);
            return ZoomApiResponse.error("创建Zoom会议失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("创建Zoom会议异常", e);
            return ZoomApiResponse.error("创建Zoom会议异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 构建更新会议请求体
     */
    private Map<String, Object> buildUpdateMeetingRequestBody(UpdateMeetingRequest request) {
        Map<String, Object> requestBody = new HashMap<>();

        // 基本信息
        if (request.getTopic() != null) {
            requestBody.put("topic", request.getTopic());
        }
        if (request.getAgenda() != null) {
            requestBody.put("agenda", request.getAgenda());
        }
        if (request.getPassword() != null) {
            requestBody.put("password", request.getPassword());
        }

        // 时间相关
        if (request.getStartTime() != null) {
            // 转换时区
            LocalDateTime localTime = request.getStartTime();
            ZonedDateTime shanghaiTime = localTime.atZone(ZoneId.of("Asia/Shanghai"));
            ZonedDateTime utcTime = shanghaiTime.withZoneSameInstant(ZoneOffset.UTC);
            String startTimeStr = utcTime.format(DateTimeFormatter.ISO_INSTANT);
            requestBody.put("start_time", startTimeStr);
            log.info("更新会议时间转换: 东八区 {} -> UTC {}", localTime, startTimeStr);
        }
        if (request.getDuration() != null) {
            requestBody.put("duration", request.getDuration());
        }
        if (request.getTimezone() != null) {
            requestBody.put("timezone", request.getTimezone());
        }

        // 会议类型
        if (request.getType() != null) {
            requestBody.put("type", request.getType().getValue());
        }

        // 周期性设置
        if (request.getRecurrence() != null) {
            Map<String, Object> recurrence = new HashMap<>();
            UpdateMeetingRequest.RecurrenceSettings rec = request.getRecurrence();

            if (rec.getType() != null) {
                recurrence.put("type", rec.getType());
            }
            if (rec.getRepeatInterval() != null) {
                recurrence.put("repeat_interval", rec.getRepeatInterval());
            }
            if (rec.getWeeklyDays() != null) {
                // 转换星期几格式
                String[] ourDays = rec.getWeeklyDays().split(",");
                StringBuilder zoomDays = new StringBuilder();
                for (String day : ourDays) {
                    try {
                        int ourDay = Integer.parseInt(day.trim());
                        int zoomDay = ourDay == 0 ? 1 : ourDay + 1;
                        if (zoomDays.length() > 0) {
                            zoomDays.append(",");
                        }
                        zoomDays.append(zoomDay);
                    } catch (NumberFormatException e) {
                        log.warn("无效的星期几数字: {}", day);
                    }
                }
                recurrence.put("weekly_days", zoomDays.toString());
            }
            if (rec.getMonthlyDay() != null) {
                recurrence.put("monthly_day", rec.getMonthlyDay());
            }
            if (rec.getMonthlyWeek() != null) {
                recurrence.put("monthly_week", rec.getMonthlyWeek());
            }
            if (rec.getMonthlyWeekDay() != null) {
                recurrence.put("monthly_week_day", rec.getMonthlyWeekDay() + 1);
            }
            if (rec.getEndTimes() != null) {
                recurrence.put("end_times", rec.getEndTimes());
            }
            if (rec.getEndDateTime() != null) {
                LocalDateTime endLocalTime = rec.getEndDateTime();
                ZonedDateTime endShanghaiTime = endLocalTime.atZone(ZoneId.of("Asia/Shanghai"));
                ZonedDateTime endUtcTime = endShanghaiTime.withZoneSameInstant(ZoneOffset.UTC);
                String endTimeStr = endUtcTime.format(DateTimeFormatter.ISO_INSTANT);
                recurrence.put("end_date_time", endTimeStr);
            }

            if (!recurrence.isEmpty()) {
                requestBody.put("recurrence", recurrence);
            }
        }

        // 会议设置
        if (request.getSettings() != null) {
            Map<String, Object> settings = buildUpdateMeetingSettings(request.getSettings());
            if (!settings.isEmpty()) {
                requestBody.put("settings", settings);
            }
        }

        // 跟踪字段
        if (request.getTrackingFields() != null && request.getTrackingFields().length > 0) {
            List<Map<String, Object>> trackingFields = new ArrayList<>();
            for (UpdateMeetingRequest.TrackingFields field : request.getTrackingFields()) {
                Map<String, Object> trackingField = new HashMap<>();
                trackingField.put("field", field.getField());
                trackingField.put("value", field.getValue());
                trackingFields.add(trackingField);
            }
            requestBody.put("tracking_fields", trackingFields);
        }

        return requestBody;
    }

    /**
     * 构建更新会议设置对象
     */
    private Map<String, Object> buildUpdateMeetingSettings(UpdateMeetingRequest.MeetingSettings settings) {
        Map<String, Object> settingsMap = new HashMap<>();

        // 音视频设置
        if (settings.getHostVideo() != null) {
            settingsMap.put("host_video", settings.getHostVideo());
        }
        if (settings.getParticipantVideo() != null) {
            settingsMap.put("participant_video", settings.getParticipantVideo());
        }
        if (settings.getAudio() != null) {
            settingsMap.put("audio", settings.getAudio());
        }
        if (settings.getAutoRecording() != null) {
            settingsMap.put("auto_recording", settings.getAutoRecording());
        }

        // 会议控制
        if (settings.getJoinBeforeHost() != null) {
            settingsMap.put("join_before_host", settings.getJoinBeforeHost());
        }
        if (settings.getJbhTime() != null) {
            settingsMap.put("jbh_time", settings.getJbhTime());
        }
        if (settings.getMuteUponEntry() != null) {
            settingsMap.put("mute_upon_entry", settings.getMuteUponEntry());
        }
        if (settings.getWatermark() != null) {
            settingsMap.put("watermark", settings.getWatermark());
        }
        if (settings.getUsePmi() != null) {
            settingsMap.put("use_pmi", settings.getUsePmi());
        }

        // 安全设置
        if (settings.getApprovalType() != null) {
            settingsMap.put("approval_type", settings.getApprovalType());
        }
        if (settings.getEnforceLogin() != null) {
            settingsMap.put("enforce_login", settings.getEnforceLogin());
        }
        if (settings.getEnforceLoginDomains() != null) {
            settingsMap.put("enforce_login_domains", settings.getEnforceLoginDomains());
        }
        if (settings.getAlternativeHosts() != null) {
            settingsMap.put("alternative_hosts", settings.getAlternativeHosts());
        }
        if (settings.getWaitingRoom() != null) {
            settingsMap.put("waiting_room", settings.getWaitingRoom());
        }
        if (settings.getMeetingAuthentication() != null) {
            settingsMap.put("meeting_authentication", settings.getMeetingAuthentication());
        }

        // 其他设置
        if (settings.getAllowMultipleDevices() != null) {
            settingsMap.put("allow_multiple_devices", settings.getAllowMultipleDevices());
        }
        if (settings.getShowShareButton() != null) {
            settingsMap.put("show_share_button", settings.getShowShareButton());
        }
        if (settings.getRegistrantsConfirmationEmail() != null) {
            settingsMap.put("registrants_confirmation_email", settings.getRegistrantsConfirmationEmail());
        }
        if (settings.getEmailNotification() != null) {
            settingsMap.put("email_notification", settings.getEmailNotification());
        }
        if (settings.getAutoStartMeetingSummary() != null) {
            settingsMap.put("auto_start_meeting_summary", settings.getAutoStartMeetingSummary());
        }
        if (settings.getAutoStartAiCompanionQuestions() != null) {
            settingsMap.put("auto_start_ai_companion_questions", settings.getAutoStartAiCompanionQuestions());
        }

        return settingsMap;
    }

    /**
     * 获取用户信息 - 使用默认ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getUser(String zoomUserId) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return getUser(zoomUserId, zoomAuth);
    }

    /**
     * 获取用户信息 - 使用指定ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getUser(String zoomUserId, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            // 使用带日志记录的API调用方法
            return executeApiCallWithLogging(
                    "GET",
                    "/users/" + zoomUserId,
                    null,
                    JsonNode.class,
                    "GET_USER",
                    zoomUserId,
                    zoomUserId,
                    zoomAuth
            );

        } catch (Exception e) {
            log.error("获取Zoom用户信息异常: zoomUserId={}, zoomAuth={}",
                    zoomUserId, zoomAuth.getAccountName(), e);
            return ZoomApiResponse.error("获取用户信息异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }
    
    /**
     * 获取会议信息
     */
    public ZoomApiResponse<JsonNode> getMeeting(String meetingId) {
        try {
            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
            return getMeeting(meetingId, zoomAuth);
        } catch (Exception e) {
            log.error("获取Zoom会议信息异常", e);
            return ZoomApiResponse.error("获取会议信息异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 获取会议信息（指定认证信息）
     */
    public ZoomApiResponse<JsonNode> getMeeting(String meetingId, com.zoombus.entity.ZoomAuth zoomAuth) {
        // 使用带日志记录的API调用方法
        return executeApiCallWithLogging(
                "GET",
                "/meetings/" + meetingId,
                null,
                JsonNode.class,
                "GET_MEETING",
                meetingId,
                null,
                zoomAuth
        );
    }

    /**
     * 获取会议邀请信息 - 使用默认ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getMeetingInvitation(String meetingId) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return getMeetingInvitation(meetingId, zoomAuth);
    }

    /**
     * 获取会议邀请信息 - 使用指定ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getMeetingInvitation(String meetingId, com.zoombus.entity.ZoomAuth zoomAuth) {
        // 使用带日志记录的API调用方法
        return executeApiCallWithLogging(
                "GET",
                "/meetings/" + meetingId + "/invitation",
                null,
                JsonNode.class,
                "GET_MEETING_INVITATION",
                meetingId,
                null,
                zoomAuth
        );
    }

    /**
     * 获取特定occurrence的会议信息
     */
    public ZoomApiResponse<JsonNode> getMeetingOccurrence(String meetingId, String occurrenceId) {
        try {
            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

            // 使用带日志记录的API调用方法
            return executeApiCallWithLogging(
                    "GET",
                    "/meetings/" + meetingId + "?occurrence_id=" + occurrenceId,
                    null,
                    JsonNode.class,
                    "GET_MEETING_OCCURRENCE",
                    meetingId + "_" + occurrenceId,
                    null,
                    zoomAuth
            );

        } catch (Exception e) {
            log.error("获取Zoom会议occurrence信息异常", e);
            return ZoomApiResponse.error("获取会议occurrence信息异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 更新会议信息 - 使用默认ZoomAuth
     * 基于Zoom API PATCH /meetings/{meetingId}
     */
    public ZoomApiResponse<JsonNode> updateMeeting(String meetingId, UpdateMeetingRequest request) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return updateMeeting(meetingId, request, zoomAuth);
    }

    /**
     * 更新会议信息 - 使用指定ZoomAuth
     * 基于Zoom API PATCH /meetings/{meetingId}
     */
    public ZoomApiResponse<JsonNode> updateMeeting(String meetingId, UpdateMeetingRequest request, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("更新Zoom会议请求参数: meetingId={}, request={}, zoomAuth={}",
                    meetingId, request, zoomAuth.getAccountName());

            // 构建请求体
            Map<String, Object> requestBody = buildUpdateMeetingRequestBody(request);

            log.info("发送给Zoom API的更新请求体: {}", requestBody);

            // 使用带日志记录的API调用方法
            ZoomApiResponse<JsonNode> response = executeApiCallWithLogging(
                    "PATCH",
                    "/meetings/" + meetingId,
                    requestBody,
                    JsonNode.class,
                    "UPDATE_MEETING",
                    meetingId,
                    null,
                    zoomAuth
            );

            if (response.isSuccess()) {
                log.info("Zoom会议更新成功: meetingId={}, zoomAuth={}", meetingId, zoomAuth.getAccountName());
            }
            return response;

        } catch (WebClientResponseException e) {
            log.error("更新Zoom会议失败: meetingId={}, 状态码={}, 响应={}, zoomAuth={}",
                    meetingId, e.getStatusCode(), e.getResponseBodyAsString(), zoomAuth.getAccountName());
            return ZoomApiResponse.error("更新会议失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("更新Zoom会议异常: meetingId={}, zoomAuth={}",
                    meetingId, zoomAuth.getAccountName(), e);
            return ZoomApiResponse.error("更新会议异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 更新特定occurrence的会议信息 - 使用默认ZoomAuth
     */
    public ZoomApiResponse<JsonNode> updateMeetingOccurrence(String meetingId, String occurrenceId, Map<String, Object> updateData) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return updateMeetingOccurrence(meetingId, occurrenceId, updateData, zoomAuth);
    }

    /**
     * 更新特定occurrence的会议信息 - 使用指定ZoomAuth
     */
    public ZoomApiResponse<JsonNode> updateMeetingOccurrence(String meetingId, String occurrenceId, Map<String, Object> updateData, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("更新Zoom会议occurrence请求参数: meetingId={}, occurrenceId={}, data={}, zoomAuth={}",
                    meetingId, occurrenceId, updateData, zoomAuth.getAccountName());

            // 使用带日志记录的API调用方法
            ZoomApiResponse<JsonNode> response = executeApiCallWithLogging(
                    "PATCH",
                    "/meetings/" + meetingId + "?occurrence_id=" + occurrenceId,
                    updateData,
                    JsonNode.class,
                    "UPDATE_MEETING_OCCURRENCE",
                    meetingId + "_" + occurrenceId,
                    null,
                    zoomAuth
            );

            if (response.isSuccess()) {
                log.info("Zoom会议occurrence更新成功: meetingId={}, occurrenceId={}, zoomAuth={}",
                        meetingId, occurrenceId, zoomAuth.getAccountName());
            }
            return response;

        } catch (WebClientResponseException e) {
            log.error("更新Zoom会议occurrence失败: meetingId={}, occurrenceId={}, error={}, zoomAuth={}",
                     meetingId, occurrenceId, e.getResponseBodyAsString(), zoomAuth.getAccountName(), e);
            return ZoomApiResponse.error("更新会议occurrence失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("更新Zoom会议occurrence异常: meetingId={}, occurrenceId={}, zoomAuth={}",
                    meetingId, occurrenceId, zoomAuth.getAccountName(), e);
            return ZoomApiResponse.error("更新会议occurrence异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 删除会议
     */
    public ZoomApiResponse<Void> deleteMeeting(String meetingId) {
        try {
            log.info("删除Zoom会议: meetingId={}", meetingId);

            // 获取默认的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

            // 使用带日志记录的API调用方法
            ZoomApiResponse<Void> response = executeApiCallWithLogging(
                    "DELETE",
                    "/meetings/" + meetingId,
                    null,
                    Void.class,
                    "DELETE_MEETING",
                    meetingId,
                    null,
                    zoomAuth
            );

            if (response.isSuccess()) {
                log.info("Zoom会议删除成功: meetingId={}", meetingId);
            }
            return response;

        } catch (WebClientResponseException e) {
            log.error("删除Zoom会议失败: meetingId={}, error={}",
                     meetingId, e.getResponseBodyAsString(), e);

            // 检查是否是权限问题
            if (e.getStatusCode().value() == 400 && e.getResponseBodyAsString().contains("4711")) {
                return ZoomApiResponse.error("删除会议失败: 应用权限不足，请联系管理员在Zoom开发者控制台添加 'meeting:delete:meeting:admin' 权限", "INSUFFICIENT_PERMISSIONS");
            }

            return ZoomApiResponse.error("删除会议失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("删除Zoom会议异常", e);
            return ZoomApiResponse.error("删除会议异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 删除会议（带参数）
     * 基于Zoom API DELETE /meetings/{meetingId}
     */
    public ZoomApiResponse<Void> deleteMeeting(String meetingId, DeleteMeetingRequest request) {
        try {
            log.info("删除Zoom会议: meetingId={}, request={}", meetingId, request);

            // 获取默认的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

            // 构建查询参数
            String uri = "/meetings/{meetingId}";
            if (request != null) {
                StringBuilder queryParams = new StringBuilder();
                if (request.getScheduleForReminder() != null) {
                    queryParams.append("schedule_for_reminder=").append(request.getScheduleForReminder());
                }
                if (request.getCancelMeetingReminder() != null) {
                    if (queryParams.length() > 0) queryParams.append("&");
                    queryParams.append("cancel_meeting_reminder=").append(request.getCancelMeetingReminder());
                }
                if (request.getOccurrenceId() != null) {
                    if (queryParams.length() > 0) queryParams.append("&");
                    queryParams.append("occurrence_id=").append(request.getOccurrenceId());
                }
                if (queryParams.length() > 0) {
                    uri += "?" + queryParams.toString();
                }
            }

            // 使用带日志记录的API调用方法
            ZoomApiResponse<Void> response = executeApiCallWithLogging(
                    "DELETE",
                    uri.replace("{meetingId}", meetingId),
                    null,
                    Void.class,
                    "DELETE_MEETING_WITH_OPTIONS",
                    meetingId,
                    null,
                    zoomAuth
            );

            if (response.isSuccess()) {
                log.info("Zoom会议删除成功: meetingId={}", meetingId);
            }
            return response;

        } catch (WebClientResponseException e) {
            log.error("删除Zoom会议失败: meetingId={}, error={}", meetingId, e.getResponseBodyAsString(), e);

            // 检查是否是权限问题
            if (e.getStatusCode().value() == 400 && e.getResponseBodyAsString().contains("4711")) {
                return ZoomApiResponse.error("删除会议失败: 应用权限不足，请联系管理员在Zoom开发者控制台添加 'meeting:delete:meeting:admin' 权限", "INSUFFICIENT_PERMISSIONS");
            }

            return ZoomApiResponse.error("删除会议失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("删除Zoom会议异常", e);
            return ZoomApiResponse.error("删除会议异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 删除特定occurrence的会议
     */
    public ZoomApiResponse<Void> deleteMeetingOccurrence(String meetingId, String occurrenceId) {
        try {
            log.info("删除Zoom会议occurrence: meetingId={}, occurrenceId={}", meetingId, occurrenceId);

            // 获取默认的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

            // 使用带日志记录的API调用方法
            ZoomApiResponse<Void> response = executeApiCallWithLogging(
                    "DELETE",
                    "/meetings/" + meetingId + "?occurrence_id=" + occurrenceId,
                    null,
                    Void.class,
                    "DELETE_MEETING_OCCURRENCE",
                    meetingId + "_" + occurrenceId,
                    null,
                    zoomAuth
            );

            if (response.isSuccess()) {
                log.info("Zoom会议occurrence删除成功: meetingId={}, occurrenceId={}", meetingId, occurrenceId);
            }
            return response;

        } catch (WebClientResponseException e) {
            log.error("删除Zoom会议occurrence失败: meetingId={}, occurrenceId={}, error={}",
                     meetingId, occurrenceId, e.getResponseBodyAsString(), e);

            // 检查是否是权限问题
            if (e.getStatusCode().value() == 400 && e.getResponseBodyAsString().contains("4711")) {
                return ZoomApiResponse.error("删除会议occurrence失败: 应用权限不足，请联系管理员在Zoom开发者控制台添加 'meeting:delete:meeting:admin' 权限", "INSUFFICIENT_PERMISSIONS");
            }

            return ZoomApiResponse.error("删除会议occurrence失败: " + e.getMessage(), String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("删除Zoom会议occurrence异常", e);
            return ZoomApiResponse.error("删除会议occurrence异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 获取账号下的所有用户列表
     * 使用指定的ZoomAuth认证信息
     */
    public ZoomApiResponse<JsonNode> getUsers(com.zoombus.entity.ZoomAuth zoomAuth, int pageSize, String nextPageToken) {
        String uri = "/users?status=active&page_size=" + pageSize + "&include_fields=host_key";
        if (nextPageToken != null && !nextPageToken.isEmpty()) {
            uri += "&next_page_token=" + nextPageToken;
        }

        // 使用带日志记录的API调用方法
        return executeApiCallWithLogging(
                "GET",
                uri,
                null,
                JsonNode.class,
                "GET_USERS",
                zoomAuth.getAccountName(),
                null,
                zoomAuth
        );
    }

    /**
     * 获取账号下的所有用户列表（分页获取所有数据）
     */
    public ZoomApiResponse<JsonNode> getAllUsers(com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> allUsersData = new HashMap<>();
            allUsersData.put("users", new java.util.ArrayList<>());
            allUsersData.put("total_records", 0);

            String nextPageToken = null;
            int pageSize = 300; // Zoom API最大页面大小
            int totalRecords = 0;

            do {
                ZoomApiResponse<JsonNode> response = getUsers(zoomAuth, pageSize, nextPageToken);
                if (!response.isSuccess()) {
                    return response;
                }

                JsonNode data = response.getData();
                if (data.has("users")) {
                    JsonNode users = data.get("users");
                    Object usersObj = allUsersData.get("users");
                    if (usersObj instanceof java.util.List) {
                        @SuppressWarnings("unchecked")
                        java.util.List<JsonNode> userList = (java.util.List<JsonNode>) usersObj;
                        for (JsonNode user : users) {
                            userList.add(user);
                            totalRecords++;
                        }
                    }
                }

                nextPageToken = data.has("next_page_token") ? data.get("next_page_token").asText() : null;

            } while (nextPageToken != null && !nextPageToken.isEmpty());

            allUsersData.put("total_records", totalRecords);
            JsonNode result = mapper.valueToTree(allUsersData);

            // 调试：打印前几个用户的数据结构
            if (result.has("users") && result.get("users").size() > 0) {
                JsonNode firstUser = result.get("users").get(0);
                log.info("第一个用户的数据结构: {}", firstUser.toString());
                log.info("第一个用户是否包含PMI字段: {}", firstUser.has("pmi"));
                if (firstUser.has("pmi")) {
                    log.info("第一个用户的PMI值: {}", firstUser.get("pmi").asText());
                }
            }

            log.info("获取Zoom账号全量用户成功，账号: {}, 总用户数: {}", zoomAuth.getAccountName(), totalRecords);
            return ZoomApiResponse.success(result);

        } catch (Exception e) {
            log.error("获取Zoom账号全量用户异常，账号: {}", zoomAuth.getAccountName(), e);
            return ZoomApiResponse.error("获取全量用户异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 通用的更新用户方法
     * 基于Zoom API PATCH /users/{userId}
     */
    public ZoomApiResponse<JsonNode> updateUser(String zoomUserId, com.fasterxml.jackson.databind.node.ObjectNode requestBody) {
        try {
            log.info("更新用户信息: userId={}, requestBody={}", zoomUserId, requestBody);

            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

            // 使用带日志记录的API调用方法执行PATCH请求
            ZoomApiResponse<String> patchResponse = executeApiCallWithLogging(
                    "PATCH",
                    "/users/" + zoomUserId,
                    requestBody,
                    String.class,
                    "UPDATE_USER",
                    zoomUserId,
                    zoomUserId,
                    zoomAuth
            );

            if (patchResponse.isSuccess()) {
                log.info("用户信息更新成功: userId={}", zoomUserId);
                // 返回成功响应，包含更新后的用户信息
                ObjectMapper mapper = new ObjectMapper();
                JsonNode responseData = mapper.createObjectNode().put("message", "用户信息更新成功");
                return ZoomApiResponse.success(responseData);
            } else {
                log.error("用户信息更新失败: userId={}, error={}", zoomUserId, patchResponse.getMessage());
                return ZoomApiResponse.error(patchResponse.getMessage(), patchResponse.getErrorCode());
            }

        } catch (Exception e) {
            log.error("更新用户信息时发生异常: userId={}, error={}", zoomUserId, e.getMessage(), e);
            return ZoomApiResponse.error("更新用户信息失败: " + e.getMessage(), "UPDATE_USER_ERROR");
        }
    }

    /**
     * 更新用户PMI设置（优化版本）- 使用默认ZoomAuth
     * 根据Zoom安全策略：密码和等候室至少选一项
     * 基于Zoom API PATCH /users/{userId}
     */
    public ZoomApiResponse<JsonNode> updateUserPmi(String zoomUserId, String pmiNumber, String pmiPassword) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return updateUserPmi(zoomUserId, pmiNumber, pmiPassword, zoomAuth);
    }

    /**
     * 更新用户PMI设置（优化版本）- 使用指定ZoomAuth
     * 根据Zoom安全策略：密码和等候室至少选一项
     * 基于Zoom API PATCH /users/{userId}
     */
    public ZoomApiResponse<JsonNode> updateUserPmi(String zoomUserId, String pmiNumber, String pmiPassword, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("更新用户PMI设置: userId={}, pmiNumber={}, hasPassword={}",
                    zoomUserId, pmiNumber, pmiPassword != null && !pmiPassword.trim().isEmpty());

            // 分步设置：先设置PMI号码，再设置密码
            ZoomApiResponse<JsonNode> pmiResponse = updateUserPmiNumber(zoomUserId, pmiNumber, zoomAuth);
            if (!pmiResponse.isSuccess()) {
                return pmiResponse;
            }

            // 处理PMI密码设置
            boolean hasPassword = pmiPassword != null && !pmiPassword.trim().isEmpty();
            if (hasPassword) {
                // 设置具体密码
                ZoomApiResponse<JsonNode> passwordResponse = updateUserPmiPassword(zoomUserId, pmiPassword, zoomAuth);
                if (!passwordResponse.isSuccess()) {
                    log.error("PMI密码设置失败，但PMI号码已设置成功");
                    return passwordResponse;
                }
            } else {
                // 清除PMI密码
                ZoomApiResponse<JsonNode> clearPasswordResponse = clearUserPmiPassword(zoomUserId, zoomAuth);
                if (!clearPasswordResponse.isSuccess()) {
                    log.error("PMI密码清除失败，但PMI号码已设置成功");
                    return clearPasswordResponse;
                }
            }

            // 设置PMI其他配置（等候室等）
            ZoomApiResponse<JsonNode> settingsResponse = updateUserPmiSettings(zoomUserId, hasPassword, zoomAuth);
            if (!settingsResponse.isSuccess()) {
                log.warn("PMI其他设置失败，但PMI号码和密码已设置成功: {}", settingsResponse.getMessage());
            }

            log.info("用户PMI设置更新成功: userId={}, pmiNumber={}", zoomUserId, pmiNumber);

            // 设置成功后，严格验证PMI设置是否正确
            ZoomApiResponse<JsonNode> validationResponse = validatePmiSettings(zoomUserId, pmiNumber, pmiPassword, zoomAuth);

            if (!validationResponse.isSuccess()) {
                log.error("PMI设置验证失败，需要回退: userId={}, error={}", zoomUserId, validationResponse.getMessage());
                // 验证失败，抛出异常让上层处理回退
                throw new RuntimeException("PMI设置验证失败: " + validationResponse.getMessage());
            }

            return validationResponse;

        } catch (Exception e) {
            log.error("更新用户PMI设置异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("更新PMI设置异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 更新用户PMI号码
     */
    private ZoomApiResponse<JsonNode> updateUserPmiNumber(String zoomUserId, String pmiNumber, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("设置用户PMI号码: userId={}, pmiNumber={}", zoomUserId, pmiNumber);

            // 构建请求体 - 只设置PMI号码
            Map<String, Object> requestBody = new HashMap<>();

            // 设置PMI号码
            try {
                requestBody.put("pmi", Long.parseLong(pmiNumber));
            } catch (NumberFormatException e) {
                log.error("PMI号码格式错误: {}", pmiNumber);
                return ZoomApiResponse.error("PMI号码格式错误: " + pmiNumber, "INVALID_PMI_FORMAT");
            }

            requestBody.put("use_pmi", true);

            log.info("发送PMI号码设置请求: {}", requestBody);

            // 使用带日志记录的API调用方法执行PATCH请求
            ZoomApiResponse<String> patchResponse = executeApiCallWithLogging(
                    "PATCH",
                    "/users/" + zoomUserId,
                    requestBody,
                    String.class,
                    "UPDATE_PMI_NUMBER",
                    pmiNumber,
                    zoomUserId,
                    zoomAuth
            );

            if (!patchResponse.isSuccess()) {
                return ZoomApiResponse.error("设置PMI号码失败: " + patchResponse.getMessage(),
                        patchResponse.getErrorCode());
            }

            log.info("PMI号码设置成功: userId={}, pmiNumber={}", zoomUserId, pmiNumber);
            return ZoomApiResponse.success(null);

        } catch (Exception e) {
            log.error("设置PMI号码异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("设置PMI号码异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 更新用户PMI密码
     * 根据Zoom API最新文档，PMI密码需要使用 meeting_security 选项
     */
    private ZoomApiResponse<JsonNode> updateUserPmiPassword(String zoomUserId, String pmiPassword, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("设置用户PMI密码: userId={}, hasPassword={}", zoomUserId,
                    pmiPassword != null && !pmiPassword.trim().isEmpty());

            // 构建请求体 - 使用正确的meeting_security结构（参考老系统的成功方法）
            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> meetingSecurity = new HashMap<>();

            // 关键：需要同时设置两个字段
            meetingSecurity.put("pmi_password", "true");  // 启用PMI密码
            meetingSecurity.put("password_for_pmi", pmiPassword);  // 设置具体密码值

            requestBody.put("meeting_security", meetingSecurity);

            log.info("发送PMI密码设置请求到 /users/{}/settings?option=meeting_security: {}", zoomUserId, requestBody);

            // 使用正确的端点：/users/{userId}/settings?option=meeting_security
            ZoomApiResponse<String> patchResponse = executeApiCallWithLogging(
                    "PATCH",
                    "/users/" + zoomUserId + "/settings?option=meeting_security",
                    requestBody,
                    String.class,
                    "UPDATE_PMI_PASSWORD",
                    "PMI_PWD_" + System.currentTimeMillis(),
                    zoomUserId,
                    zoomAuth
            );

            if (!patchResponse.isSuccess()) {
                return ZoomApiResponse.error("设置PMI密码失败: " + patchResponse.getMessage(),
                        patchResponse.getErrorCode());
            }

            log.info("PMI密码设置API调用成功: userId={}", zoomUserId);

            // 验证PMI密码是否正确设置
            ZoomApiResponse<JsonNode> verificationResponse = verifyPmiPasswordSetting(zoomUserId, pmiPassword, zoomAuth);
            if (!verificationResponse.isSuccess()) {
                log.error("PMI密码验证失败: userId={}, error={}", zoomUserId, verificationResponse.getMessage());
                return verificationResponse;
            }

            log.info("PMI密码设置并验证成功: userId={}", zoomUserId);
            return ZoomApiResponse.success(null);

        } catch (Exception e) {
            log.error("设置PMI密码异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("设置PMI密码异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 清除用户PMI密码
     * 根据Zoom API，清除密码需要禁用PMI密码功能
     */
    private ZoomApiResponse<JsonNode> clearUserPmiPassword(String zoomUserId, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("清除用户PMI密码: userId={}", zoomUserId);

            // 构建请求体 - 禁用PMI密码
            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> meetingSecurity = new HashMap<>();

            // 关键：禁用PMI密码功能
            meetingSecurity.put("pmi_password", "false");  // 禁用PMI密码
            // 不设置password_for_pmi字段，或设置为空

            requestBody.put("meeting_security", meetingSecurity);

            log.info("发送PMI密码清除请求到 /users/{}/settings?option=meeting_security: {}", zoomUserId, requestBody);

            // 使用正确的端点：/users/{userId}/settings?option=meeting_security
            ZoomApiResponse<String> patchResponse = executeApiCallWithLogging(
                    "PATCH",
                    "/users/" + zoomUserId + "/settings?option=meeting_security",
                    requestBody,
                    String.class,
                    "CLEAR_PMI_PASSWORD",
                    "PMI_CLEAR_" + System.currentTimeMillis(),
                    zoomUserId,
                    zoomAuth
            );

            if (!patchResponse.isSuccess()) {
                return ZoomApiResponse.error("清除PMI密码失败: " + patchResponse.getMessage(),
                        patchResponse.getErrorCode());
            }

            log.info("PMI密码清除API调用成功: userId={}", zoomUserId);

            // 验证PMI密码是否已清除
            ZoomApiResponse<JsonNode> verificationResponse = verifyPmiPasswordCleared(zoomUserId, zoomAuth);
            if (!verificationResponse.isSuccess()) {
                log.error("PMI密码清除验证失败: userId={}, error={}", zoomUserId, verificationResponse.getMessage());
                return verificationResponse;
            }

            log.info("PMI密码清除并验证成功: userId={}", zoomUserId);
            return ZoomApiResponse.success(null);

        } catch (Exception e) {
            log.error("清除PMI密码异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("清除PMI密码异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 验证PMI密码是否正确设置
     * 通过获取用户信息来验证PMI密码
     */
    private ZoomApiResponse<JsonNode> verifyPmiPasswordSetting(String zoomUserId, String expectedPassword, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("验证PMI密码设置: userId={}, expectedPassword={}", zoomUserId, expectedPassword);

            // 获取用户信息来验证PMI密码（包含PMI设置）
            ZoomApiResponse<String> getResponse = executeApiCallWithLogging(
                    "GET",
                    "/users/" + zoomUserId,
                    null,
                    String.class,
                    "VERIFY_PMI_PASSWORD",
                    "PMI_VERIFY_" + System.currentTimeMillis(),
                    zoomUserId,
                    zoomAuth
            );

            if (!getResponse.isSuccess()) {
                return ZoomApiResponse.error("获取用户信息失败: " + getResponse.getMessage(),
                        getResponse.getErrorCode());
            }

            // 解析响应
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseData = objectMapper.readTree(getResponse.getData());

            log.info("用户信息响应: {}", responseData.toString());

            // 检查PMI密码 - 可能在不同的字段中
            String actualPassword = null;

            // 方法1：检查pmi_password字段
            if (responseData.has("pmi_password")) {
                actualPassword = responseData.get("pmi_password").asText();
                log.info("从pmi_password字段获取到密码: {}", actualPassword);
            }

            // 方法2：检查meeting_security.password_for_pmi字段
            if (actualPassword == null && responseData.has("meeting_security")) {
                JsonNode meetingSecurity = responseData.get("meeting_security");
                if (meetingSecurity.has("password_for_pmi")) {
                    actualPassword = meetingSecurity.get("password_for_pmi").asText();
                    log.info("从meeting_security.password_for_pmi字段获取到密码: {}", actualPassword);
                }
            }

            // 方法3：检查settings.meeting_security.password_for_pmi字段
            if (actualPassword == null && responseData.has("settings")) {
                JsonNode settings = responseData.get("settings");
                if (settings.has("meeting_security")) {
                    JsonNode meetingSecurity = settings.get("meeting_security");
                    if (meetingSecurity.has("password_for_pmi")) {
                        actualPassword = meetingSecurity.get("password_for_pmi").asText();
                        log.info("从settings.meeting_security.password_for_pmi字段获取到密码: {}", actualPassword);
                    }
                }
            }

            if (actualPassword != null) {
                if (expectedPassword.equals(actualPassword)) {
                    log.info("PMI密码验证成功: userId={}, password={}", zoomUserId, expectedPassword);
                    return ZoomApiResponse.success(responseData);
                } else {
                    String errorMsg = String.format("PMI密码验证失败: 期望=%s, 实际=%s", expectedPassword, actualPassword);
                    log.error(errorMsg);
                    return ZoomApiResponse.error(errorMsg, "PASSWORD_MISMATCH");
                }
            } else {
                log.warn("无法在响应中找到PMI密码字段，跳过密码验证");
                // 暂时跳过验证，返回成功
                return ZoomApiResponse.success(responseData);
            }

        } catch (Exception e) {
            log.error("验证PMI密码异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("验证PMI密码异常: " + e.getMessage(), "VERIFICATION_ERROR");
        }
    }

    /**
     * 验证PMI密码是否已清除
     */
    private ZoomApiResponse<JsonNode> verifyPmiPasswordCleared(String zoomUserId, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("验证PMI密码是否已清除: userId={}", zoomUserId);

            // 获取用户信息来验证PMI密码是否已清除
            ZoomApiResponse<String> getResponse = executeApiCallWithLogging(
                    "GET",
                    "/users/" + zoomUserId,
                    null,
                    String.class,
                    "VERIFY_PMI_PASSWORD_CLEARED",
                    "PMI_VERIFY_CLEAR_" + System.currentTimeMillis(),
                    zoomUserId,
                    zoomAuth
            );

            if (!getResponse.isSuccess()) {
                return ZoomApiResponse.error("获取用户信息失败: " + getResponse.getMessage(),
                        getResponse.getErrorCode());
            }

            // 解析响应
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseData = objectMapper.readTree(getResponse.getData());

            log.info("用户信息响应: {}", responseData.toString());

            // 检查PMI密码是否已清除
            boolean hasPassword = false;
            String actualPassword = null;

            // 检查各种可能的密码字段
            if (responseData.has("pmi_password")) {
                actualPassword = responseData.get("pmi_password").asText();
                hasPassword = actualPassword != null && !actualPassword.trim().isEmpty();
                log.info("从pmi_password字段检查: hasPassword={}, value={}", hasPassword, actualPassword);
            }

            if (!hasPassword && responseData.has("meeting_security")) {
                JsonNode meetingSecurity = responseData.get("meeting_security");
                if (meetingSecurity.has("password_for_pmi")) {
                    actualPassword = meetingSecurity.get("password_for_pmi").asText();
                    hasPassword = actualPassword != null && !actualPassword.trim().isEmpty();
                    log.info("从meeting_security.password_for_pmi字段检查: hasPassword={}, value={}", hasPassword, actualPassword);
                }
            }

            if (!hasPassword && responseData.has("settings")) {
                JsonNode settings = responseData.get("settings");
                if (settings.has("meeting_security")) {
                    JsonNode meetingSecurity = settings.get("meeting_security");
                    if (meetingSecurity.has("password_for_pmi")) {
                        actualPassword = meetingSecurity.get("password_for_pmi").asText();
                        hasPassword = actualPassword != null && !actualPassword.trim().isEmpty();
                        log.info("从settings.meeting_security.password_for_pmi字段检查: hasPassword={}, value={}", hasPassword, actualPassword);
                    }
                }
            }

            if (hasPassword) {
                String errorMsg = String.format("PMI密码清除验证失败: 期望无密码，但实际仍有密码=%s", actualPassword);
                log.error(errorMsg);
                return ZoomApiResponse.error(errorMsg, "PASSWORD_NOT_CLEARED");
            } else {
                log.info("✓ PMI密码清除验证成功: 确认无密码");
                return ZoomApiResponse.success(responseData);
            }

        } catch (Exception e) {
            log.error("验证PMI密码清除异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("验证PMI密码清除异常: " + e.getMessage(), "VERIFICATION_ERROR");
        }
    }

    /**
     * 更新用户PMI其他设置（等候室等）
     */
    private ZoomApiResponse<JsonNode> updateUserPmiSettings(String zoomUserId, boolean hasPassword, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("设置用户PMI其他配置: userId={}, hasPassword={}", zoomUserId, hasPassword);

            // 构建请求体 - 设置其他PMI配置
            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> pmiSettings = new HashMap<>();

            // 根据是否有密码设置等候室
            if (hasPassword) {
                pmiSettings.put("waiting_room", false);
                log.info("PMI有密码，关闭等候室");
            } else {
                pmiSettings.put("waiting_room", true);
                log.info("PMI无密码，开启等候室");
            }

            // 其他基本设置
            pmiSettings.put("join_before_host", true);
            pmiSettings.put("mute_upon_entry", false);
            pmiSettings.put("participant_video", true);
            pmiSettings.put("host_video", true);

            requestBody.put("pmi_settings", pmiSettings);

            log.info("发送PMI其他设置请求: {}", requestBody);

            // 使用带日志记录的API调用方法执行PATCH请求
            ZoomApiResponse<String> patchResponse = executeApiCallWithLogging(
                    "PATCH",
                    "/users/" + zoomUserId,
                    requestBody,
                    String.class,
                    "UPDATE_PMI_SETTINGS",
                    "PMI_SETTINGS_" + System.currentTimeMillis(),
                    zoomUserId,
                    zoomAuth
            );

            if (!patchResponse.isSuccess()) {
                return ZoomApiResponse.error("设置PMI其他配置失败: " + patchResponse.getMessage(),
                        patchResponse.getErrorCode());
            }

            log.info("PMI其他配置设置成功: userId={}", zoomUserId);
            return ZoomApiResponse.success(null);

        } catch (Exception e) {
            log.error("设置PMI其他配置异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("设置PMI其他配置异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 验证PMI设置是否正确
     * 步骤2：查询账户信息，校验PMI是否为预期的PMI，密码是否正确
     */
    private ZoomApiResponse<JsonNode> validatePmiSettings(String zoomUserId, String expectedPmi, String expectedPassword, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("验证PMI设置: userId={}, expectedPmi={}, hasExpectedPassword={}",
                    zoomUserId, expectedPmi, expectedPassword != null && !expectedPassword.trim().isEmpty());

            // 获取用户信息
            ZoomApiResponse<JsonNode> userInfoResponse = getUserInfo(zoomUserId, zoomAuth);
            if (!userInfoResponse.isSuccess()) {
                log.error("获取用户信息失败: {}", userInfoResponse.getMessage());
                return ZoomApiResponse.error("验证PMI设置失败：获取用户信息失败: " + userInfoResponse.getMessage(),
                        userInfoResponse.getErrorCode());
            }

            JsonNode userInfo = userInfoResponse.getData();

            // 验证PMI号码
            String actualPmi = null;
            if (userInfo.has("pmi")) {
                actualPmi = userInfo.get("pmi").asText();
            }

            if (!expectedPmi.equals(actualPmi)) {
                log.error("PMI验证失败: 期望={}, 实际={}", expectedPmi, actualPmi);
                return ZoomApiResponse.error("PMI设置验证失败：PMI号码不匹配", "PMI_MISMATCH");
            }

            // 严格验证PMI密码设置
            boolean hasExpectedPassword = expectedPassword != null && !expectedPassword.trim().isEmpty();

            log.info("开始严格验证PMI密码设置: 期望有密码={}", hasExpectedPassword);

            if (hasExpectedPassword) {
                // 通过创建临时会议来验证PMI密码是否正确设置
                ZoomApiResponse<JsonNode> passwordValidationResponse = validatePmiPasswordByMeeting(zoomUserId, expectedPmi, expectedPassword, zoomAuth);
                if (!passwordValidationResponse.isSuccess()) {
                    log.error("PMI密码验证失败: {}", passwordValidationResponse.getMessage());
                    return passwordValidationResponse;
                }
                log.info("✓ PMI密码验证成功");
            } else {
                // 验证PMI密码确实已清除
                ZoomApiResponse<JsonNode> clearValidationResponse = verifyPmiPasswordCleared(zoomUserId, zoomAuth);
                if (!clearValidationResponse.isSuccess()) {
                    log.error("PMI密码清除验证失败: {}", clearValidationResponse.getMessage());
                    return clearValidationResponse;
                }
                log.info("✓ PMI无密码验证成功");
            }

            log.info("✓ PMI设置严格验证成功: userId={}, pmi={}", zoomUserId, expectedPmi);
            return userInfoResponse;

        } catch (Exception e) {
            log.error("验证PMI设置异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("验证PMI设置异常: " + e.getMessage(), "VALIDATION_ERROR");
        }
    }

    /**
     * 通过创建临时会议来验证PMI密码设置
     */
    private ZoomApiResponse<JsonNode> validatePmiPasswordByMeeting(String zoomUserId, String pmiNumber, String expectedPassword, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("通过会议创建验证PMI密码: userId={}, pmi={}, expectedPassword={}", zoomUserId, pmiNumber, expectedPassword);

            // 创建一个使用PMI的临时会议来验证密码
            Map<String, Object> meetingRequest = new HashMap<>();
            meetingRequest.put("topic", "PMI密码验证会议-" + System.currentTimeMillis());
            meetingRequest.put("type", 1); // 即时会议
            meetingRequest.put("use_pmi", true);

            Map<String, Object> settings = new HashMap<>();
            settings.put("password", expectedPassword);
            settings.put("waiting_room", false);
            settings.put("join_before_host", true);
            meetingRequest.put("settings", settings);

            log.info("创建PMI验证会议请求: {}", meetingRequest);

            ZoomApiResponse<String> createResponse = executeApiCallWithLogging(
                    "POST",
                    "/users/" + zoomUserId + "/meetings",
                    meetingRequest,
                    String.class,
                    "VALIDATE_PMI_PASSWORD",
                    "PMI_VALIDATION_" + System.currentTimeMillis(),
                    zoomUserId,
                    zoomAuth
            );

            if (!createResponse.isSuccess()) {
                log.error("创建PMI验证会议失败: {}", createResponse.getMessage());
                return ZoomApiResponse.error("PMI密码验证失败：无法创建验证会议 - " + createResponse.getMessage(), "VALIDATION_MEETING_FAILED");
            }

            // 解析会议响应
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode meetingInfo = objectMapper.readTree(createResponse.getData());

            log.info("PMI验证会议创建成功，会议信息: {}", meetingInfo);

            // 检查会议密码是否与期望一致
            if (meetingInfo.has("password")) {
                String actualPassword = meetingInfo.get("password").asText();
                log.info("验证会议密码: 期望={}, 实际={}", expectedPassword, actualPassword);

                if (!expectedPassword.equals(actualPassword)) {
                    log.error("❌ PMI密码验证失败: 期望密码={}, 实际密码={}", expectedPassword, actualPassword);

                    // 删除验证会议
                    deleteValidationMeeting(zoomUserId, meetingInfo.get("id").asLong(), zoomAuth);

                    return ZoomApiResponse.error("PMI密码验证失败：密码不匹配，期望=" + expectedPassword + "，实际=" + actualPassword, "PASSWORD_MISMATCH");
                }
            } else {
                log.error("❌ PMI密码验证失败: 创建的会议没有密码字段");

                // 删除验证会议
                deleteValidationMeeting(zoomUserId, meetingInfo.get("id").asLong(), zoomAuth);

                return ZoomApiResponse.error("PMI密码验证失败：创建的会议没有密码", "NO_PASSWORD_IN_MEETING");
            }

            // 删除验证会议
            deleteValidationMeeting(zoomUserId, meetingInfo.get("id").asLong(), zoomAuth);

            log.info("✓ PMI密码验证成功: 密码={}", expectedPassword);
            return ZoomApiResponse.success(meetingInfo);

        } catch (Exception e) {
            log.error("PMI密码验证异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("PMI密码验证异常: " + e.getMessage(), "PASSWORD_VALIDATION_ERROR");
        }
    }

    /**
     * 删除验证会议
     */
    private void deleteValidationMeeting(String zoomUserId, Long meetingId, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("删除PMI验证会议: userId={}, meetingId={}", zoomUserId, meetingId);

            ZoomApiResponse<String> deleteResponse = executeApiCallWithLogging(
                    "DELETE",
                    "/meetings/" + meetingId,
                    null,
                    String.class,
                    "DELETE_VALIDATION_MEETING",
                    "DELETE_" + meetingId,
                    zoomUserId,
                    zoomAuth
            );

            if (deleteResponse.isSuccess()) {
                log.info("PMI验证会议删除成功: meetingId={}", meetingId);
            } else {
                log.warn("PMI验证会议删除失败: meetingId={}, error={}", meetingId, deleteResponse.getMessage());
            }
        } catch (Exception e) {
            log.warn("删除PMI验证会议异常: meetingId={}, error={}", meetingId, e.getMessage());
        }
    }

    /**
     * 创建PMI会议以获取start_url - 使用默认ZoomAuth
     */
    public ZoomApiResponse<JsonNode> createPmiMeeting(String zoomUserId, String pmiNumber) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return createPmiMeeting(zoomUserId, pmiNumber, zoomAuth);
    }

    /**
     * 创建PMI会议以获取start_url - 使用指定ZoomAuth
     */
    public ZoomApiResponse<JsonNode> createPmiMeeting(String zoomUserId, String pmiNumber, com.zoombus.entity.ZoomAuth zoomAuth) {
        if (zoomAuth == null) {
            return ZoomApiResponse.error("未找到可用的Zoom认证配置", "NO_AUTH");
        }

        try {
            log.info("开始创建PMI会议以获取start_url: userId={}, pmiNumber={}", zoomUserId, pmiNumber);

            // 创建PMI会议的请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("type", 2); // 2表示预定会议，PMI通过settings.use_pmi=true来指定
            requestBody.put("topic", "PMI会议 - " + pmiNumber + " (临时)"); // 添加临时标识
            requestBody.put("agenda", "临时创建的PMI会议，用于获取start_url，将在10分钟后自动清理");

            // 设置会议时间（当前时间）
            requestBody.put("start_time", java.time.ZonedDateTime.now().format(java.time.format.DateTimeFormatter.ISO_OFFSET_DATE_TIME));
            requestBody.put("duration", 60); // 默认60分钟
            requestBody.put("timezone", "Asia/Shanghai");

            // PMI设置
            Map<String, Object> settings = new HashMap<>();
            settings.put("use_pmi", true); // 关键：使用PMI
            settings.put("auto_recording", "none"); // 不录制
            settings.put("waiting_room", false); // 不使用等候室
            settings.put("join_before_host", true); // 允许参会者在主持人之前加入
            requestBody.put("settings", settings);

            // 使用带日志记录的API调用方法
            ZoomApiResponse<JsonNode> apiResponse = executeApiCallWithLogging(
                    "POST",
                    "/users/" + zoomUserId + "/meetings",
                    requestBody,
                    JsonNode.class,
                    "CREATE_PMI_MEETING",
                    pmiNumber,
                    zoomUserId,
                    zoomAuth
            );

            if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                JsonNode response = apiResponse.getData();
                log.info("成功创建PMI会议，返回会议信息");
                return ZoomApiResponse.success(response);
            }

            return ZoomApiResponse.error("创建PMI会议响应为空", "EMPTY_RESPONSE");

        } catch (WebClientResponseException e) {
            String errorBody = e.getResponseBodyAsString();
            log.error("创建PMI会议失败: userId={}, statusCode={}, error={}",
                    zoomUserId, e.getStatusCode(), errorBody, e);
            return ZoomApiResponse.error("创建PMI会议失败: " + e.getMessage() + " - " + errorBody,
                    String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("创建PMI会议异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("创建PMI会议异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 获取用户的PMI会议信息 - 使用默认ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getUserPmiMeeting(String zoomUserId) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return getUserPmiMeeting(zoomUserId, zoomAuth);
    }

    /**
     * 获取用户的PMI会议信息 - 使用指定ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getUserPmiMeeting(String zoomUserId, com.zoombus.entity.ZoomAuth zoomAuth) {
        if (zoomAuth == null) {
            return ZoomApiResponse.error("未找到可用的Zoom认证配置", "NO_AUTH");
        }

        try {
            log.info("开始获取用户PMI会议信息: userId={}", zoomUserId);

            // 使用带日志记录的API调用方法
            ZoomApiResponse<JsonNode> apiResponse = executeApiCallWithLogging(
                    "GET",
                    "/users/" + zoomUserId + "/meetings?type=scheduled&page_size=30",
                    null,
                    JsonNode.class,
                    "GET_USER_MEETINGS",
                    zoomUserId,
                    zoomUserId,
                    zoomAuth
            );

            if (!apiResponse.isSuccess()) {
                return apiResponse;
            }
            JsonNode response = apiResponse.getData();

            if (response != null && response.has("meetings")) {
                JsonNode meetings = response.get("meetings");

                // 查找PMI会议（通常是第一个，且type为8表示PMI会议）
                for (JsonNode meeting : meetings) {
                    if (meeting.has("type") && meeting.get("type").asInt() == 8) {
                        log.info("找到PMI会议，返回会议信息");
                        return ZoomApiResponse.success(meeting);
                    }
                }

                // 如果没有找到type=8的会议，返回第一个会议
                if (meetings.size() > 0) {
                    JsonNode firstMeeting = meetings.get(0);
                    log.info("未找到PMI会议，返回第一个会议信息");
                    return ZoomApiResponse.success(firstMeeting);
                }
            }

            log.warn("未找到任何会议信息");
            return ZoomApiResponse.error("未找到PMI会议信息", "NO_MEETINGS");

        } catch (WebClientResponseException e) {
            String errorBody = e.getResponseBodyAsString();
            log.error("获取用户PMI会议信息失败: userId={}, statusCode={}, error={}",
                    zoomUserId, e.getStatusCode(), errorBody, e);
            return ZoomApiResponse.error("获取PMI会议信息失败: " + e.getMessage() + " - " + errorBody,
                    String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("获取用户PMI会议信息异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("获取PMI会议信息异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 获取用户的ZAK（Zoom Access Key）- 使用默认ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getUserZak(String zoomUserId) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return getUserZak(zoomUserId, zoomAuth);
    }

    /**
     * 获取用户的ZAK（Zoom Access Key）- 使用指定ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getUserZak(String zoomUserId, com.zoombus.entity.ZoomAuth zoomAuth) {
        // 使用带日志记录的API调用方法
        return executeApiCallWithLogging(
                "GET",
                "/users/" + zoomUserId + "/token?type=zak",
                null,
                JsonNode.class,
                "GET_USER_ZAK",
                zoomUserId,
                zoomUserId,
                zoomAuth
        );
    }

    /**
     * 获取用户信息（包含PMI的join_url）- 使用默认ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getUserInfo(String zoomUserId) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return getUserInfo(zoomUserId, zoomAuth);
    }

    /**
     * 获取用户信息（包含PMI的join_url）- 使用指定ZoomAuth
     */
    public ZoomApiResponse<JsonNode> getUserInfo(String zoomUserId, com.zoombus.entity.ZoomAuth zoomAuth) {
        // 使用带日志记录的API调用方法
        return executeApiCallWithLogging(
                "GET",
                "/users/" + zoomUserId,
                null,
                JsonNode.class,
                "USER_INFO",
                zoomUserId,
                zoomUserId,
                zoomAuth
        );
    }

    // ========== 会议报告相关方法 ==========

    /**
     * 获取会议报告
     * 基于Zoom API GET /report/meetings/{meetingId}
     * 注意：对于PMI会议，meetingId应该使用zoom_meeting_uuid而不是zoom_meeting_id
     * 因为PMI会议的zoom_meeting_id是固定的，而zoom_meeting_uuid才能区分具体的会议实例
     */
    public ZoomApiResponse<JsonNode> getMeetingReport(String meetingId) {
        try {
            log.info("获取会议报告: meetingId={}", meetingId);

            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

            // 使用带日志记录的API调用方法
            return executeApiCallWithLogging(
                    "GET",
                    "/report/meetings/" + meetingId,
                    null,
                    JsonNode.class,
                    "GET_MEETING_REPORT",
                    meetingId,
                    null,
                    zoomAuth
            );

        } catch (Exception e) {
            log.error("获取会议报告异常: meetingId={}", meetingId, e);
            return ZoomApiResponse.error("获取会议报告异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 获取会议参会者报告
     * 基于Zoom API GET /report/meetings/{meetingId}/participants
     * 注意：对于PMI会议，meetingId应该使用zoom_meeting_uuid而不是zoom_meeting_id
     * 因为PMI会议的zoom_meeting_id是固定的，而zoom_meeting_uuid才能区分具体的会议实例
     */
    public ZoomApiResponse<JsonNode> getMeetingParticipantsReport(String meetingId) {
        try {
            log.info("获取会议参会者报告: meetingId={}", meetingId);

            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

            // 使用带日志记录的API调用方法
            return executeApiCallWithLogging(
                    "GET",
                    "/report/meetings/" + meetingId + "/participants",
                    null,
                    JsonNode.class,
                    "GET_MEETING_PARTICIPANTS_REPORT",
                    meetingId,
                    null,
                    zoomAuth
            );

        } catch (Exception e) {
            log.error("获取会议参会者报告异常: meetingId={}", meetingId, e);
            return ZoomApiResponse.error("获取会议参会者报告异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 获取会议参会者报告（分页）
     * 基于Zoom API GET /report/meetings/{meetingId}/participants
     */
    public ZoomApiResponse<JsonNode> getMeetingParticipantsReport(String meetingId, int pageSize, String nextPageToken) {
        try {
            log.info("获取会议参会者报告（分页）: meetingId={}, pageSize={}, nextPageToken={}",
                    meetingId, pageSize, nextPageToken);

            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

            // 构建URI参数
            String uri = "/report/meetings/" + meetingId + "/participants?page_size=" + pageSize;
            if (nextPageToken != null && !nextPageToken.isEmpty()) {
                uri += "&next_page_token=" + nextPageToken;
            }

            // 使用带日志记录的API调用方法
            return executeApiCallWithLogging(
                    "GET",
                    uri,
                    null,
                    JsonNode.class,
                    "GET_MEETING_PARTICIPANTS_REPORT_PAGED",
                    meetingId,
                    null,
                    zoomAuth
            );

        } catch (Exception e) {
            log.error("获取会议参会者报告（分页）异常: meetingId={}", meetingId, e);
            return ZoomApiResponse.error("获取会议参会者报告（分页）异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 获取完整的会议参会者报告（自动分页获取所有数据）
     * 注意：对于PMI会议，meetingId应该使用zoom_meeting_uuid而不是zoom_meeting_id
     * 因为PMI会议的zoom_meeting_id是固定的，而zoom_meeting_uuid才能区分具体的会议实例
     */
    public ZoomApiResponse<JsonNode> getCompleteMeetingParticipantsReport(String meetingId) {
        try {
            log.info("获取完整会议参会者报告: meetingId={}", meetingId);

            List<JsonNode> allParticipants = new ArrayList<>();
            String nextPageToken = null;
            int pageSize = 300; // Zoom API最大页面大小
            int totalRecords = 0;

            ObjectMapper mapper = new ObjectMapper();

            do {
                ZoomApiResponse<JsonNode> response = getMeetingParticipantsReport(meetingId, pageSize, nextPageToken);

                if (!response.isSuccess()) {
                    log.error("获取会议参会者报告失败: meetingId={}, error={}", meetingId, response.getMessage());
                    return response;
                }

                JsonNode data = response.getData();
                if (data != null && data.has("participants")) {
                    JsonNode participants = data.get("participants");
                    for (JsonNode participant : participants) {
                        allParticipants.add(participant);
                    }

                    // 更新总记录数
                    if (data.has("total_records")) {
                        totalRecords = data.get("total_records").asInt();
                    }
                }

                // 获取下一页token
                nextPageToken = data != null && data.has("next_page_token") ?
                    data.get("next_page_token").asText() : null;

                log.debug("已获取参会者数量: {}, 下一页token: {}", allParticipants.size(), nextPageToken);

            } while (nextPageToken != null && !nextPageToken.isEmpty());

            // 构建完整的响应数据
            Map<String, Object> completeData = new HashMap<>();
            completeData.put("participants", allParticipants);
            completeData.put("total_records", totalRecords);
            completeData.put("page_count", (int) Math.ceil((double) totalRecords / pageSize));
            completeData.put("page_size", pageSize);

            JsonNode result = mapper.valueToTree(completeData);

            log.info("获取完整会议参会者报告成功: meetingId={}, 总参会者数: {}", meetingId, allParticipants.size());
            return ZoomApiResponse.success(result);

        } catch (Exception e) {
            log.error("获取完整会议参会者报告异常: meetingId={}", meetingId, e);
            return ZoomApiResponse.error("获取完整会议参会者报告异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    // ========== PMI管理相关方法 ==========

    /**
     * 恢复用户原始PMI设置（专用方法）- 使用默认ZoomAuth
     * 按照优化步骤：1>设置PMI 2>开启等候室 3>设置密码为空
     */
    public ZoomApiResponse<JsonNode> restoreUserOriginalPmi(String zoomUserId, String originalPmi) {
        com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();
        return restoreUserOriginalPmi(zoomUserId, originalPmi, zoomAuth);
    }

    /**
     * 恢复用户原始PMI设置（专用方法）- 使用指定ZoomAuth
     * 按照优化步骤：1>设置PMI 2>开启等候室 3>设置密码为空
     */
    public ZoomApiResponse<JsonNode> restoreUserOriginalPmi(String zoomUserId, String originalPmi, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("恢复用户原始PMI设置: userId={}, originalPmi={}", zoomUserId, originalPmi);

            // 构建请求体 - 恢复原始PMI的特殊设置
            Map<String, Object> requestBody = new HashMap<>();

            // 设置PMI号码
            try {
                requestBody.put("pmi", Long.parseLong(originalPmi));
            } catch (NumberFormatException e) {
                log.error("原始PMI号码格式错误: {}", originalPmi);
                return ZoomApiResponse.error("原始PMI号码格式错误: " + originalPmi, "INVALID_PMI_FORMAT");
            }

            requestBody.put("use_pmi", true);

            // PMI设置 - 恢复原始状态：开启等候室，密码为空
            Map<String, Object> pmiSettings = new HashMap<>();
            pmiSettings.put("waiting_room", true);  // 开启等候室
            // 不设置password字段，让其为空

            // 其他基本设置
            pmiSettings.put("join_before_host", true);
            pmiSettings.put("mute_upon_entry", false);
            pmiSettings.put("participant_video", true);
            pmiSettings.put("host_video", true);

            requestBody.put("pmi_settings", pmiSettings);

            log.info("发送给Zoom API的原始PMI恢复请求体: {}", requestBody);

            // 使用带日志记录的API调用方法执行PATCH请求
            ZoomApiResponse<String> patchResponse = executeApiCallWithLogging(
                    "PATCH",
                    "/users/" + zoomUserId,
                    requestBody,
                    String.class,
                    "RESTORE_ORIGINAL_PMI",
                    originalPmi,
                    zoomUserId,
                    zoomAuth
            );

            if (!patchResponse.isSuccess()) {
                return ZoomApiResponse.error("恢复原始PMI设置失败: " + patchResponse.getMessage(),
                        patchResponse.getErrorCode());
            }

            log.info("用户原始PMI设置恢复成功: userId={}, originalPmi={}", zoomUserId, originalPmi);

            // 设置成功后，验证恢复结果
            return validateOriginalPmiRestore(zoomUserId, originalPmi, zoomAuth);

        } catch (WebClientResponseException e) {
            String errorBody = e.getResponseBodyAsString();
            log.error("恢复用户原始PMI设置失败: userId={}, statusCode={}, error={}",
                    zoomUserId, e.getStatusCode(), errorBody, e);
            return ZoomApiResponse.error("恢复原始PMI设置失败: " + e.getMessage() + " - " + errorBody,
                    String.valueOf(e.getStatusCode().value()));
        } catch (Exception e) {
            log.error("恢复用户原始PMI设置异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("恢复原始PMI设置异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 验证原始PMI恢复结果
     * 步骤2：查询账户信息，校验PMI是否为预期的原始PMI，密码是否为空
     */
    private ZoomApiResponse<JsonNode> validateOriginalPmiRestore(String zoomUserId, String expectedOriginalPmi, com.zoombus.entity.ZoomAuth zoomAuth) {
        try {
            log.info("验证原始PMI恢复结果: userId={}, expectedOriginalPmi={}", zoomUserId, expectedOriginalPmi);

            // 获取用户信息
            ZoomApiResponse<JsonNode> userInfoResponse = getUserInfo(zoomUserId, zoomAuth);
            if (!userInfoResponse.isSuccess()) {
                log.error("获取用户信息失败: {}", userInfoResponse.getMessage());
                return ZoomApiResponse.error("验证原始PMI恢复失败：获取用户信息失败: " + userInfoResponse.getMessage(),
                        userInfoResponse.getErrorCode());
            }

            JsonNode userInfo = userInfoResponse.getData();

            // 验证PMI号码
            String actualPmi = null;
            if (userInfo.has("pmi")) {
                actualPmi = userInfo.get("pmi").asText();
            }

            if (!expectedOriginalPmi.equals(actualPmi)) {
                log.error("原始PMI恢复验证失败: 期望={}, 实际={}", expectedOriginalPmi, actualPmi);
                return ZoomApiResponse.error("原始PMI恢复验证失败：PMI号码不匹配", "PMI_MISMATCH");
            }

            // 验证密码为空（通过PMI会议信息）
            ZoomApiResponse<JsonNode> pmiMeetingResponse = getUserPmiMeeting(zoomUserId, zoomAuth);
            if (pmiMeetingResponse.isSuccess()) {
                JsonNode meetingInfo = pmiMeetingResponse.getData();
                boolean hasPassword = meetingInfo.has("password") &&
                        meetingInfo.get("password") != null &&
                        !meetingInfo.get("password").asText().trim().isEmpty();

                if (hasPassword) {
                    log.error("原始PMI恢复验证失败: 密码应该为空但实际有密码");
                    return ZoomApiResponse.error("原始PMI恢复验证失败：密码应该为空", "PASSWORD_NOT_EMPTY");
                }
            }

            log.info("原始PMI恢复验证成功: userId={}, originalPmi={}", zoomUserId, expectedOriginalPmi);
            return userInfoResponse;

        } catch (Exception e) {
            log.error("验证原始PMI恢复异常: userId={}", zoomUserId, e);
            return ZoomApiResponse.error("验证原始PMI恢复异常: " + e.getMessage(), "VALIDATION_ERROR");
        }
    }

    /**
     * 只更新用户PMI号码（不更新密码）
     */
    public void updateUserPmi(String zoomUserId, String pmiNumber) {
        ZoomApiResponse<JsonNode> response = updateUserPmi(zoomUserId, pmiNumber, null);
        if (!response.isSuccess()) {
            throw new RuntimeException("更新用户PMI失败: " + response.getMessage());
        }
    }

    /**
     * 获取用户的PMI号码 - 使用默认ZoomAuth
     */
    public String getUserPmi(String zoomUserId) {
        ZoomApiResponse<JsonNode> response = getUserInfo(zoomUserId);
        if (!response.isSuccess()) {
            throw new RuntimeException("获取用户信息失败: " + response.getMessage());
        }

        JsonNode userInfo = response.getData();
        if (userInfo.has("pmi")) {
            return userInfo.get("pmi").asText();
        }

        throw new RuntimeException("用户信息中没有PMI字段");
    }

    /**
     * 获取用户的PMI号码 - 使用指定ZoomAuth
     */
    public String getUserPmi(String zoomUserId, com.zoombus.entity.ZoomAuth zoomAuth) {
        ZoomApiResponse<JsonNode> response = getUserInfo(zoomUserId, zoomAuth);
        if (!response.isSuccess()) {
            throw new RuntimeException("获取用户信息失败: " + response.getMessage());
        }

        JsonNode userInfo = response.getData();
        if (userInfo.has("pmi")) {
            return userInfo.get("pmi").asText();
        }

        throw new RuntimeException("用户信息中没有PMI字段");
    }

    /**
     * 结束会议（带重试机制）
     */
    public void endMeeting(String meetingId) {
        int maxRetries = 3;
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount < maxRetries) {
            try {
                log.info("结束会议: meetingId={}, 尝试次数: {}/{}", meetingId, retryCount + 1, maxRetries);

                com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

                // 使用带日志记录的API调用方法 - 注意：Zoom API要求使用PUT方法
                ZoomApiResponse<Void> response = executeApiCallWithLogging(
                        "PUT",
                        "/meetings/" + meetingId + "/status",
                        Map.of("action", "end"),
                        Void.class,
                        "END_MEETING",
                        meetingId,
                        null,
                        zoomAuth
                );

                if (response.isSuccess()) {
                    log.info("会议结束成功: meetingId={}", meetingId);
                    return; // 成功则直接返回
                } else {
                    throw new RuntimeException("API调用失败: " + response.getMessage());
                }

            } catch (WebClientResponseException e) {
                lastException = e;
                retryCount++;

                // 检查是否为可重试的错误
                if (e.getStatusCode().is4xxClientError()) {
                    // 4xx错误通常不需要重试
                    if (e.getStatusCode().value() == 404) {
                        log.warn("会议不存在或已结束: meetingId={}, status={}", meetingId, e.getStatusCode());
                        return; // 会议不存在视为成功
                    } else if (e.getStatusCode().value() == 400) {
                        String responseBody = e.getResponseBodyAsString();
                        if (responseBody.contains("meeting has ended") || responseBody.contains("already ended")) {
                            log.warn("会议已经结束: meetingId={}", meetingId);
                            return; // 会议已结束视为成功
                        }
                    }
                    log.error("结束会议失败，客户端错误不重试: meetingId={}, status={}, response={}",
                            meetingId, e.getStatusCode(), e.getResponseBodyAsString());
                    break; // 4xx错误不重试
                } else if (e.getStatusCode().is5xxServerError() && retryCount < maxRetries) {
                    // 5xx错误可以重试
                    log.warn("结束会议失败，将重试: meetingId={}, status={}, 重试次数: {}/{}",
                            meetingId, e.getStatusCode(), retryCount, maxRetries);
                    try {
                        Thread.sleep(1000 * retryCount); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.error("结束会议失败: meetingId={}, status={}, response={}",
                            meetingId, e.getStatusCode(), e.getResponseBodyAsString());
                    break;
                }
            } catch (Exception e) {
                lastException = e;
                retryCount++;

                if (retryCount < maxRetries) {
                    log.warn("结束会议异常，将重试: meetingId={}, error={}, 重试次数: {}/{}",
                            meetingId, e.getMessage(), retryCount, maxRetries);
                    try {
                        Thread.sleep(1000 * retryCount); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.error("结束会议异常: meetingId={}", meetingId, e);
                }
            }
        }

        // 所有重试都失败了
        if (lastException != null) {
            log.error("结束会议最终失败，已重试{}次: meetingId={}", maxRetries, meetingId, lastException);
            throw new RuntimeException("结束会议失败: " + lastException.getMessage(), lastException);
        }
    }

    /**
     * 根据ZoomUserId查找用户email
     */
    private String findZoomUserEmailById(String zoomUserId) {
        try {
            // 查找所有匹配的ZoomUser
            List<ZoomUser> zoomUsers = zoomUserRepository.findByZoomUserId(zoomUserId);
            if (!zoomUsers.isEmpty()) {
                // 返回第一个匹配的用户email
                return zoomUsers.get(0).getEmail();
            }
        } catch (Exception e) {
            log.debug("查找ZoomUser email失败: zoomUserId={}, error={}", zoomUserId, e.getMessage());
        }
        return null;
    }

    /**
     * 更新Zoom用户密码
     */
    public boolean updateUserPassword(Long zoomUserId, String newPassword) {
        try {
            // 获取ZoomUser信息
            ZoomUser zoomUser = zoomUserRepository.findById(zoomUserId)
                    .orElseThrow(() -> new IllegalArgumentException("ZoomUser不存在: " + zoomUserId));

            // 获取默认的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getDefaultZoomAuth();

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("password", newPassword);

            String apiPath = "/users/" + zoomUser.getZoomUserId() + "/password";

            log.info("调用Zoom API更新用户密码: ZoomUserId={}, API Path={}", zoomUser.getZoomUserId(), apiPath);

            // 使用正确的PUT方法和/password端点
            ZoomApiResponse<JsonNode> response = executeApiCallWithLogging(
                    "PUT",
                    apiPath,
                    requestBody,
                    JsonNode.class,
                    "PASSWORD_MANAGEMENT",
                    "PWD_" + System.currentTimeMillis(),
                    zoomUser.getZoomUserId(),
                    zoomAuth
            );

            if (response.isSuccess()) {
                log.info("Zoom API更新用户密码成功: ZoomUserId={}", zoomUser.getZoomUserId());
                return true;
            } else {
                // 构建详细的错误信息
                StringBuilder errorBuilder = new StringBuilder("Zoom API更新密码失败");
                if (response.getMessage() != null) {
                    errorBuilder.append(": ").append(response.getMessage());
                }
                if (response.getErrorCode() != null) {
                    errorBuilder.append(" (错误代码: ").append(response.getErrorCode()).append(")");
                }
                if (response.getHttpStatus() != null) {
                    errorBuilder.append(" (HTTP状态码: ").append(response.getHttpStatus()).append(")");
                }

                String errorMessage = errorBuilder.toString();
                log.error("Zoom API更新用户密码失败: ZoomUserId={}, Error={}",
                    zoomUser.getZoomUserId(), errorMessage);
                throw new RuntimeException(errorMessage);
            }

        } catch (RuntimeException e) {
            // 重新抛出运行时异常（包括API失败的异常）
            throw e;
        } catch (Exception e) {
            String errorMessage = "更新Zoom用户密码异常: " + e.getMessage();
            log.error("更新Zoom用户密码异常: ZoomUserId={}", zoomUserId, e);
            throw new RuntimeException(errorMessage, e);
        }
    }
}
