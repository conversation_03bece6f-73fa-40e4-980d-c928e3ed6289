package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.dto.CreateZoomAuthRequest;
import com.zoombus.dto.SyncUsersResult;
import com.zoombus.dto.UpdateZoomAuthRequest;
import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.dto.ZoomTokenResponse;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.entity.ZoomApiLog;
import com.zoombus.repository.ZoomAuthRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ZoomAuthService {

    private final ZoomAuthRepository zoomAuthRepository;
    private final ZoomApiService zoomApiService;
    private final WebClient.Builder webClientBuilder;
    private final ObjectMapper objectMapper;
    private final ZoomApiLogService zoomApiLogService;
    
    private static final String ZOOM_OAUTH_URL = "https://zoom.us/oauth/token";
    
    /**
     * 创建Zoom认证信息
     */
    public ZoomAuth createZoomAuth(CreateZoomAuthRequest request) {
        // 检查账号名称是否已存在
        if (zoomAuthRepository.existsByAccountName(request.getAccountName())) {
            throw new RuntimeException("账号名称已存在: " + request.getAccountName());
        }
        
        // 检查Zoom账号ID是否已存在
        if (zoomAuthRepository.existsByZoomAccountId(request.getZoomAccountId())) {
            throw new RuntimeException("Zoom账号ID已存在: " + request.getZoomAccountId());
        }
        
        // 检查客户端ID是否已存在
        if (zoomAuthRepository.existsByClientId(request.getClientId())) {
            throw new RuntimeException("客户端ID已存在: " + request.getClientId());
        }

        // 检查主账号邮箱是否已存在
        if (zoomAuthRepository.existsByPrimaryEmail(request.getPrimaryEmail())) {
            throw new RuntimeException("主账号邮箱已存在: " + request.getPrimaryEmail());
        }
        
        ZoomAuth zoomAuth = new ZoomAuth();
        zoomAuth.setAccountName(request.getAccountName());
        zoomAuth.setZoomAccountId(request.getZoomAccountId());
        zoomAuth.setPrimaryEmail(request.getPrimaryEmail());
        zoomAuth.setClientId(request.getClientId());
        zoomAuth.setClientSecret(request.getClientSecret());
        zoomAuth.setAuthType(request.getAuthType());
        zoomAuth.setWebhookSecretToken(request.getWebhookSecretToken());
        zoomAuth.setApiBaseUrl(request.getApiBaseUrl());
        zoomAuth.setDescription(request.getDescription());
        zoomAuth.setStatus(ZoomAuth.AuthStatus.ACTIVE);
        
        ZoomAuth savedAuth = zoomAuthRepository.save(zoomAuth);
        log.info("创建Zoom认证信息成功: {}", savedAuth.getAccountName());
        return savedAuth;
    }
    
    /**
     * 获取所有认证信息（分页）
     */
    @Transactional(readOnly = true)
    public Page<ZoomAuth> getAllZoomAuths(Pageable pageable) {
        return zoomAuthRepository.findAll(pageable);
    }
    
    /**
     * 根据ID获取认证信息
     */
    @Transactional(readOnly = true)
    public Optional<ZoomAuth> getZoomAuthById(Long id) {
        return zoomAuthRepository.findById(id);
    }
    
    /**
     * 根据账号名称获取认证信息
     */
    @Transactional(readOnly = true)
    public Optional<ZoomAuth> getZoomAuthByAccountName(String accountName) {
        return zoomAuthRepository.findByAccountName(accountName);
    }

    /**
     * 根据Zoom账号ID获取认证信息
     */
    @Transactional(readOnly = true)
    public Optional<ZoomAuth> getZoomAuthByZoomAccountId(String zoomAccountId) {
        return zoomAuthRepository.findByZoomAccountId(zoomAccountId);
    }
    
    /**
     * 获取默认的认证信息
     */
    @Transactional(readOnly = true)
    public Optional<ZoomAuth> getDefaultZoomAuth() {
        return zoomAuthRepository.findFirstActiveAuth();
    }
    
    /**
     * 更新认证信息
     */
    public ZoomAuth updateZoomAuth(Long id, UpdateZoomAuthRequest request) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + id));
        
        // 检查账号名称是否被其他记录使用
        if (!zoomAuth.getAccountName().equals(request.getAccountName()) &&
            zoomAuthRepository.existsByAccountName(request.getAccountName())) {
            throw new RuntimeException("账号名称已存在: " + request.getAccountName());
        }

        // 检查主账号邮箱是否被其他记录使用
        if (!zoomAuth.getPrimaryEmail().equals(request.getPrimaryEmail()) &&
            zoomAuthRepository.existsByPrimaryEmail(request.getPrimaryEmail())) {
            throw new RuntimeException("主账号邮箱已存在: " + request.getPrimaryEmail());
        }
        
        zoomAuth.setAccountName(request.getAccountName());
        zoomAuth.setZoomAccountId(request.getZoomAccountId());
        zoomAuth.setPrimaryEmail(request.getPrimaryEmail());
        zoomAuth.setClientId(request.getClientId());
        zoomAuth.setClientSecret(request.getClientSecret());
        
        if (request.getAuthType() != null) {
            zoomAuth.setAuthType(request.getAuthType());
        }
        if (request.getWebhookSecretToken() != null) {
            zoomAuth.setWebhookSecretToken(request.getWebhookSecretToken());
        }
        if (request.getApiBaseUrl() != null) {
            zoomAuth.setApiBaseUrl(request.getApiBaseUrl());
        }
        if (request.getDescription() != null) {
            zoomAuth.setDescription(request.getDescription());
        }
        if (request.getStatus() != null) {
            zoomAuth.setStatus(request.getStatus());
        }
        
        ZoomAuth updatedAuth = zoomAuthRepository.save(zoomAuth);
        log.info("更新Zoom认证信息成功: {}", updatedAuth.getAccountName());
        return updatedAuth;
    }
    
    /**
     * 删除认证信息
     */
    public void deleteZoomAuth(Long id) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + id));
        
        zoomAuthRepository.delete(zoomAuth);
        log.info("删除Zoom认证信息成功: {}", zoomAuth.getAccountName());
    }
    
    /**
     * 手动刷新token
     */
    public ZoomAuth refreshToken(Long id) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + id));
        
        return refreshTokenInternal(zoomAuth);
    }
    
    /**
     * 获取有效的访问令牌
     */
    @Transactional(readOnly = true)
    public String getValidAccessToken(Long authId) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(authId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + authId));
        
        return getValidAccessToken(zoomAuth);
    }
    
    /**
     * 获取有效的访问令牌（使用默认认证）
     */
    @Transactional(readOnly = true)
    public String getValidAccessToken() {
        ZoomAuth zoomAuth = getDefaultZoomAuth()
                .orElseThrow(() -> new RuntimeException("未找到可用的Zoom认证信息"));
        
        return getValidAccessToken(zoomAuth);
    }
    
    /**
     * 获取有效的访问令牌（内部方法）
     */
    private String getValidAccessToken(ZoomAuth zoomAuth) {
        // 检查token是否需要刷新
        if (zoomAuth.isTokenExpired() || zoomAuth.isTokenExpiringSoon()) {
            // 需要刷新token
            zoomAuth = refreshTokenInternal(zoomAuth);
        }
        
        if (zoomAuth.getAccessToken() == null) {
            throw new RuntimeException("无法获取有效的访问令牌: " + zoomAuth.getAccountName());
        }
        
        return zoomAuth.getAccessToken();
    }
    
    /**
     * 内部刷新token方法
     */
    private ZoomAuth refreshTokenInternal(ZoomAuth zoomAuth) {
        try {
            log.info("🔄 开始刷新token: {}, 当前状态: {}, 过期时间: {}",
                    zoomAuth.getAccountName(), zoomAuth.getStatus(), zoomAuth.getTokenExpiresAt());

            ZoomTokenResponse tokenResponse;
            
            if (zoomAuth.getAuthType() == ZoomAuth.AuthType.SERVER_TO_SERVER) {
                // Server-to-Server OAuth
                tokenResponse = refreshServerToServerToken(zoomAuth);
            } else {
                // OAuth2.0 refresh token
                if (zoomAuth.getRefreshToken() == null) {
                    throw new RuntimeException("缺少refresh token，无法刷新");
                }
                tokenResponse = refreshOAuth2Token(zoomAuth);
            }
            
            if (tokenResponse.getError() != null) {
                throw new RuntimeException("刷新token失败: " + tokenResponse.getErrorDescription());
            }
            
            // 更新token信息
            zoomAuth.updateTokenInfo(
                tokenResponse.getAccessToken(),
                tokenResponse.getRefreshToken(),
                tokenResponse.getExpiresIn()
            );
            
            if (tokenResponse.getScope() != null) {
                zoomAuth.setScope(tokenResponse.getScope());
            }
            
            ZoomAuth savedAuth = zoomAuthRepository.save(zoomAuth);
            log.info("✅ 刷新token成功: {}, 新过期时间: {}, 刷新次数: {}",
                    savedAuth.getAccountName(), savedAuth.getTokenExpiresAt(), savedAuth.getRefreshCount());

            return savedAuth;
            
        } catch (Exception e) {
            log.error("❌ 刷新token失败: {}, 错误: {}", zoomAuth.getAccountName(), e.getMessage(), e);
            zoomAuth.markRefreshFailed(e.getMessage());
            zoomAuthRepository.save(zoomAuth);
            throw new RuntimeException("刷新token失败: " + e.getMessage(), e);
        }
    }

    /**
     * 刷新Server-to-Server OAuth token
     */
    private ZoomTokenResponse refreshServerToServerToken(ZoomAuth zoomAuth) {
        // 创建API日志记录
        ZoomApiLog apiLog = zoomApiLogService.createApiLog(
                "POST",
                "/oauth/token",
                ZOOM_OAUTH_URL,
                "TOKEN_REFRESH_S2S",
                zoomAuth.getAccountName(),
                null,
                null
        );

        WebClient webClient = webClientBuilder.build();

        String credentials = Base64.getEncoder()
                .encodeToString((zoomAuth.getClientId() + ":" + zoomAuth.getClientSecret()).getBytes());

        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("grant_type", "account_credentials");
        formData.add("account_id", zoomAuth.getZoomAccountId());

        try {
            log.debug("开始刷新Server-to-Server token，账号: {}, URL: {}", zoomAuth.getAccountName(), ZOOM_OAUTH_URL);

            // 记录请求信息
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.set(HttpHeaders.AUTHORIZATION, "Basic " + credentials);
            requestHeaders.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            requestHeaders.set(HttpHeaders.USER_AGENT, "ZoomBus/1.0");
            zoomApiLogService.recordRequest(apiLog, requestHeaders, formData);

            ZoomTokenResponse response = webClient.post()
                    .uri(ZOOM_OAUTH_URL)
                    .header(HttpHeaders.AUTHORIZATION, "Basic " + credentials)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                    .header(HttpHeaders.USER_AGENT, "ZoomBus/1.0")
                    .body(BodyInserters.fromFormData(formData))
                    .retrieve()
                    .bodyToMono(ZoomTokenResponse.class)
                    .timeout(java.time.Duration.ofSeconds(30))
                    .retry(2)
                    .block();

            log.debug("Server-to-Server token刷新成功，账号: {}", zoomAuth.getAccountName());

            // 记录成功响应（隐藏敏感信息）
            String sanitizedResponse = sanitizeTokenResponse(response);
            zoomApiLogService.recordResponse(apiLog, 200, new HttpHeaders(), sanitizedResponse);
            zoomApiLogService.saveLogAsync(apiLog);

            return response;

        } catch (WebClientResponseException e) {
            log.error("Server-to-Server OAuth请求失败，账号: {}, HTTP状态: {}, 响应: {}",
                    zoomAuth.getAccountName(), e.getStatusCode(), e.getResponseBodyAsString(), e);

            // 记录错误响应
            zoomApiLogService.recordError(apiLog, e);
            zoomApiLogService.saveLogAsync(apiLog);

            ZoomTokenResponse errorResponse = new ZoomTokenResponse();
            errorResponse.setError("request_failed");
            errorResponse.setErrorDescription("HTTP " + e.getStatusCode() + ": " + e.getResponseBodyAsString());
            return errorResponse;
        } catch (Exception e) {
            log.error("Server-to-Server token刷新异常，账号: {}, 错误: {}", zoomAuth.getAccountName(), e.getMessage(), e);

            // 记录其他错误
            zoomApiLogService.recordError(apiLog, e);
            zoomApiLogService.saveLogAsync(apiLog);

            ZoomTokenResponse errorResponse = new ZoomTokenResponse();
            errorResponse.setError("connection_error");
            errorResponse.setErrorDescription("连接失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 刷新OAuth2.0 token
     */
    private ZoomTokenResponse refreshOAuth2Token(ZoomAuth zoomAuth) {
        // 创建API日志记录
        ZoomApiLog apiLog = zoomApiLogService.createApiLog(
                "POST",
                "/oauth/token",
                ZOOM_OAUTH_URL,
                "TOKEN_REFRESH_OAUTH2",
                zoomAuth.getAccountName(),
                null,
                null
        );

        WebClient webClient = webClientBuilder.build();

        String credentials = Base64.getEncoder()
                .encodeToString((zoomAuth.getClientId() + ":" + zoomAuth.getClientSecret()).getBytes());

        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("grant_type", "refresh_token");
        formData.add("refresh_token", zoomAuth.getRefreshToken());

        try {
            log.debug("开始刷新OAuth2.0 token，账号: {}, URL: {}", zoomAuth.getAccountName(), ZOOM_OAUTH_URL);

            // 记录请求信息
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.set(HttpHeaders.AUTHORIZATION, "Basic " + credentials);
            requestHeaders.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            requestHeaders.set(HttpHeaders.USER_AGENT, "ZoomBus/1.0");
            zoomApiLogService.recordRequest(apiLog, requestHeaders, formData);

            ZoomTokenResponse response = webClient.post()
                    .uri(ZOOM_OAUTH_URL)
                    .header(HttpHeaders.AUTHORIZATION, "Basic " + credentials)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                    .header(HttpHeaders.USER_AGENT, "ZoomBus/1.0")
                    .body(BodyInserters.fromFormData(formData))
                    .retrieve()
                    .bodyToMono(ZoomTokenResponse.class)
                    .timeout(java.time.Duration.ofSeconds(30))
                    .retry(2)
                    .block();

            log.debug("OAuth2.0 token刷新成功，账号: {}", zoomAuth.getAccountName());

            // 记录成功响应（隐藏敏感信息）
            String sanitizedResponse = sanitizeTokenResponse(response);
            zoomApiLogService.recordResponse(apiLog, 200, new HttpHeaders(), sanitizedResponse);
            zoomApiLogService.saveLogAsync(apiLog);

            return response;

        } catch (WebClientResponseException e) {
            log.error("OAuth2.0刷新请求失败，账号: {}, HTTP状态: {}, 响应: {}",
                    zoomAuth.getAccountName(), e.getStatusCode(), e.getResponseBodyAsString(), e);

            // 记录错误响应
            zoomApiLogService.recordError(apiLog, e);
            zoomApiLogService.saveLogAsync(apiLog);

            ZoomTokenResponse errorResponse = new ZoomTokenResponse();
            errorResponse.setError("request_failed");
            errorResponse.setErrorDescription("HTTP " + e.getStatusCode() + ": " + e.getResponseBodyAsString());
            return errorResponse;
        } catch (Exception e) {
            log.error("OAuth2.0 token刷新异常，账号: {}, 错误: {}", zoomAuth.getAccountName(), e.getMessage(), e);

            // 记录其他错误
            zoomApiLogService.recordError(apiLog, e);
            zoomApiLogService.saveLogAsync(apiLog);

            ZoomTokenResponse errorResponse = new ZoomTokenResponse();
            errorResponse.setError("connection_error");
            errorResponse.setErrorDescription("连接失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 定时检查并刷新即将过期的token
     */
    public void refreshExpiredTokens() {
        // 1. 刷新即将过期的token（10分钟内过期的都刷新）
        LocalDateTime refreshTime = LocalDateTime.now().plusMinutes(10);
        List<ZoomAuth> tokensNeedingRefresh = zoomAuthRepository.findTokensNeedingRefresh(refreshTime);

        // 2. 尝试恢复ERROR状态的token（每5分钟尝试一次）
        List<ZoomAuth> errorTokens = zoomAuthRepository.findByStatus(ZoomAuth.AuthStatus.ERROR);

        int totalTokens = tokensNeedingRefresh.size() + errorTokens.size();
        if (totalTokens > 0) {
            log.info("🔄 发现{}个需要刷新的token（即将过期：{}个，错误状态：{}个）",
                    totalTokens, tokensNeedingRefresh.size(), errorTokens.size());
        }

        // 刷新即将过期的token
        for (ZoomAuth zoomAuth : tokensNeedingRefresh) {
            try {
                log.info("🔄 刷新即将过期的token: {}, 过期时间: {}",
                        zoomAuth.getAccountName(), zoomAuth.getTokenExpiresAt());
                refreshTokenInternal(zoomAuth);
            } catch (Exception e) {
                log.error("❌ 定时刷新token失败: {}", zoomAuth.getAccountName(), e);
            }
        }

        // 尝试恢复ERROR状态的token
        for (ZoomAuth zoomAuth : errorTokens) {
            try {
                // 检查是否应该尝试恢复（避免频繁重试）
                if (shouldRetryErrorToken(zoomAuth)) {
                    log.info("🔄 尝试恢复ERROR状态的token: {}, 错误信息: {}",
                            zoomAuth.getAccountName(), zoomAuth.getErrorMessage());
                    refreshTokenInternal(zoomAuth);
                }
            } catch (Exception e) {
                log.error("❌ 恢复ERROR状态token失败: {}", zoomAuth.getAccountName(), e);
            }
        }
    }

    /**
     * 判断是否应该重试ERROR状态的token
     */
    private boolean shouldRetryErrorToken(ZoomAuth zoomAuth) {
        // 如果没有最后刷新时间，或者距离上次刷新超过5分钟，则可以重试
        if (zoomAuth.getLastRefreshAt() == null) {
            return true;
        }

        LocalDateTime fiveMinutesAgo = LocalDateTime.now().minusMinutes(5);
        return zoomAuth.getLastRefreshAt().isBefore(fiveMinutesAgo);
    }

    /**
     * 根据状态获取认证信息
     */
    @Transactional(readOnly = true)
    public List<ZoomAuth> getZoomAuthsByStatus(ZoomAuth.AuthStatus status) {
        return zoomAuthRepository.findByStatus(status);
    }

    /**
     * 获取token状态统计信息
     */
    @Transactional(readOnly = true)
    public TokenStatusSummary getTokenStatusSummary() {
        List<ZoomAuth> allAuths = zoomAuthRepository.findAll();

        int activeCount = 0;
        int errorCount = 0;
        int expiredCount = 0;
        int expiringSoonCount = 0;

        for (ZoomAuth auth : allAuths) {
            switch (auth.getStatus()) {
                case ACTIVE:
                    activeCount++;
                    if (auth.isTokenExpired()) {
                        expiredCount++;
                    } else if (auth.isTokenExpiringSoon()) {
                        expiringSoonCount++;
                    }
                    break;
                case ERROR:
                    errorCount++;
                    break;
                case EXPIRED:
                    expiredCount++;
                    break;
                case DISABLED:
                    // 禁用状态不计入统计
                    break;
            }
        }

        return new TokenStatusSummary(allAuths.size(), activeCount, errorCount, expiredCount, expiringSoonCount);
    }

    /**
     * Token状态统计信息
     */
    public static class TokenStatusSummary {
        private final int total;
        private final int active;
        private final int error;
        private final int expired;
        private final int expiringSoon;

        public TokenStatusSummary(int total, int active, int error, int expired, int expiringSoon) {
            this.total = total;
            this.active = active;
            this.error = error;
            this.expired = expired;
            this.expiringSoon = expiringSoon;
        }

        public int getTotal() { return total; }
        public int getActive() { return active; }
        public int getError() { return error; }
        public int getExpired() { return expired; }
        public int getExpiringSoon() { return expiringSoon; }

        @Override
        public String toString() {
            return String.format("TokenStatus[总数:%d, 正常:%d, 错误:%d, 已过期:%d, 即将过期:%d]",
                    total, active, error, expired, expiringSoon);
        }
    }

    /**
     * 根据认证类型获取认证信息
     */
    @Transactional(readOnly = true)
    public List<ZoomAuth> getZoomAuthsByAuthType(ZoomAuth.AuthType authType) {
        return zoomAuthRepository.findByAuthType(authType);
    }

    /**
     * 更新认证状态
     */
    public ZoomAuth updateZoomAuthStatus(Long id, ZoomAuth.AuthStatus status) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + id));

        zoomAuth.setStatus(status);
        ZoomAuth updatedAuth = zoomAuthRepository.save(zoomAuth);
        log.info("更新认证状态成功: {} -> {}", updatedAuth.getAccountName(), status);
        return updatedAuth;
    }

    /**
     * 搜索认证信息
     */
    @Transactional(readOnly = true)
    public Page<ZoomAuth> searchZoomAuths(String keyword, Pageable pageable) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllZoomAuths(pageable);
        }
        return zoomAuthRepository.findByAccountNameContainingIgnoreCase(keyword.trim(), pageable);
    }

    /**
     * 获取认证统计信息
     */
    @Transactional(readOnly = true)
    public Object getZoomAuthStats() {
        // 这里可以返回各种统计信息，比如各状态的数量等
        return zoomAuthRepository.countByStatus();
    }

    /**
     * 清理token响应中的敏感信息，用于日志记录
     */
    private String sanitizeTokenResponse(ZoomTokenResponse response) {
        try {
            Map<String, Object> sanitized = new HashMap<>();
            sanitized.put("token_type", response.getTokenType());
            sanitized.put("expires_in", response.getExpiresIn());
            sanitized.put("scope", response.getScope());

            // 隐藏敏感信息
            if (response.getAccessToken() != null) {
                sanitized.put("access_token", "***HIDDEN***");
            }
            if (response.getRefreshToken() != null) {
                sanitized.put("refresh_token", "***HIDDEN***");
            }
            if (response.getError() != null) {
                sanitized.put("error", response.getError());
                sanitized.put("error_description", response.getErrorDescription());
            }

            return objectMapper.writeValueAsString(sanitized);
        } catch (Exception e) {
            return "Failed to serialize response: " + e.getMessage();
        }
    }

}
