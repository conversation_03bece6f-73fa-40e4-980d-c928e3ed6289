package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.dto.MeetingDetailWithTime;
import com.zoombus.entity.ZoomMeetingDetail;
import com.zoombus.repository.ZoomMeetingDetailRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ZoomMeetingDetailService {

    private final ZoomMeetingDetailRepository zoomMeetingDetailRepository;
    private final com.zoombus.repository.MeetingRepository meetingRepository;
    private final com.zoombus.repository.ZoomUserRepository zoomUserRepository;
    private final ObjectMapper objectMapper;

    /**
     * 根据hostId获取主持人密钥
     */
    private String getHostKeyByHostId(String hostId) {
        try {
            List<com.zoombus.entity.ZoomUser> users = zoomUserRepository.findByZoomUserId(hostId);
            if (!users.isEmpty()) {
                com.zoombus.entity.ZoomUser user = users.get(0);
                String hostKey = user.getHostKey();
                if (hostKey != null && !hostKey.trim().isEmpty()) {
                    return hostKey;
                }
            }

            // 如果没有找到真实的主持人密钥，生成一个模拟的密钥作为后备
            String fallbackKey = String.format("%06d", Math.abs(hostId.hashCode()) % 1000000);
            log.debug("未找到用户 {} 的主持人密钥，使用后备密钥: {}", hostId, fallbackKey);
            return fallbackKey;
        } catch (Exception e) {
            log.error("获取主持人密钥失败: {}, 错误: {}", hostId, e.getMessage());
            // 发生错误时返回后备密钥
            return String.format("%06d", Math.abs(hostId.hashCode()) % 1000000);
        }
    }


    
    /**
     * 根据会议ID查找所有Zoom会议详情（支持一对多关系，如周期性会议）
     */
    public List<ZoomMeetingDetail> getAllByMeetingId(Long meetingId) {
        return zoomMeetingDetailRepository.findByMeetingId(meetingId);
    }

    /**
     * 根据会议ID查找未删除的Zoom会议详情（过滤已删除的occurrence）
     */
    public List<ZoomMeetingDetail> getActiveByMeetingId(Long meetingId) {
        return zoomMeetingDetailRepository.findActiveByMeetingId(meetingId);
    }

    /**
     * 根据会议ID查找未删除的Zoom会议详情，包含Meeting信息（智能过滤场次记录）
     */
    public List<com.zoombus.dto.ZoomMeetingDetailWithMeeting> getActiveByMeetingIdWithMeeting(Long meetingId) {
        // 首先获取所有未删除的记录
        List<ZoomMeetingDetail> allDetails = zoomMeetingDetailRepository.findByMeetingId(meetingId)
                .stream()
                .filter(detail -> detail.getOccurrenceStatus() == null || !"deleted".equals(detail.getOccurrenceStatus()))
                .collect(java.util.stream.Collectors.toList());

        // 智能过滤逻辑：
        // 1. 如果有occurrence_start_time不为空的记录，只返回这些记录（周期性会议的具体场次）
        // 2. 如果没有occurrence_start_time不为空的记录，返回主记录（单场次会议）
        List<ZoomMeetingDetail> filteredDetails = allDetails.stream()
                .filter(detail -> detail.getOccurrenceStartTime() != null)
                .collect(java.util.stream.Collectors.toList());

        // 如果没有找到有occurrence_start_time的记录，则使用主记录
        if (filteredDetails.isEmpty()) {
            filteredDetails = allDetails.stream()
                    .filter(detail -> detail.getIsMainOccurrence() != null && detail.getIsMainOccurrence())
                    .collect(java.util.stream.Collectors.toList());
        }

        // 获取Meeting信息
        com.zoombus.entity.Meeting meeting = null;
        if (!filteredDetails.isEmpty()) {
            Optional<com.zoombus.entity.Meeting> meetingOpt = meetingRepository.findById(meetingId);
            meeting = meetingOpt.orElse(null);
        }

        // 转换为DTO并设置主持人密钥
        final com.zoombus.entity.Meeting finalMeeting = meeting;
        return filteredDetails.stream()
                .map(detail -> {
                    com.zoombus.dto.ZoomMeetingDetailWithMeeting dto = com.zoombus.dto.ZoomMeetingDetailWithMeeting.from(detail, finalMeeting);

                    // 获取主持人密钥 - 从ZoomUser表获取真实的主持人密钥
                    if (detail.getHostId() != null) {
                        String hostKey = getHostKeyByHostId(detail.getHostId());
                        dto.setHostKey(hostKey);
                        log.debug("为主持人 {} 获取主持人密钥: {}", detail.getHostId(), hostKey);
                    } else {
                        dto.setHostKey("无");
                    }

                    return dto;
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据会议ID查找第一个Zoom会议详情（兼容旧代码）
     */
    public Optional<ZoomMeetingDetail> getByMeetingId(Long meetingId) {
        return zoomMeetingDetailRepository.findFirstByMeetingId(meetingId);
    }

    /**
     * 获取最近的会议详情（用于仪表板显示）
     */
    public Page<ZoomMeetingDetail> getRecentMeetingDetails(Pageable pageable) {
        return zoomMeetingDetailRepository.findAllByOrderByIdDesc(pageable);
    }

    /**
     * 获取最近一周的会议详情（包含正确的开始时间）
     * 时间范围：当日0点0分至之后的7天时间内
     */
    public Page<MeetingDetailWithTime> getRecentWeekMeetingDetails(Pageable pageable) {
        // 当日0点0分
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        // 7天后的23:59:59
        LocalDateTime sevenDaysLater = todayStart.plusDays(7).withHour(23).withMinute(59).withSecond(59);

        Page<Object[]> results = zoomMeetingDetailRepository.findRecentWeekMeetingsWithTime(todayStart, sevenDaysLater, pageable);

        List<MeetingDetailWithTime> meetingDetails = results.getContent().stream()
                .map(MeetingDetailWithTime::from)
                .filter(detail -> detail != null)
                .collect(java.util.stream.Collectors.toList());

        return new PageImpl<>(meetingDetails, pageable, results.getTotalElements());
    }
    
    /**
     * 根据Zoom会议ID查找详情
     */
    public Optional<ZoomMeetingDetail> getByZoomMeetingId(String zoomMeetingId) {
        return zoomMeetingDetailRepository.findByZoomMeetingId(zoomMeetingId);
    }

    /**
     * 保存ZoomMeetingDetail实体
     */
    @Transactional
    public ZoomMeetingDetail save(ZoomMeetingDetail detail) {
        return zoomMeetingDetailRepository.save(detail);
    }
    
    /**
     * 根据多个会议ID批量查找Zoom会议详情
     */
    public List<ZoomMeetingDetail> getByMeetingIds(List<Long> meetingIds) {
        return zoomMeetingDetailRepository.findByMeetingIdIn(meetingIds);
    }
    
    /**
     * 保存Zoom API返回的会议详情（支持周期性会议的多个occurrence）
     */
    @Transactional
    public List<ZoomMeetingDetail> saveZoomMeetingDetailWithOccurrences(Long meetingId, JsonNode zoomResponse) {
        try {
            List<ZoomMeetingDetail> savedDetails = new ArrayList<>();

            // 记录完整的API响应用于调试
            log.info("Zoom API完整响应 for Meeting ID {}: {}", meetingId, zoomResponse.toString());

            // 首先保存主会议记录
            ZoomMeetingDetail mainDetail = createZoomMeetingDetail(meetingId, zoomResponse, null, true);
            savedDetails.add(zoomMeetingDetailRepository.save(mainDetail));
            log.info("保存主会议记录成功，Meeting ID: {}, Zoom Meeting ID: {}", meetingId, mainDetail.getZoomMeetingId());

            // 检查是否有occurrences数组（周期性会议）
            JsonNode occurrencesNode = zoomResponse.get("occurrences");
            log.info("检查occurrences节点: {}", occurrencesNode);

            if (occurrencesNode != null && occurrencesNode.isArray()) {
                log.info("发现周期性会议，包含 {} 个occurrence", occurrencesNode.size());

                for (JsonNode occurrence : occurrencesNode) {
                    log.info("处理occurrence: {}", occurrence.toString());
                    ZoomMeetingDetail occurrenceDetail = createZoomMeetingDetail(meetingId, zoomResponse, occurrence, false);
                    savedDetails.add(zoomMeetingDetailRepository.save(occurrenceDetail));
                }

                log.info("保存周期性会议详情完成，总共保存 {} 条记录", savedDetails.size());
            } else {
                log.info("普通会议，只保存主记录。occurrences节点为null或不是数组");
            }

            return savedDetails;

        } catch (Exception e) {
            log.error("Failed to save zoom meeting detail for meetingId: {}", meetingId, e);
            throw new RuntimeException("保存Zoom会议详情失败", e);
        }
    }

    /**
     * 创建ZoomMeetingDetail对象
     */
    private ZoomMeetingDetail createZoomMeetingDetail(Long meetingId, JsonNode zoomResponse, JsonNode occurrence, boolean isMainOccurrence) {
        ZoomMeetingDetail detail = new ZoomMeetingDetail();

        // 基本信息
        detail.setMeetingId(meetingId);
        detail.setZoomMeetingId(zoomResponse.get("id").asText());
        detail.setUuid(getStringValue(zoomResponse, "uuid"));
        // 注意：topic, type, duration, timezone, agenda 字段已迁移到 t_meetings 表
        // 这些字段现在只在 Meeting 实体中维护，ZoomMeetingDetail 只保存单场级别的信息
        detail.setStatus(getStringValue(zoomResponse, "status"));

        // 设置是否为主记录
        detail.setIsMainOccurrence(isMainOccurrence);

        // 如果是occurrence记录，设置occurrence特定信息
        if (occurrence != null && !isMainOccurrence) {
            detail.setOccurrenceId(getStringValue(occurrence, "occurrence_id"));
            detail.setOccurrenceStatus(getStringValue(occurrence, "status"));

            // 解析occurrence的开始时间
            String startTimeStr = getStringValue(occurrence, "start_time");
            if (startTimeStr != null) {
                try {
                    // 解析UTC时间并转换为东八区时间
                    ZonedDateTime utcTime = ZonedDateTime.parse(startTimeStr, DateTimeFormatter.ISO_DATE_TIME.withZone(ZoneOffset.UTC));
                    LocalDateTime shanghaiTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
                    detail.setOccurrenceStartTime(shanghaiTime);
                    log.debug("Occurrence开始时间时区转换: UTC {} -> 东八区 {}", startTimeStr, shanghaiTime);
                } catch (Exception e) {
                    log.warn("Failed to parse occurrence start time: {}", startTimeStr, e);
                }
            }

            // 注意：duration 字段已迁移到 t_meetings 表，不再在 ZoomMeetingDetail 中设置
        }

        // 时间处理
        String startTimeStr = getStringValue(zoomResponse, "start_time");
        if (startTimeStr != null) {
            try {
                // 解析UTC时间并转换为东八区时间
                ZonedDateTime utcTime = ZonedDateTime.parse(startTimeStr, DateTimeFormatter.ISO_DATE_TIME.withZone(ZoneOffset.UTC));
                LocalDateTime shanghaiTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
                detail.setStartTime(shanghaiTime);

                // 对于非周期性会议或主记录，用startTime填充occurrenceStartTime
                if (isMainOccurrence || occurrence == null) {
                    detail.setOccurrenceStartTime(shanghaiTime);
                    log.debug("非周期性会议或主记录，使用startTime填充occurrenceStartTime: {}", shanghaiTime);
                }

                log.debug("开始时间时区转换: UTC {} -> 东八区 {}", startTimeStr, shanghaiTime);
            } catch (Exception e) {
                log.warn("Failed to parse start_time: {}", startTimeStr, e);
            }
        }

        String createdAtStr = getStringValue(zoomResponse, "created_at");
        if (createdAtStr != null) {
            try {
                // 解析UTC时间并转换为东八区时间
                ZonedDateTime utcTime = ZonedDateTime.parse(createdAtStr, DateTimeFormatter.ISO_DATE_TIME.withZone(ZoneOffset.UTC));
                LocalDateTime shanghaiTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
                detail.setZoomCreatedAt(shanghaiTime);
                log.debug("时区转换: UTC {} -> 东八区 {}", createdAtStr, shanghaiTime);
            } catch (Exception e) {
                log.warn("Failed to parse created_at: {}", createdAtStr, e);
            }
        }

        // 主持人信息
        detail.setHostId(getStringValue(zoomResponse, "host_id"));
        detail.setHostEmail(getStringValue(zoomResponse, "host_email"));

        // URL信息
        detail.setJoinUrl(getStringValue(zoomResponse, "join_url"));
        detail.setStartUrl(getStringValue(zoomResponse, "start_url"));
        // 注意：password 字段已迁移到 t_meetings.meeting_password，不再在 ZoomMeetingDetail 中设置
        detail.setPmi(getStringValue(zoomResponse, "pmi"));

        // 设置信息
        JsonNode settingsNode = zoomResponse.get("settings");
        if (settingsNode != null) {
            detail.setSettings(settingsNode.toString());
            detail.setWaitingRoom(getBooleanValue(settingsNode, "waiting_room"));
            detail.setMuteUponEntry(getBooleanValue(settingsNode, "mute_upon_entry"));
            detail.setJoinBeforeHost(getBooleanValue(settingsNode, "join_before_host"));
            detail.setAutoRecording(getStringValue(settingsNode, "auto_recording"));
            detail.setAlternativeHosts(getStringValue(settingsNode, "alternative_hosts"));
        }

        // 注意：recurrence 字段已迁移到 t_meetings 的周期性字段，不再在 ZoomMeetingDetail 中设置

        // 跟踪字段
        JsonNode trackingFieldsNode = zoomResponse.get("tracking_fields");
        if (trackingFieldsNode != null) {
            detail.setTrackingFields(trackingFieldsNode.toString());
        }

        // 其他设置
        detail.setEncrypted(getBooleanValue(zoomResponse, "encrypted"));

        return detail;
    }

    /**
     * 保存Zoom API返回的会议详情（兼容旧版本）
     */
    @Transactional
    public ZoomMeetingDetail saveZoomMeetingDetail(Long meetingId, JsonNode zoomResponse) {
        List<ZoomMeetingDetail> details = saveZoomMeetingDetailWithOccurrences(meetingId, zoomResponse);
        // 返回主记录（第一个记录）
        return details.get(0);
    }
    
    /**
     * 更新Zoom会议详情
     */
    @Transactional
    public ZoomMeetingDetail updateZoomMeetingDetail(Long meetingId, JsonNode zoomResponse) {
        Optional<ZoomMeetingDetail> existingDetail = zoomMeetingDetailRepository.findFirstByMeetingId(meetingId);
        if (existingDetail.isPresent()) {
            // 删除旧记录，创建新记录
            zoomMeetingDetailRepository.delete(existingDetail.get());
        }
        return saveZoomMeetingDetail(meetingId, zoomResponse);
    }
    
    /**
     * 删除Zoom会议详情
     */
    @Transactional
    public void deleteByMeetingId(Long meetingId) {
        try {
            List<ZoomMeetingDetail> details = zoomMeetingDetailRepository.findByMeetingId(meetingId);
            if (!details.isEmpty()) {
                zoomMeetingDetailRepository.deleteAll(details);
                log.info("删除meeting details记录成功: meetingId={}, 删除数量={}", meetingId, details.size());
            } else {
                log.info("未找到需要删除的meeting details记录: meetingId={}", meetingId);
            }
        } catch (Exception e) {
            log.error("删除meeting details记录失败: meetingId={}", meetingId, e);
            throw new RuntimeException("删除meeting details记录失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查会议是否已有Zoom详情记录
     */
    public boolean existsByMeetingId(Long meetingId) {
        return zoomMeetingDetailRepository.existsByMeetingId(meetingId);
    }

    /**
     * 更新特定occurrence的信息
     */
    @Transactional
    public void updateOccurrenceFromZoomResponse(Long meetingId, String occurrenceId, JsonNode zoomResponse) {
        try {
            // 查找对应的occurrence记录
            Optional<ZoomMeetingDetail> occurrenceOpt = zoomMeetingDetailRepository
                    .findByMeetingIdAndOccurrenceId(meetingId, occurrenceId);

            if (occurrenceOpt.isPresent()) {
                ZoomMeetingDetail occurrence = occurrenceOpt.get();

                // 更新occurrence的信息
                if (zoomResponse.has("start_time")) {
                    String startTimeStr = zoomResponse.get("start_time").asText();
                    try {
                        occurrence.setOccurrenceStartTime(LocalDateTime.parse(startTimeStr.replace("Z", ""),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));
                    } catch (Exception e) {
                        log.warn("Failed to parse updated occurrence start time: {}", startTimeStr, e);
                    }
                }

                // 注意：duration, topic, agenda 字段已迁移到 t_meetings 表
                // 这些字段的更新现在在 MeetingService 中处理

                // 更新状态
                occurrence.setOccurrenceStatus("updated");

                zoomMeetingDetailRepository.save(occurrence);
                log.info("更新occurrence记录成功: meetingId={}, occurrenceId={}", meetingId, occurrenceId);
            } else {
                log.warn("未找到对应的occurrence记录: meetingId={}, occurrenceId={}", meetingId, occurrenceId);
            }

        } catch (Exception e) {
            log.error("更新occurrence记录失败: meetingId={}, occurrenceId={}", meetingId, occurrenceId, e);
            throw new RuntimeException("更新occurrence记录失败", e);
        }
    }

    /**
     * 标记特定occurrence为已删除
     */
    @Transactional
    public void markOccurrenceAsDeleted(Long meetingId, String occurrenceId) {
        try {
            // 查找对应的occurrence记录
            Optional<ZoomMeetingDetail> occurrenceOpt = zoomMeetingDetailRepository
                    .findByMeetingIdAndOccurrenceId(meetingId, occurrenceId);

            if (occurrenceOpt.isPresent()) {
                ZoomMeetingDetail occurrence = occurrenceOpt.get();
                occurrence.setOccurrenceStatus("deleted");
                zoomMeetingDetailRepository.save(occurrence);
                log.info("标记occurrence为已删除: meetingId={}, occurrenceId={}", meetingId, occurrenceId);
            } else {
                log.warn("未找到对应的occurrence记录: meetingId={}, occurrenceId={}", meetingId, occurrenceId);
            }

        } catch (Exception e) {
            log.error("标记occurrence为已删除失败: meetingId={}, occurrenceId={}", meetingId, occurrenceId, e);
            throw new RuntimeException("标记occurrence为已删除失败", e);
        }
    }
    
    // 辅助方法
    private String getStringValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asText() : null;
    }
    
    private Integer getIntValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asInt() : null;
    }
    
    private Boolean getBooleanValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asBoolean() : null;
    }
}
