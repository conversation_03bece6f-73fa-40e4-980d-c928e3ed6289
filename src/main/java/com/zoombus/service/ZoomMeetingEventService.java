package com.zoombus.service;

import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.ZoomMeetingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * Zoom会议事件处理服务
 * 专门处理Zoom Webhook事件
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ZoomMeetingEventService {
    
    private final ZoomMeetingRepository zoomMeetingRepository;
    
    /**
     * 处理zoom.started事件
     * 按照meeting_id和host_id查找t_zoom_meetings里状态为活跃【PENDING-待开启、USING-进行中】的记录
     * 如果PENDING则更新为USING，USING则保持不变，以便开始计费
     * 
     * @param meetingId Zoom会议ID
     * @param hostId 主持人ID
     * @param meetingUuid 会议UUID（可选）
     * @param topic 会议主题（可选）
     * @return 处理结果
     */
    @Transactional
    public ZoomApiResponse<String> handleMeetingStarted(String meetingId, String hostId, 
                                                       String meetingUuid, String topic) {
        log.info("处理zoom.started事件: meetingId={}, hostId={}, uuid={}, topic={}", 
                meetingId, hostId, meetingUuid, topic);
        
        try {
            // 查找所有匹配meeting_id的会议记录
            List<ZoomMeeting> allMeetings = zoomMeetingRepository.findByZoomMeetingId(meetingId);
            
            if (allMeetings.isEmpty()) {
                log.warn("未找到会议记录: meetingId={}, hostId={}", meetingId, hostId);
                return ZoomApiResponse.error("未找到会议记录", "MEETING_NOT_FOUND");
            }
            
            // 过滤出匹配hostId且状态为活跃的会议（PENDING或USING）
            List<ZoomMeeting> activeMeetings = allMeetings.stream()
                    .filter(meeting -> hostId.equals(meeting.getHostId()))
                    .filter(meeting -> isActiveStatus(meeting.getStatus()))
                    .collect(Collectors.toList());
            
            if (activeMeetings.isEmpty()) {
                log.warn("未找到匹配的活跃会议记录: meetingId={}, hostId={}", meetingId, hostId);
                return ZoomApiResponse.error("未找到匹配的活跃会议记录", "ACTIVE_MEETING_NOT_FOUND");
            }
            
            int updatedCount = 0;
            LocalDateTime now = LocalDateTime.now();
            
            for (ZoomMeeting meeting : activeMeetings) {
                if (meeting.getStatus() == ZoomMeeting.MeetingStatus.WAITING) {
                    // WAITING状态更新为STARTED，开始计费
                    meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
                    meeting.setStartTime(now);
                    meeting.setUpdatedAt(now);
                    
                    // 更新会议信息（如果提供）
                    if (meetingUuid != null && !meetingUuid.isEmpty()) {
                        meeting.setZoomMeetingUuid(meetingUuid);
                    }
                    if (topic != null && !topic.isEmpty()) {
                        meeting.setTopic(topic);
                    }
                    
                    zoomMeetingRepository.save(meeting);
                    updatedCount++;
                    
                    log.info("会议状态已更新为USING，开始计费: meetingId={}, hostId={}, recordId={}, startTime={}", 
                            meetingId, hostId, meeting.getId(), now);
                            
                } else if (meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED) {
                    // STARTED状态保持不变，但更新最后修改时间
                    meeting.setUpdatedAt(now);
                    
                    // 更新会议信息（如果提供且当前为空）
                    if (meetingUuid != null && !meetingUuid.isEmpty() && 
                        (meeting.getZoomMeetingUuid() == null || meeting.getZoomMeetingUuid().isEmpty())) {
                        meeting.setZoomMeetingUuid(meetingUuid);
                    }
                    if (topic != null && !topic.isEmpty() && 
                        (meeting.getTopic() == null || meeting.getTopic().isEmpty())) {
                        meeting.setTopic(topic);
                    }
                    
                    zoomMeetingRepository.save(meeting);
                    
                    log.info("会议已在进行中，状态保持不变，更新时间: meetingId={}, hostId={}, recordId={}", 
                            meetingId, hostId, meeting.getId());
                }
            }
            
            String message = String.format("处理完成，更新了%d条记录为USING状态", updatedCount);
            log.info("zoom.started事件处理完成: meetingId={}, hostId={}, updatedCount={}", 
                    meetingId, hostId, updatedCount);
            
            return ZoomApiResponse.success(message);
            
        } catch (Exception e) {
            log.error("处理zoom.started事件异常: meetingId={}, hostId={}", meetingId, hostId, e);
            return ZoomApiResponse.error("处理事件异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }
    
    /**
     * 处理zoom.ended事件
     * 将USING状态的会议更新为ENDED状态，停止计费
     * 
     * @param meetingId Zoom会议ID
     * @param hostId 主持人ID
     * @param meetingUuid 会议UUID（可选）
     * @param duration 会议时长（分钟）
     * @return 处理结果
     */
    @Transactional
    public ZoomApiResponse<String> handleMeetingEnded(String meetingId, String hostId, 
                                                     String meetingUuid, Integer duration) {
        log.info("处理zoom.ended事件: meetingId={}, hostId={}, uuid={}, duration={}分钟", 
                meetingId, hostId, meetingUuid, duration);
        
        try {
            // 查找正在进行中的会议
            List<ZoomMeeting> usingMeetings = zoomMeetingRepository.findByZoomMeetingId(meetingId)
                    .stream()
                    .filter(meeting -> hostId.equals(meeting.getHostId()))
                    .filter(meeting -> meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED)
                    .collect(Collectors.toList());
            
            if (usingMeetings.isEmpty()) {
                log.warn("未找到进行中的会议记录: meetingId={}, hostId={}", meetingId, hostId);
                return ZoomApiResponse.error("未找到进行中的会议记录", "USING_MEETING_NOT_FOUND");
            }
            
            int updatedCount = 0;
            LocalDateTime now = LocalDateTime.now();
            
            for (ZoomMeeting meeting : usingMeetings) {
                // 更新为ENDED状态
                meeting.setStatus(ZoomMeeting.MeetingStatus.ENDED);
                meeting.setEndTime(now);
                meeting.setUpdatedAt(now);
                
                // 计算会议时长
                if (duration != null && duration > 0) {
                    meeting.setDurationMinutes(duration);
                } else if (meeting.getStartTime() != null) {
                    // 如果没有提供时长，根据开始时间计算
                    long minutes = java.time.Duration.between(meeting.getStartTime(), now).toMinutes();
                    meeting.setDurationMinutes((int) Math.max(0, minutes));
                }
                
                // 更新会议UUID（如果提供）
                if (meetingUuid != null && !meetingUuid.isEmpty()) {
                    meeting.setZoomMeetingUuid(meetingUuid);
                }
                
                zoomMeetingRepository.save(meeting);
                updatedCount++;
                
                log.info("会议状态已更新为ENDED，停止计费: meetingId={}, hostId={}, recordId={}, duration={}分钟", 
                        meetingId, hostId, meeting.getId(), meeting.getDurationMinutes());
            }
            
            String message = String.format("处理完成，更新了%d条记录为ENDED状态", updatedCount);
            log.info("zoom.ended事件处理完成: meetingId={}, hostId={}, updatedCount={}", 
                    meetingId, hostId, updatedCount);
            
            return ZoomApiResponse.success(message);
            
        } catch (Exception e) {
            log.error("处理zoom.ended事件异常: meetingId={}, hostId={}", meetingId, hostId, e);
            return ZoomApiResponse.error("处理事件异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }
    
    /**
     * 查找活跃状态的会议记录
     * 用于验证和调试
     * 
     * @param meetingId Zoom会议ID
     * @param hostId 主持人ID
     * @return 活跃会议列表
     */
    public List<ZoomMeeting> findActiveMeetings(String meetingId, String hostId) {
        return zoomMeetingRepository.findByZoomMeetingId(meetingId)
                .stream()
                .filter(meeting -> hostId.equals(meeting.getHostId()))
                .filter(meeting -> isActiveStatus(meeting.getStatus()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取会议状态统计信息
     * 用于监控和调试
     * 
     * @param meetingId Zoom会议ID
     * @return 状态统计
     */
    public Map<String, Object> getMeetingStatusStats(String meetingId) {
        List<ZoomMeeting> meetings = zoomMeetingRepository.findByZoomMeetingId(meetingId);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalMeetings", meetings.size());
        stats.put("waitingCount", countByStatus(meetings, ZoomMeeting.MeetingStatus.WAITING));
        stats.put("startedCount", countByStatus(meetings, ZoomMeeting.MeetingStatus.STARTED));
        stats.put("endedCount", countByStatus(meetings, ZoomMeeting.MeetingStatus.ENDED));
        stats.put("settledCount", countByStatus(meetings, ZoomMeeting.MeetingStatus.SETTLED));
        
        return stats;
    }
    
    /**
     * 检查状态是否为活跃状态
     */
    private boolean isActiveStatus(ZoomMeeting.MeetingStatus status) {
        return status == ZoomMeeting.MeetingStatus.WAITING ||
               status == ZoomMeeting.MeetingStatus.STARTED;
    }
    
    /**
     * 统计指定状态的会议数量
     */
    private long countByStatus(List<ZoomMeeting> meetings, ZoomMeeting.MeetingStatus status) {
        return meetings.stream()
                .mapToLong(m -> m.getStatus() == status ? 1 : 0)
                .sum();
    }
}
