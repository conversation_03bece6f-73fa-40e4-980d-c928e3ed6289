package com.zoombus.service;

import com.zoombus.entity.Meeting;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.User;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.entity.ZoomUser;
import com.zoombus.exception.ResourceNotFoundException;
import com.zoombus.repository.MeetingRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.UserRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.repository.ZoomUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.dto.ZoomApiResponse;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Zoom会议记录管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ZoomMeetingService {

    /**
     * 会议结束来源枚举
     */
    public enum EndingSource {
        MANUAL_END("手动结束"),
        WEBHOOK_END("Webhook结束"),
        POLLING_SYNC("轮询同步"),
        CLEANUP("清理操作");

        private final String description;

        EndingSource(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
    
    private final ZoomMeetingRepository zoomMeetingRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final ZoomUserPmiService zoomUserPmiService;
    private final BillingMonitorService billingMonitorService;
    private final MeetingSettlementService meetingSettlementService;
    private final ZoomApiService zoomApiService;
    private final ZoomUserRepository zoomUserRepository;
    private final UserRepository userRepository;
    private final MeetingRepository meetingRepository;
    private final TransactionMonitorService transactionMonitorService;
    private final AsyncMeetingProcessService asyncMeetingProcessService;
    private final MeetingLifecycleManager meetingLifecycleManager;
    private final DistributedLockManager distributedLockManager;
    private final DistributedLockService distributedLockService;
    private final ZoomAuthService zoomAuthService;

    /**
     * 获取会议对应的ZoomAuth
     */
    private com.zoombus.entity.ZoomAuth getZoomAuthForMeeting(ZoomMeeting meeting) {
        if (meeting.getAssignedZoomUserId() == null) {
            throw new RuntimeException("会议没有关联的ZoomUser: meetingId=" + meeting.getId());
        }

        // 通过assignedZoomUserId查找ZoomUser，然后获取对应的ZoomAuth
        Optional<ZoomUser> zoomUserOpt = zoomUserRepository.findById(meeting.getAssignedZoomUserId());
        if (!zoomUserOpt.isPresent()) {
            throw new RuntimeException("未找到ZoomUser: zoomUserId=" + meeting.getAssignedZoomUserId());
        }

        ZoomUser zoomUser = zoomUserOpt.get();
        if (zoomUser.getZoomAuth() == null) {
            throw new RuntimeException("ZoomUser没有关联的ZoomAuth: zoomUserId=" + meeting.getAssignedZoomUserId());
        }

        log.debug("找到会议对应的ZoomAuth: meetingId={}, zoomUserId={}, zoomAuthAccount={}",
                meeting.getId(), meeting.getAssignedZoomUserId(), zoomUser.getZoomAuth().getAccountName());
        return zoomUser.getZoomAuth();
    }

    /**
     * 创建会议记录（PMI开启时）
     */
    @Transactional
    public ZoomMeeting createMeetingRecord(Long pmiRecordId, String zoomMeetingUuid, 
                                         String zoomMeetingId, String topic, String hostId) {
        PmiRecord pmiRecord = pmiRecordRepository.findById(pmiRecordId)
            .orElseThrow(() -> new ResourceNotFoundException("PMI记录不存在"));

        // 创建会议记录
        ZoomMeeting meeting = new ZoomMeeting();
        meeting.setPmiRecordId(pmiRecordId);
        meeting.setZoomMeetingUuid(zoomMeetingUuid);
        meeting.setZoomMeetingId(zoomMeetingId);
        meeting.setTopic(topic);
        meeting.setHostId(hostId);
        meeting.setStatus(ZoomMeeting.MeetingStatus.WAITING);
        meeting.setBillingMode(pmiRecord.getBillingMode());

        meeting = zoomMeetingRepository.save(meeting);

        // 分配ZoomUser账号
        try {
            ZoomUser assignedUser = zoomUserPmiService.assignZoomUserForMeeting(
                meeting.getId(), pmiRecord.getPmiNumber());

            // 更新会议记录中的ZoomUser信息
            meeting.setAssignedZoomUserId(assignedUser.getId());
            meeting.setAssignedZoomUserEmail(assignedUser.getEmail());
            zoomMeetingRepository.save(meeting);

            log.info("会议 {} 已分配ZoomUser: {}", meeting.getId(), assignedUser.getEmail());

        } catch (Exception e) {
            log.error("为会议 {} 分配ZoomUser失败", meeting.getId(), e);
            // 分配失败不影响会议记录创建，但需要记录错误
            meeting.setAssignmentError("ZoomUser分配失败: " + e.getMessage());
            zoomMeetingRepository.save(meeting);
        }

        return meeting;
    }
    
    /**
     * 处理会议开始事件（优化版本 - 并发安全 + 事务一致性）
     */
    @Transactional
    public void handleMeetingStarted(String meetingUuid, String meetingId, String hostId, String topic) {
        // 使用分布式锁确保并发安全
        distributedLockManager.executeWithMeetingUuidLock(
            meetingUuid,
            Duration.ofSeconds(30),
            () -> {
                doHandleMeetingStarted(meetingUuid, meetingId, hostId, topic);
                return null;
            }
        );
    }

    /**
     * 执行会议开始处理的核心逻辑
     */
    private void doHandleMeetingStarted(String meetingUuid, String meetingId, String hostId, String topic) {
        log.info("处理会议开始事件: uuid={}, meetingId={}, hostId={}, topic={}",
                meetingUuid, meetingId, hostId, topic);

        try {
            // 参数验证
            validateMeetingStartedParams(meetingUuid, meetingId, hostId);

            // 查找现有记录
            Optional<ZoomMeeting> existingMeeting = findExistingMeetingWithLock(meetingUuid, meetingId, hostId);

            if (existingMeeting.isPresent()) {
                updateExistingMeetingToStarted(existingMeeting.get(), meetingUuid, topic);
            } else {
                createNewMeetingRecord(meetingUuid, meetingId, hostId, topic);
            }

            log.info("会议开始事件处理完成: uuid={}", meetingUuid);

        } catch (Exception e) {
            log.error("处理会议开始事件失败: uuid={}, meetingId={}, hostId={}",
                    meetingUuid, meetingId, hostId, e);
            throw new RuntimeException("处理会议开始事件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 兼容旧版本的方法
     */
    @Transactional
    public void handleMeetingStarted(String meetingUuid) {
        handleMeetingStarted(meetingUuid, null, null, null);
    }

    /**
     * 验证会议开始事件的参数
     */
    private void validateMeetingStartedParams(String meetingUuid, String meetingId, String hostId) {
        if (meetingUuid == null || meetingUuid.trim().isEmpty()) {
            throw new IllegalArgumentException("meetingUuid不能为空");
        }
        if (meetingId == null || meetingId.trim().isEmpty()) {
            throw new IllegalArgumentException("meetingId不能为空");
        }
        if (hostId == null || hostId.trim().isEmpty()) {
            throw new IllegalArgumentException("hostId不能为空");
        }
        log.debug("参数验证通过: uuid={}, meetingId={}, hostId={}", meetingUuid, meetingId, hostId);
    }

    /**
     * 查找现有会议记录（优化版本 - 使用更精确的策略）
     */
    private Optional<ZoomMeeting> findExistingMeetingWithLock(String meetingUuid, String meetingId, String hostId) {
        // 策略1: 优先通过UUID查找
        if (meetingUuid != null && !meetingUuid.isEmpty()) {
            Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);
            if (meetingOpt.isPresent()) {
                log.info("通过UUID找到会议记录: meetingId={}, uuid={}", meetingOpt.get().getId(), meetingUuid);
                return meetingOpt;
            }
        }

        // 策略2: 通过meetingId+hostId查找最新的活跃会议
        if (meetingId != null && hostId != null) {
            List<ZoomMeeting> activeMeetings = zoomMeetingRepository
                .findByZoomMeetingIdAndHostIdAndStatusIn(
                    meetingId, hostId,
                    Arrays.asList(ZoomMeeting.MeetingStatus.WAITING, ZoomMeeting.MeetingStatus.STARTED)
                );

            if (!activeMeetings.isEmpty()) {
                // 选择最新创建的记录，避免选择错误的会议实例
                ZoomMeeting latestMeeting = activeMeetings.stream()
                    .max(Comparator.comparing(ZoomMeeting::getCreatedAt))
                    .orElse(activeMeetings.get(0));

                log.info("通过meetingId+hostId找到活跃会议: meetingId={}, hostId={}, recordId={}, createdAt={}",
                        meetingId, hostId, latestMeeting.getId(), latestMeeting.getCreatedAt());
                return Optional.of(latestMeeting);
            }
        }

        log.info("未找到现有会议记录: uuid={}, meetingId={}, hostId={}", meetingUuid, meetingId, hostId);
        return Optional.empty();
    }

    /**
     * 更新现有会议为开始状态（优化版本 - 简化事务边界）
     */
    private void updateExistingMeetingToStarted(ZoomMeeting meeting, String meetingUuid, String topic) {
        LocalDateTime now = LocalDateTime.now();
        boolean needsUpdate = false;
        ZoomMeeting.MeetingStatus originalStatus = meeting.getStatus();

        // 直接在当前事务中更新状态，避免跨事务操作
        if (meeting.getStatus() == ZoomMeeting.MeetingStatus.WAITING) {
            meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
            meeting.setStartTime(now);
            needsUpdate = true;
            log.info("会议状态更新: WAITING -> STARTED, meetingUuid={}, meetingId={}",
                    meetingUuid, meeting.getId());
        } else if (meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED) {
            log.info("会议已在进行中，保持幂等性: meetingUuid={}, meetingId={}",
                    meetingUuid, meeting.getId());
            // 幂等性处理：即使已经是STARTED状态，也要确保其他字段正确
        }

        // 更新UUID（如果需要）
        if (meetingUuid != null && !meetingUuid.equals(meeting.getZoomMeetingUuid())) {
            meeting.setZoomMeetingUuid(meetingUuid);
            needsUpdate = true;
            log.info("更新会议UUID: meetingId={}, newUuid={}", meeting.getId(), meetingUuid);
        }

        // 更新主题（如果提供）
        if (topic != null && !topic.isEmpty() && !topic.equals(meeting.getTopic())) {
            meeting.setTopic(topic);
            needsUpdate = true;
            log.info("更新会议主题: meetingId={}, topic={}", meeting.getId(), topic);
        }

        // 统一保存和启动计费监控
        if (needsUpdate) {
            meeting.setUpdatedAt(now);
            ZoomMeeting savedMeeting = zoomMeetingRepository.save(meeting);

            // 启动计费监控
            startBillingMonitorSafely(savedMeeting);

            // 发布状态变更事件（如果状态确实发生了变化）
            if (originalStatus != savedMeeting.getStatus()) {
                publishMeetingStatusChangedEvent(savedMeeting, "会议开始事件");
            }

            log.info("会议信息已更新: meetingId={}, status={}", savedMeeting.getId(), savedMeeting.getStatus());
        } else {
            log.debug("会议信息无需更新: meetingId={}, status={}", meeting.getId(), meeting.getStatus());
        }
    }

    /**
     * 创建新的会议记录（优化版本 - 改进错误处理）
     */
    private void createNewMeetingRecord(String meetingUuid, String meetingId, String hostId, String topic) {
        log.info("=== 开始创建新会议记录 ===");
        log.info("输入参数: uuid={}, meetingId={}, hostId={}, topic={}", meetingUuid, meetingId, hostId, topic);

        try {
            // 最后一次检查UUID是否已存在（防止并发创建）
            if (zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid.trim()).isPresent()) {
                log.warn("UUID {} 已存在，跳过创建新记录", meetingUuid);
                return;
            }

            ZoomMeeting meeting = buildNewMeetingRecord(meetingUuid, meetingId, hostId, topic);
            ZoomMeeting savedMeeting = zoomMeetingRepository.save(meeting);

            startBillingMonitorSafely(savedMeeting);
            publishMeetingStatusChangedEvent(savedMeeting, "Webhook创建会议");

            log.info("从Webhook创建会议记录成功: id={}, meetingId={}, uuid={}, type={}",
                    savedMeeting.getId(), meetingId, meetingUuid,
                    savedMeeting.getPmiRecordId() != null ? "PMI会议" : "其他会议");

        } catch (Exception e) {
            log.error("创建会议记录失败: uuid={}, meetingId={}", meetingUuid, meetingId, e);
            throw new RuntimeException("创建会议记录失败: " + e.getMessage(), e);
        }

        log.info("=== 完成创建新会议记录 ===");
    }

    /**
     * 安全地启动计费监控
     */
    private void startBillingMonitorSafely(ZoomMeeting meeting) {
        try {
            if (meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED &&
                meeting.getBillingMode() == PmiRecord.BillingMode.BY_TIME) {

                billingMonitorService.startBillingMonitor(meeting.getId());
                log.info("已启动会议 {} 的计费监控", meeting.getId());
            } else {
                log.debug("会议 {} 无需启动计费监控: status={}, billingMode={}",
                        meeting.getId(), meeting.getStatus(), meeting.getBillingMode());
            }
        } catch (Exception e) {
            log.error("启动会议 {} 计费监控失败，将在后台重试", meeting.getId(), e);
            // 可以考虑添加重试机制或异步处理
        }
    }

    /**
     * 发布会议状态变更事件
     */
    private void publishMeetingStatusChangedEvent(ZoomMeeting meeting, String reason) {
        try {
            // 这里可以发布Spring事件或其他形式的事件通知
            log.info("会议状态变更事件: meetingId={}, uuid={}, status={}, reason={}",
                    meeting.getId(), meeting.getZoomMeetingUuid(), meeting.getStatus(), reason);
            // 实际的事件发布逻辑可以在这里实现
        } catch (Exception e) {
            log.error("发布会议状态变更事件失败: meetingId={}", meeting.getId(), e);
            // 不抛异常，避免影响主流程
        }
    }

    /**
     * 构建新的会议记录对象
     */
    private ZoomMeeting buildNewMeetingRecord(String meetingUuid, String meetingId, String hostId, String topic) {
        ZoomMeeting meeting = new ZoomMeeting();
        meeting.setZoomMeetingUuid(meetingUuid.trim());
        meeting.setZoomMeetingId(meetingId.trim());
        meeting.setHostId(hostId);
        meeting.setTopic(topic != null ? topic : "Webhook会议 - " + meetingId);
        meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
        meeting.setStartTime(LocalDateTime.now());

        boolean isPmiMeeting = false;
        boolean isScheduledMeeting = false;

        // 第一步：尝试通过meetingId查找PMI记录
        if (meetingId != null) {
            try {
                Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findByPmiNumber(meetingId);
                if (pmiRecordOpt.isPresent()) {
                    PmiRecord pmiRecord = pmiRecordOpt.get();
                    meeting.setPmiRecordId(pmiRecord.getId());
                    meeting.setBillingMode(pmiRecord.getBillingMode());
                    meeting.setCreationSource(ZoomMeeting.CreationSource.PMI_MEETING);
                    isPmiMeeting = true;

                    log.info("找到对应的PMI记录: meetingId={}, pmiRecordId={}, billingMode={}",
                            meetingId, pmiRecord.getId(), pmiRecord.getBillingMode());
                }
            } catch (Exception e) {
                log.error("查找PMI记录失败: meetingId={}", meetingId, e);
            }
        }

        // 第二步：如果不是PMI会议，尝试通过meetingId查找t_meetings记录
        if (!isPmiMeeting && meetingId != null) {
            try {
                Optional<Meeting> scheduledMeetingOpt = meetingRepository.findByZoomMeetingId(meetingId);
                if (scheduledMeetingOpt.isPresent()) {
                    Meeting scheduledMeeting = scheduledMeetingOpt.get();
                    isScheduledMeeting = true;
                    meeting.setCreationSource(ZoomMeeting.CreationSource.ADMIN_PANEL);

                    // 使用安排会议的主题（如果Webhook没有提供主题）
                    if (topic == null || topic.trim().isEmpty()) {
                        meeting.setTopic(scheduledMeeting.getTopic());
                    }

                    // 设置默认计费模式为不计费
                    meeting.setBillingMode(PmiRecord.BillingMode.FREE);

                    log.info("这是通过会议安排功能创建的会议: meetingId={}", meetingId);
                }
            } catch (Exception e) {
                log.error("查找安排会议记录失败: meetingId={}", meetingId, e);
            }
        }

        // 第三步：如果既不是PMI会议也不是安排会议，则为其他类型会议
        if (!isPmiMeeting && !isScheduledMeeting) {
            log.info("这是其他类型的会议（可能是Zoom App直接创建）: meetingId={}", meetingId);
            meeting.setBillingMode(PmiRecord.BillingMode.FREE);
            meeting.setCreationSource(ZoomMeeting.CreationSource.ZOOM_CLIENT);
        }

        // 根据hostId补全ZoomUser信息
        if (hostId != null) {
            try {
                Optional<ZoomUser> zoomUserOpt = findZoomUserByZoomUserId(hostId);
                if (zoomUserOpt.isPresent()) {
                    ZoomUser zoomUser = zoomUserOpt.get();
                    meeting.setAssignedZoomUserId(zoomUser.getId());
                    meeting.setAssignedZoomUserEmail(zoomUser.getEmail());
                    meeting.setZoomAuthAccountName(zoomUser.getZoomAuth() != null ?
                        zoomUser.getZoomAuth().getAccountName() : null);

                    log.info("补全ZoomUser信息: hostId={}, userId={}, email={}",
                            hostId, zoomUser.getId(), zoomUser.getEmail());
                } else {
                    log.warn("未找到对应的ZoomUser: hostId={}", hostId);
                }
            } catch (Exception e) {
                log.error("查找ZoomUser失败: hostId={}", hostId, e);
            }
        }

        return meeting;
    }



    /**
     * 处理PMI会议开始事件（通过PMI号码和主持人ID查找PENDING记录）
     */
    @Transactional
    public void handlePmiMeetingStarted(String meetingUuid, String meetingId, String hostId, String topic) {
        log.info("处理PMI会议开始事件: meetingUuid={}, meetingId={}, hostId={}", meetingUuid, meetingId, hostId);

        try {
            // 参数验证
            if (meetingUuid == null || meetingUuid.trim().isEmpty()) {
                log.error("meetingUuid不能为空");
                return;
            }
            if (meetingId == null || meetingId.trim().isEmpty()) {
                log.error("meetingId不能为空");
                return;
            }
            if (hostId == null || hostId.trim().isEmpty()) {
                log.error("hostId不能为空");
                return;
            }

            // 首先尝试通过UUID查找（防重复处理）
            Optional<ZoomMeeting> existingMeeting = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);
            if (existingMeeting.isPresent()) {
                log.info("会议UUID {} 已存在，使用标准处理流程", meetingUuid);
                handleMeetingStarted(meetingUuid);
                return;
            }

            // 首先检查是否已有活跃的会议记录（防止并发问题）
            Optional<ZoomMeeting> activeMeeting = zoomMeetingRepository
                .findActiveMeetingByPmiNumber(meetingId);

            if (activeMeeting.isPresent()) {
                ZoomMeeting meeting = activeMeeting.get();

                // 如果已经是STARTED状态，直接返回
                if (meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED) {
                    log.info("PMI {} 会议已经是STARTED状态，跳过处理", meetingId);
                    return;
                }

                // 如果是WAITING状态，更新为STARTED
                if (meeting.getStatus() == ZoomMeeting.MeetingStatus.WAITING) {
                    // 更新会议信息
                    meeting.setZoomMeetingUuid(meetingUuid); // 更新为真实的UUID
                    meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
                    meeting.setStartTime(LocalDateTime.now());
                    if (topic != null && !topic.trim().isEmpty()) {
                        meeting.setTopic(topic);
                    }

                    zoomMeetingRepository.save(meeting);

                    // 开始计费监控（带异常处理）
                    try {
                        if (meeting.getBillingMode() == PmiRecord.BillingMode.BY_TIME) {
                            billingMonitorService.startBillingMonitor(meeting.getId());
                            log.info("已启动会议 {} 的计费监控", meeting.getId());
                        } else {
                            log.info("会议 {} 使用按时段计费，无需启动实时监控", meeting.getId());
                        }
                    } catch (Exception e) {
                        log.error("启动会议 {} 计费监控失败", meeting.getId(), e);
                        // 计费监控失败不影响会议状态更新
                    }

                    log.info("PMI会议 {} 已开始，状态从PENDING更新为USING，PMI: {}, 计费模式: {}",
                            meeting.getId(), meetingId, meeting.getBillingMode());
                }
            } else {
                // 通过PMI号码和主持人ID查找WAITING状态的会议记录（兼容旧逻辑）
                Optional<ZoomMeeting> waitingMeeting = zoomMeetingRepository
                    .findWaitingMeetingByMeetingIdAndHostId(meetingId, hostId);

                if (waitingMeeting.isPresent()) {
                    ZoomMeeting meeting = waitingMeeting.get();

                    // 更新会议信息
                    meeting.setZoomMeetingUuid(meetingUuid); // 更新为真实的UUID
                    meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
                    meeting.setStartTime(LocalDateTime.now());
                    if (topic != null && !topic.trim().isEmpty()) {
                        meeting.setTopic(topic);
                    }

                    zoomMeetingRepository.save(meeting);

                    // 开始计费监控（带异常处理）
                    try {
                        if (meeting.getBillingMode() == PmiRecord.BillingMode.BY_TIME) {
                            billingMonitorService.startBillingMonitor(meeting.getId());
                            log.info("已启动会议 {} 的计费监控", meeting.getId());
                        } else {
                            log.info("会议 {} 使用按时段计费，无需启动实时监控", meeting.getId());
                        }
                    } catch (Exception e) {
                        log.error("启动会议 {} 计费监控失败", meeting.getId(), e);
                        // 计费监控失败不影响会议状态更新
                    }

                    log.info("PMI会议 {} 已开始，状态从PENDING更新为USING，PMI: {}, 计费模式: {}",
                            meeting.getId(), meetingId, meeting.getBillingMode());

                } else {
                    // 如果找不到PENDING记录，创建新记录
                    log.warn("未找到PMI {} (hostId: {}) 的PENDING会议记录，创建新记录", meetingId, hostId);
                    createMeetingRecordFromWebhook(meetingUuid, meetingId, hostId, topic);
                }
            }

        } catch (Exception e) {
            log.error("处理PMI会议开始事件失败: meetingUuid={}, meetingId={}, hostId={}",
                    meetingUuid, meetingId, hostId, e);
            // 不重新抛出异常，避免影响Webhook响应
        }
    }

    /**
     * 处理会议结束事件（Webhook和轮询调用）
     */
    @Transactional
    public void handleMeetingEnded(String meetingUuid) {
        // 使用分布式锁防止并发处理同一个会议
        String lockKey = DistributedLockService.getMeetingEndLockKey(meetingUuid);
        String lockValue = distributedLockService.tryLockWithRetry(lockKey, 30, 3, 100);

        if (lockValue == null) {
            log.warn("获取会议结束锁失败，可能正在被其他线程处理: meetingUuid={}", meetingUuid);
            return;
        }

        try {
            log.info("🔒 获取会议结束锁成功，开始处理: meetingUuid={}", meetingUuid);
            handleMeetingEndedInternal(meetingUuid, EndingSource.WEBHOOK_END, false);
        } finally {
            distributedLockService.releaseLock(lockKey, lockValue);
            log.info("🔓 释放会议结束锁: meetingUuid={}", meetingUuid);
        }
    }

    /**
     * 统一的会议结束处理方法
     * @param meetingUuid 会议UUID
     * @param source 结束来源
     * @param needZoomApiCall 是否需要调用Zoom API
     */
    private void handleMeetingEndedInternal(String meetingUuid, EndingSource source, boolean needZoomApiCall) {
        String operationId = UUID.randomUUID().toString().substring(0, 8);
        long startTime = System.currentTimeMillis();

        log.info("🎯 [{}] 开始处理会议结束: uuid={}, source={}, needZoomApiCall={}",
            operationId, meetingUuid, source.getDescription(), needZoomApiCall);

        Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository
            .findByZoomMeetingUuid(meetingUuid);

        if (meetingOpt.isPresent()) {
            ZoomMeeting meeting = meetingOpt.get();

            log.info("📋 [{}] 找到会议记录: meetingId={}, currentStatus={}, startTime={}, pmiRecordId={}",
                operationId, meeting.getId(), meeting.getStatus(), meeting.getStartTime(), meeting.getPmiRecordId());

            // 状态验证 - 防止重复结束操作
            if (meeting.getStatus() == ZoomMeeting.MeetingStatus.ENDED ||
                meeting.getStatus() == ZoomMeeting.MeetingStatus.SETTLED) {
                log.warn("⚠️ [{}] 会议已经结束，跳过重复处理: meetingId={}, status={}",
                    operationId, meeting.getId(), meeting.getStatus());
                return;
            }

            // 记录会议基本信息用于监控
            boolean isPmiMeeting = isPmiMeeting(meeting);
            log.info("📊 [{}] 会议类型分析: isPMI={}, billingMode={}, creationSource={}",
                operationId, isPmiMeeting, meeting.getBillingMode(), meeting.getCreationSource());

            // 如果需要调用Zoom API（仅手动结束且会议状态为waiting时）
            if (needZoomApiCall && meeting.getZoomMeetingId() != null &&
                !meeting.getZoomMeetingId().isEmpty() && !meeting.getZoomMeetingId().startsWith("pending-")) {

                try {
                    log.info("📞 [{}] 调用Zoom API结束会议: zoomMeetingId={}, source={}",
                            operationId, meeting.getZoomMeetingId(), source.getDescription());
                    long apiStartTime = System.currentTimeMillis();
                    zoomApiService.endMeeting(meeting.getZoomMeetingId());
                    long apiDuration = System.currentTimeMillis() - apiStartTime;
                    log.info("✅ [{}] Zoom API结束会议成功: zoomMeetingId={}, 耗时={}ms",
                        operationId, meeting.getZoomMeetingId(), apiDuration);
                } catch (Exception e) {
                    log.warn("⚠️ [{}] Zoom API结束会议失败，继续本地处理: zoomMeetingId={}, error={}",
                        operationId, meeting.getZoomMeetingId(), e.getMessage());
                    // 不阻断本地处理流程
                }
            }

            LocalDateTime endTime = LocalDateTime.now();
            log.info("⏰ [{}] 设置会议结束时间: endTime={}", operationId, endTime);

            // 更新会议状态
            meeting.setStatus(ZoomMeeting.MeetingStatus.ENDED);
            meeting.setEndTime(endTime);
            log.info("📝 [{}] 更新会议状态: STARTED -> ENDED", operationId);

            // 计算会议时长
            if (meeting.getStartTime() != null) {
                Duration duration = Duration.between(meeting.getStartTime(), endTime);
                long minutes = Math.max(0, duration.toMinutes()); // 确保时长不为负数
                meeting.setDurationMinutes((int) minutes);
                log.info("⏱️ [{}] 会议时长计算: startTime={}, endTime={}, duration={}分钟",
                        operationId, meeting.getStartTime(), endTime, minutes);
            } else {
                // 如果没有开始时间（PENDING状态直接结束），设置时长为0
                meeting.setDurationMinutes(0);
                log.info("⏱️ [{}] 会议未开始即结束，时长设为0", operationId);
            }

            // 保存会议记录
            try {
                log.info("💾 [{}] 开始保存会议记录到数据库", operationId);
                long saveStartTime = System.currentTimeMillis();
                zoomMeetingRepository.save(meeting);
                long saveDuration = System.currentTimeMillis() - saveStartTime;
                log.info("✅ [{}] 会议状态更新成功: meetingId={}, status=ENDED, 耗时={}ms",
                    operationId, meeting.getId(), saveDuration);
            } catch (Exception e) {
                log.error("❌ [{}] 保存会议状态失败: meetingId={}, error={}",
                    operationId, meeting.getId(), e.getMessage(), e);
                throw new RuntimeException("保存会议状态失败: " + e.getMessage());
            }

            // 停止计费监控（带重试机制）
            try {
                log.info("🛑 [{}] 开始停止计费监控: meetingId={}", operationId, meeting.getId());
                long monitorStartTime = System.currentTimeMillis();
                billingMonitorService.stopBillingMonitor(meeting.getId());
                long monitorDuration = System.currentTimeMillis() - monitorStartTime;
                log.info("✅ [{}] 计费监控已停止: meetingId={}, 耗时={}ms",
                    operationId, meeting.getId(), monitorDuration);
            } catch (Exception e) {
                log.error("❌ [{}] 停止计费监控失败: meetingId={}, error={}",
                    operationId, meeting.getId(), e.getMessage(), e);
                // 不阻断后续流程，但记录错误
            }

            // 执行结算（带重试机制）
            try {
                log.info("💰 [{}] 开始执行会议结算: meetingId={}, billingMode={}",
                    operationId, meeting.getId(), meeting.getBillingMode());
                long settlementStartTime = System.currentTimeMillis();
                meetingSettlementService.settleMeeting(meeting.getId());
                long settlementDuration = System.currentTimeMillis() - settlementStartTime;
                log.info("✅ [{}] 会议结算完成: meetingId={}, 耗时={}ms",
                    operationId, meeting.getId(), settlementDuration);
            } catch (Exception e) {
                log.error("❌ [{}] 会议结算失败，将在后续批处理中重试: meetingId={}, error={}",
                    operationId, meeting.getId(), e.getMessage(), e);
                // 结算失败不阻断资源释放
            }

            // 释放ZoomUser账号（仅PMI类型会议需要释放）
            if (isPmiMeeting(meeting)) {
                try {
                    log.info("🔓 [{}] 开始释放ZoomUser账号: meetingId={}, creationSource={}",
                        operationId, meeting.getId(), meeting.getCreationSource());
                    long releaseStartTime = System.currentTimeMillis();
                    zoomUserPmiService.releaseZoomUser(meeting.getId());
                    long releaseDuration = System.currentTimeMillis() - releaseStartTime;
                    log.info("✅ [{}] PMI会议ZoomUser账号已释放: meetingId={}, creationSource={}, 耗时={}ms",
                            operationId, meeting.getId(), meeting.getCreationSource(), releaseDuration);
                } catch (Exception e) {
                    log.error("❌ [{}] 释放PMI会议ZoomUser账号失败，需要手动处理: meetingId={}, creationSource={}, error={}",
                            operationId, meeting.getId(), meeting.getCreationSource(), e.getMessage(), e);
                    // 资源释放失败需要记录，可能需要手动干预
                }
            } else {
                log.info("ℹ️ [{}] 非PMI会议，跳过ZoomUser账号释放: meetingId={}, creationSource={}",
                        operationId, meeting.getId(), meeting.getCreationSource());
            }

            long totalDuration = System.currentTimeMillis() - startTime;
            log.info("🎉 [{}] 会议结束处理完成: meetingId={}, duration={}分钟, 总耗时={}ms",
                    operationId, meeting.getId(), meeting.getDurationMinutes(), totalDuration);

        } else {
            log.warn("❌ [{}] 未找到会议记录: uuid={}", operationId, meetingUuid);
            throw new ResourceNotFoundException("未找到会议记录: " + meetingUuid);
        }
    }

    /**
     * 判断是否为PMI会议
     */
    private boolean isPmiMeeting(ZoomMeeting meeting) {
        return meeting.getCreationSource() == ZoomMeeting.CreationSource.PMI_MEETING;
    }

    /**
     * 通过UUID查找会议
     */
    public Optional<ZoomMeeting> findByUuid(String meetingUuid) {
        return zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);
    }

    /**
     * 通过UUID结束会议
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void endMeetingByUuid(String meetingUuid) {
        // 使用分布式锁防止并发处理同一个会议
        String lockKey = DistributedLockService.getMeetingEndLockKey(meetingUuid);
        String lockValue = distributedLockService.tryLockWithRetry(lockKey, 30, 3, 100);

        if (lockValue == null) {
            log.warn("获取会议结束锁失败，可能正在被其他线程处理: meetingUuid={}", meetingUuid);
            throw new IllegalStateException("会议正在被其他操作处理中，请稍后重试");
        }

        try {
            log.info("🔒 获取会议结束锁成功，开始手动结束会议: meetingUuid={}", meetingUuid);
            endMeetingByUuidInternal(meetingUuid);
        } finally {
            distributedLockService.releaseLock(lockKey, lockValue);
            log.info("🔓 释放会议结束锁: meetingUuid={}", meetingUuid);
        }
    }

    /**
     * 内部方法：通过UUID结束会议的具体实现
     */
    private void endMeetingByUuidInternal(String meetingUuid) {
        Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);
        if (!meetingOpt.isPresent()) {
            throw new ResourceNotFoundException("会议不存在: " + meetingUuid);
        }

        ZoomMeeting meeting = meetingOpt.get();

        // 检查会议是否已经结束
        if (meeting.getEndTime() != null) {
            log.info("会议已经被结束，跳过处理: meetingUuid={}, endTime={}", meetingUuid, meeting.getEndTime());
            return;
        }

        log.info("开始手动结束会议: meetingUuid={}, meetingId={}, zoomMeetingId={}, status={}",
                meetingUuid, meeting.getId(), meeting.getZoomMeetingId(), meeting.getStatus());

        try {
            // 检查是否需要调用Zoom API结束会议
            boolean needsZoomApiCall = false;
            if (meeting.getZoomMeetingId() != null && !meeting.getZoomMeetingId().isEmpty() &&
                !meeting.getZoomMeetingId().startsWith("pending-")) {
                needsZoomApiCall = checkIfMeetingNeedsToBeEnded(meeting);
            }

            // 如果需要调用Zoom API，先调用
            if (needsZoomApiCall) {
                try {
                    log.info("📞 调用Zoom API结束会议: zoomMeetingId={}", meeting.getZoomMeetingId());
                    zoomApiService.endMeeting(meeting.getZoomMeetingId());
                    log.info("✅ Zoom API结束会议成功: zoomMeetingId={}", meeting.getZoomMeetingId());
                } catch (Exception e) {
                    log.warn("⚠️ Zoom API结束会议失败，继续本地处理: zoomMeetingId={}, error={}",
                            meeting.getZoomMeetingId(), e.getMessage());
                    // 不阻断本地处理流程
                }
            }

            // 更新本地状态
            try {
                log.info("🔄 开始更新本地会议状态: meetingUuid={}", meetingUuid);
                endMeetingWithSeparateTransaction(meeting.getZoomMeetingUuid());
                log.info("✅ 手动结束会议成功: meetingUuid={}, needsZoomApiCall={}", meetingUuid, needsZoomApiCall);

                // 记录事务成功
                transactionMonitorService.recordTransactionSuccess("endMeetingByUuid-" + meetingUuid);
            } catch (Exception e) {
                log.error("❌ 更新本地会议状态失败: meetingUuid={}", meetingUuid, e);

                // 记录事务失败
                transactionMonitorService.recordTransactionFailure("endMeetingByUuid-" + meetingUuid, e);

                throw new RuntimeException("结束会议失败: " + e.getMessage());
            }

        } catch (Exception e) {
            log.error("❌ 手动结束会议失败: meetingUuid={}, zoomMeetingId={}", meetingUuid, meeting.getZoomMeetingId(), e);
            throw new RuntimeException("结束会议失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从Webhook创建会议记录（兼容旧版本，已废弃）
     * @deprecated 使用 createNewMeetingRecord 替代
     */
    @Deprecated
    private void createMeetingRecordFromWebhook(String meetingUuid, String meetingId, String hostId, String topic) {
        log.warn("使用了已废弃的createMeetingRecordFromWebhook方法，建议使用createNewMeetingRecord");
        createNewMeetingRecord(meetingUuid, meetingId, hostId, topic);
    }

    /**
     * 根据ZoomUserId查找ZoomUser
     */
    private Optional<ZoomUser> findZoomUserByZoomUserId(String zoomUserId) {
        try {
            List<ZoomUser> zoomUsers = zoomUserRepository.findByZoomUserId(zoomUserId);
            if (!zoomUsers.isEmpty()) {
                return Optional.of(zoomUsers.get(0)); // 返回第一个匹配的用户
            }
        } catch (Exception e) {
            log.error("查找ZoomUser失败: zoomUserId={}", zoomUserId, e);
        }
        return Optional.empty();
    }
    
    /**
     * 手动结束会议
     * 使用独立事务避免并发冲突
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 60)
    public void endMeeting(Long meetingId) {
        log.info("🔄 开始结束会议事务: meetingId={}, transactionName={}",
                meetingId, TransactionSynchronizationManager.getCurrentTransactionName());

        // 开始事务监控
        transactionMonitorService.onTransactionBegin("endMeeting-" + meetingId);

        try {
            // 使用乐观锁策略避免并发冲突
            ZoomMeeting meeting = zoomMeetingRepository.findById(meetingId)
                .orElseThrow(() -> new ResourceNotFoundException("会议记录不存在"));

            log.info("📋 查询到会议记录: meetingId={}, status={}, zoomMeetingId={}",
                    meetingId, meeting.getStatus(), meeting.getZoomMeetingId());

            // 状态验证 - 支持WAITING和STARTED状态的会议结束
        if (meeting.getStatus() != ZoomMeeting.MeetingStatus.STARTED &&
            meeting.getStatus() != ZoomMeeting.MeetingStatus.WAITING) {
            log.warn("会议状态不允许结束: meetingId={}, status={}", meetingId, meeting.getStatus());
            throw new IllegalStateException("只能结束等待开始或正在进行中的会议，当前状态: " + meeting.getStatus());
        }

        // 检查是否已经被其他进程结束
        if (meeting.getEndTime() != null) {
            log.info("会议已经被结束，跳过处理: meetingId={}, endTime={}", meetingId, meeting.getEndTime());
            return;
        }

        log.info("开始手动结束会议: meetingId={}, status={}, zoomMeetingId={}",
                meetingId, meeting.getStatus(), meeting.getZoomMeetingId());

        // 检查是否需要调用Zoom API结束会议
        boolean needsZoomApiCall = false;
        if (meeting.getZoomMeetingId() != null && !meeting.getZoomMeetingId().isEmpty() &&
            !meeting.getZoomMeetingId().startsWith("pending-")) {
            needsZoomApiCall = checkIfMeetingNeedsToBeEnded(meeting);
        }

        // 如果需要调用Zoom API，先调用
        if (needsZoomApiCall) {
            try {
                log.info("调用Zoom API结束会议: zoomMeetingId={}", meeting.getZoomMeetingId());
                zoomApiService.endMeeting(meeting.getZoomMeetingId());
                log.info("Zoom API结束会议成功: zoomMeetingId={}", meeting.getZoomMeetingId());
            } catch (Exception e) {
                log.warn("Zoom API结束会议失败，继续本地处理: zoomMeetingId={}, error={}",
                        meeting.getZoomMeetingId(), e.getMessage());
                // 不阻断本地处理流程
            }
        }

        // 更新本地状态
        try {
            endMeetingWithSeparateTransaction(meeting.getZoomMeetingUuid());
            log.info("✅ 手动结束会议成功: meetingId={}, needsZoomApiCall={}", meetingId, needsZoomApiCall);

            // 记录事务成功
            transactionMonitorService.recordTransactionSuccess("endMeeting-" + meetingId);
        } catch (Exception e) {
            log.error("❌ 更新本地会议状态失败: meetingId={}", meetingId, e);

            // 记录事务失败
            transactionMonitorService.recordTransactionFailure("endMeeting-" + meetingId, e);

            throw new RuntimeException("结束会议失败: " + e.getMessage());
        }

        } catch (Exception e) {
            log.error("🚨 结束会议事务异常: meetingId={}, error={}", meetingId, e.getMessage(), e);

            // 记录事务失败
            transactionMonitorService.recordTransactionFailure("endMeeting-" + meetingId, e);

            throw e;
        }
    }

    /**
     * 使用独立事务结束会议，避免与其他事务冲突
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void endMeetingWithSeparateTransaction(String meetingUuid) {
        log.info("🔄 开始独立事务结束会议: uuid={}, transactionName={}",
                meetingUuid, TransactionSynchronizationManager.getCurrentTransactionName());

        Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository
            .findByZoomMeetingUuid(meetingUuid);

        log.debug("📋 查询会议记录结果: uuid={}, found={}", meetingUuid, meetingOpt.isPresent());

        if (!meetingOpt.isPresent()) {
            log.warn("未找到会议记录: uuid={}", meetingUuid);
            throw new ResourceNotFoundException("未找到会议记录: " + meetingUuid);
        }

        ZoomMeeting meeting = meetingOpt.get();

        log.info("📊 会议当前状态: meetingId={}, status={}, endTime={}",
                meeting.getId(), meeting.getStatus(), meeting.getEndTime());

        // 再次检查状态，避免并发问题
        if (meeting.getStatus() != ZoomMeeting.MeetingStatus.STARTED &&
            meeting.getStatus() != ZoomMeeting.MeetingStatus.WAITING) {
            log.warn("⚠️ 会议状态不是STARTED或WAITING，跳过处理: meetingId={}, status={}",
                    meeting.getId(), meeting.getStatus());
            return;
        }

        // 检查是否已经结束
        if (meeting.getEndTime() != null) {
            log.warn("⚠️ 会议已经结束，跳过处理: meetingId={}, endTime={}",
                    meeting.getId(), meeting.getEndTime());
            return;
        }

        LocalDateTime endTime = LocalDateTime.now();

        log.info("🔄 开始更新会议状态: meetingId={}, 当前状态={}", meeting.getId(), meeting.getStatus());

        // 更新会议状态
        meeting.setStatus(ZoomMeeting.MeetingStatus.ENDED);
        meeting.setEndTime(endTime);

        log.info("📝 设置会议结束状态: meetingId={}, endTime={}", meeting.getId(), endTime);

        // 计算会议时长
        if (meeting.getStartTime() != null) {
            Duration duration = Duration.between(meeting.getStartTime(), endTime);
            long minutes = Math.max(0, duration.toMinutes());
            meeting.setDurationMinutes((int) minutes);
            log.info("⏱️ 会议时长计算: meetingId={}, startTime={}, endTime={}, duration={}分钟",
                    meeting.getId(), meeting.getStartTime(), endTime, minutes);
        } else {
            meeting.setDurationMinutes(0);
            log.info("⚠️ 会议未开始即结束，时长设为0: meetingId={}", meeting.getId());
        }

        log.info("💾 准备保存会议记录: meetingId={}", meeting.getId());

        // 保存会议记录
        try {
            ZoomMeeting savedMeeting = zoomMeetingRepository.save(meeting);
            log.info("✅ 会议状态更新成功: meetingId={}, status=ENDED, savedId={}",
                    meeting.getId(), savedMeeting.getId());
        } catch (Exception e) {
            log.error("❌ 保存会议记录失败: meetingId={}, error={}", meeting.getId(), e.getMessage(), e);
            throw e;
        }

        // 使用独立的异步服务处理后续操作，避免事务冲突
        asyncMeetingProcessService.asyncProcessMeetingEndByUuid(meetingUuid);
    }

    /**
     * 检查Zoom会议是否需要调用结束API
     *
     * @param meeting Zoom会议对象
     * @return true表示需要调用结束API，false表示不需要
     */
    private boolean checkIfMeetingNeedsToBeEnded(ZoomMeeting meeting) {
        String zoomMeetingId = meeting.getZoomMeetingId();
        try {
            log.info("查询Zoom会议状态: meetingId={}", zoomMeetingId);

            // 获取会议对应的ZoomAuth
            com.zoombus.entity.ZoomAuth zoomAuth = getZoomAuthForMeeting(meeting);

            // 使用正确的ZoomAuth调用API
            ZoomApiResponse<JsonNode> response = zoomApiService.getMeeting(zoomMeetingId, zoomAuth);

            if (!response.isSuccess()) {
                String errorCode = response.getErrorCode();
                String errorMessage = response.getMessage();

                // 会议不存在的错误码
                if ("3001".equals(errorCode) || "404".equals(errorCode)) {
                    log.info("Zoom会议不存在，无需调用结束API: meetingId={}, error={}",
                            zoomMeetingId, errorMessage);
                    return false;
                }

                // 其他错误，为了安全起见，仍然尝试调用结束API
                log.warn("查询Zoom会议状态失败，为安全起见仍将调用结束API: meetingId={}, error={}",
                        zoomMeetingId, errorMessage);
                return true;
            }

            JsonNode meetingData = response.getData();

            // 检查会议状态
            if (meetingData.has("status")) {
                String status = meetingData.get("status").asText();
                log.info("Zoom会议当前状态: meetingId={}, status={}", zoomMeetingId, status);

                // 根据Zoom API文档，会议状态包括：
                // - waiting: 等待开始
                // - started: 进行中
                // - ended: 已结束
                if ("ended".equals(status)) {
                    log.info("Zoom会议已结束，无需调用结束API: meetingId={}", zoomMeetingId);
                    return false;
                } else if ("started".equals(status)) {
                    log.info("Zoom会议正在进行中，需要调用结束API: meetingId={}", zoomMeetingId);
                    return true;
                } else if ("waiting".equals(status)) {
                    log.info("Zoom会议等待开始，调用结束API以确保清理: meetingId={}", zoomMeetingId);
                    return true;
                } else {
                    log.info("Zoom会议状态未知({}), 为安全起见调用结束API: meetingId={}", status, zoomMeetingId);
                    return true;
                }
            }

            // 如果没有状态字段，检查其他信息
            if (meetingData.has("start_time") && meetingData.has("duration")) {
                // 有开始时间和时长信息，可能是已安排的会议
                log.info("Zoom会议有时间信息，为安全起见调用结束API: meetingId={}", zoomMeetingId);
                return true;
            }

            // 默认情况下，为了安全起见，调用结束API
            log.info("无法确定Zoom会议状态，为安全起见调用结束API: meetingId={}", zoomMeetingId);
            return true;

        } catch (Exception e) {
            log.error("查询Zoom会议状态异常，为安全起见仍将调用结束API: meetingId={}", zoomMeetingId, e);
            return true;
        }
    }


    
    /**
     * 获取活跃会议列表
     */
    public Page<ZoomMeeting> getActiveMeetings(Pageable pageable) {
        return zoomMeetingRepository.findActiveMeetings(pageable);
    }
    
    /**
     * 获取历史会议列表
     */
    public Page<ZoomMeeting> getHistoryMeetings(Pageable pageable) {
        return zoomMeetingRepository.findHistoryMeetings(pageable);
    }
    
    /**
     * 根据条件查询活跃会议
     */
    public Page<ZoomMeeting> getActiveMeetingsWithFilters(PmiRecord.BillingMode billingMode,
                                                        String keyword, Pageable pageable) {
        Page<ZoomMeeting> meetings = zoomMeetingRepository.findActiveMeetingsWithFilters(billingMode, keyword, pageable);
        // 补充用户姓名信息
        enrichMeetingsWithUserNames(meetings.getContent());
        return meetings;
    }
    
    /**
     * 根据条件查询历史会议
     */
    public Page<ZoomMeeting> getHistoryMeetingsWithFilters(PmiRecord.BillingMode billingMode,
                                                         String keyword,
                                                         LocalDateTime startDate,
                                                         LocalDateTime endDate,
                                                         Pageable pageable) {
        Page<ZoomMeeting> meetings = zoomMeetingRepository.findHistoryMeetingsWithFilters(
            billingMode, keyword, startDate, endDate, pageable);
        // 补充用户姓名信息
        enrichMeetingsWithUserNames(meetings.getContent());
        return meetings;
    }
    
    /**
     * 获取会议详情
     */
    public ZoomMeeting getMeetingById(Long meetingId) {
        return zoomMeetingRepository.findById(meetingId)
            .orElseThrow(() -> new ResourceNotFoundException("会议记录不存在"));
    }
    
    /**
     * 获取PMI的会议统计
     */
    public MeetingStatistics getPmiMeetingStatistics(Long pmiRecordId) {
        long totalMeetings = zoomMeetingRepository.countByPmiRecordId(pmiRecordId);
        long totalMinutes = zoomMeetingRepository.sumDurationMinutesByPmiRecordId(pmiRecordId);
        
        return new MeetingStatistics(totalMeetings, totalMinutes);
    }
    
    /**
     * 查找需要结算的会议
     */
    public List<ZoomMeeting> getMeetingsNeedSettlement() {
        return zoomMeetingRepository.findMeetingsNeedSettlement();
    }

    /**
     * 为会议列表补充用户姓名信息
     * 优先通过PMI记录关联到系统用户，获取t_users.full_name
     */
    private void enrichMeetingsWithUserNames(List<ZoomMeeting> meetings) {
        if (meetings == null || meetings.isEmpty()) {
            return;
        }

        for (ZoomMeeting meeting : meetings) {
            String userFullName = null;

            // 方案1：通过PMI记录关联到系统用户（优先）
            if (meeting.getPmiRecordId() != null) {
                try {
                    Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(meeting.getPmiRecordId());
                    if (pmiRecordOpt.isPresent()) {
                        PmiRecord pmiRecord = pmiRecordOpt.get();
                        if (pmiRecord.getUserId() != null) {
                            Optional<User> userOpt = userRepository.findById(pmiRecord.getUserId());
                            if (userOpt.isPresent()) {
                                userFullName = userOpt.get().getFullName();
                                log.debug("通过PMI记录获取用户姓名: meetingId={}, pmiRecordId={}, userId={}, fullName={}",
                                    meeting.getId(), meeting.getPmiRecordId(), pmiRecord.getUserId(), userFullName);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("通过PMI记录获取用户姓名失败: meetingId={}, pmiRecordId={}, error={}",
                        meeting.getId(), meeting.getPmiRecordId(), e.getMessage());
                }
            }

            // 方案2：通过ZoomUser关联到系统用户（备用）
            if (userFullName == null && meeting.getAssignedZoomUserId() != null) {
                try {
                    Optional<ZoomUser> zoomUserOpt = zoomUserRepository.findById(meeting.getAssignedZoomUserId());
                    if (zoomUserOpt.isPresent()) {
                        ZoomUser zoomUser = zoomUserOpt.get();
                        if (zoomUser.getUser() != null) {
                            // 从关联的User获取姓名
                            userFullName = zoomUser.getUser().getFullName();
                            log.debug("通过ZoomUser关联获取用户姓名: meetingId={}, zoomUserId={}, fullName={}",
                                meeting.getId(), meeting.getAssignedZoomUserId(), userFullName);
                        } else {
                            // 如果没有关联User，使用ZoomUser的firstName + lastName
                            String fullName = "";
                            if (zoomUser.getFirstName() != null) {
                                fullName += zoomUser.getFirstName();
                            }
                            if (zoomUser.getLastName() != null) {
                                if (!fullName.isEmpty()) {
                                    fullName += " ";
                                }
                                fullName += zoomUser.getLastName();
                            }
                            if (fullName.isEmpty()) {
                                fullName = zoomUser.getEmail(); // 最后使用邮箱作为显示名
                            }
                            userFullName = fullName;
                            log.debug("使用ZoomUser信息作为姓名: meetingId={}, zoomUserId={}, fullName={}",
                                meeting.getId(), meeting.getAssignedZoomUserId(), userFullName);
                        }
                    }
                } catch (Exception e) {
                    log.warn("通过ZoomUser获取用户姓名失败: meetingId={}, zoomUserId={}, error={}",
                        meeting.getId(), meeting.getAssignedZoomUserId(), e.getMessage());
                }
            }

            // 设置最终的用户姓名
            meeting.setAssignedUserFullName(userFullName);
        }
    }
    
    /**
     * 会议统计数据类
     */
    public static class MeetingStatistics {
        private final long totalMeetings;
        private final long totalMinutes;
        
        public MeetingStatistics(long totalMeetings, long totalMinutes) {
            this.totalMeetings = totalMeetings;
            this.totalMinutes = totalMinutes;
        }
        
        public long getTotalMeetings() {
            return totalMeetings;
        }
        
        public long getTotalMinutes() {
            return totalMinutes;
        }
        
        public double getAverageMinutes() {
            return totalMeetings > 0 ? (double) totalMinutes / totalMeetings : 0;
        }
    }
}
