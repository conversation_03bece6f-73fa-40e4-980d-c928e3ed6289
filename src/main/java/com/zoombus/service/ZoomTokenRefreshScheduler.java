package com.zoombus.service;

import com.zoombus.entity.ZoomAuth;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "zoom.token.refresh.enabled", havingValue = "true", matchIfMissing = true)
public class ZoomTokenRefreshScheduler {

    private final ZoomAuthService zoomAuthService;
    private final AsyncScheduledTaskExecutor asyncTaskExecutor;

    /**
     * 每1分钟检查一次token是否需要刷新
     * 优化后：使用异步执行，独立事务管理，增强错误处理
     */
    @Scheduled(fixedRate = 60000) // 1分钟 = 60,000毫秒
    public void refreshExpiredTokens() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "refreshExpiredTokens",
                "ZOOM_TOKEN_REFRESH",
                this::doRefreshExpiredTokens,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.withRetry(2, 30000) // 最多重试2次，间隔30秒
        );
    }

    /**
     * 执行Token刷新的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doRefreshExpiredTokens() {
        log.debug("🔄 开始定时检查Zoom token状态");

        // 获取刷新前的状态统计
        ZoomAuthService.TokenStatusSummary beforeStatus = zoomAuthService.getTokenStatusSummary();

        // 执行token刷新
        zoomAuthService.refreshExpiredTokens();

        // 获取刷新后的状态统计
        ZoomAuthService.TokenStatusSummary afterStatus = zoomAuthService.getTokenStatusSummary();

        // 如果有状态变化，记录详细信息
        if (beforeStatus.getError() != afterStatus.getError() ||
            beforeStatus.getExpiringSoon() != afterStatus.getExpiringSoon()) {
            log.info("📊 Token状态变化 - 刷新前: {}, 刷新后: {}", beforeStatus, afterStatus);
        }

        log.debug("✅ 定时检查Zoom token状态完成");

        // 构建执行结果
        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();

        int totalTokens = beforeStatus.getTotal();

        result.setProcessedCount(totalTokens);
        result.setSuccessCount(totalTokens - afterStatus.getError());
        result.setFailedCount(afterStatus.getError());
        result.setResultData(String.format("刷新前: %s, 刷新后: %s", beforeStatus, afterStatus));

        return result;
    }
    
    /**
     * 每10分钟输出详细的token状态报告
     * 优化后：使用异步执行，避免阻塞调度器
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600,000毫秒
    public void reportTokenStatus() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "reportTokenStatus",
                "ZOOM_TOKEN_REPORT",
                this::doReportTokenStatus,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.defaultConfig()
        );
    }

    /**
     * 执行Token状态报告的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doReportTokenStatus() {
        ZoomAuthService.TokenStatusSummary status = zoomAuthService.getTokenStatusSummary();
        log.info("📊 Zoom Token状态报告: {}", status);

        int errorCount = 0;
        // 如果有错误状态的token，输出详细信息
        if (status.getError() > 0) {
            List<ZoomAuth> errorTokens = zoomAuthService.getZoomAuthsByStatus(ZoomAuth.AuthStatus.ERROR);
            errorCount = errorTokens.size();
            for (ZoomAuth auth : errorTokens) {
                log.warn("⚠️ ERROR状态Token: {}, 错误信息: {}, 最后刷新: {}",
                        auth.getAccountName(), auth.getErrorMessage(), auth.getLastRefreshAt());
            }
        }

        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
        result.setProcessedCount(status.getTotal());
        result.setSuccessCount(status.getTotal() - errorCount);
        result.setFailedCount(errorCount);
        result.setResultData(status.toString());

        return result;
    }

    /**
     * 每天凌晨2点清理过期的错误记录
     * 优化后：使用异步执行，避免阻塞调度器
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredRecords() {
        // 异步执行任务，避免阻塞调度器
        asyncTaskExecutor.executeAsync(
                "cleanupExpiredRecords",
                "ZOOM_TOKEN_CLEANUP",
                this::doCleanupExpiredRecords,
                AsyncScheduledTaskExecutor.TaskExecutionConfig.defaultConfig()
        );
    }

    /**
     * 执行清理过期记录的具体逻辑
     */
    private AsyncScheduledTaskExecutor.TaskExecutionResult doCleanupExpiredRecords() {
        log.info("🧹 开始清理过期的Zoom认证记录");

        // 这里可以添加清理逻辑，比如清理长时间处于错误状态的记录
        // 目前暂时只记录日志，具体清理逻辑可以根据业务需求添加

        log.info("✅ 清理过期的Zoom认证记录完成");

        AsyncScheduledTaskExecutor.TaskExecutionResult result =
                new AsyncScheduledTaskExecutor.TaskExecutionResult();
        result.setProcessedCount(0); // 暂时没有实际清理操作
        result.setSuccessCount(0);
        result.setFailedCount(0);
        result.setResultData("清理任务完成");

        return result;
    }
}
