package com.zoombus.service;

import com.zoombus.entity.ZoomUser;
import com.zoombus.exception.ResourceNotFoundException;
import com.zoombus.repository.ZoomUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.dto.ZoomApiResponse;

/**
 * ZoomUser PMI管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ZoomUserPmiService {
    
    private final ZoomUserRepository zoomUserRepository;
    private final ZoomApiService zoomApiService;
    
    /**
     * 为会议分配ZoomUser账号
     */
    @Transactional
    public ZoomUser assignZoomUserForMeeting(Long meetingId, String targetPmi) {
        // 查找可用的ZoomUser
        ZoomUser availableUser = findAvailableZoomUser();
        if (availableUser == null) {
            throw new RuntimeException("没有可用的ZoomUser账号");
        }
        
        // 立即设置状态为使用中，防止其他PMI再次使用这个ZoomUser
        availableUser.setUsageStatus(ZoomUser.UsageStatus.IN_USE);
        availableUser.setCurrentMeetingId(meetingId);
        availableUser.setLastUsedTime(LocalDateTime.now());

        // 立即保存状态，确保其他并发请求看到最新状态
        zoomUserRepository.save(availableUser);
        log.info("ZoomUser状态已立即更新为IN_USE: userId={}, meetingId={}", availableUser.getId(), meetingId);
        
        // 更新PMI
        try {
            updateZoomUserPmi(availableUser, targetPmi);
            availableUser.setCurrentPmi(targetPmi);
            availableUser.setPmiUpdatedAt(LocalDateTime.now());
        } catch (Exception e) {
            log.error("更新ZoomUser {} PMI失败", availableUser.getId(), e);
            // 恢复状态
            availableUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
            availableUser.setCurrentMeetingId(null);
            throw new RuntimeException("更新ZoomUser PMI失败: " + e.getMessage());
        }
        
        zoomUserRepository.save(availableUser);
        
        log.info("为会议 {} 分配ZoomUser: {}, PMI: {}", meetingId, availableUser.getEmail(), targetPmi);
        return availableUser;
    }
    
    /**
     * 释放ZoomUser账号
     */
    @Transactional
    public void releaseZoomUser(Long meetingId) {
        // 查找分配给该会议的ZoomUser
        List<ZoomUser> assignedUsers = zoomUserRepository.findByCurrentMeetingId(meetingId);
        
        for (ZoomUser user : assignedUsers) {
            try {
                // 恢复原始PMI（优化版本）
                if (user.getOriginalPmi() != null && !user.getOriginalPmi().equals(user.getCurrentPmi())) {
                    restoreOriginalPmi(user);
                    user.setCurrentPmi(user.getOriginalPmi());
                }

                // 更新使用统计
                if (user.getCurrentMeetingId() != null) {
                    // 这里可以计算会议时长并更新统计
                    user.updateUsageStats(0); // 暂时传0，实际应该计算会议时长
                }

                // 设置状态为可用
                user.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
                user.setCurrentMeetingId(null);
                user.setLastUsedTime(LocalDateTime.now());
                user.setPmiUpdatedAt(LocalDateTime.now());

                zoomUserRepository.save(user);

                log.info("释放ZoomUser: {}, 恢复PMI: {}", user.getEmail(), user.getOriginalPmi());
                
            } catch (Exception e) {
                log.error("释放ZoomUser {} 失败", user.getId(), e);
                // 即使恢复PMI失败，也要释放状态
                user.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
                user.setCurrentMeetingId(null);
                user.setLastUsedTime(LocalDateTime.now());
                zoomUserRepository.save(user);
            }
        }
    }

    /**
     * 恢复ZoomUser的原始PMI设置（优化版本）
     * 按照优化的步骤：1>设置PMI 2>开启等候室 3>设置密码为空
     */
    private void restoreOriginalPmi(ZoomUser zoomUser) {
        try {
            log.info("开始恢复ZoomUser {} 的原始PMI: {}", zoomUser.getEmail(), zoomUser.getOriginalPmi());

            // 使用专用的原始PMI恢复方法（使用ZoomUser对应的ZoomAuth）
            ZoomApiResponse<JsonNode> response = zoomApiService.restoreUserOriginalPmi(
                    zoomUser.getZoomUserId(),
                    zoomUser.getOriginalPmi(),
                    zoomUser.getZoomAuth()
            );

            if (!response.isSuccess()) {
                log.error("恢复原始PMI失败: {}", response.getMessage());
                throw new RuntimeException("恢复原始PMI失败: " + response.getMessage());
            }

            log.info("成功恢复ZoomUser {} 的原始PMI: {}", zoomUser.getEmail(), zoomUser.getOriginalPmi());

        } catch (Exception e) {
            log.error("恢复ZoomUser {} 原始PMI异常", zoomUser.getEmail(), e);
            throw new RuntimeException("恢复原始PMI异常: " + e.getMessage());
        }
    }

    /**
     * 查找可用的ZoomUser
     */
    private ZoomUser findAvailableZoomUser() {
        // 查找状态为AVAILABLE的ZoomUser
        List<ZoomUser> availableUsers = zoomUserRepository.findByUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
        
        if (availableUsers.isEmpty()) {
            return null;
        }
        
        // 优先选择最近最少使用的
        return availableUsers.stream()
            .min((u1, u2) -> {
                LocalDateTime t1 = u1.getLastUsedTime();
                LocalDateTime t2 = u2.getLastUsedTime();
                if (t1 == null && t2 == null) return 0;
                if (t1 == null) return -1;
                if (t2 == null) return 1;
                return t1.compareTo(t2);
            })
            .orElse(availableUsers.get(0));
    }
    
    /**
     * 更新ZoomUser的PMI
     */
    private void updateZoomUserPmi(ZoomUser zoomUser, String newPmi) {
        // 调用Zoom API更新PMI
        try {
            zoomApiService.updateUserPmi(zoomUser.getZoomUserId(), newPmi);
            log.info("成功更新ZoomUser {} PMI: {} -> {}", 
                zoomUser.getEmail(), zoomUser.getCurrentPmi(), newPmi);
        } catch (Exception e) {
            log.error("调用Zoom API更新PMI失败", e);
            throw e;
        }
    }
    
    /**
     * 初始化ZoomUser的原始PMI
     */
    @Transactional
    public void initializeOriginalPmi(Long zoomUserId) {
        ZoomUser zoomUser = zoomUserRepository.findById(zoomUserId)
            .orElseThrow(() -> new ResourceNotFoundException("ZoomUser不存在"));
        
        if (zoomUser.getOriginalPmi() != null) {
            log.info("ZoomUser {} 原始PMI已存在: {}", zoomUser.getEmail(), zoomUser.getOriginalPmi());
            return;
        }
        
        try {
            // 从Zoom API获取用户的默认PMI（使用ZoomUser对应的ZoomAuth）
            String originalPmi = zoomApiService.getUserPmi(zoomUser.getZoomUserId(), zoomUser.getZoomAuth());
            
            zoomUser.setOriginalPmi(originalPmi);
            zoomUser.setCurrentPmi(originalPmi);
            zoomUser.setPmiUpdatedAt(LocalDateTime.now());
            
            zoomUserRepository.save(zoomUser);
            
            log.info("初始化ZoomUser {} 原始PMI: {}", zoomUser.getEmail(), originalPmi);
            
        } catch (Exception e) {
            log.error("初始化ZoomUser {} 原始PMI失败", zoomUser.getId(), e);
            throw new RuntimeException("初始化原始PMI失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量初始化所有ZoomUser的原始PMI
     */
    @Transactional
    public void initializeAllOriginalPmis() {
        List<ZoomUser> users = zoomUserRepository.findByOriginalPmiIsNull();
        
        int successCount = 0;
        int failureCount = 0;
        
        for (ZoomUser user : users) {
            try {
                initializeOriginalPmi(user.getId());
                successCount++;
            } catch (Exception e) {
                log.error("初始化ZoomUser {} 原始PMI失败", user.getId(), e);
                failureCount++;
            }
        }
        
        log.info("批量初始化原始PMI完成: 成功 {}, 失败 {}", successCount, failureCount);
    }
    
    /**
     * 强制释放所有使用中的ZoomUser
     */
    @Transactional
    public void forceReleaseAllZoomUsers() {
        List<ZoomUser> inUseUsers = zoomUserRepository.findByUsageStatus(ZoomUser.UsageStatus.IN_USE);
        
        for (ZoomUser user : inUseUsers) {
            try {
                // 恢复原始PMI
                if (user.getOriginalPmi() != null) {
                    updateZoomUserPmi(user, user.getOriginalPmi());
                    user.setCurrentPmi(user.getOriginalPmi());
                }
                
                // 设置状态为可用
                user.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
                user.setCurrentMeetingId(null);
                user.setLastUsedTime(LocalDateTime.now());
                user.setPmiUpdatedAt(LocalDateTime.now());
                
                zoomUserRepository.save(user);
                
                log.info("强制释放ZoomUser: {}", user.getEmail());
                
            } catch (Exception e) {
                log.error("强制释放ZoomUser {} 失败", user.getId(), e);
            }
        }
        
        log.info("强制释放了 {} 个ZoomUser", inUseUsers.size());
    }
    
    /**
     * 获取ZoomUser使用统计
     */
    public ZoomUserUsageStats getZoomUserUsageStats() {
        long totalUsers = zoomUserRepository.count();
        long availableUsers = zoomUserRepository.countByUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
        long inUseUsers = zoomUserRepository.countByUsageStatus(ZoomUser.UsageStatus.IN_USE);
        long maintenanceUsers = zoomUserRepository.countByUsageStatus(ZoomUser.UsageStatus.MAINTENANCE);
        
        return new ZoomUserUsageStats(totalUsers, availableUsers, inUseUsers, maintenanceUsers);
    }
    
    /**
     * ZoomUser使用统计类
     */
    public static class ZoomUserUsageStats {
        private final long totalUsers;
        private final long availableUsers;
        private final long inUseUsers;
        private final long maintenanceUsers;
        
        public ZoomUserUsageStats(long totalUsers, long availableUsers, long inUseUsers, long maintenanceUsers) {
            this.totalUsers = totalUsers;
            this.availableUsers = availableUsers;
            this.inUseUsers = inUseUsers;
            this.maintenanceUsers = maintenanceUsers;
        }
        
        public long getTotalUsers() { return totalUsers; }
        public long getAvailableUsers() { return availableUsers; }
        public long getInUseUsers() { return inUseUsers; }
        public long getMaintenanceUsers() { return maintenanceUsers; }
        
        public double getUsageRate() {
            return totalUsers > 0 ? (double) inUseUsers / totalUsers : 0;
        }
        
        public boolean isHealthy() {
            return availableUsers > 0; // 至少有一个可用账号
        }
    }

    // ========== 新增PMI管理功能 ==========

    /**
     * 生成随机10位PMI号码
     * 规则：不以0、1开头
     */
    public String generateRandomPmi() {
        Random random = new Random();
        StringBuilder pmi = new StringBuilder();

        // 第一位：2-9
        int[] firstDigits = {2, 3, 4, 5, 6, 7, 8, 9};
        pmi.append(firstDigits[random.nextInt(firstDigits.length)]);

        // 后9位：0-9
        int[] otherDigits = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
        for (int i = 0; i < 9; i++) {
            pmi.append(otherDigits[random.nextInt(otherDigits.length)]);
        }

        return pmi.toString();
    }

    /**
     * 验证PMI号码格式
     */
    public boolean isValidPmi(String pmi) {
        if (pmi == null || pmi.length() != 10) {
            return false;
        }

        // 检查是否全为数字
        if (!pmi.matches("\\d{10}")) {
            return false;
        }

        // 检查第一位不能是0或1
        char firstChar = pmi.charAt(0);
        if (firstChar == '0' || firstChar == '1') {
            return false;
        }

        return true;
    }

    /**
     * 检查PMI是否已被使用
     */
    public boolean isPmiInUse(String pmi) {
        return zoomUserRepository.existsByCurrentPmi(pmi) ||
               zoomUserRepository.existsByOriginalPmi(pmi);
    }

    /**
     * 检查PMI是否已被其他用户使用（排除指定用户）
     */
    public boolean isPmiInUseByOthers(String pmi, Long excludeUserId) {
        // 查找使用此PMI的用户
        List<ZoomUser> usersWithCurrentPmi = zoomUserRepository.findByCurrentPmi(pmi);
        List<ZoomUser> usersWithOriginalPmi = zoomUserRepository.findByOriginalPmi(pmi);

        // 检查是否有其他用户在使用这个PMI
        boolean currentPmiUsedByOthers = usersWithCurrentPmi.stream()
            .anyMatch(user -> !user.getId().equals(excludeUserId));

        boolean originalPmiUsedByOthers = usersWithOriginalPmi.stream()
            .anyMatch(user -> !user.getId().equals(excludeUserId));

        return currentPmiUsedByOthers || originalPmiUsedByOthers;
    }

    /**
     * 生成唯一的PMI号码
     */
    public String generateUniquePmi() {
        String pmi;
        int attempts = 0;
        do {
            pmi = generateRandomPmi();
            attempts++;
            if (attempts > 100) {
                throw new RuntimeException("无法生成唯一的PMI号码，请稍后重试");
            }
        } while (isPmiInUse(pmi));

        return pmi;
    }

    /**
     * 更新用户原始PMI
     * 会调用Zoom API设置PMI，成功后才更新数据库
     */
    @Transactional
    public ZoomApiResponse<String> updateUserOriginalPmi(Long userId, String newPmi) {
        log.info("开始更新用户原始PMI: userId={}, newPmi={}", userId, newPmi);

        // 验证PMI格式
        if (!isValidPmi(newPmi)) {
            return ZoomApiResponse.error("PMI格式不正确：必须是10位数字，不以0、1开头", "INVALID_PMI_FORMAT");
        }

        // 查找用户
        ZoomUser zoomUser = zoomUserRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 检查PMI是否已被其他用户使用（排除当前用户）
        if (isPmiInUseByOthers(newPmi, userId)) {
            return ZoomApiResponse.error("PMI号码已被其他用户使用", "PMI_ALREADY_IN_USE");
        }

        try {
            // 调用Zoom API设置PMI（使用ZoomUser对应的ZoomAuth）
            ZoomApiResponse<JsonNode> apiResponse = zoomApiService.updateUserPmi(
                    zoomUser.getZoomUserId(), newPmi, null, zoomUser.getZoomAuth());

            if (!apiResponse.isSuccess()) {
                log.error("Zoom API设置PMI失败: {}", apiResponse.getMessage());
                return ZoomApiResponse.error("设置PMI失败: " + apiResponse.getMessage(),
                        apiResponse.getErrorCode());
            }

            // API调用成功，更新数据库
            String oldOriginalPmi = zoomUser.getOriginalPmi();
            zoomUser.setOriginalPmi(newPmi);
            zoomUser.setCurrentPmi(newPmi);
            zoomUser.setPmiUpdatedAt(LocalDateTime.now());
            zoomUser.setUpdatedAt(LocalDateTime.now());

            zoomUserRepository.save(zoomUser);

            log.info("用户原始PMI更新成功: userId={}, oldPmi={}, newPmi={}",
                    userId, oldOriginalPmi, newPmi);

            return ZoomApiResponse.successWithMessage("PMI更新成功");

        } catch (Exception e) {
            log.error("更新用户原始PMI异常: userId={}, newPmi={}", userId, newPmi, e);
            return ZoomApiResponse.error("更新PMI异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 回收用户账号
     * 结束当前会议，恢复原始PMI，设置状态为可用
     */
    @Transactional
    public ZoomApiResponse<String> recycleUserAccount(Long userId) {
        log.info("开始回收用户账号: userId={}", userId);

        // 查找用户
        ZoomUser zoomUser = zoomUserRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        try {
            // 如果有进行中的会议，先结束会议
            if (zoomUser.getCurrentMeetingId() != null) {
                log.info("结束用户当前会议: userId={}, meetingId={}",
                        userId, zoomUser.getCurrentMeetingId());

                // 这里需要调用结束会议的API，暂时跳过具体实现
                log.warn("会议结束功能待实现: meetingId={}", zoomUser.getCurrentMeetingId());
            }

            // 恢复原始PMI
            String originalPmi = zoomUser.getOriginalPmi();
            if (originalPmi != null && !originalPmi.equals(zoomUser.getCurrentPmi())) {
                log.info("恢复用户原始PMI: userId={}, originalPmi={}", userId, originalPmi);

                ZoomApiResponse<JsonNode> pmiResponse = zoomApiService.updateUserPmi(
                        zoomUser.getZoomUserId(), originalPmi, null, zoomUser.getZoomAuth());

                if (!pmiResponse.isSuccess()) {
                    log.error("恢复原始PMI失败: {}", pmiResponse.getMessage());
                    return ZoomApiResponse.error("恢复原始PMI失败: " + pmiResponse.getMessage(),
                            pmiResponse.getErrorCode());
                }

                zoomUser.setCurrentPmi(originalPmi);
            }

            // 更新账号状态
            zoomUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
            zoomUser.setCurrentMeetingId(null);
            zoomUser.setLastUsedTime(LocalDateTime.now());
            zoomUser.setPmiUpdatedAt(LocalDateTime.now());
            zoomUser.setUpdatedAt(LocalDateTime.now());

            zoomUserRepository.save(zoomUser);

            log.info("用户账号回收成功: userId={}", userId);
            return ZoomApiResponse.successWithMessage("账号回收成功");

        } catch (Exception e) {
            log.error("回收用户账号异常: userId={}", userId, e);
            return ZoomApiResponse.error("回收账号异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }
}
