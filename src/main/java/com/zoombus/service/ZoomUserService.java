package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.zoombus.dto.CreateZoomUserRequest;
import com.zoombus.dto.UpdateZoomUserRequest;
import com.zoombus.dto.SyncUsersResult;
import com.zoombus.dto.ZoomApiResponse;
import com.zoombus.entity.User;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.entity.ZoomUser;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.ZoomAuthRepository;
import com.zoombus.repository.ZoomUserRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ZoomUserService {

    private final ZoomUserRepository zoomUserRepository;
    private final ZoomAuthRepository zoomAuthRepository;
    private final ZoomApiService zoomApiService;
    private final UserService userService;
    private final ZoomMeetingRepository zoomMeetingRepository;

    /**
     * 创建Zoom用户
     */
    @Transactional
    public ZoomUser createZoomUser(CreateZoomUserRequest request) {
        // 检查用户是否存在
        User user = userService.getUserById(request.getUserId())
                .orElseThrow(() -> new RuntimeException("用户不存在: " + request.getUserId()));

        // 检查ZoomAuth是否存在
        ZoomAuth zoomAuth = zoomAuthRepository.findById(request.getZoomAuthId())
                .orElseThrow(() -> new RuntimeException("Zoom认证信息不存在: " + request.getZoomAuthId()));

        // 检查邮箱是否已存在于该ZoomAuth下
        if (zoomUserRepository.findByZoomAuthAndEmail(zoomAuth, request.getEmail()).isPresent()) {
            throw new RuntimeException("该邮箱在此Zoom账号下已存在: " + request.getEmail());
        }

        // 调用Zoom API创建用户
        ZoomApiResponse<JsonNode> apiResponse = zoomApiService.createUserWithAuth(request, zoomAuth);
        if (!apiResponse.isSuccess()) {
            throw new RuntimeException("创建Zoom用户失败: " + apiResponse.getMessage());
        }

        // 从API响应中获取用户信息
        JsonNode userData = apiResponse.getData();
        String zoomUserId = userData.get("id").asText();

        // 创建ZoomUser实体
        ZoomUser zoomUser = new ZoomUser();
        zoomUser.setZoomAuth(zoomAuth);
        zoomUser.setUser(user);  // 设置关联的用户
        zoomUser.setZoomUserId(zoomUserId);
        zoomUser.setEmail(request.getEmail());
        zoomUser.setFirstName(request.getFirstName());
        zoomUser.setLastName(request.getLastName());
        zoomUser.setUserType(request.getUserType());
        zoomUser.setStatus(ZoomUser.UserStatus.ACTIVE);
        zoomUser.setDepartment(request.getDepartment());
        zoomUser.setJobTitle(request.getJobTitle());
        zoomUser.setPhoneNumber(request.getPhoneNumber());
        zoomUser.setTimezone(request.getTimezone());
        zoomUser.setLanguage(request.getLanguage());
        zoomUser.setAccountUsage(request.getAccountUsage());

        // 从API响应中设置其他字段
        if (userData.has("created_at")) {
            try {
                String createdAtStr = userData.get("created_at").asText();
                LocalDateTime createdAt = LocalDateTime.parse(createdAtStr, DateTimeFormatter.ISO_DATE_TIME);
                zoomUser.setZoomCreatedAt(createdAt);
            } catch (Exception e) {
                log.warn("解析创建时间失败: {}", userData.get("created_at").asText());
            }
        }

        ZoomUser savedUser = zoomUserRepository.save(zoomUser);
        log.info("Zoom用户创建成功: {}, ZoomUserId: {}", savedUser.getEmail(), savedUser.getZoomUserId());
        return savedUser;
    }

    /**
     * 更新Zoom用户
     */
    @Transactional
    public ZoomUser updateZoomUser(Long id, UpdateZoomUserRequest request) {
        // 查找现有的ZoomUser
        ZoomUser zoomUser = zoomUserRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Zoom用户不存在: " + id));

        // 更新用户关联
        if (request.getUserId() != null) {
            User user = userService.getUserById(request.getUserId())
                    .orElseThrow(() -> new RuntimeException("用户不存在: " + request.getUserId()));
            zoomUser.setUser(user);
        } else {
            zoomUser.setUser(null); // 取消关联
        }

        // 更新其他字段（如果提供了值）
        if (request.getEmail() != null) {
            zoomUser.setEmail(request.getEmail());
        }
        if (request.getFirstName() != null) {
            zoomUser.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            zoomUser.setLastName(request.getLastName());
        }
        if (request.getDisplayName() != null) {
            zoomUser.setDisplayName(request.getDisplayName());
        }
        if (request.getUserType() != null) {
            zoomUser.setUserType(request.getUserType());
        }
        if (request.getStatus() != null) {
            zoomUser.setStatus(request.getStatus());
        }
        if (request.getDepartment() != null) {
            zoomUser.setDepartment(request.getDepartment());
        }
        if (request.getJobTitle() != null) {
            zoomUser.setJobTitle(request.getJobTitle());
        }
        if (request.getPhoneNumber() != null) {
            zoomUser.setPhoneNumber(request.getPhoneNumber());
        }
        if (request.getTimezone() != null) {
            zoomUser.setTimezone(request.getTimezone());
        }
        if (request.getLanguage() != null) {
            zoomUser.setLanguage(request.getLanguage());
        }
        if (request.getAccountUsage() != null) {
            zoomUser.setAccountUsage(request.getAccountUsage());
        }

        ZoomUser savedUser = zoomUserRepository.save(zoomUser);
        log.info("Zoom用户更新成功: {}, ZoomUserId: {}", savedUser.getEmail(), savedUser.getZoomUserId());
        return savedUser;
    }

    /**
     * 获取所有Zoom用户（分页）
     */
    @Transactional(readOnly = true)
    public Page<ZoomUser> getAllZoomUsers(Pageable pageable) {
        // 使用自定义排序：优先显示专业版且使用中的用户
        return zoomUserRepository.findAllWithPrioritySort(pageable);
    }
    
    /**
     * 根据ZoomAuth获取用户列表（分页）
     */
    @Transactional(readOnly = true)
    public Page<ZoomUser> getZoomUsersByAuth(Long zoomAuthId, Pageable pageable) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        // 使用自定义排序：优先显示专业版且使用中的用户
        return zoomUserRepository.findByZoomAuthWithPrioritySort(zoomAuth, pageable);
    }
    
    /**
     * 根据ID获取Zoom用户
     */
    @Transactional(readOnly = true)
    public Optional<ZoomUser> getZoomUserById(Long id) {
        return zoomUserRepository.findById(id);
    }
    
    /**
     * 根据ZoomAuth和ZoomUserId获取用户
     */
    @Transactional(readOnly = true)
    public Optional<ZoomUser> getZoomUserByZoomUserId(Long zoomAuthId, String zoomUserId) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        return zoomUserRepository.findByZoomAuthAndZoomUserId(zoomAuth, zoomUserId);
    }
    
    /**
     * 根据ZoomAuth和邮箱获取用户
     */
    @Transactional(readOnly = true)
    public Optional<ZoomUser> getZoomUserByEmail(Long zoomAuthId, String email) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        return zoomUserRepository.findByZoomAuthAndEmail(zoomAuth, email);
    }
    
    /**
     * 根据状态获取用户列表
     */
    @Transactional(readOnly = true)
    public List<ZoomUser> getZoomUsersByStatus(Long zoomAuthId, ZoomUser.UserStatus status) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        return zoomUserRepository.findByZoomAuthAndStatus(zoomAuth, status);
    }
    
    /**
     * 根据用户类型获取用户列表
     */
    @Transactional(readOnly = true)
    public List<ZoomUser> getZoomUsersByUserType(Long zoomAuthId, ZoomUser.UserType userType) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        return zoomUserRepository.findByZoomAuthAndUserType(zoomAuth, userType);
    }

    /**
     * 根据用户类型和账号用途获取用户列表（全局搜索）
     */
    @Transactional(readOnly = true)
    public List<ZoomUser> getZoomUsersByUserTypeAndAccountUsage(ZoomUser.UserType userType, ZoomUser.AccountUsage accountUsage) {
        return zoomUserRepository.findByUserTypeAndAccountUsage(userType, accountUsage);
    }

    /**
     * 根据用户ID获取ZoomUser列表
     */
    @Transactional(readOnly = true)
    public List<ZoomUser> getZoomUsersByUserId(Long userId) {
        User user = userService.getUserById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));
        return zoomUserRepository.findByUser(user);
    }

    /**
     * 根据用户ID获取ZoomUser列表（分页）
     */
    @Transactional(readOnly = true)
    public Page<ZoomUser> getZoomUsersByUserId(Long userId, Pageable pageable) {
        User user = userService.getUserById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));
        return zoomUserRepository.findByUser(user, pageable);
    }

    /**
     * 根据ZoomUser ID获取单个Zoom用户（用于从PMI管理页面跳转）
     */
    @Transactional(readOnly = true)
    public Page<ZoomUser> getZoomUserByZoomUserId(Long zoomUserId, Pageable pageable) {
        // 首先验证ZoomUser是否存在
        ZoomUser zoomUser = zoomUserRepository.findById(zoomUserId)
                .orElseThrow(() -> new RuntimeException("Zoom用户不存在: " + zoomUserId));

        // 返回包含单个ZoomUser的Page对象
        List<ZoomUser> zoomUsers = Arrays.asList(zoomUser);
        return new PageImpl<>(zoomUsers, pageable, 1);
    }

    /**
     * 根据用户类型和账号用途搜索用户（支持关键词搜索）
     */
    @Transactional(readOnly = true)
    public Page<ZoomUser> searchZoomUsersByUserTypeAndAccountUsage(ZoomUser.UserType userType,
                                                                 ZoomUser.AccountUsage accountUsage,
                                                                 String keyword,
                                                                 Pageable pageable) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // 如果没有关键词，返回所有符合条件的用户
            List<ZoomUser> users = zoomUserRepository.findByUserTypeAndAccountUsage(userType, accountUsage);
            return new PageImpl<>(users, pageable, users.size());
        }
        return zoomUserRepository.searchByUserTypeAndAccountUsageAndKeyword(userType, accountUsage, keyword.trim(), pageable);
    }
    
    /**
     * 搜索用户（根据邮箱）
     */
    @Transactional(readOnly = true)
    public Page<ZoomUser> searchZoomUsersByEmail(Long zoomAuthId, String email, Pageable pageable) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        // 搜索时也使用优先排序
        return zoomUserRepository.searchByZoomAuthAndEmailWithPrioritySort(zoomAuth, email, pageable);
    }
    
    /**
     * 搜索用户（根据姓名）
     */
    @Transactional(readOnly = true)
    public Page<ZoomUser> searchZoomUsersByName(Long zoomAuthId, String name, Pageable pageable) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        // 搜索时也使用优先排序
        return zoomUserRepository.searchByZoomAuthAndNameWithPrioritySort(zoomAuth, name, pageable);
    }

    /**
     * 全局搜索用户（根据邮箱）
     */
    @Transactional(readOnly = true)
    public Page<ZoomUser> globalSearchZoomUsersByEmail(String email, Pageable pageable) {
        // 全局搜索时也使用优先排序
        return zoomUserRepository.globalSearchByEmailWithPrioritySort(email, pageable);
    }

    /**
     * 全局搜索用户（根据姓名）
     */
    @Transactional(readOnly = true)
    public Page<ZoomUser> globalSearchZoomUsersByName(String name, Pageable pageable) {
        // 全局搜索时也使用优先排序
        return zoomUserRepository.globalSearchByNameWithPrioritySort(name, pageable);
    }
    
    /**
     * 统计ZoomAuth下的用户数量
     */
    @Transactional(readOnly = true)
    public long countZoomUsersByAuth(Long zoomAuthId) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        return zoomUserRepository.countByZoomAuth(zoomAuth);
    }
    
    /**
     * 统计各状态的用户数量
     */
    @Transactional(readOnly = true)
    public List<Object[]> countZoomUsersByStatus(Long zoomAuthId) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        return zoomUserRepository.countByZoomAuthGroupByStatus(zoomAuth);
    }
    
    /**
     * 统计各用户类型的数量
     */
    @Transactional(readOnly = true)
    public List<Object[]> countZoomUsersByUserType(Long zoomAuthId) {
        ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));
        return zoomUserRepository.countByZoomAuthGroupByUserType(zoomAuth);
    }

    /**
     * 统计所有用户数量
     */
    @Transactional(readOnly = true)
    public long countAllZoomUsers() {
        return zoomUserRepository.count();
    }

    /**
     * 统计所有用户各状态的数量
     */
    @Transactional(readOnly = true)
    public List<Object[]> countAllZoomUsersByStatus() {
        return zoomUserRepository.countAllGroupByStatus();
    }

    /**
     * 统计所有用户各类型的数量
     */
    @Transactional(readOnly = true)
    public List<Object[]> countAllZoomUsersByUserType() {
        return zoomUserRepository.countAllGroupByUserType();
    }
    
    /**
     * 删除Zoom用户
     */
    @Transactional
    public void deleteZoomUser(Long id) {
        ZoomUser zoomUser = zoomUserRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Zoom用户不存在: " + id));
        
        zoomUserRepository.delete(zoomUser);
        log.info("删除Zoom用户成功: {}", zoomUser.getEmail());
    }
    
    /**
     * 批量删除Zoom用户
     */
    @Transactional
    public void deleteZoomUsers(List<Long> ids) {
        List<ZoomUser> users = zoomUserRepository.findAllById(ids);
        zoomUserRepository.deleteAll(users);
        log.info("批量删除Zoom用户成功，数量: {}", users.size());
    }
    
    /**
     * 从Zoom API同步单个用户信息
     */
    @Transactional
    public ZoomUser syncZoomUserInfo(Long id) {
        ZoomUser zoomUser = zoomUserRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Zoom用户不存在: " + id));
        
        // 从Zoom API获取最新用户信息（使用ZoomUser对应的ZoomAuth）
        ZoomApiResponse<JsonNode> apiResponse = zoomApiService.getUser(zoomUser.getZoomUserId(), zoomUser.getZoomAuth());
        if (!apiResponse.isSuccess()) {
            throw new RuntimeException("获取Zoom用户信息失败: " + apiResponse.getMessage() +
                    " (ZoomAuth: " + zoomUser.getZoomAuth().getAccountName() + ")");
        }
        
        JsonNode userData = apiResponse.getData();
        updateZoomUserFromApiData(zoomUser, userData);
        
        ZoomUser updatedUser = zoomUserRepository.save(zoomUser);
        log.info("同步Zoom用户信息成功: {}", updatedUser.getEmail());
        return updatedUser;
    }
    
    /**
     * 从API数据更新ZoomUser实体
     * 注意：不会更新accountUsage字段，以保留手动设置的账户用途
     */
    private void updateZoomUserFromApiData(ZoomUser zoomUser, JsonNode userData) {
        // 保存当前的accountUsage值，确保不会被覆盖
        ZoomUser.AccountUsage currentAccountUsage = zoomUser.getAccountUsage();

        if (userData.has("email")) {
            zoomUser.setEmail(userData.get("email").asText());
        }
        if (userData.has("first_name")) {
            zoomUser.setFirstName(userData.get("first_name").asText());
        }
        if (userData.has("last_name")) {
            zoomUser.setLastName(userData.get("last_name").asText());
        }
        if (userData.has("display_name")) {
            zoomUser.setDisplayName(userData.get("display_name").asText());
        }
        if (userData.has("type")) {
            int userType = userData.get("type").asInt();
            zoomUser.setUserType(ZoomUser.UserType.fromValue(userType));
        }
        if (userData.has("status")) {
            String status = userData.get("status").asText();
            zoomUser.setStatus(ZoomUser.UserStatus.fromValue(status));
        }

        // 恢复原来的accountUsage值，确保不会被API同步覆盖
        zoomUser.setAccountUsage(currentAccountUsage);
        if (userData.has("dept")) {
            zoomUser.setDepartment(userData.get("dept").asText());
        }
        if (userData.has("job_title")) {
            zoomUser.setJobTitle(userData.get("job_title").asText());
        }
        if (userData.has("phone_number")) {
            zoomUser.setPhoneNumber(userData.get("phone_number").asText());
        }
        if (userData.has("timezone")) {
            zoomUser.setTimezone(userData.get("timezone").asText());
        }
        if (userData.has("language")) {
            zoomUser.setLanguage(userData.get("language").asText());
        }
        if (userData.has("created_at")) {
            try {
                String createdAtStr = userData.get("created_at").asText();
                LocalDateTime createdAt = LocalDateTime.parse(createdAtStr, 
                    DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
                zoomUser.setZoomCreatedAt(createdAt);
            } catch (Exception e) {
                log.warn("解析创建时间失败: {}", userData.get("created_at").asText());
            }
        }
        if (userData.has("last_login_time")) {
            try {
                String lastLoginStr = userData.get("last_login_time").asText();
                LocalDateTime lastLogin = LocalDateTime.parse(lastLoginStr,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
                zoomUser.setZoomLastLoginTime(lastLogin);
            } catch (Exception e) {
                log.warn("解析最后登录时间失败: {}", userData.get("last_login_time").asText());
            }
        }

        // 处理PMI字段 - 保持与Zoom端一致
        if (userData.has("pmi") && !userData.get("pmi").isNull()) {
            String pmiFromApi = userData.get("pmi").asText().trim();
            if (!pmiFromApi.isEmpty()) {
                // 保存当前的original_pmi值，避免被覆盖
                String currentOriginalPmi = zoomUser.getOriginalPmi();

                // 如果是新用户（original_pmi为空），则设置original_pmi
                if (currentOriginalPmi == null || currentOriginalPmi.trim().isEmpty()) {
                    zoomUser.setOriginalPmi(pmiFromApi);
                    zoomUser.setCurrentPmi(pmiFromApi);
                    zoomUser.setPmiUpdatedAt(LocalDateTime.now());
                    log.info("为用户 {} 设置原始PMI: {}", zoomUser.getEmail(), pmiFromApi);
                } else {
                    // 如果是已存在用户，保持original_pmi不变，但始终更新current_pmi以与Zoom端保持一致
                    zoomUser.setOriginalPmi(currentOriginalPmi);

                    // 检查current_pmi是否需要更新
                    String currentPmi = zoomUser.getCurrentPmi();
                    if (!pmiFromApi.equals(currentPmi)) {
                        zoomUser.setCurrentPmi(pmiFromApi);
                        zoomUser.setPmiUpdatedAt(LocalDateTime.now());
                        log.info("为用户 {} 更新当前PMI: {} -> {} (保持与Zoom端一致)",
                            zoomUser.getEmail(), currentPmi, pmiFromApi);
                    } else {
                        log.debug("用户 {} 的当前PMI无需更新: {}", zoomUser.getEmail(), currentPmi);
                    }
                }
            }
        }

        // 处理主持人密钥
        log.debug("API响应数据字段: {}", userData.fieldNames());
        if (userData.has("host_key") && !userData.get("host_key").isNull()) {
            String hostKey = userData.get("host_key").asText().trim();
            if (!hostKey.isEmpty()) {
                zoomUser.setHostKey(hostKey);
                log.info("更新用户主持人密钥: {}, hostKey: {}", zoomUser.getEmail(), hostKey);
            }
        } else {
            log.warn("API响应中未包含host_key字段，用户: {}, 可用字段: {}", zoomUser.getEmail(), userData.fieldNames());
        }
    }



    /**
     * 从Zoom API同步指定ZoomAuth的所有用户信息
     * 注意：此方法不使用事务注解，避免与子事务冲突
     */
    public SyncUsersResult syncUsersFromZoomApi(Long zoomAuthId) {
        try {
            ZoomAuth zoomAuth = zoomAuthRepository.findById(zoomAuthId)
                    .orElseThrow(() -> new RuntimeException("认证信息不存在: " + zoomAuthId));

            log.info("开始同步用户信息，账号: {}, ZoomAccountId: {}", zoomAuth.getAccountName(), zoomAuth.getZoomAccountId());

            // 使用 zoomAccountId 而不是数据库 id 来获取用户列表
            ZoomApiResponse<JsonNode> apiResponse = zoomApiService.getAllUsers(zoomAuth);
            if (!apiResponse.isSuccess()) {
                log.error("获取Zoom用户列表失败，账号: {}, 错误: {}", zoomAuth.getAccountName(), apiResponse.getMessage());
                return SyncUsersResult.error("获取Zoom用户列表失败: " + apiResponse.getMessage());
            }

            JsonNode usersData = apiResponse.getData();
            JsonNode usersArray = usersData.get("users");

            if (usersArray == null || !usersArray.isArray()) {
                log.warn("Zoom API返回的用户数据格式不正确，账号: {}", zoomAuth.getAccountName());
                return SyncUsersResult.error("Zoom API返回的用户数据格式不正确");
            }

            int totalUsers = usersArray.size();
            int newUsers = 0;
            int updatedUsers = 0;
            int skippedUsers = 0;
            List<String> errors = new ArrayList<>();

            for (JsonNode userNode : usersArray) {
                try {
                    String zoomUserId = userNode.get("id").asText();
                    String email = userNode.get("email").asText();

                    // 检查是否是新用户还是更新
                    Optional<ZoomUser> existingUser = zoomUserRepository.findByZoomAuthAndZoomUserId(zoomAuth, zoomUserId);
                    boolean isNewUser = !existingUser.isPresent();

                    // 直接在这里处理用户同步，避免Bean依赖问题
                    boolean success = processSingleUser(zoomAuth, userNode, zoomUserId, email);

                    if (success) {
                        if (isNewUser) {
                            newUsers++;
                            log.debug("新增用户: {}", email);
                        } else {
                            updatedUsers++;
                            log.debug("更新用户: {}", email);
                        }
                    } else {
                        skippedUsers++;
                    }
                } catch (Exception e) {
                    String errorMsg = "处理用户失败: " + e.getMessage();
                    errors.add(errorMsg);
                    log.error("处理用户失败，账号: {}, 错误: {}", zoomAuth.getAccountName(), e.getMessage(), e);
                    skippedUsers++;
                }
            }

            SyncUsersResult.SyncStats stats = new SyncUsersResult.SyncStats(
                    totalUsers, newUsers, updatedUsers, skippedUsers, errors);

            log.info("用户同步完成，账号: {}, 总用户: {}, 新增: {}, 更新: {}, 跳过: {}, 错误: {}",
                    zoomAuth.getAccountName(), totalUsers, newUsers, updatedUsers, skippedUsers, errors.size());

            // 验证同步结果
            long actualCount = zoomUserRepository.countByZoomAuth(zoomAuth);
            log.info("同步后数据库中实际用户数量: {}", actualCount);

            return SyncUsersResult.success(stats);

        } catch (Exception e) {
            log.error("同步用户失败，ID: {}, 错误: {}", zoomAuthId, e.getMessage(), e);
            return SyncUsersResult.error("同步用户失败: " + e.getMessage());
        }
    }

    /**
     * 处理单个用户的同步
     * 每个用户在独立的事务中处理
     */
    @Transactional
    private boolean processSingleUser(ZoomAuth zoomAuth, JsonNode userNode, String zoomUserId, String email) {
        try {
            // 查找是否已存在该用户
            Optional<ZoomUser> existingUser = zoomUserRepository.findByZoomAuthAndZoomUserId(zoomAuth, zoomUserId);

            if (existingUser.isPresent()) {
                // 更新现有用户
                ZoomUser user = existingUser.get();
                updateZoomUserFromJson(user, userNode);
                ZoomUser savedUser = zoomUserRepository.save(user);
                log.info("更新用户成功: {}, ID: {}", email, savedUser.getId());
                return true;
            } else {
                // 创建新用户
                ZoomUser newUser = createZoomUserFromJson(zoomAuth, userNode);
                ZoomUser savedUser = zoomUserRepository.save(newUser);
                log.info("新增用户成功: {}, ID: {}", email, savedUser.getId());
                return true;
            }
        } catch (Exception e) {
            log.error("处理单个用户失败，用户: {}, 错误: {}", email, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从JSON数据创建ZoomUser对象
     */
    private ZoomUser createZoomUserFromJson(ZoomAuth zoomAuth, JsonNode userNode) {
        ZoomUser user = new ZoomUser();
        user.setId(null); // 确保ID为null，让数据库自动生成
        user.setZoomAuth(zoomAuth);
        user.setUser(null); // 新用户暂时不关联系统用户

        // 设置默认值
        user.setAccountUsage(null); // 新用户暂时不设置账户用途，等待手动分配
        user.setStatus(ZoomUser.UserStatus.ACTIVE); // 设置默认状态
        user.setUserType(ZoomUser.UserType.BASIC); // 设置默认用户类型

        // 初始化PMI相关字段
        user.setOriginalPmi(null); // 确保为null，让updateZoomUserFromJson正确处理
        user.setCurrentPmi(null);
        user.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
        user.setTotalUsageCount(0);
        user.setTotalUsageMinutes(0);

        updateZoomUserFromJson(user, userNode);
        return user;
    }

    /**
     * 从JSON数据更新ZoomUser对象
     * 注意：不会更新accountUsage字段，以保留手动设置的账户用途
     */
    private void updateZoomUserFromJson(ZoomUser user, JsonNode userNode) {
        log.info("开始处理用户PMI，用户: {}, API数据包含PMI: {}",
            user.getEmail(), userNode.has("pmi"));

        // 保存当前的accountUsage值，确保不会被覆盖
        ZoomUser.AccountUsage currentAccountUsage = user.getAccountUsage();

        // 保存当前的original_pmi值，避免被覆盖
        String currentOriginalPmi = user.getOriginalPmi();

        log.info("用户 {} 当前original_pmi: {}", user.getEmail(), currentOriginalPmi);

        // 验证必填字段
        if (!userNode.has("id") || userNode.get("id").isNull() || userNode.get("id").asText().trim().isEmpty()) {
            throw new RuntimeException("Zoom用户ID不能为空");
        }
        if (!userNode.has("email") || userNode.get("email").isNull() || userNode.get("email").asText().trim().isEmpty()) {
            throw new RuntimeException("用户邮箱不能为空");
        }

        user.setZoomUserId(userNode.get("id").asText().trim());
        user.setEmail(userNode.get("email").asText().trim());

        if (userNode.has("first_name") && !userNode.get("first_name").isNull()) {
            user.setFirstName(userNode.get("first_name").asText());
        }

        if (userNode.has("last_name") && !userNode.get("last_name").isNull()) {
            user.setLastName(userNode.get("last_name").asText());
        }

        if (userNode.has("display_name") && !userNode.get("display_name").isNull()) {
            user.setDisplayName(userNode.get("display_name").asText());
        }

        if (userNode.has("type")) {
            int typeValue = userNode.get("type").asInt();
            user.setUserType(ZoomUser.UserType.fromValue(typeValue));
        }

        // 恢复原来的accountUsage值，确保不会被API同步覆盖
        user.setAccountUsage(currentAccountUsage);

        if (userNode.has("status")) {
            String statusStr = userNode.get("status").asText();
            user.setStatus(ZoomUser.UserStatus.fromValue(statusStr));
        }

        if (userNode.has("dept") && !userNode.get("dept").isNull()) {
            user.setDepartment(userNode.get("dept").asText());
        }

        if (userNode.has("job_title") && !userNode.get("job_title").isNull()) {
            user.setJobTitle(userNode.get("job_title").asText());
        }

        if (userNode.has("phone_number") && !userNode.get("phone_number").isNull()) {
            user.setPhoneNumber(userNode.get("phone_number").asText());
        }

        if (userNode.has("timezone") && !userNode.get("timezone").isNull()) {
            user.setTimezone(userNode.get("timezone").asText());
        }

        if (userNode.has("language") && !userNode.get("language").isNull()) {
            user.setLanguage(userNode.get("language").asText());
        }

        if (userNode.has("created_at") && !userNode.get("created_at").isNull()) {
            try {
                String createdAtStr = userNode.get("created_at").asText();
                // Zoom API 通常返回 ISO 8601 格式的时间
                LocalDateTime createdAt = LocalDateTime.parse(createdAtStr.replace("Z", ""),
                        DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                user.setZoomCreatedAt(createdAt);
            } catch (Exception e) {
                log.warn("解析创建时间失败: {}", userNode.get("created_at").asText());
            }
        }

        if (userNode.has("last_login_time") && !userNode.get("last_login_time").isNull()) {
            try {
                String lastLoginStr = userNode.get("last_login_time").asText();
                LocalDateTime lastLogin = LocalDateTime.parse(lastLoginStr.replace("Z", ""),
                        DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                user.setZoomLastLoginTime(lastLogin);
            } catch (Exception e) {
                log.warn("解析最后登录时间失败: {}", userNode.get("last_login_time").asText());
            }
        }

        // 处理PMI字段
        if (userNode.has("pmi") && !userNode.get("pmi").isNull()) {
            String pmiFromApi = userNode.get("pmi").asText().trim();
            log.info("API返回的PMI值: {}", pmiFromApi);

            // 如果是新用户（original_pmi为空），则设置original_pmi
            if (currentOriginalPmi == null || currentOriginalPmi.trim().isEmpty()) {
                user.setOriginalPmi(pmiFromApi);
                user.setCurrentPmi(pmiFromApi);
                user.setPmiUpdatedAt(LocalDateTime.now());
                log.info("为用户 {} 设置原始PMI: {}", user.getEmail(), pmiFromApi);
            } else {
                // 如果是已存在用户，保持original_pmi不变，但始终更新current_pmi以与Zoom端保持一致
                user.setOriginalPmi(currentOriginalPmi);

                // 检查current_pmi是否需要更新
                String currentPmi = user.getCurrentPmi();
                if (!pmiFromApi.equals(currentPmi)) {
                    user.setCurrentPmi(pmiFromApi);
                    user.setPmiUpdatedAt(LocalDateTime.now());
                    log.info("为用户 {} 更新当前PMI: {} -> {} (保持与Zoom端一致)",
                        user.getEmail(), currentPmi, pmiFromApi);
                } else {
                    log.debug("用户 {} 的当前PMI无需更新: {}", user.getEmail(), currentPmi);
                }
            }
        } else {
            // 如果API没有返回PMI，保持原有值
            log.warn("用户 {} 的API数据中没有PMI字段或PMI为空", user.getEmail());
            user.setOriginalPmi(currentOriginalPmi);
        }

        // 注意：Zoom API已不再返回host_key字段
        // 主持人密钥需要通过手工重置功能来设置
    }



    /**
     * 批量同步多个ZoomAuth的子账号用户信息
     */
    public List<SyncUsersResult> batchSyncUsersFromZoomApi(List<Long> zoomAuthIds) {
        List<SyncUsersResult> results = new ArrayList<>();

        for (Long id : zoomAuthIds) {
            try {
                SyncUsersResult result = syncUsersFromZoomApi(id);
                results.add(result);
            } catch (Exception e) {
                log.error("批量同步用户失败，ID: {}, 错误: {}", id, e.getMessage(), e);
                results.add(SyncUsersResult.error("同步失败: " + e.getMessage()));
            }
        }

        return results;
    }

    // ========== Zoom会议事件处理 ==========

    /**
     * 处理zoom.started事件
     * 按照meeting_id和host_id查找t_zoom_meetings里状态为活跃【PENDING-待开启、USING-进行中】的记录
     * 如果PENDING则更新为USING，USING则保持不变，以便开始计费
     *
     * @param meetingId Zoom会议ID
     * @param hostId 主持人ID
     * @return 处理结果
     */
    @Transactional
    public ZoomApiResponse<String> handleMeetingStarted(String meetingId, String hostId) {
        log.info("处理zoom.started事件: meetingId={}, hostId={}", meetingId, hostId);

        try {
            // 查找活跃状态的会议记录（PENDING或USING）
            List<ZoomMeeting> activeMeetings = zoomMeetingRepository.findByZoomMeetingId(meetingId);

            if (activeMeetings.isEmpty()) {
                log.warn("未找到会议记录: meetingId={}, hostId={}", meetingId, hostId);
                return ZoomApiResponse.error("未找到会议记录", "MEETING_NOT_FOUND");
            }

            // 过滤出匹配hostId且状态为活跃的会议
            List<ZoomMeeting> matchingMeetings = activeMeetings.stream()
                    .filter(meeting -> hostId.equals(meeting.getHostId()))
                    .filter(meeting -> meeting.getStatus() == ZoomMeeting.MeetingStatus.WAITING ||
                                     meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED)
                    .collect(java.util.stream.Collectors.toList());

            if (matchingMeetings.isEmpty()) {
                log.warn("未找到匹配的活跃会议记录: meetingId={}, hostId={}", meetingId, hostId);
                return ZoomApiResponse.error("未找到匹配的活跃会议记录", "ACTIVE_MEETING_NOT_FOUND");
            }

            int updatedCount = 0;
            for (ZoomMeeting meeting : matchingMeetings) {
                if (meeting.getStatus() == ZoomMeeting.MeetingStatus.WAITING) {
                    // WAITING状态更新为STARTED，开始计费
                    meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
                    meeting.setStartTime(LocalDateTime.now());
                    meeting.setUpdatedAt(LocalDateTime.now());

                    zoomMeetingRepository.save(meeting);
                    updatedCount++;

                    log.info("会议状态已更新为STARTED，开始计费: meetingId={}, hostId={}, recordId={}",
                            meetingId, hostId, meeting.getId());
                } else if (meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED) {
                    // STARTED状态保持不变
                    log.info("会议已在进行中，状态保持不变: meetingId={}, hostId={}, recordId={}",
                            meetingId, hostId, meeting.getId());
                }
            }

            String message = String.format("处理完成，更新了%d条记录", updatedCount);
            log.info("zoom.started事件处理完成: meetingId={}, hostId={}, updatedCount={}",
                    meetingId, hostId, updatedCount);

            return ZoomApiResponse.success(message);

        } catch (Exception e) {
            log.error("处理zoom.started事件异常: meetingId={}, hostId={}", meetingId, hostId, e);
            return ZoomApiResponse.error("处理事件异常: " + e.getMessage(), "INTERNAL_ERROR");
        }
    }

    /**
     * 查找活跃状态的会议记录
     * 用于验证和调试
     *
     * @param meetingId Zoom会议ID
     * @param hostId 主持人ID
     * @return 活跃会议列表
     */
    public List<ZoomMeeting> findActiveMeetings(String meetingId, String hostId) {
        List<ZoomMeeting> allMeetings = zoomMeetingRepository.findByZoomMeetingId(meetingId);

        return allMeetings.stream()
                .filter(meeting -> hostId.equals(meeting.getHostId()))
                .filter(meeting -> meeting.getStatus() == ZoomMeeting.MeetingStatus.WAITING ||
                                 meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取会议状态统计信息
     * 用于监控和调试
     *
     * @param meetingId Zoom会议ID
     * @return 状态统计
     */
    public java.util.Map<String, Object> getMeetingStatusStats(String meetingId) {
        List<ZoomMeeting> meetings = zoomMeetingRepository.findByZoomMeetingId(meetingId);

        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalMeetings", meetings.size());
        stats.put("waitingCount", meetings.stream()
                .mapToInt(m -> m.getStatus() == ZoomMeeting.MeetingStatus.WAITING ? 1 : 0)
                .sum());
        stats.put("startedCount", meetings.stream()
                .mapToInt(m -> m.getStatus() == ZoomMeeting.MeetingStatus.STARTED ? 1 : 0)
                .sum());
        stats.put("endedCount", meetings.stream()
                .mapToInt(m -> m.getStatus() == ZoomMeeting.MeetingStatus.ENDED ? 1 : 0)
                .sum());
        stats.put("settledCount", meetings.stream()
                .mapToInt(m -> m.getStatus() == ZoomMeeting.MeetingStatus.SETTLED ? 1 : 0)
                .sum());

        return stats;
    }

    /**
     * 生成主持人密钥
     */
    private String generateHostKey() {
        // 生成6位数字的host_key
        java.util.Random random = new java.util.Random();
        return String.format("%06d", random.nextInt(1000000));
    }

    /**
     * 通过API设置用户的host_key到Zoom
     */
    private void setUserHostKeyToZoom(String zoomUserId, String hostKey) {
        try {
            // 构建请求体
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.databind.node.ObjectNode requestBody = mapper.createObjectNode();
            requestBody.put("host_key", hostKey);

            // 通过ZoomApiService设置用户host_key
            ZoomApiResponse<JsonNode> response = zoomApiService.updateUser(zoomUserId, requestBody);

            if (response.isSuccess()) {
                log.info("成功设置用户host_key到Zoom: {}", zoomUserId);
            } else {
                log.error("设置用户host_key到Zoom失败: {}, 错误: {}", zoomUserId, response.getMessage());
                throw new RuntimeException("设置host_key失败: " + response.getMessage());
            }
        } catch (Exception e) {
            log.error("设置用户host_key到Zoom时发生异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 手工重置用户的host_key
     * @param userId 用户ID
     * @return 重置结果
     */
    @Transactional
    public Map<String, Object> resetUserHostKey(Long userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查找用户
            Optional<ZoomUser> userOpt = zoomUserRepository.findById(userId);
            if (!userOpt.isPresent()) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            ZoomUser user = userOpt.get();
            String generatedHostKey = generateHostKey();

            log.info("手工重置用户主持人密钥: {}, 新hostKey: {}", user.getEmail(), generatedHostKey);

            // 通过API设置用户的host_key到Zoom
            setUserHostKeyToZoom(user.getZoomUserId(), generatedHostKey);

            // 只有API调用成功才更新本地数据库
            user.setHostKey(generatedHostKey);
            zoomUserRepository.save(user);

            result.put("success", true);
            result.put("message", "主持人密钥重置成功");
            result.put("hostKey", generatedHostKey);

            log.info("成功重置用户主持人密钥: {}, hostKey: {}", user.getEmail(), generatedHostKey);

        } catch (Exception e) {
            log.error("重置用户主持人密钥失败: userId={}, 错误: {}", userId, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "重置失败: " + e.getMessage());
        }

        return result;
    }
}
