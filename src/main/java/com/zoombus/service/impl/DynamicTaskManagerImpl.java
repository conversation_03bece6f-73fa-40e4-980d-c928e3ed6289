package com.zoombus.service.impl;

import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.entity.PmiRecord;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import com.zoombus.repository.PmiScheduleWindowRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.PmiScheduleRepository;
import com.zoombus.service.DynamicTaskManager;
import com.zoombus.service.PmiWindowTaskExecutor;
import com.zoombus.service.TaskExecutionStrategyDecider;
import com.zoombus.config.ImmediateExecutionConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

/**
 * 动态任务管理器实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DynamicTaskManagerImpl implements DynamicTaskManager {
    
    private final TaskScheduler taskScheduler;
    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiScheduleWindowRepository windowRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final PmiScheduleRepository pmiScheduleRepository;
    private final PmiWindowTaskExecutor taskExecutor;
    private final TaskExecutionStrategyDecider strategyDecider;
    private final ImmediateExecutionConfig immediateExecutionConfig;
    
    // 存储任务键到ScheduledFuture的映射
    private final ConcurrentHashMap<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    
    @PostConstruct
    @Override
    public void initializePmiScheduledTasks() {
        log.info("开始初始化PMI定时任务...");
        
        try {
            // 查找所有未完成的任务
            List<PmiScheduleWindowTask> pendingTasks = taskRepository.findByStatus(
                PmiScheduleWindowTask.TaskStatus.SCHEDULED);
            
            LocalDateTime now = LocalDateTime.now();
            int rescheduledCount = 0;
            int expiredCount = 0;
            
            for (PmiScheduleWindowTask task : pendingTasks) {
                if (task.getScheduledTime().isBefore(now)) {
                    // 时间已过期，标记为过期并立即执行
                    handleExpiredPmiTask(task);
                    expiredCount++;
                } else {
                    // 重新调度任务
                    reschedulePmiTask(task);
                    rescheduledCount++;
                }
            }
            
            log.info("PMI定时任务初始化完成: 重新调度={}, 过期处理={}", rescheduledCount, expiredCount);
            
        } catch (Exception e) {
            log.error("初始化PMI定时任务失败", e);
        }
    }
    
    @Override
    @Transactional
    public Long schedulePmiWindowOpenTask(Long windowId, LocalDateTime executeTime) {
        log.info("调度PMI窗口开启任务: windowId={}, executeTime={}", windowId, executeTime);

        // 判断执行策略
        TaskExecutionStrategyDecider.ExecutionStrategy strategy =
            strategyDecider.decideStrategy(windowId, executeTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);

        log.info("任务执行策略: windowId={}, strategy={}", windowId, strategy);

        switch (strategy) {
            case IMMEDIATE:
                return executeImmediately(windowId, executeTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);

            case SCHEDULED:
                return scheduleForFuture(windowId, executeTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);

            case EXPIRED:
                return markAsExpired(windowId, executeTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);

            default:
                throw new IllegalStateException("未知的执行策略: " + strategy);
        }
    }
    
    @Override
    @Transactional
    public Long schedulePmiWindowCloseTask(Long windowId, LocalDateTime executeTime) {
        log.info("调度PMI窗口关闭任务: windowId={}, executeTime={}", windowId, executeTime);

        // 判断执行策略
        TaskExecutionStrategyDecider.ExecutionStrategy strategy =
            strategyDecider.decideStrategy(windowId, executeTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_CLOSE);

        log.info("任务执行策略: windowId={}, strategy={}", windowId, strategy);

        switch (strategy) {
            case IMMEDIATE:
                return executeImmediately(windowId, executeTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_CLOSE);

            case SCHEDULED:
                return scheduleForFuture(windowId, executeTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_CLOSE);

            case EXPIRED:
                return markAsExpired(windowId, executeTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_CLOSE);

            default:
                throw new IllegalStateException("未知的执行策略: " + strategy);
        }
    }
    
    @Override
    @Transactional
    public boolean cancelTask(String taskKey) {
        log.info("取消任务: taskKey={}", taskKey);
        
        try {
            // 取消调度的任务
            ScheduledFuture<?> future = scheduledTasks.remove(taskKey);
            if (future != null) {
                future.cancel(false);
            }
            
            // 更新数据库记录
            Optional<PmiScheduleWindowTask> taskOpt = taskRepository.findByTaskKey(taskKey);
            if (taskOpt.isPresent()) {
                PmiScheduleWindowTask task = taskOpt.get();
                if (task.canCancel()) {
                    task.markAsCancelled();
                    taskRepository.save(task);
                    log.info("任务取消成功: taskKey={}", taskKey);
                    return true;
                } else {
                    log.warn("任务无法取消，当前状态: {}", task.getStatus());
                    return false;
                }
            } else {
                log.warn("未找到任务记录: taskKey={}", taskKey);
                return false;
            }
            
        } catch (Exception e) {
            log.error("取消任务失败: taskKey={}", taskKey, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public Long rescheduleTask(String oldTaskKey, LocalDateTime newExecuteTime) {
        log.info("重新调度任务: oldTaskKey={}, newExecuteTime={}", oldTaskKey, newExecuteTime);

        try {
            // 取消原任务
            cancelTask(oldTaskKey);

            // 获取原任务信息
            Optional<PmiScheduleWindowTask> oldTaskOpt = taskRepository.findByTaskKey(oldTaskKey);
            if (!oldTaskOpt.isPresent()) {
                throw new RuntimeException("未找到原任务: " + oldTaskKey);
            }

            PmiScheduleWindowTask oldTask = oldTaskOpt.get();

            // 创建新任务
            if (oldTask.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                return schedulePmiWindowOpenTask(oldTask.getPmiWindowId(), newExecuteTime);
            } else {
                return schedulePmiWindowCloseTask(oldTask.getPmiWindowId(), newExecuteTime);
            }

        } catch (Exception e) {
            log.error("重新调度任务失败: oldTaskKey={}", oldTaskKey, e);
            throw new RuntimeException("重新调度任务失败", e);
        }
    }
    
    @Override
    public List<ScheduledTaskInfo> getUpcomingTasks(int hours) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime maxTime = now.plusHours(hours);
        
        List<PmiScheduleWindowTask> tasks = taskRepository.findUpcomingTasks(
            PmiScheduleWindowTask.TaskStatus.SCHEDULED, maxTime);
        
        return tasks.stream()
            .map(this::convertToScheduledTaskInfo)
            .collect(Collectors.toList());
    }
    
    @Override
    public TaskStatus getTaskStatus(String taskKey) {
        Optional<PmiScheduleWindowTask> taskOpt = taskRepository.findByTaskKey(taskKey);
        if (taskOpt.isPresent()) {
            PmiScheduleWindowTask.TaskStatus status = taskOpt.get().getStatus();
            return TaskStatus.valueOf(status.name());
        }
        return null;
    }
    
    @Override
    @Transactional
    public int batchCancelTasks(List<String> taskKeys) {
        int cancelledCount = 0;
        for (String taskKey : taskKeys) {
            if (cancelTask(taskKey)) {
                cancelledCount++;
            }
        }
        return cancelledCount;
    }
    
    @Override
    @Transactional
    public int cleanupExpiredTasks(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        
        List<PmiScheduleWindowTask> expiredTasks = taskRepository.findAll().stream()
            .filter(task -> task.getCreatedAt().isBefore(cutoffTime))
            .filter(task -> task.getStatus() == PmiScheduleWindowTask.TaskStatus.COMPLETED ||
                           task.getStatus() == PmiScheduleWindowTask.TaskStatus.CANCELLED ||
                           task.getStatus() == PmiScheduleWindowTask.TaskStatus.FAILED)
            .collect(Collectors.toList());
        
        taskRepository.deleteAll(expiredTasks);
        
        log.info("清理过期任务完成: 清理数量={}", expiredTasks.size());
        return expiredTasks.size();
    }
    
    @Override
    public SchedulerStatus getSchedulerStatus() {
        int totalScheduled = scheduledTasks.size();
        int runningTasks = (int) taskRepository.countByStatus(PmiScheduleWindowTask.TaskStatus.EXECUTING);
        
        return new SchedulerStatus(
            true, 
            totalScheduled, 
            runningTasks, 
            LocalDateTime.now(), 
            "HEALTHY"
        );
    }

    /**
     * 生成任务键
     */
    private String generateTaskKey(String prefix, Long windowId) {
        return String.format("%s_%d_%d", prefix, windowId, System.currentTimeMillis());
    }

    /**
     * 创建任务记录
     */
    private PmiScheduleWindowTask createTaskRecord(Long windowId,
                                                  PmiScheduleWindowTask.TaskType taskType,
                                                  LocalDateTime executeTime,
                                                  String taskKey) {
        PmiScheduleWindowTask task = new PmiScheduleWindowTask();
        task.setPmiWindowId(windowId);
        task.setTaskType(taskType);
        task.setScheduledTime(executeTime);
        task.setTaskKey(taskKey);
        task.setStatus(PmiScheduleWindowTask.TaskStatus.SCHEDULED);
        task.setRetryCount(0);
        return task;
    }

    /**
     * 处理过期的PMI任务
     */
    private void handleExpiredPmiTask(PmiScheduleWindowTask task) {
        log.warn("处理过期PMI任务: taskId={}, scheduledTime={}, taskType={}",
                task.getId(), task.getScheduledTime(), task.getTaskType());

        LocalDateTime now = LocalDateTime.now();
        long delayMinutes = java.time.Duration.between(task.getScheduledTime(), now).toMinutes();

        if (task.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
            // 开启任务：检查是否还在窗口关闭时间之前
            if (isOpenTaskStillValid(task, now)) {
                log.info("开启任务仍在有效期内，立即执行: taskId={}, delay={}分钟", task.getId(), delayMinutes);
                taskExecutor.executeOpenTask(task.getPmiWindowId(), task.getId());
            } else {
                log.warn("开启任务已超过窗口关闭时间，标记为失败: taskId={}, delay={}分钟", task.getId(), delayMinutes);
                task.markAsFailed("任务过期，窗口已关闭，延迟" + delayMinutes + "分钟");
                taskRepository.save(task);
            }
        } else {
            // 关闭任务：检查PMI的当前窗口是否仍然是该任务对应的窗口
            if (isCloseTaskStillResponsible(task, now)) {
                log.info("关闭任务仍有责任关闭PMI，立即执行: taskId={}, delay={}分钟", task.getId(), delayMinutes);
                taskExecutor.executeCloseTask(task.getPmiWindowId(), task.getId());
            } else {
                log.warn("关闭任务已无责任关闭PMI，标记为失败: taskId={}, delay={}分钟", task.getId(), delayMinutes);
                task.markAsFailed("任务过期，PMI当前窗口已变更，延迟" + delayMinutes + "分钟");
                taskRepository.save(task);
            }
        }
    }

    /**
     * 检查开启任务是否仍然有效
     * 开启任务只要在窗口关闭时间之前都认为是有效的
     */
    private boolean isOpenTaskStillValid(PmiScheduleWindowTask task, LocalDateTime now) {
        try {
            // 获取PMI窗口信息
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(task.getPmiWindowId());
            if (!windowOpt.isPresent()) {
                log.warn("PMI窗口不存在，开启任务无效: windowId={}", task.getPmiWindowId());
                return false;
            }

            PmiScheduleWindow window = windowOpt.get();
            LocalDateTime windowCloseTime = window.getEndDateTime();

            // 如果当前时间还在窗口关闭时间之前，则任务仍然有效
            boolean isValid = now.isBefore(windowCloseTime);

            log.debug("检查开启任务有效性: taskId={}, windowCloseTime={}, now={}, isValid={}",
                    task.getId(), windowCloseTime, now, isValid);

            return isValid;

        } catch (Exception e) {
            log.error("检查开启任务有效性失败: taskId={}", task.getId(), e);
            // 出现异常时，为了安全起见，认为任务无效
            return false;
        }
    }

    /**
     * 检查关闭任务是否仍然有责任关闭PMI
     * 如果PMI的当前窗口仍然是该任务对应的窗口，则关闭任务有责任关闭它
     */
    private boolean isCloseTaskStillResponsible(PmiScheduleWindowTask task, LocalDateTime now) {
        try {
            // 获取PMI窗口信息
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(task.getPmiWindowId());
            if (!windowOpt.isPresent()) {
                log.warn("PMI窗口不存在，关闭任务无责任: windowId={}", task.getPmiWindowId());
                return false;
            }

            PmiScheduleWindow window = windowOpt.get();

            // 获取该窗口对应的PMI记录
            Long pmiRecordId = getPmiRecordIdFromWindow(window);
            if (pmiRecordId == null) {
                log.warn("无法获取PMI记录ID，关闭任务无责任: windowId={}", task.getPmiWindowId());
                return false;
            }

            // 获取PMI记录
            Optional<PmiRecord> pmiRecordOpt = pmiRecordRepository.findById(pmiRecordId);
            if (!pmiRecordOpt.isPresent()) {
                log.warn("PMI记录不存在，关闭任务无责任: pmiRecordId={}", pmiRecordId);
                return false;
            }

            PmiRecord pmiRecord = pmiRecordOpt.get();

            // 检查PMI的当前窗口是否仍然是该任务对应的窗口
            boolean isResponsible = Objects.equals(pmiRecord.getCurrentWindowId(), task.getPmiWindowId());

            log.debug("检查关闭任务责任: taskId={}, windowId={}, pmiCurrentWindowId={}, isResponsible={}",
                    task.getId(), task.getPmiWindowId(), pmiRecord.getCurrentWindowId(), isResponsible);

            return isResponsible;

        } catch (Exception e) {
            log.error("检查关闭任务责任失败: taskId={}", task.getId(), e);
            // 出现异常时，为了安全起见，认为任务仍有责任（避免PMI无法关闭）
            return true;
        }
    }

    /**
     * 从窗口获取PMI记录ID
     */
    private Long getPmiRecordIdFromWindow(PmiScheduleWindow window) {
        // 直接从窗口获取PMI记录ID
        if (window.getPmiRecordId() != null) {
            return window.getPmiRecordId();
        }

        // 如果窗口没有直接的PMI记录ID，通过计划ID查找
        if (window.getScheduleId() != null) {
            return pmiScheduleRepository.findById(window.getScheduleId())
                    .map(schedule -> schedule.getPmiRecordId())
                    .orElse(null);
        }

        return null;
    }

    /**
     * 重新调度PMI任务
     */
    private void reschedulePmiTask(PmiScheduleWindowTask task) {
        try {
            ScheduledFuture<?> future;
            if (task.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                future = taskScheduler.schedule(
                    () -> taskExecutor.executeOpenTask(task.getPmiWindowId(), task.getId()),
                    Date.from(task.getScheduledTime().atZone(ZoneId.systemDefault()).toInstant())
                );
            } else {
                future = taskScheduler.schedule(
                    () -> taskExecutor.executeCloseTask(task.getPmiWindowId(), task.getId()),
                    Date.from(task.getScheduledTime().atZone(ZoneId.systemDefault()).toInstant())
                );
            }

            scheduledTasks.put(task.getTaskKey(), future);
            log.info("重新调度PMI任务成功: taskId={}, taskKey={}", task.getId(), task.getTaskKey());

        } catch (Exception e) {
            log.error("重新调度PMI任务失败: taskId={}", task.getId(), e);
            task.markAsFailed("重新调度失败: " + e.getMessage());
            taskRepository.save(task);
        }
    }

    /**
     * 转换为调度任务信息
     */
    private ScheduledTaskInfo convertToScheduledTaskInfo(PmiScheduleWindowTask task) {
        ScheduledTaskInfo info = new ScheduledTaskInfo(
            task.getTaskKey(),
            task.getPmiWindowId(),
            task.getTaskType(),
            task.getScheduledTime(),
            task.getStatus()
        );

        // 获取关联的PMI信息
        try {
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(task.getPmiWindowId());
            if (windowOpt.isPresent()) {
                PmiScheduleWindow window = windowOpt.get();
                Optional<PmiRecord> pmiOpt = pmiRecordRepository.findById(window.getPmiRecordId());
                if (pmiOpt.isPresent()) {
                    PmiRecord pmi = pmiOpt.get();
                    info.setPmiNumber(pmi.getPmiNumber());
                    // 这里可以根据需要添加用户名信息
                }
            }
        } catch (Exception e) {
            log.warn("获取PMI信息失败: windowId={}", task.getPmiWindowId(), e);
        }

        return info;
    }

    /**
     * 立即执行任务
     */
    private Long executeImmediately(Long windowId, LocalDateTime originalTime, PmiScheduleWindowTask.TaskType taskType) {
        log.info("立即执行任务: windowId={}, originalTime={}, taskType={}", windowId, originalTime, taskType);

        String taskKey = generateTaskKey(taskType == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN ? "PMI_OPEN" : "PMI_CLOSE", windowId);

        try {
            // 创建任务记录
            PmiScheduleWindowTask task = createTaskRecord(windowId, taskType, originalTime, taskKey);
            task = taskRepository.save(task);
            final Long taskId = task.getId();

            // 异步立即执行
            CompletableFuture.runAsync(() -> {
                try {
                    if (taskType == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                        taskExecutor.executeOpenTask(windowId, taskId);
                    } else {
                        taskExecutor.executeCloseTask(windowId, taskId);
                    }
                } catch (Exception e) {
                    log.error("立即执行任务失败: taskId={}", taskId, e);
                }
            });

            log.info("任务已提交立即执行: taskKey={}, taskId={}", taskKey, taskId);
            return taskId;

        } catch (Exception e) {
            log.error("立即执行任务失败: windowId={}", windowId, e);
            throw new RuntimeException("立即执行任务失败", e);
        }
    }

    /**
     * 调度到未来执行（原有逻辑）
     */
    private Long scheduleForFuture(Long windowId, LocalDateTime executeTime, PmiScheduleWindowTask.TaskType taskType) {
        log.info("调度任务到未来执行: windowId={}, executeTime={}, taskType={}", windowId, executeTime, taskType);

        String taskKey = generateTaskKey(taskType == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN ? "PMI_OPEN" : "PMI_CLOSE", windowId);

        try {
            // 创建任务记录
            PmiScheduleWindowTask task = createTaskRecord(windowId, taskType, executeTime, taskKey);
            task = taskRepository.save(task);
            final Long taskId = task.getId();

            // 调度任务
            ScheduledFuture<?> future = taskScheduler.schedule(
                () -> {
                    if (taskType == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                        taskExecutor.executeOpenTask(windowId, taskId);
                    } else {
                        taskExecutor.executeCloseTask(windowId, taskId);
                    }
                },
                Date.from(executeTime.atZone(ZoneId.systemDefault()).toInstant())
            );

            scheduledTasks.put(taskKey, future);

            log.info("任务调度成功: taskKey={}, taskId={}", taskKey, taskId);
            return taskId;

        } catch (Exception e) {
            log.error("调度任务失败: windowId={}", windowId, e);
            throw new RuntimeException("调度任务失败", e);
        }
    }

    /**
     * 标记任务为过期
     */
    private Long markAsExpired(Long windowId, LocalDateTime executeTime, PmiScheduleWindowTask.TaskType taskType) {
        log.warn("任务已过期，标记为失败: windowId={}, executeTime={}, taskType={}", windowId, executeTime, taskType);

        String taskKey = generateTaskKey(taskType == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN ? "PMI_OPEN" : "PMI_CLOSE", windowId);

        try {
            // 创建任务记录
            PmiScheduleWindowTask task = createTaskRecord(windowId, taskType, executeTime, taskKey);
            task.markAsFailed("任务创建时已过期");
            task = taskRepository.save(task);

            log.info("过期任务已标记为失败: taskKey={}, taskId={}", taskKey, task.getId());
            return task.getId();

        } catch (Exception e) {
            log.error("标记过期任务失败: windowId={}", windowId, e);
            throw new RuntimeException("标记过期任务失败", e);
        }
    }

    /**
     * 重新调度卡住的任务
     */
    @Transactional
    public boolean rescheduleStuckTask(PmiScheduleWindowTask stuckTask) {
        try {
            log.info("重新调度卡住的任务: taskId={}, taskKey={}", stuckTask.getId(), stuckTask.getTaskKey());

            // 取消原有的调度（如果存在）
            ScheduledFuture<?> existingFuture = scheduledTasks.remove(stuckTask.getTaskKey());
            if (existingFuture != null && !existingFuture.isDone()) {
                existingFuture.cancel(false);
                log.info("已取消原有调度: taskKey={}", stuckTask.getTaskKey());
            }

            // 重新调度任务
            ScheduledFuture<?> future;
            if (stuckTask.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                future = taskScheduler.schedule(
                    () -> taskExecutor.executeOpenTask(stuckTask.getPmiWindowId(), stuckTask.getId()),
                    Date.from(stuckTask.getScheduledTime().atZone(ZoneId.systemDefault()).toInstant())
                );
            } else {
                future = taskScheduler.schedule(
                    () -> taskExecutor.executeCloseTask(stuckTask.getPmiWindowId(), stuckTask.getId()),
                    Date.from(stuckTask.getScheduledTime().atZone(ZoneId.systemDefault()).toInstant())
                );
            }

            // 保存调度结果
            scheduledTasks.put(stuckTask.getTaskKey(), future);
            log.info("任务重新调度成功: taskId={}, taskKey={}", stuckTask.getId(), stuckTask.getTaskKey());
            return true;

        } catch (Exception e) {
            log.error("重新调度卡住任务失败: taskId={}", stuckTask.getId(), e);
            return false;
        }
    }

    /**
     * 检查任务是否已被正确调度
     */
    public boolean isTaskProperlyScheduled(String taskKey) {
        ScheduledFuture<?> future = scheduledTasks.get(taskKey);
        return future != null && !future.isDone() && !future.isCancelled();
    }

    /**
     * 获取调度器中的任务数量
     */
    public int getScheduledTaskCount() {
        return scheduledTasks.size();
    }

    /**
     * 清理已完成的调度任务
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void cleanupCompletedScheduledTasks() {
        try {
            int initialSize = scheduledTasks.size();
            scheduledTasks.entrySet().removeIf(entry -> {
                ScheduledFuture<?> future = entry.getValue();
                return future.isDone() || future.isCancelled();
            });
            int cleanedCount = initialSize - scheduledTasks.size();

            if (cleanedCount > 0) {
                log.debug("清理已完成的调度任务: 清理数量={}, 剩余数量={}", cleanedCount, scheduledTasks.size());
            }
        } catch (Exception e) {
            log.error("清理已完成调度任务失败", e);
        }
    }
}
