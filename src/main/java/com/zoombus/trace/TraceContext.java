package com.zoombus.trace;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;

/**
 * TraceId上下文管理
 * 使用ThreadLocal存储当前线程的TraceId和相关上下文信息
 */
@Slf4j
public class TraceContext {
    
    private static final String TRACE_ID_KEY = "traceId";
    private static final ThreadLocal<String> TRACE_ID = new ThreadLocal<>();
    private static final ThreadLocal<Map<String, Object>> CONTEXT = new ThreadLocal<>();
    
    /**
     * 设置当前线程的TraceId
     */
    public static void setTraceId(String traceId) {
        if (traceId != null && !traceId.trim().isEmpty()) {
            TRACE_ID.set(traceId);
            MDC.put(TRACE_ID_KEY, traceId);
            initContext();
            log.debug("设置TraceId: {}", traceId);
        }
    }
    
    /**
     * 获取当前线程的TraceId
     */
    public static String getTraceId() {
        return TRACE_ID.get();
    }
    
    /**
     * 添加上下文信息
     */
    public static void addContext(String key, Object value) {
        if (key == null || value == null) {
            return;
        }
        
        Map<String, Object> context = CONTEXT.get();
        if (context != null) {
            context.put(key, value);
            
            // 同时添加到MDC中，便于日志输出
            if (value instanceof String) {
                MDC.put(key, (String) value);
            } else {
                MDC.put(key, value.toString());
            }
            
            log.debug("添加上下文: {}={}", key, value);
        }
    }
    
    /**
     * 获取上下文信息
     */
    public static Object getContext(String key) {
        Map<String, Object> context = CONTEXT.get();
        return context != null ? context.get(key) : null;
    }
    
    /**
     * 获取所有上下文信息
     */
    public static Map<String, Object> getAllContext() {
        Map<String, Object> context = CONTEXT.get();
        return context != null ? new HashMap<>(context) : new HashMap<>();
    }
    
    /**
     * 设置用户ID
     */
    public static void setUserId(String userId) {
        addContext("userId", userId);
    }
    
    /**
     * 获取用户ID
     */
    public static String getUserId() {
        Object userId = getContext("userId");
        return userId != null ? userId.toString() : null;
    }
    
    /**
     * 设置请求URI
     */
    public static void setRequestUri(String requestUri) {
        addContext("requestUri", requestUri);
    }
    
    /**
     * 获取请求URI
     */
    public static String getRequestUri() {
        Object requestUri = getContext("requestUri");
        return requestUri != null ? requestUri.toString() : null;
    }
    
    /**
     * 设置HTTP方法
     */
    public static void setMethod(String method) {
        addContext("method", method);
    }
    
    /**
     * 获取HTTP方法
     */
    public static String getMethod() {
        Object method = getContext("method");
        return method != null ? method.toString() : null;
    }
    
    /**
     * 设置Webhook相关信息
     */
    public static void setWebhookInfo(String source, String eventType, String eventId) {
        addContext("webhookSource", source);
        addContext("webhookEventType", eventType);
        addContext("webhookEventId", eventId);
    }
    
    /**
     * 清除当前线程的所有上下文信息
     */
    public static void clear() {
        String traceId = TRACE_ID.get();
        if (traceId != null) {
            log.debug("清除TraceId上下文: {}", traceId);
        }
        
        TRACE_ID.remove();
        CONTEXT.remove();
        MDC.clear();
    }
    
    /**
     * 复制当前上下文到新线程
     * 用于异步任务中传递TraceId
     */
    public static TraceSnapshot createSnapshot() {
        String traceId = getTraceId();
        Map<String, Object> context = getAllContext();
        Map<String, String> mdcContext = MDC.getCopyOfContextMap();
        
        return new TraceSnapshot(traceId, context, mdcContext);
    }
    
    /**
     * 从快照恢复上下文
     */
    public static void restoreFromSnapshot(TraceSnapshot snapshot) {
        if (snapshot == null) {
            return;
        }
        
        // 恢复TraceId
        if (snapshot.getTraceId() != null) {
            setTraceId(snapshot.getTraceId());
        }
        
        // 恢复上下文
        if (snapshot.getContext() != null) {
            Map<String, Object> context = CONTEXT.get();
            if (context != null) {
                context.putAll(snapshot.getContext());
            }
        }
        
        // 恢复MDC
        if (snapshot.getMdcContext() != null) {
            MDC.setContextMap(snapshot.getMdcContext());
        }
    }
    
    /**
     * 初始化上下文Map
     */
    private static void initContext() {
        if (CONTEXT.get() == null) {
            CONTEXT.set(new HashMap<>());
        }
    }
    
    /**
     * TraceId快照，用于跨线程传递
     */
    public static class TraceSnapshot {
        private final String traceId;
        private final Map<String, Object> context;
        private final Map<String, String> mdcContext;
        
        public TraceSnapshot(String traceId, Map<String, Object> context, Map<String, String> mdcContext) {
            this.traceId = traceId;
            this.context = context;
            this.mdcContext = mdcContext;
        }
        
        public String getTraceId() {
            return traceId;
        }
        
        public Map<String, Object> getContext() {
            return context;
        }
        
        public Map<String, String> getMdcContext() {
            return mdcContext;
        }
    }
}
