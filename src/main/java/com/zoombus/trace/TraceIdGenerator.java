package com.zoombus.trace;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

/**
 * TraceId生成器
 * 格式：{timestamp}-{nodeId}-{sequence}-{random}
 * 示例：20250823143025-001-000001-a1b2c3
 */
@Component
@Slf4j
public class TraceIdGenerator {
    
    private static final AtomicLong SEQUENCE = new AtomicLong(0);
    private static final SecureRandom RANDOM = new SecureRandom();
    private static final SimpleDateFormat TIMESTAMP_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");
    
    @Value("${app.trace.node-id:001}")
    private String nodeId;
    
    /**
     * 生成标准TraceId
     */
    public String generateTraceId() {
        long timestamp = System.currentTimeMillis();
        long sequence = SEQUENCE.incrementAndGet() % 1000000;
        String random = generateRandomString();
        
        String traceId = String.format("%s-%s-%06d-%s",
            TIMESTAMP_FORMAT.format(new Date(timestamp)),
            nodeId,
            sequence,
            random);
            
        log.debug("生成TraceId: {}", traceId);
        return traceId;
    }
    
    /**
     * 生成Webhook专用TraceId
     * 格式：WH-{timestamp}-{source}-{eventId}-{random}
     */
    public String generateWebhookTraceId(String source, String eventId) {
        long timestamp = System.currentTimeMillis();
        String random = generateRandomString();
        
        // 确保source和eventId不为空且长度合适
        source = sanitizeComponent(source, "UNKNOWN");
        eventId = sanitizeComponent(eventId, "EVENT");
        
        String traceId = String.format("WH-%s-%s-%s-%s",
            TIMESTAMP_FORMAT.format(new Date(timestamp)),
            source,
            eventId,
            random);
            
        log.debug("生成Webhook TraceId: {}", traceId);
        return traceId;
    }
    
    /**
     * 增强Webhook TraceId，添加业务信息
     */
    public String enhanceWebhookTraceId(String originalTraceId, String eventType, String businessId) {
        if (originalTraceId == null) {
            return generateWebhookTraceId("UNKNOWN", "EVENT");
        }
        
        // 清理业务信息
        eventType = sanitizeComponent(eventType, "UNKNOWN");
        businessId = sanitizeComponent(businessId, "");
        
        String enhanced = originalTraceId;
        if (!eventType.equals("UNKNOWN")) {
            enhanced += "-" + eventType;
        }
        if (!businessId.isEmpty()) {
            enhanced += "-" + businessId;
        }
        
        log.debug("增强Webhook TraceId: {} -> {}", originalTraceId, enhanced);
        return enhanced;
    }
    
    /**
     * 验证TraceId格式是否有效
     */
    public boolean isValidTraceId(String traceId) {
        if (traceId == null || traceId.trim().isEmpty()) {
            return false;
        }
        
        // 标准格式：yyyyMMddHHmmss-nnn-nnnnnn-xxxxxx
        // Webhook格式：WH-yyyyMMddHHmmss-SOURCE-EVENT-xxxxxx
        String[] parts = traceId.split("-");
        
        if (parts.length < 4) {
            return false;
        }
        
        // 检查时间戳部分
        String timestampPart = parts[0].equals("WH") ? parts[1] : parts[0];
        if (timestampPart.length() != 14) {
            return false;
        }
        
        try {
            TIMESTAMP_FORMAT.parse(timestampPart);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 从TraceId中提取时间戳
     */
    public Date extractTimestamp(String traceId) {
        if (!isValidTraceId(traceId)) {
            return null;
        }
        
        try {
            String[] parts = traceId.split("-");
            String timestampPart = parts[0].equals("WH") ? parts[1] : parts[0];
            return TIMESTAMP_FORMAT.parse(timestampPart);
        } catch (Exception e) {
            log.warn("无法从TraceId中提取时间戳: {}", traceId, e);
            return null;
        }
    }
    
    /**
     * 判断是否为Webhook TraceId
     */
    public boolean isWebhookTraceId(String traceId) {
        return traceId != null && traceId.startsWith("WH-");
    }
    
    /**
     * 生成随机字符串
     */
    private String generateRandomString() {
        return String.format("%06d", RANDOM.nextInt(1000000));
    }
    
    /**
     * 清理组件字符串，确保安全和长度合适
     */
    private String sanitizeComponent(String component, String defaultValue) {
        if (component == null || component.trim().isEmpty()) {
            return defaultValue;
        }
        
        // 移除特殊字符，只保留字母数字和下划线
        String sanitized = component.replaceAll("[^a-zA-Z0-9_]", "").toUpperCase();
        
        // 限制长度
        if (sanitized.length() > 10) {
            sanitized = sanitized.substring(0, 10);
        }
        
        return sanitized.isEmpty() ? defaultValue : sanitized;
    }
}
