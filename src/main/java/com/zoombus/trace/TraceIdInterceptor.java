package com.zoombus.trace;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.stream.Collectors;

/**
 * TraceId HTTP请求拦截器
 * 负责在请求开始时生成或提取TraceId，在请求结束时清理上下文
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TraceIdInterceptor implements HandlerInterceptor {
    
    private final TraceIdGenerator traceIdGenerator;
    
    @Value("${app.trace.header-name:X-Trace-Id}")
    private String traceIdHeader;
    
    private static final String TRACE_ID_PARAM = "traceId";
    
    @Override
    public boolean preHandle(@NonNull HttpServletRequest request,
                           @NonNull HttpServletResponse response,
                           @NonNull Object handler) {
        
        String traceId = extractTraceId(request);
        if (traceId == null) {
            traceId = traceIdGenerator.generateTraceId();
        }
        
        // 设置到ThreadLocal
        TraceContext.setTraceId(traceId);
        
        // 设置基本的请求信息
        TraceContext.setRequestUri(request.getRequestURI());
        TraceContext.setMethod(request.getMethod());
        
        // 添加到响应头
        response.setHeader(traceIdHeader, traceId);
        
        // 记录请求开始
        log.info("请求开始: {} {}", request.getMethod(), request.getRequestURI());
        
        return true;
    }
    
    @Override
    public void afterCompletion(@NonNull HttpServletRequest request,
                              @NonNull HttpServletResponse response,
                              @NonNull Object handler, @Nullable Exception ex) {
        
        // 记录请求完成
        if (ex != null) {
            log.error("请求异常完成: {} {}, status={}", 
                    request.getMethod(), request.getRequestURI(), response.getStatus(), ex);
        } else {
            log.info("请求正常完成: {} {}, status={}", 
                    request.getMethod(), request.getRequestURI(), response.getStatus());
        }
        
        // 清理上下文
        TraceContext.clear();
    }
    
    /**
     * 从请求中提取TraceId
     */
    private String extractTraceId(HttpServletRequest request) {
        // 1. 从请求头获取
        String traceId = request.getHeader(traceIdHeader);
        if (traceId != null && !traceId.trim().isEmpty()) {
            log.debug("从请求头获取TraceId: {}", traceId);
            return traceId;
        }
        
        // 2. 从URL参数获取
        traceId = request.getParameter(TRACE_ID_PARAM);
        if (traceId != null && !traceId.trim().isEmpty()) {
            log.debug("从URL参数获取TraceId: {}", traceId);
            return traceId;
        }
        
        // 3. 特殊处理：Webhook请求
        if (isWebhookRequest(request)) {
            traceId = generateWebhookTraceId(request);
            log.debug("为Webhook生成TraceId: {}", traceId);
            return traceId;
        }
        
        return null;
    }
    
    /**
     * 判断是否为Webhook请求
     */
    private boolean isWebhookRequest(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return uri.contains("/webhook") || uri.contains("/callback");
    }
    
    /**
     * 为Webhook生成特殊格式的TraceId
     */
    private String generateWebhookTraceId(HttpServletRequest request) {
        String source = extractWebhookSource(request);
        String eventId = extractWebhookEventId(request);
        
        String traceId = traceIdGenerator.generateWebhookTraceId(source, eventId);
        
        // 设置Webhook相关上下文信息
        TraceContext.setWebhookInfo(source, "UNKNOWN", eventId);
        
        // 记录Webhook接收日志
        logWebhookReceived(request, traceId, source);
        
        return traceId;
    }
    
    /**
     * 从请求中识别Webhook来源
     */
    private String extractWebhookSource(HttpServletRequest request) {
        // 从User-Agent或特定头部识别来源
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null) {
            String lowerUserAgent = userAgent.toLowerCase();
            if (lowerUserAgent.contains("zoom")) {
                return "ZOOM";
            } else if (lowerUserAgent.contains("teams")) {
                return "TEAMS";
            } else if (lowerUserAgent.contains("slack")) {
                return "SLACK";
            }
        }
        
        // 从URI路径识别
        String uri = request.getRequestURI();
        if (uri.contains("/zoom")) {
            return "ZOOM";
        } else if (uri.contains("/teams")) {
            return "TEAMS";
        } else if (uri.contains("/slack")) {
            return "SLACK";
        }
        
        return "UNKNOWN";
    }
    
    /**
     * 从请求中提取事件ID
     */
    private String extractWebhookEventId(HttpServletRequest request) {
        // 尝试从请求头获取事件ID
        String eventId = request.getHeader("X-Event-Id");
        if (eventId != null && !eventId.trim().isEmpty()) {
            return eventId.length() > 8 ? eventId.substring(0, 8) : eventId;
        }
        
        // 尝试从其他常见的事件ID头部获取
        eventId = request.getHeader("X-Request-Id");
        if (eventId != null && !eventId.trim().isEmpty()) {
            return eventId.length() > 8 ? eventId.substring(0, 8) : eventId;
        }
        
        // 如果都没有，使用默认值
        return "EVENT";
    }
    
    /**
     * 记录Webhook接收日志
     */
    private void logWebhookReceived(HttpServletRequest request, String traceId, String source) {
        try {
            String contentType = request.getContentType();
            int contentLength = request.getContentLength();
            String userAgent = request.getHeader("User-Agent");
            
            log.info("Webhook接收: source={}, contentType={}, contentLength={}, userAgent={}", 
                    source, contentType, contentLength, userAgent);
            
            // 如果是POST请求且有请求体，记录请求体的前100个字符
            if ("POST".equalsIgnoreCase(request.getMethod()) && contentLength > 0) {
                try {
                    String body = getRequestBody(request);
                    if (body != null && !body.isEmpty()) {
                        String bodyPreview = body.length() > 100 ? body.substring(0, 100) + "..." : body;
                        log.debug("Webhook请求体预览: {}", bodyPreview);
                    }
                } catch (Exception e) {
                    log.warn("读取Webhook请求体失败", e);
                }
            }
            
        } catch (Exception e) {
            log.error("记录Webhook日志失败", e);
        }
    }
    
    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            BufferedReader reader = request.getReader();
            return reader.lines().collect(Collectors.joining("\n"));
        } catch (IOException e) {
            log.warn("读取请求体失败", e);
            return null;
        }
    }
}
