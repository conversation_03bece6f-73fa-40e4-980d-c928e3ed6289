package com.zoombus.trace;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskDecorator;

/**
 * TraceId任务装饰器
 * 用于在异步任务中传递TraceId上下文
 */
@Slf4j
public class TraceIdTaskDecorator implements TaskDecorator {
    
    @Override
    public Runnable decorate(Runnable runnable) {
        // 在主线程中创建上下文快照
        TraceContext.TraceSnapshot snapshot = TraceContext.createSnapshot();
        
        return () -> {
            try {
                // 在异步线程中恢复上下文
                TraceContext.restoreFromSnapshot(snapshot);
                
                if (snapshot.getTraceId() != null) {
                    log.debug("异步任务开始，TraceId: {}", snapshot.getTraceId());
                }
                
                // 执行原始任务
                runnable.run();
                
                if (snapshot.getTraceId() != null) {
                    log.debug("异步任务完成，TraceId: {}", snapshot.getTraceId());
                }
                
            } catch (Exception e) {
                if (snapshot.getTraceId() != null) {
                    log.error("异步任务执行失败，TraceId: {}", snapshot.getTraceId(), e);
                } else {
                    log.error("异步任务执行失败", e);
                }
                throw e;
            } finally {
                // 清理异步线程的上下文
                TraceContext.clear();
            }
        };
    }
}
