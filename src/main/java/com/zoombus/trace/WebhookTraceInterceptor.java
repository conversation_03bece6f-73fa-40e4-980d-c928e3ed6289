package com.zoombus.trace;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * Webhook专用TraceId拦截器
 * 专门处理外部系统的Webhook调用，生成专用的TraceId格式
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WebhookTraceInterceptor implements HandlerInterceptor {
    
    private final TraceIdGenerator traceIdGenerator;
    
    @Value("${app.trace.header-name:X-Trace-Id}")
    private String traceIdHeader;
    
    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, 
                           @NonNull HttpServletResponse response, 
                           @NonNull Object handler) {
        
        // 只处理Webhook请求
        if (!isWebhookRequest(request)) {
            return true;
        }
        
        // 生成Webhook专用TraceId
        String webhookTraceId = generateWebhookTraceId(request);
        TraceContext.setTraceId(webhookTraceId);
        
        // 设置基本请求信息
        TraceContext.setRequestUri(request.getRequestURI());
        TraceContext.setMethod(request.getMethod());
        
        // 提取并设置Webhook相关信息
        String source = extractWebhookSource(request);
        String eventType = extractEventType(request);
        String eventId = extractEventId(request);
        
        TraceContext.setWebhookInfo(source, eventType, eventId);
        
        // 记录详细的Webhook接收日志
        logWebhookReceived(request, webhookTraceId, source, eventType, eventId);
        
        // 设置响应头
        response.setHeader(traceIdHeader, webhookTraceId);
        
        return true;
    }
    
    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, 
                              @NonNull HttpServletResponse response, 
                              @NonNull Object handler, @Nullable Exception ex) {
        
        if (isWebhookRequest(request)) {
            String traceId = TraceContext.getTraceId();
            
            // 记录Webhook处理完成
            if (ex != null) {
                log.error("Webhook处理异常完成: {} {}, status={}", 
                        request.getMethod(), request.getRequestURI(), response.getStatus(), ex);
            } else {
                log.info("Webhook处理正常完成: {} {}, status={}", 
                        request.getMethod(), request.getRequestURI(), response.getStatus());
            }
            
            // 清理上下文
            TraceContext.clear();
        }
    }
    
    /**
     * 判断是否为Webhook请求
     */
    private boolean isWebhookRequest(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return uri.contains("/webhook") || uri.contains("/callback") || uri.contains("/api/webhook");
    }
    
    /**
     * 生成Webhook专用TraceId
     */
    private String generateWebhookTraceId(HttpServletRequest request) {
        String source = extractWebhookSource(request);
        String eventId = extractEventId(request);
        
        return traceIdGenerator.generateWebhookTraceId(source, eventId);
    }
    
    /**
     * 从请求中识别Webhook来源
     */
    private String extractWebhookSource(HttpServletRequest request) {
        // 1. 从User-Agent识别
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null) {
            String lowerUserAgent = userAgent.toLowerCase();
            if (lowerUserAgent.contains("zoom")) {
                return "ZOOM";
            } else if (lowerUserAgent.contains("teams")) {
                return "TEAMS";
            } else if (lowerUserAgent.contains("slack")) {
                return "SLACK";
            } else if (lowerUserAgent.contains("microsoft")) {
                return "MICROSOFT";
            }
        }
        
        // 2. 从URI路径识别
        String uri = request.getRequestURI().toLowerCase();
        if (uri.contains("/zoom")) {
            return "ZOOM";
        } else if (uri.contains("/teams")) {
            return "TEAMS";
        } else if (uri.contains("/slack")) {
            return "SLACK";
        } else if (uri.contains("/microsoft")) {
            return "MICROSOFT";
        }
        
        // 3. 从特定头部识别
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null) {
            if (authHeader.toLowerCase().contains("zoom")) {
                return "ZOOM";
            }
        }
        
        return "UNKNOWN";
    }
    
    /**
     * 从请求中提取事件类型
     */
    private String extractEventType(HttpServletRequest request) {
        // 1. 从头部获取
        String eventType = request.getHeader("X-Event-Type");
        if (eventType != null && !eventType.trim().isEmpty()) {
            return eventType.trim().toUpperCase();
        }

        // 2. 从Zoom特定头部获取
        eventType = request.getHeader("X-Zm-Event-Type");
        if (eventType != null && !eventType.trim().isEmpty()) {
            return eventType.trim().toUpperCase();
        }

        // 3. 尝试从请求体中提取（如果是POST请求且已经被包装）
        if ("POST".equalsIgnoreCase(request.getMethod())) {
            try {
                String body = getRequestBodySafely(request);
                if (body != null && body.contains("\"event\"")) {
                    // 简单的JSON解析，提取event字段
                    String[] parts = body.split("\"event\"\\s*:\\s*\"");
                    if (parts.length > 1) {
                        String eventPart = parts[1].split("\"")[0];
                        return eventPart.toUpperCase();
                    }
                }
            } catch (Exception e) {
                log.debug("从请求体提取事件类型失败", e);
            }
        }

        return "UNKNOWN";
    }
    
    /**
     * 从请求中提取事件ID
     */
    private String extractEventId(HttpServletRequest request) {
        // 1. 从头部获取
        String eventId = request.getHeader("X-Event-Id");
        if (eventId != null && !eventId.trim().isEmpty()) {
            return sanitizeEventId(eventId);
        }
        
        // 2. 从请求ID获取
        eventId = request.getHeader("X-Request-Id");
        if (eventId != null && !eventId.trim().isEmpty()) {
            return sanitizeEventId(eventId);
        }
        
        // 3. 从Zoom特定头部获取
        eventId = request.getHeader("X-Zm-Request-Id");
        if (eventId != null && !eventId.trim().isEmpty()) {
            return sanitizeEventId(eventId);
        }
        
        // 4. 生成基于时间的ID
        return "EVT" + System.currentTimeMillis() % 1000000;
    }
    
    /**
     * 清理事件ID，确保长度合适
     */
    private String sanitizeEventId(String eventId) {
        if (eventId == null) {
            return "EVENT";
        }
        
        // 移除特殊字符，只保留字母数字
        String cleaned = eventId.replaceAll("[^a-zA-Z0-9]", "");
        
        // 限制长度
        if (cleaned.length() > 10) {
            cleaned = cleaned.substring(0, 10);
        }
        
        return cleaned.isEmpty() ? "EVENT" : cleaned.toUpperCase();
    }
    
    /**
     * 记录详细的Webhook接收日志
     */
    private void logWebhookReceived(HttpServletRequest request, String traceId, 
                                  String source, String eventType, String eventId) {
        try {
            // 基本信息
            String method = request.getMethod();
            String uri = request.getRequestURI();
            String contentType = request.getContentType();
            int contentLength = request.getContentLength();
            String userAgent = request.getHeader("User-Agent");
            String remoteAddr = getClientIpAddress(request);
            
            log.info("Webhook接收: source={}, eventType={}, eventId={}, method={}, uri={}, " +
                    "contentType={}, contentLength={}, userAgent={}, remoteAddr={}", 
                    source, eventType, eventId, method, uri, contentType, contentLength, userAgent, remoteAddr);
            
            // 记录所有头部信息（调试用）
            if (log.isDebugEnabled()) {
                Map<String, String> headers = new HashMap<>();
                Enumeration<String> headerNames = request.getHeaderNames();
                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    headers.put(headerName, request.getHeader(headerName));
                }
                log.debug("Webhook请求头: {}", headers);
            }
            
            // 记录请求体预览（如果是POST请求）
            if ("POST".equalsIgnoreCase(method) && contentLength > 0) {
                try {
                    String body = getRequestBodySafely(request);
                    if (body != null && !body.isEmpty()) {
                        String bodyPreview = body.length() > 200 ? body.substring(0, 200) + "..." : body;
                        log.info("Webhook请求体预览: {}", bodyPreview);

                        // 完整请求体记录到DEBUG级别
                        log.debug("Webhook完整请求体: {}", body);
                    }
                } catch (Exception e) {
                    log.debug("读取Webhook请求体失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("记录Webhook接收日志失败", e);
        }
    }
    
    /**
     * 安全地获取请求体内容
     * 只有在请求已经被ContentCachingRequestWrapper包装时才读取
     */
    private String getRequestBodySafely(HttpServletRequest request) {
        try {
            if (request instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
                byte[] content = wrapper.getContentAsByteArray();
                if (content.length > 0) {
                    return new String(content, StandardCharsets.UTF_8);
                }
            }

            // 如果不是包装类，不尝试读取请求体，避免消耗InputStream
            log.debug("请求未被ContentCachingRequestWrapper包装，跳过请求体读取");
            return null;

        } catch (Exception e) {
            log.debug("读取请求体失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
