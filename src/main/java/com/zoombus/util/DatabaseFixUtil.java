package com.zoombus.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * 数据库修复工具
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DatabaseFixUtil {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 检查并修复PMI相关表结构
     */
    @PostConstruct
    public void checkAndFixPmiTables() {
        try {
            log.info("开始检查PMI相关表结构...");
            
            // 检查t_pmi_schedule_windows表是否存在
            if (!tableExists("t_pmi_schedule_windows")) {
                log.warn("表 t_pmi_schedule_windows 不存在，尝试创建...");
                createPmiScheduleWindowsTable();
            } else {
                log.info("表 t_pmi_schedule_windows 存在");
                checkAndFixPmiScheduleWindowsColumns();
            }
            
            // 检查t_pmi_schedule_window_tasks表是否存在
            if (!tableExists("t_pmi_schedule_window_tasks")) {
                log.warn("表 t_pmi_schedule_window_tasks 不存在，尝试创建...");
                createPmiScheduleWindowTasksTable();
            } else {
                log.info("表 t_pmi_schedule_window_tasks 存在");
            }
            
            log.info("PMI相关表结构检查完成");
            
        } catch (Exception e) {
            log.error("检查PMI表结构时发生错误", e);
        }
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables " +
                        "WHERE table_schema = DATABASE() AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查表 {} 是否存在时发生错误", tableName, e);
            return false;
        }
    }

    /**
     * 检查列是否存在
     */
    private boolean columnExists(String tableName, String columnName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.columns " +
                        "WHERE table_schema = DATABASE() AND table_name = ? AND column_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName, columnName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查列 {}.{} 是否存在时发生错误", tableName, columnName, e);
            return false;
        }
    }

    /**
     * 创建PMI窗口表
     */
    private void createPmiScheduleWindowsTable() {
        try {
            String sql = "CREATE TABLE t_pmi_schedule_windows (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY," +
                "schedule_id BIGINT NOT NULL COMMENT '关联的计划ID'," +
                "pmi_record_id BIGINT NOT NULL COMMENT 'PMI记录ID'," +
                "start_date_time DATETIME COMMENT '窗口开始时间（精确到秒）'," +
                "end_date_time DATETIME COMMENT '窗口结束时间（精确到秒）'," +
                "status VARCHAR(50) NOT NULL DEFAULT 'PENDING' COMMENT '状态'," +
                "zoom_user_id BIGINT COMMENT '分配的Zoom用户ID'," +
                "error_message TEXT COMMENT '错误信息'," +
                "actual_start_time DATETIME COMMENT '实际开始时间'," +
                "actual_end_time DATETIME COMMENT '实际结束时间'," +
                "open_task_id BIGINT COMMENT '开启任务ID'," +
                "close_task_id BIGINT COMMENT '关闭任务ID'," +
                "created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                "INDEX idx_schedule_id (schedule_id)," +
                "INDEX idx_pmi_record_id (pmi_record_id)," +
                "INDEX idx_start_date_time (start_date_time)," +
                "INDEX idx_end_date_time (end_date_time)," +
                "INDEX idx_status (status)," +
                "INDEX idx_zoom_user_id (zoom_user_id)," +
                "INDEX idx_created_at (created_at)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PMI窗口表'";
            
            jdbcTemplate.execute(sql);
            log.info("成功创建表 t_pmi_schedule_windows");
            
        } catch (Exception e) {
            log.error("创建表 t_pmi_schedule_windows 失败", e);
        }
    }

    /**
     * 创建PMI窗口任务表
     */
    private void createPmiScheduleWindowTasksTable() {
        try {
            String sql = "CREATE TABLE t_pmi_schedule_window_tasks (" +
                "id BIGINT PRIMARY KEY AUTO_INCREMENT," +
                "pmi_window_id BIGINT NOT NULL COMMENT '关联的PMI窗口ID'," +
                "task_type ENUM('PMI_WINDOW_OPEN', 'PMI_WINDOW_CLOSE') NOT NULL COMMENT '任务类型'," +
                "scheduled_time DATETIME NOT NULL COMMENT '计划执行时间'," +
                "actual_execution_time DATETIME COMMENT '实际执行时间'," +
                "status ENUM('SCHEDULED', 'EXECUTING', 'COMPLETED', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'SCHEDULED' COMMENT '任务状态'," +
                "task_key VARCHAR(255) UNIQUE NOT NULL COMMENT '任务唯一标识'," +
                "retry_count INT DEFAULT 0 COMMENT '重试次数'," +
                "error_message TEXT COMMENT '错误信息'," +
                "created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                "updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
                "INDEX idx_pmi_window_id (pmi_window_id)," +
                "INDEX idx_scheduled_time (scheduled_time)," +
                "INDEX idx_status (status)," +
                "INDEX idx_task_key (task_key)," +
                "INDEX idx_task_type (task_type)," +
                "INDEX idx_created_at (created_at)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PMI窗口定时任务表'";
            
            jdbcTemplate.execute(sql);
            log.info("成功创建表 t_pmi_schedule_window_tasks");
            
        } catch (Exception e) {
            log.error("创建表 t_pmi_schedule_window_tasks 失败", e);
        }
    }

    /**
     * 检查并修复PMI窗口表的列
     */
    private void checkAndFixPmiScheduleWindowsColumns() {
        try {
            // 检查必要的列是否存在
            String[] requiredColumns = {
                "start_date_time", "end_date_time", "open_task_id", "close_task_id"
            };
            
            for (String column : requiredColumns) {
                if (!columnExists("t_pmi_schedule_windows", column)) {
                    addMissingColumn("t_pmi_schedule_windows", column);
                }
            }
            
        } catch (Exception e) {
            log.error("检查PMI窗口表列时发生错误", e);
        }
    }

    /**
     * 添加缺失的列
     */
    private void addMissingColumn(String tableName, String columnName) {
        try {
            String sql = null;

            switch (columnName) {
                case "start_date_time":
                    sql = "ALTER TABLE " + tableName + " ADD COLUMN start_date_time DATETIME COMMENT '窗口开始时间（精确到秒）'";
                    break;
                case "end_date_time":
                    sql = "ALTER TABLE " + tableName + " ADD COLUMN end_date_time DATETIME COMMENT '窗口结束时间（精确到秒）'";
                    break;
                case "open_task_id":
                    sql = "ALTER TABLE " + tableName + " ADD COLUMN open_task_id BIGINT COMMENT '开启任务ID'";
                    break;
                case "close_task_id":
                    sql = "ALTER TABLE " + tableName + " ADD COLUMN close_task_id BIGINT COMMENT '关闭任务ID'";
                    break;
                default:
                    break;
            }

            if (sql != null) {
                jdbcTemplate.execute(sql);
                log.info("成功添加列 {}.{}", tableName, columnName);
            }

        } catch (Exception e) {
            log.error("添加列 {}.{} 失败", tableName, columnName, e);
        }
    }

    /**
     * 获取数据库诊断信息
     */
    public Map<String, Object> getDatabaseDiagnostics() {
        try {
            // 检查表状态
            List<Map<String, Object>> tableStatus = jdbcTemplate.queryForList(
                "SHOW TABLE STATUS LIKE 't_pmi_%'"
            );
            
            // 检查外键约束
            List<Map<String, Object>> foreignKeys = jdbcTemplate.queryForList(
                "SELECT * FROM information_schema.KEY_COLUMN_USAGE " +
                "WHERE TABLE_SCHEMA = DATABASE() AND REFERENCED_TABLE_NAME IS NOT NULL " +
                "AND TABLE_NAME LIKE 't_pmi_%'"
            );
            
            return Map.of(
                "tableStatus", tableStatus,
                "foreignKeys", foreignKeys,
                "timestamp", System.currentTimeMillis()
            );
            
        } catch (Exception e) {
            log.error("获取数据库诊断信息失败", e);
            return Map.of("error", e.getMessage());
        }
    }
}
