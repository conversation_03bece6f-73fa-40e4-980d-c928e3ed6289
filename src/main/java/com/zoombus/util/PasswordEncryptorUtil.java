package com.zoombus.util;

import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

/**
 * 密码加密工具类
 * 用于生成加密后的密码，供配置文件使用
 */
public class PasswordEncryptorUtil {
    
    private static final String DEFAULT_ENCRYPTOR_PASSWORD = "zoombus-secret-key-2024";
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("Usage: java PasswordEncryptorUtil <password-to-encrypt> [encryptor-password]");
            System.out.println("Example: java PasswordEncryptorUtil nvshen2018");
            return;
        }
        
        String passwordToEncrypt = args[0];
        String encryptorPassword = args.length > 1 ? args[1] : DEFAULT_ENCRYPTOR_PASSWORD;
        
        String encryptedPassword = encrypt(passwordToEncrypt, encryptorPassword);
        
        System.out.println("=== Password Encryption Result ===");
        System.out.println("Original password: " + passwordToEncrypt);
        System.out.println("Encryptor password: " + encryptorPassword);
        System.out.println("Encrypted password: " + encryptedPassword);
        System.out.println("Configuration format: ENC(" + encryptedPassword + ")");
        System.out.println("===================================");
    }
    
    public static String encrypt(String plainText, String encryptorPassword) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        
        config.setPassword(encryptorPassword);
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        
        encryptor.setConfig(config);
        return encryptor.encrypt(plainText);
    }
    
    public static String decrypt(String encryptedText, String encryptorPassword) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        
        config.setPassword(encryptorPassword);
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        
        encryptor.setConfig(config);
        return encryptor.decrypt(encryptedText);
    }
}
