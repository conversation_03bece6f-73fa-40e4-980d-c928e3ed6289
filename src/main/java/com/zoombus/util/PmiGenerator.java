package com.zoombus.util;

import java.security.SecureRandom;
import java.util.HashSet;
import java.util.Set;

/**
 * PMI号码生成工具类
 * 生成10位随机数字，首位不能是0和1
 */
public class PmiGenerator {

    private static final SecureRandom RANDOM = new SecureRandom();

    // 可用的数字
    private static final char[] AVAILABLE_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};

    // 首位可用的数字（不包含0、1）
    private static final char[] FIRST_DIGIT_OPTIONS = {'2', '3', '4', '5', '6', '7', '8', '9'};
    
    /**
     * 生成一个PMI号码
     * @return 10位PMI号码字符串
     */
    public static String generatePmi() {
        StringBuilder pmi = new StringBuilder(10);
        
        // 第一位：从2,3,4,5,6,7,8,9中选择
        pmi.append(FIRST_DIGIT_OPTIONS[RANDOM.nextInt(FIRST_DIGIT_OPTIONS.length)]);

        // 后9位：从0,1,2,3,4,5,6,7,8,9中选择
        for (int i = 1; i < 10; i++) {
            pmi.append(AVAILABLE_DIGITS[RANDOM.nextInt(AVAILABLE_DIGITS.length)]);
        }
        
        return pmi.toString();
    }
    
    /**
     * 生成多个不重复的PMI号码
     * @param count 需要生成的数量
     * @return PMI号码集合
     */
    public static Set<String> generateMultiplePmi(int count) {
        Set<String> pmiSet = new HashSet<>();
        
        while (pmiSet.size() < count) {
            pmiSet.add(generatePmi());
        }
        
        return pmiSet;
    }
    
    /**
     * 验证PMI号码格式是否正确
     * @param pmi PMI号码
     * @return 是否有效
     */
    public static boolean isValidPmi(String pmi) {
        if (pmi == null || pmi.length() != 10) {
            return false;
        }
        
        // 检查是否全为数字
        if (!pmi.matches("\\d{10}")) {
            return false;
        }
        
        // 检查首位不能是0或1
        char firstDigit = pmi.charAt(0);
        if (firstDigit == '0' || firstDigit == '1') {
            return false;
        }

        return true;
    }
    
    /**
     * 生成PMI密码
     * @return 6位随机密码
     */
    public static String generatePmiPassword() {
        StringBuilder password = new StringBuilder(6);
        
        // 生成6位数字密码
        for (int i = 0; i < 6; i++) {
            password.append(RANDOM.nextInt(10));
        }
        
        return password.toString();
    }
    
    /**
     * 生成包含字母和数字的复杂密码
     * @param length 密码长度
     * @return 密码字符串
     */
    public static String generateComplexPassword(int length) {
        String chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789";
        StringBuilder password = new StringBuilder(length);
        
        for (int i = 0; i < length; i++) {
            password.append(chars.charAt(RANDOM.nextInt(chars.length())));
        }
        
        return password.toString();
    }
}
