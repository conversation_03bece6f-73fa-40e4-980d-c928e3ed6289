package com.zoombus.util;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;

/**
 * 密码加密工具 - 使用 Jasypt 兼容的算法
 */
public class SimplePasswordEncryptor {

    private static final String SECRET_KEY = "zoombus-secret-key-2024";
    
    public static void main(String[] args) {
        String originalPassword = "nvshen2018";
        
        try {
            String encryptedPassword = encrypt(originalPassword);
            System.out.println("=== 密码加密结果 ===");
            System.out.println("原始密码: " + originalPassword);
            System.out.println("加密后密码: " + encryptedPassword);
            System.out.println("配置格式: ENC(" + encryptedPassword + ")");
            System.out.println("==================");
            
            // 验证解密
            String decryptedPassword = decrypt(encryptedPassword);
            System.out.println("解密验证: " + decryptedPassword);
            System.out.println("加密解密是否成功: " + originalPassword.equals(decryptedPassword));
            
        } catch (Exception e) {
            System.err.println("加密失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static String encrypt(String plainText) throws Exception {
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setPassword(SECRET_KEY);
        encryptor.setAlgorithm("PBEWithMD5AndDES");
        return encryptor.encrypt(plainText);
    }

    public static String decrypt(String encryptedText) throws Exception {
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setPassword(SECRET_KEY);
        encryptor.setAlgorithm("PBEWithMD5AndDES");
        return encryptor.decrypt(encryptedText);
    }
}
