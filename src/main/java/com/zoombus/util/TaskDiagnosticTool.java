package com.zoombus.util;

import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.entity.TaskExecutionRecord;
import com.zoombus.repository.PmiScheduleWindowRepository;
import com.zoombus.repository.PmiScheduleWindowTaskRepository;
import com.zoombus.repository.TaskExecutionRecordRepository;
import com.zoombus.service.DynamicTaskManager;
import com.zoombus.service.PmiTaskManagementService;
import com.zoombus.service.TaskExecutionTracker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 任务诊断工具
 * 用于诊断生产环境中任务执行问题
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TaskDiagnosticTool {
    
    private final PmiScheduleWindowTaskRepository taskRepository;
    private final PmiScheduleWindowRepository windowRepository;
    private final TaskExecutionRecordRepository executionRecordRepository;
    private final DynamicTaskManager dynamicTaskManager;
    private final PmiTaskManagementService taskManagementService;
    private final TaskExecutionTracker executionTracker;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 诊断指定任务ID的问题
     */
    public Map<String, Object> diagnoseTask(Long taskId) {
        log.info("开始诊断任务: taskId={}", taskId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", taskId);
        result.put("diagnosisTime", LocalDateTime.now().format(FORMATTER));
        
        try {
            // 1. 获取任务基本信息
            Optional<PmiScheduleWindowTask> taskOpt = taskRepository.findById(taskId);
            if (!taskOpt.isPresent()) {
                result.put("error", "任务不存在: " + taskId);
                return result;
            }
            
            PmiScheduleWindowTask task = taskOpt.get();
            result.put("taskInfo", buildTaskInfo(task));
            
            // 2. 检查任务状态
            result.put("statusCheck", checkTaskStatus(task));
            
            // 3. 检查调度时间
            result.put("scheduleCheck", checkScheduleTime(task));
            
            // 4. 检查PMI窗口状态
            result.put("windowCheck", checkWindowStatus(task));
            
            // 5. 检查执行记录
            result.put("executionRecords", checkExecutionRecords(task));
            
            // 6. 检查Redis状态
            result.put("redisCheck", checkRedisStatus(task));
            
            // 7. 检查系统状态
            result.put("systemCheck", checkSystemStatus());
            
            // 8. 生成诊断建议
            result.put("recommendations", generateRecommendations(task, result));
            
            log.info("任务诊断完成: taskId={}", taskId);
            
        } catch (Exception e) {
            log.error("诊断任务失败: taskId={}", taskId, e);
            result.put("error", "诊断失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 构建任务基本信息
     */
    private Map<String, Object> buildTaskInfo(PmiScheduleWindowTask task) {
        Map<String, Object> info = new HashMap<>();
        info.put("id", task.getId());
        info.put("pmiWindowId", task.getPmiWindowId());
        info.put("taskType", task.getTaskType());
        info.put("status", task.getStatus());
        info.put("scheduledTime", task.getScheduledTime().format(FORMATTER));
        info.put("actualExecutionTime", task.getActualExecutionTime() != null ? 
                task.getActualExecutionTime().format(FORMATTER) : null);
        info.put("taskKey", task.getTaskKey());
        info.put("retryCount", task.getRetryCount());
        info.put("errorMessage", task.getErrorMessage());
        info.put("createdAt", task.getCreatedAt().format(FORMATTER));
        info.put("updatedAt", task.getUpdatedAt().format(FORMATTER));
        return info;
    }
    
    /**
     * 检查任务状态
     */
    private Map<String, Object> checkTaskStatus(PmiScheduleWindowTask task) {
        Map<String, Object> check = new HashMap<>();
        check.put("currentStatus", task.getStatus());
        check.put("canRetry", task.canRetry());
        
        // 检查状态是否合理
        LocalDateTime now = LocalDateTime.now();
        if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.SCHEDULED) {
            if (task.getScheduledTime().isBefore(now)) {
                check.put("issue", "任务已过期但仍为SCHEDULED状态");
                check.put("severity", "HIGH");
            } else {
                check.put("status", "正常，等待执行");
                check.put("severity", "LOW");
            }
        } else if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.EXECUTING) {
            if (task.getActualExecutionTime() != null && 
                task.getActualExecutionTime().isBefore(now.minusMinutes(30))) {
                check.put("issue", "任务执行时间过长，可能卡死");
                check.put("severity", "HIGH");
            }
        } else if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.FAILED) {
            check.put("issue", "任务执行失败");
            check.put("severity", "MEDIUM");
            check.put("errorMessage", task.getErrorMessage());
        }
        
        return check;
    }
    
    /**
     * 检查调度时间
     */
    private Map<String, Object> checkScheduleTime(PmiScheduleWindowTask task) {
        Map<String, Object> check = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime scheduledTime = task.getScheduledTime();
        
        check.put("scheduledTime", scheduledTime.format(FORMATTER));
        check.put("currentTime", now.format(FORMATTER));
        
        long minutesOverdue = java.time.Duration.between(scheduledTime, now).toMinutes();
        check.put("minutesOverdue", minutesOverdue);
        
        if (minutesOverdue > 0) {
            if (minutesOverdue > 60) {
                check.put("issue", "任务严重过期，超过1小时");
                check.put("severity", "HIGH");
            } else if (minutesOverdue > 10) {
                check.put("issue", "任务过期，超过10分钟");
                check.put("severity", "MEDIUM");
            } else {
                check.put("status", "轻微过期，在正常范围内");
                check.put("severity", "LOW");
            }
        } else {
            check.put("status", "任务尚未到执行时间");
            check.put("severity", "LOW");
        }
        
        return check;
    }
    
    /**
     * 检查PMI窗口状态
     */
    private Map<String, Object> checkWindowStatus(PmiScheduleWindowTask task) {
        Map<String, Object> check = new HashMap<>();
        
        try {
            Optional<PmiScheduleWindow> windowOpt = windowRepository.findById(task.getPmiWindowId());
            if (!windowOpt.isPresent()) {
                check.put("issue", "关联的PMI窗口不存在");
                check.put("severity", "HIGH");
                return check;
            }
            
            PmiScheduleWindow window = windowOpt.get();
            check.put("windowId", window.getId());
            check.put("windowStatus", window.getStatus());
            check.put("startDateTime", window.getStartDateTime().format(FORMATTER));
            check.put("endDateTime", window.getEndDateTime().format(FORMATTER));
            
            // 检查窗口状态是否合理
            LocalDateTime now = LocalDateTime.now();
            if (task.getTaskType() == PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN) {
                if (window.getEndDateTime().isBefore(now)) {
                    check.put("issue", "开启任务的窗口已过期");
                    check.put("severity", "HIGH");
                }
            }
            
        } catch (Exception e) {
            check.put("error", "检查窗口状态失败: " + e.getMessage());
            check.put("severity", "HIGH");
        }
        
        return check;
    }
    
    /**
     * 检查执行记录
     */
    private Map<String, Object> checkExecutionRecords(PmiScheduleWindowTask task) {
        Map<String, Object> check = new HashMap<>();
        
        try {
            // 查找相关的执行记录
            String taskName = "task_" + task.getId();
            List<TaskExecutionRecord> records = executionRecordRepository
                    .findByTaskNameAndStatus(taskName, TaskExecutionRecord.ExecutionStatus.RUNNING);
            
            check.put("runningRecords", records.size());
            
            if (!records.isEmpty()) {
                check.put("issue", "发现运行中的执行记录，可能存在重复执行");
                check.put("severity", "MEDIUM");
            }
            
            // 查找最近的执行记录
            Optional<TaskExecutionRecord> latestRecord = executionRecordRepository
                    .findFirstByTaskNameOrderByExecutionTimeDesc(taskName);
            
            if (latestRecord.isPresent()) {
                TaskExecutionRecord record = latestRecord.get();
                check.put("lastExecution", record.getExecutionTime().format(FORMATTER));
                check.put("lastStatus", record.getStatus());
                check.put("lastError", record.getErrorMessage());
            }
            
        } catch (Exception e) {
            check.put("error", "检查执行记录失败: " + e.getMessage());
        }
        
        return check;
    }
    
    /**
     * 检查Redis状态
     */
    private Map<String, Object> checkRedisStatus(PmiScheduleWindowTask task) {
        Map<String, Object> check = new HashMap<>();
        
        try {
            // 检查任务是否在Redis中被标记为运行中
            String taskName = "task_" + task.getId();
            boolean isRunning = executionTracker.isTaskRunning(taskName);
            check.put("isRunningInRedis", isRunning);
            
            if (isRunning && task.getStatus() != PmiScheduleWindowTask.TaskStatus.EXECUTING) {
                check.put("issue", "Redis中标记为运行中，但数据库状态不一致");
                check.put("severity", "MEDIUM");
            }
            
            // 检查分布式锁
            String lockKey = "task:" + task.getId();
            Boolean hasLock = redisTemplate.hasKey(lockKey);
            check.put("hasDistributedLock", hasLock);
            
            if (hasLock) {
                check.put("issue", "任务被分布式锁锁定，可能影响执行");
                check.put("severity", "MEDIUM");
            }
            
        } catch (Exception e) {
            check.put("error", "检查Redis状态失败: " + e.getMessage());
            check.put("severity", "HIGH");
        }
        
        return check;
    }
    
    /**
     * 检查系统状态
     */
    private Map<String, Object> checkSystemStatus() {
        Map<String, Object> check = new HashMap<>();
        
        try {
            // 检查调度器状态
            var schedulerStatus = dynamicTaskManager.getSchedulerStatus();
            check.put("schedulerStatus", schedulerStatus);
            
            // 检查运行中的任务数量
            List<PmiScheduleWindowTask> runningTasks = taskRepository
                    .findByStatus(PmiScheduleWindowTask.TaskStatus.EXECUTING);
            check.put("totalRunningTasks", runningTasks.size());
            
            // 检查待执行的任务数量
            List<PmiScheduleWindowTask> scheduledTasks = taskRepository
                    .findByStatus(PmiScheduleWindowTask.TaskStatus.SCHEDULED);
            check.put("totalScheduledTasks", scheduledTasks.size());
            
        } catch (Exception e) {
            check.put("error", "检查系统状态失败: " + e.getMessage());
        }
        
        return check;
    }
    
    /**
     * 生成诊断建议
     */
    private List<String> generateRecommendations(PmiScheduleWindowTask task, Map<String, Object> diagnosisResult) {
        List<String> recommendations = new java.util.ArrayList<>();
        
        // 基于诊断结果生成建议
        if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.SCHEDULED) {
            LocalDateTime now = LocalDateTime.now();
            long minutesOverdue = java.time.Duration.between(task.getScheduledTime(), now).toMinutes();
            
            if (minutesOverdue > 60) {
                recommendations.add("任务严重过期，建议立即手动执行或重新调度");
                recommendations.add("检查调度器是否正常运行");
                recommendations.add("检查系统资源是否充足");
            } else if (minutesOverdue > 10) {
                recommendations.add("任务轻微过期，建议检查调度器状态");
            }
        }
        
        if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.FAILED) {
            recommendations.add("任务执行失败，建议查看错误信息并修复问题");
            if (task.canRetry()) {
                recommendations.add("任务可以重试，建议手动触发重试");
            }
        }
        
        if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.EXECUTING) {
            if (task.getActualExecutionTime() != null) {
                long runningMinutes = java.time.Duration.between(task.getActualExecutionTime(), LocalDateTime.now()).toMinutes();
                if (runningMinutes > 30) {
                    recommendations.add("任务执行时间过长，建议检查是否卡死");
                    recommendations.add("考虑重启任务或清理分布式锁");
                }
            }
        }
        
        // 添加通用建议
        recommendations.add("检查应用日志获取更多详细信息");
        recommendations.add("确认数据库和Redis连接正常");
        recommendations.add("检查系统资源使用情况");
        
        return recommendations;
    }
    
    /**
     * 尝试修复任务
     */
    public Map<String, Object> attemptTaskRepair(Long taskId) {
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", taskId);
        result.put("repairTime", LocalDateTime.now().format(FORMATTER));
        
        try {
            Optional<PmiScheduleWindowTask> taskOpt = taskRepository.findById(taskId);
            if (!taskOpt.isPresent()) {
                result.put("error", "任务不存在: " + taskId);
                return result;
            }
            
            PmiScheduleWindowTask task = taskOpt.get();
            List<String> actions = new java.util.ArrayList<>();
            
            // 清理Redis状态
            String taskName = "task_" + task.getId();
            String lockKey = "task:" + task.getId();
            
            if (redisTemplate.hasKey(lockKey)) {
                redisTemplate.delete(lockKey);
                actions.add("清理分布式锁: " + lockKey);
            }
            
            executionTracker.markTaskAsCompleted(taskName, task.getId());
            actions.add("清理Redis运行状态");
            
            // 如果任务过期且状态为SCHEDULED，尝试立即执行
            if (task.getStatus() == PmiScheduleWindowTask.TaskStatus.SCHEDULED) {
                LocalDateTime now = LocalDateTime.now();
                if (task.getScheduledTime().isBefore(now)) {
                    try {
                        taskManagementService.executePmiTask(taskId);
                        actions.add("手动执行过期任务");
                    } catch (Exception e) {
                        actions.add("手动执行失败: " + e.getMessage());
                    }
                }
            }
            
            result.put("actions", actions);
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("修复任务失败: taskId={}", taskId, e);
            result.put("error", "修复失败: " + e.getMessage());
            result.put("success", false);
        }
        
        return result;
    }
}
