# 开发环境配置
spring:
  # DevTools配置
  devtools:
    restart:
      enabled: true
      # 排除不需要重启的目录
      exclude: static/**,public/**,templates/**,META-INF/maven/**,META-INF/resources/**
      # 监控额外的目录
      additional-paths: src/main/java
      # 重启触发文件
      trigger-file: .reloadtrigger
    livereload:
      enabled: true
      port: 35729
    remote:
      secret: zoombus-dev-secret

  # 数据库配置
  datasource:
    url: **************************************************************************************************************************************************
    username: root
    password: nvshen2018
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true

  # Redis配置
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# 日志配置
logging:
  level:
    com.zoombus: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,beans,configprops,mappings
  endpoint:
    health:
      show-details: always

# 应用配置
server:
  port: 8080
  servlet:
    context-path: /

# 开发环境特定配置
zoombus:
  dev:
    auto-reload: true
    debug-mode: true
    hot-swap: true
