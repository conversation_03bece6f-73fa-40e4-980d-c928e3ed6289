# WebSocket配置
websocket:
  enabled: true

# PMI任务调度配置
pmi:
  task:
    scheduling:
      # 是否启用精准定时任务机制（启用精准调度）
      enable-precise-scheduling: true

      # 是否启用任务监控
      enable-task-monitoring: true
      
      # 任务清理配置
      cleanup:
        enabled: true
        retention-days: 30
        cron-expression: "0 0 2 * * ?"  # 每天凌晨2点执行
      
      # 任务重试配置
      retry:
        max-retry-count: 3
        retry-interval-minutes: 5
        exponential-backoff: true
      
      # 性能配置
      performance:
        batch-size: 100
        thread-pool-size: 10
        task-timeout-minutes: 30
        enable-task-cache: true
        cache-expiration-minutes: 10

      # 立即执行配置
      immediate-execution:
        enabled: true                    # 是否启用立即执行机制
        tolerance-minutes: 2             # 时间容差（分钟）
        max-delay-minutes: 60           # 最大延迟执行时间（分钟）
        enable-past-execution: true      # 是否允许执行过往时间的任务

        # 线程池配置
        thread-pool:
          core-size: 5
          max-size: 10
          queue-capacity: 100
          thread-name-prefix: "immediate-task-"
          keep-alive-seconds: 60
          allow-core-thread-time-out: false

        # 监控配置
        monitoring:
          enabled: true
          metrics-interval-seconds: 60
          record-execution-details: true

          # 告警配置
          alert:
            enabled: true
            failure-rate-threshold: 5.0      # 失败率阈值（百分比）
            expired-task-threshold: 10       # 过期任务数量阈值（每小时）
            avg-execution-time-threshold: 5  # 平均执行时间阈值（秒）
            queue-full-threshold: 80.0       # 队列满阈值（百分比）

# 日志配置
logging:
  level:
    com.zoombus.service.DynamicTaskManager: INFO
    com.zoombus.service.PmiWindowTaskExecutor: INFO
    com.zoombus.service.PmiWindowTaskSchedulingService: INFO
    com.zoombus.scheduler.PmiTaskCleanupScheduler: INFO
