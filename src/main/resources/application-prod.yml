# 生产环境配置
spring:
  # 数据库配置 - MySQL
  datasource:
    url: ********************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME:zoombus}
    password: ${DB_PASSWORD:password}
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
  
  # H2控制台配置
  h2:
    console:
      enabled: false

# 日志配置
logging:
  level:
    com.zoombus: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
  file:
    name: logs/zoombus.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
