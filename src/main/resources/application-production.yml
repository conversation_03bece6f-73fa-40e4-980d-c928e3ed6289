# 生产环境配置
server:
  port: 8080

spring:
  application:
    name: zoombus

  # 允许循环引用（临时解决方案）
  main:
    allow-circular-references: true
  
  # 数据库配置
  datasource:
    url: ********************************************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: ENC(488oQC1ICM7BEzgVViFIi0nC6bo1B6CG)
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false

  # Redis配置
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  # H2控制台配置（生产环境禁用）
  h2:
    console:
      enabled: false
  
  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss

# Zoom配置
zoom:
  api-base-url: https://api.zoom.us/v2
  account-id: ${ZOOM_ACCOUNT_ID:your_account_id}
  client-id: ${ZOOM_CLIENT_ID:your_client_id}
  client-secret: ${ZOOM_CLIENT_SECRET:your_client_secret}
  webhook-secret-token: ${ZOOM_WEBHOOK_SECRET:your_webhook_secret}
  jwt-api-key: ${ZOOM_JWT_API_KEY:your_jwt_api_key}
  jwt-api-secret: ${ZOOM_JWT_API_SECRET:your_jwt_api_secret}

  # Token刷新配置
  token:
    refresh:
      enabled: true  # 是否启用定时刷新token
      interval: 300000  # 刷新间隔（毫秒），默认5分钟

# 日志配置
logging:
  level:
    com.zoombus: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  file:
    name: logs/zoombus.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# JWT配置
app:
  jwt:
    secret: ${JWT_SECRET:zoombus-secret-key-for-jwt-token-generation-must-be-at-least-256-bits}
    expiration: ${JWT_EXPIRATION:86400000} # 24小时

# CORS配置（生产环境）
cors:
  allowed-origins:
    - https://m.zoombus.com
    - http://m.zoombus.com
    - http://localhost:3000
    - http://127.0.0.1:3000
