server:
  port: 8080

spring:
  application:
    name: zoombus
    version: 1.0.0

  # 允许循环引用（临时解决方案）
  main:
    allow-circular-references: true

  # 配置文件包含
  profiles:
    include:
      - pmi-tasks

  # Flyway配置
  flyway:
    enabled: false
    auto-migrate: false
    baseline-on-migrate: true
    validate-on-migrate: true
    clean-disabled: true
    locations: classpath:db/migration
    table: flyway_schema_history
  
  # 数据库配置
  datasource:
    url: *******************************************************************************************************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: ENC(488oQC1ICM7BEzgVViFIi0nC6bo1B6CG)
    hikari:
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      maximum-pool-size: 10
      minimum-idle: 5
      connection-test-query: SELECT 1
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        jdbc:
          time_zone: Asia/Shanghai

  # Redis配置
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss

# Zoom配置
zoom:
  api-base-url: https://api.zoom.us/v2
  account-id: ${ZOOM_ACCOUNT_ID:your_account_id}
  client-id: ${ZOOM_CLIENT_ID:your_client_id}
  client-secret: ${ZOOM_CLIENT_SECRET:your_client_secret}
  webhook-secret-token: ${ZOOM_WEBHOOK_SECRET:your_webhook_secret}
  jwt-api-key: ${ZOOM_JWT_API_KEY:your_jwt_api_key}
  jwt-api-secret: ${ZOOM_JWT_API_SECRET:your_jwt_api_secret}

  # Token刷新配置
  token:
    refresh:
      enabled: true  # 是否启用定时刷新token
      interval: 300000  # 刷新间隔（毫秒），默认5分钟

# ZoomBus系统配置
zoombus:
  # 网络环境配置
  network:
    auto-detect-enabled: true  # 是否启用自动检测外网IP
    proxy-enabled: true        # 是否启用代理功能
    target-external-ip: "**************"  # 需要使用代理的目标外网IP
    proxy-host: "127.0.0.1"    # 代理服务器地址
    proxy-port: "6690"         # 代理服务器端口

  # 定时任务配置
  scheduled-task:
    # 重试调度器配置
    retry-scheduler:
      enabled: true  # 是否启用重试调度器

    # 任务执行配置
    execution:
      # 默认超时时间（分钟）
      default-timeout-minutes: 30
      # 默认最大重试次数
      default-max-retry-count: 3
      # 默认重试间隔（毫秒）
      default-retry-delay-ms: 60000
      # 是否防止并发执行
      prevent-concurrent-execution: true

    # 任务清理配置
    cleanup:
      # 执行记录保留天数
      record-retention-days: 30
      # 是否启用自动清理
      auto-cleanup-enabled: true

# 日志配置 - 使用logback-spring.xml进行详细配置
# WebSocket日志将单独记录到 logs/zoombus-websocket.log
# 应用日志记录到 logs/zoombus-application.log
# SQL日志记录到 logs/zoombus-sql.log
# 错误日志记录到 logs/zoombus-error.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# JWT配置和TraceId配置
app:
  jwt:
    secret: ${JWT_SECRET:zoombus-secret-key-for-jwt-token-generation-must-be-at-least-256-bits}
    expiration: ${JWT_EXPIRATION:********} # 24小时
  trace:
    enabled: true
    node-id: ${NODE_ID:001}
    header-name: X-Trace-Id
  logs:
    directory: ${LOG_DIR:./logs}
    max-search-days: 30
    retention-days: 90

# WebSocket配置
websocket:
  enabled: true  # WebSocket功能开关，启用以支持实时监控
  monitoring:
    enabled: true  # 监控数据推送开关
    intervals:
      zoom-account: 30000     # Zoom账号状态收集间隔（毫秒）
      meeting: 60000          # 会议状态收集间隔（毫秒）
      pmi: 120000            # PMI状态收集间隔（毫秒）
      system: 15000          # 系统性能收集间隔（毫秒）
      comprehensive: 300000   # 综合数据推送间隔（毫秒）
      alert-check: 60000     # 告警检查间隔（毫秒）

# CORS配置
cors:
  allowed-origins:
    - https://m.zoombus.com
    - https://zoombus.com
    - http://m.zoombus.com
    - http://localhost:3000
    - http://127.0.0.1:3000
    - https://3c61cbcb7ae6.ngrok-free.app
    - https://*.ngrok.io
    - https://*.ngrok-free.app
