-- ZoomBus 基线数据库结构
-- 版本: V1.0
-- 描述: 创建所有基础表结构
-- 日期: 2025-08-04

-- 管理员用户表
CREATE TABLE IF NOT EXISTS admin_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    email VARCHAR(100) COMMENT '邮箱',
    real_name VARCHAR(100) COMMENT '真实姓名',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE, INACTIVE',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';

-- Zoom认证信息表
CREATE TABLE IF NOT EXISTS t_zoom_auth (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    account_name VARCHAR(100) NOT NULL COMMENT '账号名称',
    zoom_account_id VARCHAR(100) NOT NULL UNIQUE COMMENT 'Zoom账号ID',
    client_id VARCHAR(255) NOT NULL COMMENT 'Zoom应用Client ID',
    client_secret VARCHAR(500) NOT NULL COMMENT 'Zoom应用Client Secret（加密）',
    access_token TEXT COMMENT '访问令牌',
    refresh_token TEXT COMMENT '刷新令牌',
    token_type VARCHAR(50) COMMENT '令牌类型',
    scope TEXT COMMENT '授权范围',
    token_expires_at TIMESTAMP NULL COMMENT '令牌过期时间',
    token_issued_at TIMESTAMP NULL COMMENT '令牌签发时间',
    webhook_secret_token VARCHAR(255) COMMENT 'Webhook密钥令牌',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE, INACTIVE, EXPIRED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_account_name (account_name),
    INDEX idx_zoom_account_id (zoom_account_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Zoom认证信息表';

-- Zoom用户表
CREATE TABLE IF NOT EXISTS t_zoom_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_auth_id BIGINT NOT NULL COMMENT 'Zoom认证ID',
    zoom_user_id VARCHAR(100) NOT NULL COMMENT 'Zoom用户ID',
    email VARCHAR(255) NOT NULL COMMENT '邮箱',
    first_name VARCHAR(100) COMMENT '名',
    last_name VARCHAR(100) COMMENT '姓',
    display_name VARCHAR(200) COMMENT '显示名称',
    type INTEGER COMMENT '用户类型',
    role_name VARCHAR(100) COMMENT '角色名称',
    pmi BIGINT COMMENT '个人会议室ID',
    use_pmi BOOLEAN DEFAULT FALSE COMMENT '是否使用PMI',
    timezone VARCHAR(100) COMMENT '时区',
    verified INTEGER COMMENT '验证状态',
    dept VARCHAR(200) COMMENT '部门',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    last_client_version VARCHAR(100) COMMENT '最后客户端版本',
    language VARCHAR(20) COMMENT '语言',
    phone_country VARCHAR(10) COMMENT '电话国家代码',
    phone_number VARCHAR(50) COMMENT '电话号码',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    job_title VARCHAR(200) COMMENT '职位',
    location VARCHAR(200) COMMENT '位置',
    login_types VARCHAR(200) COMMENT '登录类型',
    plan_united_type VARCHAR(50) COMMENT '计划类型',
    user_created_at TIMESTAMP NULL COMMENT 'Zoom用户创建时间',
    FOREIGN KEY (zoom_auth_id) REFERENCES t_zoom_auth(id) ON DELETE CASCADE,
    UNIQUE KEY uk_zoom_user (zoom_auth_id, zoom_user_id),
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_pmi (pmi)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Zoom用户表';

-- PMI记录表
CREATE TABLE IF NOT EXISTS t_pmi_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_user_id BIGINT NOT NULL COMMENT 'Zoom用户ID',
    pmi_number VARCHAR(20) NOT NULL COMMENT 'PMI号码',
    billing_mode VARCHAR(20) NOT NULL DEFAULT 'BY_TIME' COMMENT '计费模式：BY_TIME, BY_COUNT',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE, INACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (zoom_user_id) REFERENCES t_zoom_users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_pmi_number (pmi_number),
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_status (status),
    INDEX idx_billing_mode (billing_mode)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PMI记录表';

-- 会议表
CREATE TABLE IF NOT EXISTS t_meetings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_meeting_id VARCHAR(50) NOT NULL COMMENT 'Zoom会议ID',
    topic VARCHAR(500) NOT NULL COMMENT '会议主题',
    creator_zoom_user_id BIGINT NOT NULL COMMENT '创建者Zoom用户ID',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    duration_minutes INTEGER NOT NULL COMMENT '持续时间（分钟）',
    timezone VARCHAR(100) COMMENT '时区',
    password VARCHAR(50) COMMENT '会议密码',
    agenda TEXT COMMENT '会议议程',
    status VARCHAR(20) DEFAULT 'SCHEDULED' COMMENT '状态：SCHEDULED, STARTED, ENDED, CANCELLED',
    join_url TEXT COMMENT '参会链接',
    start_url TEXT COMMENT '主持人链接',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_recurring BOOLEAN DEFAULT FALSE COMMENT '是否为循环会议',
    recurrence_type VARCHAR(20) COMMENT '循环类型：DAILY, WEEKLY, MONTHLY',
    recurrence_interval INTEGER COMMENT '循环间隔',
    recurrence_end_date DATE COMMENT '循环结束日期',
    occurrence_id VARCHAR(50) COMMENT '会议实例ID',
    parent_meeting_id BIGINT COMMENT '父会议ID（用于循环会议）',
    FOREIGN KEY (creator_zoom_user_id) REFERENCES t_zoom_users(id),
    FOREIGN KEY (parent_meeting_id) REFERENCES t_meetings(id),
    UNIQUE KEY uk_zoom_meeting_id (zoom_meeting_id),
    INDEX idx_creator (creator_zoom_user_id),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status),
    INDEX idx_recurring (is_recurring),
    INDEX idx_parent_meeting (parent_meeting_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议表';

-- Zoom会议记录表
CREATE TABLE IF NOT EXISTS t_zoom_meetings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    pmi_record_id BIGINT NULL COMMENT 'PMI记录ID（PMI会议时有值，非PMI会议时为null）',
    zoom_meeting_uuid VARCHAR(255) NOT NULL UNIQUE COMMENT 'Zoom会议UUID',
    zoom_meeting_id VARCHAR(50) NOT NULL COMMENT 'Zoom会议ID',
    host_id VARCHAR(100) COMMENT '主持人ID',
    topic VARCHAR(500) COMMENT '会议主题',
    status VARCHAR(20) DEFAULT 'WAITING' COMMENT '状态：WAITING, USING, ENDED',
    billing_mode VARCHAR(20) DEFAULT 'BY_TIME' COMMENT '计费模式：BY_TIME, BY_COUNT',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration_minutes INTEGER COMMENT '持续时间（分钟）',
    participant_count INTEGER COMMENT '参与人数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (pmi_record_id) REFERENCES t_pmi_records(id),
    INDEX idx_pmi_record_id (pmi_record_id),
    INDEX idx_zoom_meeting_id (zoom_meeting_id),
    INDEX idx_host_id (host_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_uuid (zoom_meeting_uuid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Zoom会议记录表';

-- PMI计划表
CREATE TABLE IF NOT EXISTS t_pmi_schedules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    pmi_record_id BIGINT NOT NULL COMMENT 'PMI记录ID',
    name VARCHAR(200) NOT NULL COMMENT '计划名称',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    start_time TIME NOT NULL COMMENT '开始时间',
    duration_minutes INTEGER NOT NULL COMMENT '持续时间（分钟）',
    repeat_type VARCHAR(20) NOT NULL COMMENT '重复类型：ONCE, DAILY, WEEKLY, MONTHLY',
    week_days VARCHAR(20) COMMENT '星期几（用于周重复）',
    month_days VARCHAR(100) COMMENT '月份中的日期（用于月重复）',
    is_all_day BOOLEAN DEFAULT FALSE COMMENT '是否全天',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE, INACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (pmi_record_id) REFERENCES t_pmi_records(id) ON DELETE CASCADE,
    INDEX idx_pmi_record_id (pmi_record_id),
    INDEX idx_start_date (start_date),
    INDEX idx_status (status),
    INDEX idx_repeat_type (repeat_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PMI计划表';

-- 计费记录表
CREATE TABLE IF NOT EXISTS t_billing_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_meeting_id BIGINT NOT NULL COMMENT 'Zoom会议记录ID',
    billing_type VARCHAR(20) NOT NULL COMMENT '计费类型：TIME_BASED, COUNT_BASED',
    amount DECIMAL(10,2) NOT NULL COMMENT '金额',
    start_time TIMESTAMP NOT NULL COMMENT '计费开始时间',
    end_time TIMESTAMP COMMENT '计费结束时间',
    duration_minutes INTEGER COMMENT '计费时长（分钟）',
    rate DECIMAL(10,4) COMMENT '费率',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PENDING, CONFIRMED, CANCELLED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (zoom_meeting_id) REFERENCES t_zoom_meetings(id),
    INDEX idx_zoom_meeting_id (zoom_meeting_id),
    INDEX idx_billing_type (billing_type),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计费记录表';

-- Webhook事件表
CREATE TABLE IF NOT EXISTS t_webhook_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    account_name VARCHAR(100) COMMENT '账号名称',
    event_type VARCHAR(100) NOT NULL COMMENT '事件类型',
    event_data JSON NOT NULL COMMENT '事件数据',
    processed BOOLEAN DEFAULT FALSE COMMENT '是否已处理',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    error_message TEXT COMMENT '错误信息',
    retry_count INTEGER DEFAULT 0 COMMENT '重试次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_event_type (event_type),
    INDEX idx_processed (processed),
    INDEX idx_created_at (created_at),
    INDEX idx_account_name (account_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Webhook事件表';

-- 版本历史表（用于版本管理）
CREATE TABLE IF NOT EXISTS t_version_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    application_name VARCHAR(100) NOT NULL COMMENT '应用名称',
    application_version VARCHAR(50) NOT NULL COMMENT '应用版本',
    database_version VARCHAR(50) NOT NULL COMMENT '数据库版本',
    flyway_version VARCHAR(50) COMMENT 'Flyway迁移版本',
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型：APPLICATION_STARTUP, DATABASE_MIGRATION, MANUAL_RECORD',
    event_description TEXT COMMENT '事件描述',
    server_info VARCHAR(200) COMMENT '服务器信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_app_version (application_name, application_version),
    INDEX idx_db_version (database_version),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用和数据库版本历史记录表';

-- 插入默认管理员用户
INSERT IGNORE INTO admin_users (username, password, email, real_name, status)
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxIcmcuY4YK0s.y', '<EMAIL>', '系统管理员', 'ACTIVE');
