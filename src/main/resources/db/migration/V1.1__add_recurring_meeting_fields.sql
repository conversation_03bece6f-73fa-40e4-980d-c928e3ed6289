-- 添加周期性会议相关字段到 t_meetings 表
-- 注意：某些字段可能已在基线脚本中存在，使用条件添加

-- 检查并添加 is_recurring 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 't_meetings'
AND COLUMN_NAME = 'is_recurring';

SET @sql = IF(@col_exists = 0,
    'ALTER TABLE t_meetings ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE COMMENT ''是否为周期性会议''',
    'SELECT ''Column is_recurring already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 recurrence_type 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 't_meetings'
AND COLUMN_NAME = 'recurrence_type';

SET @sql = IF(@col_exists = 0,
    'ALTER TABLE t_meetings ADD COLUMN recurrence_type VARCHAR(20) COMMENT ''重复类型：DAILY, WEEKLY, MONTHLY''',
    'SELECT ''Column recurrence_type already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 repeat_interval 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 't_meetings'
AND COLUMN_NAME = 'repeat_interval';

SET @sql = IF(@col_exists = 0,
    'ALTER TABLE t_meetings ADD COLUMN repeat_interval INT DEFAULT 1 COMMENT ''重复间隔''',
    'SELECT ''Column repeat_interval already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 end_type 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 't_meetings'
AND COLUMN_NAME = 'end_type';

SET @sql = IF(@col_exists = 0,
    'ALTER TABLE t_meetings ADD COLUMN end_type VARCHAR(20) COMMENT ''结束类型：NO_END, BY_DATE, BY_TIMES''',
    'SELECT ''Column end_type already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 end_date_time 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 't_meetings'
AND COLUMN_NAME = 'end_date_time';

SET @sql = IF(@col_exists = 0,
    'ALTER TABLE t_meetings ADD COLUMN end_date_time DATETIME COMMENT ''结束日期时间''',
    'SELECT ''Column end_date_time already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 end_times 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 't_meetings'
AND COLUMN_NAME = 'end_times';

SET @sql = IF(@col_exists = 0,
    'ALTER TABLE t_meetings ADD COLUMN end_times INT COMMENT ''结束次数''',
    'SELECT ''Column end_times already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引以提高查询性能
CREATE INDEX idx_meetings_recurring ON t_meetings(is_recurring);
CREATE INDEX idx_meetings_recurrence_type ON t_meetings(recurrence_type);
CREATE INDEX idx_meetings_end_date ON t_meetings(end_date_time);
