-- 修改t_zoom_meetings表，允许pmi_record_id为null
-- 这是为了支持非PMI会议（如通过会议安排功能或Zoom App创建的会议）
--
-- 背景：meeting.started事件需要为所有类型的会议创建t_zoom_meetings记录
-- PMI会议：pmi_record_id有值，关联具体的PMI记录
-- 非PMI会议：pmi_record_id为null，包括安排会议和Zoom App直接创建的会议

-- 修改字段约束，允许NULL值（字段已经允许NULL，此操作为幂等）
-- ALTER TABLE t_zoom_meetings
-- MODIFY COLUMN pmi_record_id BIGINT NULL COMMENT 'PMI记录ID（PMI会议时有值，非PMI会议时为null）';

-- 索引已在基线中存在，无需重复创建
-- 空操作，确保迁移成功
SELECT 'V1.3 migration completed - pmi_record_id already allows NULL' as status;
