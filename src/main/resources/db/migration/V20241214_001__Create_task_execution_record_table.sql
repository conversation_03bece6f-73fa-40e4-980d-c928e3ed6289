-- 创建任务执行记录表
-- 用于跟踪定时任务的执行状态和历史

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS task_execution_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_type VARCHAR(50) COMMENT '任务类型',
    status VARCHAR(20) NOT NULL COMMENT '执行状态：PENDING, RUNNING, SUCCESS, FAILED, RETRYING, CANCELLED, TIMEOUT',
    execution_time DATETIME NOT NULL COMMENT '执行时间',
    completion_time DATETIME COMMENT '完成时间',
    duration_ms BIGINT COMMENT '执行耗时（毫秒）',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retry_count INT DEFAULT 3 COMMENT '最大重试次数',
    next_retry_time DATETIME COMMENT '下次重试时间',
    error_message TEXT COMMENT '错误信息',
    error_stack TEXT COMMENT '错误堆栈信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务执行记录表';

-- 创建索引
CREATE INDEX idx_task_name_status ON task_execution_record(task_name, status);
CREATE INDEX idx_execution_time ON task_execution_record(execution_time);
CREATE INDEX idx_task_name_execution_time ON task_execution_record(task_name, execution_time);
CREATE INDEX idx_status_next_retry_time ON task_execution_record(status, next_retry_time);
CREATE INDEX idx_created_at ON task_execution_record(created_at);

-- 添加表注释
ALTER TABLE task_execution_record COMMENT = '定时任务执行记录表，用于跟踪任务执行状态、性能监控和错误处理';
