-- 创建会议报告表
CREATE TABLE IF NOT EXISTS t_meeting_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_meeting_id VARCHAR(255) NOT NULL COMMENT '会议ID',
    zoom_meeting_uuid VARCHAR(255) COMMENT '会议UUID',
    topic VARCHAR(500) COMMENT '会议主题',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration_minutes INT COMMENT '持续时间(分钟)',
    total_participants INT COMMENT '总参与人数',
    unique_participants INT COMMENT '唯一参与人数',
    has_video BOOLEAN DEFAULT FALSE COMMENT '是否有视频',
    has_voip BOOLEAN DEFAULT FALSE COMMENT '是否有VoIP',
    has_3rd_party_audio BOOLEAN DEFAULT FALSE COMMENT '是否有第三方音频',
    has_pstn BOOLEAN DEFAULT FALSE COMMENT '是否有PSTN',
    has_recording BOOLEAN DEFAULT FALSE COMMENT '是否有录制',
    has_screen_share BOOLEAN DEFAULT FALSE COMMENT '是否有屏幕共享',
    report_data JSON COMMENT '报告详细数据',
    fetch_status ENUM('PENDING', 'SUCCESS', 'FAILED') DEFAULT 'PENDING' COMMENT '获取状态',
    fetch_retry_count INT DEFAULT 0 COMMENT '重试次数',
    fetch_error_message TEXT COMMENT '获取错误信息',
    last_fetch_attempt DATETIME COMMENT '最后获取尝试时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_meeting_reports_zoom_meeting_id (zoom_meeting_id),
    INDEX idx_meeting_reports_uuid (zoom_meeting_uuid),
    INDEX idx_meeting_reports_start_time (start_time),
    INDEX idx_meeting_reports_fetch_status (fetch_status),
    INDEX idx_meeting_reports_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议报告表';

-- 创建会议报告任务表
CREATE TABLE IF NOT EXISTS t_meeting_report_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_meeting_id VARCHAR(255) NOT NULL COMMENT '会议ID',
    zoom_meeting_uuid VARCHAR(255) COMMENT '会议UUID',
    task_status ENUM('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED') DEFAULT 'PENDING' COMMENT '任务状态',
    priority INT DEFAULT 5 COMMENT '优先级(1-10, 数字越小优先级越高)',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retry_count INT DEFAULT 3 COMMENT '最大重试次数',
    scheduled_time DATETIME COMMENT '计划执行时间',
    started_time DATETIME COMMENT '开始执行时间',
    completed_time DATETIME COMMENT '完成时间',
    error_message TEXT COMMENT '错误信息',
    task_data JSON COMMENT '任务数据',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_meeting_report_tasks_zoom_meeting_id (zoom_meeting_id),
    INDEX idx_meeting_report_tasks_uuid (zoom_meeting_uuid),
    INDEX idx_meeting_report_tasks_status (task_status),
    INDEX idx_meeting_report_tasks_priority (priority),
    INDEX idx_meeting_report_tasks_scheduled_time (scheduled_time),
    INDEX idx_meeting_report_tasks_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议报告任务表';
