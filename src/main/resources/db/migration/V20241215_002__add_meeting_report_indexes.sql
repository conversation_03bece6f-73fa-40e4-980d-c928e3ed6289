-- 会议报告系统性能优化索引

-- 会议报告表索引（跳过已存在的索引）
-- CREATE INDEX idx_meeting_reports_uuid ON t_meeting_reports(zoom_meeting_uuid);  -- 已在实体类中定义
-- CREATE INDEX idx_meeting_reports_meeting_id ON t_meeting_reports(zoom_meeting_id);  -- 已在实体类中定义
-- CREATE INDEX idx_meeting_reports_start_time ON t_meeting_reports(start_time);  -- 已在实体类中定义
-- CREATE INDEX idx_meeting_reports_fetch_status ON t_meeting_reports(fetch_status);  -- 已在实体类中定义
-- CREATE INDEX idx_meeting_reports_created_at ON t_meeting_reports(created_at);  -- 已在实体类中定义

-- 参会者表索引（等待表创建后再添加）
-- 这些索引将在参会者表创建后通过后续迁移添加

-- 任务表索引（跳过已存在的索引）
-- CREATE INDEX idx_meeting_report_tasks_status ON t_meeting_report_tasks(task_status);  -- 已在实体类中定义
-- CREATE INDEX idx_meeting_report_tasks_scheduled_time ON t_meeting_report_tasks(scheduled_time);  -- 已在实体类中定义
-- CREATE INDEX idx_meeting_report_tasks_uuid ON t_meeting_report_tasks(zoom_meeting_uuid);  -- 已在实体类中定义
-- CREATE INDEX idx_meeting_report_tasks_created_at ON t_meeting_report_tasks(created_at);  -- 已在实体类中定义

-- 暂时跳过复合索引，等待所有表创建完成后再添加
-- CREATE INDEX idx_meeting_reports_status_time ON t_meeting_reports(fetch_status, start_time);
-- CREATE INDEX idx_meeting_participants_report_type ON t_meeting_participants(meeting_report_id, user_type);
-- CREATE INDEX idx_meeting_report_tasks_status_scheduled ON t_meeting_report_tasks(task_status, scheduled_time);

-- 占位符，确保迁移文件有效
SELECT 'Meeting report indexes migration completed' as status;
