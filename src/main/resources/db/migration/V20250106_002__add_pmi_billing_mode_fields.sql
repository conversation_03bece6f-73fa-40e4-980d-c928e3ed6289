-- 添加PMI计费模式改进字段
-- 解决原始计费模式丢失和多窗口管理问题

-- 添加原始计费模式字段
ALTER TABLE t_pmi_records
ADD COLUMN original_billing_mode VARCHAR(20) COMMENT '原始计费模式（用于窗口关闭后恢复）';

-- 添加活跃窗口ID列表字段
ALTER TABLE t_pmi_records
ADD COLUMN active_window_ids TEXT COMMENT '活跃窗口ID列表（JSON格式）';

-- 添加索引以提高查询性能
CREATE INDEX idx_pmi_records_billing_mode ON t_pmi_records(billing_mode);
CREATE INDEX idx_pmi_records_original_billing_mode ON t_pmi_records(original_billing_mode);
CREATE INDEX idx_pmi_records_window_expire_time ON t_pmi_records(window_expire_time);

-- 添加注释
ALTER TABLE t_pmi_records MODIFY COLUMN billing_mode VARCHAR(20) NOT NULL DEFAULT 'BY_TIME' 
COMMENT '当前计费模式：LONG=按时段计费，BY_TIME=按时长计费，FREE=免费';

ALTER TABLE t_pmi_records MODIFY COLUMN current_window_id BIGINT 
COMMENT '当前活跃的主窗口ID（LONG模式时使用）';

ALTER TABLE t_pmi_records MODIFY COLUMN window_expire_time DATETIME 
COMMENT '窗口到期时间（所有活跃窗口中最晚的到期时间）';
