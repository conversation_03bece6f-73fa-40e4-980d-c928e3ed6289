-- 创建会议报告相关表
-- 版本: V20250115_001
-- 描述: 创建会议报告主表和参会人员详情表，支持Zoom Reports API数据存储
-- 日期: 2025-01-15

-- 会议报告主表
CREATE TABLE IF NOT EXISTS t_meeting_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_meeting_uuid VARCHAR(255) NOT NULL COMMENT 'Zoom会议UUID，关联t_zoom_meetings表',
    zoom_meeting_id VARCHAR(255) NOT NULL COMMENT 'Zoom会议ID',
    topic VARCHAR(500) COMMENT '会议主题',
    start_time DATETIME COMMENT '会议开始时间',
    end_time DATETIME COMMENT '会议结束时间',
    duration_minutes INT COMMENT '会议总时长（分钟）',
    total_participants INT DEFAULT 0 COMMENT '总参会人次',
    unique_participants INT DEFAULT 0 COMMENT '唯一参会人数',
    has_recording BOOLEAN DEFAULT FALSE COMMENT '是否有录制',
    has_pstn BOOLEAN DEFAULT FALSE COMMENT '是否有电话接入',
    has_voip BOOLEAN DEFAULT FALSE COMMENT '是否有网络语音',
    has_3rd_party_audio BOOLEAN DEFAULT FALSE COMMENT '是否有第三方音频',
    has_video BOOLEAN DEFAULT FALSE COMMENT '是否有视频',
    has_screen_share BOOLEAN DEFAULT FALSE COMMENT '是否有屏幕共享',
    report_data JSON COMMENT '完整的报告原始数据（JSON格式）',
    fetch_status ENUM('PENDING', 'SUCCESS', 'FAILED', 'RETRY') DEFAULT 'PENDING' COMMENT '报告获取状态',
    fetch_error_message TEXT COMMENT '获取失败时的错误信息',
    fetch_retry_count INT DEFAULT 0 COMMENT '重试次数',
    last_fetch_attempt DATETIME COMMENT '最后一次获取尝试时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_zoom_meeting_uuid (zoom_meeting_uuid),
    INDEX idx_zoom_meeting_id (zoom_meeting_id),
    INDEX idx_start_time (start_time),
    INDEX idx_fetch_status (fetch_status),
    INDEX idx_created_at (created_at),
    
    -- 唯一约束：每个会议UUID只能有一个报告
    UNIQUE KEY uk_zoom_meeting_uuid (zoom_meeting_uuid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议报告主表';

-- 参会人员详情表
CREATE TABLE IF NOT EXISTS t_meeting_participants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    meeting_report_id BIGINT NOT NULL COMMENT '会议报告ID',
    participant_uuid VARCHAR(255) COMMENT '参会者UUID（如果有）',
    participant_name VARCHAR(255) COMMENT '参会者姓名',
    participant_email VARCHAR(255) COMMENT '参会者邮箱',
    join_time DATETIME COMMENT '加入时间',
    leave_time DATETIME COMMENT '离开时间',
    duration_minutes INT COMMENT '参会时长（分钟）',
    user_type ENUM('HOST', 'CO_HOST', 'PANELIST', 'ATTENDEE', 'UNKNOWN') DEFAULT 'ATTENDEE' COMMENT '用户类型',
    participant_user_id VARCHAR(255) COMMENT '参会者用户ID（如果是注册用户）',
    registrant_id VARCHAR(255) COMMENT '注册者ID（如果是注册会议）',
    status VARCHAR(50) COMMENT '参会状态',
    recording_consent BOOLEAN COMMENT '是否同意录制',
    in_waiting_room BOOLEAN DEFAULT FALSE COMMENT '是否在等候室',
    has_pstn BOOLEAN DEFAULT FALSE COMMENT '是否使用电话接入',
    has_voip BOOLEAN DEFAULT FALSE COMMENT '是否使用网络语音',
    has_3rd_party_audio BOOLEAN DEFAULT FALSE COMMENT '是否使用第三方音频',
    has_video BOOLEAN DEFAULT FALSE COMMENT '是否开启视频',
    has_screen_share BOOLEAN DEFAULT FALSE COMMENT '是否进行屏幕共享',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    location VARCHAR(255) COMMENT '地理位置',
    network_type VARCHAR(50) COMMENT '网络类型',
    microphone VARCHAR(255) COMMENT '麦克风设备',
    speaker VARCHAR(255) COMMENT '扬声器设备',
    camera VARCHAR(255) COMMENT '摄像头设备',
    data_center VARCHAR(100) COMMENT '数据中心',
    connection_type VARCHAR(50) COMMENT '连接类型',
    join_count INT DEFAULT 1 COMMENT '加入次数（如果多次进出会议）',
    participant_data JSON COMMENT '参会者完整原始数据（JSON格式）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 外键约束
    FOREIGN KEY (meeting_report_id) REFERENCES t_meeting_reports(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_meeting_report_id (meeting_report_id),
    INDEX idx_participant_email (participant_email),
    INDEX idx_join_time (join_time),
    INDEX idx_duration (duration_minutes),
    INDEX idx_user_type (user_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议参会人员详情表';

-- 创建会议报告获取任务表（用于异步处理和重试机制）
CREATE TABLE IF NOT EXISTS t_meeting_report_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    zoom_meeting_uuid VARCHAR(255) NOT NULL COMMENT 'Zoom会议UUID',
    zoom_meeting_id VARCHAR(255) NOT NULL COMMENT 'Zoom会议ID',
    task_status ENUM('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '任务状态',
    priority INT DEFAULT 5 COMMENT '优先级（1-10，数字越小优先级越高）',
    scheduled_time DATETIME COMMENT '计划执行时间',
    started_time DATETIME COMMENT '开始执行时间',
    completed_time DATETIME COMMENT '完成时间',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retry_count INT DEFAULT 3 COMMENT '最大重试次数',
    error_message TEXT COMMENT '错误信息',
    task_data JSON COMMENT '任务相关数据',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_zoom_meeting_uuid (zoom_meeting_uuid),
    INDEX idx_task_status (task_status),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    
    -- 唯一约束：每个会议UUID只能有一个活跃任务
    UNIQUE KEY uk_zoom_meeting_uuid_active (zoom_meeting_uuid, task_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议报告获取任务表';

-- 插入初始化数据（如果需要）
-- 这里可以添加一些默认配置或测试数据

-- 创建视图：会议报告汇总视图（便于查询）
CREATE OR REPLACE VIEW v_meeting_reports_summary AS
SELECT 
    mr.id,
    mr.zoom_meeting_uuid,
    mr.zoom_meeting_id,
    mr.topic,
    mr.start_time,
    mr.end_time,
    mr.duration_minutes,
    mr.total_participants,
    mr.unique_participants,
    mr.has_recording,
    mr.fetch_status,
    zm.host_id,
    zm.status as meeting_status,
    zm.billing_mode,
    zm.pmi_record_id,
    COUNT(mp.id) as participant_records_count,
    AVG(mp.duration_minutes) as avg_participant_duration,
    MAX(mp.duration_minutes) as max_participant_duration,
    MIN(mp.duration_minutes) as min_participant_duration
FROM t_meeting_reports mr
LEFT JOIN t_zoom_meetings zm ON mr.zoom_meeting_uuid = zm.zoom_meeting_uuid
LEFT JOIN t_meeting_participants mp ON mr.id = mp.meeting_report_id
GROUP BY mr.id, mr.zoom_meeting_uuid, mr.zoom_meeting_id, mr.topic, 
         mr.start_time, mr.end_time, mr.duration_minutes, mr.total_participants,
         mr.unique_participants, mr.has_recording, mr.fetch_status,
         zm.host_id, zm.status, zm.billing_mode, zm.pmi_record_id;
