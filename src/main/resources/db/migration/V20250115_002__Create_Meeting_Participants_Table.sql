-- 创建会议参会者表
-- 用于存储会议报告中的参会者详细信息

CREATE TABLE IF NOT EXISTS t_meeting_participants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    meeting_report_id BIGINT NOT NULL COMMENT '关联的会议报告ID',
    participant_uuid VARCHAR(255) COMMENT '参会者UUID',
    user_name VARCHAR(255) COMMENT '参会者姓名',
    user_email VARCHAR(255) COMMENT '参会者邮箱',
    join_time DATETIME COMMENT '加入时间',
    leave_time DATETIME COMMENT '离开时间',
    duration_minutes INT DEFAULT 0 COMMENT '参会时长（分钟）',
    attentiveness_score INT COMMENT '专注度评分',
    status VARCHAR(50) COMMENT '参会状态',
    failover BOOLEAN DEFAULT FALSE COMMENT '是否故障转移',
    role VARCHAR(50) COMMENT '参会者角色',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_meeting_participants_report 
        FOREIGN KEY (meeting_report_id) 
        REFERENCES t_meeting_reports(id) 
        ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_meeting_report_id (meeting_report_id),
    INDEX idx_user_email (user_email),
    INDEX idx_join_time (join_time),
    INDEX idx_duration (duration_minutes)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议参会者表';
