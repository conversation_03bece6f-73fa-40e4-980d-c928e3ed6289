-- 添加会议参会者表缺失的字段
-- 使其与 MeetingParticipant 实体类完全匹配

-- 添加用户类型字段
ALTER TABLE t_meeting_participants
ADD COLUMN user_type ENUM('HOST', 'CO_HOST', 'PANELIST', 'ATTENDEE', 'UNKNOWN') DEFAULT 'ATTENDEE' COMMENT '用户类型';

-- 添加参会者用户ID字段
ALTER TABLE t_meeting_participants
ADD COLUMN participant_user_id VARCHAR(255) COMMENT '参会者用户ID';

-- 添加注册者ID字段
ALTER TABLE t_meeting_participants
ADD COLUMN registrant_id VARCHAR(255) COMMENT '注册者ID';

-- 添加录制同意字段
ALTER TABLE t_meeting_participants
ADD COLUMN recording_consent BOOLEAN COMMENT '是否同意录制';

-- 添加等候室字段
ALTER TABLE t_meeting_participants
ADD COLUMN in_waiting_room BOOLEAN DEFAULT FALSE COMMENT '是否在等候室';

-- 添加音频相关字段
ALTER TABLE t_meeting_participants
ADD COLUMN has_pstn BOOLEAN DEFAULT FALSE COMMENT '是否有电话接入';

ALTER TABLE t_meeting_participants
ADD COLUMN has_voip BOOLEAN DEFAULT FALSE COMMENT '是否有网络语音';

ALTER TABLE t_meeting_participants
ADD COLUMN has_3rd_party_audio BOOLEAN DEFAULT FALSE COMMENT '是否有第三方音频';

-- 添加视频相关字段
ALTER TABLE t_meeting_participants
ADD COLUMN has_video BOOLEAN DEFAULT FALSE COMMENT '是否有视频';

ALTER TABLE t_meeting_participants
ADD COLUMN has_screen_share BOOLEAN DEFAULT FALSE COMMENT '是否有屏幕共享';

-- 添加网络相关字段
ALTER TABLE t_meeting_participants
ADD COLUMN ip_address VARCHAR(45) COMMENT 'IP地址';

ALTER TABLE t_meeting_participants
ADD COLUMN location VARCHAR(255) COMMENT '地理位置';

ALTER TABLE t_meeting_participants
ADD COLUMN network_type VARCHAR(50) COMMENT '网络类型';

-- 添加设备相关字段
ALTER TABLE t_meeting_participants
ADD COLUMN microphone VARCHAR(255) COMMENT '麦克风设备';

ALTER TABLE t_meeting_participants
ADD COLUMN speaker VARCHAR(255) COMMENT '扬声器设备';

ALTER TABLE t_meeting_participants
ADD COLUMN camera VARCHAR(255) COMMENT '摄像头设备';

-- 添加连接相关字段
ALTER TABLE t_meeting_participants
ADD COLUMN data_center VARCHAR(100) COMMENT '数据中心';

ALTER TABLE t_meeting_participants
ADD COLUMN connection_type VARCHAR(50) COMMENT '连接类型';

ALTER TABLE t_meeting_participants
ADD COLUMN join_count INT DEFAULT 1 COMMENT '加入次数';

-- 添加扩展数据字段
ALTER TABLE t_meeting_participants
ADD COLUMN participant_data JSON COMMENT '参会者扩展数据';

-- 删除不需要的字段（如果存在）
-- ALTER TABLE t_meeting_participants DROP COLUMN IF EXISTS attentiveness_score;
-- ALTER TABLE t_meeting_participants DROP COLUMN IF EXISTS failover;
-- ALTER TABLE t_meeting_participants DROP COLUMN IF EXISTS role;
-- ALTER TABLE t_meeting_participants DROP COLUMN IF EXISTS updated_at;

-- 添加索引（MySQL不支持IF NOT EXISTS，如果索引已存在会报错但不影响功能）
-- CREATE INDEX idx_participant_user_id ON t_meeting_participants(participant_user_id);
-- CREATE INDEX idx_registrant_id ON t_meeting_participants(registrant_id);
-- CREATE INDEX idx_user_type ON t_meeting_participants(user_type);
-- CREATE INDEX idx_has_video ON t_meeting_participants(has_video);
-- CREATE INDEX idx_connection_type ON t_meeting_participants(connection_type);

-- 占位符确保迁移成功
SELECT 'Meeting participants fields migration completed' as status;
