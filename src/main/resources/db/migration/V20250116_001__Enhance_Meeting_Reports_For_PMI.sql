-- 会议报告功能增强 - PMI级别报告支持
-- 版本: V20250116_001
-- 描述: 增强会议报告表结构，支持PMI级别的会议报告聚合和全量报告管理
-- 日期: 2025-01-16

-- 1. 增强 t_meeting_reports 表结构
ALTER TABLE t_meeting_reports 
ADD COLUMN pmi_number VARCHAR(20) COMMENT 'PMI号码，关联PMI会议',
ADD COLUMN meeting_type ENUM('SCHEDULED', 'PMI', 'INSTANT') DEFAULT 'SCHEDULED' COMMENT '会议类型：预约会议、PMI会议、即时会议',
ADD COLUMN zoom_auth_id BIGINT COMMENT 'Zoom主账号ID，关联t_zoom_auth表',
ADD COLUMN host_user_id VARCHAR(255) COMMENT '主持人用户ID',
ADD COLUMN participant_count INT DEFAULT 0 COMMENT '实际参会人数（缓存字段）';

-- 2. 添加新的索引
CREATE INDEX idx_meeting_reports_pmi ON t_meeting_reports(pmi_number);
CREATE INDEX idx_meeting_reports_type ON t_meeting_reports(meeting_type);
CREATE INDEX idx_meeting_reports_auth ON t_meeting_reports(zoom_auth_id);
CREATE INDEX idx_meeting_reports_host ON t_meeting_reports(host_user_id);

-- 3. 添加复合索引用于优化查询
CREATE INDEX idx_meeting_reports_pmi_time ON t_meeting_reports(pmi_number, start_time);
CREATE INDEX idx_meeting_reports_auth_time ON t_meeting_reports(zoom_auth_id, start_time);
CREATE INDEX idx_meeting_reports_type_status ON t_meeting_reports(meeting_type, fetch_status);

-- 4. 创建 PMI 会议统计表
CREATE TABLE IF NOT EXISTS t_pmi_meeting_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    pmi_number VARCHAR(20) NOT NULL COMMENT 'PMI号码',
    pmi_record_id BIGINT COMMENT '关联的PMI记录ID',
    total_meetings INT DEFAULT 0 COMMENT '总会议次数',
    total_duration_minutes INT DEFAULT 0 COMMENT '总时长(分钟)',
    total_participants INT DEFAULT 0 COMMENT '总参会人数',
    unique_participants_count INT DEFAULT 0 COMMENT '唯一参会人数',
    avg_duration_minutes DECIMAL(10,2) DEFAULT 0 COMMENT '平均会议时长',
    avg_participants DECIMAL(10,2) DEFAULT 0 COMMENT '平均参会人数',
    last_meeting_time DATETIME COMMENT '最后会议时间',
    first_meeting_time DATETIME COMMENT '首次会议时间',
    most_used_zoom_auth_id BIGINT COMMENT '最常使用的Zoom主账号ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 唯一约束
    UNIQUE KEY uk_pmi_number (pmi_number),
    
    -- 索引
    INDEX idx_pmi_record_id (pmi_record_id),
    INDEX idx_last_meeting_time (last_meeting_time),
    INDEX idx_total_meetings (total_meetings),
    INDEX idx_most_used_auth (most_used_zoom_auth_id),
    
    -- 外键约束
    FOREIGN KEY (pmi_record_id) REFERENCES t_pmi_records(id) ON DELETE SET NULL,
    FOREIGN KEY (most_used_zoom_auth_id) REFERENCES t_zoom_auth(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PMI会议统计表';

-- 5. 创建会议报告汇总视图（增强版）
CREATE OR REPLACE VIEW v_meeting_reports_enhanced AS
SELECT 
    mr.id,
    mr.zoom_meeting_uuid,
    mr.zoom_meeting_id,
    mr.topic,
    mr.start_time,
    mr.end_time,
    mr.duration_minutes,
    mr.total_participants,
    mr.unique_participants,
    mr.participant_count,
    mr.has_recording,
    mr.fetch_status,
    mr.pmi_number,
    mr.meeting_type,
    mr.zoom_auth_id,
    mr.host_user_id,
    mr.created_at,
    mr.updated_at,
    
    -- 关联 Zoom 主账号信息
    za.account_name as zoom_account_name,
    za.primary_email as zoom_account_email,
    
    -- 关联 PMI 信息
    pr.pmi_number as pmi_record_number,
    pr.user_id as pmi_user_id,
    pr.status as pmi_status,
    
    -- 关联 Zoom 会议信息
    zm.host_id as zoom_meeting_host_id,
    zm.status as zoom_meeting_status,
    zm.billing_mode,
    zm.pmi_record_id,
    
    -- 参会者统计
    COUNT(mp.id) as participant_records_count,
    AVG(mp.duration_minutes) as avg_participant_duration,
    MAX(mp.duration_minutes) as max_participant_duration,
    MIN(mp.duration_minutes) as min_participant_duration
    
FROM t_meeting_reports mr
LEFT JOIN t_zoom_auth za ON mr.zoom_auth_id = za.id
LEFT JOIN t_pmi_records pr ON mr.pmi_number = pr.pmi_number
LEFT JOIN t_zoom_meetings zm ON mr.zoom_meeting_uuid = zm.zoom_meeting_uuid
LEFT JOIN t_meeting_participants mp ON mr.id = mp.meeting_report_id
GROUP BY mr.id, mr.zoom_meeting_uuid, mr.zoom_meeting_id, mr.topic, 
         mr.start_time, mr.end_time, mr.duration_minutes, mr.total_participants,
         mr.unique_participants, mr.participant_count, mr.has_recording, mr.fetch_status,
         mr.pmi_number, mr.meeting_type, mr.zoom_auth_id, mr.host_user_id,
         mr.created_at, mr.updated_at,
         za.account_name, za.primary_email,
         pr.pmi_number, pr.user_id, pr.status,
         zm.host_id, zm.status, zm.billing_mode, zm.pmi_record_id;

-- 6. 创建 PMI 会议报告汇总视图
CREATE OR REPLACE VIEW v_pmi_meeting_reports AS
SELECT 
    pms.pmi_number,
    pms.pmi_record_id,
    pms.total_meetings,
    pms.total_duration_minutes,
    pms.total_participants,
    pms.unique_participants_count,
    pms.avg_duration_minutes,
    pms.avg_participants,
    pms.last_meeting_time,
    pms.first_meeting_time,
    pms.most_used_zoom_auth_id,
    pms.created_at as stats_created_at,
    pms.updated_at as stats_updated_at,
    
    -- PMI 基础信息
    pr.user_id as pmi_user_id,
    pr.status as pmi_status,
    pr.total_minutes as pmi_total_minutes,
    pr.available_minutes as pmi_available_minutes,
    pr.billing_mode as pmi_billing_mode,
    
    -- 最常用的 Zoom 账号信息
    za.account_name as most_used_account_name,
    za.primary_email as most_used_account_email,
    
    -- 最近会议信息
    latest_mr.topic as latest_meeting_topic,
    latest_mr.zoom_meeting_id as latest_meeting_id,
    latest_mr.duration_minutes as latest_meeting_duration,
    latest_mr.total_participants as latest_meeting_participants
    
FROM t_pmi_meeting_stats pms
LEFT JOIN t_pmi_records pr ON pms.pmi_record_id = pr.id
LEFT JOIN t_zoom_auth za ON pms.most_used_zoom_auth_id = za.id
LEFT JOIN t_meeting_reports latest_mr ON (
    latest_mr.pmi_number = pms.pmi_number 
    AND latest_mr.start_time = pms.last_meeting_time
)
ORDER BY pms.last_meeting_time DESC;

-- 7. 创建触发器：自动更新 PMI 统计数据
DELIMITER $$

CREATE TRIGGER tr_meeting_reports_pmi_stats_insert
AFTER INSERT ON t_meeting_reports
FOR EACH ROW
BEGIN
    IF NEW.pmi_number IS NOT NULL AND NEW.fetch_status = 'SUCCESS' THEN
        INSERT INTO t_pmi_meeting_stats (
            pmi_number, 
            total_meetings, 
            total_duration_minutes, 
            total_participants,
            last_meeting_time,
            first_meeting_time,
            most_used_zoom_auth_id
        ) VALUES (
            NEW.pmi_number, 
            1, 
            COALESCE(NEW.duration_minutes, 0), 
            COALESCE(NEW.total_participants, 0),
            NEW.start_time,
            NEW.start_time,
            NEW.zoom_auth_id
        )
        ON DUPLICATE KEY UPDATE
            total_meetings = total_meetings + 1,
            total_duration_minutes = total_duration_minutes + COALESCE(NEW.duration_minutes, 0),
            total_participants = total_participants + COALESCE(NEW.total_participants, 0),
            avg_duration_minutes = total_duration_minutes / total_meetings,
            avg_participants = total_participants / total_meetings,
            last_meeting_time = CASE 
                WHEN NEW.start_time > last_meeting_time THEN NEW.start_time 
                ELSE last_meeting_time 
            END,
            first_meeting_time = CASE 
                WHEN NEW.start_time < first_meeting_time THEN NEW.start_time 
                ELSE first_meeting_time 
            END,
            updated_at = CURRENT_TIMESTAMP;
    END IF;
END$$

CREATE TRIGGER tr_meeting_reports_pmi_stats_update
AFTER UPDATE ON t_meeting_reports
FOR EACH ROW
BEGIN
    -- 如果报告状态从非成功变为成功，更新统计
    IF NEW.pmi_number IS NOT NULL 
       AND OLD.fetch_status != 'SUCCESS' 
       AND NEW.fetch_status = 'SUCCESS' THEN
        
        INSERT INTO t_pmi_meeting_stats (
            pmi_number, 
            total_meetings, 
            total_duration_minutes, 
            total_participants,
            last_meeting_time,
            first_meeting_time,
            most_used_zoom_auth_id
        ) VALUES (
            NEW.pmi_number, 
            1, 
            COALESCE(NEW.duration_minutes, 0), 
            COALESCE(NEW.total_participants, 0),
            NEW.start_time,
            NEW.start_time,
            NEW.zoom_auth_id
        )
        ON DUPLICATE KEY UPDATE
            total_meetings = total_meetings + 1,
            total_duration_minutes = total_duration_minutes + COALESCE(NEW.duration_minutes, 0),
            total_participants = total_participants + COALESCE(NEW.total_participants, 0),
            avg_duration_minutes = total_duration_minutes / total_meetings,
            avg_participants = total_participants / total_meetings,
            last_meeting_time = CASE 
                WHEN NEW.start_time > last_meeting_time THEN NEW.start_time 
                ELSE last_meeting_time 
            END,
            first_meeting_time = CASE 
                WHEN NEW.start_time < first_meeting_time THEN NEW.start_time 
                ELSE first_meeting_time 
            END,
            updated_at = CURRENT_TIMESTAMP;
    END IF;
END$$

DELIMITER ;

-- 8. 初始化现有数据的 PMI 统计
-- 注意：这个操作可能需要一些时间，建议在维护窗口执行
INSERT INTO t_pmi_meeting_stats (
    pmi_number,
    total_meetings,
    total_duration_minutes,
    total_participants,
    avg_duration_minutes,
    avg_participants,
    last_meeting_time,
    first_meeting_time,
    most_used_zoom_auth_id
)
SELECT 
    mr.pmi_number,
    COUNT(*) as total_meetings,
    SUM(COALESCE(mr.duration_minutes, 0)) as total_duration_minutes,
    SUM(COALESCE(mr.total_participants, 0)) as total_participants,
    AVG(COALESCE(mr.duration_minutes, 0)) as avg_duration_minutes,
    AVG(COALESCE(mr.total_participants, 0)) as avg_participants,
    MAX(mr.start_time) as last_meeting_time,
    MIN(mr.start_time) as first_meeting_time,
    (
        SELECT zoom_auth_id 
        FROM t_meeting_reports mr2 
        WHERE mr2.pmi_number = mr.pmi_number 
          AND mr2.zoom_auth_id IS NOT NULL
        GROUP BY zoom_auth_id 
        ORDER BY COUNT(*) DESC 
        LIMIT 1
    ) as most_used_zoom_auth_id
FROM t_meeting_reports mr
WHERE mr.pmi_number IS NOT NULL 
  AND mr.fetch_status = 'SUCCESS'
GROUP BY mr.pmi_number
ON DUPLICATE KEY UPDATE
    total_meetings = VALUES(total_meetings),
    total_duration_minutes = VALUES(total_duration_minutes),
    total_participants = VALUES(total_participants),
    avg_duration_minutes = VALUES(avg_duration_minutes),
    avg_participants = VALUES(avg_participants),
    last_meeting_time = VALUES(last_meeting_time),
    first_meeting_time = VALUES(first_meeting_time),
    most_used_zoom_auth_id = VALUES(most_used_zoom_auth_id),
    updated_at = CURRENT_TIMESTAMP;

-- 9. 创建用于数据一致性检查的存储过程
DELIMITER $$

CREATE PROCEDURE sp_check_pmi_stats_consistency()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_pmi_number VARCHAR(20);
    DECLARE v_calculated_meetings INT;
    DECLARE v_stored_meetings INT;
    
    DECLARE pmi_cursor CURSOR FOR 
        SELECT DISTINCT pmi_number FROM t_meeting_reports WHERE pmi_number IS NOT NULL;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_pmi_inconsistencies (
        pmi_number VARCHAR(20),
        calculated_meetings INT,
        stored_meetings INT,
        difference INT
    );
    
    OPEN pmi_cursor;
    
    read_loop: LOOP
        FETCH pmi_cursor INTO v_pmi_number;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 计算实际会议数量
        SELECT COUNT(*) INTO v_calculated_meetings
        FROM t_meeting_reports 
        WHERE pmi_number = v_pmi_number AND fetch_status = 'SUCCESS';
        
        -- 获取存储的会议数量
        SELECT COALESCE(total_meetings, 0) INTO v_stored_meetings
        FROM t_pmi_meeting_stats 
        WHERE pmi_number = v_pmi_number;
        
        -- 如果不一致，记录到临时表
        IF v_calculated_meetings != v_stored_meetings THEN
            INSERT INTO temp_pmi_inconsistencies 
            VALUES (v_pmi_number, v_calculated_meetings, v_stored_meetings, 
                   v_calculated_meetings - v_stored_meetings);
        END IF;
        
    END LOOP;
    
    CLOSE pmi_cursor;
    
    -- 返回不一致的记录
    SELECT * FROM temp_pmi_inconsistencies;
    
    DROP TEMPORARY TABLE temp_pmi_inconsistencies;
END$$

DELIMITER ;

-- 10. 添加注释说明
SELECT 'Meeting reports enhancement for PMI support completed successfully' as migration_status;
