-- 创建PMI窗口定时任务表
CREATE TABLE t_pmi_schedule_window_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    pmi_window_id BIGINT NOT NULL COMMENT '关联的PMI窗口ID',
    task_type ENUM('PMI_WINDOW_OPEN', 'PMI_WINDOW_CLOSE') NOT NULL COMMENT '任务类型',
    scheduled_time DATETIME NOT NULL COMMENT '计划执行时间',
    actual_execution_time DATETIME COMMENT '实际执行时间',
    status ENUM('SCHEDULED', 'EXECUTING', 'COMPLETED', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'SCHEDULED' COMMENT '任务状态',
    task_key VARCHAR(255) UNIQUE NOT NULL COMMENT '任务唯一标识',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    error_message TEXT COMMENT '错误信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_pmi_window_id (pmi_window_id),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status (status),
    INDEX idx_task_key (task_key),
    INDEX idx_task_type (task_type),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (pmi_window_id) REFERENCES t_pmi_schedule_windows(id) ON DELETE CASCADE
) COMMENT='PMI窗口定时任务表';

-- 扩展现有PMI窗口表，添加任务ID字段
ALTER TABLE t_pmi_schedule_windows 
ADD COLUMN open_task_id BIGINT COMMENT '开启任务ID',
ADD COLUMN close_task_id BIGINT COMMENT '关闭任务ID';

-- 添加索引
ALTER TABLE t_pmi_schedule_windows
ADD INDEX idx_open_task_id (open_task_id),
ADD INDEX idx_close_task_id (close_task_id);
