-- 添加重复策略详细选择字段到PMI计划表
ALTER TABLE t_pmi_schedules 
ADD COLUMN week_days VARCHAR(50) COMMENT '按周重复时选择的星期几 (1-7, 1=周一, 7=周日)，逗号分隔',
ADD COLUMN month_days VARCHAR(200) COMMENT '按月重复时选择的日期 (1-31)，逗号分隔';

-- 添加索引以提高查询性能
CREATE INDEX idx_pmi_schedules_repeat_type ON t_pmi_schedules(repeat_type);
CREATE INDEX idx_pmi_schedules_week_days ON t_pmi_schedules(week_days);
CREATE INDEX idx_pmi_schedules_month_days ON t_pmi_schedules(month_days);
