-- 添加窗口实际开启和关闭时间字段
-- 2025-07-23

-- 添加实际开启时间字段
ALTER TABLE pmi_schedule_window 
ADD COLUMN actual_start_time DATETIME NULL COMMENT '实际开启时间';

-- 添加实际关闭时间字段  
ALTER TABLE pmi_schedule_window 
ADD COLUMN actual_end_time DATETIME NULL COMMENT '实际关闭时间';

-- 为现有的已完成窗口设置实际关闭时间（使用更新时间作为估算）
UPDATE pmi_schedule_window 
SET actual_end_time = updated_at 
WHERE status = 'completed' AND updated_at IS NOT NULL;

-- 为现有的执行中窗口设置实际开启时间（使用更新时间作为估算）
UPDATE pmi_schedule_window 
SET actual_start_time = updated_at 
WHERE status = 'active' AND updated_at IS NOT NULL;
