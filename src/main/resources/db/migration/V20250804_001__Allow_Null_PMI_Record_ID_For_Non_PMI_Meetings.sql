-- 修改t_zoom_meetings表，允许pmi_record_id为null
-- 
-- 目的：支持meeting.started事件为所有类型的会议创建t_zoom_meetings记录
-- 
-- 会议类型：
-- 1. PMI会议：pmi_record_id有值，关联具体的PMI记录
-- 2. 安排会议：pmi_record_id为null，通过zoom_meeting_id关联t_meetings表
-- 3. 其他会议：pmi_record_id为null，如Zoom App直接创建的会议
-- 
-- 修改原因：
-- 原来的约束要求pmi_record_id NOT NULL，导致非PMI会议无法创建记录
-- 修改后支持全量会议监控，实现"Zoom会议看板"的完整覆盖
-- 
-- 影响：
-- - 现有PMI会议记录不受影响
-- - 新的非PMI会议可以正常创建记录
-- - 向后兼容，不破坏现有功能

-- 修改字段约束，允许NULL值
ALTER TABLE t_zoom_meetings 
MODIFY COLUMN pmi_record_id BIGINT NULL 
COMMENT 'PMI记录ID（PMI会议时有值，非PMI会议时为null）';

-- 添加性能优化索引（忽略已存在的索引错误）
CREATE INDEX idx_zoom_meetings_pmi_record_id ON t_zoom_meetings(pmi_record_id);
CREATE INDEX idx_zoom_meetings_status_start_time ON t_zoom_meetings(status, start_time);
CREATE INDEX idx_zoom_meetings_host_id ON t_zoom_meetings(host_id);
CREATE INDEX idx_zoom_meetings_uuid ON t_zoom_meetings(zoom_meeting_uuid);
CREATE INDEX idx_zoom_meetings_meeting_id ON t_zoom_meetings(zoom_meeting_id);
