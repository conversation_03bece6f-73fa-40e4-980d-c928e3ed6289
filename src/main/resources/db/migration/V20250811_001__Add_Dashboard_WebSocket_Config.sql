-- 添加Dashboard WebSocket配置
INSERT INTO t_system_config (config_key, config_value, description, config_type, is_active, created_by, updated_by, created_at, updated_at)
VALUES 
('dashboard.websocket.enabled', 'true', 'Dashboard实时监控WebSocket开关，控制是否启用WebSocket实时数据推送', 'BOOLEAN', true, 'SYSTEM', 'SYSTEM', NOW(), NOW())
ON DUPLICATE KEY UPDATE
config_value = VALUES(config_value),
description = VALUES(description),
config_type = VALUES(config_type),
updated_by = VALUES(updated_by),
updated_at = NOW();
