-- 添加会议UUID和主持人密钥字段
-- 用于支持会议主持信息复制功能和会议主持人页面

-- 添加会议UUID字段
ALTER TABLE t_meetings ADD COLUMN meeting_uuid VARCHAR(36) UNIQUE COMMENT '会议UUID，用于生成主持人页面链接';

-- 添加主持人密钥字段
ALTER TABLE t_meetings ADD COLUMN host_key VARCHAR(10) COMMENT '主持人密钥（Host Key），用于会议控制权转移';

-- 为现有记录生成UUID
UPDATE t_meetings SET meeting_uuid = UUID() WHERE meeting_uuid IS NULL;

-- 设置meeting_uuid为非空
ALTER TABLE t_meetings MODIFY COLUMN meeting_uuid VARCHAR(36) NOT NULL COMMENT '会议UUID，用于生成主持人页面链接';

-- 创建索引以提高查询性能
CREATE INDEX idx_meetings_uuid ON t_meetings(meeting_uuid);
