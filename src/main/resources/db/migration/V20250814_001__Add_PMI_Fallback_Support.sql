-- 添加PMI回退支持功能
-- 版本: V20250814_001
-- 描述: 为PMI记录表添加回退标志字段，并添加老系统域名配置
-- 日期: 2025-08-14

-- 1. 为PMI记录表添加回退标志字段
ALTER TABLE t_pmi_records 
ADD COLUMN fallback_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用回退到老系统';

-- 2. 为现有记录设置默认值
UPDATE t_pmi_records SET fallback_enabled = FALSE WHERE fallback_enabled IS NULL;

-- 3. 添加索引以提高查询性能
CREATE INDEX idx_pmi_fallback_enabled ON t_pmi_records(fallback_enabled);

-- 4. 添加老系统域名配置
INSERT INTO t_system_config (config_key, config_value, description, config_type, is_active, created_by, updated_by, created_at, updated_at)
VALUES 
('user_frontend_old.domain.base_url', 'https://r.wxzoom.com', '老系统用户前端基础域名，用于PMI回退功能', 'STRING', true, 'SYSTEM', 'SYSTEM', NOW(), NOW())
ON DUPLICATE KEY UPDATE
config_value = VALUES(config_value),
description = VALUES(description),
config_type = VALUES(config_type),
updated_by = VALUES(updated_by),
updated_at = NOW();

-- 5. 添加PMI回退功能说明配置
INSERT INTO t_system_config (config_key, config_value, description, config_type, is_active, created_by, updated_by, created_at, updated_at)
VALUES 
('pmi.fallback.enabled', 'true', 'PMI回退功能总开关，控制是否启用PMI级别的系统回退', 'BOOLEAN', true, 'SYSTEM', 'SYSTEM', NOW(), NOW())
ON DUPLICATE KEY UPDATE
config_value = VALUES(config_value),
description = VALUES(description),
config_type = VALUES(config_type),
updated_by = VALUES(updated_by),
updated_at = NOW();
