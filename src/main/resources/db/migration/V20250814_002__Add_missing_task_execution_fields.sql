-- 添加任务执行记录表缺失的字段
-- 用于支持更丰富的任务执行统计和监控功能

-- 添加处理数量相关字段（如果不存在）
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND column_name = 'processed_count') = 0,
    'ALTER TABLE task_execution_record ADD COLUMN processed_count INT COMMENT ''处理的记录数量'' AFTER duration_ms',
    'SELECT ''Column processed_count already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND column_name = 'success_count') = 0,
    'ALTER TABLE task_execution_record ADD COLUMN success_count INT COMMENT ''成功处理的记录数量'' AFTER processed_count',
    'SELECT ''Column success_count already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND column_name = 'failure_count') = 0,
    'ALTER TABLE task_execution_record ADD COLUMN failure_count INT COMMENT ''失败处理的记录数量'' AFTER success_count',
    'SELECT ''Column failure_count already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加任务参数和结果字段（如果不存在）
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND column_name = 'task_parameters') = 0,
    'ALTER TABLE task_execution_record ADD COLUMN task_parameters TEXT COMMENT ''任务参数（JSON格式）'' AFTER next_retry_time',
    'SELECT ''Column task_parameters already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND column_name = 'execution_result') = 0,
    'ALTER TABLE task_execution_record ADD COLUMN execution_result TEXT COMMENT ''执行结果（JSON格式）'' AFTER task_parameters',
    'SELECT ''Column execution_result already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加执行节点信息字段（如果不存在）
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND column_name = 'execution_node') = 0,
    'ALTER TABLE task_execution_record ADD COLUMN execution_node VARCHAR(100) COMMENT ''执行节点信息'' AFTER execution_result',
    'SELECT ''Column execution_node already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建新的索引以提升查询性能（安全创建）
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND index_name = 'idx_task_name_status_execution_time') = 0,
    'CREATE INDEX idx_task_name_status_execution_time ON task_execution_record(task_name, status, execution_time)',
    'SELECT ''Index idx_task_name_status_execution_time already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND index_name = 'idx_processed_count') = 0,
    'CREATE INDEX idx_processed_count ON task_execution_record(processed_count)',
    'SELECT ''Index idx_processed_count already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND index_name = 'idx_success_failure_count') = 0,
    'CREATE INDEX idx_success_failure_count ON task_execution_record(success_count, failure_count)',
    'SELECT ''Index idx_success_failure_count already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
    WHERE table_name = 'task_execution_record'
    AND table_schema = DATABASE()
    AND index_name = 'idx_execution_node') = 0,
    'CREATE INDEX idx_execution_node ON task_execution_record(execution_node)',
    'SELECT ''Index idx_execution_node already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新表注释
ALTER TABLE task_execution_record COMMENT = '定时任务执行记录表，用于跟踪任务执行状态、性能监控、错误处理和统计分析';
