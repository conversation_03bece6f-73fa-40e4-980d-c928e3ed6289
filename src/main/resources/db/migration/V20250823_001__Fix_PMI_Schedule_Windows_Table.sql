-- 修复PMI窗口表结构和数据问题
-- 2025-08-23

-- 首先检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS t_pmi_schedule_windows (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    schedule_id BIGINT NOT NULL COMMENT '关联的计划ID',
    pmi_record_id BIGINT NOT NULL COMMENT 'PMI记录ID',
    window_date DATE NOT NULL COMMENT '窗口日期',
    end_date DATE COMMENT '窗口结束日期',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING, ACTIVE, COMPLETED, CANCELLED',
    zoom_user_id BIGINT COMMENT '分配的Zoom用户ID',
    error_message TEXT COMMENT '错误信息',
    actual_start_time DATETIME COMMENT '实际开始时间',
    actual_end_time DATETIME COMMENT '实际结束时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_schedule_id (schedule_id),
    INDEX idx_pmi_record_id (pmi_record_id),
    INDEX idx_window_date (window_date),
    INDEX idx_status (status),
    INDEX idx_zoom_user_id (zoom_user_id),
    INDEX idx_created_at (created_at),
    
    -- 外键约束（如果引用表存在）
    CONSTRAINT fk_window_schedule 
        FOREIGN KEY (schedule_id) REFERENCES t_pmi_schedules(id) 
        ON DELETE CASCADE,
    CONSTRAINT fk_window_pmi_record 
        FOREIGN KEY (pmi_record_id) REFERENCES t_pmi_records(id) 
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PMI窗口表';

-- 检查并添加缺失的字段
-- 添加start_date_time和end_date_time字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 't_pmi_schedule_windows' 
     AND COLUMN_NAME = 'start_date_time') = 0,
    'ALTER TABLE t_pmi_schedule_windows ADD COLUMN start_date_time DATETIME COMMENT ''窗口开始时间（精确到秒）''',
    'SELECT ''start_date_time column already exists'' as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 't_pmi_schedule_windows' 
     AND COLUMN_NAME = 'end_date_time') = 0,
    'ALTER TABLE t_pmi_schedule_windows ADD COLUMN end_date_time DATETIME COMMENT ''窗口结束时间（精确到秒）''',
    'SELECT ''end_date_time column already exists'' as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 't_pmi_schedule_windows' 
     AND INDEX_NAME = 'idx_start_date_time') = 0,
    'CREATE INDEX idx_start_date_time ON t_pmi_schedule_windows(start_date_time)',
    'SELECT ''idx_start_date_time index already exists'' as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 't_pmi_schedule_windows' 
     AND INDEX_NAME = 'idx_end_date_time') = 0,
    'CREATE INDEX idx_end_date_time ON t_pmi_schedule_windows(end_date_time)',
    'SELECT ''idx_end_date_time index already exists'' as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保PMI任务表存在
CREATE TABLE IF NOT EXISTS t_pmi_schedule_window_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    pmi_window_id BIGINT NOT NULL COMMENT '关联的PMI窗口ID',
    task_type ENUM('PMI_WINDOW_OPEN', 'PMI_WINDOW_CLOSE') NOT NULL COMMENT '任务类型',
    scheduled_time DATETIME NOT NULL COMMENT '计划执行时间',
    actual_execution_time DATETIME COMMENT '实际执行时间',
    status ENUM('SCHEDULED', 'EXECUTING', 'COMPLETED', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'SCHEDULED' COMMENT '任务状态',
    task_key VARCHAR(255) UNIQUE NOT NULL COMMENT '任务唯一标识',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    error_message TEXT COMMENT '错误信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_pmi_window_id (pmi_window_id),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status (status),
    INDEX idx_task_key (task_key),
    INDEX idx_task_type (task_type),
    INDEX idx_created_at (created_at),
    
    CONSTRAINT fk_task_window 
        FOREIGN KEY (pmi_window_id) REFERENCES t_pmi_schedule_windows(id) 
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PMI窗口定时任务表';

-- 为PMI窗口表添加任务ID字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 't_pmi_schedule_windows' 
     AND COLUMN_NAME = 'open_task_id') = 0,
    'ALTER TABLE t_pmi_schedule_windows ADD COLUMN open_task_id BIGINT COMMENT ''开启任务ID''',
    'SELECT ''open_task_id column already exists'' as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 't_pmi_schedule_windows' 
     AND COLUMN_NAME = 'close_task_id') = 0,
    'ALTER TABLE t_pmi_schedule_windows ADD COLUMN close_task_id BIGINT COMMENT ''关闭任务ID''',
    'SELECT ''close_task_id column already exists'' as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加任务ID索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 't_pmi_schedule_windows' 
     AND INDEX_NAME = 'idx_open_task_id') = 0,
    'CREATE INDEX idx_open_task_id ON t_pmi_schedule_windows(open_task_id)',
    'SELECT ''idx_open_task_id index already exists'' as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 't_pmi_schedule_windows' 
     AND INDEX_NAME = 'idx_close_task_id') = 0,
    'CREATE INDEX idx_close_task_id ON t_pmi_schedule_windows(close_task_id)',
    'SELECT ''idx_close_task_id index already exists'' as result'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 修复数据完整性
-- 更新start_date_time和end_date_time字段
UPDATE t_pmi_schedule_windows 
SET start_date_time = TIMESTAMP(window_date, start_time),
    end_date_time = TIMESTAMP(COALESCE(end_date, window_date), end_time)
WHERE start_date_time IS NULL OR end_date_time IS NULL;
