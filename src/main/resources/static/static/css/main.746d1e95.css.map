{"version": 3, "file": "static/css/main.746d1e95.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CAEA,YACE,gBACF,CAEA,MAME,kBAAmB,CAHnB,oBAAoC,CACpC,iBAAkB,CAIlB,UAAY,CAHZ,YAAa,CAIb,eAAiB,CARjB,WAAY,CAMZ,sBAAuB,CALvB,WAQF,CAEA,qBAGE,eAAgB,CAFhB,gBAAiB,CACjB,YAEF,CAGA,uCACE,kCAAoC,CACpC,iBAAkB,CAClB,cAAe,CACf,uBACF,CAEA,6CACE,8BACF,CAEA,2GAEE,oBACF,CAEA,4CACE,oBAAyB,CACzB,eACF,CAGA,oCACE,oCAKF,CAGA,kEAPE,iBAAkB,CAClB,cAAe,CAEf,uBAAyB,CADzB,uBAUF,CAGA,yBAEE,qBACE,sBACF,CAGA,eACE,sBACF,CAGA,mBACE,eACF,CAOA,8CAEE,wBAA0B,CAD1B,yBAEF,CAGA,0EAEE,sBACF,CAGA,0EAKE,wBAA0B,CAH1B,yBAA2B,CAC3B,oBAAsB,CACtB,oBAEF,CAGA,gCAEE,wBAA0B,CAC1B,qBAAuB,CACvB,wBAA0B,CAH1B,yBAIF,CAEA,uCACE,0BACF,CAGA,uBACE,kBACF,CAEA,mCACE,wBACF,CAEA,2CACE,wBACF,CAGA,4BACE,2BACF,CAEA,4BACE,iBACF,CAGA,mBACE,sBAAwB,CACxB,+BAAiC,CACjC,iBACF,CAEA,4BAGE,wBAA0B,CAC1B,qBAAuB,CAFvB,yBAA2B,CAD3B,oBAIF,CAGA,8CAGE,wBAA0B,CAE1B,cAAe,CAHf,yBAA2B,CAE3B,kBAEF,CAEA,uBACE,4BAA8B,CAE9B,wBAA0B,CAD1B,yBAEF,CAEA,gCAGE,wBAA0B,CAD1B,qBAAuB,CAEvB,wBAA0B,CAH1B,yBAIF,CAEA,eAGE,yBAA4B,CAE5B,yCAAiD,CAJjD,yBAA2B,CAC3B,iBAAmB,CAEnB,mBAEF,CAGA,SAKE,2BAA6B,CAF7B,uBAAyB,CACzB,0BAA4B,CAH5B,kBAAoB,CACpB,uBAIF,CAGA,0BACE,cAAe,CACf,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAGA,oBACE,kBAAmB,CACnB,wBAAyB,CAGzB,iBAAkB,CAElB,aAAc,CADd,cAAe,CAGf,eAAgB,CALhB,kBAAmB,CADnB,gBAAiB,CAKjB,iBAEF,CAGA,gBACE,2BACF,CAMA,mDACE,sBACF,CACF,CAGA,yBACE,qBACE,qBACF,CAEA,eACE,sBACF,CAEA,4BACE,wBAA0B,CAC1B,qBAAuB,CACvB,yBACF,CAEA,8CAGE,wBAA0B,CAD1B,yBAEF,CAEA,2CACE,wBACF,CACF,CAGA,yBAEE,YACE,uBACF,CAGA,mBACE,wBACF,CAGA,oBACE,uBACF,CAGA,mBACE,yBACF,CAEA,wBAEE,wBAA0B,CAD1B,0BAEF,CAGA,qBACE,wBAA0B,CAE1B,eAAgB,CAChB,sBAAuB,CAFvB,kBAGF,CAGA,4BACE,2BACF,CACF", "sources": ["index.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n.ant-layout {\n  min-height: 100vh;\n}\n\n.logo {\n  height: 32px;\n  margin: 16px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n}\n\n.site-layout-content {\n  min-height: 280px;\n  padding: 24px;\n  background: #fff;\n}\n\n/* 菜单高亮样式 */\n.ant-menu-dark .ant-menu-item-selected {\n  background-color: #1890ff !important;\n  border-radius: 6px;\n  margin: 4px 8px;\n  width: calc(100% - 16px);\n}\n\n.ant-menu-dark .ant-menu-item-selected::after {\n  border-right: 3px solid #1890ff;\n}\n\n.ant-menu-dark .ant-menu-item-selected .ant-menu-item-icon,\n.ant-menu-dark .ant-menu-item-selected .anticon {\n  color: #ffffff !important;\n}\n\n.ant-menu-dark .ant-menu-item-selected span {\n  color: #ffffff !important;\n  font-weight: 600;\n}\n\n/* 菜单项悬停效果 */\n.ant-menu-dark .ant-menu-item:hover {\n  background-color: rgba(24, 144, 255, 0.2) !important;\n  border-radius: 6px;\n  margin: 4px 8px;\n  width: calc(100% - 16px);\n  transition: all 0.3s ease;\n}\n\n/* 菜单项间距调整 */\n.ant-menu-dark .ant-menu-item {\n  margin: 4px 8px;\n  border-radius: 6px;\n  width: calc(100% - 16px);\n  transition: all 0.3s ease;\n}\n\n/* 移动端响应式样式 */\n@media (max-width: 768px) {\n  /* 页面内边距调整 */\n  .site-layout-content {\n    padding: 12px !important;\n  }\n\n  /* 卡片内边距调整 */\n  .ant-card-body {\n    padding: 16px !important;\n  }\n\n  /* 表格响应式 */\n  .ant-table-wrapper {\n    overflow-x: auto;\n  }\n\n  .ant-table-thead > tr > th {\n    padding: 8px 4px !important;\n    font-size: 12px !important;\n  }\n\n  .ant-table-tbody > tr > td {\n    padding: 8px 4px !important;\n    font-size: 12px !important;\n  }\n\n  /* 隐藏不重要的列 */\n  .ant-table-thead > tr > th.mobile-hidden,\n  .ant-table-tbody > tr > td.mobile-hidden {\n    display: none !important;\n  }\n\n  /* 操作列样式优化 */\n  .ant-table-thead > tr > th.action-column,\n  .ant-table-tbody > tr > td.action-column {\n    position: static !important;\n    right: auto !important;\n    width: auto !important;\n    min-width: 80px !important;\n  }\n\n  /* 操作按钮样式 */\n  .mobile-action-buttons .ant-btn {\n    padding: 4px 6px !important;\n    font-size: 12px !important;\n    height: auto !important;\n    min-width: auto !important;\n  }\n\n  .mobile-action-buttons .ant-space-item {\n    margin-right: 4px !important;\n  }\n\n  /* 统计信息响应式 */\n  .mobile-stats .ant-col {\n    margin-bottom: 12px;\n  }\n\n  .mobile-stats .ant-statistic-title {\n    font-size: 12px !important;\n  }\n\n  .mobile-stats .ant-statistic-content-value {\n    font-size: 16px !important;\n  }\n\n  /* 搜索和筛选区域 */\n  .zoom-user-filters .ant-row {\n    margin-bottom: 8px !important;\n  }\n\n  .zoom-user-filters .ant-col {\n    margin-bottom: 8px;\n  }\n\n  /* 操作按钮区域 */\n  .zoom-user-actions {\n    display: flex !important;\n    flex-direction: column !important;\n    gap: 8px !important;\n  }\n\n  .zoom-user-actions .ant-btn {\n    width: 100% !important;\n    margin-bottom: 0 !important;\n    font-size: 13px !important;\n    height: 36px !important;\n  }\n\n  /* 表格优化 */\n  .ant-table-thead > tr > th,\n  .ant-table-tbody > tr > td {\n    padding: 6px 3px !important;\n    font-size: 11px !important;\n    white-space: nowrap;\n    min-width: 60px;\n  }\n\n  .ant-table-thead > tr > th {\n    background: #fafafa !important;\n    font-weight: 600 !important;\n    font-size: 10px !important;\n  }\n\n  .mobile-action-buttons .ant-btn {\n    padding: 2px 4px !important;\n    height: 24px !important;\n    font-size: 10px !important;\n    min-width: 24px !important;\n  }\n\n  .action-column {\n    position: sticky !important;\n    right: 0 !important;\n    background: white !important;\n    z-index: 1 !important;\n    box-shadow: -2px 0 4px rgba(0,0,0,0.1) !important;\n  }\n\n  /* 移动端表格标签优化 */\n  .ant-tag {\n    margin: 0 !important;\n    padding: 0 4px !important;\n    font-size: 9px !important;\n    line-height: 16px !important;\n    border-radius: 2px !important;\n  }\n\n  /* 移动端状态标签优化 */\n  .ant-table-tbody .ant-tag {\n    max-width: 50px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  /* 表格滚动提示 */\n  .mobile-scroll-hint {\n    background: #e6f7ff;\n    border: 1px solid #91d5ff;\n    padding: 8px 12px;\n    margin-bottom: 12px;\n    border-radius: 4px;\n    font-size: 11px;\n    color: #1890ff;\n    text-align: center;\n    line-height: 1.4;\n  }\n\n  /* 分页器响应式 */\n  .ant-pagination {\n    text-align: center !important;\n  }\n\n  .ant-pagination-options {\n    display: none !important;\n  }\n\n  .ant-pagination-total-text {\n    display: none !important;\n  }\n}\n\n/* 超小屏幕优化 (iPhone SE等) */\n@media (max-width: 375px) {\n  .site-layout-content {\n    padding: 8px !important;\n  }\n\n  .ant-card-body {\n    padding: 12px !important;\n  }\n\n  .zoom-user-actions .ant-btn {\n    font-size: 12px !important;\n    height: 32px !important;\n    padding: 4px 8px !important;\n  }\n\n  .ant-table-thead > tr > th,\n  .ant-table-tbody > tr > td {\n    padding: 6px 2px !important;\n    font-size: 11px !important;\n  }\n\n  .mobile-stats .ant-statistic-content-value {\n    font-size: 14px !important;\n  }\n}\n\n/* 移动端布局优化 */\n@media (max-width: 768px) {\n  /* 确保内容区域占满全宽 */\n  .ant-layout {\n    margin-left: 0 !important;\n  }\n\n  /* 头部在移动端的优化 */\n  .ant-layout-header {\n    padding: 0 16px !important;\n  }\n\n  /* 移动端内容区域调整 */\n  .ant-layout-content {\n    margin-left: 0 !important;\n  }\n\n  /* 移动端用户下拉菜单优化 */\n  .ant-dropdown-menu {\n    min-width: 120px !important;\n  }\n\n  .ant-dropdown-menu-item {\n    padding: 8px 12px !important;\n    font-size: 14px !important;\n  }\n\n  /* 移动端标题优化 */\n  .mobile-header-title {\n    font-size: 16px !important;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  /* 移动端菜单切换按钮优化 */\n  .ant-layout-header .ant-btn {\n    border-radius: 6px !important;\n  }\n}\n\n\n"], "names": [], "sourceRoot": ""}