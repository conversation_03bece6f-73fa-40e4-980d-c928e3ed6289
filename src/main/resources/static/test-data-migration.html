<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据迁移测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #117a8b;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            display: none;
            color: #666;
            font-style: italic;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据迁移测试页面</h1>
        
        <!-- 数据迁移状态 -->
        <div class="section">
            <h2>📊 数据迁移状态</h2>
            <div class="button-group">
                <button class="btn-info" onclick="getMigrationStatus()">获取迁移状态</button>
            </div>
            <div class="loading" id="statusLoading">正在获取状态...</div>
            <div id="statusResult" class="result info" style="display: none;"></div>
            <div id="statusStats" class="stats-grid" style="display: none;"></div>
        </div>

        <!-- 数据迁移操作 -->
        <div class="section">
            <h2>🔄 数据迁移操作</h2>
            <div class="button-group">
                <button class="btn-primary" onclick="fillMeetingReportFields()">填充会议报告字段</button>
                <button class="btn-success" onclick="initializePmiStats()">初始化PMI统计</button>
                <button class="btn-warning" onclick="performFullMigration()">执行完整迁移</button>
            </div>
            <div class="loading" id="migrationLoading">正在执行迁移...</div>
            <div id="migrationResult" class="result" style="display: none;"></div>
        </div>

        <!-- PMI报告测试 -->
        <div class="section">
            <h2>📈 PMI报告测试</h2>
            <div class="button-group">
                <button class="btn-info" onclick="getPmiOverview()">获取PMI概览</button>
                <button class="btn-info" onclick="getPmiList()">获取PMI列表</button>
                <button class="btn-info" onclick="getPmiStats()">获取PMI统计</button>
            </div>
            <div class="loading" id="pmiLoading">正在获取PMI数据...</div>
            <div id="pmiResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';

        function showLoading(elementId) {
            document.getElementById(elementId).style.display = 'block';
        }

        function hideLoading(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw new Error(`请求失败: ${error.message}`);
            }
        }

        // 获取数据迁移状态
        async function getMigrationStatus() {
            showLoading('statusLoading');
            try {
                const data = await apiCall(`${API_BASE}/data-migration/status`);
                hideLoading('statusLoading');
                
                showResult('statusResult', JSON.stringify(data, null, 2), 'success');
                
                // 显示统计卡片
                const statsContainer = document.getElementById('statusStats');
                statsContainer.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-value">${data.totalReports || 0}</div>
                        <div class="stat-label">总报告数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${data.reportsWithPmi || 0}</div>
                        <div class="stat-label">有PMI号码的报告</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${data.reportsWithZoomAuth || 0}</div>
                        <div class="stat-label">有Zoom主账号的报告</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${data.reportsWithHostUser || 0}</div>
                        <div class="stat-label">有主持人用户的报告</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${(data.pmiCoverage || 0).toFixed(1)}%</div>
                        <div class="stat-label">PMI覆盖率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${(data.zoomAuthCoverage || 0).toFixed(1)}%</div>
                        <div class="stat-label">Zoom主账号覆盖率</div>
                    </div>
                `;
                statsContainer.style.display = 'grid';
                
            } catch (error) {
                hideLoading('statusLoading');
                showResult('statusResult', error.message, 'error');
            }
        }

        // 填充会议报告字段
        async function fillMeetingReportFields() {
            showLoading('migrationLoading');
            try {
                const data = await apiCall(`${API_BASE}/data-migration/fill-meeting-report-fields`, {
                    method: 'POST'
                });
                hideLoading('migrationLoading');
                showResult('migrationResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                hideLoading('migrationLoading');
                showResult('migrationResult', error.message, 'error');
            }
        }

        // 初始化PMI统计
        async function initializePmiStats() {
            showLoading('migrationLoading');
            try {
                const data = await apiCall(`${API_BASE}/data-migration/initialize-pmi-stats`, {
                    method: 'POST'
                });
                hideLoading('migrationLoading');
                showResult('migrationResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                hideLoading('migrationLoading');
                showResult('migrationResult', error.message, 'error');
            }
        }

        // 执行完整迁移
        async function performFullMigration() {
            showLoading('migrationLoading');
            try {
                const data = await apiCall(`${API_BASE}/data-migration/full`, {
                    method: 'POST'
                });
                hideLoading('migrationLoading');
                showResult('migrationResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                hideLoading('migrationLoading');
                showResult('migrationResult', error.message, 'error');
            }
        }

        // 获取PMI概览
        async function getPmiOverview() {
            showLoading('pmiLoading');
            try {
                const data = await apiCall(`${API_BASE}/pmi-reports/overview`);
                hideLoading('pmiLoading');
                showResult('pmiResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                hideLoading('pmiLoading');
                showResult('pmiResult', error.message, 'error');
            }
        }

        // 获取PMI列表
        async function getPmiList() {
            showLoading('pmiLoading');
            try {
                const data = await apiCall(`${API_BASE}/pmi-reports/list?page=0&size=10`);
                hideLoading('pmiLoading');
                showResult('pmiResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                hideLoading('pmiLoading');
                showResult('pmiResult', error.message, 'error');
            }
        }

        // 获取PMI统计
        async function getPmiStats() {
            showLoading('pmiLoading');
            try {
                const data = await apiCall(`${API_BASE}/pmi-reports/stats`);
                hideLoading('pmiLoading');
                showResult('pmiResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                hideLoading('pmiLoading');
                showResult('pmiResult', error.message, 'error');
            }
        }

        // 页面加载时自动获取状态
        window.onload = function() {
            getMigrationStatus();
        };
    </script>
</body>
</html>
