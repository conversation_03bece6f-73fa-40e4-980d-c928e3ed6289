<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议报告功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #40a9ff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
        }
    </style>
</head>
<body>
    <h1>会议报告功能测试</h1>
    
    <div class="test-section">
        <h3>1. 测试获取会议报告（通过系统UUID）</h3>
        <p>系统内部UUID: 54c809e2-7e4e-49f7-80fc-ad2ed60c1a94</p>
        <button onclick="testGetReportByUuid()">获取会议报告</button>
        <div id="getReportResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试触发报告拉取（通过系统UUID）</h3>
        <p>系统内部UUID: 54c809e2-7e4e-49f7-80fc-ad2ed60c1a94</p>
        <button onclick="testTriggerReportFetch()">触发报告拉取</button>
        <div id="triggerReportResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试异步拉取功能模拟</h3>
        <p>模拟弹窗打开时的行为：先获取现有报告，然后异步触发拉取</p>
        <button onclick="testAsyncFetch()">模拟异步拉取</button>
        <div id="asyncFetchResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        const TEST_UUID = '54c809e2-7e4e-49f7-80fc-ad2ed60c1a94';

        // 测试获取会议报告
        async function testGetReportByUuid() {
            const resultDiv = document.getElementById('getReportResult');
            resultDiv.textContent = '正在获取会议报告...';
            
            try {
                const response = await fetch(`${API_BASE}/meeting-reports/uuid/${TEST_UUID}`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功获取会议报告！
报告ID: ${data.id}
会议主题: ${data.topic}
开始时间: ${data.startTime}
时长: ${data.durationMinutes}分钟
参会人数: ${data.participantCount || 0}人`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败: ${data.message || response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 测试触发报告拉取
        async function testTriggerReportFetch() {
            const resultDiv = document.getElementById('triggerReportResult');
            resultDiv.textContent = '正在触发报告拉取...';
            
            try {
                const response = await fetch(`${API_BASE}/meeting-reports/fetch/meeting/${TEST_UUID}`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功触发报告拉取！
消息: ${data.message}
报告ID: ${data.data?.id || '未知'}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 触发失败: ${data.message || response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 测试异步拉取功能
        async function testAsyncFetch() {
            const resultDiv = document.getElementById('asyncFetchResult');
            resultDiv.textContent = '开始模拟异步拉取流程...\n';
            
            try {
                // 步骤1: 获取现有报告
                resultDiv.textContent += '1. 获取现有报告...\n';
                const getResponse = await fetch(`${API_BASE}/meeting-reports/uuid/${TEST_UUID}`);
                const currentReport = await getResponse.json();
                
                if (getResponse.ok) {
                    resultDiv.textContent += `   ✅ 找到现有报告 (ID: ${currentReport.id})\n`;
                    const originalReportId = currentReport.id;
                    
                    // 步骤2: 异步触发拉取
                    resultDiv.textContent += '2. 异步触发报告拉取...\n';
                    const triggerResponse = await fetch(`${API_BASE}/meeting-reports/fetch/meeting/${TEST_UUID}`, {
                        method: 'POST'
                    });
                    const triggerData = await triggerResponse.json();
                    
                    if (triggerResponse.ok && triggerData.success) {
                        resultDiv.textContent += '   ✅ 成功触发拉取\n';
                        
                        // 步骤3: 轮询检查更新
                        resultDiv.textContent += '3. 轮询检查更新...\n';
                        let retryCount = 0;
                        const maxRetries = 8;
                        
                        const pollForUpdate = async () => {
                            try {
                                const checkResponse = await fetch(`${API_BASE}/meeting-reports/uuid/${TEST_UUID}`);
                                const updatedReport = await checkResponse.json();
                                
                                if (checkResponse.ok && updatedReport.id !== originalReportId) {
                                    resultDiv.className = 'result success';
                                    resultDiv.textContent += `   🎉 检测到新报告！
   原报告ID: ${originalReportId}
   新报告ID: ${updatedReport.id}
   更新时间: ${new Date().toLocaleTimeString()}`;
                                    return;
                                }
                                
                                retryCount++;
                                if (retryCount < maxRetries) {
                                    resultDiv.textContent += `   ⏳ 第${retryCount}次检查，暂无更新...\n`;
                                    setTimeout(pollForUpdate, 3000);
                                } else {
                                    resultDiv.className = 'result';
                                    resultDiv.textContent += '   ⏰ 轮询结束，未检测到更新';
                                }
                            } catch (error) {
                                resultDiv.textContent += `   ❌ 轮询出错: ${error.message}\n`;
                            }
                        };
                        
                        setTimeout(pollForUpdate, 2000);
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent += `   ❌ 触发拉取失败: ${triggerData.message}`;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent += `   ❌ 获取现有报告失败: ${currentReport.message || getResponse.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent += `❌ 流程失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
