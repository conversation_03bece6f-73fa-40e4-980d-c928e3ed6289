<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试多会议报告功能</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1890ff;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        .result {
            margin-top: 15px;
            padding: 12px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .result.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .result.info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .report-item {
            background: #fafafa;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 12px;
            margin: 8px 0;
        }
        .report-item h4 {
            margin: 0 0 8px 0;
            color: #262626;
        }
        .report-meta {
            font-size: 12px;
            color: #8c8c8c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 多会议报告功能测试</h1>
        <p>测试一个会议ID对应多条会议报告记录的功能</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>📊 测试获取单条会议报告（原API）</h3>
            <p>测试原有的API是否仍然正常工作</p>
            <button onclick="testGetSingleReport()">获取单条报告</button>
            <div id="singleReportResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试获取所有会议报告（新API）</h3>
            <p>测试新的API是否能返回多条会议报告记录</p>
            <button onclick="testGetAllReports()">获取所有报告</button>
            <div id="allReportsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 测试通过会议ID获取所有报告</h3>
            <p>测试通过会议ID获取多条报告的功能</p>
            <button onclick="testGetAllReportsByMeetingId()">通过会议ID获取所有报告</button>
            <div id="meetingIdReportsResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        const TEST_UUID = '1a0d3ce3-512a-4526-a4ed-6ee727e8a60f'; // 系统会议UUID
        const TEST_MEETING_ID = '88679475686'; // Zoom会议ID

        // 测试获取单条会议报告
        async function testGetSingleReport() {
            const resultDiv = document.getElementById('singleReportResult');
            resultDiv.textContent = '正在获取单条会议报告...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch(`${API_BASE}/meeting-reports/uuid/${TEST_UUID}`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功获取单条会议报告！
报告ID: ${data.id}
会议主题: ${data.topic}
开始时间: ${data.startTime}
时长: ${data.durationMinutes}分钟
参会人数: ${data.participantCount || 0}人
Zoom UUID: ${data.zoomMeetingUuid}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败: ${data.message || response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 测试获取所有会议报告
        async function testGetAllReports() {
            const resultDiv = document.getElementById('allReportsResult');
            resultDiv.textContent = '正在获取所有会议报告...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch(`${API_BASE}/meeting-reports/uuid/${TEST_UUID}/all`);
                const data = await response.json();
                
                if (response.ok && Array.isArray(data)) {
                    resultDiv.className = 'result success';
                    let resultText = `✅ 成功获取 ${data.length} 条会议报告！\n\n`;
                    
                    data.forEach((report, index) => {
                        resultText += `📋 报告 ${index + 1}:\n`;
                        resultText += `  ID: ${report.id}\n`;
                        resultText += `  主题: ${report.topic}\n`;
                        resultText += `  开始时间: ${report.startTime}\n`;
                        resultText += `  时长: ${report.durationMinutes}分钟\n`;
                        resultText += `  参会人数: ${report.participantCount || 0}人\n`;
                        resultText += `  Zoom UUID: ${report.zoomMeetingUuid}\n\n`;
                    });
                    
                    resultDiv.textContent = resultText;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败: ${data.message || response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 测试通过会议ID获取所有报告
        async function testGetAllReportsByMeetingId() {
            const resultDiv = document.getElementById('meetingIdReportsResult');
            resultDiv.textContent = '正在通过会议ID获取所有报告...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch(`${API_BASE}/meeting-reports/meeting-id/${TEST_MEETING_ID}/all`);
                const data = await response.json();
                
                if (response.ok && Array.isArray(data)) {
                    resultDiv.className = 'result success';
                    let resultText = `✅ 通过会议ID成功获取 ${data.length} 条会议报告！\n\n`;
                    
                    data.forEach((report, index) => {
                        resultText += `📋 报告 ${index + 1}:\n`;
                        resultText += `  ID: ${report.id}\n`;
                        resultText += `  主题: ${report.topic}\n`;
                        resultText += `  开始时间: ${report.startTime}\n`;
                        resultText += `  时长: ${report.durationMinutes}分钟\n`;
                        resultText += `  参会人数: ${report.participantCount || 0}人\n`;
                        resultText += `  Zoom UUID: ${report.zoomMeetingUuid}\n`;
                        resultText += `  会议ID: ${report.zoomMeetingId}\n\n`;
                    });
                    
                    resultDiv.textContent = resultText;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败: ${data.message || response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 页面加载完成后自动运行测试
        window.onload = function() {
            console.log('页面加载完成，可以开始测试多会议报告功能');
        };
    </script>
</body>
</html>
