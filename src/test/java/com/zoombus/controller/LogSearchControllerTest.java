package com.zoombus.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.dto.PageResult;
import com.zoombus.dto.log.LogEntry;
import com.zoombus.dto.log.LogSearchRequest;
import com.zoombus.service.LogSearchService;
import com.zoombus.trace.TraceContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 日志搜索控制器测试
 */
@WebMvcTest(LogSearchController.class)
class LogSearchControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private LogSearchService logSearchService;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        TraceContext.setTraceId("20250823143025-001-000001-a1b2c3");
    }

    @AfterEach
    void tearDown() {
        TraceContext.clear();
    }

    @Test
    void testSearchLogsSuccess() throws Exception {
        // 准备测试数据
        LogSearchRequest request = new LogSearchRequest();
        request.setTraceId("20250823143025-001-000001-a1b2c3");
        request.setPage(1);
        request.setSize(50);

        LogEntry logEntry = LogEntry.builder()
                .timestamp("2025-08-23 14:30:25.123")
                .level("INFO")
                .traceId("20250823143025-001-000001-a1b2c3")
                .logger("com.zoombus.service.TestService")
                .message("Test log message")
                .thread("main")
                .source("APPLICATION")
                .rawLine("2025-08-23 14:30:25.123 [main] INFO [20250823143025-001-000001-a1b2c3] com.zoombus.service.TestService - Test log message")
                .build();

        PageResult<LogEntry> pageResult = new PageResult<>(
                Collections.singletonList(logEntry), 1, 1, 1, 50);

        when(logSearchService.searchLogs(any(LogSearchRequest.class))).thenReturn(pageResult);

        // 执行测试
        mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].traceId").value("20250823143025-001-000001-a1b2c3"))
                .andExpect(jsonPath("$.data[0].level").value("INFO"))
                .andExpect(jsonPath("$.data[0].message").value("Test log message"))
                .andExpect(jsonPath("$.total").value(1))
                .andExpect(jsonPath("$.page").value(1))
                .andExpect(jsonPath("$.size").value(50))
                .andExpect(jsonPath("$.traceId").value("20250823143025-001-000001-a1b2c3"));
    }

    @Test
    void testSearchLogsInvalidRequest() throws Exception {
        // 准备无效请求
        LogSearchRequest request = new LogSearchRequest();
        // 不设置任何搜索条件

        // 执行测试
        mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("请求参数无效，至少需要一个搜索条件"));
    }

    @Test
    void testSearchLogsException() throws Exception {
        // 准备测试数据
        LogSearchRequest request = new LogSearchRequest();
        request.setTraceId("20250823143025-001-000001-a1b2c3");

        when(logSearchService.searchLogs(any(LogSearchRequest.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // 执行测试
        mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("日志检索失败: Database connection failed"));
    }

    @Test
    void testGetTraceChainSuccess() throws Exception {
        String targetTraceId = "20250823143025-001-000002-b2c3d4";

        // 准备测试数据
        List<LogEntry> traceChain = Arrays.asList(
                LogEntry.builder()
                        .timestamp("2025-08-23 14:30:25.123")
                        .level("INFO")
                        .traceId(targetTraceId)
                        .logger("com.zoombus.controller.TestController")
                        .message("Request received")
                        .build(),
                LogEntry.builder()
                        .timestamp("2025-08-23 14:30:25.456")
                        .level("INFO")
                        .traceId(targetTraceId)
                        .logger("com.zoombus.service.TestService")
                        .message("Processing request")
                        .build(),
                LogEntry.builder()
                        .timestamp("2025-08-23 14:30:25.789")
                        .level("INFO")
                        .traceId(targetTraceId)
                        .logger("com.zoombus.controller.TestController")
                        .message("Request completed")
                        .build()
        );

        when(logSearchService.getCompleteTraceChain(targetTraceId)).thenReturn(traceChain);

        // 执行测试
        mockMvc.perform(get("/api/logs/trace/{targetTraceId}", targetTraceId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(3))
                .andExpect(jsonPath("$[0].traceId").value(targetTraceId))
                .andExpect(jsonPath("$[0].message").value("Request received"))
                .andExpect(jsonPath("$[1].message").value("Processing request"))
                .andExpect(jsonPath("$[2].message").value("Request completed"));
    }

    @Test
    void testGetTraceChainException() throws Exception {
        String targetTraceId = "20250823143025-001-000002-b2c3d4";

        when(logSearchService.getCompleteTraceChain(targetTraceId))
                .thenThrow(new RuntimeException("File not found"));

        // 执行测试
        mockMvc.perform(get("/api/logs/trace/{targetTraceId}", targetTraceId))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void testExportLogsSuccess() throws Exception {
        String targetTraceId = "20250823143025-001-000001-a1b2c3";
        String format = "txt";

        // 准备测试数据
        String logContent = "# 日志导出报告\n" +
                "# TraceId: " + targetTraceId + "\n" +
                "# 导出时间: 2025-08-23 14:30:25\n" +
                "# 总记录数: 2\n" +
                "# ================================================================================\n\n" +
                "2025-08-23 14:30:25.123 [main] INFO [" + targetTraceId + "] com.zoombus.controller.TestController - Request received\n" +
                "2025-08-23 14:30:25.456 [main] INFO [" + targetTraceId + "] com.zoombus.service.TestService - Request processed\n";

        ByteArrayResource resource = new ByteArrayResource(logContent.getBytes());

        when(logSearchService.exportLogs(targetTraceId, format)).thenReturn(resource);

        // 执行测试
        mockMvc.perform(get("/api/logs/export")
                .param("traceId", targetTraceId)
                .param("format", format))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Disposition", 
                    org.hamcrest.Matchers.containsString("attachment; filename=\"logs_" + targetTraceId)))
                .andExpect(header().string("Content-Type", "text/plain"))
                .andExpect(content().string(logContent));
    }

    @Test
    void testExportLogsDefaultFormat() throws Exception {
        String targetTraceId = "20250823143025-001-000001-a1b2c3";

        ByteArrayResource resource = new ByteArrayResource("test content".getBytes());

        when(logSearchService.exportLogs(targetTraceId, "txt")).thenReturn(resource);

        // 执行测试（不指定format参数，应该使用默认值txt）
        mockMvc.perform(get("/api/logs/export")
                .param("traceId", targetTraceId))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "text/plain"));
    }

    @Test
    void testExportLogsException() throws Exception {
        String targetTraceId = "20250823143025-001-000001-a1b2c3";

        when(logSearchService.exportLogs(anyString(), anyString()))
                .thenThrow(new RuntimeException("Export failed"));

        // 执行测试
        mockMvc.perform(get("/api/logs/export")
                .param("traceId", targetTraceId))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void testGetLogStats() throws Exception {
        // 执行测试
        mockMvc.perform(get("/api/logs/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalFiles").value(0))
                .andExpect(jsonPath("$.totalSize").value(0))
                .andExpect(jsonPath("$.availableSources").isArray())
                .andExpect(jsonPath("$.availableSources[0]").value("APPLICATION"))
                .andExpect(jsonPath("$.availableSources[1]").value("WEBHOOK"))
                .andExpect(jsonPath("$.availableSources[2]").value("SCHEDULER"))
                .andExpect(jsonPath("$.availableSources[3]").value("API"))
                .andExpect(jsonPath("$.availableLevels").isArray())
                .andExpect(jsonPath("$.availableLevels[0]").value("ERROR"))
                .andExpect(jsonPath("$.availableLevels[1]").value("WARN"))
                .andExpect(jsonPath("$.availableLevels[2]").value("INFO"))
                .andExpect(jsonPath("$.availableLevels[3]").value("DEBUG"));
    }

    @Test
    void testHealthCheck() throws Exception {
        // 执行测试
        mockMvc.perform(get("/api/logs/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("UP"))
                .andExpect(jsonPath("$.service").value("LogSearchService"))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testSearchLogsWithTimeRange() throws Exception {
        // 准备测试数据
        LogSearchRequest request = new LogSearchRequest();
        request.setTimeRange(new String[]{"2025-08-23 14:00:00", "2025-08-23 15:00:00"});
        request.setPage(1);
        request.setSize(50);

        PageResult<LogEntry> pageResult = new PageResult<>(
                Collections.emptyList(), 0, 0, 1, 50);

        when(logSearchService.searchLogs(any(LogSearchRequest.class))).thenReturn(pageResult);

        // 执行测试
        mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.total").value(0));
    }

    @Test
    void testSearchLogsWithLogLevel() throws Exception {
        // 准备测试数据
        LogSearchRequest request = new LogSearchRequest();
        request.setLogLevel("ERROR");
        request.setPage(1);
        request.setSize(50);

        PageResult<LogEntry> pageResult = new PageResult<>(
                Collections.emptyList(), 0, 0, 1, 50);

        when(logSearchService.searchLogs(any(LogSearchRequest.class))).thenReturn(pageResult);

        // 执行测试
        mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    void testSearchLogsWithSource() throws Exception {
        // 准备测试数据
        LogSearchRequest request = new LogSearchRequest();
        request.setSource("WEBHOOK");
        request.setPage(1);
        request.setSize(50);

        PageResult<LogEntry> pageResult = new PageResult<>(
                Collections.emptyList(), 0, 0, 1, 50);

        when(logSearchService.searchLogs(any(LogSearchRequest.class))).thenReturn(pageResult);

        // 执行测试
        mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    void testSearchLogsWithKeyword() throws Exception {
        // 准备测试数据
        LogSearchRequest request = new LogSearchRequest();
        request.setKeyword("meeting");
        request.setPage(1);
        request.setSize(50);

        PageResult<LogEntry> pageResult = new PageResult<>(
                Collections.emptyList(), 0, 0, 1, 50);

        when(logSearchService.searchLogs(any(LogSearchRequest.class))).thenReturn(pageResult);

        // 执行测试
        mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
}
