package com.zoombus.entity;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * Meeting实体验证测试 - 简单的main方法测试
 */
public class MeetingValidationTest {

    public static void main(String[] args) {
        MeetingValidationTest test = new MeetingValidationTest();
        test.runTests();
    }

    public void runTests() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();

        System.out.println("开始测试Meeting实体验证...");

        // 测试1：创建阶段可以有空的zoomMeetingId
        testMeetingCreationWithNullZoomMeetingId(validator);

        // 测试2：验证必填字段
        testRequiredFields(validator);

        System.out.println("所有测试完成！");
    }

    private void testMeetingCreationWithNullZoomMeetingId(Validator validator) {
        System.out.println("测试1：创建阶段可以有空的zoomMeetingId");

        Meeting meeting = new Meeting();
        meeting.setTopic("测试会议");
        meeting.setStartTime(LocalDateTime.now().plusDays(1));
        meeting.setStatus(Meeting.MeetingStatus.CREATING);
        meeting.setType(Meeting.MeetingType.SCHEDULED);
        meeting.setCreationSource(Meeting.CreationSource.ADMIN_PANEL);
        meeting.setZoomMeetingId(null); // 创建阶段可以为空

        Set<ConstraintViolation<Meeting>> violations = validator.validate(meeting);
        if (violations.isEmpty()) {
            System.out.println("✓ 测试通过：创建阶段zoomMeetingId可以为空");
        } else {
            System.out.println("✗ 测试失败：创建阶段zoomMeetingId应该可以为空");
            for (ConstraintViolation<Meeting> violation : violations) {
                System.out.println("  - " + violation.getMessage());
            }
        }
    }

    private void testRequiredFields(Validator validator) {
        System.out.println("\n测试2：验证必填字段");

        // 测试空主题
        Meeting meeting = new Meeting();
        meeting.setTopic(""); // 空主题
        meeting.setStartTime(LocalDateTime.now().plusDays(1));
        meeting.setStatus(Meeting.MeetingStatus.CREATING);
        meeting.setType(Meeting.MeetingType.SCHEDULED);
        meeting.setCreationSource(Meeting.CreationSource.ADMIN_PANEL);

        Set<ConstraintViolation<Meeting>> violations = validator.validate(meeting);
        boolean hasTopicViolation = false;
        for (ConstraintViolation<Meeting> violation : violations) {
            if (violation.getMessage().contains("会议主题不能为空")) {
                hasTopicViolation = true;
                break;
            }
        }

        if (hasTopicViolation) {
            System.out.println("✓ 测试通过：空主题被正确验证");
        } else {
            System.out.println("✗ 测试失败：空主题应该被验证");
        }

        // 测试空开始时间
        meeting = new Meeting();
        meeting.setTopic("测试会议");
        meeting.setStartTime(null); // 空开始时间
        meeting.setStatus(Meeting.MeetingStatus.CREATING);
        meeting.setType(Meeting.MeetingType.SCHEDULED);
        meeting.setCreationSource(Meeting.CreationSource.ADMIN_PANEL);

        violations = validator.validate(meeting);
        boolean hasStartTimeViolation = false;
        for (ConstraintViolation<Meeting> violation : violations) {
            if (violation.getMessage().contains("开始时间不能为空")) {
                hasStartTimeViolation = true;
                break;
            }
        }

        if (hasStartTimeViolation) {
            System.out.println("✓ 测试通过：空开始时间被正确验证");
        } else {
            System.out.println("✗ 测试失败：空开始时间应该被验证");
        }
    }
}
