package com.zoombus.integration;

import com.zoombus.entity.JoinAccountRentalToken;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.JoinAccountRentalTokenRepository;
import com.zoombus.repository.ZoomUserRepository;
import com.zoombus.service.JoinAccountRentalTokenService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Join Account Rental功能集成测试
 */
@SpringBootTest
@Transactional
public class JoinAccountRentalIntegrationTest {

    @Autowired
    private JoinAccountRentalTokenService tokenService;

    @Autowired
    private JoinAccountRentalTokenRepository tokenRepository;

    @Autowired
    private ZoomUserRepository zoomUserRepository;

    private ZoomUser testZoomUser;
    private JoinAccountRentalToken testToken;

    @BeforeEach
    void setUp() {
        // 创建测试用的Zoom用户
        testZoomUser = new ZoomUser();
        testZoomUser.setZoomUserId("test_zoom_user_id");
        testZoomUser.setEmail("<EMAIL>");
        testZoomUser.setUserType(ZoomUser.UserType.LICENSED);
        testZoomUser.setStatus(ZoomUser.UserStatus.ACTIVE);
        testZoomUser.setAccountUsage(ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL);
        testZoomUser.setTotalUsageCount(0);
        testZoomUser.setTotalUsageMinutes(0);
        testZoomUser = zoomUserRepository.save(testZoomUser);

        // 创建测试Token
        testToken = new JoinAccountRentalToken();
        testToken.setTokenNumber("TEST001");
        testToken.setBatchNumber("BATCH001");
        testToken.setUsageDays(3);
        testToken.setStatus(JoinAccountRentalToken.TokenStatus.PENDING);
        testToken.setExportStatus(JoinAccountRentalToken.ExportStatus.NOT_EXPORTED);
        testToken = tokenRepository.save(testToken);
    }

    @Test
    void testTokenService() {
        // 测试Token服务基本功能

        // 1. 测试获取Token
        var tokenOpt = tokenService.getTokenByNumber("TEST001");
        assertTrue(tokenOpt.isPresent());
        assertEquals("TEST001", tokenOpt.get().getTokenNumber());

        // 2. 测试检查Token是否可预约
        boolean canReserve = tokenService.canReserveToken("TEST001");
        assertTrue(canReserve);

        // 3. 测试批量生成Token
        var generatedTokens = tokenService.batchGenerateTokens(3, 2, "测试批次", "测试备注");
        assertEquals(2, generatedTokens.size());

        // 4. 测试获取统计信息
        var statistics = tokenService.getTokenStatistics();
        assertNotNull(statistics);
        assertTrue(statistics.containsKey("totalCount"));
    }

    @Test
    void testTokenWorkflow() {
        // 测试完整的Token工作流程

        // 1. 检查Token是否可预约
        boolean canReserve = tokenService.canReserveToken(testToken.getTokenNumber());
        assertTrue(canReserve);

        // 2. 预约Token
        LocalDateTime startTime = LocalDateTime.now().plusDays(1);
        LocalDateTime endTime = startTime.plusDays(2);

        JoinAccountRentalToken reservedToken = tokenService.reserveToken(
                testToken.getTokenNumber(),
                testZoomUser.getId(),
                testZoomUser.getEmail(),
                startTime,
                "test_password",
                "TEST_USER"
        );

        assertNotNull(reservedToken);
        assertEquals(JoinAccountRentalToken.TokenStatus.RESERVED, reservedToken.getStatus());
        assertEquals(testZoomUser.getId(), reservedToken.getAssignedZoomUserId());

        // 3. 激活Token
        JoinAccountRentalToken activatedToken = tokenService.activateToken(
                testToken.getTokenNumber(),
                "test_password"
        );

        assertNotNull(activatedToken);
        assertEquals(JoinAccountRentalToken.TokenStatus.ACTIVE, activatedToken.getStatus());

        // 4. 完成Token
        JoinAccountRentalToken completedToken = tokenService.completeToken(testToken.getTokenNumber());

        assertNotNull(completedToken);
        assertEquals(JoinAccountRentalToken.TokenStatus.COMPLETED, completedToken.getStatus());
    }
}
