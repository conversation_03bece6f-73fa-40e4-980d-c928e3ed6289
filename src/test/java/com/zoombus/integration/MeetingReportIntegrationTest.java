package com.zoombus.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.MeetingReport;
import com.zoombus.entity.MeetingParticipant;
import com.zoombus.entity.MeetingReportTask;
import com.zoombus.repository.MeetingReportRepository;
import com.zoombus.repository.MeetingParticipantRepository;
import com.zoombus.repository.MeetingReportTaskRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.service.MeetingReportService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 会议报告功能集成测试
 */
@SpringBootTest(classes = {
    MeetingReportService.class,
    MeetingReportRepository.class,
    MeetingParticipantRepository.class,
    MeetingReportTaskRepository.class,
    ZoomMeetingRepository.class
})
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.ANY)
@Transactional
public class MeetingReportIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private MeetingReportService meetingReportService;

    @Autowired
    private MeetingReportRepository meetingReportRepository;

    @Autowired
    private MeetingParticipantRepository meetingParticipantRepository;

    @Autowired
    private MeetingReportTaskRepository meetingReportTaskRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 清理测试数据
        meetingParticipantRepository.deleteAll();
        meetingReportRepository.deleteAll();
        meetingReportTaskRepository.deleteAll();
    }

    @Test
    void testCreateMeetingReport() {
        // Given
        String zoomMeetingUuid = "test-uuid-123";
        String zoomMeetingId = "123456789";
        String topic = "测试会议";

        // When
        MeetingReport report = new MeetingReport();
        report.setZoomMeetingUuid(zoomMeetingUuid);
        report.setZoomMeetingId(zoomMeetingId);
        report.setTopic(topic);
        report.setStartTime(LocalDateTime.now().minusHours(1));
        report.setEndTime(LocalDateTime.now());
        report.setDurationMinutes(60);
        report.setTotalParticipants(5);
        report.setUniqueParticipants(4);
        report.setHasRecording(true);
        report.setHasVideo(true);
        report.setHasVoip(true);
        report.setHasScreenShare(false);
        report.setFetchStatus(MeetingReport.FetchStatus.SUCCESS);
        report.setCreatedAt(LocalDateTime.now());
        report.setUpdatedAt(LocalDateTime.now());

        MeetingReport savedReport = meetingReportRepository.save(report);

        // Then
        assertNotNull(savedReport.getId());
        assertEquals(zoomMeetingUuid, savedReport.getZoomMeetingUuid());
        assertEquals(zoomMeetingId, savedReport.getZoomMeetingId());
        assertEquals(topic, savedReport.getTopic());
        assertEquals(MeetingReport.FetchStatus.SUCCESS, savedReport.getFetchStatus());
    }

    @Test
    void testCreateMeetingParticipants() {
        // Given
        MeetingReport report = createTestMeetingReport();
        
        // When
        MeetingParticipant participant1 = new MeetingParticipant();
        participant1.setMeetingReportId(report.getId());
        participant1.setParticipantUuid("participant-1");
        participant1.setParticipantName("张三");
        participant1.setParticipantEmail("<EMAIL>");
        participant1.setUserType(MeetingParticipant.UserType.HOST);
        participant1.setJoinTime(LocalDateTime.now().minusHours(1));
        participant1.setLeaveTime(LocalDateTime.now());
        participant1.setDurationMinutes(60);
        participant1.setCreatedAt(LocalDateTime.now());

        MeetingParticipant participant2 = new MeetingParticipant();
        participant2.setMeetingReportId(report.getId());
        participant2.setParticipantUuid("participant-2");
        participant2.setParticipantName("李四");
        participant2.setParticipantEmail("<EMAIL>");
        participant2.setUserType(MeetingParticipant.UserType.ATTENDEE);
        participant2.setJoinTime(LocalDateTime.now().minusMinutes(50));
        participant2.setLeaveTime(LocalDateTime.now());
        participant2.setDurationMinutes(50);
        participant2.setCreatedAt(LocalDateTime.now());

        List<MeetingParticipant> savedParticipants = meetingParticipantRepository.saveAll(
                List.of(participant1, participant2));

        // Then
        assertEquals(2, savedParticipants.size());
        
        List<MeetingParticipant> foundParticipants = meetingParticipantRepository
                .findByMeetingReportId(report.getId());
        assertEquals(2, foundParticipants.size());
    }

    @Test
    void testMeetingReportService() {
        // Given
        MeetingReport report = createTestMeetingReport();
        
        // When
        Optional<MeetingReport> foundByUuid = meetingReportService
                .getReportByUuid(report.getZoomMeetingUuid());
        Optional<MeetingReport> foundByMeetingId = meetingReportService
                .getReportByMeetingId(report.getZoomMeetingId());
        
        // Then
        assertTrue(foundByUuid.isPresent());
        assertTrue(foundByMeetingId.isPresent());
        assertEquals(report.getId(), foundByUuid.get().getId());
        assertEquals(report.getId(), foundByMeetingId.get().getId());
    }

    @Test
    void testGetReportsWithFilters() {
        // Given
        createTestMeetingReport();
        createTestMeetingReport("test-uuid-456", "987654321", "另一个测试会议");
        
        // When
        Page<MeetingReport> allReports = meetingReportService.getReportsWithFilters(
                null, null, null, null, null, null, PageRequest.of(0, 10));
        
        Page<MeetingReport> filteredReports = meetingReportService.getReportsWithFilters(
                null, null, null, null, "测试", null, PageRequest.of(0, 10));
        
        // Then
        assertEquals(2, allReports.getTotalElements());
        assertEquals(2, filteredReports.getTotalElements()); // 两个都包含"测试"
    }

    @Test
    void testCreateReportTask() {
        // Given
        String zoomMeetingUuid = "task-test-uuid";
        String zoomMeetingId = "task-test-id";
        
        // When
        MeetingReportTask task = meetingReportService.createReportTask(
                zoomMeetingUuid, zoomMeetingId);

        // Then
        assertNotNull(task.getId());
        assertEquals(zoomMeetingUuid, task.getZoomMeetingUuid());
        assertEquals(zoomMeetingId, task.getZoomMeetingId());
        assertEquals(MeetingReportTask.TaskStatus.PENDING, task.getTaskStatus());
        assertEquals(3, task.getMaxRetryCount());
        assertEquals(0, task.getRetryCount());
    }

    @Test
    void testGetReportStatistics() {
        // Given
        createTestMeetingReport();
        createTestMeetingReport("test-uuid-456", "987654321", "另一个测试会议");
        
        // When
        Map<String, Object> statistics = meetingReportService.getReportStatistics(null, null);
        
        // Then
        assertNotNull(statistics);
        assertEquals(2L, statistics.get("totalReports"));
        assertTrue((Long) statistics.get("totalParticipants") >= 0);
        assertTrue((Long) statistics.get("totalDuration") >= 0);
    }

    @Test
    void testMeetingReportController() throws Exception {
        // Given
        MeetingReport report = createTestMeetingReport();
        
        // Test get report by UUID
        mockMvc.perform(get("/api/meeting-reports/uuid/{uuid}", report.getZoomMeetingUuid()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.zoomMeetingUuid").value(report.getZoomMeetingUuid()));
        
        // Test get report by meeting ID
        mockMvc.perform(get("/api/meeting-reports/meeting-id/{meetingId}", report.getZoomMeetingId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.zoomMeetingId").value(report.getZoomMeetingId()));
        
        // Test get reports list
        mockMvc.perform(get("/api/meeting-reports")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
        
        // Test get statistics
        mockMvc.perform(get("/api/meeting-reports/statistics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalReports").exists());
    }

    @Test
    void testReportNotFound() throws Exception {
        // Test get non-existent report
        mockMvc.perform(get("/api/meeting-reports/uuid/non-existent-uuid"))
                .andExpect(status().isNotFound());
        
        mockMvc.perform(get("/api/meeting-reports/meeting-id/non-existent-id"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testCreateDelayedReportTask() {
        // Given
        String zoomMeetingUuid = "delayed-task-uuid";
        String zoomMeetingId = "delayed-task-id";
        LocalDateTime scheduledTime = LocalDateTime.now().plusMinutes(5);
        
        // When
        MeetingReportTask task = meetingReportService.createDelayedReportTask(
                zoomMeetingUuid, zoomMeetingId, scheduledTime, 3);
        
        // Then
        assertNotNull(task.getId());
        assertEquals(zoomMeetingUuid, task.getZoomMeetingUuid());
        assertEquals(scheduledTime, task.getScheduledTime());
        assertEquals(MeetingReportTask.TaskStatus.PENDING, task.getTaskStatus());
    }

    // Helper methods
    private MeetingReport createTestMeetingReport() {
        return createTestMeetingReport("test-uuid-123", "123456789", "测试会议");
    }

    private MeetingReport createTestMeetingReport(String uuid, String meetingId, String topic) {
        MeetingReport report = new MeetingReport();
        report.setZoomMeetingUuid(uuid);
        report.setZoomMeetingId(meetingId);
        report.setTopic(topic);
        report.setStartTime(LocalDateTime.now().minusHours(1));
        report.setEndTime(LocalDateTime.now());
        report.setDurationMinutes(60);
        report.setTotalParticipants(5);
        report.setUniqueParticipants(4);
        report.setHasRecording(true);
        report.setHasVideo(true);
        report.setHasVoip(true);
        report.setHasScreenShare(false);
        report.setFetchStatus(MeetingReport.FetchStatus.SUCCESS);
        report.setCreatedAt(LocalDateTime.now());
        report.setUpdatedAt(LocalDateTime.now());
        
        return meetingReportRepository.save(report);
    }
}
