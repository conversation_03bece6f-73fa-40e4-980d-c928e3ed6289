package com.zoombus.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.service.PmiBillingModeService;
import com.zoombus.service.PmiRechargeService;
import com.zoombus.service.ZoomMeetingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.ANY)
@Transactional
class PmiBillingIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private PmiRecordRepository pmiRecordRepository;

    @Autowired
    private ZoomMeetingRepository zoomMeetingRepository;

    @Autowired
    private PmiBillingModeService pmiBillingModeService;

    @Autowired
    private PmiRechargeService pmiRechargeService;

    @Autowired
    private ZoomMeetingService zoomMeetingService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;
    private PmiRecord testPmiRecord;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 创建测试PMI记录
        testPmiRecord = new PmiRecord();
        testPmiRecord.setUserId(1L);
        testPmiRecord.setPmiNumber("123456789");
        testPmiRecord.setBillingMode(PmiRecord.BillingMode.BY_TIME);
        testPmiRecord.setBillingStatus(PmiRecord.BillingStatus.ACTIVE);
        testPmiRecord.setTotalMinutes(100);
        testPmiRecord.setAvailableMinutes(50);
        testPmiRecord.setOverdraftMinutes(0);
        testPmiRecord.setPendingDeductMinutes(0);
        testPmiRecord.setTotalUsedMinutes(0);
        testPmiRecord = pmiRecordRepository.save(testPmiRecord);
    }

    @Test
    void testPmiRechargeWorkflow() throws Exception {
        // 1. 获取充值预览
        mockMvc.perform(get("/api/pmi/{pmiRecordId}/recharge/preview", testPmiRecord.getId())
                .param("minutes", "50"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.rechargeMinutes").value(50))
                .andExpect(jsonPath("$.data.canStartMeeting").value(true));

        // 2. 执行充值
        mockMvc.perform(post("/api/pmi/{pmiRecordId}/recharge", testPmiRecord.getId())
                .param("minutes", "50")
                .param("description", "测试充值"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("充值成功"));

        // 3. 验证PMI记录更新
        PmiRecord updatedRecord = pmiRecordRepository.findById(testPmiRecord.getId()).orElse(null);
        assertNotNull(updatedRecord);
        assertEquals(100, updatedRecord.getAvailableMinutes()); // 50 + 50
        assertEquals(150, updatedRecord.getTotalMinutes()); // 100 + 50
    }

    @Test
    void testPmiRechargeWithOverdraft() {
        // 设置超额时长
        testPmiRecord.setOverdraftMinutes(30);
        testPmiRecord.setAvailableMinutes(0);
        pmiRecordRepository.save(testPmiRecord);

        // 执行充值
        PmiRechargeService.RechargeResult result = pmiRechargeService.rechargePmi(
            testPmiRecord.getId(), 50, "测试充值");

        assertTrue(result.isSuccess());

        // 验证分配策略
        PmiRechargeService.RechargeAllocation allocation = result.getAllocation();
        assertEquals(30, allocation.getOverdraftSettled()); // 结清超额
        assertEquals(20, allocation.getActualAdded()); // 实际增加
        assertEquals(0, allocation.getFinalOverdraft()); // 超额清零
        assertEquals(20, allocation.getFinalAvailable()); // 可用时长
    }

    @Test
    void testBillingModeSwitch() throws Exception {
        // 1. 切换到按时段计费
        LocalDateTime expireTime = LocalDateTime.now().plusHours(2);
        mockMvc.perform(post("/api/pmi/{pmiRecordId}/billing/switch-to-long", testPmiRecord.getId())
                .param("windowId", "123")
                .param("expireTime", expireTime.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证模式切换
        PmiRecord updatedRecord = pmiRecordRepository.findById(testPmiRecord.getId()).orElse(null);
        assertNotNull(updatedRecord);
        assertEquals(PmiRecord.BillingMode.LONG, updatedRecord.getBillingMode());
        assertEquals(123L, updatedRecord.getCurrentWindowId());

        // 2. 切换回按时长计费
        mockMvc.perform(post("/api/pmi/{pmiRecordId}/billing/switch-to-time", testPmiRecord.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证模式切换
        updatedRecord = pmiRecordRepository.findById(testPmiRecord.getId()).orElse(null);
        assertNotNull(updatedRecord);
        assertEquals(PmiRecord.BillingMode.BY_TIME, updatedRecord.getBillingMode());
        assertNull(updatedRecord.getCurrentWindowId());
    }

    @Test
    void testCanStartMeetingCheck() throws Exception {
        // 1. 正常情况下可以开启会议
        mockMvc.perform(get("/api/pmi/{pmiRecordId}/billing/can-start-meeting", testPmiRecord.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.canStartMeeting").value(true));

        // 2. 设置超额时长后不能开启会议
        testPmiRecord.setOverdraftMinutes(10);
        pmiRecordRepository.save(testPmiRecord);

        mockMvc.perform(get("/api/pmi/{pmiRecordId}/billing/can-start-meeting", testPmiRecord.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.canStartMeeting").value(false));
    }

    @Test
    void testMeetingLifecycle() {
        // 1. 创建会议记录
        ZoomMeeting meeting = zoomMeetingService.createMeetingRecord(
            testPmiRecord.getId(), "test-uuid", "123456789", "测试会议", "host123");

        assertNotNull(meeting);
        assertEquals(ZoomMeeting.MeetingStatus.WAITING, meeting.getStatus());

        // 2. 开始会议
        zoomMeetingService.handleMeetingStarted("test-uuid");

        ZoomMeeting updatedMeeting = zoomMeetingRepository.findById(meeting.getId()).orElse(null);
        assertNotNull(updatedMeeting);
        assertEquals(ZoomMeeting.MeetingStatus.STARTED, updatedMeeting.getStatus());
        assertNotNull(updatedMeeting.getStartTime());

        // 3. 结束会议
        zoomMeetingService.handleMeetingEnded("test-uuid");

        updatedMeeting = zoomMeetingRepository.findById(meeting.getId()).orElse(null);
        assertNotNull(updatedMeeting);
        assertEquals(ZoomMeeting.MeetingStatus.ENDED, updatedMeeting.getStatus());
        assertNotNull(updatedMeeting.getEndTime());
    }

    @Test
    void testZoomMeetingDashboardAPI() throws Exception {
        // 创建测试会议
        ZoomMeeting meeting = new ZoomMeeting();
        meeting.setPmiRecordId(testPmiRecord.getId());
        meeting.setZoomMeetingUuid("test-uuid");
        meeting.setZoomMeetingId("123456789");
        meeting.setTopic("测试会议");
        meeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
        meeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);
        meeting.setStartTime(LocalDateTime.now().minusMinutes(30));
        zoomMeetingRepository.save(meeting);

        // 1. 获取活跃会议列表
        mockMvc.perform(get("/api/zoom-meetings/active")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray());

        // 2. 获取会议详情
        mockMvc.perform(get("/api/zoom-meetings/{meetingId}", meeting.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(meeting.getId()));

        // 3. 获取看板数据
        mockMvc.perform(get("/api/zoom-meetings/dashboard"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.activeMeetingsCount").exists());
    }

    @Test
    void testPmiBillingManagementAPI() throws Exception {
        // 1. 获取计费记录
        mockMvc.perform(get("/api/pmi-billing/records")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 2. 获取监控状态
        mockMvc.perform(get("/api/pmi-billing/monitor/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 3. 获取ZoomUser统计
        mockMvc.perform(get("/api/pmi-billing/zoom-users/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 4. 获取计费统计
        mockMvc.perform(get("/api/pmi-billing/statistics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
}
