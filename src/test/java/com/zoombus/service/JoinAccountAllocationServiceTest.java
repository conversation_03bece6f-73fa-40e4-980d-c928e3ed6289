package com.zoombus.service;

import com.zoombus.entity.JoinAccountUsageWindow;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.JoinAccountUsageWindowRepository;
import com.zoombus.repository.ZoomUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * Join Account智能分配服务测试类
 */
@ExtendWith(MockitoExtension.class)
class JoinAccountAllocationServiceTest {
    
    @Mock
    private ZoomUserRepository zoomUserRepository;
    
    @Mock
    private JoinAccountUsageWindowRepository windowRepository;
    
    @Mock
    private SystemConfigService systemConfigService;
    
    @InjectMocks
    private JoinAccountAllocationService allocationService;
    
    private ZoomUser testAccount1;
    private ZoomUser testAccount2;
    private ZoomUser testAccount3;
    
    @BeforeEach
    void setUp() {
        // 创建测试账号1 - 新账号，无使用历史
        testAccount1 = new ZoomUser();
        testAccount1.setId(1L);
        testAccount1.setEmail("<EMAIL>");
        testAccount1.setUserType(ZoomUser.UserType.LICENSED);
        testAccount1.setStatus(ZoomUser.UserStatus.ACTIVE);
        testAccount1.setAccountUsage(ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL);
        testAccount1.setTotalUsageCount(0);
        testAccount1.setTotalUsageMinutes(0);
        
        // 创建测试账号2 - 中等使用频率
        testAccount2 = new ZoomUser();
        testAccount2.setId(2L);
        testAccount2.setEmail("<EMAIL>");
        testAccount2.setUserType(ZoomUser.UserType.LICENSED);
        testAccount2.setStatus(ZoomUser.UserStatus.ACTIVE);
        testAccount2.setAccountUsage(ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL);
        testAccount2.setTotalUsageCount(25);
        testAccount2.setTotalUsageMinutes(1500); // 平均60分钟
        testAccount2.setLastUsedTime(LocalDateTime.now().minusDays(2));
        
        // 创建测试账号3 - 高使用频率
        testAccount3 = new ZoomUser();
        testAccount3.setId(3L);
        testAccount3.setEmail("<EMAIL>");
        testAccount3.setUserType(ZoomUser.UserType.LICENSED);
        testAccount3.setStatus(ZoomUser.UserStatus.ACTIVE);
        testAccount3.setAccountUsage(ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL);
        testAccount3.setTotalUsageCount(80);
        testAccount3.setTotalUsageMinutes(4800); // 平均60分钟
        testAccount3.setLastUsedTime(LocalDateTime.now().minusHours(6));
    }
    
    @Test
    void testAllocateAccountSuccess() {
        // Given
        LocalDateTime startTime = LocalDateTime.now().plusDays(1);
        LocalDateTime endTime = startTime.plusDays(3);
        
        List<ZoomUser> availableAccounts = Arrays.asList(testAccount1, testAccount2, testAccount3);
        
        when(zoomUserRepository.findByUserTypeAndStatusAndAccountUsage(
                ZoomUser.UserType.LICENSED, 
                ZoomUser.UserStatus.ACTIVE,
                ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL))
                .thenReturn(availableAccounts);
        
        // 模拟没有时间冲突
        when(windowRepository.findOverlappingWindows(anyLong(), any(), any()))
                .thenReturn(Collections.emptyList());
        
        // 模拟算法权重配置
        when(systemConfigService.getDoubleConfigValue("join_account.algorithm.time_weight", 0.4))
                .thenReturn(0.4);
        when(systemConfigService.getDoubleConfigValue("join_account.algorithm.load_weight", 0.4))
                .thenReturn(0.4);
        when(systemConfigService.getDoubleConfigValue("join_account.algorithm.history_weight", 0.2))
                .thenReturn(0.2);
        
        // 模拟使用统计
        when(windowRepository.countByZoomUserIdAndTimeRange(anyLong(), any(), any()))
                .thenReturn(0L);
        
        // When
        ZoomUser allocatedAccount = allocationService.allocateAccount(startTime, endTime);
        
        // Then
        assertNotNull(allocatedAccount);
        assertTrue(Arrays.asList(testAccount1, testAccount2, testAccount3).contains(allocatedAccount));
        
        verify(zoomUserRepository).findByUserTypeAndStatusAndAccountUsage(
                ZoomUser.UserType.LICENSED, 
                ZoomUser.UserStatus.ACTIVE,
                ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL);
    }
    
    @Test
    void testAllocateAccountNoAvailableAccounts() {
        // Given
        LocalDateTime startTime = LocalDateTime.now().plusDays(1);
        LocalDateTime endTime = startTime.plusDays(3);
        
        when(zoomUserRepository.findByUserTypeAndStatusAndAccountUsage(
                ZoomUser.UserType.LICENSED, 
                ZoomUser.UserStatus.ACTIVE,
                ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL))
                .thenReturn(Collections.emptyList());
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            allocationService.allocateAccount(startTime, endTime);
        });
        
        assertEquals("没有可用的专业版账号", exception.getMessage());
    }
    
    @Test
    void testAllocateAccountAllConflicting() {
        // Given
        LocalDateTime startTime = LocalDateTime.now().plusDays(1);
        LocalDateTime endTime = startTime.plusDays(3);
        
        List<ZoomUser> availableAccounts = Arrays.asList(testAccount1, testAccount2);
        
        when(zoomUserRepository.findByUserTypeAndStatusAndAccountUsage(
                ZoomUser.UserType.LICENSED, 
                ZoomUser.UserStatus.ACTIVE,
                ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL))
                .thenReturn(availableAccounts);
        
        // 模拟所有账号都有时间冲突
        JoinAccountUsageWindow conflictWindow = new JoinAccountUsageWindow();
        conflictWindow.setStartTime(startTime.minusHours(1));
        conflictWindow.setEndTime(endTime.plusHours(1));
        
        when(windowRepository.findOverlappingWindows(anyLong(), any(), any()))
                .thenReturn(Arrays.asList(conflictWindow));
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            allocationService.allocateAccount(startTime, endTime);
        });
        
        assertEquals("在指定时间段内没有可用的账号", exception.getMessage());
    }
    
    @Test
    void testAllocateAccountInvalidTimeRange() {
        // Given
        LocalDateTime startTime = LocalDateTime.now().plusDays(1);
        LocalDateTime endTime = startTime.minusHours(1); // 结束时间早于开始时间
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            allocationService.allocateAccount(startTime, endTime);
        });
        
        assertEquals("开始时间必须早于结束时间", exception.getMessage());
    }
    
    @Test
    void testAllocateAccountNullParameters() {
        // Test null start time
        assertThrows(IllegalArgumentException.class, () -> {
            allocationService.allocateAccount(null, LocalDateTime.now().plusDays(1));
        });
        
        // Test null end time
        assertThrows(IllegalArgumentException.class, () -> {
            allocationService.allocateAccount(LocalDateTime.now().plusDays(1), null);
        });
    }
    
    @Test
    void testGetAllocationStatistics() {
        // Given
        List<ZoomUser> availableAccounts = Arrays.asList(testAccount1, testAccount2, testAccount3);
        
        when(zoomUserRepository.findByUserTypeAndStatusAndAccountUsage(
                ZoomUser.UserType.LICENSED, 
                ZoomUser.UserStatus.ACTIVE,
                ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL))
                .thenReturn(availableAccounts);
        
        when(windowRepository.countByZoomUserIdAndTimeRange(anyLong(), any(), any()))
                .thenReturn(0L);
        
        // When
        Map<String, Object> statistics = allocationService.getAllocationStatistics();
        
        // Then
        assertNotNull(statistics);
        assertEquals(3, statistics.get("totalAvailableAccounts"));
        assertEquals(0L, statistics.get("currentActiveAccounts"));
        assertNotNull(statistics.get("loadDistribution"));
        
        @SuppressWarnings("unchecked")
        Map<String, Long> loadDistribution = (Map<String, Long>) statistics.get("loadDistribution");
        assertEquals(3L, loadDistribution.get("空闲"));
    }
    
    @Test
    void testPreferNewAccountWithLowUsage() {
        // Given
        LocalDateTime startTime = LocalDateTime.now().plusDays(1);
        LocalDateTime endTime = startTime.plusDays(3);
        
        // 只提供新账号和高使用频率账号
        List<ZoomUser> availableAccounts = Arrays.asList(testAccount1, testAccount3);
        
        when(zoomUserRepository.findByUserTypeAndStatusAndAccountUsage(
                ZoomUser.UserType.LICENSED, 
                ZoomUser.UserStatus.ACTIVE,
                ZoomUser.AccountUsage.JOIN_ACCOUNT_RENTAL))
                .thenReturn(availableAccounts);
        
        when(windowRepository.findOverlappingWindows(anyLong(), any(), any()))
                .thenReturn(Collections.emptyList());
        
        when(systemConfigService.getDoubleConfigValue("join_account.algorithm.time_weight", 0.4))
                .thenReturn(0.4);
        when(systemConfigService.getDoubleConfigValue("join_account.algorithm.load_weight", 0.4))
                .thenReturn(0.4);
        when(systemConfigService.getDoubleConfigValue("join_account.algorithm.history_weight", 0.2))
                .thenReturn(0.2);
        
        // 新账号没有使用记录
        when(windowRepository.countByZoomUserIdAndTimeRange(eq(1L), any(), any()))
                .thenReturn(0L);
        // 高频账号有很多使用记录
        when(windowRepository.countByZoomUserIdAndTimeRange(eq(3L), any(), any()))
                .thenReturn(50L);
        
        // When
        ZoomUser allocatedAccount = allocationService.allocateAccount(startTime, endTime);
        
        // Then
        assertNotNull(allocatedAccount);
        // 应该优先选择新账号（使用频率低）
        assertEquals(testAccount1.getId(), allocatedAccount.getId());
    }
}
