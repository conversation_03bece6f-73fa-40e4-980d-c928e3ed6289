package com.zoombus.service;

import com.zoombus.entity.JoinAccountRentalToken;
import com.zoombus.repository.JoinAccountRentalTokenRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * Join Account Rental令牌服务测试类
 */
@ExtendWith(MockitoExtension.class)
class JoinAccountRentalTokenServiceTest {
    
    @Mock
    private JoinAccountRentalTokenRepository tokenRepository;
    
    @Mock
    private SystemConfigService systemConfigService;
    
    @InjectMocks
    private JoinAccountRentalTokenService tokenService;
    
    private JoinAccountRentalToken testToken;
    
    @BeforeEach
    void setUp() {
        testToken = new JoinAccountRentalToken();
        testToken.setId(1L);
        testToken.setTokenNumber("ABC123");
        testToken.setBatchNumber("JAR_BATCH_20250807_123456");
        testToken.setUsageDays(3);
        testToken.setStatus(JoinAccountRentalToken.TokenStatus.PENDING);
        testToken.setExportStatus(JoinAccountRentalToken.ExportStatus.NOT_EXPORTED);
        testToken.setCreatedAt(LocalDateTime.now());
    }
    
    @Test
    void testBatchGenerateTokens() {
        // Given
        when(tokenRepository.existsByTokenNumber(any())).thenReturn(false);
        when(tokenRepository.saveAll(anyList())).thenAnswer(invocation -> {
            List<JoinAccountRentalToken> tokens = invocation.getArgument(0);
            for (int i = 0; i < tokens.size(); i++) {
                tokens.get(i).setId((long) (i + 1));
            }
            return tokens;
        });
        
        // When
        List<JoinAccountRentalToken> result = tokenService.batchGenerateTokens(3, 5, "测试生成", "TEST_USER");
        
        // Then
        assertNotNull(result);
        assertEquals(5, result.size());
        
        for (JoinAccountRentalToken token : result) {
            assertEquals(3, token.getUsageDays());
            assertEquals(JoinAccountRentalToken.TokenStatus.PENDING, token.getStatus());
            assertEquals(JoinAccountRentalToken.ExportStatus.NOT_EXPORTED, token.getExportStatus());
            assertNotNull(token.getTokenNumber());
            assertEquals(6, token.getTokenNumber().length());
            assertTrue(token.getBatchNumber().startsWith("JAR_BATCH_"));
        }
        
        verify(tokenRepository).saveAll(anyList());
    }
    
    @Test
    void testBatchGenerateTokensInvalidParams() {
        // Test invalid usage days
        assertThrows(IllegalArgumentException.class, () -> {
            tokenService.batchGenerateTokens(0, 5, "测试", "TEST_USER");
        });
        
        // Test invalid quantity
        assertThrows(IllegalArgumentException.class, () -> {
            tokenService.batchGenerateTokens(3, 0, "测试", "TEST_USER");
        });
        
        // Test quantity too large
        assertThrows(IllegalArgumentException.class, () -> {
            tokenService.batchGenerateTokens(3, 1001, "测试", "TEST_USER");
        });
    }
    
    @Test
    void testGetTokenByNumber() {
        // Given
        when(tokenRepository.findByTokenNumber("ABC123")).thenReturn(Optional.of(testToken));
        
        // When
        Optional<JoinAccountRentalToken> result = tokenService.getTokenByNumber("ABC123");
        
        // Then
        assertTrue(result.isPresent());
        assertEquals(testToken, result.get());
        verify(tokenRepository).findByTokenNumber("ABC123");
    }
    
    @Test
    void testGenerateTokenLink() {
        // Given
        when(systemConfigService.getConfigValue("join_account.domain.base_url", "https://zoombus.cn"))
                .thenReturn("https://test.zoombus.cn");
        when(systemConfigService.getConfigValue("join_account.domain.path_prefix", "rf"))
                .thenReturn("test");
        
        // When
        String link = tokenService.generateTokenLink("ABC123");
        
        // Then
        assertEquals("https://test.zoombus.cn/test/ABC123", link);
    }
    
    @Test
    void testCanReserveToken() {
        // Given
        when(tokenRepository.findByTokenNumber("ABC123")).thenReturn(Optional.of(testToken));
        
        // When
        boolean canReserve = tokenService.canReserveToken("ABC123");
        
        // Then
        assertTrue(canReserve);
        verify(tokenRepository).findByTokenNumber("ABC123");
    }
    
    @Test
    void testCanReserveTokenNotFound() {
        // Given
        when(tokenRepository.findByTokenNumber("NOTFOUND")).thenReturn(Optional.empty());
        
        // When
        boolean canReserve = tokenService.canReserveToken("NOTFOUND");
        
        // Then
        assertFalse(canReserve);
    }
    
    @Test
    void testMarkTokensAsExported() {
        // Given
        List<Long> tokenIds = List.of(1L);
        when(tokenRepository.findByIdIn(tokenIds)).thenReturn(List.of(testToken));
        when(tokenRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));
        
        // When
        List<JoinAccountRentalToken> result = tokenService.markTokensAsExported(tokenIds, "TEST_USER");
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        JoinAccountRentalToken updatedToken = result.get(0);
        assertEquals(JoinAccountRentalToken.TokenStatus.EXPORTED, updatedToken.getStatus());
        assertEquals(JoinAccountRentalToken.ExportStatus.EXPORTED, updatedToken.getExportStatus());
        assertNotNull(updatedToken.getExportedAt());
        assertEquals("TEST_USER", updatedToken.getExportedBy());
        
        verify(tokenRepository).findByIdIn(tokenIds);
        verify(tokenRepository).saveAll(anyList());
    }
    
    @Test
    void testCancelTokens() {
        // Given
        List<Long> tokenIds = List.of(1L);
        when(tokenRepository.findByIdIn(tokenIds)).thenReturn(List.of(testToken));
        when(tokenRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));
        
        // When
        List<JoinAccountRentalToken> result = tokenService.cancelTokens(tokenIds, "TEST_USER", "测试作废");
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        JoinAccountRentalToken cancelledToken = result.get(0);
        assertEquals(JoinAccountRentalToken.TokenStatus.CANCELLED, cancelledToken.getStatus());
        assertNotNull(cancelledToken.getCancelledAt());
        assertEquals("TEST_USER", cancelledToken.getCancelledBy());
        assertTrue(cancelledToken.getRemark().contains("作废原因: 测试作废"));
        
        verify(tokenRepository).findByIdIn(tokenIds);
        verify(tokenRepository).saveAll(anyList());
    }
    
    @Test
    void testReserveToken() {
        // Given
        when(tokenRepository.findByTokenNumber("ABC123")).thenReturn(Optional.of(testToken));
        when(tokenRepository.save(any(JoinAccountRentalToken.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        LocalDateTime startTime = LocalDateTime.now().plusDays(1);
        
        // When
        JoinAccountRentalToken result = tokenService.reserveToken(
                "ABC123", 100L, "<EMAIL>", startTime, "test_password", "TEST_USER");
        
        // Then
        assertNotNull(result);
        assertEquals(JoinAccountRentalToken.TokenStatus.RESERVED, result.getStatus());
        assertEquals(100L, result.getAssignedZoomUserId());
        assertEquals("<EMAIL>", result.getAssignedZoomUserEmail());
        assertEquals(startTime, result.getWindowStartTime());
        assertNotNull(result.getWindowEndTime());
        assertNotNull(result.getReservedAt());
        
        verify(tokenRepository).findByTokenNumber("ABC123");
        verify(tokenRepository).save(any(JoinAccountRentalToken.class));
    }
    
    @Test
    void testActivateToken() {
        // Given
        testToken.setStatus(JoinAccountRentalToken.TokenStatus.RESERVED);
        testToken.setWindowStartTime(LocalDateTime.now().minusMinutes(10));
        
        when(tokenRepository.findByTokenNumber("ABC123")).thenReturn(Optional.of(testToken));
        when(tokenRepository.save(any(JoinAccountRentalToken.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // When
        JoinAccountRentalToken result = tokenService.activateToken("ABC123", "password123");
        
        // Then
        assertNotNull(result);
        assertEquals(JoinAccountRentalToken.TokenStatus.ACTIVE, result.getStatus());
        assertEquals("password123", result.getAssignedPassword());
        
        verify(tokenRepository).findByTokenNumber("ABC123");
        verify(tokenRepository).save(any(JoinAccountRentalToken.class));
    }
    
    @Test
    void testCompleteToken() {
        // Given
        testToken.setStatus(JoinAccountRentalToken.TokenStatus.ACTIVE);
        
        when(tokenRepository.findByTokenNumber("ABC123")).thenReturn(Optional.of(testToken));
        when(tokenRepository.save(any(JoinAccountRentalToken.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // When
        JoinAccountRentalToken result = tokenService.completeToken("ABC123");
        
        // Then
        assertNotNull(result);
        assertEquals(JoinAccountRentalToken.TokenStatus.COMPLETED, result.getStatus());
        
        verify(tokenRepository).findByTokenNumber("ABC123");
        verify(tokenRepository).save(any(JoinAccountRentalToken.class));
    }
}
