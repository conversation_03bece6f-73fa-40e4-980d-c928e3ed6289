package com.zoombus.service;

import com.zoombus.entity.ZoomMeeting;
import com.zoombus.entity.ZoomMeeting.MeetingStatus;
import com.zoombus.repository.ZoomMeetingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 会议生命周期管理器测试
 */
@ExtendWith(MockitoExtension.class)
class MeetingLifecycleManagerTest {

    @Mock
    private ZoomMeetingRepository zoomMeetingRepository;

    @Mock
    private MeetingStatusTransitionValidator statusTransitionValidator;

    @Mock
    private DistributedLockManager distributedLockManager;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @InjectMocks
    private MeetingLifecycleManager meetingLifecycleManager;

    private ZoomMeeting testMeeting;

    @BeforeEach
    void setUp() {
        testMeeting = new ZoomMeeting();
        testMeeting.setId(1L);
        testMeeting.setZoomMeetingId("test-meeting-123");
        testMeeting.setZoomMeetingUuid("test-uuid-123");
        testMeeting.setStatus(MeetingStatus.WAITING);
        testMeeting.setTopic("测试会议");
        testMeeting.setCreatedAt(LocalDateTime.now());
        testMeeting.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    void testValidStatusTransition() {
        // 模拟分布式锁执行
        doAnswer(invocation -> {
            Runnable operation = invocation.getArgument(2);
            operation.run();
            return null;
        }).when(distributedLockManager).executeWithMeetingStatusLock(any(Long.class), any(Duration.class), any(Runnable.class));

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(testMeeting));
        when(statusTransitionValidator.isValidTransition(MeetingStatus.WAITING, MeetingStatus.STARTED))
            .thenReturn(true);

        // 执行状态转换
        boolean result = meetingLifecycleManager.updateMeetingStatus(1L, MeetingStatus.STARTED, "测试开始");

        // 验证结果
        assertTrue(result);
        assertEquals(MeetingStatus.STARTED, testMeeting.getStatus());
        assertNotNull(testMeeting.getStartTime());
        verify(zoomMeetingRepository).save(testMeeting);
        verify(eventPublisher).publishEvent(any(MeetingStatusChangedEvent.class));
    }

    @Test
    void testInvalidStatusTransition() {
        // 模拟分布式锁执行
        doAnswer(invocation -> {
            Runnable operation = invocation.getArgument(2);
            operation.run();
            return null;
        }).when(distributedLockManager).executeWithMeetingStatusLock(any(Long.class), any(Duration.class), any(Runnable.class));

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(testMeeting));
        when(statusTransitionValidator.isValidTransition(MeetingStatus.WAITING, MeetingStatus.SETTLED))
            .thenReturn(false);
        doThrow(new IllegalStateException("非法状态转换"))
            .when(statusTransitionValidator)
            .validateTransitionOrThrow(MeetingStatus.WAITING, MeetingStatus.SETTLED, any());

        // 执行状态转换应该抛出异常
        assertThrows(IllegalStateException.class, () -> {
            meetingLifecycleManager.updateMeetingStatus(1L, MeetingStatus.SETTLED, "非法转换");
        });

        // 验证状态没有改变
        assertEquals(MeetingStatus.WAITING, testMeeting.getStatus());
        verify(zoomMeetingRepository, never()).save(testMeeting);
    }

    @Test
    void testIdempotentStatusUpdate() {
        // 模拟分布式锁执行
        doAnswer(invocation -> {
            Runnable operation = invocation.getArgument(2);
            operation.run();
            return null;
        }).when(distributedLockManager).executeWithMeetingStatusLock(any(Long.class), any(Duration.class), any(Runnable.class));

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(testMeeting));

        // 执行相同状态的转换（幂等操作）
        boolean result = meetingLifecycleManager.updateMeetingStatus(1L, MeetingStatus.WAITING, "幂等测试");

        // 验证结果
        assertTrue(result);
        assertEquals(MeetingStatus.WAITING, testMeeting.getStatus());
        // 幂等操作不应该保存到数据库
        verify(zoomMeetingRepository, never()).save(testMeeting);
    }

    @Test
    void testConcurrentModificationHandling() {
        // 模拟并发修改异常
        doThrow(new DistributedLockManager.ConcurrentModificationException("锁获取失败"))
            .when(distributedLockManager).executeWithMeetingStatusLock(any(Long.class), any(Duration.class), any(Runnable.class));

        // 执行状态转换
        boolean result = meetingLifecycleManager.tryUpdateMeetingStatus(1L, MeetingStatus.STARTED, "并发测试");

        // 验证结果
        assertFalse(result);
        verify(zoomMeetingRepository, never()).findById(any());
    }

    @Test
    void testMeetingNotFound() {
        // 模拟分布式锁执行
        doAnswer(invocation -> {
            Runnable operation = invocation.getArgument(2);
            operation.run();
            return null;
        }).when(distributedLockManager).executeWithMeetingStatusLock(any(Long.class), any(Duration.class), any(Runnable.class));

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.empty());

        // 执行状态转换应该抛出异常
        assertThrows(MeetingLifecycleManager.ResourceNotFoundException.class, () -> {
            meetingLifecycleManager.updateMeetingStatus(1L, MeetingStatus.STARTED, "会议不存在");
        });
    }
}
