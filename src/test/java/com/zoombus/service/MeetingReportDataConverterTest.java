package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.MeetingParticipant;
import com.zoombus.entity.MeetingReport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * MeetingReportDataConverter 单元测试
 */
@ExtendWith(MockitoExtension.class)
class MeetingReportDataConverterTest {
    
    @Mock
    private ObjectMapper objectMapper;
    
    @InjectMocks
    private MeetingReportDataConverter converter;
    
    private JsonNode mockReportData;
    private JsonNode mockParticipantsData;
    
    @BeforeEach
    void setUp() throws Exception {
        // 模拟会议报告数据
        String reportJson = "{\n" +
            "    \"uuid\": \"test-uuid-123\",\n" +
            "    \"id\": 123456789,\n" +
            "    \"topic\": \"测试会议\",\n" +
            "    \"type\": 2,\n" +
            "    \"start_time\": \"2023-12-15T10:00:00Z\",\n" +
            "    \"end_time\": \"2023-12-15T11:00:00Z\",\n" +
            "    \"duration\": 60,\n" +
            "    \"participants_count\": 5,\n" +
            "    \"has_recording\": true,\n" +
            "    \"has_pstn\": false,\n" +
            "    \"has_voip\": true,\n" +
            "    \"has_video\": true,\n" +
            "    \"has_screen_share\": false\n" +
            "}";
        
        // 模拟参会者数据
        String participantsJson = "{\n" +
            "    \"page_count\": 1,\n" +
            "    \"page_size\": 300,\n" +
            "    \"total_records\": 2,\n" +
            "    \"participants\": [\n" +
            "        {\n" +
            "            \"id\": \"participant-1\",\n" +
            "            \"user_id\": \"user-1\",\n" +
            "            \"name\": \"张三\",\n" +
            "            \"user_email\": \"<EMAIL>\",\n" +
            "            \"join_time\": \"2023-12-15T10:00:00Z\",\n" +
            "            \"leave_time\": \"2023-12-15T11:00:00Z\",\n" +
            "            \"duration\": 60,\n" +
            "            \"user_type\": 1,\n" +
            "            \"status\": \"in_meeting\",\n" +
            "            \"audio_type\": \"voip\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": \"participant-2\",\n" +
            "            \"user_id\": \"user-2\",\n" +
            "            \"name\": \"李四\",\n" +
            "            \"user_email\": \"<EMAIL>\",\n" +
            "            \"join_time\": \"2023-12-15T10:05:00Z\",\n" +
            "            \"leave_time\": \"2023-12-15T10:55:00Z\",\n" +
            "            \"duration\": 50,\n" +
            "            \"user_type\": 0,\n" +
            "            \"status\": \"in_meeting\",\n" +
            "            \"audio_type\": \"voip\"\n" +
            "        }\n" +
            "    ]\n" +
            "}";
        
        ObjectMapper realMapper = new ObjectMapper();
        mockReportData = realMapper.readTree(reportJson);
        mockParticipantsData = realMapper.readTree(participantsJson);
    }
    
    @Test
    void testConvertToMeetingReport_Success() throws Exception {
        // Given
        String zoomMeetingUuid = "test-uuid-123";
        String zoomMeetingId = "123456789";
        
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        
        // When
        MeetingReport result = converter.convertToMeetingReport(mockReportData, zoomMeetingUuid, zoomMeetingId);
        
        // Then
        assertNotNull(result);
        assertEquals(zoomMeetingUuid, result.getZoomMeetingUuid());
        assertEquals(zoomMeetingId, result.getZoomMeetingId());
        assertEquals("测试会议", result.getTopic());
        assertEquals(60, result.getDurationMinutes());
        assertEquals(5, result.getTotalParticipants());
        assertTrue(result.getHasRecording());
        assertFalse(result.getHasPstn());
        assertTrue(result.getHasVoip());
        assertTrue(result.getHasVideo());
        assertFalse(result.getHasScreenShare());
        assertEquals(MeetingReport.FetchStatus.SUCCESS, result.getFetchStatus());
        assertNotNull(result.getStartTime());
        assertNotNull(result.getEndTime());
    }
    
    @Test
    void testConvertToMeetingReport_NullData() {
        // Given
        String zoomMeetingUuid = "test-uuid-123";
        String zoomMeetingId = "123456789";
        
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            converter.convertToMeetingReport(null, zoomMeetingUuid, zoomMeetingId);
        });
    }
    
    @Test
    void testConvertToMeetingParticipants_Success() throws Exception {
        // Given
        Long meetingReportId = 1L;
        
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        
        // When
        List<MeetingParticipant> result = converter.convertToMeetingParticipants(mockParticipantsData, meetingReportId);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        MeetingParticipant participant1 = result.get(0);
        assertEquals(meetingReportId, participant1.getMeetingReportId());
        assertEquals("participant-1", participant1.getParticipantUuid());
        assertEquals("张三", participant1.getParticipantName());
        assertEquals("<EMAIL>", participant1.getParticipantEmail());
        assertEquals(MeetingParticipant.UserType.HOST, participant1.getUserType());
        assertEquals(60, participant1.getDurationMinutes());
        
        MeetingParticipant participant2 = result.get(1);
        assertEquals("李四", participant2.getParticipantName());
        assertEquals(MeetingParticipant.UserType.ATTENDEE, participant2.getUserType());
        assertEquals(50, participant2.getDurationMinutes());
    }
    
    @Test
    void testConvertToMeetingParticipants_EmptyData() throws Exception {
        // Given
        Long meetingReportId = 1L;
        String emptyJson = "{\n" +
            "    \"page_count\": 0,\n" +
            "    \"page_size\": 300,\n" +
            "    \"total_records\": 0,\n" +
            "    \"participants\": []\n" +
            "}";
        
        ObjectMapper realMapper = new ObjectMapper();
        JsonNode emptyData = realMapper.readTree(emptyJson);
        
        // When
        List<MeetingParticipant> result = converter.convertToMeetingParticipants(emptyData, meetingReportId);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
    
    @Test
    void testUpdateReportParticipantStatistics() {
        // Given
        MeetingReport report = new MeetingReport();
        report.setId(1L);
        
        MeetingParticipant participant1 = new MeetingParticipant();
        participant1.setParticipantEmail("<EMAIL>");
        participant1.setHasVideo(true);
        participant1.setHasScreenShare(false);
        
        MeetingParticipant participant2 = new MeetingParticipant();
        participant2.setParticipantEmail("<EMAIL>");
        participant2.setHasVideo(false);
        participant2.setHasScreenShare(true);
        
        MeetingParticipant participant3 = new MeetingParticipant();
        participant3.setParticipantEmail("<EMAIL>"); // 重复邮箱
        participant3.setHasVideo(false);
        participant3.setHasScreenShare(false);
        
        List<MeetingParticipant> participants = List.of(participant1, participant2, participant3);
        
        // When
        converter.updateReportParticipantStatistics(report, participants);
        
        // Then
        assertEquals(3, report.getTotalParticipants()); // 总人次
        assertEquals(2, report.getUniqueParticipants()); // 唯一人数（按邮箱去重）
        assertTrue(report.getHasVideo()); // 有人使用视频
        assertTrue(report.getHasScreenShare()); // 有人使用屏幕共享
    }
    
    @Test
    void testValidateMeetingReport_Valid() {
        // Given
        MeetingReport report = new MeetingReport();
        report.setZoomMeetingUuid("test-uuid");
        report.setZoomMeetingId("123456");
        
        // When
        boolean result = converter.validateMeetingReport(report);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testValidateMeetingReport_Invalid() {
        // Given
        MeetingReport report = new MeetingReport();
        // 缺少必要字段
        
        // When
        boolean result = converter.validateMeetingReport(report);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidateMeetingReport_Null() {
        // When
        boolean result = converter.validateMeetingReport(null);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidateMeetingParticipants_Valid() {
        // Given
        MeetingParticipant participant = new MeetingParticipant();
        participant.setMeetingReportId(1L);
        
        List<MeetingParticipant> participants = List.of(participant);
        
        // When
        boolean result = converter.validateMeetingParticipants(participants);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testValidateMeetingParticipants_Invalid() {
        // Given
        MeetingParticipant participant = new MeetingParticipant();
        // 缺少meetingReportId
        
        List<MeetingParticipant> participants = List.of(participant);
        
        // When
        boolean result = converter.validateMeetingParticipants(participants);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidateMeetingParticipants_Null() {
        // When
        boolean result = converter.validateMeetingParticipants(null);
        
        // Then
        assertFalse(result);
    }
}
