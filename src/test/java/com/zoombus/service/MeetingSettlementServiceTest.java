package com.zoombus.service;

import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.repository.PmiBillingRecordRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MeetingSettlementServiceTest {

    @Mock
    private ZoomMeetingRepository zoomMeetingRepository;

    @Mock
    private PmiRecordRepository pmiRecordRepository;

    @Mock
    private PmiBillingRecordRepository billingRecordRepository;

    @InjectMocks
    private MeetingSettlementService meetingSettlementService;

    private ZoomMeeting meeting;
    private PmiRecord pmiRecord;

    @BeforeEach
    void setUp() {
        meeting = new ZoomMeeting();
        meeting.setId(1L);
        meeting.setPmiRecordId(1L);
        meeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);
        meeting.setStatus(ZoomMeeting.MeetingStatus.ENDED);
        meeting.setIsSettled(false);
        meeting.setStartTime(LocalDateTime.now().minusHours(1));
        meeting.setEndTime(LocalDateTime.now());

        pmiRecord = new PmiRecord();
        pmiRecord.setId(1L);
        pmiRecord.setUserId(1L);
        pmiRecord.setBillingMode(PmiRecord.BillingMode.BY_TIME);
        pmiRecord.setStatus(PmiRecord.PmiStatus.ACTIVE);
        pmiRecord.setAvailableMinutes(100);
        pmiRecord.setPendingDeductMinutes(50);
        pmiRecord.setOverdraftMinutes(0);
        pmiRecord.setTotalUsedMinutes(0);
    }

    @Test
    void testSettleMeeting_WithSufficientBalance_ShouldKeepPmiActive() {
        // 安排：余额充足的情况
        pmiRecord.setAvailableMinutes(100);
        pmiRecord.setPendingDeductMinutes(50);

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(meeting));
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(meeting);

        // 执行
        MeetingSettlementService.SettlementResult result = meetingSettlementService.settleMeeting(1L);

        // 验证
        assertTrue(result.isSuccess());
        verify(pmiRecordRepository).save(argThat(record -> {
            assertEquals(50, record.getAvailableMinutes()); // 100 - 50 = 50
            assertEquals(PmiRecord.PmiStatus.ACTIVE, record.getStatus()); // 应该保持活跃
            return true;
        }));
    }

    @Test
    void testSettleMeeting_WithZeroBalance_ShouldDeactivatePmi() {
        // 安排：余额刚好用完的情况
        pmiRecord.setAvailableMinutes(50);
        pmiRecord.setPendingDeductMinutes(50);

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(meeting));
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(meeting);

        // 执行
        MeetingSettlementService.SettlementResult result = meetingSettlementService.settleMeeting(1L);

        // 验证
        assertTrue(result.isSuccess());
        assertTrue(result.getMessage().contains("PMI已自动设置为未激活状态"));
        verify(pmiRecordRepository).save(argThat(record -> {
            assertEquals(0, record.getAvailableMinutes()); // 50 - 50 = 0
            assertEquals(PmiRecord.PmiStatus.INACTIVE, record.getStatus()); // 应该被设置为未激活
            return true;
        }));
    }

    @Test
    void testSettleMeeting_WithInsufficientBalance_ShouldDeactivatePmi() {
        // 安排：余额不足的情况
        pmiRecord.setAvailableMinutes(30);
        pmiRecord.setPendingDeductMinutes(50);

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(meeting));
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(meeting);

        // 执行
        MeetingSettlementService.SettlementResult result = meetingSettlementService.settleMeeting(1L);

        // 验证
        assertTrue(result.isSuccess());
        assertTrue(result.getMessage().contains("PMI已自动设置为未激活状态"));
        verify(pmiRecordRepository).save(argThat(record -> {
            assertEquals(0, record.getAvailableMinutes()); // 余额用完
            assertEquals(20, record.getOverdraftMinutes()); // 产生20分钟超额
            assertEquals(PmiRecord.PmiStatus.INACTIVE, record.getStatus()); // 应该被设置为未激活
            return true;
        }));
    }

    @Test
    void testSettleMeeting_LongBillingMode_ShouldNotCheckBalance() {
        // 安排：按时段计费模式
        meeting.setBillingMode(PmiRecord.BillingMode.LONG);
        pmiRecord.setBillingMode(PmiRecord.BillingMode.LONG);
        pmiRecord.setAvailableMinutes(0); // 即使余额为0
        meeting.setDurationMinutes(60);

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(meeting));
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(meeting);

        // 执行
        MeetingSettlementService.SettlementResult result = meetingSettlementService.settleMeeting(1L);

        // 验证
        assertTrue(result.isSuccess());
        assertFalse(result.getMessage().contains("PMI已自动设置为未激活状态"));
        verify(pmiRecordRepository).save(argThat(record -> {
            assertEquals(PmiRecord.PmiStatus.ACTIVE, record.getStatus()); // 按时段计费不应该修改状态
            return true;
        }));
    }

    @Test
    void testSettleMeeting_AlreadySettled_ShouldReturnWarning() {
        // 安排：已经结算过的会议
        meeting.setStatus(ZoomMeeting.MeetingStatus.SETTLED); // 设置状态为已结算

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(meeting));

        // 执行
        MeetingSettlementService.SettlementResult result = meetingSettlementService.settleMeeting(1L);

        // 验证
        assertFalse(result.isSuccess());
        assertEquals("会议已经结算过了", result.getMessage());
        verify(pmiRecordRepository, never()).save(any());
        verify(pmiRecordRepository, never()).findById(any()); // 不应该查找PMI记录
    }

    @Test
    void testSettleMeeting_NoPmiRecord_ShouldSkipSettlement() {
        // 安排：没有关联PMI记录的会议
        meeting.setPmiRecordId(null);

        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(meeting));

        // 执行
        MeetingSettlementService.SettlementResult result = meetingSettlementService.settleMeeting(1L);

        // 验证
        assertTrue(result.isSuccess());
        assertEquals("会议没有关联PMI记录，无需结算", result.getMessage());
        verify(pmiRecordRepository, never()).save(any());
    }
}
