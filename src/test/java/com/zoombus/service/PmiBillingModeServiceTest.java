package com.zoombus.service;

import com.zoombus.entity.PmiRecord;
import com.zoombus.repository.PmiRecordRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PmiBillingModeServiceTest {

    @Mock
    private PmiRecordRepository pmiRecordRepository;

    @InjectMocks
    private PmiBillingModeService pmiBillingModeService;

    private PmiRecord pmiRecord;

    @BeforeEach
    void setUp() {
        pmiRecord = new PmiRecord();
        pmiRecord.setId(1L);
        pmiRecord.setBillingMode(PmiRecord.BillingMode.BY_TIME);
        pmiRecord.setBillingStatus(PmiRecord.BillingStatus.ACTIVE);
        pmiRecord.setAvailableMinutes(100);
        pmiRecord.setOverdraftMinutes(0);
    }

    @Test
    void testSwitchToLongBilling() {
        // Given
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);

        LocalDateTime expireTime = LocalDateTime.now().plusHours(1);

        // When
        pmiBillingModeService.switchToLongBilling(1L, 123L, expireTime);

        // Then
        verify(pmiRecordRepository).save(argThat(record -> 
            record.getBillingMode() == PmiRecord.BillingMode.LONG &&
            record.getCurrentWindowId().equals(123L) &&
            record.getWindowExpireTime().equals(expireTime)
        ));
    }

    @Test
    void testSwitchToTimeBilling() {
        // Given
        pmiRecord.setBillingMode(PmiRecord.BillingMode.LONG);
        pmiRecord.setCurrentWindowId(123L);
        pmiRecord.setWindowExpireTime(LocalDateTime.now().plusHours(1));
        
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);

        // When
        pmiBillingModeService.switchToTimeBilling(1L);

        // Then
        verify(pmiRecordRepository).save(argThat(record -> 
            record.getBillingMode() == PmiRecord.BillingMode.BY_TIME &&
            record.getCurrentWindowId() == null &&
            record.getWindowExpireTime() == null
        ));
    }

    @Test
    void testCanStartMeeting_TimeBilling_WithAvailableMinutes() {
        // Given
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));

        // When
        boolean canStart = pmiBillingModeService.canStartMeeting(1L);

        // Then
        assertTrue(canStart);
    }

    @Test
    void testCanStartMeeting_TimeBilling_WithOverdraft() {
        // Given
        pmiRecord.setOverdraftMinutes(10);
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));

        // When
        boolean canStart = pmiBillingModeService.canStartMeeting(1L);

        // Then
        assertFalse(canStart);
    }

    @Test
    void testCanStartMeeting_TimeBilling_NoAvailableMinutes() {
        // Given
        pmiRecord.setAvailableMinutes(0);
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));

        // When
        boolean canStart = pmiBillingModeService.canStartMeeting(1L);

        // Then
        assertFalse(canStart);
    }

    @Test
    void testCanStartMeeting_LongBilling_ValidWindow() {
        // Given
        pmiRecord.setBillingMode(PmiRecord.BillingMode.LONG);
        pmiRecord.setWindowExpireTime(LocalDateTime.now().plusHours(1));
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));

        // When
        boolean canStart = pmiBillingModeService.canStartMeeting(1L);

        // Then
        assertTrue(canStart);
    }

    @Test
    void testCanStartMeeting_LongBilling_ExpiredWindow() {
        // Given
        pmiRecord.setBillingMode(PmiRecord.BillingMode.LONG);
        pmiRecord.setWindowExpireTime(LocalDateTime.now().minusHours(1));
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));

        // When
        boolean canStart = pmiBillingModeService.canStartMeeting(1L);

        // Then
        assertFalse(canStart);
    }

    @Test
    void testCanStartMeeting_InactiveStatus() {
        // Given
        pmiRecord.setBillingStatus(PmiRecord.BillingStatus.SUSPENDED);
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));

        // When
        boolean canStart = pmiBillingModeService.canStartMeeting(1L);

        // Then
        assertFalse(canStart);
    }

    @Test
    void testGetBillingModeInfo() {
        // Given
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));

        // When
        String info = pmiBillingModeService.getBillingModeInfo(1L);

        // Then
        assertNotNull(info);
        assertTrue(info.contains("按时长"));
        assertTrue(info.contains("正常"));
        assertTrue(info.contains("100"));
    }
}
