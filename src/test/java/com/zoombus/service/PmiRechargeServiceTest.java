package com.zoombus.service;

import com.zoombus.entity.PmiBillingRecord;
import com.zoombus.entity.PmiRecord;
import com.zoombus.repository.PmiBillingRecordRepository;
import com.zoombus.repository.PmiRecordRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PmiRechargeServiceTest {

    @Mock
    private PmiRecordRepository pmiRecordRepository;

    @Mock
    private PmiBillingRecordRepository billingRecordRepository;

    @InjectMocks
    private PmiRechargeService pmiRechargeService;

    private PmiRecord pmiRecord;

    @BeforeEach
    void setUp() {
        pmiRecord = new PmiRecord();
        pmiRecord.setId(1L);
        pmiRecord.setUserId(100L);
        pmiRecord.setBillingMode(PmiRecord.BillingMode.BY_TIME);
        pmiRecord.setTotalMinutes(100);
        pmiRecord.setAvailableMinutes(50);
        pmiRecord.setOverdraftMinutes(0);
        pmiRecord.setPendingDeductMinutes(0);
    }

    @Test
    void testRechargeNormalCase() {
        // Given
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(billingRecordRepository.save(any(PmiBillingRecord.class))).thenReturn(new PmiBillingRecord());

        // When
        PmiRechargeService.RechargeResult result = pmiRechargeService.rechargePmi(1L, 50, "测试充值");

        // Then
        assertTrue(result.isSuccess());
        assertEquals("充值成功", result.getMessage());
        
        // 验证PMI记录更新
        verify(pmiRecordRepository).save(argThat(record -> 
            record.getAvailableMinutes() == 100 && // 50 + 50
            record.getTotalMinutes() == 150 // 100 + 50
        ));
        
        // 验证计费记录创建
        verify(billingRecordRepository).save(argThat(record -> 
            record.getTransactionType() == PmiBillingRecord.TransactionType.RECHARGE &&
            record.getAmountMinutes() == 50 &&
            record.getBalanceBefore() == 50 &&
            record.getBalanceAfter() == 100
        ));
    }

    @Test
    void testRechargeWithOverdraft() {
        // Given
        pmiRecord.setOverdraftMinutes(30);
        pmiRecord.setAvailableMinutes(0);
        
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(billingRecordRepository.save(any(PmiBillingRecord.class))).thenReturn(new PmiBillingRecord());

        // When
        PmiRechargeService.RechargeResult result = pmiRechargeService.rechargePmi(1L, 50, "测试充值");

        // Then
        assertTrue(result.isSuccess());
        
        // 验证分配策略：30分钟结清超额，20分钟增加到可用时长
        PmiRechargeService.RechargeAllocation allocation = result.getAllocation();
        assertEquals(30, allocation.getOverdraftSettled());
        assertEquals(20, allocation.getActualAdded());
        assertEquals(0, allocation.getFinalOverdraft());
        assertEquals(20, allocation.getFinalAvailable());
    }

    @Test
    void testRechargeWithPendingDeduct() {
        // Given
        pmiRecord.setPendingDeductMinutes(20);
        
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(billingRecordRepository.save(any(PmiBillingRecord.class))).thenReturn(new PmiBillingRecord());

        // When
        PmiRechargeService.RechargeResult result = pmiRechargeService.rechargePmi(1L, 50, "测试充值");

        // Then
        assertTrue(result.isSuccess());
        
        // 验证分配策略：20分钟结清待扣，30分钟增加到可用时长
        PmiRechargeService.RechargeAllocation allocation = result.getAllocation();
        assertEquals(20, allocation.getPendingDeductSettled());
        assertEquals(30, allocation.getActualAdded());
        assertEquals(0, allocation.getFinalPendingDeduct());
        assertEquals(80, allocation.getFinalAvailable()); // 50 + 30
    }

    @Test
    void testRechargeWithOverdraftAndPendingDeduct() {
        // Given
        pmiRecord.setOverdraftMinutes(20);
        pmiRecord.setPendingDeductMinutes(15);
        pmiRecord.setAvailableMinutes(0);
        
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(billingRecordRepository.save(any(PmiBillingRecord.class))).thenReturn(new PmiBillingRecord());

        // When
        PmiRechargeService.RechargeResult result = pmiRechargeService.rechargePmi(1L, 50, "测试充值");

        // Then
        assertTrue(result.isSuccess());
        
        // 验证分配策略：优先结清超额20分钟，再结清待扣15分钟，剩余15分钟增加到可用时长
        PmiRechargeService.RechargeAllocation allocation = result.getAllocation();
        assertEquals(20, allocation.getOverdraftSettled());
        assertEquals(15, allocation.getPendingDeductSettled());
        assertEquals(15, allocation.getActualAdded());
        assertEquals(0, allocation.getFinalOverdraft());
        assertEquals(0, allocation.getFinalPendingDeduct());
        assertEquals(15, allocation.getFinalAvailable());
    }

    @Test
    void testRechargePreview() {
        // Given
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));

        // When
        PmiRechargeService.RechargePreview preview = pmiRechargeService.getRechargePreview(1L, 50);

        // Then
        assertNotNull(preview);
        assertEquals(1L, preview.getPmiRecordId());
        assertEquals(50, preview.getRechargeMinutes());
        assertTrue(preview.isCanStartMeeting());
        
        PmiRechargeService.RechargeAllocation allocation = preview.getAllocation();
        assertEquals(50, allocation.getActualAdded());
        assertEquals(100, allocation.getFinalAvailable()); // 50 + 50
    }

    @Test
    void testRechargeInvalidAmount() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            pmiRechargeService.rechargePmi(1L, 0, "无效充值");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            pmiRechargeService.rechargePmi(1L, -10, "无效充值");
        });
    }

    @Test
    void testRechargeNonTimeBillingMode() {
        // Given
        pmiRecord.setBillingMode(PmiRecord.BillingMode.LONG);
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));

        // When & Then
        assertThrows(IllegalStateException.class, () -> {
            pmiRechargeService.rechargePmi(1L, 50, "测试充值");
        });
    }

    @Test
    void testRechargeNonExistentPmi() {
        // Given
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            pmiRechargeService.rechargePmi(1L, 50, "测试充值");
        });
    }

    @Test
    void testRechargeActivatesPmiWhenAvailableMinutesGreaterThanZero() {
        // Given - PMI状态为INACTIVE，可用时长为0
        pmiRecord.setStatus(PmiRecord.PmiStatus.INACTIVE);
        pmiRecord.setAvailableMinutes(0);

        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(billingRecordRepository.save(any(PmiBillingRecord.class))).thenReturn(new PmiBillingRecord());

        // When - 充值50分钟
        PmiRechargeService.RechargeResult result = pmiRechargeService.rechargePmi(1L, 50, "测试充值激活");

        // Then
        assertTrue(result.isSuccess());

        // 验证PMI状态被激活为ACTIVE
        verify(pmiRecordRepository).save(argThat(record ->
            record.getStatus() == PmiRecord.PmiStatus.ACTIVE &&
            record.getAvailableMinutes() == 50
        ));
    }

    @Test
    void testRechargeDoesNotChangeStatusWhenAlreadyActive() {
        // Given - PMI状态已经是ACTIVE
        pmiRecord.setStatus(PmiRecord.PmiStatus.ACTIVE);
        pmiRecord.setAvailableMinutes(10);

        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(billingRecordRepository.save(any(PmiBillingRecord.class))).thenReturn(new PmiBillingRecord());

        // When - 充值50分钟
        PmiRechargeService.RechargeResult result = pmiRechargeService.rechargePmi(1L, 50, "测试充值");

        // Then
        assertTrue(result.isSuccess());

        // 验证PMI状态保持ACTIVE
        verify(pmiRecordRepository).save(argThat(record ->
            record.getStatus() == PmiRecord.PmiStatus.ACTIVE &&
            record.getAvailableMinutes() == 60 // 10 + 50
        ));
    }

    @Test
    void testRechargeDoesNotActivateWhenFinalAvailableIsZero() {
        // Given - PMI状态为INACTIVE，有超额时长，充值刚好结清超额
        pmiRecord.setStatus(PmiRecord.PmiStatus.INACTIVE);
        pmiRecord.setAvailableMinutes(0);
        pmiRecord.setOverdraftMinutes(50);

        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(pmiRecordRepository.save(any(PmiRecord.class))).thenReturn(pmiRecord);
        when(billingRecordRepository.save(any(PmiBillingRecord.class))).thenReturn(new PmiBillingRecord());

        // When - 充值50分钟，刚好结清超额，最终可用时长为0
        PmiRechargeService.RechargeResult result = pmiRechargeService.rechargePmi(1L, 50, "测试充值结清超额");

        // Then
        assertTrue(result.isSuccess());

        // 验证PMI状态保持INACTIVE（因为最终可用时长为0）
        verify(pmiRecordRepository).save(argThat(record ->
            record.getStatus() == PmiRecord.PmiStatus.INACTIVE &&
            record.getAvailableMinutes() == 0 &&
            record.getOverdraftMinutes() == 0
        ));
    }
}
