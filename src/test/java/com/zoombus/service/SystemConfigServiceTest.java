package com.zoombus.service;

import com.zoombus.entity.SystemConfig;
import com.zoombus.repository.SystemConfigRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 系统配置服务测试类
 */
@ExtendWith(MockitoExtension.class)
class SystemConfigServiceTest {
    
    @Mock
    private SystemConfigRepository systemConfigRepository;
    
    @InjectMocks
    private SystemConfigService systemConfigService;
    
    private SystemConfig testConfig;
    
    @BeforeEach
    void setUp() {
        testConfig = new SystemConfig();
        testConfig.setId(1L);
        testConfig.setConfigKey("test.key");
        testConfig.setConfigValue("test.value");
        testConfig.setDescription("测试配置");
        testConfig.setConfigType(SystemConfig.ConfigType.STRING);
        testConfig.setIsActive(true);
    }
    
    @Test
    void testGetConfigValue() {
        // Given
        when(systemConfigRepository.findActiveByConfigKey("test.key"))
                .thenReturn(Optional.of(testConfig));
        
        // When
        String value = systemConfigService.getConfigValue("test.key");
        
        // Then
        assertEquals("test.value", value);
        verify(systemConfigRepository).findActiveByConfigKey("test.key");
    }
    
    @Test
    void testGetConfigValueWithDefault() {
        // Given
        when(systemConfigRepository.findActiveByConfigKey("nonexistent.key"))
                .thenReturn(Optional.empty());
        
        // When
        String value = systemConfigService.getConfigValue("nonexistent.key", "default.value");
        
        // Then
        assertEquals("default.value", value);
        verify(systemConfigRepository).findActiveByConfigKey("nonexistent.key");
    }
    
    @Test
    void testGetIntConfigValue() {
        // Given
        testConfig.setConfigValue("123");
        testConfig.setConfigType(SystemConfig.ConfigType.NUMBER);
        when(systemConfigRepository.findActiveByConfigKey("test.key"))
                .thenReturn(Optional.of(testConfig));
        
        // When
        Integer value = systemConfigService.getIntConfigValue("test.key");
        
        // Then
        assertEquals(123, value);
    }
    
    @Test
    void testGetDoubleConfigValue() {
        // Given
        testConfig.setConfigValue("123.45");
        testConfig.setConfigType(SystemConfig.ConfigType.NUMBER);
        when(systemConfigRepository.findActiveByConfigKey("test.key"))
                .thenReturn(Optional.of(testConfig));
        
        // When
        Double value = systemConfigService.getDoubleConfigValue("test.key");
        
        // Then
        assertEquals(123.45, value);
    }
    
    @Test
    void testGetBooleanConfigValue() {
        // Given
        testConfig.setConfigValue("true");
        testConfig.setConfigType(SystemConfig.ConfigType.BOOLEAN);
        when(systemConfigRepository.findActiveByConfigKey("test.key"))
                .thenReturn(Optional.of(testConfig));
        
        // When
        Boolean value = systemConfigService.getBooleanConfigValue("test.key");
        
        // Then
        assertTrue(value);
    }
    
    @Test
    void testSaveOrUpdateConfigNew() {
        // Given
        when(systemConfigRepository.findByConfigKey("new.key"))
                .thenReturn(Optional.empty());
        when(systemConfigRepository.save(any(SystemConfig.class)))
                .thenReturn(testConfig);
        
        // When
        SystemConfig result = systemConfigService.saveOrUpdateConfig(
                "new.key", "new.value", "新配置", SystemConfig.ConfigType.STRING, "TEST_USER");
        
        // Then
        assertNotNull(result);
        verify(systemConfigRepository).findByConfigKey("new.key");
        verify(systemConfigRepository).save(any(SystemConfig.class));
    }
    
    @Test
    void testSaveOrUpdateConfigExisting() {
        // Given
        when(systemConfigRepository.findByConfigKey("test.key"))
                .thenReturn(Optional.of(testConfig));
        when(systemConfigRepository.save(any(SystemConfig.class)))
                .thenReturn(testConfig);
        
        // When
        SystemConfig result = systemConfigService.saveOrUpdateConfig(
                "test.key", "updated.value", "更新配置", SystemConfig.ConfigType.STRING, "TEST_USER");
        
        // Then
        assertNotNull(result);
        verify(systemConfigRepository).findByConfigKey("test.key");
        verify(systemConfigRepository).save(any(SystemConfig.class));
    }
    
    @Test
    void testCreateConfigSuccess() {
        // Given
        when(systemConfigRepository.existsByConfigKey("new.key"))
                .thenReturn(false);
        when(systemConfigRepository.save(any(SystemConfig.class)))
                .thenReturn(testConfig);
        
        SystemConfig newConfig = new SystemConfig();
        newConfig.setConfigKey("new.key");
        newConfig.setConfigValue("new.value");
        
        // When
        SystemConfig result = systemConfigService.createConfig(newConfig);
        
        // Then
        assertNotNull(result);
        verify(systemConfigRepository).existsByConfigKey("new.key");
        verify(systemConfigRepository).save(newConfig);
    }
    
    @Test
    void testCreateConfigKeyExists() {
        // Given
        when(systemConfigRepository.existsByConfigKey("test.key"))
                .thenReturn(true);
        
        SystemConfig newConfig = new SystemConfig();
        newConfig.setConfigKey("test.key");
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            systemConfigService.createConfig(newConfig);
        });
        
        verify(systemConfigRepository).existsByConfigKey("test.key");
        verify(systemConfigRepository, never()).save(any());
    }
    
    @Test
    void testUpdateConfig() {
        // Given
        when(systemConfigRepository.findById(1L))
                .thenReturn(Optional.of(testConfig));
        when(systemConfigRepository.existsByConfigKey("updated.key"))
                .thenReturn(false);
        when(systemConfigRepository.save(any(SystemConfig.class)))
                .thenReturn(testConfig);
        
        SystemConfig updateConfig = new SystemConfig();
        updateConfig.setConfigKey("updated.key");
        updateConfig.setConfigValue("updated.value");
        updateConfig.setDescription("更新的配置");
        updateConfig.setConfigType(SystemConfig.ConfigType.STRING);
        updateConfig.setIsActive(true);
        updateConfig.setUpdatedBy("TEST_USER");
        
        // When
        SystemConfig result = systemConfigService.updateConfig(1L, updateConfig);
        
        // Then
        assertNotNull(result);
        verify(systemConfigRepository).findById(1L);
        verify(systemConfigRepository).save(any(SystemConfig.class));
    }
    
    @Test
    void testDeleteConfig() {
        // Given
        when(systemConfigRepository.findById(1L))
                .thenReturn(Optional.of(testConfig));
        
        // When
        systemConfigService.deleteConfig(1L);
        
        // Then
        verify(systemConfigRepository).findById(1L);
        verify(systemConfigRepository).delete(testConfig);
    }
    
    @Test
    void testToggleConfigStatus() {
        // Given
        when(systemConfigRepository.findById(1L))
                .thenReturn(Optional.of(testConfig));
        when(systemConfigRepository.save(any(SystemConfig.class)))
                .thenReturn(testConfig);
        
        // When
        SystemConfig result = systemConfigService.toggleConfigStatus(1L, "TEST_USER");
        
        // Then
        assertNotNull(result);
        verify(systemConfigRepository).findById(1L);
        verify(systemConfigRepository).save(any(SystemConfig.class));
    }
}
