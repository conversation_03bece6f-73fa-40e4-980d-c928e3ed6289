package com.zoombus.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.TaskExecutionRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TaskExecutionCacheService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class TaskExecutionCacheServiceTest {

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private ObjectMapper objectMapper;

    private TaskExecutionCacheService cacheService;

    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        cacheService = new TaskExecutionCacheService(redisTemplate, objectMapper);
    }

    @Test
    void testCacheTaskRecord() throws Exception {
        // Arrange
        TaskExecutionRecord record = new TaskExecutionRecord();
        record.setId(1L);
        record.setTaskName("testTask");
        record.setTaskType("TEST_TYPE");
        record.setStatus(TaskExecutionRecord.ExecutionStatus.SUCCESS);

        String expectedJson = "{\"id\":1,\"taskName\":\"testTask\"}";
        when(objectMapper.writeValueAsString(record)).thenReturn(expectedJson);

        // Act
        cacheService.cacheTaskRecord(record);

        // Assert
        verify(valueOperations).set(
                eq("task_execution:record:1"),
                eq(expectedJson),
                eq(600L),
                eq(TimeUnit.SECONDS)
        );
    }

    @Test
    void testGetCachedTaskRecord() throws Exception {
        // Arrange
        Long recordId = 1L;
        String cachedJson = "{\"id\":1,\"taskName\":\"testTask\"}";
        TaskExecutionRecord expectedRecord = new TaskExecutionRecord();
        expectedRecord.setId(recordId);
        expectedRecord.setTaskName("testTask");

        when(valueOperations.get("task_execution:record:1")).thenReturn(cachedJson);
        when(objectMapper.readValue(cachedJson, TaskExecutionRecord.class)).thenReturn(expectedRecord);

        // Act
        TaskExecutionRecord result = cacheService.getCachedTaskRecord(recordId);

        // Assert
        assertNotNull(result);
        assertEquals(recordId, result.getId());
        assertEquals("testTask", result.getTaskName());
    }

    @Test
    void testGetCachedTaskRecord_NotFound() {
        // Arrange
        Long recordId = 999L;
        when(valueOperations.get("task_execution:record:999")).thenReturn(null);

        // Act
        TaskExecutionRecord result = cacheService.getCachedTaskRecord(recordId);

        // Assert
        assertNull(result);
    }

    @Test
    void testCacheTaskStatistics() throws Exception {
        // Arrange
        String taskName = "testTask";
        Map<String, Object> statistics = Map.of(
                "totalExecutions", 10,
                "successExecutions", 8,
                "failedExecutions", 2
        );
        String expectedJson = "{\"totalExecutions\":10,\"successExecutions\":8,\"failedExecutions\":2}";
        when(objectMapper.writeValueAsString(statistics)).thenReturn(expectedJson);

        // Act
        cacheService.cacheTaskStatistics(taskName, statistics);

        // Assert
        verify(valueOperations).set(
                eq("task_stats:testTask"),
                eq(expectedJson),
                eq(300L),
                eq(TimeUnit.SECONDS)
        );
    }

    @Test
    void testGetCachedTaskStatistics() throws Exception {
        // Arrange
        String taskName = "testTask";
        String cachedJson = "{\"totalExecutions\":10,\"successExecutions\":8}";
        Map<String, Object> expectedStats = Map.of(
                "totalExecutions", 10,
                "successExecutions", 8
        );

        when(valueOperations.get("task_stats:testTask")).thenReturn(cachedJson);
        when(objectMapper.readValue(eq(cachedJson), eq(Map.class))).thenReturn(expectedStats);

        // Act
        Map<String, Object> result = cacheService.getCachedTaskStatistics(taskName);

        // Assert
        assertNotNull(result);
        assertEquals(10, result.get("totalExecutions"));
        assertEquals(8, result.get("successExecutions"));
    }

    @Test
    void testCacheRunningTasks() throws Exception {
        // Arrange
        List<String> runningTasks = Arrays.asList("task1", "task2", "task3");
        String expectedJson = "[\"task1\",\"task2\",\"task3\"]";
        when(objectMapper.writeValueAsString(runningTasks)).thenReturn(expectedJson);

        // Act
        cacheService.cacheRunningTasks(runningTasks);

        // Assert
        verify(valueOperations).set(
                eq("running_tasks"),
                eq(expectedJson),
                eq(60L),
                eq(TimeUnit.SECONDS)
        );
    }

    @Test
    void testGetCachedRunningTasks() throws Exception {
        // Arrange
        String cachedJson = "[\"task1\",\"task2\",\"task3\"]";
        List<String> expectedTasks = Arrays.asList("task1", "task2", "task3");

        when(valueOperations.get("running_tasks")).thenReturn(cachedJson);
        when(objectMapper.readValue(eq(cachedJson), eq(List.class))).thenReturn(expectedTasks);

        // Act
        List<String> result = cacheService.getCachedRunningTasks();

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("task1"));
        assertTrue(result.contains("task2"));
        assertTrue(result.contains("task3"));
    }

    @Test
    void testClearTaskCache() {
        // Arrange
        String taskName = "testTask";

        // Act
        cacheService.clearTaskCache(taskName);

        // Assert
        verify(redisTemplate).delete("task_stats:testTask");
        verify(redisTemplate).delete("task_execution:recent:testTask");
    }

    @Test
    void testIsCacheExists() {
        // Arrange
        String key = "test_key";
        when(redisTemplate.hasKey(key)).thenReturn(true);

        // Act
        boolean exists = cacheService.isCacheExists(key);

        // Assert
        assertTrue(exists);
        verify(redisTemplate).hasKey(key);
    }

    @Test
    void testGetCacheExpireTime() {
        // Arrange
        String key = "test_key";
        when(redisTemplate.getExpire(key, TimeUnit.SECONDS)).thenReturn(300L);

        // Act
        long expireTime = cacheService.getCacheExpireTime(key);

        // Assert
        assertEquals(300L, expireTime);
        verify(redisTemplate).getExpire(key, TimeUnit.SECONDS);
    }

    @Test
    void testCacheTaskRecord_JsonException() throws Exception {
        // Arrange
        TaskExecutionRecord record = new TaskExecutionRecord();
        record.setId(1L);
        when(objectMapper.writeValueAsString(record)).thenThrow(new RuntimeException("JSON error"));

        // Act & Assert - 应该不抛出异常，只记录日志
        assertDoesNotThrow(() -> cacheService.cacheTaskRecord(record));
        
        // 验证没有调用 Redis 操作
        verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testGetCachedTaskRecord_JsonException() throws Exception {
        // Arrange
        Long recordId = 1L;
        String invalidJson = "invalid json";
        when(valueOperations.get("task_execution:record:1")).thenReturn(invalidJson);
        when(objectMapper.readValue(invalidJson, TaskExecutionRecord.class))
                .thenThrow(new RuntimeException("JSON parse error"));

        // Act
        TaskExecutionRecord result = cacheService.getCachedTaskRecord(recordId);

        // Assert
        assertNull(result);
    }

    @Test
    void testGetCacheStatistics() {
        // Arrange
        when(redisTemplate.keys("task_execution:record:*")).thenReturn(java.util.Set.of("key1", "key2"));
        when(redisTemplate.keys("task_stats:*")).thenReturn(java.util.Set.of("key3"));
        when(redisTemplate.keys("task_execution:recent:*")).thenReturn(java.util.Set.of("key4", "key5"));
        when(redisTemplate.hasKey("running_tasks")).thenReturn(true);

        // Act
        Map<String, Object> stats = cacheService.getCacheStatistics();

        // Assert
        assertNotNull(stats);
        assertEquals(2L, stats.get("taskRecordCacheCount"));
        assertEquals(1L, stats.get("taskStatsCacheCount"));
        assertEquals(2L, stats.get("recentRecordsCacheCount"));
        assertEquals(true, stats.get("runningTasksCached"));
        assertNotNull(stats.get("cacheTimestamp"));
    }
}
