package com.zoombus.service;

import com.zoombus.config.ImmediateExecutionConfig;
import com.zoombus.entity.PmiScheduleWindow;
import com.zoombus.entity.PmiScheduleWindowTask;
import com.zoombus.repository.PmiScheduleWindowRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 任务执行策略判断器测试
 */
@ExtendWith(MockitoExtension.class)
class TaskExecutionStrategyDeciderTest {

    @Mock
    private ImmediateExecutionConfig config;

    @Mock
    private PmiScheduleWindowRepository windowRepository;

    private TaskExecutionStrategyDecider decider;

    private PmiScheduleWindow testWindow;

    @BeforeEach
    void setUp() {
        decider = new TaskExecutionStrategyDecider(config, windowRepository);
        
        // 设置默认配置
        when(config.isEnabled()).thenReturn(true);
        when(config.getToleranceMinutes()).thenReturn(2);
        when(config.getMaxDelayMinutes()).thenReturn(60);
        when(config.isEnablePastExecution()).thenReturn(true);
        
        // 创建测试窗口
        testWindow = new PmiScheduleWindow();
        testWindow.setId(1L);
        testWindow.setStartDateTime(LocalDateTime.now().plusHours(1));
        testWindow.setEndDateTime(LocalDateTime.now().plusHours(2));
        
        when(windowRepository.findById(1L)).thenReturn(Optional.of(testWindow));
    }

    @Test
    void testImmediateExecutionDisabled() {
        // 禁用立即执行
        when(config.isEnabled()).thenReturn(false);
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            1L, LocalDateTime.now(), PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);
        
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.SCHEDULED, strategy);
    }

    @Test
    void testCurrentTimeWithinTolerance() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime scheduledTime = now.minusMinutes(1); // 1分钟前
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            1L, scheduledTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);
        
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.IMMEDIATE, strategy);
    }

    @Test
    void testFutureTimeScheduled() {
        LocalDateTime futureTime = LocalDateTime.now().plusMinutes(10);
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            1L, futureTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);
        
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.SCHEDULED, strategy);
    }

    @Test
    void testPastOpenTaskStillValid() {
        LocalDateTime pastTime = LocalDateTime.now().minusMinutes(30);
        // 窗口还没结束
        testWindow.setEndDateTime(LocalDateTime.now().plusHours(1));
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            1L, pastTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);
        
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.IMMEDIATE, strategy);
    }

    @Test
    void testPastOpenTaskExpired() {
        LocalDateTime pastTime = LocalDateTime.now().minusMinutes(30);
        // 窗口已经结束
        testWindow.setEndDateTime(LocalDateTime.now().minusMinutes(10));
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            1L, pastTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);
        
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.EXPIRED, strategy);
    }

    @Test
    void testPastCloseTaskWithinMaxDelay() {
        LocalDateTime pastTime = LocalDateTime.now().minusMinutes(30);
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            1L, pastTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_CLOSE);
        
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.IMMEDIATE, strategy);
    }

    @Test
    void testPastCloseTaskExceedsMaxDelay() {
        LocalDateTime pastTime = LocalDateTime.now().minusMinutes(90); // 超过60分钟
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            1L, pastTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_CLOSE);
        
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.EXPIRED, strategy);
    }

    @Test
    void testPastExecutionDisabled() {
        when(config.isEnablePastExecution()).thenReturn(false);
        LocalDateTime pastTime = LocalDateTime.now().minusMinutes(10);
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            1L, pastTime, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);
        
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.EXPIRED, strategy);
    }

    @Test
    void testWindowNotFound() {
        when(windowRepository.findById(999L)).thenReturn(Optional.empty());
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            999L, LocalDateTime.now(), PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);
        
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.SCHEDULED, strategy);
    }

    @Test
    void testIsWindowStillValidForOpenTask() {
        // 窗口还没结束
        testWindow.setEndDateTime(LocalDateTime.now().plusHours(1));
        
        boolean isValid = decider.isWindowStillValid(1L, PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);
        
        assertTrue(isValid);
    }

    @Test
    void testIsWindowStillValidForCloseTask() {
        // 窗口已经开始
        testWindow.setStartDateTime(LocalDateTime.now().minusMinutes(30));
        
        boolean isValid = decider.isWindowStillValid(1L, PmiScheduleWindowTask.TaskType.PMI_WINDOW_CLOSE);
        
        assertTrue(isValid);
    }

    @Test
    void testGetTaskDelayInfo() {
        LocalDateTime scheduledTime = LocalDateTime.now().minusMinutes(15);
        
        TaskExecutionStrategyDecider.TaskDelayInfo delayInfo = decider.getTaskDelayInfo(scheduledTime);
        
        assertNotNull(delayInfo);
        assertEquals(scheduledTime, delayInfo.getScheduledTime());
        assertTrue(delayInfo.getIsDelayed());
        assertEquals(15, delayInfo.getDelayMinutes());
        assertFalse(delayInfo.getIsWithinTolerance()); // 15分钟 > 2分钟容差
        assertTrue(delayInfo.getIsWithinMaxDelay()); // 15分钟 < 60分钟最大延迟
    }

    @Test
    void testExceptionHandling() {
        when(windowRepository.findById(1L)).thenThrow(new RuntimeException("Database error"));
        
        TaskExecutionStrategyDecider.ExecutionStrategy strategy = decider.decideStrategy(
            1L, LocalDateTime.now(), PmiScheduleWindowTask.TaskType.PMI_WINDOW_OPEN);
        
        // 异常时应该降级到调度执行
        assertEquals(TaskExecutionStrategyDecider.ExecutionStrategy.SCHEDULED, strategy);
    }
}
