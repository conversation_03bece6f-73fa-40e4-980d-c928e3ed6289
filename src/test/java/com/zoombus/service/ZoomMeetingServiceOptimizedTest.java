package com.zoombus.service;

import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.MeetingRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.repository.ZoomUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ZoomMeetingService优化后的测试类
 * 测试并发安全性、事务一致性和错误处理
 */
@ExtendWith(MockitoExtension.class)
class ZoomMeetingServiceOptimizedTest {

    @Mock
    private ZoomMeetingRepository zoomMeetingRepository;
    
    @Mock
    private PmiRecordRepository pmiRecordRepository;
    
    @Mock
    private ZoomUserRepository zoomUserRepository;
    
    @Mock
    private MeetingRepository meetingRepository;
    
    @Mock
    private ZoomUserPmiService zoomUserPmiService;
    
    @Mock
    private BillingMonitorService billingMonitorService;
    
    @Mock
    private MeetingSettlementService meetingSettlementService;
    
    @Mock
    private ZoomApiService zoomApiService;
    
    @Mock
    private TransactionMonitorService transactionMonitorService;
    
    @Mock
    private AsyncMeetingProcessService asyncMeetingProcessService;
    
    @Mock
    private MeetingLifecycleManager meetingLifecycleManager;
    
    @Mock
    private DistributedLockManager distributedLockManager;

    @InjectMocks
    private ZoomMeetingService zoomMeetingService;

    private String testMeetingUuid;
    private String testMeetingId;
    private String testHostId;
    private String testTopic;

    @BeforeEach
    void setUp() {
        testMeetingUuid = "test-uuid-12345";
        testMeetingId = "123456789";
        testHostId = "host-123";
        testTopic = "测试会议";
    }

    @Test
    void testHandleMeetingStarted_WithDistributedLock() {
        // 模拟分布式锁执行
        when(distributedLockManager.executeWithMeetingUuidLock(
            eq(testMeetingUuid), 
            any(), 
            any()
        )).thenAnswer(invocation -> {
            Runnable task = invocation.getArgument(2);
            task.run();
            return null;
        });

        // 模拟未找到现有记录
        when(zoomMeetingRepository.findByZoomMeetingUuid(testMeetingUuid))
            .thenReturn(Optional.empty());
        when(zoomMeetingRepository.findByZoomMeetingIdAndHostIdAndStatusIn(
            eq(testMeetingId), eq(testHostId), any()))
            .thenReturn(Arrays.asList());

        // 模拟PMI记录查找
        PmiRecord pmiRecord = new PmiRecord();
        pmiRecord.setId(1L);
        pmiRecord.setBillingMode(PmiRecord.BillingMode.BY_TIME);
        when(pmiRecordRepository.findByPmiNumber(testMeetingId))
            .thenReturn(Optional.of(pmiRecord));

        // 模拟保存操作
        ZoomMeeting savedMeeting = new ZoomMeeting();
        savedMeeting.setId(1L);
        savedMeeting.setZoomMeetingUuid(testMeetingUuid);
        savedMeeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
        savedMeeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);
        when(zoomMeetingRepository.save(any(ZoomMeeting.class)))
            .thenReturn(savedMeeting);

        // 执行测试
        assertDoesNotThrow(() -> {
            zoomMeetingService.handleMeetingStarted(testMeetingUuid, testMeetingId, testHostId, testTopic);
        });

        // 验证分布式锁被调用
        verify(distributedLockManager).executeWithMeetingUuidLock(
            eq(testMeetingUuid), any(), any());
        
        // 验证保存操作被调用
        verify(zoomMeetingRepository).save(any(ZoomMeeting.class));
        
        // 验证计费监控被启动
        verify(billingMonitorService).startBillingMonitor(1L);
    }

    @Test
    void testHandleMeetingStarted_ParameterValidation() {
        // 测试空UUID
        when(distributedLockManager.executeWithMeetingUuidLock(any(), any(), any()))
            .thenAnswer(invocation -> {
                Runnable task = invocation.getArgument(2);
                task.run();
                return null;
            });

        assertThrows(IllegalArgumentException.class, () -> {
            zoomMeetingService.handleMeetingStarted(null, testMeetingId, testHostId, testTopic);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            zoomMeetingService.handleMeetingStarted("", testMeetingId, testHostId, testTopic);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            zoomMeetingService.handleMeetingStarted(testMeetingUuid, null, testHostId, testTopic);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            zoomMeetingService.handleMeetingStarted(testMeetingUuid, testMeetingId, null, testTopic);
        });
    }

    @Test
    void testHandleMeetingStarted_UpdateExistingMeeting() {
        // 模拟分布式锁执行
        when(distributedLockManager.executeWithMeetingUuidLock(any(), any(), any()))
            .thenAnswer(invocation -> {
                Runnable task = invocation.getArgument(2);
                task.run();
                return null;
            });

        // 模拟找到现有记录
        ZoomMeeting existingMeeting = new ZoomMeeting();
        existingMeeting.setId(1L);
        existingMeeting.setZoomMeetingUuid(testMeetingUuid);
        existingMeeting.setStatus(ZoomMeeting.MeetingStatus.WAITING);
        existingMeeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);
        
        when(zoomMeetingRepository.findByZoomMeetingUuid(testMeetingUuid))
            .thenReturn(Optional.of(existingMeeting));
        
        when(zoomMeetingRepository.save(any(ZoomMeeting.class)))
            .thenReturn(existingMeeting);

        // 执行测试
        assertDoesNotThrow(() -> {
            zoomMeetingService.handleMeetingStarted(testMeetingUuid, testMeetingId, testHostId, testTopic);
        });

        // 验证状态被更新为STARTED
        verify(zoomMeetingRepository).save(argThat(meeting -> 
            meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED));
        
        // 验证计费监控被启动
        verify(billingMonitorService).startBillingMonitor(1L);
    }

    @Test
    void testHandleMeetingStarted_IdempotentBehavior() {
        // 模拟分布式锁执行
        when(distributedLockManager.executeWithMeetingUuidLock(any(), any(), any()))
            .thenAnswer(invocation -> {
                Runnable task = invocation.getArgument(2);
                task.run();
                return null;
            });

        // 模拟找到已经是STARTED状态的会议
        ZoomMeeting startedMeeting = new ZoomMeeting();
        startedMeeting.setId(1L);
        startedMeeting.setZoomMeetingUuid(testMeetingUuid);
        startedMeeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
        startedMeeting.setTopic(testTopic);
        
        when(zoomMeetingRepository.findByZoomMeetingUuid(testMeetingUuid))
            .thenReturn(Optional.of(startedMeeting));

        // 执行测试
        assertDoesNotThrow(() -> {
            zoomMeetingService.handleMeetingStarted(testMeetingUuid, testMeetingId, testHostId, testTopic);
        });

        // 验证幂等性：不会重复保存或启动计费监控
        verify(zoomMeetingRepository, never()).save(any());
        verify(billingMonitorService, never()).startBillingMonitor(any());
    }

    @Test
    void testConcurrentMeetingStarted() throws Exception {
        // 模拟分布式锁确保串行执行
        when(distributedLockManager.executeWithMeetingUuidLock(any(), any(), any()))
            .thenAnswer(invocation -> {
                Runnable task = invocation.getArgument(2);
                // 模拟锁的串行执行
                synchronized (this) {
                    task.run();
                }
                return null;
            });

        when(zoomMeetingRepository.findByZoomMeetingUuid(testMeetingUuid))
            .thenReturn(Optional.empty());
        when(zoomMeetingRepository.findByZoomMeetingIdAndHostIdAndStatusIn(any(), any(), any()))
            .thenReturn(Arrays.asList());

        ZoomMeeting savedMeeting = new ZoomMeeting();
        savedMeeting.setId(1L);
        when(zoomMeetingRepository.save(any(ZoomMeeting.class)))
            .thenReturn(savedMeeting);

        // 并发执行相同的会议开始事件
        ExecutorService executor = Executors.newFixedThreadPool(5);
        CompletableFuture<Void>[] futures = new CompletableFuture[5];
        
        for (int i = 0; i < 5; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                assertDoesNotThrow(() -> {
                    zoomMeetingService.handleMeetingStarted(testMeetingUuid, testMeetingId, testHostId, testTopic);
                });
            }, executor);
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures).get();
        executor.shutdown();

        // 验证分布式锁被调用了5次（每个并发请求一次）
        verify(distributedLockManager, times(5))
            .executeWithMeetingUuidLock(eq(testMeetingUuid), any(), any());
    }
}
