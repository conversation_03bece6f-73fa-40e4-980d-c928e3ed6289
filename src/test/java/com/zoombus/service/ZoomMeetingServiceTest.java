package com.zoombus.service;

import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ZoomMeetingServiceTest {

    @Mock
    private ZoomMeetingRepository zoomMeetingRepository;

    @Mock
    private PmiRecordRepository pmiRecordRepository;

    @Mock
    private ZoomUserPmiService zoomUserPmiService;

    @Mock
    private BillingMonitorService billingMonitorService;

    @Mock
    private MeetingSettlementService meetingSettlementService;

    @InjectMocks
    private ZoomMeetingService zoomMeetingService;

    private PmiRecord pmiRecord;
    private ZoomMeeting zoomMeeting;
    private ZoomUser zoomUser;

    @BeforeEach
    void setUp() {
        pmiRecord = new PmiRecord();
        pmiRecord.setId(1L);
        pmiRecord.setPmiNumber("123456789");
        pmiRecord.setBillingMode(PmiRecord.BillingMode.BY_TIME);

        zoomMeeting = new ZoomMeeting();
        zoomMeeting.setId(1L);
        zoomMeeting.setPmiRecordId(1L);
        zoomMeeting.setZoomMeetingUuid("test-uuid");
        zoomMeeting.setZoomMeetingId("123456789");
        zoomMeeting.setTopic("测试会议");
        zoomMeeting.setHostId("host123");
        zoomMeeting.setStatus(ZoomMeeting.MeetingStatus.WAITING);
        zoomMeeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);

        zoomUser = new ZoomUser();
        zoomUser.setId(1L);
        zoomUser.setEmail("<EMAIL>");
    }

    @Test
    void testCreateMeetingRecord() {
        // Given
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(zoomMeeting);
        when(zoomUserPmiService.assignZoomUserForMeeting(anyLong(), anyString())).thenReturn(zoomUser);

        // When
        ZoomMeeting result = zoomMeetingService.createMeetingRecord(
            1L, "test-uuid", "123456789", "测试会议", "host123");

        // Then
        assertNotNull(result);
        assertEquals("test-uuid", result.getZoomMeetingUuid());
        assertEquals("123456789", result.getZoomMeetingId());
        assertEquals("测试会议", result.getTopic());
        assertEquals(ZoomMeeting.MeetingStatus.WAITING, result.getStatus());
        assertEquals(PmiRecord.BillingMode.BY_TIME, result.getBillingMode());

        verify(zoomMeetingRepository, times(2)).save(any(ZoomMeeting.class)); // 创建时保存一次，分配ZoomUser后再保存一次
        verify(zoomUserPmiService).assignZoomUserForMeeting(anyLong(), eq("123456789"));
    }

    @Test
    void testCreateMeetingRecordWithZoomUserAssignmentFailure() {
        // Given
        when(pmiRecordRepository.findById(1L)).thenReturn(Optional.of(pmiRecord));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(zoomMeeting);
        when(zoomUserPmiService.assignZoomUserForMeeting(anyLong(), anyString()))
            .thenThrow(new RuntimeException("ZoomUser分配失败"));

        // When
        ZoomMeeting result = zoomMeetingService.createMeetingRecord(
            1L, "test-uuid", "123456789", "测试会议", "host123");

        // Then
        assertNotNull(result);
        verify(zoomMeetingRepository, times(2)).save(any(ZoomMeeting.class)); // 创建时保存一次，记录错误后再保存一次
        verify(zoomUserPmiService).assignZoomUserForMeeting(anyLong(), eq("123456789"));
    }

    @Test
    void testHandleMeetingStarted() {
        // Given
        when(zoomMeetingRepository.findByZoomMeetingUuid("test-uuid")).thenReturn(Optional.of(zoomMeeting));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(zoomMeeting);

        // When
        zoomMeetingService.handleMeetingStarted("test-uuid");

        // Then
        verify(zoomMeetingRepository).save(argThat(meeting ->
            meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED &&
            meeting.getStartTime() != null
        ));
        verify(billingMonitorService).startBillingMonitor(1L);
    }

    @Test
    void testHandleMeetingStartedForLongBilling() {
        // Given
        zoomMeeting.setBillingMode(PmiRecord.BillingMode.LONG);
        when(zoomMeetingRepository.findByZoomMeetingUuid("test-uuid")).thenReturn(Optional.of(zoomMeeting));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(zoomMeeting);

        // When
        zoomMeetingService.handleMeetingStarted("test-uuid");

        // Then
        verify(zoomMeetingRepository).save(argThat(meeting ->
            meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED
        ));
        verify(billingMonitorService, never()).startBillingMonitor(anyLong()); // 按时段计费不需要启动监控
    }

    @Test
    void testHandleMeetingEnded() {
        // Given
        zoomMeeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
        zoomMeeting.setStartTime(LocalDateTime.now().minusMinutes(30));
        
        when(zoomMeetingRepository.findByZoomMeetingUuid("test-uuid")).thenReturn(Optional.of(zoomMeeting));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(zoomMeeting);

        // When
        zoomMeetingService.handleMeetingEnded("test-uuid");

        // Then
        verify(zoomMeetingRepository).save(argThat(meeting -> 
            meeting.getStatus() == ZoomMeeting.MeetingStatus.ENDED &&
            meeting.getEndTime() != null &&
            meeting.getDurationMinutes() > 0
        ));
        verify(billingMonitorService).stopBillingMonitor(1L);
        verify(meetingSettlementService).settleMeeting(1L);
        verify(zoomUserPmiService).releaseZoomUser(1L);
    }

    @Test
    void testHandleMeetingEndedWithSettlementFailure() {
        // Given
        zoomMeeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
        zoomMeeting.setStartTime(LocalDateTime.now().minusMinutes(30));
        
        when(zoomMeetingRepository.findByZoomMeetingUuid("test-uuid")).thenReturn(Optional.of(zoomMeeting));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(zoomMeeting);
        when(meetingSettlementService.settleMeeting(1L)).thenThrow(new RuntimeException("结算失败"));

        // When
        zoomMeetingService.handleMeetingEnded("test-uuid");

        // Then
        verify(zoomMeetingRepository).save(any(ZoomMeeting.class));
        verify(billingMonitorService).stopBillingMonitor(1L);
        verify(meetingSettlementService).settleMeeting(1L);
        verify(zoomUserPmiService).releaseZoomUser(1L); // 即使结算失败也要释放ZoomUser
    }

    @Test
    void testEndMeeting() {
        // Given
        zoomMeeting.setStatus(ZoomMeeting.MeetingStatus.STARTED);
        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(zoomMeeting));
        when(zoomMeetingRepository.findByZoomMeetingUuid("test-uuid")).thenReturn(Optional.of(zoomMeeting));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(zoomMeeting);

        // When
        zoomMeetingService.endMeeting(1L);

        // Then
        // 验证会议结束处理逻辑被调用
        verify(zoomMeetingRepository).findById(1L);
    }

    @Test
    void testEndMeetingWithInvalidStatus() {
        // Given
        zoomMeeting.setStatus(ZoomMeeting.MeetingStatus.ENDED);
        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(zoomMeeting));

        // When & Then
        assertThrows(IllegalStateException.class, () -> {
            zoomMeetingService.endMeeting(1L);
        });
    }

    @Test
    void testGetMeetingById() {
        // Given
        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.of(zoomMeeting));

        // When
        ZoomMeeting result = zoomMeetingService.getMeetingById(1L);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("test-uuid", result.getZoomMeetingUuid());
    }

    @Test
    void testGetMeetingByIdNotFound() {
        // Given
        when(zoomMeetingRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            zoomMeetingService.getMeetingById(1L);
        });
    }

    @Test
    void testGetPmiMeetingStatistics() {
        // Given
        when(zoomMeetingRepository.countByPmiRecordId(1L)).thenReturn(5L);
        when(zoomMeetingRepository.sumDurationMinutesByPmiRecordId(1L)).thenReturn(150L);

        // When
        ZoomMeetingService.MeetingStatistics stats = zoomMeetingService.getPmiMeetingStatistics(1L);

        // Then
        assertNotNull(stats);
        assertEquals(5L, stats.getTotalMeetings());
        assertEquals(150L, stats.getTotalMinutes());
        assertEquals(30.0, stats.getAverageMinutes()); // 150 / 5 = 30
    }
}
