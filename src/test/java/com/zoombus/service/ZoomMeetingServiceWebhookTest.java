package com.zoombus.service;

import com.zoombus.entity.Meeting;
import com.zoombus.entity.PmiRecord;
import com.zoombus.entity.ZoomMeeting;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.MeetingRepository;
import com.zoombus.repository.PmiRecordRepository;
import com.zoombus.repository.ZoomMeetingRepository;
import com.zoombus.repository.ZoomUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ZoomMeetingServiceWebhookTest {

    @Mock
    private ZoomMeetingRepository zoomMeetingRepository;

    @Mock
    private PmiRecordRepository pmiRecordRepository;

    @Mock
    private MeetingRepository meetingRepository;

    @Mock
    private ZoomUserRepository zoomUserRepository;

    @Mock
    private BillingMonitorService billingMonitorService;

    @Mock
    private ZoomUserPmiService zoomUserPmiService;

    @Mock
    private MeetingSettlementService meetingSettlementService;

    @Mock
    private ZoomApiService zoomApiService;

    @InjectMocks
    private ZoomMeetingService zoomMeetingService;

    private String testUuid;
    private String testMeetingId;
    private String testHostId;
    private String testTopic;

    @BeforeEach
    void setUp() {
        testUuid = "test-uuid-123";
        testMeetingId = "1234567890";
        testHostId = "host123";
        testTopic = "测试会议";
    }

    @Test
    void testHandleMeetingStarted_PmiMeeting() {
        // Given - PMI会议场景
        PmiRecord pmiRecord = new PmiRecord();
        pmiRecord.setId(100L);
        pmiRecord.setPmiNumber(testMeetingId);
        pmiRecord.setBillingMode(PmiRecord.BillingMode.BY_TIME);

        ZoomMeeting savedMeeting = new ZoomMeeting();
        savedMeeting.setId(200L);

        when(zoomMeetingRepository.findByZoomMeetingUuid(testUuid)).thenReturn(Optional.empty());
        when(pmiRecordRepository.findByPmiNumber(testMeetingId)).thenReturn(Optional.of(pmiRecord));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(savedMeeting);

        // When
        zoomMeetingService.handleMeetingStarted(testUuid, testMeetingId, testHostId, testTopic);

        // Then
        verify(zoomMeetingRepository).save(argThat(meeting -> 
            meeting.getZoomMeetingUuid().equals(testUuid) &&
            meeting.getZoomMeetingId().equals(testMeetingId) &&
            meeting.getPmiRecordId().equals(100L) &&
            meeting.getBillingMode() == PmiRecord.BillingMode.BY_TIME &&
            meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED
        ));
        
        verify(billingMonitorService).startBillingMonitor(200L);
    }

    @Test
    void testHandleMeetingStarted_ScheduledMeeting() {
        // Given - 安排会议场景
        Meeting scheduledMeeting = new Meeting();
        scheduledMeeting.setId(300L);
        scheduledMeeting.setZoomMeetingId(testMeetingId);
        scheduledMeeting.setCreatorUserId(400L);
        scheduledMeeting.setTopic("安排的会议主题");

        ZoomMeeting savedMeeting = new ZoomMeeting();
        savedMeeting.setId(500L);

        when(zoomMeetingRepository.findByZoomMeetingUuid(testUuid)).thenReturn(Optional.empty());
        when(pmiRecordRepository.findByPmiNumber(testMeetingId)).thenReturn(Optional.empty());
        when(meetingRepository.findByZoomMeetingId(testMeetingId)).thenReturn(Optional.of(scheduledMeeting));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(savedMeeting);

        // When
        zoomMeetingService.handleMeetingStarted(testUuid, testMeetingId, testHostId, null); // topic为null

        // Then
        verify(zoomMeetingRepository).save(argThat(meeting -> 
            meeting.getZoomMeetingUuid().equals(testUuid) &&
            meeting.getZoomMeetingId().equals(testMeetingId) &&
            meeting.getPmiRecordId() == null &&
            meeting.getBillingMode() == PmiRecord.BillingMode.BY_TIME &&
            meeting.getTopic().equals("安排的会议主题") && // 应该使用安排会议的主题
            meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED
        ));
        
        // 安排会议不启动计费监控
        verify(billingMonitorService, never()).startBillingMonitor(anyLong());
    }

    @Test
    void testHandleMeetingStarted_OtherMeeting() {
        // Given - 其他类型会议场景（如Zoom App直接创建）
        ZoomMeeting savedMeeting = new ZoomMeeting();
        savedMeeting.setId(600L);

        when(zoomMeetingRepository.findByZoomMeetingUuid(testUuid)).thenReturn(Optional.empty());
        when(pmiRecordRepository.findByPmiNumber(testMeetingId)).thenReturn(Optional.empty());
        when(meetingRepository.findByZoomMeetingId(testMeetingId)).thenReturn(Optional.empty());
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(savedMeeting);

        // When
        zoomMeetingService.handleMeetingStarted(testUuid, testMeetingId, testHostId, testTopic);

        // Then
        verify(zoomMeetingRepository).save(argThat(meeting -> 
            meeting.getZoomMeetingUuid().equals(testUuid) &&
            meeting.getZoomMeetingId().equals(testMeetingId) &&
            meeting.getPmiRecordId() == null &&
            meeting.getBillingMode() == PmiRecord.BillingMode.BY_TIME &&
            meeting.getTopic().equals(testTopic) &&
            meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED
        ));
        
        // 其他会议不启动计费监控
        verify(billingMonitorService, never()).startBillingMonitor(anyLong());
    }

    @Test
    void testHandleMeetingStarted_ExistingMeeting() {
        // Given - 会议记录已存在的场景
        ZoomMeeting existingMeeting = new ZoomMeeting();
        existingMeeting.setId(700L);
        existingMeeting.setStatus(ZoomMeeting.MeetingStatus.WAITING);

        when(zoomMeetingRepository.findByZoomMeetingUuid(testUuid)).thenReturn(Optional.of(existingMeeting));

        // When
        zoomMeetingService.handleMeetingStarted(testUuid, testMeetingId, testHostId, testTopic);

        // Then
        verify(zoomMeetingRepository).save(argThat(meeting ->
            meeting.getId().equals(700L) &&
            meeting.getStatus() == ZoomMeeting.MeetingStatus.STARTED &&
            meeting.getStartTime() != null
        ));
        
        // 不应该创建新记录
        verify(zoomMeetingRepository, never()).save(argThat(meeting -> meeting.getId() == null));
    }

    @Test
    void testHandleMeetingStarted_WithZoomUserInfo() {
        // Given - 包含ZoomUser信息的场景
        ZoomUser zoomUser = new ZoomUser();
        zoomUser.setId(800L);
        zoomUser.setEmail("<EMAIL>");

        ZoomMeeting savedMeeting = new ZoomMeeting();
        savedMeeting.setId(900L);

        when(zoomMeetingRepository.findByZoomMeetingUuid(testUuid)).thenReturn(Optional.empty());
        when(pmiRecordRepository.findByPmiNumber(testMeetingId)).thenReturn(Optional.empty());
        when(meetingRepository.findByZoomMeetingId(testMeetingId)).thenReturn(Optional.empty());
        when(zoomUserRepository.findByZoomUserId(testHostId)).thenReturn(java.util.Arrays.asList(zoomUser));
        when(zoomMeetingRepository.save(any(ZoomMeeting.class))).thenReturn(savedMeeting);

        // When
        zoomMeetingService.handleMeetingStarted(testUuid, testMeetingId, testHostId, testTopic);

        // Then
        verify(zoomMeetingRepository).save(argThat(meeting -> 
            meeting.getAssignedZoomUserId().equals(800L) &&
            meeting.getAssignedZoomUserEmail().equals("<EMAIL>") &&
            meeting.getHostId().equals(testHostId)
        ));
    }
}
