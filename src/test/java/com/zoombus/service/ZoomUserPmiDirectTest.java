package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.ZoomUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ZoomUserPmiDirectTest {

    @Mock
    private ZoomUserRepository zoomUserRepository;

    @InjectMocks
    private ZoomUserService zoomUserService;

    private ObjectMapper objectMapper;
    private ZoomAuth testZoomAuth;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        
        testZoomAuth = new ZoomAuth();
        testZoomAuth.setId(1L);
        testZoomAuth.setAccountName("测试账号");
        testZoomAuth.setZoomAccountId("test_account_id");
    }

    @Test
    void testUpdateZoomUserFromJsonWithPmi() throws Exception {
        // 创建一个新用户（original_pmi为空）
        ZoomUser user = new ZoomUser();
        user.setEmail("<EMAIL>");
        user.setOriginalPmi(null);
        user.setCurrentPmi(null);

        // 模拟包含PMI的API数据
        String userJsonWithPmi = "{" +
            "\"id\": \"test_user_123\"," +
            "\"email\": \"<EMAIL>\"," +
            "\"first_name\": \"Test\"," +
            "\"last_name\": \"User\"," +
            "\"type\": 1," +
            "\"status\": \"active\"," +
            "\"pmi\": \"**********\"," +
            "\"timezone\": \"Asia/Shanghai\"," +
            "\"created_at\": \"2024-01-01T10:00:00Z\"" +
            "}";
        JsonNode userNode = objectMapper.readTree(userJsonWithPmi);

        // 调用updateZoomUserFromJson方法
        invokeUpdateZoomUserFromJson(user, userNode);

        // 验证PMI字段是否正确设置
        assertEquals("**********", user.getOriginalPmi());
        assertEquals("**********", user.getCurrentPmi());
        assertNotNull(user.getPmiUpdatedAt());
        
        System.out.println("✅ PMI处理逻辑测试成功:");
        System.out.println("  - Email: " + user.getEmail());
        System.out.println("  - Original PMI: " + user.getOriginalPmi());
        System.out.println("  - Current PMI: " + user.getCurrentPmi());
        System.out.println("  - Updated At: " + user.getPmiUpdatedAt());
    }

    @Test
    void testUpdateZoomUserFromJsonWithoutPmi() throws Exception {
        // 创建一个新用户
        ZoomUser user = new ZoomUser();
        user.setEmail("<EMAIL>");
        user.setOriginalPmi(null);
        user.setCurrentPmi(null);

        // 模拟不包含PMI的API数据
        String userJsonWithoutPmi = "{" +
            "\"id\": \"test_user_456\"," +
            "\"email\": \"<EMAIL>\"," +
            "\"first_name\": \"Test2\"," +
            "\"last_name\": \"User2\"," +
            "\"type\": 1," +
            "\"status\": \"active\"," +
            "\"timezone\": \"Asia/Shanghai\"," +
            "\"created_at\": \"2024-01-01T10:00:00Z\"" +
            "}";
        JsonNode userNode = objectMapper.readTree(userJsonWithoutPmi);

        // 调用updateZoomUserFromJson方法
        invokeUpdateZoomUserFromJson(user, userNode);

        // 验证PMI字段保持为空
        assertNull(user.getOriginalPmi());
        assertNull(user.getCurrentPmi());
        assertNull(user.getPmiUpdatedAt());
        
        System.out.println("✅ 无PMI字段处理测试成功:");
        System.out.println("  - Email: " + user.getEmail());
        System.out.println("  - Original PMI: " + user.getOriginalPmi());
        System.out.println("  - Current PMI: " + user.getCurrentPmi());
    }

    @Test
    void testUpdateExistingUserWithEmptyOriginalPmi() throws Exception {
        // 创建一个现有用户（original_pmi为空）
        ZoomUser user = new ZoomUser();
        user.setEmail("<EMAIL>");
        user.setOriginalPmi(null);
        user.setCurrentPmi(null);

        // 模拟包含PMI的API数据
        String userJsonWithPmi = "{" +
            "\"id\": \"test_user_789\"," +
            "\"email\": \"<EMAIL>\"," +
            "\"first_name\": \"Test3\"," +
            "\"last_name\": \"User3\"," +
            "\"type\": 1," +
            "\"status\": \"active\"," +
            "\"pmi\": \"9876543210\"," +
            "\"timezone\": \"Asia/Shanghai\"," +
            "\"created_at\": \"2024-01-01T10:00:00Z\"" +
            "}";
        JsonNode userNode = objectMapper.readTree(userJsonWithPmi);

        // 调用updateZoomUserFromJson方法
        invokeUpdateZoomUserFromJson(user, userNode);

        // 验证PMI字段是否正确补全
        assertEquals("9876543210", user.getOriginalPmi());
        assertEquals("9876543210", user.getCurrentPmi());
        assertNotNull(user.getPmiUpdatedAt());
        
        System.out.println("✅ 现有用户PMI补全测试成功:");
        System.out.println("  - Email: " + user.getEmail());
        System.out.println("  - Original PMI: " + user.getOriginalPmi());
        System.out.println("  - Current PMI: " + user.getCurrentPmi());
        System.out.println("  - Updated At: " + user.getPmiUpdatedAt());
    }

    @Test
    void testUpdateExistingUserWithExistingOriginalPmi() throws Exception {
        // 创建一个现有用户（已有original_pmi）
        ZoomUser user = new ZoomUser();
        user.setEmail("<EMAIL>");
        user.setOriginalPmi("5555555555");
        user.setCurrentPmi("5555555555");
        user.setPmiUpdatedAt(LocalDateTime.now().minusDays(1));

        // 模拟包含不同PMI的API数据
        String userJsonWithPmi = "{" +
            "\"id\": \"test_user_999\"," +
            "\"email\": \"<EMAIL>\"," +
            "\"first_name\": \"Test4\"," +
            "\"last_name\": \"User4\"," +
            "\"type\": 1," +
            "\"status\": \"active\"," +
            "\"pmi\": \"1111111111\"," +
            "\"timezone\": \"Asia/Shanghai\"," +
            "\"created_at\": \"2024-01-01T10:00:00Z\"" +
            "}";
        JsonNode userNode = objectMapper.readTree(userJsonWithPmi);

        // 调用updateZoomUserFromJson方法
        invokeUpdateZoomUserFromJson(user, userNode);

        // 验证original_pmi保持不变
        assertEquals("5555555555", user.getOriginalPmi());
        assertEquals("5555555555", user.getCurrentPmi());
        
        System.out.println("✅ 已有PMI用户保护测试成功:");
        System.out.println("  - Email: " + user.getEmail());
        System.out.println("  - Original PMI: " + user.getOriginalPmi() + " (保持不变)");
        System.out.println("  - Current PMI: " + user.getCurrentPmi());
        System.out.println("  - API返回的PMI: 1111111111 (被忽略)");
    }

    /**
     * 通过反射调用私有方法updateZoomUserFromJson
     */
    private void invokeUpdateZoomUserFromJson(ZoomUser user, JsonNode userNode) {
        try {
            var method = ZoomUserService.class.getDeclaredMethod("updateZoomUserFromJson", 
                ZoomUser.class, JsonNode.class);
            method.setAccessible(true);
            method.invoke(zoomUserService, user, userNode);
        } catch (Exception e) {
            throw new RuntimeException("调用updateZoomUserFromJson方法失败", e);
        }
    }
}
