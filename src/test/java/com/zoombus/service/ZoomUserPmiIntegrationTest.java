package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.ZoomAuthRepository;
import com.zoombus.repository.ZoomUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(properties = {
    "zoombus.billing.auto-init.enabled=false",
    "zoombus.billing.window-expiry-scheduler.enabled=false",
    "zoombus.billing.monitor-scheduler.enabled=false"
})
@ActiveProfiles("test")
@Transactional
class ZoomUserPmiIntegrationTest {

    @Autowired
    private ZoomUserService zoomUserService;

    @Autowired
    private ZoomUserRepository zoomUserRepository;

    @Autowired
    private ZoomAuthRepository zoomAuthRepository;

    private ObjectMapper objectMapper;
    private ZoomAuth testZoomAuth;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        
        // 创建测试ZoomAuth
        testZoomAuth = new ZoomAuth();
        testZoomAuth.setAccountName("测试账号");
        testZoomAuth.setZoomAccountId("test_account_id");
        testZoomAuth.setClientId("test_client_id");
        testZoomAuth.setClientSecret("test_client_secret");
        testZoomAuth.setCreatedAt(LocalDateTime.now());
        testZoomAuth = zoomAuthRepository.save(testZoomAuth);
    }

    @Test
    void testPmiProcessingWithRealData() throws Exception {
        // 模拟Zoom API返回的用户数据（包含PMI）
        String userJsonWithPmi = "{" +
            "\"id\": \"test_user_123\"," +
            "\"email\": \"<EMAIL>\"," +
            "\"first_name\": \"Test\"," +
            "\"last_name\": \"User\"," +
            "\"type\": 1," +
            "\"status\": \"active\"," +
            "\"pmi\": \"**********\"," +
            "\"timezone\": \"Asia/Shanghai\"," +
            "\"created_at\": \"2024-01-01T10:00:00Z\"" +
            "}";
        JsonNode userNode = objectMapper.readTree(userJsonWithPmi);

        // 调用私有方法processSingleUser
        boolean result = invokeProcessSingleUser(testZoomAuth, userNode, "test_user_123", "<EMAIL>");

        // 验证结果
        assertTrue(result);

        // 查询数据库验证PMI是否正确设置
        List<ZoomUser> users = zoomUserRepository.findByZoomAuth(testZoomAuth);
        assertEquals(1, users.size());

        ZoomUser savedUser = users.get(0);
        assertEquals("**********", savedUser.getOriginalPmi());
        assertEquals("**********", savedUser.getCurrentPmi());
        assertNotNull(savedUser.getPmiUpdatedAt());
        
        System.out.println("✅ 新用户PMI设置成功:");
        System.out.println("  - Email: " + savedUser.getEmail());
        System.out.println("  - Original PMI: " + savedUser.getOriginalPmi());
        System.out.println("  - Current PMI: " + savedUser.getCurrentPmi());
        System.out.println("  - Updated At: " + savedUser.getPmiUpdatedAt());
    }

    @Test
    void testPmiProcessingWithoutPmiField() throws Exception {
        // 模拟Zoom API返回的用户数据（不包含PMI）
        String userJsonWithoutPmi = "{" +
            "\"id\": \"test_user_456\"," +
            "\"email\": \"<EMAIL>\"," +
            "\"first_name\": \"Test2\"," +
            "\"last_name\": \"User2\"," +
            "\"type\": 1," +
            "\"status\": \"active\"," +
            "\"timezone\": \"Asia/Shanghai\"," +
            "\"created_at\": \"2024-01-01T10:00:00Z\"" +
            "}";
        JsonNode userNode = objectMapper.readTree(userJsonWithoutPmi);

        // 调用私有方法processSingleUser
        boolean result = invokeProcessSingleUser(testZoomAuth, userNode, "test_user_456", "<EMAIL>");

        // 验证结果
        assertTrue(result);

        // 查询数据库验证PMI字段为空
        List<ZoomUser> users = zoomUserRepository.findByZoomAuth(testZoomAuth);
        assertEquals(1, users.size());

        ZoomUser savedUser = users.get(0);
        assertNull(savedUser.getOriginalPmi());
        assertNull(savedUser.getCurrentPmi());
        assertNull(savedUser.getPmiUpdatedAt());
        
        System.out.println("✅ 无PMI字段的用户处理成功:");
        System.out.println("  - Email: " + savedUser.getEmail());
        System.out.println("  - Original PMI: " + savedUser.getOriginalPmi());
        System.out.println("  - Current PMI: " + savedUser.getCurrentPmi());
    }

    @Test
    void testUpdateExistingUserWithEmptyPmi() throws Exception {
        // 先创建一个没有PMI的用户
        ZoomUser existingUser = new ZoomUser();
        existingUser.setZoomAuth(testZoomAuth);
        existingUser.setZoomUserId("test_user_789");
        existingUser.setEmail("<EMAIL>");
        existingUser.setFirstName("Test3");
        existingUser.setLastName("User3");
        existingUser.setOriginalPmi(null);
        existingUser.setCurrentPmi(null);
        existingUser.setCreatedAt(LocalDateTime.now());
        zoomUserRepository.save(existingUser);

        // 模拟API返回包含PMI的数据
        String userJsonWithPmi = "{" +
            "\"id\": \"test_user_789\"," +
            "\"email\": \"<EMAIL>\"," +
            "\"first_name\": \"Test3\"," +
            "\"last_name\": \"User3\"," +
            "\"type\": 1," +
            "\"status\": \"active\"," +
            "\"pmi\": \"9876543210\"," +
            "\"timezone\": \"Asia/Shanghai\"," +
            "\"created_at\": \"2024-01-01T10:00:00Z\"" +
            "}";
        JsonNode userNode = objectMapper.readTree(userJsonWithPmi);

        // 调用更新逻辑
        boolean result = invokeProcessSingleUser(testZoomAuth, userNode, "test_user_789", "<EMAIL>");

        // 验证结果
        assertTrue(result);

        // 查询数据库验证PMI是否正确补全
        ZoomUser updatedUser = zoomUserRepository.findByZoomAuthAndZoomUserId(testZoomAuth, "test_user_789").orElse(null);
        assertNotNull(updatedUser);
        assertEquals("9876543210", updatedUser.getOriginalPmi());
        assertEquals("9876543210", updatedUser.getCurrentPmi());
        assertNotNull(updatedUser.getPmiUpdatedAt());
        
        System.out.println("✅ 现有用户PMI补全成功:");
        System.out.println("  - Email: " + updatedUser.getEmail());
        System.out.println("  - Original PMI: " + updatedUser.getOriginalPmi());
        System.out.println("  - Current PMI: " + updatedUser.getCurrentPmi());
        System.out.println("  - Updated At: " + updatedUser.getPmiUpdatedAt());
    }

    /**
     * 通过反射调用私有方法processSingleUser
     */
    private boolean invokeProcessSingleUser(ZoomAuth zoomAuth, JsonNode userNode, String zoomUserId, String email) {
        try {
            var method = ZoomUserService.class.getDeclaredMethod("processSingleUser", 
                ZoomAuth.class, JsonNode.class, String.class, String.class);
            method.setAccessible(true);
            return (Boolean) method.invoke(zoomUserService, zoomAuth, userNode, zoomUserId, email);
        } catch (Exception e) {
            throw new RuntimeException("调用processSingleUser方法失败", e);
        }
    }
}
