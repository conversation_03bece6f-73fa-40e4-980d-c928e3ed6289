package com.zoombus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoombus.entity.ZoomAuth;
import com.zoombus.entity.ZoomUser;
import com.zoombus.repository.ZoomAuthRepository;
import com.zoombus.repository.ZoomUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ZoomUserSyncPmiTest {

    @Mock
    private ZoomUserRepository zoomUserRepository;

    @Mock
    private ZoomAuthRepository zoomAuthRepository;

    @Mock
    private ZoomApiService zoomApiService;

    @InjectMocks
    private ZoomUserService zoomUserService;

    private ObjectMapper objectMapper;
    private ZoomAuth testZoomAuth;
    private JsonNode testUserNode;

    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        
        // 创建测试ZoomAuth
        testZoomAuth = new ZoomAuth();
        testZoomAuth.setId(1L);
        testZoomAuth.setAccountName("测试账号");
        testZoomAuth.setZoomAccountId("test_account_id");

        // 创建测试用户JSON数据（包含PMI）
        String userJsonWithPmi = "{" +
            "\"id\": \"test_user_123\"," +
            "\"email\": \"<EMAIL>\"," +
            "\"first_name\": \"Test\"," +
            "\"last_name\": \"User\"," +
            "\"type\": 1," +
            "\"status\": \"active\"," +
            "\"pmi\": \"**********\"," +
            "\"created_at\": \"2024-01-01T10:00:00Z\"" +
            "}";
        testUserNode = objectMapper.readTree(userJsonWithPmi);
    }

    @Test
    void testCreateNewUserWithPmi() {
        // Given
        when(zoomUserRepository.findByZoomAuthAndZoomUserId(testZoomAuth, "test_user_123"))
            .thenReturn(Optional.empty());
        when(zoomUserRepository.save(any(ZoomUser.class))).thenAnswer(invocation -> {
            ZoomUser user = invocation.getArgument(0);
            user.setId(1L); // 模拟数据库生成ID
            return user;
        });

        // When
        boolean result = invokeProcessSingleUser(testZoomAuth, testUserNode, "test_user_123", "<EMAIL>");

        // Then
        assertTrue(result);
        verify(zoomUserRepository).save(argThat(user -> 
            "**********".equals(user.getOriginalPmi()) &&
            "**********".equals(user.getCurrentPmi()) &&
            user.getPmiUpdatedAt() != null
        ));
    }

    @Test
    void testUpdateExistingUserWithEmptyOriginalPmi() {
        // Given - 现有用户没有original_pmi
        ZoomUser existingUser = new ZoomUser();
        existingUser.setId(1L);
        existingUser.setZoomAuth(testZoomAuth);
        existingUser.setZoomUserId("test_user_123");
        existingUser.setEmail("<EMAIL>");
        existingUser.setOriginalPmi(null); // 原始PMI为空
        existingUser.setCurrentPmi(null);

        when(zoomUserRepository.findByZoomAuthAndZoomUserId(testZoomAuth, "test_user_123"))
            .thenReturn(Optional.of(existingUser));
        when(zoomUserRepository.save(any(ZoomUser.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        boolean result = invokeProcessSingleUser(testZoomAuth, testUserNode, "test_user_123", "<EMAIL>");

        // Then
        assertTrue(result);
        verify(zoomUserRepository).save(argThat(user -> 
            "**********".equals(user.getOriginalPmi()) &&
            "**********".equals(user.getCurrentPmi()) &&
            user.getPmiUpdatedAt() != null
        ));
    }

    @Test
    void testUpdateExistingUserWithExistingOriginalPmi() {
        // Given - 现有用户已有original_pmi
        ZoomUser existingUser = new ZoomUser();
        existingUser.setId(1L);
        existingUser.setZoomAuth(testZoomAuth);
        existingUser.setZoomUserId("test_user_123");
        existingUser.setEmail("<EMAIL>");
        existingUser.setOriginalPmi("9876543210"); // 已有原始PMI
        existingUser.setCurrentPmi("9876543210");
        existingUser.setPmiUpdatedAt(LocalDateTime.now().minusDays(1));

        when(zoomUserRepository.findByZoomAuthAndZoomUserId(testZoomAuth, "test_user_123"))
            .thenReturn(Optional.of(existingUser));
        when(zoomUserRepository.save(any(ZoomUser.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        boolean result = invokeProcessSingleUser(testZoomAuth, testUserNode, "test_user_123", "<EMAIL>");

        // Then
        assertTrue(result);
        verify(zoomUserRepository).save(argThat(user -> 
            "9876543210".equals(user.getOriginalPmi()) && // 保持原有的original_pmi
            "9876543210".equals(user.getCurrentPmi()) // 保持原有的current_pmi
        ));
    }

    @Test
    void testUpdateExistingUserWithEmptyCurrentPmi() {
        // Given - 现有用户有original_pmi但current_pmi为空
        ZoomUser existingUser = new ZoomUser();
        existingUser.setId(1L);
        existingUser.setZoomAuth(testZoomAuth);
        existingUser.setZoomUserId("test_user_123");
        existingUser.setEmail("<EMAIL>");
        existingUser.setOriginalPmi("9876543210"); // 已有原始PMI
        existingUser.setCurrentPmi(null); // 当前PMI为空

        when(zoomUserRepository.findByZoomAuthAndZoomUserId(testZoomAuth, "test_user_123"))
            .thenReturn(Optional.of(existingUser));
        when(zoomUserRepository.save(any(ZoomUser.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        boolean result = invokeProcessSingleUser(testZoomAuth, testUserNode, "test_user_123", "<EMAIL>");

        // Then
        assertTrue(result);
        verify(zoomUserRepository).save(argThat(user -> 
            "9876543210".equals(user.getOriginalPmi()) && // 保持原有的original_pmi
            "**********".equals(user.getCurrentPmi()) && // 用API的PMI补全current_pmi
            user.getPmiUpdatedAt() != null
        ));
    }

    @Test
    void testCreateUserWithoutPmiInApi() throws Exception {
        // Given - API返回的用户数据不包含PMI
        String userJsonWithoutPmi = "{" +
            "\"id\": \"test_user_456\"," +
            "\"email\": \"<EMAIL>\"," +
            "\"first_name\": \"Test2\"," +
            "\"last_name\": \"User2\"," +
            "\"type\": 1," +
            "\"status\": \"active\"," +
            "\"created_at\": \"2024-01-01T10:00:00Z\"" +
            "}";
        JsonNode userNodeWithoutPmi = objectMapper.readTree(userJsonWithoutPmi);

        when(zoomUserRepository.findByZoomAuthAndZoomUserId(testZoomAuth, "test_user_456"))
            .thenReturn(Optional.empty());
        when(zoomUserRepository.save(any(ZoomUser.class))).thenAnswer(invocation -> {
            ZoomUser user = invocation.getArgument(0);
            user.setId(2L);
            return user;
        });

        // When
        boolean result = invokeProcessSingleUser(testZoomAuth, userNodeWithoutPmi, "test_user_456", "<EMAIL>");

        // Then
        assertTrue(result);
        verify(zoomUserRepository).save(argThat(user -> 
            user.getOriginalPmi() == null &&
            user.getCurrentPmi() == null
        ));
    }

    /**
     * 通过反射调用私有方法processSingleUser
     */
    private boolean invokeProcessSingleUser(ZoomAuth zoomAuth, JsonNode userNode, String zoomUserId, String email) {
        try {
            var method = ZoomUserService.class.getDeclaredMethod("processSingleUser", 
                ZoomAuth.class, JsonNode.class, String.class, String.class);
            method.setAccessible(true);
            return (Boolean) method.invoke(zoomUserService, zoomAuth, userNode, zoomUserId, email);
        } catch (Exception e) {
            throw new RuntimeException("调用processSingleUser方法失败", e);
        }
    }
}
