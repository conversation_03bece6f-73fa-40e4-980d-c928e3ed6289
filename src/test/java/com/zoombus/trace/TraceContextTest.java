package com.zoombus.trace;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TraceContext上下文测试
 */
class TraceContextTest {

    @BeforeEach
    void setUp() {
        // 清理上下文
        TraceContext.clear();
        MDC.clear();
    }

    @AfterEach
    void tearDown() {
        // 清理上下文
        TraceContext.clear();
        MDC.clear();
    }

    @Test
    void testSetAndGetTraceId() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        
        // 设置TraceId
        TraceContext.setTraceId(traceId);
        
        // 验证获取
        assertEquals(traceId, TraceContext.getTraceId());
        
        // 验证MDC中也有设置
        assertEquals(traceId, MDC.get("traceId"));
    }

    @Test
    void testSetNullTraceId() {
        // 设置null TraceId
        TraceContext.setTraceId(null);
        
        // 应该不会设置
        assertNull(TraceContext.getTraceId());
    }

    @Test
    void testSetEmptyTraceId() {
        // 设置空TraceId
        TraceContext.setTraceId("");
        
        // 应该不会设置
        assertNull(TraceContext.getTraceId());
    }

    @Test
    void testAddAndGetContext() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        
        // 添加上下文信息
        TraceContext.addContext("userId", "user123");
        TraceContext.addContext("requestUri", "/api/test");
        TraceContext.addContext("method", "POST");
        
        // 验证获取
        assertEquals("user123", TraceContext.getContext("userId"));
        assertEquals("/api/test", TraceContext.getContext("requestUri"));
        assertEquals("POST", TraceContext.getContext("method"));
        
        // 验证MDC中也有设置
        assertEquals("user123", MDC.get("userId"));
        assertEquals("/api/test", MDC.get("requestUri"));
        assertEquals("POST", MDC.get("method"));
    }

    @Test
    void testGetAllContext() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        
        // 添加多个上下文信息
        TraceContext.addContext("key1", "value1");
        TraceContext.addContext("key2", "value2");
        TraceContext.addContext("key3", "value3");
        
        // 获取所有上下文
        Map<String, Object> allContext = TraceContext.getAllContext();
        
        assertNotNull(allContext);
        assertEquals(3, allContext.size());
        assertEquals("value1", allContext.get("key1"));
        assertEquals("value2", allContext.get("key2"));
        assertEquals("value3", allContext.get("key3"));
    }

    @Test
    void testUserIdMethods() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        
        // 设置用户ID
        TraceContext.setUserId("user123");
        
        // 验证获取
        assertEquals("user123", TraceContext.getUserId());
        assertEquals("user123", TraceContext.getContext("userId"));
    }

    @Test
    void testRequestUriMethods() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        
        // 设置请求URI
        TraceContext.setRequestUri("/api/test");
        
        // 验证获取
        assertEquals("/api/test", TraceContext.getRequestUri());
        assertEquals("/api/test", TraceContext.getContext("requestUri"));
    }

    @Test
    void testMethodMethods() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        
        // 设置HTTP方法
        TraceContext.setMethod("POST");
        
        // 验证获取
        assertEquals("POST", TraceContext.getMethod());
        assertEquals("POST", TraceContext.getContext("method"));
    }

    @Test
    void testWebhookInfo() {
        String traceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        TraceContext.setTraceId(traceId);
        
        // 设置Webhook信息
        TraceContext.setWebhookInfo("ZOOM", "meeting.started", "EVENT123");
        
        // 验证获取
        assertEquals("ZOOM", TraceContext.getContext("webhookSource"));
        assertEquals("meeting.started", TraceContext.getContext("webhookEventType"));
        assertEquals("EVENT123", TraceContext.getContext("webhookEventId"));
    }

    @Test
    void testClear() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        TraceContext.addContext("userId", "user123");
        TraceContext.addContext("requestUri", "/api/test");
        
        // 验证设置成功
        assertEquals(traceId, TraceContext.getTraceId());
        assertEquals("user123", TraceContext.getContext("userId"));
        assertNotNull(MDC.get("traceId"));
        
        // 清理上下文
        TraceContext.clear();
        
        // 验证清理成功
        assertNull(TraceContext.getTraceId());
        assertNull(TraceContext.getContext("userId"));
        assertNull(MDC.get("traceId"));
        assertNull(MDC.get("userId"));
    }

    @Test
    void testCreateSnapshot() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        TraceContext.addContext("userId", "user123");
        TraceContext.addContext("requestUri", "/api/test");
        
        // 创建快照
        TraceContext.TraceSnapshot snapshot = TraceContext.createSnapshot();
        
        assertNotNull(snapshot);
        assertEquals(traceId, snapshot.getTraceId());
        assertNotNull(snapshot.getContext());
        assertEquals("user123", snapshot.getContext().get("userId"));
        assertEquals("/api/test", snapshot.getContext().get("requestUri"));
        assertNotNull(snapshot.getMdcContext());
    }

    @Test
    void testRestoreFromSnapshot() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        TraceContext.addContext("userId", "user123");
        TraceContext.addContext("requestUri", "/api/test");
        
        // 创建快照
        TraceContext.TraceSnapshot snapshot = TraceContext.createSnapshot();
        
        // 清理上下文
        TraceContext.clear();
        assertNull(TraceContext.getTraceId());
        
        // 从快照恢复
        TraceContext.restoreFromSnapshot(snapshot);
        
        // 验证恢复成功
        assertEquals(traceId, TraceContext.getTraceId());
        assertEquals("user123", TraceContext.getContext("userId"));
        assertEquals("/api/test", TraceContext.getContext("requestUri"));
    }

    @Test
    void testRestoreFromNullSnapshot() {
        // 从null快照恢复应该不会出错
        assertDoesNotThrow(() -> TraceContext.restoreFromSnapshot(null));
    }

    @Test
    void testThreadIsolation() throws ExecutionException, InterruptedException {
        String mainTraceId = "20250823143025-001-000001-a1b2c3";
        String asyncTraceId = "20250823143025-001-000002-b2c3d4";
        
        // 在主线程设置TraceId
        TraceContext.setTraceId(mainTraceId);
        TraceContext.addContext("thread", "main");
        
        // 在异步线程中设置不同的TraceId
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            TraceContext.setTraceId(asyncTraceId);
            TraceContext.addContext("thread", "async");
            return TraceContext.getTraceId();
        });
        
        String asyncResult = future.get();
        
        // 验证线程隔离
        assertEquals(asyncTraceId, asyncResult);
        assertEquals(mainTraceId, TraceContext.getTraceId()); // 主线程的TraceId不受影响
        assertEquals("main", TraceContext.getContext("thread")); // 主线程的上下文不受影响
    }

    @Test
    void testSnapshotThreadTransfer() throws ExecutionException, InterruptedException {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        TraceContext.addContext("userId", "user123");
        TraceContext.addContext("requestUri", "/api/test");
        
        // 创建快照
        TraceContext.TraceSnapshot snapshot = TraceContext.createSnapshot();
        
        // 在异步线程中使用快照
        CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
            // 恢复快照
            TraceContext.restoreFromSnapshot(snapshot);
            
            // 验证恢复成功
            boolean traceIdMatch = traceId.equals(TraceContext.getTraceId());
            boolean userIdMatch = "user123".equals(TraceContext.getContext("userId"));
            boolean uriMatch = "/api/test".equals(TraceContext.getContext("requestUri"));
            
            return traceIdMatch && userIdMatch && uriMatch;
        });
        
        Boolean result = future.get();
        assertTrue(result, "快照应该能够在异步线程中正确恢复");
    }

    @Test
    void testAddNullContext() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        
        // 添加null上下文应该不会出错
        assertDoesNotThrow(() -> {
            TraceContext.addContext(null, "value");
            TraceContext.addContext("key", null);
            TraceContext.addContext(null, null);
        });
        
        // 验证没有添加无效的上下文
        assertNull(TraceContext.getContext(null));
        assertNull(TraceContext.getContext("key"));
    }

    @Test
    void testGetContextWithoutTraceId() {
        // 在没有设置TraceId的情况下获取上下文
        assertNull(TraceContext.getContext("userId"));
        
        Map<String, Object> allContext = TraceContext.getAllContext();
        assertNotNull(allContext);
        assertTrue(allContext.isEmpty());
    }
}
