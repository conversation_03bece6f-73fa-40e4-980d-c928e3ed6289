package com.zoombus.trace;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TraceId生成器测试
 */
@ExtendWith(MockitoExtension.class)
class TraceIdGeneratorTest {

    private TraceIdGenerator traceIdGenerator;

    @BeforeEach
    void setUp() {
        traceIdGenerator = new TraceIdGenerator();
        // 设置节点ID
        ReflectionTestUtils.setField(traceIdGenerator, "nodeId", "001");
    }

    @Test
    void testGenerateTraceId() {
        // 生成TraceId
        String traceId = traceIdGenerator.generateTraceId();
        
        // 验证格式
        assertNotNull(traceId);
        assertTrue(traceId.matches("\\d{14}-\\d{3}-\\d{6}-\\w{6}"));
        
        // 验证组成部分
        String[] parts = traceId.split("-");
        assertEquals(4, parts.length);
        assertEquals(14, parts[0].length()); // timestamp
        assertEquals("001", parts[1]); // nodeId
        assertEquals(6, parts[2].length()); // sequence
        assertEquals(6, parts[3].length()); // random
    }

    @Test
    void testGenerateWebhookTraceId() {
        // 测试基本Webhook TraceId生成
        String traceId = traceIdGenerator.generateWebhookTraceId("ZOOM", "EVENT123");
        
        assertNotNull(traceId);
        assertTrue(traceId.startsWith("WH-"));
        assertTrue(traceId.contains("-ZOOM-"));
        assertTrue(traceId.contains("-EVENT123-"));
        
        // 验证格式
        String[] parts = traceId.split("-");
        assertEquals(5, parts.length);
        assertEquals("WH", parts[0]);
        assertEquals(14, parts[1].length()); // timestamp
        assertEquals("ZOOM", parts[2]); // source
        assertEquals("EVENT123", parts[3]); // eventId
        assertEquals(6, parts[4].length()); // random
    }

    @Test
    void testGenerateWebhookTraceIdWithNullValues() {
        // 测试空值处理
        String traceId = traceIdGenerator.generateWebhookTraceId(null, null);
        
        assertNotNull(traceId);
        assertTrue(traceId.startsWith("WH-"));
        assertTrue(traceId.contains("-UNKNOWN-"));
        assertTrue(traceId.contains("-EVENT-"));
    }

    @Test
    void testEnhanceWebhookTraceId() {
        String originalTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        
        // 测试增强TraceId
        String enhanced = traceIdGenerator.enhanceWebhookTraceId(
            originalTraceId, "meeting.started", "M123456");
        
        assertNotNull(enhanced);
        assertTrue(enhanced.startsWith(originalTraceId));
        assertTrue(enhanced.contains("-MEETINGSTA")); // "meeting.started" -> "MEETINGSTA" (10个字符)
        assertTrue(enhanced.contains("-M123456"));
    }

    @Test
    void testEnhanceWebhookTraceIdWithNullOriginal() {
        // 测试空原始TraceId
        String enhanced = traceIdGenerator.enhanceWebhookTraceId(
            null, "meeting.started", "M123456");
        
        assertNotNull(enhanced);
        assertTrue(enhanced.startsWith("WH-"));
    }

    @Test
    void testIsValidTraceId() {
        // 测试有效的标准TraceId
        String validStandardTraceId = "20250823143025-001-000001-a1b2c3";
        assertTrue(traceIdGenerator.isValidTraceId(validStandardTraceId));
        
        // 测试有效的Webhook TraceId
        String validWebhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        assertTrue(traceIdGenerator.isValidTraceId(validWebhookTraceId));
        
        // 测试无效的TraceId
        assertFalse(traceIdGenerator.isValidTraceId(null));
        assertFalse(traceIdGenerator.isValidTraceId(""));
        assertFalse(traceIdGenerator.isValidTraceId("invalid"));
        assertFalse(traceIdGenerator.isValidTraceId("20250823-001-000001"));
        assertFalse(traceIdGenerator.isValidTraceId("2025082314302-001-000001-a1b2c3"));
    }

    @Test
    void testExtractTimestamp() {
        String traceId = "20250823143025-001-000001-a1b2c3";
        Date timestamp = traceIdGenerator.extractTimestamp(traceId);
        
        assertNotNull(timestamp);
        
        // 验证时间戳解析正确
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        Date webhookTimestamp = traceIdGenerator.extractTimestamp(webhookTraceId);
        
        assertNotNull(webhookTimestamp);
        assertEquals(timestamp, webhookTimestamp);
    }

    @Test
    void testExtractTimestampWithInvalidTraceId() {
        // 测试无效TraceId的时间戳提取
        assertNull(traceIdGenerator.extractTimestamp("invalid"));
        assertNull(traceIdGenerator.extractTimestamp(null));
    }

    @Test
    void testIsWebhookTraceId() {
        // 测试Webhook TraceId识别
        assertTrue(traceIdGenerator.isWebhookTraceId("WH-20250823143025-ZOOM-EVENT123-a1b2c3"));
        assertFalse(traceIdGenerator.isWebhookTraceId("20250823143025-001-000001-a1b2c3"));
        assertFalse(traceIdGenerator.isWebhookTraceId(null));
        assertFalse(traceIdGenerator.isWebhookTraceId(""));
    }

    @Test
    void testTraceIdUniqueness() {
        // 测试TraceId唯一性
        Set<String> traceIds = new HashSet<>();
        int count = 1000;
        
        for (int i = 0; i < count; i++) {
            String traceId = traceIdGenerator.generateTraceId();
            assertTrue(traceIds.add(traceId), "TraceId应该是唯一的: " + traceId);
        }
        
        assertEquals(count, traceIds.size());
    }

    @Test
    void testWebhookTraceIdUniqueness() {
        // 测试Webhook TraceId唯一性
        Set<String> traceIds = new HashSet<>();
        int count = 1000;
        
        for (int i = 0; i < count; i++) {
            String traceId = traceIdGenerator.generateWebhookTraceId("ZOOM", "EVENT" + i);
            assertTrue(traceIds.add(traceId), "Webhook TraceId应该是唯一的: " + traceId);
        }
        
        assertEquals(count, traceIds.size());
    }

    @Test
    void testSanitizeComponent() {
        // 测试组件清理功能（通过生成Webhook TraceId间接测试）
        String traceId = traceIdGenerator.generateWebhookTraceId("ZOOM@#$%", "EVENT!@#123");
        
        assertNotNull(traceId);
        assertTrue(traceId.contains("-ZOOM-")); // 特殊字符应该被移除
        assertTrue(traceId.contains("-EVENT123-")); // 特殊字符应该被移除
    }

    @Test
    void testLongComponentTruncation() {
        // 测试长组件截断
        String longSource = "VERYLONGSOURCENAME";
        String longEventId = "VERYLONGEVENTIDNAME";
        
        String traceId = traceIdGenerator.generateWebhookTraceId(longSource, longEventId);
        
        assertNotNull(traceId);
        String[] parts = traceId.split("-");
        assertTrue(parts[2].length() <= 10); // source应该被截断
        assertTrue(parts[3].length() <= 10); // eventId应该被截断
    }

    @Test
    void testConcurrentGeneration() throws InterruptedException {
        // 测试并发生成TraceId
        Set<String> traceIds = new HashSet<>();
        int threadCount = 10;
        int countPerThread = 100;
        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < countPerThread; j++) {
                    String traceId = traceIdGenerator.generateTraceId();
                    synchronized (traceIds) {
                        traceIds.add(traceId);
                    }
                }
            });
            threads[i].start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 验证所有TraceId都是唯一的
        assertEquals(threadCount * countPerThread, traceIds.size());
    }
}
