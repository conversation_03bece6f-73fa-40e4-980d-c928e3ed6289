package com.zoombus.trace;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * TraceId机制集成测试
 */
@SpringBootTest
@AutoConfigureWebMvc
@TestPropertySource(properties = {
    "app.trace.enabled=true",
    "app.trace.node-id=TEST",
    "app.trace.header-name=X-Trace-Id"
})
class TraceIdIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testTraceIdGenerationAndPropagation() throws Exception {
        // 测试不带TraceId的请求，应该自动生成
        MvcResult result = mockMvc.perform(get("/api/logs/health"))
                .andExpect(status().isOk())
                .andExpect(header().exists("X-Trace-Id"))
                .andReturn();

        String generatedTraceId = result.getResponse().getHeader("X-Trace-Id");
        assertNotNull(generatedTraceId);
        assertTrue(generatedTraceId.matches("\\d{14}-TEST-\\d{6}-\\w{6}"));
    }

    @Test
    void testTraceIdPropagationFromHeader() throws Exception {
        String existingTraceId = "20250823143025-001-000001-a1b2c3";

        // 测试带TraceId的请求，应该传递现有的TraceId
        mockMvc.perform(get("/api/logs/health")
                .header("X-Trace-Id", existingTraceId))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Trace-Id", existingTraceId));
    }

    @Test
    void testTraceIdPropagationFromParameter() throws Exception {
        String existingTraceId = "20250823143025-001-000001-a1b2c3";

        // 测试从URL参数传递TraceId
        mockMvc.perform(get("/api/logs/health")
                .param("traceId", existingTraceId))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Trace-Id", existingTraceId));
    }

    @Test
    void testWebhookTraceIdGeneration() throws Exception {
        // 测试Webhook请求的TraceId生成
        String webhookPayload = "{\"event\":\"meeting.started\",\"payload\":{\"object\":{\"id\":\"123456\"}}}";

        MvcResult result = mockMvc.perform(post("/api/webhooks/zoom/test")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Agent", "Zoom-Webhook/1.0")
                .header("X-Event-Id", "EVENT123")
                .content(webhookPayload))
                .andExpect(header().exists("X-Trace-Id"))
                .andReturn();

        String webhookTraceId = result.getResponse().getHeader("X-Trace-Id");
        assertNotNull(webhookTraceId);
        assertTrue(webhookTraceId.startsWith("WH-"));
        assertTrue(webhookTraceId.contains("-ZOOM-"));
    }

    @Test
    void testLogSearchWithTraceId() throws Exception {
        String searchRequest = "{\n" +
                "    \"traceId\": \"20250823143025-001-000001-a1b2c3\",\n" +
                "    \"page\": 1,\n" +
                "    \"size\": 50\n" +
                "}";

        MvcResult result = mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(searchRequest))
                .andExpect(status().isOk())
                .andExpect(header().exists("X-Trace-Id"))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.traceId").exists())
                .andReturn();

        String responseTraceId = result.getResponse().getHeader("X-Trace-Id");
        assertNotNull(responseTraceId);
    }

    @Test
    void testTraceIdInErrorResponse() throws Exception {
        // 测试错误响应中也包含TraceId
        String invalidRequest = "{\n" +
                "    \"page\": 1,\n" +
                "    \"size\": 50\n" +
                "}";

        MvcResult result = mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidRequest))
                .andExpect(status().isBadRequest())
                .andExpect(header().exists("X-Trace-Id"))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.traceId").exists())
                .andReturn();

        String errorTraceId = result.getResponse().getHeader("X-Trace-Id");
        assertNotNull(errorTraceId);
    }

    @Test
    void testConcurrentRequests() throws Exception {
        // 测试并发请求的TraceId隔离
        int threadCount = 10;
        String[] traceIds = new String[threadCount];
        Thread[] threads = new Thread[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    MvcResult result = mockMvc.perform(get("/api/logs/health"))
                            .andExpect(status().isOk())
                            .andExpect(header().exists("X-Trace-Id"))
                            .andReturn();
                    
                    traceIds[index] = result.getResponse().getHeader("X-Trace-Id");
                } catch (Exception e) {
                    fail("Concurrent request failed: " + e.getMessage());
                }
            });
            threads[i].start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // 验证所有TraceId都不为空且唯一
        for (int i = 0; i < threadCount; i++) {
            assertNotNull(traceIds[i], "TraceId " + i + " should not be null");
            for (int j = i + 1; j < threadCount; j++) {
                assertNotEquals(traceIds[i], traceIds[j], 
                    "TraceId " + i + " and " + j + " should be different");
            }
        }
    }

    @Test
    void testTraceIdHeaderPriority() throws Exception {
        String headerTraceId = "20250823143025-001-000001-a1b2c3";
        String paramTraceId = "20250823143025-001-000002-b2c3d4";

        // 同时设置请求头和参数，请求头应该优先
        mockMvc.perform(get("/api/logs/health")
                .header("X-Trace-Id", headerTraceId)
                .param("traceId", paramTraceId))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Trace-Id", headerTraceId));
    }

    @Test
    void testCustomTraceIdHeader() throws Exception {
        String customTraceId = "20250823143025-001-000001-a1b2c3";

        // 测试自定义TraceId头部名称（如果配置支持）
        mockMvc.perform(get("/api/logs/health")
                .header("X-Trace-Id", customTraceId))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Trace-Id", customTraceId));
    }

    @Test
    void testTraceIdValidation() throws Exception {
        // 测试有效的TraceId
        String validTraceId = "20250823143025-001-000001-a1b2c3";
        mockMvc.perform(get("/api/logs/health")
                .header("X-Trace-Id", validTraceId))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Trace-Id", validTraceId));

        // 测试Webhook TraceId
        String validWebhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        mockMvc.perform(get("/api/logs/health")
                .header("X-Trace-Id", validWebhookTraceId))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Trace-Id", validWebhookTraceId));
    }

    @Test
    void testTraceIdInLogSearch() throws Exception {
        String searchTraceId = "20250823143025-001-000001-a1b2c3";
        String searchRequest = String.format("{\n" +
                "    \"traceId\": \"%s\",\n" +
                "    \"page\": 1,\n" +
                "    \"size\": 10\n" +
                "}", searchTraceId);

        mockMvc.perform(post("/api/logs/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(searchRequest))
                .andExpect(status().isOk())
                .andExpect(header().exists("X-Trace-Id"))
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    void testTraceIdInExport() throws Exception {
        String exportTraceId = "20250823143025-001-000001-a1b2c3";

        mockMvc.perform(get("/api/logs/export")
                .param("traceId", exportTraceId)
                .param("format", "txt"))
                .andExpect(header().exists("X-Trace-Id"));
    }

    @Test
    void testWebhookSourceDetection() throws Exception {
        // 测试Zoom Webhook
        mockMvc.perform(post("/api/webhooks/zoom/events")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Agent", "Zoom-Webhook/1.0")
                .content("{\"event\":\"meeting.started\"}"))
                .andExpect(header().exists("X-Trace-Id"))
                .andExpect(result -> {
                    String traceId = result.getResponse().getHeader("X-Trace-Id");
                    assertNotNull(traceId);
                    assertTrue(traceId.contains("-ZOOM-"));
                });

        // 测试Teams Webhook
        mockMvc.perform(post("/api/webhooks/teams/callback")
                .contentType(MediaType.APPLICATION_JSON)
                .header("User-Agent", "Microsoft-Teams/1.0")
                .content("{\"type\":\"message\"}"))
                .andExpect(header().exists("X-Trace-Id"))
                .andExpect(result -> {
                    String traceId = result.getResponse().getHeader("X-Trace-Id");
                    assertNotNull(traceId);
                    assertTrue(traceId.contains("-TEAMS-"));
                });
    }

    @Test
    void testTraceIdPersistenceAcrossRequestLifecycle() throws Exception {
        String initialTraceId = "20250823143025-001-000001-a1b2c3";

        // 发起请求并验证TraceId在整个请求生命周期中保持一致
        mockMvc.perform(get("/api/logs/health")
                .header("X-Trace-Id", initialTraceId))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Trace-Id", initialTraceId))
                .andExpect(result -> {
                    // 验证响应中的TraceId与请求中的一致
                    String responseTraceId = result.getResponse().getHeader("X-Trace-Id");
                    assertEquals(initialTraceId, responseTraceId);
                });
    }
}
