package com.zoombus.trace;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * TraceId拦截器测试
 */
@ExtendWith(MockitoExtension.class)
class TraceIdInterceptorTest {

    @Mock
    private TraceIdGenerator traceIdGenerator;

    @InjectMocks
    private TraceIdInterceptor traceIdInterceptor;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private Object handler = new Object();

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        
        // 设置默认配置
        ReflectionTestUtils.setField(traceIdInterceptor, "traceIdHeader", "X-Trace-Id");
        
        // 清理上下文
        TraceContext.clear();
    }

    @AfterEach
    void tearDown() {
        TraceContext.clear();
    }

    @Test
    void testPreHandleWithExistingTraceIdInHeader() throws Exception {
        String existingTraceId = "20250823143025-001-000001-a1b2c3";
        
        // 设置请求头中的TraceId
        request.addHeader("X-Trace-Id", existingTraceId);
        request.setRequestURI("/api/test");
        request.setMethod("GET");
        
        // 执行拦截器
        boolean result = traceIdInterceptor.preHandle(request, response, handler);
        
        // 验证结果
        assertTrue(result);
        assertEquals(existingTraceId, TraceContext.getTraceId());
        assertEquals("/api/test", TraceContext.getRequestUri());
        assertEquals("GET", TraceContext.getMethod());
        assertEquals(existingTraceId, response.getHeader("X-Trace-Id"));
    }

    @Test
    void testPreHandleWithTraceIdInParameter() throws Exception {
        String existingTraceId = "20250823143025-001-000001-a1b2c3";
        
        // 设置URL参数中的TraceId
        request.setParameter("traceId", existingTraceId);
        request.setRequestURI("/api/test");
        request.setMethod("POST");
        
        // 执行拦截器
        boolean result = traceIdInterceptor.preHandle(request, response, handler);
        
        // 验证结果
        assertTrue(result);
        assertEquals(existingTraceId, TraceContext.getTraceId());
        assertEquals("/api/test", TraceContext.getRequestUri());
        assertEquals("POST", TraceContext.getMethod());
        assertEquals(existingTraceId, response.getHeader("X-Trace-Id"));
    }

    @Test
    void testPreHandleGenerateNewTraceId() throws Exception {
        String generatedTraceId = "20250823143025-001-000002-b2c3d4";
        
        // 模拟生成新的TraceId
        when(traceIdGenerator.generateTraceId()).thenReturn(generatedTraceId);
        
        request.setRequestURI("/api/test");
        request.setMethod("PUT");
        
        // 执行拦截器
        boolean result = traceIdInterceptor.preHandle(request, response, handler);
        
        // 验证结果
        assertTrue(result);
        assertEquals(generatedTraceId, TraceContext.getTraceId());
        assertEquals("/api/test", TraceContext.getRequestUri());
        assertEquals("PUT", TraceContext.getMethod());
        assertEquals(generatedTraceId, response.getHeader("X-Trace-Id"));
    }

    @Test
    void testPreHandleWebhookRequest() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        
        // 模拟Webhook TraceId生成
        when(traceIdGenerator.generateWebhookTraceId(anyString(), anyString()))
            .thenReturn(webhookTraceId);
        
        // 设置Webhook请求
        request.setRequestURI("/api/webhook/zoom");
        request.setMethod("POST");
        request.addHeader("User-Agent", "Zoom-Webhook/1.0");
        request.setContent("{\"event\":\"meeting.started\"}".getBytes());
        
        // 执行拦截器
        boolean result = traceIdInterceptor.preHandle(request, response, handler);
        
        // 验证结果
        assertTrue(result);
        assertEquals(webhookTraceId, TraceContext.getTraceId());
        assertEquals("/api/webhook/zoom", TraceContext.getRequestUri());
        assertEquals("POST", TraceContext.getMethod());
        assertEquals(webhookTraceId, response.getHeader("X-Trace-Id"));
    }

    @Test
    void testAfterCompletionSuccess() throws Exception {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        
        request.setRequestURI("/api/test");
        request.setMethod("GET");
        response.setStatus(200);
        
        // 执行afterCompletion
        traceIdInterceptor.afterCompletion(request, response, handler, null);
        
        // 验证上下文被清理
        assertNull(TraceContext.getTraceId());
    }

    @Test
    void testAfterCompletionWithException() throws Exception {
        String traceId = "20250823143025-001-000001-a1b2c3";
        TraceContext.setTraceId(traceId);
        
        request.setRequestURI("/api/test");
        request.setMethod("GET");
        response.setStatus(500);
        Exception exception = new RuntimeException("Test exception");
        
        // 执行afterCompletion
        traceIdInterceptor.afterCompletion(request, response, handler, exception);
        
        // 验证上下文被清理
        assertNull(TraceContext.getTraceId());
    }

    @Test
    void testIsWebhookRequest() throws Exception {
        // 测试Webhook URL识别
        request.setRequestURI("/api/webhook/zoom");
        when(traceIdGenerator.generateWebhookTraceId(anyString(), anyString()))
            .thenReturn("WH-20250823143025-ZOOM-EVENT123-a1b2c3");
        
        boolean result = traceIdInterceptor.preHandle(request, response, handler);
        assertTrue(result);
        assertTrue(TraceContext.getTraceId().startsWith("WH-"));
        
        // 测试callback URL识别
        TraceContext.clear();
        request.setRequestURI("/api/callback/teams");
        when(traceIdGenerator.generateWebhookTraceId(anyString(), anyString()))
            .thenReturn("WH-20250823143025-TEAMS-EVENT456-c3d4e5");
        
        result = traceIdInterceptor.preHandle(request, response, handler);
        assertTrue(result);
        assertTrue(TraceContext.getTraceId().startsWith("WH-"));
    }

    @Test
    void testExtractWebhookSource() throws Exception {
        // 测试从User-Agent提取来源
        request.setRequestURI("/api/webhook/test");
        request.addHeader("User-Agent", "Zoom-Webhook/1.0");
        when(traceIdGenerator.generateWebhookTraceId(eq("ZOOM"), anyString()))
            .thenReturn("WH-20250823143025-ZOOM-EVENT123-a1b2c3");
        
        traceIdInterceptor.preHandle(request, response, handler);
        assertEquals("ZOOM", TraceContext.getContext("webhookSource"));
        
        // 测试从URI提取来源
        TraceContext.clear();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        request.setRequestURI("/api/webhook/teams");
        when(traceIdGenerator.generateWebhookTraceId(eq("TEAMS"), anyString()))
            .thenReturn("WH-20250823143025-TEAMS-EVENT456-c3d4e5");
        
        traceIdInterceptor.preHandle(request, response, handler);
        assertEquals("TEAMS", TraceContext.getContext("webhookSource"));
    }

    @Test
    void testExtractEventId() throws Exception {
        // 测试从请求头提取事件ID
        request.setRequestURI("/api/webhook/zoom");
        request.addHeader("X-Event-Id", "EVENT123456");
        when(traceIdGenerator.generateWebhookTraceId(anyString(), eq("EVENT123456")))
            .thenReturn("WH-20250823143025-ZOOM-EVENT123456-a1b2c3");
        
        traceIdInterceptor.preHandle(request, response, handler);
        assertEquals("EVENT123456", TraceContext.getContext("webhookEventId"));
        
        // 测试从X-Request-Id提取事件ID
        TraceContext.clear();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        request.setRequestURI("/api/webhook/zoom");
        request.addHeader("X-Request-Id", "REQ789012");
        when(traceIdGenerator.generateWebhookTraceId(anyString(), eq("REQ789012")))
            .thenReturn("WH-20250823143025-ZOOM-REQ789012-b2c3d4");
        
        traceIdInterceptor.preHandle(request, response, handler);
        assertEquals("REQ789012", TraceContext.getContext("webhookEventId"));
    }

    @Test
    void testHeaderPriority() throws Exception {
        String headerTraceId = "20250823143025-001-000001-a1b2c3";
        String paramTraceId = "20250823143025-001-000002-b2c3d4";
        
        // 同时设置请求头和参数中的TraceId
        request.addHeader("X-Trace-Id", headerTraceId);
        request.setParameter("traceId", paramTraceId);
        request.setRequestURI("/api/test");
        request.setMethod("GET");
        
        // 执行拦截器
        boolean result = traceIdInterceptor.preHandle(request, response, handler);
        
        // 验证请求头优先级更高
        assertTrue(result);
        assertEquals(headerTraceId, TraceContext.getTraceId());
    }

    @Test
    void testEmptyTraceIdInHeader() throws Exception {
        String generatedTraceId = "20250823143025-001-000001-a1b2c3";
        
        // 设置空的TraceId请求头
        request.addHeader("X-Trace-Id", "");
        request.setRequestURI("/api/test");
        request.setMethod("GET");
        
        when(traceIdGenerator.generateTraceId()).thenReturn(generatedTraceId);
        
        // 执行拦截器
        boolean result = traceIdInterceptor.preHandle(request, response, handler);
        
        // 验证生成新的TraceId
        assertTrue(result);
        assertEquals(generatedTraceId, TraceContext.getTraceId());
    }

    @Test
    void testCustomTraceIdHeader() throws Exception {
        String customTraceId = "20250823143025-001-000001-a1b2c3";
        
        // 设置自定义请求头名称
        ReflectionTestUtils.setField(traceIdInterceptor, "traceIdHeader", "Custom-Trace-Id");
        
        request.addHeader("Custom-Trace-Id", customTraceId);
        request.setRequestURI("/api/test");
        request.setMethod("GET");
        
        // 执行拦截器
        boolean result = traceIdInterceptor.preHandle(request, response, handler);
        
        // 验证结果
        assertTrue(result);
        assertEquals(customTraceId, TraceContext.getTraceId());
        assertEquals(customTraceId, response.getHeader("Custom-Trace-Id"));
    }
}
