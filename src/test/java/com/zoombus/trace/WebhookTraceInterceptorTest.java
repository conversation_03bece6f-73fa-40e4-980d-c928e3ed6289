package com.zoombus.trace;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Webhook TraceId拦截器测试
 */
@ExtendWith(MockitoExtension.class)
class WebhookTraceInterceptorTest {

    @Mock
    private TraceIdGenerator traceIdGenerator;

    @InjectMocks
    private WebhookTraceInterceptor webhookTraceInterceptor;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private Object handler = new Object();

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        
        // 设置默认配置
        ReflectionTestUtils.setField(webhookTraceInterceptor, "traceIdHeader", "X-Trace-Id");
        
        // 清理上下文
        TraceContext.clear();
    }

    @AfterEach
    void tearDown() {
        TraceContext.clear();
    }

    @Test
    void testPreHandleWebhookRequest() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        
        // 模拟Webhook TraceId生成
        when(traceIdGenerator.generateWebhookTraceId("ZOOM", "EVENT123"))
            .thenReturn(webhookTraceId);
        
        // 设置Zoom Webhook请求
        request.setRequestURI("/api/webhooks/zoom");
        request.setMethod("POST");
        request.addHeader("User-Agent", "Zoom-Webhook/1.0");
        request.addHeader("X-Event-Id", "EVENT123");
        request.setContent("{\"event\":\"meeting.started\",\"payload\":{\"object\":{\"id\":\"123456\"}}}".getBytes());
        
        // 执行拦截器
        boolean result = webhookTraceInterceptor.preHandle(request, response, handler);
        
        // 验证结果
        assertTrue(result);
        assertEquals(webhookTraceId, TraceContext.getTraceId());
        assertEquals("/api/webhooks/zoom", TraceContext.getRequestUri());
        assertEquals("POST", TraceContext.getMethod());
        assertEquals("ZOOM", TraceContext.getContext("webhookSource"));
        assertEquals("UNKNOWN", TraceContext.getContext("webhookEventType"));
        assertEquals("EVENT123", TraceContext.getContext("webhookEventId"));
        assertEquals(webhookTraceId, response.getHeader("X-Trace-Id"));
    }

    @Test
    void testPreHandleNonWebhookRequest() throws Exception {
        // 设置非Webhook请求
        request.setRequestURI("/api/users");
        request.setMethod("GET");
        
        // 执行拦截器
        boolean result = webhookTraceInterceptor.preHandle(request, response, handler);
        
        // 验证结果 - 应该直接通过，不处理
        assertTrue(result);
        assertNull(TraceContext.getTraceId());
    }

    @Test
    void testExtractWebhookSourceFromUserAgent() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        when(traceIdGenerator.generateWebhookTraceId("ZOOM", anyString()))
            .thenReturn(webhookTraceId);
        
        // 测试Zoom User-Agent
        request.setRequestURI("/api/webhooks/test");
        request.addHeader("User-Agent", "Zoom-Webhook/1.0");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("ZOOM", TraceContext.getContext("webhookSource"));
        
        // 测试Teams User-Agent
        TraceContext.clear();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        request.setRequestURI("/api/webhooks/test");
        request.addHeader("User-Agent", "Microsoft-Teams/1.0");
        when(traceIdGenerator.generateWebhookTraceId("TEAMS", anyString()))
            .thenReturn("WH-20250823143025-TEAMS-EVENT456-c3d4e5");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("TEAMS", TraceContext.getContext("webhookSource"));
        
        // 测试Slack User-Agent
        TraceContext.clear();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        request.setRequestURI("/api/webhooks/test");
        request.addHeader("User-Agent", "Slack-Webhook/1.0");
        when(traceIdGenerator.generateWebhookTraceId("SLACK", anyString()))
            .thenReturn("WH-20250823143025-SLACK-EVENT789-d4e5f6");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("SLACK", TraceContext.getContext("webhookSource"));
    }

    @Test
    void testExtractWebhookSourceFromUri() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        when(traceIdGenerator.generateWebhookTraceId("ZOOM", anyString()))
            .thenReturn(webhookTraceId);
        
        // 测试从URI提取Zoom
        request.setRequestURI("/api/webhooks/zoom/events");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("ZOOM", TraceContext.getContext("webhookSource"));
        
        // 测试从URI提取Teams
        TraceContext.clear();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        request.setRequestURI("/api/webhooks/teams/callback");
        when(traceIdGenerator.generateWebhookTraceId("TEAMS", anyString()))
            .thenReturn("WH-20250823143025-TEAMS-EVENT456-c3d4e5");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("TEAMS", TraceContext.getContext("webhookSource"));
    }

    @Test
    void testExtractWebhookSourceFromAuthHeader() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        when(traceIdGenerator.generateWebhookTraceId("ZOOM", anyString()))
            .thenReturn(webhookTraceId);
        
        // 测试从Authorization头提取
        request.setRequestURI("/api/webhooks/test");
        request.addHeader("Authorization", "Bearer zoom-token-123");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("ZOOM", TraceContext.getContext("webhookSource"));
    }

    @Test
    void testExtractEventType() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        when(traceIdGenerator.generateWebhookTraceId(anyString(), anyString()))
            .thenReturn(webhookTraceId);
        
        // 测试从X-Event-Type头提取
        request.setRequestURI("/api/webhooks/zoom");
        request.addHeader("X-Event-Type", "meeting.started");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("MEETING.STARTED", TraceContext.getContext("webhookEventType"));
        
        // 测试从X-Zm-Event-Type头提取
        TraceContext.clear();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        request.setRequestURI("/api/webhooks/zoom");
        request.addHeader("X-Zm-Event-Type", "user.created");
        when(traceIdGenerator.generateWebhookTraceId(anyString(), anyString()))
            .thenReturn("WH-20250823143025-ZOOM-EVENT456-c3d4e5");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("USER.CREATED", TraceContext.getContext("webhookEventType"));
    }

    @Test
    void testExtractEventTypeFromRequestBody() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        when(traceIdGenerator.generateWebhookTraceId(anyString(), anyString()))
            .thenReturn(webhookTraceId);
        
        // 测试从请求体提取事件类型
        request.setRequestURI("/api/webhooks/zoom");
        request.setMethod("POST");
        String requestBody = "{\"event\":\"meeting.participant_joined\",\"payload\":{\"object\":{\"id\":\"123\"}}}";
        request.setContent(requestBody.getBytes());
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("MEETING.PARTICIPANT_JOINED", TraceContext.getContext("webhookEventType"));
    }

    @Test
    void testExtractEventId() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        when(traceIdGenerator.generateWebhookTraceId(anyString(), "EVENT123"))
            .thenReturn(webhookTraceId);
        
        // 测试从X-Event-Id头提取
        request.setRequestURI("/api/webhooks/zoom");
        request.addHeader("X-Event-Id", "EVENT123");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("EVENT123", TraceContext.getContext("webhookEventId"));
        
        // 测试从X-Request-Id头提取
        TraceContext.clear();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        request.setRequestURI("/api/webhooks/zoom");
        request.addHeader("X-Request-Id", "REQ456");
        when(traceIdGenerator.generateWebhookTraceId(anyString(), "REQ456"))
            .thenReturn("WH-20250823143025-ZOOM-REQ456-c3d4e5");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("REQ456", TraceContext.getContext("webhookEventId"));
        
        // 测试从X-Zm-Request-Id头提取
        TraceContext.clear();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        request.setRequestURI("/api/webhooks/zoom");
        request.addHeader("X-Zm-Request-Id", "ZM789");
        when(traceIdGenerator.generateWebhookTraceId(anyString(), "ZM789"))
            .thenReturn("WH-20250823143025-ZOOM-ZM789-d4e5f6");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("ZM789", TraceContext.getContext("webhookEventId"));
    }

    @Test
    void testSanitizeEventId() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        when(traceIdGenerator.generateWebhookTraceId(anyString(), "EVENT123"))
            .thenReturn(webhookTraceId);
        
        // 测试事件ID清理
        request.setRequestURI("/api/webhooks/zoom");
        request.addHeader("X-Event-Id", "EVENT@#$123!@#");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("EVENT123", TraceContext.getContext("webhookEventId"));
    }

    @Test
    void testAfterCompletionSuccess() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        TraceContext.setTraceId(webhookTraceId);
        
        request.setRequestURI("/api/webhooks/zoom");
        request.setMethod("POST");
        response.setStatus(200);
        
        // 执行afterCompletion
        webhookTraceInterceptor.afterCompletion(request, response, handler, null);
        
        // 验证上下文被清理
        assertNull(TraceContext.getTraceId());
    }

    @Test
    void testAfterCompletionWithException() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        TraceContext.setTraceId(webhookTraceId);
        
        request.setRequestURI("/api/webhooks/zoom");
        request.setMethod("POST");
        response.setStatus(500);
        Exception exception = new RuntimeException("Webhook processing failed");
        
        // 执行afterCompletion
        webhookTraceInterceptor.afterCompletion(request, response, handler, exception);
        
        // 验证上下文被清理
        assertNull(TraceContext.getTraceId());
    }

    @Test
    void testGetClientIpAddress() throws Exception {
        String webhookTraceId = "WH-20250823143025-ZOOM-EVENT123-a1b2c3";
        when(traceIdGenerator.generateWebhookTraceId(anyString(), anyString()))
            .thenReturn(webhookTraceId);
        
        // 测试X-Forwarded-For头
        request.setRequestURI("/api/webhooks/zoom");
        request.addHeader("X-Forwarded-For", "*************, ********");
        request.setRemoteAddr("127.0.0.1");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        // IP地址会在日志中记录，这里主要验证不会抛异常
        
        // 测试X-Real-IP头
        TraceContext.clear();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        request.setRequestURI("/api/webhooks/zoom");
        request.addHeader("X-Real-IP", "*************");
        request.setRemoteAddr("127.0.0.1");
        when(traceIdGenerator.generateWebhookTraceId(anyString(), anyString()))
            .thenReturn("WH-20250823143025-ZOOM-EVENT456-c3d4e5");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        // IP地址会在日志中记录，这里主要验证不会抛异常
    }

    @Test
    void testIsWebhookRequest() throws Exception {
        // 测试webhook路径识别
        assertTrue(isWebhookRequest("/api/webhooks/zoom"));
        assertTrue(isWebhookRequest("/api/webhook/teams"));
        assertTrue(isWebhookRequest("/api/callback/slack"));
        
        // 测试非webhook路径
        assertFalse(isWebhookRequest("/api/users"));
        assertFalse(isWebhookRequest("/api/meetings"));
        assertFalse(isWebhookRequest("/"));
    }

    private boolean isWebhookRequest(String uri) throws Exception {
        request.setRequestURI(uri);
        
        // 如果是webhook请求，会设置TraceId
        boolean result = webhookTraceInterceptor.preHandle(request, response, handler);
        boolean isWebhook = TraceContext.getTraceId() != null;
        
        TraceContext.clear();
        return isWebhook;
    }

    @Test
    void testUnknownWebhookSource() throws Exception {
        String webhookTraceId = "WH-20250823143025-UNKNOWN-EVENT123-a1b2c3";
        when(traceIdGenerator.generateWebhookTraceId("UNKNOWN", anyString()))
            .thenReturn(webhookTraceId);
        
        // 测试未知来源的webhook
        request.setRequestURI("/api/webhooks/unknown");
        request.addHeader("User-Agent", "Unknown-Webhook/1.0");
        
        webhookTraceInterceptor.preHandle(request, response, handler);
        assertEquals("UNKNOWN", TraceContext.getContext("webhookSource"));
    }
}
