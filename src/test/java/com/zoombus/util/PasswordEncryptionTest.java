package com.zoombus.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 密码加密功能测试
 */
public class PasswordEncryptionTest {

    @Test
    public void testPasswordEncryption() {
        String originalPassword = "nvshen2018";
        
        try {
            // 测试加密
            String encryptedPassword = SimplePasswordEncryptor.encrypt(originalPassword);
            assertNotNull(encryptedPassword);
            assertNotEquals(originalPassword, encryptedPassword);
            
            System.out.println("原始密码: " + originalPassword);
            System.out.println("加密后密码: " + encryptedPassword);
            System.out.println("配置格式: ENC(" + encryptedPassword + ")");
            
            // 测试解密
            String decryptedPassword = SimplePasswordEncryptor.decrypt(encryptedPassword);
            assertEquals(originalPassword, decryptedPassword);
            
            System.out.println("解密验证: " + decryptedPassword);
            System.out.println("加密解密测试通过！");
            
        } catch (Exception e) {
            fail("密码加密测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testSpecificEncryptedPassword() {
        String originalPassword = "nvshen2018";
        String expectedEncrypted = "Ds+S3TwEqUkWVcthbG93xg==";
        
        try {
            // 测试解密我们之前生成的加密密码
            String decryptedPassword = SimplePasswordEncryptor.decrypt(expectedEncrypted);
            assertEquals(originalPassword, decryptedPassword);
            
            System.out.println("验证配置文件中的加密密码:");
            System.out.println("加密密码: " + expectedEncrypted);
            System.out.println("解密结果: " + decryptedPassword);
            System.out.println("验证成功！");
            
        } catch (Exception e) {
            fail("配置文件密码验证失败: " + e.getMessage());
        }
    }
}
