spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
  
  h2:
    console:
      enabled: false

zoom:
  api-base-url: https://api.zoom.us/v2
  account-id: test_account_id
  client-id: test_client_id
  client-secret: test_client_secret
  webhook-secret-token: test_webhook_secret

# 计费系统配置
zoombus:
  billing:
    window-expiry-scheduler:
      enabled: false  # 测试时禁用调度器
    monitor-scheduler:
      enabled: false  # 测试时禁用调度器
    auto-init:
      enabled: false  # 测试时禁用自动初始化
  # 会议报告配置
  meeting-report:
    scheduler:
      enabled: false # 测试时禁用定时任务
    fetch:
      retry-max-attempts: 2
      retry-delay: 1000
      timeout: 10000

logging:
  level:
    com.zoombus: DEBUG
    org.springframework.web: DEBUG
