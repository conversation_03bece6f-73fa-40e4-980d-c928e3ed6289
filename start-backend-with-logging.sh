#!/bin/bash

# ZoomBus 后端启动脚本 - 带日志配置
# 确保日志输出到固定文件，便于检查

echo "🚀 启动 ZoomBus 后端应用（带日志配置）"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
LOG_FILE="zoombus-application.log"
CONSOLE_LOG_FILE="zoombus-console.log"
PID_FILE="zoombus.pid"

# 检查Java版本
echo -e "${BLUE}🔍 检查Java环境${NC}"
if command -v /usr/libexec/java_home &> /dev/null; then
    # macOS系统，尝试找到Java 11
    JAVA_11_HOME=$(/usr/libexec/java_home -v 11 2>/dev/null)
    if [ -n "$JAVA_11_HOME" ]; then
        export JAVA_HOME="$JAVA_11_HOME"
        export PATH="$JAVA_HOME/bin:$PATH"
        echo "✅ 使用Java 11: $JAVA_HOME"
    fi
fi

if ! command -v java &> /dev/null; then
    echo -e "${RED}❌ 未找到Java，请安装Java 11或更高版本${NC}"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F '.' '{print $1}')
echo "Java版本: $JAVA_VERSION"

# 检查Maven
if [ -f "./mvnw" ]; then
    MVN_CMD="./mvnw"
elif command -v mvn &> /dev/null; then
    MVN_CMD="mvn"
else
    echo -e "${RED}❌ 未找到Maven${NC}"
    exit 1
fi

echo "✅ Maven命令: $MVN_CMD"

# 清理旧的日志文件（可选）
echo -e "\n${BLUE}🧹 清理旧日志文件${NC}"
read -p "是否清理旧的日志文件？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f "$LOG_FILE" "$CONSOLE_LOG_FILE" "$PID_FILE"
    echo "✅ 已清理旧日志文件"
else
    echo "保留旧日志文件"
fi

# 检查端口占用
echo -e "\n${BLUE}🔍 检查端口占用${NC}"
if lsof -i :8080 > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️ 端口8080已被占用${NC}"
    echo "当前占用进程:"
    lsof -i :8080
    echo ""
    read -p "是否停止现有进程并继续？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "停止现有进程..."
        pkill -f "spring-boot:run"
        sleep 3
    else
        echo "取消启动"
        exit 1
    fi
fi

# 创建日志目录
mkdir -p logs

# 启动应用
echo -e "\n${GREEN}🚀 启动后端应用${NC}"
echo "----------------------------------------"
echo "应用日志文件: $LOG_FILE"
echo "控制台日志文件: $CONSOLE_LOG_FILE"
echo "PID文件: $PID_FILE"
echo ""

# 设置JVM参数
JVM_OPTS="-Xmx2g -Xms512m"
JVM_OPTS="$JVM_OPTS -Dspring.profiles.active=default"
JVM_OPTS="$JVM_OPTS -Dlogging.file.name=$LOG_FILE"
JVM_OPTS="$JVM_OPTS -Dlogging.level.com.zoombus=DEBUG"
JVM_OPTS="$JVM_OPTS -Dlogging.level.org.springframework.transaction=DEBUG"

echo "JVM参数: $JVM_OPTS"
echo ""

# 启动应用并记录PID
echo "正在启动应用..."
nohup $MVN_CMD spring-boot:run -DskipTests -Dspring-boot.run.jvmArguments="$JVM_OPTS" > "$CONSOLE_LOG_FILE" 2>&1 &
APP_PID=$!

# 保存PID
echo $APP_PID > "$PID_FILE"
echo "应用PID: $APP_PID"

# 等待应用启动
echo ""
echo "等待应用启动..."
for i in {1..30}; do
    if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 应用启动成功！${NC}"
        break
    fi
    
    # 检查进程是否还在运行
    if ! kill -0 $APP_PID 2>/dev/null; then
        echo -e "${RED}❌ 应用进程意外退出${NC}"
        echo "查看控制台日志:"
        tail -20 "$CONSOLE_LOG_FILE"
        exit 1
    fi
    
    echo "等待中... ($i/30)"
    sleep 2
done

# 检查最终状态
if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
    HEALTH_STATUS=$(curl -s http://localhost:8080/actuator/health | jq -r '.status' 2>/dev/null || echo "UNKNOWN")
    echo -e "${GREEN}🎉 应用启动完成！${NC}"
    echo ""
    echo "📊 应用信息:"
    echo "  - 状态: $HEALTH_STATUS"
    echo "  - URL: http://localhost:8080"
    echo "  - 健康检查: http://localhost:8080/actuator/health"
    echo "  - PID: $APP_PID"
    echo ""
    echo "📋 日志文件:"
    echo "  - 应用日志: $LOG_FILE"
    echo "  - 控制台日志: $CONSOLE_LOG_FILE"
    echo "  - PID文件: $PID_FILE"
    echo ""
    echo "🔍 日志监控命令:"
    echo "  - 实时查看应用日志: tail -f $LOG_FILE"
    echo "  - 实时查看控制台日志: tail -f $CONSOLE_LOG_FILE"
    echo "  - 搜索错误日志: grep -i error $LOG_FILE"
    echo "  - 搜索meeting相关日志: grep -i meeting $LOG_FILE"
    echo ""
    echo "🛑 停止应用命令:"
    echo "  - 优雅停止: kill $APP_PID"
    echo "  - 强制停止: kill -9 $APP_PID"
    echo "  - 使用脚本停止: ./stop-backend.sh"
    
else
    echo -e "${RED}❌ 应用启动失败${NC}"
    echo ""
    echo "查看错误信息:"
    echo "控制台日志:"
    tail -20 "$CONSOLE_LOG_FILE"
    echo ""
    if [[ -f "$LOG_FILE" ]]; then
        echo "应用日志:"
        tail -20 "$LOG_FILE"
    fi
    exit 1
fi

# 显示初始日志
echo -e "\n${BLUE}📋 初始日志内容${NC}"
echo "----------------------------------------"
if [[ -f "$LOG_FILE" ]]; then
    echo "应用日志最后10行:"
    tail -10 "$LOG_FILE"
else
    echo "应用日志文件尚未创建，查看控制台日志:"
    tail -10 "$CONSOLE_LOG_FILE"
fi

echo ""
echo -e "${GREEN}✅ 后端应用启动完成！${NC}"
echo "应用正在后台运行，日志将写入 $LOG_FILE"
echo ""
echo "💡 提示:"
echo "  - 使用 'tail -f $LOG_FILE' 实时查看日志"
echo "  - 使用 'grep meeting $LOG_FILE' 搜索会议相关日志"
echo "  - 使用 'kill $APP_PID' 停止应用"
