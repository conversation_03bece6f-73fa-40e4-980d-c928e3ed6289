#!/bin/bash

# ZoomBus Java 11 启动脚本
# 确保使用Java 11编译和运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 强制设置Java 11环境
setup_java11() {
    log_info "🔧 配置Java 11环境..."
    
    if command -v /usr/libexec/java_home &> /dev/null; then
        # macOS系统，强制使用Java 11
        JAVA_11_HOME=$(/usr/libexec/java_home -v 11 2>/dev/null)
        if [ -n "$JAVA_11_HOME" ]; then
            export JAVA_HOME="$JAVA_11_HOME"
            export PATH="$JAVA_HOME/bin:$PATH"
            log_info "✅ 设置JAVA_HOME: $JAVA_HOME"
        else
            log_error "❌ 未找到Java 11，请安装Java 11"
            log_info "💡 安装建议: brew install openjdk@11"
            exit 1
        fi
    else
        log_error "❌ 无法检测Java环境（非macOS系统）"
        exit 1
    fi
    
    # 验证Java版本
    if ! command -v java &> /dev/null; then
        log_error "❌ Java命令不可用"
        exit 1
    fi
    
    CURRENT_JAVA_VERSION=$(java -version 2>&1 | head -n 1)
    log_info "📋 当前Java版本: $CURRENT_JAVA_VERSION"
    
    # 检查是否为Java 11
    if java -version 2>&1 | grep -q "11\."; then
        log_info "✅ Java 11环境配置成功"
    else
        log_error "❌ Java版本不正确，需要Java 11"
        exit 1
    fi
}

# 检查Maven
check_maven() {
    log_info "🔧 检查Maven环境..."
    
    if ! command -v mvn &> /dev/null; then
        log_error "❌ Maven未找到，请安装Maven"
        log_info "💡 安装建议: brew install maven"
        exit 1
    fi
    
    MAVEN_VERSION=$(mvn -version | head -n 1)
    log_info "📋 Maven版本: $MAVEN_VERSION"
    
    # 检查Maven使用的Java版本
    MAVEN_JAVA_VERSION=$(mvn -version | grep "Java version" | head -n 1)
    log_info "📋 Maven使用的Java版本: $MAVEN_JAVA_VERSION"
    
    log_info "✅ Maven环境检查通过"
}

# 清理和编译
build_project() {
    log_info "🔨 开始编译项目..."
    
    # 设置Maven环境变量
    export MAVEN_OPTS="-Xmx2g -Xms1g -XX:+UseG1GC"
    
    # 清理
    log_debug "🧹 清理项目..."
    mvn clean -q
    
    # 编译（跳过测试）
    log_debug "⚙️ 编译项目（跳过测试）..."
    mvn compile -DskipTests -q
    
    if [ $? -eq 0 ]; then
        log_info "✅ 项目编译成功"
    else
        log_error "❌ 项目编译失败"
        exit 1
    fi
}

# 启动应用
start_application() {
    log_info "🚀 启动ZoomBus应用..."
    
    # 设置JVM参数
    export MAVEN_OPTS="-Xmx2g -Xms1g -XX:+UseG1GC -Dfile.encoding=UTF-8"
    
    # 启动Spring Boot应用
    log_info "🌟 正在启动应用，请稍候..."
    log_info "📱 前端地址: http://localhost:3001"
    log_info "🔗 后端地址: http://localhost:8080"
    log_info "📊 PMI任务监控: http://localhost:3001/pmi-task-monitor"
    
    mvn spring-boot:run -Dspring-boot.run.profiles=dev -DskipTests
}

# 主函数
main() {
    echo "================================================"
    log_info "🎯 ZoomBus Java 11 启动脚本"
    log_info "📅 日期: $(date)"
    echo "================================================"
    
    # 设置Java 11环境
    setup_java11
    
    # 检查Maven
    check_maven
    
    # 编译项目
    build_project
    
    # 启动应用
    start_application
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
