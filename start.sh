#!/bin/bash

# ZoomBus 启动脚本

# ngrok配置 - 固定域名
NGROK_DOMAIN="patient-correctly-pipefish.ngrok-free.app"
NGROK_DOMAIN_ID="rd_30XRXvkrG7BXth1jBbMadiQiQwV"

echo "=== ZoomBus 启动脚本 ==="

# 强制设置Java 11环境
echo "🔧 配置Java 11环境..."

if command -v /usr/libexec/java_home &> /dev/null; then
    # macOS系统，强制使用Java 11
    JAVA_11_HOME=$(/usr/libexec/java_home -v 11 2>/dev/null)
    if [ -n "$JAVA_11_HOME" ]; then
        export JAVA_HOME="$JAVA_11_HOME"
        export PATH="$JAVA_HOME/bin:$PATH"
        echo "✅ 强制使用Java 11: $JAVA_HOME"
    else
        echo "❌ 错误: 未找到Java 11，请安装Java 11"
        echo "💡 安装建议: brew install openjdk@11"
        exit 1
    fi
else
    echo "❌ 错误: 无法检测Java环境（非macOS系统）"
    exit 1
fi

# 验证Java版本
if ! command -v java &> /dev/null; then
    echo "❌ 错误: Java命令不可用"
    exit 1
fi

CURRENT_JAVA_VERSION=$(java -version 2>&1 | head -n 1)
echo "📋 当前Java版本: $CURRENT_JAVA_VERSION"

JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F '.' '{print $1}')
if [ "$JAVA_VERSION" -ne 11 ]; then
    echo "❌ 错误: Java版本不正确，需要Java 11，当前版本: $JAVA_VERSION"
    echo "请设置JAVA_HOME环境变量指向Java 11安装目录"
    exit 1
fi

# 检查Maven (优先使用Maven Wrapper)
if [ -f "./mvnw" ]; then
    MVN_CMD="./mvnw"
elif command -v mvn &> /dev/null; then
    MVN_CMD="mvn"
else
    echo "错误: 未找到Maven，请安装Maven或使用Maven Wrapper"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请安装Node.js 16或更高版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "错误: Node.js版本过低，需要Node.js 16或更高版本"
    exit 1
fi

# 检查ngrok
NGROK_AVAILABLE=false
if command -v ngrok &> /dev/null; then
    # 测试ngrok是否能正常运行
    if ngrok version > /dev/null 2>&1; then
        NGROK_AVAILABLE=true
        echo "✓ 检测到ngrok"
    else
        echo "⚠ ngrok已安装但无法正常运行"
        echo "  可能需要重新安装: https://ngrok.com/download"
    fi
else
    echo "⚠ 未检测到ngrok，webhook功能将不可用"
    echo "  安装ngrok: https://ngrok.com/download"
fi

echo "✓ 环境检查通过"

# ngrok启动函数
start_ngrok() {
    if [ "$NGROK_AVAILABLE" = false ]; then
        echo "错误: ngrok未安装，无法启动隧道"
        echo "请访问 https://ngrok.com/download 下载安装ngrok"
        return 1
    fi

    echo "启动ngrok隧道..."

    # 检查是否已有ngrok进程在运行
    if pgrep -f "ngrok" > /dev/null; then
        echo "⚠ 检测到ngrok已在运行，正在停止..."
        pkill -f "ngrok"
        sleep 3
    fi

    # 检查认证token
    echo "检查ngrok认证配置..."
    if ! ngrok config check > /dev/null 2>&1; then
        echo "⚠ ngrok认证token未配置或无效"
        echo "请按照以下步骤配置:"
        echo "1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken"
        echo "2. 复制您的认证token"
        echo "3. 运行: ngrok config add-authtoken YOUR_TOKEN"
        echo ""
        echo "继续使用免费模式启动..."
    fi

    # 启动ngrok，连接到ngrok.com官方服务器
    echo "启动ngrok隧道到localhost:8080..."

    # 清理旧的日志文件
    rm -f ngrok.log

    # 尝试启动ngrok (使用固定域名)
    echo "使用固定域名: $NGROK_DOMAIN"
    ngrok http 8080 --domain=$NGROK_DOMAIN --log=stdout --log-level=info > ngrok.log 2>&1 &
    NGROK_PID=$!

    echo "ngrok进程ID: $NGROK_PID"

    echo "等待ngrok启动..."
    sleep 5

    # 检查ngrok进程是否还在运行
    if ! kill -0 $NGROK_PID 2>/dev/null; then
        echo "❌ ngrok进程启动失败"
        echo "查看错误日志:"
        cat ngrok.log
        echo ""
        echo "可能的解决方案:"
        echo "1. 检查认证token是否正确配置"
        echo "2. 重新安装ngrok: https://ngrok.com/download"
        echo "3. 检查网络连接"
        return 1
    fi

    # 使用固定域名，构建URL
    NGROK_URL="https://$NGROK_DOMAIN"
    echo "等待ngrok隧道建立..."
    echo "固定域名URL: $NGROK_URL"

    # 验证隧道是否成功建立
    for i in {1..15}; do
        # 检查ngrok进程是否还在运行
        if ! kill -0 $NGROK_PID 2>/dev/null; then
            echo "❌ ngrok进程意外退出"
            break
        fi

        # 检查日志中是否有成功启动的信息
        if [ -f "ngrok.log" ] && grep -q "started tunnel" ngrok.log; then
            echo "✓ 隧道建立成功 ($i/15)"
            break
        fi

        # 检查是否有错误信息
        if [ -f "ngrok.log" ] && grep -q "error\|failed\|ERROR" ngrok.log; then
            echo "❌ 检测到错误，查看日志:"
            tail -5 ngrok.log
            break
        fi

        echo "等待ngrok隧道建立... ($i/15)"
        sleep 3
    done

    # 检查隧道是否成功建立
    if [ -f "ngrok.log" ] && grep -q "started tunnel" ngrok.log; then
        echo ""
        echo "🎉 ngrok隧道启动成功!"
        echo "🌐 固定域名: $NGROK_DOMAIN"
        echo "🔗 公网访问地址: $NGROK_URL"
        echo "🎯 Webhook URL: $NGROK_URL/api/webhooks/zoom/{account_id}"
        echo "📊 ngrok控制台: http://localhost:4040"
        echo "📋 隧道信息: curl http://localhost:4040/api/tunnels"
        echo ""

        # 测试隧道连通性
        echo "测试隧道连通性..."
        if curl -s "$NGROK_URL/actuator/health" > /dev/null 2>&1; then
            echo "✅ 隧道连通性测试成功"
        else
            echo "⚠️ 隧道连通性测试失败，请检查后端服务是否运行"
        fi

        return 0
    else
        echo ""
        echo "❌ ngrok隧道启动失败"
        echo "查看详细错误信息:"
        if [ -f "ngrok.log" ]; then
            tail -10 ngrok.log
        fi
        echo ""
        echo "故障排除建议:"
        echo "1. 检查认证token是否正确"
        echo "2. 检查网络连接"
        echo "3. 查看完整日志: cat ngrok.log"

        kill $NGROK_PID 2>/dev/null
        return 1
    fi
}

# 选择启动模式
echo ""
echo "请选择启动模式:"
echo "1. 开发模式 (前后端分离启动)"
echo "2. 开发模式 + ngrok (包含webhook隧道)"
echo "3. 生产模式 (Docker Compose)"
echo "4. 仅启动后端"
echo "5. 仅启动管理端前端"
echo "6. 仅启动用户端前端"
echo "7. 仅启动两个前端"
echo "8. 仅启动ngrok隧道"
read -p "请输入选择 (1-8): " choice

case $choice in
    1)
        echo "=== 开发模式启动 ==="

        # 启动后端
        echo "启动后端服务..."

        # 配置日志文件
        LOG_FILE="zoombus-application.log"
        CONSOLE_LOG_FILE="zoombus-console.log"

        # 设置系统属性，确保日志输出到文件
        echo "📋 日志将写入: $LOG_FILE"

        $MVN_CMD spring-boot:run -DskipTests \
            -Dlogging.file.name="$LOG_FILE" \
            -Dlogging.level.com.zoombus=DEBUG \
            -Dlogging.level.org.springframework.transaction=DEBUG \
            > "$CONSOLE_LOG_FILE" 2>&1 &
        BACKEND_PID=$!

        # 等待后端启动
        echo "等待后端服务启动..."
        sleep 10

        # 检查后端是否启动成功
        if curl -s http://localhost:8080/actuator/health > /dev/null; then
            echo "✓ 后端服务启动成功"
        else
            echo "⚠ 后端服务可能未完全启动，请稍等..."
        fi

        # 启动管理端前端
        echo "启动管理端前端服务..."
        cd frontend
        if [ ! -d "node_modules" ]; then
            echo "安装管理端前端依赖..."
            npm install
        fi

        # 设置管理端前端环境变量
        export BROWSER=none  # 防止自动打开浏览器
        export PORT=3000     # 管理端使用3000端口

        npm start &
        ADMIN_FRONTEND_PID=$!
        cd ..

        # 启动用户端前端
        echo "启动用户端前端服务..."
        cd user-frontend
        if [ ! -d "node_modules" ]; then
            echo "安装用户端前端依赖..."
            npm install
        fi

        # 用户端前端使用3001端口（在vite.config.js中已配置）
        npm run dev &
        USER_FRONTEND_PID=$!
        cd ..

        echo ""
        echo "=== 启动完成 ==="
        echo "🚀 后端服务: http://localhost:8080"
        echo "🎨 管理端前端: http://localhost:3000"
        echo "👥 用户端前端: http://localhost:3001"
        echo "🗄️  H2控制台: http://localhost:8080/h2-console"
        echo "📊 健康检查: http://localhost:8080/actuator/health"
        echo ""
        echo "💡 开发提示:"
        echo "   - 管理端前端 (端口3000): 管理员使用，需要登录"
        echo "   - 用户端前端 (端口3001): 终端用户使用，无需登录"
        echo "   - 前端代码修改会自动热重载"
        echo "   - 后端API请求会自动代理到8080端口"
        echo "   - 生产环境用户端访问: http://localhost:8080/m"
        echo ""
        echo "按 Ctrl+C 停止所有服务"

        # 等待用户中断
        trap "echo '正在停止服务...'; kill $BACKEND_PID $ADMIN_FRONTEND_PID $USER_FRONTEND_PID 2>/dev/null; exit" INT
        wait
        ;;

    2)
        echo "=== 开发模式 + ngrok 启动 ==="

        # 启动后端
        echo "启动后端服务..."

        # 配置日志文件
        LOG_FILE="zoombus-application.log"
        CONSOLE_LOG_FILE="zoombus-console.log"

        # 设置系统属性，确保日志输出到文件
        echo "📋 日志将写入: $LOG_FILE"

        $MVN_CMD spring-boot:run -DskipTests \
            -Dlogging.file.name="$LOG_FILE" \
            -Dlogging.level.com.zoombus=DEBUG \
            -Dlogging.level.org.springframework.transaction=DEBUG \
            > "$CONSOLE_LOG_FILE" 2>&1 &
        BACKEND_PID=$!

        # 等待后端启动
        echo "等待后端服务启动..."
        sleep 10

        # 检查后端是否启动成功
        if curl -s http://localhost:8080/actuator/health > /dev/null; then
            echo "✓ 后端服务启动成功"
        else
            echo "⚠ 后端服务可能未完全启动，请稍等..."
        fi

        # 启动ngrok
        start_ngrok
        if [ $? -eq 0 ]; then
            NGROK_STARTED=true
        else
            NGROK_STARTED=false
            echo "⚠ ngrok启动失败，继续启动其他服务..."
        fi

        # 启动管理端前端
        echo "启动管理端前端服务..."
        cd frontend
        if [ ! -d "node_modules" ]; then
            echo "安装管理端前端依赖..."
            npm install
        fi

        export BROWSER=none
        export PORT=3000
        npm start &
        ADMIN_FRONTEND_PID=$!
        cd ..

        # 启动用户端前端
        echo "启动用户端前端服务..."
        cd user-frontend
        if [ ! -d "node_modules" ]; then
            echo "安装用户端前端依赖..."
            npm install
        fi

        npm run dev &
        USER_FRONTEND_PID=$!
        cd ..

        echo ""
        echo "=== 启动完成 ==="
        echo "🚀 后端服务: http://localhost:8080"
        echo "🎨 管理端前端: http://localhost:3000"
        echo "👥 用户端前端: http://localhost:3001"
        echo "🗄️  H2控制台: http://localhost:8080/h2-console"
        echo "📊 健康检查: http://localhost:8080/actuator/health"

        if [ "$NGROK_STARTED" = true ]; then
            echo "🌐 固定域名: $NGROK_DOMAIN"
            echo "🔗 公网访问地址: $NGROK_URL"
            echo "🎯 Webhook URL: $NGROK_URL/api/webhooks/zoom/{account_id}"
            echo "📊 ngrok控制台: http://localhost:4040"
        fi

        echo ""
        echo "💡 开发提示:"
        echo "   - 管理端前端 (端口3000): 管理员使用，需要登录"
        echo "   - 用户端前端 (端口3001): 终端用户使用，无需登录"
        echo "   - 前端代码修改会自动热重载"
        echo "   - 后端API请求会自动代理到8080端口"
        echo ""
        echo "📋 日志监控:"
        echo "   - 应用日志: tail -f $LOG_FILE"
        echo "   - 控制台日志: tail -f $CONSOLE_LOG_FILE"
        echo "   - 搜索错误: grep -i error $LOG_FILE"
        echo "   - 搜索meeting: grep -i meeting $LOG_FILE"
        echo "   - 搜索webhook: grep -i webhook $LOG_FILE"
        if [ "$NGROK_STARTED" = true ]; then
            echo "   - 使用固定域名，URL不会变化，适合长期开发"
            echo "   - Webhook URL可直接用于Zoom开发者控制台配置"
            echo "   - ngrok隧道提供HTTPS访问，适合webhook测试"
        fi
        echo ""
        echo "按 Ctrl+C 停止所有服务"

        # 等待用户中断
        if [ "$NGROK_STARTED" = true ]; then
            trap "echo '正在停止服务...'; kill $BACKEND_PID $ADMIN_FRONTEND_PID $USER_FRONTEND_PID $NGROK_PID 2>/dev/null; exit" INT
        else
            trap "echo '正在停止服务...'; kill $BACKEND_PID $ADMIN_FRONTEND_PID $USER_FRONTEND_PID 2>/dev/null; exit" INT
        fi
        wait
        ;;

    3)
        echo "=== 生产模式启动 ==="
        
        # 检查Docker
        if ! command -v docker &> /dev/null; then
            echo "错误: 未找到Docker，请安装Docker"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            echo "错误: 未找到Docker Compose，请安装Docker Compose"
            exit 1
        fi
        
        # 检查环境变量文件
        if [ ! -f ".env" ]; then
            echo "警告: 未找到.env文件，请复制.env.example并配置Zoom API信息"
            echo "cp .env.example .env"
            echo "然后编辑.env文件配置Zoom API信息"
            exit 1
        fi
        
        echo "使用Docker Compose启动..."
        docker-compose up --build
        ;;

    4)
        echo "=== 仅启动后端 ==="

        # 配置日志文件
        LOG_FILE="zoombus-application.log"
        CONSOLE_LOG_FILE="zoombus-console.log"
        PID_FILE="zoombus.pid"

        echo "启动后端服务..."
        echo "📋 日志配置:"
        echo "  - 应用日志: $LOG_FILE"
        echo "  - 控制台日志: $CONSOLE_LOG_FILE"
        echo "  - PID文件: $PID_FILE"

        # 检查端口占用
        if lsof -i :8080 > /dev/null 2>&1; then
            echo "⚠️ 端口8080已被占用，停止现有进程..."
            pkill -f "spring-boot:run"
            sleep 3
        fi

        # 设置系统属性，确保日志输出到文件
        echo "🚀 启动应用..."
        echo "日志文件: $LOG_FILE"

        # 启动应用并记录PID（使用系统属性而不是JVM参数）
        nohup $MVN_CMD spring-boot:run -DskipTests \
            -Dlogging.file.name="$LOG_FILE" \
            -Dlogging.level.com.zoombus=DEBUG \
            -Dlogging.level.org.springframework.transaction=DEBUG \
            -Dlogging.level.org.springframework.web=DEBUG \
            > "$CONSOLE_LOG_FILE" 2>&1 &
        APP_PID=$!
        echo $APP_PID > "$PID_FILE"

        echo "应用PID: $APP_PID"
        echo "等待应用启动..."

        # 等待应用启动
        for i in {1..30}; do
            if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
                echo "✅ 后端服务启动成功!"
                HEALTH_STATUS=$(curl -s http://localhost:8080/actuator/health | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
                echo "健康状态: $HEALTH_STATUS"
                break
            fi

            # 检查进程是否还在运行
            if ! kill -0 $APP_PID 2>/dev/null; then
                echo "❌ 应用进程意外退出"
                echo "查看控制台日志:"
                tail -20 "$CONSOLE_LOG_FILE"
                exit 1
            fi

            echo "等待中... ($i/30)"
            sleep 2
        done

        echo ""
        echo "🎉 后端服务启动完成!"
        echo "🚀 后端服务: http://localhost:8080"
        echo "📊 健康检查: http://localhost:8080/actuator/health"
        echo "🗄️  H2控制台: http://localhost:8080/h2-console"
        echo ""
        echo "📋 日志文件:"
        echo "  - 应用日志: $LOG_FILE"
        echo "  - 控制台日志: $CONSOLE_LOG_FILE"
        echo ""
        echo "🔍 日志监控命令:"
        echo "  - 实时查看: tail -f $LOG_FILE"
        echo "  - 搜索错误: grep -i error $LOG_FILE"
        echo "  - 搜索meeting: grep -i meeting $LOG_FILE"
        echo "  - 搜索webhook: grep -i webhook $LOG_FILE"
        echo ""
        echo "🛑 停止服务: kill $APP_PID"
        echo ""
        echo "应用正在后台运行，按 Ctrl+C 退出脚本（不会停止应用）"

        # 显示初始日志
        echo ""
        echo "📋 初始日志内容:"
        sleep 2
        if [[ -f "$LOG_FILE" ]]; then
            echo "应用日志最后10行:"
            tail -10 "$LOG_FILE"
        else
            echo "应用日志文件尚未创建，查看控制台日志:"
            tail -10 "$CONSOLE_LOG_FILE"
        fi

        # 等待用户中断（可选）
        echo ""
        echo "💡 提示: 应用已在后台运行，可以安全退出此脚本"
        read -p "按 Enter 键退出脚本，或 Ctrl+C 直接退出..."
        ;;

    5)
        echo "=== 仅启动管理端前端 ==="
        cd frontend
        if [ ! -d "node_modules" ]; then
            echo "安装管理端前端依赖..."
            npm install
        fi
        echo "启动管理端前端服务..."
        echo "🎨 管理端前端: http://localhost:3000"
        npm start
        ;;

    6)
        echo "=== 仅启动用户端前端 ==="
        cd user-frontend
        if [ ! -d "node_modules" ]; then
            echo "安装用户端前端依赖..."
            npm install
        fi
        echo "启动用户端前端服务..."
        echo "👥 用户端前端: http://localhost:3001"
        npm run dev
        ;;

    7)
        echo "=== 仅启动两个前端 ==="

        # 启动管理端前端
        echo "启动管理端前端服务..."
        cd frontend
        if [ ! -d "node_modules" ]; then
            echo "安装管理端前端依赖..."
            npm install
        fi

        export BROWSER=none
        export PORT=3000
        npm start &
        ADMIN_FRONTEND_PID=$!
        cd ..

        # 启动用户端前端
        echo "启动用户端前端服务..."
        cd user-frontend
        if [ ! -d "node_modules" ]; then
            echo "安装用户端前端依赖..."
            npm install
        fi

        npm run dev &
        USER_FRONTEND_PID=$!
        cd ..

        echo ""
        echo "=== 前端启动完成 ==="
        echo "🎨 管理端前端: http://localhost:3000"
        echo "👥 用户端前端: http://localhost:3001"
        echo ""
        echo "💡 提示:"
        echo "   - 管理端前端 (端口3000): 管理员使用"
        echo "   - 用户端前端 (端口3001): 终端用户使用"
        echo "   - 需要单独启动后端服务才能正常使用API"
        echo ""
        echo "按 Ctrl+C 停止所有前端服务"

        trap "echo '正在停止前端服务...'; kill $ADMIN_FRONTEND_PID $USER_FRONTEND_PID 2>/dev/null; exit" INT
        wait
        ;;

    8)
        echo "=== 仅启动ngrok隧道 ==="

        if [ "$NGROK_AVAILABLE" = false ]; then
            echo "错误: ngrok未安装"
            echo "请访问 https://ngrok.com/download 下载安装ngrok"
            exit 1
        fi

        echo "启动ngrok隧道连接到localhost:8080..."
        start_ngrok

        if [ $? -eq 0 ]; then
            echo ""
            echo "=== ngrok隧道启动完成 ==="
            echo "🌐 固定域名: $NGROK_DOMAIN"
            echo "🔗 公网访问地址: $NGROK_URL"
            echo "🎯 Webhook URL: $NGROK_URL/api/webhooks/zoom/{account_id}"
            echo "📊 ngrok控制台: http://localhost:4040"
            echo ""
            echo "💡 使用提示:"
            echo "   - 使用固定域名，URL永远不变"
            echo "   - 确保后端服务在8080端口运行"
            echo "   - 将Webhook URL配置到Zoom开发者控制台"
            echo "   - 隧道提供HTTPS访问，适合webhook测试"
            echo ""
            echo "按 Ctrl+C 停止ngrok隧道"

            trap "echo '正在停止ngrok隧道...'; kill $NGROK_PID 2>/dev/null; exit" INT
            wait
        else
            echo "❌ ngrok隧道启动失败"
            exit 1
        fi
        ;;

    *)
        echo "无效选择，请输入1-8"
        exit 1
        ;;
esac
