#!/bin/bash

# ZoomBus 后端停止脚本

echo "🛑 停止 ZoomBus 后端服务"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PID_FILE="zoombus.pid"

# 检查PID文件
if [[ -f "$PID_FILE" ]]; then
    APP_PID=$(cat "$PID_FILE")
    echo "从PID文件读取到进程ID: $APP_PID"
    
    # 检查进程是否存在
    if kill -0 "$APP_PID" 2>/dev/null; then
        echo "正在停止进程 $APP_PID..."
        kill "$APP_PID"
        
        # 等待进程停止
        for i in {1..10}; do
            if ! kill -0 "$APP_PID" 2>/dev/null; then
                echo -e "${GREEN}✅ 进程已停止${NC}"
                rm -f "$PID_FILE"
                break
            fi
            echo "等待进程停止... ($i/10)"
            sleep 1
        done
        
        # 如果进程仍在运行，强制停止
        if kill -0 "$APP_PID" 2>/dev/null; then
            echo -e "${YELLOW}⚠️ 进程未响应，强制停止...${NC}"
            kill -9 "$APP_PID"
            rm -f "$PID_FILE"
            echo -e "${GREEN}✅ 进程已强制停止${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ PID文件中的进程不存在，清理PID文件${NC}"
        rm -f "$PID_FILE"
    fi
else
    echo "未找到PID文件，尝试查找Spring Boot进程..."
    
    # 查找Spring Boot进程
    SPRING_PIDS=$(pgrep -f "spring-boot:run")
    if [[ -n "$SPRING_PIDS" ]]; then
        echo "找到Spring Boot进程: $SPRING_PIDS"
        echo "正在停止所有Spring Boot进程..."
        pkill -f "spring-boot:run"
        sleep 3
        
        # 检查是否还有进程
        REMAINING_PIDS=$(pgrep -f "spring-boot:run")
        if [[ -n "$REMAINING_PIDS" ]]; then
            echo -e "${YELLOW}⚠️ 强制停止剩余进程...${NC}"
            pkill -9 -f "spring-boot:run"
        fi
        echo -e "${GREEN}✅ Spring Boot进程已停止${NC}"
    else
        echo -e "${BLUE}ℹ️ 未找到运行中的Spring Boot进程${NC}"
    fi
fi

# 检查端口占用
echo ""
echo "检查端口8080占用情况..."
if lsof -i :8080 > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️ 端口8080仍被占用${NC}"
    echo "占用进程:"
    lsof -i :8080
else
    echo -e "${GREEN}✅ 端口8080已释放${NC}"
fi

echo ""
echo -e "${GREEN}🎉 后端服务停止完成${NC}"
