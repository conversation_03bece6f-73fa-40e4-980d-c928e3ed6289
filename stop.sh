#!/bin/bash

# ZoomBus 停止脚本
# 用于停止前后端应用以及相关服务

echo "=== ZoomBus 停止脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 停止函数
stop_service() {
    local service_name=$1
    local process_pattern=$2
    local port=$3
    
    echo -e "${BLUE}正在停止 $service_name...${NC}"
    
    # 方法1: 通过进程名称停止
    if [ -n "$process_pattern" ]; then
        local pids=$(pgrep -f "$process_pattern")
        if [ -n "$pids" ]; then
            echo "找到进程: $pids"
            kill $pids 2>/dev/null
            sleep 2
            
            # 检查是否还在运行，如果是则强制杀死
            local remaining_pids=$(pgrep -f "$process_pattern")
            if [ -n "$remaining_pids" ]; then
                echo "强制停止进程: $remaining_pids"
                kill -9 $remaining_pids 2>/dev/null
            fi
            echo -e "${GREEN}✓ $service_name 已停止${NC}"
        else
            echo -e "${YELLOW}⚠ $service_name 未运行${NC}"
        fi
    fi
    
    # 方法2: 通过端口停止
    if [ -n "$port" ]; then
        local port_pids=$(lsof -ti:$port 2>/dev/null)
        if [ -n "$port_pids" ]; then
            echo "停止占用端口 $port 的进程: $port_pids"
            kill $port_pids 2>/dev/null
            sleep 2
            
            # 检查端口是否还被占用
            local remaining_port_pids=$(lsof -ti:$port 2>/dev/null)
            if [ -n "$remaining_port_pids" ]; then
                echo "强制停止占用端口 $port 的进程: $remaining_port_pids"
                kill -9 $remaining_port_pids 2>/dev/null
            fi
            echo -e "${GREEN}✓ 端口 $port 已释放${NC}"
        fi
    fi
}

# 停止Docker Compose服务
stop_docker_compose() {
    echo -e "${BLUE}正在停止Docker Compose服务...${NC}"
    
    if command -v docker-compose &> /dev/null; then
        if [ -f "docker-compose.yml" ]; then
            docker-compose down
            echo -e "${GREEN}✓ Docker Compose服务已停止${NC}"
        else
            echo -e "${YELLOW}⚠ 未找到docker-compose.yml文件${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ Docker Compose未安装${NC}"
    fi
}

# 清理临时文件
cleanup_files() {
    echo -e "${BLUE}清理临时文件...${NC}"
    
    # 清理ngrok日志
    if [ -f "ngrok.log" ]; then
        rm -f ngrok.log
        echo "✓ 清理ngrok.log"
    fi
    
    # 清理其他临时文件
    rm -f /tmp/ngrok_api.json 2>/dev/null
    
    echo -e "${GREEN}✓ 临时文件清理完成${NC}"
}

# 显示停止选项
show_stop_options() {
    echo ""
    echo "请选择停止模式:"
    echo "1. 停止所有服务 (后端 + 前端 + ngrok + Docker)"
    echo "2. 停止开发模式服务 (后端 + 前端 + ngrok)"
    echo "3. 停止后端服务"
    echo "4. 停止前端服务 (管理端 + 用户端)"
    echo "5. 停止管理端前端"
    echo "6. 停止用户端前端"
    echo "7. 停止ngrok隧道"
    echo "8. 停止Docker Compose服务"
    echo "9. 仅清理临时文件"
    echo "0. 退出"
}

# 主停止逻辑
main() {
    show_stop_options
    read -p "请输入选择 (0-9): " choice
    
    case $choice in
        1)
            echo "=== 停止所有服务 ==="
            stop_service "后端服务 (Spring Boot)" "spring-boot:run" "8080"
            stop_service "管理端前端 (React)" "npm.*start" "3000"
            stop_service "用户端前端 (Vue)" "npm.*run.*dev" "3001"
            stop_service "ngrok隧道" "ngrok" ""
            stop_docker_compose
            cleanup_files
            ;;
            
        2)
            echo "=== 停止开发模式服务 ==="
            stop_service "后端服务 (Spring Boot)" "spring-boot:run" "8080"
            stop_service "管理端前端 (React)" "npm.*start" "3000"
            stop_service "用户端前端 (Vue)" "npm.*run.*dev" "3001"
            stop_service "ngrok隧道" "ngrok" ""
            cleanup_files
            ;;
            
        3)
            echo "=== 停止后端服务 ==="
            stop_service "后端服务 (Spring Boot)" "spring-boot:run" "8080"
            ;;
            
        4)
            echo "=== 停止前端服务 ==="
            stop_service "管理端前端 (React)" "npm.*start" "3000"
            stop_service "用户端前端 (Vue)" "npm.*run.*dev" "3001"
            ;;
            
        5)
            echo "=== 停止管理端前端 ==="
            stop_service "管理端前端 (React)" "npm.*start" "3000"
            ;;
            
        6)
            echo "=== 停止用户端前端 ==="
            stop_service "用户端前端 (Vue)" "npm.*run.*dev" "3001"
            ;;
            
        7)
            echo "=== 停止ngrok隧道 ==="
            stop_service "ngrok隧道" "ngrok" ""
            cleanup_files
            ;;
            
        8)
            echo "=== 停止Docker Compose服务 ==="
            stop_docker_compose
            ;;
            
        9)
            echo "=== 清理临时文件 ==="
            cleanup_files
            ;;
            
        0)
            echo "退出停止脚本"
            exit 0
            ;;
            
        *)
            echo -e "${RED}无效选择，请输入0-9${NC}"
            exit 1
            ;;
    esac
    
    echo ""
    echo -e "${GREEN}=== 停止操作完成 ===${NC}"
    
    # 显示当前运行状态
    echo ""
    echo "当前服务状态:"
    
    # 检查后端服务
    if lsof -i:8080 > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠ 后端服务 (8080端口) 仍在运行${NC}"
    else
        echo -e "${GREEN}✓ 后端服务已停止${NC}"
    fi
    
    # 检查管理端前端
    if lsof -i:3000 > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠ 管理端前端 (3000端口) 仍在运行${NC}"
    else
        echo -e "${GREEN}✓ 管理端前端已停止${NC}"
    fi
    
    # 检查用户端前端
    if lsof -i:3001 > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠ 用户端前端 (3001端口) 仍在运行${NC}"
    else
        echo -e "${GREEN}✓ 用户端前端已停止${NC}"
    fi
    
    # 检查ngrok
    if pgrep -f "ngrok" > /dev/null; then
        echo -e "${YELLOW}⚠ ngrok隧道仍在运行${NC}"
    else
        echo -e "${GREEN}✓ ngrok隧道已停止${NC}"
    fi
    
    # 检查Docker
    if command -v docker &> /dev/null && docker ps -q > /dev/null 2>&1; then
        local running_containers=$(docker ps -q | wc -l)
        if [ "$running_containers" -gt 0 ]; then
            echo -e "${YELLOW}⚠ Docker容器仍在运行 ($running_containers 个)${NC}"
        else
            echo -e "${GREEN}✓ Docker容器已停止${NC}"
        fi
    fi
}

# 快速停止模式 (通过命令行参数)
if [ $# -gt 0 ]; then
    case $1 in
        "all"|"ALL")
            echo "=== 快速停止所有服务 ==="
            stop_service "后端服务" "spring-boot:run" "8080"
            stop_service "管理端前端" "npm.*start" "3000"
            stop_service "用户端前端" "npm.*run.*dev" "3001"
            stop_service "ngrok隧道" "ngrok" ""
            stop_docker_compose
            cleanup_files
            ;;
        "dev"|"DEV")
            echo "=== 快速停止开发模式服务 ==="
            stop_service "后端服务" "spring-boot:run" "8080"
            stop_service "管理端前端" "npm.*start" "3000"
            stop_service "用户端前端" "npm.*run.*dev" "3001"
            stop_service "ngrok隧道" "ngrok" ""
            cleanup_files
            ;;
        "backend"|"BACKEND")
            echo "=== 快速停止后端服务 ==="
            stop_service "后端服务" "spring-boot:run" "8080"
            ;;
        "frontend"|"FRONTEND")
            echo "=== 快速停止前端服务 ==="
            stop_service "管理端前端" "npm.*start" "3000"
            stop_service "用户端前端" "npm.*run.*dev" "3001"
            ;;
        "ngrok"|"NGROK")
            echo "=== 快速停止ngrok隧道 ==="
            stop_service "ngrok隧道" "ngrok" ""
            cleanup_files
            ;;
        "docker"|"DOCKER")
            echo "=== 快速停止Docker服务 ==="
            stop_docker_compose
            ;;
        "help"|"HELP"|"-h"|"--help")
            echo "ZoomBus 停止脚本使用说明:"
            echo ""
            echo "交互模式:"
            echo "  ./stop.sh"
            echo ""
            echo "快速模式:"
            echo "  ./stop.sh all      - 停止所有服务"
            echo "  ./stop.sh dev      - 停止开发模式服务"
            echo "  ./stop.sh backend  - 停止后端服务"
            echo "  ./stop.sh frontend - 停止前端服务"
            echo "  ./stop.sh ngrok    - 停止ngrok隧道"
            echo "  ./stop.sh docker   - 停止Docker服务"
            echo "  ./stop.sh help     - 显示帮助信息"
            ;;
        *)
            echo -e "${RED}未知参数: $1${NC}"
            echo "使用 './stop.sh help' 查看帮助信息"
            exit 1
            ;;
    esac
else
    # 交互模式
    main
fi

echo ""
echo -e "${BLUE}感谢使用ZoomBus停止脚本！${NC}"
