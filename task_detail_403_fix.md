# 任务详情403权限问题修复

## 🔍 **问题分析**

### **错误现象**
```
Request URL: http://localhost:8080/api/pmi-scheduled-tasks/29
Request Method: GET
Status Code: 403 Forbidden
```

### **问题根源**
1. **权限限制**：PMI任务详情API需要`ADMIN`或`SUPER_ADMIN`角色权限
2. **API调用方式**：直接使用`fetch`而不是配置好的axios实例，缺少认证头
3. **用户权限不足**：当前用户可能没有足够的权限访问任务详情

### **后端权限配置**
```java
@GetMapping("/{id}")
@PreAuthorize("hasAnyRole('ADMIN', 'SUPER_ADMIN')")
public ApiResponse<PmiScheduledTaskInfo> getTaskDetail(@PathVariable Long id)
```

## ✅ **解决方案**

### **1. 修复API调用方式**

#### **修改前：直接使用fetch**
```javascript
// 问题代码：缺少认证头
const response = await fetch(`http://localhost:8080/api/pmi-scheduled-tasks/${id}`);
```

#### **修改后：使用配置好的API服务**
```javascript
// 修复后：使用带认证的API服务
const response = await pmiTaskApi.getTaskDetail(id);
```

### **2. 添加权限错误处理**

#### **任务详情页面 (PmiTaskManagement.js)**
```javascript
} catch (error) {
  console.error('加载任务详情失败:', error);
  
  // 检查是否是权限问题
  if (error.response && error.response.status === 403) {
    message.error('权限不足：需要管理员权限才能查看任务详情');
    setTaskDetail({
      id: id,
      error: 'PERMISSION_DENIED',
      message: '权限不足：需要管理员权限才能查看任务详情'
    });
  } else {
    message.error('加载任务详情失败');
    setTaskDetail({
      id: id,
      error: 'LOAD_FAILED',
      message: '加载任务详情失败'
    });
  }
}
```

#### **PMI计划管理页面 (PmiScheduleManagement.js)**
```javascript
} catch (error) {
  console.error('加载任务详情失败:', error);
  
  // 检查是否是权限问题
  if (error.response && error.response.status === 403) {
    message.error('权限不足：需要管理员权限才能查看任务详情');
    setTaskDetail({
      id: taskId,
      error: 'PERMISSION_DENIED',
      message: '权限不足：需要管理员权限才能查看任务详情'
    });
  } else {
    message.error('加载任务详情失败');
    setTaskDetail({
      id: taskId,
      error: 'LOAD_FAILED',
      message: '加载任务详情失败'
    });
    setTaskDetailVisible(false);
  }
}
```

### **3. 添加错误状态显示**

#### **错误信息展示组件**
```javascript
taskDetail.error ? (
  <div style={{ textAlign: 'center', padding: '40px' }}>
    <Alert
      message={taskDetail.error === 'PERMISSION_DENIED' ? '权限不足' : '加载失败'}
      description={taskDetail.message}
      type="error"
      showIcon
      style={{ marginBottom: 16 }}
    />
    <div style={{ color: '#999' }}>
      任务ID: {taskDetail.id}
    </div>
  </div>
) : (
  // 正常的任务详情显示
  <div>
    {/* 任务详情内容 */}
  </div>
)
```

## 🎯 **修复效果**

### **1. API调用修复**
- ✅ 使用正确的API服务，自动添加认证头
- ✅ 避免直接使用fetch导致的认证问题
- ✅ 利用axios拦截器的错误处理机制

### **2. 权限错误处理**
- ✅ 识别403权限错误，显示友好的错误信息
- ✅ 区分权限不足和其他类型的错误
- ✅ 提供明确的权限要求说明

### **3. 用户体验优化**
- ✅ 权限不足时显示清晰的错误提示
- ✅ 保持弹窗打开，显示错误信息而不是直接关闭
- ✅ 提供任务ID信息，便于用户识别

## 🔧 **测试验证**

### **1. 权限充足的用户**
- 访问任务详情应该正常显示完整信息
- 弹窗中显示任务状态、类型、执行时间等详细信息

### **2. 权限不足的用户**
- 显示权限不足的错误提示
- 错误信息清晰说明需要管理员权限
- 弹窗保持打开状态，显示任务ID

### **3. 网络错误情况**
- 显示加载失败的错误提示
- 提供重试或联系管理员的建议

## 📊 **修改文件清单**

### **前端文件**
1. **`frontend/src/pages/PmiTaskManagement.js`**
   - 修改API调用方式：使用`pmiTaskApi.getTaskDetail()`
   - 添加权限错误处理逻辑
   - 添加错误状态显示组件

2. **`frontend/src/pages/PmiScheduleManagement.js`**
   - 修改API调用方式：使用`pmiTaskApi.getTaskDetail()`
   - 添加权限错误处理逻辑
   - 添加错误状态显示组件
   - 添加`Descriptions`组件导入

## 🎯 **权限说明**

### **当前权限要求**
- **任务详情查看**：需要`ADMIN`或`SUPER_ADMIN`角色
- **任务执行**：需要`SUPER_ADMIN`角色
- **任务取消**：需要`SUPER_ADMIN`角色
- **任务重新调度**：需要`ADMIN`或`SUPER_ADMIN`角色

### **权限获取方式**
1. 联系系统管理员分配相应角色
2. 使用具有管理员权限的账号登录
3. 检查当前用户的角色配置

## 📝 **后续优化建议**

### **1. 权限优化**
- 考虑为普通用户提供只读的任务信息查看权限
- 区分任务查看和任务操作的权限级别

### **2. 用户体验优化**
- 在界面上提前显示权限要求
- 为没有权限的用户隐藏相关功能入口

### **3. 错误处理完善**
- 添加更详细的错误分类和处理
- 提供权限申请的指导链接

## 🎉 **总结**

通过修复API调用方式和添加完善的权限错误处理，解决了任务详情403权限问题：

1. **修复了API调用**：使用正确的认证API服务
2. **优化了错误处理**：区分权限错误和其他错误
3. **改善了用户体验**：提供清晰的错误提示和说明

**🎉 现在用户在访问任务详情时，如果权限不足会看到友好的错误提示，如果权限充足则能正常查看详细信息！**
