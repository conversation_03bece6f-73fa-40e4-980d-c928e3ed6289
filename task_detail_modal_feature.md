# 任务详情弹窗功能实现

## 🎯 **功能需求**
1. 修复任务详情页面中"任务类型"和"任务状态"显示不正确的问题
2. 在PMI计划管理页面中以弹窗形式展示任务详情，避免跳转到新页面

## 🔍 **问题分析**

### **原有问题**
1. **任务详情页面使用模拟数据**：`PmiTaskManagement.js` 中使用的是硬编码的模拟数据，而不是真实的API数据
2. **任务类型显示不准确**：使用简单的条件判断而不是标准的类型映射
3. **跳转到新页面**：点击任务详情链接会打开新窗口，用户体验不佳

## ✅ **解决方案**

### **1. 修复任务详情页面**

#### **使用真实API数据**
修改 `PmiTaskManagement.js` 中的 `loadTaskDetail` 函数：

```javascript
// 修改前：使用模拟数据
const mockTaskDetail = {
  id: id,
  taskKey: `PMI_OPEN_${id}_${Date.now()}`,
  taskType: id % 2 === 0 ? 'PMI_WINDOW_OPEN' : 'PMI_WINDOW_CLOSE',
  // ... 其他模拟数据
};

// 修改后：调用真实API
const response = await fetch(`http://localhost:8080/api/pmi-scheduled-tasks/${id}`);
const result = await response.json();

if (result.success && result.data) {
  setTaskDetail(result.data);
} else {
  throw new Error(result.message || '获取任务详情失败');
}
```

#### **使用标准的任务类型映射**
添加正确的导入和使用标准映射函数：

```javascript
import { getTaskTypeText, getTaskStatusText as getApiTaskStatusText } from '../services/pmiTaskApi';

// 在组件中使用
<Statistic
  title="任务类型"
  value={getTaskTypeText(taskDetail.taskType)}
/>
```

### **2. 在PMI计划管理页面添加任务详情弹窗**

#### **添加状态管理**
在 `PmiScheduleManagement.js` 中添加弹窗相关状态：

```javascript
// 任务详情弹窗状态
const [taskDetailVisible, setTaskDetailVisible] = useState(false);
const [taskDetail, setTaskDetail] = useState(null);
const [taskDetailLoading, setTaskDetailLoading] = useState(false);
```

#### **修改任务详情查看函数**
将原来打开新窗口的方式改为弹窗显示：

```javascript
// 修改前：打开新窗口
const handleViewTaskDetail = (taskId) => {
  const taskDetailUrl = `/pmi-task-management?taskId=${taskId}`;
  window.open(taskDetailUrl, '_blank');
};

// 修改后：显示弹窗
const handleViewTaskDetail = async (taskId) => {
  try {
    setTaskDetailLoading(true);
    setTaskDetailVisible(true);
    
    const response = await fetch(`http://localhost:8080/api/pmi-scheduled-tasks/${taskId}`);
    const result = await response.json();
    
    if (result.success && result.data) {
      setTaskDetail(result.data);
    } else {
      throw new Error(result.message || '获取任务详情失败');
    }
  } catch (error) {
    console.error('加载任务详情失败:', error);
    message.error('加载任务详情失败');
    setTaskDetailVisible(false);
  } finally {
    setTaskDetailLoading(false);
  }
};
```

#### **添加任务详情弹窗组件**
在页面中添加完整的任务详情弹窗：

```javascript
{/* 任务详情弹窗 */}
<Modal
  title="任务详情"
  open={taskDetailVisible}
  onCancel={handleCloseTaskDetail}
  width={800}
  footer={[
    <Button key="close" onClick={handleCloseTaskDetail}>
      关闭
    </Button>
  ]}
>
  {taskDetailLoading ? (
    <div style={{ textAlign: 'center', padding: '40px' }}>
      <Spin size="large" />
    </div>
  ) : taskDetail ? (
    <div>
      {/* 任务状态统计 */}
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic
            title="任务状态"
            value={getTaskStatusText(taskDetail.status)}
            prefix={getTaskStatusIcon(taskDetail.status)}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="任务类型"
            value={getTaskTypeText(taskDetail.taskType)}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="重试次数"
            value={taskDetail.retryCount || 0}
          />
        </Col>
      </Row>

      {/* 任务详细信息 */}
      <Descriptions
        title="任务详情"
        bordered
        column={1}
        style={{ marginTop: 24 }}
      >
        <Descriptions.Item label="任务ID">{taskDetail.id}</Descriptions.Item>
        <Descriptions.Item label="任务键">{taskDetail.taskKey}</Descriptions.Item>
        <Descriptions.Item label="PMI窗口ID">{taskDetail.pmiWindowId}</Descriptions.Item>
        <Descriptions.Item label="计划执行时间">{taskDetail.scheduledTime}</Descriptions.Item>
        {/* 其他字段... */}
      </Descriptions>

      {/* 状态相关的提示信息 */}
      {taskDetail.status === 'FAILED' && (
        <Alert
          message="任务执行失败"
          description={taskDetail.errorMessage || '任务执行过程中发生错误，请检查系统日志获取详细信息。'}
          type="error"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </div>
  ) : (
    <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
      暂无任务详情
    </div>
  )}
</Modal>
```

#### **添加必要的工具函数**
添加任务状态图标映射函数：

```javascript
// 任务状态图标映射
const getTaskStatusIcon = (status) => {
  const statusIcons = {
    'SCHEDULED': <ClockCircleOutlined />,
    'EXECUTING': <PlayCircleOutlined />,
    'COMPLETED': <CheckCircleOutlined />,
    'FAILED': <ExclamationCircleOutlined />,
    'CANCELLED': <StopOutlined />,
    'RETRY': <ReloadOutlined />
  };
  return statusIcons[status] || <InfoCircleOutlined />;
};
```

## 🎯 **功能特点**

### **1. 数据准确性**
- ✅ 使用真实的API数据而不是模拟数据
- ✅ 正确显示任务类型和状态
- ✅ 实时反映任务的最新状态

### **2. 用户体验优化**
- ✅ 弹窗显示，避免页面跳转
- ✅ 在当前页面上下文中查看详情
- ✅ 快速关闭，返回原页面

### **3. 界面一致性**
- ✅ 使用统一的任务类型和状态映射
- ✅ 保持与其他页面相同的视觉风格
- ✅ 响应式设计，适配不同屏幕尺寸

### **4. 功能完整性**
- ✅ 显示完整的任务信息
- ✅ 根据任务状态显示相应的提示信息
- ✅ 支持错误处理和加载状态

## 🔧 **测试方法**

### **1. 任务详情页面测试**
1. 访问：`http://localhost:3000/pmi-task-management?taskId=29`
2. 验证任务类型和状态显示是否正确
3. 检查是否使用真实API数据

### **2. PMI计划管理页面弹窗测试**
1. 访问：`http://localhost:3000/pmi-schedule-management/320`
2. 展开计划窗口信息
3. 点击任务状态中的"详情"链接
4. 验证是否以弹窗形式显示任务详情
5. 检查弹窗中的任务类型和状态是否正确

### **3. 边界情况测试**
- 测试无效任务ID的处理
- 测试网络错误的处理
- 测试加载状态的显示

## 📊 **修改文件清单**

### **前端文件**
1. `frontend/src/pages/PmiTaskManagement.js`
   - 修改 `loadTaskDetail` 函数使用真实API
   - 添加正确的任务类型映射导入
   - 修复任务类型显示

2. `frontend/src/pages/PmiScheduleManagement.js`
   - 添加任务详情弹窗状态管理
   - 修改 `handleViewTaskDetail` 函数
   - 添加任务详情弹窗组件
   - 添加任务状态图标映射函数
   - 添加必要的图标导入

## 📝 **预期效果**

### **用户体验提升**
- ✅ 任务详情显示准确，不再有错误信息
- ✅ 在当前页面查看详情，无需跳转
- ✅ 快速访问任务信息，提高工作效率

### **数据一致性**
- ✅ 所有页面使用统一的任务类型和状态映射
- ✅ 实时显示最新的任务状态
- ✅ 避免模拟数据导致的信息不准确

### **界面优化**
- ✅ 弹窗设计美观，信息展示清晰
- ✅ 加载状态和错误处理完善
- ✅ 保持与整体设计风格一致

## 🎉 **总结**

通过这次修改，解决了以下问题：

1. **修复了任务详情页面的数据问题**：从模拟数据改为真实API数据
2. **修复了任务类型和状态显示问题**：使用标准的映射函数
3. **优化了用户体验**：在PMI计划管理页面以弹窗形式显示任务详情
4. **提高了界面一致性**：统一了任务信息的显示方式

**🎉 现在用户可以在 `http://localhost:3000/pmi-schedule-management/320` 页面中点击任务详情链接，以弹窗形式查看准确的任务信息，无需跳转到新页面！**
