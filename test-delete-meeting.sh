#!/bin/bash

# 测试删除会议70的脚本
# 这个脚本会先登录获取token，然后调用删除API来测试删除功能

echo "=== 测试删除会议70 ==="

# 首先检查会议是否存在
echo "1. 检查会议70是否存在..."
mysql -u root -pnvshen2018 zoombusV -e "SELECT id, zoom_meeting_id, topic, status FROM t_meetings WHERE id = 70;"

echo ""
echo "2. 登录获取JWT token..."

# 登录获取token
login_response=$(curl -s -X POST "http://localhost:8080/api/auth/login" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{"username":"admin","password":"admin123"}')

echo "登录响应: $login_response"

# 提取token
token=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$token" ]; then
  echo "❌ 登录失败，无法获取token"
  exit 1
fi

echo "✅ 成功获取token: ${token:0:20}..."

echo ""
echo "3. 使用token删除会议70..."

# 调用删除API
delete_response=$(curl -s -X DELETE "http://localhost:8080/api/meetings/70" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $token" \
  --noproxy "*" \
  -w "HTTP_STATUS:%{http_code}")

echo "删除API响应: $delete_response"

echo ""
echo "4. 检查会议是否已被删除..."
mysql -u root -pnvshen2018 zoombusV -e "SELECT id, zoom_meeting_id, topic, status FROM t_meetings WHERE id = 70;"

echo ""
echo "5. 检查相关的zoom_meeting_details记录..."
mysql -u root -pnvshen2018 zoombusV -e "SELECT * FROM t_zoom_meeting_details WHERE meeting_id = 70;"

echo ""
echo "6. 检查后端日志中的删除相关信息..."
tail -50 backend.log | grep -E "(删除会议|deleteMeeting|Zoom API|会议.*70)" || echo "没有找到相关日志"

echo "=== 测试完成 ==="
