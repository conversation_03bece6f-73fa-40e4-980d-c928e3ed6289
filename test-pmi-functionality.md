# PMI功能测试指南

## 测试环境

- 后端服务：http://localhost:8080
- 用户前端开发服务：http://localhost:3001
- 用户前端生产访问：http://localhost:8080/m

## 测试步骤

### 1. 验证后端API

#### 测试PMI信息获取
```bash
# 假设有一个PMI号码为 2345678901
curl -X GET "http://localhost:8080/api/public/pmi/2345678901"
```

#### 测试PMI激活
```bash
curl -X POST "http://localhost:8080/api/public/pmi/2345678901/activate"
```

### 2. 验证前端页面

#### 开发环境测试
1. 访问 http://localhost:3001/m
2. 输入PMI号码进行测试
3. 访问 http://localhost:3001/m/2345678901 直接测试特定PMI

#### 生产环境测试
1. 访问 http://localhost:8080/m
2. 访问 http://localhost:8080/m/2345678901

### 3. 功能验证清单

- [ ] 页面正常加载
- [ ] PMI号码输入功能正常
- [ ] PMI信息正确显示
- [ ] 一键开启PMI功能正常
- [ ] 复制信息功能正常
- [ ] 错误处理正常（无效PMI号码）
- [ ] 响应式设计在移动设备上正常

### 4. 预期结果

#### 成功场景
- PMI信息正确显示（号码、密码）
- 点击"一键开启PMI"后弹出成功消息
- 自动打开主持人链接（新窗口）
- 复制信息包含完整的会议详情

#### 错误场景
- 无效PMI号码显示404错误
- 无可用Zoom账号时显示相应错误信息
- 网络错误时显示友好的错误提示

## 注意事项

1. 确保数据库中有PMI记录用于测试
2. 确保有可用的Zoom账号（user_type=LICENSED, account_usage=PUBLIC_HOST, status=ACTIVE, in_use=false）
3. 测试前请确认Zoom API配置正确

## 创建测试数据

如果需要创建测试PMI记录，可以通过管理端创建：
1. 访问 http://localhost:8080/pmi-management
2. 点击"生成PMI"创建测试记录
3. 使用生成的PMI号码进行用户端测试
