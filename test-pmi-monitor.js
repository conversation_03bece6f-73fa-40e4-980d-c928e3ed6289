const { chromium } = require('playwright');

async function testPmiMonitor() {
  console.log('🎭 开始Playwright测试...');
  
  // 启动浏览器
  const browser = await chromium.launch({ 
    headless: false,  // 显示浏览器窗口
    slowMo: 1000      // 每个操作间隔1秒
  });
  
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  
  const page = await context.newPage();
  
  try {
    console.log('📱 访问前端应用...');
    
    // 1. 访问前端应用
    await page.goto('http://localhost:3000');
    await page.waitForTimeout(2000);
    
    console.log('🔐 尝试登录...');
    
    // 2. 检查是否在登录页面
    const isLoginPage = await page.locator('input[placeholder="用户名"]').isVisible();
    
    if (isLoginPage) {
      console.log('📝 填写登录信息...');
      
      // 填写登录表单
      await page.fill('input[placeholder="用户名"]', 'admin');
      await page.fill('input[placeholder="密码"]', 'admin123');
      
      // 点击登录按钮
      await page.click('button[type="submit"]');
      
      // 等待登录完成
      await page.waitForTimeout(3000);
      
      console.log('✅ 登录完成');
    } else {
      console.log('ℹ️ 已经登录或不在登录页面');
    }
    
    console.log('🎯 导航到PMI任务监控页面...');
    
    // 3. 导航到PMI任务监控页面
    await page.goto('http://localhost:3000/pmi-task-monitor');
    await page.waitForTimeout(3000);
    
    console.log('🔍 检查页面元素...');
    
    // 4. 检查页面是否正常加载
    const pageTitle = await page.title();
    console.log('页面标题:', pageTitle);
    
    // 检查是否有错误信息
    const errorElements = await page.locator('.ant-message-error').count();
    if (errorElements > 0) {
      console.log('⚠️ 发现错误消息');
      const errorTexts = await page.locator('.ant-message-error').allTextContents();
      console.log('错误信息:', errorTexts);
    }
    
    // 检查统计卡片
    const statCards = await page.locator('.ant-statistic').count();
    console.log('统计卡片数量:', statCards);
    
    // 检查表格
    const tableExists = await page.locator('.ant-table').isVisible();
    console.log('表格是否存在:', tableExists);
    
    if (tableExists) {
      const tableRows = await page.locator('.ant-table-tbody tr').count();
      console.log('表格行数:', tableRows);
    }
    
    // 5. 检查API调用
    console.log('🌐 监听网络请求...');
    
    // 监听网络请求
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log(`API请求: ${response.status()} ${response.url()}`);
      }
    });
    
    // 刷新页面触发API调用
    await page.reload();
    await page.waitForTimeout(5000);
    
    console.log('📱 测试移动端适配...');
    
    // 6. 测试移动端适配
    await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE尺寸
    await page.waitForTimeout(2000);
    
    // 检查移动端布局
    const isMobileLayout = await page.locator('.ant-layout-sider-collapsed').isVisible();
    console.log('移动端布局:', isMobileLayout ? '侧边栏已收起' : '桌面端布局');
    
    console.log('✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 保持浏览器打开5秒以便观察
    await page.waitForTimeout(5000);
    await browser.close();
  }
}

// 运行测试
testPmiMonitor().catch(console.error);
