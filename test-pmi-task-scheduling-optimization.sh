#!/bin/bash

# 测试PMI任务调度优化效果
# 验证长时间SCHEDULED状态任务的处理

set -e

BASE_URL="http://localhost:8080"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=== PMI任务调度优化测试 ==="
echo "时间: $(date)"
echo

# 1. 获取任务调度统计信息
log_info "1. 获取任务调度统计信息..."

STATS_RESPONSE=$(curl -s "$BASE_URL/api/pmi-task-monitor/stats")

if [ $? -ne 0 ]; then
    log_error "无法连接到API服务器"
    exit 1
fi

echo "任务调度统计:"
echo "$STATS_RESPONSE" | jq '.data'

SCHEDULED_COUNT=$(echo "$STATS_RESPONSE" | jq -r '.data.scheduledCount')
EXECUTING_COUNT=$(echo "$STATS_RESPONSE" | jq -r '.data.executingCount')
FAILED_COUNT=$(echo "$STATS_RESPONSE" | jq -r '.data.failedCount')
SCHEDULER_TASK_COUNT=$(echo "$STATS_RESPONSE" | jq -r '.scheduledTaskCount')

log_info "当前状态: SCHEDULED=$SCHEDULED_COUNT, EXECUTING=$EXECUTING_COUNT, FAILED=$FAILED_COUNT"
log_info "调度器中任务数: $SCHEDULER_TASK_COUNT"

echo

# 2. 检查长时间处于SCHEDULED状态的任务
log_info "2. 检查长时间处于SCHEDULED状态的任务..."

STUCK_RESPONSE=$(curl -s "$BASE_URL/api/pmi-task-monitor/stuck-scheduled?minutes=5")
STUCK_COUNT=$(echo "$STUCK_RESPONSE" | jq -r '.count')

log_info "发现长时间SCHEDULED任务: $STUCK_COUNT 个"

if [ "$STUCK_COUNT" -gt 0 ]; then
    log_warning "存在长时间SCHEDULED的任务:"
    echo "$STUCK_RESPONSE" | jq '.data[] | {id, taskType, status, scheduledTime, createdAt}'
else
    log_success "没有发现长时间SCHEDULED的任务"
fi

echo

# 3. 检查过期的SCHEDULED任务
log_info "3. 检查过期的SCHEDULED任务..."

EXPIRED_RESPONSE=$(curl -s "$BASE_URL/api/pmi-task-monitor/expired-scheduled")
EXPIRED_COUNT=$(echo "$EXPIRED_RESPONSE" | jq -r '.count')

log_info "发现过期SCHEDULED任务: $EXPIRED_COUNT 个"

if [ "$EXPIRED_COUNT" -gt 0 ]; then
    log_warning "存在过期的SCHEDULED任务:"
    echo "$EXPIRED_RESPONSE" | jq '.data[] | {id, taskType, status, scheduledTime, createdAt}'
else
    log_success "没有发现过期的SCHEDULED任务"
fi

echo

# 4. 手动触发检查长时间SCHEDULED任务
log_info "4. 手动触发检查长时间SCHEDULED任务..."

CHECK_RESPONSE=$(curl -s -X POST "$BASE_URL/api/pmi-task-monitor/check-stuck-scheduled")
CHECK_SUCCESS=$(echo "$CHECK_RESPONSE" | jq -r '.success')

if [ "$CHECK_SUCCESS" = "true" ]; then
    log_success "手动检查执行成功"
else
    log_error "手动检查执行失败"
    echo "$CHECK_RESPONSE" | jq '.message'
fi

echo

# 5. 等待处理完成后重新检查
log_info "5. 等待处理完成后重新检查..."
sleep 10

STATS_RESPONSE_AFTER=$(curl -s "$BASE_URL/api/pmi-task-monitor/stats")
SCHEDULED_COUNT_AFTER=$(echo "$STATS_RESPONSE_AFTER" | jq -r '.data.scheduledCount')
FAILED_COUNT_AFTER=$(echo "$STATS_RESPONSE_AFTER" | jq -r '.data.failedCount')

log_info "处理后状态: SCHEDULED=$SCHEDULED_COUNT_AFTER, FAILED=$FAILED_COUNT_AFTER"

# 比较处理前后的变化
SCHEDULED_CHANGE=$((SCHEDULED_COUNT_AFTER - SCHEDULED_COUNT))
FAILED_CHANGE=$((FAILED_COUNT_AFTER - FAILED_COUNT))

if [ "$SCHEDULED_CHANGE" -lt 0 ]; then
    log_success "SCHEDULED任务数量减少: $SCHEDULED_CHANGE"
else
    log_info "SCHEDULED任务数量变化: $SCHEDULED_CHANGE"
fi

if [ "$FAILED_CHANGE" -gt 0 ]; then
    log_info "FAILED任务数量增加: $FAILED_CHANGE (可能是过期任务被标记为失败)"
fi

echo

# 6. 手动触发健康检查
log_info "6. 手动触发健康检查..."

HEALTH_RESPONSE=$(curl -s -X POST "$BASE_URL/api/pmi-task-monitor/health-check")
HEALTH_SUCCESS=$(echo "$HEALTH_RESPONSE" | jq -r '.success')

if [ "$HEALTH_SUCCESS" = "true" ]; then
    log_success "健康检查执行成功"
else
    log_error "健康检查执行失败"
    echo "$HEALTH_RESPONSE" | jq '.message'
fi

echo

# 7. 获取最近的任务列表
log_info "7. 获取最近的任务列表..."

RECENT_RESPONSE=$(curl -s "$BASE_URL/api/pmi-task-monitor/recent?size=10")
RECENT_SUCCESS=$(echo "$RECENT_RESPONSE" | jq -r '.success')

if [ "$RECENT_SUCCESS" = "true" ]; then
    log_info "最近10个任务:"
    echo "$RECENT_RESPONSE" | jq '.data.content[] | {id, taskType, status, scheduledTime, actualExecutionTime, errorMessage}'
else
    log_error "获取最近任务失败"
fi

echo

# 8. 测试特定任务的调度状态检查
log_info "8. 测试任务调度状态检查..."

# 获取一个SCHEDULED状态的任务ID进行测试
SCHEDULED_TASK_ID=$(echo "$RECENT_RESPONSE" | jq -r '.data.content[] | select(.status == "SCHEDULED") | .id' | head -1)

if [ "$SCHEDULED_TASK_ID" != "null" ] && [ -n "$SCHEDULED_TASK_ID" ]; then
    log_info "检查任务 $SCHEDULED_TASK_ID 的调度状态..."
    
    TASK_STATUS_RESPONSE=$(curl -s "$BASE_URL/api/pmi-task-monitor/task/$SCHEDULED_TASK_ID/scheduling-status")
    TASK_STATUS_SUCCESS=$(echo "$TASK_STATUS_RESPONSE" | jq -r '.success')
    
    if [ "$TASK_STATUS_SUCCESS" = "true" ]; then
        IS_PROPERLY_SCHEDULED=$(echo "$TASK_STATUS_RESPONSE" | jq -r '.data.isProperlyScheduled')
        log_info "任务调度状态: isProperlyScheduled=$IS_PROPERLY_SCHEDULED"
        
        if [ "$IS_PROPERLY_SCHEDULED" = "false" ]; then
            log_warning "任务未被正确调度，尝试强制重新调度..."
            
            RESCHEDULE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/pmi-task-monitor/task/$SCHEDULED_TASK_ID/reschedule")
            RESCHEDULE_SUCCESS=$(echo "$RESCHEDULE_RESPONSE" | jq -r '.success')
            
            if [ "$RESCHEDULE_SUCCESS" = "true" ]; then
                log_success "任务重新调度成功"
            else
                log_error "任务重新调度失败"
                echo "$RESCHEDULE_RESPONSE" | jq '.message'
            fi
        else
            log_success "任务已被正确调度"
        fi
    else
        log_error "检查任务调度状态失败"
    fi
else
    log_info "没有找到SCHEDULED状态的任务进行测试"
fi

echo

# 9. 总结测试结果
log_info "=== 测试总结 ==="

echo "优化前后对比:"
echo "  SCHEDULED任务数量: $SCHEDULED_COUNT → $SCHEDULED_COUNT_AFTER (变化: $SCHEDULED_CHANGE)"
echo "  FAILED任务数量: $FAILED_COUNT → $FAILED_COUNT_AFTER (变化: $FAILED_CHANGE)"
echo "  长时间SCHEDULED任务: $STUCK_COUNT 个"
echo "  过期SCHEDULED任务: $EXPIRED_COUNT 个"

echo
echo "优化效果评估:"

if [ "$STUCK_COUNT" -eq 0 ] && [ "$EXPIRED_COUNT" -eq 0 ]; then
    log_success "✅ 没有发现问题任务，调度系统运行正常"
elif [ "$SCHEDULED_CHANGE" -lt 0 ]; then
    log_success "✅ 优化生效，问题任务已被处理"
else
    log_warning "⚠️ 仍存在问题任务，需要进一步调查"
fi

echo
echo "建议:"
echo "1. 定期监控任务调度统计信息"
echo "2. 关注长时间SCHEDULED状态的任务"
echo "3. 及时处理过期的任务"
echo "4. 监控任务失败率和重试情况"

log_success "测试完成！"
