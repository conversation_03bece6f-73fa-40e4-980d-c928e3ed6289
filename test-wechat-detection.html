<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信浏览器检测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            font-weight: bold;
            margin: 10px 0;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .info {
            background: #f0f8ff;
            padding: 10px;
            border-left: 4px solid #007cba;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>微信浏览器检测功能测试</h1>
    
    <div class="info">
        <strong>说明：</strong>此页面用于测试微信浏览器检测功能。在正常浏览器中访问会显示正常页面，在微信中访问会显示引导页面。
    </div>

    <div class="test-section">
        <h3>当前浏览器信息</h3>
        <div id="userAgent"></div>
        <div id="browserInfo"></div>
    </div>

    <div class="test-section">
        <h3>检测结果</h3>
        <div id="detectionResult"></div>
    </div>

    <div class="test-section">
        <h3>模拟测试</h3>
        <p>点击下面的按钮模拟不同的浏览器环境：</p>
        <button onclick="simulateWechat()">模拟微信浏览器</button>
        <button onclick="simulateNormal()">模拟普通浏览器</button>
        <button onclick="resetUserAgent()">恢复原始User-Agent</button>
    </div>

    <div class="test-section">
        <h3>测试链接</h3>
        <p>在微信中打开以下链接测试引导功能：</p>
        <ul>
            <li><a href="http://127.0.0.1:3002/m/2024062168" target="_blank">PMI测试页面（开发环境）</a></li>
            <li><a href="http://127.0.0.1:3002/meeting/test-uuid-123" target="_blank">会议测试页面（开发环境）</a></li>
            <li><a href="http://localhost:8080/m/1234567890" target="_blank">PMI测试页面（生产环境）</a></li>
            <li><a href="http://localhost:8080/meeting/test-uuid-123" target="_blank">会议测试页面（生产环境）</a></li>
        </ul>
        <div class="info">
            <strong>新功能说明：</strong>现在微信检测改为遮罩模式，页面内容正常显示，但会在上方显示半透明引导遮罩，提示用户在浏览器中打开。
        </div>
    </div>

    <script>
        // 微信浏览器检测函数（复制自实际代码）
        function isWechatBrowser() {
            const userAgent = navigator.userAgent.toLowerCase();
            return userAgent.includes('micromessenger');
        }

        function isMobileDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        }

        function isIOSDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            return /iphone|ipad|ipod/i.test(userAgent);
        }

        function isAndroidDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            return /android/i.test(userAgent);
        }

        function shouldShowBrowserGuide() {
            return isWechatBrowser();
        }

        function updateDisplay() {
            // 显示User-Agent
            document.getElementById('userAgent').innerHTML = `
                <strong>User-Agent:</strong><br>
                <code style="word-break: break-all; font-size: 12px;">${navigator.userAgent}</code>
            `;

            // 显示浏览器信息
            const browserInfo = {
                isWechat: isWechatBrowser(),
                isMobile: isMobileDevice(),
                isIOS: isIOSDevice(),
                isAndroid: isAndroidDevice(),
                needsGuide: shouldShowBrowserGuide()
            };

            document.getElementById('browserInfo').innerHTML = `
                <strong>浏览器信息：</strong><br>
                微信浏览器: ${browserInfo.isWechat ? '是' : '否'}<br>
                移动设备: ${browserInfo.isMobile ? '是' : '否'}<br>
                iOS设备: ${browserInfo.isIOS ? '是' : '否'}<br>
                Android设备: ${browserInfo.isAndroid ? '是' : '否'}
            `;

            // 显示检测结果
            const resultClass = browserInfo.needsGuide ? 'error' : 'success';
            const resultText = browserInfo.needsGuide ? 
                '检测到微信浏览器，应该显示引导页面' : 
                '非微信浏览器，显示正常页面';

            document.getElementById('detectionResult').innerHTML = `
                <div class="result ${resultClass}">${resultText}</div>
            `;
        }

        // 模拟微信浏览器
        function simulateWechat() {
            alert('注意：这只是演示，实际的User-Agent无法在客户端修改。\n\n微信浏览器的User-Agent通常包含 "MicroMessenger" 关键字。\n\n要真正测试，请在微信中打开测试链接。');
        }

        // 模拟普通浏览器
        function simulateNormal() {
            alert('当前就是普通浏览器环境。\n\n在微信中打开测试链接才能看到引导页面效果。');
        }

        // 恢复原始User-Agent
        function resetUserAgent() {
            updateDisplay();
        }

        // 页面加载时更新显示
        window.onload = function() {
            updateDisplay();
        };
    </script>
</body>
</html>
