#!/bin/bash

# 测试PMI精准管理task关闭窗口时的状态更新
# 验证window状态和schedule状态是否正确更新

set -e

BASE_URL="http://localhost:8080"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=== 测试PMI精准管理task关闭窗口状态更新 ==="
echo "时间: $(date)"
echo

# 1. 查找一个可以测试的活跃窗口
log_info "1. 查找可测试的活跃窗口..."

WINDOWS_RESPONSE=$(curl -s "$BASE_URL/api/pmi-schedule-windows?page=0&size=20&status=ACTIVE")

if [ $? -ne 0 ]; then
    log_error "无法连接到API服务器"
    exit 1
fi

# 检查是否有活跃窗口
ACTIVE_WINDOW_COUNT=$(echo "$WINDOWS_RESPONSE" | jq -r '.data.content | length')
log_info "找到 $ACTIVE_WINDOW_COUNT 个活跃窗口"

if [ "$ACTIVE_WINDOW_COUNT" = "0" ]; then
    log_warning "没有找到活跃窗口，尝试查找待开启窗口..."
    
    PENDING_WINDOWS_RESPONSE=$(curl -s "$BASE_URL/api/pmi-schedule-windows?page=0&size=20&status=PENDING")
    PENDING_WINDOW_COUNT=$(echo "$PENDING_WINDOWS_RESPONSE" | jq -r '.data.content | length')
    
    if [ "$PENDING_WINDOW_COUNT" = "0" ]; then
        log_error "没有找到可测试的窗口（需要ACTIVE或PENDING状态）"
        log_info "建议创建一个新的PMI计划进行测试"
        exit 1
    fi
    
    log_info "找到 $PENDING_WINDOW_COUNT 个待开启窗口，但无法测试关闭功能"
    exit 1
fi

# 选择第一个活跃窗口进行测试
WINDOW_ID=$(echo "$WINDOWS_RESPONSE" | jq -r '.data.content[0].id')
SCHEDULE_ID=$(echo "$WINDOWS_RESPONSE" | jq -r '.data.content[0].scheduleId')
PMI_RECORD_ID=$(echo "$WINDOWS_RESPONSE" | jq -r '.data.content[0].pmiRecordId')
WINDOW_STATUS=$(echo "$WINDOWS_RESPONSE" | jq -r '.data.content[0].status')

log_info "选择测试窗口: windowId=$WINDOW_ID, scheduleId=$SCHEDULE_ID, pmiRecordId=$PMI_RECORD_ID"
log_info "当前窗口状态: $WINDOW_STATUS"

# 2. 获取关闭前的状态信息
log_info "2. 获取关闭前的状态信息..."

# 获取PMI记录状态
PMI_RESPONSE=$(curl -s "$BASE_URL/api/pmi-records/$PMI_RECORD_ID")
PMI_STATUS_BEFORE=$(echo "$PMI_RESPONSE" | jq -r '.data.status')
log_info "PMI状态（关闭前）: $PMI_STATUS_BEFORE"

# 获取Schedule状态
SCHEDULE_RESPONSE=$(curl -s "$BASE_URL/api/pmi-schedules/$SCHEDULE_ID")
SCHEDULE_STATUS_BEFORE=$(echo "$SCHEDULE_RESPONSE" | jq -r '.data.status')
SCHEDULE_NAME=$(echo "$SCHEDULE_RESPONSE" | jq -r '.data.name')
log_info "Schedule状态（关闭前）: $SCHEDULE_STATUS_BEFORE"
log_info "Schedule名称: $SCHEDULE_NAME"

# 获取该Schedule下的所有窗口状态
SCHEDULE_WINDOWS_RESPONSE=$(curl -s "$BASE_URL/api/pmi-schedules/$SCHEDULE_ID/windows")
TOTAL_WINDOWS=$(echo "$SCHEDULE_WINDOWS_RESPONSE" | jq -r '.data | length')
ACTIVE_WINDOWS=$(echo "$SCHEDULE_WINDOWS_RESPONSE" | jq -r '[.data[] | select(.status == "ACTIVE")] | length')
COMPLETED_WINDOWS=$(echo "$SCHEDULE_WINDOWS_RESPONSE" | jq -r '[.data[] | select(.status == "COMPLETED")] | length')
MANUALLY_CLOSED_WINDOWS=$(echo "$SCHEDULE_WINDOWS_RESPONSE" | jq -r '[.data[] | select(.status == "MANUALLY_CLOSED")] | length')

log_info "Schedule窗口统计（关闭前）:"
log_info "  总窗口数: $TOTAL_WINDOWS"
log_info "  活跃窗口: $ACTIVE_WINDOWS"
log_info "  已完成窗口: $COMPLETED_WINDOWS"
log_info "  人工关闭窗口: $MANUALLY_CLOSED_WINDOWS"

# 获取该PMI下的所有活跃窗口
PMI_ACTIVE_WINDOWS_RESPONSE=$(curl -s "$BASE_URL/api/pmi-schedule-windows?pmiRecordId=$PMI_RECORD_ID&status=ACTIVE")
PMI_ACTIVE_WINDOW_COUNT=$(echo "$PMI_ACTIVE_WINDOWS_RESPONSE" | jq -r '.data.content | length')
log_info "PMI下活跃窗口数（关闭前）: $PMI_ACTIVE_WINDOW_COUNT"

echo

# 3. 执行窗口关闭操作
log_info "3. 执行窗口关闭操作..."

CLOSE_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID/close")
CLOSE_SUCCESS=$(echo "$CLOSE_RESPONSE" | jq -r '.success')

if [ "$CLOSE_SUCCESS" = "true" ]; then
    log_success "窗口关闭成功"
else
    log_error "窗口关闭失败"
    echo "$CLOSE_RESPONSE" | jq '.message'
    exit 1
fi

echo

# 4. 等待状态更新完成
log_info "4. 等待状态更新完成..."
sleep 3

# 5. 验证状态更新结果
log_info "5. 验证状态更新结果..."

# 验证窗口状态
WINDOW_RESPONSE_AFTER=$(curl -s "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID")
WINDOW_STATUS_AFTER=$(echo "$WINDOW_RESPONSE_AFTER" | jq -r '.data.status')
ACTUAL_END_TIME=$(echo "$WINDOW_RESPONSE_AFTER" | jq -r '.data.actualEndTime')

log_info "窗口状态验证:"
log_info "  关闭前: $WINDOW_STATUS"
log_info "  关闭后: $WINDOW_STATUS_AFTER"
log_info "  实际结束时间: $ACTUAL_END_TIME"

if [ "$WINDOW_STATUS_AFTER" = "MANUALLY_CLOSED" ]; then
    log_success "✅ 窗口状态更新正确: $WINDOW_STATUS_AFTER"
else
    log_error "❌ 窗口状态更新错误，期望: MANUALLY_CLOSED，实际: $WINDOW_STATUS_AFTER"
fi

# 验证PMI状态
PMI_RESPONSE_AFTER=$(curl -s "$BASE_URL/api/pmi-records/$PMI_RECORD_ID")
PMI_STATUS_AFTER=$(echo "$PMI_RESPONSE_AFTER" | jq -r '.data.status')

log_info "PMI状态验证:"
log_info "  关闭前: $PMI_STATUS_BEFORE"
log_info "  关闭后: $PMI_STATUS_AFTER"

# 检查PMI下是否还有其他活跃窗口
PMI_ACTIVE_WINDOWS_RESPONSE_AFTER=$(curl -s "$BASE_URL/api/pmi-schedule-windows?pmiRecordId=$PMI_RECORD_ID&status=ACTIVE")
PMI_ACTIVE_WINDOW_COUNT_AFTER=$(echo "$PMI_ACTIVE_WINDOWS_RESPONSE_AFTER" | jq -r '.data.content | length')

log_info "PMI下活跃窗口数（关闭后）: $PMI_ACTIVE_WINDOW_COUNT_AFTER"

if [ "$PMI_ACTIVE_WINDOW_COUNT_AFTER" = "0" ]; then
    if [ "$PMI_STATUS_AFTER" = "INACTIVE" ]; then
        log_success "✅ PMI状态更新正确: 无活跃窗口，状态为 $PMI_STATUS_AFTER"
    else
        log_warning "⚠️ PMI状态可能不正确: 无活跃窗口但状态为 $PMI_STATUS_AFTER（可能是FREE模式）"
    fi
else
    if [ "$PMI_STATUS_AFTER" = "ACTIVE" ]; then
        log_success "✅ PMI状态更新正确: 还有 $PMI_ACTIVE_WINDOW_COUNT_AFTER 个活跃窗口，状态保持 $PMI_STATUS_AFTER"
    else
        log_error "❌ PMI状态更新错误: 还有活跃窗口但状态为 $PMI_STATUS_AFTER"
    fi
fi

# 验证Schedule状态
SCHEDULE_RESPONSE_AFTER=$(curl -s "$BASE_URL/api/pmi-schedules/$SCHEDULE_ID")
SCHEDULE_STATUS_AFTER=$(echo "$SCHEDULE_RESPONSE_AFTER" | jq -r '.data.status')

log_info "Schedule状态验证:"
log_info "  关闭前: $SCHEDULE_STATUS_BEFORE"
log_info "  关闭后: $SCHEDULE_STATUS_AFTER"

# 重新统计窗口状态
SCHEDULE_WINDOWS_RESPONSE_AFTER=$(curl -s "$BASE_URL/api/pmi-schedules/$SCHEDULE_ID/windows")
ACTIVE_WINDOWS_AFTER=$(echo "$SCHEDULE_WINDOWS_RESPONSE_AFTER" | jq -r '[.data[] | select(.status == "ACTIVE")] | length')
COMPLETED_WINDOWS_AFTER=$(echo "$SCHEDULE_WINDOWS_RESPONSE_AFTER" | jq -r '[.data[] | select(.status == "COMPLETED")] | length')
MANUALLY_CLOSED_WINDOWS_AFTER=$(echo "$SCHEDULE_WINDOWS_RESPONSE_AFTER" | jq -r '[.data[] | select(.status == "MANUALLY_CLOSED")] | length')
FINISHED_WINDOWS_AFTER=$((COMPLETED_WINDOWS_AFTER + MANUALLY_CLOSED_WINDOWS_AFTER))

log_info "Schedule窗口统计（关闭后）:"
log_info "  总窗口数: $TOTAL_WINDOWS"
log_info "  活跃窗口: $ACTIVE_WINDOWS_AFTER"
log_info "  已完成窗口: $COMPLETED_WINDOWS_AFTER"
log_info "  人工关闭窗口: $MANUALLY_CLOSED_WINDOWS_AFTER"
log_info "  已结束窗口总数: $FINISHED_WINDOWS_AFTER"

if [ "$FINISHED_WINDOWS_AFTER" = "$TOTAL_WINDOWS" ]; then
    if [ "$SCHEDULE_STATUS_AFTER" = "COMPLETED" ]; then
        log_success "✅ Schedule状态更新正确: 所有窗口已结束，状态为 $SCHEDULE_STATUS_AFTER"
    else
        log_error "❌ Schedule状态更新错误: 所有窗口已结束但状态为 $SCHEDULE_STATUS_AFTER"
    fi
else
    if [ "$SCHEDULE_STATUS_AFTER" = "ACTIVE" ]; then
        log_success "✅ Schedule状态更新正确: 还有未结束窗口，状态保持 $SCHEDULE_STATUS_AFTER"
    else
        log_warning "⚠️ Schedule状态可能不正确: 还有未结束窗口但状态为 $SCHEDULE_STATUS_AFTER"
    fi
fi

echo
log_info "=== 测试总结 ==="
log_info "窗口关闭操作: ✅ 成功"
log_info "窗口状态更新: $([ "$WINDOW_STATUS_AFTER" = "MANUALLY_CLOSED" ] && echo "✅ 正确" || echo "❌ 错误")"
log_info "PMI状态更新: $([ "$PMI_ACTIVE_WINDOW_COUNT_AFTER" = "0" ] && [ "$PMI_STATUS_AFTER" = "INACTIVE" ] || [ "$PMI_ACTIVE_WINDOW_COUNT_AFTER" != "0" ] && [ "$PMI_STATUS_AFTER" = "ACTIVE" ] && echo "✅ 正确" || echo "⚠️ 需检查")"
log_info "Schedule状态更新: $([ "$FINISHED_WINDOWS_AFTER" = "$TOTAL_WINDOWS" ] && [ "$SCHEDULE_STATUS_AFTER" = "COMPLETED" ] || [ "$FINISHED_WINDOWS_AFTER" != "$TOTAL_WINDOWS" ] && [ "$SCHEDULE_STATUS_AFTER" = "ACTIVE" ] && echo "✅ 正确" || echo "⚠️ 需检查")"

log_success "测试完成！"
