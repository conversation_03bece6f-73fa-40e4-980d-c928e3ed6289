#!/bin/bash

# 测试修复后的API
echo "=== 测试PMI窗口表字段简化后的API ==="

# 1. 测试健康检查
echo "1. 测试后端健康检查..."
curl -s http://localhost:8080/actuator/health | jq .

# 2. 测试前端是否正常运行
echo -e "\n2. 测试前端服务..."
curl -s -I http://localhost:3000 | head -1

# 3. 测试用户端前端
echo -e "\n3. 测试用户端前端..."
curl -s -I http://localhost:3004 | head -1

echo -e "\n=== 测试完成 ==="
echo "✅ 后端服务: http://localhost:8080"
echo "✅ 管理端前端: http://localhost:3000"
echo "✅ 用户端前端: http://localhost:3004"
echo ""
echo "🔧 修复内容:"
echo "- 修复了前端排序字段从 windowDate 改为 startDateTime"
echo "- 修复了后端默认排序字段"
echo "- 更新了 PmiScheduleWindowRequest DTO 支持新字段"
echo "- 修复了窗口更新服务方法"
echo ""
echo "📝 注意事项:"
echo "- 前端编辑窗口功能现在使用兼容性处理"
echo "- 旧字段会自动转换为新的 DateTime 字段"
echo "- 所有查询和排序都使用新的精确时间字段"
