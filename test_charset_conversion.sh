#!/bin/bash

# 测试字符集转换功能
# 验证导出文件的字符集转换是否正确
# 执行日期: 2025-08-20

set -e

# 配置变量
LOCAL_DB_USER="root"
LOCAL_DB_PASS="nvshen2018"
LOCAL_DB_NAME="zoombusV"

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}=========================================="
echo "字符集转换功能测试"
echo -e "==========================================${NC}"

# 创建测试目录
TEST_DIR="charset_test_$(date +%Y%m%d_%H%M%S)"
mkdir -p $TEST_DIR

echo -e "${BLUE}[1/4] 导出测试数据...${NC}"

# 导出一个小表进行测试
mysqldump -u$LOCAL_DB_USER -p$LOCAL_DB_PASS \
    --single-transaction \
    --no-create-info \
    --complete-insert \
    --extended-insert=FALSE \
    $LOCAL_DB_NAME t_users > $TEST_DIR/t_users_original.sql

echo "✓ 原始数据导出完成"

echo -e "${BLUE}[2/4] 分析原始文件字符集...${NC}"

# 检查原始文件中的字符集声明
echo "原始文件中的字符集声明:"
grep -n "utf8mb4" $TEST_DIR/t_users_original.sql | head -5 || echo "  未找到utf8mb4声明"

# 统计各种字符集的数量
echo ""
echo "字符集统计:"
echo "  utf8mb4_0900_ai_ci: $(grep -c "utf8mb4_0900_ai_ci" $TEST_DIR/t_users_original.sql || echo "0")"
echo "  utf8mb4_unicode_ci: $(grep -c "utf8mb4_unicode_ci" $TEST_DIR/t_users_original.sql || echo "0")"
echo "  utf8mb4_general_ci: $(grep -c "utf8mb4_general_ci" $TEST_DIR/t_users_original.sql || echo "0")"
echo "  utf8mb4_bin: $(grep -c "utf8mb4_bin" $TEST_DIR/t_users_original.sql || echo "0")"

echo -e "${BLUE}[3/4] 执行字符集转换...${NC}"

# 复制文件进行转换
cp $TEST_DIR/t_users_original.sql $TEST_DIR/t_users_converted.sql

# 执行字符集转换
sed -i.backup \
    -e 's/utf8mb4_0900_ai_ci/utf8mb4_general_ci/g' \
    -e 's/utf8mb4_unicode_ci/utf8mb4_general_ci/g' \
    -e 's/utf8mb4_unicode_520_ci/utf8mb4_general_ci/g' \
    -e 's/utf8mb4_bin/utf8mb4_general_ci/g' \
    $TEST_DIR/t_users_converted.sql

echo "✓ 字符集转换完成"

echo -e "${BLUE}[4/4] 验证转换结果...${NC}"

# 检查转换后的文件
echo "转换后的字符集统计:"
echo "  utf8mb4_0900_ai_ci: $(grep -c "utf8mb4_0900_ai_ci" $TEST_DIR/t_users_converted.sql || echo "0")"
echo "  utf8mb4_unicode_ci: $(grep -c "utf8mb4_unicode_ci" $TEST_DIR/t_users_converted.sql || echo "0")"
echo "  utf8mb4_general_ci: $(grep -c "utf8mb4_general_ci" $TEST_DIR/t_users_converted.sql || echo "0")"
echo "  utf8mb4_bin: $(grep -c "utf8mb4_bin" $TEST_DIR/t_users_converted.sql || echo "0")"

# 显示转换示例
echo ""
echo "转换示例对比:"
echo "转换前:"
grep -n "utf8mb4" $TEST_DIR/t_users_original.sql | head -3 || echo "  无utf8mb4声明"
echo ""
echo "转换后:"
grep -n "utf8mb4" $TEST_DIR/t_users_converted.sql | head -3 || echo "  无utf8mb4声明"

# 验证转换是否成功
OLD_COUNT=$(grep -c "utf8mb4_0900_ai_ci" $TEST_DIR/t_users_converted.sql || echo "0")
NEW_COUNT=$(grep -c "utf8mb4_general_ci" $TEST_DIR/t_users_converted.sql || echo "0")

echo ""
echo "转换结果验证:"
if [ "$OLD_COUNT" -eq 0 ]; then
    echo -e "  ${GREEN}✓ 所有utf8mb4_0900_ai_ci已转换${NC}"
else
    echo -e "  ${RED}✗ 仍有 $OLD_COUNT 个utf8mb4_0900_ai_ci未转换${NC}"
fi

if [ "$NEW_COUNT" -gt 0 ]; then
    echo -e "  ${GREEN}✓ 成功转换为 $NEW_COUNT 个utf8mb4_general_ci${NC}"
else
    echo -e "  ${YELLOW}⚠ 未发现utf8mb4_general_ci声明${NC}"
fi

# 文件大小对比
ORIGINAL_SIZE=$(ls -lh $TEST_DIR/t_users_original.sql | awk '{print $5}')
CONVERTED_SIZE=$(ls -lh $TEST_DIR/t_users_converted.sql | awk '{print $5}')

echo ""
echo "文件大小对比:"
echo "  原始文件: $ORIGINAL_SIZE"
echo "  转换文件: $CONVERTED_SIZE"

# 测试SQL语法
echo ""
echo -e "${BLUE}测试SQL语法有效性...${NC}"

# 创建测试数据库连接（不执行，只检查语法）
mysql -u$LOCAL_DB_USER -p$LOCAL_DB_PASS --execute="SELECT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "  ${GREEN}✓ 数据库连接正常${NC}"
    
    # 可以添加更多的语法检查
    if head -10 $TEST_DIR/t_users_converted.sql | grep -q "INSERT INTO"; then
        echo -e "  ${GREEN}✓ SQL语法格式正确${NC}"
    else
        echo -e "  ${YELLOW}⚠ SQL格式可能有问题${NC}"
    fi
else
    echo -e "  ${RED}✗ 数据库连接失败${NC}"
fi

echo ""
echo -e "${GREEN}=========================================="
echo "字符集转换测试完成！"
echo ""
echo "测试文件保存在: $TEST_DIR/"
echo "  - t_users_original.sql (原始文件)"
echo "  - t_users_converted.sql (转换后文件)"
echo "  - t_users_converted.sql.backup (转换备份)"
echo -e "==========================================${NC}"

# 询问是否清理测试文件
echo ""
read -p "是否清理测试文件？(y/N): " cleanup_confirm
if [[ $cleanup_confirm =~ ^[Yy]$ ]]; then
    rm -rf $TEST_DIR
    echo "✓ 测试文件已清理"
else
    echo "测试文件保留在: $TEST_DIR/"
fi
