#!/bin/bash

# 连接测试脚本
# 用于测试到测试环境和生产环境的连接

set -e

# 加载配置文件
CONFIG_FILE="./migration_config.conf"
if [ -f "$CONFIG_FILE" ]; then
    source "$CONFIG_FILE"
    echo "✅ 已加载配置文件: $CONFIG_FILE"
else
    echo "⚠️  配置文件不存在，使用默认配置: $CONFIG_FILE"
fi

# 默认配置
TEST_DB_USER="${TEST_DB_USER:-root}"
TEST_DB_PASS="${TEST_DB_PASS:-nvshen2018}"
TEST_DB_NAME="${TEST_DB_NAME:-zoombusV}"
TEST_DB_HOST="${TEST_DB_HOST:-localhost}"

PROD_SERVER="${PROD_SERVER:-<EMAIL>}"
PROD_DB_USER="${PROD_DB_USER:-root}"
PROD_DB_PASS="${PROD_DB_PASS:-nvshen2018}"
PROD_DB_NAME="${PROD_DB_NAME:-zoombusV}"
PROD_DB_HOST="${PROD_DB_HOST:-localhost}"

SSH_KEY_PATH="${SSH_KEY_PATH:-}"
SSH_PORT="${SSH_PORT:-22}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}         连接测试开始${NC}"
echo -e "${BLUE}========================================${NC}"

# 测试本地MySQL连接
echo -e "\n${BLUE}1. 测试本地MySQL连接...${NC}"
echo "   主机: $TEST_DB_HOST"
echo "   用户: $TEST_DB_USER"
echo "   数据库: $TEST_DB_NAME"

if mysql -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" -e "USE $TEST_DB_NAME; SELECT 'MySQL连接成功' as status;" 2>/dev/null; then
    echo -e "   ${GREEN}✅ 本地MySQL连接成功${NC}"
    
    # 显示表统计
    echo -e "\n   ${BLUE}数据表统计:${NC}"
    mysql -h"$TEST_DB_HOST" -u"$TEST_DB_USER" -p"$TEST_DB_PASS" "$TEST_DB_NAME" -e "
    SELECT 
        't_users' as table_name, COUNT(*) as record_count FROM t_users
    UNION ALL
    SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
    UNION ALL
    SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
    UNION ALL
    SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows;
    " 2>/dev/null
else
    echo -e "   ${RED}❌ 本地MySQL连接失败${NC}"
    echo -e "   ${YELLOW}请检查:${NC}"
    echo -e "   - MySQL服务是否运行"
    echo -e "   - 用户名密码是否正确"
    echo -e "   - 数据库是否存在"
fi

# 测试SSH连接
echo -e "\n${BLUE}2. 测试生产服务器SSH连接...${NC}"
echo "   服务器: $PROD_SERVER"
echo "   端口: $SSH_PORT"
if [ -n "$SSH_KEY_PATH" ]; then
    echo "   认证方式: SSH密钥 ($SSH_KEY_PATH)"
else
    echo "   认证方式: 密码认证"
fi

ssh_cmd="ssh"
if [ -n "$SSH_KEY_PATH" ]; then
    if [ ! -f "$SSH_KEY_PATH" ]; then
        echo -e "   ${RED}❌ SSH私钥文件不存在: $SSH_KEY_PATH${NC}"
        exit 1
    fi
    ssh_cmd="ssh -i $SSH_KEY_PATH"
fi
ssh_cmd="$ssh_cmd -p $SSH_PORT -o ConnectTimeout=10 -o BatchMode=yes"

if $ssh_cmd "$PROD_SERVER" "echo 'SSH连接测试成功'" 2>/dev/null; then
    echo -e "   ${GREEN}✅ SSH连接成功${NC}"
    
    # 获取服务器信息
    echo -e "\n   ${BLUE}服务器信息:${NC}"
    $ssh_cmd "$PROD_SERVER" "
        echo '   操作系统: '$(uname -s)
        echo '   内核版本: '$(uname -r)
        echo '   主机名: '$(hostname)
        echo '   当前时间: '$(date)
        echo '   磁盘使用: '$(df -h / | tail -1 | awk '{print \$5}')
    " 2>/dev/null
else
    echo -e "   ${RED}❌ SSH连接失败${NC}"
    echo -e "   ${YELLOW}请检查:${NC}"
    echo -e "   - 服务器地址是否正确"
    echo -e "   - SSH端口是否正确"
    echo -e "   - SSH密钥配置是否正确"
    echo -e "   - 网络连接是否正常"
    echo -e "   - 防火墙设置"
    exit 1
fi

# 测试生产环境MySQL连接
echo -e "\n${BLUE}3. 测试生产环境MySQL连接...${NC}"
echo "   主机: $PROD_DB_HOST (通过SSH)"
echo "   用户: $PROD_DB_USER"
echo "   数据库: $PROD_DB_NAME"

if $ssh_cmd "$PROD_SERVER" "mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' -e 'USE $PROD_DB_NAME; SELECT \"MySQL连接成功\" as status;'" 2>/dev/null; then
    echo -e "   ${GREEN}✅ 生产环境MySQL连接成功${NC}"
    
    # 显示生产环境表统计
    echo -e "\n   ${BLUE}生产环境数据表统计:${NC}"
    $ssh_cmd "$PROD_SERVER" "mysql -h'$PROD_DB_HOST' -u'$PROD_DB_USER' -p'$PROD_DB_PASS' '$PROD_DB_NAME' -e \"
    SELECT 
        't_users' as table_name, COUNT(*) as record_count FROM t_users
    UNION ALL
    SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
    UNION ALL
    SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
    UNION ALL
    SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows;
    \"" 2>/dev/null
else
    echo -e "   ${RED}❌ 生产环境MySQL连接失败${NC}"
    echo -e "   ${YELLOW}请检查:${NC}"
    echo -e "   - 生产服务器上MySQL服务是否运行"
    echo -e "   - 数据库用户名密码是否正确"
    echo -e "   - 数据库是否存在"
    echo -e "   - MySQL端口是否开放"
fi

# 测试文件传输
echo -e "\n${BLUE}4. 测试文件传输...${NC}"

# 创建测试文件
test_file="/tmp/migration_test_$(date +%s).txt"
echo "这是一个测试文件，用于验证文件传输功能" > "$test_file"

scp_cmd="scp"
if [ -n "$SSH_KEY_PATH" ]; then
    scp_cmd="scp -i $SSH_KEY_PATH"
fi
scp_cmd="$scp_cmd -P $SSH_PORT"

remote_test_file="/tmp/migration_test_$(date +%s).txt"

if $scp_cmd "$test_file" "$PROD_SERVER:$remote_test_file" 2>/dev/null; then
    echo -e "   ${GREEN}✅ 文件上传成功${NC}"
    
    # 验证文件内容
    if $ssh_cmd "$PROD_SERVER" "cat $remote_test_file" 2>/dev/null | grep -q "测试文件"; then
        echo -e "   ${GREEN}✅ 文件内容验证成功${NC}"
    else
        echo -e "   ${YELLOW}⚠️  文件内容验证失败${NC}"
    fi
    
    # 清理测试文件
    $ssh_cmd "$PROD_SERVER" "rm -f $remote_test_file" 2>/dev/null || true
else
    echo -e "   ${RED}❌ 文件传输失败${NC}"
    echo -e "   ${YELLOW}请检查SCP权限和网络连接${NC}"
fi

# 清理本地测试文件
rm -f "$test_file"

# 测试总结
echo -e "\n${BLUE}========================================${NC}"
echo -e "${BLUE}         连接测试完成${NC}"
echo -e "${BLUE}========================================${NC}"

echo -e "\n${GREEN}✅ 所有连接测试通过，可以开始数据迁移！${NC}"
echo -e "\n${YELLOW}使用以下命令开始迁移:${NC}"
echo -e "   ${BLUE}./migrate_data_to_production.sh${NC}                    # 完整迁移"
echo -e "   ${BLUE}./migrate_data_to_production.sh --export-only${NC}      # 仅导出"
echo -e "   ${BLUE}./migrate_data_to_production.sh --import-only${NC}      # 仅导入"
echo -e "   ${BLUE}./migrate_data_to_production.sh --keep-files${NC}       # 保留文件"

if [ -n "$SSH_KEY_PATH" ]; then
    echo -e "   ${BLUE}./migrate_data_to_production.sh --ssh-key $SSH_KEY_PATH${NC}  # 指定SSH密钥"
fi

echo -e "\n${YELLOW}注意事项:${NC}"
echo -e "   - 迁移前会自动备份生产环境数据"
echo -e "   - 建议在业务低峰期执行迁移"
echo -e "   - 迁移过程中请勿中断"
