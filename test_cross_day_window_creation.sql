-- 测试跨日窗口创建逻辑
-- 验证修复后的窗口创建是否正确处理跨日窗口的 end_date

USE zoombusV;

-- 1. 验证窗口2051的修复结果
SELECT '=== 窗口2051修复验证 ===' as step;

SELECT 
    psw.id,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    pr.pmi_number,
    CASE 
        WHEN psw.end_time < psw.start_time THEN 'CROSS_DAY_WINDOW'
        ELSE 'SAME_DAY_WINDOW'
    END as window_type,
    CASE 
        WHEN psw.end_time < psw.start_time AND psw.end_date = DATE_ADD(psw.window_date, INTERVAL 1 DAY) THEN 'CORRECT'
        WHEN psw.end_time >= psw.start_time AND psw.end_date = psw.window_date THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as end_date_correctness,
    TIMESTAMPDIFF(MINUTE, 
        TIMESTAMP(psw.window_date, psw.start_time),
        TIMESTAMP(psw.end_date, psw.end_time)
    ) as duration_minutes
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.id = 2051;

-- 2. 检查所有跨日窗口的修复状态
SELECT '=== 跨日窗口修复状态统计 ===' as step;

SELECT 
    'Cross-Day Windows' as window_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN psw.end_date = DATE_ADD(psw.window_date, INTERVAL 1 DAY) THEN 1 END) as correct_count,
    COUNT(CASE WHEN psw.end_date != DATE_ADD(psw.window_date, INTERVAL 1 DAY) THEN 1 END) as incorrect_count,
    ROUND(
        COUNT(CASE WHEN psw.end_date = DATE_ADD(psw.window_date, INTERVAL 1 DAY) THEN 1 END) * 100.0 / COUNT(*), 
        2
    ) as correct_percentage
FROM t_pmi_schedule_windows psw
WHERE psw.end_time < psw.start_time OR psw.end_time = '00:00:00'

UNION ALL

SELECT 
    'Same-Day Windows' as window_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN psw.end_date = psw.window_date THEN 1 END) as correct_count,
    COUNT(CASE WHEN psw.end_date != psw.window_date THEN 1 END) as incorrect_count,
    ROUND(
        COUNT(CASE WHEN psw.end_date = psw.window_date THEN 1 END) * 100.0 / COUNT(*), 
        2
    ) as correct_percentage
FROM t_pmi_schedule_windows psw
WHERE psw.end_time >= psw.start_time AND psw.end_time != '00:00:00';

-- 3. 显示一些跨日窗口的例子
SELECT '=== 跨日窗口示例 ===' as step;

SELECT 
    psw.id,
    pr.pmi_number,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    TIMESTAMPDIFF(MINUTE, 
        TIMESTAMP(psw.window_date, psw.start_time),
        TIMESTAMP(psw.end_date, psw.end_time)
    ) as duration_minutes,
    CASE 
        WHEN psw.end_date = DATE_ADD(psw.window_date, INTERVAL 1 DAY) THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as end_date_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.end_time < psw.start_time
ORDER BY psw.id DESC
LIMIT 10;

-- 4. 显示一些同日窗口的例子
SELECT '=== 同日窗口示例 ===' as step;

SELECT 
    psw.id,
    pr.pmi_number,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    TIMESTAMPDIFF(MINUTE, 
        TIMESTAMP(psw.window_date, psw.start_time),
        TIMESTAMP(psw.end_date, psw.end_time)
    ) as duration_minutes,
    CASE 
        WHEN psw.end_date = psw.window_date THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as end_date_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.end_time >= psw.start_time 
AND psw.end_time != '00:00:00'
ORDER BY psw.id DESC
LIMIT 10;

-- 5. 检查特殊情况：end_time = 00:00:00 的窗口
SELECT '=== 特殊情况：end_time = 00:00:00 的窗口 ===' as step;

SELECT 
    psw.id,
    pr.pmi_number,
    psw.window_date,
    psw.end_date,
    psw.start_time,
    psw.end_time,
    psw.status,
    CASE 
        WHEN psw.end_time = '00:00:00' AND psw.end_date = DATE_ADD(psw.window_date, INTERVAL 1 DAY) THEN 'CORRECT'
        WHEN psw.end_time = '00:00:00' AND psw.end_date != DATE_ADD(psw.window_date, INTERVAL 1 DAY) THEN 'INCORRECT'
        ELSE 'N/A'
    END as end_date_status
FROM t_pmi_schedule_windows psw
JOIN t_pmi_records pr ON psw.pmi_record_id = pr.id
WHERE psw.end_time = '00:00:00'
ORDER BY psw.id DESC
LIMIT 10;

-- 6. 验证窗口关闭逻辑的正确性
SELECT '=== 窗口关闭逻辑验证 ===' as step;

SELECT 
    'Active Windows That Should Be Active' as check_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows psw
WHERE psw.status = 'ACTIVE'
AND (
    CURDATE() < psw.end_date 
    OR (CURDATE() = psw.end_date AND CURTIME() < psw.end_time)
)

UNION ALL

SELECT 
    'Active Windows That Should Be Closed' as check_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows psw
WHERE psw.status = 'ACTIVE'
AND (
    CURDATE() > psw.end_date 
    OR (CURDATE() = psw.end_date AND CURTIME() >= psw.end_time)
)

UNION ALL

SELECT 
    'Completed Windows That Should Still Be Active' as check_type,
    COUNT(*) as count
FROM t_pmi_schedule_windows psw
WHERE psw.status = 'COMPLETED'
AND (
    CURDATE() < psw.end_date 
    OR (CURDATE() = psw.end_date AND CURTIME() < psw.end_time)
);

-- 7. 最终总结报告
SELECT '=== 窗口 end_date 修复总结报告 ===' as step;

SELECT 
    'Total Windows' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows

UNION ALL

SELECT 
    'Cross-Day Windows (Correct)' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows psw
WHERE (psw.end_time < psw.start_time OR psw.end_time = '00:00:00')
AND psw.end_date = DATE_ADD(psw.window_date, INTERVAL 1 DAY)

UNION ALL

SELECT 
    'Same-Day Windows (Correct)' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows psw
WHERE psw.end_time >= psw.start_time 
AND psw.end_time != '00:00:00'
AND psw.end_date = psw.window_date

UNION ALL

SELECT 
    'Windows with Incorrect end_date' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows psw
WHERE (
    -- 跨日窗口但 end_date 不正确
    ((psw.end_time < psw.start_time OR psw.end_time = '00:00:00') 
     AND psw.end_date != DATE_ADD(psw.window_date, INTERVAL 1 DAY))
    OR
    -- 同日窗口但 end_date 不正确  
    (psw.end_time >= psw.start_time AND psw.end_time != '00:00:00'
     AND psw.end_date != psw.window_date)
);

-- 8. 显示窗口2051的完整信息作为成功案例
SELECT '=== 窗口2051成功修复案例 ===' as step;

SELECT 
    '修复前问题' as description,
    'end_date = window_date (2025-08-21)' as issue,
    '跨日窗口应该 end_date = window_date + 1天' as expected_fix
    
UNION ALL

SELECT 
    '修复后结果' as description,
    CONCAT('end_date = ', psw.end_date) as issue,
    CONCAT('正确！跨日窗口 end_date = ', psw.end_date) as expected_fix
FROM t_pmi_schedule_windows psw
WHERE psw.id = 2051;
