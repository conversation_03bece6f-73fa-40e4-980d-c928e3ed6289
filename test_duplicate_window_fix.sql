-- 测试重复窗口修复的验证脚本

-- 1. 验证TraceId对应的问题已修复
SELECT '=== 验证TraceId 20250823193951674-001-833004-0s659y 问题已修复 ===' as step;
SELECT 
    ps.id as schedule_id,
    ps.pmi_record_id,
    ps.name,
    ps.start_date,
    ps.end_date,
    ps.repeat_type,
    ps.created_at,
    COUNT(psw.id) as window_count,
    GROUP_CONCAT(psw.id ORDER BY psw.id) as window_ids,
    GROUP_CONCAT(psw.status ORDER BY psw.id) as window_statuses
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id AND psw.status IN ('PENDING', 'ACTIVE')
WHERE ps.id = 1052
GROUP BY ps.id, ps.pmi_record_id, ps.name, ps.start_date, ps.end_date, ps.repeat_type, ps.created_at;

-- 2. 验证窗口详细信息
SELECT '=== 验证窗口详细信息 ===' as step;
SELECT 
    psw.id,
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    psw.open_task_id,
    psw.close_task_id,
    psw.created_at
FROM t_pmi_schedule_windows psw
WHERE psw.schedule_id = 1052
ORDER BY psw.created_at;

-- 3. 验证任务信息
SELECT '=== 验证任务信息 ===' as step;
SELECT 
    pswt.id,
    pswt.task_key,
    pswt.pmi_window_id,
    pswt.task_type,
    pswt.scheduled_time,
    pswt.status,
    pswt.created_at
FROM t_pmi_schedule_window_tasks pswt
WHERE pswt.pmi_window_id = 2080
ORDER BY pswt.task_type;

-- 4. 验证系统中没有重复窗口
SELECT '=== 验证系统中没有重复窗口 ===' as step;
SELECT 
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    COUNT(*) as window_count,
    GROUP_CONCAT(psw.id ORDER BY psw.id) as window_ids,
    'DUPLICATE_FOUND' as issue
FROM t_pmi_schedule_windows psw
WHERE psw.status IN ('PENDING', 'ACTIVE')
GROUP BY psw.schedule_id, psw.pmi_record_id, psw.start_date_time, psw.end_date_time
HAVING COUNT(*) > 1
ORDER BY psw.schedule_id;

-- 5. 验证PMI记录状态
SELECT '=== 验证PMI记录状态 ===' as step;
SELECT 
    pr.id,
    pr.pmi_number,
    pr.status,
    pr.billing_mode,
    pr.current_window_id,
    pr.window_expire_time,
    pr.created_at
FROM t_pmi_records pr
WHERE pr.id = 611;

-- 6. 统计报告
SELECT '=== 修复后统计报告 ===' as step;
SELECT 
    'Total Active Schedules' as metric,
    COUNT(*) as count
FROM t_pmi_schedules 
WHERE status = 'ACTIVE'

UNION ALL

SELECT 
    'Total Active Windows' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_windows 
WHERE status IN ('PENDING', 'ACTIVE')

UNION ALL

SELECT 
    'Total Scheduled Tasks' as metric,
    COUNT(*) as count
FROM t_pmi_schedule_window_tasks 
WHERE status = 'SCHEDULED'

UNION ALL

SELECT 
    'Schedules with Multiple Windows (Same Time)' as metric,
    COUNT(*) as count
FROM (
    SELECT 
        psw.schedule_id,
        psw.start_date_time,
        psw.end_date_time,
        COUNT(*) as window_count
    FROM t_pmi_schedule_windows psw
    WHERE psw.status IN ('PENDING', 'ACTIVE')
    GROUP BY psw.schedule_id, psw.start_date_time, psw.end_date_time
    HAVING COUNT(*) > 1
) duplicates;

-- 7. 验证业务逻辑正确性
SELECT '=== 验证业务逻辑正确性 ===' as step;
SELECT 
    ps.id as schedule_id,
    ps.name,
    ps.start_date,
    ps.end_date,
    ps.repeat_type,
    ps.is_all_day,
    ps.start_time,
    ps.duration_minutes,
    COUNT(psw.id) as actual_window_count,
    CASE 
        WHEN ps.repeat_type = 'ONCE' THEN 1
        WHEN ps.repeat_type = 'DAILY' AND ps.start_date = ps.end_date THEN 1
        WHEN ps.repeat_type = 'DAILY' THEN DATEDIFF(ps.end_date, ps.start_date) + 1
        WHEN ps.repeat_type = 'WEEKLY' THEN CEIL(DATEDIFF(ps.end_date, ps.start_date) / 7)
        WHEN ps.repeat_type = 'MONTHLY' THEN PERIOD_DIFF(DATE_FORMAT(ps.end_date, '%Y%m'), DATE_FORMAT(ps.start_date, '%Y%m')) + 1
        ELSE 0
    END as expected_window_count,
    CASE 
        WHEN COUNT(psw.id) = (
            CASE 
                WHEN ps.repeat_type = 'ONCE' THEN 1
                WHEN ps.repeat_type = 'DAILY' AND ps.start_date = ps.end_date THEN 1
                WHEN ps.repeat_type = 'DAILY' THEN DATEDIFF(ps.end_date, ps.start_date) + 1
                WHEN ps.repeat_type = 'WEEKLY' THEN CEIL(DATEDIFF(ps.end_date, ps.start_date) / 7)
                WHEN ps.repeat_type = 'MONTHLY' THEN PERIOD_DIFF(DATE_FORMAT(ps.end_date, '%Y%m'), DATE_FORMAT(ps.start_date, '%Y%m')) + 1
                ELSE 0
            END
        ) THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as validation_result
FROM t_pmi_schedules ps
LEFT JOIN t_pmi_schedule_windows psw ON ps.id = psw.schedule_id AND psw.status IN ('PENDING', 'ACTIVE')
WHERE ps.id IN (1051, 1052)
GROUP BY ps.id, ps.name, ps.start_date, ps.end_date, ps.repeat_type, ps.is_all_day, ps.start_time, ps.duration_minutes
ORDER BY ps.id;
