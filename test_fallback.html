<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PMI回退功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>PMI回退功能测试</h1>
    
    <div class="test-section">
        <h3>1. 测试获取PMI信息（包含回退状态）</h3>
        <input type="text" id="pmiNumber" placeholder="输入PMI号码" value="8519274868">
        <button onclick="testGetPmiInfo()">获取PMI信息</button>
        <div id="pmiInfoResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试设置PMI回退状态</h3>
        <input type="text" id="pmiId" placeholder="输入PMI ID" value="1">
        <button onclick="testSetFallback(true)">启用回退</button>
        <button onclick="testSetFallback(false)">禁用回退</button>
        <div id="fallbackResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试用户前端回退跳转</h3>
        <input type="text" id="testPmiNumber" placeholder="输入PMI号码" value="8519274868">
        <button onclick="testUserFrontend()">测试用户前端访问</button>
        <div id="userFrontendResult" class="result"></div>
    </div>

    <script>
        async function testGetPmiInfo() {
            const pmiNumber = document.getElementById('pmiNumber').value;
            const resultDiv = document.getElementById('pmiInfoResult');
            
            try {
                const response = await fetch(`http://localhost:8080/api/public/pmi/${pmiNumber}`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }

        async function testSetFallback(enabled) {
            const pmiId = document.getElementById('pmiId').value;
            const resultDiv = document.getElementById('fallbackResult');
            
            try {
                // 这里需要认证token，实际测试时需要先登录获取token
                const response = await fetch(`http://localhost:8080/api/admin/pmi/${pmiId}/fallback`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
                    },
                    body: JSON.stringify({
                        fallbackEnabled: enabled
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }

        function testUserFrontend() {
            const pmiNumber = document.getElementById('testPmiNumber').value;
            const resultDiv = document.getElementById('userFrontendResult');
            
            // 打开用户前端页面进行测试
            const userFrontendUrl = `http://localhost:8080/m/${pmiNumber}`;
            window.open(userFrontendUrl, '_blank');
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `已打开用户前端页面: ${userFrontendUrl}\n请在新窗口中查看是否正确跳转到老系统。`;
        }

        // 页面加载时自动测试获取PMI信息
        window.onload = function() {
            testGetPmiInfo();
        };
    </script>
</body>
</html>
