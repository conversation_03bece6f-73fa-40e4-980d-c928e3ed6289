<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iCalendar信息移除测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #262626;
            margin-top: 0;
        }
        .invitation-text {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #d9d9d9;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s;
        }
        button:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        button.primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        button.primary:hover {
            background: #40a9ff;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            overflow: hidden;
        }
        .comparison-header {
            background: #fafafa;
            padding: 10px;
            font-weight: bold;
            border-bottom: 1px solid #d9d9d9;
        }
        .comparison-content {
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .highlight {
            background: #fff2f0;
            color: #ff4d4f;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📅 iCalendar信息移除测试</h1>
        
        <div class="test-section">
            <h3>🎯 测试目标</h3>
            <p>验证多天会议邀请信息中iCalendar相关内容的移除功能，确保邀请信息简洁易读。</p>
            <ul>
                <li>移除iCalendar文件下载链接</li>
                <li>移除.ics文件相关URL</li>
                <li>移除日历文件相关描述</li>
                <li>清理多余的空行</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试用例1：包含iCalendar信息的邀请</h3>
            <div class="button-group">
                <button class="primary" onclick="testICalendarRemoval()">测试iCalendar移除</button>
                <button onclick="testMultiDayDetection()">测试多天会议检测</button>
                <button onclick="testNormalMeeting()">测试普通会议</button>
            </div>
            
            <div class="comparison" id="comparison1" style="display: none;">
                <div class="comparison-item">
                    <div class="comparison-header">原始邀请信息</div>
                    <div class="comparison-content" id="original-text"></div>
                </div>
                <div class="comparison-item">
                    <div class="comparison-header">简化后邀请信息</div>
                    <div class="comparison-content" id="simplified-text"></div>
                </div>
            </div>
            
            <div id="test-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔄 测试用例2：周期性会议检测</h3>
            <div class="button-group">
                <button onclick="testRecurringMeeting()">测试周期性会议</button>
                <button onclick="testLongDurationMeeting()">测试长时间会议</button>
                <button onclick="testRegularMeeting()">测试普通会议</button>
            </div>
            
            <div id="detection-results" style="display: none;">
                <h4>检测结果：</h4>
                <div id="detection-content"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 实际测试</h3>
            <div class="button-group">
                <button class="primary" onclick="copyTestInvitation()">复制测试邀请</button>
                <button onclick="copySimplifiedInvitation()">复制简化邀请</button>
            </div>
            <div id="copy-status" class="status" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟包含iCalendar信息的邀请文本
        const sampleInvitationWithICalendar = `Zoom邀请你参加已安排的Zoom会议。

主题: 每周项目评审会议
时间: 2025年8月11日 14:00 北京，上海
加入Zoom会议
https://us06web.zoom.us/j/123456789?pwd=abc123

会议号: 123 456 789
密码: abc123

下载iCalendar文件
https://us06web.zoom.us/meeting/123456789/ics?icsToken=98tyAeSrqDkjE9GdsRyARpwqGo_4KeTziClEjbDVkQm7AhVLZjD1b-dWZuFnFNdQ

或者，复制此URL到您的日历应用程序中：
https://us06web.zoom.us/meeting/123456789/ics?icsToken=98tyAeSrqDkjE9GdsRyARpwqGo_4KeTziClEjbDVkQm7AhVLZjD1b-dWZuFnFNdQ

加入说明
https://zoom.us/j/123456789

一键加入移动设备
+8675536550000,,123456789#,,,,*abc123# 中国
+8675536550001,,123456789#,,,,*abc123# 中国

拨入号码
        +86 755 3655 0000 中国
        +86 755 3655 0001 中国

会议ID: 123 456 789
密码: abc123

查找本地号码: https://us06web.zoom.us/u/kcpN4KGUAh

如需了解更多信息，请访问 https://support.zoom.us/hc/zh-cn/articles/201362193

Download iCalendar file for this meeting
https://us06web.zoom.us/meeting/123456789/ics?icsToken=98tyAeSrqDkjE9GdsRyARpwqGo_4KeTziClEjbDVkQm7AhVLZjD1b-dWZuFnFNdQ`;

        // 简化邀请信息的函数（复制自前端代码）
        function simplifyInvitationText(invitationText) {
            if (!invitationText) return invitationText;

            let simplified = invitationText;

            // 移除iCalendar文件下载链接
            simplified = simplified.replace(/下载iCalendar文件[\s\S]*?(?=\n\n|\n$|$)/gi, '');
            simplified = simplified.replace(/Download iCalendar file[\s\S]*?(?=\n\n|\n$|$)/gi, '');
            
            // 移除iCalendar相关的URL
            simplified = simplified.replace(/https?:\/\/[^\s]*\.ics[^\s]*/gi, '');
            
            // 移除包含"icalendar"、"ics"、"calendar"等关键词的行
            simplified = simplified.replace(/.*(?:icalendar|\.ics|calendar file|日历文件).*\n?/gi, '');
            
            // 移除多余的空行（超过2个连续换行符的情况）
            simplified = simplified.replace(/\n{3,}/g, '\n\n');
            
            // 移除开头和结尾的空白字符
            simplified = simplified.trim();

            return simplified;
        }

        // 检查是否为多天会议
        function isMultiDayMeeting(meeting) {
            // 检查是否为周期性会议
            if (meeting.type === 8 || meeting.type === 9) {
                return true;
            }
            
            // 检查是否有recurrence信息
            if (meeting.recurrence || meeting.isRecurring) {
                return true;
            }
            
            // 检查会议时长是否超过24小时（1440分钟）
            const duration = meeting.durationMinutes || meeting.duration;
            if (duration && duration > 1440) {
                return true;
            }
            
            return false;
        }

        // 测试iCalendar移除功能
        function testICalendarRemoval() {
            const originalText = sampleInvitationWithICalendar;
            const simplifiedText = simplifyInvitationText(originalText);
            
            document.getElementById('original-text').textContent = originalText;
            document.getElementById('simplified-text').textContent = simplifiedText;
            document.getElementById('comparison1').style.display = 'grid';
            
            const statusEl = document.getElementById('test-status');
            statusEl.style.display = 'block';
            statusEl.className = 'status success';
            statusEl.textContent = '✅ iCalendar信息移除测试完成！请对比查看效果。';
            
            // 高亮显示被移除的内容
            const originalEl = document.getElementById('original-text');
            let highlightedText = originalText;
            highlightedText = highlightedText.replace(/(下载iCalendar文件[\s\S]*?(?=\n\n|\n$|$))/gi, '<span class="highlight">$1</span>');
            highlightedText = highlightedText.replace(/(https?:\/\/[^\s]*\.ics[^\s]*)/gi, '<span class="highlight">$1</span>');
            highlightedText = highlightedText.replace(/(.*(?:icalendar|\.ics|calendar file|日历文件).*)/gi, '<span class="highlight">$1</span>');
            originalEl.innerHTML = highlightedText;
        }

        // 测试多天会议检测
        function testMultiDayDetection() {
            const testMeetings = [
                { type: 8, topic: '周期性会议（固定时间）' },
                { type: 9, topic: '周期性会议（无固定时间）' },
                { isRecurring: true, topic: '标记为周期性的会议' },
                { duration: 1500, topic: '长时间会议（25小时）' },
                { type: 2, duration: 60, topic: '普通预定会议' }
            ];
            
            let results = '';
            testMeetings.forEach((meeting, index) => {
                const isMultiDay = isMultiDayMeeting(meeting);
                results += `${index + 1}. ${meeting.topic}: ${isMultiDay ? '✅ 多天会议' : '❌ 普通会议'}\n`;
            });
            
            document.getElementById('detection-results').style.display = 'block';
            document.getElementById('detection-content').textContent = results;
        }

        // 测试普通会议
        function testNormalMeeting() {
            const normalMeeting = { type: 2, duration: 60, topic: '普通会议' };
            const isMultiDay = isMultiDayMeeting(normalMeeting);
            
            const statusEl = document.getElementById('test-status');
            statusEl.style.display = 'block';
            statusEl.className = isMultiDay ? 'status warning' : 'status success';
            statusEl.textContent = `普通会议检测结果: ${isMultiDay ? '❌ 误判为多天会议' : '✅ 正确识别为普通会议'}`;
        }

        // 测试周期性会议
        function testRecurringMeeting() {
            const recurringMeeting = { type: 8, isRecurring: true, topic: '周期性会议' };
            const isMultiDay = isMultiDayMeeting(recurringMeeting);
            
            const statusEl = document.getElementById('test-status');
            statusEl.style.display = 'block';
            statusEl.className = isMultiDay ? 'status success' : 'status warning';
            statusEl.textContent = `周期性会议检测结果: ${isMultiDay ? '✅ 正确识别为多天会议' : '❌ 误判为普通会议'}`;
        }

        // 测试长时间会议
        function testLongDurationMeeting() {
            const longMeeting = { type: 2, duration: 1500, topic: '长时间会议' };
            const isMultiDay = isMultiDayMeeting(longMeeting);
            
            const statusEl = document.getElementById('test-status');
            statusEl.style.display = 'block';
            statusEl.className = isMultiDay ? 'status success' : 'status warning';
            statusEl.textContent = `长时间会议检测结果: ${isMultiDay ? '✅ 正确识别为多天会议' : '❌ 误判为普通会议'}`;
        }

        // 测试普通会议
        function testRegularMeeting() {
            const regularMeeting = { type: 2, duration: 60, topic: '普通会议' };
            const isMultiDay = isMultiDayMeeting(regularMeeting);
            
            const statusEl = document.getElementById('test-status');
            statusEl.style.display = 'block';
            statusEl.className = isMultiDay ? 'status warning' : 'status success';
            statusEl.textContent = `普通会议检测结果: ${isMultiDay ? '❌ 误判为多天会议' : '✅ 正确识别为普通会议'}`;
        }

        // 复制测试邀请
        function copyTestInvitation() {
            copyToClipboard(sampleInvitationWithICalendar, '原始邀请信息（包含iCalendar）');
        }

        // 复制简化邀请
        function copySimplifiedInvitation() {
            const simplified = simplifyInvitationText(sampleInvitationWithICalendar);
            copyToClipboard(simplified, '简化邀请信息（已移除iCalendar）');
        }

        // 复制到剪贴板
        function copyToClipboard(text, description) {
            navigator.clipboard.writeText(text).then(() => {
                const statusEl = document.getElementById('copy-status');
                statusEl.style.display = 'block';
                statusEl.className = 'status success';
                statusEl.textContent = `✅ ${description}已复制到剪贴板`;
            }).catch(() => {
                const statusEl = document.getElementById('copy-status');
                statusEl.style.display = 'block';
                statusEl.className = 'status warning';
                statusEl.textContent = `⚠️ 复制失败，请手动复制`;
            });
        }
    </script>
</body>
</html>
