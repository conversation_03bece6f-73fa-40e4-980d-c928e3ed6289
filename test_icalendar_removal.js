/**
 * iCalendar移除功能测试脚本
 * 可以在Node.js环境中运行，验证移除逻辑的正确性
 */

// 简化邀请信息的函数（从前端代码复制）
function simplifyInvitationText(invitationText) {
  if (!invitationText) return invitationText;

  let simplified = invitationText;

  // 移除iCalendar文件下载链接（包括多行内容）
  simplified = simplified.replace(/下载iCalendar文件[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');
  simplified = simplified.replace(/Download iCalendar file[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');

  // 移除包含"或者，复制此URL到您的日历应用程序中"的段落
  simplified = simplified.replace(/或者，复制此URL到您的日历应用程序中：[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');
  simplified = simplified.replace(/Or copy this URL into your calendar application:[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');

  // 移除iCalendar相关的URL（包括ics链接）
  simplified = simplified.replace(/https?:\/\/[^\s]*\.ics[^\s]*/gi, '');
  simplified = simplified.replace(/https?:\/\/[^\s]*icsToken[^\s]*/gi, '');

  // 移除包含"icalendar"、"ics"、"calendar"等关键词的行
  simplified = simplified.replace(/.*(?:icalendar|\.ics|calendar file|日历文件|icsToken).*\n?/gi, '');

  // 移除空行（只包含空白字符的行）
  simplified = simplified.replace(/^\s*\n/gm, '');

  // 移除多余的空行（超过2个连续换行符的情况）
  simplified = simplified.replace(/\n{3,}/g, '\n\n');

  // 移除开头和结尾的空白字符
  simplified = simplified.trim();

  return simplified;
}

// 检查是否为多天会议
function isMultiDayMeeting(meeting) {
  // 检查是否为周期性会议
  if (meeting.type === 8 || meeting.type === 9) {
    return true;
  }
  
  // 检查是否有recurrence信息
  if (meeting.recurrence || meeting.isRecurring) {
    return true;
  }
  
  // 检查会议时长是否超过24小时（1440分钟）
  const duration = meeting.durationMinutes || meeting.duration;
  if (duration && duration > 1440) {
    return true;
  }
  
  return false;
}

// 测试数据
const testInvitationWithICalendar = `Zoom邀请你参加已安排的Zoom会议。

主题: 每周项目评审会议
时间: 2025年8月11日 14:00 北京，上海
加入Zoom会议
https://us06web.zoom.us/j/123456789?pwd=abc123

会议号: 123 456 789
密码: abc123

下载iCalendar文件
https://us06web.zoom.us/meeting/123456789/ics?icsToken=98tyAeSrqDkjE9GdsRyARpwqGo_4KeTziClEjbDVkQm7AhVLZjD1b-dWZuFnFNdQ

或者，复制此URL到您的日历应用程序中：
https://us06web.zoom.us/meeting/123456789/ics?icsToken=98tyAeSrqDkjE9GdsRyARpwqGo_4KeTziClEjbDVkQm7AhVLZjD1b-dWZuFnFNdQ

加入说明
https://zoom.us/j/123456789

一键加入移动设备
+8675536550000,,123456789#,,,,*abc123# 中国
+8675536550001,,123456789#,,,,*abc123# 中国

拨入号码
        +86 755 3655 0000 中国
        +86 755 3655 0001 中国

会议ID: 123 456 789
密码: abc123

查找本地号码: https://us06web.zoom.us/u/kcpN4KGUAh

如需了解更多信息，请访问 https://support.zoom.us/hc/zh-cn/articles/201362193

Download iCalendar file for this meeting
https://us06web.zoom.us/meeting/123456789/ics?icsToken=98tyAeSrqDkjE9GdsRyARpwqGo_4KeTziClEjbDVkQm7AhVLZjD1b-dWZuFnFNdQ`;

const testMeetings = [
  { type: 8, topic: '周期性会议（固定时间）', isMultiDay: true },
  { type: 9, topic: '周期性会议（无固定时间）', isMultiDay: true },
  { isRecurring: true, topic: '标记为周期性的会议', isMultiDay: true },
  { duration: 1500, topic: '长时间会议（25小时）', isMultiDay: true },
  { type: 2, duration: 60, topic: '普通预定会议', isMultiDay: false },
  { type: 1, duration: 30, topic: '即时会议', isMultiDay: false }
];

// 运行测试
function runTests() {
  console.log('🧪 开始iCalendar移除功能测试\n');

  // 测试1: 多天会议检测
  console.log('📋 测试1: 多天会议检测');
  console.log('=' * 50);
  
  testMeetings.forEach((meeting, index) => {
    const detected = isMultiDayMeeting(meeting);
    const expected = meeting.isMultiDay;
    const status = detected === expected ? '✅' : '❌';
    
    console.log(`${index + 1}. ${meeting.topic}`);
    console.log(`   预期: ${expected ? '多天会议' : '普通会议'}`);
    console.log(`   检测: ${detected ? '多天会议' : '普通会议'} ${status}`);
    console.log('');
  });

  // 测试2: iCalendar信息移除
  console.log('📋 测试2: iCalendar信息移除');
  console.log('=' * 50);
  
  const originalText = testInvitationWithICalendar;
  const simplifiedText = simplifyInvitationText(originalText);
  
  console.log('原始邀请信息长度:', originalText.length);
  console.log('简化后邀请信息长度:', simplifiedText.length);
  console.log('减少字符数:', originalText.length - simplifiedText.length);
  console.log('');
  
  // 检查是否成功移除iCalendar相关内容
  const hasICalendarContent = simplifiedText.includes('iCalendar') || 
                             simplifiedText.includes('.ics') || 
                             simplifiedText.includes('下载iCalendar') ||
                             simplifiedText.includes('Download iCalendar');
  
  console.log('iCalendar内容移除:', hasICalendarContent ? '❌ 未完全移除' : '✅ 成功移除');
  
  // 检查核心内容是否保留
  const hasCoreContent = simplifiedText.includes('会议号') && 
                        simplifiedText.includes('密码') && 
                        simplifiedText.includes('加入Zoom会议');
  
  console.log('核心内容保留:', hasCoreContent ? '✅ 保留完整' : '❌ 内容丢失');
  console.log('');

  // 测试3: 显示简化前后对比
  console.log('📋 测试3: 简化前后对比');
  console.log('=' * 50);
  
  console.log('🔸 原始邀请信息:');
  console.log(originalText);
  console.log('\n' + '=' * 50 + '\n');
  
  console.log('🔸 简化后邀请信息:');
  console.log(simplifiedText);
  console.log('\n' + '=' * 50 + '\n');

  // 测试4: 边界情况测试
  console.log('📋 测试4: 边界情况测试');
  console.log('=' * 50);
  
  const edgeCases = [
    { input: null, description: 'null输入' },
    { input: '', description: '空字符串' },
    { input: '普通文本，不包含iCalendar信息', description: '无iCalendar内容' },
    { input: 'https://example.com/meeting.ics', description: '仅包含ics链接' }
  ];
  
  edgeCases.forEach((testCase, index) => {
    const result = simplifyInvitationText(testCase.input);
    console.log(`${index + 1}. ${testCase.description}`);
    console.log(`   输入: ${testCase.input || 'null'}`);
    console.log(`   输出: ${result || 'null'}`);
    console.log(`   状态: ${result !== undefined ? '✅ 正常处理' : '❌ 处理异常'}`);
    console.log('');
  });

  console.log('🎉 测试完成！');
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    simplifyInvitationText,
    isMultiDayMeeting,
    runTests
  };
  
  // 直接运行测试
  if (require.main === module) {
    runTests();
  }
} else {
  // 在浏览器环境中运行
  runTests();
}
