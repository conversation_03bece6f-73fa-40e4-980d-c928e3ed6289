#!/bin/bash

# 测试PMI窗口即时执行机制
# 使用方法: ./test_immediate_execution.sh

BASE_URL="http://localhost:8080"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 获取认证token (简化版，实际需要登录)
get_auth_token() {
    # 这里应该通过登录API获取token，暂时跳过
    echo ""
}

# 测试API连通性
test_api_connectivity() {
    log_info "测试API连通性..."
    
    response=$(curl -s "$BASE_URL/actuator/health")
    if echo "$response" | grep -q '"status":"UP"'; then
        log_success "API连通性正常"
        return 0
    else
        log_error "API连通性异常"
        return 1
    fi
}

# 创建测试PMI记录
create_test_pmi() {
    log_info "创建测试PMI记录..."
    
    # 这里需要实际的PMI创建逻辑
    # 暂时返回一个假的PMI ID
    echo "999"
}

# 测试即刻开始的窗口
test_immediate_window() {
    local pmi_id=$1
    log_info "测试即刻开始的窗口..."
    
    # 获取当前时间
    current_time=$(date -u +"%Y-%m-%dT%H:%M:%S")
    end_time=$(date -u -d "+2 hours" +"%Y-%m-%dT%H:%M:%S")
    
    log_info "创建即刻开始的窗口: $current_time - $end_time"
    
    # 创建窗口的API调用（需要根据实际API调整）
    window_data="{
        \"pmiRecordId\": $pmi_id,
        \"startDateTime\": \"$current_time\",
        \"endDateTime\": \"$end_time\",
        \"description\": \"即时执行测试窗口\"
    }"
    
    response=$(curl -s -X POST "$BASE_URL/api/pmi-schedule-windows" \
        -H "Content-Type: application/json" \
        -d "$window_data")
    
    if echo "$response" | grep -q '"success":true'; then
        window_id=$(echo "$response" | jq -r '.data.id' 2>/dev/null || echo "unknown")
        log_success "窗口创建成功: ID=$window_id"
        
        # 等待一下让任务执行
        sleep 3
        
        # 检查任务状态
        check_window_tasks "$window_id"
        return 0
    else
        log_error "窗口创建失败: $response"
        return 1
    fi
}

# 测试过往时间的窗口
test_past_window() {
    local pmi_id=$1
    log_info "测试过往时间的窗口..."
    
    # 获取30分钟前的时间
    past_time=$(date -u -d "-30 minutes" +"%Y-%m-%dT%H:%M:%S")
    end_time=$(date -u -d "+1 hour" +"%Y-%m-%dT%H:%M:%S")
    
    log_info "创建过往时间的窗口: $past_time - $end_time"
    
    window_data="{
        \"pmiRecordId\": $pmi_id,
        \"startDateTime\": \"$past_time\",
        \"endDateTime\": \"$end_time\",
        \"description\": \"过往时间测试窗口\"
    }"
    
    response=$(curl -s -X POST "$BASE_URL/api/pmi-schedule-windows" \
        -H "Content-Type: application/json" \
        -d "$window_data")
    
    if echo "$response" | grep -q '"success":true'; then
        window_id=$(echo "$response" | jq -r '.data.id' 2>/dev/null || echo "unknown")
        log_success "过往窗口创建成功: ID=$window_id"
        
        # 等待一下让任务执行
        sleep 3
        
        # 检查任务状态
        check_window_tasks "$window_id"
        return 0
    else
        log_error "过往窗口创建失败: $response"
        return 1
    fi
}

# 测试未来时间的窗口
test_future_window() {
    local pmi_id=$1
    log_info "测试未来时间的窗口..."
    
    # 获取1小时后的时间
    future_time=$(date -u -d "+1 hour" +"%Y-%m-%dT%H:%M:%S")
    end_time=$(date -u -d "+3 hours" +"%Y-%m-%dT%H:%M:%S")
    
    log_info "创建未来时间的窗口: $future_time - $end_time"
    
    window_data="{
        \"pmiRecordId\": $pmi_id,
        \"startDateTime\": \"$future_time\",
        \"endDateTime\": \"$end_time\",
        \"description\": \"未来时间测试窗口\"
    }"
    
    response=$(curl -s -X POST "$BASE_URL/api/pmi-schedule-windows" \
        -H "Content-Type: application/json" \
        -d "$window_data")
    
    if echo "$response" | grep -q '"success":true'; then
        window_id=$(echo "$response" | jq -r '.data.id' 2>/dev/null || echo "unknown")
        log_success "未来窗口创建成功: ID=$window_id"
        
        # 检查任务状态（应该是SCHEDULED）
        check_window_tasks "$window_id"
        return 0
    else
        log_error "未来窗口创建失败: $response"
        return 1
    fi
}

# 检查窗口的任务状态
check_window_tasks() {
    local window_id=$1
    log_info "检查窗口 $window_id 的任务状态..."
    
    # 获取窗口的任务
    response=$(curl -s "$BASE_URL/api/pmi-scheduled-tasks?windowId=$window_id")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "任务查询成功"
        
        # 解析任务信息
        echo "$response" | jq '.data.items[] | {id: .id, taskType: .taskType, status: .status, scheduledTime: .scheduledTime, actualExecutionTime: .actualExecutionTime}' 2>/dev/null || {
            log_warn "无法解析任务信息，原始响应:"
            echo "$response"
        }
    else
        log_error "任务查询失败: $response"
    fi
}

# 检查系统配置
check_system_config() {
    log_info "检查系统配置..."
    
    # 检查立即执行配置是否生效
    response=$(curl -s "$BASE_URL/actuator/configprops" | grep -A 20 "immediate-execution" || echo "配置未找到")
    
    if [ "$response" != "配置未找到" ]; then
        log_success "找到立即执行配置"
        echo "$response"
    else
        log_warn "未找到立即执行配置，可能使用默认值"
    fi
}

# 查看应用日志中的相关信息
check_application_logs() {
    log_info "检查应用日志..."
    
    if [ -f "logs/zoombus-application.log" ]; then
        log_info "最近的立即执行相关日志:"
        tail -50 logs/zoombus-application.log | grep -i "immediate\|strategy\|立即执行" || log_warn "未找到相关日志"
    else
        log_warn "日志文件不存在"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "PMI窗口即时执行机制测试"
    echo "========================================"
    echo "测试时间: $(date)"
    echo "========================================"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl命令未找到，请安装curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warn "jq命令未找到，JSON输出将不会格式化"
    fi
    
    # 1. 测试API连通性
    if ! test_api_connectivity; then
        exit 1
    fi
    echo
    
    # 2. 检查系统配置
    check_system_config
    echo
    
    # 3. 检查应用日志
    check_application_logs
    echo
    
    # 4. 创建测试PMI
    pmi_id=$(create_test_pmi)
    log_info "使用PMI ID: $pmi_id"
    echo
    
    # 5. 测试即刻开始的窗口
    test_immediate_window "$pmi_id"
    echo
    
    # 6. 测试过往时间的窗口
    test_past_window "$pmi_id"
    echo
    
    # 7. 测试未来时间的窗口
    test_future_window "$pmi_id"
    echo
    
    log_success "测试完成！"
    echo
    echo "========================================"
    echo "测试总结："
    echo "1. 即刻开始的窗口应该立即执行（秒级响应）"
    echo "2. 过往时间的窗口应该立即执行（如果在有效期内）"
    echo "3. 未来时间的窗口应该调度执行（状态为SCHEDULED）"
    echo "4. 查看日志确认执行策略判断是否正确"
    echo "========================================"
}

# 执行主函数
main "$@"
