-- 测试会议创建的SQL脚本
-- 验证我们的修复是否有效

-- 1. 测试创建会议时 zoom_meeting_id 为 NULL（这应该成功）
INSERT INTO t_meetings (
    topic, 
    start_time, 
    status, 
    type, 
    creation_source, 
    created_at, 
    updated_at,
    zoom_meeting_id
) VALUES (
    '测试会议 - NULL zoom_meeting_id', 
    '2025-07-21 10:00:00', 
    'CREATING', 
    'SCHEDULED', 
    'ADMIN_PANEL', 
    NOW(), 
    NOW(),
    NULL
);

-- 2. 查看刚创建的会议
SELECT id, topic, zoom_meeting_id, status, created_at 
FROM t_meetings 
WHERE topic = '测试会议 - NULL zoom_meeting_id';

-- 3. 更新会议，添加 zoom_meeting_id 并改变状态（模拟 Zoom API 成功后的更新）
UPDATE t_meetings 
SET zoom_meeting_id = '123456789', 
    status = 'SCHEDULED', 
    updated_at = NOW() 
WHERE topic = '测试会议 - NULL zoom_meeting_id';

-- 4. 验证更新结果
SELECT id, topic, zoom_meeting_id, status, updated_at 
FROM t_meetings 
WHERE topic = '测试会议 - NULL zoom_meeting_id';

-- 5. 清理测试数据
DELETE FROM t_meetings WHERE topic = '测试会议 - NULL zoom_meeting_id';

-- 6. 验证表结构
DESCRIBE t_meetings;
