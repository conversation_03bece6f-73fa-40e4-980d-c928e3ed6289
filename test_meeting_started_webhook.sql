-- 测试meeting.started webhook处理的SQL脚本
-- 用于验证修复是否有效

-- 1. 清理测试数据
DELETE FROM t_zoom_meetings WHERE zoom_meeting_uuid LIKE 'test-uuid-%';
DELETE FROM t_meetings WHERE zoom_meeting_id LIKE 'test-meeting-%';
DELETE FROM t_pmi_records WHERE pmi_number LIKE 'test-pmi-%';

-- 2. 创建测试PMI记录
INSERT INTO t_pmi_records (
    pmi_number, billing_mode, available_minutes, total_minutes,
    overdraft_minutes, pending_deduct_minutes, status,
    created_at, updated_at
) VALUES (
    'test-pmi-123456', 'BY_TIME', 100, 200, 0, 0, 'ACTIVE',
    NOW(), NOW()
);

-- 3. 创建测试安排会议记录
INSERT INTO t_meetings (
    zoom_meeting_id, topic, start_time, status, type,
    creation_source, creator_user_id, created_at, updated_at
) VALUES (
    'test-meeting-789012', '测试安排会议',
    DATE_ADD(NOW(), INTERVAL 1 HOUR), 'SCHEDULED', 'SCHEDULED',
    'ADMIN_PANEL', 1, NOW(), NOW()
);

-- 4. 查看测试数据
SELECT 'PMI记录' as type, id, pmi_number as identifier FROM t_pmi_records WHERE pmi_number LIKE 'test-%'
UNION ALL
SELECT 'Meeting记录' as type, id, zoom_meeting_id as identifier FROM t_meetings WHERE zoom_meeting_id LIKE 'test-%';

-- 验证查询（在webhook处理后执行）
-- SELECT
--     zm.id,
--     zm.zoom_meeting_uuid,
--     zm.zoom_meeting_id,
--     zm.pmi_record_id,
--     zm.topic,
--     zm.status,
--     zm.billing_mode,
--     CASE
--         WHEN zm.pmi_record_id IS NOT NULL THEN 'PMI会议'
--         WHEN m.id IS NOT NULL THEN '安排会议'
--         ELSE '其他会议'
--     END as meeting_type
-- FROM t_zoom_meetings zm
-- LEFT JOIN t_meetings m ON zm.zoom_meeting_id = m.zoom_meeting_id
-- WHERE zm.zoom_meeting_uuid LIKE 'test-uuid-%'
-- ORDER BY zm.created_at;
