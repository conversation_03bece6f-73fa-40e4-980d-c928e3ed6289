#!/bin/bash

# 测试Zoom会议看板"姓名"字段功能
echo "=== 测试Zoom会议看板新增姓名字段功能 ==="

# 1. 测试后端健康检查
echo "1. 测试后端服务状态..."
HEALTH_RESPONSE=$(curl -s http://localhost:8080/actuator/health)
if echo "$HEALTH_RESPONSE" | grep -q '"status":"UP"'; then
    echo "✅ 后端服务运行正常"
else
    echo "❌ 后端服务异常"
    echo "$HEALTH_RESPONSE"
    exit 1
fi

# 2. 测试前端服务
echo -e "\n2. 测试前端服务状态..."
FRONTEND_STATUS=$(curl -s -I http://localhost:3000 | head -1)
if echo "$FRONTEND_STATUS" | grep -q "200 OK"; then
    echo "✅ 前端服务运行正常"
else
    echo "❌ 前端服务异常"
    echo "$FRONTEND_STATUS"
fi

# 3. 检查前端代码修改
echo -e "\n3. 检查前端代码修改..."
if grep -q "assignedUserFullName" frontend/src/pages/ZoomMeetingDashboard.js; then
    echo "✅ 前端已添加assignedUserFullName字段"
else
    echo "❌ 前端未找到assignedUserFullName字段"
fi

if grep -q "title: '姓名'" frontend/src/pages/ZoomMeetingDashboard.js; then
    echo "✅ 前端已添加姓名列标题"
else
    echo "❌ 前端未找到姓名列标题"
fi

# 4. 检查后端实体类
echo -e "\n4. 检查后端实体类..."
if grep -q "assignedUserFullName" src/main/java/com/zoombus/entity/ZoomMeeting.java; then
    echo "✅ 后端实体已包含assignedUserFullName字段"
else
    echo "❌ 后端实体未找到assignedUserFullName字段"
fi

# 5. 检查后端服务方法
echo -e "\n5. 检查后端服务方法..."
if grep -q "enrichMeetingsWithUserNames" src/main/java/com/zoombus/service/ZoomMeetingService.java; then
    echo "✅ 后端服务已包含用户姓名填充方法"
else
    echo "❌ 后端服务未找到用户姓名填充方法"
fi

# 6. 功能总结
echo -e "\n=== 功能实现总结 ==="
echo "✅ 需求: 在Zoom会议看板页面的'会议号'后面新增'姓名'字段"
echo "✅ 数据源: 展示会议对应的t_user的full_name字段"
echo "✅ 实现范围: 活跃会议表格 + 历史会议表格"

echo -e "\n📋 技术实现:"
echo "1. 前端修改:"
echo "   - 在activeMeetingColumns中添加姓名列"
echo "   - 在historyMeetingColumns中添加姓名列"
echo "   - 列配置: dataIndex='assignedUserFullName', title='姓名'"
echo "   - 样式: 支持移动端适配，ellipsis省略显示"

echo "2. 后端支持:"
echo "   - ZoomMeeting实体已有@Transient字段assignedUserFullName"
echo "   - ZoomMeetingService.enrichMeetingsWithUserNames()方法填充用户姓名"
echo "   - 数据流: assignedZoomUserId -> ZoomUser -> User.fullName"
echo "   - 降级显示: fullName -> firstName+lastName -> email"

echo "3. API接口:"
echo "   - GET /api/zoom-meetings/active - 活跃会议列表"
echo "   - GET /api/zoom-meetings/history - 历史会议列表"
echo "   - 两个接口都会自动填充assignedUserFullName字段"

echo -e "\n🌐 访问地址:"
echo "- Zoom会议看板: http://localhost:3000/zoom-meeting-dashboard"
echo "- 后端API: http://localhost:8080/api/zoom-meetings/"
echo "- 健康检查: http://localhost:8080/actuator/health"

echo -e "\n🎉 姓名字段功能已成功实现并部署！"
echo "现在可以在Zoom会议看板页面看到会议号后面的姓名列，显示对应用户的full_name信息。"
