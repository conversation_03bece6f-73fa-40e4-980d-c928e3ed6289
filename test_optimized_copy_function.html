<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的复制功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #262626;
            margin-top: 0;
        }
        .meeting-info {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s;
        }
        button:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        button.primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        button.primary:hover {
            background: #40a9ff;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        .status.warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        .cache-info {
            background: #e6f7ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 12px;
            color: #1890ff;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #52c41a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 优化后的会议复制功能测试</h1>
        
        <div class="test-section">
            <h3>📋 优化功能特性</h3>
            <ul class="feature-list">
                <li>统一复制接口：所有复制操作都优先调用后台API</li>
                <li>智能缓存：5分钟缓存减少重复API调用</li>
                <li>详细错误处理：根据错误类型提供具体提示</li>
                <li>优雅降级：API失败时使用本地信息</li>
                <li>兼容性优化：支持现代和传统剪贴板API</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试场景1：正常会议复制</h3>
            <div class="meeting-info">
                <strong>测试会议信息：</strong><br>
                主题: 产品评审会议<br>
                时间: 2025-08-11 14:00:00<br>
                时长: 60分钟<br>
                会议ID: 123456789<br>
                密码: abc123<br>
                加入链接: https://zoom.us/j/123456789
            </div>
            <div class="button-group">
                <button class="primary" onclick="testNormalCopy()">测试正常复制</button>
                <button onclick="testCacheHit()">测试缓存命中</button>
                <button onclick="clearCache()">清除缓存</button>
            </div>
            <div id="normal-status" class="status" style="display: none;"></div>
            <div id="cache-info" class="cache-info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔄 测试场景2：周期性会议复制</h3>
            <div class="meeting-info">
                <strong>周期性会议 - Occurrence信息：</strong><br>
                主题: 每周例会<br>
                主会议时间: 2025-08-11 10:00:00<br>
                Occurrence时间: 2025-08-18 10:00:00<br>
                时长: 30分钟<br>
                会议ID: 987654321<br>
                密码: xyz789
            </div>
            <div class="button-group">
                <button class="primary" onclick="testOccurrenceCopy()">测试Occurrence复制</button>
                <button onclick="testMainMeetingCopy()">测试主会议复制</button>
            </div>
            <div id="occurrence-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>⚠️ 测试场景3：错误处理</h3>
            <div class="button-group">
                <button onclick="testApiError404()">模拟404错误</button>
                <button onclick="testApiError401()">模拟401错误</button>
                <button onclick="testApiError500()">模拟500错误</button>
                <button onclick="testNetworkError()">模拟网络错误</button>
            </div>
            <div id="error-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 缓存状态监控</h3>
            <div class="button-group">
                <button onclick="showCacheStatus()">查看缓存状态</button>
                <button onclick="testCacheExpiry()">测试缓存过期</button>
            </div>
            <div id="cache-status" class="status" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟优化后的复制功能
        class OptimizedCopyFunction {
            constructor() {
                this.cache = new Map();
                this.CACHE_DURATION = 5 * 60 * 1000; // 5分钟
            }

            cleanExpiredCache() {
                const now = Date.now();
                for (const [key, value] of this.cache.entries()) {
                    if (now - value.timestamp > this.CACHE_DURATION) {
                        this.cache.delete(key);
                    }
                }
            }

            getCachedInvitation(meetingId) {
                this.cleanExpiredCache();
                const cached = this.cache.get(meetingId);
                if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
                    return cached.invitation;
                }
                return null;
            }

            setCachedInvitation(meetingId, invitation) {
                this.cache.set(meetingId, {
                    invitation,
                    timestamp: Date.now()
                });
            }

            async mockApiCall(meetingId, shouldFail = false, errorType = '404') {
                // 模拟API延迟
                await new Promise(resolve => setTimeout(resolve, 500));
                
                if (shouldFail) {
                    const error = new Error('API调用失败');
                    error.response = { status: parseInt(errorType) };
                    throw error;
                }

                return {
                    data: {
                        success: true,
                        data: {
                            invitation: `Zoom邀请你参加已安排的Zoom会议。

主题: 测试会议 (ID: ${meetingId})
时间: 2025年8月11日 14:00 北京，上海
加入Zoom会议
https://zoom.us/j/${meetingId}

会议号: ${meetingId.replace(/(\d{3})(?=\d)/g, '$1 ')}
密码: abc123

这是从API获取的最新邀请信息！`
                        }
                    }
                };
            }

            async copyMeetingInvitation(meeting, occurrence = null, shouldFail = false, errorType = '404') {
                try {
                    let invitationText = null;
                    const meetingId = meeting.id;
                    
                    // 检查缓存
                    const cachedInvitation = this.getCachedInvitation(meetingId);
                    if (cachedInvitation) {
                        console.log('使用缓存的邀请信息');
                        invitationText = cachedInvitation;
                        return { success: true, source: 'cache', invitation: invitationText };
                    }

                    // 模拟API调用
                    try {
                        console.log('正在获取最新的邀请信息...');
                        const response = await this.mockApiCall(meetingId, shouldFail, errorType);
                        
                        if (response.data.success && response.data.data && response.data.data.invitation) {
                            invitationText = response.data.data.invitation;
                            this.setCachedInvitation(meetingId, invitationText);
                            console.log('成功获取并缓存Zoom API邀请信息');
                            return { success: true, source: 'api', invitation: invitationText };
                        }
                    } catch (apiError) {
                        console.warn('获取Zoom API邀请信息失败:', apiError);
                        
                        let errorMessage = '获取最新邀请信息失败';
                        if (apiError.response) {
                            const status = apiError.response.status;
                            if (status === 404) {
                                errorMessage = '会议不存在或已被删除';
                            } else if (status === 401) {
                                errorMessage = 'Zoom认证失效，请联系管理员';
                            } else if (status >= 500) {
                                errorMessage = '服务器暂时不可用，请稍后重试';
                            } else {
                                errorMessage = `获取邀请信息失败 (${status})`;
                            }
                        }
                        
                        // 使用本地信息生成邀请
                        const fallbackInvitation = this.generateFallbackInvitation(meeting, occurrence);
                        return { 
                            success: true, 
                            source: 'fallback', 
                            invitation: fallbackInvitation,
                            warning: errorMessage
                        };
                    }
                } catch (error) {
                    return { success: false, error: error.message };
                }
            }

            generateFallbackInvitation(meeting, occurrence = null) {
                const meetingTime = (occurrence && occurrence.occurrenceStartTime) || meeting.startTime;
                return `会议主题：${meeting.topic}
会议时间：${meetingTime}
持续时间：${meeting.duration} 分钟
会议ID：${meeting.id}
会议密码：${meeting.password || '无'}
加入链接：${meeting.joinUrl}

(使用本地信息生成)`;
            }

            getCacheStatus() {
                this.cleanExpiredCache();
                return {
                    size: this.cache.size,
                    entries: Array.from(this.cache.entries()).map(([key, value]) => ({
                        meetingId: key,
                        timestamp: new Date(value.timestamp).toLocaleString(),
                        age: Math.round((Date.now() - value.timestamp) / 1000) + '秒'
                    }))
                };
            }

            clearCache() {
                this.cache.clear();
            }
        }

        const copyFunction = new OptimizedCopyFunction();

        // 测试数据
        const testMeeting = {
            id: '123456789',
            topic: '产品评审会议',
            startTime: '2025-08-11 14:00:00',
            duration: 60,
            password: 'abc123',
            joinUrl: 'https://zoom.us/j/123456789'
        };

        const testOccurrence = {
            occurrenceStartTime: '2025-08-18 10:00:00',
            duration: 30
        };

        const recurringMeeting = {
            id: '987654321',
            topic: '每周例会',
            startTime: '2025-08-11 10:00:00',
            duration: 30,
            password: 'xyz789',
            joinUrl: 'https://zoom.us/j/987654321'
        };

        // 测试函数
        async function testNormalCopy() {
            const statusEl = document.getElementById('normal-status');
            statusEl.style.display = 'block';
            statusEl.className = 'status';
            statusEl.textContent = '正在复制...';

            const result = await copyFunction.copyMeetingInvitation(testMeeting);
            
            if (result.success) {
                statusEl.className = 'status success';
                statusEl.textContent = `✅ 复制成功！数据来源：${result.source === 'api' ? 'API调用' : result.source === 'cache' ? '缓存' : '本地生成'}`;
                
                if (result.warning) {
                    statusEl.className = 'status warning';
                    statusEl.textContent = `⚠️ ${result.warning}，已使用本地信息生成邀请`;
                }
                
                updateCacheInfo();
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 复制失败：${result.error}`;
            }
        }

        async function testCacheHit() {
            await testNormalCopy(); // 先执行一次确保有缓存
            setTimeout(async () => {
                await testNormalCopy(); // 再执行一次测试缓存命中
            }, 1000);
        }

        async function testOccurrenceCopy() {
            const statusEl = document.getElementById('occurrence-status');
            statusEl.style.display = 'block';
            statusEl.className = 'status';
            statusEl.textContent = '正在复制Occurrence...';

            const result = await copyFunction.copyMeetingInvitation(recurringMeeting, testOccurrence);
            
            if (result.success) {
                statusEl.className = 'status success';
                statusEl.textContent = `✅ Occurrence复制成功！数据来源：${result.source === 'api' ? 'API调用' : result.source === 'cache' ? '缓存' : '本地生成'}`;
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 复制失败：${result.error}`;
            }
        }

        async function testMainMeetingCopy() {
            const statusEl = document.getElementById('occurrence-status');
            statusEl.style.display = 'block';
            statusEl.className = 'status';
            statusEl.textContent = '正在复制主会议...';

            const result = await copyFunction.copyMeetingInvitation(recurringMeeting);
            
            if (result.success) {
                statusEl.className = 'status success';
                statusEl.textContent = `✅ 主会议复制成功！数据来源：${result.source === 'api' ? 'API调用' : result.source === 'cache' ? '缓存' : '本地生成'}`;
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 复制失败：${result.error}`;
            }
        }

        async function testApiError404() {
            await testApiError('404');
        }

        async function testApiError401() {
            await testApiError('401');
        }

        async function testApiError500() {
            await testApiError('500');
        }

        async function testNetworkError() {
            await testApiError('network');
        }

        async function testApiError(errorType) {
            const statusEl = document.getElementById('error-status');
            statusEl.style.display = 'block';
            statusEl.className = 'status';
            statusEl.textContent = `正在测试${errorType}错误...`;

            const result = await copyFunction.copyMeetingInvitation(testMeeting, null, true, errorType);
            
            if (result.success && result.warning) {
                statusEl.className = 'status warning';
                statusEl.textContent = `⚠️ ${result.warning}，已使用本地信息生成邀请`;
            } else if (result.success) {
                statusEl.className = 'status success';
                statusEl.textContent = `✅ 错误处理成功，使用降级方案`;
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 错误处理失败：${result.error}`;
            }
        }

        function showCacheStatus() {
            const statusEl = document.getElementById('cache-status');
            const cacheStatus = copyFunction.getCacheStatus();
            
            statusEl.style.display = 'block';
            statusEl.className = 'status success';
            
            if (cacheStatus.size === 0) {
                statusEl.textContent = '📊 缓存为空';
            } else {
                const entries = cacheStatus.entries.map(entry => 
                    `会议${entry.meetingId}: ${entry.timestamp} (${entry.age}前)`
                ).join('\n');
                statusEl.innerHTML = `📊 缓存状态 (${cacheStatus.size}条记录):<br><pre style="margin: 10px 0; font-size: 12px;">${entries}</pre>`;
            }
        }

        function testCacheExpiry() {
            // 模拟缓存过期（实际项目中是5分钟，这里为了测试设置为较短时间）
            const statusEl = document.getElementById('cache-status');
            statusEl.style.display = 'block';
            statusEl.className = 'status warning';
            statusEl.textContent = '⏰ 缓存过期测试：实际项目中缓存5分钟后自动过期';
        }

        function clearCache() {
            copyFunction.clearCache();
            updateCacheInfo();
            
            const statusEl = document.getElementById('normal-status');
            statusEl.style.display = 'block';
            statusEl.className = 'status success';
            statusEl.textContent = '🗑️ 缓存已清除';
        }

        function updateCacheInfo() {
            const cacheInfoEl = document.getElementById('cache-info');
            const cacheStatus = copyFunction.getCacheStatus();
            
            if (cacheStatus.size > 0) {
                cacheInfoEl.style.display = 'block';
                cacheInfoEl.textContent = `💾 当前缓存：${cacheStatus.size}条记录`;
            } else {
                cacheInfoEl.style.display = 'none';
            }
        }

        // 页面加载时显示初始状态
        window.onload = function() {
            updateCacheInfo();
        };
    </script>
</body>
</html>
