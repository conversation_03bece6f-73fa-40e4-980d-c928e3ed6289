-- 测试PMI筛选和排序功能
-- 执行日期: 2025-08-18

USE zoombusV;

-- ========================================
-- 测试数据验证
-- ========================================

SELECT '=== PMI筛选和排序功能测试 ===' as test_title;

-- 1. 验证LONG类型PMI的排序（按到期日由近至远）
SELECT '=== LONG类型PMI排序测试（按到期日由近至远） ===' as test_section;

SELECT 
    p.id,
    p.pmi_number,
    p.billing_mode,
    DATE_FORMAT(p.window_expire_time, '%Y-%m-%d %H:%i:%s') as window_expire_time,
    DATEDIFF(p.window_expire_time, NOW()) as days_until_expire
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND p.window_expire_time IS NOT NULL
ORDER BY p.window_expire_time ASC
LIMIT 10;

-- 2. 验证BY_TIME类型PMI的排序（按剩余可用时长由长到短）
SELECT '=== BY_TIME类型PMI排序测试（按剩余可用时长由长到短） ===' as test_section;

SELECT 
    p.id,
    p.pmi_number,
    p.billing_mode,
    p.available_minutes,
    ROUND(p.available_minutes / 60.0, 2) as available_hours
FROM t_pmi_records p
WHERE p.billing_mode = 'BY_TIME'
ORDER BY p.available_minutes DESC
LIMIT 10;

-- 3. 验证混合排序（LONG优先，然后BY_TIME）
SELECT '=== 混合排序测试（LONG优先，按到期日；BY_TIME按剩余时长） ===' as test_section;

SELECT 
    p.id,
    p.pmi_number,
    p.billing_mode,
    CASE 
        WHEN p.billing_mode = 'LONG' THEN DATE_FORMAT(p.window_expire_time, '%Y-%m-%d %H:%i:%s')
        ELSE NULL
    END as window_expire_time,
    CASE 
        WHEN p.billing_mode = 'BY_TIME' THEN p.available_minutes
        ELSE NULL
    END as available_minutes,
    CASE 
        WHEN p.billing_mode = 'LONG' THEN DATEDIFF(p.window_expire_time, NOW())
        ELSE NULL
    END as days_until_expire
FROM t_pmi_records p
ORDER BY 
    CASE WHEN p.billing_mode = 'LONG' THEN 0 ELSE 1 END,
    CASE WHEN p.billing_mode = 'LONG' THEN p.window_expire_time END ASC,
    CASE WHEN p.billing_mode = 'BY_TIME' THEN p.available_minutes END DESC,
    p.created_at DESC
LIMIT 15;

-- 4. 统计各计费类型的数量
SELECT '=== 计费类型统计 ===' as statistics;

SELECT 
    billing_mode,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_pmi_records), 2) as percentage
FROM t_pmi_records
GROUP BY billing_mode
ORDER BY count DESC;

-- 5. 验证LONG类型PMI的到期时间分布
SELECT '=== LONG类型PMI到期时间分布 ===' as expire_distribution;

SELECT 
    CASE 
        WHEN window_expire_time IS NULL THEN '无到期时间'
        WHEN window_expire_time <= NOW() THEN '已过期'
        WHEN window_expire_time <= DATE_ADD(NOW(), INTERVAL 7 DAY) THEN '7天内到期'
        WHEN window_expire_time <= DATE_ADD(NOW(), INTERVAL 30 DAY) THEN '30天内到期'
        WHEN window_expire_time <= DATE_ADD(NOW(), INTERVAL 90 DAY) THEN '90天内到期'
        ELSE '90天后到期'
    END as expire_category,
    COUNT(*) as count
FROM t_pmi_records
WHERE billing_mode = 'LONG'
GROUP BY expire_category
ORDER BY 
    CASE expire_category
        WHEN '已过期' THEN 1
        WHEN '7天内到期' THEN 2
        WHEN '30天内到期' THEN 3
        WHEN '90天内到期' THEN 4
        WHEN '90天后到期' THEN 5
        WHEN '无到期时间' THEN 6
    END;

-- 6. 验证BY_TIME类型PMI的可用时长分布
SELECT '=== BY_TIME类型PMI可用时长分布 ===' as time_distribution;

SELECT 
    CASE 
        WHEN available_minutes = 0 THEN '已用完'
        WHEN available_minutes <= 60 THEN '1小时内'
        WHEN available_minutes <= 300 THEN '5小时内'
        WHEN available_minutes <= 600 THEN '10小时内'
        WHEN available_minutes <= 1440 THEN '24小时内'
        ELSE '超过24小时'
    END as time_category,
    COUNT(*) as count,
    MIN(available_minutes) as min_minutes,
    MAX(available_minutes) as max_minutes,
    ROUND(AVG(available_minutes), 2) as avg_minutes
FROM t_pmi_records
WHERE billing_mode = 'BY_TIME'
GROUP BY time_category
ORDER BY 
    CASE time_category
        WHEN '已用完' THEN 1
        WHEN '1小时内' THEN 2
        WHEN '5小时内' THEN 3
        WHEN '10小时内' THEN 4
        WHEN '24小时内' THEN 5
        WHEN '超过24小时' THEN 6
    END;

-- 7. 模拟前端API调用的排序结果
SELECT '=== 模拟前端API默认排序结果 ===' as api_simulation;

SELECT 
    p.id,
    p.pmi_number,
    p.billing_mode,
    p.status,
    CASE 
        WHEN p.billing_mode = 'LONG' THEN DATE_FORMAT(p.window_expire_time, '%Y-%m-%d')
        ELSE '-'
    END as expire_date,
    CASE 
        WHEN p.billing_mode = 'BY_TIME' THEN CONCAT(ROUND(p.available_minutes / 60.0, 1), '小时')
        ELSE '-'
    END as available_time,
    DATE_FORMAT(p.created_at, '%Y-%m-%d %H:%i') as created_time
FROM t_pmi_records p
ORDER BY 
    CASE WHEN p.billing_mode = 'LONG' THEN 0 ELSE 1 END,
    CASE WHEN p.billing_mode = 'LONG' THEN p.window_expire_time END ASC,
    CASE WHEN p.billing_mode = 'BY_TIME' THEN p.available_minutes END DESC,
    p.created_at DESC
LIMIT 20;

SELECT '=== 测试完成 ===' as test_completion;
