#!/usr/bin/env python3
"""
测试PMI同步功能的脚本
"""

import requests
import json
import time

def test_user_updated_webhook():
    """测试user.updated webhook事件的PMI同步"""
    
    # 模拟user.updated事件，PMI从**********变更为**********
    test_payload = {
        "event": "user.updated",
        "payload": {
            "account_id": "wBQaPsl8TraGuSogkZWZeQ",
            "operator": "<EMAIL>",
            "operator_id": "adminOperatorId",
            "object": {
                "id": "testUserId123",
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "User",
                "pmi": "**********",  # 新的PMI值
                "type": 1,
                "status": "active"
            },
            "old_object": {
                "id": "testUserId123",
                "email": "<EMAIL>", 
                "first_name": "Test",
                "last_name": "User",
                "pmi": "**********",  # 旧的PMI值
                "type": 1,
                "status": "active"
            },
            "time_stamp": int(time.time() * 1000)
        },
        "event_ts": int(time.time() * 1000)
    }
    
    webhook_url = "http://localhost:8080/api/webhooks/zoom/wBQaPsl8TraGuSogkZWZeQ"
    
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Zoom Marketplace/1.0a"
    }
    
    print(f"🧪 测试user.updated Webhook (PMI变更): {webhook_url}")
    print(f"📤 PMI变更: ********** → **********")
    print(f"📤 发送事件数据: {json.dumps(test_payload, indent=2)}")
    
    try:
        response = requests.post(
            webhook_url,
            json=test_payload,
            headers=headers,
            timeout=10
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ user.updated Webhook (PMI变更) 测试成功！")
            return True
        else:
            print(f"❌ user.updated Webhook测试失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_user_updated_no_pmi_change():
    """测试user.updated事件但PMI没有变化"""
    
    test_payload = {
        "event": "user.updated",
        "payload": {
            "account_id": "wBQaPsl8TraGuSogkZWZeQ",
            "operator": "<EMAIL>",
            "operator_id": "adminOperatorId",
            "object": {
                "id": "testUserId456",
                "email": "<EMAIL>",
                "first_name": "Test2",
                "last_name": "User2",
                "pmi": "**********",  # PMI没有变化
                "type": 1,
                "status": "active"
            },
            "old_object": {
                "id": "testUserId456",
                "email": "<EMAIL>",
                "first_name": "Test2",
                "last_name": "User2", 
                "pmi": "**********",  # PMI没有变化
                "type": 1,
                "status": "active"
            },
            "time_stamp": int(time.time() * 1000)
        },
        "event_ts": int(time.time() * 1000)
    }
    
    webhook_url = "http://localhost:8080/api/webhooks/zoom/wBQaPsl8TraGuSogkZWZeQ"
    
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Zoom Marketplace/1.0a"
    }
    
    print(f"\n🧪 测试user.updated Webhook (PMI无变化): {webhook_url}")
    print(f"📤 PMI保持不变: **********")
    
    try:
        response = requests.post(
            webhook_url,
            json=test_payload,
            headers=headers,
            timeout=10
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ user.updated Webhook (PMI无变化) 测试成功！")
            return True
        else:
            print(f"❌ user.updated Webhook测试失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_user_updated_new_pmi():
    """测试user.updated事件，用户新增PMI"""
    
    test_payload = {
        "event": "user.updated", 
        "payload": {
            "account_id": "wBQaPsl8TraGuSogkZWZeQ",
            "operator": "<EMAIL>",
            "operator_id": "adminOperatorId",
            "object": {
                "id": "testUserId789",
                "email": "<EMAIL>",
                "first_name": "Test3",
                "last_name": "User3",
                "pmi": "**********",  # 新增的PMI
                "type": 1,
                "status": "active"
            },
            "old_object": {
                "id": "testUserId789",
                "email": "<EMAIL>",
                "first_name": "Test3", 
                "last_name": "User3",
                # 没有pmi字段，表示之前没有PMI
                "type": 1,
                "status": "active"
            },
            "time_stamp": int(time.time() * 1000)
        },
        "event_ts": int(time.time() * 1000)
    }
    
    webhook_url = "http://localhost:8080/api/webhooks/zoom/wBQaPsl8TraGuSogkZWZeQ"
    
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Zoom Marketplace/1.0a"
    }
    
    print(f"\n🧪 测试user.updated Webhook (新增PMI): {webhook_url}")
    print(f"📤 新增PMI: null → **********")
    
    try:
        response = requests.post(
            webhook_url,
            json=test_payload,
            headers=headers,
            timeout=10
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ user.updated Webhook (新增PMI) 测试成功！")
            return True
        else:
            print(f"❌ user.updated Webhook测试失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始PMI同步功能测试...")
    
    # 测试PMI变更事件
    success1 = test_user_updated_webhook()
    
    # 测试PMI无变化事件
    success2 = test_user_updated_no_pmi_change()
    
    # 测试新增PMI事件
    success3 = test_user_updated_new_pmi()
    
    if success1 and success2 and success3:
        print("\n🎉 所有PMI同步测试通过！")
    else:
        print("\n❌ 部分PMI同步测试失败")
