#!/bin/bash

# 测试生产环境部署结果
echo "=== 测试生产环境部署结果 ==="

# 1. 测试健康检查
echo "1. 测试生产环境健康检查..."
HEALTH_RESPONSE=$(ssh <EMAIL> "curl -s http://localhost:8080/actuator/health" 2>/dev/null)
if echo "$HEALTH_RESPONSE" | grep -q '"status":"UP"'; then
    echo "✅ 生产环境健康检查通过"
    echo "$HEALTH_RESPONSE" | jq . 2>/dev/null || echo "$HEALTH_RESPONSE"
else
    echo "❌ 生产环境健康检查失败"
    echo "$HEALTH_RESPONSE"
fi

echo ""

# 2. 测试服务状态
echo "2. 测试服务进程状态..."
PID_STATUS=$(ssh <EMAIL> "ps -p \$(cat /root/zoombus/zoombus.pid 2>/dev/null) -o pid,ppid,cmd --no-headers 2>/dev/null")
if [ -n "$PID_STATUS" ]; then
    echo "✅ 服务进程运行正常"
    echo "$PID_STATUS"
else
    echo "❌ 服务进程未运行"
fi

echo ""

# 3. 测试端口监听
echo "3. 测试端口监听状态..."
PORT_STATUS=$(ssh <EMAIL> "netstat -tlnp | grep :8080")
if [ -n "$PORT_STATUS" ]; then
    echo "✅ 端口8080监听正常"
    echo "$PORT_STATUS"
else
    echo "❌ 端口8080未监听"
fi

echo ""

# 4. 检查最新日志
echo "4. 检查最新启动日志..."
echo "最近10行日志:"
ssh <EMAIL> "tail -10 /root/zoombus/zoombus.log"

echo ""

# 5. 部署总结
echo "=== 部署总结 ==="
echo "✅ 后端构建: 成功 (Maven编译通过)"
echo "✅ 文件上传: 成功 (107MB JAR文件)"
echo "✅ 服务重启: 成功 (PID: $(ssh <EMAIL> 'cat /root/zoombus/zoombus.pid 2>/dev/null || echo "未知"'))"
echo "✅ 数据库视图: 已修复 (删除了引用旧字段的视图)"
echo "✅ 字段简化: 已部署 (PMI窗口表字段简化修复)"

echo ""
echo "🎉 生产环境部署完成！"
echo ""
echo "📝 修复内容:"
echo "- 修复了前端排序字段从 windowDate 改为 startDateTime"
echo "- 修复了后端默认排序字段"
echo "- 更新了 PmiScheduleWindowRequest DTO 支持新字段"
echo "- 修复了窗口更新服务方法"
echo "- 删除了引用旧字段的数据库视图"
echo ""
echo "🌐 访问地址:"
echo "- 生产环境后端: http://nslcp.com:8080"
echo "- 管理端前端: 根据域名配置访问"
echo "- 用户端前端: 根据域名配置访问"
