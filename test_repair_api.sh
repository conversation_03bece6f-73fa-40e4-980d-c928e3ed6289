#!/bin/bash

# PMI窗口任务修复API测试脚本

BASE_URL="http://localhost:8080/api/pmi-window-task-repair"

echo "=== PMI窗口任务修复测试 ==="
echo "时间: $(date)"
echo

# 1. 检查需要修复的窗口数量
echo "1. 检查需要修复的窗口数量..."
curl -s -X GET "$BASE_URL/check" | jq '.'
echo

# 2. 批量修复所有窗口
echo "2. 批量修复所有没有任务的窗口..."
curl -s -X POST "$BASE_URL/repair-all" | jq '.'
echo

# 3. 再次检查修复结果
echo "3. 再次检查修复结果..."
curl -s -X GET "$BASE_URL/check" | jq '.'
echo

# 4. 测试修复单个窗口（窗口2084）
echo "4. 测试修复单个窗口（窗口2084）..."
curl -s -X POST "$BASE_URL/repair/2084" | jq '.'
echo

echo "=== 测试完成 ==="
