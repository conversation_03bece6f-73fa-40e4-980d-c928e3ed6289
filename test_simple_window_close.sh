#!/bin/bash

# 简单测试：查看现有窗口的close task取消功能

BASE_URL="http://localhost:8080"

echo "=== 简单测试：窗口关闭时取消close task功能 ==="
echo "时间: $(date)"
echo

# 1. 查询所有待执行的任务
echo "1. 查询所有待执行的任务..."
TASKS_RESPONSE=$(curl -s "$BASE_URL/api/pmi-scheduled-tasks?status=SCHEDULED")
echo "待执行任务:"
echo "$TASKS_RESPONSE" | jq '.data.content[] | {id, taskType, pmiWindowId, scheduledTime, status}'

# 找到一个CLOSE类型的任务
CLOSE_TASK=$(echo "$TASKS_RESPONSE" | jq -r '.data.content[] | select(.taskType == "PMI_WINDOW_CLOSE") | .id' | head -1)

if [ -z "$CLOSE_TASK" ] || [ "$CLOSE_TASK" = "null" ]; then
    echo "❌ 没有找到待执行的CLOSE任务，无法测试"
    exit 1
fi

echo "✅ 找到CLOSE任务ID: $CLOSE_TASK"

# 获取对应的窗口ID
WINDOW_ID=$(echo "$TASKS_RESPONSE" | jq -r ".data.content[] | select(.id == $CLOSE_TASK) | .pmiWindowId")
echo "✅ 对应的窗口ID: $WINDOW_ID"
echo

# 2. 查询窗口状态
echo "2. 查询窗口状态..."
WINDOW_RESPONSE=$(curl -s "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID")
echo "窗口详情:"
echo "$WINDOW_RESPONSE" | jq '.data | {id, status, closeTaskId}'

WINDOW_STATUS=$(echo "$WINDOW_RESPONSE" | jq -r '.data.status')
echo "窗口状态: $WINDOW_STATUS"

# 如果窗口不是ACTIVE状态，先激活它
if [ "$WINDOW_STATUS" = "PENDING" ]; then
    echo "3. 激活窗口..."
    ACTIVATE_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID/activate")
    echo "激活响应:"
    echo "$ACTIVATE_RESPONSE" | jq '.'
    
    if [ "$(echo "$ACTIVATE_RESPONSE" | jq -r '.success')" != "true" ]; then
        echo "❌ 窗口激活失败"
        exit 1
    fi
    echo "✅ 窗口激活成功"
elif [ "$WINDOW_STATUS" != "ACTIVE" ]; then
    echo "❌ 窗口状态为 $WINDOW_STATUS，无法测试关闭功能"
    exit 1
fi

echo

# 4. 人工关闭窗口
echo "4. 人工关闭窗口..."
CLOSE_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID/close")
echo "关闭响应:"
echo "$CLOSE_RESPONSE" | jq '.'

if [ "$(echo "$CLOSE_RESPONSE" | jq -r '.success')" = "true" ]; then
    echo "✅ 窗口关闭成功"
else
    echo "❌ 窗口关闭失败"
    echo "$CLOSE_RESPONSE" | jq '.message'
    exit 1
fi
echo

# 5. 验证close task是否被取消
echo "5. 验证close task是否被取消..."
sleep 2

TASK_RESPONSE_AFTER=$(curl -s "$BASE_URL/api/pmi-scheduled-tasks/$CLOSE_TASK")
echo "关闭后任务详情:"
echo "$TASK_RESPONSE_AFTER" | jq '.data | {id, taskType, status, errorMessage}'

TASK_STATUS_AFTER=$(echo "$TASK_RESPONSE_AFTER" | jq -r '.data.status')
echo "关闭后任务状态: $TASK_STATUS_AFTER"

if [ "$TASK_STATUS_AFTER" = "CANCELLED" ]; then
    echo "✅ 测试成功：close task已被取消"
    echo "🎉 功能验证通过：人工关闭窗口时成功联动取消了close task"
else
    echo "❌ 测试失败：close task状态为 $TASK_STATUS_AFTER，期望为 CANCELLED"
    echo "可能的原因："
    echo "  1. 代码修改未生效，需要重启应用"
    echo "  2. 任务取消逻辑有问题"
    echo "  3. 窗口没有正确关联close task"
fi

echo
echo "=== 测试完成 ==="
