<!DOCTYPE html>
<html>
<head>
    <title>测试任务30调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
        }
        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
        }
    </style>
</head>
<body>
    <h1>任务30详情调试</h1>
    
    <div class="test-section">
        <h2>1. 直接API测试</h2>
        <p>直接调用任务30的API</p>
        <button class="test-button" onclick="testTask30Api()">测试任务30 API</button>
        <button class="test-button" onclick="testTask29Api()">测试任务29 API (对比)</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 权限检查</h2>
        <p>检查当前用户权限和token状态</p>
        <button class="test-button" onclick="checkPermissions()">检查权限</button>
        <div id="permission-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 任务列表检查</h2>
        <p>查看任务列表中是否存在任务30</p>
        <button class="test-button" onclick="checkTaskList()">检查任务列表</button>
        <div id="task-list-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 模拟弹窗调用</h2>
        <p>模拟前端弹窗的调用过程</p>
        <button class="test-button" onclick="simulateModalCall()">模拟弹窗调用</button>
        <div id="modal-result" class="result"></div>
    </div>

    <script>
        async function testTask30Api() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = '正在测试任务30 API...';
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/pmi-scheduled-tasks/30', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const responseText = await response.text();
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        resultDiv.className = 'result success';
                        resultDiv.textContent = `✅ 任务30 API调用成功\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                    } catch (parseError) {
                        resultDiv.className = 'result warning';
                        resultDiv.textContent = `⚠️ 响应成功但JSON解析失败\n状态码: ${response.status}\n原始响应:\n${responseText}`;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 任务30 API调用失败\n状态码: ${response.status}\n错误信息: ${response.statusText}\n响应内容:\n${responseText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }
        
        async function testTask29Api() {
            const resultDiv = document.getElementById('api-result');
            const currentContent = resultDiv.textContent;
            resultDiv.textContent = currentContent + '\n\n正在测试任务29 API (对比)...';
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/pmi-scheduled-tasks/29', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent = currentContent + '\n\n✅ 任务29 API调用成功 (对比)\n状态码: ' + response.status + '\n任务状态: ' + data.data.status;
                } else {
                    resultDiv.textContent = currentContent + '\n\n❌ 任务29 API调用失败\n状态码: ' + response.status;
                }
            } catch (error) {
                resultDiv.textContent = currentContent + '\n\n❌ 任务29请求失败: ' + error.message;
            }
        }
        
        async function checkPermissions() {
            const resultDiv = document.getElementById('permission-result');
            resultDiv.textContent = '正在检查权限...';
            
            try {
                const userInfo = localStorage.getItem('userInfo');
                const token = localStorage.getItem('token');
                
                let result = '权限检查结果:\n\n';
                result += `Token存在: ${!!token}\n`;
                
                if (userInfo) {
                    const user = JSON.parse(userInfo);
                    result += `用户名: ${user.username || '未知'}\n`;
                    result += `角色: ${user.roles || '未知'}\n`;
                    result += `权限: ${JSON.stringify(user.authorities || [], null, 2)}\n`;
                } else {
                    result += '用户信息: 不存在\n';
                }
                
                // 测试一个简单的API调用来验证token有效性
                const testResponse = await fetch('/api/pmi-scheduled-tasks?page=0&size=1', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                result += `\nAPI访问测试: ${testResponse.status} ${testResponse.statusText}`;
                
                if (testResponse.ok) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `权限检查失败: ${error.message}`;
            }
        }
        
        async function checkTaskList() {
            const resultDiv = document.getElementById('task-list-result');
            resultDiv.textContent = '正在检查任务列表...';
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/pmi-scheduled-tasks?page=0&size=50', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const tasks = data.data.content || [];
                    
                    let result = `任务列表检查结果:\n\n`;
                    result += `总任务数: ${tasks.length}\n`;
                    
                    const task30 = tasks.find(task => task.id === 30);
                    const task29 = tasks.find(task => task.id === 29);
                    
                    result += `任务29存在: ${!!task29}\n`;
                    result += `任务30存在: ${!!task30}\n\n`;
                    
                    if (task30) {
                        result += `任务30详情:\n`;
                        result += `- ID: ${task30.id}\n`;
                        result += `- 类型: ${task30.taskType}\n`;
                        result += `- 状态: ${task30.status}\n`;
                        result += `- PMI窗口ID: ${task30.pmiWindowId}\n`;
                    }
                    
                    result += `\n最近的任务ID列表: ${tasks.map(t => t.id).slice(0, 10).join(', ')}`;
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = result;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取任务列表失败: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `检查任务列表失败: ${error.message}`;
            }
        }
        
        async function simulateModalCall() {
            const resultDiv = document.getElementById('modal-result');
            resultDiv.textContent = '正在模拟弹窗调用...';
            
            try {
                // 模拟前端的调用过程
                console.log('开始获取任务详情, taskId:', 30);
                
                const token = localStorage.getItem('token');
                const response = await fetch('/api/pmi-scheduled-tasks/30', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('API响应:', response);
                
                let result = '模拟弹窗调用结果:\n\n';
                result += `1. 请求发送: ✅\n`;
                result += `2. 响应状态: ${response.status} ${response.statusText}\n`;
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('任务详情数据:', data);
                    
                    result += `3. 数据解析: ✅\n`;
                    result += `4. 数据结构检查:\n`;
                    result += `   - success: ${data.success}\n`;
                    result += `   - data存在: ${!!data.data}\n`;
                    
                    if (data.data) {
                        result += `   - 任务ID: ${data.data.id}\n`;
                        result += `   - 任务状态: ${data.data.status}\n`;
                        result += `   - 状态描述: ${data.data.statusDescription}\n`;
                        result += `   - 任务类型: ${data.data.taskType}\n`;
                        result += `   - 类型描述: ${data.data.taskTypeDescription}\n`;
                        
                        result += `\n5. 弹窗应该显示: ✅ 正常\n`;
                        resultDiv.className = 'result success';
                    } else {
                        result += `\n5. 弹窗显示: ❌ 数据为空\n`;
                        resultDiv.className = 'result error';
                    }
                } else {
                    result += `3. 请求失败: ${response.status}\n`;
                    result += `4. 弹窗应该显示: 错误信息\n`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                console.error('模拟调用失败:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `模拟调用失败: ${error.message}`;
            }
        }
        
        // 页面加载时显示基本信息
        window.onload = function() {
            console.log('页面加载完成，开始调试任务30');
            
            const userInfo = localStorage.getItem('userInfo');
            const token = localStorage.getItem('token');
            
            console.log('用户信息:', userInfo ? JSON.parse(userInfo) : '无');
            console.log('Token存在:', !!token);
        };
    </script>
</body>
</html>
