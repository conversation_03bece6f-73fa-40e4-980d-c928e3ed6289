<!DOCTYPE html>
<html>
<head>
    <title>测试任务详情弹窗</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
        }
    </style>
</head>
<body>
    <h1>任务详情弹窗测试</h1>
    
    <div class="test-section">
        <h2>1. 测试API调用</h2>
        <p>测试直接调用任务详情API</p>
        <button class="test-button" onclick="testApiCall()">测试API调用</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试页面跳转</h2>
        <p>测试跳转到任务详情页面和PMI计划管理页面</p>
        <button class="test-button" onclick="openTaskDetail()">打开任务详情页面</button>
        <button class="test-button" onclick="openScheduleManagement()">打开PMI计划管理页面</button>
    </div>
    
    <div class="test-section">
        <h2>3. 测试数据结构</h2>
        <p>验证API返回的数据结构</p>
        <button class="test-button" onclick="analyzeDataStructure()">分析数据结构</button>
        <div id="data-result" class="result"></div>
    </div>

    <script>
        async function testApiCall() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = '正在测试API调用...';
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/pmi-scheduled-tasks/29', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ API调用成功\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ API调用失败\n状态码: ${response.status}\n错误信息: ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }
        
        function openTaskDetail() {
            window.open('/pmi-task-management?taskId=29', '_blank');
        }
        
        function openScheduleManagement() {
            window.open('/pmi-schedule-management/320', '_blank');
        }
        
        async function analyzeDataStructure() {
            const resultDiv = document.getElementById('data-result');
            resultDiv.textContent = '正在分析数据结构...';
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/pmi-scheduled-tasks/29', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    const data = result.data;
                    
                    let analysis = '✅ 数据结构分析:\n\n';
                    analysis += `基本信息:\n`;
                    analysis += `- ID: ${data.id}\n`;
                    analysis += `- 任务类型: ${data.taskType} (${data.taskTypeDescription})\n`;
                    analysis += `- 任务状态: ${data.status} (${data.statusDescription})\n`;
                    analysis += `- PMI窗口ID: ${data.pmiWindowId}\n`;
                    analysis += `- PMI号码: ${data.pmiNumber}\n\n`;
                    
                    analysis += `时间信息:\n`;
                    analysis += `- 计划执行时间: ${data.scheduledTime}\n`;
                    analysis += `- 实际执行时间: ${data.actualExecutionTime}\n`;
                    analysis += `- 创建时间: ${data.createdAt}\n`;
                    analysis += `- 更新时间: ${data.updatedAt}\n\n`;
                    
                    analysis += `其他信息:\n`;
                    analysis += `- 重试次数: ${data.retryCount}\n`;
                    analysis += `- 延迟分钟: ${data.delayMinutes}\n`;
                    analysis += `- 是否延迟: ${data.delayed}\n`;
                    analysis += `- 延迟描述: ${data.delayDescription}\n`;
                    analysis += `- 可取消: ${data.canCancel}\n`;
                    analysis += `- 可重新调度: ${data.canReschedule}\n`;
                    
                    if (data.errorMessage) {
                        analysis += `\n错误信息: ${data.errorMessage}`;
                    }
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = analysis;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 无法获取数据进行分析\n状态码: ${response.status}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 分析失败: ${error.message}`;
            }
        }
        
        // 页面加载时显示当前用户信息
        window.onload = function() {
            const userInfo = localStorage.getItem('userInfo');
            const token = localStorage.getItem('token');
            
            if (userInfo && token) {
                const user = JSON.parse(userInfo);
                console.log('当前用户:', user);
                console.log('Token存在:', !!token);
            } else {
                console.log('用户未登录或token不存在');
            }
        };
    </script>
</body>
</html>
