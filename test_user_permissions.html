<!DOCTYPE html>
<html>
<head>
    <title>测试用户权限</title>
</head>
<body>
    <h1>测试用户权限</h1>
    <div id="result"></div>

    <script>
        async function testUserPermissions() {
            const resultDiv = document.getElementById('result');
            
            try {
                // 获取当前用户信息
                const userInfo = localStorage.getItem('userInfo');
                const token = localStorage.getItem('token');
                
                resultDiv.innerHTML += '<h2>本地存储信息:</h2>';
                resultDiv.innerHTML += '<p><strong>Token:</strong> ' + (token ? '存在' : '不存在') + '</p>';
                resultDiv.innerHTML += '<p><strong>用户信息:</strong> ' + (userInfo || '不存在') + '</p>';
                
                if (userInfo) {
                    const user = JSON.parse(userInfo);
                    resultDiv.innerHTML += '<p><strong>用户角色:</strong> ' + (user.roles || '未知') + '</p>';
                }
                
                // 测试API调用
                resultDiv.innerHTML += '<h2>API测试:</h2>';
                
                // 测试获取任务列表
                try {
                    const response = await fetch('/api/pmi-scheduled-tasks?page=1&size=1', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    resultDiv.innerHTML += '<p><strong>任务列表API:</strong> ' + response.status + ' ' + response.statusText + '</p>';
                    
                    if (response.ok) {
                        const data = await response.json();
                        resultDiv.innerHTML += '<p>响应数据: ' + JSON.stringify(data, null, 2) + '</p>';
                    }
                } catch (error) {
                    resultDiv.innerHTML += '<p><strong>任务列表API错误:</strong> ' + error.message + '</p>';
                }
                
                // 测试获取任务详情
                try {
                    const response = await fetch('/api/pmi-scheduled-tasks/29', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    resultDiv.innerHTML += '<p><strong>任务详情API:</strong> ' + response.status + ' ' + response.statusText + '</p>';
                    
                    if (response.ok) {
                        const data = await response.json();
                        resultDiv.innerHTML += '<p>任务详情: ' + JSON.stringify(data, null, 2) + '</p>';
                    } else {
                        const errorText = await response.text();
                        resultDiv.innerHTML += '<p>错误响应: ' + errorText + '</p>';
                    }
                } catch (error) {
                    resultDiv.innerHTML += '<p><strong>任务详情API错误:</strong> ' + error.message + '</p>';
                }
                
            } catch (error) {
                resultDiv.innerHTML += '<p><strong>测试错误:</strong> ' + error.message + '</p>';
            }
        }
        
        // 页面加载后执行测试
        window.onload = testUserPermissions;
    </script>
</body>
</html>
