#!/usr/bin/env python3
"""
测试Webhook功能的脚本
"""

import requests
import json
import time

def test_webhook_endpoint():
    """测试webhook端点是否正常工作"""
    
    # 测试数据 - 模拟Zoom webhook事件
    test_payload = {
        "event": "user.updated",
        "payload": {
            "account_id": "wBQaPsl8TraGuSogkZWZeQ",
            "operator": "<EMAIL>",
            "operator_id": "testOperatorId",
            "object": {
                "id": "testUserId",
                "pmi": **********
            },
            "old_object": {
                "id": "testUserId", 
                "pmi": **********
            },
            "time_stamp": int(time.time() * 1000)
        },
        "event_ts": int(time.time() * 1000)
    }
    
    # 测试URL
    webhook_url = "http://localhost:8080/api/webhooks/zoom/wBQaPsl8TraGuSogkZWZeQ"
    
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Zoom Marketplace/1.0a"
    }
    
    print(f"🧪 测试Webhook端点: {webhook_url}")
    print(f"📤 发送测试数据: {json.dumps(test_payload, indent=2)}")
    
    try:
        response = requests.post(
            webhook_url,
            json=test_payload,
            headers=headers,
            timeout=10
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        print(f"📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ Webhook测试成功！")
            return True
        else:
            print(f"❌ Webhook测试失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_meeting_ended_webhook():
    """测试会议结束webhook事件"""
    
    test_payload = {
        "event": "meeting.ended",
        "payload": {
            "account_id": "wBQaPsl8TraGuSogkZWZeQ",
            "object": {
                "duration": 60,
                "start_time": "2025-08-27T05:00:00Z",
                "timezone": "Asia/Shanghai",
                "end_time": "2025-08-27T06:00:00Z",
                "topic": "测试会议",
                "id": "**********",
                "type": 4,
                "uuid": "test-meeting-uuid-123",
                "host_id": "testHostId123"
            }
        },
        "event_ts": int(time.time() * 1000)
    }
    
    webhook_url = "http://localhost:8080/api/webhooks/zoom/wBQaPsl8TraGuSogkZWZeQ"
    
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Zoom Marketplace/1.0a"
    }
    
    print(f"\n🧪 测试会议结束Webhook: {webhook_url}")
    print(f"📤 发送会议结束事件: {json.dumps(test_payload, indent=2)}")
    
    try:
        response = requests.post(
            webhook_url,
            json=test_payload,
            headers=headers,
            timeout=10
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 会议结束Webhook测试成功！")
            return True
        else:
            print(f"❌ 会议结束Webhook测试失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始Webhook功能测试...")
    
    # 测试基本webhook功能
    success1 = test_webhook_endpoint()
    
    # 测试会议结束事件
    success2 = test_meeting_ended_webhook()
    
    if success1 and success2:
        print("\n🎉 所有Webhook测试通过！")
    else:
        print("\n❌ 部分Webhook测试失败")
