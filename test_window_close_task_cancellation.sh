#!/bin/bash

# 测试窗口关闭时联动取消close task功能

BASE_URL="http://localhost:8080"

echo "=== 测试窗口关闭时联动取消close task功能 ==="
echo "时间: $(date)"
echo

# 1. 创建一个新的PMI计划来测试
echo "1. 创建测试PMI计划..."
CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/pmi-schedules" \
  -H "Content-Type: application/json" \
  -d '{
    "pmiRecordId": 320,
    "name": "测试窗口关闭任务取消",
    "description": "测试人工关闭窗口时取消close task",
    "startDate": "'$(date -v+1M +%Y-%m-%d)'",
    "endDate": "'$(date -v+1M +%Y-%m-%d)'",
    "startTime": "'$(date -v+5M +%H:%M)'",
    "endTime": "'$(date -v+65M +%H:%M)'",
    "recurrenceType": "DAILY",
    "daysOfWeek": []
  }')

echo "创建计划响应:"
echo "$CREATE_RESPONSE" | jq '.'

# 提取窗口ID
WINDOW_ID=$(echo "$CREATE_RESPONSE" | jq -r '.data.windows[0].id // empty')

if [ -z "$WINDOW_ID" ] || [ "$WINDOW_ID" = "null" ]; then
    echo "❌ 无法获取窗口ID，测试失败"
    exit 1
fi

echo "✅ 获取到窗口ID: $WINDOW_ID"
echo

# 2. 等待任务创建完成
echo "2. 等待任务创建完成..."
sleep 3

# 3. 查询窗口详情，获取close task ID
echo "3. 查询窗口详情..."
WINDOW_RESPONSE=$(curl -s "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID")
echo "窗口详情:"
echo "$WINDOW_RESPONSE" | jq '.'

CLOSE_TASK_ID=$(echo "$WINDOW_RESPONSE" | jq -r '.data.closeTaskId // empty')

if [ -z "$CLOSE_TASK_ID" ] || [ "$CLOSE_TASK_ID" = "null" ]; then
    echo "❌ 窗口没有关联的close task，跳过测试"
    exit 1
fi

echo "✅ 获取到close task ID: $CLOSE_TASK_ID"
echo

# 4. 查询close task状态
echo "4. 查询close task状态..."
TASK_RESPONSE=$(curl -s "$BASE_URL/api/pmi-scheduled-tasks/$CLOSE_TASK_ID")
echo "任务详情:"
echo "$TASK_RESPONSE" | jq '.'

TASK_STATUS=$(echo "$TASK_RESPONSE" | jq -r '.data.status // empty')
echo "任务状态: $TASK_STATUS"

if [ "$TASK_STATUS" != "SCHEDULED" ]; then
    echo "❌ 任务状态不是SCHEDULED，无法测试取消功能"
    exit 1
fi

echo "✅ 任务状态正常，可以进行测试"
echo

# 5. 手动激活窗口（模拟窗口开启）
echo "5. 手动激活窗口..."
ACTIVATE_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID/activate")
echo "激活响应:"
echo "$ACTIVATE_RESPONSE" | jq '.'
echo

# 6. 人工关闭窗口
echo "6. 人工关闭窗口..."
CLOSE_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID/close")
echo "关闭响应:"
echo "$CLOSE_RESPONSE" | jq '.'

if [ "$(echo "$CLOSE_RESPONSE" | jq -r '.success')" = "true" ]; then
    echo "✅ 窗口关闭成功"
else
    echo "❌ 窗口关闭失败"
    exit 1
fi
echo

# 7. 再次查询close task状态，验证是否被取消
echo "7. 验证close task是否被取消..."
sleep 2

TASK_RESPONSE_AFTER=$(curl -s "$BASE_URL/api/pmi-scheduled-tasks/$CLOSE_TASK_ID")
echo "关闭后任务详情:"
echo "$TASK_RESPONSE_AFTER" | jq '.'

TASK_STATUS_AFTER=$(echo "$TASK_RESPONSE_AFTER" | jq -r '.data.status // empty')
echo "关闭后任务状态: $TASK_STATUS_AFTER"

if [ "$TASK_STATUS_AFTER" = "CANCELLED" ]; then
    echo "✅ 测试成功：close task已被取消"
    echo "🎉 功能验证通过：人工关闭窗口时成功联动取消了close task"
else
    echo "❌ 测试失败：close task状态为 $TASK_STATUS_AFTER，期望为 CANCELLED"
    echo "可能的原因："
    echo "  1. 代码修改未生效"
    echo "  2. 任务取消逻辑有问题"
    echo "  3. 窗口没有正确关联close task"
fi

echo
echo "=== 测试完成 ==="
