-- 测试窗口任务流程修复效果的SQL脚本

-- 1. 检查窗口2083的当前状态
SELECT '=== 检查窗口2083当前状态 ===' as step;
SELECT 
    psw.id,
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    psw.open_task_id,
    psw.close_task_id,
    psw.created_at
FROM t_pmi_schedule_windows psw
WHERE psw.id = 2083;

-- 2. 检查窗口2083相关的任务
SELECT '=== 检查窗口2083相关任务 ===' as step;
SELECT 
    pswt.id,
    pswt.task_key,
    pswt.pmi_window_id,
    pswt.task_type,
    pswt.scheduled_time,
    pswt.status,
    pswt.created_at
FROM t_pmi_schedule_window_tasks pswt
WHERE pswt.pmi_window_id = 2083
ORDER BY pswt.task_type;

-- 3. 检查最近创建的窗口和任务关联情况
SELECT '=== 检查最近窗口任务关联情况 ===' as step;
SELECT 
    psw.id as window_id,
    psw.schedule_id,
    psw.open_task_id,
    psw.close_task_id,
    open_task.id as actual_open_task_id,
    open_task.task_type as open_task_type,
    open_task.status as open_task_status,
    close_task.id as actual_close_task_id,
    close_task.task_type as close_task_type,
    close_task.status as close_task_status,
    CASE 
        WHEN psw.open_task_id IS NULL AND psw.close_task_id IS NULL THEN 'NO_TASKS'
        WHEN psw.open_task_id = open_task.id AND psw.close_task_id = close_task.id THEN 'CORRECT'
        WHEN psw.open_task_id != open_task.id OR psw.close_task_id != close_task.id THEN 'MISMATCH'
        ELSE 'PARTIAL'
    END as validation_result,
    psw.created_at as window_created_at
FROM t_pmi_schedule_windows psw
LEFT JOIN t_pmi_schedule_window_tasks open_task 
    ON psw.open_task_id = open_task.id 
    AND open_task.task_type = 'PMI_WINDOW_OPEN'
LEFT JOIN t_pmi_schedule_window_tasks close_task 
    ON psw.close_task_id = close_task.id 
    AND close_task.task_type = 'PMI_WINDOW_CLOSE'
WHERE psw.id >= 2080
ORDER BY psw.id;

-- 4. 统计窗口任务关联状态
SELECT '=== 窗口任务关联统计 ===' as step;
SELECT 
    validation_result,
    COUNT(*) as count,
    GROUP_CONCAT(window_id ORDER BY window_id) as window_ids
FROM (
    SELECT 
        psw.id as window_id,
        CASE 
            WHEN psw.open_task_id IS NULL AND psw.close_task_id IS NULL THEN 'NO_TASKS'
            WHEN psw.open_task_id = open_task.id AND psw.close_task_id = close_task.id THEN 'CORRECT'
            WHEN psw.open_task_id != open_task.id OR psw.close_task_id != close_task.id THEN 'MISMATCH'
            ELSE 'PARTIAL'
        END as validation_result
    FROM t_pmi_schedule_windows psw
    LEFT JOIN t_pmi_schedule_window_tasks open_task 
        ON psw.open_task_id = open_task.id 
        AND open_task.task_type = 'PMI_WINDOW_OPEN'
    LEFT JOIN t_pmi_schedule_window_tasks close_task 
        ON psw.close_task_id = close_task.id 
        AND close_task.task_type = 'PMI_WINDOW_CLOSE'
    WHERE psw.status IN ('PENDING', 'ACTIVE')
) validation_summary
GROUP BY validation_result
ORDER BY 
    CASE validation_result 
        WHEN 'CORRECT' THEN 1 
        WHEN 'PARTIAL' THEN 2 
        WHEN 'MISMATCH' THEN 3 
        WHEN 'NO_TASKS' THEN 4 
    END;

-- 5. 检查孤立的任务（没有对应窗口的任务）
SELECT '=== 检查孤立任务 ===' as step;
SELECT 
    pswt.id,
    pswt.task_key,
    pswt.pmi_window_id,
    pswt.task_type,
    pswt.status,
    pswt.created_at,
    'ORPHANED_TASK' as issue
FROM t_pmi_schedule_window_tasks pswt
LEFT JOIN t_pmi_schedule_windows psw ON pswt.pmi_window_id = psw.id
WHERE psw.id IS NULL
ORDER BY pswt.created_at DESC
LIMIT 10;

-- 6. 检查任务创建时间和窗口创建时间的关系
SELECT '=== 检查任务创建时序 ===' as step;
SELECT 
    psw.id as window_id,
    psw.created_at as window_created_at,
    open_task.id as open_task_id,
    open_task.created_at as open_task_created_at,
    close_task.id as close_task_id,
    close_task.created_at as close_task_created_at,
    TIMESTAMPDIFF(MICROSECOND, psw.created_at, open_task.created_at) / 1000 as open_task_delay_ms,
    TIMESTAMPDIFF(MICROSECOND, psw.created_at, close_task.created_at) / 1000 as close_task_delay_ms
FROM t_pmi_schedule_windows psw
LEFT JOIN t_pmi_schedule_window_tasks open_task 
    ON psw.open_task_id = open_task.id 
    AND open_task.task_type = 'PMI_WINDOW_OPEN'
LEFT JOIN t_pmi_schedule_window_tasks close_task 
    ON psw.close_task_id = close_task.id 
    AND close_task.task_type = 'PMI_WINDOW_CLOSE'
WHERE psw.id >= 2080
  AND (open_task.id IS NOT NULL OR close_task.id IS NOT NULL)
ORDER BY psw.id;

-- 7. 检查任务状态分布
SELECT '=== 任务状态分布 ===' as step;
SELECT 
    task_type,
    status,
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM t_pmi_schedule_window_tasks
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY task_type, status
ORDER BY task_type, status;

-- 8. 检查需要修复的窗口（没有任务的窗口）
SELECT '=== 需要修复的窗口 ===' as step;
SELECT 
    psw.id,
    psw.schedule_id,
    psw.pmi_record_id,
    psw.start_date_time,
    psw.end_date_time,
    psw.status,
    psw.created_at,
    'NEEDS_TASK_CREATION' as action_needed
FROM t_pmi_schedule_windows psw
WHERE psw.status IN ('PENDING', 'ACTIVE')
  AND (psw.open_task_id IS NULL OR psw.close_task_id IS NULL)
ORDER BY psw.created_at DESC;

-- 9. 生成修复建议
SELECT '=== 修复建议 ===' as step;
SELECT 
    CASE 
        WHEN no_tasks_count > 0 THEN CONCAT('发现 ', no_tasks_count, ' 个窗口没有任务，需要手动创建任务')
        WHEN mismatch_count > 0 THEN CONCAT('发现 ', mismatch_count, ' 个窗口任务ID不匹配，需要检查数据一致性')
        ELSE '所有窗口任务关联正常'
    END as recommendation
FROM (
    SELECT 
        SUM(CASE WHEN validation_result = 'NO_TASKS' THEN 1 ELSE 0 END) as no_tasks_count,
        SUM(CASE WHEN validation_result = 'MISMATCH' THEN 1 ELSE 0 END) as mismatch_count
    FROM (
        SELECT 
            CASE 
                WHEN psw.open_task_id IS NULL AND psw.close_task_id IS NULL THEN 'NO_TASKS'
                WHEN psw.open_task_id = open_task.id AND psw.close_task_id = close_task.id THEN 'CORRECT'
                WHEN psw.open_task_id != open_task.id OR psw.close_task_id != close_task.id THEN 'MISMATCH'
                ELSE 'PARTIAL'
            END as validation_result
        FROM t_pmi_schedule_windows psw
        LEFT JOIN t_pmi_schedule_window_tasks open_task 
            ON psw.open_task_id = open_task.id 
            AND open_task.task_type = 'PMI_WINDOW_OPEN'
        LEFT JOIN t_pmi_schedule_window_tasks close_task 
            ON psw.close_task_id = close_task.id 
            AND close_task.task_type = 'PMI_WINDOW_CLOSE'
        WHERE psw.status IN ('PENDING', 'ACTIVE')
    ) validation_data
) summary;
