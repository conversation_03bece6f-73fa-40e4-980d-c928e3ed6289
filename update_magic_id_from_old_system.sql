-- 从老系统更新现有记录的magic_id
-- 执行时间：2025-08-14

USE zoombusV;

-- 更新现有记录的magic_id，从old_t_zoom_pmi表获取mg_id
UPDATE t_pmi_records pmi
JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
SET pmi.magic_id = COALESCE(old_pmi.mg_id, old_pmi.pmi)
WHERE old_pmi.mg_id IS NOT NULL AND old_pmi.mg_id != '';

-- 验证更新结果
SELECT 
    COUNT(*) as total_updated,
    COUNT(CASE WHEN pmi.magic_id != pmi.pmi_number THEN 1 END) as different_magic_ids
FROM t_pmi_records pmi
JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
WHERE old_pmi.mg_id IS NOT NULL AND old_pmi.mg_id != '';

-- 显示一些更新的示例
SELECT 
    pmi.id,
    pmi.pmi_number,
    pmi.magic_id,
    old_pmi.mg_id as old_mg_id
FROM t_pmi_records pmi
JOIN old_t_zoom_pmi old_pmi ON pmi.pmi_number = old_pmi.pmi
WHERE old_pmi.mg_id IS NOT NULL 
  AND old_pmi.mg_id != ''
  AND pmi.magic_id != pmi.pmi_number
LIMIT 10;
