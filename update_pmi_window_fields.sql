-- 补全 t_pmi_records 的 current_window_id 和 window_expire_time 字段
-- 2025-08-18

-- 1. 为计费模式为 LONG 且有活跃窗口的 PMI 记录更新字段
UPDATE t_pmi_records p
JOIN (
    -- 查找每个PMI记录的当前活跃窗口信息
    SELECT 
        w.pmi_record_id,
        w.id as current_window_id,
        -- 计算窗口到期时间：如果有 end_date，使用 end_date + end_time，否则使用 window_date + end_time
        CASE 
            WHEN w.end_date IS NOT NULL THEN 
                TIMESTAMP(w.end_date, w.end_time)
            ELSE 
                TIMESTAMP(w.window_date, w.end_time)
        END as window_expire_time,
        -- 用于排序，优先选择最新激活的窗口
        w.actual_start_time,
        w.created_at
    FROM t_pmi_schedule_windows w
    WHERE w.status = 'ACTIVE'
    ORDER BY w.pmi_record_id, w.actual_start_time DESC, w.created_at DESC
) active_windows ON p.id = active_windows.pmi_record_id
SET 
    p.current_window_id = active_windows.current_window_id,
    p.window_expire_time = active_windows.window_expire_time,
    p.updated_at = NOW()
WHERE p.billing_mode = 'LONG'
AND (p.current_window_id IS NULL OR p.window_expire_time IS NULL);

-- 2. 为没有活跃窗口但计费模式为 LONG 的 PMI 记录清空字段
UPDATE t_pmi_records p
SET 
    p.current_window_id = NULL,
    p.window_expire_time = NULL,
    p.updated_at = NOW()
WHERE p.billing_mode = 'LONG'
AND p.id NOT IN (
    SELECT DISTINCT w.pmi_record_id 
    FROM t_pmi_schedule_windows w 
    WHERE w.status = 'ACTIVE'
);

-- 3. 为计费模式不是 LONG 的 PMI 记录清空这些字段
UPDATE t_pmi_records p
SET 
    p.current_window_id = NULL,
    p.window_expire_time = NULL,
    p.updated_at = NOW()
WHERE p.billing_mode != 'LONG'
AND (p.current_window_id IS NOT NULL OR p.window_expire_time IS NOT NULL);

-- 4. 查看更新结果统计
SELECT 
    '=== 更新结果统计 ===' as info,
    '' as details
UNION ALL
SELECT 
    '计费模式为LONG且有活跃窗口的PMI数量',
    CAST(COUNT(*) AS CHAR)
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND p.current_window_id IS NOT NULL
AND p.window_expire_time IS NOT NULL
UNION ALL
SELECT 
    '计费模式为LONG但无活跃窗口的PMI数量',
    CAST(COUNT(*) AS CHAR)
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND (p.current_window_id IS NULL OR p.window_expire_time IS NULL)
UNION ALL
SELECT 
    '计费模式不是LONG的PMI数量',
    CAST(COUNT(*) AS CHAR)
FROM t_pmi_records p
WHERE p.billing_mode != 'LONG';

-- 5. 显示一些示例数据
SELECT 
    '=== 示例数据 ===' as title,
    '' as pmi_number,
    '' as billing_mode,
    '' as current_window_id,
    '' as window_expire_time,
    '' as window_status
UNION ALL
SELECT 
    'LONG模式PMI示例',
    p.pmi_number,
    p.billing_mode,
    CAST(p.current_window_id AS CHAR),
    CAST(p.window_expire_time AS CHAR),
    COALESCE(w.status, 'NO_WINDOW')
FROM t_pmi_records p
LEFT JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
WHERE p.billing_mode = 'LONG'
ORDER BY p.updated_at DESC
LIMIT 10;

-- 6. 创建一个视图来方便查看PMI和窗口的关联信息
CREATE OR REPLACE VIEW v_pmi_window_info AS
SELECT 
    p.id as pmi_id,
    p.pmi_number,
    p.billing_mode,
    p.current_window_id,
    p.window_expire_time,
    p.status as pmi_status,
    w.id as window_id,
    w.window_date,
    w.end_date,
    w.start_time,
    w.end_time,
    w.status as window_status,
    w.actual_start_time,
    w.actual_end_time,
    -- 计算窗口是否已过期
    CASE 
        WHEN p.window_expire_time IS NULL THEN 'NO_WINDOW'
        WHEN p.window_expire_time > NOW() THEN 'ACTIVE'
        WHEN p.window_expire_time <= NOW() THEN 'EXPIRED'
        ELSE 'UNKNOWN'
    END as expire_status,
    -- 计算剩余时间（小时）
    CASE 
        WHEN p.window_expire_time IS NULL THEN NULL
        WHEN p.window_expire_time > NOW() THEN 
            ROUND(TIMESTAMPDIFF(MINUTE, NOW(), p.window_expire_time) / 60.0, 1)
        ELSE 0
    END as remaining_hours
FROM t_pmi_records p
LEFT JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
ORDER BY p.billing_mode, p.pmi_number;

-- 7. 显示使用说明
SELECT '=== 使用说明 ===' as info
UNION ALL
SELECT '1. 使用 v_pmi_window_info 视图查看PMI和窗口关联信息' as info
UNION ALL
SELECT '2. current_window_id 字段指向当前活跃的主窗口' as info
UNION ALL
SELECT '3. window_expire_time 字段表示窗口到期时间' as info
UNION ALL
SELECT '4. 只有计费模式为LONG的PMI才会有这些字段值' as info
UNION ALL
SELECT '5. 前端可以使用这些字段显示长租到期日' as info;
