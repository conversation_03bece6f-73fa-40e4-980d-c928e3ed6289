# ZoomBus 用户前端

这是ZoomBus系统的用户前端项目，专门为终端用户提供PMI会议室快速开启功能。

## 功能特性

- 🚀 **一键开启PMI** - 快速配置Zoom账号并生成主持人链接
- 📱 **响应式设计** - 支持桌面和移动设备
- 🔗 **直接访问** - 仅支持 `/m/{pmiNumber}` 直接访问特定PMI
- 📋 **信息复制** - 一键复制会议信息
- 🎯 **无需登录** - 终端用户无需注册登录即可使用
- 🔒 **安全访问** - 用户只能访问自己的PMI链接，无法全局搜索

## 访问方式

### 直接访问特定PMI（唯一方式）
```
https://yourdomain.com/m/1234567890
```

**注意：** 用户前端不支持PMI搜索功能，用户只能通过直接链接访问自己的PMI。

## 开发环境

### 前置要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```
访问 http://localhost:3001

### 构建生产版本
```bash
npm run build
```
构建文件将输出到 `../src/main/resources/static-user/`

## 技术栈

- **React 18** - 前端框架
- **Ant Design** - UI组件库
- **React Router** - 路由管理
- **Axios** - HTTP客户端
- **Vite** - 构建工具

## 项目结构

```
user-frontend/
├── src/
│   ├── components/     # 可复用组件
│   ├── pages/         # 页面组件
│   │   └── PublicPmiUsage.jsx  # 公共PMI使用页面
│   ├── services/      # API服务
│   │   └── api.js     # API接口定义
│   ├── App.jsx        # 主应用组件
│   ├── main.jsx       # 入口文件
│   └── index.css      # 全局样式
├── index.html         # HTML模板
├── vite.config.js     # Vite配置
└── package.json       # 项目配置
```

## API接口

### 获取PMI信息
```
GET /api/public/pmi/{pmiNumber}
```

### 一键开启PMI
```
POST /api/public/pmi/{pmiNumber}/activate
```

### 获取复制信息
```
GET /api/public/pmi/{pmiNumber}/copy-text
```

## 部署说明

1. 构建前端项目：
   ```bash
   npm run build
   ```

2. 构建文件会自动输出到Spring Boot的静态资源目录

3. 启动Spring Boot应用即可访问用户前端

## 使用流程

1. 用户访问 `/m/{pmiNumber}` 或 `/m`
2. 系统展示PMI基本信息（号码、密码）
3. 用户点击"一键开启PMI"
4. 系统自动分配可用的Zoom账号
5. 配置PMI设置并生成主持人链接
6. 用户通过主持人链接开启会议室

## 注意事项

- PMI号码必须为10位数字
- 系统会自动查找可用的LICENSED类型、PUBLIC_HOST用途的Zoom账号
- 主持人链接仅供主持人使用，请勿分享给参会者
- 如遇问题，请联系技术支持
