{"name": "user-frontend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.6", "axios": "^1.11.0", "i18next": "^25.4.0", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "moment": "^2.30.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.7.0", "react-router-dom": "^7.7.1", "recharts": "^3.1.2"}, "devDependencies": {"@vitejs/plugin-react": "^4.7.0", "vite": "^7.0.6"}}