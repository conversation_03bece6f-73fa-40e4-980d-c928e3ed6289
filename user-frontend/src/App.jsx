import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { getCurrentAntdLocale } from './i18n/antdLocales';
import PublicPmiUsage from './pages/PublicPmiUsage';
import JoinAccountRental from './pages/JoinAccountRental';
import MeetingHost from './pages/MeetingHost';
import LanguageTest from './pages/LanguageTest';
import MyPmiReport from './pages/MyPmiReport.jsx';
import { ResponsiveLanguageSelector } from './components/LanguageSelector';
import './i18n'; // 初始化i18n

// 404页面组件
const NotFoundPage = () => {
  const { t } = useTranslation();

  return (
    <div style={{
      textAlign: 'center',
      padding: '50px',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      {/* 响应式语言选择器 */}
      <ResponsiveLanguageSelector
        mobilePosition="topRight"
        desktopPosition="topRight"
      />

      <h2>页面不存在</h2>
      <p>请通过正确的链接访问</p>
      <p style={{ marginTop: '20px', color: '#666' }}>
        支持的访问方式：<br/>
        PMI会议室：/m/会议室号<br/>
        参会账号：/join/权益编号<br/>
        会议主持：/meeting/会议UUID
      </p>
    </div>
  );
};

const App = () => {
  const { i18n } = useTranslation();
  const currentAntdLocale = getCurrentAntdLocale(i18n.language);

  return (
    <ConfigProvider locale={currentAntdLocale}>
      <Suspense fallback={<Spin size="large" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh'
      }} />}>
        <Router>
        <Routes>
          {/* PMI使用页面 - 只支持带参数的直接访问 */}
          <Route path="/m/:pmiNumber" element={<PublicPmiUsage />} />

          {/* Join Account Rental页面 - 支持带Token的直接访问 */}
          <Route path="/join/:tokenNumber" element={<JoinAccountRental />} />

          {/* 会议主持人页面 - 支持带UUID的直接访问 */}
          <Route path="/meeting/:meetingUuid" element={<MeetingHost />} />

          {/* 多语言测试页面 */}
          <Route path="/test" element={<LanguageTest />} />

          {/* PMI报告页面 - 支持带PMI号码的直接访问 */}
          <Route path="/report/:pmiNumber" element={<MyPmiReport />} />

          {/* 根路径重定向到404，因为用户必须通过直接链接访问 */}
          <Route path="/" element={<Navigate to="/404" replace />} />

          {/* 404页面 */}
          <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Router>
      </Suspense>
    </ConfigProvider>
  );
};

export default App;
