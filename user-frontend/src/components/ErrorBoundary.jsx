import React from 'react';
import { Result, Button } from 'antd';
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons';

/**
 * 错误边界组件
 * 用于捕获和处理React组件中的错误
 */
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { 
            hasError: false, 
            error: null,
            errorInfo: null 
        };
    }

    static getDerivedStateFromError(error) {
        // 更新state以显示错误UI
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // 记录错误信息
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        
        this.setState({
            error: error,
            errorInfo: errorInfo
        });

        // 可以在这里发送错误报告到监控服务
        // this.reportError(error, errorInfo);
    }

    reportError = (error, errorInfo) => {
        // 发送错误报告到监控服务
        try {
            // 这里可以集成错误监控服务，如Sentry
            console.log('Reporting error to monitoring service:', {
                error: error.toString(),
                stack: error.stack,
                componentStack: errorInfo.componentStack,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href
            });
        } catch (reportingError) {
            console.error('Failed to report error:', reportingError);
        }
    };

    handleReload = () => {
        // 重新加载页面
        window.location.reload();
    };

    handleGoHome = () => {
        // 返回首页
        window.location.href = '/';
    };

    handleRetry = () => {
        // 重置错误状态，重新渲染组件
        this.setState({ 
            hasError: false, 
            error: null, 
            errorInfo: null 
        });
    };

    render() {
        if (this.state.hasError) {
            const { title, showDetails = false, showRetry = true } = this.props;
            
            return (
                <div style={{ 
                    padding: '50px 20px', 
                    textAlign: 'center',
                    minHeight: '400px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <Result
                        status="error"
                        title={title || "页面出现错误"}
                        subTitle="抱歉，页面遇到了一些问题。您可以尝试刷新页面或返回首页。"
                        extra={[
                            showRetry && (
                                <Button 
                                    type="primary" 
                                    key="retry"
                                    icon={<ReloadOutlined />}
                                    onClick={this.handleRetry}
                                >
                                    重试
                                </Button>
                            ),
                            <Button 
                                key="reload"
                                icon={<ReloadOutlined />}
                                onClick={this.handleReload}
                            >
                                刷新页面
                            </Button>,
                            <Button 
                                key="home"
                                icon={<HomeOutlined />}
                                onClick={this.handleGoHome}
                            >
                                返回首页
                            </Button>
                        ].filter(Boolean)}
                    />
                    
                    {showDetails && this.state.error && (
                        <details style={{ 
                            marginTop: '20px', 
                            textAlign: 'left',
                            maxWidth: '600px',
                            margin: '20px auto 0'
                        }}>
                            <summary style={{ 
                                cursor: 'pointer', 
                                padding: '10px',
                                backgroundColor: '#f5f5f5',
                                border: '1px solid #d9d9d9',
                                borderRadius: '4px'
                            }}>
                                查看错误详情
                            </summary>
                            <div style={{ 
                                padding: '10px',
                                backgroundColor: '#fff',
                                border: '1px solid #d9d9d9',
                                borderTop: 'none',
                                borderRadius: '0 0 4px 4px',
                                fontSize: '12px',
                                fontFamily: 'monospace',
                                whiteSpace: 'pre-wrap',
                                overflow: 'auto',
                                maxHeight: '200px'
                            }}>
                                <strong>错误信息:</strong>
                                {this.state.error.toString()}
                                
                                <br /><br />
                                
                                <strong>错误堆栈:</strong>
                                {this.state.error.stack}
                                
                                {this.state.errorInfo && (
                                    <>
                                        <br /><br />
                                        <strong>组件堆栈:</strong>
                                        {this.state.errorInfo.componentStack}
                                    </>
                                )}
                            </div>
                        </details>
                    )}
                </div>
            );
        }

        return this.props.children;
    }
}

/**
 * 高阶组件：为组件添加错误边界
 */
export const withErrorBoundary = (WrappedComponent, errorBoundaryProps = {}) => {
    const WithErrorBoundaryComponent = (props) => (
        <ErrorBoundary {...errorBoundaryProps}>
            <WrappedComponent {...props} />
        </ErrorBoundary>
    );
    
    WithErrorBoundaryComponent.displayName = 
        `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;
    
    return WithErrorBoundaryComponent;
};

/**
 * Hook：用于在函数组件中处理错误
 */
export const useErrorHandler = () => {
    const handleError = React.useCallback((error, errorInfo = {}) => {
        console.error('Error caught by useErrorHandler:', error);
        
        // 可以在这里添加错误报告逻辑
        // reportError(error, errorInfo);
        
        // 可以显示全局错误提示
        // message.error('操作失败，请稍后重试');
    }, []);

    return handleError;
};

/**
 * 网络错误处理工具
 */
export const handleNetworkError = (error) => {
    if (!navigator.onLine) {
        return '网络连接已断开，请检查网络设置';
    }
    
    if (error.code === 'NETWORK_ERROR') {
        return '网络请求失败，请检查网络连接';
    }
    
    if (error.response) {
        const status = error.response.status;
        switch (status) {
            case 400:
                return '请求参数错误';
            case 401:
                return '未授权访问，请重新登录';
            case 403:
                return '访问被拒绝，权限不足';
            case 404:
                return '请求的资源不存在';
            case 408:
                return '请求超时，请稍后重试';
            case 429:
                return '请求过于频繁，请稍后重试';
            case 500:
                return '服务器内部错误，请稍后重试';
            case 502:
                return '网关错误，请稍后重试';
            case 503:
                return '服务暂时不可用，请稍后重试';
            case 504:
                return '网关超时，请稍后重试';
            default:
                return `请求失败 (${status})，请稍后重试`;
        }
    }
    
    if (error.request) {
        return '网络请求失败，请检查网络连接';
    }
    
    return error.message || '未知错误，请稍后重试';
};

/**
 * 异步操作错误处理装饰器
 */
export const withAsyncErrorHandling = (asyncFunction, errorHandler) => {
    return async (...args) => {
        try {
            return await asyncFunction(...args);
        } catch (error) {
            if (errorHandler) {
                errorHandler(error);
            } else {
                console.error('Async operation failed:', error);
            }
            throw error;
        }
    };
};

export default ErrorBoundary;
