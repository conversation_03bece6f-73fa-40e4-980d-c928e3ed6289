import React, { useState, useEffect } from 'react';
import { Dropdown, Button, Space, Typography } from 'antd';
import { GlobalOutlined, DownOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { languages, getCurrentLanguage, changeLanguage } from '../i18n';

const { Text } = Typography;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const LanguageSelector = ({
  size = 'middle',
  type = 'default',
  showText = true,
  placement = 'bottomRight',
  isMobileMode = false
}) => {
  const { t } = useTranslation();
  const [currentLang, setCurrentLang] = useState(getCurrentLanguage());
  const [isMobileView, setIsMobileView] = useState(isMobile());

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLanguageChange = async (languageCode) => {
    try {
      await changeLanguage(languageCode);
      const newLang = languages.find(lang => lang.code === languageCode);
      setCurrentLang(newLang);

      // 不需要刷新页面，i18next会自动更新所有使用useTranslation的组件
    } catch (error) {
      console.error('Language change failed:', error);
    }
  };

  const menuItems = languages.map(lang => ({
    key: lang.code,
    label: (
      <Space>
        <span style={{ fontSize: '16px' }}>{lang.flag}</span>
        <span>{lang.nativeName}</span>
        {lang.code === currentLang.code && (
          <Text type="success" style={{ fontSize: '12px' }}>
            ✓
          </Text>
        )}
      </Space>
    ),
    onClick: () => handleLanguageChange(lang.code)
  }));

  // 根据移动端状态调整配置
  const actualPlacement = isMobileView || isMobileMode ? 'bottomLeft' : placement;
  const actualSize = isMobileView || isMobileMode ? 'small' : size;
  const actualShowText = isMobileView || isMobileMode ? false : showText;

  const dropdownProps = {
    menu: { items: menuItems },
    placement: actualPlacement,
    trigger: ['click'],
    overlayStyle: {
      minWidth: '150px',
      maxWidth: isMobileView ? '200px' : '250px'
    }
  };

  return (
    <Dropdown {...dropdownProps}>
      <Button
        type={type}
        size={actualSize}
        style={{
          display: 'flex',
          alignItems: 'center',
          border: type === 'text' ? 'none' : undefined,
          boxShadow: type === 'text' ? 'none' : undefined,
          minWidth: isMobileView || isMobileMode ? 'auto' : undefined
        }}
      >
        <Space size="small">
          <GlobalOutlined />
          {actualShowText && (
            <>
              <span style={{ fontSize: '16px' }}>{currentLang.flag}</span>
              <span>{currentLang.nativeName}</span>
            </>
          )}
          {!actualShowText && (
            <span style={{ fontSize: '16px' }}>{currentLang.flag}</span>
          )}
          <DownOutlined style={{ fontSize: '12px' }} />
        </Space>
      </Button>
    </Dropdown>
  );
};

// 紧凑版语言选择器（只显示图标和旗帜）
export const CompactLanguageSelector = (props) => (
  <LanguageSelector
    {...props}
    showText={false}
    type="text"
    size="small"
  />
);

// 移动端语言选择器
export const MobileLanguageSelector = (props) => (
  <LanguageSelector
    {...props}
    isMobileMode={true}
    showText={false}
    size="small"
    placement="bottomLeft"
  />
);

// 响应式语言选择器容器组件
export const ResponsiveLanguageSelector = ({
  desktopPosition = 'topRight',
  mobilePosition = 'topCenter',
  ...props
}) => {
  const [isMobileView, setIsMobileView] = useState(isMobile());

  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const getPositionStyle = () => {
    if (isMobileView) {
      switch (mobilePosition) {
        case 'topCenter':
          return {
            position: 'relative',
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '16px',
            padding: '8px 0'
          };
        case 'topRight':
          return {
            position: 'absolute',
            top: '8px',
            right: '8px',
            zIndex: 1000
          };
        case 'topLeft':
          return {
            position: 'absolute',
            top: '8px',
            left: '8px',
            zIndex: 1000
          };
        default:
          return {
            position: 'relative',
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '16px'
          };
      }
    } else {
      switch (desktopPosition) {
        case 'topRight':
          return {
            position: 'absolute',
            top: '20px',
            right: '20px',
            zIndex: 1000
          };
        case 'topLeft':
          return {
            position: 'absolute',
            top: '20px',
            left: '20px',
            zIndex: 1000
          };
        default:
          return {
            position: 'absolute',
            top: '20px',
            right: '20px',
            zIndex: 1000
          };
      }
    }
  };

  return (
    <div style={getPositionStyle()}>
      <LanguageSelector
        {...props}
        isMobileMode={isMobileView}
      />
    </div>
  );
};

export default LanguageSelector;
