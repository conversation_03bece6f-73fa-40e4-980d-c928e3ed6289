import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { handleMeetingReportError, createRetryFunction } from '../utils/errorHandler';
import {
    Card,
    Table,
    Button,
    Row,
    Col,
    Tag,
    Badge,
    Typography,
    message,
    Spin,
    Empty,
    Space,
    Divider
} from 'antd';
import {
    EyeOutlined,
    SyncOutlined,
    UserOutlined,
    ClockCircleOutlined,
    VideoCameraOutlined,
    DesktopOutlined,
    CalendarOutlined,
    TeamOutlined
} from '@ant-design/icons';
import { meetingReportApi } from '../services/api';
import dayjs from 'dayjs';
import '../styles/mobile-optimizations.css';

const { Text, Title } = Typography;

/**
 * 用户端会议报告查看组件
 * 支持移动端和多语言
 */
const MeetingReportViewer = React.memo(({
    meeting,
    isMobileView = false,
    showTitle = true,
    compact = false
}) => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState(false);
    const [reportData, setReportData] = useState(null);
    const [participants, setParticipants] = useState([]);
    const [participantsLoading, setParticipantsLoading] = useState(false);
    const [showParticipants, setShowParticipants] = useState(false);

    // 使用 ref 来防止重复触发
    const lastFetchedMeetingRef = useRef(null);
    const isInitialLoadRef = useRef(true);
    const lastTriggerTimeRef = useRef(0);

    // 获取会议报告数据
    const fetchReportData = useCallback(async () => {
        if (!meeting) return;

        // 防止重复请求相同的会议
        const meetingKey = `${meeting.zoomMeetingUuid || ''}-${meeting.zoomMeetingId || ''}`;
        if (lastFetchedMeetingRef.current === meetingKey && !isInitialLoadRef.current) {
            console.log('防止重复获取相同会议的报告数据:', meetingKey);
            return;
        }

        console.log('获取会议报告数据:', meetingKey);
        lastFetchedMeetingRef.current = meetingKey;
        isInitialLoadRef.current = false;

        setLoading(true);
        try {
            let reportResponse = null;

            // 首先尝试通过UUID获取报告
            if (meeting.zoomMeetingUuid) {
                try {
                    reportResponse = await meetingReportApi.getReportByUuid(meeting.zoomMeetingUuid);
                } catch (error) {
                    console.log('通过UUID获取报告失败，尝试通过会议ID获取:', error);
                }
            }

            // 如果通过UUID获取失败，尝试通过会议ID获取
            if (!reportResponse && meeting.zoomMeetingId) {
                try {
                    reportResponse = await meetingReportApi.getReportByMeetingId(meeting.zoomMeetingId);
                } catch (error) {
                    console.log('通过会议ID获取报告失败:', error);
                }
            }

            if (reportResponse && reportResponse.data) {
                setReportData(reportResponse.data);
            } else {
                setReportData(null);
            }
        } catch (error) {
            console.error('获取会议报告失败:', error);
            handleMeetingReportError(error, meeting?.zoomMeetingId || meeting?.zoomMeetingUuid);
        } finally {
            setLoading(false);
        }
    }, [meeting?.zoomMeetingUuid, meeting?.zoomMeetingId]);

    // 获取参会者列表（使用useCallback优化性能）
    const fetchParticipants = useCallback(async () => {
        if (!reportData?.id) return;

        setParticipantsLoading(true);
        try {
            const response = await meetingReportApi.getParticipants(reportData.id, {
                page: 0,
                size: 100
            });
            
            if (response.data) {
                setParticipants(response.data.content || []);
            }
        } catch (error) {
            console.error('获取参会者列表失败:', error);
            handleMeetingReportError(error, reportData?.id);
        } finally {
            setParticipantsLoading(false);
        }
    }, [reportData?.id, t]);

    // 手动触发会议报告获取
    const handleTriggerReportFetch = async () => {
        if (!meeting?.meetingUuid && !meeting?.zoomMeetingUuid) {
            message.error(t('meetingReport.errors.missingUuid', '缺少会议UUID，无法获取报告'));
            return;
        }

        // 防抖：防止短时间内重复触发（5秒内只能触发一次）
        const now = Date.now();
        if (now - lastTriggerTimeRef.current < 5000) {
            message.warning(t('meetingReport.messages.tooFrequent', '请勿频繁触发，请等待5秒后再试'));
            return;
        }
        lastTriggerTimeRef.current = now;

        try {
            setLoading(true);
            let response;

            console.log('手动触发会议报告获取:', {
                meetingUuid: meeting.meetingUuid,
                zoomMeetingUuid: meeting.zoomMeetingUuid
            });

            // 优先使用系统会议UUID（meeting.meetingUuid）
            if (meeting.meetingUuid) {
                response = await meetingReportApi.triggerReportFetchByMeetingUuid(meeting.meetingUuid);
            } else if (meeting.zoomMeetingUuid) {
                // 如果没有系统会议UUID，使用Zoom会议UUID
                response = await meetingReportApi.triggerReportFetch(meeting.zoomMeetingUuid);
            }

            if (response.data && response.data.success) {
                message.success(t('meetingReport.messages.fetchTriggered', '会议报告获取已触发，请稍后查看'));
                // 延迟重新获取报告，并重置防重复标记
                setTimeout(() => {
                    lastFetchedMeetingRef.current = null; // 重置，允许重新获取
                    fetchReportData();
                }, 3000);
            } else {
                message.error(response.data?.message || t('meetingReport.errors.triggerFailed', '触发报告获取失败'));
            }
        } catch (error) {
            console.error('触发报告获取失败:', error);
            handleMeetingReportError(error, meeting?.zoomMeetingId || meeting?.zoomMeetingUuid);
        } finally {
            setLoading(false);
        }
    };

    // 切换参会者列表显示（使用useCallback优化性能）
    const toggleParticipants = useCallback(() => {
        if (!showParticipants && participants.length === 0) {
            fetchParticipants();
        }
        setShowParticipants(!showParticipants);
    }, [showParticipants, participants.length, fetchParticipants]);

    // 当组件挂载或meeting变化时获取数据
    useEffect(() => {
        if (meeting && (meeting.zoomMeetingUuid || meeting.zoomMeetingId)) {
            fetchReportData();
        }
    }, [fetchReportData]); // 使用 fetchReportData 作为依赖，它已经包含了正确的依赖项

    // 参会者表格列配置（使用useMemo优化性能）
    const participantColumns = useMemo(() => [
        {
            title: t('meetingReport.participant.name', '姓名'),
            dataIndex: 'participantName',
            key: 'name',
            ellipsis: true,
            render: (name) => name || t('meetingReport.participant.unknown', '未知用户')
        },
        // 移动端显示简化的时长列
        ...(isMobileView ? [{
            title: t('meetingReport.participant.duration', '时长'),
            dataIndex: 'durationMinutes',
            key: 'duration',
            width: 60,
            render: (minutes) => (
                <span style={{ fontSize: '12px' }}>
                    {minutes || 0}分
                </span>
            )
        }] : [{
            title: t('meetingReport.participant.duration', '时长'),
            dataIndex: 'durationMinutes',
            key: 'duration',
            width: 100,
            render: (minutes) => (
                <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <ClockCircleOutlined style={{ fontSize: '12px', color: '#666' }} />
                    {minutes || 0}{t('common.minutes', '分钟')}
                </span>
            )
        }]),
        {
            title: t('meetingReport.participant.type', '类型'),
            dataIndex: 'userType',
            key: 'userType',
            width: isMobileView ? 50 : 80,
            render: (type) => (
                <Tag
                    color={type === 'HOST' ? 'blue' : 'default'}
                    icon={isMobileView ? null : <UserOutlined />}
                    style={{
                        fontSize: isMobileView ? '10px' : '12px',
                        padding: isMobileView ? '2px 4px' : '4px 8px'
                    }}
                >
                    {type === 'HOST' ?
                        (isMobileView ? '主持' : t('meetingReport.participant.host', '主持人')) :
                        (isMobileView ? '参会' : t('meetingReport.participant.attendee', '参会者'))
                    }
                </Tag>
            )
        }
    ], [isMobileView, t]);

    if (loading) {
        return (
            <div style={{ textAlign: 'center', padding: compact ? '20px 0' : '40px 0' }}>
                <Spin size={compact ? 'default' : 'large'} />
                <div style={{ marginTop: '16px', color: '#666' }}>
                    {t('meetingReport.loading', '正在获取会议报告...')}
                </div>
            </div>
        );
    }

    if (!reportData) {
        return (
            <div style={{ textAlign: 'center', padding: compact ? '20px 0' : '40px 0' }}>
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={
                        <div>
                            <div style={{ fontSize: '16px', color: '#666', marginBottom: '16px' }}>
                                {t('meetingReport.noData', '暂无会议报告数据')}
                            </div>
                            <div style={{ fontSize: '14px', color: '#999', marginBottom: '24px' }}>
                                {t('meetingReport.dataAvailableAfter', '会议报告通常在会议结束后5-10分钟内生成')}
                            </div>
                            <Button
                                type="primary"
                                icon={<SyncOutlined />}
                                onClick={handleTriggerReportFetch}
                                loading={loading}
                                size={compact ? 'small' : 'default'}
                            >
                                {t('meetingReport.actions.manualFetch', '手动获取报告')}
                            </Button>
                        </div>
                    }
                />
            </div>
        );
    }

    return (
        <div className="meeting-report-viewer">
            {showTitle && (
                <div style={{ marginBottom: compact ? '16px' : '24px' }}>
                    <Title level={isMobileView ? 4 : 3}>
                        <EyeOutlined style={{ marginRight: '8px' }} />
                        {t('meetingReport.title', '会议报告')}
                    </Title>
                </div>
            )}

            {/* 会议基本信息 */}
            <Card
                size={compact ? 'small' : 'default'}
                className="meeting-report-basic-info"
                style={{ marginBottom: '16px' }}
            >
                <Row gutter={[16, 8]}>
                    <Col xs={24} sm={12}>
                        <Text strong>{t('meetingReport.basic.topic', '会议主题')}：</Text>
                        <Text>{reportData.topic || t('meetingReport.basic.noTopic', '无主题')}</Text>
                    </Col>
                    <Col xs={24} sm={12}>
                        <Text strong>{t('meetingReport.basic.duration', '会议时长')}：</Text>
                        <Text>
                            <ClockCircleOutlined style={{ marginRight: '4px', color: '#666' }} />
                            {reportData.durationMinutes || 0} {t('common.minutes', '分钟')}
                        </Text>
                    </Col>
                    <Col xs={24} sm={12}>
                        <Text strong>{t('meetingReport.basic.startTime', '开始时间')}：</Text>
                        <Text>
                            {reportData.startTime ? 
                                dayjs(reportData.startTime).format('YYYY-MM-DD HH:mm:ss') : 
                                '-'
                            }
                        </Text>
                    </Col>
                    <Col xs={24} sm={12}>
                        <Text strong>{t('meetingReport.basic.endTime', '结束时间')}：</Text>
                        <Text>
                            {reportData.endTime ? 
                                dayjs(reportData.endTime).format('YYYY-MM-DD HH:mm:ss') : 
                                '-'
                            }
                        </Text>
                    </Col>
                    <Col xs={24} sm={12}>
                        <Text strong>{t('meetingReport.basic.totalParticipants', '参会人数')}：</Text>
                        <Text>
                            <UserOutlined style={{ marginRight: '4px', color: '#666' }} />
                            {reportData.totalParticipants || 0} {t('common.people', '人')}
                        </Text>
                    </Col>
                    <Col xs={24} sm={12}>
                        <Text strong>{t('meetingReport.basic.uniqueParticipants', '唯一参会人数')}：</Text>
                        <Text>
                            <TeamOutlined style={{ marginRight: '4px', color: '#666' }} />
                            {reportData.uniqueParticipants || 0} {t('common.people', '人')}
                        </Text>
                    </Col>
                </Row>
            </Card>

            {/* 会议功能使用情况 */}
            <Card
                size={compact ? 'small' : 'default'}
                title={t('meetingReport.features.title', '会议功能使用情况')}
                className="meeting-report-features"
                style={{ marginBottom: '16px' }}
            >
                <Row gutter={[16, 8]}>
                    <Col xs={8} sm={8}>
                        <div style={{ textAlign: 'center' }}>
                            <Badge 
                                status={reportData.hasRecording ? 'success' : 'default'} 
                                text={
                                    <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                                        {reportData.hasRecording ? 
                                            t('meetingReport.features.hasRecording', '有录制') : 
                                            t('meetingReport.features.noRecording', '无录制')
                                        }
                                    </span>
                                }
                            />
                        </div>
                    </Col>
                    <Col xs={8} sm={8}>
                        <div style={{ textAlign: 'center' }}>
                            <Badge 
                                status={reportData.hasVideo ? 'success' : 'default'} 
                                text={
                                    <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                                        <VideoCameraOutlined style={{ marginRight: '4px' }} />
                                        {reportData.hasVideo ? 
                                            t('meetingReport.features.hasVideo', '有视频') : 
                                            t('meetingReport.features.noVideo', '无视频')
                                        }
                                    </span>
                                }
                            />
                        </div>
                    </Col>
                    <Col xs={8} sm={8}>
                        <div style={{ textAlign: 'center' }}>
                            <Badge 
                                status={reportData.hasScreenShare ? 'success' : 'default'} 
                                text={
                                    <span style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                                        <DesktopOutlined style={{ marginRight: '4px' }} />
                                        {reportData.hasScreenShare ? 
                                            t('meetingReport.features.hasScreenShare', '有屏幕共享') : 
                                            t('meetingReport.features.noScreenShare', '无屏幕共享')
                                        }
                                    </span>
                                }
                            />
                        </div>
                    </Col>
                </Row>
            </Card>

            {/* 参会者列表 */}
            <Card
                size={compact ? 'small' : 'default'}
                className="meeting-report-participants"
                title={
                    <Space>
                        <span>{t('meetingReport.participants.title', '参会者列表')}</span>
                        {reportData.totalParticipants > 0 && (
                            <Tag color="blue">
                                {reportData.totalParticipants} {t('common.people', '人')}
                            </Tag>
                        )}
                    </Space>
                }
                extra={
                    reportData.totalParticipants > 0 && (
                        <Button
                            type="link"
                            size="small"
                            onClick={toggleParticipants}
                            loading={participantsLoading}
                        >
                            {showParticipants ? 
                                t('meetingReport.participants.hide', '隐藏') : 
                                t('meetingReport.participants.show', '显示')
                            }
                        </Button>
                    )
                }
            >
                {showParticipants && (
                    <Table
                        size="small"
                        dataSource={participants}
                        rowKey="id"
                        loading={participantsLoading}
                        pagination={{
                            pageSize: isMobileView ? 3 : 10,
                            size: 'small',
                            showSizeChanger: false,
                            showQuickJumper: false,
                            showTotal: (total, range) =>
                                isMobileView ?
                                    `${range[0]}-${range[1]}/${total}` :
                                    `共 ${total} 条记录`,
                            simple: isMobileView
                        }}
                        columns={participantColumns}
                        scroll={{ x: isMobileView ? 250 : 'auto' }}
                    />
                )}
                
                {!showParticipants && reportData.totalParticipants > 0 && (
                    <div style={{ textAlign: 'center', color: '#666', padding: '20px 0' }}>
                        {t('meetingReport.participants.clickToView', '点击上方"显示"按钮查看参会者详情')}
                    </div>
                )}
                
                {reportData.totalParticipants === 0 && (
                    <Empty 
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={t('meetingReport.participants.noData', '暂无参会者数据')}
                    />
                )}
            </Card>
        </div>
    );
});

// 设置displayName用于调试
MeetingReportViewer.displayName = 'MeetingReportViewer';

export default MeetingReportViewer;
