import React from 'react';
import { Table, Tag, Button, Tooltip } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  CheckCircleOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  DesktopOutlined,
  PhoneOutlined
} from '@ant-design/icons';
import moment from 'moment';

const PmiMeetingList = ({ 
  meetings = [], 
  loading = false, 
  pagination = true,
  size = 'small',
  onMeetingClick = null 
}) => {
  
  const getStatusTag = (fetchStatus) => {
    const statusConfig = {
      'SUCCESS': { color: 'green', text: '成功' },
      'PENDING': { color: 'blue', text: '待处理' },
      'FAILED': { color: 'red', text: '失败' },
      'PROCESSING': { color: 'orange', text: '处理中' },
    };
    
    const config = statusConfig[fetchStatus] || { color: 'default', text: fetchStatus };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getMeetingTypeTag = (meetingType) => {
    const typeConfig = {
      'PMI': { color: 'blue', text: 'PMI会议' },
      'SCHEDULED': { color: 'green', text: '预定会议' },
      'INSTANT': { color: 'orange', text: '即时会议' },
      'RECURRING': { color: 'purple', text: '周期会议' },
    };
    
    const config = typeConfig[meetingType] || { color: 'default', text: meetingType };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderFeatures = (record) => {
    const features = [];
    
    if (record.hasVideo) {
      features.push(
        <Tooltip title="有视频" key="video">
          <VideoCameraOutlined style={{ color: '#1890ff', marginRight: 4 }} />
        </Tooltip>
      );
    }
    
    if (record.hasVoip) {
      features.push(
        <Tooltip title="有VoIP音频" key="voip">
          <AudioOutlined style={{ color: '#52c41a', marginRight: 4 }} />
        </Tooltip>
      );
    }
    
    if (record.hasScreenShare) {
      features.push(
        <Tooltip title="有屏幕共享" key="screen">
          <DesktopOutlined style={{ color: '#faad14', marginRight: 4 }} />
        </Tooltip>
      );
    }
    
    if (record.hasPstn) {
      features.push(
        <Tooltip title="有电话接入" key="pstn">
          <PhoneOutlined style={{ color: '#722ed1', marginRight: 4 }} />
        </Tooltip>
      );
    }
    
    if (record.hasRecording) {
      features.push(
        <Tooltip title="有录制" key="recording">
          <Tag color="red" size="small">录制</Tag>
        </Tooltip>
      );
    }
    
    return <div>{features}</div>;
  };

  const columns = [
    {
      title: '会议主题',
      dataIndex: 'topic',
      key: 'topic',
      ellipsis: true,
      render: (text, record) => (
        onMeetingClick ? (
          <Button type="link" onClick={() => onMeetingClick(record)} style={{ padding: 0 }}>
            {text || '未命名会议'}
          </Button>
        ) : (
          <span>{text || '未命名会议'}</span>
        )
      ),
    },
    {
      title: '会议类型',
      dataIndex: 'meetingType',
      key: 'meetingType',
      width: 100,
      render: getMeetingTypeTag,
      responsive: ['md'],
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 150,
      render: (text) => text ? moment(text).format('MM-DD HH:mm') : '-',
      sorter: (a, b) => moment(a.startTime).unix() - moment(b.startTime).unix(),
    },
    {
      title: '时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 80,
      render: (duration) => {
        if (!duration) return '-';
        const hours = Math.floor(duration / 60);
        const minutes = duration % 60;
        return hours > 0 ? `${hours}h${minutes}m` : `${minutes}m`;
      },
      sorter: (a, b) => (a.duration || 0) - (b.duration || 0),
    },
    {
      title: '参与人数',
      dataIndex: 'participantCount',
      key: 'participantCount',
      width: 100,
      render: (count) => count || 0,
      sorter: (a, b) => (a.participantCount || 0) - (b.participantCount || 0),
    },
    {
      title: '功能',
      key: 'features',
      width: 120,
      render: (_, record) => renderFeatures(record),
      responsive: ['lg'],
    },
  ];

  const defaultPagination = {
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  };

  return (
    <Table
      columns={columns}
      dataSource={meetings}
      rowKey="id"
      loading={loading}
      size={size}
      pagination={pagination ? defaultPagination : false}
      scroll={{ x: 600 }}
    />
  );
};

export default PmiMeetingList;
