import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Table,
  Button,
  message,
  Space,
  Tag,
  Tooltip,
  Empty,
  Alert,
  Card,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  EyeOutlined,
  SyncOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { meetingReportApi } from '../services/api';
import MeetingReportViewer from './MeetingReportViewer';
import dayjs from 'dayjs';

const PmiMeetingReports = ({ 
  visible, 
  onCancel, 
  pmiNumber, 
  pmiRecordId,
  isMobileView = false 
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [selectedMeeting, setSelectedMeeting] = useState(null);
  const [reportDetailVisible, setReportDetailVisible] = useState(false);
  const [statistics, setStatistics] = useState(null);

  // 获取PMI会议报告列表
  const fetchReports = async (page = 1, size = 10) => {
    if (!pmiRecordId) return;
    
    setLoading(true);
    try {
      const params = {
        page: page - 1,
        size
      };

      const response = await meetingReportApi.getReportsByPmiRecordId(pmiRecordId, params);
      
      if (response.data) {
        setReports(response.data.content || []);
        setPagination({
          current: page,
          pageSize: size,
          total: response.data.totalElements || 0
        });
      }
    } catch (error) {
      console.error('获取PMI会议报告失败:', error);
      message.error('获取会议报告失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取PMI统计信息
  const fetchStatistics = async () => {
    if (!pmiRecordId) return;
    
    try {
      // 这里可以调用统计API，暂时从报告列表计算
      if (reports.length > 0) {
        const totalMeetings = reports.length;
        const totalDuration = reports.reduce((sum, report) => sum + (report.durationMinutes || 0), 0);
        const totalParticipants = reports.reduce((sum, report) => sum + (report.participantCount || 0), 0);
        const avgDuration = totalMeetings > 0 ? Math.round(totalDuration / totalMeetings) : 0;
        const avgParticipants = totalMeetings > 0 ? Math.round(totalParticipants / totalMeetings) : 0;

        setStatistics({
          totalMeetings,
          totalDuration,
          totalParticipants,
          avgDuration,
          avgParticipants
        });
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  // 查看会议报告详情
  const handleViewReport = (record) => {
    setSelectedMeeting({
      zoomMeetingUuid: record.zoomMeetingUuid,
      zoomMeetingId: record.zoomMeetingId,
      meetingUuid: record.meetingUuid,
      topic: record.topic || `PMI ${pmiNumber} 会议`
    });
    setReportDetailVisible(true);
  };

  // 重新获取会议报告
  const handleRefetchReport = async (record) => {
    try {
      message.loading('正在重新获取会议报告...', 0);
      
      let response;
      if (record.meetingUuid) {
        response = await meetingReportApi.triggerReportFetchByMeetingUuid(record.meetingUuid);
      } else if (record.zoomMeetingUuid) {
        response = await meetingReportApi.triggerReportFetch(record.zoomMeetingUuid);
      }

      message.destroy();
      
      if (response?.data?.success) {
        message.success('已触发报告重新获取，请稍后刷新查看');
        // 延迟刷新列表
        setTimeout(() => {
          fetchReports(pagination.current, pagination.pageSize);
        }, 3000);
      } else {
        message.error('触发报告获取失败');
      }
    } catch (error) {
      message.destroy();
      console.error('重新获取报告失败:', error);
      message.error('重新获取报告失败');
    }
  };

  // 获取状态标签
  const getStatusTag = (fetchStatus) => {
    const statusConfig = {
      'SUCCESS': { color: 'green', text: '成功' },
      'PENDING': { color: 'blue', text: '待处理' },
      'FAILED': { color: 'red', text: '失败' },
      'PROCESSING': { color: 'orange', text: '处理中' },
    };
    
    const config = statusConfig[fetchStatus] || { color: 'default', text: fetchStatus };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '会议主题',
      dataIndex: 'topic',
      key: 'topic',
      ellipsis: true,
      render: (text) => text || '未命名会议'
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: isMobileView ? 120 : 150,
      render: (text) => text ? dayjs(text).format('MM-DD HH:mm') : '-',
      sorter: (a, b) => dayjs(a.startTime).unix() - dayjs(b.startTime).unix(),
    },
    {
      title: '时长',
      dataIndex: 'durationMinutes',
      key: 'durationMinutes',
      width: 80,
      render: (duration) => {
        if (!duration) return '-';
        const hours = Math.floor(duration / 60);
        const minutes = duration % 60;
        return hours > 0 ? `${hours}h${minutes}m` : `${minutes}m`;
      },
      sorter: (a, b) => (a.durationMinutes || 0) - (b.durationMinutes || 0),
    },
    {
      title: '参与人数',
      dataIndex: 'participantCount',
      key: 'participantCount',
      width: 100,
      render: (count) => count || 0,
      sorter: (a, b) => (a.participantCount || 0) - (b.participantCount || 0),
    },
    {
      title: '状态',
      dataIndex: 'fetchStatus',
      key: 'fetchStatus',
      width: 80,
      render: getStatusTag,
    },
    {
      title: '操作',
      key: 'action',
      width: isMobileView ? 100 : 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewReport(record)}
            />
          </Tooltip>
          {record.fetchStatus !== 'SUCCESS' && (
            <Tooltip title="重新获取">
              <Button
                type="link"
                size="small"
                icon={<SyncOutlined />}
                onClick={() => handleRefetchReport(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  // 初始化数据
  useEffect(() => {
    if (visible && pmiRecordId) {
      fetchReports();
    }
  }, [visible, pmiRecordId]);

  // 计算统计信息
  useEffect(() => {
    if (reports.length > 0) {
      fetchStatistics();
    }
  }, [reports]);

  return (
    <>
      <Modal
        title={`PMI ${pmiNumber} 会议报告`}
        open={visible}
        onCancel={onCancel}
        footer={null}
        width={isMobileView ? '95%' : 1000}
        style={{ top: isMobileView ? 20 : 50 }}
      >
        {/* 统计信息 */}
        {statistics && (
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col xs={12} sm={6}>
                <Statistic
                  title="总会议数"
                  value={statistics.totalMeetings}
                  prefix={<CalendarOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="总时长(分钟)"
                  value={statistics.totalDuration}
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="平均时长(分钟)"
                  value={statistics.avgDuration}
                  prefix={<FileTextOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="平均参与人数"
                  value={statistics.avgParticipants}
                  prefix={<UserOutlined />}
                />
              </Col>
            </Row>
          </Card>
        )}

        {/* 会议报告列表 */}
        {reports.length > 0 ? (
          <Table
            columns={columns}
            dataSource={reports}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              onChange: fetchReports,
              showSizeChanger: !isMobileView,
              showQuickJumper: !isMobileView,
              showTotal: (total) => `共 ${total} 条记录`,
              size: isMobileView ? 'small' : 'default'
            }}
            scroll={{ x: isMobileView ? 600 : 'auto' }}
            size={isMobileView ? 'small' : 'middle'}
          />
        ) : (
          !loading && (
            <Empty 
              description="暂无会议报告"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Alert
                message="提示"
                description="只有已结束的会议才会生成报告，请在会议结束后查看。"
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            </Empty>
          )
        )}
      </Modal>

      {/* 会议报告详情弹窗 */}
      <Modal
        title={`会议报告详情 - ${selectedMeeting?.topic || '未命名会议'}`}
        open={reportDetailVisible}
        onCancel={() => {
          setReportDetailVisible(false);
          setSelectedMeeting(null);
        }}
        footer={null}
        width={isMobileView ? '95%' : 800}
        style={{ top: isMobileView ? 20 : 50 }}
      >
        {selectedMeeting && (
          <MeetingReportViewer
            meeting={selectedMeeting}
            isMobileView={isMobileView}
            compact={false}
          />
        )}
      </Modal>
    </>
  );
};

export default PmiMeetingReports;
