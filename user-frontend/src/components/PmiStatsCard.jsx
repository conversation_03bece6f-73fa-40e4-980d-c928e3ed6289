import React from 'react';
import { Card, Statistic, Row, Col } from 'antd';
import { 
  FileTextOutlined, 
  CalendarOutlined, 
  UserOutlined, 
  ClockCircleOutlined,
  TrophyOutlined,
  TeamOutlined 
} from '@ant-design/icons';

const PmiStatsCard = ({ stats, loading = false }) => {
  const statsConfig = [
    {
      title: '总会议数',
      value: stats?.totalMeetings || 0,
      icon: <CalendarOutlined />,
      color: '#1890ff',
    },
    {
      title: '总时长(分钟)',
      value: stats?.totalDurationMinutes || 0,
      icon: <ClockCircleOutlined />,
      color: '#52c41a',
    },
    {
      title: '平均时长(分钟)',
      value: stats?.avgDurationMinutes ? stats.avgDurationMinutes.toFixed(1) : '0',
      icon: <TrophyOutlined />,
      color: '#faad14',
    },
    {
      title: '总参与人数',
      value: stats?.totalParticipants || 0,
      icon: <TeamOutlined />,
      color: '#722ed1',
    },
    {
      title: '平均参与人数',
      value: stats?.avgParticipants ? stats.avgParticipants.toFixed(1) : '0',
      icon: <UserOutlined />,
      color: '#eb2f96',
    },
    {
      title: '独特参与者',
      value: stats?.uniqueParticipantsCount || 0,
      icon: <FileTextOutlined />,
      color: '#13c2c2',
    },
  ];

  return (
    <Row gutter={[16, 16]}>
      {statsConfig.map((stat, index) => (
        <Col xs={24} sm={12} md={8} lg={6} xl={4} key={index}>
          <Card loading={loading} size="small">
            <Statistic
              title={stat.title}
              value={stat.value}
              prefix={React.cloneElement(stat.icon, { style: { color: stat.color } })}
              valueStyle={{ color: stat.color }}
            />
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default PmiStatsCard;
