import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Space,
  Alert
} from 'antd';
import {
  GlobalOutlined,
  ExclamationCircleOutlined,
  EllipsisOutlined
} from '@ant-design/icons';
import { isMobileDevice } from '../utils/wechatDetection';

const { Title, Text } = Typography;

/**
 * 微信浏览器引导遮罩组件
 * 当检测到用户在微信内置浏览器中访问时显示半透明遮罩
 * 引导用户在系统默认浏览器中打开页面以正常使用Zoom功能
 * 不提供关闭功能，确保用户必须在浏览器中打开
 */
const WechatGuide = () => {
  const { t } = useTranslation();
  const isMobile = isMobileDevice();



  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <div
        style={{
          maxWidth: isMobile ? '90vw' : '500px',
          width: '100%',
          backgroundColor: '#fff',
          borderRadius: '8px',
          padding: isMobile ? '20px' : '30px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
        }}
      >
        {/* 警告提示 */}
        <Alert
          message={t('wechatGuide.overlay.warning.title')}
          description={t('wechatGuide.overlay.warning.description')}
          type="warning"
          icon={<ExclamationCircleOutlined />}
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* 引导内容 */}
        <div style={{ textAlign: 'center' }}>
          <Typography.Title level={4} style={{ marginBottom: 20, color: '#333' }}>
            {t('wechatGuide.overlay.title')}
          </Typography.Title>

          <div style={{
            fontSize: isMobile ? '16px' : '18px',
            lineHeight: '1.8',
            color: '#555'
          }}>
            {isMobile ? (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '12px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '6px'
                }}>
                  <EllipsisOutlined style={{ fontSize: '28px', marginRight: '12px', color: '#1890ff' }} />
                  <Typography.Text strong style={{ fontSize: '16px' }}>
                    {t('wechatGuide.overlay.mobile.step1')}
                  </Typography.Text>
                </div>
                <div style={{ fontSize: '16px', color: '#666' }}>
                  <Typography.Text>
                    {t('wechatGuide.overlay.mobile.step2')}
                  </Typography.Text>
                </div>
              </Space>
            ) : (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '12px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '6px'
                }}>
                  <GlobalOutlined style={{ fontSize: '28px', marginRight: '12px', color: '#1890ff' }} />
                  <Typography.Text strong style={{ fontSize: '18px' }}>
                    {t('wechatGuide.overlay.desktop.step1')}
                  </Typography.Text>
                </div>
                <div style={{ fontSize: '16px', color: '#666' }}>
                  <Typography.Text>
                    {t('wechatGuide.overlay.desktop.step2')}
                  </Typography.Text>
                </div>
              </Space>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WechatGuide;
