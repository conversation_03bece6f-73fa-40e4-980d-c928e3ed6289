import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import jaJP from 'antd/locale/ja_JP';
import esES from 'antd/locale/es_ES';

// Ant Design 语言包映射
export const antdLocales = {
  'zh-CN': zhCN,
  'en-US': enUS,
  'ja-JP': jaJP,
  'es-ES': esES
};

// 获取当前语言对应的 Ant Design 语言包
export const getAntdLocale = (languageCode) => {
  return antdLocales[languageCode] || antdLocales['zh-CN'];
};

// 根据 i18n 语言代码获取 Ant Design 语言包
export const getCurrentAntdLocale = (i18nLanguage) => {
  // 处理语言代码映射
  const langMap = {
    'zh': 'zh-CN',
    'en': 'en-US', 
    'ja': 'ja-JP',
    'es': 'es-ES'
  };
  
  // 如果是完整的语言代码，直接使用
  if (antdLocales[i18nLanguage]) {
    return antdLocales[i18nLanguage];
  }
  
  // 如果是简短的语言代码，进行映射
  const shortLang = i18nLanguage.split('-')[0];
  const mappedLang = langMap[shortLang];
  
  return antdLocales[mappedLang] || antdLocales['zh-CN'];
};
