import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入翻译文件
import zhCN from './locales/zh-CN.json';
import enUS from './locales/en-US.json';
import jaJP from './locales/ja-JP.json';
import esES from './locales/es-ES.json';

// 语言资源
const resources = {
  'zh-CN': {
    translation: zhCN
  },
  'en-US': {
    translation: enUS
  },
  'ja-JP': {
    translation: jaJP
  },
  'es-ES': {
    translation: esES
  }
};

// 语言检测配置
const detectionOptions = {
  // 检测顺序：localStorage -> navigator -> 默认语言
  order: ['localStorage', 'navigator'],

  // 在localStorage中存储的key
  lookupLocalStorage: 'i18nextLng',

  // 缓存用户语言
  caches: ['localStorage'],

  // 排除某些语言检测
  excludeCacheFor: ['cimode'],

  // 检查所有插件
  checkWhitelist: true,

  // 语言代码映射，将简短的语言代码映射到完整的语言代码
  lookupFromPathIndex: 0,
  lookupFromSubdomainIndex: 0,

  // 自定义检测函数，处理语言代码映射
  convertDetectedLanguage: (lng) => {
    // 语言代码映射表
    const langMap = {
      'zh': 'zh-CN',
      'zh-cn': 'zh-CN',
      'zh-Hans': 'zh-CN',
      'zh-Hans-CN': 'zh-CN',
      'en': 'en-US',
      'ja': 'ja-JP',
      'es': 'es-ES'
    };

    // 转换为小写进行匹配
    const lowerLng = lng.toLowerCase();

    // 如果有直接映射，使用映射后的语言代码
    if (langMap[lowerLng]) {
      return langMap[lowerLng];
    }

    // 如果是完整的语言代码且在支持列表中，直接返回
    if (['zh-CN', 'en-US', 'ja-JP', 'es-ES'].includes(lng)) {
      return lng;
    }

    // 默认返回中文
    return 'zh-CN';
  }
};

i18n
  // 检测用户语言
  .use(LanguageDetector)
  // 传递i18n实例给react-i18next
  .use(initReactI18next)
  // 初始化i18next
  .init({
    resources,

    // 默认语言 - 设置为中文
    lng: 'zh-CN',
    fallbackLng: 'zh-CN',

    // 调试模式 - 在开发环境下关闭，减少控制台噪音
    debug: false,

    // 语言检测配置
    detection: detectionOptions,

    // 插值配置
    interpolation: {
      escapeValue: false, // React已经默认转义了
    },

    // 支持的语言列表
    supportedLngs: ['zh-CN', 'en-US', 'ja-JP', 'es-ES'],

    // 严格模式，不允许回退到不支持的语言
    nonExplicitSupportedLngs: false,

    // 语言映射 - 使用完整的语言代码
    load: 'all',

    // React配置
    react: {
      useSuspense: false
    },

    // 忽略不支持的语言代码
    cleanCode: true,

    // 语言回退配置
    fallbackLng: {
      'zh': ['zh-CN'],
      'en': ['en-US'],
      'ja': ['ja-JP'],
      'es': ['es-ES'],
      'default': ['zh-CN']
    }
  });

// 导出语言信息
export const languages = [
  {
    code: 'zh-CN',
    name: '中文',
    nativeName: '中文',
    flag: '🇨🇳'
  },
  {
    code: 'en-US',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'ja-JP',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵'
  },
  {
    code: 'es-ES',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸'
  }
];

// 获取当前语言信息
export const getCurrentLanguage = () => {
  const currentLng = i18n.language;
  return languages.find(lang => lang.code === currentLng) || languages[0];
};

// 切换语言
export const changeLanguage = (languageCode) => {
  return i18n.changeLanguage(languageCode);
};

export default i18n;
