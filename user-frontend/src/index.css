body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.pmi-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.pmi-card {
  max-width: 600px;
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.pmi-header {
  text-align: center;
  margin-bottom: 24px;
}

.pmi-logo {
  font-size: 32px;
  font-weight: bold;
  color: #0273fe;
  margin-bottom: 8px;
}

.pmi-subtitle {
  color: #666;
  font-size: 16px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .pmi-container {
    padding: 10px;
  }

  .pmi-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .pmi-logo {
    font-size: 28px;
  }

  .pmi-subtitle {
    font-size: 14px;
  }

  /* 移动端按钮优化 */
  .ant-btn {
    min-height: 44px; /* 确保按钮足够大，便于触摸 */
  }

  .ant-btn-lg {
    min-height: 48px;
  }

  /* 移动端表单优化 */
  .ant-input, .ant-select-selector {
    min-height: 44px;
  }

  /* 移动端卡片间距优化 */
  .ant-card {
    margin-bottom: 16px !important;
  }

  /* 移动端文字大小优化 */
  .ant-typography h1 {
    font-size: 24px !important;
  }

  .ant-typography h2 {
    font-size: 20px !important;
  }

  .ant-typography h3 {
    font-size: 18px !important;
  }

  .ant-typography h4 {
    font-size: 16px !important;
  }
}

/* 主持人页面特殊样式 */
.host-page .pmi-logo {
  color: #ff7a00;
}

.host-page .ant-card-head-title {
  font-weight: 600;
}

.host-page .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.host-page .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}
