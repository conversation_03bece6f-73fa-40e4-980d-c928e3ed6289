import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Button,
  Steps,
  DatePicker,
  TimePicker,
  Form,
  message,
  Spin,
  Alert,
  Typography,
  Space,
  Divider,
  Tag,
  Row,
  Col,
  Statistic,
  Modal,
  Input
} from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  UserOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { joinAccountRentalApi } from '../services/api';
import { copyWithMessage, formatAccountInfo } from '../utils/copyUtils';
import { ResponsiveLanguageSelector } from '../components/LanguageSelector';
import moment from 'moment';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

const JoinAccountRental = () => {
  const { tokenNumber } = useParams();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [tokenInfo, setTokenInfo] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedDates, setSelectedDates] = useState(null);
  const [allocatedAccount, setAllocatedAccount] = useState(null);
  const [reservationData, setReservationData] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [countdown, setCountdown] = useState('');
  const [timePreview, setTimePreview] = useState(null);
  const [form] = Form.useForm();

  // 获取Token信息
  const fetchTokenInfo = async () => {
    setLoading(true);
    try {
      const response = await joinAccountRentalApi.getTokenInfo(tokenNumber);
      if (response.data.success) {
        setTokenInfo(response.data.data);
        
        // 根据Token状态与窗口真实状态设置当前步骤（不再仅依赖本地时间）
        const status = response.data.data.status;
        const windowStatus = response.data.data.windowStatus || response.data.data.usageWindow?.status;
        if (status === 'PENDING' || status === 'EXPORTED') {
          setCurrentStep(0);
        } else if (status === 'RESERVED') {
          // 仅依据后端返回的窗口状态决定当前步骤
          if (windowStatus === 'ACTIVE') {
            setCurrentStep(2);
          } else {
            setCurrentStep(1);
          }
          // RESERVED状态也设置账号信息，方便用户提前获取
          if (response.data.data.allocatedAccount) {
            setAllocatedAccount(response.data.data.allocatedAccount);
            setReservationData(response.data.data);
          }
        } else if (status === 'ACTIVE') {
          setCurrentStep(2);
          setAllocatedAccount(response.data.data.allocatedAccount);
        } else if (status === 'COMPLETED') {
          setCurrentStep(3);
        }
      } else {
        message.error(response.data.message || t('joinAccount.errors.tokenNotFound'));
      }
    } catch (error) {
      message.error(t('joinAccount.errors.networkError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (tokenNumber) {
      fetchTokenInfo();
    }
  }, [tokenNumber]);

  // 定时检查是否需要切换步骤（从等待开启到使用账号）
  useEffect(() => {
    if (currentStep === 1) {
      // 等待开启时，改为每隔5秒向后端查询真实状态；仅用于倒计时展示则使用本地时间
      const poll = async () => {
        try {
          const resp = await joinAccountRentalApi.getTokenInfo(tokenNumber);
          if (resp.data.success) {
            const data = resp.data.data;
            const status = data.status;
            const windowStatus = data.windowStatus || data.usageWindow?.status;

            if (status === 'ACTIVE' || windowStatus === 'ACTIVE') {
              setCurrentStep(2);
              setAllocatedAccount(data.allocatedAccount || allocatedAccount);
              setCountdown('');
              message.info(t('joinAccount.messages.allocationSuccess'));
            } else if (data.windowStartTime || data.usageWindow?.startTime) {
              const startTime = moment(data.windowStartTime || data.usageWindow?.startTime);
              const now = moment();
              if (startTime.isAfter(now)) {
                const duration = moment.duration(startTime.diff(now));
                const days = Math.floor(duration.asDays());
                const hours = duration.hours();
                const minutes = duration.minutes();
                const seconds = duration.seconds();
                if (days > 0) setCountdown(`${days}天 ${hours}小时 ${minutes}分钟 ${seconds}秒`);
                else if (hours > 0) setCountdown(`${hours}小时 ${minutes}分钟 ${seconds}秒`);
                else if (minutes > 0) setCountdown(`${minutes}分钟 ${seconds}秒`);
                else setCountdown(`${seconds}秒`);
              } else {
                // 已到时间但后端仍未激活，继续轮询等待后端真实状态
                setCountdown('');
              }
            }
          }
        } catch {}
      };

      // 立即拉一次
      poll();
      const interval = setInterval(poll, 5000);
      return () => clearInterval(interval);
    } else {
      setCountdown('');
    }
  }, [currentStep, reservationData, tokenInfo]);

  // 预约时间段
  const handleReservation = async (values) => {
    setSubmitting(true);
    try {
      // 根据选择的日期自动计算时间段
      const startDate = values.startDate;
      const startTime = startDate.startOf('day').format('YYYY-MM-DD HH:mm:ss'); // 00:00:00
      const endTime = startDate.clone().add(tokenInfo.usageDays, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'); // N天后的00:00:00

      const response = await joinAccountRentalApi.reserveToken(tokenNumber, {
        startTime,
        endTime
      });

      if (response.data.success) {
        message.success(t('joinAccount.messages.verificationSuccess'));
        setReservationData(response.data.data);
        // 同时设置allocatedAccount，以便后续步骤使用
        if (response.data.data.allocatedAccount) {
          setAllocatedAccount(response.data.data.allocatedAccount);
        }

        // 根据后端返回的真实状态判断当前步骤
        const windowStatus = response.data.data.usageWindow?.status;
        if (windowStatus === 'ACTIVE' || response.data.data.status === 'ACTIVE') {
          setCurrentStep(2);
        } else {
          setCurrentStep(1);
        }

        // 重新获取Token信息，确保数据一致性
        fetchTokenInfo();
      } else {
        message.error(response.data.message || t('joinAccount.errors.allocationFailed'));
      }
    } catch (error) {
      console.error('预约失败:', error);
      // 显示详细的错误信息
      if (error.response && error.response.data && error.response.data.message) {
        message.error(error.response.data.message);
      } else {
        message.error(t('joinAccount.errors.networkError'));
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 获取账号
  const handleGetAccount = async () => {
    setSubmitting(true);
    try {
      const response = await joinAccountRentalApi.activateToken(tokenNumber);
      if (response.data.success) {
        message.success('账号分配成功！');
        setAllocatedAccount(response.data.data);
        setCurrentStep(2);
        fetchTokenInfo();
      } else {
        message.error(response.data.message || '获取账号失败');
      }
    } catch (error) {
      message.error('获取账号失败，请稍后重试');
    } finally {
      setSubmitting(false);
    }
  };

  // 处理日期选择变化
  const handleDateChange = (date) => {
    if (date && tokenInfo) {
      const startTime = date.startOf('day');
      const endTime = startTime.clone().add(tokenInfo.usageDays, 'days');
      setTimePreview({
        startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
        endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
        startDisplay: startTime.format('YYYY年MM月DD日 00:00'),
        endDisplay: endTime.format('YYYY年MM月DD日 00:00')
      });
    } else {
      setTimePreview(null);
    }
  };

  // 复制账号信息（优化版本）
  const handleCopyAccount = async () => {
    // 获取账号信息，支持多个数据源
    const accountData = reservationData?.allocatedAccount || allocatedAccount || tokenInfo?.allocatedAccount;
    if (!accountData) {
      message.error('账号信息尚未加载，请稍后重试');
      return;
    }

    // 获取时间区间信息
    const startTime = reservationData?.usageWindow?.startTime || tokenInfo?.windowStartTime;
    const endTime = reservationData?.usageWindow?.endTime || tokenInfo?.windowEndTime;
    const timeRange = startTime && endTime ?
      `${moment(startTime).format('YYYY-MM-DD HH:mm')} 至 ${moment(endTime).format('YYYY-MM-DD HH:mm')}` :
      '请查看页面显示的时间';

    // 使用统一的格式化函数
    const formattedAccountInfo = formatAccountInfo(accountData, timeRange);

    // 使用优化的复制方法
    await copyWithMessage(
      formattedAccountInfo,
      message,
      '账号信息已复制到剪贴板',
      '复制失败，请手动复制账号信息'
    );
  };

  // 禁用日期（只能选择未来的日期）
  const disabledDate = (current) => {
    return current && current < moment().startOf('day');
  };



  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: '#f0f2f5',
        position: 'relative'
      }}>
        {/* 响应式语言选择器 */}
        <ResponsiveLanguageSelector
          mobilePosition="topRight"
          desktopPosition="topRight"
        />

        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh'
        }}>
          <Spin size="large" />
        </div>
      </div>
    );
  }

  if (!tokenInfo) {
    return (
      <div style={{
        minHeight: '100vh',
        background: '#f0f2f5',
        position: 'relative'
      }}>
        {/* 响应式语言选择器 */}
        <ResponsiveLanguageSelector
          mobilePosition="topRight"
          desktopPosition="topRight"
        />

        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh'
        }}>
          <Card style={{ maxWidth: 400, textAlign: 'center' }}>
            <ExclamationCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />
            <Title level={4}>权益链接无效</Title>
            <Paragraph>
              请检查链接是否正确，或联系管理员获取有效的权益链接。
            </Paragraph>
          </Card>
        </div>
      </div>
    );
  }

  const steps = [
    {
      title: t('joinAccount.steps.verify'),
      description: t('joinAccount.verifying'),
      icon: <CalendarOutlined />
    },
    {
      title: t('joinAccount.steps.allocate'),
      description: t('joinAccount.allocating'),
      icon: <ClockCircleOutlined />
    },
    {
      title: t('joinAccount.accountInfo'),
      description: t('joinAccount.usageInstructions'),
      icon: <UserOutlined />
    },
    {
      title: t('joinAccount.steps.complete'),
      description: t('joinAccount.messages.setupComplete'),
      icon: <CheckCircleOutlined />
    }
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: '#f0f2f5',
      padding: '16px',
      position: 'relative'
    }}>
      {/* 响应式语言选择器 */}
      <ResponsiveLanguageSelector
        mobilePosition="topRight"
        desktopPosition="topRight"
      />

      <div style={{ maxWidth: 800, margin: '0 auto' }}>
        {/* 头部信息 */}
        <Card style={{ marginBottom: 24 }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Statistic
                title={t('joinAccount.tokenNumber')}
                value={tokenInfo.tokenNumber}
                prefix={<Tag color="blue">JOIN</Tag>}
              />
            </Col>
            <Col xs={24} sm={12}>
              <Statistic
                title={t('joinAccount.usageTime')}
                value={tokenInfo.usageDays}
                suffix={t('time.days')}
                prefix={<CalendarOutlined />}
              />
            </Col>
          </Row>
        </Card>

        {/* 进度步骤 */}
        <Card style={{ marginBottom: 24 }}>
          <Steps
            current={currentStep}
            responsive={true}
            direction="horizontal"
            size="small"
          >
            {steps.map((step, index) => (
              <Step
                key={index}
                title={<div style={{ textAlign: 'center' }}>{step.title}</div>}
                description={<div style={{ textAlign: 'center' }}>{step.description}</div>}
                icon={step.icon}
              />
            ))}
          </Steps>
        </Card>

        {/* 主要内容 */}
        {currentStep === 0 && (
          <Card title="选择使用时间">
            <Alert
              message="请选择您开始使用Zoom账号的日期"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
            
            <Form
              form={form}
              layout="vertical"
              onFinish={handleReservation}
            >
              <Form.Item
                name="startDate"
                label="使用开始日期"
                rules={[{ required: true, message: '请选择使用开始日期' }]}
              >
                <DatePicker
                  format="YYYY-MM-DD"
                  placeholder="选择开始日期"
                  disabledDate={(current) => {
                    // 允许选择当天及以后的日期
                    return current && current < moment().startOf('day');
                  }}
                  onChange={handleDateChange}
                  style={{ width: '100%' }}
                />
              </Form.Item>

              {timePreview && (
                <div style={{
                  background: '#f6f6f6',
                  padding: '16px',
                  borderRadius: '6px',
                  marginBottom: '16px',
                  border: '1px solid #d9d9d9'
                }}>
                  <div style={{ marginBottom: '8px', fontWeight: 'bold', color: '#1890ff' }}>
                    📅 使用时间预览
                  </div>
                  <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
                    <div><strong>开始时间：</strong>{timePreview.startDisplay}</div>
                    <div><strong>结束时间：</strong>{timePreview.endDisplay}</div>
                    <div style={{ color: '#666', marginTop: '8px' }}>
                      使用时长：{tokenInfo?.usageDays}天
                    </div>
                  </div>
                </div>
              )}

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitting}
                  size="large"
                  block
                >
                  确认预约
                </Button>
              </Form.Item>
            </Form>
          </Card>
        )}

        {currentStep === 1 && (
          <Card title={
            <div>
              预约成功 - 等待开启
              {countdown && (
                <span style={{ marginLeft: '16px', fontSize: '14px', color: '#1890ff', fontWeight: 'normal' }}>
                  (距离开启还有：{countdown})
                </span>
              )}
            </div>
          }>
            <Alert
              message="预约成功"
              description={(() => {
                const startTime = reservationData?.usageWindow?.startTime || tokenInfo?.windowStartTime;
                if (startTime) {
                  const formattedTime = moment(startTime).format('YYYY-MM-DD HH:mm');
                  return `您的预约已成功！账号将在 ${formattedTime} 开始激活。`;
                }
                return '您的预约已成功！账号将在预约时间开始激活。';
              })()}
              type="success"
              showIcon
              style={{ marginBottom: 24 }}
            />

            <div style={{ background: '#f6f6f6', padding: 20, borderRadius: 6, marginBottom: 24 }}>
              <Title level={4}>已分配账号信息</Title>
              <Row gutter={[16, 16]}>
                <Col xs={24}>
                  <Text strong>邮箱地址：</Text>
                  <Text copyable>
                    {reservationData?.allocatedAccount?.email || allocatedAccount?.email || tokenInfo?.allocatedAccount?.email || '加载中...'}
                  </Text>
                </Col>
                <Col xs={24}>
                  <Text strong>登录密码：</Text>
                  <Space>
                    <Text code>
                      {showPassword ?
                        (reservationData?.allocatedAccount?.password || allocatedAccount?.password || tokenInfo?.allocatedAccount?.password || '加载中...') :
                        '••••••••••••'}
                    </Text>
                    <Button
                      type="link"
                      size="small"
                      icon={showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? '隐藏' : '显示'}
                    </Button>
                    <Button
                      type="link"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => {
                        const password = reservationData?.allocatedAccount?.password || allocatedAccount?.password || tokenInfo?.allocatedAccount?.password;
                        if (password) {
                          navigator.clipboard.writeText(password);
                          message.success('密码已复制');
                        }
                      }}
                    >
                      复制
                    </Button>
                  </Space>
                </Col>
                <Col xs={24}>
                  <Text strong>使用时间：</Text>
                  <Text>
                    {(() => {
                      const startTime = reservationData?.usageWindow?.startTime || tokenInfo?.windowStartTime;
                      const endTime = reservationData?.usageWindow?.endTime || tokenInfo?.windowEndTime;
                      if (startTime && endTime) {
                        return `${moment(startTime).format('YYYY-MM-DD HH:mm')} 至 ${moment(endTime).format('YYYY-MM-DD HH:mm')}`;
                      }
                      return '加载中...';
                    })()}
                  </Text>
                </Col>
                <Col xs={24}>
                  <Text strong>登录地址：</Text>
                  <Text copyable>https://zoom.us/signin</Text>
                </Col>
              </Row>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Button
                type="primary"
                size="large"
                icon={<CopyOutlined />}
                onClick={handleCopyAccount}
                block
              >
                复制完整账号信息
              </Button>
            </div>


          </Card>
        )}

        {currentStep === 2 && allocatedAccount && (
          <Card title="账号信息">
            <Alert
              message="账号分配成功"
              description="请使用以下账号信息登录Zoom，账号仅在预约时间段内有效。"
              type="success"
              showIcon
              style={{ marginBottom: 24 }}
            />
            
            <div style={{ background: '#f6f6f6', padding: 20, borderRadius: 6, marginBottom: 24 }}>
              <Row gutter={[16, 16]}>
                <Col xs={24}>
                  <Text strong>邮箱地址：</Text>
                  <Text copyable>{allocatedAccount.email}</Text>
                </Col>
                <Col xs={24}>
                  <Text strong>登录密码：</Text>
                  <Space>
                    <Text code>
                      {showPassword ? allocatedAccount.password : '••••••••••••'}
                    </Text>
                    <Button
                      type="link"
                      size="small"
                      icon={showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? '隐藏' : '显示'}
                    </Button>
                    <Button
                      type="link"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => {
                        navigator.clipboard.writeText(allocatedAccount.password);
                        message.success('密码已复制');
                      }}
                    >
                      复制
                    </Button>
                  </Space>
                </Col>
                <Col xs={24}>
                  <Text strong>登录地址：</Text>
                  <Text copyable>https://zoom.us/signin</Text>
                </Col>
                <Col xs={24}>
                  <Text strong>可用时间：</Text>
                  <Text>
                    {(() => {
                      const startTime = reservationData?.usageWindow?.startTime || tokenInfo?.windowStartTime;
                      const endTime = reservationData?.usageWindow?.endTime || tokenInfo?.windowEndTime;
                      if (startTime && endTime) {
                        return `${moment(startTime).format('YYYY-MM-DD HH:mm')} 至 ${moment(endTime).format('YYYY-MM-DD HH:mm')}`;
                      }
                      return '加载中...';
                    })()}
                  </Text>
                </Col>
              </Row>
            </div>

            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                size="large"
                icon={<CopyOutlined />}
                onClick={handleCopyAccount}
                block
              >
                复制完整账号信息
              </Button>
              
              <Button
                size="large"
                onClick={() => window.open('https://zoom.us/signin', '_blank')}
                block
              >
                前往Zoom登录
              </Button>
            </Space>

            <Divider />
            
            <Alert
              message="使用说明"
              description={
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  <li>请使用上述账号信息登录Zoom</li>
                  <li>账号仅在预约时间段内有效</li>
                  <li>请勿修改账号设置或密码</li>
                  <li>使用完毕后账号将自动回收</li>
                  <li>如遇问题请联系技术支持</li>
                </ul>
              }
              type="info"
              showIcon
            />
          </Card>
        )}

        {currentStep === 3 && (
          <Card title="使用完成">
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <CheckCircleOutlined style={{ fontSize: 64, color: '#52c41a', marginBottom: 16 }} />
              <Title level={3}>账号使用完成</Title>
              <Paragraph>
                感谢您使用我们的服务！账号已自动回收，如需再次使用请获取新的权益链接。
              </Paragraph>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default JoinAccountRental;
