import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Space, Divider, Row, Col, Tag } from 'antd';
import { ResponsiveLanguageSelector } from '../components/LanguageSelector';

const { Title, Text, Paragraph } = Typography;

const LanguageTest = () => {
  const { t, i18n } = useTranslation();

  return (
    <div style={{
      minHeight: '100vh',
      background: '#f0f2f5',
      padding: '20px',
      position: 'relative'
    }}>
      {/* 响应式语言选择器 */}
      <ResponsiveLanguageSelector
        mobilePosition="topRight"
        desktopPosition="topRight"
      />

      <div style={{ maxWidth: 1200, margin: '0 auto' }}>
        <Title level={1} style={{ textAlign: 'center', marginBottom: '40px' }}>
          {t('common.language')} {t('common.test')} / Language Test
        </Title>

        <Row gutter={[24, 24]}>
          {/* 当前语言信息 */}
          <Col xs={24} lg={12}>
            <Card title={t('languageSelector.current')}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div>
                  <Text strong>Language Code: </Text>
                  <Tag color="blue">{i18n.language}</Tag>
                </div>
                <div>
                  <Text strong>Direction: </Text>
                  <Tag>{i18n.dir()}</Tag>
                </div>
              </Space>
            </Card>
          </Col>

          {/* 通用词汇测试 */}
          <Col xs={24} lg={12}>
            <Card title={t('common.common')}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div><Text strong>{t('common.loading')}</Text></div>
                <div><Text strong>{t('common.success')}</Text></div>
                <div><Text strong>{t('common.error')}</Text></div>
                <div><Text strong>{t('common.confirm')}</Text></div>
                <div><Text strong>{t('common.cancel')}</Text></div>
                <div><Text strong>{t('common.save')}</Text></div>
                <div><Text strong>{t('common.copy')}</Text></div>
                <div><Text strong>{t('common.search')}</Text></div>
              </Space>
            </Card>
          </Col>

          {/* Join Account 相关 */}
          <Col xs={24} lg={12}>
            <Card title={t('joinAccount.title')}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div><Text strong>{t('joinAccount.subtitle')}</Text></div>
                <div><Text>{t('joinAccount.tokenNumber')}</Text></div>
                <div><Text>{t('joinAccount.email')}</Text></div>
                <div><Text>{t('joinAccount.password')}</Text></div>
                <div><Text>{t('joinAccount.usageTime')}</Text></div>
                <div><Text>{t('joinAccount.accountInfo')}</Text></div>
                <Divider />
                <div><Text type="success">{t('joinAccount.messages.verificationSuccess')}</Text></div>
                <div><Text type="danger">{t('joinAccount.errors.tokenNotFound')}</Text></div>
              </Space>
            </Card>
          </Col>

          {/* Meeting Host 相关 */}
          <Col xs={24} lg={12}>
            <Card title={t('meetingHost.title')}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div><Text strong>{t('meetingHost.subtitle')}</Text></div>
                <div><Text>{t('meetingHost.meetingId')}</Text></div>
                <div><Text>{t('meetingHost.meetingPassword')}</Text></div>
                <div><Text>{t('meetingHost.hostKey')}</Text></div>
                <div><Text>{t('meetingHost.joinUrl')}</Text></div>
                <div><Text>{t('meetingHost.participants')}</Text></div>
                <Divider />
                <div><Text type="success">{t('meetingHost.messages.meetingStarted')}</Text></div>
                <div><Text type="warning">{t('meetingHost.statuses.waiting')}</Text></div>
              </Space>
            </Card>
          </Col>

          {/* PMI Usage 相关 */}
          <Col xs={24} lg={12}>
            <Card title={t('pmiUsage.title')}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div><Text strong>{t('pmiUsage.subtitle')}</Text></div>
                <div><Text>{t('pmiUsage.pmiNumber')}</Text></div>
                <div><Text>{t('pmiUsage.pmiPassword')}</Text></div>
                <div><Text>{t('pmiUsage.currentStatus')}</Text></div>
                <div><Text>{t('pmiUsage.usageStats')}</Text></div>
                <div><Text>{t('pmiUsage.recentMeetings')}</Text></div>
                <Divider />
                <div><Text type="success">{t('pmiUsage.statuses.available')}</Text></div>
                <div><Text type="warning">{t('pmiUsage.statuses.inUse')}</Text></div>
              </Space>
            </Card>
          </Col>

          {/* 时间相关 */}
          <Col xs={24} lg={12}>
            <Card title={t('time.time')}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div><Text>{t('time.minutes')}</Text></div>
                <div><Text>{t('time.hours')}</Text></div>
                <div><Text>{t('time.days')}</Text></div>
                <div><Text>{t('time.weeks')}</Text></div>
                <div><Text>{t('time.months')}</Text></div>
                <div><Text>{t('time.today')}</Text></div>
                <div><Text>{t('time.yesterday')}</Text></div>
                <div><Text>{t('time.tomorrow')}</Text></div>
              </Space>
            </Card>
          </Col>
        </Row>

        <Card style={{ marginTop: '24px' }}>
          <Title level={3}>使用说明 / Instructions</Title>
          <Paragraph>
            <Text strong>中文：</Text> 点击右上角的语言选择器可以切换语言。支持中文、英语、日语、西班牙语。
          </Paragraph>
          <Paragraph>
            <Text strong>English：</Text> Click the language selector in the top right corner to switch languages. Supports Chinese, English, Japanese, and Spanish.
          </Paragraph>
          <Paragraph>
            <Text strong>日本語：</Text> 右上角の言語セレクターをクリックして言語を切り替えます。中国語、英語、日本語、スペイン語をサポートしています。
          </Paragraph>
          <Paragraph>
            <Text strong>Español：</Text> Haga clic en el selector de idioma en la esquina superior derecha para cambiar idiomas. Compatible con chino, inglés, japonés y español.
          </Paragraph>
        </Card>
      </div>
    </div>
  );
};

export default LanguageTest;
