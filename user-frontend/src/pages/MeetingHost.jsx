import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Button,
  message,
  Alert,
  Typography,
  Row,
  Col,
  Divider,
  Modal,
  Result,
  Tag,
  Space,
  Spin
} from 'antd';
import {
  PlayCircleOutlined,
  CopyOutlined,
  InfoCircleOutlined,
  VideoCameraOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { meetingApi } from '../services/api';
import { copyWithMessage } from '../utils/copyUtils';
import { ResponsiveLanguageSelector } from '../components/LanguageSelector';
import { shouldShowBrowserGuide } from '../utils/wechatDetection';
import WechatGuide from '../components/WechatGuide';
import MeetingReportViewer from '../components/MeetingReportViewer';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const { Title, Text } = Typography;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const MeetingHost = () => {
  const { meetingUuid } = useParams();
  const { t } = useTranslation();
  const [meeting, setMeeting] = useState(null);
  const [meetingDetails, setMeetingDetails] = useState([]);
  const [loading, setLoading] = useState(true);
  const [countdown, setCountdown] = useState({});
  const [currentTime, setCurrentTime] = useState(dayjs());
  const [copyModalVisible, setCopyModalVisible] = useState(false);
  const [copyText, setCopyText] = useState('');
  const [isMobileView, setIsMobileView] = useState(isMobile());

  // 检测是否需要显示微信引导遮罩
  const showWechatGuide = shouldShowBrowserGuide();

  // 加载会议信息
  const loadMeetingInfo = async () => {
    try {
      setLoading(true);
      const response = await meetingApi.getMeetingByUuid(meetingUuid);
      setMeeting(response.data);
      
      // 获取会议详情（场次信息）
      const detailsResponse = await meetingApi.getZoomMeetingDetails(response.data.id);
      setMeetingDetails(detailsResponse.data || []);
    } catch (error) {
      console.error('加载会议信息失败:', error);
      message.error('会议不存在或已被删除');
    } finally {
      setLoading(false);
    }
  };

  // 获取复制信息（参会邀请）
  const handleGetCopyText = async () => {
    try {
      if (!meeting) return;

      const hostKey = (meetingDetails.length > 0 && meetingDetails[0].hostKey) || '无';

      const invitationText = `Zoom会议邀请

会议主题：${meeting.topic}
会议号：${meeting.zoomMeetingId}
会议密码：${meeting.password || '无'}

加入链接：${meeting.joinUrl}

会议时间：${dayjs(meeting.startTime).format('YYYY年MM月DD日 HH:mm')}

主持人信息：
主持人密钥：${hostKey}
主持人链接：请通过主持人页面获取最新链接

---
ZoomBus 会议服务`;

      setCopyText(invitationText);
      setCopyModalVisible(true);
    } catch (error) {
      console.error('获取复制信息失败:', error);
      message.error('获取复制信息失败');
    }
  };

  // 复制到剪贴板
  const copyToClipboard = async () => {
    const success = await copyWithMessage(copyText, message, '已复制到剪贴板', '复制失败，请手动复制');
    if (success) {
      setCopyModalVisible(false);
    }
  };

  // 开始会议
  const handleStartMeeting = async () => {
    try {
      const response = await meetingApi.getLatestHostUrl(meetingUuid);
      if (response.data.success && response.data.hostUrl) {
        window.open(response.data.hostUrl, '_blank');
        message.success('正在跳转到主持人链接...');
      } else {
        message.error('获取主持人链接失败');
      }
    } catch (error) {
      console.error('开始会议失败:', error);
      message.error('开始会议失败，请稍后重试');
    }
  };

  // 计算倒计时
  const calculateCountdown = (startTime) => {
    const now = currentTime;
    const start = dayjs(startTime);
    const diff = start.diff(now);
    
    if (diff <= 0) {
      return null; // 已开始或已过期
    }
    
    const duration = dayjs.duration(diff);
    return {
      days: Math.floor(duration.asDays()),
      hours: duration.hours(),
      minutes: duration.minutes(),
      seconds: duration.seconds()
    };
  };

  // 判断是否在会议时间窗口内（包括会议开始前30分钟的准备时间）
  const isInMeetingWindow = (startTime, durationMinutes) => {
    const now = currentTime;
    const start = dayjs(startTime);
    const end = start.add(durationMinutes || 60, 'minute');
    const prepareStart = start.subtract(30, 'minute'); // 会议开始前30分钟可以进入
    return now.isAfter(prepareStart) && now.isBefore(end);
  };

  // 获取会议状态
  const getMeetingStatus = (startTime, durationMinutes) => {
    const now = currentTime;
    const start = dayjs(startTime);
    const end = start.add(durationMinutes || 60, 'minute');

    if (now.isBefore(start)) {
      return { status: 'waiting', text: t('meetingHost.statuses.waiting'), color: 'blue' };
    } else if (now.isAfter(end)) {
      return { status: 'ended', text: t('meetingHost.statuses.ended'), color: 'default' };
    } else {
      return { status: 'ongoing', text: t('meetingHost.statuses.started'), color: 'green' };
    }
  };

  // 获取最近的场次
  const getUpcomingOccurrence = () => {
    if (!meetingDetails.length) return null;

    const now = currentTime;
    const upcoming = meetingDetails
      .filter(detail => dayjs(detail.occurrenceStartTime || detail.startTime).isAfter(now))
      .sort((a, b) => dayjs(a.occurrenceStartTime || a.startTime).diff(dayjs(b.occurrenceStartTime || b.startTime)));

    return upcoming[0] || null;
  };

  // 获取可以开始的会议（在准备时间窗口内的会议）
  const getStartableMeeting = () => {
    if (!meetingDetails.length) return null;

    return meetingDetails.find(detail => {
      const startTime = dayjs(detail.occurrenceStartTime || detail.startTime);
      return isInMeetingWindow(startTime, meeting?.durationMinutes || 60);
    }) || null;
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 更新时间和倒计时
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(dayjs());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (meetingUuid) {
      loadMeetingInfo();
    }
  }, [meetingUuid]);

  if (loading) {
    return (
      <div className="pmi-container">
        <Card className="pmi-card">
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>正在加载会议信息...</div>
          </div>
        </Card>
      </div>
    );
  }

  if (!meeting) {
    return (
      <div className="pmi-container">
        <Card className="pmi-card">
          <div className="pmi-header">
            <div className="pmi-logo">
              <VideoCameraOutlined /> ZoomBus
            </div>
            <div className="pmi-subtitle">会议主持人页面</div>
          </div>
          <Result
            status="404"
            title="会议不存在"
            subTitle="请检查链接是否正确，或联系管理员"
          />
        </Card>
      </div>
    );
  }

  const upcomingOccurrence = getUpcomingOccurrence();
  const startableMeeting = getStartableMeeting();
  const nextCountdown = upcomingOccurrence ? calculateCountdown(upcomingOccurrence.occurrenceStartTime || upcomingOccurrence.startTime) : null;
  const hasUpcomingMeeting = upcomingOccurrence !== null;
  const isInCurrentWindow = startableMeeting !== null;





  return (
    <div className="pmi-container host-page" style={{ position: 'relative' }}>
      {/* 响应式语言选择器 */}
      <ResponsiveLanguageSelector
        mobilePosition="topRight"
        desktopPosition="topRight"
      />

      <Card className="pmi-card">
        {/* 页面头部 */}
        <div className="pmi-header">
          <div className="pmi-logo">
            <UserOutlined /> ZoomBus {t('meetingHost.title')}
          </div>
          <div className="pmi-subtitle">{t('meetingHost.subtitle')}</div>
        </div>

        {/* 会议基本信息 */}
        <Card 
          title={
            <Space>
              <InfoCircleOutlined />
              {t('meetingHost.meetingInfo')}
            </Space>
          }
          style={{ marginBottom: isMobileView ? 16 : 24 }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24}>
              <div style={{ marginBottom: 16 }}>
                <Text strong style={{ fontSize: isMobileView ? '16px' : '18px' }}>{t('meetingHost.meetingTopicLabel')}</Text>
                <br />
                <Title level={isMobileView ? 4 : 3} style={{ margin: '8px 0', color: '#1890ff' }}>
                  {meeting.topic}
                </Title>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <div style={{ position: 'relative' }}>
                <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>{t('meetingHost.meetingIdLabel')}</Text>
                <br />
                <Text copyable style={{
                  fontSize: isMobileView ? 16 : 18,
                  color: '#1890ff',
                  wordBreak: 'break-all'
                }}>
                  {meeting.zoomMeetingId}
                </Text>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <div style={{ position: 'relative' }}>
                <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>{t('meetingHost.meetingPasswordLabel')}</Text>
                <br />
                <Text copyable style={{
                  fontSize: isMobileView ? 16 : 18,
                  color: '#1890ff',
                  wordBreak: 'break-all'
                }}>
                  {meeting.password || '无'}
                </Text>
                {/* 获取复制信息按钮 */}
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={handleGetCopyText}
                  style={{
                    position: 'absolute',
                    bottom: -8,
                    right: 0,
                    fontSize: '12px',
                    height: '24px',
                    padding: '0 8px'
                  }}
                >
                  {t('meetingHost.copyInvitation')}
                </Button>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>{t('meetingHost.hostKeyLabel')}</Text>
              <br />
              <Text copyable style={{
                fontSize: isMobileView ? 16 : 18,
                color: '#ff7a00',
                wordBreak: 'break-all'
              }}>
                {(meetingDetails.length > 0 && meetingDetails[0].hostKey) || '无'}
              </Text>
            </Col>
            <Col xs={24} sm={12}>
              <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>{t('meetingHost.createdTimeLabel')}</Text>
              <br />
              <Text style={{ fontSize: isMobileView ? 14 : 16 }}>
                {dayjs(meeting.createdAt).format('YYYY年MM月DD日 HH:mm')}
              </Text>
            </Col>
          </Row>

          <Divider />

          {/* 会议状态和操作 */}
          {isInCurrentWindow ? (
            <Alert
              message={t('meetingHost.meetingInProgress')}
              description={t('meetingHost.meetingInProgressDesc')}
              type="success"
              showIcon
              style={{ marginBottom: 24 }}
            />
          ) : hasUpcomingMeeting ? (
            <Alert
              message={t('meetingHost.upcomingMeeting')}
              description={
                <div>
                  <p><strong>{t('meetingHost.nextMeetingTime')}</strong>{dayjs(upcomingOccurrence.occurrenceStartTime || upcomingOccurrence.startTime).format('YYYY年MM月DD日 HH:mm')}</p>
                  {nextCountdown && (
                    <p><strong>{t('meetingHost.timeToStart')}</strong>
                      {nextCountdown.days > 0 && `${nextCountdown.days}${t('meetingHost.timeUnits.days')}`}
                      {nextCountdown.hours > 0 && `${nextCountdown.hours}${t('meetingHost.timeUnits.hours')}`}
                      {nextCountdown.minutes > 0 && `${nextCountdown.minutes}${t('meetingHost.timeUnits.minutes')}`}
                      {nextCountdown.seconds}{t('meetingHost.timeUnits.seconds')}
                    </p>
                  )}
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
          ) : (
            <Alert
              message={t('meetingHost.noScheduledMeeting')}
              description={t('meetingHost.noScheduledMeetingDesc')}
              type="warning"
              showIcon
              style={{ marginBottom: 24 }}
            />
          )}

          {/* 主持会议按钮 */}
          <Button
            type="primary"
            size="large"
            block
            icon={<PlayCircleOutlined />}
            onClick={handleStartMeeting}
            disabled={!isInCurrentWindow && !hasUpcomingMeeting}
            style={{
              height: isMobileView ? 48 : 56,
              fontSize: isMobileView ? '16px' : '18px',
              fontWeight: 'bold'
            }}
          >
            {isInCurrentWindow ? t('meetingHost.startMeetingRoom') : hasUpcomingMeeting ? t('meetingHost.startMeetingRoom') : t('meetingHost.noAvailableMeeting')}
          </Button>
        </Card>

        {/* 会议场次详情 */}
        {meetingDetails.length > 0 && (
          <Card
            title={
              <Space>
                <ClockCircleOutlined />
                {t('meetingHost.meetingSchedule')}
              </Space>
            }
            size="small"
            style={{ marginBottom: 16 }}
          >
            {(() => {
              // 找到最近即将开始的场次（待开始状态中时间最早的）
              const now = currentTime;
              const upcomingMeetings = meetingDetails
                .map((detail, index) => ({
                  ...detail,
                  originalIndex: index,
                  startTime: dayjs(detail.occurrenceStartTime || detail.startTime)
                }))
                .filter(detail => detail.startTime.isAfter(now))
                .sort((a, b) => a.startTime.diff(b.startTime));

              const nextUpcomingMeeting = upcomingMeetings.length > 0 ? upcomingMeetings[0] : null;

              return meetingDetails.map((detail, index) => {
                const startTime = dayjs(detail.occurrenceStartTime || detail.startTime);
                const durationMinutes = meeting?.durationMinutes || 60;
                const endTime = startTime.isValid() ? startTime.add(durationMinutes, 'minute') : null;
                const isInWindow = isInMeetingWindow(startTime, durationMinutes);
                const countdown = calculateCountdown(startTime);
                const meetingStatus = getMeetingStatus(startTime, durationMinutes);

                // 只有最近即将开始的场次才显示倒计时
                const shouldShowCountdown = nextUpcomingMeeting &&
                  nextUpcomingMeeting.originalIndex === index &&
                  meetingStatus.status === 'waiting';

                return (
                  <div key={index} style={{
                    padding: '12px 0',
                    borderBottom: index < meetingDetails.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}>
                    <Row gutter={16} align="middle">
                      <Col xs={24} sm={16}>
                        <div>
                          <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>
                            {startTime.format('YYYY年MM月DD日 HH:mm')}
                          </Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                            {t('meetingHost.endTimeLabel')}{endTime && endTime.isValid() ? endTime.format('HH:mm') : '未知'}
                          </Text>
                        </div>
                      </Col>
                      <Col xs={24} sm={8} style={{ textAlign: isMobileView ? 'left' : 'right' }}>
                        <Space direction={isMobileView ? 'horizontal' : 'vertical'} size="small">
                          {/* 会议状态 */}
                          <Tag color={meetingStatus.color} style={{ fontSize: '12px' }}>
                            {meetingStatus.text}
                          </Tag>
                          {/* 倒计时（仅在最近即将开始的场次显示） */}
                          {countdown && shouldShowCountdown && (
                            <Tag icon={<ClockCircleOutlined />} color="blue" style={{ fontSize: '12px' }}>
                              {countdown.days > 0 && `${countdown.days}${t('meetingHost.timeUnits.days')}`}
                              {countdown.hours > 0 && `${countdown.hours}${t('meetingHost.timeUnits.hoursShort')}`}
                              {countdown.minutes > 0 && `${countdown.minutes}${t('meetingHost.timeUnits.minutesShort')}`}
                              {countdown.seconds}{t('meetingHost.timeUnits.seconds')}{t('meetingHost.timeUnits.after')}
                            </Tag>
                          )}
                        </Space>
                      </Col>
                    </Row>
                  </div>
                );
              });
            })()}
          </Card>
        )}

        {/* 会议报告区域 */}
        {meeting && (
          <div style={{ marginBottom: 16 }}>
            <MeetingReportViewer
              meeting={{
                meetingUuid: meeting.meetingUuid,
                zoomMeetingUuid: meeting.zoomMeetingUuid,
                zoomMeetingId: meeting.zoomMeetingId,
                topic: meeting.topic
              }}
              isMobileView={isMobileView}
              compact={true}
            />
          </div>
        )}

        {/* 使用提示 */}
        <Card size="small" style={{ backgroundColor: '#fafafa' }}>
          <Title level={5}>{t('meetingHost.hostUsageTips')}</Title>
          <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
            <li>{t('meetingHost.usageTips.tip1')}</li>
            <li>{t('meetingHost.usageTips.tip2')}</li>
            <li>{t('meetingHost.usageTips.tip3')}</li>
            <li>{t('meetingHost.usageTips.tip4')}</li>
            <li>{t('meetingHost.usageTips.tip5')}</li>
          </ul>
        </Card>

        {/* 复制信息模态框 */}
        <Modal
          title={t('meetingHost.meetingInvitationInfo')}
          open={copyModalVisible}
          onCancel={() => setCopyModalVisible(false)}
          footer={[
            <Button key="copy" type="primary" onClick={copyToClipboard}>
              复制到剪贴板
            </Button>,
            <Button key="close" onClick={() => setCopyModalVisible(false)}>
              关闭
            </Button>,
          ]}
          width={600}
        >
          <div style={{ whiteSpace: 'pre-line', fontFamily: 'monospace', backgroundColor: '#f5f5f5', padding: 16, borderRadius: 4 }}>
            {copyText}
          </div>
        </Modal>
      </Card>

      {/* 微信引导遮罩 */}
      {showWechatGuide && <WechatGuide />}
    </div>
  );
};

export default MeetingHost;
