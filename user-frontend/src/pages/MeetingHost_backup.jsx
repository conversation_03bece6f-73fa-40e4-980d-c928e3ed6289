import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Card,
  Button,
  message,
  Alert,
  Typography,
  Row,
  Col,
  Divider,
  Modal,
  Result,
  Tag,
  Space,
  Spin
} from 'antd';
import {
  PlayCircleOutlined,
  CopyOutlined,
  InfoCircleOutlined,
  VideoCameraOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { meetingApi } from '../services/api';
import { copyWithMessage } from '../utils/copyUtils';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const { Title, Text } = Typography;

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

const MeetingHost = () => {
  const { meetingUuid } = useParams();
  const [meeting, setMeeting] = useState(null);
  const [meetingDetails, setMeetingDetails] = useState([]);
  const [loading, setLoading] = useState(true);
  const [countdown, setCountdown] = useState({});
  const [currentTime, setCurrentTime] = useState(dayjs());
  const [copyModalVisible, setCopyModalVisible] = useState(false);
  const [copyText, setCopyText] = useState('');
  const [isMobileView, setIsMobileView] = useState(isMobile());

  // 加载会议信息
  const loadMeetingInfo = async () => {
    try {
      setLoading(true);
      const response = await meetingApi.getMeetingByUuid(meetingUuid);
      setMeeting(response.data);

      // 获取会议详情（场次信息）
      const detailsResponse = await meetingApi.getZoomMeetingDetails(response.data.id);
      setMeetingDetails(detailsResponse.data || []);
    } catch (error) {
      console.error('加载会议信息失败:', error);
      message.error('会议不存在或已被删除');
    } finally {
      setLoading(false);
    }
  };

  // 获取复制信息（参会邀请）
  const handleGetCopyText = async () => {
    try {
      if (!meeting) return;

      const invitationText = `Zoom会议邀请

会议主题：${meeting.topic}
会议号：${meeting.zoomMeetingId}
会议密码：${meeting.password || '无'}

加入链接：${meeting.joinUrl}

会议时间：${dayjs(meeting.startTime).format('YYYY年MM月DD日 HH:mm')}

主持人信息：
主持人密钥：${meeting.hostKey || '无'}
主持人链接：请通过主持人页面获取最新链接

---
ZoomBus 会议服务`;

      setCopyText(invitationText);
      setCopyModalVisible(true);
    } catch (error) {
      console.error('获取复制信息失败:', error);
      message.error('获取复制信息失败');
    }
  };

  // 复制到剪贴板
  const copyToClipboard = async () => {
    const success = await copyWithMessage(copyText, message, '已复制到剪贴板', '复制失败，请手动复制');
    if (success) {
      setCopyModalVisible(false);
    }
  };

  // 开始会议
  const handleStartMeeting = async () => {
    try {
      const response = await meetingApi.getLatestHostUrl(meetingUuid);
      if (response.data.success && response.data.hostUrl) {
        window.open(response.data.hostUrl, '_blank');
        message.success('正在跳转到主持人链接...');
      } else {
        message.error('获取主持人链接失败');
      }
    } catch (error) {
      console.error('开始会议失败:', error);
      message.error('开始会议失败，请稍后重试');
    }
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 计算倒计时
  const calculateCountdown = (startTime) => {
    const now = currentTime;
    const start = dayjs(startTime);
    const diff = start.diff(now);
    
    if (diff <= 0) {
      return null; // 已开始或已过期
    }
    
    const duration = dayjs.duration(diff);
    return {
      days: Math.floor(duration.asDays()),
      hours: duration.hours(),
      minutes: duration.minutes(),
      seconds: duration.seconds()
    };
  };

  // 判断是否在会议时间窗口内
  const isInMeetingWindow = (startTime, durationMinutes) => {
    const now = currentTime;
    const start = dayjs(startTime);
    const end = start.add(durationMinutes || 60, 'minute');
    return now.isAfter(start) && now.isBefore(end);
  };

  // 获取最近的场次
  const getUpcomingOccurrence = () => {
    if (!meetingDetails.length) return null;
    
    const now = currentTime;
    const upcoming = meetingDetails
      .filter(detail => dayjs(detail.occurrenceStartTime || detail.startTime).isAfter(now))
      .sort((a, b) => dayjs(a.occurrenceStartTime || a.startTime).diff(dayjs(b.occurrenceStartTime || b.startTime)));
    
    return upcoming[0] || null;
  };

  // 更新时间和倒计时
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(dayjs());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (meetingUuid) {
      loadMeetingInfo();
    }
  }, [meetingUuid]);

  if (loading) {
    return (
      <div className="pmi-container">
        <Card className="pmi-card">
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>正在加载会议信息...</div>
          </div>
        </Card>
      </div>
    );
  }

  if (!meeting) {
    return (
      <div className="pmi-container">
        <Card className="pmi-card">
          <div className="pmi-header">
            <div className="pmi-logo">
              <VideoCameraOutlined /> ZoomBus
            </div>
            <div className="pmi-subtitle">会议主持人页面</div>
          </div>
          <Result
            status="404"
            title="会议不存在"
            subTitle="请检查链接是否正确，或联系管理员"
          />
        </Card>
      </div>
    );
  }

  const upcomingOccurrence = getUpcomingOccurrence();
  const nextCountdown = upcomingOccurrence ? calculateCountdown(upcomingOccurrence.occurrenceStartTime || upcomingOccurrence.startTime) : null;
  const hasUpcomingMeeting = upcomingOccurrence !== null;
  const isInCurrentWindow = meetingDetails.some(detail => {
    const startTime = dayjs(detail.occurrenceStartTime || detail.startTime);
    return isInMeetingWindow(startTime, meeting.durationMinutes);
  });

  return (
    <div className="pmi-container host-page">
      <Card className="pmi-card">
        {/* 页面头部 */}
        <div className="pmi-header">
          <div className="pmi-logo">
            <UserOutlined /> ZoomBus 主持人
          </div>
          <div className="pmi-subtitle">会议主持控制台</div>
        </div>

        {/* 会议基本信息 */}
        <Card
          title={
            <Space>
              <InfoCircleOutlined />
              会议信息
            </Space>
          }
          style={{ marginBottom: isMobileView ? 16 : 24 }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24}>
              <div style={{ marginBottom: 16 }}>
                <Text strong style={{ fontSize: isMobileView ? '16px' : '18px' }}>会议主题：</Text>
                <br />
                <Title level={isMobileView ? 4 : 3} style={{ margin: '8px 0', color: '#1890ff' }}>
                  {meeting.topic}
                </Title>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <div style={{ position: 'relative' }}>
                <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>会议号：</Text>
                <br />
                <Text copyable style={{
                  fontSize: isMobileView ? 16 : 18,
                  color: '#1890ff',
                  wordBreak: 'break-all'
                }}>
                  {meeting.zoomMeetingId}
                </Text>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <div style={{ position: 'relative' }}>
                <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>会议密码：</Text>
                <br />
                <Text copyable style={{
                  fontSize: isMobileView ? 16 : 18,
                  color: '#1890ff',
                  wordBreak: 'break-all'
                }}>
                  {meeting.password || '无'}
                </Text>
                {/* 获取复制信息按钮 */}
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={handleGetCopyText}
                  style={{
                    position: 'absolute',
                    bottom: -8,
                    right: 0,
                    fontSize: '12px',
                    height: '24px',
                    padding: '0 8px'
                  }}
                >
                  复制邀请
                </Button>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>主持人密钥：</Text>
              <br />
              <Text copyable style={{
                fontSize: isMobileView ? 16 : 18,
                color: '#ff7a00',
                wordBreak: 'break-all'
              }}>
                {meeting.hostKey || '无'}
              </Text>
            </Col>
            <Col xs={24} sm={12}>
              <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>创建时间：</Text>
              <br />
              <Text style={{ fontSize: isMobileView ? 14 : 16 }}>
                {dayjs(meeting.createdAt).format('YYYY年MM月DD日 HH:mm')}
              </Text>
            </Col>
          </Row>

          <Divider />

          {/* 会议状态和操作 */}
          {isInCurrentWindow ? (
            <Alert
              message="会议进行中"
              description="您可以点击下方按钮开始主持会议"
              type="success"
              showIcon
              style={{ marginBottom: 24 }}
            />
          ) : hasUpcomingMeeting ? (
            <Alert
              message="即将开始的会议"
              description={
                <div>
                  <p><strong>下次会议时间：</strong>{dayjs(upcomingOccurrence.occurrenceStartTime || upcomingOccurrence.startTime).format('YYYY年MM月DD日 HH:mm')}</p>
                  {nextCountdown && (
                    <p><strong>距离开始：</strong>
                      {nextCountdown.days > 0 && `${nextCountdown.days}天`}
                      {nextCountdown.hours > 0 && `${nextCountdown.hours}小时`}
                      {nextCountdown.minutes > 0 && `${nextCountdown.minutes}分钟`}
                      {nextCountdown.seconds}秒
                    </p>
                  )}
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
          ) : (
            <Alert
              message="暂无安排的会议"
              description="当前没有安排的会议场次"
              type="warning"
              showIcon
              style={{ marginBottom: 24 }}
            />
          )}

          {/* 主持会议按钮 */}
          <Button
            type="primary"
            size="large"
            block
            icon={<PlayCircleOutlined />}
            onClick={handleStartMeeting}
            disabled={!hasUpcomingMeeting && !isInCurrentWindow}
            style={{
              height: isMobileView ? 48 : 56,
              fontSize: isMobileView ? '16px' : '18px',
              fontWeight: 'bold'
            }}
          >
            {isInCurrentWindow ? '开始主持会议 Start Hosting' : hasUpcomingMeeting ? '获取主持人链接 Get Host Link' : '暂无可主持的会议'}
          </Button>
        </Card>

        {/* 会议场次详情 */}
        {meetingDetails.length > 0 && (
          <Card
            title={
              <Space>
                <ClockCircleOutlined />
                会议场次安排
              </Space>
            }
            size="small"
            style={{ marginBottom: 16 }}
          >
            {meetingDetails.map((detail, index) => {
              const startTime = dayjs(detail.occurrenceStartTime || detail.startTime);
              const endTime = startTime.add(meeting.durationMinutes || 60, 'minute');
              const isInWindow = isInMeetingWindow(startTime, meeting.durationMinutes);
              const countdown = calculateCountdown(startTime);

              return (
                <div key={index} style={{
                  padding: '12px 0',
                  borderBottom: index < meetingDetails.length - 1 ? '1px solid #f0f0f0' : 'none'
                }}>
                  <Row gutter={16} align="middle">
                    <Col xs={24} sm={16}>
                      <div>
                        <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>
                          {startTime.format('YYYY年MM月DD日 HH:mm')}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: isMobileView ? '12px' : '14px' }}>
                          结束时间：{endTime.format('HH:mm')}
                        </Text>
                      </div>
                    </Col>
                    <Col xs={24} sm={8} style={{ textAlign: isMobileView ? 'left' : 'right' }}>
                      <Space direction={isMobileView ? 'horizontal' : 'vertical'} size="small">
                        {countdown && (
                          <Tag icon={<ClockCircleOutlined />} color="blue" style={{ fontSize: '12px' }}>
                            {countdown.days > 0 && `${countdown.days}天`}
                            {countdown.hours > 0 && `${countdown.hours}时`}
                            {countdown.minutes > 0 && `${countdown.minutes}分`}
                            {countdown.seconds}秒后
                          </Tag>
                        )}
                        {isInWindow && <Tag color="green">进行中</Tag>}
                      </Space>
                    </Col>
                  </Row>
                </div>
              );
            })}
          </Card>
        )}

        {/* 使用提示 */}
        <Card size="small" style={{ backgroundColor: '#fafafa' }}>
          <Title level={5}>主持人使用提示：</Title>
          <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
            <li>点击"开始主持会议"按钮获取最新的主持人链接</li>
            <li>主持人密钥用于会议中的主持权转移</li>
            <li>复制邀请信息分享给参会者，包含会议号、密码和加入链接</li>
            <li>主持人链接仅供主持人使用，请勿分享给参会者</li>
            <li>如遇问题，请联系技术支持</li>
          </ul>
        </Card>

        {/* 复制信息模态框 */}
        <Modal
          title="会议邀请信息"
          open={copyModalVisible}
          onCancel={() => setCopyModalVisible(false)}
          footer={[
            <Button key="copy" type="primary" onClick={copyToClipboard}>
              复制到剪贴板
            </Button>,
            <Button key="close" onClick={() => setCopyModalVisible(false)}>
              关闭
            </Button>,
          ]}
          width={600}
        >
          <div style={{ whiteSpace: 'pre-line', fontFamily: 'monospace', backgroundColor: '#f5f5f5', padding: 16, borderRadius: 4 }}>
            {copyText}
          </div>
        </Modal>
      </Card>
    </div>
  );
};

export default MeetingHost;
