import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Button, message, Spin, Empty, Select, DatePicker } from 'antd';
import { useParams } from 'react-router-dom';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  UserOutlined,
  TrophyOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import moment from 'moment';
import axios from 'axios';
import PmiStatsCard from '../components/PmiStatsCard.jsx';
import PmiTrendsChart from '../components/PmiTrendsChart.jsx';
import PmiMeetingList from '../components/PmiMeetingList.jsx';

const { RangePicker } = DatePicker;
const { Option } = Select;

const MyPmiReport = () => {
  const { pmiNumber } = useParams(); // 从URL参数获取PMI号码
  const [loading, setLoading] = useState(false);
  const [userPmis, setUserPmis] = useState([]);
  const [selectedPmi, setSelectedPmi] = useState(pmiNumber || null);
  const [pmiDetails, setPmiDetails] = useState(null);
  const [pmiMeetings, setPmiMeetings] = useState([]);
  const [trendsData, setTrendsData] = useState([]);
  const [dateRange, setDateRange] = useState([moment().subtract(30, 'days'), moment()]);
  const [trendDays, setTrendDays] = useState(30);

  // 获取用户的PMI列表
  const fetchUserPmis = async () => {
    try {
      setLoading(true);
      // 这里需要根据实际API调整，获取当前用户的PMI列表
      const response = await axios.get('/api/user/pmis');
      setUserPmis(response.data);
      
      // 如果有PMI，默认选择第一个
      if (response.data.length > 0) {
        const firstPmi = response.data[0].pmiNumber;
        setSelectedPmi(firstPmi);
        await fetchPmiDetails(firstPmi);
      }
    } catch (error) {
      message.error('获取PMI列表失败');
      console.error('Error fetching user PMIs:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取特定PMI详情
  const fetchPmiDetails = async (pmiNumber) => {
    if (!pmiNumber) return;
    
    try {
      setLoading(true);
      const response = await axios.get(`/api/pmi-reports/${pmiNumber}`);
      setPmiDetails(response.data);
      
      // 获取PMI会议列表
      const meetingsResponse = await axios.get(`/api/pmi-reports/${pmiNumber}/meetings?page=0&size=50`);
      setPmiMeetings(meetingsResponse.data.content || []);
      
      // 获取趋势数据
      const trendsResponse = await axios.get(`/api/pmi-reports/trends?days=${trendDays}&pmiNumber=${pmiNumber}`);
      setTrendsData(trendsResponse.data.trends || []);
    } catch (error) {
      message.error('获取PMI详情失败');
      console.error('Error fetching PMI details:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    if (selectedPmi) {
      fetchPmiDetails(selectedPmi);
    } else {
      fetchUserPmis();
    }
  };

  // 导出报告
  const handleExport = async () => {
    if (!selectedPmi) {
      message.warning('请先选择一个PMI');
      return;
    }
    
    try {
      const response = await axios.get(`/api/pmi-reports/${selectedPmi}/export`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `PMI_${selectedPmi}_报告_${moment().format('YYYY-MM-DD')}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      message.success('报告导出成功');
    } catch (error) {
      message.error('导出报告失败');
      console.error('Error exporting report:', error);
    }
  };

  useEffect(() => {
    // 如果URL中有PMI号码，直接获取该PMI的详情
    if (pmiNumber) {
      setSelectedPmi(pmiNumber);
      fetchPmiDetails(pmiNumber);
    } else {
      fetchUserPmis();
    }
  }, [pmiNumber]);

  useEffect(() => {
    if (selectedPmi) {
      fetchPmiDetails(selectedPmi);
    }
  }, [selectedPmi, trendDays]);

  // 如果没有PMI
  if (!loading && userPmis.length === 0) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Empty 
          description="您还没有PMI会议室"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" onClick={() => window.location.href = '/pmi/apply'}>
            申请PMI会议室
          </Button>
        </Empty>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '24px',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h1 style={{ margin: 0 }}>我的PMI使用报告</h1>
        
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          <Select
            value={selectedPmi}
            onChange={setSelectedPmi}
            style={{ minWidth: '200px' }}
            placeholder="选择PMI"
            loading={loading}
          >
            {userPmis.map(pmi => (
              <Option key={pmi.pmiNumber} value={pmi.pmiNumber}>
                {pmi.pmiNumber} - {pmi.topic || '未命名'}
              </Option>
            ))}
          </Select>
          
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
          
          <Button 
            type="primary"
            icon={<DownloadOutlined />} 
            onClick={handleExport}
            disabled={!selectedPmi}
          >
            导出报告
          </Button>
        </div>
      </div>

      <Spin spinning={loading}>
        {pmiDetails ? (
          <>
            {/* 统计概览 */}
            <Card title="使用统计" style={{ marginBottom: '24px' }}>
              <PmiStatsCard stats={pmiDetails.stats} />
            </Card>

            {/* 使用趋势 */}
            <div style={{ marginBottom: '24px' }}>
              <PmiTrendsChart 
                data={trendsData}
                loading={loading}
                days={trendDays}
                onDaysChange={setTrendDays}
              />
            </div>

            {/* 会议记录 */}
            <Card title="会议记录" style={{ marginBottom: '24px' }}>
              <PmiMeetingList 
                meetings={pmiMeetings}
                loading={loading}
                pagination={{ 
                  pageSize: 20,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }}
              />
            </Card>

            {/* 使用建议 */}
            {pmiDetails.stats && (
              <Card title="使用建议" style={{ marginBottom: '24px' }}>
                <Row gutter={16}>
                  <Col xs={24} md={12}>
                    <div style={{ padding: '16px', background: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '6px' }}>
                      <h4 style={{ color: '#52c41a', marginBottom: '8px' }}>
                        <TrophyOutlined /> 使用效率
                      </h4>
                      <p style={{ margin: 0 }}>
                        {pmiDetails.stats.avgDurationMinutes > 60 
                          ? '您的会议平均时长较长，建议合理安排会议议程，提高会议效率。'
                          : '您的会议时长控制得很好，继续保持高效的会议习惯。'
                        }
                      </p>
                    </div>
                  </Col>
                  <Col xs={24} md={12}>
                    <div style={{ padding: '16px', background: '#fff7e6', border: '1px solid #ffd591', borderRadius: '6px' }}>
                      <h4 style={{ color: '#fa8c16', marginBottom: '8px' }}>
                        <UserOutlined /> 参与度分析
                      </h4>
                      <p style={{ margin: 0 }}>
                        {pmiDetails.stats.avgParticipants > 5 
                          ? '您的会议参与人数较多，建议提前准备会议材料，确保会议顺利进行。'
                          : '小型会议有利于深度讨论，建议充分利用这个优势。'
                        }
                      </p>
                    </div>
                  </Col>
                </Row>
              </Card>
            )}
          </>
        ) : (
          <Card>
            <Empty description="请选择一个PMI查看详细报告" />
          </Card>
        )}
      </Spin>
    </div>
  );
};

export default MyPmiReport;
