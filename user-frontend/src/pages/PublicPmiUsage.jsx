import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Button,
  message,
  Alert,
  Typography,
  Row,
  Col,
  Divider,
  Modal,
  Result,
  Tag,
  Space,
  Spin
} from 'antd';
import {
  PlayCircleOutlined,
  InfoCircleOutlined,
  CopyOutlined
} from '@ant-design/icons';
import { publicPmiApi } from '../services/api';
import { formatPmiInfo } from '../utils/copyUtils';

import { ResponsiveLanguageSelector } from '../components/LanguageSelector';
import { shouldShowBrowserGuide } from '../utils/wechatDetection';
import WechatGuide from '../components/WechatGuide';



const { Title, Text } = Typography;

// 自定义ZoomBus图标组件
const ZoomBusIcon = ({ style = {} }) => (
  <img
    src="/images/zoombus_logo_s.png"
    alt="ZoomBus"
    style={{
      height: '1em',
      display: 'inline-block',
      verticalAlign: 'middle',
      ...style
    }}
  />
);

// 检测是否为移动设备
const isMobile = () => window.innerWidth <= 768;

// 格式化时长为小时分钟格式
const formatDuration = (minutes, t) => {
  if (!minutes || minutes <= 0) return `0${t('time.minutes')}`;

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours === 0) {
    return `${remainingMinutes}${t('time.minutes')}`;
  } else if (remainingMinutes === 0) {
    return `${hours}${t('time.hours')}`;
  } else {
    return `${hours}${t('time.hours')}${remainingMinutes}${t('time.minutes')}`;
  }
};

// 计算会议持续时长
const calculateMeetingDuration = (startTime, t) => {
  if (!startTime) return t('pmiUsage.messages.unknown');

  const start = new Date(startTime);
  const now = new Date();
  const diffMs = now - start;
  const diffMinutes = Math.floor(diffMs / (1000 * 60));

  if (diffMinutes < 1) return t('pmiUsage.messages.justStarted');

  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;

  if (hours === 0) {
    return `${minutes}${t('time.minutes')}`;
  } else if (minutes === 0) {
    return `${hours}${t('time.hours')}`;
  } else {
    return `${hours}${t('time.hours')}${minutes}${t('time.minutes')}`;
  }
};

const PublicPmiUsage = () => {
  const { pmiNumber: magicId } = useParams(); // URL参数实际上是magicId，为了保持兼容性仍命名为pmiNumber
  const { t } = useTranslation();
  const [pmiInfo, setPmiInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activating, setActivating] = useState(false);

  const [isMobileView, setIsMobileView] = useState(isMobile());
  const [refreshInterval, setRefreshInterval] = useState(null);
  const [isRedirecting, setIsRedirecting] = useState(false); // 标记是否正在回退跳转
  const [copyModalVisible, setCopyModalVisible] = useState(false);
  const [copyText, setCopyText] = useState('');


  // 检测是否需要显示微信引导遮罩
  const showWechatGuide = shouldShowBrowserGuide();

  // 页面加载时，如果URL中有魔链ID，自动加载会议室信息
  useEffect(() => {
    if (magicId) {
      loadPmiInfo(magicId);
    }
  }, [magicId]);

  // 设置定时刷新
  useEffect(() => {
    // 清除之前的定时器
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }

    // 如果有活跃会议，设置每10秒刷新一次
    if (pmiInfo?.hasActiveMeeting) {
      const interval = setInterval(() => {
        loadPmiInfo(magicId);
      }, 10000); // 10秒刷新一次

      setRefreshInterval(interval);
    }

    // 组件卸载时清除定时器
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [pmiInfo?.hasActiveMeeting, magicId]);

  // 加载会议室信息
  const loadPmiInfo = async (magicIdParam) => {
    if (!magicIdParam || magicIdParam.length < 3) {
      message.error(t('pmiUsage.errors.invalidRoomNumber'));
      return;
    }

    try {
      setLoading(true);
      const response = await publicPmiApi.getPmiInfo(magicIdParam);

      if (response.data && response.data.success) {
        const pmiData = response.data.data;

        // 检查是否需要回退到老系统
        if (pmiData.fallbackEnabled && pmiData.fallbackUrl) {
          console.log('PMI需要回退到老系统:', pmiData.fallbackUrl);

          // 设置回退跳转状态，避免显示404页面
          setIsRedirecting(true);

          // 显示跳转提示
          message.info(t('pmiUsage.messages.redirectingToOldSystem', '正在跳转到备用系统...'), 2);

          // 延迟跳转，让用户看到提示
          setTimeout(() => {
            window.location.href = pmiData.fallbackUrl;
          }, 2000);

          return; // 不继续处理，等待跳转
        }

        setPmiInfo(pmiData);
      } else {
        message.error(response.data.message || t('pmiUsage.errors.roomNotExists'));
        setPmiInfo(null);
        setIsRedirecting(false); // 重置回退状态
      }
    } catch (error) {
      console.error('Error loading PMI info:', error);
      if (error.response?.status === 404) {
        message.error(t('pmiUsage.errors.checkRoomNumberCorrect'));
      } else {
        message.error(t('pmiUsage.errors.loadRoomInfoFailed') + ': ' + (error.response?.data?.message || error.message));
      }
      setPmiInfo(null);
      setIsRedirecting(false); // 重置回退状态
    } finally {
      setLoading(false);
    }
  };

  // 一键开启会议室
  const handleActivatePmi = async () => {
    try {
      setActivating(true);
      const response = await publicPmiApi.activatePmi(magicId);

      message.success(t('pmiUsage.errors.activateSuccess'));

      // 跳转到主持人链接
      if (response.data.data?.hostUrl) {
        window.open(response.data.data.hostUrl, '_blank');
      }

      // 开启成功后立即刷新页面数据
      setTimeout(() => {
        loadPmiInfo(magicId);
      }, 1000); // 延迟1秒刷新，确保后端状态已更新

    } catch (error) {
      message.error(t('pmiUsage.errors.activateFailed') + ': ' + (error.response?.data?.message || error.message));
      console.error('Error activating PMI:', error);
    } finally {
      setActivating(false);
    }
  };

  // 获取复制信息
  const handleGetCopyText = () => {
    if (pmiInfo) {
      const formattedText = formatPmiInfo(pmiInfo, t);
      setCopyText(formattedText);
      setCopyModalVisible(true);
    } else {
      message.error(t('pmiUsage.errors.roomInfoIncomplete'));
    }
  };

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(copyText);
      message.success(t('common.copied'));
      setCopyModalVisible(false);
    } catch (error) {
      message.error(t('pmiUsage.errors.copyFailed'));
    }
  };

  // 结束会议
  const handleEndMeeting = async () => {
    if (!pmiInfo?.activeMeetingId) {
      message.error(t('pmiUsage.errors.noActiveMeeting'));
      return;
    }

    // 使用浏览器原生确认对话框
    const confirmed = window.confirm(t('pmiUsage.errors.confirmEndMeeting'));

    if (confirmed) {
      try {
        await publicPmiApi.endMeeting(pmiInfo.activeMeetingId);
        message.success(t('pmiUsage.errors.meetingEndSuccess'));
        // 重新加载PMI信息以更新状态
        loadPmiInfo(magicId);
      } catch (error) {
        message.error(t('pmiUsage.errors.endMeetingFailed') + ': ' + (error.response?.data?.message || error.message));
        console.error('Error ending meeting:', error);
      }
    }
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="pmi-container" style={{ position: 'relative' }}>
      {/* 响应式语言选择器 */}
      <ResponsiveLanguageSelector
        mobilePosition="topRight"
        desktopPosition="topRight"
      />

      <Card className="pmi-card">
        {/* 页面头部 */}
        <div className="pmi-header">
          <div className="pmi-logo">
            <ZoomBusIcon /> ZoomBus
          </div>
          <div className="pmi-subtitle">{t('pmiUsage.subtitle')}</div>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>{t('common.loading')}</div>
          </div>
        )}

        {/* 会议室信息展示 */}
        {pmiInfo && (
          <Card 
            title={
              <Space>
                <InfoCircleOutlined />
                {t('meetingHost.meetingInfo')}
              </Space>
            }
            style={{ marginBottom: isMobileView ? 16 : 24 }}
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <div style={{ position: 'relative' }}>
                  <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>{t('pmiUsage.pmiNumber')}：</Text>
                  <br />
                  <Text copyable style={{
                    fontSize: isMobileView ? 16 : 18,
                    color: '#1890ff',
                    wordBreak: 'break-all'
                  }}>
                    {pmiInfo.pmiNumber}
                  </Text>

                </div>
              </Col>
              {pmiInfo.pmiPassword && (
                <Col xs={24} sm={12}>
                  <div style={{ position: 'relative' }}>
                    <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>{t('pmiUsage.pmiPassword')}：</Text>
                    <br />
                    <Text copyable style={{
                      fontSize: isMobileView ? 16 : 18,
                      color: '#1890ff',
                      wordBreak: 'break-all'
                    }}>
                      {pmiInfo.pmiPassword}
                    </Text>

                  </div>
                </Col>
              )}
              <Col xs={24} sm={12}>
                <Text strong style={{ fontSize: isMobileView ? '14px' : '16px' }}>{t('pmiUsage.currentStatus')}：</Text>
                <Tag color={pmiInfo.status === 'ACTIVE' ? 'green' : pmiInfo.status === 'INACTIVE' ? 'orange' : 'red'} style={{ marginLeft: 8, whiteSpace: 'nowrap' }}>
                  {pmiInfo.statusDescription || pmiInfo.status}
                </Tag>
              </Col>
              <Col xs={24} sm={12} style={{ textAlign: isMobileView ? 'left' : 'right', marginTop: isMobileView ? 8 : 0 }}>
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={handleGetCopyText}
                  style={{
                    fontSize: '12px',
                    height: '28px'
                  }}
                >
                  {t('pmiUsage.getCopyInfo')}
                </Button>
              </Col>
            </Row>

            <Divider />

            {/* 根据会议室状态和计费模式显示不同的提示信息 */}
            {pmiInfo.canActivate || pmiInfo.hasActiveMeeting ? (
              <>
                {/* 会议进行中时的显示 */}
                {pmiInfo.hasActiveMeeting && (
                  <Alert
                    message={t('pmiUsage.meetingInProgress')}
                    description={
                      <div>
                        <p><strong>{t('pmiUsage.meetingDuration')}：</strong>{calculateMeetingDuration(pmiInfo.activeMeetingStartTime, t)}</p>
                        {pmiInfo.billingMode === 'BY_TIME' && (
                          <p><strong>{t('pmiUsage.remainingTime')}：</strong>{
                            pmiInfo.remainingMinutes !== undefined
                              ? formatDuration(pmiInfo.remainingMinutes, t)
                              : formatDuration(Math.max(0, (pmiInfo.availableMinutes || 0) - (pmiInfo.pendingDeductMinutes || 0)), t)
                          }</p>
                        )}
                        {pmiInfo.billingMode === 'LONG' && pmiInfo.windowExpireTime && (
                          <p><strong>{t('pmiUsage.validUntil')}：</strong>{new Date(pmiInfo.windowExpireTime).toLocaleString()}</p>
                        )}
                      </div>
                    }
                    type="success"
                    showIcon
                    style={{ marginBottom: 24 }}
                  />
                )}

                {/* 会议未开始时的显示 */}
                {!pmiInfo.hasActiveMeeting && (
                  <>
                    {pmiInfo.billingMode === 'BY_TIME' && (
                      <Alert
                        description={
                          <div>
                            <p><strong>{t('pmiUsage.remainingTime')}：</strong>{
                              pmiInfo.remainingMinutes !== undefined
                                ? formatDuration(pmiInfo.remainingMinutes, t)
                                : formatDuration(Math.max(0, (pmiInfo.availableMinutes || 0) - (pmiInfo.pendingDeductMinutes || 0)), t)
                            }</p>
                            {pmiInfo.overdraftMinutes > 0 && (
                              <p style={{ color: '#ff4d4f' }}><strong>{t('pmiUsage.overdraftTime')}：</strong>{pmiInfo.overdraftMinutes} {t('time.minutes')}</p>
                            )}
                          </div>
                        }
                        type="info"
                        showIcon
                        style={{ marginBottom: 24 }}
                      />
                    )}
                    {pmiInfo.billingMode === 'LONG' && (
                      <Alert
                        description={
                          <div>
                            {pmiInfo.windowExpireTime ? (
                              <p><strong>{t('pmiUsage.validUntil')}：</strong>{new Date(pmiInfo.windowExpireTime).toLocaleString()}</p>
                            ) : (
                              <p style={{ color: '#ff4d4f' }}>{t('pmiUsage.noValidWindow')}</p>
                            )}
                          </div>
                        }
                        type="info"
                        showIcon
                        style={{ marginBottom: 24 }}
                      />
                    )}
                  </>
                )}
              </>
            ) : (
              <Alert
                message={t('pmiUsage.roomStatus')}
                description={pmiInfo.statusMessage || t('pmiUsage.roomUnavailable')}
                type="warning"
                showIcon
                style={{ marginBottom: 24 }}
              />
            )}

            {/* 会议室操作按钮 */}
            {pmiInfo.hasActiveMeeting ? (
              // 会议进行中时显示进入会议和结束会议按钮
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={() => window.open(pmiInfo.hostUrl, '_blank')}
                  style={{
                    flex: 1,
                    height: isMobileView ? 48 : 56,
                    fontSize: isMobileView ? '14px' : '16px',
                    fontWeight: 'bold'
                  }}
                >
                  {t('pmiUsage.actions.joinMeeting')}
                </Button>
                <Button
                  type="default"
                  size="large"
                  danger
                  onClick={handleEndMeeting}
                  style={{
                    flex: 1,
                    height: isMobileView ? 48 : 56,
                    fontSize: isMobileView ? '14px' : '16px',
                    fontWeight: 'bold'
                  }}
                >
                  {t('pmiUsage.actions.endMeeting')}
                </Button>
              </div>
            ) : (
              // 会议未开启时显示开启按钮
              <Button
                type="primary"
                size="large"
                block
                icon={<PlayCircleOutlined />}
                loading={activating}
                onClick={handleActivatePmi}
                disabled={!pmiInfo.canActivate}
                style={{
                  height: isMobileView ? 48 : 56,
                  fontSize: isMobileView ? '16px' : '18px',
                  fontWeight: 'bold'
                }}
              >
                {pmiInfo.canActivate ? t('pmiUsage.actions.startInstantMeeting') : t('pmiUsage.statuses.unavailable')}
              </Button>
            )}


          </Card>
        )}

        {/* 会议室不存在 */}
        {!pmiInfo && !loading && !isRedirecting && magicId && (
          <Result
            status="404"
            title={t('pmiUsage.roomNotFound')}
            subTitle={t('pmiUsage.checkRoomNumber')}
          />
        )}

        {/* 回退跳转中的提示 */}
        {isRedirecting && (
          <Result
            icon={<Spin size="large" />}
            title={t('pmiUsage.messages.redirectingToOldSystem', '正在跳转到备用系统...')}
            subTitle={t('pmiUsage.messages.pleaseWait', '请稍候...')}
          />
        )}



        {/* 使用提示 */}
        <Card size="small" style={{ backgroundColor: '#fafafa' }}>
          <Title level={5}>{t('pmiUsage.usageTips')}：</Title>
          <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
            <li>{t('pmiUsage.tip1')}</li>
            <li>{t('pmiUsage.tip2')}</li>
            <li>{t('pmiUsage.tip3')}</li>
            <li>{t('pmiUsage.tip4')}</li>
          </ul>
        </Card>


      </Card>

      {/* 微信引导遮罩 */}
      {showWechatGuide && <WechatGuide />}

      {/* 复制信息模态框 */}
      <Modal
        title={t('pmiUsage.roomInfo')}
        open={copyModalVisible}
        onCancel={() => setCopyModalVisible(false)}
        footer={[
          <Button key="copy" type="primary" onClick={copyToClipboard}>
            {t('pmiUsage.copyToClipboard')}
          </Button>,
          <Button key="close" onClick={() => setCopyModalVisible(false)}>
            {t('pmiUsage.close')}
          </Button>,
        ]}
        width={600}
      >
        <div style={{ whiteSpace: 'pre-line', fontFamily: 'monospace', backgroundColor: '#f5f5f5', padding: 16, borderRadius: 4 }}>
          {copyText}
        </div>
      </Modal>
    </div>
  );
};

export default PublicPmiUsage;
