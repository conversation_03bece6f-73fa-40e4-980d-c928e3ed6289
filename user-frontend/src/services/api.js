import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.config.url, response.data);
    return response;
  },
  (error) => {
    console.error('Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// 公共PMI使用API
export const publicPmiApi = {
  // 获取PMI基本信息
  getPmiInfo: (pmiNumber) => api.get(`/public/pmi/${pmiNumber}`),

  // 一键开启PMI
  activatePmi: (pmiNumber) => api.post(`/public/pmi/${pmiNumber}/activate`),

  // 获取PMI复制信息
  getPmiCopyText: (pmiNumber) => api.get(`/public/pmi/${pmiNumber}/copy-text`),

  // 结束会议
  endMeeting: (meetingId) => api.post(`/zoom-meetings/${meetingId}/end`),
};

// 会议API
export const meetingApi = {
  // 根据UUID获取会议详情
  getMeetingByUuid: (meetingUuid) => api.get(`/meetings/uuid/${meetingUuid}`),

  // 获取会议详情列表（支持周期性会议的多个详情记录）
  getZoomMeetingDetails: (meetingId) => api.get(`/meetings/${meetingId}/zoom-details`),

  // 获取最新的主持人链接
  getLatestHostUrl: (meetingUuid) => api.post(`/meetings/uuid/${meetingUuid}/host-url`),
};

// Join Account Rental公共API
export const joinAccountRentalApi = {
  // 获取Token信息
  getTokenInfo: (tokenNumber) => api.get(`/public/join-account/tokens/${tokenNumber}`),

  // 预约Token
  reserveToken: (tokenNumber, params) => api.post(`/public/join-account/tokens/${tokenNumber}/reserve`, null, { params }),

  // 激活Token（获取账号）
  activateToken: (tokenNumber) => api.post(`/public/join-account/tokens/${tokenNumber}/activate`),

  // 获取Token状态
  getTokenStatus: (tokenNumber) => api.get(`/public/join-account/tokens/${tokenNumber}/status`),
};

// 会议报告API（用户端）
export const meetingReportApi = {
  // 根据UUID获取会议报告详情
  getReportByUuid: (zoomMeetingUuid) => api.get(`/meeting-reports/uuid/${zoomMeetingUuid}`),

  // 根据会议ID获取会议报告详情
  getReportByMeetingId: (zoomMeetingId) => api.get(`/meeting-reports/meeting-id/${zoomMeetingId}`),

  // 获取会议参会人员列表
  getParticipants: (reportId, params) => api.get(`/meeting-reports/${reportId}/participants`, { params }),

  // 检查会议报告是否存在
  checkReportExists: (zoomMeetingUuid) => api.get(`/meeting-reports/exists/${zoomMeetingUuid}`),

  // 手动触发会议报告获取（通过Zoom会议UUID）
  triggerReportFetch: (zoomMeetingUuid) => api.post(`/meeting-reports/fetch/${zoomMeetingUuid}`),

  // 手动触发会议报告获取（通过系统会议UUID）
  triggerReportFetchByMeetingUuid: (meetingUuid) => api.post(`/meeting-reports/fetch/meeting/${meetingUuid}`),
};

export default api;
