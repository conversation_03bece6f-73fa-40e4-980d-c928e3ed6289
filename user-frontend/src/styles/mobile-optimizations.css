/* 移动端优化样式 */

/* 触摸友好的按钮 */
@media (max-width: 768px) {
  .ant-btn {
    min-height: 44px; /* iOS推荐的最小触摸目标 */
    padding: 8px 16px;
  }
  
  .ant-btn-sm {
    min-height: 36px;
    padding: 6px 12px;
  }
  
  .ant-btn-lg {
    min-height: 48px;
    padding: 12px 20px;
  }
}

/* 表格移动端优化 */
@media (max-width: 768px) {
  .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
    font-weight: 600;
  }
  
  .ant-table-pagination {
    margin: 16px 0 8px 0;
  }
  
  .ant-pagination-simple .ant-pagination-simple-pager {
    margin: 0 8px;
  }
}

/* 卡片移动端优化 */
@media (max-width: 768px) {
  .ant-card {
    margin-bottom: 12px;
  }
  
  .ant-card-head {
    padding: 12px 16px;
    min-height: auto;
  }
  
  .ant-card-head-title {
    font-size: 14px;
    font-weight: 600;
  }
  
  .ant-card-body {
    padding: 12px 16px;
  }
  
  .ant-card-small .ant-card-head {
    padding: 8px 12px;
  }
  
  .ant-card-small .ant-card-body {
    padding: 8px 12px;
  }
}

/* 标签移动端优化 */
@media (max-width: 768px) {
  .ant-tag {
    margin: 2px 4px 2px 0;
    padding: 2px 6px;
    font-size: 10px;
    line-height: 16px;
  }
}

/* 徽章移动端优化 */
@media (max-width: 768px) {
  .ant-badge-status-text {
    font-size: 12px;
    margin-left: 6px;
  }
}

/* 统计数字移动端优化 */
@media (max-width: 768px) {
  .ant-statistic-title {
    font-size: 12px;
    margin-bottom: 4px;
  }
  
  .ant-statistic-content {
    font-size: 18px;
    line-height: 1.2;
  }
  
  .ant-statistic-content-prefix,
  .ant-statistic-content-suffix {
    font-size: 14px;
  }
}

/* 行和列移动端间距优化 */
@media (max-width: 768px) {
  .ant-row {
    margin-left: -8px;
    margin-right: -8px;
  }
  
  .ant-col {
    padding-left: 8px;
    padding-right: 8px;
  }
}

/* 空状态移动端优化 */
@media (max-width: 768px) {
  .ant-empty {
    margin: 16px 0;
  }
  
  .ant-empty-description {
    font-size: 12px;
    color: #999;
  }
  
  .ant-empty-image {
    height: 60px;
    margin-bottom: 8px;
  }
}

/* 加载状态移动端优化 */
@media (max-width: 768px) {
  .ant-spin-container {
    min-height: 120px;
  }
  
  .ant-spin-text {
    font-size: 12px;
    margin-top: 8px;
  }
}

/* 模态框移动端优化 */
@media (max-width: 768px) {
  .ant-modal {
    max-width: calc(100vw - 16px);
    margin: 8px;
  }
  
  .ant-modal-content {
    border-radius: 8px;
  }
  
  .ant-modal-header {
    padding: 16px 20px 12px;
  }
  
  .ant-modal-body {
    padding: 12px 20px 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
  }
  
  .ant-modal-footer {
    padding: 12px 20px 16px;
  }
}

/* 触摸滚动优化 */
@media (max-width: 768px) {
  .ant-table-body {
    -webkit-overflow-scrolling: touch;
  }
  
  .ant-modal-body {
    -webkit-overflow-scrolling: touch;
  }
}

/* 文本选择优化 */
@media (max-width: 768px) {
  .meeting-report-viewer {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  .meeting-report-viewer .selectable-text {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

/* 会议报告特定的移动端优化 */
@media (max-width: 768px) {
  .meeting-report-basic-info .ant-typography {
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 4px;
  }
  
  .meeting-report-features .ant-badge-status-text {
    font-size: 11px;
  }
  
  .meeting-report-participants .ant-card-extra {
    font-size: 12px;
  }
}

/* 响应式间距 */
@media (max-width: 768px) {
  .mobile-spacing-sm {
    margin-bottom: 8px;
  }
  
  .mobile-spacing-md {
    margin-bottom: 12px;
  }
  
  .mobile-spacing-lg {
    margin-bottom: 16px;
  }
}

/* 触摸反馈 */
@media (max-width: 768px) {
  .ant-btn:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
  
  .ant-card:active {
    transform: scale(0.995);
    transition: transform 0.1s ease;
  }
}
