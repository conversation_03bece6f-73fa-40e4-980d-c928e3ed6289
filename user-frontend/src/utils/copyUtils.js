/**
 * 通用复制工具类
 * 为用户前端提供统一的复制功能，包含错误处理和兼容性支持
 */

/**
 * 复制文本到剪贴板的通用方法
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 复制是否成功
 */
export const copyToClipboard = async (text) => {
  try {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级方案：使用传统的复制方法
      return fallbackCopyTextToClipboard(text);
    }
  } catch (error) {
    console.error('复制失败:', error);
    // 尝试降级方案
    return fallbackCopyTextToClipboard(text);
  }
};

/**
 * 降级复制方法（兼容旧浏览器）
 * @param {string} text - 要复制的文本
 * @returns {boolean} 复制是否成功
 */
const fallbackCopyTextToClipboard = (text) => {
  try {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 避免在页面上显示
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);
    
    return successful;
  } catch (error) {
    console.error('降级复制也失败:', error);
    return false;
  }
};

/**
 * 复制文本并显示反馈消息
 * @param {string} text - 要复制的文本
 * @param {Function} messageApi - Ant Design的message API
 * @param {string} successMessage - 成功消息
 * @param {string} errorMessage - 失败消息
 * @param {boolean} simplifyForMultiDay - 是否简化多天会议的iCalendar信息
 * @returns {Promise<boolean>} 复制是否成功
 */
export const copyWithMessage = async (text, messageApi, successMessage = '已复制到剪贴板', errorMessage = '复制失败，请手动复制', simplifyForMultiDay = false) => {
  let finalText = text;

  // 如果需要简化多天会议信息
  if (simplifyForMultiDay) {
    finalText = simplifyInvitationText(text);
  }

  const success = await copyToClipboard(finalText);

  if (success) {
    messageApi.success(successMessage);
  } else {
    messageApi.error(errorMessage);
  }

  return success;
};

/**
 * 简化邀请信息，移除iCalendar相关内容
 * @param {string} invitationText - 原始邀请信息
 * @returns {string} 简化后的邀请信息
 */
export const simplifyInvitationText = (invitationText) => {
  if (!invitationText) return invitationText;

  // 移除iCalendar相关的内容
  let simplified = invitationText;

  // 移除iCalendar文件下载链接（包括多行内容）
  simplified = simplified.replace(/下载iCalendar文件[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');
  simplified = simplified.replace(/Download iCalendar file[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');

  // 移除包含"或者，复制此URL到您的日历应用程序中"的段落
  simplified = simplified.replace(/或者，复制此URL到您的日历应用程序中：[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');
  simplified = simplified.replace(/Or copy this URL into your calendar application:[\s\S]*?(?=\n\n|\n[^\s]|\n$|$)/gi, '');

  // 移除iCalendar相关的URL（包括ics链接）
  simplified = simplified.replace(/https?:\/\/[^\s]*\.ics[^\s]*/gi, '');
  simplified = simplified.replace(/https?:\/\/[^\s]*icsToken[^\s]*/gi, '');

  // 移除包含"icalendar"、"ics"、"calendar"等关键词的行
  simplified = simplified.replace(/.*(?:icalendar|\.ics|calendar file|日历文件|icsToken).*\n?/gi, '');

  // 移除空行（只包含空白字符的行）
  simplified = simplified.replace(/^\s*\n/gm, '');

  // 移除多余的空行（超过2个连续换行符的情况）
  simplified = simplified.replace(/\n{3,}/g, '\n\n');

  // 移除开头和结尾的空白字符
  simplified = simplified.trim();

  return simplified;
};

/**
 * 格式化PMI会议室信息
 * @param {Object} pmiInfo - PMI信息对象
 * @param {boolean} removeICalendar - 是否移除iCalendar信息（默认false）
 * @returns {string} 格式化后的文本
 */
export const formatPmiInfo = (pmiInfo, removeICalendar = false) => {
  const {
    pmiNumber,
    pmiPassword,
    joinUrl,
    hostUrl,
    topic = '会议室',
    hasActiveMeeting = false
  } = pmiInfo;

  let formattedText = `${topic}

会议号：${pmiNumber}`;

  // 只有当密码不为空时才添加密码信息
  if (pmiPassword && pmiPassword.trim()) {
    formattedText += `
会议密码：${pmiPassword}`;
  }

  formattedText += `
加入链接：${joinUrl || '暂无'}`;

  if (hasActiveMeeting) {
    formattedText += `
主持人链接：${hostUrl || '暂无'}

会议状态：进行中`;
  } else {
    formattedText += `

会议状态：未开启`;
  }

  formattedText += `

使用说明：
1. 参会者可通过会议号和密码加入会议
2. 也可直接点击加入链接进入会议
3. 主持人请使用主持人链接开启会议`;

  // 如果需要移除iCalendar信息
  if (removeICalendar) {
    formattedText = simplifyInvitationText(formattedText);
  }

  return formattedText;
};

/**
 * 格式化Join Account账号信息
 * @param {Object} accountInfo - 账号信息对象
 * @param {string} timeRange - 时间范围
 * @returns {string} 格式化后的文本
 */
export const formatAccountInfo = (accountInfo, timeRange = '') => {
  const {
    email,
    password,
    accountName = '',
    description = ''
  } = accountInfo;

  let formattedText = `Zoom账号信息

邮箱：${email}
密码：${password}
登录地址：https://zoom.us/signin`;

  if (accountName) {
    formattedText += `
账号名称：${accountName}`;
  }

  if (timeRange) {
    formattedText += `
可用时间：${timeRange}`;
  }

  if (description) {
    formattedText += `
说明：${description}`;
  }

  formattedText += `

使用提示：
1. 请在指定时间段内使用此账号
2. 登录后即可创建和管理Zoom会议
3. 请妥善保管账号信息，勿与他人分享
4. 如遇问题请联系技术支持`;

  return formattedText;
};

/**
 * 格式化简短的会议信息（用于快速分享）
 * @param {Object} meetingInfo - 会议信息对象
 * @returns {string} 简短的会议信息
 */
export const formatShortMeetingInfo = (meetingInfo) => {
  const {
    topic = '会议',
    meetingId,
    password,
    joinUrl,
    startTime
  } = meetingInfo;

  let shortInfo = `${topic}`;
  
  if (startTime) {
    const time = new Date(startTime).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
    shortInfo += ` ${time}`;
  }
  
  if (meetingId) {
    shortInfo += ` ID:${meetingId}`;
  }
  
  if (password) {
    shortInfo += ` 密码:${password}`;
  }

  return shortInfo;
};

/**
 * 检查浏览器是否支持剪贴板API
 * @returns {boolean} 是否支持
 */
export const isClipboardSupported = () => {
  return !!(navigator.clipboard && window.isSecureContext);
};

/**
 * 复制对象为JSON格式（用于调试）
 * @param {Object} obj - 要复制的对象
 * @param {Function} messageApi - Ant Design的message API
 * @returns {Promise<boolean>} 复制是否成功
 */
export const copyObjectAsJson = async (obj, messageApi) => {
  try {
    const jsonText = JSON.stringify(obj, null, 2);
    return await copyWithMessage(jsonText, messageApi, 'JSON数据已复制', 'JSON复制失败');
  } catch (error) {
    console.error('JSON序列化失败:', error);
    messageApi.error('数据格式错误，无法复制');
    return false;
  }
};

/**
 * 批量复制多个文本项
 * @param {Array<string>} textItems - 文本数组
 * @param {string} separator - 分隔符
 * @param {Function} messageApi - Ant Design的message API
 * @returns {Promise<boolean>} 复制是否成功
 */
export const copyMultipleItems = async (textItems, separator = '\n\n', messageApi) => {
  if (!Array.isArray(textItems) || textItems.length === 0) {
    messageApi.error('没有可复制的内容');
    return false;
  }

  const combinedText = textItems.join(separator);
  return await copyWithMessage(
    combinedText, 
    messageApi, 
    `已复制${textItems.length}项内容`, 
    '批量复制失败'
  );
};

export default {
  copyToClipboard,
  copyWithMessage,
  simplifyInvitationText,
  formatPmiInfo,
  formatAccountInfo,
  formatShortMeetingInfo,
  isClipboardSupported,
  copyObjectAsJson,
  copyMultipleItems
};
