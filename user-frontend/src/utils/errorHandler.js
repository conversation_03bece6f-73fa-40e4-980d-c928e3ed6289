import { message, notification } from 'antd';

/**
 * API错误处理工具类
 */
class ErrorHandler {
    constructor() {
        this.errorCodes = {
            // 网络错误
            'NETWORK_ERROR': '网络连接失败',
            'TIMEOUT_ERROR': '请求超时',
            'ABORT_ERROR': '请求被取消',
            
            // 业务错误
            'MEETING_NOT_FOUND': '会议不存在',
            'REPORT_NOT_FOUND': '会议报告不存在',
            'REPORT_GENERATING': '会议报告正在生成中',
            'REPORT_FAILED': '会议报告生成失败',
            'INSUFFICIENT_PERMISSIONS': '权限不足',
            'INVALID_PARAMETERS': '参数错误',
            
            // 系统错误
            'INTERNAL_ERROR': '系统内部错误',
            'SERVICE_UNAVAILABLE': '服务暂时不可用',
            'RATE_LIMIT_EXCEEDED': '请求过于频繁'
        };
        
        this.setupGlobalErrorHandlers();
    }

    /**
     * 设置全局错误处理器
     */
    setupGlobalErrorHandlers() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.handleError(event.reason, {
                type: 'unhandledrejection',
                context: 'global'
            });
            event.preventDefault();
        });

        // 捕获全局JavaScript错误
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.handleError(event.error, {
                type: 'javascript',
                context: 'global',
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });
    }

    /**
     * 处理API响应错误
     */
    handleApiError(error, options = {}) {
        const {
            showMessage = true,
            showNotification = false,
            context = '',
            fallbackMessage = '操作失败，请稍后重试'
        } = options;

        let errorMessage = fallbackMessage;
        let errorCode = null;

        // 解析错误信息
        if (error.response) {
            // HTTP错误响应
            const { status, data } = error.response;
            errorCode = data?.errorCode || `HTTP_${status}`;
            errorMessage = this.getErrorMessage(errorCode, data?.message);
            
            // 特殊状态码处理
            if (status === 401) {
                this.handleUnauthorized();
                return;
            }
            
            if (status === 403) {
                errorMessage = '访问被拒绝，权限不足';
            }
        } else if (error.request) {
            // 网络错误
            errorCode = 'NETWORK_ERROR';
            errorMessage = this.getNetworkErrorMessage();
        } else {
            // 其他错误
            errorMessage = error.message || fallbackMessage;
        }

        // 记录错误
        this.logError(error, {
            errorCode,
            errorMessage,
            context,
            timestamp: new Date().toISOString()
        });

        // 显示错误信息
        if (showMessage) {
            message.error(errorMessage);
        }

        if (showNotification) {
            notification.error({
                message: '操作失败',
                description: errorMessage,
                duration: 5
            });
        }

        return {
            errorCode,
            errorMessage,
            originalError: error
        };
    }

    /**
     * 获取错误信息
     */
    getErrorMessage(errorCode, serverMessage) {
        // 优先使用服务器返回的错误信息
        if (serverMessage && typeof serverMessage === 'string') {
            return serverMessage;
        }

        // 使用预定义的错误信息
        return this.errorCodes[errorCode] || '未知错误，请稍后重试';
    }

    /**
     * 获取网络错误信息
     */
    getNetworkErrorMessage() {
        if (!navigator.onLine) {
            return '网络连接已断开，请检查网络设置';
        }
        return '网络请求失败，请检查网络连接';
    }

    /**
     * 处理未授权错误
     */
    handleUnauthorized() {
        message.error('登录已过期，请重新登录');
        
        // 清除本地存储的认证信息
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        
        // 跳转到登录页面
        setTimeout(() => {
            window.location.href = '/login';
        }, 1500);
    }

    /**
     * 处理会议报告相关错误
     */
    handleMeetingReportError(error, meetingId) {
        const errorInfo = this.handleApiError(error, {
            context: `meeting-report-${meetingId}`,
            showMessage: false
        });

        const { errorCode, errorMessage } = errorInfo;

        // 根据错误类型显示不同的提示
        switch (errorCode) {
            case 'MEETING_NOT_FOUND':
                message.warning('会议不存在或已被删除');
                break;
            case 'REPORT_NOT_FOUND':
                message.info('会议报告尚未生成，请稍后再试');
                break;
            case 'REPORT_GENERATING':
                message.loading('会议报告正在生成中，请稍后刷新查看');
                break;
            case 'REPORT_FAILED':
                notification.error({
                    message: '会议报告生成失败',
                    description: '系统无法生成该会议的报告，请联系管理员',
                    duration: 8
                });
                break;
            default:
                message.error(errorMessage);
        }

        return errorInfo;
    }

    /**
     * 处理文件下载错误
     */
    handleDownloadError(error, filename) {
        const errorInfo = this.handleApiError(error, {
            context: `download-${filename}`,
            showMessage: false
        });

        notification.error({
            message: '文件下载失败',
            description: `无法下载文件 "${filename}"，请稍后重试`,
            duration: 5
        });

        return errorInfo;
    }

    /**
     * 记录错误信息
     */
    logError(error, metadata = {}) {
        const errorLog = {
            timestamp: new Date().toISOString(),
            error: {
                message: error.message,
                stack: error.stack,
                name: error.name
            },
            metadata,
            userAgent: navigator.userAgent,
            url: window.location.href,
            userId: this.getCurrentUserId()
        };

        // 输出到控制台
        console.error('Error logged:', errorLog);

        // 可以在这里发送到错误监控服务
        // this.sendToMonitoringService(errorLog);
    }

    /**
     * 发送错误到监控服务
     */
    sendToMonitoringService(errorLog) {
        // 这里可以集成第三方错误监控服务
        // 如Sentry、Bugsnag等
        try {
            // 示例：发送到自定义错误收集接口
            // fetch('/api/errors', {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json'
            //     },
            //     body: JSON.stringify(errorLog)
            // }).catch(err => {
            //     console.error('Failed to send error log:', err);
            // });
        } catch (err) {
            console.error('Error in sendToMonitoringService:', err);
        }
    }

    /**
     * 获取当前用户ID
     */
    getCurrentUserId() {
        try {
            const userInfo = localStorage.getItem('userInfo');
            return userInfo ? JSON.parse(userInfo).id : null;
        } catch {
            return null;
        }
    }

    /**
     * 创建重试函数
     */
    createRetryFunction(originalFunction, maxRetries = 3, delay = 1000) {
        return async (...args) => {
            let lastError;
            
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    return await originalFunction(...args);
                } catch (error) {
                    lastError = error;
                    
                    if (attempt === maxRetries) {
                        throw error;
                    }
                    
                    // 指数退避延迟
                    const retryDelay = delay * Math.pow(2, attempt - 1);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    
                    console.warn(`Retry attempt ${attempt} failed, retrying in ${retryDelay}ms...`);
                }
            }
            
            throw lastError;
        };
    }

    /**
     * 处理表单验证错误
     */
    handleValidationError(error) {
        if (error.response && error.response.status === 422) {
            const validationErrors = error.response.data.errors || {};
            
            Object.keys(validationErrors).forEach(field => {
                const fieldErrors = validationErrors[field];
                if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
                    message.error(`${field}: ${fieldErrors[0]}`);
                }
            });
            
            return true;
        }
        
        return false;
    }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler();

// 导出常用方法
export const handleApiError = errorHandler.handleApiError.bind(errorHandler);
export const handleMeetingReportError = errorHandler.handleMeetingReportError.bind(errorHandler);
export const handleDownloadError = errorHandler.handleDownloadError.bind(errorHandler);
export const createRetryFunction = errorHandler.createRetryFunction.bind(errorHandler);
export const handleValidationError = errorHandler.handleValidationError.bind(errorHandler);

export default errorHandler;
