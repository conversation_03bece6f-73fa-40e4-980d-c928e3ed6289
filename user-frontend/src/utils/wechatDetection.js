/**
 * 微信浏览器检测工具
 * 用于检测用户是否在微信内置浏览器中访问页面
 */

/**
 * 检测是否为微信内置浏览器
 * @returns {boolean} 是否为微信浏览器
 */
export const isWechatBrowser = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('micromessenger');
};

/**
 * 检测是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export const isMobileDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
};

/**
 * 检测是否为iOS设备
 * @returns {boolean} 是否为iOS设备
 */
export const isIOSDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return /iphone|ipad|ipod/i.test(userAgent);
};

/**
 * 检测是否为Android设备
 * @returns {boolean} 是否为Android设备
 */
export const isAndroidDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return /android/i.test(userAgent);
};

/**
 * 获取当前页面的完整URL
 * @returns {string} 当前页面URL
 */
export const getCurrentPageUrl = () => {
  return window.location.href;
};

/**
 * 检测是否需要显示浏览器引导
 * 目前主要针对微信浏览器，因为微信无法调起Zoom应用
 * @returns {boolean} 是否需要显示引导
 */
export const shouldShowBrowserGuide = () => {
  return isWechatBrowser();
};

/**
 * 获取浏览器类型信息
 * @returns {Object} 浏览器信息对象
 */
export const getBrowserInfo = () => {
  const userAgent = navigator.userAgent;
  
  return {
    isWechat: isWechatBrowser(),
    isMobile: isMobileDevice(),
    isIOS: isIOSDevice(),
    isAndroid: isAndroidDevice(),
    userAgent: userAgent,
    needsGuide: shouldShowBrowserGuide()
  };
};

/**
 * 复制文本到剪贴板
 * @param {string} text 要复制的文本
 * @returns {Promise<boolean>} 复制是否成功
 */
export const copyToClipboard = async (text) => {
  try {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级方案：使用传统的复制方法
      return fallbackCopyTextToClipboard(text);
    }
  } catch (error) {
    console.error('复制失败:', error);
    // 尝试降级方案
    return fallbackCopyTextToClipboard(text);
  }
};

/**
 * 降级复制方法（兼容旧浏览器）
 * @param {string} text 要复制的文本
 * @returns {boolean} 复制是否成功
 */
const fallbackCopyTextToClipboard = (text) => {
  try {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 避免在页面上显示
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);
    
    return successful;
  } catch (error) {
    console.error('降级复制也失败:', error);
    return false;
  }
};
