-- 验证PMI窗口数据补全效果
-- 执行日期: 2025-08-18

USE zoombusV;

-- ========================================
-- 验证数据补全效果
-- ========================================

SELECT '=== PMI窗口数据补全验证报告 ===' as report;

-- 1. 检查LONG类型PMI总数和补全情况
SELECT 
    'LONG类型PMI总数' as item,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG';

SELECT 
    '已补全current_window_id的LONG类型PMI' as item,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG' 
AND current_window_id IS NOT NULL;

SELECT 
    '已补全window_expire_time的LONG类型PMI' as item,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG' 
AND window_expire_time IS NOT NULL;

-- 2. 显示补全后的数据样例
SELECT '=== LONG类型PMI窗口信息样例 ===' as sample_data;

SELECT 
    p.id as pmi_id,
    p.pmi_number,
    p.billing_mode,
    p.current_window_id,
    DATE_FORMAT(p.window_expire_time, '%Y-%m-%d %H:%i:%s') as window_expire_time,
    w.status as window_status,
    w.window_date,
    w.end_date,
    w.start_time,
    w.end_time
FROM t_pmi_records p
LEFT JOIN t_pmi_schedule_windows w ON p.current_window_id = w.id
WHERE p.billing_mode = 'LONG'
ORDER BY p.id
LIMIT 10;

-- 3. 检查窗口状态分布
SELECT '=== LONG类型PMI对应的窗口状态分布 ===' as window_status_distribution;

SELECT 
    w.status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_pmi_schedule_windows w2 
                              JOIN t_pmi_records p2 ON w2.pmi_record_id = p2.id 
                              WHERE p2.billing_mode = 'LONG'), 2) as percentage
FROM t_pmi_schedule_windows w
JOIN t_pmi_records p ON w.pmi_record_id = p.id
WHERE p.billing_mode = 'LONG'
GROUP BY w.status
ORDER BY count DESC;

-- 4. 检查到期时间分布
SELECT '=== LONG类型PMI到期时间分布 ===' as expire_time_distribution;

SELECT 
    DATE(window_expire_time) as expire_date,
    COUNT(*) as count
FROM t_pmi_records 
WHERE billing_mode = 'LONG' 
AND window_expire_time IS NOT NULL
GROUP BY DATE(window_expire_time)
ORDER BY expire_date
LIMIT 10;

-- 5. 检查即将到期的PMI（7天内）
SELECT '=== 7天内即将到期的LONG类型PMI ===' as expiring_soon;

SELECT 
    p.id,
    p.pmi_number,
    DATE_FORMAT(p.window_expire_time, '%Y-%m-%d %H:%i:%s') as window_expire_time,
    DATEDIFF(p.window_expire_time, NOW()) as days_until_expire
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND p.window_expire_time IS NOT NULL
AND p.window_expire_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)
ORDER BY p.window_expire_time;

-- 6. 检查已过期的PMI
SELECT '=== 已过期的LONG类型PMI ===' as expired_pmis;

SELECT 
    p.id,
    p.pmi_number,
    DATE_FORMAT(p.window_expire_time, '%Y-%m-%d %H:%i:%s') as window_expire_time,
    DATEDIFF(NOW(), p.window_expire_time) as days_expired
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND p.window_expire_time IS NOT NULL
AND p.window_expire_time < NOW()
ORDER BY p.window_expire_time DESC
LIMIT 5;

-- 7. 验证数据完整性
SELECT '=== 数据完整性验证 ===' as data_integrity;

-- 检查是否有current_window_id指向不存在的窗口
SELECT 
    '指向不存在窗口的PMI数量' as issue,
    COUNT(*) as count
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
AND p.current_window_id IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM t_pmi_schedule_windows w 
    WHERE w.id = p.current_window_id
);

-- 检查是否有窗口没有对应的PMI记录
SELECT 
    '没有对应PMI记录的窗口数量' as issue,
    COUNT(*) as count
FROM t_pmi_schedule_windows w
WHERE NOT EXISTS (
    SELECT 1 FROM t_pmi_records p 
    WHERE p.id = w.pmi_record_id
);

-- 8. 前端展示数据格式验证
SELECT '=== 前端展示数据格式验证 ===' as frontend_data_format;

SELECT 
    p.id,
    p.pmi_number,
    p.billing_mode,
    p.current_window_id,
    p.window_expire_time,
    -- 模拟前端需要的格式
    CASE 
        WHEN p.billing_mode = 'LONG' AND p.window_expire_time IS NOT NULL THEN
            CASE 
                WHEN p.window_expire_time <= NOW() THEN '已过期'
                WHEN p.window_expire_time <= DATE_ADD(NOW(), INTERVAL 7 DAY) THEN '即将到期'
                ELSE '长租中'
            END
        ELSE '-'
    END as display_status,
    -- 前端显示的到期日期格式
    CASE 
        WHEN p.billing_mode = 'LONG' AND p.window_expire_time IS NOT NULL THEN
            DATE_FORMAT(p.window_expire_time, '%Y-%m-%d')
        ELSE NULL
    END as display_expire_date
FROM t_pmi_records p
WHERE p.billing_mode = 'LONG'
ORDER BY p.id
LIMIT 5;

SELECT '=== 验证完成 ===' as completion;
