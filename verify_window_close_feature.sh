#!/bin/bash

# 验证窗口关闭联动取消close task功能

BASE_URL="http://localhost:8080"

echo "=== 验证窗口关闭联动取消close task功能 ==="
echo "时间: $(date)"
echo

# 1. 查询所有窗口，找到一个可以测试的窗口
echo "1. 查询所有窗口..."
WINDOWS_RESPONSE=$(curl -s "$BASE_URL/api/pmi-schedule-windows?page=0&size=10")

echo "窗口列表:"
echo "$WINDOWS_RESPONSE" | jq '.data.content[] | {id, status, closeTaskId, pmiNumber}' | head -5

# 找到一个有closeTaskId的PENDING窗口
WINDOW_ID=$(echo "$WINDOWS_RESPONSE" | jq -r '.data.content[] | select(.status == "PENDING" and .closeTaskId != null) | .id' | head -1)

if [ -z "$WINDOW_ID" ] || [ "$WINDOW_ID" = "null" ]; then
    echo "❌ 没有找到合适的测试窗口（需要PENDING状态且有closeTaskId）"
    echo "建议："
    echo "  1. 创建一个新的PMI计划"
    echo "  2. 或者查看现有窗口的状态"
    exit 1
fi

echo "✅ 找到测试窗口ID: $WINDOW_ID"

# 获取对应的close task ID
CLOSE_TASK_ID=$(echo "$WINDOWS_RESPONSE" | jq -r ".data.content[] | select(.id == $WINDOW_ID) | .closeTaskId")
echo "✅ 对应的close task ID: $CLOSE_TASK_ID"
echo

# 2. 查询close task的当前状态
echo "2. 查询close task当前状态..."
TASK_RESPONSE=$(curl -s "$BASE_URL/api/pmi-scheduled-tasks/$CLOSE_TASK_ID")
echo "任务详情:"
echo "$TASK_RESPONSE" | jq '.data | {id, taskType, status, scheduledTime}'

TASK_STATUS=$(echo "$TASK_RESPONSE" | jq -r '.data.status')
echo "任务状态: $TASK_STATUS"

if [ "$TASK_STATUS" != "SCHEDULED" ]; then
    echo "❌ 任务状态不是SCHEDULED，无法测试取消功能"
    exit 1
fi
echo

# 3. 激活窗口
echo "3. 激活窗口..."
ACTIVATE_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID/activate")
echo "激活响应:"
echo "$ACTIVATE_RESPONSE" | jq '.'

if [ "$(echo "$ACTIVATE_RESPONSE" | jq -r '.success')" != "true" ]; then
    echo "❌ 窗口激活失败"
    echo "$ACTIVATE_RESPONSE" | jq '.message'
    exit 1
fi

echo "✅ 窗口激活成功"
echo

# 4. 关闭窗口
echo "4. 关闭窗口..."
CLOSE_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/pmi-schedule-windows/$WINDOW_ID/close")
echo "关闭响应:"
echo "$CLOSE_RESPONSE" | jq '.'

if [ "$(echo "$CLOSE_RESPONSE" | jq -r '.success')" != "true" ]; then
    echo "❌ 窗口关闭失败"
    echo "$CLOSE_RESPONSE" | jq '.message'
    exit 1
fi

echo "✅ 窗口关闭成功"
echo

# 5. 验证close task是否被取消
echo "5. 验证close task是否被取消..."
sleep 2

TASK_RESPONSE_AFTER=$(curl -s "$BASE_URL/api/pmi-scheduled-tasks/$CLOSE_TASK_ID")
echo "关闭后任务详情:"
echo "$TASK_RESPONSE_AFTER" | jq '.data | {id, taskType, status, errorMessage}'

TASK_STATUS_AFTER=$(echo "$TASK_RESPONSE_AFTER" | jq -r '.data.status')
echo "关闭后任务状态: $TASK_STATUS_AFTER"

# 6. 验证结果
echo
echo "=== 验证结果 ==="
if [ "$TASK_STATUS_AFTER" = "CANCELLED" ]; then
    echo "✅ 测试成功：close task已被取消"
    echo "🎉 功能验证通过：人工关闭窗口时成功联动取消了close task"
    echo
    echo "详细信息："
    echo "  - 窗口ID: $WINDOW_ID"
    echo "  - Close Task ID: $CLOSE_TASK_ID"
    echo "  - 任务状态变化: SCHEDULED → CANCELLED"
    echo "  - 功能状态: ✅ 正常工作"
else
    echo "❌ 测试失败：close task状态为 $TASK_STATUS_AFTER，期望为 CANCELLED"
    echo
    echo "可能的原因："
    echo "  1. 代码修改未生效，需要重启应用"
    echo "  2. 任务取消逻辑有问题"
    echo "  3. 窗口没有正确关联close task"
    echo "  4. PmiTaskManagementService.cancelPmiTask()方法有问题"
    echo
    echo "建议检查："
    echo "  - 应用日志中的错误信息"
    echo "  - 数据库中的任务状态"
    echo "  - 服务依赖注入是否正确"
fi

echo
echo "=== 验证完成 ==="
