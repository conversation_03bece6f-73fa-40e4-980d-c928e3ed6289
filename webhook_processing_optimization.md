# Webhook处理逻辑优化

## 🎯 优化目标

根据建议实现三个关键优化：
1. **改进Webhook处理逻辑** - 多重匹配策略
2. **ZoomUser状态管理** - 选取时立即更新状态
3. **会议记录补全** - 根据hostId补全必要字段

## ✅ 已完成的优化

### 1. 改进Webhook处理逻辑 - 多重匹配策略

#### 新的handleMeetingStarted方法
```java
@Transactional
public void handleMeetingStarted(String meetingUuid, String meetingId, String hostId, String topic) {
    ZoomMeeting meeting = null;
    boolean foundByUuid = false;
    
    // 策略1: 优先通过UUID查找
    if (meetingUuid != null && !meetingUuid.isEmpty()) {
        Optional<ZoomMeeting> meetingOpt = zoomMeetingRepository.findByZoomMeetingUuid(meetingUuid);
        if (meetingOpt.isPresent()) {
            meeting = meetingOpt.get();
            foundByUuid = true;
        }
    }
    
    // 策略2: UUID失败时通过meetingId+hostId查找活跃会议
    if (meeting == null && meetingId != null && hostId != null) {
        List<ZoomMeeting> activeMeetings = zoomMeetingRepository
            .findByZoomMeetingIdAndHostIdAndStatusIn(
                meetingId, hostId, 
                Arrays.asList(PENDING, USING)
            );
        
        if (!activeMeetings.isEmpty()) {
            meeting = activeMeetings.get(0);
        }
    }
    
    // 策略3: 更新真实UUID和状态
    if (meeting != null) {
        updateMeetingToStarted(meeting, meetingUuid, topic, foundByUuid);
    } else {
        // 策略4: 创建新记录
        createMeetingRecordFromWebhook(meetingUuid, meetingId, hostId, topic);
    }
}
```

#### 新增Repository查询方法
```java
@Query("SELECT zm FROM ZoomMeeting zm WHERE zm.zoomMeetingId = :meetingId " +
       "AND zm.hostId = :hostId AND zm.status IN :statuses " +
       "ORDER BY zm.createdAt DESC")
List<ZoomMeeting> findByZoomMeetingIdAndHostIdAndStatusIn(
    @Param("meetingId") String meetingId,
    @Param("hostId") String hostId, 
    @Param("statuses") List<ZoomMeeting.MeetingStatus> statuses
);
```

### 2. ZoomUser状态管理优化

#### 立即更新状态防止重复分配
```java
// ZoomUserPmiService.assignZoomUserForMeeting()
public ZoomUser assignZoomUserForMeeting(Long meetingId, String pmiNumber) {
    ZoomUser availableUser = findAvailableZoomUser();
    
    // 立即设置状态为使用中，防止其他PMI再次使用这个ZoomUser
    availableUser.setUsageStatus(ZoomUser.UsageStatus.IN_USE);
    availableUser.setCurrentMeetingId(meetingId);
    availableUser.setLastUsedTime(LocalDateTime.now());
    
    // 立即保存状态，确保其他并发请求看到最新状态
    zoomUserRepository.save(availableUser);
    log.info("ZoomUser状态已立即更新为IN_USE: userId={}, meetingId={}", 
            availableUser.getId(), meetingId);
    
    // 然后进行PMI设置
    try {
        zoomApiService.updateUserPmi(availableUser, pmiNumber);
        // ... 其他逻辑
    } catch (Exception e) {
        // 失败时恢复状态
        availableUser.setUsageStatus(ZoomUser.UsageStatus.AVAILABLE);
        availableUser.setCurrentMeetingId(null);
        zoomUserRepository.save(availableUser);
        throw e;
    }
}
```

#### 优化效果
- ✅ **防止重复分配**：状态立即更新，避免并发问题
- ✅ **提高可靠性**：即使后续操作失败，状态也能正确恢复
- ✅ **减少竞争条件**：多个PMI同时激活时不会分配到同一个ZoomUser

### 3. 会议记录补全优化

#### 根据hostId补全ZoomUser信息
```java
private void createMeetingRecordFromWebhook(String meetingUuid, String meetingId, String hostId, String topic) {
    ZoomMeeting meeting = new ZoomMeeting();
    meeting.setZoomMeetingUuid(meetingUuid);
    meeting.setZoomMeetingId(meetingId);
    meeting.setHostId(hostId);
    meeting.setStatus(ZoomMeeting.MeetingStatus.USING);
    meeting.setStartTime(LocalDateTime.now());
    
    // 根据hostId补全ZoomUser信息
    if (hostId != null) {
        Optional<ZoomUser> zoomUserOpt = findZoomUserByZoomUserId(hostId);
        if (zoomUserOpt.isPresent()) {
            ZoomUser zoomUser = zoomUserOpt.get();
            meeting.setAssignedZoomUserId(zoomUser.getId());
            meeting.setAssignedZoomUserEmail(zoomUser.getEmail());
            meeting.setZoomAuthAccountName(zoomUser.getZoomAuth() != null ? 
                zoomUser.getZoomAuth().getAccountName() : null);
        }
    }
    
    meeting.setBillingMode(PmiRecord.BillingMode.BY_TIME);
    zoomMeetingRepository.save(meeting);
}
```

#### ZoomUser查找方法
```java
private Optional<ZoomUser> findZoomUserByZoomUserId(String zoomUserId) {
    try {
        List<ZoomUser> zoomUsers = zoomUserRepository.findByZoomUserId(zoomUserId);
        if (!zoomUsers.isEmpty()) {
            return Optional.of(zoomUsers.get(0));
        }
    } catch (Exception e) {
        log.error("查找ZoomUser失败: zoomUserId={}", zoomUserId, e);
    }
    return Optional.empty();
}
```

## 🔄 处理流程对比

### 优化前的问题
```
Webhook事件 → 只通过UUID查找 → 找不到 → 创建基础记录
                                ↓
                            缺少关联信息
```

### 优化后的流程
```
Webhook事件 → 1. 优先UUID查找 → 找到 → 更新状态+UUID
            ↓
            2. meetingId+hostId查找 → 找到 → 更新状态+UUID
            ↓
            3. 创建新记录 → 补全ZoomUser信息 → 完整记录
```

## 📊 优化效果

### 1. 健壮性提升
- ✅ **多重匹配策略**：UUID不匹配时仍能找到会议记录
- ✅ **状态同步**：真实UUID更新，保持数据一致性
- ✅ **信息完整**：新创建的记录包含完整的ZoomUser关联信息

### 2. 并发安全性
- ✅ **立即状态更新**：防止ZoomUser重复分配
- ✅ **事务保护**：状态更新和业务逻辑在同一事务中
- ✅ **异常恢复**：失败时能正确恢复状态

### 3. 数据完整性
- ✅ **关联信息补全**：根据hostId自动关联ZoomUser
- ✅ **账号信息记录**：包含ZoomAuth账号名称
- ✅ **计费信息准备**：设置默认计费模式

## 🧪 测试场景

### 1. UUID匹配成功
```
输入: uuid=real-uuid, meetingId=123, hostId=host1
结果: 通过UUID找到会议 → 更新状态为USING
```

### 2. UUID不匹配，meetingId+hostId匹配
```
输入: uuid=new-uuid, meetingId=123, hostId=host1
结果: UUID查找失败 → meetingId+hostId找到 → 更新UUID和状态
```

### 3. 都不匹配，创建新记录
```
输入: uuid=unknown-uuid, meetingId=999, hostId=host2
结果: 都找不到 → 创建新记录 → 补全ZoomUser信息
```

### 4. 并发ZoomUser分配
```
场景: 两个PMI同时激活
结果: 第一个立即获得ZoomUser → 第二个看到状态已更新 → 分配下一个可用ZoomUser
```

## 🚀 后续监控建议

### 1. 监控指标
```java
// 匹配策略成功率
meterRegistry.counter("meeting.started.match.strategy", "type", "uuid").increment();
meterRegistry.counter("meeting.started.match.strategy", "type", "meetingId").increment();
meterRegistry.counter("meeting.started.match.strategy", "type", "create").increment();

// ZoomUser分配成功率
meterRegistry.counter("zoomuser.assignment", "result", "success").increment();
meterRegistry.counter("zoomuser.assignment", "result", "conflict").increment();
```

### 2. 日志监控
- UUID不匹配但meetingId+hostId匹配的频率
- 新创建会议记录的频率
- ZoomUser状态冲突的频率

### 3. 数据一致性检查
```sql
-- 检查会议状态和ZoomUser状态的一致性
SELECT COUNT(*) FROM t_zoom_meetings zm
JOIN t_zoom_accounts zu ON zm.assigned_zoom_user_id = zu.id
WHERE zm.status = 'USING' 
  AND zu.usage_status != 'IN_USE';
```

## ✅ 优化完成

### 核心改进
1. **多重匹配策略** - 解决UUID不匹配问题
2. **立即状态更新** - 防止ZoomUser重复分配
3. **信息自动补全** - 确保会议记录完整性

### 系统稳定性
- 🛡️ **容错能力**：多种查找策略确保会议记录能被正确处理
- 🛡️ **并发安全**：立即状态更新防止资源冲突
- 🛡️ **数据完整**：自动补全关联信息，减少手动维护

现在Webhook处理逻辑更加健壮，能够正确处理各种边界情况，确保会议状态和ZoomUser状态的一致性！🎯
