# 窗口2084任务缺失问题分析与流程优化

## 🔍 **问题调查结果**

### **窗口2084状态确认**
从应用日志中发现关键信息：
```
2025-08-23 20:55:01.040 [main-scheduler-6] INFO  [] c.z.s.PmiWindowTaskSchedulingService - 处理PMI窗口创建事件: windowId=2084
2025-08-23 20:55:01.041 [main-scheduler-6] WARN  [] c.z.s.PmiWindowTaskSchedulingService - PMI窗口不存在，跳过任务创建: windowId=2084
```

**问题确认**：即使修复了代码返回类型，窗口2084仍然出现"PMI窗口不存在，跳过任务创建"的问题！

## 🚨 **深层问题根源**

### **事务边界和异步处理问题**
虽然我们修改了返回类型从`String taskKey`到`Long taskId`，但是**核心的事务时序问题**仍然存在：

1. **事务提交时序**：窗口保存在主事务中，但事件监听器是异步执行的
2. **事务隔离**：异步事件监听器可能在主事务提交之前就开始执行
3. **数据库可见性**：异步线程可能看不到主事务中尚未提交的数据

### **问题流程图**
```
主线程事务开始
    ↓
保存窗口到数据库 (未提交)
    ↓
发布事件 ❌ (事务未提交)
    ↓
异步线程接收事件
    ↓
查询窗口 ❌ (查询不到未提交的数据)
    ↓
跳过任务创建
    ↓
主线程事务提交 (太晚了)
```

## ✅ **优化解决方案**

### **1. 事务提交后发布事件**

修改 `PmiScheduleService`，确保事件在事务提交后才发布：

```java
// 在事务提交后发布窗口创建事件
TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
    @Override
    public void afterCommit() {
        for (PmiScheduleWindow window : savedWindows) {
            try {
                eventPublisher.publishEvent(new PmiWindowCreatedEvent(PmiScheduleService.this, window));
                log.debug("发布PMI窗口创建事件: windowId={}", window.getId());
            } catch (Exception e) {
                log.warn("发布PMI窗口创建事件失败: windowId={}", window.getId(), e);
            }
        }
    }
});
```

**优势**：
- ✅ 确保窗口数据已提交到数据库
- ✅ 异步事件监听器能正确查询到窗口
- ✅ 避免事务时序问题

### **2. 直接返回任务ID**

保持之前的优化，让 `DynamicTaskManager` 直接返回任务ID：

```java
// 修复后的流程
Long openTaskId = dynamicTaskManager.schedulePmiWindowOpenTask(window.getId(), window.getStartDateTime());
Long closeTaskId = dynamicTaskManager.schedulePmiWindowCloseTask(window.getId(), window.getEndDateTime());

// 直接设置任务ID，无需额外查询
if (openTaskId != null) {
    window.setOpenTaskId(openTaskId);
}
if (closeTaskId != null) {
    window.setCloseTaskId(closeTaskId);
}
```

### **3. 批量修复服务**

创建 `PmiWindowTaskRepairService` 来修复现有的没有任务的窗口：

**功能**：
- 查找所有没有任务的活跃窗口
- 批量创建缺失的任务
- 更新窗口的任务ID关联
- 提供单个窗口修复功能

**API接口**：
- `GET /api/pmi-window-task-repair/check` - 检查需要修复的窗口数量
- `POST /api/pmi-window-task-repair/repair-all` - 批量修复所有窗口
- `POST /api/pmi-window-task-repair/repair/{windowId}` - 修复单个窗口

## 🎯 **优化后的完整流程**

### **新建窗口流程**
```
1. 创建窗口 → 保存到数据库
2. 事务提交 ✅
3. 发布事件 (afterCommit)
4. 异步事件监听器接收事件
5. 查询窗口 ✅ (能查到已提交的数据)
6. 创建任务 → 返回任务ID
7. 更新窗口任务ID → 保存
```

### **修复现有窗口流程**
```
1. 查询没有任务的窗口
2. 为每个窗口创建任务
3. 更新窗口的任务ID关联
4. 验证修复结果
```

## 📊 **修复效果预期**

### **解决的问题**
1. ✅ **事务时序问题**：事件在事务提交后发布
2. ✅ **数据可见性问题**：异步线程能正确查询到窗口
3. ✅ **任务创建失败问题**：直接返回任务ID，简化流程
4. ✅ **历史数据修复**：提供批量修复功能

### **性能优化**
1. ✅ **减少数据库查询**：不再需要立即查询刚创建的任务
2. ✅ **简化事务逻辑**：明确的事务边界
3. ✅ **提高成功率**：避免时序竞争条件

## 🔧 **验证方法**

### **1. 新建窗口测试**
创建新的PMI计划，验证窗口和任务是否正确关联

### **2. 批量修复测试**
```bash
# 检查需要修复的窗口
curl -X GET "http://localhost:8080/api/pmi-window-task-repair/check"

# 批量修复
curl -X POST "http://localhost:8080/api/pmi-window-task-repair/repair-all"

# 验证修复结果
curl -X GET "http://localhost:8080/api/pmi-window-task-repair/check"
```

### **3. 数据库验证**
```sql
-- 检查窗口任务关联状态
SELECT 
    validation_result,
    COUNT(*) as count
FROM (
    SELECT 
        CASE 
            WHEN psw.open_task_id IS NULL AND psw.close_task_id IS NULL THEN 'NO_TASKS'
            WHEN psw.open_task_id = open_task.id AND psw.close_task_id = close_task.id THEN 'CORRECT'
            ELSE 'PARTIAL'
        END as validation_result
    FROM t_pmi_schedule_windows psw
    LEFT JOIN t_pmi_schedule_window_tasks open_task ON psw.open_task_id = open_task.id
    LEFT JOIN t_pmi_schedule_window_tasks close_task ON psw.close_task_id = close_task.id
    WHERE psw.status IN ('PENDING', 'ACTIVE')
) validation_data
GROUP BY validation_result;
```

## 📝 **总结**

### **问题根源**
窗口2084没有任务的问题是由于**事务边界和异步处理的时序问题**导致的，即使修复了返回类型，事务提交时序仍然是关键问题。

### **解决方案**
1. **事务提交后发布事件**：确保数据可见性
2. **直接返回任务ID**：简化流程，避免额外查询
3. **批量修复服务**：修复历史数据

### **预期效果**
- ✅ 新建窗口必定有对应的任务
- ✅ 历史问题窗口可以批量修复
- ✅ 系统更加稳定可靠
- ✅ 运维效率提升

**🎉 通过这些优化，确保了用户描述的预期流程能够正确执行：创建窗口 → 保存到数据库 → 创建任务 → 将窗口ID传给任务 → 创建任务成功后更新窗口里的task ID。**
