# 人工关闭窗口联动取消Close Task功能实现

## 🎯 **功能需求**
当用户通过API人工关闭PMI窗口时，需要联动取消该窗口对应的close task，避免任务在窗口已关闭后仍然执行。

## 🔍 **问题分析**

### **现有流程**
1. 用户调用 `PUT /api/pmi-schedule-windows/{id}/close` 关闭窗口
2. 系统更新窗口状态为 `MANUALLY_CLOSED`
3. 但是对应的close task仍然保持 `SCHEDULED` 状态
4. 任务到时间后仍会执行，造成不必要的操作

### **问题影响**
- ✅ 窗口已经人工关闭
- ❌ Close task仍然会在预定时间执行
- ❌ 可能导致重复的PMI关闭操作
- ❌ 任务监控页面显示不准确的任务状态

## ✅ **解决方案**

### **1. 修改窗口关闭服务**

在 `PmiScheduleWindowService.closeWindow()` 方法中添加取消close task的逻辑：

```java
/**
 * 关闭窗口
 */
@Transactional
public PmiScheduleWindow closeWindow(Long id) {
    log.info("关闭计划窗口: id={}", id);

    PmiScheduleWindow window = getWindow(id);

    // 检查窗口状态，只有执行中的窗口才能关闭
    if (window.getStatus() != PmiScheduleWindow.WindowStatus.ACTIVE) {
        throw new IllegalStateException("只有执行中状态的窗口才能关闭");
    }

    // 取消对应的关闭任务（如果存在）
    if (window.getCloseTaskId() != null) {
        try {
            pmiTaskManagementService.cancelPmiTask(window.getCloseTaskId());
            log.info("已取消窗口关闭任务: windowId={}, closeTaskId={}", id, window.getCloseTaskId());
        } catch (Exception e) {
            log.warn("取消窗口关闭任务失败: windowId={}, closeTaskId={}, error={}", 
                    id, window.getCloseTaskId(), e.getMessage());
            // 不抛出异常，继续执行窗口关闭操作
        }
    } else {
        log.warn("窗口没有关联的关闭任务: windowId={}", id);
    }

    // 更新窗口状态为人工关闭，并记录实际关闭时间
    window.setStatus(PmiScheduleWindow.WindowStatus.MANUALLY_CLOSED);
    window.setActualEndTime(LocalDateTime.now());
    window.setUpdatedAt(LocalDateTime.now());

    PmiScheduleWindow savedWindow = windowRepository.save(window);

    // 检查是否需要关闭PMI
    checkAndClosePmi(window.getPmiRecordId());

    // 检查是否需要将计划状态置为完成
    checkAndCompleteSchedule(window.getScheduleId());

    log.info("计划窗口关闭成功: id={}", id);

    return savedWindow;
}
```

### **2. 添加依赖注入**

在 `PmiScheduleWindowService` 中注入 `PmiTaskManagementService`：

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class PmiScheduleWindowService {
    
    private final PmiScheduleWindowRepository windowRepository;
    private final PmiScheduleRepository scheduleRepository;
    private final PmiRecordRepository pmiRecordRepository;
    private final PmiService pmiService;
    private final PmiTaskManagementService pmiTaskManagementService;  // 新增
    private final ApplicationEventPublisher eventPublisher;
    
    // ... 其他代码
}
```

### **3. 添加测试支持API**

为了便于测试，添加了激活窗口的API：

#### **服务方法**
```java
/**
 * 激活窗口（用于测试）
 */
@Transactional
public PmiScheduleWindow activateWindow(Long id) {
    log.info("激活计划窗口: id={}", id);

    PmiScheduleWindow window = getWindow(id);

    // 检查窗口状态，只有待执行的窗口才能激活
    if (window.getStatus() != PmiScheduleWindow.WindowStatus.PENDING) {
        throw new IllegalStateException("只有待执行状态的窗口才能激活");
    }

    // 更新窗口状态为活跃，并记录实际开始时间
    window.setStatus(PmiScheduleWindow.WindowStatus.ACTIVE);
    window.setActualStartTime(LocalDateTime.now());
    window.setUpdatedAt(LocalDateTime.now());

    PmiScheduleWindow savedWindow = windowRepository.save(window);

    log.info("计划窗口激活成功: id={}", id);

    return savedWindow;
}
```

#### **控制器方法**
```java
/**
 * 激活计划窗口（用于测试）
 */
@PutMapping("/{id}/activate")
public ResponseEntity<Map<String, Object>> activateWindow(@PathVariable Long id) {
    try {
        log.info("收到激活计划窗口请求: id={}", id);

        PmiScheduleWindow window = pmiScheduleWindowService.activateWindow(id);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "窗口激活成功");
        response.put("data", window);

        return ResponseEntity.ok(response);

    } catch (Exception e) {
        log.error("激活计划窗口失败", e);
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "激活窗口失败: " + e.getMessage());
        return ResponseEntity.badRequest().body(response);
    }
}
```

## 🎯 **功能特点**

### **1. 联动取消**
- ✅ 人工关闭窗口时自动取消对应的close task
- ✅ 避免重复的PMI关闭操作
- ✅ 保持数据一致性

### **2. 错误处理**
- ✅ 如果取消任务失败，不影响窗口关闭操作
- ✅ 记录详细的日志信息
- ✅ 优雅的异常处理

### **3. 状态管理**
- ✅ 窗口状态更新为 `MANUALLY_CLOSED`
- ✅ Close task状态更新为 `CANCELLED`
- ✅ 记录实际关闭时间

### **4. 完整性检查**
- ✅ 检查窗口是否有关联的close task
- ✅ 只有ACTIVE状态的窗口才能关闭
- ✅ 保持原有的PMI关闭和计划完成检查逻辑

## 🔧 **测试方法**

### **1. 自动化测试脚本**
创建了 `test_simple_window_close.sh` 脚本来验证功能：

```bash
# 查询待执行的CLOSE任务
# 激活对应的窗口
# 人工关闭窗口
# 验证close task是否被取消
```

### **2. 手动测试步骤**
1. 创建一个PMI计划，生成窗口和任务
2. 激活窗口：`PUT /api/pmi-schedule-windows/{id}/activate`
3. 查询close task状态：`GET /api/pmi-scheduled-tasks/{taskId}`
4. 关闭窗口：`PUT /api/pmi-schedule-windows/{id}/close`
5. 再次查询close task状态，验证是否为 `CANCELLED`

### **3. 验证要点**
- ✅ 窗口状态从 `ACTIVE` 变为 `MANUALLY_CLOSED`
- ✅ Close task状态从 `SCHEDULED` 变为 `CANCELLED`
- ✅ 日志中显示取消任务的信息
- ✅ 不影响其他窗口和任务

## 📊 **预期效果**

### **用户体验**
- ✅ 人工关闭窗口后，不会有多余的任务执行
- ✅ 任务监控页面显示准确的任务状态
- ✅ 避免用户困惑和重复操作

### **系统稳定性**
- ✅ 减少不必要的任务执行
- ✅ 提高系统资源利用效率
- ✅ 保持数据状态的一致性

### **运维效率**
- ✅ 减少异常任务的排查工作
- ✅ 提供清晰的操作日志
- ✅ 简化任务管理流程

## 📝 **总结**

通过在人工关闭窗口时联动取消对应的close task，实现了：

1. **数据一致性**：窗口状态和任务状态保持同步
2. **操作合理性**：避免在窗口已关闭后仍执行关闭任务
3. **用户体验**：提供更直观和准确的系统状态
4. **系统效率**：减少不必要的任务执行

**🎉 功能已完成实现，通过API调用 `PUT /api/pmi-schedule-windows/{id}/close` 关闭窗口时，会自动取消对应的close task！**
