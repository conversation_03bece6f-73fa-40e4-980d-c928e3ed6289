# 窗口2083任务创建流程分析与修复

## 🔍 **问题分析**

### **用户反馈的问题**
> 窗口2083创建成功后，没有相应的task

### **预期流程**
1. **创建窗口** → 保存到数据库 → 获得窗口ID
2. **发布事件** → 传递窗口对象（包含ID）
3. **事件监听器** → 创建任务 → 传递窗口ID给任务
4. **任务创建成功** → 获得任务ID → 更新窗口的task_id字段

## 🚨 **发现的根本问题**

### **事务时序问题**
原来的流程中存在一个关键问题：

```java
// 原来的问题代码
@EventListener
public void handlePmiWindowCreated(PmiWindowCreatedEvent event) {
    // 1. 创建任务，返回taskKey
    String openTaskKey = dynamicTaskManager.schedulePmiWindowOpenTask(...);
    
    // 2. 立即查询刚创建的任务 ❌ 可能查询不到
    Optional<PmiScheduleWindowTask> openTask = taskRepository.findByTaskKey(openTaskKey);
    
    // 3. 更新窗口的任务ID
    if (openTask.isPresent()) {
        window.setOpenTaskId(openTask.get().getId());
    }
}
```

**问题原因**：
- `DynamicTaskManager.schedulePmiWindowOpenTask()` 在同一个事务中创建并保存任务
- 但是 `taskRepository.findByTaskKey(openTaskKey)` **立即查询**刚创建的任务
- 由于事务还未提交，可能存在**读取不到刚创建的任务**的情况

## ✅ **修复方案**

### **核心改进：直接返回任务ID**

修改 `DynamicTaskManager` 接口和实现，让任务创建方法直接返回任务ID而不是taskKey：

```java
// 修复后的接口
public interface DynamicTaskManager {
    /**
     * 调度PMI窗口开启任务
     * @return 任务ID（而不是taskKey）
     */
    Long schedulePmiWindowOpenTask(Long windowId, LocalDateTime executeTime);
    
    Long schedulePmiWindowCloseTask(Long windowId, LocalDateTime executeTime);
}
```

```java
// 修复后的实现
@Override
@Transactional
public Long schedulePmiWindowOpenTask(Long windowId, LocalDateTime executeTime) {
    // 创建任务记录
    PmiScheduleWindowTask task = createTaskRecord(...);
    
    // 保存任务记录
    task = taskRepository.save(task);
    final Long taskId = task.getId();
    
    // 调度任务
    ScheduledFuture<?> future = taskScheduler.schedule(...);
    scheduledTasks.put(taskKey, future);
    
    // 直接返回任务ID ✅
    return taskId;
}
```

```java
// 修复后的事件监听器
@EventListener
public void handlePmiWindowCreated(PmiWindowCreatedEvent event) {
    // 直接获取任务ID ✅
    Long openTaskId = dynamicTaskManager.schedulePmiWindowOpenTask(...);
    Long closeTaskId = dynamicTaskManager.schedulePmiWindowCloseTask(...);
    
    // 直接设置任务ID，无需额外查询
    if (openTaskId != null) {
        window.setOpenTaskId(openTaskId);
    }
    if (closeTaskId != null) {
        window.setCloseTaskId(closeTaskId);
    }
    
    windowRepository.save(window);
}
```

## 🎯 **修复效果**

### **解决的问题**
1. ✅ **消除事务时序问题**：不再需要立即查询刚创建的任务
2. ✅ **简化流程**：直接返回任务ID，减少数据库查询
3. ✅ **提高可靠性**：避免任务创建成功但窗口更新失败的情况
4. ✅ **性能优化**：减少不必要的数据库查询操作

### **流程对比**

**修复前**：
```
创建窗口 → 发布事件 → 创建任务(返回taskKey) → 查询任务(可能失败) → 更新窗口
```

**修复后**：
```
创建窗口 → 发布事件 → 创建任务(返回taskId) → 直接更新窗口 ✅
```

## 📊 **影响范围分析**

### **修改的文件**
1. `DynamicTaskManager.java` - 接口方法返回类型
2. `DynamicTaskManagerImpl.java` - 实现方法返回类型
3. `PmiWindowTaskSchedulingService.java` - 事件监听器逻辑
4. `PmiTaskManagementService.java` - 任务重新调度逻辑

### **兼容性**
- ✅ 向前兼容：现有的任务记录不受影响
- ✅ 数据库结构：无需修改数据库表结构
- ✅ API接口：不影响对外API接口

## 🔧 **验证方法**

### **测试场景**
1. **新建计划**：创建新的PMI计划，验证窗口和任务是否正确关联
2. **并发创建**：同时创建多个计划，验证任务ID是否正确设置
3. **异常处理**：模拟任务创建失败，验证错误处理逻辑

### **验证SQL**
```sql
-- 检查窗口和任务的关联关系
SELECT 
    psw.id as window_id,
    psw.open_task_id,
    psw.close_task_id,
    open_task.id as actual_open_task_id,
    close_task.id as actual_close_task_id,
    CASE 
        WHEN psw.open_task_id = open_task.id AND psw.close_task_id = close_task.id 
        THEN 'CORRECT' 
        ELSE 'INCORRECT' 
    END as validation_result
FROM t_pmi_schedule_windows psw
LEFT JOIN t_pmi_schedule_window_tasks open_task 
    ON psw.open_task_id = open_task.id 
    AND open_task.task_type = 'PMI_WINDOW_OPEN'
LEFT JOIN t_pmi_schedule_window_tasks close_task 
    ON psw.close_task_id = close_task.id 
    AND close_task.task_type = 'PMI_WINDOW_CLOSE'
WHERE psw.id >= 2083
ORDER BY psw.id;
```

## 📝 **总结**

### **问题根源**
窗口2083没有相应任务的问题是由于**事务时序问题**导致的：事件监听器在创建任务后立即查询任务记录，但由于事务隔离可能查询不到刚创建的记录。

### **解决方案**
通过修改 `DynamicTaskManager` 直接返回任务ID而不是taskKey，消除了对立即查询的依赖，确保窗口和任务的正确关联。

### **预期效果**
- ✅ 窗口创建后必定有对应的任务
- ✅ 任务ID正确设置到窗口记录中
- ✅ 系统更加稳定可靠
- ✅ 性能得到优化

**这个修复确保了用户描述的预期流程能够正确执行：创建窗口 → 保存到数据库 → 创建任务 → 将窗口ID传给任务 → 创建任务成功后更新窗口里的task ID。**
