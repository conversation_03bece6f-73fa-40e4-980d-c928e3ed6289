# 数据迁移使用说明

## 📋 概述

本文档说明如何将测试环境中移植后的数据导入到生产环境。提供了三种方式：
1. **自动化脚本**：`migrate_data_to_production.sh`
2. **SQL脚本**：`export_import_migrated_data.sql`
3. **手动命令**：直接使用mysqldump和mysql命令

## 🎯 涉及的表

- `t_users` - 用户表
- `t_pmi_records` - PMI记录表
- `t_pmi_schedules` - PMI计划表
- `t_pmi_schedule_windows` - PMI窗口表

## 🚀 方法一：使用自动化脚本（推荐）

### 0. 连接测试（首次使用必须）
```bash
./test_connections.sh
```

### 1. 完整迁移（导出+导入）
```bash
./migrate_data_to_production.sh
```

### 2. 仅导出数据
```bash
./migrate_data_to_production.sh --export-only
```

### 3. 仅导入数据（需要先有导出文件）
```bash
./migrate_data_to_production.sh --import-only
```

### 4. 保留导出和备份文件
```bash
./migrate_data_to_production.sh --keep-files
```

### 5. 指定SSH配置
```bash
# 使用SSH密钥
./migrate_data_to_production.sh --ssh-key /path/to/private_key

# 指定SSH端口
./migrate_data_to_production.sh --ssh-port 2222

# 组合使用
./migrate_data_to_production.sh --ssh-key ~/.ssh/prod_key --ssh-port 2222 --keep-files
```

### 6. 查看帮助
```bash
./migrate_data_to_production.sh --help
```

### 脚本功能特性
- ✅ 支持远程生产服务器（SSH连接）
- ✅ 自动检查SSH和MySQL连接
- ✅ 自动文件上传和下载
- ✅ 自动备份生产环境数据
- ✅ 完整的数据验证
- ✅ 彩色日志输出
- ✅ 错误处理和回滚
- ✅ 临时文件自动清理
- ✅ 支持SSH密钥认证
- ✅ 配置文件支持

## 📝 方法二：使用SQL脚本

### 1. 在测试环境执行导出
```sql
-- 在MySQL客户端执行
source export_import_migrated_data.sql;
```

### 2. 手动传输文件到生产环境

### 3. 在生产环境执行导入
```sql
-- 在MySQL客户端执行导入部分
source export_import_migrated_data.sql;
```

## ⚡ 方法三：手动命令（最灵活）

### 测试环境导出

#### 分别导出各表
```bash
# 导出用户数据
mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  --where="1=1" zoombusV t_users > migrated_t_users.sql

# 导出PMI记录数据
mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  --where="1=1" zoombusV t_pmi_records > migrated_t_pmi_records.sql

# 导出PMI计划数据
mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  --where="1=1" zoombusV t_pmi_schedules > migrated_t_pmi_schedules.sql

# 导出PMI窗口数据
mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  --where="1=1" zoombusV t_pmi_schedule_windows > migrated_t_pmi_schedule_windows.sql
```

#### 一次性导出所有表
```bash
mysqldump -u root -pnvshen2018 --no-create-info --complete-insert --single-transaction \
  zoombusV t_users t_pmi_records t_pmi_schedules t_pmi_schedule_windows > migrated_all_tables.sql
```

#### 打包文件
```bash
tar -czf migrated_data_$(date +%Y%m%d_%H%M%S).tar.gz migrated_*.sql
```

### 生产环境导入

#### 备份现有数据（重要！）
```bash
mysqldump -u root -p --single-transaction zoombusV \
  t_users t_pmi_records t_pmi_schedules t_pmi_schedule_windows \
  > production_backup_$(date +%Y%m%d_%H%M%S).sql
```

#### 分别导入各表（按顺序）
```bash
mysql -u root -p zoombusV < migrated_t_users.sql
mysql -u root -p zoombusV < migrated_t_pmi_records.sql
mysql -u root -p zoombusV < migrated_t_pmi_schedules.sql
mysql -u root -p zoombusV < migrated_t_pmi_schedule_windows.sql
```

#### 一次性导入所有表
```bash
mysql -u root -p zoombusV < migrated_all_tables.sql
```

## ⚠️ 重要注意事项

### 1. 数据备份
- **必须**在导入前备份生产环境数据
- 建议保留多个版本的备份文件

### 2. 导入顺序
如果分别导入，必须按以下顺序：
1. `t_users` （用户表）
2. `t_pmi_records` （PMI记录表）
3. `t_pmi_schedules` （PMI计划表）
4. `t_pmi_schedule_windows` （PMI窗口表）

### 3. 外键约束
- 导入时会临时禁用外键检查
- 导入完成后会重新启用外键检查

### 4. 数据冲突处理
- 使用 `ON DUPLICATE KEY UPDATE` 处理重复数据
- 建议在测试环境先验证导入效果

## 🔍 数据验证

### 导入后验证SQL
```sql
-- 检查表记录数
SELECT 
    'Table' as table_name,
    'Count' as record_count
UNION ALL
SELECT 't_users', COUNT(*) FROM t_users
UNION ALL
SELECT 't_pmi_records', COUNT(*) FROM t_pmi_records
UNION ALL
SELECT 't_pmi_schedules', COUNT(*) FROM t_pmi_schedules
UNION ALL
SELECT 't_pmi_schedule_windows', COUNT(*) FROM t_pmi_schedule_windows;

-- 检查LONG类型PMI完整性
SELECT 
    COUNT(*) as long_pmi_count,
    COUNT(CASE WHEN s.id IS NOT NULL THEN 1 END) as has_schedule,
    COUNT(CASE WHEN w.id IS NOT NULL THEN 1 END) as has_window
FROM t_pmi_records p
LEFT JOIN t_pmi_schedules s ON p.id = s.pmi_record_id
LEFT JOIN t_pmi_schedule_windows w ON p.id = w.pmi_record_id AND w.status = 'ACTIVE'
WHERE p.billing_mode = 'LONG';

-- 检查关联关系
SELECT 
    'PMI-User Relationship' as check_type,
    COUNT(*) as total_pmi,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as valid_user_links
FROM t_pmi_records p
LEFT JOIN t_users u ON p.user_id = u.id;
```

## 🛠️ 故障排除

### 常见问题

1. **MySQL连接失败**
   - 检查数据库服务是否运行
   - 验证用户名密码是否正确
   - 确认网络连接正常

2. **外键约束错误**
   - 确保按正确顺序导入表
   - 检查是否正确禁用了外键检查

3. **重复键错误**
   - 检查是否有重复的PMI号码
   - 验证用户邮箱唯一性

4. **文件权限错误**
   - 确保脚本有执行权限：`chmod +x migrate_data_to_production.sh`
   - 检查导出文件的读取权限

### 回滚操作
如果导入出现问题，可以使用备份文件回滚：
```bash
mysql -u root -p zoombusV < production_backup_YYYYMMDD_HHMMSS.sql
```

## 📊 预期结果

导入成功后，应该看到：
- 用户数据完整迁移
- PMI记录正确关联到用户
- LONG类型PMI有对应的计划和窗口
- 所有关联关系完整
- 数据格式和约束正确

## 📞 技术支持

如果在迁移过程中遇到问题，请：
1. 检查日志输出中的错误信息
2. 验证数据库连接和权限
3. 确认文件完整性
4. 联系技术支持团队
